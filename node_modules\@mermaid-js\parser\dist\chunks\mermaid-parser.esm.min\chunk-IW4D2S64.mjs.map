{"version": 3, "sources": ["../../../src/language/packet/tokenBuilder.ts", "../../../src/language/packet/module.ts"], "sourcesContent": ["import { AbstractMermaidTokenBuilder } from '../common/index.js';\n\nexport class PacketTokenBuilder extends AbstractMermaidTokenBuilder {\n  public constructor() {\n    super(['packet-beta']);\n  }\n}\n", "import type {\n  DefaultSharedCoreModuleContext,\n  LangiumCoreServices,\n  LangiumSharedCoreServices,\n  Module,\n  PartialLangiumCoreServices,\n} from 'langium';\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject,\n} from 'langium';\n\nimport { CommonValueConverter } from '../common/valueConverter.js';\nimport { MermaidGeneratedSharedModule, PacketGeneratedModule } from '../generated/module.js';\nimport { PacketTokenBuilder } from './tokenBuilder.js';\n\n/**\n * Declaration of `Packet` services.\n */\ninterface PacketAddedServices {\n  parser: {\n    TokenBuilder: PacketTokenBuilder;\n    ValueConverter: CommonValueConverter;\n  };\n}\n\n/**\n * Union of Langium default services and `Packet` services.\n */\nexport type PacketServices = LangiumCoreServices & PacketAddedServices;\n\n/**\n * Dependency injection module that overrides Langium default services and\n * contributes the declared `Packet` services.\n */\nexport const PacketModule: Module<\n  PacketServices,\n  PartialLangiumCoreServices & PacketAddedServices\n> = {\n  parser: {\n    TokenBuilder: () => new PacketTokenBuilder(),\n    ValueConverter: () => new CommonValueConverter(),\n  },\n};\n\n/**\n * Create the full set of services required by Langium.\n *\n * First inject the shared services by merging two modules:\n *  - Langium default shared services\n *  - Services generated by langium-cli\n *\n * Then inject the language-specific services by merging three modules:\n *  - Langium default language-specific services\n *  - Services generated by langium-cli\n *  - Services specified in this file\n * @param context - Optional module context with the LSP connection\n * @returns An object wrapping the shared services and the language-specific services\n */\nexport function createPacketServices(context: DefaultSharedCoreModuleContext = EmptyFileSystem): {\n  shared: LangiumSharedCoreServices;\n  Packet: PacketServices;\n} {\n  const shared: LangiumSharedCoreServices = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Packet: PacketServices = inject(\n    createDefaultCoreModule({ shared }),\n    PacketGeneratedModule,\n    PacketModule\n  );\n  shared.ServiceRegistry.register(Packet);\n  return { shared, Packet };\n}\n"], "mappings": "wGAEO,IAAMA,EAAN,cAAiCC,CAA4B,CAFpE,MAEoE,CAAAC,EAAA,2BAC3D,aAAc,CACnB,MAAM,CAAC,aAAa,CAAC,CACvB,CACF,EC+BO,IAAMC,EAGT,CACF,OAAQ,CACN,aAAcC,EAAA,IAAM,IAAIC,EAAV,gBACd,eAAgBD,EAAA,IAAM,IAAIE,EAAV,iBAClB,CACF,EAgBO,SAASC,EAAqBC,EAA0CC,EAG7E,CACA,IAAMC,EAAoCC,EACxCC,EAA8BJ,CAAO,EACrCK,CACF,EACMC,EAAyBH,EAC7BI,EAAwB,CAAE,OAAAL,CAAO,CAAC,EAClCM,EACAb,CACF,EACA,OAAAO,EAAO,gBAAgB,SAASI,CAAM,EAC/B,CAAE,OAAAJ,EAAQ,OAAAI,CAAO,CAC1B,CAfgBV,EAAAG,EAAA", "names": ["PacketTokenBuilder", "AbstractMermaidTokenBuilder", "__name", "PacketModule", "__name", "PacketTokenBuilder", "CommonValueConverter", "createPacketServices", "context", "EmptyFileSystem", "shared", "inject", "createDefaultSharedCoreModule", "MermaidGeneratedSharedModule", "Packet", "createDefaultCoreModule", "PacketGeneratedModule"]}