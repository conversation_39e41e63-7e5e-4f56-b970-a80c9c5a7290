!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).predicates={})}(this,(function(t){"use strict";const n=11102230246251565e-32,e=134217729,o=(3+8*n)*n;function f(t,n,e,o,f){let i,r,c,s,u=n[0],a=o[0],l=0,b=0;a>u==a>-u?(i=u,u=n[++l]):(i=a,a=o[++b]);let d=0;if(l<t&&b<e)for(a>u==a>-u?(r=u+i,c=i-(r-u),u=n[++l]):(r=a+i,c=i-(r-a),a=o[++b]),i=r,0!==c&&(f[d++]=c);l<t&&b<e;)a>u==a>-u?(r=i+u,s=r-i,c=i-(r-s)+(u-s),u=n[++l]):(r=i+a,s=r-i,c=i-(r-s)+(a-s),a=o[++b]),i=r,0!==c&&(f[d++]=c);for(;l<t;)r=i+u,s=r-i,c=i-(r-s)+(u-s),u=n[++l],i=r,0!==c&&(f[d++]=c);for(;b<e;)r=i+a,s=r-i,c=i-(r-s)+(a-s),a=o[++b],i=r,0!==c&&(f[d++]=c);return 0===i&&0!==d||(f[d++]=i),d}function i(t,n,e,o,i,r,c,s){return f(f(t,n,e,o,c),c,i,r,s)}function r(t,n,o,f){let i,r,c,s,u,a,l,b,d,h,p;l=e*o,h=l-(l-o),p=o-h;let M=n[0];i=M*o,l=e*M,b=l-(l-M),d=M-b,c=d*p-(i-b*h-d*h-b*p);let y=0;0!==c&&(f[y++]=c);for(let x=1;x<t;x++)M=n[x],s=M*o,l=e*M,b=l-(l-M),d=M-b,u=d*p-(s-b*h-d*h-b*p),r=i+u,a=r-i,c=i-(r-a)+(u-a),0!==c&&(f[y++]=c),i=s+r,c=r-(i-s),0!==c&&(f[y++]=c);return 0===i&&0!==y||(f[y++]=i),y}function c(t){return new Float64Array(t)}const s=4440892098500632e-31,u=5423418723394464e-46,a=c(4),l=c(4),b=c(4),d=c(4),h=c(4),p=c(4),M=c(4),y=c(4),x=c(8),g=c(8),m=c(8),T=c(8),j=c(8),w=c(8),A=c(8),F=c(8),k=c(8),q=c(4),v=c(4),z=c(4),B=c(8),C=c(16),D=c(16),E=c(16),G=c(32),H=c(32),I=c(48),J=c(64);let K=c(1152),L=c(1152);function N(t,n,e){t=f(t,K,n,e,L);const o=K;return K=L,L=o,t}t.incircle=function(t,n,c,L,O,P,Q,R){const S=t-Q,U=c-Q,V=O-Q,W=n-R,X=L-R,Y=P-R,Z=U*Y,$=V*X,_=S*S+W*W,tt=V*W,nt=S*Y,et=U*U+X*X,ot=S*X,ft=U*W,it=V*V+Y*Y,rt=_*(Z-$)+et*(tt-nt)+it*(ot-ft),ct=(Math.abs(Z)+Math.abs($))*_+(Math.abs(tt)+Math.abs(nt))*et+(Math.abs(ot)+Math.abs(ft))*it,st=11102230246251577e-31*ct;return rt>st||-rt>st?rt:function(t,n,c,L,O,P,Q,R,S){let U,V,W,X,Y,Z,$,_,tt,nt,et,ot,ft,it,rt,ct,st,ut,at,lt,bt,dt,ht,pt,Mt,yt,xt,gt,mt,Tt,jt,wt,At,Ft,kt;const qt=t-Q,vt=c-Q,zt=O-Q,Bt=n-R,Ct=L-R,Dt=P-R;jt=vt*Dt,ht=e*vt,pt=ht-(ht-vt),Mt=vt-pt,ht=e*Dt,yt=ht-(ht-Dt),xt=Dt-yt,wt=Mt*xt-(jt-pt*yt-Mt*yt-pt*xt),At=zt*Ct,ht=e*zt,pt=ht-(ht-zt),Mt=zt-pt,ht=e*Ct,yt=ht-(ht-Ct),xt=Ct-yt,Ft=Mt*xt-(At-pt*yt-Mt*yt-pt*xt),gt=wt-Ft,dt=wt-gt,a[0]=wt-(gt+dt)+(dt-Ft),mt=jt+gt,dt=mt-jt,Tt=jt-(mt-dt)+(gt-dt),gt=Tt-At,dt=Tt-gt,a[1]=Tt-(gt+dt)+(dt-At),kt=mt+gt,dt=kt-mt,a[2]=mt-(kt-dt)+(gt-dt),a[3]=kt,jt=zt*Bt,ht=e*zt,pt=ht-(ht-zt),Mt=zt-pt,ht=e*Bt,yt=ht-(ht-Bt),xt=Bt-yt,wt=Mt*xt-(jt-pt*yt-Mt*yt-pt*xt),At=qt*Dt,ht=e*qt,pt=ht-(ht-qt),Mt=qt-pt,ht=e*Dt,yt=ht-(ht-Dt),xt=Dt-yt,Ft=Mt*xt-(At-pt*yt-Mt*yt-pt*xt),gt=wt-Ft,dt=wt-gt,l[0]=wt-(gt+dt)+(dt-Ft),mt=jt+gt,dt=mt-jt,Tt=jt-(mt-dt)+(gt-dt),gt=Tt-At,dt=Tt-gt,l[1]=Tt-(gt+dt)+(dt-At),kt=mt+gt,dt=kt-mt,l[2]=mt-(kt-dt)+(gt-dt),l[3]=kt,jt=qt*Ct,ht=e*qt,pt=ht-(ht-qt),Mt=qt-pt,ht=e*Ct,yt=ht-(ht-Ct),xt=Ct-yt,wt=Mt*xt-(jt-pt*yt-Mt*yt-pt*xt),At=vt*Bt,ht=e*vt,pt=ht-(ht-vt),Mt=vt-pt,ht=e*Bt,yt=ht-(ht-Bt),xt=Bt-yt,Ft=Mt*xt-(At-pt*yt-Mt*yt-pt*xt),gt=wt-Ft,dt=wt-gt,b[0]=wt-(gt+dt)+(dt-Ft),mt=jt+gt,dt=mt-jt,Tt=jt-(mt-dt)+(gt-dt),gt=Tt-At,dt=Tt-gt,b[1]=Tt-(gt+dt)+(dt-At),kt=mt+gt,dt=kt-mt,b[2]=mt-(kt-dt)+(gt-dt),b[3]=kt,U=f(f(f(r(r(4,a,qt,B),B,qt,C),C,r(r(4,a,Bt,B),B,Bt,D),D,G),G,f(r(r(4,l,vt,B),B,vt,C),C,r(r(4,l,Ct,B),B,Ct,D),D,H),H,J),J,f(r(r(4,b,zt,B),B,zt,C),C,r(r(4,b,Dt,B),B,Dt,D),D,G),G,K);let Et=function(t,n){let e=n[0];for(let o=1;o<t;o++)e+=n[o];return e}(U,K),Gt=s*S;if(Et>=Gt||-Et>=Gt)return Et;if(dt=t-qt,V=t-(qt+dt)+(dt-Q),dt=n-Bt,Y=n-(Bt+dt)+(dt-R),dt=c-vt,W=c-(vt+dt)+(dt-Q),dt=L-Ct,Z=L-(Ct+dt)+(dt-R),dt=O-zt,X=O-(zt+dt)+(dt-Q),dt=P-Dt,$=P-(Dt+dt)+(dt-R),0===V&&0===W&&0===X&&0===Y&&0===Z&&0===$)return Et;if(Gt=u*S+o*Math.abs(Et),Et+=(qt*qt+Bt*Bt)*(vt*$+Dt*W-(Ct*X+zt*Z))+2*(qt*V+Bt*Y)*(vt*Dt-Ct*zt)+((vt*vt+Ct*Ct)*(zt*Y+Bt*X-(Dt*V+qt*$))+2*(vt*W+Ct*Z)*(zt*Bt-Dt*qt))+((zt*zt+Dt*Dt)*(qt*Z+Ct*V-(Bt*W+vt*Y))+2*(zt*X+Dt*$)*(qt*Ct-Bt*vt)),Et>=Gt||-Et>=Gt)return Et;if(0===W&&0===Z&&0===X&&0===$||(jt=qt*qt,ht=e*qt,pt=ht-(ht-qt),Mt=qt-pt,wt=Mt*Mt-(jt-pt*pt-(pt+pt)*Mt),At=Bt*Bt,ht=e*Bt,pt=ht-(ht-Bt),Mt=Bt-pt,Ft=Mt*Mt-(At-pt*pt-(pt+pt)*Mt),gt=wt+Ft,dt=gt-wt,d[0]=wt-(gt-dt)+(Ft-dt),mt=jt+gt,dt=mt-jt,Tt=jt-(mt-dt)+(gt-dt),gt=Tt+At,dt=gt-Tt,d[1]=Tt-(gt-dt)+(At-dt),kt=mt+gt,dt=kt-mt,d[2]=mt-(kt-dt)+(gt-dt),d[3]=kt),0===X&&0===$&&0===V&&0===Y||(jt=vt*vt,ht=e*vt,pt=ht-(ht-vt),Mt=vt-pt,wt=Mt*Mt-(jt-pt*pt-(pt+pt)*Mt),At=Ct*Ct,ht=e*Ct,pt=ht-(ht-Ct),Mt=Ct-pt,Ft=Mt*Mt-(At-pt*pt-(pt+pt)*Mt),gt=wt+Ft,dt=gt-wt,h[0]=wt-(gt-dt)+(Ft-dt),mt=jt+gt,dt=mt-jt,Tt=jt-(mt-dt)+(gt-dt),gt=Tt+At,dt=gt-Tt,h[1]=Tt-(gt-dt)+(At-dt),kt=mt+gt,dt=kt-mt,h[2]=mt-(kt-dt)+(gt-dt),h[3]=kt),0===V&&0===Y&&0===W&&0===Z||(jt=zt*zt,ht=e*zt,pt=ht-(ht-zt),Mt=zt-pt,wt=Mt*Mt-(jt-pt*pt-(pt+pt)*Mt),At=Dt*Dt,ht=e*Dt,pt=ht-(ht-Dt),Mt=Dt-pt,Ft=Mt*Mt-(At-pt*pt-(pt+pt)*Mt),gt=wt+Ft,dt=gt-wt,p[0]=wt-(gt-dt)+(Ft-dt),mt=jt+gt,dt=mt-jt,Tt=jt-(mt-dt)+(gt-dt),gt=Tt+At,dt=gt-Tt,p[1]=Tt-(gt-dt)+(At-dt),kt=mt+gt,dt=kt-mt,p[2]=mt-(kt-dt)+(gt-dt),p[3]=kt),0!==V&&(_=r(4,a,V,x),U=N(U,i(r(_,x,2*qt,C),C,r(r(4,p,V,B),B,Ct,D),D,r(r(4,h,V,B),B,-Dt,E),E,G,I),I)),0!==Y&&(tt=r(4,a,Y,g),U=N(U,i(r(tt,g,2*Bt,C),C,r(r(4,h,Y,B),B,zt,D),D,r(r(4,p,Y,B),B,-vt,E),E,G,I),I)),0!==W&&(nt=r(4,l,W,m),U=N(U,i(r(nt,m,2*vt,C),C,r(r(4,d,W,B),B,Dt,D),D,r(r(4,p,W,B),B,-Bt,E),E,G,I),I)),0!==Z&&(et=r(4,l,Z,T),U=N(U,i(r(et,T,2*Ct,C),C,r(r(4,p,Z,B),B,qt,D),D,r(r(4,d,Z,B),B,-zt,E),E,G,I),I)),0!==X&&(ot=r(4,b,X,j),U=N(U,i(r(ot,j,2*zt,C),C,r(r(4,h,X,B),B,Bt,D),D,r(r(4,d,X,B),B,-Ct,E),E,G,I),I)),0!==$&&(ft=r(4,b,$,w),U=N(U,i(r(ft,w,2*Dt,C),C,r(r(4,d,$,B),B,vt,D),D,r(r(4,h,$,B),B,-qt,E),E,G,I),I)),0!==V||0!==Y){if(0!==W||0!==Z||0!==X||0!==$?(jt=W*Dt,ht=e*W,pt=ht-(ht-W),Mt=W-pt,ht=e*Dt,yt=ht-(ht-Dt),xt=Dt-yt,wt=Mt*xt-(jt-pt*yt-Mt*yt-pt*xt),At=vt*$,ht=e*vt,pt=ht-(ht-vt),Mt=vt-pt,ht=e*$,yt=ht-(ht-$),xt=$-yt,Ft=Mt*xt-(At-pt*yt-Mt*yt-pt*xt),gt=wt+Ft,dt=gt-wt,M[0]=wt-(gt-dt)+(Ft-dt),mt=jt+gt,dt=mt-jt,Tt=jt-(mt-dt)+(gt-dt),gt=Tt+At,dt=gt-Tt,M[1]=Tt-(gt-dt)+(At-dt),kt=mt+gt,dt=kt-mt,M[2]=mt-(kt-dt)+(gt-dt),M[3]=kt,jt=X*-Ct,ht=e*X,pt=ht-(ht-X),Mt=X-pt,ht=e*-Ct,yt=ht-(ht- -Ct),xt=-Ct-yt,wt=Mt*xt-(jt-pt*yt-Mt*yt-pt*xt),At=zt*-Z,ht=e*zt,pt=ht-(ht-zt),Mt=zt-pt,ht=e*-Z,yt=ht-(ht- -Z),xt=-Z-yt,Ft=Mt*xt-(At-pt*yt-Mt*yt-pt*xt),gt=wt+Ft,dt=gt-wt,y[0]=wt-(gt-dt)+(Ft-dt),mt=jt+gt,dt=mt-jt,Tt=jt-(mt-dt)+(gt-dt),gt=Tt+At,dt=gt-Tt,y[1]=Tt-(gt-dt)+(At-dt),kt=mt+gt,dt=kt-mt,y[2]=mt-(kt-dt)+(gt-dt),y[3]=kt,rt=f(4,M,4,y,F),jt=W*$,ht=e*W,pt=ht-(ht-W),Mt=W-pt,ht=e*$,yt=ht-(ht-$),xt=$-yt,wt=Mt*xt-(jt-pt*yt-Mt*yt-pt*xt),At=X*Z,ht=e*X,pt=ht-(ht-X),Mt=X-pt,ht=e*Z,yt=ht-(ht-Z),xt=Z-yt,Ft=Mt*xt-(At-pt*yt-Mt*yt-pt*xt),gt=wt-Ft,dt=wt-gt,v[0]=wt-(gt+dt)+(dt-Ft),mt=jt+gt,dt=mt-jt,Tt=jt-(mt-dt)+(gt-dt),gt=Tt-At,dt=Tt-gt,v[1]=Tt-(gt+dt)+(dt-At),kt=mt+gt,dt=kt-mt,v[2]=mt-(kt-dt)+(gt-dt),v[3]=kt,ut=4):(F[0]=0,rt=1,v[0]=0,ut=1),0!==V){const t=r(rt,F,V,E);U=N(U,f(r(_,x,V,C),C,r(t,E,2*qt,G),G,I),I);const n=r(ut,v,V,B);U=N(U,i(r(n,B,2*qt,C),C,r(n,B,V,D),D,r(t,E,V,G),G,H,J),J),0!==Z&&(U=N(U,r(r(4,p,V,B),B,Z,C),C)),0!==$&&(U=N(U,r(r(4,h,-V,B),B,$,C),C))}if(0!==Y){const t=r(rt,F,Y,E);U=N(U,f(r(tt,g,Y,C),C,r(t,E,2*Bt,G),G,I),I);const n=r(ut,v,Y,B);U=N(U,i(r(n,B,2*Bt,C),C,r(n,B,Y,D),D,r(t,E,Y,G),G,H,J),J)}}if(0!==W||0!==Z){if(0!==X||0!==$||0!==V||0!==Y?(jt=X*Bt,ht=e*X,pt=ht-(ht-X),Mt=X-pt,ht=e*Bt,yt=ht-(ht-Bt),xt=Bt-yt,wt=Mt*xt-(jt-pt*yt-Mt*yt-pt*xt),At=zt*Y,ht=e*zt,pt=ht-(ht-zt),Mt=zt-pt,ht=e*Y,yt=ht-(ht-Y),xt=Y-yt,Ft=Mt*xt-(At-pt*yt-Mt*yt-pt*xt),gt=wt+Ft,dt=gt-wt,M[0]=wt-(gt-dt)+(Ft-dt),mt=jt+gt,dt=mt-jt,Tt=jt-(mt-dt)+(gt-dt),gt=Tt+At,dt=gt-Tt,M[1]=Tt-(gt-dt)+(At-dt),kt=mt+gt,dt=kt-mt,M[2]=mt-(kt-dt)+(gt-dt),M[3]=kt,lt=-Dt,bt=-$,jt=V*lt,ht=e*V,pt=ht-(ht-V),Mt=V-pt,ht=e*lt,yt=ht-(ht-lt),xt=lt-yt,wt=Mt*xt-(jt-pt*yt-Mt*yt-pt*xt),At=qt*bt,ht=e*qt,pt=ht-(ht-qt),Mt=qt-pt,ht=e*bt,yt=ht-(ht-bt),xt=bt-yt,Ft=Mt*xt-(At-pt*yt-Mt*yt-pt*xt),gt=wt+Ft,dt=gt-wt,y[0]=wt-(gt-dt)+(Ft-dt),mt=jt+gt,dt=mt-jt,Tt=jt-(mt-dt)+(gt-dt),gt=Tt+At,dt=gt-Tt,y[1]=Tt-(gt-dt)+(At-dt),kt=mt+gt,dt=kt-mt,y[2]=mt-(kt-dt)+(gt-dt),y[3]=kt,ct=f(4,M,4,y,k),jt=X*Y,ht=e*X,pt=ht-(ht-X),Mt=X-pt,ht=e*Y,yt=ht-(ht-Y),xt=Y-yt,wt=Mt*xt-(jt-pt*yt-Mt*yt-pt*xt),At=V*$,ht=e*V,pt=ht-(ht-V),Mt=V-pt,ht=e*$,yt=ht-(ht-$),xt=$-yt,Ft=Mt*xt-(At-pt*yt-Mt*yt-pt*xt),gt=wt-Ft,dt=wt-gt,z[0]=wt-(gt+dt)+(dt-Ft),mt=jt+gt,dt=mt-jt,Tt=jt-(mt-dt)+(gt-dt),gt=Tt-At,dt=Tt-gt,z[1]=Tt-(gt+dt)+(dt-At),kt=mt+gt,dt=kt-mt,z[2]=mt-(kt-dt)+(gt-dt),z[3]=kt,at=4):(k[0]=0,ct=1,z[0]=0,at=1),0!==W){const t=r(ct,k,W,E);U=N(U,f(r(nt,m,W,C),C,r(t,E,2*vt,G),G,I),I);const n=r(at,z,W,B);U=N(U,i(r(n,B,2*vt,C),C,r(n,B,W,D),D,r(t,E,W,G),G,H,J),J),0!==$&&(U=N(U,r(r(4,d,W,B),B,$,C),C)),0!==Y&&(U=N(U,r(r(4,p,-W,B),B,Y,C),C))}if(0!==Z){const t=r(ct,k,Z,E);U=N(U,f(r(et,T,Z,C),C,r(t,E,2*Ct,G),G,I),I);const n=r(at,z,Z,B);U=N(U,i(r(n,B,2*Ct,C),C,r(n,B,Z,D),D,r(t,E,Z,G),G,H,J),J)}}if(0!==X||0!==$){if(0!==V||0!==Y||0!==W||0!==Z?(jt=V*Ct,ht=e*V,pt=ht-(ht-V),Mt=V-pt,ht=e*Ct,yt=ht-(ht-Ct),xt=Ct-yt,wt=Mt*xt-(jt-pt*yt-Mt*yt-pt*xt),At=qt*Z,ht=e*qt,pt=ht-(ht-qt),Mt=qt-pt,ht=e*Z,yt=ht-(ht-Z),xt=Z-yt,Ft=Mt*xt-(At-pt*yt-Mt*yt-pt*xt),gt=wt+Ft,dt=gt-wt,M[0]=wt-(gt-dt)+(Ft-dt),mt=jt+gt,dt=mt-jt,Tt=jt-(mt-dt)+(gt-dt),gt=Tt+At,dt=gt-Tt,M[1]=Tt-(gt-dt)+(At-dt),kt=mt+gt,dt=kt-mt,M[2]=mt-(kt-dt)+(gt-dt),M[3]=kt,lt=-Bt,bt=-Y,jt=W*lt,ht=e*W,pt=ht-(ht-W),Mt=W-pt,ht=e*lt,yt=ht-(ht-lt),xt=lt-yt,wt=Mt*xt-(jt-pt*yt-Mt*yt-pt*xt),At=vt*bt,ht=e*vt,pt=ht-(ht-vt),Mt=vt-pt,ht=e*bt,yt=ht-(ht-bt),xt=bt-yt,Ft=Mt*xt-(At-pt*yt-Mt*yt-pt*xt),gt=wt+Ft,dt=gt-wt,y[0]=wt-(gt-dt)+(Ft-dt),mt=jt+gt,dt=mt-jt,Tt=jt-(mt-dt)+(gt-dt),gt=Tt+At,dt=gt-Tt,y[1]=Tt-(gt-dt)+(At-dt),kt=mt+gt,dt=kt-mt,y[2]=mt-(kt-dt)+(gt-dt),y[3]=kt,it=f(4,M,4,y,A),jt=V*Z,ht=e*V,pt=ht-(ht-V),Mt=V-pt,ht=e*Z,yt=ht-(ht-Z),xt=Z-yt,wt=Mt*xt-(jt-pt*yt-Mt*yt-pt*xt),At=W*Y,ht=e*W,pt=ht-(ht-W),Mt=W-pt,ht=e*Y,yt=ht-(ht-Y),xt=Y-yt,Ft=Mt*xt-(At-pt*yt-Mt*yt-pt*xt),gt=wt-Ft,dt=wt-gt,q[0]=wt-(gt+dt)+(dt-Ft),mt=jt+gt,dt=mt-jt,Tt=jt-(mt-dt)+(gt-dt),gt=Tt-At,dt=Tt-gt,q[1]=Tt-(gt+dt)+(dt-At),kt=mt+gt,dt=kt-mt,q[2]=mt-(kt-dt)+(gt-dt),q[3]=kt,st=4):(A[0]=0,it=1,q[0]=0,st=1),0!==X){const t=r(it,A,X,E);U=N(U,f(r(ot,j,X,C),C,r(t,E,2*zt,G),G,I),I);const n=r(st,q,X,B);U=N(U,i(r(n,B,2*zt,C),C,r(n,B,X,D),D,r(t,E,X,G),G,H,J),J),0!==Y&&(U=N(U,r(r(4,h,X,B),B,Y,C),C)),0!==Z&&(U=N(U,r(r(4,d,-X,B),B,Z,C),C))}if(0!==$){const t=r(it,A,$,E);U=N(U,f(r(ft,w,$,C),C,r(t,E,2*Dt,G),G,I),I);const n=r(st,q,$,B);U=N(U,i(r(n,B,2*Dt,C),C,r(n,B,$,D),D,r(t,E,$,G),G,H,J),J)}}return K[U-1]}(t,n,c,L,O,P,Q,R,ct)},t.incirclefast=function(t,n,e,o,f,i,r,c){const s=t-r,u=n-c,a=e-r,l=o-c,b=f-r,d=i-c;return(s*s+u*u)*(a*d-b*l)+(a*a+l*l)*(b*u-s*d)+(b*b+d*d)*(s*l-a*u)}}));
