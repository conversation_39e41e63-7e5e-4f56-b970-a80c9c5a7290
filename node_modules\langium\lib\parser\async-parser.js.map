{"version": 3, "file": "async-parser.js", "sourceRoot": "", "sources": ["../../src/parser/async-parser.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAQhF,OAAO,EAAE,QAAQ,EAAE,kBAAkB,EAAE,MAAM,2BAA2B,CAAC;AACzE,OAAO,EAAE,OAAO,EAAE,MAAM,mBAAmB,CAAC;AAsB5C;;;;;GAKG;AACH,MAAM,OAAO,kBAAkB;IAI3B,YAAY,QAA6B;QACrC,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC;IACpD,CAAC;IAED,KAAK,CAAoB,IAAY,EAAE,YAA+B;QAClE,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAI,IAAI,CAAC,CAAC,CAAC;IAC3D,CAAC;CACJ;AAED,MAAM,OAAgB,2BAA2B;IAiB7C,YAAY,QAA6B;QAfzC;;;WAGG;QACO,gBAAW,GAAG,CAAC,CAAC;QAC1B;;;WAGG;QACO,qBAAgB,GAAG,GAAG,CAAC;QACvB,eAAU,GAAmB,EAAE,CAAC;QAChC,UAAK,GAAkC,EAAE,CAAC;QAKhD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;IACjD,CAAC;IAES,iBAAiB;QACvB,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE;gBAChB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;oBACpC,IAAI,QAAQ,EAAE,CAAC;wBACX,MAAM,CAAC,IAAI,EAAE,CAAC;wBACd,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oBAC7B,CAAC;gBACL,CAAC;YACL,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,KAAK,CAAoB,IAAY,EAAE,WAA8B;QACvE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAC3D,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAkB,CAAC;QAChD,IAAI,OAAmC,CAAC;QACxC,oGAAoG;QACpG,6GAA6G;QAC7G,gGAAgG;QAChG,MAAM,YAAY,GAAG,WAAW,CAAC,uBAAuB,CAAC,GAAG,EAAE;YAC1D,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;gBACtB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YACjC,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QACH,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAI,MAAM,CAAC,CAAC;YAClD,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;YACX,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE;YACZ,YAAY,CAAC,OAAO,EAAE,CAAC;YACvB,YAAY,CAAC,OAAO,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC,OAAO,CAAC;IAC5B,CAAC;IAES,eAAe,CAAC,MAAoB;QAC1C,MAAM,CAAC,SAAS,EAAE,CAAC;QACnB,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC9C,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;YACb,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACrC,CAAC;IACL,CAAC;IAES,KAAK,CAAC,mBAAmB,CAAC,WAA8B;QAC9D,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACnC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,EAAE,CAAC;gBACd,OAAO,MAAM,CAAC;YAClB,CAAC;QACL,CAAC;QACD,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAgB,CAAC;QAC9C,WAAW,CAAC,uBAAuB,CAAC,GAAG,EAAE;YACrC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC3C,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;gBACb,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAChC,CAAC;YACD,QAAQ,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1B,OAAO,QAAQ,CAAC,OAAO,CAAC;IAC5B,CAAC;CAGJ;AAKD,MAAM,OAAO,YAAY;IAUrB,IAAI,KAAK;QACL,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,IAAI,OAAO;QACP,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;IACrC,CAAC;IAED,YAAY,WAA8B,EAAE,SAAgC,EAAE,OAA8B,EAAE,SAAqB;QAdhH,mBAAc,GAAG,IAAI,OAAO,EAAQ,CAAC;QAE9C,aAAQ,GAAG,IAAI,QAAQ,EAAe,CAAC;QACvC,WAAM,GAAG,IAAI,CAAC;QACd,aAAQ,GAAG,KAAK,CAAC;QAWvB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,SAAS,CAAC,MAAM,CAAC,EAAE;YACf,MAAM,WAAW,GAAG,MAAqB,CAAC;YAC1C,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YACnC,IAAI,CAAC,MAAM,EAAE,CAAC;QAClB,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,KAAK,CAAC,EAAE;YACZ,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5B,IAAI,CAAC,MAAM,EAAE,CAAC;QAClB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,SAAS;QACL,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QACzC,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IAED,IAAI;QACA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAED,MAAM;QACF,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,IAAY;QACd,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC7C,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;QAC/B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACvB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;IACjC,CAAC;CACJ"}