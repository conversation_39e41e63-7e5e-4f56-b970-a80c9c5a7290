import type { DiagramOrientation, Commit, GitGraphDB, CommitDB, MergeDB, BranchDB, CherryPickDB } from './gitGraphTypes.js';
export declare const setDirection: (dir: DiagramOrientation) => void;
export declare const setOptions: (rawOptString: string) => void;
export declare const getOptions: () => any;
export declare const commit: (commitDB: CommitDB) => void;
export declare const branch: (branchDB: BranchDB) => void;
export declare const merge: (mergeDB: MergeDB) => void;
export declare const cherryPick: (cherryPickDB: CherryPickDB) => void;
export declare const checkout: (branch: string) => void;
export declare const prettyPrint: () => void;
export declare const clear: () => void;
export declare const getBranchesAsObjArray: () => {
    name: string;
}[];
export declare const getBranches: () => Map<string, string | null>;
export declare const getCommits: () => Map<string, Commit>;
export declare const getCommitsArray: () => Commit[];
export declare const getCurrentBranch: () => string;
export declare const getDirection: () => DiagramOrientation;
export declare const getHead: () => Commit | null;
export declare const db: GitGraphDB;
