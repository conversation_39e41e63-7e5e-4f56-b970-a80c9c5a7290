{"version": 3, "file": "index.esm2017.js", "sources": ["../../src/serializer.ts", "../../src/constants.ts", "../../src/error.ts", "../../src/context.ts", "../../src/service.ts", "../../src/config.ts", "../../src/api.ts", "../../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst LONG_TYPE = 'type.googleapis.com/google.protobuf.Int64Value';\nconst UNSIGNED_LONG_TYPE = 'type.googleapis.com/google.protobuf.UInt64Value';\n\nfunction mapValues(\n  // { [k: string]: unknown } is no longer a wildcard assignment target after typescript 3.5\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  o: { [key: string]: any },\n  f: (arg0: unknown) => unknown\n): object {\n  const result: { [key: string]: unknown } = {};\n  for (const key in o) {\n    if (o.hasOwnProperty(key)) {\n      result[key] = f(o[key]);\n    }\n  }\n  return result;\n}\n\n/**\n * Takes data and encodes it in a JSON-friendly way, such that types such as\n * Date are preserved.\n * @internal\n * @param data - Data to encode.\n */\nexport function encode(data: unknown): unknown {\n  if (data == null) {\n    return null;\n  }\n  if (data instanceof Number) {\n    data = data.valueOf();\n  }\n  if (typeof data === 'number' && isFinite(data)) {\n    // Any number in JS is safe to put directly in JSON and parse as a double\n    // without any loss of precision.\n    return data;\n  }\n  if (data === true || data === false) {\n    return data;\n  }\n  if (Object.prototype.toString.call(data) === '[object String]') {\n    return data;\n  }\n  if (data instanceof Date) {\n    return data.toISOString();\n  }\n  if (Array.isArray(data)) {\n    return data.map(x => encode(x));\n  }\n  if (typeof data === 'function' || typeof data === 'object') {\n    return mapValues(data!, x => encode(x));\n  }\n  // If we got this far, the data is not encodable.\n  throw new Error('Data cannot be encoded in JSON: ' + data);\n}\n\n/**\n * Takes data that's been encoded in a JSON-friendly form and returns a form\n * with richer datatypes, such as Dates, etc.\n * @internal\n * @param json - JSON to convert.\n */\nexport function decode(json: unknown): unknown {\n  if (json == null) {\n    return json;\n  }\n  if ((json as { [key: string]: unknown })['@type']) {\n    switch ((json as { [key: string]: unknown })['@type']) {\n      case LONG_TYPE:\n      // Fall through and handle this the same as unsigned.\n      case UNSIGNED_LONG_TYPE: {\n        // Technically, this could work return a valid number for malformed\n        // data if there was a number followed by garbage. But it's just not\n        // worth all the extra code to detect that case.\n        const value = Number((json as { [key: string]: unknown })['value']);\n        if (isNaN(value)) {\n          throw new Error('Data cannot be decoded from JSON: ' + json);\n        }\n        return value;\n      }\n      default: {\n        throw new Error('Data cannot be decoded from JSON: ' + json);\n      }\n    }\n  }\n  if (Array.isArray(json)) {\n    return json.map(x => decode(x));\n  }\n  if (typeof json === 'function' || typeof json === 'object') {\n    return mapValues(json!, x => decode(x));\n  }\n  // Anything else is safe to return.\n  return json;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Type constant for Firebase Functions.\n */\nexport const FUNCTIONS_TYPE = 'functions';\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FunctionsErrorCodeCore as FunctionsErrorCode } from './public-types';\nimport { decode } from './serializer';\nimport { HttpResponseBody } from './service';\nimport { FirebaseError } from '@firebase/util';\nimport { FUNCTIONS_TYPE } from './constants';\n\n/**\n * Standard error codes for different ways a request can fail, as defined by:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * This map is used primarily to convert from a backend error code string to\n * a client SDK error code string, and make sure it's in the supported set.\n */\nconst errorCodeMap: { [name: string]: FunctionsErrorCode } = {\n  OK: 'ok',\n  CANCELLED: 'cancelled',\n  UNKNOWN: 'unknown',\n  INVALID_ARGUMENT: 'invalid-argument',\n  DEADLINE_EXCEEDED: 'deadline-exceeded',\n  NOT_FOUND: 'not-found',\n  ALREADY_EXISTS: 'already-exists',\n  PERMISSION_DENIED: 'permission-denied',\n  UNAUTHENTICATED: 'unauthenticated',\n  RESOURCE_EXHAUSTED: 'resource-exhausted',\n  FAILED_PRECONDITION: 'failed-precondition',\n  ABORTED: 'aborted',\n  OUT_OF_RANGE: 'out-of-range',\n  UNIMPLEMENTED: 'unimplemented',\n  INTERNAL: 'internal',\n  UNAVAILABLE: 'unavailable',\n  DATA_LOSS: 'data-loss'\n};\n\n/**\n * An error returned by the Firebase Functions client SDK.\n *\n * See {@link FunctionsErrorCode} for full documentation of codes.\n *\n * @public\n */\nexport class FunctionsError extends FirebaseError {\n  /**\n   * Constructs a new instance of the `FunctionsError` class.\n   */\n  constructor(\n    /**\n     * A standard error code that will be returned to the client. This also\n     * determines the HTTP status code of the response, as defined in code.proto.\n     */\n    code: FunctionsErrorCode,\n    message?: string,\n    /**\n     * Additional details to be converted to JSON and included in the error response.\n     */\n    readonly details?: unknown\n  ) {\n    super(`${FUNCTIONS_TYPE}/${code}`, message || '');\n\n    // Since the FirebaseError constructor sets the prototype of `this` to FirebaseError.prototype,\n    // we also have to do it in all subclasses to allow for correct `instanceof` checks.\n    Object.setPrototypeOf(this, FunctionsError.prototype);\n  }\n}\n\n/**\n * Takes an HTTP status code and returns the corresponding ErrorCode.\n * This is the standard HTTP status code -> error mapping defined in:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * @param status An HTTP status code.\n * @return The corresponding ErrorCode, or ErrorCode.UNKNOWN if none.\n */\nfunction codeForHTTPStatus(status: number): FunctionsErrorCode {\n  // Make sure any successful status is OK.\n  if (status >= 200 && status < 300) {\n    return 'ok';\n  }\n  switch (status) {\n    case 0:\n      // This can happen if the server returns 500.\n      return 'internal';\n    case 400:\n      return 'invalid-argument';\n    case 401:\n      return 'unauthenticated';\n    case 403:\n      return 'permission-denied';\n    case 404:\n      return 'not-found';\n    case 409:\n      return 'aborted';\n    case 429:\n      return 'resource-exhausted';\n    case 499:\n      return 'cancelled';\n    case 500:\n      return 'internal';\n    case 501:\n      return 'unimplemented';\n    case 503:\n      return 'unavailable';\n    case 504:\n      return 'deadline-exceeded';\n    default: // ignore\n  }\n  return 'unknown';\n}\n\n/**\n * Takes an HTTP response and returns the corresponding Error, if any.\n */\nexport function _errorForResponse(\n  status: number,\n  bodyJSON: HttpResponseBody | null\n): Error | null {\n  let code = codeForHTTPStatus(status);\n\n  // Start with reasonable defaults from the status code.\n  let description: string = code;\n\n  let details: unknown = undefined;\n\n  // Then look through the body for explicit details.\n  try {\n    const errorJSON = bodyJSON && bodyJSON.error;\n    if (errorJSON) {\n      const status = errorJSON.status;\n      if (typeof status === 'string') {\n        if (!errorCodeMap[status]) {\n          // They must've included an unknown error code in the body.\n          return new FunctionsError('internal', 'internal');\n        }\n        code = errorCodeMap[status];\n\n        // TODO(klimt): Add better default descriptions for error enums.\n        // The default description needs to be updated for the new code.\n        description = status;\n      }\n\n      const message = errorJSON.message;\n      if (typeof message === 'string') {\n        description = message;\n      }\n\n      details = errorJSON.details;\n      if (details !== undefined) {\n        details = decode(details);\n      }\n    }\n  } catch (e) {\n    // If we couldn't parse explicit error data, that's fine.\n  }\n\n  if (code === 'ok') {\n    // Technically, there's an edge case where a developer could explicitly\n    // return an error code of OK, and we will treat it as success, but that\n    // seems reasonable.\n    return null;\n  }\n\n  return new FunctionsError(code, description, details);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Provider } from '@firebase/component';\nimport { _isFirebaseServerApp, FirebaseApp } from '@firebase/app';\nimport {\n  AppCheckInternalComponentName,\n  FirebaseAppCheckInternal\n} from '@firebase/app-check-interop-types';\nimport {\n  MessagingInternal,\n  MessagingInternalComponentName\n} from '@firebase/messaging-interop-types';\nimport {\n  FirebaseAuthInternal,\n  FirebaseAuthInternalName\n} from '@firebase/auth-interop-types';\n\n/**\n * The metadata that should be supplied with function calls.\n * @internal\n */\nexport interface Context {\n  authToken?: string;\n  messagingToken?: string;\n  appCheckToken: string | null;\n}\n\n/**\n * Helper class to get metadata that should be included with a function call.\n * @internal\n */\nexport class ContextProvider {\n  private auth: FirebaseAuthInternal | null = null;\n  private messaging: MessagingInternal | null = null;\n  private appCheck: FirebaseAppCheckInternal | null = null;\n  private serverAppAppCheckToken: string | null = null;\n  constructor(\n    readonly app: FirebaseApp,\n    authProvider: Provider<FirebaseAuthInternalName>,\n    messagingProvider: Provider<MessagingInternalComponentName>,\n    appCheckProvider: Provider<AppCheckInternalComponentName>\n  ) {\n    if (_isFirebaseServerApp(app) && app.settings.appCheckToken) {\n      this.serverAppAppCheckToken = app.settings.appCheckToken;\n    }\n    this.auth = authProvider.getImmediate({ optional: true });\n    this.messaging = messagingProvider.getImmediate({\n      optional: true\n    });\n\n    if (!this.auth) {\n      authProvider.get().then(\n        auth => (this.auth = auth),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n\n    if (!this.messaging) {\n      messagingProvider.get().then(\n        messaging => (this.messaging = messaging),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n\n    if (!this.appCheck) {\n      appCheckProvider?.get().then(\n        appCheck => (this.appCheck = appCheck),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n  }\n\n  async getAuthToken(): Promise<string | undefined> {\n    if (!this.auth) {\n      return undefined;\n    }\n\n    try {\n      const token = await this.auth.getToken();\n      return token?.accessToken;\n    } catch (e) {\n      // If there's any error when trying to get the auth token, leave it off.\n      return undefined;\n    }\n  }\n\n  async getMessagingToken(): Promise<string | undefined> {\n    if (\n      !this.messaging ||\n      !('Notification' in self) ||\n      Notification.permission !== 'granted'\n    ) {\n      return undefined;\n    }\n\n    try {\n      return await this.messaging.getToken();\n    } catch (e) {\n      // We don't warn on this, because it usually means messaging isn't set up.\n      // console.warn('Failed to retrieve instance id token.', e);\n\n      // If there's any error when trying to get the token, leave it off.\n      return undefined;\n    }\n  }\n\n  async getAppCheckToken(\n    limitedUseAppCheckTokens?: boolean\n  ): Promise<string | null> {\n    if (this.serverAppAppCheckToken) {\n      return this.serverAppAppCheckToken;\n    }\n    if (this.appCheck) {\n      const result = limitedUseAppCheckTokens\n        ? await this.appCheck.getLimitedUseToken()\n        : await this.appCheck.getToken();\n      if (result.error) {\n        // Do not send the App Check header to the functions endpoint if\n        // there was an error from the App Check exchange endpoint. The App\n        // Check SDK will already have logged the error to console.\n        return null;\n      }\n      return result.token;\n    }\n    return null;\n  }\n\n  async getContext(limitedUseAppCheckTokens?: boolean): Promise<Context> {\n    const authToken = await this.getAuthToken();\n    const messagingToken = await this.getMessagingToken();\n    const appCheckToken = await this.getAppCheckToken(limitedUseAppCheckTokens);\n    return { authToken, messagingToken, appCheckToken };\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport {\n  HttpsCallable,\n  HttpsCallableResult,\n  HttpsCallableStreamResult,\n  HttpsCallableOptions,\n  HttpsCallableStreamOptions\n} from './public-types';\nimport { _errorForResponse, FunctionsError } from './error';\nimport { ContextProvider } from './context';\nimport { encode, decode } from './serializer';\nimport { Provider } from '@firebase/component';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { MessagingInternalComponentName } from '@firebase/messaging-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\n\nexport const DEFAULT_REGION = 'us-central1';\n\nconst responseLineRE = /^data: (.*?)(?:\\n|$)/;\n\n/**\n * The response to an http request.\n */\ninterface HttpResponse {\n  status: number;\n  json: HttpResponseBody | null;\n}\n/**\n * Describes the shape of the HttpResponse body.\n * It makes functions that would otherwise take {} able to access the\n * possible elements in the body more easily\n */\nexport interface HttpResponseBody {\n  data?: unknown;\n  result?: unknown;\n  error?: {\n    message?: unknown;\n    status?: unknown;\n    details?: unknown;\n  };\n}\n\ninterface CancellablePromise<T> {\n  promise: Promise<T>;\n  cancel: () => void;\n}\n\n/**\n * Returns a Promise that will be rejected after the given duration.\n * The error will be of type FunctionsError.\n *\n * @param millis Number of milliseconds to wait before rejecting.\n */\nfunction failAfter(millis: number): CancellablePromise<never> {\n  // Node timers and browser timers are fundamentally incompatible, but we\n  // don't care about the value here\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let timer: any | null = null;\n  return {\n    promise: new Promise((_, reject) => {\n      timer = setTimeout(() => {\n        reject(new FunctionsError('deadline-exceeded', 'deadline-exceeded'));\n      }, millis);\n    }),\n    cancel: () => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n    }\n  };\n}\n\n/**\n * The main class for the Firebase Functions SDK.\n * @internal\n */\nexport class FunctionsService implements _FirebaseService {\n  readonly contextProvider: ContextProvider;\n  emulatorOrigin: string | null = null;\n  cancelAllRequests: Promise<void>;\n  deleteService!: () => Promise<void>;\n  region: string;\n  customDomain: string | null;\n\n  /**\n   * Creates a new Functions service for the given app.\n   * @param app - The FirebaseApp to use.\n   */\n  constructor(\n    readonly app: FirebaseApp,\n    authProvider: Provider<FirebaseAuthInternalName>,\n    messagingProvider: Provider<MessagingInternalComponentName>,\n    appCheckProvider: Provider<AppCheckInternalComponentName>,\n    regionOrCustomDomain: string = DEFAULT_REGION,\n    readonly fetchImpl: typeof fetch = (...args) => fetch(...args)\n  ) {\n    this.contextProvider = new ContextProvider(\n      app,\n      authProvider,\n      messagingProvider,\n      appCheckProvider\n    );\n    // Cancels all ongoing requests when resolved.\n    this.cancelAllRequests = new Promise(resolve => {\n      this.deleteService = () => {\n        return Promise.resolve(resolve());\n      };\n    });\n\n    // Resolve the region or custom domain overload by attempting to parse it.\n    try {\n      const url = new URL(regionOrCustomDomain);\n      this.customDomain =\n        url.origin + (url.pathname === '/' ? '' : url.pathname);\n      this.region = DEFAULT_REGION;\n    } catch (e) {\n      this.customDomain = null;\n      this.region = regionOrCustomDomain;\n    }\n  }\n\n  _delete(): Promise<void> {\n    return this.deleteService();\n  }\n\n  /**\n   * Returns the URL for a callable with the given name.\n   * @param name - The name of the callable.\n   * @internal\n   */\n  _url(name: string): string {\n    const projectId = this.app.options.projectId;\n    if (this.emulatorOrigin !== null) {\n      const origin = this.emulatorOrigin;\n      return `${origin}/${projectId}/${this.region}/${name}`;\n    }\n\n    if (this.customDomain !== null) {\n      return `${this.customDomain}/${name}`;\n    }\n\n    return `https://${this.region}-${projectId}.cloudfunctions.net/${name}`;\n  }\n}\n\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host The emulator host (ex: localhost)\n * @param port The emulator port (ex: 5001)\n * @public\n */\nexport function connectFunctionsEmulator(\n  functionsInstance: FunctionsService,\n  host: string,\n  port: number\n): void {\n  functionsInstance.emulatorOrigin = `http://${host}:${port}`;\n}\n\n/**\n * Returns a reference to the callable https trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nexport function httpsCallable<RequestData, ResponseData, StreamData = unknown>(\n  functionsInstance: FunctionsService,\n  name: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  const callable = (\n    data?: RequestData | null\n  ): Promise<HttpsCallableResult> => {\n    return call(functionsInstance, name, data, options || {});\n  };\n\n  callable.stream = (\n    data?: RequestData | null,\n    options?: HttpsCallableStreamOptions\n  ) => {\n    return stream(functionsInstance, name, data, options);\n  };\n\n  return callable as HttpsCallable<RequestData, ResponseData, StreamData>;\n}\n\n/**\n * Returns a reference to the callable https trigger with the given url.\n * @param url - The url of the trigger.\n * @public\n */\nexport function httpsCallableFromURL<\n  RequestData,\n  ResponseData,\n  StreamData = unknown\n>(\n  functionsInstance: FunctionsService,\n  url: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  const callable = (\n    data?: RequestData | null\n  ): Promise<HttpsCallableResult> => {\n    return callAtURL(functionsInstance, url, data, options || {});\n  };\n\n  callable.stream = (\n    data?: RequestData | null,\n    options?: HttpsCallableStreamOptions\n  ) => {\n    return streamAtURL(functionsInstance, url, data, options || {});\n  };\n  return callable as HttpsCallable<RequestData, ResponseData, StreamData>;\n}\n\n/**\n * Does an HTTP POST and returns the completed response.\n * @param url The url to post to.\n * @param body The JSON body of the post.\n * @param headers The HTTP headers to include in the request.\n * @return A Promise that will succeed when the request finishes.\n */\nasync function postJSON(\n  url: string,\n  body: unknown,\n  headers: { [key: string]: string },\n  fetchImpl: typeof fetch\n): Promise<HttpResponse> {\n  headers['Content-Type'] = 'application/json';\n\n  let response: Response;\n  try {\n    response = await fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers\n    });\n  } catch (e) {\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    return {\n      status: 0,\n      json: null\n    };\n  }\n  let json: HttpResponseBody | null = null;\n  try {\n    json = await response.json();\n  } catch (e) {\n    // If we fail to parse JSON, it will fail the same as an empty body.\n  }\n  return {\n    status: response.status,\n    json\n  };\n}\n\n/**\n * Creates authorization headers for Firebase Functions requests.\n * @param functionsInstance The Firebase Functions service instance.\n * @param options Options for the callable function, including AppCheck token settings.\n * @return A Promise that resolves a headers map to include in outgoing fetch request.\n */\nasync function makeAuthHeaders(\n  functionsInstance: FunctionsService,\n  options: HttpsCallableOptions\n): Promise<Record<string, string>> {\n  const headers: Record<string, string> = {};\n  const context = await functionsInstance.contextProvider.getContext(\n    options.limitedUseAppCheckTokens\n  );\n  if (context.authToken) {\n    headers['Authorization'] = 'Bearer ' + context.authToken;\n  }\n  if (context.messagingToken) {\n    headers['Firebase-Instance-ID-Token'] = context.messagingToken;\n  }\n  if (context.appCheckToken !== null) {\n    headers['X-Firebase-AppCheck'] = context.appCheckToken;\n  }\n  return headers;\n}\n\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param name The name of the callable trigger.\n * @param data The data to pass as params to the function.\n */\nfunction call(\n  functionsInstance: FunctionsService,\n  name: string,\n  data: unknown,\n  options: HttpsCallableOptions\n): Promise<HttpsCallableResult> {\n  const url = functionsInstance._url(name);\n  return callAtURL(functionsInstance, url, data, options);\n}\n\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param url The url of the callable trigger.\n * @param data The data to pass as params to the function.\n */\nasync function callAtURL(\n  functionsInstance: FunctionsService,\n  url: string,\n  data: unknown,\n  options: HttpsCallableOptions\n): Promise<HttpsCallableResult> {\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = { data };\n\n  // Add a header for the authToken.\n  const headers = await makeAuthHeaders(functionsInstance, options);\n\n  // Default timeout to 70s, but let the options override it.\n  const timeout = options.timeout || 70000;\n\n  const failAfterHandle = failAfter(timeout);\n  const response = await Promise.race([\n    postJSON(url, body, headers, functionsInstance.fetchImpl),\n    failAfterHandle.promise,\n    functionsInstance.cancelAllRequests\n  ]);\n\n  // Always clear the failAfter timeout\n  failAfterHandle.cancel();\n\n  // If service was deleted, interrupted response throws an error.\n  if (!response) {\n    throw new FunctionsError(\n      'cancelled',\n      'Firebase Functions instance was deleted.'\n    );\n  }\n\n  // Check for an error status, regardless of http status.\n  const error = _errorForResponse(response.status, response.json);\n  if (error) {\n    throw error;\n  }\n\n  if (!response.json) {\n    throw new FunctionsError('internal', 'Response is not valid JSON object.');\n  }\n\n  let responseData = response.json.data;\n  // TODO(klimt): For right now, allow \"result\" instead of \"data\", for\n  // backwards compatibility.\n  if (typeof responseData === 'undefined') {\n    responseData = response.json.result;\n  }\n  if (typeof responseData === 'undefined') {\n    // Consider the response malformed.\n    throw new FunctionsError('internal', 'Response is missing data field.');\n  }\n\n  // Decode any special types, such as dates, in the returned data.\n  const decodedData = decode(responseData);\n\n  return { data: decodedData };\n}\n\n/**\n * Calls a callable function asynchronously and returns a streaming result.\n * @param name The name of the callable trigger.\n * @param data The data to pass as params to the function.\n * @param options Streaming request options.\n */\nfunction stream(\n  functionsInstance: FunctionsService,\n  name: string,\n  data: unknown,\n  options?: HttpsCallableStreamOptions\n): Promise<HttpsCallableStreamResult> {\n  const url = functionsInstance._url(name);\n  return streamAtURL(functionsInstance, url, data, options || {});\n}\n\n/**\n * Calls a callable function asynchronously and return a streaming result.\n * @param url The url of the callable trigger.\n * @param data The data to pass as params to the function.\n * @param options Streaming request options.\n */\nasync function streamAtURL(\n  functionsInstance: FunctionsService,\n  url: string,\n  data: unknown,\n  options: HttpsCallableStreamOptions\n): Promise<HttpsCallableStreamResult> {\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = { data };\n  //\n  // Add a header for the authToken.\n  const headers = await makeAuthHeaders(functionsInstance, options);\n  headers['Content-Type'] = 'application/json';\n  headers['Accept'] = 'text/event-stream';\n\n  let response: Response;\n  try {\n    response = await functionsInstance.fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers,\n      signal: options?.signal\n    });\n  } catch (e) {\n    if (e instanceof Error && e.name === 'AbortError') {\n      const error = new FunctionsError('cancelled', 'Request was cancelled.');\n      return {\n        data: Promise.reject(error),\n        stream: {\n          [Symbol.asyncIterator]() {\n            return {\n              next() {\n                return Promise.reject(error);\n              }\n            };\n          }\n        }\n      };\n    }\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    const error = _errorForResponse(0, null);\n    return {\n      data: Promise.reject(error),\n      // Return an empty async iterator\n      stream: {\n        [Symbol.asyncIterator]() {\n          return {\n            next() {\n              return Promise.reject(error);\n            }\n          };\n        }\n      }\n    };\n  }\n  let resultResolver: (value: unknown) => void;\n  let resultRejecter: (reason: unknown) => void;\n  const resultPromise = new Promise<unknown>((resolve, reject) => {\n    resultResolver = resolve;\n    resultRejecter = reject;\n  });\n  options?.signal?.addEventListener('abort', () => {\n    const error = new FunctionsError('cancelled', 'Request was cancelled.');\n    resultRejecter(error);\n  });\n  const reader = response.body!.getReader();\n  const rstream = createResponseStream(\n    reader,\n    resultResolver!,\n    resultRejecter!,\n    options?.signal\n  );\n  return {\n    stream: {\n      [Symbol.asyncIterator]() {\n        const rreader = rstream.getReader();\n        return {\n          async next() {\n            const { value, done } = await rreader.read();\n            return { value: value as unknown, done };\n          },\n          async return() {\n            await rreader.cancel();\n            return { done: true, value: undefined };\n          }\n        };\n      }\n    },\n    data: resultPromise\n  };\n}\n\n/**\n * Creates a ReadableStream that processes a streaming response from a streaming\n * callable function that returns data in server-sent event format.\n *\n * @param reader The underlying reader providing raw response data\n * @param resultResolver Callback to resolve the final result when received\n * @param resultRejecter Callback to reject with an error if encountered\n * @param signal Optional AbortSignal to cancel the stream processing\n * @returns A ReadableStream that emits decoded messages from the response\n *\n * The returned ReadableStream:\n *   1. Emits individual messages when \"message\" data is received\n *   2. Resolves with the final result when a \"result\" message is received\n *   3. Rejects with an error if an \"error\" message is received\n */\nfunction createResponseStream(\n  reader: ReadableStreamDefaultReader<Uint8Array>,\n  resultResolver: (value: unknown) => void,\n  resultRejecter: (reason: unknown) => void,\n  signal?: AbortSignal\n): ReadableStream<unknown> {\n  const processLine = (\n    line: string,\n    controller: ReadableStreamDefaultController\n  ): void => {\n    const match = line.match(responseLineRE);\n    // ignore all other lines (newline, comments, etc.)\n    if (!match) {\n      return;\n    }\n    const data = match[1];\n    try {\n      const jsonData = JSON.parse(data);\n      if ('result' in jsonData) {\n        resultResolver(decode(jsonData.result));\n        return;\n      }\n      if ('message' in jsonData) {\n        controller.enqueue(decode(jsonData.message));\n        return;\n      }\n      if ('error' in jsonData) {\n        const error = _errorForResponse(0, jsonData);\n        controller.error(error);\n        resultRejecter(error);\n        return;\n      }\n    } catch (error) {\n      if (error instanceof FunctionsError) {\n        controller.error(error);\n        resultRejecter(error);\n        return;\n      }\n      // ignore other parsing errors\n    }\n  };\n\n  const decoder = new TextDecoder();\n  return new ReadableStream({\n    start(controller) {\n      let currentText = '';\n      return pump();\n      async function pump(): Promise<void> {\n        if (signal?.aborted) {\n          const error = new FunctionsError(\n            'cancelled',\n            'Request was cancelled'\n          );\n          controller.error(error);\n          resultRejecter(error);\n          return Promise.resolve();\n        }\n        try {\n          const { value, done } = await reader.read();\n          if (done) {\n            if (currentText.trim()) {\n              processLine(currentText.trim(), controller);\n            }\n            controller.close();\n            return;\n          }\n          if (signal?.aborted) {\n            const error = new FunctionsError(\n              'cancelled',\n              'Request was cancelled'\n            );\n            controller.error(error);\n            resultRejecter(error);\n            await reader.cancel();\n            return;\n          }\n          currentText += decoder.decode(value, { stream: true });\n          const lines = currentText.split('\\n');\n          currentText = lines.pop() || '';\n          for (const line of lines) {\n            if (line.trim()) {\n              processLine(line.trim(), controller);\n            }\n          }\n          return pump();\n        } catch (error) {\n          const functionsError =\n            error instanceof FunctionsError\n              ? error\n              : _errorForResponse(0, null);\n          controller.error(functionsError);\n          resultRejecter(functionsError);\n        }\n      }\n    },\n    cancel() {\n      return reader.cancel();\n    }\n  });\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, registerVersion } from '@firebase/app';\nimport { FunctionsService } from './service';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactory\n} from '@firebase/component';\nimport { FUNCTIONS_TYPE } from './constants';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\nimport { MessagingInternalComponentName } from '@firebase/messaging-interop-types';\nimport { name, version } from '../package.json';\n\nconst AUTH_INTERNAL_NAME: FirebaseAuthInternalName = 'auth-internal';\nconst APP_CHECK_INTERNAL_NAME: AppCheckInternalComponentName =\n  'app-check-internal';\nconst MESSAGING_INTERNAL_NAME: MessagingInternalComponentName =\n  'messaging-internal';\n\nexport function registerFunctions(variant?: string): void {\n  const factory: InstanceFactory<'functions'> = (\n    container: ComponentContainer,\n    { instanceIdentifier: regionOrCustomDomain }\n  ) => {\n    // Dependencies\n    const app = container.getProvider('app').getImmediate();\n    const authProvider = container.getProvider(AUTH_INTERNAL_NAME);\n    const messagingProvider = container.getProvider(MESSAGING_INTERNAL_NAME);\n    const appCheckProvider = container.getProvider(APP_CHECK_INTERNAL_NAME);\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return new FunctionsService(\n      app,\n      authProvider,\n      messagingProvider,\n      appCheckProvider,\n      regionOrCustomDomain\n    );\n  };\n\n  _registerComponent(\n    new Component(\n      FUNCTIONS_TYPE,\n      factory,\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\nimport { FUNCTIONS_TYPE } from './constants';\n\nimport { Provider } from '@firebase/component';\nimport { Functions, HttpsCallableOptions, HttpsCallable } from './public-types';\nimport {\n  FunctionsService,\n  DEFAULT_REGION,\n  connectFunctionsEmulator as _connectFunctionsEmulator,\n  httpsCallable as _httpsCallable,\n  httpsCallableFromURL as _httpsCallableFromURL\n} from './service';\nimport {\n  getModularInstance,\n  getDefaultEmulatorHostnameAndPort\n} from '@firebase/util';\n\nexport { FunctionsError } from './error';\nexport * from './public-types';\n\n/**\n * Returns a {@link Functions} instance for the given app.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @param regionOrCustomDomain - one of:\n *   a) The region the callable functions are located in (ex: us-central1)\n *   b) A custom domain hosting the callable functions (ex: https://mydomain.com)\n * @public\n */\nexport function getFunctions(\n  app: FirebaseApp = getApp(),\n  regionOrCustomDomain: string = DEFAULT_REGION\n): Functions {\n  // Dependencies\n  const functionsProvider: Provider<'functions'> = _getProvider(\n    getModularInstance(app),\n    FUNCTIONS_TYPE\n  );\n  const functionsInstance = functionsProvider.getImmediate({\n    identifier: regionOrCustomDomain\n  });\n  const emulator = getDefaultEmulatorHostnameAndPort('functions');\n  if (emulator) {\n    connectFunctionsEmulator(functionsInstance, ...emulator);\n  }\n  return functionsInstance;\n}\n\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @public\n */\nexport function connectFunctionsEmulator(\n  functionsInstance: Functions,\n  host: string,\n  port: number\n): void {\n  _connectFunctionsEmulator(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    host,\n    port\n  );\n}\n\n/**\n * Returns a reference to the callable HTTPS trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nexport function httpsCallable<\n  RequestData = unknown,\n  ResponseData = unknown,\n  StreamData = unknown\n>(\n  functionsInstance: Functions,\n  name: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  return _httpsCallable<RequestData, ResponseData, StreamData>(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    name,\n    options\n  );\n}\n\n/**\n * Returns a reference to the callable HTTPS trigger with the specified url.\n * @param url - The url of the trigger.\n * @public\n */\nexport function httpsCallableFromURL<\n  RequestData = unknown,\n  ResponseData = unknown,\n  StreamData = unknown\n>(\n  functionsInstance: Functions,\n  url: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  return _httpsCallableFromURL<RequestData, ResponseData, StreamData>(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    url,\n    options\n  );\n}\n", "/**\n * Cloud Functions for Firebase\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { registerFunctions } from './config';\n\nexport * from './api';\nexport * from './public-types';\n\nregisterFunctions();\n"], "names": ["connectFunctionsEmulator", "httpsCallable", "httpsCallableFromURL", "_connectFunctionsEmulator", "_httpsCallable", "_httpsCallableFromURL"], "mappings": ";;;;AAAA;;;;;;;;;;;;;;;AAeG;AACH,MAAM,SAAS,GAAG,gDAAgD,CAAC;AACnE,MAAM,kBAAkB,GAAG,iDAAiD,CAAC;AAE7E,SAAS,SAAS;AAChB;AACA;AACA,CAAyB,EACzB,CAA6B,EAAA;IAE7B,MAAM,MAAM,GAA+B,EAAE,CAAC;AAC9C,IAAA,KAAK,MAAM,GAAG,IAAI,CAAC,EAAE;AACnB,QAAA,IAAI,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YACzB,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SACzB;KACF;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;AAKG;AACG,SAAU,MAAM,CAAC,IAAa,EAAA;AAClC,IAAA,IAAI,IAAI,IAAI,IAAI,EAAE;AAChB,QAAA,OAAO,IAAI,CAAC;KACb;AACD,IAAA,IAAI,IAAI,YAAY,MAAM,EAAE;AAC1B,QAAA,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;KACvB;IACD,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;;;AAG9C,QAAA,OAAO,IAAI,CAAC;KACb;IACD,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE;AACnC,QAAA,OAAO,IAAI,CAAC;KACb;AACD,IAAA,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,iBAAiB,EAAE;AAC9D,QAAA,OAAO,IAAI,CAAC;KACb;AACD,IAAA,IAAI,IAAI,YAAY,IAAI,EAAE;AACxB,QAAA,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;KAC3B;AACD,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACvB,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;KACjC;IACD,IAAI,OAAO,IAAI,KAAK,UAAU,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC1D,QAAA,OAAO,SAAS,CAAC,IAAK,EAAE,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;KACzC;;AAED,IAAA,MAAM,IAAI,KAAK,CAAC,kCAAkC,GAAG,IAAI,CAAC,CAAC;AAC7D,CAAC;AAED;;;;;AAKG;AACG,SAAU,MAAM,CAAC,IAAa,EAAA;AAClC,IAAA,IAAI,IAAI,IAAI,IAAI,EAAE;AAChB,QAAA,OAAO,IAAI,CAAC;KACb;AACD,IAAA,IAAK,IAAmC,CAAC,OAAO,CAAC,EAAE;AACjD,QAAA,QAAS,IAAmC,CAAC,OAAO,CAAC;AACnD,YAAA,KAAK,SAAS,CAAC;;YAEf,KAAK,kBAAkB,EAAE;;;;gBAIvB,MAAM,KAAK,GAAG,MAAM,CAAE,IAAmC,CAAC,OAAO,CAAC,CAAC,CAAC;AACpE,gBAAA,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE;AAChB,oBAAA,MAAM,IAAI,KAAK,CAAC,oCAAoC,GAAG,IAAI,CAAC,CAAC;iBAC9D;AACD,gBAAA,OAAO,KAAK,CAAC;aACd;YACD,SAAS;AACP,gBAAA,MAAM,IAAI,KAAK,CAAC,oCAAoC,GAAG,IAAI,CAAC,CAAC;aAC9D;SACF;KACF;AACD,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACvB,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;KACjC;IACD,IAAI,OAAO,IAAI,KAAK,UAAU,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC1D,QAAA,OAAO,SAAS,CAAC,IAAK,EAAE,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;KACzC;;AAED,IAAA,OAAO,IAAI,CAAC;AACd;;AC5GA;;;;;;;;;;;;;;;AAeG;AAEH;;AAEG;AACI,MAAM,cAAc,GAAG,WAAW;;ACpBzC;;;;;;;;;;;;;;;AAeG;AAQH;;;;;;AAMG;AACH,MAAM,YAAY,GAA2C;AAC3D,IAAA,EAAE,EAAE,IAAI;AACR,IAAA,SAAS,EAAE,WAAW;AACtB,IAAA,OAAO,EAAE,SAAS;AAClB,IAAA,gBAAgB,EAAE,kBAAkB;AACpC,IAAA,iBAAiB,EAAE,mBAAmB;AACtC,IAAA,SAAS,EAAE,WAAW;AACtB,IAAA,cAAc,EAAE,gBAAgB;AAChC,IAAA,iBAAiB,EAAE,mBAAmB;AACtC,IAAA,eAAe,EAAE,iBAAiB;AAClC,IAAA,kBAAkB,EAAE,oBAAoB;AACxC,IAAA,mBAAmB,EAAE,qBAAqB;AAC1C,IAAA,OAAO,EAAE,SAAS;AAClB,IAAA,YAAY,EAAE,cAAc;AAC5B,IAAA,aAAa,EAAE,eAAe;AAC9B,IAAA,QAAQ,EAAE,UAAU;AACpB,IAAA,WAAW,EAAE,aAAa;AAC1B,IAAA,SAAS,EAAE,WAAW;CACvB,CAAC;AAEF;;;;;;AAMG;AACG,MAAO,cAAe,SAAQ,aAAa,CAAA;AAC/C;;AAEG;AACH,IAAA,WAAA;AACE;;;AAGG;AACH,IAAA,IAAwB,EACxB,OAAgB;AAChB;;AAEG;IACM,OAAiB,EAAA;QAE1B,KAAK,CAAC,CAAG,EAAA,cAAc,CAAI,CAAA,EAAA,IAAI,CAAE,CAAA,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC;QAFzC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAU;;;QAM1B,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,SAAS,CAAC,CAAC;KACvD;AACF,CAAA;AAED;;;;;;;AAOG;AACH,SAAS,iBAAiB,CAAC,MAAc,EAAA;;IAEvC,IAAI,MAAM,IAAI,GAAG,IAAI,MAAM,GAAG,GAAG,EAAE;AACjC,QAAA,OAAO,IAAI,CAAC;KACb;IACD,QAAQ,MAAM;AACZ,QAAA,KAAK,CAAC;;AAEJ,YAAA,OAAO,UAAU,CAAC;AACpB,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,kBAAkB,CAAC;AAC5B,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,iBAAiB,CAAC;AAC3B,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,mBAAmB,CAAC;AAC7B,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,WAAW,CAAC;AACrB,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,SAAS,CAAC;AACnB,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,oBAAoB,CAAC;AAC9B,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,WAAW,CAAC;AACrB,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,UAAU,CAAC;AACpB,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,eAAe,CAAC;AACzB,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,aAAa,CAAC;AACvB,QAAA,KAAK,GAAG;AACN,YAAA,OAAO,mBAAmB,CAAC;KAE9B;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;AAEG;AACa,SAAA,iBAAiB,CAC/B,MAAc,EACd,QAAiC,EAAA;AAEjC,IAAA,IAAI,IAAI,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;;IAGrC,IAAI,WAAW,GAAW,IAAI,CAAC;IAE/B,IAAI,OAAO,GAAY,SAAS,CAAC;;AAGjC,IAAA,IAAI;AACF,QAAA,MAAM,SAAS,GAAG,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC;QAC7C,IAAI,SAAS,EAAE;AACb,YAAA,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;AAChC,YAAA,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAC9B,gBAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;;AAEzB,oBAAA,OAAO,IAAI,cAAc,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;iBACnD;AACD,gBAAA,IAAI,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;;;gBAI5B,WAAW,GAAG,MAAM,CAAC;aACtB;AAED,YAAA,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;AAClC,YAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;gBAC/B,WAAW,GAAG,OAAO,CAAC;aACvB;AAED,YAAA,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;AAC5B,YAAA,IAAI,OAAO,KAAK,SAAS,EAAE;AACzB,gBAAA,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;aAC3B;SACF;KACF;IAAC,OAAO,CAAC,EAAE;;KAEX;AAED,IAAA,IAAI,IAAI,KAAK,IAAI,EAAE;;;;AAIjB,QAAA,OAAO,IAAI,CAAC;KACb;IAED,OAAO,IAAI,cAAc,CAAC,IAAI,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;AACxD;;AClLA;;;;;;;;;;;;;;;AAeG;AA2BH;;;AAGG;MACU,eAAe,CAAA;AAK1B,IAAA,WAAA,CACW,GAAgB,EACzB,YAAgD,EAChD,iBAA2D,EAC3D,gBAAyD,EAAA;QAHhD,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;QALnB,IAAI,CAAA,IAAA,GAAgC,IAAI,CAAC;QACzC,IAAS,CAAA,SAAA,GAA6B,IAAI,CAAC;QAC3C,IAAQ,CAAA,QAAA,GAAoC,IAAI,CAAC;QACjD,IAAsB,CAAA,sBAAA,GAAkB,IAAI,CAAC;QAOnD,IAAI,oBAAoB,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,aAAa,EAAE;YAC3D,IAAI,CAAC,sBAAsB,GAAG,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC;SAC1D;AACD,QAAA,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,YAAY,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1D,QAAA,IAAI,CAAC,SAAS,GAAG,iBAAiB,CAAC,YAAY,CAAC;AAC9C,YAAA,QAAQ,EAAE,IAAI;AACf,SAAA,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AACd,YAAA,YAAY,CAAC,GAAG,EAAE,CAAC,IAAI,CACrB,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,EAC1B,MAAK;;AAEL,aAAC,CACF,CAAC;SACH;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,iBAAiB,CAAC,GAAG,EAAE,CAAC,IAAI,CAC1B,SAAS,KAAK,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,EACzC,MAAK;;AAEL,aAAC,CACF,CAAC;SACH;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,gBAAgB,KAAA,IAAA,IAAhB,gBAAgB,KAAhB,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,gBAAgB,CAAE,GAAG,EAAA,CAAG,IAAI,CAC1B,QAAQ,KAAK,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,EACtC,MAAK;;AAEL,aAAC,CACF,CAAC;SACH;KACF;AAED,IAAA,MAAM,YAAY,GAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AACd,YAAA,OAAO,SAAS,CAAC;SAClB;AAED,QAAA,IAAI;YACF,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;AACzC,YAAA,OAAO,KAAK,KAAL,IAAA,IAAA,KAAK,uBAAL,KAAK,CAAE,WAAW,CAAC;SAC3B;QAAC,OAAO,CAAC,EAAE;;AAEV,YAAA,OAAO,SAAS,CAAC;SAClB;KACF;AAED,IAAA,MAAM,iBAAiB,GAAA;QACrB,IACE,CAAC,IAAI,CAAC,SAAS;AACf,YAAA,EAAE,cAAc,IAAI,IAAI,CAAC;AACzB,YAAA,YAAY,CAAC,UAAU,KAAK,SAAS,EACrC;AACA,YAAA,OAAO,SAAS,CAAC;SAClB;AAED,QAAA,IAAI;AACF,YAAA,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;SACxC;QAAC,OAAO,CAAC,EAAE;;;;AAKV,YAAA,OAAO,SAAS,CAAC;SAClB;KACF;IAED,MAAM,gBAAgB,CACpB,wBAAkC,EAAA;AAElC,QAAA,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC/B,OAAO,IAAI,CAAC,sBAAsB,CAAC;SACpC;AACD,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,MAAM,MAAM,GAAG,wBAAwB;AACrC,kBAAE,MAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE;kBACxC,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;AACnC,YAAA,IAAI,MAAM,CAAC,KAAK,EAAE;;;;AAIhB,gBAAA,OAAO,IAAI,CAAC;aACb;YACD,OAAO,MAAM,CAAC,KAAK,CAAC;SACrB;AACD,QAAA,OAAO,IAAI,CAAC;KACb;IAED,MAAM,UAAU,CAAC,wBAAkC,EAAA;AACjD,QAAA,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;AAC5C,QAAA,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACtD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;AAC5E,QAAA,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,aAAa,EAAE,CAAC;KACrD;AACF;;AC1JD;;;;;;;;;;;;;;;AAeG;AAkBI,MAAM,cAAc,GAAG,aAAa,CAAC;AAE5C,MAAM,cAAc,GAAG,sBAAsB,CAAC;AA6B9C;;;;;AAKG;AACH,SAAS,SAAS,CAAC,MAAc,EAAA;;;;IAI/B,IAAI,KAAK,GAAe,IAAI,CAAC;IAC7B,OAAO;QACL,OAAO,EAAE,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,KAAI;AACjC,YAAA,KAAK,GAAG,UAAU,CAAC,MAAK;gBACtB,MAAM,CAAC,IAAI,cAAc,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,CAAC,CAAC;aACtE,EAAE,MAAM,CAAC,CAAC;AACb,SAAC,CAAC;QACF,MAAM,EAAE,MAAK;YACX,IAAI,KAAK,EAAE;gBACT,YAAY,CAAC,KAAK,CAAC,CAAC;aACrB;SACF;KACF,CAAC;AACJ,CAAC;AAED;;;AAGG;MACU,gBAAgB,CAAA;AAQ3B;;;AAGG;IACH,WACW,CAAA,GAAgB,EACzB,YAAgD,EAChD,iBAA2D,EAC3D,gBAAyD,EACzD,oBAAA,GAA+B,cAAc,EACpC,YAA0B,CAAC,GAAG,IAAI,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC,EAAA;QALrD,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;QAKhB,IAAS,CAAA,SAAA,GAAT,SAAS,CAA4C;QAhBhE,IAAc,CAAA,cAAA,GAAkB,IAAI,CAAC;AAkBnC,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,CACxC,GAAG,EACH,YAAY,EACZ,iBAAiB,EACjB,gBAAgB,CACjB,CAAC;;QAEF,IAAI,CAAC,iBAAiB,GAAG,IAAI,OAAO,CAAC,OAAO,IAAG;AAC7C,YAAA,IAAI,CAAC,aAAa,GAAG,MAAK;AACxB,gBAAA,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;AACpC,aAAC,CAAC;AACJ,SAAC,CAAC,CAAC;;AAGH,QAAA,IAAI;AACF,YAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,oBAAoB,CAAC,CAAC;AAC1C,YAAA,IAAI,CAAC,YAAY;gBACf,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,QAAQ,KAAK,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC1D,YAAA,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC;SAC9B;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AACzB,YAAA,IAAI,CAAC,MAAM,GAAG,oBAAoB,CAAC;SACpC;KACF;IAED,OAAO,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;KAC7B;AAED;;;;AAIG;AACH,IAAA,IAAI,CAAC,IAAY,EAAA;QACf,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC;AAC7C,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;AAChC,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC;YACnC,OAAO,CAAA,EAAG,MAAM,CAAA,CAAA,EAAI,SAAS,CAAA,CAAA,EAAI,IAAI,CAAC,MAAM,CAAA,CAAA,EAAI,IAAI,CAAA,CAAE,CAAC;SACxD;AAED,QAAA,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE;AAC9B,YAAA,OAAO,GAAG,IAAI,CAAC,YAAY,CAAI,CAAA,EAAA,IAAI,EAAE,CAAC;SACvC;QAED,OAAO,CAAA,QAAA,EAAW,IAAI,CAAC,MAAM,IAAI,SAAS,CAAA,oBAAA,EAAuB,IAAI,CAAA,CAAE,CAAC;KACzE;AACF,CAAA;AAED;;;;;;;;AAQG;SACaA,0BAAwB,CACtC,iBAAmC,EACnC,IAAY,EACZ,IAAY,EAAA;IAEZ,iBAAiB,CAAC,cAAc,GAAG,CAAA,OAAA,EAAU,IAAI,CAAI,CAAA,EAAA,IAAI,EAAE,CAAC;AAC9D,CAAC;AAED;;;;AAIG;SACaC,eAAa,CAC3B,iBAAmC,EACnC,IAAY,EACZ,OAA8B,EAAA;AAE9B,IAAA,MAAM,QAAQ,GAAG,CACf,IAAyB,KACO;AAChC,QAAA,OAAO,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC;AAC5D,KAAC,CAAC;IAEF,QAAQ,CAAC,MAAM,GAAG,CAChB,IAAyB,EACzB,OAAoC,KAClC;QACF,OAAO,MAAM,CAAC,iBAAiB,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AACxD,KAAC,CAAC;AAEF,IAAA,OAAO,QAAgE,CAAC;AAC1E,CAAC;AAED;;;;AAIG;SACaC,sBAAoB,CAKlC,iBAAmC,EACnC,GAAW,EACX,OAA8B,EAAA;AAE9B,IAAA,MAAM,QAAQ,GAAG,CACf,IAAyB,KACO;AAChC,QAAA,OAAO,SAAS,CAAC,iBAAiB,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC;AAChE,KAAC,CAAC;IAEF,QAAQ,CAAC,MAAM,GAAG,CAChB,IAAyB,EACzB,OAAoC,KAClC;AACF,QAAA,OAAO,WAAW,CAAC,iBAAiB,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC;AAClE,KAAC,CAAC;AACF,IAAA,OAAO,QAAgE,CAAC;AAC1E,CAAC;AAED;;;;;;AAMG;AACH,eAAe,QAAQ,CACrB,GAAW,EACX,IAAa,EACb,OAAkC,EAClC,SAAuB,EAAA;AAEvB,IAAA,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAC;AAE7C,IAAA,IAAI,QAAkB,CAAC;AACvB,IAAA,IAAI;AACF,QAAA,QAAQ,GAAG,MAAM,SAAS,CAAC,GAAG,EAAE;AAC9B,YAAA,MAAM,EAAE,MAAM;AACd,YAAA,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAC1B,OAAO;AACR,SAAA,CAAC,CAAC;KACJ;IAAC,OAAO,CAAC,EAAE;;;;;QAKV,OAAO;AACL,YAAA,MAAM,EAAE,CAAC;AACT,YAAA,IAAI,EAAE,IAAI;SACX,CAAC;KACH;IACD,IAAI,IAAI,GAA4B,IAAI,CAAC;AACzC,IAAA,IAAI;AACF,QAAA,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;KAC9B;IAAC,OAAO,CAAC,EAAE;;KAEX;IACD,OAAO;QACL,MAAM,EAAE,QAAQ,CAAC,MAAM;QACvB,IAAI;KACL,CAAC;AACJ,CAAC;AAED;;;;;AAKG;AACH,eAAe,eAAe,CAC5B,iBAAmC,EACnC,OAA6B,EAAA;IAE7B,MAAM,OAAO,GAA2B,EAAE,CAAC;AAC3C,IAAA,MAAM,OAAO,GAAG,MAAM,iBAAiB,CAAC,eAAe,CAAC,UAAU,CAChE,OAAO,CAAC,wBAAwB,CACjC,CAAC;AACF,IAAA,IAAI,OAAO,CAAC,SAAS,EAAE;QACrB,OAAO,CAAC,eAAe,CAAC,GAAG,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;KAC1D;AACD,IAAA,IAAI,OAAO,CAAC,cAAc,EAAE;AAC1B,QAAA,OAAO,CAAC,4BAA4B,CAAC,GAAG,OAAO,CAAC,cAAc,CAAC;KAChE;AACD,IAAA,IAAI,OAAO,CAAC,aAAa,KAAK,IAAI,EAAE;AAClC,QAAA,OAAO,CAAC,qBAAqB,CAAC,GAAG,OAAO,CAAC,aAAa,CAAC;KACxD;AACD,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;AAIG;AACH,SAAS,IAAI,CACX,iBAAmC,EACnC,IAAY,EACZ,IAAa,EACb,OAA6B,EAAA;IAE7B,MAAM,GAAG,GAAG,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzC,OAAO,SAAS,CAAC,iBAAiB,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC1D,CAAC;AAED;;;;AAIG;AACH,eAAe,SAAS,CACtB,iBAAmC,EACnC,GAAW,EACX,IAAa,EACb,OAA6B,EAAA;;AAG7B,IAAA,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AACpB,IAAA,MAAM,IAAI,GAAG,EAAE,IAAI,EAAE,CAAC;;IAGtB,MAAM,OAAO,GAAG,MAAM,eAAe,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;;AAGlE,IAAA,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,KAAK,CAAC;AAEzC,IAAA,MAAM,eAAe,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;AAC3C,IAAA,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;QAClC,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,iBAAiB,CAAC,SAAS,CAAC;AACzD,QAAA,eAAe,CAAC,OAAO;AACvB,QAAA,iBAAiB,CAAC,iBAAiB;AACpC,KAAA,CAAC,CAAC;;IAGH,eAAe,CAAC,MAAM,EAAE,CAAC;;IAGzB,IAAI,CAAC,QAAQ,EAAE;AACb,QAAA,MAAM,IAAI,cAAc,CACtB,WAAW,EACX,0CAA0C,CAC3C,CAAC;KACH;;AAGD,IAAA,MAAM,KAAK,GAAG,iBAAiB,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;IAChE,IAAI,KAAK,EAAE;AACT,QAAA,MAAM,KAAK,CAAC;KACb;AAED,IAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AAClB,QAAA,MAAM,IAAI,cAAc,CAAC,UAAU,EAAE,oCAAoC,CAAC,CAAC;KAC5E;AAED,IAAA,IAAI,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;;;AAGtC,IAAA,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AACvC,QAAA,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;KACrC;AACD,IAAA,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;;AAEvC,QAAA,MAAM,IAAI,cAAc,CAAC,UAAU,EAAE,iCAAiC,CAAC,CAAC;KACzE;;AAGD,IAAA,MAAM,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC;AAEzC,IAAA,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;AAC/B,CAAC;AAED;;;;;AAKG;AACH,SAAS,MAAM,CACb,iBAAmC,EACnC,IAAY,EACZ,IAAa,EACb,OAAoC,EAAA;IAEpC,MAAM,GAAG,GAAG,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzC,IAAA,OAAO,WAAW,CAAC,iBAAiB,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC;AAClE,CAAC;AAED;;;;;AAKG;AACH,eAAe,WAAW,CACxB,iBAAmC,EACnC,GAAW,EACX,IAAa,EACb,OAAmC,EAAA;;;AAGnC,IAAA,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AACpB,IAAA,MAAM,IAAI,GAAG,EAAE,IAAI,EAAE,CAAC;;;IAGtB,MAAM,OAAO,GAAG,MAAM,eAAe,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;AAClE,IAAA,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAC;AAC7C,IAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,mBAAmB,CAAC;AAExC,IAAA,IAAI,QAAkB,CAAC;AACvB,IAAA,IAAI;AACF,QAAA,QAAQ,GAAG,MAAM,iBAAiB,CAAC,SAAS,CAAC,GAAG,EAAE;AAChD,YAAA,MAAM,EAAE,MAAM;AACd,YAAA,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAC1B,OAAO;AACP,YAAA,MAAM,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,MAAM;AACxB,SAAA,CAAC,CAAC;KACJ;IAAC,OAAO,CAAC,EAAE;QACV,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,CAAC,IAAI,KAAK,YAAY,EAAE;YACjD,MAAM,KAAK,GAAG,IAAI,cAAc,CAAC,WAAW,EAAE,wBAAwB,CAAC,CAAC;YACxE,OAAO;AACL,gBAAA,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;AAC3B,gBAAA,MAAM,EAAE;oBACN,CAAC,MAAM,CAAC,aAAa,CAAC,GAAA;wBACpB,OAAO;4BACL,IAAI,GAAA;AACF,gCAAA,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;6BAC9B;yBACF,CAAC;qBACH;AACF,iBAAA;aACF,CAAC;SACH;;;;;QAKD,MAAM,KAAK,GAAG,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACzC,OAAO;AACL,YAAA,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;;AAE3B,YAAA,MAAM,EAAE;gBACN,CAAC,MAAM,CAAC,aAAa,CAAC,GAAA;oBACpB,OAAO;wBACL,IAAI,GAAA;AACF,4BAAA,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;yBAC9B;qBACF,CAAC;iBACH;AACF,aAAA;SACF,CAAC;KACH;AACD,IAAA,IAAI,cAAwC,CAAC;AAC7C,IAAA,IAAI,cAAyC,CAAC;IAC9C,MAAM,aAAa,GAAG,IAAI,OAAO,CAAU,CAAC,OAAO,EAAE,MAAM,KAAI;QAC7D,cAAc,GAAG,OAAO,CAAC;QACzB,cAAc,GAAG,MAAM,CAAC;AAC1B,KAAC,CAAC,CAAC;AACH,IAAA,CAAA,EAAA,GAAA,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,gBAAgB,CAAC,OAAO,EAAE,MAAK;QAC9C,MAAM,KAAK,GAAG,IAAI,cAAc,CAAC,WAAW,EAAE,wBAAwB,CAAC,CAAC;QACxE,cAAc,CAAC,KAAK,CAAC,CAAC;AACxB,KAAC,CAAC,CAAC;IACH,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAK,CAAC,SAAS,EAAE,CAAC;AAC1C,IAAA,MAAM,OAAO,GAAG,oBAAoB,CAClC,MAAM,EACN,cAAe,EACf,cAAe,EACf,OAAO,aAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,MAAM,CAChB,CAAC;IACF,OAAO;AACL,QAAA,MAAM,EAAE;YACN,CAAC,MAAM,CAAC,aAAa,CAAC,GAAA;AACpB,gBAAA,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;gBACpC,OAAO;AACL,oBAAA,MAAM,IAAI,GAAA;wBACR,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;AAC7C,wBAAA,OAAO,EAAE,KAAK,EAAE,KAAgB,EAAE,IAAI,EAAE,CAAC;qBAC1C;AACD,oBAAA,MAAM,MAAM,GAAA;AACV,wBAAA,MAAM,OAAO,CAAC,MAAM,EAAE,CAAC;wBACvB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;qBACzC;iBACF,CAAC;aACH;AACF,SAAA;AACD,QAAA,IAAI,EAAE,aAAa;KACpB,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;AAcG;AACH,SAAS,oBAAoB,CAC3B,MAA+C,EAC/C,cAAwC,EACxC,cAAyC,EACzC,MAAoB,EAAA;AAEpB,IAAA,MAAM,WAAW,GAAG,CAClB,IAAY,EACZ,UAA2C,KACnC;QACR,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;;QAEzC,IAAI,CAAC,KAAK,EAAE;YACV,OAAO;SACR;AACD,QAAA,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACtB,QAAA,IAAI;YACF,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAClC,YAAA,IAAI,QAAQ,IAAI,QAAQ,EAAE;gBACxB,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;gBACxC,OAAO;aACR;AACD,YAAA,IAAI,SAAS,IAAI,QAAQ,EAAE;gBACzB,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;gBAC7C,OAAO;aACR;AACD,YAAA,IAAI,OAAO,IAAI,QAAQ,EAAE;gBACvB,MAAM,KAAK,GAAG,iBAAiB,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC7C,gBAAA,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACxB,cAAc,CAAC,KAAK,CAAC,CAAC;gBACtB,OAAO;aACR;SACF;QAAC,OAAO,KAAK,EAAE;AACd,YAAA,IAAI,KAAK,YAAY,cAAc,EAAE;AACnC,gBAAA,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACxB,cAAc,CAAC,KAAK,CAAC,CAAC;gBACtB,OAAO;aACR;;SAEF;AACH,KAAC,CAAC;AAEF,IAAA,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;IAClC,OAAO,IAAI,cAAc,CAAC;AACxB,QAAA,KAAK,CAAC,UAAU,EAAA;YACd,IAAI,WAAW,GAAG,EAAE,CAAC;YACrB,OAAO,IAAI,EAAE,CAAC;AACd,YAAA,eAAe,IAAI,GAAA;gBACjB,IAAI,MAAM,aAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAE,OAAO,EAAE;oBACnB,MAAM,KAAK,GAAG,IAAI,cAAc,CAC9B,WAAW,EACX,uBAAuB,CACxB,CAAC;AACF,oBAAA,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBACxB,cAAc,CAAC,KAAK,CAAC,CAAC;AACtB,oBAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;iBAC1B;AACD,gBAAA,IAAI;oBACF,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;oBAC5C,IAAI,IAAI,EAAE;AACR,wBAAA,IAAI,WAAW,CAAC,IAAI,EAAE,EAAE;4BACtB,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,UAAU,CAAC,CAAC;yBAC7C;wBACD,UAAU,CAAC,KAAK,EAAE,CAAC;wBACnB,OAAO;qBACR;oBACD,IAAI,MAAM,aAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAE,OAAO,EAAE;wBACnB,MAAM,KAAK,GAAG,IAAI,cAAc,CAC9B,WAAW,EACX,uBAAuB,CACxB,CAAC;AACF,wBAAA,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;wBACxB,cAAc,CAAC,KAAK,CAAC,CAAC;AACtB,wBAAA,MAAM,MAAM,CAAC,MAAM,EAAE,CAAC;wBACtB,OAAO;qBACR;AACD,oBAAA,WAAW,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;oBACvD,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACtC,oBAAA,WAAW,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;AAChC,oBAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AACxB,wBAAA,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE;4BACf,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,UAAU,CAAC,CAAC;yBACtC;qBACF;oBACD,OAAO,IAAI,EAAE,CAAC;iBACf;gBAAC,OAAO,KAAK,EAAE;AACd,oBAAA,MAAM,cAAc,GAClB,KAAK,YAAY,cAAc;AAC7B,0BAAE,KAAK;AACP,0BAAE,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACjC,oBAAA,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;oBACjC,cAAc,CAAC,cAAc,CAAC,CAAC;iBAChC;aACF;SACF;QACD,MAAM,GAAA;AACJ,YAAA,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC;SACxB;AACF,KAAA,CAAC,CAAC;AACL;;;;;ACxmBA;;;;;;;;;;;;;;;AAeG;AAgBH,MAAM,kBAAkB,GAA6B,eAAe,CAAC;AACrE,MAAM,uBAAuB,GAC3B,oBAAoB,CAAC;AACvB,MAAM,uBAAuB,GAC3B,oBAAoB,CAAC;AAEjB,SAAU,iBAAiB,CAAC,OAAgB,EAAA;IAChD,MAAM,OAAO,GAAiC,CAC5C,SAA6B,EAC7B,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,KAC1C;;QAEF,MAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,CAAC;QACxD,MAAM,YAAY,GAAG,SAAS,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;QAC/D,MAAM,iBAAiB,GAAG,SAAS,CAAC,WAAW,CAAC,uBAAuB,CAAC,CAAC;QACzE,MAAM,gBAAgB,GAAG,SAAS,CAAC,WAAW,CAAC,uBAAuB,CAAC,CAAC;;AAGxE,QAAA,OAAO,IAAI,gBAAgB,CACzB,GAAG,EACH,YAAY,EACZ,iBAAiB,EACjB,gBAAgB,EAChB,oBAAoB,CACrB,CAAC;AACJ,KAAC,CAAC;AAEF,IAAA,kBAAkB,CAChB,IAAI,SAAS,CACX,cAAc,EACd,OAAO,EAER,QAAA,4BAAA,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAC7B,CAAC;AAEF,IAAA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;;AAExC,IAAA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,SAAkB,CAAC,CAAC;AACrD;;ACrEA;;;;;;;;;;;;;;;AAeG;AAsBH;;;;;;;AAOG;AACG,SAAU,YAAY,CAC1B,GAAA,GAAmB,MAAM,EAAE,EAC3B,uBAA+B,cAAc,EAAA;;IAG7C,MAAM,iBAAiB,GAA0B,YAAY,CAC3D,kBAAkB,CAAC,GAAG,CAAC,EACvB,cAAc,CACf,CAAC;AACF,IAAA,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,YAAY,CAAC;AACvD,QAAA,UAAU,EAAE,oBAAoB;AACjC,KAAA,CAAC,CAAC;AACH,IAAA,MAAM,QAAQ,GAAG,iCAAiC,CAAC,WAAW,CAAC,CAAC;IAChE,IAAI,QAAQ,EAAE;AACZ,QAAA,wBAAwB,CAAC,iBAAiB,EAAE,GAAG,QAAQ,CAAC,CAAC;KAC1D;AACD,IAAA,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAED;;;;;;;;AAQG;SACa,wBAAwB,CACtC,iBAA4B,EAC5B,IAAY,EACZ,IAAY,EAAA;IAEZC,0BAAyB,CACvB,kBAAkB,CAAmB,iBAAqC,CAAC,EAC3E,IAAI,EACJ,IAAI,CACL,CAAC;AACJ,CAAC;AAED;;;;AAIG;SACa,aAAa,CAK3B,iBAA4B,EAC5B,IAAY,EACZ,OAA8B,EAAA;IAE9B,OAAOC,eAAc,CACnB,kBAAkB,CAAmB,iBAAqC,CAAC,EAC3E,IAAI,EACJ,OAAO,CACR,CAAC;AACJ,CAAC;AAED;;;;AAIG;SACa,oBAAoB,CAKlC,iBAA4B,EAC5B,GAAW,EACX,OAA8B,EAAA;IAE9B,OAAOC,sBAAqB,CAC1B,kBAAkB,CAAmB,iBAAqC,CAAC,EAC3E,GAAG,EACH,OAAO,CACR,CAAC;AACJ;;AC7HA;;;;AAIG;AAuBH,iBAAiB,EAAE;;;;"}