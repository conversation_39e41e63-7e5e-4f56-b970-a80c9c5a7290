{"name": "cytoscape", "version": "3.31.3", "license": "MIT", "description": "Graph theory (a.k.a. network) library for analysis and visualisation", "homepage": "http://js.cytoscape.org", "repository": {"type": "git", "url": "https://github.com/cytoscape/cytoscape.js.git"}, "bugs": {"url": "https://github.com/cytoscape/cytoscape.js/issues"}, "keywords": ["graph", "graph-theory", "network", "node", "edge", "vertex", "link", "analysis", "visualisation", "visualization", "draw", "render", "biojs", "cytoscape"], "engines": {"node": ">=0.10"}, "types": "index.d.ts", "main": "dist/cytoscape.cjs.js", "module": "dist/cytoscape.esm.mjs", "exports": {".": {"import": "./dist/cytoscape.esm.mjs", "require": "./dist/cytoscape.cjs.js", "types": "./index.d.ts"}, "./dist/cytoscape.esm": {"import": "./dist/cytoscape.esm.mjs"}, "./dist/cytoscape.esm.min": {"import": "./dist/cytoscape.esm.min.mjs"}, "./dist/cytoscape.umd.js": {"import": "./dist/cytoscape.umd.js", "require": "./dist/cytoscape.umd.js"}, "./dist/*": {"import": "./dist/*.js", "require": "./dist/*.js"}}, "unpkg": "dist/cytoscape.min.js", "jsdelivr": "dist/cytoscape.min.js", "scripts": {"postinstall": "npx playwright install", "lint": "eslint src/**/*.mjs", "build": "rollup -c", "build:esm": "cross-env FILE=esm rollup -c", "build:esm.min": "cross-env FILE=esm.min rollup -c", "build:cjs": "cross-env FILE=cjs rollup -c", "build:umd": "cross-env FILE=umd rollup -c", "build:min": "cross-env FILE=min rollup -c", "clean": "rimraf build/*", "copyright": "node license-update.mjs", "dist:copy": "cpy --flat build/cytoscape.umd.js build/cytoscape.min.js build/cytoscape.cjs.js build/cytoscape.esm.mjs build/cytoscape.esm.min.mjs dist", "dist": "cross-env NODE_ENV=production run-s build dist:*", "release": "run-s copyright dist docs", "watch": "run-s watch:fast", "watch:sync": "livereload \"build, debug\" -w 500", "watch:http": "http-server -p 3333 -s -c -1 -o debug", "watch:fast": "run-p watch:sync watch:http watch:build:fast", "watch:umd": "run-p watch:sync watch:http watch:build:umd", "watch:build:fast": "cross-env FILE=umd SOURCEMAPS=true BABEL=false NODE_ENV=development rollup -c -w", "watch:build:umd": "cross-env FILE=umd SOURCEMAPS=true NODE_ENV=development rollup -c -w", "watch:build:cjs": "cross-env FILE=cjs SOURCEMAPS=true NODE_ENV=development rollup -c -w", "test": "run-s test:js test:modules test:playwright lint", "test:js": "mocha  --recursive", "test:js:debug": "mocha inspect  --recursive", "test:build": "cross-env TEST_BUILD=true mocha", "test:modules": "mocha  test/modules", "test:modules:debug": "mocha inspect  test/modules", "test:playwright:http": "http-server -p 3333 -s -c -1", "test:playwright:build": "cross-env FILE=umd SOURCEMAPS=true BABEL=false NODE_ENV=development rollup -c", "test:playwright:setup": "run-s test:playwright:build test:playwright:http", "test:playwright": "playwright test", "docs": "run-s docs:build docs:js", "docs:js": "cpy --flat build/cytoscape.min.js documentation/js", "docs:build": "node documentation/docmaker.mjs", "docs:push": "gh-pages -d documentation", "benchmark": "run-s benchmark:all", "benchmark:download": "download https://raw.githubusercontent.com/cytoscape/cytoscape.js/master/dist/cytoscape.cjs.js --out build --filename cytoscape.benchmark.js", "benchmark:all:exec": "node benchmark/all", "benchmark:all": "run-s benchmark:download benchmark:all:exec", "benchmark:single:exec": "node benchmark/single", "benchmark:single": "run-s benchmark:download benchmark:single:exec"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@eslint/js": "^9.18.0", "@playwright/test": "^1.49.1", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.2", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@types/node": "^22.10.7", "benchmark": "^2.1.4", "bluebird": "^3.5.0", "chai": "^5.1.2", "cpy-cli": "^5.0.0", "cross-env": "^7.0.0", "download-cli": "^1.0.5", "eslint": "^9.18.0", "gh-pages": "^5.0.0", "gl-matrix": "^3.4.3", "globals": "^15.14.0", "handlebars": "^4.7.6", "heap": "^0.2.7", "highlight.js": "^10.0.0", "http-server": "^14.1.1", "jsonlint": "^1.6.2", "livereload": "^0.9.1", "lodash": "^4.17.21", "marked": "^4.0.10", "mocha": "^11.1.0", "npm-run-all": "^4.1.5", "rimraf": "^3.0.0", "rollup": "^4.31.0", "rollup-plugin-license": "^3.5.3"}}