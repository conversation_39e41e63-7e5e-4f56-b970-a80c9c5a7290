/** @license
Copyright The Closure Library Authors.
SPDX-License-Identifier: Apache-2.0
*/
(function() {'use strict';var n;function aa(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}var ba="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
function ca(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");}var p=ca(this);function r(a,b){if(b)a:{var c=p;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&ba(c,a,{configurable:!0,writable:!0,value:b})}}
r("Symbol",function(a){function b(f){if(this instanceof b)throw new TypeError("Symbol is not a constructor");return new c(d+(f||"")+"_"+e++,f)}function c(f,g){this.g=f;ba(this,"description",{configurable:!0,writable:!0,value:g})}if(a)return a;c.prototype.toString=function(){return this.g};var d="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",e=0;return b});
r("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=p[b[c]];"function"===typeof d&&"function"!=typeof d.prototype[a]&&ba(d.prototype,a,{configurable:!0,writable:!0,value:function(){return da(aa(this))}})}return a});function da(a){a={next:a};a[Symbol.iterator]=function(){return this};return a}
function t(a){var b="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if("number"==typeof a.length)return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");}var ea="function"==typeof Object.create?Object.create:function(a){function b(){}b.prototype=a;return new b},fa;
if("function"==typeof Object.setPrototypeOf)fa=Object.setPrototypeOf;else{var ha;a:{var ia={a:!0},ja={};try{ja.__proto__=ia;ha=ja.a;break a}catch(a){}ha=!1}fa=ha?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var ka=fa;
r("Promise",function(a){function b(g){this.g=0;this.i=void 0;this.h=[];this.m=!1;var h=this.j();try{g(h.resolve,h.reject)}catch(k){h.reject(k)}}function c(){this.g=null}function d(g){return g instanceof b?g:new b(function(h){h(g)})}if(a)return a;c.prototype.h=function(g){if(null==this.g){this.g=[];var h=this;this.i(function(){h.l()})}this.g.push(g)};var e=p.setTimeout;c.prototype.i=function(g){e(g,0)};c.prototype.l=function(){for(;this.g&&this.g.length;){var g=this.g;this.g=[];for(var h=0;h<g.length;++h){var k=
g[h];g[h]=null;try{k()}catch(l){this.j(l)}}}this.g=null};c.prototype.j=function(g){this.i(function(){throw g;})};b.prototype.j=function(){function g(l){return function(m){k||(k=!0,l.call(h,m))}}var h=this,k=!1;return{resolve:g(this.A),reject:g(this.l)}};b.prototype.A=function(g){if(g===this)this.l(new TypeError("A Promise cannot resolve to itself"));else if(g instanceof b)this.D(g);else{a:switch(typeof g){case "object":var h=null!=g;break a;case "function":h=!0;break a;default:h=!1}h?this.v(g):this.s(g)}};
b.prototype.v=function(g){var h=void 0;try{h=g.then}catch(k){this.l(k);return}"function"==typeof h?this.F(h,g):this.s(g)};b.prototype.l=function(g){this.o(2,g)};b.prototype.s=function(g){this.o(1,g)};b.prototype.o=function(g,h){if(0!=this.g)throw Error("Cannot settle("+g+", "+h+"): Promise already settled in state"+this.g);this.g=g;this.i=h;2===this.g&&this.B();this.u()};b.prototype.B=function(){var g=this;e(function(){if(g.C()){var h=p.console;"undefined"!==typeof h&&h.error(g.i)}},1)};b.prototype.C=
function(){if(this.m)return!1;var g=p.CustomEvent,h=p.Event,k=p.dispatchEvent;if("undefined"===typeof k)return!0;"function"===typeof g?g=new g("unhandledrejection",{cancelable:!0}):"function"===typeof h?g=new h("unhandledrejection",{cancelable:!0}):(g=p.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.i;return k(g)};b.prototype.u=function(){if(null!=this.h){for(var g=0;g<this.h.length;++g)f.h(this.h[g]);this.h=null}};var f=new c;b.prototype.D=
function(g){var h=this.j();g.ia(h.resolve,h.reject)};b.prototype.F=function(g,h){var k=this.j();try{g.call(h,k.resolve,k.reject)}catch(l){k.reject(l)}};b.prototype.then=function(g,h){function k(u,v){return"function"==typeof u?function(J){try{l(u(J))}catch(U){m(U)}}:v}var l,m,q=new b(function(u,v){l=u;m=v});this.ia(k(g,l),k(h,m));return q};b.prototype.catch=function(g){return this.then(void 0,g)};b.prototype.ia=function(g,h){function k(){switch(l.g){case 1:g(l.i);break;case 2:h(l.i);break;default:throw Error("Unexpected state: "+
l.g);}}var l=this;null==this.h?f.h(k):this.h.push(k);this.m=!0};b.resolve=d;b.reject=function(g){return new b(function(h,k){k(g)})};b.race=function(g){return new b(function(h,k){for(var l=t(g),m=l.next();!m.done;m=l.next())d(m.value).ia(h,k)})};b.all=function(g){var h=t(g),k=h.next();return k.done?d([]):new b(function(l,m){function q(J){return function(U){u[J]=U;v--;0==v&&l(u)}}var u=[],v=0;do u.push(void 0),v++,d(k.value).ia(q(u.length-1),m),k=h.next();while(!k.done)})};return b});
function w(a,b){return Object.prototype.hasOwnProperty.call(a,b)}
r("WeakMap",function(a){function b(k){this.g=(h+=Math.random()+1).toString();if(k){k=t(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}}function c(){}function d(k){var l=typeof k;return"object"===l&&null!==k||"function"===l}function e(k){if(!w(k,g)){var l=new c;ba(k,g,{value:l})}}function f(k){var l=Object[k];l&&(Object[k]=function(m){if(m instanceof c)return m;Object.isExtensible(m)&&e(m);return l(m)})}if(function(){if(!a||!Object.seal)return!1;try{var k=Object.seal({}),l=Object.seal({}),
m=new a([[k,2],[l,3]]);if(2!=m.get(k)||3!=m.get(l))return!1;m.delete(k);m.set(l,4);return!m.has(k)&&4==m.get(l)}catch(q){return!1}}())return a;var g="$jscomp_hidden_"+Math.random();f("freeze");f("preventExtensions");f("seal");var h=0;b.prototype.set=function(k,l){if(!d(k))throw Error("Invalid WeakMap key");e(k);if(!w(k,g))throw Error("WeakMap key fail: "+k);k[g][this.g]=l;return this};b.prototype.get=function(k){return d(k)&&w(k,g)?k[g][this.g]:void 0};b.prototype.has=function(k){return d(k)&&w(k,
g)&&w(k[g],this.g)};b.prototype.delete=function(k){return d(k)&&w(k,g)&&w(k[g],this.g)?delete k[g][this.g]:!1};return b});
r("Map",function(a){function b(){var h={};return h.P=h.next=h.head=h}function c(h,k){var l=h[1];return da(function(){if(l){for(;l.head!=h[1];)l=l.P;for(;l.next!=l.head;)return l=l.next,{done:!1,value:k(l)};l=null}return{done:!0,value:void 0}})}function d(h,k){var l=k&&typeof k;"object"==l||"function"==l?f.has(k)?l=f.get(k):(l=""+ ++g,f.set(k,l)):l="p_"+k;var m=h[0][l];if(m&&w(h[0],l))for(h=0;h<m.length;h++){var q=m[h];if(k!==k&&q.key!==q.key||k===q.key)return{id:l,list:m,index:h,K:q}}return{id:l,
list:m,index:-1,K:void 0}}function e(h){this[0]={};this[1]=b();this.size=0;if(h){h=t(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}}if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var h=Object.seal({x:4}),k=new a(t([[h,"s"]]));if("s"!=k.get(h)||1!=k.size||k.get({x:4})||k.set({x:4},"t")!=k||2!=k.size)return!1;var l=k.entries(),m=l.next();if(m.done||m.value[0]!=h||"s"!=m.value[1])return!1;m=l.next();return m.done||4!=m.value[0].x||
"t"!=m.value[1]||!l.next().done?!1:!0}catch(q){return!1}}())return a;var f=new WeakMap;e.prototype.set=function(h,k){h=0===h?0:h;var l=d(this,h);l.list||(l.list=this[0][l.id]=[]);l.K?l.K.value=k:(l.K={next:this[1],P:this[1].P,head:this[1],key:h,value:k},l.list.push(l.K),this[1].P.next=l.K,this[1].P=l.K,this.size++);return this};e.prototype.delete=function(h){h=d(this,h);return h.K&&h.list?(h.list.splice(h.index,1),h.list.length||delete this[0][h.id],h.K.P.next=h.K.next,h.K.next.P=h.K.P,h.K.head=null,
this.size--,!0):!1};e.prototype.clear=function(){this[0]={};this[1]=this[1].P=b();this.size=0};e.prototype.has=function(h){return!!d(this,h).K};e.prototype.get=function(h){return(h=d(this,h).K)&&h.value};e.prototype.entries=function(){return c(this,function(h){return[h.key,h.value]})};e.prototype.keys=function(){return c(this,function(h){return h.key})};e.prototype.values=function(){return c(this,function(h){return h.value})};e.prototype.forEach=function(h,k){for(var l=this.entries(),m;!(m=l.next()).done;)m=
m.value,h.call(k,m[1],m[0],this)};e.prototype[Symbol.iterator]=e.prototype.entries;var g=0;return e});r("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var g=d[f];if(b.call(c,g,f,d)){b=g;break a}}b=void 0}return b}});
function la(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e}r("Array.prototype.keys",function(a){return a?a:function(){return la(this,function(b){return b})}});
r("Set",function(a){function b(c){this.g=new Map;if(c){c=t(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.g.size}if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var c=Object.seal({x:4}),d=new a(t([c]));if(!d.has(c)||1!=d.size||d.add(c)!=d||1!=d.size||d.add({x:4})!=d||2!=d.size)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||4!=f.value[0].x||
f.value[1]!=f.value[0]?!1:e.next().done}catch(g){return!1}}())return a;b.prototype.add=function(c){c=0===c?0:c;this.g.set(c,c);this.size=this.g.size;return this};b.prototype.delete=function(c){c=this.g.delete(c);this.size=this.g.size;return c};b.prototype.clear=function(){this.g.clear();this.size=0};b.prototype.has=function(c){return this.g.has(c)};b.prototype.entries=function(){return this.g.entries()};b.prototype.values=function(){return this.g.values()};b.prototype.keys=b.prototype.values;b.prototype[Symbol.iterator]=
b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.g.forEach(function(f){return c.call(d,f,f,e)})};return b});r("Array.prototype.entries",function(a){return a?a:function(){return la(this,function(b,c){return[b,c]})}});r("Array.prototype.values",function(a){return a?a:function(){return la(this,function(b,c){return c})}});
r("Array.from",function(a){return a?a:function(b,c,d){c=null!=c?c:function(h){return h};var e=[],f="undefined"!=typeof Symbol&&Symbol.iterator&&b[Symbol.iterator];if("function"==typeof f){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}});/** @license

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var ma=ma||{},x=this||self;function na(a){var b=typeof a;b="object"!=b?b:a?Array.isArray(a)?"array":b:"null";return"array"==b||"object"==b&&"number"==typeof a.length}function y(a){var b=typeof a;return"object"==b&&null!=a||"function"==b}function oa(a,b,c){return a.call.apply(a.bind,arguments)}
function pa(a,b,c){if(!a)throw Error();if(2<arguments.length){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}function z(a,b,c){z=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?oa:pa;return z.apply(null,arguments)}
function qa(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}}function A(a,b){function c(){}c.prototype=b.prototype;a.Y=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Tb=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var ra=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if("string"===typeof a)return"string"!==typeof b||1!=b.length?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};function sa(a){var b=a.length;if(0<b){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]}
function ta(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(na(d)){var e=a.length||0,f=d.length||0;a.length=e+f;for(var g=0;g<f;g++)a[e+g]=d[g]}else a.push(d)}};function ua(a,b){this.i=a;this.j=b;this.h=0;this.g=null}ua.prototype.get=function(){if(0<this.h){this.h--;var a=this.g;this.g=a.next;a.next=null}else a=this.i();return a};function va(a){return/^[\s\xa0]*$/.test(a)}function B(a,b){return-1!=a.indexOf(b)};function C(){var a=x.navigator;return a&&(a=a.userAgent)?a:""};function wa(a){wa[" "](a);return a}wa[" "]=function(){};var xa=B(C(),"Gecko")&&!(B(C().toLowerCase(),"webkit")&&!B(C(),"Edge"))&&!(B(C(),"Trident")||B(C(),"MSIE"))&&!B(C(),"Edge");function ya(a,b,c){for(var d in a)b.call(c,a[d],d,a)}function za(a,b){for(var c in a)b.call(void 0,a[c],c,a)}function Aa(a){var b={},c;for(c in a)b[c]=a[c];return b}var Ba="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function Ca(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<Ba.length;f++)c=Ba[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};function Da(a){var b=1;a=a.split(":");for(var c=[];0<b&&a.length;)c.push(a.shift()),b--;a.length&&c.push(a.join(":"));return c};function Ea(a){x.setTimeout(function(){throw a;},0)};function Fa(){this.h=this.g=null}Fa.prototype.add=function(a,b){var c=Ga.get();c.set(a,b);this.h?this.h.next=c:this.g=c;this.h=c};function Ha(){var a=Ia,b=null;a.g&&(b=a.g,a.g=a.g.next,a.g||(a.h=null),b.next=null);return b}var Ga=new ua(function(){return new Ja},function(a){return a.reset()});function Ja(){this.next=this.g=this.h=null}Ja.prototype.set=function(a,b){this.h=a;this.g=b;this.next=null};Ja.prototype.reset=function(){this.next=this.g=this.h=null};var Ka,La=!1,Ia=new Fa;function Ma(a,b){Ka||Na();La||(Ka(),La=!0);Ia.add(a,b)}function Na(){var a=x.Promise.resolve(void 0);Ka=function(){a.then(Oa)}}function Oa(){for(var a;a=Ha();){try{a.h.call(a.g)}catch(c){Ea(c)}var b=Ga;b.j(a);100>b.h&&(b.h++,a.next=b.g,b.g=a)}La=!1};function D(){this.s=this.s;this.C=this.C}D.prototype.s=!1;D.prototype.pa=function(){this.s||(this.s=!0,this.O())};D.prototype.O=function(){if(this.C)for(;this.C.length;)this.C.shift()()};function E(a,b){this.type=a;this.g=this.target=b;this.defaultPrevented=!1}E.prototype.h=function(){this.defaultPrevented=!0};var Pa=function(){if(!x.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=function(){};x.addEventListener("test",c,b);x.removeEventListener("test",c,b)}catch(d){}return a}();function Qa(a,b){E.call(this,a?a.type:"");this.relatedTarget=this.g=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=0;this.key="";this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.i=null;if(a){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.g=b;if(b=a.relatedTarget){if(xa){a:{try{wa(b.nodeName);var e=!0;break a}catch(f){}e=
!1}e||(b=null)}}else"mouseover"==c?b=a.fromElement:"mouseout"==c&&(b=a.toElement);this.relatedTarget=b;d?(this.clientX=void 0!==d.clientX?d.clientX:d.pageX,this.clientY=void 0!==d.clientY?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.clientX=void 0!==a.clientX?a.clientX:a.pageX,this.clientY=void 0!==a.clientY?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.key=a.key||"";this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=
a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType="string"===typeof a.pointerType?a.pointerType:Ra[a.pointerType]||"";this.state=a.state;this.i=a;a.defaultPrevented&&Qa.Y.h.call(this)}}A(Qa,E);var Ra={2:"touch",3:"pen",4:"mouse"};Qa.prototype.h=function(){Qa.Y.h.call(this);var a=this.i;a.preventDefault?a.preventDefault():a.returnValue=!1};var Sa="closure_listenable_"+(1E6*Math.random()|0);var Ta=0;function Ua(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.ka=e;this.key=++Ta;this.fa=this.ha=!1}function Va(a){a.fa=!0;a.listener=null;a.proxy=null;a.src=null;a.ka=null};function Wa(a){this.src=a;this.g={};this.h=0}Wa.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.g[f];a||(a=this.g[f]=[],this.h++);var g=Xa(a,b,d,e);-1<g?(b=a[g],c||(b.ha=!1)):(b=new Ua(b,this.src,f,!!d,e),b.ha=c,a.push(b));return b};function Ya(a,b){var c=b.type;if(c in a.g){var d=a.g[c],e=ra(d,b),f;(f=0<=e)&&Array.prototype.splice.call(d,e,1);f&&(Va(b),0==a.g[c].length&&(delete a.g[c],a.h--))}}
function Xa(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.fa&&f.listener==b&&f.capture==!!c&&f.ka==d)return e}return-1};var Za="closure_lm_"+(1E6*Math.random()|0),$a={},ab=0;function bb(a,b,c,d,e){if(d&&d.once)return cb(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)bb(a,b[f],c,d,e);return null}c=db(c);return a&&a[Sa]?a.L(b,c,y(d)?!!d.capture:!!d,e):eb(a,b,c,!1,d,e)}
function eb(a,b,c,d,e,f){if(!b)throw Error("Invalid event type");var g=y(e)?!!e.capture:!!e,h=fb(a);h||(a[Za]=h=new Wa(a));c=h.add(b,c,d,g,f);if(c.proxy)return c;d=gb();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)Pa||(e=g),void 0===e&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(hb(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("addEventListener and attachEvent are unavailable.");ab++;return c}
function gb(){function a(c){return b.call(a.src,a.listener,c)}var b=ib;return a}function cb(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)cb(a,b[f],c,d,e);return null}c=db(c);return a&&a[Sa]?a.M(b,c,y(d)?!!d.capture:!!d,e):eb(a,b,c,!0,d,e)}
function jb(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)jb(a,b[f],c,d,e);else(d=y(d)?!!d.capture:!!d,c=db(c),a&&a[Sa])?(a=a.i,b=String(b).toString(),b in a.g&&(f=a.g[b],c=Xa(f,c,d,e),-1<c&&(Va(f[c]),Array.prototype.splice.call(f,c,1),0==f.length&&(delete a.g[b],a.h--)))):a&&(a=fb(a))&&(b=a.g[b.toString()],a=-1,b&&(a=Xa(b,c,d,e)),(c=-1<a?b[a]:null)&&kb(c))}
function kb(a){if("number"!==typeof a&&a&&!a.fa){var b=a.src;if(b&&b[Sa])Ya(b.i,a);else{var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(hb(c),d):b.addListener&&b.removeListener&&b.removeListener(d);ab--;(c=fb(b))?(Ya(c,a),0==c.h&&(c.src=null,b[Za]=null)):Va(a)}}}function hb(a){return a in $a?$a[a]:$a[a]="on"+a}function ib(a,b){if(a.fa)a=!0;else{b=new Qa(b,this);var c=a.listener,d=a.ka||a.src;a.ha&&kb(a);a=c.call(d,b)}return a}
function fb(a){a=a[Za];return a instanceof Wa?a:null}var lb="__closure_events_fn_"+(1E9*Math.random()>>>0);function db(a){if("function"===typeof a)return a;a[lb]||(a[lb]=function(b){return a.handleEvent(b)});return a[lb]};function F(){D.call(this);this.i=new Wa(this);this.N=this;this.F=null}A(F,D);F.prototype[Sa]=!0;F.prototype.removeEventListener=function(a,b,c,d){jb(this,a,b,c,d)};
function G(a,b){var c,d=a.F;if(d)for(c=[];d;d=d.F)c.push(d);a=a.N;d=b.type||b;if("string"===typeof b)b=new E(b,a);else if(b instanceof E)b.target=b.target||a;else{var e=b;b=new E(d,a);Ca(b,e)}e=!0;if(c)for(var f=c.length-1;0<=f;f--){var g=b.g=c[f];e=mb(g,d,!0,b)&&e}g=b.g=a;e=mb(g,d,!0,b)&&e;e=mb(g,d,!1,b)&&e;if(c)for(f=0;f<c.length;f++)g=b.g=c[f],e=mb(g,d,!1,b)&&e}
F.prototype.O=function(){F.Y.O.call(this);if(this.i){var a=this.i,b=0,c;for(c in a.g){for(var d=a.g[c],e=0;e<d.length;e++)++b,Va(d[e]);delete a.g[c];a.h--}}this.F=null};F.prototype.L=function(a,b,c,d){return this.i.add(String(a),b,!1,c,d)};F.prototype.M=function(a,b,c,d){return this.i.add(String(a),b,!0,c,d)};
function mb(a,b,c,d){b=a.i.g[String(b)];if(!b)return!0;b=b.concat();for(var e=!0,f=0;f<b.length;++f){var g=b[f];if(g&&!g.fa&&g.capture==c){var h=g.listener,k=g.ka||g.src;g.ha&&Ya(a.i,g);e=!1!==h.call(k,d)&&e}}return e&&!d.defaultPrevented};function nb(a,b,c){if("function"===typeof a)c&&(a=z(a,c));else if(a&&"function"==typeof a.handleEvent)a=z(a.handleEvent,a);else throw Error("Invalid listener argument");return 2147483647<Number(b)?-1:x.setTimeout(a,b||0)};function H(a,b,c){D.call(this);this.m=null!=c?a.bind(c):a;this.l=b;this.h=null;this.i=!1;this.g=null}H.prototype=ea(D.prototype);H.prototype.constructor=H;if(ka)ka(H,D);else for(var ob in D)if("prototype"!=ob)if(Object.defineProperties){var pb=Object.getOwnPropertyDescriptor(D,ob);pb&&Object.defineProperty(H,ob,pb)}else H[ob]=D[ob];H.Y=D.prototype;H.prototype.j=function(a){this.h=arguments;this.g?this.i=!0:qb(this)};
H.prototype.O=function(){D.prototype.O.call(this);this.g&&(x.clearTimeout(this.g),this.g=null,this.i=!1,this.h=null)};function qb(a){a.g=nb(function(){a.g=null;a.i&&(a.i=!1,qb(a))},a.l);var b=a.h;a.h=null;a.m.apply(null,b)};function rb(a){D.call(this);this.h=a;this.g={}}A(rb,D);var sb=[];function tb(a){ya(a.g,function(b,c){this.g.hasOwnProperty(c)&&kb(b)},a);a.g={}}rb.prototype.O=function(){rb.Y.O.call(this);tb(this)};rb.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented");};var ub=x.JSON.stringify;var vb=x.JSON.parse;function wb(){}wb.prototype.stringify=function(a){return x.JSON.stringify(a,void 0)};wb.prototype.parse=function(a){return x.JSON.parse(a,void 0)};function xb(){}xb.prototype.h=null;function yb(a){return a.h||(a.h=a.i())};function zb(){}var Ab={OPEN:"a",nb:"b",Ma:"c",zb:"d"};function Bb(){E.call(this,"d")}A(Bb,E);function Cb(){E.call(this,"c")}A(Cb,E);var I={},Db=null;function Eb(){return Db=Db||new F}I.Oa="serverreachability";function Fb(a){E.call(this,I.Oa,a)}A(Fb,E);function Gb(a){var b=Eb();G(b,new Fb(b,a))}I.STAT_EVENT="statevent";function Hb(a,b){E.call(this,I.STAT_EVENT,a);this.stat=b}A(Hb,E);function K(a){var b=Eb();G(b,new Hb(b,a))}I.Pa="timingevent";function Ib(a,b){E.call(this,I.Pa,a);this.size=b}A(Ib,E);
function Jb(a,b){if("function"!==typeof a)throw Error("Fn must not be null and must be a function");return x.setTimeout(function(){a()},b)};function Kb(){this.g=!0}Kb.prototype.Aa=function(){this.g=!1};function Lb(a,b,c,d,e,f){a.info(function(){if(a.g)if(f){var g="";for(var h=f.split("&"),k=0;k<h.length;k++){var l=h[k].split("=");if(1<l.length){var m=l[0];l=l[1];var q=m.split("_");g=2<=q.length&&"type"==q[1]?g+(m+"="+l+"&"):g+(m+"=redacted&")}}}else g=null;else g=f;return"XMLHTTP REQ ("+d+") [attempt "+e+"]: "+b+"\n"+c+"\n"+g})}
function Mb(a,b,c,d,e,f,g){a.info(function(){return"XMLHTTP RESP ("+d+") [ attempt "+e+"]: "+b+"\n"+c+"\n"+f+" "+g})}function L(a,b,c,d){a.info(function(){return"XMLHTTP TEXT ("+b+"): "+Nb(a,c)+(d?" "+d:"")})}function Ob(a,b){a.info(function(){return"TIMEOUT: "+b})}Kb.prototype.info=function(){};
function Nb(a,b){if(!a.g)return b;if(!b)return null;try{var c=JSON.parse(b);if(c)for(a=0;a<c.length;a++)if(Array.isArray(c[a])){var d=c[a];if(!(2>d.length)){var e=d[1];if(Array.isArray(e)&&!(1>e.length)){var f=e[0];if("noop"!=f&&"stop"!=f&&"close"!=f)for(var g=1;g<e.length;g++)e[g]=""}}}return ub(c)}catch(h){return b}};var Pb={NO_ERROR:0,jb:1,wb:2,vb:3,qb:4,ub:5,xb:6,La:7,TIMEOUT:8,Ab:9};var Qb={ob:"complete",Kb:"success",Ma:"error",La:"abort",Cb:"ready",Db:"readystatechange",TIMEOUT:"timeout",yb:"incrementaldata",Bb:"progress",rb:"downloadprogress",Sb:"uploadprogress"};var Rb;function Sb(){}A(Sb,xb);Sb.prototype.g=function(){return new XMLHttpRequest};Sb.prototype.i=function(){return{}};Rb=new Sb;function M(a,b,c,d){this.j=a;this.i=b;this.l=c;this.T=d||1;this.W=new rb(this);this.I=45E3;this.H=null;this.o=!1;this.m=this.A=this.v=this.M=this.F=this.U=this.B=null;this.D=[];this.g=null;this.C=0;this.s=this.u=null;this.aa=-1;this.J=!1;this.R=0;this.N=null;this.Z=this.L=this.V=this.S=!1;this.h=new Tb}function Tb(){this.i=null;this.g="";this.h=!1}var Ub={},Vb={};function Wb(a,b,c){a.M=1;a.v=Xb(N(b));a.m=c;a.S=!0;Yb(a,null)}
function Yb(a,b){a.F=Date.now();Zb(a);a.A=N(a.v);var c=a.A,d=a.T;Array.isArray(d)||(d=[String(d)]);$b(c.i,"t",d);a.C=0;c=a.j.J;a.h=new Tb;a.g=ac(a.j,c?b:null,!a.m);0<a.R&&(a.N=new H(z(a.ba,a,a.g),a.R));b=a.W;c=a.g;d=a.ea;var e="readystatechange";Array.isArray(e)||(e&&(sb[0]=e.toString()),e=sb);for(var f=0;f<e.length;f++){var g=bb(c,e[f],d||b.handleEvent,!1,b.h||b);if(!g)break;b.g[g.key]=g}b=a.H?Aa(a.H):{};a.m?(a.u||(a.u="POST"),b["Content-Type"]="application/x-www-form-urlencoded",a.g.ga(a.A,a.u,
a.m,b)):(a.u="GET",a.g.ga(a.A,a.u,null,b));Gb(1);Lb(a.i,a.u,a.A,a.l,a.T,a.m)}M.prototype.ea=function(a){a=a.target;var b=this.N;b&&3==O(a)?b.j():this.ba(a)};
M.prototype.ba=function(a){try{if(a==this.g)a:{var b=O(this.g),c=this.g.Ea(),d=this.g.ca();if(!(3>b)&&(3!=b||this.g&&(this.h.h||this.g.ra()||bc(this.g)))){this.J||4!=b||7==c||(8==c||0>=d?Gb(3):Gb(2));cc(this);var e=this.g.ca();this.aa=e;b:if(dc(this)){var f=bc(this.g);a="";var g=f.length,h=4==O(this.g);if(!this.h.i){if("undefined"===typeof TextDecoder){P(this);ec(this);var k="";break b}this.h.i=new x.TextDecoder}for(c=0;c<g;c++)this.h.h=!0,a+=this.h.i.decode(f[c],{stream:!(h&&c==g-1)});f.length=0;
this.h.g+=a;this.C=0;k=this.h.g}else k=this.g.ra();this.o=200==e;Mb(this.i,this.u,this.A,this.l,this.T,b,e);if(this.o){if(this.V&&!this.L){b:{if(this.g){var l,m=this.g;if((l=m.g?m.g.getResponseHeader("X-HTTP-Initial-Response"):null)&&!va(l)){var q=l;break b}}q=null}if(e=q)L(this.i,this.l,e,"Initial handshake response via X-HTTP-Initial-Response"),this.L=!0,fc(this,e);else{this.o=!1;this.s=3;K(12);P(this);ec(this);break a}}if(this.S){e=!0;for(var u;!this.J&&this.C<k.length;)if(u=gc(this,k),u==Vb){4==
b&&(this.s=4,K(14),e=!1);L(this.i,this.l,null,"[Incomplete Response]");break}else if(u==Ub){this.s=4;K(15);L(this.i,this.l,k,"[Invalid Chunk]");e=!1;break}else L(this.i,this.l,u,null),fc(this,u);dc(this)&&0!=this.C&&(this.h.g=this.h.g.slice(this.C),this.C=0);4!=b||0!=k.length||this.h.h||(this.s=1,K(16),e=!1);this.o=this.o&&e;if(!e)L(this.i,this.l,k,"[Invalid Chunked Response]"),P(this),ec(this);else if(0<k.length&&!this.Z){this.Z=!0;var v=this.j;v.g==this&&v.da&&!v.N&&(v.j.info("Great, no buffering proxy detected. Bytes received: "+
k.length),hc(v),v.N=!0,K(11))}}else L(this.i,this.l,k,null),fc(this,k);4==b&&P(this);this.o&&!this.J&&(4==b?jc(this.j,this):(this.o=!1,Zb(this)))}else kc(this.g),400==e&&0<k.indexOf("Unknown SID")?(this.s=3,K(12)):(this.s=0,K(13)),P(this),ec(this)}}}catch(J){}finally{}};function dc(a){return a.g?"GET"==a.u&&2!=a.M&&a.j.Fa:!1}
function gc(a,b){var c=a.C,d=b.indexOf("\n",c);if(-1==d)return Vb;c=Number(b.substring(c,d));if(isNaN(c))return Ub;d+=1;if(d+c>b.length)return Vb;b=b.slice(d,d+c);a.C=d+c;return b}M.prototype.cancel=function(){this.J=!0;P(this)};function Zb(a){a.U=Date.now()+a.I;lc(a,a.I)}function lc(a,b){if(null!=a.B)throw Error("WatchDog timer not null");a.B=Jb(z(a.da,a),b)}function cc(a){a.B&&(x.clearTimeout(a.B),a.B=null)}
M.prototype.da=function(){this.B=null;var a=Date.now();0<=a-this.U?(Ob(this.i,this.A),2!=this.M&&(Gb(3),K(17)),P(this),this.s=2,ec(this)):lc(this,this.U-a)};function ec(a){0==a.j.G||a.J||jc(a.j,a)}function P(a){cc(a);var b=a.N;b&&"function"==typeof b.pa&&b.pa();a.N=null;tb(a.W);a.g&&(b=a.g,a.g=null,b.abort(),b.pa())}
function fc(a,b){try{var c=a.j;if(0!=c.G&&(c.g==a||mc(c.h,a)))if(!a.L&&mc(c.h,a)&&3==c.G){try{var d=c.Ga.g.parse(b)}catch(ic){d=null}if(Array.isArray(d)&&3==d.length){var e=d;if(0==e[0])a:{if(!c.u){if(c.g)if(c.g.F+3E3<a.F)nc(c),oc(c);else break a;pc(c);K(18)}}else c.Ca=e[1],0<c.Ca-c.V&&37500>e[2]&&c.F&&0==c.v&&!c.C&&(c.C=Jb(z(c.cb,c),6E3));if(1>=qc(c.h)&&c.ea){try{c.ea()}catch(ic){}c.ea=void 0}}else Q(c,11)}else if((a.L||c.g==a)&&nc(c),!va(b))for(e=c.Ga.g.parse(b),b=0;b<e.length;b++){var f=e[b];c.V=
f[0];f=f[1];if(2==c.G)if("c"==f[0]){c.L=f[1];c.la=f[2];var g=f[3];null!=g&&(c.oa=g,c.j.info("VER="+c.oa));var h=f[4];null!=h&&(c.Da=h,c.j.info("SVER="+c.Da));var k=f[5];null!=k&&"number"===typeof k&&0<k&&(d=1.5*k,c.M=d,c.j.info("backChannelRequestTimeoutMs_="+d));d=c;var l=a.g;if(l){var m=l.g?l.g.getResponseHeader("X-Client-Wire-Protocol"):null;if(m){var q=d.h;!q.g&&(B(m,"spdy")||B(m,"quic")||B(m,"h2"))&&(q.j=q.l,q.g=new Set,q.h&&(rc(q,q.h),q.h=null))}if(d.D){var u=l.g?l.g.getResponseHeader("X-HTTP-Session-Id"):
null;u&&(d.Ba=u,R(d.I,d.D,u))}}c.G=3;c.l&&c.l.xa();c.da&&(c.T=Date.now()-a.F,c.j.info("Handshake RTT: "+c.T+"ms"));d=c;var v=a;d.ta=sc(d,d.J?d.la:null,d.Z);if(v.L){tc(d.h,v);var J=v,U=d.M;U&&(J.I=U);J.B&&(cc(J),Zb(J));d.g=v}else uc(d);0<c.i.length&&vc(c)}else"stop"!=f[0]&&"close"!=f[0]||Q(c,7);else 3==c.G&&("stop"==f[0]||"close"==f[0]?"stop"==f[0]?Q(c,7):wc(c):"noop"!=f[0]&&c.l&&c.l.wa(f),c.v=0)}Gb(4)}catch(ic){}};function xc(a,b){this.g=a;this.map=b};function yc(a){this.l=a||10;x.PerformanceNavigationTiming?(a=x.performance.getEntriesByType("navigation"),a=0<a.length&&("hq"==a[0].nextHopProtocol||"h2"==a[0].nextHopProtocol)):a=!!(x.chrome&&x.chrome.loadTimes&&x.chrome.loadTimes()&&x.chrome.loadTimes().wasFetchedViaSpdy);this.j=a?this.l:1;this.g=null;1<this.j&&(this.g=new Set);this.h=null;this.i=[]}function zc(a){return a.h?!0:a.g?a.g.size>=a.j:!1}function qc(a){return a.h?1:a.g?a.g.size:0}function mc(a,b){return a.h?a.h==b:a.g?a.g.has(b):!1}
function rc(a,b){a.g?a.g.add(b):a.h=b}function tc(a,b){a.h&&a.h==b?a.h=null:a.g&&a.g.has(b)&&a.g.delete(b)}yc.prototype.cancel=function(){this.i=Ac(this);if(this.h)this.h.cancel(),this.h=null;else if(this.g&&0!==this.g.size){for(var a=t(this.g.values()),b=a.next();!b.done;b=a.next())b.value.cancel();this.g.clear()}};
function Ac(a){if(null!=a.h)return a.i.concat(a.h.D);if(null!=a.g&&0!==a.g.size){var b=a.i;a=t(a.g.values());for(var c=a.next();!c.done;c=a.next())b=b.concat(c.value.D);return b}return sa(a.i)};function Bc(a){if(a.X&&"function"==typeof a.X)return a.X();if("undefined"!==typeof Map&&a instanceof Map||"undefined"!==typeof Set&&a instanceof Set)return Array.from(a.values());if("string"===typeof a)return a.split("");if(na(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}b=[];c=0;for(d in a)b[c++]=a[d];return b}
function Cc(a){if(a.qa&&"function"==typeof a.qa)return a.qa();if(!a.X||"function"!=typeof a.X){if("undefined"!==typeof Map&&a instanceof Map)return Array.from(a.keys());if(!("undefined"!==typeof Set&&a instanceof Set)){if(na(a)||"string"===typeof a){var b=[];a=a.length;for(var c=0;c<a;c++)b.push(c);return b}b=[];c=0;for(var d in a)b[c++]=d;return b}}}
function Dc(a,b){if(a.forEach&&"function"==typeof a.forEach)a.forEach(b,void 0);else if(na(a)||"string"===typeof a)Array.prototype.forEach.call(a,b,void 0);else for(var c=Cc(a),d=Bc(a),e=d.length,f=0;f<e;f++)b.call(void 0,d[f],c&&c[f],a)};var Ec=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function Fc(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(0<=d){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}};function S(a){this.g=this.o=this.j="";this.m=null;this.s=this.h="";this.l=!1;var b;a instanceof S?(this.l=a.l,Gc(this,a.j),this.o=a.o,this.g=a.g,Hc(this,a.m),this.h=a.h,Ic(this,Jc(a.i)),this.s=a.s):a&&(b=String(a).match(Ec))?(this.l=!1,Gc(this,b[1]||"",!0),this.o=Kc(b[2]||""),this.g=Kc(b[3]||"",!0),Hc(this,b[4]),this.h=Kc(b[5]||"",!0),Ic(this,b[6]||"",!0),this.s=Kc(b[7]||"")):(this.l=!1,this.i=new Lc(null,this.l))}
S.prototype.toString=function(){var a=[],b=this.j;b&&a.push(Mc(b,Nc,!0),":");var c=this.g;if(c||"file"==b)a.push("//"),(b=this.o)&&a.push(Mc(b,Nc,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.m,null!=c&&a.push(":",String(c));if(c=this.h)this.g&&"/"!=c.charAt(0)&&a.push("/"),a.push(Mc(c,"/"==c.charAt(0)?Oc:Pc,!0));(c=this.i.toString())&&a.push("?",c);(c=this.s)&&a.push("#",Mc(c,Qc));return a.join("")};
S.prototype.resolve=function(a){var b=N(this),c=!!a.j;c?Gc(b,a.j):c=!!a.o;c?b.o=a.o:c=!!a.g;c?b.g=a.g:c=null!=a.m;var d=a.h;if(c)Hc(b,a.m);else if(c=!!a.h){if("/"!=d.charAt(0))if(this.g&&!this.h)d="/"+d;else{var e=b.h.lastIndexOf("/");-1!=e&&(d=b.h.slice(0,e+1)+d)}e=d;if(".."==e||"."==e)d="";else if(B(e,"./")||B(e,"/.")){d=0==e.lastIndexOf("/",0);e=e.split("/");for(var f=[],g=0;g<e.length;){var h=e[g++];"."==h?d&&g==e.length&&f.push(""):".."==h?((1<f.length||1==f.length&&""!=f[0])&&f.pop(),d&&g==
e.length&&f.push("")):(f.push(h),d=!0)}d=f.join("/")}else d=e}c?b.h=d:c=""!==a.i.toString();c?Ic(b,Jc(a.i)):c=!!a.s;c&&(b.s=a.s);return b};function N(a){return new S(a)}function Gc(a,b,c){a.j=c?Kc(b,!0):b;a.j&&(a.j=a.j.replace(/:$/,""))}function Hc(a,b){if(b){b=Number(b);if(isNaN(b)||0>b)throw Error("Bad port number "+b);a.m=b}else a.m=null}function Ic(a,b,c){b instanceof Lc?(a.i=b,Rc(a.i,a.l)):(c||(b=Mc(b,Sc)),a.i=new Lc(b,a.l))}function R(a,b,c){a.i.set(b,c)}
function Xb(a){R(a,"zx",Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^Date.now()).toString(36));return a}function Kc(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""}function Mc(a,b,c){return"string"===typeof a?(a=encodeURI(a).replace(b,Tc),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null}function Tc(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)}
var Nc=/[#\/\?@]/g,Pc=/[#\?:]/g,Oc=/[#\?]/g,Sc=/[#\?@]/g,Qc=/#/g;function Lc(a,b){this.h=this.g=null;this.i=a||null;this.j=!!b}function T(a){a.g||(a.g=new Map,a.h=0,a.i&&Fc(a.i,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))}n=Lc.prototype;n.add=function(a,b){T(this);this.i=null;a=V(this,a);var c=this.g.get(a);c||this.g.set(a,c=[]);c.push(b);this.h+=1;return this};function Uc(a,b){T(a);b=V(a,b);a.g.has(b)&&(a.i=null,a.h-=a.g.get(b).length,a.g.delete(b))}
function Vc(a,b){T(a);b=V(a,b);return a.g.has(b)}n.forEach=function(a,b){T(this);this.g.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};n.qa=function(){T(this);for(var a=Array.from(this.g.values()),b=Array.from(this.g.keys()),c=[],d=0;d<b.length;d++)for(var e=a[d],f=0;f<e.length;f++)c.push(b[d]);return c};
n.X=function(a){T(this);var b=[];if("string"===typeof a)Vc(this,a)&&(b=b.concat(this.g.get(V(this,a))));else{a=Array.from(this.g.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};n.set=function(a,b){T(this);this.i=null;a=V(this,a);Vc(this,a)&&(this.h-=this.g.get(a).length);this.g.set(a,[b]);this.h+=1;return this};n.get=function(a,b){if(!a)return b;a=this.X(a);return 0<a.length?String(a[0]):b};function $b(a,b,c){Uc(a,b);0<c.length&&(a.i=null,a.g.set(V(a,b),sa(c)),a.h+=c.length)}
n.toString=function(){if(this.i)return this.i;if(!this.g)return"";for(var a=[],b=Array.from(this.g.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.X(d);for(var f=0;f<d.length;f++){var g=e;""!==d[f]&&(g+="="+encodeURIComponent(String(d[f])));a.push(g)}}return this.i=a.join("&")};function Jc(a){var b=new Lc;b.i=a.i;a.g&&(b.g=new Map(a.g),b.h=a.h);return b}function V(a,b){b=String(b);a.j&&(b=b.toLowerCase());return b}
function Rc(a,b){b&&!a.j&&(T(a),a.i=null,a.g.forEach(function(c,d){var e=d.toLowerCase();d!=e&&(Uc(this,d),$b(this,e,c))},a));a.j=b};function Wc(a,b){var c=new Kb;if(x.Image){var d=new Image;d.onload=qa(W,c,"TestLoadImage: loaded",!0,b,d);d.onerror=qa(W,c,"TestLoadImage: error",!1,b,d);d.onabort=qa(W,c,"TestLoadImage: abort",!1,b,d);d.ontimeout=qa(W,c,"TestLoadImage: timeout",!1,b,d);x.setTimeout(function(){if(d.ontimeout)d.ontimeout()},1E4);d.src=a}else b(!1)}
function Xc(a,b){var c=new Kb,d=new AbortController,e=setTimeout(function(){d.abort();W(c,"TestPingServer: timeout",!1,b)},1E4);fetch(a,{signal:d.signal}).then(function(f){clearTimeout(e);f.ok?W(c,"TestPingServer: ok",!0,b):W(c,"TestPingServer: server error",!1,b)}).catch(function(){clearTimeout(e);W(c,"TestPingServer: error",!1,b)})}function W(a,b,c,d,e){try{e&&(e.onload=null,e.onerror=null,e.onabort=null,e.ontimeout=null),d(c)}catch(f){}};function Yc(){this.g=new wb}function Zc(a,b,c){var d=c||"";try{Dc(a,function(e,f){var g=e;y(e)&&(g=ub(e));b.push(d+f+"="+encodeURIComponent(g))})}catch(e){throw b.push(d+"type="+encodeURIComponent("_badmap")),e;}};function $c(a){this.l=a.Xb||null;this.j=a.hb||!1}A($c,xb);$c.prototype.g=function(){return new ad(this.l,this.j)};$c.prototype.i=function(a){return function(){return a}}({});function ad(a,b){F.call(this);this.D=a;this.o=b;this.m=void 0;this.status=this.readyState=0;this.responseType=this.responseText=this.response=this.statusText="";this.onreadystatechange=null;this.u=new Headers;this.h=null;this.B="GET";this.A="";this.g=!1;this.v=this.j=this.l=null}A(ad,F);n=ad.prototype;
n.open=function(a,b){if(0!=this.readyState)throw this.abort(),Error("Error reopening a connection");this.B=a;this.A=b;this.readyState=1;bd(this)};n.send=function(a){if(1!=this.readyState)throw this.abort(),Error("need to call open() first. ");this.g=!0;var b={headers:this.u,method:this.B,credentials:this.m,cache:void 0};a&&(b.body=a);(this.D||x).fetch(new Request(this.A,b)).then(this.Va.bind(this),this.ja.bind(this))};
n.abort=function(){this.response=this.responseText="";this.u=new Headers;this.status=0;this.j&&this.j.cancel("Request was aborted.").catch(function(){});1<=this.readyState&&this.g&&4!=this.readyState&&(this.g=!1,cd(this));this.readyState=0};
n.Va=function(a){if(this.g&&(this.l=a,this.h||(this.status=this.l.status,this.statusText=this.l.statusText,this.h=a.headers,this.readyState=2,bd(this)),this.g&&(this.readyState=3,bd(this),this.g)))if("arraybuffer"===this.responseType)a.arrayBuffer().then(this.Ta.bind(this),this.ja.bind(this));else if("undefined"!==typeof x.ReadableStream&&"body"in a){this.j=a.body.getReader();if(this.o){if(this.responseType)throw Error('responseType must be empty for "streamBinaryChunks" mode responses.');this.response=
[]}else this.response=this.responseText="",this.v=new TextDecoder;dd(this)}else a.text().then(this.Ua.bind(this),this.ja.bind(this))};function dd(a){a.j.read().then(a.Sa.bind(a)).catch(a.ja.bind(a))}n.Sa=function(a){if(this.g){if(this.o&&a.value)this.response.push(a.value);else if(!this.o){var b=a.value?a.value:new Uint8Array(0);if(b=this.v.decode(b,{stream:!a.done}))this.response=this.responseText+=b}a.done?cd(this):bd(this);3==this.readyState&&dd(this)}};
n.Ua=function(a){this.g&&(this.response=this.responseText=a,cd(this))};n.Ta=function(a){this.g&&(this.response=a,cd(this))};n.ja=function(){this.g&&cd(this)};function cd(a){a.readyState=4;a.l=null;a.j=null;a.v=null;bd(a)}n.setRequestHeader=function(a,b){this.u.append(a,b)};n.getResponseHeader=function(a){return this.h?this.h.get(a.toLowerCase())||"":""};
n.getAllResponseHeaders=function(){if(!this.h)return"";for(var a=[],b=this.h.entries(),c=b.next();!c.done;)c=c.value,a.push(c[0]+": "+c[1]),c=b.next();return a.join("\r\n")};function bd(a){a.onreadystatechange&&a.onreadystatechange.call(a)}Object.defineProperty(ad.prototype,"withCredentials",{get:function(){return"include"===this.m},set:function(a){this.m=a?"include":"same-origin"}});function ed(a){var b="";ya(a,function(c,d){b+=d;b+=":";b+=c;b+="\r\n"});return b}function fd(a,b,c){a:{for(d in c){var d=!1;break a}d=!0}d||(c=ed(c),"string"===typeof a?(encodeURIComponent(String(b)),null!=c&&encodeURIComponent(String(c))):R(a,b,c))};function X(a){F.call(this);this.headers=new Map;this.o=a||null;this.h=!1;this.v=this.g=null;this.D="";this.m=0;this.l="";this.j=this.B=this.u=this.A=!1;this.I=null;this.H="";this.J=!1}A(X,F);var gd=/^https?$/i,hd=["POST","PUT"];n=X.prototype;n.Ka=function(a){this.J=a};
n.ga=function(a,b,c,d){if(this.g)throw Error("[goog.net.XhrIo] Object is active with another request="+this.D+"; newUri="+a);b=b?b.toUpperCase():"GET";this.D=a;this.l="";this.m=0;this.A=!1;this.h=!0;this.g=this.o?this.o.g():Rb.g();this.v=this.o?yb(this.o):yb(Rb);this.g.onreadystatechange=z(this.Ha,this);try{this.B=!0,this.g.open(b,String(a),!0),this.B=!1}catch(g){id(this,g);return}a=c||"";c=new Map(this.headers);if(d)if(Object.getPrototypeOf(d)===Object.prototype)for(var e in d)c.set(e,d[e]);else if("function"===
typeof d.keys&&"function"===typeof d.get){e=t(d.keys());for(var f=e.next();!f.done;f=e.next())f=f.value,c.set(f,d.get(f))}else throw Error("Unknown input type for opt_headers: "+String(d));d=Array.from(c.keys()).find(function(g){return"content-type"==g.toLowerCase()});e=x.FormData&&a instanceof x.FormData;!(0<=ra(hd,b))||d||e||c.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");b=t(c);for(d=b.next();!d.done;d=b.next())c=t(d.value),d=c.next().value,c=c.next().value,this.g.setRequestHeader(d,
c);this.H&&(this.g.responseType=this.H);"withCredentials"in this.g&&this.g.withCredentials!==this.J&&(this.g.withCredentials=this.J);try{jd(this),this.u=!0,this.g.send(a),this.u=!1}catch(g){id(this,g)}};function id(a,b){a.h=!1;a.g&&(a.j=!0,a.g.abort(),a.j=!1);a.l=b;a.m=5;kd(a);ld(a)}function kd(a){a.A||(a.A=!0,G(a,"complete"),G(a,"error"))}n.abort=function(a){this.g&&this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1,this.m=a||7,G(this,"complete"),G(this,"abort"),ld(this))};
n.O=function(){this.g&&(this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1),ld(this,!0));X.Y.O.call(this)};n.Ha=function(){this.s||(this.B||this.u||this.j?md(this):this.fb())};n.fb=function(){md(this)};
function md(a){if(a.h&&"undefined"!=typeof ma&&(!a.v[1]||4!=O(a)||2!=a.ca()))if(a.u&&4==O(a))nb(a.Ha,0,a);else if(G(a,"readystatechange"),4==O(a)){a.h=!1;try{var b=a.ca();a:switch(b){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var c=!0;break a;default:c=!1}var d;if(!(d=c)){var e;if(e=0===b){var f=String(a.D).match(Ec)[1]||null;!f&&x.self&&x.self.location&&(f=x.self.location.protocol.slice(0,-1));e=!gd.test(f?f.toLowerCase():"")}d=e}if(d)G(a,"complete"),G(a,"success");else{a.m=
6;try{var g=2<O(a)?a.g.statusText:""}catch(h){g=""}a.l=g+" ["+a.ca()+"]";kd(a)}}finally{ld(a)}}}function ld(a,b){if(a.g){jd(a);var c=a.g,d=a.v[0]?function(){}:null;a.g=null;a.v=null;b||G(a,"ready");try{c.onreadystatechange=d}catch(e){}}}function jd(a){a.I&&(x.clearTimeout(a.I),a.I=null)}n.isActive=function(){return!!this.g};function O(a){return a.g?a.g.readyState:0}n.ca=function(){try{return 2<O(this)?this.g.status:-1}catch(a){return-1}};n.ra=function(){try{return this.g?this.g.responseText:""}catch(a){return""}};
n.Ra=function(a){if(this.g){var b=this.g.responseText;a&&0==b.indexOf(a)&&(b=b.substring(a.length));return vb(b)}};function bc(a){try{if(!a.g)return null;if("response"in a.g)return a.g.response;switch(a.H){case "":case "text":return a.g.responseText;case "arraybuffer":if("mozResponseArrayBuffer"in a.g)return a.g.mozResponseArrayBuffer}return null}catch(b){return null}}
function kc(a){var b={};a=(a.g&&2<=O(a)?a.g.getAllResponseHeaders()||"":"").split("\r\n");for(var c=0;c<a.length;c++)if(!va(a[c])){var d=Da(a[c]),e=d[0];d=d[1];if("string"===typeof d){d=d.trim();var f=b[e]||[];b[e]=f;f.push(d)}}za(b,function(g){return g.join(", ")})}n.Ea=function(){return this.m};n.Na=function(){return"string"===typeof this.l?this.l:String(this.l)};function nd(a,b,c){return c&&c.internalChannelParams?c.internalChannelParams[a]||b:b}
function od(a){this.Da=0;this.i=[];this.j=new Kb;this.la=this.ta=this.I=this.Z=this.g=this.Ba=this.D=this.H=this.m=this.U=this.o=null;this.bb=this.W=0;this.Ya=nd("failFast",!1,a);this.F=this.C=this.u=this.s=this.l=null;this.aa=!0;this.Ca=this.V=-1;this.ba=this.v=this.B=0;this.Wa=nd("baseRetryDelayMs",5E3,a);this.gb=nd("retryDelaySeedMs",1E4,a);this.Za=nd("forwardChannelMaxRetries",2,a);this.za=nd("forwardChannelRequestTimeoutMs",2E4,a);this.sa=a&&a.xmlHttpFactory||void 0;this.ab=a&&a.Wb||void 0;this.Fa=
a&&a.useFetchStreams||!1;this.M=void 0;this.J=a&&a.supportsCrossDomainXhr||!1;this.L="";this.h=new yc(a&&a.concurrentRequestLimit);this.Ga=new Yc;this.S=a&&a.fastHandshake||!1;this.R=a&&a.encodeInitMessageHeaders||!1;this.S&&this.R&&(this.R=!1);this.Xa=a&&a.Ub||!1;a&&a.Aa&&this.j.Aa();a&&a.forceLongPolling&&(this.aa=!1);this.da=!this.S&&this.aa&&a&&a.detectBufferingProxy||!1;this.ma=void 0;a&&a.longPollingTimeout&&0<a.longPollingTimeout&&(this.ma=a.longPollingTimeout);this.ea=void 0;this.T=0;this.N=
!1;this.na=this.A=null}n=od.prototype;n.oa=8;n.G=1;n.connect=function(a,b,c,d){K(0);this.Z=a;this.H=b||{};c&&void 0!==d&&(this.H.OSID=c,this.H.OAID=d);this.F=this.aa;this.I=sc(this,null,this.Z);vc(this)};
function wc(a){pd(a);if(3==a.G){var b=a.W++,c=N(a.I);R(c,"SID",a.L);R(c,"RID",b);R(c,"TYPE","terminate");qd(a,c);b=new M(a,a.j,b);b.M=2;b.v=Xb(N(c));c=!1;if(x.navigator&&x.navigator.sendBeacon)try{c=x.navigator.sendBeacon(b.v.toString(),"")}catch(d){}!c&&x.Image&&((new Image).src=b.v,c=!0);c||(b.g=ac(b.j,null),b.g.ga(b.v));b.F=Date.now();Zb(b)}rd(a)}function oc(a){a.g&&(hc(a),a.g.cancel(),a.g=null)}
function pd(a){oc(a);a.u&&(x.clearTimeout(a.u),a.u=null);nc(a);a.h.cancel();a.s&&("number"===typeof a.s&&x.clearTimeout(a.s),a.s=null)}function vc(a){zc(a.h)||a.s||(a.s=!0,Ma(a.Ja,a),a.B=0)}function sd(a,b){if(qc(a.h)>=a.h.j-(a.s?1:0))return!1;if(a.s)return a.i=b.D.concat(a.i),!0;if(1==a.G||2==a.G||a.B>=(a.Ya?0:a.Za))return!1;a.s=Jb(z(a.Ja,a,b),td(a,a.B));a.B++;return!0}
n.Ja=function(a){if(this.s)if(this.s=null,1==this.G){if(!a){this.W=Math.floor(1E5*Math.random());a=this.W++;var b=new M(this,this.j,a),c=this.o;this.U&&(c?(c=Aa(c),Ca(c,this.U)):c=this.U);null!==this.m||this.R||(b.H=c,c=null);var d;if(this.S)a:{for(var e=d=0;e<this.i.length;e++){b:{var f=this.i[e];if("__data__"in f.map&&(f=f.map.__data__,"string"===typeof f)){f=f.length;break b}f=void 0}if(void 0===f)break;d+=f;if(4096<d){d=e;break a}if(4096===d||e===this.i.length-1){d=e+1;break a}}d=1E3}else d=1E3;
d=ud(this,b,d);e=N(this.I);R(e,"RID",a);R(e,"CVER",22);this.D&&R(e,"X-HTTP-Session-Id",this.D);qd(this,e);c&&(this.R?d="headers="+encodeURIComponent(String(ed(c)))+"&"+d:this.m&&fd(e,this.m,c));rc(this.h,b);this.Xa&&R(e,"TYPE","init");this.S?(R(e,"$req",d),R(e,"SID","null"),b.V=!0,Wb(b,e,null)):Wb(b,e,d);this.G=2}}else 3==this.G&&(a?vd(this,a):0==this.i.length||zc(this.h)||vd(this))};
function vd(a,b){var c;b?c=b.l:c=a.W++;var d=N(a.I);R(d,"SID",a.L);R(d,"RID",c);R(d,"AID",a.V);qd(a,d);a.m&&a.o&&fd(d,a.m,a.o);c=new M(a,a.j,c,a.B+1);null===a.m&&(c.H=a.o);b&&(a.i=b.D.concat(a.i));b=ud(a,c,1E3);c.I=Math.round(.5*a.za)+Math.round(.5*a.za*Math.random());rc(a.h,c);Wb(c,d,b)}function qd(a,b){a.H&&ya(a.H,function(c,d){R(b,d,c)});a.l&&Dc({},function(c,d){R(b,d,c)})}
function ud(a,b,c){c=Math.min(a.i.length,c);var d=a.l?z(a.l.Qa,a.l,a):null;a:for(var e=a.i,f=-1;;){var g=["count="+c];-1==f?0<c?(f=e[0].g,g.push("ofs="+f)):f=0:g.push("ofs="+f);for(var h=!0,k=0;k<c;k++){var l=e[k].g,m=e[k].map;l-=f;if(0>l)f=Math.max(0,e[k].g-100),h=!1;else try{Zc(m,g,"req"+l+"_")}catch(q){d&&d(m)}}if(h){d=g.join("&");break a}}a=a.i.splice(0,c);b.D=a;return d}function uc(a){a.g||a.u||(a.ba=1,Ma(a.Ia,a),a.v=0)}
function pc(a){if(a.g||a.u||3<=a.v)return!1;a.ba++;a.u=Jb(z(a.Ia,a),td(a,a.v));a.v++;return!0}n.Ia=function(){this.u=null;wd(this);if(this.da&&!(this.N||null==this.g||0>=this.T)){var a=2*this.T;this.j.info("BP detection timer enabled: "+a);this.A=Jb(z(this.eb,this),a)}};n.eb=function(){this.A&&(this.A=null,this.j.info("BP detection timeout reached."),this.j.info("Buffering proxy detected and switch to long-polling!"),this.F=!1,this.N=!0,K(10),oc(this),wd(this))};
function hc(a){null!=a.A&&(x.clearTimeout(a.A),a.A=null)}function wd(a){a.g=new M(a,a.j,"rpc",a.ba);null===a.m&&(a.g.H=a.o);a.g.R=0;var b=N(a.ta);R(b,"RID","rpc");R(b,"SID",a.L);R(b,"AID",a.V);R(b,"CI",a.F?"0":"1");!a.F&&a.ma&&R(b,"TO",a.ma);R(b,"TYPE","xmlhttp");qd(a,b);a.m&&a.o&&fd(b,a.m,a.o);a.M&&(a.g.I=a.M);var c=a.g;a=a.la;c.M=1;c.v=Xb(N(b));c.m=null;c.S=!0;Yb(c,a)}n.cb=function(){null!=this.C&&(this.C=null,oc(this),pc(this),K(19))};function nc(a){null!=a.C&&(x.clearTimeout(a.C),a.C=null)}
function jc(a,b){var c=null;if(a.g==b){nc(a);hc(a);a.g=null;var d=2}else if(mc(a.h,b))c=b.D,tc(a.h,b),d=1;else return;if(0!=a.G)if(b.o)if(1==d){c=b.m?b.m.length:0;b=Date.now()-b.F;var e=a.B;d=Eb();G(d,new Ib(d,c,b,e));vc(a)}else uc(a);else if(e=b.s,3==e||0==e&&0<b.aa||!(1==d&&sd(a,b)||2==d&&pc(a)))switch(c&&0<c.length&&(b=a.h,b.i=b.i.concat(c)),e){case 1:Q(a,5);break;case 4:Q(a,10);break;case 3:Q(a,6);break;default:Q(a,2)}}
function td(a,b){var c=a.Wa+Math.floor(Math.random()*a.gb);a.isActive()||(c*=2);return c*b}function Q(a,b){a.j.info("Error code "+b);if(2==b){var c=z(a.ib,a),d=a.ab,e=!d;d=new S(d||"//www.google.com/images/cleardot.gif");x.location&&"http"==x.location.protocol||Gc(d,"https");Xb(d);e?Wc(d.toString(),c):Xc(d.toString(),c)}else K(2);a.G=0;a.l&&a.l.va(b);rd(a);pd(a)}n.ib=function(a){a?(this.j.info("Successfully pinged google.com"),K(2)):(this.j.info("Failed to ping google.com"),K(1))};
function rd(a){a.G=0;a.na=[];if(a.l){var b=Ac(a.h);if(0!=b.length||0!=a.i.length)ta(a.na,b),ta(a.na,a.i),a.h.i.length=0,sa(a.i),a.i.length=0;a.l.ua()}}function sc(a,b,c){var d=c instanceof S?N(c):new S(c);if(""!=d.g)b&&(d.g=b+"."+d.g),Hc(d,d.m);else{var e=x.location;d=e.protocol;b=b?b+"."+e.hostname:e.hostname;e=+e.port;var f=new S(null);d&&Gc(f,d);b&&(f.g=b);e&&Hc(f,e);c&&(f.h=c);d=f}c=a.D;b=a.Ba;c&&b&&R(d,c,b);R(d,"VER",a.oa);qd(a,d);return d}
function ac(a,b,c){if(b&&!a.J)throw Error("Can't create secondary domain capable XhrIo object.");b=a.Fa&&!a.sa?new X(new $c({hb:c})):new X(a.sa);b.Ka(a.J);return b}n.isActive=function(){return!!this.l&&this.l.isActive(this)};function xd(){}n=xd.prototype;n.xa=function(){};n.wa=function(){};n.va=function(){};n.ua=function(){};n.isActive=function(){return!0};n.Qa=function(){};function yd(){}yd.prototype.g=function(a,b){return new Y(a,b)};
function Y(a,b){F.call(this);this.g=new od(b);this.l=a;this.h=b&&b.messageUrlParams||null;a=b&&b.messageHeaders||null;b&&b.clientProtocolHeaderRequired&&(a?a["X-Client-Protocol"]="webchannel":a={"X-Client-Protocol":"webchannel"});this.g.o=a;a=b&&b.initMessageHeaders||null;b&&b.messageContentType&&(a?a["X-WebChannel-Content-Type"]=b.messageContentType:a={"X-WebChannel-Content-Type":b.messageContentType});b&&b.ya&&(a?a["X-WebChannel-Client-Profile"]=b.ya:a={"X-WebChannel-Client-Profile":b.ya});this.g.U=
a;(a=b&&b.Vb)&&!va(a)&&(this.g.m=a);this.v=b&&b.supportsCrossDomainXhr||!1;this.u=b&&b.sendRawJson||!1;(b=b&&b.httpSessionIdParam)&&!va(b)&&(this.g.D=b,a=this.h,null!==a&&b in a&&(a=this.h,b in a&&delete a[b]));this.j=new Z(this)}A(Y,F);Y.prototype.m=function(){this.g.l=this.j;this.v&&(this.g.J=!0);this.g.connect(this.l,this.h||void 0)};Y.prototype.close=function(){wc(this.g)};
Y.prototype.o=function(a){var b=this.g;if("string"===typeof a){var c={};c.__data__=a;a=c}else this.u&&(c={},c.__data__=ub(a),a=c);b.i.push(new xc(b.bb++,a));3==b.G&&vc(b)};Y.prototype.O=function(){this.g.l=null;delete this.j;wc(this.g);delete this.g;Y.Y.O.call(this)};
function zd(a){Bb.call(this);a.__headers__&&(this.headers=a.__headers__,this.statusCode=a.__status__,delete a.__headers__,delete a.__status__);var b=a.__sm__;if(b){a:{for(var c in b){a=c;break a}a=void 0}if(this.i=a)a=this.i,b=null!==b&&a in b?b[a]:void 0;this.data=b}else this.data=a}A(zd,Bb);function Ad(){Cb.call(this);this.status=1}A(Ad,Cb);function Z(a){this.g=a}A(Z,xd);Z.prototype.xa=function(){G(this.g,"a")};Z.prototype.wa=function(a){G(this.g,new zd(a))};
Z.prototype.va=function(a){G(this.g,new Ad(a))};Z.prototype.ua=function(){G(this.g,"b")};yd.prototype.createWebChannel=yd.prototype.g;Y.prototype.send=Y.prototype.o;Y.prototype.open=Y.prototype.m;Y.prototype.close=Y.prototype.close;module.exports.createWebChannelTransport=function(){return new yd};module.exports.getStatEventTarget=function(){return Eb()};module.exports.Event=I;module.exports.Stat={pb:0,sb:1,tb:2,Mb:3,Rb:4,Ob:5,Pb:6,Nb:7,Lb:8,Qb:9,PROXY:10,NOPROXY:11,Jb:12,Fb:13,Gb:14,Eb:15,Hb:16,Ib:17,lb:18,kb:19,mb:20};Pb.NO_ERROR=0;Pb.TIMEOUT=8;Pb.HTTP_ERROR=6;
module.exports.ErrorCode=Pb;Qb.COMPLETE="complete";module.exports.EventType=Qb;zb.EventType=Ab;Ab.OPEN="a";Ab.CLOSE="b";Ab.ERROR="c";Ab.MESSAGE="d";F.prototype.listen=F.prototype.L;module.exports.WebChannel=zb;module.exports.FetchXmlHttpFactory=$c;X.prototype.listenOnce=X.prototype.M;X.prototype.getLastError=X.prototype.Na;X.prototype.getLastErrorCode=X.prototype.Ea;X.prototype.getStatus=X.prototype.ca;X.prototype.getResponseJson=X.prototype.Ra;X.prototype.getResponseText=X.prototype.ra;
X.prototype.send=X.prototype.ga;X.prototype.setWithCredentials=X.prototype.Ka;module.exports.XhrIo=X;}).apply( typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self  : typeof window !== 'undefined' ? window  : {});
