{"name": "d3-sankey", "version": "0.12.3", "description": "Visualize flow between nodes in a directed acyclic network.", "keywords": ["d3", "d3-module", "sankey"], "author": {"name": "<PERSON>", "url": "https://bost.ocks.org/mike/"}, "license": "BSD-3-<PERSON><PERSON>", "main": "dist/d3-sankey.js", "unpkg": "dist/d3-sankey.min.js", "module": "src/index.js", "homepage": "https://github.com/d3/d3-sankey", "repository": {"type": "git", "url": "https://github.com/d3/d3-sankey.git"}, "files": ["dist/**/*.js", "src/**/*.js"], "scripts": {"pretest": "rollup -c", "test": "tape 'test/**/*-test.js' && eslint src", "prepublishOnly": "rm -rf dist && yarn test", "postpublish": "git push && git push --tags && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js"}, "dependencies": {"d3-array": "1 - 2", "d3-shape": "^1.2.0"}, "devDependencies": {"eslint": "6", "rollup": "1", "rollup-plugin-terser": "5", "tape": "4"}}