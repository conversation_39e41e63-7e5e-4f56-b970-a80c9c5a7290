{"version": 3, "file": "firebase-data-connect.js", "sources": ["../util/src/errors.ts", "../component/src/component.ts", "../logger/src/logger.ts", "../data-connect/src/core/version.ts", "../data-connect/src/core/AppCheckTokenProvider.ts", "../data-connect/src/core/error.ts", "../data-connect/src/logger.ts", "../data-connect/src/core/FirebaseAuthProvider.ts", "../data-connect/src/api/Reference.ts", "../data-connect/src/util/encoder.ts", "../data-connect/src/core/QueryManager.ts", "../data-connect/src/util/map.ts", "../data-connect/src/network/transport/index.ts", "../data-connect/src/util/url.ts", "../data-connect/src/network/fetch.ts", "../data-connect/src/network/transport/rest.ts", "../data-connect/src/api/Mutation.ts", "../data-connect/src/api/DataConnect.ts", "../data-connect/src/api/query.ts", "../data-connect/src/util/validateArgs.ts", "../data-connect/src/api.browser.ts", "../data-connect/src/register.ts", "../data-connect/src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type LogLevelString =\n  | 'debug'\n  | 'verbose'\n  | 'info'\n  | 'warn'\n  | 'error'\n  | 'silent';\n\nexport interface LogOptions {\n  level: LogLevelString;\n}\n\nexport type LogCallback = (callbackParams: LogCallbackParams) => void;\n\nexport interface LogCallbackParams {\n  level: LogLevelString;\n  message: string;\n  args: unknown[];\n  type: string;\n}\n\n/**\n * A container for all of the Logger instances\n */\nexport const instances: Logger[] = [];\n\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nexport enum LogLevel {\n  DEBUG,\n  VERBOSE,\n  INFO,\n  WARN,\n  ERROR,\n  SILENT\n}\n\nconst levelStringToEnum: { [key in LogLevelString]: LogLevel } = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n\n/**\n * The default log level\n */\nconst defaultLogLevel: LogLevel = LogLevel.INFO;\n\n/**\n * We allow users the ability to pass their own log handler. We will pass the\n * type of log, the current log level, and any other arguments passed (i.e. the\n * messages that the user wants to log) to this function.\n */\nexport type LogHandler = (\n  loggerInstance: Logger,\n  logType: LogLevel,\n  ...args: unknown[]\n) => void;\n\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler: LogHandler = (instance, logType, ...args): void => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType as keyof typeof ConsoleMethod];\n  if (method) {\n    console[method as 'log' | 'info' | 'warn' | 'error'](\n      `[${now}]  ${instance.name}:`,\n      ...args\n    );\n  } else {\n    throw new Error(\n      `Attempted to log a message with an invalid logType (value: ${logType})`\n    );\n  }\n};\n\nexport class Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(public name: string) {\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n\n  /**\n   * The log level of the given Logger instance.\n   */\n  private _logLevel = defaultLogLevel;\n\n  get logLevel(): LogLevel {\n    return this._logLevel;\n  }\n\n  set logLevel(val: LogLevel) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val: LogLevel | LogLevelString): void {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n\n  /**\n   * The main (internal) log handler for the Logger instance.\n   * Can be set to a new function in internal package code but not by user.\n   */\n  private _logHandler: LogHandler = defaultLogHandler;\n  get logHandler(): LogHandler {\n    return this._logHandler;\n  }\n  set logHandler(val: LogHandler) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n\n  /**\n   * The optional, additional, user-defined log handler for the Logger instance.\n   */\n  private _userLogHandler: LogHandler | null = null;\n  get userLogHandler(): LogHandler | null {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val: LogHandler | null) {\n    this._userLogHandler = val;\n  }\n\n  /**\n   * The functions below are all based on the `console` interface\n   */\n\n  debug(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args: unknown[]): void {\n    this._userLogHandler &&\n      this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\n\nexport function setLogLevel(level: LogLevelString | LogLevel): void {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\n\nexport function setUserLogHandler(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  for (const instance of instances) {\n    let customLogLevel: LogLevel | null = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (\n        instance: Logger,\n        level: LogLevel,\n        ...args: unknown[]\n      ) => {\n        const message = args\n          .map(arg => {\n            if (arg == null) {\n              return null;\n            } else if (typeof arg === 'string') {\n              return arg;\n            } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n              return arg.toString();\n            } else if (arg instanceof Error) {\n              return arg.message;\n            } else {\n              try {\n                return JSON.stringify(arg);\n              } catch (ignored) {\n                return null;\n              }\n            }\n          })\n          .filter(arg => arg)\n          .join(' ');\n        if (level >= (customLogLevel ?? instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase() as LogLevelString,\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** The semver (www.semver.org) version of the SDK. */\nexport let SDK_VERSION = '';\n\n/**\n * SDK_VERSION should be set before any database instance is created\n * @internal\n */\nexport function setSDKVersion(version: string): void {\n  SDK_VERSION = version;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _isFirebaseServerApp } from '@firebase/app';\nimport {\n  AppCheckInternalComponentName,\n  AppCheckTokenListener,\n  AppCheckTokenResult,\n  FirebaseAppCheckInternal\n} from '@firebase/app-check-interop-types';\nimport { Provider } from '@firebase/component';\n\n/**\n * @internal\n * Abstraction around AppCheck's token fetching capabilities.\n */\nexport class AppCheckTokenProvider {\n  private appCheck?: FirebaseAppCheckInternal;\n  private serverAppAppCheckToken?: string;\n  constructor(\n    app: FirebaseApp,\n    private appCheckProvider?: Provider<AppCheckInternalComponentName>\n  ) {\n    if (_isFirebaseServerApp(app) && app.settings.appCheckToken) {\n      this.serverAppAppCheckToken = app.settings.appCheckToken;\n    }\n    this.appCheck = appCheckProvider?.getImmediate({ optional: true });\n    if (!this.appCheck) {\n      void appCheckProvider\n        ?.get()\n        .then(appCheck => (this.appCheck = appCheck))\n        .catch();\n    }\n  }\n\n  getToken(): Promise<AppCheckTokenResult> {\n    if (this.serverAppAppCheckToken) {\n      return Promise.resolve({ token: this.serverAppAppCheckToken });\n    }\n\n    if (!this.appCheck) {\n      return new Promise<AppCheckTokenResult>((resolve, reject) => {\n        // Support delayed initialization of FirebaseAppCheck. This allows our\n        // customers to initialize the RTDB SDK before initializing Firebase\n        // AppCheck and ensures that all requests are authenticated if a token\n        // becomes available before the timoeout below expires.\n        setTimeout(() => {\n          if (this.appCheck) {\n            this.getToken().then(resolve, reject);\n          } else {\n            resolve(null);\n          }\n        }, 0);\n      });\n    }\n    return this.appCheck.getToken();\n  }\n\n  addTokenChangeListener(listener: AppCheckTokenListener): void {\n    void this.appCheckProvider\n      ?.get()\n      .then(appCheck => appCheck.addTokenListener(listener));\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\n\nexport type DataConnectErrorCode =\n  | 'other'\n  | 'already-initialized'\n  | 'not-initialized'\n  | 'not-supported'\n  | 'invalid-argument'\n  | 'partial-error'\n  | 'unauthorized';\n\nexport type Code = DataConnectErrorCode;\n\nexport const Code = {\n  OTHER: 'other' as DataConnectErrorCode,\n  ALREADY_INITIALIZED: 'already-initialized' as DataConnectErrorCode,\n  NOT_INITIALIZED: 'not-initialized' as DataConnect<PERSON><PERSON>rCode,\n  NOT_SUPPORTED: 'not-supported' as DataConnect<PERSON>rrorCode,\n  INVALID_ARGUMENT: 'invalid-argument' as DataConnectErrorCode,\n  PARTIAL_ERROR: 'partial-error' as DataConnectErrorCode,\n  UNAUTHORIZED: 'unauthorized' as DataConnectErrorCode\n};\n\n/** An error returned by a DataConnect operation. */\nexport class DataConnectError extends FirebaseError {\n  /** @internal */\n  readonly name: string = 'DataConnectError';\n\n  constructor(code: Code, message: string) {\n    super(code, message);\n\n    // Ensure the instanceof operator works as expected on subclasses of Error.\n    // See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#custom_error_types\n    // and https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    Object.setPrototypeOf(this, DataConnectError.prototype);\n  }\n\n  /** @internal */\n  toString(): string {\n    return `${this.name}[code=${this.code}]: ${this.message}`;\n  }\n}\n\n/** An error returned by a DataConnect operation. */\nexport class DataConnectOperationError extends DataConnectError {\n  /** @internal */\n  readonly name: string = 'DataConnectOperationError';\n\n  /** The response received from the backend. */\n  readonly response: DataConnectOperationFailureResponse;\n\n  /** @hideconstructor */\n  constructor(message: string, response: DataConnectOperationFailureResponse) {\n    super(Code.PARTIAL_ERROR, message);\n    this.response = response;\n  }\n}\n\nexport interface DataConnectOperationFailureResponse {\n  // The \"data\" provided by the backend in the response message.\n  //\n  // Will be `undefined` if no \"data\" was provided in the response message.\n  // Otherwise, will be `null` if `null` was explicitly specified as the \"data\"\n  // in the response message. Otherwise, will be the value of the \"data\"\n  // specified as the \"data\" in the response message\n  readonly data?: Record<string, unknown> | null;\n\n  // The list of errors provided by the backend in the response message.\n  readonly errors: DataConnectOperationFailureResponseErrorInfo[];\n}\n\n// Information about the error, as provided in the response from the backend.\n// See https://spec.graphql.org/draft/#sec-Errors\nexport interface DataConnectOperationFailureResponseErrorInfo {\n  // The error message.\n  readonly message: string;\n\n  // The path of the field in the response data to which this error relates.\n  // String values in this array refer to field names. Numeric values in this\n  // array always satisfy `Number.isInteger()` and refer to the index in an\n  // array.\n  readonly path: Array<string | number>;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { Logger, LogLevelString } from '@firebase/logger';\n\nimport { SDK_VERSION } from './core/version';\n\nconst logger = new Logger('@firebase/data-connect');\nexport function setLogLevel(logLevel: LogLevelString): void {\n  logger.setLogLevel(logLevel);\n}\nexport function logDebug(msg: string): void {\n  logger.debug(`DataConnect (${SDK_VERSION}): ${msg}`);\n}\n\nexport function logError(msg: string): void {\n  logger.error(`DataConnect (${SDK_VERSION}): ${msg}`);\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseOptions } from '@firebase/app-types';\nimport {\n  FirebaseAuthInternal,\n  FirebaseAuthInternalName,\n  FirebaseAuthTokenData\n} from '@firebase/auth-interop-types';\nimport { Provider } from '@firebase/component';\n\nimport { logDebug, logError } from '../logger';\n\n// @internal\nexport interface AuthTokenProvider {\n  getToken(forceRefresh: boolean): Promise<FirebaseAuthTokenData | null>;\n  addTokenChangeListener(listener: AuthTokenListener): void;\n}\nexport type AuthTokenListener = (token: string | null) => void;\n\n// @internal\nexport class FirebaseAuthProvider implements AuthTokenProvider {\n  private _auth: FirebaseAuthInternal;\n  constructor(\n    private _appName: string,\n    private _options: FirebaseOptions,\n    private _authProvider: Provider<FirebaseAuthInternalName>\n  ) {\n    this._auth = _authProvider.getImmediate({ optional: true })!;\n    if (!this._auth) {\n      _authProvider.onInit(auth => (this._auth = auth));\n    }\n  }\n  getToken(forceRefresh: boolean): Promise<FirebaseAuthTokenData | null> {\n    if (!this._auth) {\n      return new Promise((resolve, reject) => {\n        setTimeout(() => {\n          if (this._auth) {\n            this.getToken(forceRefresh).then(resolve, reject);\n          } else {\n            resolve(null);\n          }\n        }, 0);\n      });\n    }\n    return this._auth.getToken(forceRefresh).catch(error => {\n      if (error && error.code === 'auth/token-not-initialized') {\n        logDebug(\n          'Got auth/token-not-initialized error.  Treating as null token.'\n        );\n        return null;\n      } else {\n        logError(\n          'Error received when attempting to retrieve token: ' +\n            JSON.stringify(error)\n        );\n        return Promise.reject(error);\n      }\n    });\n  }\n  addTokenChangeListener(listener: AuthTokenListener): void {\n    this._auth?.addAuthTokenListener(listener);\n  }\n  removeTokenChangeListener(listener: (token: string | null) => void): void {\n    this._authProvider\n      .get()\n      .then(auth => auth.removeAuthTokenListener(listener))\n      .catch(err => logError(err));\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnect, DataConnectOptions } from './DataConnect';\nexport const QUERY_STR = 'query';\nexport const MUTATION_STR = 'mutation';\nexport type ReferenceType = typeof QUERY_STR | typeof MUTATION_STR;\n\nexport const SOURCE_SERVER = 'SERVER';\nexport const SOURCE_CACHE = 'CACHE';\nexport type DataSource = typeof SOURCE_CACHE | typeof SOURCE_SERVER;\n\nexport interface OpResult<Data> {\n  data: Data;\n  source: DataSource;\n  fetchTime: string;\n}\n\nexport interface OperationRef<_Data, Variables> {\n  name: string;\n  variables: Variables;\n  refType: ReferenceType;\n  dataConnect: DataConnect;\n}\n\nexport interface DataConnectResult<Data, Variables> extends OpResult<Data> {\n  ref: OperationRef<Data, Variables>;\n  // future metadata\n}\n\n/**\n * Serialized RefInfo as a result of `QueryResult.toJSON().refInfo`\n */\nexport interface RefInfo<Variables> {\n  name: string;\n  variables: Variables;\n  connectorConfig: DataConnectOptions;\n}\n/**\n * Serialized Ref as a result of `QueryResult.toJSON()`\n */\nexport interface SerializedRef<Data, Variables> extends OpResult<Data> {\n  refInfo: RefInfo<Variables>;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type HmacImpl = (obj: unknown) => string;\nexport let encoderImpl: HmacImpl;\nexport function setEncoder(encoder: HmacImpl): void {\n  encoderImpl = encoder;\n}\nsetEncoder(o => JSON.stringify(o));\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  DataConnectSubscription,\n  OnErrorSubscription,\n  OnResultSubscription,\n  QueryPromise,\n  QueryRef,\n  QueryResult\n} from '../api/query';\nimport {\n  OperationRef,\n  QUERY_STR,\n  OpResult,\n  SerializedRef,\n  SOURCE_SERVER,\n  DataSource,\n  SOURCE_CACHE\n} from '../api/Reference';\nimport { logDebug } from '../logger';\nimport { DataConnectTransport } from '../network';\nimport { encoderImpl } from '../util/encoder';\nimport { setIfNotExists } from '../util/map';\n\nimport { Code, DataConnectError } from './error';\n\ninterface TrackedQuery<Data, Variables> {\n  ref: Omit<OperationRef<Data, Variables>, 'dataConnect'>;\n  subscriptions: Array<DataConnectSubscription<Data, Variables>>;\n  currentCache: OpResult<Data> | null;\n  lastError: DataConnectError | null;\n}\n\nfunction getRefSerializer<Data, Variables>(\n  queryRef: QueryRef<Data, Variables>,\n  data: Data,\n  source: DataSource\n) {\n  return function toJSON(): SerializedRef<Data, Variables> {\n    return {\n      data,\n      refInfo: {\n        name: queryRef.name,\n        variables: queryRef.variables,\n        connectorConfig: {\n          projectId: queryRef.dataConnect.app.options.projectId!,\n          ...queryRef.dataConnect.getSettings()\n        }\n      },\n      fetchTime: Date.now().toLocaleString(),\n      source\n    };\n  };\n}\n\nexport class QueryManager {\n  _queries: Map<string, TrackedQuery<unknown, unknown>>;\n  constructor(private transport: DataConnectTransport) {\n    this._queries = new Map();\n  }\n  track<Data, Variables>(\n    queryName: string,\n    variables: Variables,\n    initialCache?: OpResult<Data>\n  ): TrackedQuery<Data, Variables> {\n    const ref: TrackedQuery<Data, Variables>['ref'] = {\n      name: queryName,\n      variables,\n      refType: QUERY_STR\n    };\n    const key = encoderImpl(ref);\n    const newTrackedQuery: TrackedQuery<Data, Variables> = {\n      ref,\n      subscriptions: [],\n      currentCache: initialCache || null,\n      lastError: null\n    };\n    // @ts-ignore\n    setIfNotExists(this._queries, key, newTrackedQuery);\n    return this._queries.get(key) as TrackedQuery<Data, Variables>;\n  }\n  addSubscription<Data, Variables>(\n    queryRef: OperationRef<Data, Variables>,\n    onResultCallback: OnResultSubscription<Data, Variables>,\n    onErrorCallback?: OnErrorSubscription,\n    initialCache?: OpResult<Data>\n  ): () => void {\n    const key = encoderImpl({\n      name: queryRef.name,\n      variables: queryRef.variables,\n      refType: QUERY_STR\n    });\n    const trackedQuery = this._queries.get(key) as TrackedQuery<\n      Data,\n      Variables\n    >;\n    const subscription = {\n      userCallback: onResultCallback,\n      errCallback: onErrorCallback\n    };\n    const unsubscribe = (): void => {\n      const trackedQuery = this._queries.get(key)!;\n      trackedQuery.subscriptions = trackedQuery.subscriptions.filter(\n        sub => sub !== subscription\n      );\n    };\n    if (initialCache && trackedQuery.currentCache !== initialCache) {\n      logDebug('Initial cache found. Comparing dates.');\n      if (\n        !trackedQuery.currentCache ||\n        (trackedQuery.currentCache &&\n          compareDates(\n            trackedQuery.currentCache.fetchTime,\n            initialCache.fetchTime\n          ))\n      ) {\n        trackedQuery.currentCache = initialCache;\n      }\n    }\n    if (trackedQuery.currentCache !== null) {\n      const cachedData = trackedQuery.currentCache.data;\n      onResultCallback({\n        data: cachedData,\n        source: SOURCE_CACHE,\n        ref: queryRef as QueryRef<Data, Variables>,\n        toJSON: getRefSerializer(\n          queryRef as QueryRef<Data, Variables>,\n          trackedQuery.currentCache.data,\n          SOURCE_CACHE\n        ),\n        fetchTime: trackedQuery.currentCache.fetchTime\n      });\n      if (trackedQuery.lastError !== null && onErrorCallback) {\n        onErrorCallback(undefined);\n      }\n    }\n\n    trackedQuery.subscriptions.push({\n      userCallback: onResultCallback,\n      errCallback: onErrorCallback,\n      unsubscribe\n    });\n    if (!trackedQuery.currentCache) {\n      logDebug(\n        `No cache available for query ${\n          queryRef.name\n        } with variables ${JSON.stringify(\n          queryRef.variables\n        )}. Calling executeQuery.`\n      );\n      const promise = this.executeQuery(queryRef as QueryRef<Data, Variables>);\n      // We want to ignore the error and let subscriptions handle it\n      promise.then(undefined, err => {});\n    }\n    return unsubscribe;\n  }\n  executeQuery<Data, Variables>(\n    queryRef: QueryRef<Data, Variables>\n  ): QueryPromise<Data, Variables> {\n    if (queryRef.refType !== QUERY_STR) {\n      throw new DataConnectError(\n        Code.INVALID_ARGUMENT,\n        `ExecuteQuery can only execute query operation`\n      );\n    }\n    const key = encoderImpl({\n      name: queryRef.name,\n      variables: queryRef.variables,\n      refType: QUERY_STR\n    });\n    const trackedQuery = this._queries.get(key)!;\n    const result = this.transport.invokeQuery<Data, Variables>(\n      queryRef.name,\n      queryRef.variables\n    );\n    const newR = result.then(\n      res => {\n        const fetchTime = new Date().toString();\n        const result: QueryResult<Data, Variables> = {\n          ...res,\n          source: SOURCE_SERVER,\n          ref: queryRef,\n          toJSON: getRefSerializer(queryRef, res.data, SOURCE_SERVER),\n          fetchTime\n        };\n        trackedQuery.subscriptions.forEach(subscription => {\n          subscription.userCallback(result);\n        });\n        trackedQuery.currentCache = {\n          data: res.data,\n          source: SOURCE_CACHE,\n          fetchTime\n        };\n        return result;\n      },\n      err => {\n        trackedQuery.lastError = err;\n        trackedQuery.subscriptions.forEach(subscription => {\n          if (subscription.errCallback) {\n            subscription.errCallback(err);\n          }\n        });\n        throw err;\n      }\n    );\n\n    return newR;\n  }\n  enableEmulator(host: string, port: number): void {\n    this.transport.useEmulator(host, port);\n  }\n}\nfunction compareDates(str1: string, str2: string): boolean {\n  const date1 = new Date(str1);\n  const date2 = new Date(str2);\n  return date1.getTime() < date2.getTime();\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function setIfNotExists<T>(\n  map: Map<string, T>,\n  key: string,\n  val: T\n): void {\n  if (!map.has(key)) {\n    map.set(key, val);\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectOptions, TransportOptions } from '../../api/DataConnect';\nimport { AppCheckTokenProvider } from '../../core/AppCheckTokenProvider';\nimport { AuthTokenProvider } from '../../core/FirebaseAuthProvider';\n\n/**\n * enum representing different flavors of the SDK used by developers\n * use the CallerSdkType for type-checking, and the CallerSdkTypeEnum for value-checking/assigning\n */\nexport type CallerSdkType =\n  | 'Base' // Core JS SDK\n  | 'Generated' // Generated JS SDK\n  | 'TanstackReactCore' // Tanstack non-generated React SDK\n  | 'GeneratedReact' // Generated React SDK\n  | 'TanstackAngularCore' // Tanstack non-generated Angular SDK\n  | 'GeneratedAngular'; // Generated Angular SDK\nexport const CallerSdkTypeEnum = {\n  Base: 'Base', // Core JS SDK\n  Generated: 'Generated', // Generated JS SDK\n  TanstackReactCore: 'TanstackReactCore', // Tanstack non-generated React SDK\n  GeneratedReact: 'GeneratedReact', // Tanstack non-generated Angular SDK\n  TanstackAngularCore: 'TanstackAngularCore', // Tanstack non-generated Angular SDK\n  GeneratedAngular: 'GeneratedAngular' // Generated Angular SDK\n} as const;\n\n/**\n * @internal\n */\nexport interface DataConnectTransport {\n  invokeQuery<T, U>(\n    queryName: string,\n    body?: U\n  ): Promise<{ data: T; errors: Error[] }>;\n  invokeMutation<T, U>(\n    queryName: string,\n    body?: U\n  ): Promise<{ data: T; errors: Error[] }>;\n  useEmulator(host: string, port?: number, sslEnabled?: boolean): void;\n  onTokenChanged: (token: string | null) => void;\n  _setCallerSdkType(callerSdkType: CallerSdkType): void;\n}\n\n/**\n * @internal\n */\nexport type TransportClass = new (\n  options: DataConnectOptions,\n  apiKey?: string,\n  appId?: string,\n  authProvider?: AuthTokenProvider,\n  appCheckProvider?: AppCheckTokenProvider,\n  transportOptions?: TransportOptions,\n  _isUsingGen?: boolean,\n  _callerSdkType?: CallerSdkType\n) => DataConnectTransport;\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectOptions, TransportOptions } from '../api/DataConnect';\nimport { Code, DataConnectError } from '../core/error';\nimport { logError } from '../logger';\n\nexport function urlBuilder(\n  projectConfig: DataConnectOptions,\n  transportOptions: TransportOptions\n): string {\n  const { connector, location, projectId: project, service } = projectConfig;\n  const { host, sslEnabled, port } = transportOptions;\n  const protocol = sslEnabled ? 'https' : 'http';\n  const realHost = host || `firebasedataconnect.googleapis.com`;\n  let baseUrl = `${protocol}://${realHost}`;\n  if (typeof port === 'number') {\n    baseUrl += `:${port}`;\n  } else if (typeof port !== 'undefined') {\n    logError('Port type is of an invalid type');\n    throw new DataConnectError(\n      Code.INVALID_ARGUMENT,\n      'Incorrect type for port passed in!'\n    );\n  }\n  return `${baseUrl}/v1/projects/${project}/locations/${location}/services/${service}/connectors/${connector}`;\n}\nexport function addToken(url: string, apiKey?: string): string {\n  if (!apiKey) {\n    return url;\n  }\n  const newUrl = new URL(url);\n  newUrl.searchParams.append('key', apiKey);\n  return newUrl.toString();\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Code,\n  DataConnectError,\n  DataConnectOperationError,\n  DataConnectOperationFailureResponse\n} from '../core/error';\nimport { SDK_VERSION } from '../core/version';\nimport { logDebug, logError } from '../logger';\n\nimport { CallerSdkType, CallerSdkTypeEnum } from './transport';\n\nlet connectFetch: typeof fetch | null = globalThis.fetch;\nexport function initializeFetch(fetchImpl: typeof fetch): void {\n  connectFetch = fetchImpl;\n}\nfunction getGoogApiClientValue(\n  _isUsingGen: boolean,\n  _callerSdkType: CallerSdkType\n): string {\n  let str = 'gl-js/ fire/' + SDK_VERSION;\n  if (\n    _callerSdkType !== CallerSdkTypeEnum.Base &&\n    _callerSdkType !== CallerSdkTypeEnum.Generated\n  ) {\n    str += ' js/' + _callerSdkType.toLowerCase();\n  } else if (_isUsingGen || _callerSdkType === CallerSdkTypeEnum.Generated) {\n    str += ' js/gen';\n  }\n  return str;\n}\nexport interface DataConnectFetchBody<T> {\n  name: string;\n  operationName: string;\n  variables: T;\n}\nexport function dcFetch<T, U>(\n  url: string,\n  body: DataConnectFetchBody<U>,\n  { signal }: AbortController,\n  appId: string | null,\n  accessToken: string | null,\n  appCheckToken: string | null,\n  _isUsingGen: boolean,\n  _callerSdkType: CallerSdkType\n): Promise<{ data: T; errors: Error[] }> {\n  if (!connectFetch) {\n    throw new DataConnectError(Code.OTHER, 'No Fetch Implementation detected!');\n  }\n  const headers: HeadersInit = {\n    'Content-Type': 'application/json',\n    'X-Goog-Api-Client': getGoogApiClientValue(_isUsingGen, _callerSdkType)\n  };\n  if (accessToken) {\n    headers['X-Firebase-Auth-Token'] = accessToken;\n  }\n  if (appId) {\n    headers['x-firebase-gmpid'] = appId;\n  }\n  if (appCheckToken) {\n    headers['X-Firebase-AppCheck'] = appCheckToken;\n  }\n  const bodyStr = JSON.stringify(body);\n  logDebug(`Making request out to ${url} with body: ${bodyStr}`);\n\n  return connectFetch(url, {\n    body: bodyStr,\n    method: 'POST',\n    headers,\n    signal\n  })\n    .catch(err => {\n      throw new DataConnectError(\n        Code.OTHER,\n        'Failed to fetch: ' + JSON.stringify(err)\n      );\n    })\n    .then(async response => {\n      let jsonResponse = null;\n      try {\n        jsonResponse = await response.json();\n      } catch (e) {\n        throw new DataConnectError(Code.OTHER, JSON.stringify(e));\n      }\n      const message = getMessage(jsonResponse);\n      if (response.status >= 400) {\n        logError(\n          'Error while performing request: ' + JSON.stringify(jsonResponse)\n        );\n        if (response.status === 401) {\n          throw new DataConnectError(Code.UNAUTHORIZED, message);\n        }\n        throw new DataConnectError(Code.OTHER, message);\n      }\n      return jsonResponse;\n    })\n    .then(res => {\n      if (res.errors && res.errors.length) {\n        const stringified = JSON.stringify(res.errors);\n        const response: DataConnectOperationFailureResponse = {\n          errors: res.errors,\n          data: res.data\n        };\n        throw new DataConnectOperationError(\n          'DataConnect error while performing request: ' + stringified,\n          response\n        );\n      }\n      return res;\n    });\n}\ninterface MessageObject {\n  message?: string;\n}\nfunction getMessage(obj: MessageObject): string {\n  if ('message' in obj) {\n    return obj.message;\n  }\n  return JSON.stringify(obj);\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectOptions, TransportOptions } from '../../api/DataConnect';\nimport { AppCheckTokenProvider } from '../../core/AppCheckTokenProvider';\nimport { DataConnectError, Code } from '../../core/error';\nimport { AuthTokenProvider } from '../../core/FirebaseAuthProvider';\nimport { logDebug } from '../../logger';\nimport { addToken, urlBuilder } from '../../util/url';\nimport { dcFetch } from '../fetch';\n\nimport { CallerSdkType, CallerSdkTypeEnum, DataConnectTransport } from '.';\n\nexport class RESTTransport implements DataConnectTransport {\n  private _host = '';\n  private _port: number | undefined;\n  private _location = 'l';\n  private _connectorName = '';\n  private _secure = true;\n  private _project = 'p';\n  private _serviceName: string;\n  private _accessToken: string | null = null;\n  private _appCheckToken: string | null = null;\n  private _lastToken: string | null = null;\n  constructor(\n    options: DataConnectOptions,\n    private apiKey?: string | undefined,\n    private appId?: string,\n    private authProvider?: AuthTokenProvider | undefined,\n    private appCheckProvider?: AppCheckTokenProvider | undefined,\n    transportOptions?: TransportOptions | undefined,\n    private _isUsingGen = false,\n    private _callerSdkType: CallerSdkType = CallerSdkTypeEnum.Base\n  ) {\n    if (transportOptions) {\n      if (typeof transportOptions.port === 'number') {\n        this._port = transportOptions.port;\n      }\n      if (typeof transportOptions.sslEnabled !== 'undefined') {\n        this._secure = transportOptions.sslEnabled;\n      }\n      this._host = transportOptions.host;\n    }\n    const { location, projectId: project, connector, service } = options;\n    if (location) {\n      this._location = location;\n    }\n    if (project) {\n      this._project = project;\n    }\n    this._serviceName = service;\n    if (!connector) {\n      throw new DataConnectError(\n        Code.INVALID_ARGUMENT,\n        'Connector Name required!'\n      );\n    }\n    this._connectorName = connector;\n    this.authProvider?.addTokenChangeListener(token => {\n      logDebug(`New Token Available: ${token}`);\n      this._accessToken = token;\n    });\n    this.appCheckProvider?.addTokenChangeListener(result => {\n      const { token } = result;\n      logDebug(`New App Check Token Available: ${token}`);\n      this._appCheckToken = token;\n    });\n  }\n  get endpointUrl(): string {\n    return urlBuilder(\n      {\n        connector: this._connectorName,\n        location: this._location,\n        projectId: this._project,\n        service: this._serviceName\n      },\n      { host: this._host, sslEnabled: this._secure, port: this._port }\n    );\n  }\n  useEmulator(host: string, port?: number, isSecure?: boolean): void {\n    this._host = host;\n    if (typeof port === 'number') {\n      this._port = port;\n    }\n    if (typeof isSecure !== 'undefined') {\n      this._secure = isSecure;\n    }\n  }\n  onTokenChanged(newToken: string | null): void {\n    this._accessToken = newToken;\n  }\n\n  async getWithAuth(forceToken = false): Promise<string> {\n    let starterPromise: Promise<string | null> = new Promise(resolve =>\n      resolve(this._accessToken)\n    );\n    if (this.appCheckProvider) {\n      this._appCheckToken = (await this.appCheckProvider.getToken())?.token;\n    }\n    if (this.authProvider) {\n      starterPromise = this.authProvider\n        .getToken(/*forceToken=*/ forceToken)\n        .then(data => {\n          if (!data) {\n            return null;\n          }\n          this._accessToken = data.accessToken;\n          return this._accessToken;\n        });\n    } else {\n      starterPromise = new Promise(resolve => resolve(''));\n    }\n    return starterPromise;\n  }\n\n  _setLastToken(lastToken: string | null): void {\n    this._lastToken = lastToken;\n  }\n\n  withRetry<T>(\n    promiseFactory: () => Promise<{ data: T; errors: Error[] }>,\n    retry = false\n  ): Promise<{ data: T; errors: Error[] }> {\n    let isNewToken = false;\n    return this.getWithAuth(retry)\n      .then(res => {\n        isNewToken = this._lastToken !== res;\n        this._lastToken = res;\n        return res;\n      })\n      .then(promiseFactory)\n      .catch(err => {\n        // Only retry if the result is unauthorized and the last token isn't the same as the new one.\n        if (\n          'code' in err &&\n          err.code === Code.UNAUTHORIZED &&\n          !retry &&\n          isNewToken\n        ) {\n          logDebug('Retrying due to unauthorized');\n          return this.withRetry(promiseFactory, true);\n        }\n        throw err;\n      });\n  }\n\n  // TODO(mtewani): Update U to include shape of body defined in line 13.\n  invokeQuery: <T, U>(\n    queryName: string,\n    body?: U\n  ) => Promise<{ data: T; errors: Error[] }> = <T, U = unknown>(\n    queryName: string,\n    body: U\n  ) => {\n    const abortController = new AbortController();\n\n    // TODO(mtewani): Update to proper value\n    const withAuth = this.withRetry(() =>\n      dcFetch<T, U>(\n        addToken(`${this.endpointUrl}:executeQuery`, this.apiKey),\n        {\n          name: `projects/${this._project}/locations/${this._location}/services/${this._serviceName}/connectors/${this._connectorName}`,\n          operationName: queryName,\n          variables: body\n        },\n        abortController,\n        this.appId,\n        this._accessToken,\n        this._appCheckToken,\n        this._isUsingGen,\n        this._callerSdkType\n      )\n    );\n    return withAuth;\n  };\n  invokeMutation: <T, U>(\n    queryName: string,\n    body?: U\n  ) => Promise<{ data: T; errors: Error[] }> = <T, U = unknown>(\n    mutationName: string,\n    body: U\n  ) => {\n    const abortController = new AbortController();\n    const taskResult = this.withRetry(() => {\n      return dcFetch<T, U>(\n        addToken(`${this.endpointUrl}:executeMutation`, this.apiKey),\n        {\n          name: `projects/${this._project}/locations/${this._location}/services/${this._serviceName}/connectors/${this._connectorName}`,\n          operationName: mutationName,\n          variables: body\n        },\n        abortController,\n        this.appId,\n        this._accessToken,\n        this._appCheckToken,\n        this._isUsingGen,\n        this._callerSdkType\n      );\n    });\n    return taskResult;\n  };\n\n  _setCallerSdkType(callerSdkType: CallerSdkType): void {\n    this._callerSdkType = callerSdkType;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectTransport } from '../network/transport';\n\nimport { DataConnect } from './DataConnect';\nimport {\n  DataConnectResult,\n  MUTATION_STR,\n  OperationRef,\n  SOURCE_SERVER\n} from './Reference';\n\nexport interface MutationRef<Data, Variables>\n  extends OperationRef<Data, Variables> {\n  refType: typeof MUTATION_STR;\n}\n\n/**\n * Creates a `MutationRef`\n * @param dcInstance Data Connect instance\n * @param mutationName name of mutation\n */\nexport function mutationRef<Data>(\n  dcInstance: DataConnect,\n  mutationName: string\n): MutationRef<Data, undefined>;\n/**\n *\n * @param dcInstance Data Connect instance\n * @param mutationName name of mutation\n * @param variables variables to send with mutation\n */\nexport function mutationRef<Data, Variables>(\n  dcInstance: DataConnect,\n  mutationName: string,\n  variables: Variables\n): MutationRef<Data, Variables>;\n/**\n *\n * @param dcInstance Data Connect instance\n * @param mutationName name of mutation\n * @param variables variables to send with mutation\n * @returns `MutationRef`\n */\nexport function mutationRef<Data, Variables>(\n  dcInstance: DataConnect,\n  mutationName: string,\n  variables?: Variables\n): MutationRef<Data, Variables> {\n  dcInstance.setInitialized();\n  const ref: MutationRef<Data, Variables> = {\n    dataConnect: dcInstance,\n    name: mutationName,\n    refType: MUTATION_STR,\n    variables: variables as Variables\n  };\n  return ref;\n}\n\n/**\n * @internal\n */\nexport class MutationManager {\n  private _inflight: Array<Promise<unknown>> = [];\n  constructor(private _transport: DataConnectTransport) {}\n  executeMutation<Data, Variables>(\n    mutationRef: MutationRef<Data, Variables>\n  ): MutationPromise<Data, Variables> {\n    const result = this._transport.invokeMutation<Data, Variables>(\n      mutationRef.name,\n      mutationRef.variables\n    );\n    const withRefPromise = result.then(res => {\n      const obj: MutationResult<Data, Variables> = {\n        ...res, // Double check that the result is result.data, not just result\n        source: SOURCE_SERVER,\n        ref: mutationRef,\n        fetchTime: Date.now().toLocaleString()\n      };\n      return obj;\n    });\n    this._inflight.push(result);\n    const removePromise = (): Array<Promise<unknown>> =>\n      (this._inflight = this._inflight.filter(promise => promise !== result));\n    result.then(removePromise, removePromise);\n    return withRefPromise;\n  }\n}\n\n/**\n * Mutation Result from `executeMutation`\n */\nexport interface MutationResult<Data, Variables>\n  extends DataConnectResult<Data, Variables> {\n  ref: MutationRef<Data, Variables>;\n}\n/**\n * Mutation return value from `executeMutation`\n */\nexport interface MutationPromise<Data, Variables>\n  extends Promise<MutationResult<Data, Variables>> {\n  // reserved for special actions like cancellation\n}\n\n/**\n * Execute Mutation\n * @param mutationRef mutation to execute\n * @returns `MutationRef`\n */\nexport function executeMutation<Data, Variables>(\n  mutationRef: MutationRef<Data, Variables>\n): MutationPromise<Data, Variables> {\n  return mutationRef.dataConnect._mutationManager.executeMutation(mutationRef);\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  _getProvider,\n  _removeServiceInstance,\n  getApp\n} from '@firebase/app';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { Provider } from '@firebase/component';\n\nimport { AppCheckTokenProvider } from '../core/AppCheckTokenProvider';\nimport { Code, DataConnectError } from '../core/error';\nimport {\n  AuthTokenProvider,\n  FirebaseAuthProvider\n} from '../core/FirebaseAuthProvider';\nimport { QueryManager } from '../core/QueryManager';\nimport { logDebug, logError } from '../logger';\nimport {\n  CallerSdkType,\n  CallerSdkTypeEnum,\n  DataConnectTransport,\n  TransportClass\n} from '../network';\nimport { RESTTransport } from '../network/transport/rest';\n\nimport { MutationManager } from './Mutation';\n\n/**\n * Connector Config for calling Data Connect backend.\n */\nexport interface ConnectorConfig {\n  location: string;\n  connector: string;\n  service: string;\n}\n\n/**\n * Options to connect to emulator\n */\nexport interface TransportOptions {\n  host: string;\n  sslEnabled?: boolean;\n  port?: number;\n}\n\nconst FIREBASE_DATA_CONNECT_EMULATOR_HOST_VAR =\n  'FIREBASE_DATA_CONNECT_EMULATOR_HOST';\n\n/**\n *\n * @param fullHost\n * @returns TransportOptions\n * @internal\n */\nexport function parseOptions(fullHost: string): TransportOptions {\n  const [protocol, hostName] = fullHost.split('://');\n  const isSecure = protocol === 'https';\n  const [host, portAsString] = hostName.split(':');\n  const port = Number(portAsString);\n  return { host, port, sslEnabled: isSecure };\n}\n/**\n * DataConnectOptions including project id\n */\nexport interface DataConnectOptions extends ConnectorConfig {\n  projectId: string;\n}\n\n/**\n * Class representing Firebase Data Connect\n */\nexport class DataConnect {\n  _queryManager!: QueryManager;\n  _mutationManager!: MutationManager;\n  isEmulator = false;\n  _initialized = false;\n  private _transport!: DataConnectTransport;\n  private _transportClass: TransportClass | undefined;\n  private _transportOptions?: TransportOptions;\n  private _authTokenProvider?: AuthTokenProvider;\n  _isUsingGeneratedSdk: boolean = false;\n  _callerSdkType: CallerSdkType = CallerSdkTypeEnum.Base;\n  private _appCheckTokenProvider?: AppCheckTokenProvider;\n  // @internal\n  constructor(\n    public readonly app: FirebaseApp,\n    // TODO(mtewani): Replace with _dataConnectOptions in the future\n    private readonly dataConnectOptions: DataConnectOptions,\n    private readonly _authProvider: Provider<FirebaseAuthInternalName>,\n    private readonly _appCheckProvider: Provider<AppCheckInternalComponentName>\n  ) {\n    if (typeof process !== 'undefined' && process.env) {\n      const host = process.env[FIREBASE_DATA_CONNECT_EMULATOR_HOST_VAR];\n      if (host) {\n        logDebug('Found custom host. Using emulator');\n        this.isEmulator = true;\n        this._transportOptions = parseOptions(host);\n      }\n    }\n  }\n  // @internal\n  _useGeneratedSdk(): void {\n    if (!this._isUsingGeneratedSdk) {\n      this._isUsingGeneratedSdk = true;\n    }\n  }\n  _setCallerSdkType(callerSdkType: CallerSdkType): void {\n    this._callerSdkType = callerSdkType;\n    if (this._initialized) {\n      this._transport._setCallerSdkType(callerSdkType);\n    }\n  }\n  _delete(): Promise<void> {\n    _removeServiceInstance(\n      this.app,\n      'data-connect',\n      JSON.stringify(this.getSettings())\n    );\n    return Promise.resolve();\n  }\n\n  // @internal\n  getSettings(): ConnectorConfig {\n    const copy = JSON.parse(JSON.stringify(this.dataConnectOptions));\n    delete copy.projectId;\n    return copy;\n  }\n\n  // @internal\n  setInitialized(): void {\n    if (this._initialized) {\n      return;\n    }\n    if (this._transportClass === undefined) {\n      logDebug('transportClass not provided. Defaulting to RESTTransport.');\n      this._transportClass = RESTTransport;\n    }\n\n    if (this._authProvider) {\n      this._authTokenProvider = new FirebaseAuthProvider(\n        this.app.name,\n        this.app.options,\n        this._authProvider\n      );\n    }\n    if (this._appCheckProvider) {\n      this._appCheckTokenProvider = new AppCheckTokenProvider(\n        this.app,\n        this._appCheckProvider\n      );\n    }\n\n    this._initialized = true;\n    this._transport = new this._transportClass(\n      this.dataConnectOptions,\n      this.app.options.apiKey,\n      this.app.options.appId,\n      this._authTokenProvider,\n      this._appCheckTokenProvider,\n      undefined,\n      this._isUsingGeneratedSdk,\n      this._callerSdkType\n    );\n    if (this._transportOptions) {\n      this._transport.useEmulator(\n        this._transportOptions.host,\n        this._transportOptions.port,\n        this._transportOptions.sslEnabled\n      );\n    }\n    this._queryManager = new QueryManager(this._transport);\n    this._mutationManager = new MutationManager(this._transport);\n  }\n\n  // @internal\n  enableEmulator(transportOptions: TransportOptions): void {\n    if (\n      this._initialized &&\n      !areTransportOptionsEqual(this._transportOptions, transportOptions)\n    ) {\n      logError('enableEmulator called after initialization');\n      throw new DataConnectError(\n        Code.ALREADY_INITIALIZED,\n        'DataConnect instance already initialized!'\n      );\n    }\n    this._transportOptions = transportOptions;\n    this.isEmulator = true;\n  }\n}\n\n/**\n * @internal\n * @param transportOptions1\n * @param transportOptions2\n * @returns\n */\nexport function areTransportOptionsEqual(\n  transportOptions1: TransportOptions,\n  transportOptions2: TransportOptions\n): boolean {\n  return (\n    transportOptions1.host === transportOptions2.host &&\n    transportOptions1.port === transportOptions2.port &&\n    transportOptions1.sslEnabled === transportOptions2.sslEnabled\n  );\n}\n\n/**\n * Connect to the DataConnect Emulator\n * @param dc Data Connect instance\n * @param host host of emulator server\n * @param port port of emulator server\n * @param sslEnabled use https\n */\nexport function connectDataConnectEmulator(\n  dc: DataConnect,\n  host: string,\n  port?: number,\n  sslEnabled = false\n): void {\n  dc.enableEmulator({ host, port, sslEnabled });\n}\n\n/**\n * Initialize DataConnect instance\n * @param options ConnectorConfig\n */\nexport function getDataConnect(options: ConnectorConfig): DataConnect;\n/**\n * Initialize DataConnect instance\n * @param app FirebaseApp to initialize to.\n * @param options ConnectorConfig\n */\nexport function getDataConnect(\n  app: FirebaseApp,\n  options: ConnectorConfig\n): DataConnect;\nexport function getDataConnect(\n  appOrOptions: FirebaseApp | ConnectorConfig,\n  optionalOptions?: ConnectorConfig\n): DataConnect {\n  let app: FirebaseApp;\n  let dcOptions: ConnectorConfig;\n  if ('location' in appOrOptions) {\n    dcOptions = appOrOptions;\n    app = getApp();\n  } else {\n    dcOptions = optionalOptions!;\n    app = appOrOptions;\n  }\n\n  if (!app || Object.keys(app).length === 0) {\n    app = getApp();\n  }\n  const provider = _getProvider(app, 'data-connect');\n  const identifier = JSON.stringify(dcOptions);\n  if (provider.isInitialized(identifier)) {\n    const dcInstance = provider.getImmediate({ identifier });\n    const options = provider.getOptions(identifier);\n    const optionsValid = Object.keys(options).length > 0;\n    if (optionsValid) {\n      logDebug('Re-using cached instance');\n      return dcInstance;\n    }\n  }\n  validateDCOptions(dcOptions);\n\n  logDebug('Creating new DataConnect instance');\n  // Initialize with options.\n  return provider.initialize({\n    instanceIdentifier: identifier,\n    options: dcOptions\n  });\n}\n\n/**\n *\n * @param dcOptions\n * @returns {void}\n * @internal\n */\nexport function validateDCOptions(dcOptions: ConnectorConfig): boolean {\n  const fields = ['connector', 'location', 'service'];\n  if (!dcOptions) {\n    throw new DataConnectError(Code.INVALID_ARGUMENT, 'DC Option Required');\n  }\n  fields.forEach(field => {\n    if (dcOptions[field] === null || dcOptions[field] === undefined) {\n      throw new DataConnectError(Code.INVALID_ARGUMENT, `${field} Required`);\n    }\n  });\n  return true;\n}\n\n/**\n * Delete DataConnect instance\n * @param dataConnect DataConnect instance\n * @returns\n */\nexport function terminate(dataConnect: DataConnect): Promise<void> {\n  return dataConnect._delete();\n  // TODO(mtewani): Stop pending tasks\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectError } from '../core/error';\n\nimport { DataConnect, getDataConnect } from './DataConnect';\nimport {\n  OperationRef,\n  QUERY_STR,\n  DataConnectResult,\n  SerializedRef\n} from './Reference';\n\n/**\n * Signature for `OnResultSubscription` for `subscribe`\n */\nexport type OnResultSubscription<Data, Variables> = (\n  res: QueryResult<Data, Variables>\n) => void;\n/**\n * Signature for `OnErrorSubscription` for `subscribe`\n */\nexport type OnErrorSubscription = (err?: DataConnectError) => void;\n/**\n * Signature for unsubscribe from `subscribe`\n */\nexport type QueryUnsubscribe = () => void;\n/**\n * Representation of user provided subscription options.\n */\nexport interface DataConnectSubscription<Data, Variables> {\n  userCallback: OnResultSubscription<Data, Variables>;\n  errCallback?: (e?: DataConnectError) => void;\n  unsubscribe: () => void;\n}\n\n/**\n * QueryRef object\n */\nexport interface QueryRef<Data, Variables>\n  extends OperationRef<Data, Variables> {\n  refType: typeof QUERY_STR;\n}\n/**\n * Result of `executeQuery`\n */\nexport interface QueryResult<Data, Variables>\n  extends DataConnectResult<Data, Variables> {\n  ref: QueryRef<Data, Variables>;\n  toJSON: () => SerializedRef<Data, Variables>;\n}\n/**\n * Promise returned from `executeQuery`\n */\nexport interface QueryPromise<Data, Variables>\n  extends Promise<QueryResult<Data, Variables>> {\n  // reserved for special actions like cancellation\n}\n\n/**\n * Execute Query\n * @param queryRef query to execute.\n * @returns `QueryPromise`\n */\nexport function executeQuery<Data, Variables>(\n  queryRef: QueryRef<Data, Variables>\n): QueryPromise<Data, Variables> {\n  return queryRef.dataConnect._queryManager.executeQuery(queryRef);\n}\n\n/**\n * Execute Query\n * @param dcInstance Data Connect instance to use.\n * @param queryName Query to execute\n * @returns `QueryRef`\n */\nexport function queryRef<Data>(\n  dcInstance: DataConnect,\n  queryName: string\n): QueryRef<Data, undefined>;\n/**\n * Execute Query\n * @param dcInstance Data Connect instance to use.\n * @param queryName Query to execute\n * @param variables Variables to execute with\n * @returns `QueryRef`\n */\nexport function queryRef<Data, Variables>(\n  dcInstance: DataConnect,\n  queryName: string,\n  variables: Variables\n): QueryRef<Data, Variables>;\n/**\n * Execute Query\n * @param dcInstance Data Connect instance to use.\n * @param queryName Query to execute\n * @param variables Variables to execute with\n * @param initialCache initial cache to use for client hydration\n * @returns `QueryRef`\n */\nexport function queryRef<Data, Variables>(\n  dcInstance: DataConnect,\n  queryName: string,\n  variables?: Variables,\n  initialCache?: QueryResult<Data, Variables>\n): QueryRef<Data, Variables> {\n  dcInstance.setInitialized();\n  dcInstance._queryManager.track(queryName, variables, initialCache);\n  return {\n    dataConnect: dcInstance,\n    refType: QUERY_STR,\n    name: queryName,\n    variables\n  };\n}\n/**\n * Converts serialized ref to query ref\n * @param serializedRef ref to convert to `QueryRef`\n * @returns `QueryRef`\n */\nexport function toQueryRef<Data, Variables>(\n  serializedRef: SerializedRef<Data, Variables>\n): QueryRef<Data, Variables> {\n  const {\n    refInfo: { name, variables, connectorConfig }\n  } = serializedRef;\n  return queryRef(getDataConnect(connectorConfig), name, variables);\n}\n/**\n * `OnCompleteSubscription`\n */\nexport type OnCompleteSubscription = () => void;\n/**\n * Representation of full observer options in `subscribe`\n */\nexport interface SubscriptionOptions<Data, Variables> {\n  onNext?: OnResultSubscription<Data, Variables>;\n  onErr?: OnErrorSubscription;\n  onComplete?: OnCompleteSubscription;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ConnectorConfig,\n  DataConnect,\n  getDataConnect\n} from '../api/DataConnect';\nimport { Code, DataConnectError } from '../core/error';\ninterface ParsedArgs<Variables> {\n  dc: DataConnect;\n  vars: Variables;\n}\n\n/**\n * The generated SDK will allow the user to pass in either the variable or the data connect instance with the variable,\n * and this function validates the variables and returns back the DataConnect instance and variables based on the arguments passed in.\n * @param connectorConfig\n * @param dcOrVars\n * @param vars\n * @param validateVars\n * @returns {DataConnect} and {Variables} instance\n * @internal\n */\nexport function validateArgs<Variables extends object>(\n  connectorConfig: ConnectorConfig,\n  dcOrVars?: DataConnect | Variables,\n  vars?: Variables,\n  validateVars?: boolean\n): ParsedArgs<Variables> {\n  let dcInstance: DataConnect;\n  let realVars: Variables;\n  if (dcOrVars && 'enableEmulator' in dcOrVars) {\n    dcInstance = dcOrVars as DataConnect;\n    realVars = vars;\n  } else {\n    dcInstance = getDataConnect(connectorConfig);\n    realVars = dcOrVars as Variables;\n  }\n  if (!dcInstance || (!realVars && validateVars)) {\n    throw new DataConnectError(Code.INVALID_ARGUMENT, 'Variables required.');\n  }\n  return { dc: dcInstance, vars: realVars };\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  OnCompleteSubscription,\n  OnErrorSubscription,\n  OnResultSubscription,\n  QueryRef,\n  QueryUnsubscribe,\n  SubscriptionOptions,\n  toQueryRef\n} from './api/query';\nimport { OpResult, SerializedRef } from './api/Reference';\nimport { DataConnectError, Code } from './core/error';\n\n/**\n * Subscribe to a `QueryRef`\n * @param queryRefOrSerializedResult query ref or serialized result.\n * @param observer observer object to use for subscribing.\n * @returns `SubscriptionOptions`\n */\nexport function subscribe<Data, Variables>(\n  queryRefOrSerializedResult:\n    | QueryRef<Data, Variables>\n    | SerializedRef<Data, Variables>,\n  observer: SubscriptionOptions<Data, Variables>\n): QueryUnsubscribe;\n/**\n * Subscribe to a `QueryRef`\n * @param queryRefOrSerializedResult query ref or serialized result.\n * @param onNext Callback to call when result comes back.\n * @param onError Callback to call when error gets thrown.\n * @param onComplete Called when subscription completes.\n * @returns `SubscriptionOptions`\n */\nexport function subscribe<Data, Variables>(\n  queryRefOrSerializedResult:\n    | QueryRef<Data, Variables>\n    | SerializedRef<Data, Variables>,\n  onNext: OnResultSubscription<Data, Variables>,\n  onError?: OnErrorSubscription,\n  onComplete?: OnCompleteSubscription\n): QueryUnsubscribe;\n/**\n * Subscribe to a `QueryRef`\n * @param queryRefOrSerializedResult query ref or serialized result.\n * @param observerOrOnNext observer object or next function.\n * @param onError Callback to call when error gets thrown.\n * @param onComplete Called when subscription completes.\n * @returns `SubscriptionOptions`\n */\nexport function subscribe<Data, Variables>(\n  queryRefOrSerializedResult:\n    | QueryRef<Data, Variables>\n    | SerializedRef<Data, Variables>,\n  observerOrOnNext:\n    | SubscriptionOptions<Data, Variables>\n    | OnResultSubscription<Data, Variables>,\n  onError?: OnErrorSubscription,\n  onComplete?: OnCompleteSubscription\n): QueryUnsubscribe {\n  let ref: QueryRef<Data, Variables>;\n  let initialCache: OpResult<Data> | undefined;\n  if ('refInfo' in queryRefOrSerializedResult) {\n    const serializedRef: SerializedRef<Data, Variables> =\n      queryRefOrSerializedResult;\n    const { data, source, fetchTime } = serializedRef;\n    initialCache = {\n      data,\n      source,\n      fetchTime\n    };\n    ref = toQueryRef(serializedRef);\n  } else {\n    ref = queryRefOrSerializedResult;\n  }\n  let onResult: OnResultSubscription<Data, Variables> | undefined = undefined;\n  if (typeof observerOrOnNext === 'function') {\n    onResult = observerOrOnNext;\n  } else {\n    onResult = observerOrOnNext.onNext;\n    onError = observerOrOnNext.onErr;\n    onComplete = observerOrOnNext.onComplete;\n  }\n  if (!onResult) {\n    throw new DataConnectError(Code.INVALID_ARGUMENT, 'Must provide onNext');\n  }\n  return ref.dataConnect._queryManager.addSubscription(\n    ref,\n    onResult,\n    onError,\n    initialCache\n  );\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport {\n  _registerComponent,\n  registerVersion,\n  SDK_VERSION\n} from '@firebase/app';\nimport { Component, ComponentType } from '@firebase/component';\n\nimport { name, version } from '../package.json';\nimport { setSDKVersion } from '../src/core/version';\n\nimport { DataConnect, ConnectorConfig } from './api/DataConnect';\nimport { Code, DataConnectError } from './core/error';\n\nexport function registerDataConnect(variant?: string): void {\n  setSDKVersion(SDK_VERSION);\n  _registerComponent(\n    new Component(\n      'data-connect',\n      (container, { instanceIdentifier: settings, options }) => {\n        const app = container.getProvider('app').getImmediate()!;\n        const authProvider = container.getProvider('auth-internal');\n        const appCheckProvider = container.getProvider('app-check-internal');\n        let newOpts = options as ConnectorConfig;\n        if (settings) {\n          newOpts = JSON.parse(settings);\n        }\n        if (!app.options.projectId) {\n          throw new DataConnectError(\n            Code.INVALID_ARGUMENT,\n            'Project ID must be provided. Did you pass in a proper projectId to initializeApp?'\n          );\n        }\n        return new DataConnect(\n          app,\n          { ...newOpts, projectId: app.options.projectId! },\n          authProvider,\n          appCheckProvider\n        );\n      },\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n", "/**\n * Firebase Data Connect\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { DataConnect } from './api/DataConnect';\nimport { registerDataConnect } from './register';\n\nexport * from './api';\nexport * from './api.browser';\n\nregisterDataConnect();\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    'data-connect': DataConnect;\n  }\n}\n"], "names": ["FirebaseError", "Error", "constructor", "code", "message", "customData", "super", "this", "name", "Object", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "replaceTemplate", "replace", "PATTERN", "_", "key", "value", "String", "fullMessage", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "LogLevel", "levelStringToEnum", "debug", "DEBUG", "verbose", "VERBOSE", "info", "INFO", "warn", "WARN", "error", "ERROR", "silent", "SILENT", "defaultLogLevel", "ConsoleMethod", "defaultLogHandler", "instance", "logType", "args", "logLevel", "now", "Date", "toISOString", "method", "console", "SDK_VERSION", "AppCheckTokenProvider", "app", "appCheckProvider", "_isFirebaseServerApp", "settings", "appCheckToken", "serverAppAppCheckToken", "appCheck", "getImmediate", "optional", "get", "then", "catch", "getToken", "Promise", "resolve", "token", "reject", "setTimeout", "addTokenChangeListener", "listener", "_a", "addTokenListener", "Code", "OTHER", "ALREADY_INITIALIZED", "NOT_INITIALIZED", "NOT_SUPPORTED", "INVALID_ARGUMENT", "PARTIAL_ERROR", "UNAUTHORIZED", "DataConnectError", "toString", "DataConnectOperationError", "response", "logger", "<PERSON><PERSON>", "_logLevel", "_log<PERSON><PERSON><PERSON>", "_userLogHandler", "val", "TypeError", "setLogLevel", "log<PERSON><PERSON><PERSON>", "userLogHandler", "log", "logDebug", "msg", "logError", "FirebaseAuthProvider", "_appName", "_options", "_authProvider", "_auth", "onInit", "auth", "forceRefresh", "JSON", "stringify", "addAuthTokenListener", "removeTokenChangeListener", "removeAuthTokenListener", "err", "QUERY_STR", "MUTATION_STR", "SOURCE_SERVER", "SOURCE_CACHE", "encoderImpl", "getRefSerializer", "queryRef", "source", "toJSON", "refInfo", "variables", "connectorConfig", "projectId", "dataConnect", "options", "getSettings", "fetchTime", "toLocaleString", "<PERSON><PERSON><PERSON><PERSON>", "encoder", "o", "QueryManager", "transport", "_queries", "Map", "track", "queryName", "initialCache", "ref", "refType", "newTrackedQuery", "subscriptions", "currentCache", "lastError", "setIfNotExists", "map", "has", "set", "addSubscription", "onResultCallback", "onError<PERSON>allback", "<PERSON><PERSON><PERSON><PERSON>", "subscription", "userCallback", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unsubscribe", "filter", "sub", "compareDates", "str1", "str2", "date1", "date2", "getTime", "undefined", "push", "execute<PERSON>uery", "invoke<PERSON><PERSON>y", "res", "result", "assign", "for<PERSON>ach", "enableEmulator", "host", "port", "useEmulator", "CallerSdkTypeEnum", "Base", "Generated", "TanstackReactCore", "GeneratedReact", "TanstackAngularCore", "GeneratedAngular", "addToken", "url", "<PERSON><PERSON><PERSON><PERSON>", "newUrl", "URL", "searchParams", "append", "connectFetch", "globalThis", "fetch", "getGoogApiClientValue", "_isUsingGen", "_callerSdkType", "str", "toLowerCase", "dcFetch", "body", "signal", "appId", "accessToken", "headers", "bodyStr", "async", "jsonResponse", "json", "e", "getMessage", "obj", "status", "length", "stringified", "RESTTransport", "authProvider", "transportOptions", "_host", "_location", "_connectorName", "_secure", "_project", "_accessToken", "_appCheckToken", "_lastToken", "abortController", "AbortController", "withRetry", "endpointUrl", "_serviceName", "operationName", "invokeMutation", "mutationName", "_port", "sslEnabled", "location", "project", "connector", "_b", "urlBuilder", "projectConfig", "baseUrl", "isSecure", "onTokenChanged", "newToken", "getWithAuth", "forceToken", "<PERSON><PERSON><PERSON><PERSON>", "_setLastToken", "lastToken", "promiseFactory", "retry", "isNewToken", "_setCallerSdkType", "callerSdkType", "mutationRef", "dcInstance", "setInitialized", "MutationManager", "_transport", "_inflight", "executeMutation", "withRefPromise", "removePromise", "promise", "_mutationManager", "parseOptions", "fullHost", "protocol", "hostName", "split", "portAsString", "Number", "DataConnect", "dataConnectOptions", "_appCheckProvider", "isEmulator", "_initialized", "_isUsingGeneratedSdk", "process", "env", "_transportOptions", "_useGeneratedSdk", "_delete", "_removeServiceInstance", "copy", "parse", "_transportClass", "_authTokenProvider", "_appCheckTokenProvider", "_queryManager", "areTransportOptionsEqual", "transportOptions1", "transportOptions2", "connectDataConnectEmulator", "dc", "getDataConnect", "appOrOptions", "optionalOptions", "dcOptions", "getApp", "keys", "provider", "_get<PERSON><PERSON><PERSON>", "identifier", "isInitialized", "getOptions", "validateDCOptions", "initialize", "instanceIdentifier", "field", "terminate", "toQueryRef", "serializedRef", "validateArgs", "dcOrVars", "vars", "validateVars", "realVars", "subscribe", "queryRefOrSerializedResult", "observerOrOnNext", "onError", "onComplete", "onResult", "onNext", "onErr", "registerDataConnect", "variant", "setSDKVersion", "version", "_registerComponent", "container", "get<PERSON><PERSON><PERSON>", "newOpts", "registerVersion"], "mappings": "wKAyEM,MAAOA,sBAAsBC,MAIjC,WAAAC,CAEWC,EACTC,EAEOC,GAEPC,MAAMF,GALGG,KAAIJ,KAAJA,EAGFI,KAAUF,WAAVA,EAPAE,KAAIC,KAdI,gBA6BfC,OAAOC,eAAeH,KAAMP,cAAcW,WAItCV,MAAMW,mBACRX,MAAMW,kBAAkBL,KAAMM,aAAaF,UAAUG,OAExD,EAGU,MAAAD,aAIX,WAAAX,CACmBa,EACAC,EACAC,GAFAV,KAAOQ,QAAPA,EACAR,KAAWS,YAAXA,EACAT,KAAMU,OAANA,CACf,CAEJ,MAAAH,CACEX,KACGe,GAEH,MAAMb,EAAca,EAAK,IAAoB,CAAA,EACvCC,EAAW,GAAGZ,KAAKQ,WAAWZ,IAC9BiB,EAAWb,KAAKU,OAAOd,GAEvBC,EAAUgB,EAUpB,SAASC,gBAAgBD,EAAkBF,GACzC,OAAOE,EAASE,QAAQC,GAAS,CAACC,EAAGC,KACnC,MAAMC,EAAQR,EAAKO,GACnB,OAAgB,MAATC,EAAgBC,OAAOD,GAAS,IAAID,KAAO,GAEtD,CAf+BJ,CAAgBD,EAAUf,GAAc,QAE7DuB,EAAc,GAAGrB,KAAKS,gBAAgBZ,MAAYe,MAIxD,OAFc,IAAInB,cAAcmB,EAAUS,EAAavB,EAGxD,EAUH,MAAMkB,EAAU,gBC3GH,MAAAM,UAiBX,WAAA3B,CACWM,EACAsB,EACAC,GAFAxB,KAAIC,KAAJA,EACAD,KAAeuB,gBAAfA,EACAvB,KAAIwB,KAAJA,EAnBXxB,KAAiByB,mBAAG,EAIpBzB,KAAY0B,aAAe,GAE3B1B,KAAA2B,kBAA2C,OAE3C3B,KAAiB4B,kBAAwC,IAYrD,CAEJ,oBAAAC,CAAqBC,GAEnB,OADA9B,KAAK2B,kBAAoBG,EAClB9B,IACR,CAED,oBAAA+B,CAAqBN,GAEnB,OADAzB,KAAKyB,kBAAoBA,EAClBzB,IACR,CAED,eAAAgC,CAAgBC,GAEd,OADAjC,KAAK0B,aAAeO,EACbjC,IACR,CAED,0BAAAkC,CAA2BC,GAEzB,OADAnC,KAAK4B,kBAAoBO,EAClBnC,IACR,MCfSoC,GAAZ,SAAYA,GACVA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,OAAA,GAAA,QACD,CAPD,CAAYA,IAAAA,EAOX,CAAA,IAED,MAAMC,EAA2D,CAC/DC,MAASF,EAASG,MAClBC,QAAWJ,EAASK,QACpBC,KAAQN,EAASO,KACjBC,KAAQR,EAASS,KACjBC,MAASV,EAASW,MAClBC,OAAUZ,EAASa,QAMfC,EAA4Bd,EAASO,KAmBrCQ,EAAgB,CACpB,CAACf,EAASG,OAAQ,MAClB,CAACH,EAASK,SAAU,MACpB,CAACL,EAASO,MAAO,OACjB,CAACP,EAASS,MAAO,OACjB,CAACT,EAASW,OAAQ,SAQdK,kBAAgC,CAACC,EAAUC,KAAYC,KAC3D,GAAID,EAAUD,EAASG,SACrB,OAEF,MAAMC,GAAM,IAAIC,MAAOC,cACjBC,EAAST,EAAcG,GAC7B,IAAIM,EAMF,MAAM,IAAIlE,MACR,8DAA8D4D,MANhEO,QAAQD,GACN,IAAIH,OAASJ,EAASpD,WACnBsD,EAMN,6CCxGI,IAAIO,EAAc,GCYZ,MAAAC,sBAGX,WAAApE,CACEqE,EACQC,GAAAjE,KAAgBiE,iBAAhBA,EAEJC,EAAqBF,IAAQA,EAAIG,SAASC,gBAC5CpE,KAAKqE,uBAAyBL,EAAIG,SAASC,eAE7CpE,KAAKsE,SAAWL,aAAA,EAAAA,EAAkBM,aAAa,CAAEC,UAAU,IACtDxE,KAAKsE,UACHL,SAAAA,EACDQ,MACDC,MAAKJ,GAAatE,KAAKsE,SAAWA,IAClCK,OAEN,CAED,QAAAC,GACE,OAAI5E,KAAKqE,uBACAQ,QAAQC,QAAQ,CAAEC,MAAO/E,KAAKqE,yBAGlCrE,KAAKsE,SAeHtE,KAAKsE,SAASM,WAdZ,IAAIC,SAA6B,CAACC,EAASE,KAKhDC,YAAW,KACLjF,KAAKsE,SACPtE,KAAK4E,WAAWF,KAAKI,EAASE,GAE9BF,EAAQ,KACT,GACA,EAAE,GAIV,CAED,sBAAAI,CAAuBC,SAEjB,QADCC,EAAApF,KAAKiE,wBACN,IAAAmB,GAAAA,EAAAX,MACDC,MAAKJ,GAAYA,EAASe,iBAAiBF,IAC/C,EC9CU,MAAAG,EAAO,CAClBC,MAAO,QACPC,oBAAqB,sBACrBC,gBAAiB,kBACjBC,cAAe,gBACfC,iBAAkB,mBAClBC,cAAe,gBACfC,aAAc,gBAIV,MAAOC,yBAAyBrG,cAIpC,WAAAE,CAAYC,EAAYC,GACtBE,MAAMH,EAAMC,GAHLG,KAAIC,KAAW,mBAQtBC,OAAOC,eAAeH,KAAM8F,iBAAiB1F,UAC9C,CAGD,QAAA2F,GACE,MAAO,GAAG/F,KAAKC,aAAaD,KAAKJ,UAAUI,KAAKH,SACjD,EAIG,MAAOmG,kCAAkCF,iBAQ7C,WAAAnG,CAAYE,EAAiBoG,GAC3BlG,MAAMuF,EAAKM,cAAe/F,GAPnBG,KAAIC,KAAW,4BAQtBD,KAAKiG,SAAWA,CACjB,ECpDH,MAAMC,EAAS,IJyGF,MAAAC,OAOX,WAAAxG,CAAmBM,GAAAD,KAAIC,KAAJA,EAUXD,KAASoG,UAAGlD,EAsBZlD,KAAWqG,YAAejD,kBAc1BpD,KAAesG,gBAAsB,IAzC5C,CAOD,YAAI9C,GACF,OAAOxD,KAAKoG,SACb,CAED,YAAI5C,CAAS+C,GACX,KAAMA,KAAOnE,GACX,MAAM,IAAIoE,UAAU,kBAAkBD,+BAExCvG,KAAKoG,UAAYG,CAClB,CAGD,WAAAE,CAAYF,GACVvG,KAAKoG,UAA2B,iBAARG,EAAmBlE,EAAkBkE,GAAOA,CACrE,CAOD,cAAIG,GACF,OAAO1G,KAAKqG,WACb,CACD,cAAIK,CAAWH,GACb,GAAmB,mBAARA,EACT,MAAM,IAAIC,UAAU,qDAEtBxG,KAAKqG,YAAcE,CACpB,CAMD,kBAAII,GACF,OAAO3G,KAAKsG,eACb,CACD,kBAAIK,CAAeJ,GACjBvG,KAAKsG,gBAAkBC,CACxB,CAMD,KAAAjE,IAASiB,GACPvD,KAAKsG,iBAAmBtG,KAAKsG,gBAAgBtG,KAAMoC,EAASG,SAAUgB,GACtEvD,KAAKqG,YAAYrG,KAAMoC,EAASG,SAAUgB,EAC3C,CACD,GAAAqD,IAAOrD,GACLvD,KAAKsG,iBACHtG,KAAKsG,gBAAgBtG,KAAMoC,EAASK,WAAYc,GAClDvD,KAAKqG,YAAYrG,KAAMoC,EAASK,WAAYc,EAC7C,CACD,IAAAb,IAAQa,GACNvD,KAAKsG,iBAAmBtG,KAAKsG,gBAAgBtG,KAAMoC,EAASO,QAASY,GACrEvD,KAAKqG,YAAYrG,KAAMoC,EAASO,QAASY,EAC1C,CACD,IAAAX,IAAQW,GACNvD,KAAKsG,iBAAmBtG,KAAKsG,gBAAgBtG,KAAMoC,EAASS,QAASU,GACrEvD,KAAKqG,YAAYrG,KAAMoC,EAASS,QAASU,EAC1C,CACD,KAAAT,IAASS,GACPvD,KAAKsG,iBAAmBtG,KAAKsG,gBAAgBtG,KAAMoC,EAASW,SAAUQ,GACtEvD,KAAKqG,YAAYrG,KAAMoC,EAASW,SAAUQ,EAC3C,GI9LuB,0BACpB,SAAUkD,YAAYjD,GAC1B0C,EAAOO,YAAYjD,EACrB,CACM,SAAUqD,SAASC,GACvBZ,EAAO5D,MAAM,gBAAgBwB,OAAiBgD,IAChD,CAEM,SAAUC,SAASD,GACvBZ,EAAOpD,MAAM,gBAAgBgB,OAAiBgD,IAChD,CCKa,MAAAE,qBAEX,WAAArH,CACUsH,EACAC,EACAC,GAFAnH,KAAQiH,SAARA,EACAjH,KAAQkH,SAARA,EACAlH,KAAamH,cAAbA,EAERnH,KAAKoH,MAAQD,EAAc5C,aAAa,CAAEC,UAAU,IAC/CxE,KAAKoH,OACRD,EAAcE,QAAOC,GAAStH,KAAKoH,MAAQE,GAE9C,CACD,QAAA1C,CAAS2C,GACP,OAAKvH,KAAKoH,MAWHpH,KAAKoH,MAAMxC,SAAS2C,GAAc5C,OAAM7B,GACzCA,GAAwB,+BAAfA,EAAMlD,MACjBiH,SACE,kEAEK,OAEPE,SACE,qDACES,KAAKC,UAAU3E,IAEZ+B,QAAQG,OAAOlC,MArBjB,IAAI+B,SAAQ,CAACC,EAASE,KAC3BC,YAAW,KACLjF,KAAKoH,MACPpH,KAAK4E,SAAS2C,GAAc7C,KAAKI,EAASE,GAE1CF,EAAQ,KACT,GACA,EAAE,GAiBV,CACD,sBAAAI,CAAuBC,SACX,QAAVC,EAAApF,KAAKoH,aAAK,IAAAhC,GAAAA,EAAEsC,qBAAqBvC,EAClC,CACD,yBAAAwC,CAA0BxC,GACxBnF,KAAKmH,cACF1C,MACAC,MAAK4C,GAAQA,EAAKM,wBAAwBzC,KAC1CR,OAAMkD,GAAOd,SAASc,IAC1B,EChEU,MAAAC,EAAY,QACZC,EAAe,WAGfC,EAAgB,SAChBC,EAAe,QCLrB,IAAIC,EC8BX,SAASC,iBACPC,EACAzH,EACA0H,GAEA,OAAO,SAASC,SACd,MAAO,CACL3H,OACA4H,QAAS,CACPtI,KAAMmI,EAASnI,KACfuI,UAAWJ,EAASI,UACpBC,+BACEC,UAAWN,EAASO,YAAY3E,IAAI4E,QAAQF,WACzCN,EAASO,YAAYE,gBAG5BC,UAAWpF,KAAKD,MAAMsF,iBACtBV,SAEJ,CACF,EDjDM,SAAUW,WAAWC,GACzBf,EAAce,CAChB,CACAD,EAAWE,GAAK1B,KAAKC,UAAUyB,KCgDlB,MAAAC,aAEX,WAAAxJ,CAAoByJ,GAAApJ,KAASoJ,UAATA,EAClBpJ,KAAKqJ,SAAW,IAAIC,GACrB,CACD,KAAAC,CACEC,EACAhB,EACAiB,GAEA,MAAMC,EAA4C,CAChDzJ,KAAMuJ,EACNhB,YACAmB,QAAS7B,GAEL5G,EAAMgH,EAAYwB,GAClBE,EAAiD,CACrDF,MACAG,cAAe,GACfC,aAAcL,GAAgB,KAC9BM,UAAW,MAIb,OC7EY,SAAAC,eACdC,EACA/I,EACAqF,GAEK0D,EAAIC,IAAIhJ,IACX+I,EAAIE,IAAIjJ,EAAKqF,EAEjB,CDoEIyD,CAAehK,KAAKqJ,SAAUnI,EAAK0I,GAC5B5J,KAAKqJ,SAAS5E,IAAIvD,EAC1B,CACD,eAAAkJ,CACEhC,EACAiC,EACAC,EACAb,GAEA,MAAMvI,EAAMgH,EAAY,CACtBjI,KAAMmI,EAASnI,KACfuI,UAAWJ,EAASI,UACpBmB,QAAS7B,IAELyC,EAAevK,KAAKqJ,SAAS5E,IAAIvD,GAIjCsJ,EAAe,CACnBC,aAAcJ,EACdK,YAAaJ,GAETK,YAAc,KAClB,MAAMJ,EAAevK,KAAKqJ,SAAS5E,IAAIvD,GACvCqJ,EAAaV,cAAgBU,EAAaV,cAAce,QACtDC,GAAOA,IAAQL,GAChB,EAeH,GAbIf,GAAgBc,EAAaT,eAAiBL,IAChD5C,SAAS,2CAEN0D,EAAaT,cACbS,EAAaT,cAsGtB,SAASgB,aAAaC,EAAcC,GAClC,MAAMC,EAAQ,IAAIvH,KAAKqH,GACjBG,EAAQ,IAAIxH,KAAKsH,GACvB,OAAOC,EAAME,UAAYD,EAAMC,SACjC,CAzGUL,CACEP,EAAaT,aAAahB,UAC1BW,EAAaX,cAGjByB,EAAaT,aAAeL,IAGE,OAA9Bc,EAAaT,aAAuB,CAEtCO,EAAiB,CACf1J,KAFiB4J,EAAaT,aAAanJ,KAG3C0H,OAAQJ,EACRyB,IAAKtB,EACLE,OAAQH,iBACNC,EACAmC,EAAaT,aAAanJ,KAC1BsH,GAEFa,UAAWyB,EAAaT,aAAahB,YAER,OAA3ByB,EAAaR,WAAsBO,GACrCA,OAAgBc,EAEnB,CAOD,GALAb,EAAaV,cAAcwB,KAAK,CAC9BZ,aAAcJ,EACdK,YAAaJ,EACbK,2BAEGJ,EAAaT,aAAc,CAC9BjD,SACE,gCACEuB,EAASnI,uBACQuH,KAAKC,UACtBW,EAASI,qCAGGxI,KAAKsL,aAAalD,GAE1B1D,UAAK0G,GAAWvD,OACzB,CACD,OAAO8C,WACR,CACD,YAAAW,CACElD,GAEA,GAAIA,EAASuB,UAAY7B,EACvB,MAAM,IAAIhC,iBACRR,EAAKK,iBACL,iDAGJ,MAAMzE,EAAMgH,EAAY,CACtBjI,KAAMmI,EAASnI,KACfuI,UAAWJ,EAASI,UACpBmB,QAAS7B,IAELyC,EAAevK,KAAKqJ,SAAS5E,IAAIvD,GAoCvC,OAnCelB,KAAKoJ,UAAUmC,YAC5BnD,EAASnI,KACTmI,EAASI,WAES9D,MAClB8G,IACE,MAAM1C,GAAY,IAAIpF,MAAOqC,WACvB0F,EACDvL,OAAAwL,OAAAxL,OAAAwL,OAAA,GAAAF,GACH,CAAAnD,OAAQL,EACR0B,IAAKtB,EACLE,OAAQH,iBAAiBC,EAAUoD,EAAI7K,KAAMqH,GAC7Cc,cAUF,OARAyB,EAAaV,cAAc8B,SAAQnB,IACjCA,EAAaC,aAAagB,EAAO,IAEnClB,EAAaT,aAAe,CAC1BnJ,KAAM6K,EAAI7K,KACV0H,OAAQJ,EACRa,aAEK2C,CAAM,IAEf5D,IAOE,MANA0C,EAAaR,UAAYlC,EACzB0C,EAAaV,cAAc8B,SAAQnB,IAC7BA,EAAaE,aACfF,EAAaE,YAAY7C,EAC1B,IAEGA,CAAG,GAKd,CACD,cAAA+D,CAAeC,EAAcC,GAC3B9L,KAAKoJ,UAAU2C,YAAYF,EAAMC,EAClC,EEjMU,MAAAE,EAAoB,CAC/BC,KAAM,OACNC,UAAW,YACXC,kBAAmB,oBACnBC,eAAgB,iBAChBC,oBAAqB,sBACrBC,iBAAkB,oBCGJ,SAAAC,SAASC,EAAaC,GACpC,IAAKA,EACH,OAAOD,EAET,MAAME,EAAS,IAAIC,IAAIH,GAEvB,OADAE,EAAOE,aAAaC,OAAO,MAAOJ,GAC3BC,EAAO3G,UAChB,CCpBA,IAAI+G,EAAoCC,WAAWC,MAInD,SAASC,sBACPC,EACAC,GAEA,IAAIC,EAAM,eAAiBtJ,EAS3B,OAPEqJ,IAAmBnB,EAAkBC,MACrCkB,IAAmBnB,EAAkBE,UAErCkB,GAAO,OAASD,EAAeE,eACtBH,GAAeC,IAAmBnB,EAAkBE,aAC7DkB,GAAO,WAEFA,CACT,CAMgB,SAAAE,QACdd,EACAe,GACAC,OAAEA,GACFC,EACAC,EACAtJ,EACA8I,EACAC,GAEA,IAAKL,EACH,MAAM,IAAIhH,iBAAiBR,EAAKC,MAAO,qCAEzC,MAAMoI,EAAuB,CAC3B,eAAgB,mBAChB,oBAAqBV,sBAAsBC,EAAaC,IAEtDO,IACFC,EAAQ,yBAA2BD,GAEjCD,IACFE,EAAQ,oBAAsBF,GAE5BrJ,IACFuJ,EAAQ,uBAAyBvJ,GAEnC,MAAMwJ,EAAUpG,KAAKC,UAAU8F,GAG/B,OAFA1G,SAAS,yBAAyB2F,gBAAkBoB,KAE7Cd,EAAaN,EAAK,CACvBe,KAAMK,EACNhK,OAAQ,OACR+J,UACAH,WAEC7I,OAAMkD,IACL,MAAM,IAAI/B,iBACRR,EAAKC,MACL,oBAAsBiC,KAAKC,UAAUI,GACtC,IAEFnD,MAAKmJ,MAAM5H,IACV,IAAI6H,EAAe,KACnB,IACEA,QAAqB7H,EAAS8H,MAC/B,CAAC,MAAOC,GACP,MAAM,IAAIlI,iBAAiBR,EAAKC,MAAOiC,KAAKC,UAAUuG,GACvD,CACD,MAAMnO,EA8BZ,SAASoO,WAAWC,GAClB,GAAI,YAAaA,EACf,OAAOA,EAAIrO,QAEb,OAAO2H,KAAKC,UAAUyG,EACxB,CAnCsBD,CAAWH,GAC3B,GAAI7H,EAASkI,QAAU,IAAK,CAI1B,GAHApH,SACE,mCAAqCS,KAAKC,UAAUqG,IAE9B,MAApB7H,EAASkI,OACX,MAAM,IAAIrI,iBAAiBR,EAAKO,aAAchG,GAEhD,MAAM,IAAIiG,iBAAiBR,EAAKC,MAAO1F,EACxC,CACD,OAAOiO,CAAY,IAEpBpJ,MAAK8G,IACJ,GAAIA,EAAI9K,QAAU8K,EAAI9K,OAAO0N,OAAQ,CACnC,MAAMC,EAAc7G,KAAKC,UAAU+D,EAAI9K,QACjCuF,EAAgD,CACpDvF,OAAQ8K,EAAI9K,OACZC,KAAM6K,EAAI7K,MAEZ,MAAM,IAAIqF,0BACR,+CAAiDqI,EACjDpI,EAEH,CACD,OAAOuF,CAAG,GAEhB,CCnGa,MAAA8C,cAWX,WAAA3O,CACEiJ,EACQ6D,EACAgB,EACAc,EACAtK,EACRuK,EACQtB,GAAc,EACdC,EAAgCnB,EAAkBC,cANlDjM,KAAMyM,OAANA,EACAzM,KAAKyN,MAALA,EACAzN,KAAYuO,aAAZA,EACAvO,KAAgBiE,iBAAhBA,EAEAjE,KAAWkN,YAAXA,EACAlN,KAAcmN,eAAdA,EAlBFnN,KAAKyO,MAAG,GAERzO,KAAS0O,UAAG,IACZ1O,KAAc2O,eAAG,GACjB3O,KAAO4O,SAAG,EACV5O,KAAQ6O,SAAG,IAEX7O,KAAY8O,aAAkB,KAC9B9O,KAAc+O,eAAkB,KAChC/O,KAAUgP,WAAkB,KA4HpChP,KAAAuL,YAG6C,CAC3C/B,EACA+D,KAEA,MAAM0B,EAAkB,IAAIC,gBAmB5B,OAhBiBlP,KAAKmP,WAAU,IAC9B7B,QACEf,SAAS,GAAGvM,KAAKoP,2BAA4BpP,KAAKyM,QAClD,CACExM,KAAM,YAAYD,KAAK6O,sBAAsB7O,KAAK0O,sBAAsB1O,KAAKqP,2BAA2BrP,KAAK2O,iBAC7GW,cAAe9F,EACfhB,UAAW+E,GAEb0B,EACAjP,KAAKyN,MACLzN,KAAK8O,aACL9O,KAAK+O,eACL/O,KAAKkN,YACLlN,KAAKmN,iBAGM,EAEjBnN,KAAAuP,eAG6C,CAC3CC,EACAjC,KAEA,MAAM0B,EAAkB,IAAIC,gBAiB5B,OAhBmBlP,KAAKmP,WAAU,IACzB7B,QACLf,SAAS,GAAGvM,KAAKoP,8BAA+BpP,KAAKyM,QACrD,CACExM,KAAM,YAAYD,KAAK6O,sBAAsB7O,KAAK0O,sBAAsB1O,KAAKqP,2BAA2BrP,KAAK2O,iBAC7GW,cAAeE,EACfhH,UAAW+E,GAEb0B,EACAjP,KAAKyN,MACLzN,KAAK8O,aACL9O,KAAK+O,eACL/O,KAAKkN,YACLlN,KAAKmN,iBAGQ,EArKbqB,IACmC,iBAA1BA,EAAiB1C,OAC1B9L,KAAKyP,MAAQjB,EAAiB1C,WAEW,IAAhC0C,EAAiBkB,aAC1B1P,KAAK4O,QAAUJ,EAAiBkB,YAElC1P,KAAKyO,MAAQD,EAAiB3C,MAEhC,MAAM8D,SAAEA,EAAUjH,UAAWkH,EAAOC,UAAEA,EAASrP,QAAEA,GAAYoI,EAQ7D,GAPI+G,IACF3P,KAAK0O,UAAYiB,GAEfC,IACF5P,KAAK6O,SAAWe,GAElB5P,KAAKqP,aAAe7O,GACfqP,EACH,MAAM,IAAI/J,iBACRR,EAAKK,iBACL,4BAGJ3F,KAAK2O,eAAiBkB,EACL,QAAjBzK,EAAApF,KAAKuO,oBAAY,IAAAnJ,GAAAA,EAAEF,wBAAuBH,IACxC8B,SAAS,wBAAwB9B,KACjC/E,KAAK8O,aAAe/J,CAAK,IAEN,QAArB+K,EAAA9P,KAAKiE,wBAAgB,IAAA6L,GAAAA,EAAE5K,wBAAuBuG,IAC5C,MAAM1G,MAAEA,GAAU0G,EAClB5E,SAAS,kCAAkC9B,KAC3C/E,KAAK+O,eAAiBhK,CAAK,GAE9B,CACD,eAAIqK,GACF,OF9DY,SAAAW,WACdC,EACAxB,GAEA,MAAMqB,UAAEA,EAASF,SAAEA,EAAUjH,UAAWkH,EAAOpP,QAAEA,GAAYwP,GACvDnE,KAAEA,EAAI6D,WAAEA,EAAU5D,KAAEA,GAAS0C,EAGnC,IAAIyB,EAAU,GAFGP,EAAa,QAAU,YACvB7D,GAAQ,uCAEzB,GAAoB,iBAATC,EACTmE,GAAW,IAAInE,SACV,QAAoB,IAATA,EAEhB,MADA/E,SAAS,mCACH,IAAIjB,iBACRR,EAAKK,iBACL,sCAGJ,MAAO,GAAGsK,iBAAuBL,eAAqBD,cAAqBnP,gBAAsBqP,GACnG,CE2CWE,CACL,CACEF,UAAW7P,KAAK2O,eAChBgB,SAAU3P,KAAK0O,UACfhG,UAAW1I,KAAK6O,SAChBrO,QAASR,KAAKqP,cAEhB,CAAExD,KAAM7L,KAAKyO,MAAOiB,WAAY1P,KAAK4O,QAAS9C,KAAM9L,KAAKyP,OAE5D,CACD,WAAA1D,CAAYF,EAAcC,EAAeoE,GACvClQ,KAAKyO,MAAQ5C,EACO,iBAATC,IACT9L,KAAKyP,MAAQ3D,QAES,IAAboE,IACTlQ,KAAK4O,QAAUsB,EAElB,CACD,cAAAC,CAAeC,GACbpQ,KAAK8O,aAAesB,CACrB,CAED,iBAAMC,CAAYC,GAAa,SAC7B,IAAIC,EAAyC,IAAI1L,SAAQC,GACvDA,EAAQ9E,KAAK8O,gBAkBf,OAhBI9O,KAAKiE,mBACPjE,KAAK+O,eAAyD,QAAxC3J,QAAOpF,KAAKiE,iBAAiBW,kBAAW,IAAAQ,OAAA,EAAAA,EAAEL,OAGhEwL,EADEvQ,KAAKuO,aACUvO,KAAKuO,aACnB3J,SAAyB0L,GACzB5L,MAAK/D,GACCA,GAGLX,KAAK8O,aAAenO,EAAK+M,YAClB1N,KAAK8O,cAHH,OAMI,IAAIjK,SAAQC,GAAWA,EAAQ,MAE3CyL,CACR,CAED,aAAAC,CAAcC,GACZzQ,KAAKgP,WAAayB,CACnB,CAED,SAAAtB,CACEuB,EACAC,GAAQ,GAER,IAAIC,GAAa,EACjB,OAAO5Q,KAAKqQ,YAAYM,GACrBjM,MAAK8G,IACJoF,EAAa5Q,KAAKgP,aAAexD,EACjCxL,KAAKgP,WAAaxD,EACXA,KAER9G,KAAKgM,GACL/L,OAAMkD,IAEL,GACE,SAAUA,GACVA,EAAIjI,OAAS0F,EAAKO,eACjB8K,GACDC,EAGA,OADA/J,SAAS,gCACF7G,KAAKmP,UAAUuB,GAAgB,GAExC,MAAM7I,CAAG,GAEd,CA0DD,iBAAAgJ,CAAkBC,GAChB9Q,KAAKmN,eAAiB2D,CACvB,EC/Ja,SAAAC,YACdC,EACAxB,EACAhH,GAEAwI,EAAWC,iBAOX,MAN0C,CACxCtI,YAAaqI,EACb/Q,KAAMuP,EACN7F,QAAS5B,EACTS,UAAWA,EAGf,CAKa,MAAA0I,gBAEX,WAAAvR,CAAoBwR,GAAAnR,KAAUmR,WAAVA,EADZnR,KAASoR,UAA4B,EACW,CACxD,eAAAC,CACEN,GAEA,MAAMtF,EAASzL,KAAKmR,WAAW5B,eAC7BwB,EAAY9Q,KACZ8Q,EAAYvI,WAER8I,EAAiB7F,EAAO/G,MAAK8G,GAE5BtL,OAAAwL,OAAAxL,OAAAwL,OAAA,CAAA,EAAAF,GAAG,CACNnD,OAAQL,EACR0B,IAAKqH,EACLjI,UAAWpF,KAAKD,MAAMsF,qBAI1B/I,KAAKoR,UAAU/F,KAAKI,GACpB,MAAM8F,cAAgB,IACnBvR,KAAKoR,UAAYpR,KAAKoR,UAAUxG,QAAO4G,GAAWA,IAAY/F,IAEjE,OADAA,EAAO/G,KAAK6M,cAAeA,eACpBD,CACR,EAuBG,SAAUD,gBACdN,GAEA,OAAOA,EAAYpI,YAAY8I,iBAAiBJ,gBAAgBN,EAClE,CCxDM,SAAUW,aAAaC,GAC3B,MAAOC,EAAUC,GAAYF,EAASG,MAAM,OACtC5B,EAAwB,UAAb0B,GACV/F,EAAMkG,GAAgBF,EAASC,MAAM,KAE5C,MAAO,CAAEjG,OAAMC,KADFkG,OAAOD,GACCrC,WAAYQ,EACnC,CAWa,MAAA+B,YAaX,WAAAtS,CACkBqE,EAECkO,EACA/K,EACAgL,GAEjB,GANgBnS,KAAGgE,IAAHA,EAEChE,KAAkBkS,mBAAlBA,EACAlS,KAAamH,cAAbA,EACAnH,KAAiBmS,kBAAjBA,EAfnBnS,KAAUoS,YAAG,EACbpS,KAAYqS,cAAG,EAKfrS,KAAoBsS,sBAAY,EAChCtS,KAAAmN,eAAgCnB,EAAkBC,KAUzB,oBAAZsG,SAA2BA,QAAQC,IAAK,CACjD,MAAM3G,EAAO0G,QAAQC,IAA2C,oCAC5D3G,IACFhF,SAAS,qCACT7G,KAAKoS,YAAa,EAClBpS,KAAKyS,kBAAoBf,aAAa7F,GAEzC,CACF,CAED,gBAAA6G,GACO1S,KAAKsS,uBACRtS,KAAKsS,sBAAuB,EAE/B,CACD,iBAAAzB,CAAkBC,GAChB9Q,KAAKmN,eAAiB2D,EAClB9Q,KAAKqS,cACPrS,KAAKmR,WAAWN,kBAAkBC,EAErC,CACD,OAAA6B,GAME,OALAC,EACE5S,KAAKgE,IACL,eACAwD,KAAKC,UAAUzH,KAAK6I,gBAEfhE,QAAQC,SAChB,CAGD,WAAA+D,GACE,MAAMgK,EAAOrL,KAAKsL,MAAMtL,KAAKC,UAAUzH,KAAKkS,qBAE5C,cADOW,EAAKnK,UACLmK,CACR,CAGD,cAAA5B,GACMjR,KAAKqS,oBAGoBjH,IAAzBpL,KAAK+S,kBACPlM,SAAS,6DACT7G,KAAK+S,gBAAkBzE,eAGrBtO,KAAKmH,gBACPnH,KAAKgT,mBAAqB,IAAIhM,qBAC5BhH,KAAKgE,IAAI/D,KACTD,KAAKgE,IAAI4E,QACT5I,KAAKmH,gBAGLnH,KAAKmS,oBACPnS,KAAKiT,uBAAyB,IAAIlP,sBAChC/D,KAAKgE,IACLhE,KAAKmS,oBAITnS,KAAKqS,cAAe,EACpBrS,KAAKmR,WAAa,IAAInR,KAAK+S,gBACzB/S,KAAKkS,mBACLlS,KAAKgE,IAAI4E,QAAQ6D,OACjBzM,KAAKgE,IAAI4E,QAAQ6E,MACjBzN,KAAKgT,mBACLhT,KAAKiT,4BACL7H,EACApL,KAAKsS,qBACLtS,KAAKmN,gBAEHnN,KAAKyS,mBACPzS,KAAKmR,WAAWpF,YACd/L,KAAKyS,kBAAkB5G,KACvB7L,KAAKyS,kBAAkB3G,KACvB9L,KAAKyS,kBAAkB/C,YAG3B1P,KAAKkT,cAAgB,IAAI/J,aAAanJ,KAAKmR,YAC3CnR,KAAKyR,iBAAmB,IAAIP,gBAAgBlR,KAAKmR,YAClD,CAGD,cAAAvF,CAAe4C,GACb,GACExO,KAAKqS,eACJc,yBAAyBnT,KAAKyS,kBAAmBjE,GAGlD,MADAzH,SAAS,8CACH,IAAIjB,iBACRR,EAAKE,oBACL,6CAGJxF,KAAKyS,kBAAoBjE,EACzBxO,KAAKoS,YAAa,CACnB,EASa,SAAAe,yBACdC,EACAC,GAEA,OACED,EAAkBvH,OAASwH,EAAkBxH,MAC7CuH,EAAkBtH,OAASuH,EAAkBvH,MAC7CsH,EAAkB1D,aAAe2D,EAAkB3D,UAEvD,CASM,SAAU4D,2BACdC,EACA1H,EACAC,EACA4D,GAAa,GAEb6D,EAAG3H,eAAe,CAAEC,OAAMC,OAAM4D,cAClC,CAgBgB,SAAA8D,eACdC,EACAC,GAEA,IAAI1P,EACA2P,EACA,aAAcF,GAChBE,EAAYF,EACZzP,EAAM4P,MAEND,EAAYD,EACZ1P,EAAMyP,GAGHzP,GAAmC,IAA5B9D,OAAO2T,KAAK7P,GAAKoK,SAC3BpK,EAAM4P,KAER,MAAME,EAAWC,aAAa/P,EAAK,gBAC7BgQ,EAAaxM,KAAKC,UAAUkM,GAClC,GAAIG,EAASG,cAAcD,GAAa,CACtC,MAAMhD,EAAa8C,EAASvP,aAAa,CAAEyP,eACrCpL,EAAUkL,EAASI,WAAWF,GAEpC,GADqB9T,OAAO2T,KAAKjL,GAASwF,OAAS,EAGjD,OADAvH,SAAS,4BACFmK,CAEV,CAKD,OAJAmD,kBAAkBR,GAElB9M,SAAS,qCAEFiN,EAASM,WAAW,CACzBC,mBAAoBL,EACpBpL,QAAS+K,GAEb,CAQM,SAAUQ,kBAAkBR,GAEhC,IAAKA,EACH,MAAM,IAAI7N,iBAAiBR,EAAKK,iBAAkB,sBAOpD,MATe,CAAC,YAAa,WAAY,WAIlCgG,SAAQ2I,IACb,GAAyB,OAArBX,EAAUW,SAAwClJ,IAArBuI,EAAUW,GACzC,MAAM,IAAIxO,iBAAiBR,EAAKK,iBAAkB,GAAG2O,aACtD,KAEI,CACT,CAOM,SAAUC,UAAU5L,GACxB,OAAOA,EAAYgK,SAErB,CCnPM,SAAUrH,aACdlD,GAEA,OAAOA,EAASO,YAAYuK,cAAc5H,aAAalD,EACzD,CAgCM,SAAUA,SACd4I,EACAxH,EACAhB,EACAiB,GAIA,OAFAuH,EAAWC,iBACXD,EAAWkC,cAAc3J,MAAMC,EAAWhB,EAAWiB,GAC9C,CACLd,YAAaqI,EACbrH,QAAS7B,EACT7H,KAAMuJ,EACNhB,YAEJ,CAMM,SAAUgM,WACdC,GAEA,MACElM,SAAStI,KAAEA,EAAIuI,UAAEA,EAASC,gBAAEA,IAC1BgM,EACJ,OAAOrM,SAASoL,eAAe/K,GAAkBxI,EAAMuI,EACzD,CCvGM,SAAUkM,aACdjM,EACAkM,EACAC,EACAC,GAEA,IAAI7D,EACA8D,EAQJ,GAPIH,GAAY,mBAAoBA,GAClC3D,EAAa2D,EACbG,EAAWF,IAEX5D,EAAawC,eAAe/K,GAC5BqM,EAAWH,IAER3D,IAAgB8D,GAAYD,EAC/B,MAAM,IAAI/O,iBAAiBR,EAAKK,iBAAkB,uBAEpD,MAAO,CAAE4N,GAAIvC,EAAY4D,KAAME,EACjC,CCQM,SAAUC,UACdC,EAGAC,EAGAC,EACAC,GAEA,IAAIzL,EACAD,EAcA2L,EAbJ,GAAI,YAAaJ,EAA4B,CAC3C,MAAMP,EACJO,GACIrU,KAAEA,EAAI0H,OAAEA,EAAMS,UAAEA,GAAc2L,EACpChL,EAAe,CACb9I,OACA0H,SACAS,aAEFY,EAAM8K,WAAWC,EAClB,MACC/K,EAAMsL,EAUR,GAPgC,mBAArBC,EACTG,EAAWH,GAEXG,EAAWH,EAAiBI,OAC5BH,EAAUD,EAAiBK,MACdL,EAAiBE,aAE3BC,EACH,MAAM,IAAItP,iBAAiBR,EAAKK,iBAAkB,uBAEpD,OAAO+D,EAAIf,YAAYuK,cAAc9I,gBACnCV,EACA0L,EACAF,EACAzL,EAEJ,EC7EM,SAAU8L,oBAAoBC,IlBN9B,SAAUC,cAAcC,GAC5B5R,EAAc4R,CAChB,CkBKED,CAAc3R,GACd6R,EACE,IAAIrU,UACF,gBACA,CAACsU,GAAavB,mBAAoBlQ,EAAUyE,cAC1C,MAAM5E,EAAM4R,EAAUC,YAAY,OAAOtR,eACnCgK,EAAeqH,EAAUC,YAAY,iBACrC5R,EAAmB2R,EAAUC,YAAY,sBAC/C,IAAIC,EAAUlN,EAId,GAHIzE,IACF2R,EAAUtO,KAAKsL,MAAM3O,KAElBH,EAAI4E,QAAQF,UACf,MAAM,IAAI5C,iBACRR,EAAKK,iBACL,qFAGJ,OAAO,IAAIsM,YACTjO,EACK9D,OAAAwL,OAAAxL,OAAAwL,OAAA,GAAAoK,GAAO,CAAEpN,UAAW1E,EAAI4E,QAAQF,YACrC6F,EACAtK,EACD,aAGHlC,sBAAqB,IAEzBgU,EAAgB9V,EAAMyV,EAASF,GAE/BO,EAAgB9V,EAAMyV,EAAS,UACjC,CClCAH", "preExistingComment": "firebase-data-connect.js.map"}