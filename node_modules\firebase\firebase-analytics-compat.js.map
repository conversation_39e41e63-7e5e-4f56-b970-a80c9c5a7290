{"version": 3, "file": "firebase-analytics-compat.js", "sources": ["../logger/src/logger.ts", "../../node_modules/idb/build/index.js", "../util/src/environment.ts", "../util/src/errors.ts", "../util/src/exponential_backoff.ts", "../util/src/compat.ts", "../component/src/component.ts", "../../node_modules/idb/build/wrap-idb-value.js", "../installations/src/util/constants.ts", "../installations/src/util/errors.ts", "../installations/src/functions/common.ts", "../installations/src/util/sleep.ts", "../installations/src/helpers/generate-fid.ts", "../installations/src/helpers/buffer-to-base64-url-safe.ts", "../installations/src/util/get-key.ts", "../installations/src/helpers/fid-changed.ts", "../installations/src/helpers/idb-manager.ts", "../installations/src/helpers/get-installation-entry.ts", "../installations/src/functions/create-installation-request.ts", "../installations/src/functions/generate-auth-token-request.ts", "../installations/src/helpers/refresh-auth-token.ts", "../installations/src/api/get-token.ts", "../installations/src/helpers/extract-app-config.ts", "../installations/src/functions/config.ts", "../installations/src/api/get-id.ts", "../installations/src/index.ts", "../analytics/src/constants.ts", "../analytics/src/logger.ts", "../analytics/src/errors.ts", "../analytics/src/helpers.ts", "../analytics/src/get-config.ts", "../analytics/src/initialize-analytics.ts", "../analytics/src/factory.ts", "../analytics/src/api.ts", "../analytics/src/functions.ts", "../analytics/src/index.ts", "../analytics-compat/src/constants.ts", "../analytics-compat/src/service.ts", "../analytics-compat/src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type LogLevelString =\n  | 'debug'\n  | 'verbose'\n  | 'info'\n  | 'warn'\n  | 'error'\n  | 'silent';\n\nexport interface LogOptions {\n  level: LogLevelString;\n}\n\nexport type LogCallback = (callbackParams: LogCallbackParams) => void;\n\nexport interface LogCallbackParams {\n  level: LogLevelString;\n  message: string;\n  args: unknown[];\n  type: string;\n}\n\n/**\n * A container for all of the Logger instances\n */\nexport const instances: Logger[] = [];\n\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nexport enum LogLevel {\n  DEBUG,\n  VERBOSE,\n  INFO,\n  WARN,\n  ERROR,\n  SILENT\n}\n\nconst levelStringToEnum: { [key in LogLevelString]: LogLevel } = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n\n/**\n * The default log level\n */\nconst defaultLogLevel: LogLevel = LogLevel.INFO;\n\n/**\n * We allow users the ability to pass their own log handler. We will pass the\n * type of log, the current log level, and any other arguments passed (i.e. the\n * messages that the user wants to log) to this function.\n */\nexport type LogHandler = (\n  loggerInstance: Logger,\n  logType: LogLevel,\n  ...args: unknown[]\n) => void;\n\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler: LogHandler = (instance, logType, ...args): void => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType as keyof typeof ConsoleMethod];\n  if (method) {\n    console[method as 'log' | 'info' | 'warn' | 'error'](\n      `[${now}]  ${instance.name}:`,\n      ...args\n    );\n  } else {\n    throw new Error(\n      `Attempted to log a message with an invalid logType (value: ${logType})`\n    );\n  }\n};\n\nexport class Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(public name: string) {\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n\n  /**\n   * The log level of the given Logger instance.\n   */\n  private _logLevel = defaultLogLevel;\n\n  get logLevel(): LogLevel {\n    return this._logLevel;\n  }\n\n  set logLevel(val: LogLevel) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val: LogLevel | LogLevelString): void {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n\n  /**\n   * The main (internal) log handler for the Logger instance.\n   * Can be set to a new function in internal package code but not by user.\n   */\n  private _logHandler: LogHandler = defaultLogHandler;\n  get logHandler(): LogHandler {\n    return this._logHandler;\n  }\n  set logHandler(val: LogHandler) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n\n  /**\n   * The optional, additional, user-defined log handler for the Logger instance.\n   */\n  private _userLogHandler: LogHandler | null = null;\n  get userLogHandler(): LogHandler | null {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val: LogHandler | null) {\n    this._userLogHandler = val;\n  }\n\n  /**\n   * The functions below are all based on the `console` interface\n   */\n\n  debug(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args: unknown[]): void {\n    this._userLogHandler &&\n      this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\n\nexport function setLogLevel(level: LogLevelString | LogLevel): void {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\n\nexport function setUserLogHandler(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  for (const instance of instances) {\n    let customLogLevel: LogLevel | null = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (\n        instance: Logger,\n        level: LogLevel,\n        ...args: unknown[]\n      ) => {\n        const message = args\n          .map(arg => {\n            if (arg == null) {\n              return null;\n            } else if (typeof arg === 'string') {\n              return arg;\n            } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n              return arg.toString();\n            } else if (arg instanceof Error) {\n              return arg.message;\n            } else {\n              try {\n                return JSON.stringify(arg);\n              } catch (ignored) {\n                return null;\n              }\n            }\n          })\n          .filter(arg => arg)\n          .join(' ');\n        if (level >= (customLogLevel ?? instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase() as LogLevelString,\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\n", "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n        });\n    }\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event.newVersion, event));\n    }\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking) {\n            db.addEventListener('versionchange', (event) => blocking(event.oldVersion, event.newVersion, event));\n        }\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event));\n    }\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSTANTS } from './constants';\nimport { getDefaults } from './defaults';\n\n/**\n * Type placeholder for `WorkerGlobalScope` from `webworker`\n */\ndeclare class WorkerGlobalScope {}\n\n/**\n * Returns navigator.userAgent string or '' if it's not defined.\n * @return user agent string\n */\nexport function getUA(): string {\n  if (\n    typeof navigator !== 'undefined' &&\n    typeof navigator['userAgent'] === 'string'\n  ) {\n    return navigator['userAgent'];\n  } else {\n    return '';\n  }\n}\n\n/**\n * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.\n *\n * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap\n * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally\n * wait for a callback.\n */\nexport function isMobileCordova(): boolean {\n  return (\n    typeof window !== 'undefined' &&\n    // @ts-ignore Setting up an broadly applicable index signature for Window\n    // just to deal with this case would probably be a bad idea.\n    !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) &&\n    /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA())\n  );\n}\n\n/**\n * Detect Node.js.\n *\n * @return true if Node.js environment is detected or specified.\n */\n// Node detection logic from: https://github.com/iliakan/detect-node/\nexport function isNode(): boolean {\n  const forceEnvironment = getDefaults()?.forceEnvironment;\n  if (forceEnvironment === 'node') {\n    return true;\n  } else if (forceEnvironment === 'browser') {\n    return false;\n  }\n\n  try {\n    return (\n      Object.prototype.toString.call(global.process) === '[object process]'\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Detect Browser Environment.\n * Note: This will return true for certain test frameworks that are incompletely\n * mimicking a browser, and should not lead to assuming all browser APIs are\n * available.\n */\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined' || isWebWorker();\n}\n\n/**\n * Detect Web Worker context.\n */\nexport function isWebWorker(): boolean {\n  return (\n    typeof WorkerGlobalScope !== 'undefined' &&\n    typeof self !== 'undefined' &&\n    self instanceof WorkerGlobalScope\n  );\n}\n\n/**\n * Detect Cloudflare Worker context.\n */\nexport function isCloudflareWorker(): boolean {\n  return (\n    typeof navigator !== 'undefined' &&\n    navigator.userAgent === 'Cloudflare-Workers'\n  );\n}\n\n/**\n * Detect browser extensions (Chrome and Firefox at least).\n */\ninterface BrowserRuntime {\n  id?: unknown;\n}\ndeclare const chrome: { runtime?: BrowserRuntime };\ndeclare const browser: { runtime?: BrowserRuntime };\nexport function isBrowserExtension(): boolean {\n  const runtime =\n    typeof chrome === 'object'\n      ? chrome.runtime\n      : typeof browser === 'object'\n      ? browser.runtime\n      : undefined;\n  return typeof runtime === 'object' && runtime.id !== undefined;\n}\n\n/**\n * Detect React Native.\n *\n * @return true if ReactNative environment is detected.\n */\nexport function isReactNative(): boolean {\n  return (\n    typeof navigator === 'object' && navigator['product'] === 'ReactNative'\n  );\n}\n\n/** Detects Electron apps. */\nexport function isElectron(): boolean {\n  return getUA().indexOf('Electron/') >= 0;\n}\n\n/** Detects Internet Explorer. */\nexport function isIE(): boolean {\n  const ua = getUA();\n  return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\n}\n\n/** Detects Universal Windows Platform apps. */\nexport function isUWP(): boolean {\n  return getUA().indexOf('MSAppHost/') >= 0;\n}\n\n/**\n * Detect whether the current SDK build is the Node version.\n *\n * @return true if it's the Node SDK build.\n */\nexport function isNodeSdk(): boolean {\n  return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;\n}\n\n/** Returns true if we are running in Safari. */\nexport function isSafari(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    navigator.userAgent.includes('Safari') &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/**\n * This method checks if indexedDB is supported by current browser/service worker context\n * @return true if indexedDB is supported by current browser/service worker context\n */\nexport function isIndexedDBAvailable(): boolean {\n  try {\n    return typeof indexedDB === 'object';\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject\n * if errors occur during the database open operation.\n *\n * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox\n * private browsing)\n */\nexport function validateIndexedDBOpenable(): Promise<boolean> {\n  return new Promise((resolve, reject) => {\n    try {\n      let preExist: boolean = true;\n      const DB_CHECK_NAME =\n        'validate-browser-context-for-indexeddb-analytics-module';\n      const request = self.indexedDB.open(DB_CHECK_NAME);\n      request.onsuccess = () => {\n        request.result.close();\n        // delete database only when it doesn't pre-exist\n        if (!preExist) {\n          self.indexedDB.deleteDatabase(DB_CHECK_NAME);\n        }\n        resolve(true);\n      };\n      request.onupgradeneeded = () => {\n        preExist = false;\n      };\n\n      request.onerror = () => {\n        reject(request.error?.message || '');\n      };\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n\n/**\n *\n * This method checks whether cookie is enabled within current browser\n * @return true if cookie is enabled within current browser\n */\nexport function areCookiesEnabled(): boolean {\n  if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {\n    return false;\n  }\n  return true;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * The amount of milliseconds to exponentially increase.\n */\nconst DEFAULT_INTERVAL_MILLIS = 1000;\n\n/**\n * The factor to backoff by.\n * Should be a number greater than 1.\n */\nconst DEFAULT_BACKOFF_FACTOR = 2;\n\n/**\n * The maximum milliseconds to increase to.\n *\n * <p>Visible for testing\n */\nexport const MAX_VALUE_MILLIS = 4 * 60 * 60 * 1000; // Four hours, like iOS and Android.\n\n/**\n * The percentage of backoff time to randomize by.\n * See\n * http://go/safe-client-behavior#step-1-determine-the-appropriate-retry-interval-to-handle-spike-traffic\n * for context.\n *\n * <p>Visible for testing\n */\nexport const RANDOM_FACTOR = 0.5;\n\n/**\n * Based on the backoff method from\n * https://github.com/google/closure-library/blob/master/closure/goog/math/exponentialbackoff.js.\n * Extracted here so we don't need to pass metadata and a stateful ExponentialBackoff object around.\n */\nexport function calculateBackoffMillis(\n  backoffCount: number,\n  intervalMillis: number = DEFAULT_INTERVAL_MILLIS,\n  backoffFactor: number = DEFAULT_BACKOFF_FACTOR\n): number {\n  // Calculates an exponentially increasing value.\n  // Deviation: calculates value from count and a constant interval, so we only need to save value\n  // and count to restore state.\n  const currBaseValue = intervalMillis * Math.pow(backoffFactor, backoffCount);\n\n  // A random \"fuzz\" to avoid waves of retries.\n  // Deviation: randomFactor is required.\n  const randomWait = Math.round(\n    // A fraction of the backoff value to add/subtract.\n    // Deviation: changes multiplication order to improve readability.\n    RANDOM_FACTOR *\n      currBaseValue *\n      // A random float (rounded to int by Math.round above) in the range [-1, 1]. Determines\n      // if we add or subtract.\n      (Math.random() - 0.5) *\n      2\n  );\n\n  // Limits backoff to max to avoid effectively permanent backoff.\n  return Math.min(MAX_VALUE_MILLIS, currBaseValue + randomWait);\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../../package.json';\n\nexport const PENDING_TIMEOUT_MS = 10000;\n\nexport const PACKAGE_VERSION = `w:${version}`;\nexport const INTERNAL_AUTH_VERSION = 'FIS_v2';\n\nexport const INSTALLATIONS_API_URL =\n  'https://firebaseinstallations.googleapis.com/v1';\n\nexport const TOKEN_EXPIRATION_BUFFER = 60 * 60 * 1000; // One hour\n\nexport const SERVICE = 'installations';\nexport const SERVICE_NAME = 'Installations';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\nimport { SERVICE, SERVICE_NAME } from './constants';\n\nexport const enum ErrorCode {\n  MISSING_APP_CONFIG_VALUES = 'missing-app-config-values',\n  NOT_REGISTERED = 'not-registered',\n  INSTALLATION_NOT_FOUND = 'installation-not-found',\n  REQUEST_FAILED = 'request-failed',\n  APP_OFFLINE = 'app-offline',\n  DELETE_PENDING_REGISTRATION = 'delete-pending-registration'\n}\n\nconst ERROR_DESCRIPTION_MAP: { readonly [key in ErrorCode]: string } = {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]:\n    'Missing App configuration value: \"{$valueName}\"',\n  [ErrorCode.NOT_REGISTERED]: 'Firebase Installation is not registered.',\n  [ErrorCode.INSTALLATION_NOT_FOUND]: 'Firebase Installation not found.',\n  [ErrorCode.REQUEST_FAILED]:\n    '{$requestName} request failed with error \"{$serverCode} {$serverStatus}: {$serverMessage}\"',\n  [ErrorCode.APP_OFFLINE]: 'Could not process request. Application offline.',\n  [ErrorCode.DELETE_PENDING_REGISTRATION]:\n    \"Can't delete installation while there is a pending registration request.\"\n};\n\ninterface ErrorParams {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]: {\n    valueName: string;\n  };\n  [ErrorCode.REQUEST_FAILED]: {\n    requestName: string;\n    [index: string]: string | number; // to make TypeScript 3.8 happy\n  } & ServerErrorData;\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  SERVICE,\n  SERVICE_NAME,\n  ERROR_DESCRIPTION_MAP\n);\n\nexport interface ServerErrorData {\n  serverCode: number;\n  serverMessage: string;\n  serverStatus: string;\n}\n\nexport type ServerError = FirebaseError & { customData: ServerErrorData };\n\n/** Returns true if error is a FirebaseError that is based on an error from the server. */\nexport function isServerError(error: unknown): error is ServerError {\n  return (\n    error instanceof FirebaseError &&\n    error.code.includes(ErrorCode.REQUEST_FAILED)\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport {\n  INSTALLATIONS_API_URL,\n  INTERNAL_AUTH_VERSION\n} from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { AppConfig } from '../interfaces/installation-impl';\n\nexport function getInstallationsEndpoint({ projectId }: AppConfig): string {\n  return `${INSTALLATIONS_API_URL}/projects/${projectId}/installations`;\n}\n\nexport function extractAuthTokenInfoFromResponse(\n  response: GenerateAuthTokenResponse\n): CompletedAuthToken {\n  return {\n    token: response.token,\n    requestStatus: RequestStatus.COMPLETED,\n    expiresIn: getExpiresInFromResponseExpiresIn(response.expiresIn),\n    creationTime: Date.now()\n  };\n}\n\nexport async function getErrorFromResponse(\n  requestName: string,\n  response: Response\n): Promise<FirebaseError> {\n  const responseJson: ErrorResponse = await response.json();\n  const errorData = responseJson.error;\n  return ERROR_FACTORY.create(ErrorCode.REQUEST_FAILED, {\n    requestName,\n    serverCode: errorData.code,\n    serverMessage: errorData.message,\n    serverStatus: errorData.status\n  });\n}\n\nexport function getHeaders({ apiKey }: AppConfig): Headers {\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\n\nexport function getHeadersWithAuth(\n  appConfig: AppConfig,\n  { refreshToken }: RegisteredInstallationEntry\n): Headers {\n  const headers = getHeaders(appConfig);\n  headers.append('Authorization', getAuthorizationHeader(refreshToken));\n  return headers;\n}\n\nexport interface ErrorResponse {\n  error: {\n    code: number;\n    message: string;\n    status: string;\n  };\n}\n\n/**\n * Calls the passed in fetch wrapper and returns the response.\n * If the returned response has a status of 5xx, re-runs the function once and\n * returns the response.\n */\nexport async function retryIfServerError(\n  fn: () => Promise<Response>\n): Promise<Response> {\n  const result = await fn();\n\n  if (result.status >= 500 && result.status < 600) {\n    // Internal Server Error. Retry request.\n    return fn();\n  }\n\n  return result;\n}\n\nfunction getExpiresInFromResponseExpiresIn(responseExpiresIn: string): number {\n  // This works because the server will never respond with fractions of a second.\n  return Number(responseExpiresIn.replace('s', '000'));\n}\n\nfunction getAuthorizationHeader(refreshToken: string): string {\n  return `${INTERNAL_AUTH_VERSION} ${refreshToken}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** Returns a promise that resolves after given time passes. */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise<void>(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bufferToBase64UrlSafe } from './buffer-to-base64-url-safe';\n\nexport const VALID_FID_PATTERN = /^[cdef][\\w-]{21}$/;\nexport const INVALID_FID = '';\n\n/**\n * Generates a new FID using random values from Web Crypto API.\n * Returns an empty string if FID generation fails for any reason.\n */\nexport function generateFid(): string {\n  try {\n    // A valid FID has exactly 22 base64 characters, which is 132 bits, or 16.5\n    // bytes. our implementation generates a 17 byte array instead.\n    const fidByteArray = new Uint8Array(17);\n    const crypto =\n      self.crypto || (self as unknown as { msCrypto: Crypto }).msCrypto;\n    crypto.getRandomValues(fidByteArray);\n\n    // Replace the first 4 random bits with the constant FID header of 0b0111.\n    fidByteArray[0] = 0b01110000 + (fidByteArray[0] % 0b00010000);\n\n    const fid = encode(fidByteArray);\n\n    return VALID_FID_PATTERN.test(fid) ? fid : INVALID_FID;\n  } catch {\n    // FID generation errored\n    return INVALID_FID;\n  }\n}\n\n/** Converts a FID Uint8Array to a base64 string representation. */\nfunction encode(fidByteArray: Uint8Array): string {\n  const b64String = bufferToBase64UrlSafe(fidByteArray);\n\n  // Remove the 23rd character that was added because of the extra 4 bits at the\n  // end of our 17 byte array, and the '=' padding.\n  return b64String.substr(0, 22);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function bufferToBase64UrlSafe(array: Uint8Array): string {\n  const b64 = btoa(String.fromCharCode(...array));\n  return b64.replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AppConfig } from '../interfaces/installation-impl';\n\n/** Returns a string key that can be used to identify the app. */\nexport function getKey(appConfig: AppConfig): string {\n  return `${appConfig.appName}!${appConfig.appId}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getKey } from '../util/get-key';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { IdChangeCallbackFn } from '../api';\n\nconst fidChangeCallbacks: Map<string, Set<IdChangeCallbackFn>> = new Map();\n\n/**\n * Calls the onIdChange callbacks with the new FID value, and broadcasts the\n * change to other tabs.\n */\nexport function fidChanged(appConfig: AppConfig, fid: string): void {\n  const key = getKey(appConfig);\n\n  callFidChangeCallbacks(key, fid);\n  broadcastFidChange(key, fid);\n}\n\nexport function addCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  // Open the broadcast channel if it's not already open,\n  // to be able to listen to change events from other tabs.\n  getBroadcastChannel();\n\n  const key = getKey(appConfig);\n\n  let callbackSet = fidChangeCallbacks.get(key);\n  if (!callbackSet) {\n    callbackSet = new Set();\n    fidChangeCallbacks.set(key, callbackSet);\n  }\n  callbackSet.add(callback);\n}\n\nexport function removeCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  const key = getKey(appConfig);\n\n  const callbackSet = fidChangeCallbacks.get(key);\n\n  if (!callbackSet) {\n    return;\n  }\n\n  callbackSet.delete(callback);\n  if (callbackSet.size === 0) {\n    fidChangeCallbacks.delete(key);\n  }\n\n  // Close broadcast channel if there are no more callbacks.\n  closeBroadcastChannel();\n}\n\nfunction callFidChangeCallbacks(key: string, fid: string): void {\n  const callbacks = fidChangeCallbacks.get(key);\n  if (!callbacks) {\n    return;\n  }\n\n  for (const callback of callbacks) {\n    callback(fid);\n  }\n}\n\nfunction broadcastFidChange(key: string, fid: string): void {\n  const channel = getBroadcastChannel();\n  if (channel) {\n    channel.postMessage({ key, fid });\n  }\n  closeBroadcastChannel();\n}\n\nlet broadcastChannel: BroadcastChannel | null = null;\n/** Opens and returns a BroadcastChannel if it is supported by the browser. */\nfunction getBroadcastChannel(): BroadcastChannel | null {\n  if (!broadcastChannel && 'BroadcastChannel' in self) {\n    broadcastChannel = new BroadcastChannel('[Firebase] FID Change');\n    broadcastChannel.onmessage = e => {\n      callFidChangeCallbacks(e.data.key, e.data.fid);\n    };\n  }\n  return broadcastChannel;\n}\n\nfunction closeBroadcastChannel(): void {\n  if (fidChangeCallbacks.size === 0 && broadcastChannel) {\n    broadcastChannel.close();\n    broadcastChannel = null;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DBSchema, IDBPDatabase, openDB } from 'idb';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { InstallationEntry } from '../interfaces/installation-entry';\nimport { getKey } from '../util/get-key';\nimport { fidChanged } from './fid-changed';\n\nconst DATABASE_NAME = 'firebase-installations-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-installations-store';\n\ninterface InstallationsDB extends DBSchema {\n  'firebase-installations-store': {\n    key: string;\n    value: InstallationEntry | undefined;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<InstallationsDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<InstallationsDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            db.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n\n/** Gets record(s) from the objectStore that match the given key. */\nexport async function get(\n  appConfig: AppConfig\n): Promise<InstallationEntry | undefined> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  return db\n    .transaction(OBJECT_STORE_NAME)\n    .objectStore(OBJECT_STORE_NAME)\n    .get(key) as Promise<InstallationEntry>;\n}\n\n/** Assigns or overwrites the record for the given key with the given value. */\nexport async function set<ValueType extends InstallationEntry>(\n  appConfig: AppConfig,\n  value: ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const objectStore = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue = (await objectStore.get(key)) as InstallationEntry;\n  await objectStore.put(value, key);\n  await tx.done;\n\n  if (!oldValue || oldValue.fid !== value.fid) {\n    fidChanged(appConfig, value.fid);\n  }\n\n  return value;\n}\n\n/** Removes record(s) from the objectStore that match the given key. */\nexport async function remove(appConfig: AppConfig): Promise<void> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n  await tx.done;\n}\n\n/**\n * Atomically updates a record with the result of updateFn, which gets\n * called with the current value. If newValue is undefined, the record is\n * deleted instead.\n * @return Updated value\n */\nexport async function update<ValueType extends InstallationEntry | undefined>(\n  appConfig: AppConfig,\n  updateFn: (previousValue: InstallationEntry | undefined) => ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const store = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue: InstallationEntry | undefined = (await store.get(\n    key\n  )) as InstallationEntry;\n  const newValue = updateFn(oldValue);\n\n  if (newValue === undefined) {\n    await store.delete(key);\n  } else {\n    await store.put(newValue, key);\n  }\n  await tx.done;\n\n  if (newValue && (!oldValue || oldValue.fid !== newValue.fid)) {\n    fidChanged(appConfig, newValue.fid);\n  }\n\n  return newValue;\n}\n\nexport async function clear(): Promise<void> {\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).clear();\n  await tx.done;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createInstallationRequest } from '../functions/create-installation-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  InProgressInstallationEntry,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { generateFid, INVALID_FID } from './generate-fid';\nimport { remove, set, update } from './idb-manager';\n\nexport interface InstallationEntryWithRegistrationPromise {\n  installationEntry: InstallationEntry;\n  /** Exist iff the installationEntry is not registered. */\n  registrationPromise?: Promise<RegisteredInstallationEntry>;\n}\n\n/**\n * Updates and returns the InstallationEntry from the database.\n * Also triggers a registration request if it is necessary and possible.\n */\nexport async function getInstallationEntry(\n  installations: FirebaseInstallationsImpl\n): Promise<InstallationEntryWithRegistrationPromise> {\n  let registrationPromise: Promise<RegisteredInstallationEntry> | undefined;\n\n  const installationEntry = await update(installations.appConfig, oldEntry => {\n    const installationEntry = updateOrCreateInstallationEntry(oldEntry);\n    const entryWithPromise = triggerRegistrationIfNecessary(\n      installations,\n      installationEntry\n    );\n    registrationPromise = entryWithPromise.registrationPromise;\n    return entryWithPromise.installationEntry;\n  });\n\n  if (installationEntry.fid === INVALID_FID) {\n    // FID generation failed. Waiting for the FID from the server.\n    return { installationEntry: await registrationPromise! };\n  }\n\n  return {\n    installationEntry,\n    registrationPromise\n  };\n}\n\n/**\n * Creates a new Installation Entry if one does not exist.\n * Also clears timed out pending requests.\n */\nfunction updateOrCreateInstallationEntry(\n  oldEntry: InstallationEntry | undefined\n): InstallationEntry {\n  const entry: InstallationEntry = oldEntry || {\n    fid: generateFid(),\n    registrationStatus: RequestStatus.NOT_STARTED\n  };\n\n  return clearTimedOutRequest(entry);\n}\n\n/**\n * If the Firebase Installation is not registered yet, this will trigger the\n * registration and return an InProgressInstallationEntry.\n *\n * If registrationPromise does not exist, the installationEntry is guaranteed\n * to be registered.\n */\nfunction triggerRegistrationIfNecessary(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InstallationEntry\n): InstallationEntryWithRegistrationPromise {\n  if (installationEntry.registrationStatus === RequestStatus.NOT_STARTED) {\n    if (!navigator.onLine) {\n      // Registration required but app is offline.\n      const registrationPromiseWithError = Promise.reject(\n        ERROR_FACTORY.create(ErrorCode.APP_OFFLINE)\n      );\n      return {\n        installationEntry,\n        registrationPromise: registrationPromiseWithError\n      };\n    }\n\n    // Try registering. Change status to IN_PROGRESS.\n    const inProgressEntry: InProgressInstallationEntry = {\n      fid: installationEntry.fid,\n      registrationStatus: RequestStatus.IN_PROGRESS,\n      registrationTime: Date.now()\n    };\n    const registrationPromise = registerInstallation(\n      installations,\n      inProgressEntry\n    );\n    return { installationEntry: inProgressEntry, registrationPromise };\n  } else if (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS\n  ) {\n    return {\n      installationEntry,\n      registrationPromise: waitUntilFidRegistration(installations)\n    };\n  } else {\n    return { installationEntry };\n  }\n}\n\n/** This will be executed only once for each new Firebase Installation. */\nasync function registerInstallation(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  try {\n    const registeredInstallationEntry = await createInstallationRequest(\n      installations,\n      installationEntry\n    );\n    return set(installations.appConfig, registeredInstallationEntry);\n  } catch (e) {\n    if (isServerError(e) && e.customData.serverCode === 409) {\n      // Server returned a \"FID cannot be used\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      // Registration failed. Set FID as not registered.\n      await set(installations.appConfig, {\n        fid: installationEntry.fid,\n        registrationStatus: RequestStatus.NOT_STARTED\n      });\n    }\n    throw e;\n  }\n}\n\n/** Call if FID registration is pending in another request. */\nasync function waitUntilFidRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<RegisteredInstallationEntry> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry: InstallationEntry = await updateInstallationRequest(\n    installations.appConfig\n  );\n  while (entry.registrationStatus === RequestStatus.IN_PROGRESS) {\n    // createInstallation request still in progress.\n    await sleep(100);\n\n    entry = await updateInstallationRequest(installations.appConfig);\n  }\n\n  if (entry.registrationStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    const { installationEntry, registrationPromise } =\n      await getInstallationEntry(installations);\n\n    if (registrationPromise) {\n      return registrationPromise;\n    } else {\n      // if there is no registrationPromise, entry is registered.\n      return installationEntry as RegisteredInstallationEntry;\n    }\n  }\n\n  return entry;\n}\n\n/**\n * Called only if there is a CreateInstallation request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * CreateInstallation request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateInstallationRequest(\n  appConfig: AppConfig\n): Promise<InstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!oldEntry) {\n      throw ERROR_FACTORY.create(ErrorCode.INSTALLATION_NOT_FOUND);\n    }\n    return clearTimedOutRequest(oldEntry);\n  });\n}\n\nfunction clearTimedOutRequest(entry: InstallationEntry): InstallationEntry {\n  if (hasInstallationRequestTimedOut(entry)) {\n    return {\n      fid: entry.fid,\n      registrationStatus: RequestStatus.NOT_STARTED\n    };\n  }\n\n  return entry;\n}\n\nfunction hasInstallationRequestTimedOut(\n  installationEntry: InstallationEntry\n): boolean {\n  return (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS &&\n    installationEntry.registrationTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CreateInstallationResponse } from '../interfaces/api-response';\nimport {\n  InProgressInstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { INTERNAL_AUTH_VERSION, PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeaders,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\n\nexport async function createInstallationRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  { fid }: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  const endpoint = getInstallationsEndpoint(appConfig);\n\n  const headers = getHeaders(appConfig);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    fid,\n    authVersion: INTERNAL_AUTH_VERSION,\n    appId: appConfig.appId,\n    sdkVersion: PACKAGE_VERSION\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: CreateInstallationResponse = await response.json();\n    const registeredInstallationEntry: RegisteredInstallationEntry = {\n      fid: responseValue.fid || fid,\n      registrationStatus: RequestStatus.COMPLETED,\n      refreshToken: responseValue.refreshToken,\n      authToken: extractAuthTokenInfoFromResponse(responseValue.authToken)\n    };\n    return registeredInstallationEntry;\n  } else {\n    throw await getErrorFromResponse('Create Installation', response);\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry\n} from '../interfaces/installation-entry';\nimport { PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeadersWithAuth,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport {\n  FirebaseInstallationsImpl,\n  AppConfig\n} from '../interfaces/installation-impl';\n\nexport async function generateAuthTokenRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  const endpoint = getGenerateAuthTokenEndpoint(appConfig, installationEntry);\n\n  const headers = getHeadersWithAuth(appConfig, installationEntry);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    installation: {\n      sdkVersion: PACKAGE_VERSION,\n      appId: appConfig.appId\n    }\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: GenerateAuthTokenResponse = await response.json();\n    const completedAuthToken: CompletedAuthToken =\n      extractAuthTokenInfoFromResponse(responseValue);\n    return completedAuthToken;\n  } else {\n    throw await getErrorFromResponse('Generate Auth Token', response);\n  }\n}\n\nfunction getGenerateAuthTokenEndpoint(\n  appConfig: AppConfig,\n  { fid }: RegisteredInstallationEntry\n): string {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}/authTokens:generate`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { generateAuthTokenRequest } from '../functions/generate-auth-token-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  AuthToken,\n  CompletedAuthToken,\n  InProgressAuthToken,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS, TOKEN_EXPIRATION_BUFFER } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { remove, set, update } from './idb-manager';\n\n/**\n * Returns a valid authentication token for the installation. Generates a new\n * token if one doesn't exist, is expired or about to expire.\n *\n * Should only be called if the Firebase Installation is registered.\n */\nexport async function refreshAuthToken(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh = false\n): Promise<CompletedAuthToken> {\n  let tokenPromise: Promise<CompletedAuthToken> | undefined;\n  const entry = await update(installations.appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (!forceRefresh && isAuthTokenValid(oldAuthToken)) {\n      // There is a valid token in the DB.\n      return oldEntry;\n    } else if (oldAuthToken.requestStatus === RequestStatus.IN_PROGRESS) {\n      // There already is a token request in progress.\n      tokenPromise = waitUntilAuthTokenRequest(installations, forceRefresh);\n      return oldEntry;\n    } else {\n      // No token or token expired.\n      if (!navigator.onLine) {\n        throw ERROR_FACTORY.create(ErrorCode.APP_OFFLINE);\n      }\n\n      const inProgressEntry = makeAuthTokenRequestInProgressEntry(oldEntry);\n      tokenPromise = fetchAuthTokenFromServer(installations, inProgressEntry);\n      return inProgressEntry;\n    }\n  });\n\n  const authToken = tokenPromise\n    ? await tokenPromise\n    : (entry.authToken as CompletedAuthToken);\n  return authToken;\n}\n\n/**\n * Call only if FID is registered and Auth Token request is in progress.\n *\n * Waits until the current pending request finishes. If the request times out,\n * tries once in this thread as well.\n */\nasync function waitUntilAuthTokenRequest(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh: boolean\n): Promise<CompletedAuthToken> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry = await updateAuthTokenRequest(installations.appConfig);\n  while (entry.authToken.requestStatus === RequestStatus.IN_PROGRESS) {\n    // generateAuthToken still in progress.\n    await sleep(100);\n\n    entry = await updateAuthTokenRequest(installations.appConfig);\n  }\n\n  const authToken = entry.authToken;\n  if (authToken.requestStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    return refreshAuthToken(installations, forceRefresh);\n  } else {\n    return authToken;\n  }\n}\n\n/**\n * Called only if there is a GenerateAuthToken request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * GenerateAuthToken request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateAuthTokenRequest(\n  appConfig: AppConfig\n): Promise<RegisteredInstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (hasAuthTokenRequestTimedOut(oldAuthToken)) {\n      return {\n        ...oldEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n    }\n\n    return oldEntry;\n  });\n}\n\nasync function fetchAuthTokenFromServer(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  try {\n    const authToken = await generateAuthTokenRequest(\n      installations,\n      installationEntry\n    );\n    const updatedInstallationEntry: RegisteredInstallationEntry = {\n      ...installationEntry,\n      authToken\n    };\n    await set(installations.appConfig, updatedInstallationEntry);\n    return authToken;\n  } catch (e) {\n    if (\n      isServerError(e) &&\n      (e.customData.serverCode === 401 || e.customData.serverCode === 404)\n    ) {\n      // Server returned a \"FID not found\" or a \"Invalid authentication\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      const updatedInstallationEntry: RegisteredInstallationEntry = {\n        ...installationEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n      await set(installations.appConfig, updatedInstallationEntry);\n    }\n    throw e;\n  }\n}\n\nfunction isEntryRegistered(\n  installationEntry: InstallationEntry | undefined\n): installationEntry is RegisteredInstallationEntry {\n  return (\n    installationEntry !== undefined &&\n    installationEntry.registrationStatus === RequestStatus.COMPLETED\n  );\n}\n\nfunction isAuthTokenValid(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.COMPLETED &&\n    !isAuthTokenExpired(authToken)\n  );\n}\n\nfunction isAuthTokenExpired(authToken: CompletedAuthToken): boolean {\n  const now = Date.now();\n  return (\n    now < authToken.creationTime ||\n    authToken.creationTime + authToken.expiresIn < now + TOKEN_EXPIRATION_BUFFER\n  );\n}\n\n/** Returns an updated InstallationEntry with an InProgressAuthToken. */\nfunction makeAuthTokenRequestInProgressEntry(\n  oldEntry: RegisteredInstallationEntry\n): RegisteredInstallationEntry {\n  const inProgressAuthToken: InProgressAuthToken = {\n    requestStatus: RequestStatus.IN_PROGRESS,\n    requestTime: Date.now()\n  };\n  return {\n    ...oldEntry,\n    authToken: inProgressAuthToken\n  };\n}\n\nfunction hasAuthTokenRequestTimedOut(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.IN_PROGRESS &&\n    authToken.requestTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Returns a Firebase Installations auth token, identifying the current\n * Firebase Installation.\n * @param installations - The `Installations` instance.\n * @param forceRefresh - Force refresh regardless of token expiration.\n *\n * @public\n */\nexport async function getToken(\n  installations: Installations,\n  forceRefresh = false\n): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  await completeInstallationRegistration(installationsImpl);\n\n  // At this point we either have a Registered Installation in the DB, or we've\n  // already thrown an error.\n  const authToken = await refreshAuthToken(installationsImpl, forceRefresh);\n  return authToken.token;\n}\n\nasync function completeInstallationRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<void> {\n  const { registrationPromise } = await getInstallationEntry(installations);\n\n  if (registrationPromise) {\n    // A createInstallation request is in progress. Wait until it finishes.\n    await registrationPromise;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, FirebaseOptions } from '@firebase/app';\nimport { FirebaseError } from '@firebase/util';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nexport function extractAppConfig(app: FirebaseApp): AppConfig {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration');\n  }\n\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n\n  // Required app config keys\n  const configKeys: Array<keyof FirebaseOptions> = [\n    'projectId',\n    'apiKey',\n    'appId'\n  ];\n\n  for (const keyName of configKeys) {\n    if (!app.options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n\n  return {\n    appName: app.name,\n    projectId: app.options.projectId!,\n    apiKey: app.options.apiKey!,\n    appId: app.options.appId!\n  };\n}\n\nfunction getMissingValueError(valueName: string): FirebaseError {\n  return ERROR_FACTORY.create(ErrorCode.MISSING_APP_CONFIG_VALUES, {\n    valueName\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, _getProvider } from '@firebase/app';\nimport {\n  Component,\n  ComponentType,\n  InstanceFactory,\n  ComponentContainer\n} from '@firebase/component';\nimport { getId, getToken } from '../api/index';\nimport { _FirebaseInstallationsInternal } from '../interfaces/public-types';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { extractAppConfig } from '../helpers/extract-app-config';\n\nconst INSTALLATIONS_NAME = 'installations';\nconst INSTALLATIONS_NAME_INTERNAL = 'installations-internal';\n\nconst publicFactory: InstanceFactory<'installations'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Throws if app isn't configured properly.\n  const appConfig = extractAppConfig(app);\n  const heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n\n  const installationsImpl: FirebaseInstallationsImpl = {\n    app,\n    appConfig,\n    heartbeatServiceProvider,\n    _delete: () => Promise.resolve()\n  };\n  return installationsImpl;\n};\n\nconst internalFactory: InstanceFactory<'installations-internal'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Internal FIS instance relies on public FIS instance.\n  const installations = _getProvider(app, INSTALLATIONS_NAME).getImmediate();\n\n  const installationsInternal: _FirebaseInstallationsInternal = {\n    getId: () => getId(installations),\n    getToken: (forceRefresh?: boolean) => getToken(installations, forceRefresh)\n  };\n  return installationsInternal;\n};\n\nexport function registerInstallations(): void {\n  _registerComponent(\n    new Component(INSTALLATIONS_NAME, publicFactory, ComponentType.PUBLIC)\n  );\n  _registerComponent(\n    new Component(\n      INSTALLATIONS_NAME_INTERNAL,\n      internalFactory,\n      ComponentType.PRIVATE\n    )\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Creates a Firebase Installation if there isn't one for the app and\n * returns the Installation ID.\n * @param installations - The `Installations` instance.\n *\n * @public\n */\nexport async function getId(installations: Installations): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  const { installationEntry, registrationPromise } = await getInstallationEntry(\n    installationsImpl\n  );\n\n  if (registrationPromise) {\n    registrationPromise.catch(console.error);\n  } else {\n    // If the installation is already registered, update the authentication\n    // token if needed.\n    refreshAuthToken(installationsImpl).catch(console.error);\n  }\n\n  return installationEntry.fid;\n}\n", "/**\n * The Firebase Installations Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerInstallations } from './functions/config';\nimport { registerVersion } from '@firebase/app';\nimport { name, version } from '../package.json';\n\nexport * from './api';\nexport * from './interfaces/public-types';\n\nregisterInstallations();\nregisterVersion(name, version);\n// BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\nregisterVersion(name, version, '__BUILD_TARGET__');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Type constant for Firebase Analytics.\n */\nexport const ANALYTICS_TYPE = 'analytics';\n\n// Key to attach FID to in gtag params.\nexport const GA_FID_KEY = 'firebase_id';\nexport const ORIGIN_KEY = 'origin';\n\nexport const FETCH_TIMEOUT_MILLIS = 60 * 1000;\n\nexport const DYNAMIC_CONFIG_URL =\n  'https://firebase.googleapis.com/v1alpha/projects/-/apps/{app-id}/webConfig';\n\nexport const GTAG_URL = 'https://www.googletagmanager.com/gtag/js';\n\nexport const enum GtagCommand {\n  EVENT = 'event',\n  SET = 'set',\n  CONFIG = 'config',\n  CONSENT = 'consent',\n  GET = 'get'\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nexport const logger = new Logger('@firebase/analytics');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum AnalyticsError {\n  ALREADY_EXISTS = 'already-exists',\n  ALREADY_INITIALIZED = 'already-initialized',\n  ALREADY_INITIALIZED_SETTINGS = 'already-initialized-settings',\n  INTEROP_COMPONENT_REG_FAILED = 'interop-component-reg-failed',\n  INVALID_ANALYTICS_CONTEXT = 'invalid-analytics-context',\n  INDEXEDDB_UNAVAILABLE = 'indexeddb-unavailable',\n  FETCH_THROTTLE = 'fetch-throttle',\n  CONFIG_FETCH_FAILED = 'config-fetch-failed',\n  NO_API_KEY = 'no-api-key',\n  NO_APP_ID = 'no-app-id',\n  NO_CLIENT_ID = 'no-client-id',\n  INVALID_GTAG_RESOURCE = 'invalid-gtag-resource'\n}\n\nconst ERRORS: ErrorMap<AnalyticsError> = {\n  [AnalyticsError.ALREADY_EXISTS]:\n    'A Firebase Analytics instance with the appId {$id} ' +\n    ' already exists. ' +\n    'Only one Firebase Analytics instance can be created for each appId.',\n  [AnalyticsError.ALREADY_INITIALIZED]:\n    'initializeAnalytics() cannot be called again with different options than those ' +\n    'it was initially called with. It can be called again with the same options to ' +\n    'return the existing instance, or getAnalytics() can be used ' +\n    'to get a reference to the already-initialized instance.',\n  [AnalyticsError.ALREADY_INITIALIZED_SETTINGS]:\n    'Firebase Analytics has already been initialized.' +\n    'settings() must be called before initializing any Analytics instance' +\n    'or it will have no effect.',\n  [AnalyticsError.INTEROP_COMPONENT_REG_FAILED]:\n    'Firebase Analytics Interop Component failed to instantiate: {$reason}',\n  [AnalyticsError.INVALID_ANALYTICS_CONTEXT]:\n    'Firebase Analytics is not supported in this environment. ' +\n    'Wrap initialization of analytics in analytics.isSupported() ' +\n    'to prevent initialization in unsupported environments. Details: {$errorInfo}',\n  [AnalyticsError.INDEXEDDB_UNAVAILABLE]:\n    'IndexedDB unavailable or restricted in this environment. ' +\n    'Wrap initialization of analytics in analytics.isSupported() ' +\n    'to prevent initialization in unsupported environments. Details: {$errorInfo}',\n  [AnalyticsError.FETCH_THROTTLE]:\n    'The config fetch request timed out while in an exponential backoff state.' +\n    ' Unix timestamp in milliseconds when fetch request throttling ends: {$throttleEndTimeMillis}.',\n  [AnalyticsError.CONFIG_FETCH_FAILED]:\n    'Dynamic config fetch failed: [{$httpStatus}] {$responseMessage}',\n  [AnalyticsError.NO_API_KEY]:\n    'The \"apiKey\" field is empty in the local Firebase config. Firebase Analytics requires this field to' +\n    'contain a valid API key.',\n  [AnalyticsError.NO_APP_ID]:\n    'The \"appId\" field is empty in the local Firebase config. Firebase Analytics requires this field to' +\n    'contain a valid app ID.',\n  [AnalyticsError.NO_CLIENT_ID]: 'The \"client_id\" field is empty.',\n  [AnalyticsError.INVALID_GTAG_RESOURCE]:\n    'Trusted Types detected an invalid gtag resource: {$gtagURL}.'\n};\n\ninterface ErrorParams {\n  [AnalyticsError.ALREADY_EXISTS]: { id: string };\n  [AnalyticsError.INTEROP_COMPONENT_REG_FAILED]: { reason: Error };\n  [AnalyticsError.FETCH_THROTTLE]: { throttleEndTimeMillis: number };\n  [AnalyticsError.CONFIG_FETCH_FAILED]: {\n    httpStatus: number;\n    responseMessage: string;\n  };\n  [AnalyticsError.INVALID_ANALYTICS_CONTEXT]: { errorInfo: string };\n  [AnalyticsError.INDEXEDDB_UNAVAILABLE]: { errorInfo: string };\n  [AnalyticsError.INVALID_GTAG_RESOURCE]: { gtagURL: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<AnalyticsError, ErrorParams>(\n  'analytics',\n  'Analytics',\n  ERRORS\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CustomParams,\n  ControlParams,\n  EventParams,\n  ConsentSettings\n} from './public-types';\nimport { DynamicConfig, DataLayer, Gtag, MinimalDynamicConfig } from './types';\nimport { GtagCommand, GTAG_URL } from './constants';\nimport { logger } from './logger';\nimport { AnalyticsError, ERROR_FACTORY } from './errors';\n\n// Possible parameter types for gtag 'event' and 'config' commands\ntype GtagConfigOrEventParams = ControlParams & EventParams & CustomParams;\n\n/**\n * Verifies and creates a TrustedScriptURL.\n */\nexport function createGtagTrustedTypesScriptURL(url: string): string {\n  if (!url.startsWith(GTAG_URL)) {\n    const err = ERROR_FACTORY.create(AnalyticsError.INVALID_GTAG_RESOURCE, {\n      gtagURL: url\n    });\n    logger.warn(err.message);\n    return '';\n  }\n  return url;\n}\n\n/**\n * Makeshift polyfill for Promise.allSettled(). Resolves when all promises\n * have either resolved or rejected.\n *\n * @param promises Array of promises to wait for.\n */\nexport function promiseAllSettled<T>(\n  promises: Array<Promise<T>>\n): Promise<T[]> {\n  return Promise.all(promises.map(promise => promise.catch(e => e)));\n}\n\n/**\n * Creates a TrustedTypePolicy object that implements the rules passed as policyOptions.\n *\n * @param policyName A string containing the name of the policy\n * @param policyOptions Object containing implementations of instance methods for TrustedTypesPolicy, see {@link https://developer.mozilla.org/en-US/docs/Web/API/TrustedTypePolicy#instance_methods\n * | the TrustedTypePolicy reference documentation}.\n */\nexport function createTrustedTypesPolicy(\n  policyName: string,\n  policyOptions: Partial<TrustedTypePolicyOptions>\n): Partial<TrustedTypePolicy> | undefined {\n  // Create a TrustedTypes policy that we can use for updating src\n  // properties\n  let trustedTypesPolicy: Partial<TrustedTypePolicy> | undefined;\n  if (window.trustedTypes) {\n    trustedTypesPolicy = window.trustedTypes.createPolicy(\n      policyName,\n      policyOptions\n    );\n  }\n  return trustedTypesPolicy;\n}\n\n/**\n * Inserts gtag script tag into the page to asynchronously download gtag.\n * @param dataLayerName Name of datalayer (most often the default, \"_dataLayer\").\n */\nexport function insertScriptTag(\n  dataLayerName: string,\n  measurementId: string\n): void {\n  const trustedTypesPolicy = createTrustedTypesPolicy(\n    'firebase-js-sdk-policy',\n    {\n      createScriptURL: createGtagTrustedTypesScriptURL\n    }\n  );\n\n  const script = document.createElement('script');\n  // We are not providing an analyticsId in the URL because it would trigger a `page_view`\n  // without fid. We will initialize ga-id using gtag (config) command together with fid.\n\n  const gtagScriptURL = `${GTAG_URL}?l=${dataLayerName}&id=${measurementId}`;\n  (script.src as string | TrustedScriptURL) = trustedTypesPolicy\n    ? (trustedTypesPolicy as TrustedTypePolicy)?.createScriptURL(gtagScriptURL)\n    : gtagScriptURL;\n\n  script.async = true;\n  document.head.appendChild(script);\n}\n\n/**\n * Get reference to, or create, global datalayer.\n * @param dataLayerName Name of datalayer (most often the default, \"_dataLayer\").\n */\nexport function getOrCreateDataLayer(dataLayerName: string): DataLayer {\n  // Check for existing dataLayer and create if needed.\n  let dataLayer: DataLayer = [];\n  if (Array.isArray(window[dataLayerName])) {\n    dataLayer = window[dataLayerName] as DataLayer;\n  } else {\n    window[dataLayerName] = dataLayer;\n  }\n  return dataLayer;\n}\n\n/**\n * Wrapped gtag logic when gtag is called with 'config' command.\n *\n * @param gtagCore Basic gtag function that just appends to dataLayer.\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\n * @param measurementId GA Measurement ID to set config for.\n * @param gtagParams Gtag config params to set.\n */\nasync function gtagOnConfig(\n  gtagCore: Gtag,\n  initializationPromisesMap: { [appId: string]: Promise<string> },\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  measurementIdToAppId: { [measurementId: string]: string },\n  measurementId: string,\n  gtagParams?: ControlParams & EventParams & CustomParams\n): Promise<void> {\n  // If config is already fetched, we know the appId and can use it to look up what FID promise we\n  /// are waiting for, and wait only on that one.\n  const correspondingAppId = measurementIdToAppId[measurementId as string];\n  try {\n    if (correspondingAppId) {\n      await initializationPromisesMap[correspondingAppId];\n    } else {\n      // If config is not fetched yet, wait for all configs (we don't know which one we need) and\n      // find the appId (if any) corresponding to this measurementId. If there is one, wait on\n      // that appId's initialization promise. If there is none, promise resolves and gtag\n      // call goes through.\n      const dynamicConfigResults = await promiseAllSettled(\n        dynamicConfigPromisesList\n      );\n      const foundConfig = dynamicConfigResults.find(\n        config => config.measurementId === measurementId\n      );\n      if (foundConfig) {\n        await initializationPromisesMap[foundConfig.appId];\n      }\n    }\n  } catch (e) {\n    logger.error(e);\n  }\n  gtagCore(GtagCommand.CONFIG, measurementId, gtagParams);\n}\n\n/**\n * Wrapped gtag logic when gtag is called with 'event' command.\n *\n * @param gtagCore Basic gtag function that just appends to dataLayer.\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementId GA Measurement ID to log event to.\n * @param gtagParams Params to log with this event.\n */\nasync function gtagOnEvent(\n  gtagCore: Gtag,\n  initializationPromisesMap: { [appId: string]: Promise<string> },\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  measurementId: string,\n  gtagParams?: ControlParams & EventParams & CustomParams\n): Promise<void> {\n  try {\n    let initializationPromisesToWaitFor: Array<Promise<string>> = [];\n\n    // If there's a 'send_to' param, check if any ID specified matches\n    // an initializeIds() promise we are waiting for.\n    if (gtagParams && gtagParams['send_to']) {\n      let gaSendToList: string | string[] = gtagParams['send_to'];\n      // Make it an array if is isn't, so it can be dealt with the same way.\n      if (!Array.isArray(gaSendToList)) {\n        gaSendToList = [gaSendToList];\n      }\n      // Checking 'send_to' fields requires having all measurement ID results back from\n      // the dynamic config fetch.\n      const dynamicConfigResults = await promiseAllSettled(\n        dynamicConfigPromisesList\n      );\n      for (const sendToId of gaSendToList) {\n        // Any fetched dynamic measurement ID that matches this 'send_to' ID\n        const foundConfig = dynamicConfigResults.find(\n          config => config.measurementId === sendToId\n        );\n        const initializationPromise =\n          foundConfig && initializationPromisesMap[foundConfig.appId];\n        if (initializationPromise) {\n          initializationPromisesToWaitFor.push(initializationPromise);\n        } else {\n          // Found an item in 'send_to' that is not associated\n          // directly with an FID, possibly a group.  Empty this array,\n          // exit the loop early, and let it get populated below.\n          initializationPromisesToWaitFor = [];\n          break;\n        }\n      }\n    }\n\n    // This will be unpopulated if there was no 'send_to' field , or\n    // if not all entries in the 'send_to' field could be mapped to\n    // a FID. In these cases, wait on all pending initialization promises.\n    if (initializationPromisesToWaitFor.length === 0) {\n      /* eslint-disable-next-line @typescript-eslint/no-floating-promises */\n      initializationPromisesToWaitFor = Object.values(\n        initializationPromisesMap\n      );\n    }\n\n    // Run core gtag function with args after all relevant initialization\n    // promises have been resolved.\n    await Promise.all(initializationPromisesToWaitFor);\n    // Workaround for http://b/141370449 - third argument cannot be undefined.\n    gtagCore(GtagCommand.EVENT, measurementId, gtagParams || {});\n  } catch (e) {\n    logger.error(e);\n  }\n}\n\n/**\n * Wraps a standard gtag function with extra code to wait for completion of\n * relevant initialization promises before sending requests.\n *\n * @param gtagCore Basic gtag function that just appends to dataLayer.\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\n */\nfunction wrapGtag(\n  gtagCore: Gtag,\n  /**\n   * Allows wrapped gtag calls to wait on whichever initialization promises are required,\n   * depending on the contents of the gtag params' `send_to` field, if any.\n   */\n  initializationPromisesMap: { [appId: string]: Promise<string> },\n  /**\n   * Wrapped gtag calls sometimes require all dynamic config fetches to have returned\n   * before determining what initialization promises (which include FIDs) to wait for.\n   */\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  /**\n   * Wrapped gtag config calls can narrow down which initialization promise (with FID)\n   * to wait for if the measurementId is already fetched, by getting the corresponding appId,\n   * which is the key for the initialization promises map.\n   */\n  measurementIdToAppId: { [measurementId: string]: string }\n): Gtag {\n  /**\n   * Wrapper around gtag that ensures FID is sent with gtag calls.\n   * @param command Gtag command type.\n   * @param idOrNameOrParams Measurement ID if command is EVENT/CONFIG, params if command is SET.\n   * @param gtagParams Params if event is EVENT/CONFIG.\n   */\n  async function gtagWrapper(\n    command: 'config' | 'set' | 'event' | 'consent' | 'get' | string,\n    ...args: unknown[]\n  ): Promise<void> {\n    try {\n      // If event, check that relevant initialization promises have completed.\n      if (command === GtagCommand.EVENT) {\n        const [measurementId, gtagParams] = args;\n        // If EVENT, second arg must be measurementId.\n        await gtagOnEvent(\n          gtagCore,\n          initializationPromisesMap,\n          dynamicConfigPromisesList,\n          measurementId as string,\n          gtagParams as GtagConfigOrEventParams\n        );\n      } else if (command === GtagCommand.CONFIG) {\n        const [measurementId, gtagParams] = args;\n        // If CONFIG, second arg must be measurementId.\n        await gtagOnConfig(\n          gtagCore,\n          initializationPromisesMap,\n          dynamicConfigPromisesList,\n          measurementIdToAppId,\n          measurementId as string,\n          gtagParams as GtagConfigOrEventParams\n        );\n      } else if (command === GtagCommand.CONSENT) {\n        const [consentAction, gtagParams] = args;\n        // consentAction can be one of 'default' or 'update'.\n        gtagCore(\n          GtagCommand.CONSENT,\n          consentAction,\n          gtagParams as ConsentSettings\n        );\n      } else if (command === GtagCommand.GET) {\n        const [measurementId, fieldName, callback] = args;\n        gtagCore(\n          GtagCommand.GET,\n          measurementId as string,\n          fieldName as string,\n          callback as (...args: unknown[]) => void\n        );\n      } else if (command === GtagCommand.SET) {\n        const [customParams] = args;\n        // If SET, second arg must be params.\n        gtagCore(GtagCommand.SET, customParams as CustomParams);\n      } else {\n        gtagCore(command, ...args);\n      }\n    } catch (e) {\n      logger.error(e);\n    }\n  }\n  return gtagWrapper as Gtag;\n}\n\n/**\n * Creates global gtag function or wraps existing one if found.\n * This wrapped function attaches Firebase instance ID (FID) to gtag 'config' and\n * 'event' calls that belong to the GAID associated with this Firebase instance.\n *\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\n * @param dataLayerName Name of global GA datalayer array.\n * @param gtagFunctionName Name of global gtag function (\"gtag\" if not user-specified).\n */\nexport function wrapOrCreateGtag(\n  initializationPromisesMap: { [appId: string]: Promise<string> },\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  measurementIdToAppId: { [measurementId: string]: string },\n  dataLayerName: string,\n  gtagFunctionName: string\n): {\n  gtagCore: Gtag;\n  wrappedGtag: Gtag;\n} {\n  // Create a basic core gtag function\n  let gtagCore: Gtag = function (..._args: unknown[]) {\n    // Must push IArguments object, not an array.\n    (window[dataLayerName] as DataLayer).push(arguments);\n  };\n\n  // Replace it with existing one if found\n  if (\n    window[gtagFunctionName] &&\n    typeof window[gtagFunctionName] === 'function'\n  ) {\n    // @ts-ignore\n    gtagCore = window[gtagFunctionName];\n  }\n\n  window[gtagFunctionName] = wrapGtag(\n    gtagCore,\n    initializationPromisesMap,\n    dynamicConfigPromisesList,\n    measurementIdToAppId\n  );\n\n  return {\n    gtagCore,\n    wrappedGtag: window[gtagFunctionName] as Gtag\n  };\n}\n\n/**\n * Returns the script tag in the DOM matching both the gtag url pattern\n * and the provided data layer name.\n */\nexport function findGtagScriptOnPage(\n  dataLayerName: string\n): HTMLScriptElement | null {\n  const scriptTags = window.document.getElementsByTagName('script');\n  for (const tag of Object.values(scriptTags)) {\n    if (\n      tag.src &&\n      tag.src.includes(GTAG_URL) &&\n      tag.src.includes(dataLayerName)\n    ) {\n      return tag;\n    }\n  }\n  return null;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Most logic is copied from packages/remote-config/src/client/retrying_client.ts\n */\n\nimport { FirebaseApp } from '@firebase/app';\nimport { DynamicConfig, ThrottleMetadata, MinimalDynamicConfig } from './types';\nimport { FirebaseError, calculateBackoffMillis } from '@firebase/util';\nimport { AnalyticsError, ERROR_FACTORY } from './errors';\nimport { DYNAMIC_CONFIG_URL, FETCH_TIMEOUT_MILLIS } from './constants';\nimport { logger } from './logger';\n\n// App config fields needed by analytics.\nexport interface AppFields {\n  appId: string;\n  apiKey: string;\n  measurementId?: string;\n}\n\n/**\n * Backoff factor for 503 errors, which we want to be conservative about\n * to avoid overloading servers. Each retry interval will be\n * BASE_INTERVAL_MILLIS * LONG_RETRY_FACTOR ^ retryCount, so the second one\n * will be ~30 seconds (with fuzzing).\n */\nexport const LONG_RETRY_FACTOR = 30;\n\n/**\n * Base wait interval to multiplied by backoffFactor^backoffCount.\n */\nconst BASE_INTERVAL_MILLIS = 1000;\n\n/**\n * Stubbable retry data storage class.\n */\nclass RetryData {\n  constructor(\n    public throttleMetadata: { [appId: string]: ThrottleMetadata } = {},\n    public intervalMillis: number = BASE_INTERVAL_MILLIS\n  ) {}\n\n  getThrottleMetadata(appId: string): ThrottleMetadata {\n    return this.throttleMetadata[appId];\n  }\n\n  setThrottleMetadata(appId: string, metadata: ThrottleMetadata): void {\n    this.throttleMetadata[appId] = metadata;\n  }\n\n  deleteThrottleMetadata(appId: string): void {\n    delete this.throttleMetadata[appId];\n  }\n}\n\nconst defaultRetryData = new RetryData();\n\n/**\n * Set GET request headers.\n * @param apiKey App API key.\n */\nfunction getHeaders(apiKey: string): Headers {\n  return new Headers({\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\n\n/**\n * Fetches dynamic config from backend.\n * @param app Firebase app to fetch config for.\n */\nexport async function fetchDynamicConfig(\n  appFields: AppFields\n): Promise<DynamicConfig> {\n  const { appId, apiKey } = appFields;\n  const request: RequestInit = {\n    method: 'GET',\n    headers: getHeaders(apiKey)\n  };\n  const appUrl = DYNAMIC_CONFIG_URL.replace('{app-id}', appId);\n  const response = await fetch(appUrl, request);\n  if (response.status !== 200 && response.status !== 304) {\n    let errorMessage = '';\n    try {\n      // Try to get any error message text from server response.\n      const jsonResponse = (await response.json()) as {\n        error?: { message?: string };\n      };\n      if (jsonResponse.error?.message) {\n        errorMessage = jsonResponse.error.message;\n      }\n    } catch (_ignored) {}\n    throw ERROR_FACTORY.create(AnalyticsError.CONFIG_FETCH_FAILED, {\n      httpStatus: response.status,\n      responseMessage: errorMessage\n    });\n  }\n  return response.json();\n}\n\n/**\n * Fetches dynamic config from backend, retrying if failed.\n * @param app Firebase app to fetch config for.\n */\nexport async function fetchDynamicConfigWithRetry(\n  app: FirebaseApp,\n  // retryData and timeoutMillis are parameterized to allow passing a different value for testing.\n  retryData: RetryData = defaultRetryData,\n  timeoutMillis?: number\n): Promise<DynamicConfig | MinimalDynamicConfig> {\n  const { appId, apiKey, measurementId } = app.options;\n\n  if (!appId) {\n    throw ERROR_FACTORY.create(AnalyticsError.NO_APP_ID);\n  }\n\n  if (!apiKey) {\n    if (measurementId) {\n      return {\n        measurementId,\n        appId\n      };\n    }\n    throw ERROR_FACTORY.create(AnalyticsError.NO_API_KEY);\n  }\n\n  const throttleMetadata: ThrottleMetadata = retryData.getThrottleMetadata(\n    appId\n  ) || {\n    backoffCount: 0,\n    throttleEndTimeMillis: Date.now()\n  };\n\n  const signal = new AnalyticsAbortSignal();\n\n  setTimeout(\n    async () => {\n      // Note a very low delay, eg < 10ms, can elapse before listeners are initialized.\n      signal.abort();\n    },\n    timeoutMillis !== undefined ? timeoutMillis : FETCH_TIMEOUT_MILLIS\n  );\n\n  return attemptFetchDynamicConfigWithRetry(\n    { appId, apiKey, measurementId },\n    throttleMetadata,\n    signal,\n    retryData\n  );\n}\n\n/**\n * Runs one retry attempt.\n * @param appFields Necessary app config fields.\n * @param throttleMetadata Ongoing metadata to determine throttling times.\n * @param signal Abort signal.\n */\nasync function attemptFetchDynamicConfigWithRetry(\n  appFields: AppFields,\n  { throttleEndTimeMillis, backoffCount }: ThrottleMetadata,\n  signal: AnalyticsAbortSignal,\n  retryData: RetryData = defaultRetryData // for testing\n): Promise<DynamicConfig | MinimalDynamicConfig> {\n  const { appId, measurementId } = appFields;\n  // Starts with a (potentially zero) timeout to support resumption from stored state.\n  // Ensures the throttle end time is honored if the last attempt timed out.\n  // Note the SDK will never make a request if the fetch timeout expires at this point.\n  try {\n    await setAbortableTimeout(signal, throttleEndTimeMillis);\n  } catch (e) {\n    if (measurementId) {\n      logger.warn(\n        `Timed out fetching this Firebase app's measurement ID from the server.` +\n          ` Falling back to the measurement ID ${measurementId}` +\n          ` provided in the \"measurementId\" field in the local Firebase config. [${\n            (e as Error)?.message\n          }]`\n      );\n      return { appId, measurementId };\n    }\n    throw e;\n  }\n\n  try {\n    const response = await fetchDynamicConfig(appFields);\n\n    // Note the SDK only clears throttle state if response is success or non-retriable.\n    retryData.deleteThrottleMetadata(appId);\n\n    return response;\n  } catch (e) {\n    const error = e as Error;\n    if (!isRetriableError(error)) {\n      retryData.deleteThrottleMetadata(appId);\n      if (measurementId) {\n        logger.warn(\n          `Failed to fetch this Firebase app's measurement ID from the server.` +\n            ` Falling back to the measurement ID ${measurementId}` +\n            ` provided in the \"measurementId\" field in the local Firebase config. [${error?.message}]`\n        );\n        return { appId, measurementId };\n      } else {\n        throw e;\n      }\n    }\n\n    const backoffMillis =\n      Number(error?.customData?.httpStatus) === 503\n        ? calculateBackoffMillis(\n            backoffCount,\n            retryData.intervalMillis,\n            LONG_RETRY_FACTOR\n          )\n        : calculateBackoffMillis(backoffCount, retryData.intervalMillis);\n\n    // Increments backoff state.\n    const throttleMetadata = {\n      throttleEndTimeMillis: Date.now() + backoffMillis,\n      backoffCount: backoffCount + 1\n    };\n\n    // Persists state.\n    retryData.setThrottleMetadata(appId, throttleMetadata);\n    logger.debug(`Calling attemptFetch again in ${backoffMillis} millis`);\n\n    return attemptFetchDynamicConfigWithRetry(\n      appFields,\n      throttleMetadata,\n      signal,\n      retryData\n    );\n  }\n}\n\n/**\n * Supports waiting on a backoff by:\n *\n * <ul>\n *   <li>Promisifying setTimeout, so we can set a timeout in our Promise chain</li>\n *   <li>Listening on a signal bus for abort events, just like the Fetch API</li>\n *   <li>Failing in the same way the Fetch API fails, so timing out a live request and a throttled\n *       request appear the same.</li>\n * </ul>\n *\n * <p>Visible for testing.\n */\nfunction setAbortableTimeout(\n  signal: AnalyticsAbortSignal,\n  throttleEndTimeMillis: number\n): Promise<void> {\n  return new Promise((resolve, reject) => {\n    // Derives backoff from given end time, normalizing negative numbers to zero.\n    const backoffMillis = Math.max(throttleEndTimeMillis - Date.now(), 0);\n\n    const timeout = setTimeout(resolve, backoffMillis);\n\n    // Adds listener, rather than sets onabort, because signal is a shared object.\n    signal.addEventListener(() => {\n      clearTimeout(timeout);\n      // If the request completes before this timeout, the rejection has no effect.\n      reject(\n        ERROR_FACTORY.create(AnalyticsError.FETCH_THROTTLE, {\n          throttleEndTimeMillis\n        })\n      );\n    });\n  });\n}\n\ntype RetriableError = FirebaseError & { customData: { httpStatus: string } };\n\n/**\n * Returns true if the {@link Error} indicates a fetch request may succeed later.\n */\nfunction isRetriableError(e: Error): e is RetriableError {\n  if (!(e instanceof FirebaseError) || !e.customData) {\n    return false;\n  }\n\n  // Uses string index defined by ErrorData, which FirebaseError implements.\n  const httpStatus = Number(e.customData['httpStatus']);\n\n  return (\n    httpStatus === 429 ||\n    httpStatus === 500 ||\n    httpStatus === 503 ||\n    httpStatus === 504\n  );\n}\n\n/**\n * Shims a minimal AbortSignal (copied from Remote Config).\n *\n * <p>AbortController's AbortSignal conveniently decouples fetch timeout logic from other aspects\n * of networking, such as retries. Firebase doesn't use AbortController enough to justify a\n * polyfill recommendation, like we do with the Fetch API, but this minimal shim can easily be\n * swapped out if/when we do.\n */\nexport class AnalyticsAbortSignal {\n  listeners: Array<() => void> = [];\n  addEventListener(listener: () => void): void {\n    this.listeners.push(listener);\n  }\n  abort(): void {\n    this.listeners.forEach(listener => listener());\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DynamicConfig, Gtag, MinimalDynamicConfig } from './types';\nimport { GtagCommand, GA_FID_KEY, ORIGIN_KEY } from './constants';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\nimport { fetchDynamicConfigWithRetry } from './get-config';\nimport { logger } from './logger';\nimport { FirebaseApp } from '@firebase/app';\nimport {\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable\n} from '@firebase/util';\nimport { ERROR_FACTORY, AnalyticsError } from './errors';\nimport { findGtagScriptOnPage, insertScriptTag } from './helpers';\nimport { AnalyticsSettings } from './public-types';\nimport {\n  defaultConsentSettingsForInit,\n  _setConsentDefaultForInit,\n  defaultEventParametersForInit,\n  _setDefaultEventParametersForInit\n} from './functions';\n\nasync function validateIndexedDB(): Promise<boolean> {\n  if (!isIndexedDBAvailable()) {\n    logger.warn(\n      ERROR_FACTORY.create(AnalyticsError.INDEXEDDB_UNAVAILABLE, {\n        errorInfo: 'IndexedDB is not available in this environment.'\n      }).message\n    );\n    return false;\n  } else {\n    try {\n      await validateIndexedDBOpenable();\n    } catch (e) {\n      logger.warn(\n        ERROR_FACTORY.create(AnalyticsError.INDEXEDDB_UNAVAILABLE, {\n          errorInfo: (e as Error)?.toString()\n        }).message\n      );\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Initialize the analytics instance in gtag.js by calling config command with fid.\n *\n * NOTE: We combine analytics initialization and setting fid together because we want fid to be\n * part of the `page_view` event that's sent during the initialization\n * @param app Firebase app\n * @param gtagCore The gtag function that's not wrapped.\n * @param dynamicConfigPromisesList Array of all dynamic config promises.\n * @param measurementIdToAppId Maps measurementID to appID.\n * @param installations _FirebaseInstallationsInternal instance.\n *\n * @returns Measurement ID.\n */\nexport async function _initializeAnalytics(\n  app: FirebaseApp,\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  measurementIdToAppId: { [key: string]: string },\n  installations: _FirebaseInstallationsInternal,\n  gtagCore: Gtag,\n  dataLayerName: string,\n  options?: AnalyticsSettings\n): Promise<string> {\n  const dynamicConfigPromise = fetchDynamicConfigWithRetry(app);\n  // Once fetched, map measurementIds to appId, for ease of lookup in wrapped gtag function.\n  dynamicConfigPromise\n    .then(config => {\n      measurementIdToAppId[config.measurementId] = config.appId;\n      if (\n        app.options.measurementId &&\n        config.measurementId !== app.options.measurementId\n      ) {\n        logger.warn(\n          `The measurement ID in the local Firebase config (${app.options.measurementId})` +\n            ` does not match the measurement ID fetched from the server (${config.measurementId}).` +\n            ` To ensure analytics events are always sent to the correct Analytics property,` +\n            ` update the` +\n            ` measurement ID field in the local config or remove it from the local config.`\n        );\n      }\n    })\n    .catch(e => logger.error(e));\n  // Add to list to track state of all dynamic config promises.\n  dynamicConfigPromisesList.push(dynamicConfigPromise);\n\n  const fidPromise: Promise<string | undefined> = validateIndexedDB().then(\n    envIsValid => {\n      if (envIsValid) {\n        return installations.getId();\n      } else {\n        return undefined;\n      }\n    }\n  );\n\n  const [dynamicConfig, fid] = await Promise.all([\n    dynamicConfigPromise,\n    fidPromise\n  ]);\n\n  // Detect if user has already put the gtag <script> tag on this page with the passed in\n  // data layer name.\n  if (!findGtagScriptOnPage(dataLayerName)) {\n    insertScriptTag(dataLayerName, dynamicConfig.measurementId);\n  }\n\n  // Detects if there are consent settings that need to be configured.\n  if (defaultConsentSettingsForInit) {\n    gtagCore(GtagCommand.CONSENT, 'default', defaultConsentSettingsForInit);\n    _setConsentDefaultForInit(undefined);\n  }\n\n  // This command initializes gtag.js and only needs to be called once for the entire web app,\n  // but since it is idempotent, we can call it multiple times.\n  // We keep it together with other initialization logic for better code structure.\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  (gtagCore as any)('js', new Date());\n  // User config added first. We don't want users to accidentally overwrite\n  // base Firebase config properties.\n  const configProperties: Record<string, unknown> = options?.config ?? {};\n\n  // guard against developers accidentally setting properties with prefix `firebase_`\n  configProperties[ORIGIN_KEY] = 'firebase';\n  configProperties.update = true;\n\n  if (fid != null) {\n    configProperties[GA_FID_KEY] = fid;\n  }\n\n  // It should be the first config command called on this GA-ID\n  // Initialize this GA-ID and set FID on it using the gtag config API.\n  // Note: This will trigger a page_view event unless 'send_page_view' is set to false in\n  // `configProperties`.\n  gtagCore(GtagCommand.CONFIG, dynamicConfig.measurementId, configProperties);\n\n  // Detects if there is data that will be set on every event logged from the SDK.\n  if (defaultEventParametersForInit) {\n    gtagCore(GtagCommand.SET, defaultEventParametersForInit);\n    _setDefaultEventParametersForInit(undefined);\n  }\n\n  return dynamicConfig.measurementId;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SettingsOptions, Analytics, AnalyticsSettings } from './public-types';\nimport { Gtag, DynamicConfig, MinimalDynamicConfig } from './types';\nimport { getOrCreateDataLayer, wrapOrCreateGtag } from './helpers';\nimport { AnalyticsError, ERROR_FACTORY } from './errors';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\nimport { areCookiesEnabled, isBrowserExtension } from '@firebase/util';\nimport { _initializeAnalytics } from './initialize-analytics';\nimport { logger } from './logger';\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\n\n/**\n * Analytics Service class.\n */\nexport class AnalyticsService implements Analytics, _FirebaseService {\n  constructor(public app: FirebaseApp) {}\n  _delete(): Promise<void> {\n    delete initializationPromisesMap[this.app.options.appId!];\n    return Promise.resolve();\n  }\n}\n\n/**\n * Maps appId to full initialization promise. Wrapped gtag calls must wait on\n * all or some of these, depending on the call's `send_to` param and the status\n * of the dynamic config fetches (see below).\n */\nexport let initializationPromisesMap: {\n  [appId: string]: Promise<string>; // Promise contains measurement ID string.\n} = {};\n\n/**\n * List of dynamic config fetch promises. In certain cases, wrapped gtag calls\n * wait on all these to be complete in order to determine if it can selectively\n * wait for only certain initialization (FID) promises or if it must wait for all.\n */\nlet dynamicConfigPromisesList: Array<\n  Promise<DynamicConfig | MinimalDynamicConfig>\n> = [];\n\n/**\n * Maps fetched measurementIds to appId. Populated when the app's dynamic config\n * fetch completes. If already populated, gtag config calls can use this to\n * selectively wait for only this app's initialization promise (FID) instead of all\n * initialization promises.\n */\nconst measurementIdToAppId: { [measurementId: string]: string } = {};\n\n/**\n * Name for window global data layer array used by GA: defaults to 'dataLayer'.\n */\nlet dataLayerName: string = 'dataLayer';\n\n/**\n * Name for window global gtag function used by GA: defaults to 'gtag'.\n */\nlet gtagName: string = 'gtag';\n\n/**\n * Reproduction of standard gtag function or reference to existing\n * gtag function on window object.\n */\nlet gtagCoreFunction: Gtag;\n\n/**\n * Wrapper around gtag function that ensures FID is sent with all\n * relevant event and config calls.\n */\nexport let wrappedGtagFunction: Gtag;\n\n/**\n * Flag to ensure page initialization steps (creation or wrapping of\n * dataLayer and gtag script) are only run once per page load.\n */\nlet globalInitDone: boolean = false;\n\n/**\n * For testing\n * @internal\n */\nexport function resetGlobalVars(\n  newGlobalInitDone = false,\n  newInitializationPromisesMap = {},\n  newDynamicPromises = []\n): void {\n  globalInitDone = newGlobalInitDone;\n  initializationPromisesMap = newInitializationPromisesMap;\n  dynamicConfigPromisesList = newDynamicPromises;\n  dataLayerName = 'dataLayer';\n  gtagName = 'gtag';\n}\n\n/**\n * For testing\n * @internal\n */\nexport function getGlobalVars(): {\n  initializationPromisesMap: { [appId: string]: Promise<string> };\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >;\n} {\n  return {\n    initializationPromisesMap,\n    dynamicConfigPromisesList\n  };\n}\n\n/**\n * Configures Firebase Analytics to use custom `gtag` or `dataLayer` names.\n * Intended to be used if `gtag.js` script has been installed on\n * this page independently of Firebase Analytics, and is using non-default\n * names for either the `gtag` function or for `dataLayer`.\n * Must be called before calling `getAnalytics()` or it won't\n * have any effect.\n *\n * @public\n *\n * @param options - Custom gtag and dataLayer names.\n */\nexport function settings(options: SettingsOptions): void {\n  if (globalInitDone) {\n    throw ERROR_FACTORY.create(AnalyticsError.ALREADY_INITIALIZED);\n  }\n  if (options.dataLayerName) {\n    dataLayerName = options.dataLayerName;\n  }\n  if (options.gtagName) {\n    gtagName = options.gtagName;\n  }\n}\n\n/**\n * Returns true if no environment mismatch is found.\n * If environment mismatches are found, throws an INVALID_ANALYTICS_CONTEXT\n * error that also lists details for each mismatch found.\n */\nfunction warnOnBrowserContextMismatch(): void {\n  const mismatchedEnvMessages = [];\n  if (isBrowserExtension()) {\n    mismatchedEnvMessages.push('This is a browser extension environment.');\n  }\n  if (!areCookiesEnabled()) {\n    mismatchedEnvMessages.push('Cookies are not available.');\n  }\n  if (mismatchedEnvMessages.length > 0) {\n    const details = mismatchedEnvMessages\n      .map((message, index) => `(${index + 1}) ${message}`)\n      .join(' ');\n    const err = ERROR_FACTORY.create(AnalyticsError.INVALID_ANALYTICS_CONTEXT, {\n      errorInfo: details\n    });\n    logger.warn(err.message);\n  }\n}\n\n/**\n * Analytics instance factory.\n * @internal\n */\nexport function factory(\n  app: FirebaseApp,\n  installations: _FirebaseInstallationsInternal,\n  options?: AnalyticsSettings\n): AnalyticsService {\n  warnOnBrowserContextMismatch();\n  const appId = app.options.appId;\n  if (!appId) {\n    throw ERROR_FACTORY.create(AnalyticsError.NO_APP_ID);\n  }\n  if (!app.options.apiKey) {\n    if (app.options.measurementId) {\n      logger.warn(\n        `The \"apiKey\" field is empty in the local Firebase config. This is needed to fetch the latest` +\n          ` measurement ID for this Firebase app. Falling back to the measurement ID ${app.options.measurementId}` +\n          ` provided in the \"measurementId\" field in the local Firebase config.`\n      );\n    } else {\n      throw ERROR_FACTORY.create(AnalyticsError.NO_API_KEY);\n    }\n  }\n  if (initializationPromisesMap[appId] != null) {\n    throw ERROR_FACTORY.create(AnalyticsError.ALREADY_EXISTS, {\n      id: appId\n    });\n  }\n\n  if (!globalInitDone) {\n    // Steps here should only be done once per page: creation or wrapping\n    // of dataLayer and global gtag function.\n\n    getOrCreateDataLayer(dataLayerName);\n\n    const { wrappedGtag, gtagCore } = wrapOrCreateGtag(\n      initializationPromisesMap,\n      dynamicConfigPromisesList,\n      measurementIdToAppId,\n      dataLayerName,\n      gtagName\n    );\n    wrappedGtagFunction = wrappedGtag;\n    gtagCoreFunction = gtagCore;\n\n    globalInitDone = true;\n  }\n  // Async but non-blocking.\n  // This map reflects the completion state of all promises for each appId.\n  initializationPromisesMap[appId] = _initializeAnalytics(\n    app,\n    dynamicConfigPromisesList,\n    measurementIdToAppId,\n    installations,\n    gtagCoreFunction,\n    dataLayerName,\n    options\n  );\n\n  const analyticsInstance: AnalyticsService = new AnalyticsService(app);\n\n  return analyticsInstance;\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable camelcase */\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\nimport {\n  Analytics,\n  AnalyticsCallOptions,\n  AnalyticsSettings,\n  ConsentSettings,\n  CustomParams,\n  EventNameString,\n  EventParams\n} from './public-types';\nimport { Provider } from '@firebase/component';\nimport {\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable,\n  areCookiesEnabled,\n  isBrowserExtension,\n  getModularInstance,\n  deepEqual\n} from '@firebase/util';\nimport { ANALYTICS_TYPE, GtagCommand } from './constants';\nimport {\n  AnalyticsService,\n  initializationPromisesMap,\n  wrappedGtagFunction\n} from './factory';\nimport { logger } from './logger';\nimport {\n  logEvent as internalLogEvent,\n  setCurrentScreen as internalSetCurrentScreen,\n  setUserId as internalSetUserId,\n  setUserProperties as internalSetUserProperties,\n  setAnalyticsCollectionEnabled as internalSetAnalyticsCollectionEnabled,\n  _setConsentDefaultForInit,\n  _setDefaultEventParametersForInit,\n  internalGetGoogleAnalyticsClientId\n} from './functions';\nimport { ERROR_FACTORY, AnalyticsError } from './errors';\n\nexport { settings } from './factory';\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    [ANALYTICS_TYPE]: AnalyticsService;\n  }\n}\n\n/**\n * Returns an {@link Analytics} instance for the given app.\n *\n * @public\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n */\nexport function getAnalytics(app: FirebaseApp = getApp()): Analytics {\n  app = getModularInstance(app);\n  // Dependencies\n  const analyticsProvider: Provider<'analytics'> = _getProvider(\n    app,\n    ANALYTICS_TYPE\n  );\n\n  if (analyticsProvider.isInitialized()) {\n    return analyticsProvider.getImmediate();\n  }\n\n  return initializeAnalytics(app);\n}\n\n/**\n * Returns an {@link Analytics} instance for the given app.\n *\n * @public\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n */\nexport function initializeAnalytics(\n  app: FirebaseApp,\n  options: AnalyticsSettings = {}\n): Analytics {\n  // Dependencies\n  const analyticsProvider: Provider<'analytics'> = _getProvider(\n    app,\n    ANALYTICS_TYPE\n  );\n  if (analyticsProvider.isInitialized()) {\n    const existingInstance = analyticsProvider.getImmediate();\n    if (deepEqual(options, analyticsProvider.getOptions())) {\n      return existingInstance;\n    } else {\n      throw ERROR_FACTORY.create(AnalyticsError.ALREADY_INITIALIZED);\n    }\n  }\n  const analyticsInstance = analyticsProvider.initialize({ options });\n  return analyticsInstance;\n}\n\n/**\n * This is a public static method provided to users that wraps four different checks:\n *\n * 1. Check if it's not a browser extension environment.\n * 2. Check if cookies are enabled in current browser.\n * 3. Check if IndexedDB is supported by the browser environment.\n * 4. Check if the current browser context is valid for using `IndexedDB.open()`.\n *\n * @public\n *\n */\nexport async function isSupported(): Promise<boolean> {\n  if (isBrowserExtension()) {\n    return false;\n  }\n  if (!areCookiesEnabled()) {\n    return false;\n  }\n  if (!isIndexedDBAvailable()) {\n    return false;\n  }\n\n  try {\n    const isDBOpenable: boolean = await validateIndexedDBOpenable();\n    return isDBOpenable;\n  } catch (error) {\n    return false;\n  }\n}\n\n/**\n * Use gtag `config` command to set `screen_name`.\n *\n * @public\n *\n * @deprecated Use {@link logEvent} with `eventName` as 'screen_view' and add relevant `eventParams`.\n * See {@link https://firebase.google.com/docs/analytics/screenviews | Track Screenviews}.\n *\n * @param analyticsInstance - The {@link Analytics} instance.\n * @param screenName - Screen name to set.\n */\nexport function setCurrentScreen(\n  analyticsInstance: Analytics,\n  screenName: string,\n  options?: AnalyticsCallOptions\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalSetCurrentScreen(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    screenName,\n    options\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Retrieves a unique Google Analytics identifier for the web client.\n * See {@link https://developers.google.com/analytics/devguides/collection/ga4/reference/config#client_id | client_id}.\n *\n * @public\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n */\nexport async function getGoogleAnalyticsClientId(\n  analyticsInstance: Analytics\n): Promise<string> {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  return internalGetGoogleAnalyticsClientId(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!]\n  );\n}\n\n/**\n * Use gtag `config` command to set `user_id`.\n *\n * @public\n *\n * @param analyticsInstance - The {@link Analytics} instance.\n * @param id - User ID to set.\n */\nexport function setUserId(\n  analyticsInstance: Analytics,\n  id: string | null,\n  options?: AnalyticsCallOptions\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalSetUserId(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    id,\n    options\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Use gtag `config` command to set all params specified.\n *\n * @public\n */\nexport function setUserProperties(\n  analyticsInstance: Analytics,\n  properties: CustomParams,\n  options?: AnalyticsCallOptions\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalSetUserProperties(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    properties,\n    options\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Sets whether Google Analytics collection is enabled for this app on this device.\n * Sets global `window['ga-disable-analyticsId'] = true;`\n *\n * @public\n *\n * @param analyticsInstance - The {@link Analytics} instance.\n * @param enabled - If true, enables collection, if false, disables it.\n */\nexport function setAnalyticsCollectionEnabled(\n  analyticsInstance: Analytics,\n  enabled: boolean\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalSetAnalyticsCollectionEnabled(\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    enabled\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Adds data that will be set on every event logged from the SDK, including automatic ones.\n * With gtag's \"set\" command, the values passed persist on the current page and are passed with\n * all subsequent events.\n * @public\n * @param customParams - Any custom params the user may pass to gtag.js.\n */\nexport function setDefaultEventParameters(customParams: CustomParams): void {\n  // Check if reference to existing gtag function on window object exists\n  if (wrappedGtagFunction) {\n    wrappedGtagFunction(GtagCommand.SET, customParams);\n  } else {\n    _setDefaultEventParametersForInit(customParams);\n  }\n}\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'add_payment_info',\n  eventParams?: {\n    coupon?: EventParams['coupon'];\n    currency?: EventParams['currency'];\n    items?: EventParams['items'];\n    payment_type?: EventParams['payment_type'];\n    value?: EventParams['value'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'add_shipping_info',\n  eventParams?: {\n    coupon?: EventParams['coupon'];\n    currency?: EventParams['currency'];\n    items?: EventParams['items'];\n    shipping_tier?: EventParams['shipping_tier'];\n    value?: EventParams['value'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'add_to_cart' | 'add_to_wishlist' | 'remove_from_cart',\n  eventParams?: {\n    currency?: EventParams['currency'];\n    value?: EventParams['value'];\n    items?: EventParams['items'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'begin_checkout',\n  eventParams?: {\n    currency?: EventParams['currency'];\n    coupon?: EventParams['coupon'];\n    value?: EventParams['value'];\n    items?: EventParams['items'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'checkout_progress',\n  eventParams?: {\n    currency?: EventParams['currency'];\n    coupon?: EventParams['coupon'];\n    value?: EventParams['value'];\n    items?: EventParams['items'];\n    checkout_step?: EventParams['checkout_step'];\n    checkout_option?: EventParams['checkout_option'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * See\n * {@link https://developers.google.com/analytics/devguides/collection/ga4/exceptions\n * | Measure exceptions}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'exception',\n  eventParams?: {\n    description?: EventParams['description'];\n    fatal?: EventParams['fatal'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'generate_lead',\n  eventParams?: {\n    value?: EventParams['value'];\n    currency?: EventParams['currency'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'login',\n  eventParams?: {\n    method?: EventParams['method'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * See\n * {@link https://developers.google.com/analytics/devguides/collection/ga4/views\n * | Page views}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'page_view',\n  eventParams?: {\n    page_title?: string;\n    page_location?: string;\n    page_path?: string;\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'purchase' | 'refund',\n  eventParams?: {\n    value?: EventParams['value'];\n    currency?: EventParams['currency'];\n    transaction_id: EventParams['transaction_id'];\n    tax?: EventParams['tax'];\n    shipping?: EventParams['shipping'];\n    items?: EventParams['items'];\n    coupon?: EventParams['coupon'];\n    affiliation?: EventParams['affiliation'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * See {@link https://firebase.google.com/docs/analytics/screenviews\n * | Track Screenviews}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'screen_view',\n  eventParams?: {\n    firebase_screen: EventParams['firebase_screen'];\n    firebase_screen_class: EventParams['firebase_screen_class'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'search' | 'view_search_results',\n  eventParams?: {\n    search_term?: EventParams['search_term'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'select_content',\n  eventParams?: {\n    content_type?: EventParams['content_type'];\n    item_id?: EventParams['item_id'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'select_item',\n  eventParams?: {\n    items?: EventParams['items'];\n    item_list_name?: EventParams['item_list_name'];\n    item_list_id?: EventParams['item_list_id'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'select_promotion' | 'view_promotion',\n  eventParams?: {\n    items?: EventParams['items'];\n    promotion_id?: EventParams['promotion_id'];\n    promotion_name?: EventParams['promotion_name'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'set_checkout_option',\n  eventParams?: {\n    checkout_step?: EventParams['checkout_step'];\n    checkout_option?: EventParams['checkout_option'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'share',\n  eventParams?: {\n    method?: EventParams['method'];\n    content_type?: EventParams['content_type'];\n    item_id?: EventParams['item_id'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'sign_up',\n  eventParams?: {\n    method?: EventParams['method'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'timing_complete',\n  eventParams?: {\n    name: string;\n    value: number;\n    event_category?: string;\n    event_label?: string;\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'view_cart' | 'view_item',\n  eventParams?: {\n    currency?: EventParams['currency'];\n    items?: EventParams['items'];\n    value?: EventParams['value'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'view_item_list',\n  eventParams?: {\n    items?: EventParams['items'];\n    item_list_name?: EventParams['item_list_name'];\n    item_list_id?: EventParams['item_list_id'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent<T extends string>(\n  analyticsInstance: Analytics,\n  eventName: CustomEventName<T>,\n  eventParams?: { [key: string]: any },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * List of official event parameters can be found in the gtag.js\n * reference documentation:\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n *\n * @public\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: string,\n  eventParams?: EventParams,\n  options?: AnalyticsCallOptions\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalLogEvent(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    eventName,\n    eventParams,\n    options\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Any custom event name string not in the standard list of recommended\n * event names.\n * @public\n */\nexport type CustomEventName<T> = T extends EventNameString ? never : T;\n\n/**\n * Sets the applicable end user consent state for this web app across all gtag references once\n * Firebase Analytics is initialized.\n *\n * Use the {@link ConsentSettings} to specify individual consent type values. By default consent\n * types are set to \"granted\".\n * @public\n * @param consentSettings - Maps the applicable end user consent state for gtag.js.\n */\nexport function setConsent(consentSettings: ConsentSettings): void {\n  // Check if reference to existing gtag function on window object exists\n  if (wrappedGtagFunction) {\n    wrappedGtagFunction(GtagCommand.CONSENT, 'update', consentSettings);\n  } else {\n    _setConsentDefaultForInit(consentSettings);\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AnalyticsCallOptions,\n  CustomParams,\n  ControlParams,\n  EventParams,\n  ConsentSettings\n} from './public-types';\nimport { Gtag } from './types';\nimport { GtagCommand } from './constants';\nimport { AnalyticsError, ERROR_FACTORY } from './errors';\n\n/**\n * Event parameters to set on 'gtag' during initialization.\n */\nexport let defaultEventParametersForInit: CustomParams | undefined;\n\n/**\n * Logs an analytics event through the Firebase SDK.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param eventName Google Analytics event name, choose from standard list or use a custom string.\n * @param eventParams Analytics event parameters.\n */\nexport async function logEvent(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>,\n  eventName: string,\n  eventParams?: EventParams,\n  options?: AnalyticsCallOptions\n): Promise<void> {\n  if (options && options.global) {\n    gtagFunction(GtagCommand.EVENT, eventName, eventParams);\n    return;\n  } else {\n    const measurementId = await initializationPromise;\n    const params: EventParams | ControlParams = {\n      ...eventParams,\n      'send_to': measurementId\n    };\n    gtagFunction(GtagCommand.EVENT, eventName, params);\n  }\n}\n\n/**\n * Set screen_name parameter for this Google Analytics ID.\n *\n * @deprecated Use {@link logEvent} with `eventName` as 'screen_view' and add relevant `eventParams`.\n * See {@link https://firebase.google.com/docs/analytics/screenviews | Track Screenviews}.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param screenName Screen name string to set.\n */\nexport async function setCurrentScreen(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>,\n  screenName: string | null,\n  options?: AnalyticsCallOptions\n): Promise<void> {\n  if (options && options.global) {\n    gtagFunction(GtagCommand.SET, { 'screen_name': screenName });\n    return Promise.resolve();\n  } else {\n    const measurementId = await initializationPromise;\n    gtagFunction(GtagCommand.CONFIG, measurementId, {\n      update: true,\n      'screen_name': screenName\n    });\n  }\n}\n\n/**\n * Set user_id parameter for this Google Analytics ID.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param id User ID string to set\n */\nexport async function setUserId(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>,\n  id: string | null,\n  options?: AnalyticsCallOptions\n): Promise<void> {\n  if (options && options.global) {\n    gtagFunction(GtagCommand.SET, { 'user_id': id });\n    return Promise.resolve();\n  } else {\n    const measurementId = await initializationPromise;\n    gtagFunction(GtagCommand.CONFIG, measurementId, {\n      update: true,\n      'user_id': id\n    });\n  }\n}\n\n/**\n * Set all other user properties other than user_id and screen_name.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param properties Map of user properties to set\n */\nexport async function setUserProperties(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>,\n  properties: CustomParams,\n  options?: AnalyticsCallOptions\n): Promise<void> {\n  if (options && options.global) {\n    const flatProperties: { [key: string]: unknown } = {};\n    for (const key of Object.keys(properties)) {\n      // use dot notation for merge behavior in gtag.js\n      flatProperties[`user_properties.${key}`] = properties[key];\n    }\n    gtagFunction(GtagCommand.SET, flatProperties);\n    return Promise.resolve();\n  } else {\n    const measurementId = await initializationPromise;\n    gtagFunction(GtagCommand.CONFIG, measurementId, {\n      update: true,\n      'user_properties': properties\n    });\n  }\n}\n\n/**\n * Retrieves a unique Google Analytics identifier for the web client.\n * See {@link https://developers.google.com/analytics/devguides/collection/ga4/reference/config#client_id | client_id}.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n */\nexport async function internalGetGoogleAnalyticsClientId(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>\n): Promise<string> {\n  const measurementId = await initializationPromise;\n  return new Promise((resolve, reject) => {\n    gtagFunction(\n      GtagCommand.GET,\n      measurementId,\n      'client_id',\n      (clientId: string) => {\n        if (!clientId) {\n          reject(ERROR_FACTORY.create(AnalyticsError.NO_CLIENT_ID));\n        }\n        resolve(clientId);\n      }\n    );\n  });\n}\n\n/**\n * Set whether collection is enabled for this ID.\n *\n * @param enabled If true, collection is enabled for this ID.\n */\nexport async function setAnalyticsCollectionEnabled(\n  initializationPromise: Promise<string>,\n  enabled: boolean\n): Promise<void> {\n  const measurementId = await initializationPromise;\n  window[`ga-disable-${measurementId}`] = !enabled;\n}\n\n/**\n * Consent parameters to default to during 'gtag' initialization.\n */\nexport let defaultConsentSettingsForInit: ConsentSettings | undefined;\n\n/**\n * Sets the variable {@link defaultConsentSettingsForInit} for use in the initialization of\n * analytics.\n *\n * @param consentSettings Maps the applicable end user consent state for gtag.js.\n */\nexport function _setConsentDefaultForInit(\n  consentSettings?: ConsentSettings\n): void {\n  defaultConsentSettingsForInit = consentSettings;\n}\n\n/**\n * Sets the variable `defaultEventParametersForInit` for use in the initialization of\n * analytics.\n *\n * @param customParams Any custom params the user may pass to gtag.js.\n */\nexport function _setDefaultEventParametersForInit(\n  customParams?: CustomParams\n): void {\n  defaultEventParametersForInit = customParams;\n}\n", "/**\n * The Firebase Analytics Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerVersion, _registerComponent } from '@firebase/app';\nimport { FirebaseAnalyticsInternal } from '@firebase/analytics-interop-types';\nimport { factory } from './factory';\nimport { ANALYTICS_TYPE } from './constants';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactoryOptions\n} from '@firebase/component';\nimport { ERROR_FACTORY, AnalyticsError } from './errors';\nimport { logEvent } from './api';\nimport { name, version } from '../package.json';\nimport { AnalyticsCallOptions } from './public-types';\nimport '@firebase/installations';\n\ndeclare global {\n  interface Window {\n    [key: string]: unknown;\n  }\n}\n\nfunction registerAnalytics(): void {\n  _registerComponent(\n    new Component(\n      ANALYTICS_TYPE,\n      (container, { options: analyticsOptions }: InstanceFactoryOptions) => {\n        // getImmediate for FirebaseApp will always succeed\n        const app = container.getProvider('app').getImmediate();\n        const installations = container\n          .getProvider('installations-internal')\n          .getImmediate();\n\n        return factory(app, installations, analyticsOptions);\n      },\n      ComponentType.PUBLIC\n    )\n  );\n\n  _registerComponent(\n    new Component('analytics-internal', internalFactory, ComponentType.PRIVATE)\n  );\n\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n\n  function internalFactory(\n    container: ComponentContainer\n  ): FirebaseAnalyticsInternal {\n    try {\n      const analytics = container.getProvider(ANALYTICS_TYPE).getImmediate();\n      return {\n        logEvent: (\n          eventName: string,\n          eventParams?: { [key: string]: unknown },\n          options?: AnalyticsCallOptions\n        ) => logEvent(analytics, eventName, eventParams, options)\n      };\n    } catch (e) {\n      throw ERROR_FACTORY.create(AnalyticsError.INTEROP_COMPONENT_REG_FAILED, {\n        reason: e as Error\n      });\n    }\n  }\n}\n\nregisterAnalytics();\n\nexport * from './api';\nexport * from './public-types';\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Officially recommended event names for gtag.js\n * Any other string is also allowed.\n */\nexport enum EventName {\n  ADD_SHIPPING_INFO = 'add_shipping_info',\n  ADD_PAYMENT_INFO = 'add_payment_info',\n  ADD_TO_CART = 'add_to_cart',\n  ADD_TO_WISHLIST = 'add_to_wishlist',\n  BEGIN_CHECKOUT = 'begin_checkout',\n  /**\n   * @deprecated\n   * This event name is deprecated and is unsupported in updated\n   * Enhanced Ecommerce reports.\n   */\n  CHECKOUT_PROGRESS = 'checkout_progress',\n  EXCEPTION = 'exception',\n  GENERATE_LEAD = 'generate_lead',\n  LOGIN = 'login',\n  PAGE_VIEW = 'page_view',\n  PURCHASE = 'purchase',\n  REFUND = 'refund',\n  REMOVE_FROM_CART = 'remove_from_cart',\n  SCREEN_VIEW = 'screen_view',\n  SEARCH = 'search',\n  SELECT_CONTENT = 'select_content',\n  SELECT_ITEM = 'select_item',\n  SELECT_PROMOTION = 'select_promotion',\n  /** @deprecated */\n  SET_CHECKOUT_OPTION = 'set_checkout_option',\n  SHARE = 'share',\n  SIGN_UP = 'sign_up',\n  TIMING_COMPLETE = 'timing_complete',\n  VIEW_CART = 'view_cart',\n  VIEW_ITEM = 'view_item',\n  VIEW_ITEM_LIST = 'view_item_list',\n  VIEW_PROMOTION = 'view_promotion',\n  VIEW_SEARCH_RESULTS = 'view_search_results'\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AnalyticsCallOptions,\n  CustomParams,\n  EventParams,\n  FirebaseAnalytics\n} from '@firebase/analytics-types';\nimport {\n  Analytics as AnalyticsServiceExp,\n  logEvent as logEventExp,\n  setAnalyticsCollectionEnabled as setAnalyticsCollectionEnabledExp,\n  setCurrentScreen as setCurrentScreenExp,\n  setUserId as setUserIdExp,\n  setUserProperties as setUserPropertiesExp\n} from '@firebase/analytics';\nimport { _FirebaseService, FirebaseApp } from '@firebase/app-compat';\n\nexport class AnalyticsService implements FirebaseAnalytics, _FirebaseService {\n  constructor(\n    public app: FirebaseApp,\n    readonly _delegate: AnalyticsServiceExp\n  ) {}\n\n  logEvent(\n    eventName: string,\n    eventParams?: EventParams | CustomParams,\n    options?: AnalyticsCallOptions\n  ): void {\n    logEventExp(this._delegate, eventName as '', eventParams, options);\n  }\n\n  /**\n   * @deprecated Use {@link logEvent} with `eventName` as 'screen_view' and add relevant `eventParams`.\n   * See {@link https://firebase.google.com/docs/analytics/screenviews | Track Screenviews}.\n   */\n  setCurrentScreen(screenName: string, options?: AnalyticsCallOptions): void {\n    setCurrentScreenExp(this._delegate, screenName, options);\n  }\n\n  setUserId(id: string, options?: AnalyticsCallOptions): void {\n    setUserIdExp(this._delegate, id, options);\n  }\n\n  setUserProperties(\n    properties: CustomParams,\n    options?: AnalyticsCallOptions\n  ): void {\n    setUserPropertiesExp(this._delegate, properties, options);\n  }\n\n  setAnalyticsCollectionEnabled(enabled: boolean): void {\n    setAnalyticsCollectionEnabledExp(this._delegate, enabled);\n  }\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase, {\n  _FirebaseNamespace,\n  FirebaseApp\n} from '@firebase/app-compat';\nimport { FirebaseAnalytics } from '@firebase/analytics-types';\nimport { name, version } from '../package.json';\nimport { AnalyticsService } from './service';\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType,\n  InstanceFactory\n} from '@firebase/component';\nimport {\n  settings as settingsExp,\n  isSupported as isSupportedExp\n} from '@firebase/analytics';\nimport { EventName } from './constants';\n\nconst factory: InstanceFactory<'analytics-compat'> = (\n  container: ComponentContainer\n) => {\n  // Dependencies\n  const app = container.getProvider('app-compat').getImmediate();\n  const analyticsServiceExp = container.getProvider('analytics').getImmediate();\n\n  return new AnalyticsService(app as FirebaseApp, analyticsServiceExp);\n};\n\nexport function registerAnalytics(): void {\n  const namespaceExports = {\n    Analytics: AnalyticsService,\n    settings: settingsExp,\n    isSupported: isSupportedExp,\n    // We removed this enum in exp so need to re-create it here for compat.\n    EventName\n  };\n  (firebase as _FirebaseNamespace).INTERNAL.registerComponent(\n    new Component('analytics-compat', factory, ComponentType.PUBLIC)\n      .setServiceProps(namespaceExports)\n      .setMultipleInstances(true)\n  );\n}\n\nregisterAnalytics();\nfirebase.registerVersion(name, version);\n\n/**\n * Define extension behavior of `registerAnalytics`\n */\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    analytics(app?: FirebaseApp): FirebaseAnalytics;\n  }\n  interface FirebaseApp {\n    analytics(): FirebaseAnalytics;\n  }\n}\n"], "names": ["LogLevel", "levelStringToEnum", "debug", "DEBUG", "verbose", "VERBOSE", "info", "INFO", "warn", "WARN", "error", "ERROR", "silent", "SILENT", "defaultLogLevel", "ConsoleMethod", "defaultLogHandler", "instance", "logType", "args", "logLevel", "now", "Date", "toISOString", "method", "Error", "console", "name", "isBrowserExtension", "runtime", "chrome", "browser", "undefined", "id", "isIndexedDBAvailable", "indexedDB", "e", "validateIndexedDBOpenable", "Promise", "resolve", "reject", "let", "preExist", "DB_CHECK_NAME", "request", "self", "open", "onsuccess", "result", "close", "deleteDatabase", "onupgradeneeded", "onerror", "_a", "message", "areCookiesEnabled", "navigator", "cookieEnabled", "FirebaseError", "constructor", "code", "customData", "super", "this", "Object", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "replace", "PATTERN", "_", "key", "value", "String", "fullMessage", "DEFAULT_INTERVAL_MILLIS", "DEFAULT_BACKOFF_FACTOR", "MAX_VALUE_MILLIS", "RANDOM_FACTOR", "calculateBackoffMillis", "backoffCount", "<PERSON><PERSON><PERSON><PERSON>", "backoffFactor", "currBaseValue", "Math", "pow", "randomWait", "round", "random", "min", "getModularInstance", "_delegate", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "instanceOfAny", "object", "constructors", "some", "c", "idbProxyableTypes", "cursorAdvanceMethods", "cursorRequestMap", "WeakMap", "transactionDoneMap", "transactionStoreNamesMap", "transformCache", "reverseTransformCache", "idbProxyTraps", "get", "target", "prop", "receiver", "IDBTransaction", "objectStoreNames", "objectStore", "wrap", "set", "has", "wrapFunction", "func", "IDBDatabase", "transaction", "IDBCursor", "advance", "continue", "continuePrimaryKey", "includes", "apply", "unwrap", "storeNames", "tx", "call", "sort", "transformCachableValue", "done", "unlisten", "removeEventListener", "complete", "DOMException", "addEventListener", "IDBObjectStore", "IDBIndex", "Proxy", "newValue", "IDBRequest", "promise", "success", "then", "catch", "readMethods", "writeMethods", "cachedMethods", "Map", "getMethod", "targetFuncName", "useIndex", "isWrite", "async", "storeName", "store", "index", "shift", "all", "oldTraps", "PENDING_TIMEOUT_MS", "PACKAGE_VERSION", "version", "INTERNAL_AUTH_VERSION", "INSTALLATIONS_API_URL", "TOKEN_EXPIRATION_BUFFER", "ERROR_FACTORY", "missing-app-config-values", "not-registered", "installation-not-found", "request-failed", "app-offline", "delete-pending-registration", "isServerError", "getInstallationsEndpoint", "projectId", "extractAuthTokenInfoFromResponse", "response", "token", "requestStatus", "expiresIn", "Number", "creationTime", "getErrorFromResponse", "requestName", "errorData", "await", "json", "serverCode", "serverMessage", "serverStatus", "status", "getHeaders", "<PERSON><PERSON><PERSON><PERSON>", "Headers", "Content-Type", "Accept", "x-goog-api-key", "getHeadersWithAuth", "appConfig", "refreshToken", "headers", "append", "retryIfServerError", "fn", "sleep", "ms", "setTimeout", "VALID_FID_PATTERN", "INVALID_FID", "generateFid", "fidByteArray", "Uint8Array", "fid", "crypto", "msCrypto", "getRandomValues", "array", "btoa", "fromCharCode", "substr", "test", "<PERSON><PERSON><PERSON>", "appName", "appId", "fidChangeCallbacks", "fidChanged", "callFidChangeCallbacks", "channel", "broadcastChannel", "BroadcastChannel", "onmessage", "postMessage", "size", "callbacks", "DATABASE_NAME", "DATABASE_VERSION", "OBJECT_STORE_NAME", "db<PERSON><PERSON><PERSON>", "getDbPromise", "blocked", "upgrade", "blocking", "terminated", "openPromise", "event", "oldVersion", "newVersion", "db", "createObjectStore", "oldValue", "put", "remove", "delete", "update", "updateFn", "getInstallationEntry", "installations", "registrationPromise", "installationEntry", "oldEntry", "clearTimedOutRequest", "registrationStatus", "entryWithPromise", "inProgressEntry", "onLine", "registrationTime", "registeredInstallationEntry", "heartbeatServiceProvider", "endpoint", "body", "heartbeatService", "getImmediate", "optional", "heartbeatsHeader", "getHeartbeatsHeader", "authVersion", "sdkVersion", "JSON", "stringify", "fetch", "ok", "responseValue", "authToken", "registrationPromiseWithError", "entry", "updateInstallationRequest", "generateAuthTokenRequest", "getGenerateAuthTokenEndpoint", "installation", "refreshAuthToken", "forceRefresh", "tokenPromise", "isEntryRegistered", "oldAuthToken", "updateAuthTokenRequest", "inProgressAuthToken", "requestTime", "assign", "updatedInstallationEntry", "getToken", "installationsImpl", "getMissingValueError", "valueName", "INSTALLATIONS_NAME", "publicFactory", "app", "container", "get<PERSON><PERSON><PERSON>", "options", "keyName", "_get<PERSON><PERSON><PERSON>", "_delete", "internalFactory", "getId", "_registerComponent", "registerVersion", "ANALYTICS_TYPE", "GA_FID_KEY", "ORIGIN_KEY", "FETCH_TIMEOUT_MILLIS", "DYNAMIC_CONFIG_URL", "GTAG_URL", "logger", "_logLevel", "_log<PERSON><PERSON><PERSON>", "_userLogHandler", "val", "TypeError", "setLogLevel", "log<PERSON><PERSON><PERSON>", "userLogHandler", "log", "already-exists", "already-initialized", "already-initialized-settings", "interop-component-reg-failed", "invalid-analytics-context", "indexeddb-unavailable", "fetch-throttle", "config-fetch-failed", "no-api-key", "no-app-id", "no-client-id", "invalid-gtag-resource", "createGtagTrustedTypesScriptURL", "url", "err", "startsWith", "gtagURL", "promiseAllSettled", "promises", "map", "insertScriptTag", "dataLayerName", "measurementId", "trustedTypesPolicy", "policyName", "policyOptions", "window", "trustedTypes", "createPolicy", "createScriptURL", "script", "document", "createElement", "gtagScriptURL", "src", "head", "append<PERSON><PERSON><PERSON>", "wrapGtag", "gtagCore", "initializationPromisesMap", "dynamicConfigPromisesList", "measurementIdToAppId", "command", "gtagParams", "initializationPromisesToWaitFor", "gaSendToList", "Array", "isArray", "dynamicConfigResults", "sendToId", "foundConfig", "find", "config", "initializationPromise", "push", "length", "values", "correspondingAppId", "consentAction", "fieldName", "customParams", "LONG_RETRY_FACTOR", "defaultRetryData", "throttleMetadata", "getThrottleMetadata", "setThrottleMetadata", "metadata", "deleteThrottleMetadata", "fetchDynamicConfig", "appFields", "appUrl", "errorMessage", "jsonResponse", "_ignored", "httpStatus", "responseMessage", "fetchDynamicConfigWithRetry", "retryData", "timeout<PERSON><PERSON><PERSON>", "throttleEnd<PERSON>imeMill<PERSON>", "signal", "AnalyticsAbortSignal", "abort", "attemptFetchDynamicConfigWithRetry", "setAbortableTimeout", "isRetriableError", "backoff<PERSON><PERSON><PERSON>", "max", "timeout", "clearTimeout", "listeners", "listener", "for<PERSON>ach", "_initializeAnalytics", "dynamicConfigPromise", "fidPromise", "errorInfo", "toString", "envIsValid", "dynamicConfig", "configProperties", "tag", "scriptTags", "getElementsByTagName", "AnalyticsService", "gtagName", "gtagCoreFunction", "wrappedGtagFunction", "globalInitDone", "settings", "factory", "mismatchedEnvMessages", "details", "join", "getOrCreateDataLayer", "dataLayer", "wrappedGtag", "gtagFunctionName", "arguments", "isSupported", "setCurrentScreen", "analyticsInstance", "screenName", "gtagFunction", "global", "screen_name", "setUserId", "user_id", "setUserProperties", "properties", "flatProperties", "keys", "user_properties", "setAnalyticsCollectionEnabled", "enabled", "logEvent", "eventName", "eventParams", "send_to", "analyticsOptions", "analytics", "reason", "EventName", "logEventExp", "setCurrentScreenExp", "setUserIdExp", "setUserPropertiesExp", "setAnalyticsCollectionEnabledExp", "analyticsServiceExp", "namespaceExports", "Analytics", "settingsExp", "isSupportedExp", "firebase", "INTERNAL", "registerComponent"], "mappings": "yaAsDYA,ECkCC,WDlCDA,EAAAA,EAAAA,GAOX,IANCA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,OAAA,GAAA,SAGF,IAAMC,EAA2D,CAC/DC,MAASF,EAASG,MAClBC,QAAWJ,EAASK,QACpBC,KAAQN,EAASO,KACjBC,KAAQR,EAASS,KACjBC,MAASV,EAASW,MAClBC,OAAUZ,EAASa,QAMfC,EAA4Bd,EAASO,KAmBrCQ,EAAgB,EACnBf,EAASG,OAAQ,OACjBH,EAASK,SAAU,OACnBL,EAASO,MAAO,QAChBP,EAASS,MAAO,QAChBT,EAASW,OAAQ,SAQdK,EAAgC,CAACC,EAAUC,KAAYC,KAC3D,GAAID,EAAAA,EAAUD,EAASG,UAAvB,CAGA,IAAMC,GAAM,IAAIC,MAAOC,YAAW,EAC5BC,EAAST,EAAcG,GAC7B,GAAIM,CAAAA,EAMF,MAAM,IAAIC,oEACsDP,IAAU,EAN1EQ,QAAQF,OACFH,OAASJ,EAASU,QACtB,GAAGR,CAAI,CANV,CAaH,WEJgBS,IACd,IAAMC,EACc,UAAlB,OAAOC,OACHA,OAAOD,QACY,UAAnB,OAAOE,QACPA,QAAQF,QACRG,KAAAA,EACN,MAA0B,UAAnB,OAAOH,GAAuCG,KAAAA,IAAfH,EAAQI,EAChD,UAoDgBC,IACd,IACE,MAA4B,UAArB,OAAOC,SAGf,CAFC,MAAOC,IAGX,UASgBC,IACd,OAAO,IAAIC,QAAQ,CAACC,EAASC,KAC3B,IACEC,IAAIC,EAAoB,CAAA,EAClBC,EACJ,0DACIC,EAAUC,KAAKV,UAAUW,KAAKH,CAAa,EACjDC,EAAQG,UAAY,KAClBH,EAAQI,OAAOC,QAEVP,GACHG,KAAKV,UAAUe,eAAeP,CAAa,EAE7CJ,EAAQ,CAAA,CAAI,CACd,EACAK,EAAQO,gBAAkB,KACxBT,EAAW,CAAA,CACb,EAEAE,EAAQQ,QAAU,WAChBZ,GAAO,OAAAa,EAAAT,EAAQlC,OAAK,KAAA,EAAA2C,EAAEC,UAAW,EAAE,CACrC,CAGD,CAFC,MAAO5C,GACP8B,EAAO9B,CAAK,CACb,CACH,CAAC,CACH,UAOgB6C,IACd,MAAI,EAAqB,aAArB,OAAOC,WAA8BA,CAAAA,UAAUC,cAIrD,OC/JaC,UAAsBjC,MAIjCkC,YAEWC,EACTN,EAEOO,GAEPC,MAAMR,CAAO,EALJS,KAAIH,KAAJA,EAGFG,KAAUF,WAAVA,EAPAE,KAAIpC,KAdI,gBA6BfqC,OAAOC,eAAeF,KAAML,EAAcQ,SAAS,EAI/CzC,MAAM0C,mBACR1C,MAAM0C,kBAAkBJ,KAAMK,EAAaF,UAAUG,MAAM,CAE9D,CACF,OAEYD,EAIXT,YACmBW,EACAC,EACAC,GAFAT,KAAOO,QAAPA,EACAP,KAAWQ,YAAXA,EACAR,KAAMS,OAANA,CACf,CAEJH,OACET,KACGa,GAEH,IAcuCA,EAdjCZ,EAAcY,EAAK,IAAoB,GACvCC,EAAcX,KAAKO,QAAR,IAAmBV,EAC9Be,EAAWZ,KAAKS,OAAOZ,GAEvBN,EAAUqB,GAUuBF,EAVcZ,EAAVc,EAW7BC,QAAQC,EAAS,CAACC,EAAGC,KACnC,IAAMC,EAAQP,EAAKM,GACnB,OAAgB,MAATC,EAAgBC,OAAOD,CAAK,MAAQD,KAC7C,CAAC,GAdoE,QAE7DG,EAAiBnB,KAAKQ,iBAAgBjB,MAAYoB,MAIxD,OAFc,IAAIhB,EAAcgB,EAAUQ,EAAarB,CAAU,CAGlE,CACF,CASD,IAAMgB,EAAU,gBCnHVM,EAA0B,IAM1BC,EAAyB,EAOlBC,EAAmB,MAUnBC,EAAgB,GAOvB,SAAUC,EACdC,EACAC,EAAyBN,EACzBO,EAAwBN,GAKxB,IAAMO,EAAgBF,EAAiBG,KAAKC,IAAIH,EAAeF,CAAY,EAIrEM,EAAaF,KAAKG,MAGtBT,EACEK,GAGCC,KAAKI,OAAQ,EAAG,IACjB,CAAC,EAIL,OAAOJ,KAAKK,IAAIZ,EAAkBM,EAAgBG,CAAU,CAC9D,CCtDM,SAAUI,EACd5B,GAEA,OAAIA,GAAYA,EAA+B6B,UACrC7B,EAA+B6B,UAEhC7B,CAEX,OCDa8B,EAiBXzC,YACWhC,EACA0E,EACAC,GAFAvC,KAAIpC,KAAJA,EACAoC,KAAesC,gBAAfA,EACAtC,KAAIuC,KAAJA,EAnBXvC,KAAiBwC,kBAAG,CAAA,EAIpBxC,KAAYyC,aAAe,GAE3BzC,KAAA0C,kBAA2C,OAE3C1C,KAAiB2C,kBAAwC,IAYrD,CAEJC,qBAAqBC,GAEnB,OADA7C,KAAK0C,kBAAoBG,EAClB7C,IACR,CAED8C,qBAAqBN,GAEnB,OADAxC,KAAKwC,kBAAoBA,EAClBxC,IACR,CAED+C,gBAAgBC,GAEd,OADAhD,KAAKyC,aAAeO,EACbhD,IACR,CAEDiD,2BAA2BC,GAEzB,OADAlD,KAAK2C,kBAAoBO,EAClBlD,IACR,CACF,CCtED,IAAMmD,EAAgB,CAACC,EAAQC,IAAiBA,EAAaC,KAAK,GAAOF,aAAkBG,CAAC,EAExFC,EACAC,EAqBJ,IAAMC,GAAmB,IAAIC,QACvBC,EAAqB,IAAID,QACzBE,GAA2B,IAAIF,QAC/BG,EAAiB,IAAIH,QACrBI,EAAwB,IAAIJ,QA0DlCjF,IAAIsF,EAAgB,CAChBC,IAAIC,EAAQC,EAAMC,GACd,GAAIF,aAAkBG,eAAgB,CAElC,GAAa,SAATF,EACA,OAAOP,EAAmBK,IAAIC,CAAM,EAExC,GAAa,qBAATC,EACA,OAAOD,EAAOI,kBAAoBT,GAAyBI,IAAIC,CAAM,EAGzE,GAAa,UAATC,EACA,OAAOC,EAASE,iBAAiB,GAC3BrG,KAAAA,EACAmG,EAASG,YAAYH,EAASE,iBAAiB,EAAE,CAE9D,CAED,OAAOE,EAAKN,EAAOC,EAAK,CAC3B,EACDM,IAAIP,EAAQC,EAAMlD,GAEd,OADAiD,EAAOC,GAAQlD,EACR,CAAA,CACV,EACDyD,IAAIR,EAAQC,GACR,OAAID,aAAkBG,iBACR,SAATF,GAA4B,UAATA,IAGjBA,KAAQD,CAClB,CACL,EAIA,SAASS,GAAaC,GAIlB,OAAIA,IAASC,YAAY1E,UAAU2E,aAC7B,qBAAsBT,eAAelE,WA7GnCsD,EAAAA,GACoB,CACpBsB,UAAU5E,UAAU6E,QACpBD,UAAU5E,UAAU8E,SACpBF,UAAU5E,UAAU+E,qBAqHEC,SAASP,CAAI,EAChC,YAAaxH,GAIhB,OADAwH,EAAKQ,MAAMC,EAAOrF,IAAI,EAAG5C,CAAI,EACtBoH,EAAKd,GAAiBO,IAAIjE,IAAI,CAAC,CAClD,EAEW,YAAa5C,GAGhB,OAAOoH,EAAKI,EAAKQ,MAAMC,EAAOrF,IAAI,EAAG5C,CAAI,CAAC,CAClD,EAvBe,SAAUkI,KAAelI,GAC5B,IAAMmI,EAAKX,EAAKY,KAAKH,EAAOrF,IAAI,EAAGsF,EAAY,GAAGlI,CAAI,EAEtD,OADAyG,GAAyBY,IAAIc,EAAID,EAAWG,KAAOH,EAAWG,KAAM,EAAG,CAACH,EAAW,EAC5Ed,EAAKe,CAAE,CAC1B,CAoBA,CACA,SAASG,GAAuBzE,GAC5B,IA5FoCsE,EAI9BI,EAwFN,MAAqB,YAAjB,OAAO1E,EACA0D,GAAa1D,CAAK,GAGzBA,aAAiBoD,iBAhGekB,EAiGDtE,EA/F/B2C,EAAmBc,IAAIa,CAAE,IAEvBI,EAAO,IAAIpH,QAAQ,CAACC,EAASC,KAC/B,IAAMmH,EAAW,KACbL,EAAGM,oBAAoB,WAAYC,CAAQ,EAC3CP,EAAGM,oBAAoB,QAASlJ,CAAK,EACrC4I,EAAGM,oBAAoB,QAASlJ,CAAK,CACjD,EACcmJ,EAAW,KACbtH,IACAoH,GACZ,EACcjJ,EAAQ,KACV8B,EAAO8G,EAAG5I,OAAS,IAAIoJ,aAAa,aAAc,YAAY,CAAC,EAC/DH,GACZ,EACQL,EAAGS,iBAAiB,WAAYF,CAAQ,EACxCP,EAAGS,iBAAiB,QAASrJ,CAAK,EAClC4I,EAAGS,iBAAiB,QAASrJ,CAAK,CAC1C,CAAK,EAEDiH,EAAmBa,IAAIc,EAAII,CAAI,IA2E3BxC,EAAclC,EAzJVuC,EAAAA,GACiB,CACjBqB,YACAoB,eACAC,SACAnB,UACAV,eAmJuC,EACpC,IAAI8B,MAAMlF,EAAO+C,CAAa,EAElC/C,EACX,CACA,SAASuD,EAAKvD,GAGV,IA1IsBpC,EAgJhBuH,EANN,OAAInF,aAAiBoF,YA1ICxH,EA2IMoC,GA1ItBqF,EAAU,IAAI/H,QAAQ,CAACC,EAASC,KAClC,IAAMmH,EAAW,KACb/G,EAAQgH,oBAAoB,UAAWU,CAAO,EAC9C1H,EAAQgH,oBAAoB,QAASlJ,CAAK,CACtD,EACc4J,EAAU,KACZ/H,EAAQgG,EAAK3F,EAAQI,MAAM,CAAC,EAC5B2G,GACZ,EACcjJ,EAAQ,KACV8B,EAAOI,EAAQlC,KAAK,EACpBiJ,GACZ,EACQ/G,EAAQmH,iBAAiB,UAAWO,CAAO,EAC3C1H,EAAQmH,iBAAiB,QAASrJ,CAAK,CAC/C,CAAK,GAEI6J,KAAK,IAGFvF,aAAiB8D,WACjBrB,GAAiBe,IAAIxD,EAAOpC,CAAO,CAG/C,CAAK,EACI4H,MAAM,MAAS,EAGpB1C,EAAsBU,IAAI6B,EAASzH,CAAO,EACnCyH,GAgHHxC,EAAeY,IAAIzD,CAAK,EACjB6C,EAAeG,IAAIhD,CAAK,IAC7BmF,EAAWV,GAAuBzE,CAAK,KAG5BA,IACb6C,EAAeW,IAAIxD,EAAOmF,CAAQ,EAClCrC,EAAsBU,IAAI2B,EAAUnF,CAAK,GAEtCmF,EACX,CACA,IAAMf,EAAS,GAAWtB,EAAsBE,IAAIhD,CAAK,ENrIzD,IAAMyF,GAAc,CAAC,MAAO,SAAU,SAAU,aAAc,SACxDC,GAAe,CAAC,MAAO,MAAO,SAAU,SACxCC,EAAgB,IAAIC,IAC1B,SAASC,GAAU5C,EAAQC,GACvB,GAAMD,aAAkBW,aAClB,EAAAV,KAAQD,IACM,UAAhB,OAAOC,EAFX,CAKA,GAAIyC,EAAc3C,IAAIE,CAAI,EACtB,OAAOyC,EAAc3C,IAAIE,CAAI,EACjC,IAAM4C,EAAiB5C,EAAKtD,QAAQ,aAAc,EAAE,EAC9CmG,EAAW7C,IAAS4C,EACpBE,EAAUN,GAAaxB,SAAS4B,CAAc,EACpD,IAMMtJ,EANN,OAEEsJ,KAAmBC,EAAWd,SAAWD,gBAAgB9F,YACrD8G,GAAWP,GAAYvB,SAAS4B,CAAc,IAG9CtJ,EAASyJ,eAAgBC,KAAc/J,GAEzC,IAAMmI,EAAKvF,KAAK8E,YAAYqC,EAAWF,EAAU,YAAc,UAAU,EACzEvI,IAAIwF,EAASqB,EAAG6B,MAQhB,OAPIJ,IACA9C,EAASA,EAAOmD,MAAMjK,EAAKkK,MAAO,CAAA,IAM/B,MAAO/I,QAAQgJ,IAAI,CACtBrD,EAAO6C,GAAgB,GAAG3J,CAAI,EAC9B6J,GAAW1B,EAAGI,KACjB,GAAG,EACZ,EACIiB,EAAcnC,IAAIN,EAAM1G,CAAM,EACvBA,GAvBP,KAAA,CANC,CA8BL,CMgCIuG,EN/BwB,CACxB,GADS,EM+BgBA,EN7BzBC,IAAK,CAACC,EAAQC,EAAMC,IAAa0C,GAAU5C,EAAQC,CAAI,GAAKqD,EAASvD,IAAIC,EAAQC,EAAMC,CAAQ,EAC/FM,IAAK,CAACR,EAAQC,IAAS,CAAC,CAAC2C,GAAU5C,EAAQC,CAAI,GAAKqD,EAAS9C,IAAIR,EAAQC,CAAI,CAChF,6COzEM,IAAMsD,GAAqB,IAErBC,GAAkB,KAAKC,EACvBC,GAAwB,SAExBC,GACX,kDAEWC,GAA0B,KCwBhC,IAAMC,EAAgB,IAAI1H,EDtBV,gBACK,gBCD2C,CACrE2H,4BACE,kDACFC,iBAA4B,2CAC5BC,yBAAoC,mCACpCC,iBACE,6FACFC,cAAyB,kDACzBC,8BACE,2EAgBmB,EAYjB,SAAUC,GAAc3L,GAC5B,OACEA,aAAiBgD,GACjBhD,EAAMkD,KAAKsF,SAAQ,iBAEvB,CCxCgB,SAAAoD,GAAyB,CAAEC,UAAAA,IACzC,OAAUX,gBAAkCW,iBAC9C,CAEM,SAAUC,GACdC,GAEA,MAAO,CACLC,MAAOD,EAASC,MAChBC,cAAsC,EACtCC,UAgEKC,OAhEwCJ,EAASG,UAgExBhI,QAAQ,IAAK,KAAK,CAAC,EA/DjDkI,aAAcxL,KAAKD,IAAK,EAE5B,CAEO4J,eAAe8B,GACpBC,EACAP,GAEA,IACMQ,GAD8BC,MAAMT,EAASU,QACpBzM,MAC/B,OAAOoL,EAAczH,OAAiC,iBAAA,CACpD2I,YAAAA,EACAI,WAAYH,EAAUrJ,KACtByJ,cAAeJ,EAAU3J,QACzBgK,aAAcL,EAAUM,MACzB,CAAA,CACH,CAEgB,SAAAC,GAAW,CAAEC,OAAAA,IAC3B,OAAO,IAAIC,QAAQ,CACjBC,eAAgB,mBAChBC,OAAQ,mBACRC,iBAAkBJ,CACnB,CAAA,CACH,CAEgB,SAAAK,GACdC,EACA,CAAEC,aAAAA,IAEF,IAAMC,EAAUT,GAAWO,CAAS,EAEpC,OADAE,EAAQC,OAAO,iBAmCeF,EAnCyBA,EAoC7CrC,GAAH,IAA4BqC,EApCiC,EAC7DC,CACT,CAeOhD,eAAekD,GACpBC,GAEA,IAAMpL,EAASkK,MAAMkB,IAErB,OAAqB,KAAjBpL,EAAOuK,QAAiBvK,EAAOuK,OAAS,IAEnCa,EAAE,EAGJpL,CACT,CCnFM,SAAUqL,GAAMC,GACpB,OAAO,IAAIhM,QAAcC,IACvBgM,WAAWhM,EAAS+L,CAAE,CACxB,CAAC,CACH,CCHO,IAAME,GAAoB,oBACpBC,EAAc,GAMX,SAAAC,KACd,IAGE,IAAMC,EAAe,IAAIC,WAAW,EAAE,EAQhCC,IANJhM,KAAKiM,QAAWjM,KAAyCkM,UACpDC,gBAAgBL,CAAY,EAGnCA,EAAa,GAAK,IAAcA,EAAa,GAAK,ICnBhBM,GACxBC,KAAKjK,OAAOkK,aAAa,GAAGF,CAAK,CAAC,EACnCrK,QAAQ,MAAO,GAAG,EAAEA,QAAQ,MAAO,GAAG,GDmB5B+J,CAW+B,EAInCS,OAAO,EAAG,EAAE,GAb3B,OAAOZ,GAAkBa,KAAKR,CAAG,EAAIA,EAAMJ,CAI5C,CAHC,MAAApL,GAEA,OAAOoL,CACR,CACH,CEzBM,SAAUa,EAAOvB,GACrB,OAAUA,EAAUwB,QAAb,IAAwBxB,EAAUyB,KAC3C,CCDA,IAAMC,GAA2D,IAAI7E,IAMrD,SAAA8E,GAAW3B,EAAsBc,GAC/C,IAAM9J,EAAMuK,EAAOvB,CAAS,EAwDFhJ,GAtD1B4K,GAAuB5K,EAAK8J,CAAG,EACZ9J,GAsDb6K,GASR,KACM,CAACC,GAAoB,qBAAsBhN,QAC7CgN,EAAmB,IAAIC,iBAAiB,uBAAuB,GAC9CC,UAAY3N,IAC3BuN,GAAuBvN,EAAEqC,KAAKM,IAAK3C,EAAEqC,KAAKoK,GAAG,CAC/C,GAEKgB,MAfHD,GACFA,EAAQI,YAAY,CAAEjL,IAAAA,EAAK8J,IAAAA,CAAK,CAAA,EAkBF,IAA5BY,GAAmBQ,MAAcJ,IACnCA,EAAiB5M,MAAK,EACtB4M,EAAmB,KA3EvB,CAyCA,SAASF,GAAuB5K,EAAa8J,GAC3C,IAAMqB,EAAYT,GAAmBzH,IAAIjD,CAAG,EAC5C,GAAKmL,EAIL,IAAK,IAAMjJ,KAAYiJ,EACrBjJ,EAAS4H,CAAG,CAEhB,CAUApM,IAAIoN,EAA4C,KCrEhD,IAAMM,GAAgB,kCAChBC,GAAmB,EACnBC,EAAoB,+BAStBC,GAA2D,KAC/D,SAASC,IAgBP,OAfKD,GAAAA,Kf1BP,CAAgB3O,EAAM+J,EAAS,CAAE8E,QAAAA,EAASC,QAAAA,EAASC,SAAAA,EAAUC,WAAAA,CAAY,KACrE,IAAM/N,EAAUT,UAAUW,KAAKnB,EAAM+J,CAAO,EAC5C,IAAMkF,EAAcrI,EAAK3F,CAAO,EAoBhC,OAnBI6N,GACA7N,EAAQmH,iBAAiB,gBAAiB,IACtC0G,EAAQlI,EAAK3F,EAAQI,MAAM,EAAG6N,EAAMC,WAAYD,EAAME,WAAYxI,EAAK3F,EAAQiG,WAAW,EAAGgI,CAAK,CAC9G,CAAS,EAEDL,GACA5N,EAAQmH,iBAAiB,UAAW,GAAWyG,EAE/CK,EAAMC,WAAYD,EAAME,WAAYF,CAAK,CAAC,EAE9CD,EACKrG,KAAK,IACFoG,GACAK,EAAGjH,iBAAiB,QAAS,IAAM4G,EAAY,CAAA,EAC/CD,GACAM,EAAGjH,iBAAiB,gBAAiB,GAAW2G,EAASG,EAAMC,WAAYD,EAAME,WAAYF,CAAK,CAAC,CAE/G,CAAK,EACIrG,MAAM,MAAS,EACboG,CACX,GeIuBT,GAAeC,GAAkB,CAClDK,QAAS,CAACO,EAAIF,KAOL,IADCA,GAEJE,EAAGC,kBAAkBZ,CAAiB,CAE3C,CACF,CAAA,CAGL,CAeOpF,eAAezC,EACpBuF,EACA/I,GAEA,IAAMD,EAAMuK,EAAOvB,CAAS,EAEtBzE,GADK4D,MAAMqD,KACH1H,YAAYwH,EAAmB,WAAW,EAClD/H,EAAcgB,EAAGhB,YAAY+H,CAAiB,EAC9Ca,EAAQ,MAAU5I,EAAYN,IAAIjD,CAAG,EAQ3C,OAPAmI,MAAM5E,EAAY6I,IAAInM,EAAOD,CAAG,EAChCmI,MAAM5D,EAAGI,KAEJwH,GAAYA,EAASrC,MAAQ7J,EAAM6J,KACtCa,GAAW3B,EAAW/I,EAAM6J,GAAG,EAG1B7J,CACT,CAGOiG,eAAemG,GAAOrD,GAC3B,IAAMhJ,EAAMuK,EAAOvB,CAAS,EAEtBzE,GADK4D,MAAMqD,KACH1H,YAAYwH,EAAmB,WAAW,EACxDnD,MAAM5D,EAAGhB,YAAY+H,CAAiB,EAAEgB,OAAOtM,CAAG,EAClDmI,MAAM5D,EAAGI,IACX,CAQOuB,eAAeqG,EACpBvD,EACAwD,GAEA,IAAMxM,EAAMuK,EAAOvB,CAAS,EAEtBzE,GADK4D,MAAMqD,KACH1H,YAAYwH,EAAmB,WAAW,EAClDlF,EAAQ7B,EAAGhB,YAAY+H,CAAiB,EACxCa,EAAQ,MAAyC/F,EAAMnD,IAC3DjD,CAAG,EAECoF,EAAWoH,EAASL,CAAQ,EAalC,OAXiBlP,KAAAA,IAAbmI,EACF+C,MAAM/B,EAAMkG,OAAOtM,CAAG,EAEtBmI,MAAM/B,EAAMgG,IAAIhH,EAAUpF,CAAG,EAE/BmI,MAAM5D,EAAGI,KAELS,CAAAA,GAAc+G,GAAYA,EAASrC,MAAQ1E,EAAS0E,KACtDa,GAAW3B,EAAW5D,EAAS0E,GAAG,EAG7B1E,CACT,CClFOc,eAAeuG,EACpBC,GAEAhP,IAAIiP,EAEJ,IAAMC,EAAoBzE,MAAMoE,EAAOG,EAAc1D,UAAW6D,IAC9D,IAAMD,EAgCDE,GAhCqDD,GA2Bf,CAC3C/C,IAAKH,GAAa,EAClBoD,mBAA6C,EAGd,EA/BzBC,GAyCV,CACEN,EACAE,KAEA,IAaQK,EAKAN,EAlBR,OAAwC,IAApCC,EAAkBG,mBACftO,UAAUyO,QAYTD,EAA+C,CACnDnD,IAAK8C,EAAkB9C,IACvBiD,mBAA6C,EAC7CI,iBAAkB5Q,KAAKD,IAAK,GAExBqQ,GAkBVzG,MACEwG,EACAE,KAEA,IACE,IAAMQ,EAA8BjF,MCxGjCjC,MACL,CAAE8C,UAAAA,EAAWqE,yBAAAA,CAAwB,EACrC,CAAEvD,IAAAA,CAAG,KAEL,IAAMwD,EAAW/F,GAAyByB,CAAS,EAEnD,IAAME,EAAUT,GAAWO,CAAS,EAa9BuE,IAPFC,EAHqBH,EAAyBI,aAAa,CAC7DC,SAAU,CAAA,CACX,CAAA,KAEOC,EAAmBxF,MAAMqF,EAAiBI,wBAE9C1E,EAAQC,OAAO,oBAAqBwE,CAAgB,EAI3C,CACX7D,IAAAA,EACA+D,YAAajH,GACb6D,MAAOzB,EAAUyB,MACjBqD,WAAYpH,KAGd,IAAM7I,EAAuB,CAC3BpB,OAAQ,OACRyM,QAAAA,EACAqE,KAAMQ,KAAKC,UAAUT,CAAI,GAI3B,IADM7F,EAAWS,MAAMiB,GAAmB,IAAM6E,MAAMX,EAAUzP,CAAO,CAAC,GAC3DqQ,GAQX,MANiE,CAC/DpE,KAFIqE,EAA4ChG,MAAMT,EAASU,QAE5C0B,KAAOA,EAC1BiD,mBAA2C,EAC3C9D,aAAckF,EAAclF,aAC5BmF,UAAW3G,GAAiC0G,EAAcC,SAAS,GAIrE,MAAMjG,MAAMH,GAAqB,sBAAuBN,CAAQ,CAEpE,GD4DMgF,EACAE,CAAiB,EAEnB,OAAOnJ,EAAIiJ,EAAc1D,UAAWoE,CAA2B,CAchE,CAbC,MAAO/P,GAYP,MAXIiK,GAAcjK,CAAC,GAAiC,MAA5BA,EAAEyB,WAAWuJ,WAGnCF,MAAMkE,GAAOK,EAAc1D,SAAS,EAGpCb,MAAM1E,EAAIiJ,EAAc1D,UAAW,CACjCc,IAAK8C,EAAkB9C,IACvBiD,mBAA6C,CAC9C,CAAA,EAEG1P,CACP,CACH,GAzCMqP,EACAO,CAAe,EAEV,CAAEL,kBAAmBK,EAAiBN,oBAAAA,KAnBrC0B,EAA+B9Q,QAAQE,OAC3CsJ,EAAczH,OAA6B,aAAA,CAAA,EAEtC,CACLsN,kBAAAA,EACAD,oBAAqB0B,IAgBW,IAApCzB,EAAkBG,mBAEX,CACLH,kBAAAA,EACAD,qBAmCNzG,MACEwG,IAMAhP,IAAI4Q,EAA2BnG,MAAMoG,GACnC7B,EAAc1D,SAAS,EAEzB,KAA+B,IAAxBsF,EAAMvB,oBAEX5E,MAAMmB,GAAM,GAAG,EAEfgF,EAAQnG,MAAMoG,GAA0B7B,EAAc1D,SAAS,EAGjE,IAEU4D,EAAmBD,EAF7B,OAA4B,IAAxB2B,EAAMvB,mBAaHuB,GAXC,CAAE1B,kBAAAA,EAAmBD,oBAAAA,CAAmB,EAC5CxE,MAAMsE,EAAqBC,CAAa,EAEtCC,GAIKC,EAKb,GAlEoDF,CAAa,GAGtD,CAAEE,kBAAAA,CAAiB,CAE9B,GA7EMF,EACAE,CAAiB,EAGnB,OADAD,EAAsBK,EAAiBL,oBAChCK,EAAiBJ,iBAC1B,CAAC,EAED,OAAIA,EAAkB9C,MAAQJ,EAErB,CAAEkD,kBAAmBzE,MAAMwE,GAG7B,CACLC,kBAAAA,EACAD,oBAAAA,EAEJ,CAoIA,SAAS4B,GACPvF,GAEA,OAAOuD,EAAOvD,EAAW6D,IACvB,GAAKA,EAGL,OAAOC,GAAqBD,CAAQ,EAFlC,MAAM9F,EAAczH,OAAM,yBAG9B,CAAC,CACH,CAEA,SAASwN,GAAqBwB,GAC5B,IAWA1B,EAXA,OAcoE,KAHpEA,EAXmC0B,GAcfvB,oBAClBH,EAAkBO,iBAAmB1G,GAAqBlK,KAAKD,IAAG,EAd3D,CACLwN,IAAKwE,EAAMxE,IACXiD,mBAA6C,GAI1CuB,CACT,CEzLOpI,eAAesI,GACpB,CAAExF,UAAAA,EAAWqE,yBAAAA,CAAwB,EACrCT,GAEiB6B,CAwCjBzF,EACEc,GAzCe2E,CAA6BzF,EAAW4D,OAAzD,IAAMU,EA2CI/F,GAAyByB,CAAS,MAAKc,wBAJnD,IACEd,EACEc,EAvCIZ,EAAUH,GAAmBC,EAAW4D,CAAiB,EAGzDY,EAAmBH,EAAyBI,aAAa,CAC7DC,SAAU,CAAA,CACX,CAAA,EAQKH,GAPFC,IACIG,EAAmBxF,MAAMqF,EAAiBI,wBAE9C1E,EAAQC,OAAO,oBAAqBwE,CAAgB,EAI3C,CACXe,aAAc,CACZZ,WAAYpH,GACZ+D,MAAOzB,EAAUyB,KAClB,IAGH,IAAM5M,EAAuB,CAC3BpB,OAAQ,OACRyM,QAAAA,EACAqE,KAAMQ,KAAKC,UAAUT,CAAI,GAGrB7F,EAAWS,MAAMiB,GAAmB,IAAM6E,MAAMX,EAAUzP,CAAO,CAAC,EACxE,GAAI6J,EAASwG,GAIX,OADEzG,GAF+CU,MAAMT,EAASU,MAEhB,EAGhD,MAAMD,MAAMH,GAAqB,sBAAuBN,CAAQ,CAEpE,CCnCOxB,eAAeyI,EACpBjC,EACAkC,EAAe,CAAA,GAEflR,IAAImR,EACJ,IAAMP,EAAQnG,MAAMoE,EAAOG,EAAc1D,UAAW6D,IAClD,GAAI,CAACiC,GAAkBjC,CAAQ,EAC7B,MAAM9F,EAAczH,OAAM,kBAG5B,IAgIsB8O,EAhIhBW,EAAelC,EAASuB,UAC9B,GAAKQ,GAiI8C,KAF7BR,EA/HgBW,GAiI5BnH,gBAKcwG,IAC1B,IAAM9R,EAAMC,KAAKD,MACjB,OACEA,EAAM8R,EAAUrG,cAChBqG,EAAUrG,aAAeqG,EAAUvG,UAAYvL,EAAMwK,EAEzD,GAVwBsH,CAAS,EA/HtB,CAAA,GAA8B,IAA1BW,EAAanH,cAGtB,OADAiH,GA0BN3I,MACEwG,EACAkC,KAMAlR,IAAI4Q,EAAQnG,MAAM6G,GAAuBtC,EAAc1D,SAAS,EAChE,KAAoC,IAA7BsF,EAAMF,UAAUxG,eAErBO,MAAMmB,GAAM,GAAG,EAEfgF,EAAQnG,MAAM6G,GAAuBtC,EAAc1D,SAAS,EAG9D,IAAMoF,EAAYE,EAAMF,UACxB,OAA2B,IAAvBA,EAAUxG,cAEL+G,EAAiBjC,EAAekC,CAAY,EAE5CR,CAEX,GAjD+C1B,EAAekC,CAAY,EAC7D/B,EAGP,GAAKpO,UAAUyO,OAMf,OAiIJL,EAnIgEA,EAqI1DoC,EAA2C,CAC/CrH,cAAwC,EACxCsH,YAAa3S,KAAKD,IAAK,GAvIf2Q,EAyIVhO,OAAAkQ,OAAAlQ,OAAAkQ,OAAA,GACKtC,CAAQ,EAAA,CACXuB,UAAWa,CAAmB,CAC9B,EA3IEJ,GAsEN3I,MACEwG,EACAE,KAEA,IACE,IAAMwB,EAAYjG,MAAMqG,GACtB9B,EACAE,CAAiB,EAEbwC,EACDnQ,OAAAkQ,OAAAlQ,OAAAkQ,OAAA,GAAAvC,CAAiB,EACpB,CAAAwB,UAAAA,CAAS,GAGX,OADAjG,MAAM1E,EAAIiJ,EAAc1D,UAAWoG,CAAwB,EACpDhB,CAiBR,CAhBC,MAAO/Q,GACP,IAQQ+R,EAMR,KAbE9H,CAAAA,GAAcjK,CAAC,GACc,MAA5BA,EAAEyB,WAAWuJ,YAAkD,MAA5BhL,EAAEyB,WAAWuJ,YAM3C+G,EACDnQ,OAAAkQ,OAAAlQ,OAAAkQ,OAAA,GAAAvC,CAAiB,EACpB,CAAAwB,UAAW,CAAExG,cAAa,CAAA,CAA6B,CAAA,EAEzDO,MAAM1E,EAAIiJ,EAAc1D,UAAWoG,CAAwB,GAN3DjH,MAAMkE,GAAOK,EAAc1D,SAAS,EAQhC3L,CACP,CACH,GAtG8CqP,EAAeO,CAAe,EAC/DA,EALL,MAAMlG,EAAczH,OAAM,cAM7B,CAdC,OAAOuN,CAeX,CAAC,EAKD,OAHkBgC,EACd1G,MAAM0G,EACLP,EAAMF,SAEb,CAyCA,SAASY,GACPhG,GAEA,OAAOuD,EAAOvD,EAAW6D,IACvB,IAIMkC,EAoF2BX,EAxFjC,GAAKU,GAAkBjC,CAAQ,EAK/B,OADMkC,EAAelC,EAASuB,UAsFuB,KAFpBA,EAnFDW,GAqFtBnH,eACVwG,EAAUc,YAAczI,GAAqBlK,KAAKD,IAAG,EApF9C2C,OAAAkQ,OAAAlQ,OAAAkQ,OAAA,GAAAtC,CAAQ,EACX,CAAAuB,UAAW,CAAExG,cAAa,CAAA,CAC1B,CAAA,EAGGiF,EAXL,MAAM9F,EAAczH,OAAM,iBAY9B,CAAC,CACH,CAoCA,SAASwP,GACPlC,GAEA,OACwB3P,KAAAA,IAAtB2P,GACgE,IAAhEA,EAAkBG,kBAEtB,CCnJO7G,eAAemJ,GACpB3C,EACAkC,EAAe,CAAA,GAEf,IAAMU,EAAoB5C,EAKpB0B,GAJNjG,MAaIwE,EAFIA,GAAwBxE,MAAMsE,EAXC6C,CAWiC,GAA3C,sBAI3BnH,CAAAA,MAAMwE,GAXUxE,MAAMwG,EAAiBW,EAAmBV,CAAY,GACxE,OAAOR,EAAUzG,KACnB,CCWA,SAAS4H,EAAqBC,GAC5B,OAAOzI,EAAczH,OAA4C,4BAAA,CAC/DkQ,UAAAA,CACD,CAAA,CACH,CC3BA,IAAMC,GAAqB,gBAGrBC,GAAkD,IAGtD,IAAMC,EAAMC,EAAUC,YAAY,KAAK,EAAEpC,aAAY,EAWrD,MANqD,KACnDkC,EACA3G,WDpB6B2G,IAC/B,GAAI,CAACA,GAAO,CAACA,EAAIG,QACf,MAAMP,EAAqB,mBAAmB,EAGhD,GAAI,CAACI,EAAI/S,KACP,MAAM2S,EAAqB,UAAU,EAIvC,IAMWQ,EAAX,IAAWA,IANsC,CAC/C,YACA,SACA,SAIA,GAAI,CAACJ,EAAIG,QAAQC,GACf,MAAMR,EAAqBQ,CAAO,EAItC,MAAO,CACLvF,QAASmF,EAAI/S,KACb4K,UAAWmI,EAAIG,QAAQtI,UACvBkB,OAAQiH,EAAIG,QAAQpH,OACpB+B,MAAOkF,EAAIG,QAAQrF,MAEvB,GCbqCkF,CAAG,EAMpCtC,yBAL+B2C,GAAAA,aAAaL,EAAK,WAAW,EAM5DM,QAAS,IAAM1S,QAAQC,QAAS,EAGpC,EAEM0S,GAA6D,IAGjE,IAAMP,EAAMC,EAAUC,YAAY,KAAK,EAAEpC,aAAY,EAErD,IAAMf,EAAgBsD,GAAAA,aAAaL,EAAKF,EAAkB,EAAEhC,aAAY,EAMxE,MAJ8D,CAC5D0C,MAAO,KC5BJjK,MAAqBwG,IAC1B,IAAM4C,EAAoB5C,EACpB,CAAEE,kBAAAA,EAAmBD,oBAAAA,CAAmB,EAAKxE,MAAMsE,EACvD6C,CAAiB,EAWnB,OARI3C,GAKFgC,EAAiBW,CAAiB,GAJd7J,MAAM9I,QAAQhB,KAAK,EAOlCiR,EAAkB9C,GAC3B,GDauB4C,CAAa,EAChC2C,SAAU,GAA4BA,GAAS3C,EAAekC,CAAY,EAG9E,EAGEwB,GAAkBA,mBAChB,IAAI/O,EAAUoO,GAAoBC,GAAoC,QAAA,CAAA,EAExEU,GAAkBA,mBAChB,IAAI/O,EAtC4B,yBAwC9B6O,GAED,SAAA,CAAA,EExCLG,GAAAA,gBAAgBzT,EAAM+J,CAAO,EAE7B0J,GAAAA,gBAAgBzT,EAAM+J,EAAS,SAAkB,ECd1C,IAAM2J,GAAiB,YAGjBC,GAAa,cACbC,GAAa,SAEbC,GAAuB,IAEvBC,GACX,6EAEWC,EAAW,2CCZXC,EAAS,U3BiHpBhS,YAAmBhC,GAAAoC,KAAIpC,KAAJA,EAUXoC,KAAS6R,UAAG9U,EAsBZiD,KAAW8R,YAAe7U,EAc1B+C,KAAe+R,gBAAsB,IAzC5C,CAOD1U,eACE,OAAO2C,KAAK6R,SACb,CAEDxU,aAAa2U,GACX,GAAI,EAAEA,KAAO/V,GACX,MAAM,IAAIgW,4BAA4BD,6BAA+B,EAEvEhS,KAAK6R,UAAYG,CAClB,CAGDE,YAAYF,GACVhS,KAAK6R,UAA2B,UAAf,OAAOG,EAAmB9V,EAAkB8V,GAAOA,CACrE,CAODG,iBACE,OAAOnS,KAAK8R,WACb,CACDK,eAAeH,GACb,GAAmB,YAAf,OAAOA,EACT,MAAM,IAAIC,UAAU,mDAAmD,EAEzEjS,KAAK8R,YAAcE,CACpB,CAMDI,qBACE,OAAOpS,KAAK+R,eACb,CACDK,mBAAmBJ,GACjBhS,KAAK+R,gBAAkBC,CACxB,CAMD7V,SAASiB,GACP4C,KAAK+R,iBAAmB/R,KAAK+R,gBAAgB/R,KAAM/D,EAASG,MAAO,GAAGgB,CAAI,EAC1E4C,KAAK8R,YAAY9R,KAAM/D,EAASG,MAAO,GAAGgB,CAAI,CAC/C,CACDiV,OAAOjV,GACL4C,KAAK+R,iBACH/R,KAAK+R,gBAAgB/R,KAAM/D,EAASK,QAAS,GAAGc,CAAI,EACtD4C,KAAK8R,YAAY9R,KAAM/D,EAASK,QAAS,GAAGc,CAAI,CACjD,CACDb,QAAQa,GACN4C,KAAK+R,iBAAmB/R,KAAK+R,gBAAgB/R,KAAM/D,EAASO,KAAM,GAAGY,CAAI,EACzE4C,KAAK8R,YAAY9R,KAAM/D,EAASO,KAAM,GAAGY,CAAI,CAC9C,CACDX,QAAQW,GACN4C,KAAK+R,iBAAmB/R,KAAK+R,gBAAgB/R,KAAM/D,EAASS,KAAM,GAAGU,CAAI,EACzE4C,KAAK8R,YAAY9R,KAAM/D,EAASS,KAAM,GAAGU,CAAI,CAC9C,CACDT,SAASS,GACP4C,KAAK+R,iBAAmB/R,KAAK+R,gBAAgB/R,KAAM/D,EAASW,MAAO,GAAGQ,CAAI,EAC1E4C,KAAK8R,YAAY9R,KAAM/D,EAASW,MAAO,GAAGQ,CAAI,CAC/C,CACF,E2BhMgC,qBAAqB,ECoEzC2K,EAAgB,IAAI1H,EAC/B,YACA,YAvDuC,CACvCiS,iBACE,0IAGFC,sBACE,mRAIFC,+BACE,iJAGFC,+BACE,wEACFC,4BACE,oMAGFC,wBACE,oMAGFC,iBACE,yKAEFC,sBACE,kEACFC,aACE,8HAEFC,YACE,4HAEFC,eAA+B,kCAC/BC,wBACE,+DAmBI,ECxDF,SAAUC,GAAgCC,GAC9C,IACQC,EADR,OAAKD,EAAIE,WAAW1B,CAAQ,EAOrBwB,GANCC,EAAMrL,EAAczH,OAA6C,wBAAA,CACrEgT,QAASH,CACV,CAAA,EACDvB,EAAOnV,KAAK2W,EAAI7T,OAAO,EAChB,GAGX,CAQM,SAAUgU,GACdC,GAEA,OAAOjV,QAAQgJ,IAAIiM,EAASC,IAAInN,GAAWA,EAAQG,MAAMpI,GAAKA,CAAC,CAAC,CAAC,CACnE,CA6BgB,SAAAqV,GACdC,EACAC,GAEA,IAAMC,GAxBQ,CACdC,EACAC,KAIArV,IAAImV,EAOJ,OALEA,EADEG,OAAOC,aACYD,OAAOC,aAAaC,aACvCJ,EACAC,CAAa,EAGVF,CACT,GAWI,yBACA,CACEM,gBAAiBjB,EAClB,CAAA,EAGGkB,EAASC,SAASC,cAAc,QAAQ,EAIxCC,EAAmB5C,QAAcgC,QAAoBC,EAC1DQ,EAAOI,IAAoCX,EACvCA,MAAAA,EAAkB,KAAA,EAAlBA,EAA0CM,gBAAgBI,CAAa,EACxEA,EAEJH,EAAOlN,MAAQ,CAAA,EACfmN,SAASI,KAAKC,YAAYN,CAAM,CAClC,CAkJA,SAASO,GACPC,EAKAC,EAKAC,EAQAC,GA8DA,OAtDA7N,eACE8N,KACG5X,GAEH,IAEE,GAAiC,UAA7B4X,EAA+B,CACjC,GAAM,CAACpB,EAAeqB,GAAc7X,EA1G1CwX,EA6GQA,EA5GRC,EA6GQA,EA5GRC,EA6GQA,EA1GRlB,EA2GQA,EA1GRqB,EA2GQA,EAzGR,IACEvW,IAAIwW,EAA0D,GAI9D,GAAID,GAAcA,EAAoB,QAAG,CACvCvW,IAAIyW,EAAkCF,EAAoB,QAErDG,MAAMC,QAAQF,CAAY,IAC7BA,EAAe,CAACA,IAIlB,IAAMG,EAAuBnM,MAAMoK,GACjCuB,CAAyB,EAE3B,IAAK,IAAMS,KAAYJ,EAAc,CAEnC,IAAMK,EAAcF,EAAqBG,KACvCC,GAAUA,EAAO9B,gBAAkB2B,CAAQ,EAEvCI,EACJH,GAAeX,EAA0BW,EAAY/J,OACvD,GAAIkK,CAAAA,EAEG,CAILT,EAAkC,GAClC,KACD,CAPCA,EAAgCU,KAAKD,CAAqB,CAQ7D,CACF,CAK8C,IAA3CT,EAAgCW,SAElCX,EAAkCjV,OAAO6V,OACvCjB,CAAyB,GAM7B1L,MAAM5K,QAAQgJ,IAAI2N,CAA+B,EAEjDN,UAA4BhB,EAAeqB,GAAc,EAAE,CAG5D,CAFC,MAAO5W,GACPuT,EAAOjV,MAAM0B,CAAC,CACf,CAuDI,MAAM,GAAkC,WAA9B2W,EAAgC,CACzC,IA3IIQ,EA2IE,CAAC5B,EAAeqB,GAAc7X,EAlK1CwX,EAqKQA,EApKRC,EAqKQA,EApKRC,EAqKQA,EAjKRlB,EAmKQA,EAlKRqB,EAmKQA,EA/JFc,EA6JEhB,EA7JwCnB,GAChD,IACMmC,EACF5M,MAAM0L,EAA0BkB,IAS1BP,GAHuBrM,MAAMoK,GACjCuB,CAAyB,GAEcW,KACvCC,GAAUA,EAAO9B,gBAAkBA,CAAa,IAGhDzK,MAAM0L,EAA0BW,EAAY/J,MAKjD,CAFC,MAAOpN,GACPuT,EAAOjV,MAAM0B,CAAC,CACf,CACDuW,EAA6B,SAAAhB,EAAeqB,CAAU,CA2IjD,KAAM,CAAA,IACEe,EAAef,EAQfrB,EAAeqC,EAAW/S,EAQ1BgT,EAjBiC,YAA/BlB,GACH,CAACgB,EAAef,GAAc7X,EAEpCwX,EAEE,UAAAoB,EACAf,CAA6B,GAEK,QAA3BD,GACH,CAACpB,EAAeqC,EAAW/S,GAAY9F,EAC7CwX,QAEEhB,EACAqC,EACA/S,CAAwC,GAEN,QAA3B8R,GACH,CAACkB,GAAgB9Y,EAEvBwX,EAAQ,MAAkBsB,CAA4B,GAEtDtB,EAASI,EAAS,GAAG5X,CAAI,CAC1B,CAGF,CAFC,MAAOiB,GACPuT,EAAOjV,MAAM0B,CAAC,CACf,CACF,CAEH,CCrSO,IAAM8X,GAAoB,GA6BjC,IAAMC,GAAmB,UAlBvBxW,YACSyW,EAA0D,GAC1D3U,EARkB,KAOlB1B,KAAgBqW,iBAAhBA,EACArW,KAAc0B,eAAdA,CACL,CAEJ4U,oBAAoB7K,GAClB,OAAOzL,KAAKqW,iBAAiB5K,EAC9B,CAED8K,oBAAoB9K,EAAe+K,GACjCxW,KAAKqW,iBAAiB5K,GAAS+K,CAChC,CAEDC,uBAAuBhL,GACrB,OAAOzL,KAAKqW,iBAAiB5K,EAC9B,CACF,EAmBMvE,eAAewP,GACpBC,SAEM,CAAElL,MAAAA,EAAO/B,OAAAA,CAAQ,EAAGiN,EACpB9X,EAAuB,CAC3BpB,OAAQ,MACRyM,QAhBK,IAAIP,QAAQ,CACjBE,OAAQ,mBACRC,iBAcoBJ,CAbrB,CAAA,GAeKkN,EAASlF,GAAmB7Q,QAAQ,WAAY4K,CAAK,EACrD/C,EAAWS,MAAM8F,MAAM2H,EAAQ/X,CAAO,EAC5C,GAAwB,MAApB6J,EAASc,QAAsC,MAApBd,EAASc,OAgBxC,OAAOd,EAASU,OAhBwC,CACtD1K,IAAImY,EAAe,GACnB,IAEE,IAAMC,EAAgB,MAAMpO,EAASU,KAAM,EAGvC,OAAA9J,EAAAwX,EAAana,QAAO2C,EAAAC,UACtBsX,EAAeC,EAAana,MAAM4C,QAEjB,CAAnB,MAAOwX,IACT,MAAMhP,EAAczH,OAA2C,sBAAA,CAC7D0W,WAAYtO,EAASc,OACrByN,gBAAiBJ,CAClB,CAAA,CACF,CAEH,CAMO3P,eAAegQ,GACpBvG,EAEAwG,EAAuBf,GACvBgB,GAEA,GAAM,CAAE3L,MAAAA,EAAO/B,OAAAA,EAAQkK,cAAAA,CAAa,EAAKjD,EAAIG,QAE7C,GAAI,CAACrF,EACH,MAAM1D,EAAczH,OAAM,aAG5B,GAAI,CAACoJ,EAAQ,CACX,GAAIkK,EACF,MAAO,CACLA,cAAAA,EACAnI,MAAAA,GAGJ,MAAM1D,EAAczH,OAAM,aAC3B,CAED,IAAM+V,EAAqCc,EAAUb,oBACnD7K,CAAK,GACF,CACHhK,aAAc,EACd4V,sBAAuB9Z,KAAKD,IAAK,GAGnC,IAAMga,EAAS,IAAIC,GAUnB,OARA/M,WACEtD,UAEEoQ,EAAOE,MAAK,CACb,EACiBvZ,KAAAA,IAAlBmZ,EAA8BA,EAAgB3F,EAAoB,EAiBtEvK,eAAeuQ,EACbd,EACA,CAAEU,sBAAAA,EAAuB5V,aAAAA,CAAY,EACrC6V,EACAH,EAAuBf,UAEvB,GAAM,CAAE3K,MAAAA,EAAOmI,cAAAA,CAAe,EAAG+C,EAIjC,IACExN,MAAMuO,GAAoBJ,EAAQD,CAAqB,CAaxD,CAZC,MAAOhZ,GACP,GAAIuV,EAQF,OAPAhC,EAAOnV,KACL,6GACyCmX,2EAEpCvV,MAAAA,EAAC,KAAA,EAADA,EAAakB,UACb,EAEA,CAAEkM,MAAAA,EAAOmI,cAAAA,GAElB,MAAMvV,CACP,CAED,IACE,IAAMqK,EAAWS,MAAMuN,GAAmBC,CAAS,EAKnD,OAFAQ,EAAUV,uBAAuBhL,CAAK,EAE/B/C,CA0CR,CAzCC,MAAOrK,GACP,IAAM1B,EAAQ0B,EACd,GAAI,CAACsZ,GAAiBhb,CAAK,EAAG,CAE5B,GADAwa,EAAUV,uBAAuBhL,CAAK,EAClCmI,EAMF,OALAhC,EAAOnV,KACL,0GACyCmX,2EACkCjX,MAAAA,EAAK,KAAA,EAALA,EAAO4C,UAAU,EAEvF,CAAEkM,MAAAA,EAAOmI,cAAAA,GAEhB,MAAMvV,CAET,CAED,IAAMuZ,EACsC,MAA1C9O,OAAO,OAAAxJ,EAAA3C,MAAAA,EAAA,KAAA,EAAAA,EAAOmD,YAAU,KAAA,EAAAR,EAAE0X,UAAU,EAChCxV,EACEC,EACA0V,EAAUzV,eACVyU,EAAiB,EAEnB3U,EAAuBC,EAAc0V,EAAUzV,cAAc,EAG7D2U,EAAmB,CACvBgB,sBAAuB9Z,KAAKD,IAAG,EAAKsa,EACpCnW,aAAcA,EAAe,GAO/B,OAHA0V,EAAUZ,oBAAoB9K,EAAO4K,CAAgB,EACrDzE,EAAOzV,uCAAuCyb,UAAsB,EAE7DH,EACLd,EACAN,EACAiB,EACAH,CAAS,CAEZ,CACH,EAxFI,CAAE1L,MAAAA,EAAO/B,OAAAA,EAAQkK,cAAAA,CAAa,EAC9ByC,EACAiB,EACAH,CAAS,CAEb,CAiGA,SAASO,GACPJ,EACAD,GAEA,OAAO,IAAI9Y,QAAQ,CAACC,EAASC,KAE3B,IAAMmZ,EAAgB/V,KAAKgW,IAAIR,EAAwB9Z,KAAKD,IAAG,EAAI,CAAC,EAEpE,IAAMwa,EAAUtN,WAAWhM,EAASoZ,CAAa,EAGjDN,EAAOtR,iBAAiB,KACtB+R,aAAaD,CAAO,EAEpBrZ,EACEsJ,EAAczH,OAAsC,iBAAA,CAClD+W,sBAAAA,CACD,CAAA,CAAC,CAEN,CAAC,CACH,CAAC,CACH,CAOA,SAASM,GAAiBtZ,GACxB,IAKM2Y,EALN,MAAI,CAAA,EAAE3Y,aAAasB,GAAmBtB,EAAEyB,cAQvB,OAHXkX,EAAalO,OAAOzK,EAAEyB,WAAuB,UAAC,IAInC,MAAfkX,GACe,MAAfA,GACe,MAAfA,EAEJ,OAUaO,GAAb3X,cACEI,KAASgY,UAAsB,EAOhC,CANChS,iBAAiBiS,GACfjY,KAAKgY,UAAUpC,KAAKqC,CAAQ,CAC7B,CACDT,QACExX,KAAKgY,UAAUE,QAAQD,GAAYA,EAAU,CAAA,CAC9C,CACF,CCzPM/Q,eAAeiR,GACpBxH,EACAmE,EAGAC,EACArH,EACAkH,EACAjB,EACA7C,OAEMsH,EAAuBlB,GAA4BvG,CAAG,EAsBtD0H,GApBND,EACG5R,KAAKkP,IACJX,EAAqBW,EAAO9B,eAAiB8B,EAAOjK,MAElDkF,EAAIG,QAAQ8C,eACZ8B,EAAO9B,gBAAkBjD,EAAIG,QAAQ8C,eAErChC,EAAOnV,yDAC+CkU,EAAIG,QAAQ8C,gFACC8B,EAAO9B,kBAGtE,wKAA+E,CAGvF,CAAC,EACAnN,MAAMpI,GAAKuT,EAAOjV,MAAM0B,CAAC,CAAC,EAE7ByW,EAA0Bc,KAAKwC,CAAoB,GAnErDlR,UACE,GAAK/I,CAAAA,EAAoB,EAMvB,OALAyT,EAAOnV,KACLsL,EAAczH,OAA6C,wBAAA,CACzDgY,UAAW,kDACZ,EAAE/Y,OAAO,EAEL,CAAA,EAEP,IACE4J,MAAM7K,EAAyB,CAQhC,CAPC,MAAOD,GAMP,OALAuT,EAAOnV,KACLsL,EAAczH,OAA6C,wBAAA,CACzDgY,UAAYja,MAAAA,EAAA,KAAA,EAAAA,EAAaka,SAAU,EACpC,EAAEhZ,OAAO,EAEL,CAAA,CACR,CAEH,MAAO,CAAA,CACT,KAgDsEiH,KAClEgS,IACE,GAAIA,EACF,OAAO9K,EAAcyD,OAIzB,CAAC,GAGG,CAACsH,EAAe3N,GAAO3B,MAAM5K,QAAQgJ,IAAI,CAC7C6Q,EACAC,EACD,EAqBKK,IF4PN/E,IAEA,IACWgF,EADLC,EAAa5E,OAAOK,SAASwE,qBAAqB,QAAQ,EAChE,IAAWF,KAAO1Y,OAAO6V,OAAO8C,CAAU,EACxC,GACED,EAAInE,KACJmE,EAAInE,IAAIrP,SAASwM,CAAQ,GACzBgH,EAAInE,IAAIrP,SAASwO,CAAa,EAE9B,OAAOgF,CAIb,GE1R4BhF,CAAa,GACrCD,GAAgBC,EAAe8E,EAAc7E,aAAa,EAa3DgB,EAAiB,KAAM,IAAIrX,IAAM,EAGgB,OAAA+B,EAAAwR,MAAAA,EAAA,KAAA,EAAAA,EAAS4E,QAAUpW,EAAA,IAsBrE,OAnBAoZ,EAAiBlH,IAAc,WAC/BkH,EAAiBnL,OAAS,CAAA,EAEf,MAAPzC,IACF4N,EAAiBnH,IAAczG,GAOjC8J,WAA6B6D,EAAc7E,cAAe8E,CAAgB,EAQnED,EAAc7E,aACvB,OCrIakF,GACXlZ,YAAmB+Q,GAAA3Q,KAAG2Q,IAAHA,CAAoB,CACvCM,UAEE,OADA,OAAO4D,EAA0B7U,KAAK2Q,IAAIG,QAAQrF,OAC3ClN,QAAQC,SAChB,CACF,CAOME,IAAImW,EAEP,GAOAC,GAEA,GAQEC,GAA4D,GAK9DpB,EAAwB,YAKxBoF,GAAmB,OAMnBC,GAMOC,EAMPC,EAA0B,CAAA,EA8CxB,SAAUC,GAASrI,GACvB,GAAIoI,EACF,MAAMnR,EAAczH,OAAM,uBAExBwQ,EAAQ6C,gBACVA,EAAgB7C,EAAQ6C,eAEtB7C,EAAQiI,WACVA,GAAWjI,EAAQiI,SAEvB,CA8BgB,SAAAK,GACdzI,EACAjD,EACAoD,GAzBMuI,EAAwB,GAC1Bxb,EAAkB,GACpBwb,EAAsBzD,KAAK,0CAA0C,EAElEpW,EAAiB,GACpB6Z,EAAsBzD,KAAK,4BAA4B,EAEtB,EAA/ByD,EAAsBxD,SAClByD,EAAUD,EACb5F,IAAI,CAAClU,EAAS8H,QAAcA,EAAQ,MAAM9H,CAAS,EACnDga,KAAK,GAAG,EACLnG,EAAMrL,EAAczH,OAAiD,4BAAA,CACzEgY,UAAWgB,CACZ,CAAA,EACD1H,EAAOnV,KAAK2W,EAAI7T,OAAO,GAf3B,IA6BQkM,EAAQkF,EAAIG,QAAQrF,MAC1B,GAAI,CAACA,EACH,MAAM1D,EAAczH,OAAM,aAE5B,GAAI,CAACqQ,EAAIG,QAAQpH,OAAQ,CACvB,GAAIiH,CAAAA,EAAIG,QAAQ8C,cAOd,MAAM7L,EAAczH,OAAM,cAN1BsR,EAAOnV,KACL,yKAC+EkU,EAAIG,QAAQ8C,cACzF,sEAAsE,CAK7E,CACD,GAAwC,MAApCiB,EAA0BpJ,GAC5B,MAAM1D,EAAczH,OAAsC,iBAAA,CACxDpC,GAAIuN,CACL,CAAA,EAGH,GAAI,CAACyN,EAAgB,CAInBM,CAAAA,IH/FiC7F,EG+FZA,EH7FvBjV,IAAI+a,EAAuB,GACvBrE,MAAMC,QAAQrB,OAAOL,EAAc,EACrC8F,EAAYzF,OAAOL,GAEnBK,OAAOL,GAAiB8F,EAEnBA,CGuF6B,CAElC,GAAM,CAAEC,YAAAA,EAAa9E,SAAAA,CAAQ,GH0I3B,CACJC,EACAC,EAGAC,EACApB,EACAgG,KAMAjb,IAAIkW,EAAiB,WAElBZ,OAAOL,GAA6BiC,KAAKgE,SAAS,CACrD,EAkBA,OAdE5F,OAAO2F,IAC6B,YAApC,OAAO3F,OAAO2F,KAGd/E,EAAWZ,OAAO2F,IAGpB3F,OAAO2F,GAAoBhF,GACzBC,EACAC,EACAC,EACAC,CAAoB,EAGf,CACLH,SAAAA,EACA8E,YAAa1F,OAAO2F,GAExB,GG/KM9E,EACAC,GACAC,GACApB,EACAoF,EAAQ,EAEVE,EAAsBS,EACtBV,GAAmBpE,EAEnBsE,EAAiB,CAAA,CAClB,CAeD,OAZArE,EAA0BpJ,GAAS0M,GACjCxH,EACAmE,GACAC,GACArH,EACAsL,GACArF,EACA7C,CAAO,EAGmC,IAAIgI,GAAiBnI,CAAG,CAGtE,CC9GOzJ,eAAe2S,KACpB,GAAIhc,EAAkB,EACpB,MAAO,CAAA,EAET,GAAI,CAAC2B,EAAiB,EACpB,MAAO,CAAA,EAET,GAAI,CAACrB,EAAoB,EACvB,MAAO,CAAA,EAGT,IAEE,OAD8BgL,MAAM7K,GAIrC,CAFC,MAAO3B,GACP,MAAO,CAAA,CACR,CACH,CAagB,SAAAmd,GACdC,EACAC,EACAlJ,GAEAiJ,EAAoB5X,EAAmB4X,CAAiB,GC5FnD7S,MACL+S,EACAtE,EACAqE,EACAlJ,KAEA,GAAIA,GAAWA,EAAQoJ,OAErB,OADAD,QAA8B,CAAEE,YAAeH,CAAY,CAAA,EACpDzb,QAAQC,UAGfyb,EAAY,SADU9Q,MAAMwM,EACoB,CAC9CpI,OAAQ,CAAA,EACR4M,YAAeH,CAChB,CAAA,CAEL,GD8EIf,EACApE,EAA0BkF,EAAkBpJ,IAAIG,QAAQrF,OACxDuO,EACAlJ,CAAO,EACPrK,MAAMpI,GAAKuT,EAAOjV,MAAM0B,CAAC,CAAC,CAC9B,CA4BgB,SAAA+b,GACdL,EACA7b,EACA4S,GAEAiJ,EAAoB5X,EAAmB4X,CAAiB,GC5GnD7S,MACL+S,EACAtE,EACAzX,EACA4S,KAEA,GAAIA,GAAWA,EAAQoJ,OAErB,OADAD,QAA8B,CAAEI,QAAWnc,CAAI,CAAA,EACxCK,QAAQC,UAGfyb,EAAY,SADU9Q,MAAMwM,EACoB,CAC9CpI,OAAQ,CAAA,EACR8M,QAAWnc,CACZ,CAAA,CAEL,GD8FI+a,EACApE,EAA0BkF,EAAkBpJ,IAAIG,QAAQrF,OACxDvN,EACA4S,CAAO,EACPrK,MAAMpI,GAAKuT,EAAOjV,MAAM0B,CAAC,CAAC,CAC9B,CAOgB,SAAAic,GACdP,EACAQ,EACAzJ,GAEAiJ,EAAoB5X,EAAmB4X,CAAiB,GCvGnD7S,MACL+S,EACAtE,EACA4E,EACAzJ,KAEA,GAAIA,GAAWA,EAAQoJ,OAAQ,CAC7B,IACWlZ,EADLwZ,EAA6C,GACnD,IAAWxZ,KAAOf,OAAOwa,KAAKF,CAAU,EAEtCC,EAAe,mBAAmBxZ,GAASuZ,EAAWvZ,GAGxD,OADAiZ,EAAY,MAAkBO,CAAc,EACrCjc,QAAQC,SAChB,CAECyb,EAAY,SADU9Q,MAAMwM,EACoB,CAC9CpI,OAAQ,CAAA,EACRmN,gBAAmBH,CACpB,CAAA,CAEL,GDoFItB,EACApE,EAA0BkF,EAAkBpJ,IAAIG,QAAQrF,OACxD8O,EACAzJ,CAAO,EACPrK,MAAMpI,GAAKuT,EAAOjV,MAAM0B,CAAC,CAAC,CAC9B,CAWgB,SAAAsc,GACdZ,EACAa,GAEAb,EAAoB5X,EAAmB4X,CAAiB,GCvEnD7S,MACLyO,EACAiF,KAEA,IAAMhH,EAAgBzK,MAAMwM,EAC5B3B,OAAO,cAAcJ,GAAmB,CAACgH,CAC3C,GDmEI/F,EAA0BkF,EAAkBpJ,IAAIG,QAAQrF,OACxDmP,CAAO,EACPnU,MAAMpI,GAAKuT,EAAOjV,MAAM0B,CAAC,CAAC,CAC9B,CAweM,SAAUwc,GACdd,EACAe,EACAC,EACAjK,GAEAiJ,EAAoB5X,EAAmB4X,CAAiB,GC7rBnD7S,MACL+S,EACAtE,EACAmF,EACAC,EACAjK,KAEA,IAIQ8C,EAJJ9C,GAAWA,EAAQoJ,OACrBD,EAAgC,QAAAa,EAAWC,CAAW,GAGhDnH,EAAgBzK,MAAMwM,EAK5BsE,EAAgC,QAAAa,EAH3B7a,OAAAkQ,OAAAlQ,OAAAkQ,OAAA,GAAA4K,CAAW,EAAA,CACdC,QAAWpH,CAAa,CAAA,CAEuB,EAErD,GD6qBIqF,EACApE,EAA0BkF,EAAkBpJ,IAAIG,QAAQrF,OACxDqP,EACAC,EACAjK,CAAO,EACPrK,MAAMpI,GAAKuT,EAAOjV,MAAM0B,CAAC,CAAC,CAC9B,2CE9rBE+S,GAAkBA,mBAChB,IAAI/O,EACFiP,GACA,CAACV,EAAW,CAAEE,QAASmK,KAOd7B,GALKxI,EAAUC,YAAY,KAAK,EAAEpC,aAAY,EAC/BmC,EACnBC,YAAY,wBAAwB,EACpCpC,eAEgCwM,CAAgB,EACpD,QAEF,CAAA,EAGH7J,GAAkBA,mBAChB,IAAI/O,EAAU,qBAOhB,SACEuO,GAEA,IACE,IAAMsK,EAAYtK,EAAUC,YAAYS,EAAc,EAAE7C,aAAY,EACpE,MAAO,CACLoM,SAAU,CACRC,EACAC,EACAjK,IACG+J,GAASK,EAAWJ,EAAWC,EAAajK,CAAO,EAM3D,CAJC,MAAOzS,GACP,MAAM0J,EAAczH,OAAoD,+BAAA,CACtE6a,OAAQ9c,CACT,CAAA,CACF,CACF,EAxB4E,SAAA,CAAA,EAG7EgT,mBAAgBzT,GAAM+J,EAAO,EAE7B0J,GAAAA,gBAAgBzT,GAAM+J,GAAS,SAAkB,MChDvCyT,EAAAA,QCYCtC,GACXlZ,YACS+Q,EACEvO,GADFpC,KAAG2Q,IAAHA,EACE3Q,KAASoC,UAATA,CACP,CAEJyY,SACEC,EACAC,EACAjK,GAEAuK,GAAYrb,KAAKoC,UAAW0Y,EAAiBC,EAAajK,CAAO,CAClE,CAMDgJ,iBAAiBE,EAAoBlJ,GACnCwK,GAAoBtb,KAAKoC,UAAW4X,EAAYlJ,CAAO,CACxD,CAEDsJ,UAAUlc,EAAY4S,GACpByK,GAAavb,KAAKoC,UAAWlE,EAAI4S,CAAO,CACzC,CAEDwJ,kBACEC,EACAzJ,GAEA0K,GAAqBxb,KAAKoC,UAAWmY,EAAYzJ,CAAO,CACzD,CAED6J,8BAA8BC,GAC5Ba,GAAiCzb,KAAKoC,UAAWwY,CAAO,CACzD,CACF,EDhDWQ,EAAAA,EAAAA,GAkCX,IAjCC,kBAAA,oBACAA,EAAA,iBAAA,mBACAA,EAAA,YAAA,cACAA,EAAA,gBAAA,kBACAA,EAAA,eAAA,iBAMAA,EAAA,kBAAA,oBACAA,EAAA,UAAA,YACAA,EAAA,cAAA,gBACAA,EAAA,MAAA,QACAA,EAAA,UAAA,YACAA,EAAA,SAAA,WACAA,EAAA,OAAA,SACAA,EAAA,iBAAA,mBACAA,EAAA,YAAA,cACAA,EAAA,OAAA,SACAA,EAAA,eAAA,iBACAA,EAAA,YAAA,cACAA,EAAA,iBAAA,mBAEAA,EAAA,oBAAA,sBACAA,EAAA,MAAA,QACAA,EAAA,QAAA,UACAA,EAAA,gBAAA,kBACAA,EAAA,UAAA,YACAA,EAAA,UAAA,YACAA,EAAA,eAAA,iBACAA,EAAA,eAAA,iBACAA,EAAA,oBAAA,sBElBF,IAAMhC,GAA+C,IAInD,IAAMzI,EAAMC,EAAUC,YAAY,YAAY,EAAEpC,aAAY,EACtDiN,EAAsB9K,EAAUC,YAAY,WAAW,EAAEpC,aAAY,EAE3E,OAAO,IAAIqK,GAAiBnI,EAAoB+K,CAAmB,CACrE,EAGQC,EAAmB,CACvBC,UAAW9C,GACXK,SAAU0C,GACVhC,YAAaiC,GAEbV,UAAAA,GAEDW,EAA+B,QAACC,SAASC,kBACxC,IAAI5Z,EAAU,mBAAoB+W,GAA8B,QAAA,EAC7DrW,gBAAgB4Y,CAAgB,EAChC7Y,qBAAqB,CAAA,CAAI,CAAC,EAKjCiZ,EAAAA,QAAS1K,qDAA6B"}