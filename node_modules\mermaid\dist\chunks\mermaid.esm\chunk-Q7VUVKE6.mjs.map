{"version": 3, "sources": ["../../../src/rendering-util/insertElementsForSize.js", "../../../src/rendering-util/setupViewPortForSVG.ts"], "sourcesContent": ["import { select } from 'd3';\n\nexport const getDiagramElement = (id, securityLevel) => {\n  let sandboxElement;\n  if (securityLevel === 'sandbox') {\n    sandboxElement = select('#i' + id);\n  }\n  const root =\n    securityLevel === 'sandbox'\n      ? select(sandboxElement.nodes()[0].contentDocument.body)\n      : select('body');\n\n  const svg = root.select(`[id=\"${id}\"]`);\n\n  // Run the renderer. This is what draws the final graph.\n\n  return svg;\n};\n", "import { configureSvgSize } from '../setupGraphViewbox.js';\nimport type { SVG } from '../diagram-api/types.js';\nimport { log } from '../logger.js';\n\nexport const setupViewPortForSVG = (\n  svg: SVG,\n  padding: number,\n  cssDiagram: string,\n  useMaxWidth: boolean\n) => {\n  // Initialize the SVG element and set the diagram class\n  svg.attr('class', cssDiagram);\n\n  // Calculate the dimensions and position with padding\n  const { width, height, x, y } = calculateDimensionsWithPadding(svg, padding);\n\n  // Configure the size and aspect ratio of the SVG\n  configureSvgSize(svg, height, width, useMaxWidth);\n\n  // Update the viewBox to ensure all content is visible with padding\n  const viewBox = createViewBox(x, y, width, height, padding);\n  svg.attr('viewBox', viewBox);\n\n  // Log the viewBox configuration for debugging\n  log.debug(`viewBox configured: ${viewBox} with padding: ${padding}`);\n};\n\nconst calculateDimensionsWithPadding = (svg: SVG, padding: number) => {\n  const bounds = svg.node()?.getBBox() || { width: 0, height: 0, x: 0, y: 0 };\n  return {\n    width: bounds.width + padding * 2,\n    height: bounds.height + padding * 2,\n    x: bounds.x,\n    y: bounds.y,\n  };\n};\n\nconst createViewBox = (x: number, y: number, width: number, height: number, padding: number) => {\n  return `${x - padding} ${y - padding} ${width} ${height}`;\n};\n"], "mappings": ";;;;;;;;;;AAEO,IAAM,oBAAoB,wBAAC,IAAI,kBAAkB;AACtD,MAAI;AACJ,MAAI,kBAAkB,WAAW;AAC/B,qBAAiB,eAAO,OAAO,EAAE;AAAA,EACnC;AACA,QAAM,OACJ,kBAAkB,YACd,eAAO,eAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,IACrD,eAAO,MAAM;AAEnB,QAAM,MAAM,KAAK,OAAO,QAAQ,EAAE,IAAI;AAItC,SAAO;AACT,GAfiC;;;ACE1B,IAAM,sBAAsB,wBACjC,KACA,SACA,YACA,gBACG;AAEH,MAAI,KAAK,SAAS,UAAU;AAG5B,QAAM,EAAE,OAAO,QAAQ,GAAG,EAAE,IAAI,+BAA+B,KAAK,OAAO;AAG3E,mBAAiB,KAAK,QAAQ,OAAO,WAAW;AAGhD,QAAM,UAAU,cAAc,GAAG,GAAG,OAAO,QAAQ,OAAO;AAC1D,MAAI,KAAK,WAAW,OAAO;AAG3B,MAAI,MAAM,uBAAuB,OAAO,kBAAkB,OAAO,EAAE;AACrE,GArBmC;AAuBnC,IAAM,iCAAiC,wBAAC,KAAU,YAAoB;AACpE,QAAM,SAAS,IAAI,KAAK,GAAG,QAAQ,KAAK,EAAE,OAAO,GAAG,QAAQ,GAAG,GAAG,GAAG,GAAG,EAAE;AAC1E,SAAO;AAAA,IACL,OAAO,OAAO,QAAQ,UAAU;AAAA,IAChC,QAAQ,OAAO,SAAS,UAAU;AAAA,IAClC,GAAG,OAAO;AAAA,IACV,GAAG,OAAO;AAAA,EACZ;AACF,GARuC;AAUvC,IAAM,gBAAgB,wBAAC,GAAW,GAAW,OAAe,QAAgB,YAAoB;AAC9F,SAAO,GAAG,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,IAAI,MAAM;AACzD,GAFsB;", "names": []}