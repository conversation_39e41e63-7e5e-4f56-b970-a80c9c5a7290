chalk = {
	"elements": {
		"nodes": [
			{
				"data": {
					"id": "nwtN_00dcd31b-2631-4af1-980b-4493dc7d43f7",
					"bbox": {
						"x": 506.4608860015869,
						"y": 105.05675506591797,
						"w": 60.3330078125,
						"h": 28.25
					},
					"originalW": 88.3330078125,
					"originalH": 56.25,
					"class": "compartment",
					"label": "types",
					"statesandinfos": [],
					"parent": "nwtN_80eb137f-5cd7-4584-9acc-87cc5698d255",
					"language": "PD",
					"border-width": 2.25,
					"border-color": "#555555",
					"background-color": "#f5f5f5",
					"background-opacity": 0.5,
					"background-image-opacity": "",
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"ports": [],
					"background-image": "",
					"background-fit": "",
					"background-position-x": "",
					"background-position-y": "",
					"background-width": "",
					"background-height": "",
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 506.4608860015869,
					"y": 105.05675506591797
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},		
			{
				"data": {
					"id": "nwtN_80eb137f-5cd7-4584-9acc-87cc5698d255",
					"bbox": {
						"x": 503.796484757312,
						"y": 149.75074733690965,
						"w": 141.2687438947998,
						"h": 150.88798454198337
					},
					"originalW": 169.2687438947998,
					"originalH": 178.88798454198337,
					"class": "compartment",
					"label": "chalk",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 2.25,
					"border-color": "#555555",
					"background-color": "#f5f5f5",
					"background-opacity": 0.5,
					"background-image-opacity": "",
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"ports": [],
					"background-image": "",
					"background-fit": "",
					"background-position-x": "",
					"background-position-y": "",
					"background-width": "",
					"background-height": "",
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 503.796484757312,
					"y": 149.75074733690965
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_23452b85-7855-413f-a03d-26412ed87bfc",
					"bbox": {
						"x": 506.4608860015869,
						"y": 105.05675506591797,
						"w": 57.0830078125,
						"h": 25
					},
					"class": "simple chemical",
					"label": "index.d.ts",
					"statesandinfos": [],
					"parent": "nwtN_00dcd31b-2631-4af1-980b-4493dc7d43f7",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#c7eae5",
					"background-opacity": 1,
					"background-image-opacity": "",
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"ports": [],
					"background-image": "",
					"background-fit": "",
					"background-position-x": "",
					"background-position-y": "",
					"background-width": "",
					"background-height": "",
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 506.4608860015869,
					"y": 105.05675506591797
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_6ffc1fdf-602f-4395-89b5-666b383a5059",
					"bbox": {
						"x": 458.4355503099121,
						"y": 211.06973960790134,
						"w": 47.296875,
						"h": 25
					},
					"class": "macromolecule",
					"label": "index.js",
					"statesandinfos": [],
					"parent": "nwtN_80eb137f-5cd7-4584-9acc-87cc5698d255",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#dfc27d",
					"background-opacity": 1,
					"background-image-opacity": "",
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"ports": [],
					"background-image": "",
					"background-fit": "",
					"background-position-x": "",
					"background-position-y": "",
					"background-width": "",
					"background-height": "",
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 458.4355503099121,
					"y": 211.06973960790134
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_c152aeef-01df-49fc-9539-9e022e8d4c64",
					"bbox": {
						"x": 538.4608860015869,
						"y": 205.05675506591797,
						"w": 68.68994140625,
						"h": 25
					},
					"class": "macromolecule",
					"label": "templates.js",
					"statesandinfos": [],
					"parent": "nwtN_80eb137f-5cd7-4584-9acc-87cc5698d255",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#dfc27d",
					"background-opacity": 1,
					"background-image-opacity": "",
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"ports": [],
					"background-image": "",
					"background-fit": "",
					"background-position-x": "",
					"background-position-y": "",
					"background-width": "",
					"background-height": "",
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 538.4608860015869,
					"y": 205.05675506591797
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_c50c8570-f7bd-46c3-b7c2-66857746b854",
					"bbox": {
						"x": 457.4608860015869,
						"y": 305.05675506591797,
						"w": 50.546875,
						"h": 28.25
					},
					"originalW": 78.546875,
					"originalH": 56.25,
					"class": "compartment",
					"label": "ansi-styles",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 2.25,
					"border-color": "#555555",
					"background-color": "#f5f5f5",
					"background-opacity": 0.5,
					"background-image-opacity": "",
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"ports": [],
					"background-image": "",
					"background-fit": "",
					"background-position-x": "",
					"background-position-y": "",
					"background-width": "",
					"background-height": "",
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 457.4608860015869,
					"y": 305.05675506591797
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_fa99aac7-9500-4ead-82c3-90c72e9837f3",
					"bbox": {
						"x": 457.4608860015869,
						"y": 305.05675506591797,
						"w": 47.296875,
						"h": 25
					},
					"class": "macromolecule",
					"label": "index.js",
					"statesandinfos": [],
					"parent": "nwtN_c50c8570-f7bd-46c3-b7c2-66857746b854",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#dfc27d",
					"background-opacity": 1,
					"background-image-opacity": "",
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"ports": [],
					"background-image": "",
					"background-fit": "",
					"background-position-x": "",
					"background-position-y": "",
					"background-width": "",
					"background-height": "",
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 457.4608860015869,
					"y": 305.05675506591797
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_cafd8043-b744-474c-99d0-7f33deb415ac",
					"bbox": {
						"x": 451.6038619242243,
						"y": 380.17711757548415,
						"w": 50.546875,
						"h": 28.25
					},
					"originalW": 78.546875,
					"originalH": 56.25,
					"class": "compartment",
					"label": "escape-string-regexp",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 2.25,
					"border-color": "#555555",
					"background-color": "#f5f5f5",
					"background-opacity": 0.5,
					"background-image-opacity": "",
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"ports": [],
					"background-image": "",
					"background-fit": "",
					"background-position-x": "",
					"background-position-y": "",
					"background-width": "",
					"background-height": "",
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 451.6038619242243,
					"y": 380.17711757548415
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_6442ea6c-8a74-4948-ab60-2a2fc6b904f7",
					"bbox": {
						"x": 451.6038619242243,
						"y": 380.17711757548415,
						"w": 47.296875,
						"h": 25
					},
					"class": "macromolecule",
					"label": "index.js",
					"statesandinfos": [],
					"parent": "nwtN_cafd8043-b744-474c-99d0-7f33deb415ac",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#dfc27d",
					"background-opacity": 1,
					"background-image-opacity": "",
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"ports": [],
					"background-image": "",
					"background-fit": "",
					"background-position-x": "",
					"background-position-y": "",
					"background-width": "",
					"background-height": "",
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 451.6038619242243,
					"y": 380.17711757548415
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_e99db3a7-4249-47e7-84cf-b61676393dc8",
					"bbox": {
						"x": 467.6089290625593,
						"y": 476.207520405494,
						"w": 50.546875,
						"h": 28.25
					},
					"originalW": 78.546875,
					"originalH": 56.25,
					"class": "compartment",
					"label": "supports-color",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 2.25,
					"border-color": "#555555",
					"background-color": "#f5f5f5",
					"background-opacity": 0.5,
					"background-image-opacity": "",
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"ports": [],
					"background-image": "",
					"background-fit": "",
					"background-position-x": "",
					"background-position-y": "",
					"background-width": "",
					"background-height": "",
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 467.6089290625593,
					"y": 476.207520405494
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_395e3fed-fcd4-427a-909a-c01a7fc94607",
					"bbox": {
						"x": 467.6089290625593,
						"y": 476.207520405494,
						"w": 47.296875,
						"h": 25
					},
					"class": "macromolecule",
					"label": "index.js",
					"statesandinfos": [],
					"parent": "nwtN_e99db3a7-4249-47e7-84cf-b61676393dc8",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#dfc27d",
					"background-opacity": 1,
					"background-image-opacity": "",
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"ports": [],
					"background-image": "",
					"background-fit": "",
					"background-position-x": "",
					"background-position-y": "",
					"background-width": "",
					"background-height": "",
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 467.6089290625593,
					"y": 476.207520405494
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_641f76bc-059e-4885-9e58-8bc08b26765d",
					"bbox": {
						"x": 663.6710015071626,
						"y": 439.19580264809434,
						"w": 50.546875,
						"h": 28.25
					},
					"originalW": 78.546875,
					"originalH": 56.25,
					"class": "compartment",
					"label": "has-flag",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 2.25,
					"border-color": "#555555",
					"background-color": "#f5f5f5",
					"background-opacity": 0.5,
					"background-image-opacity": "",
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"ports": [],
					"background-image": "",
					"background-fit": "",
					"background-position-x": "",
					"background-position-y": "",
					"background-width": "",
					"background-height": "",
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 663.6710015071626,
					"y": 439.19580264809434
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_fc4a5aa8-171f-41bd-8ab3-5eee343c4f9f",
					"bbox": {
						"x": 663.6710015071626,
						"y": 439.19580264809434,
						"w": 47.296875,
						"h": 25
					},
					"class": "macromolecule",
					"label": "index.js",
					"statesandinfos": [],
					"parent": "nwtN_641f76bc-059e-4885-9e58-8bc08b26765d",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#dfc27d",
					"background-opacity": 1,
					"background-image-opacity": "",
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"ports": [],
					"background-image": "",
					"background-fit": "",
					"background-position-x": "",
					"background-position-y": "",
					"background-width": "",
					"background-height": "",
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 663.6710015071626,
					"y": 439.19580264809434
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_e4a59b7b-986f-40c1-8211-07ecd7bd4a85",
					"bbox": {
						"x": 769.0413352597577,
						"y": 155.046620789248,
						"w": 209.7825138067825,
						"h": 76.26330123812932
					},
					"originalW": 237.7825138067825,
					"originalH": 104.26330123812932,
					"class": "compartment",
					"label": "color-convert",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 2.25,
					"border-color": "#555555",
					"background-color": "#f5f5f5",
					"background-opacity": 0.5,
					"background-image-opacity": "",
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"ports": [],
					"background-image": "",
					"background-fit": "",
					"background-position-x": "",
					"background-position-y": "",
					"background-width": "",
					"background-height": "",
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 769.0413352597577,
					"y": 155.046620789248
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_10cd6cfc-6d2a-48b1-82e1-29d16dadd069",
					"bbox": {
						"x": 689.4235158563665,
						"y": 179.05327140831267,
						"w": 47.296875,
						"h": 25
					},
					"class": "macromolecule",
					"label": "index.js",
					"statesandinfos": [],
					"parent": "nwtN_e4a59b7b-986f-40c1-8211-07ecd7bd4a85",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#dfc27d",
					"background-opacity": 1,
					"background-image-opacity": "",
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"ports": [],
					"background-image": "",
					"background-fit": "",
					"background-position-x": "",
					"background-position-y": "",
					"background-width": "",
					"background-height": "",
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 689.4235158563665,
					"y": 179.05327140831267
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_da2a2f8c-a185-4ffd-b577-f2c5a7680a37",
					"bbox": {
						"x": 832.459935913149,
						"y": 174.05612167362608,
						"w": 79.6953125,
						"h": 25
					},
					"class": "macromolecule",
					"label": "conversions.js",
					"statesandinfos": [],
					"parent": "nwtN_e4a59b7b-986f-40c1-8211-07ecd7bd4a85",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#dfc27d",
					"background-opacity": 1,
					"background-image-opacity": "",
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"ports": [],
					"background-image": "",
					"background-fit": "",
					"background-position-x": "",
					"background-position-y": "",
					"background-width": "",
					"background-height": "",
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 832.459935913149,
					"y": 174.05612167362608
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_ec240627-f567-42b4-a9c6-4564450e9946",
					"bbox": {
						"x": 761.4273162101177,
						"y": 131.03997017018335,
						"w": 46.072265625,
						"h": 25
					},
					"class": "macromolecule",
					"label": "route.js",
					"statesandinfos": [],
					"parent": "nwtN_e4a59b7b-986f-40c1-8211-07ecd7bd4a85",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#dfc27d",
					"background-opacity": 1,
					"background-image-opacity": "",
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"ports": [],
					"background-image": "",
					"background-fit": "",
					"background-position-x": "",
					"background-position-y": "",
					"background-width": "",
					"background-height": "",
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 761.4273162101177,
					"y": 131.03997017018335
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_bc39472b-e95f-4e5c-955e-eb6fd7fe0cdf",
					"bbox": {
						"x": 914.4608860015869,
						"y": 275.05675506591797,
						"w": 50.546875,
						"h": 28.25
					},
					"originalW": 78.546875,
					"originalH": 56.25,
					"class": "compartment",
					"label": "color-name",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 2.25,
					"border-color": "#555555",
					"background-color": "#f5f5f5",
					"background-opacity": 0.5,
					"background-image-opacity": "",
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"ports": [],
					"background-image": "",
					"background-fit": "",
					"background-position-x": "",
					"background-position-y": "",
					"background-width": "",
					"background-height": "",
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 914.4608860015869,
					"y": 275.05675506591797
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_2f088c82-aac6-4e48-8fde-8574a3390d24",
					"bbox": {
						"x": 914.4608860015869,
						"y": 275.05675506591797,
						"w": 47.296875,
						"h": 25
					},
					"class": "macromolecule",
					"label": "index.js",
					"statesandinfos": [],
					"parent": "nwtN_bc39472b-e95f-4e5c-955e-eb6fd7fe0cdf",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#dfc27d",
					"background-opacity": 1,
					"background-image-opacity": "",
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"ports": [],
					"background-image": "",
					"background-fit": "",
					"background-position-x": "",
					"background-position-y": "",
					"background-width": "",
					"background-height": "",
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 914.4608860015869,
					"y": 275.05675506591797
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_88c73829-9d76-4b03-ab27-335fd9581531",
					"bbox": {
						"x": 638.6630841035143,
						"y": 555.2325394010229,
						"w": 30,
						"h": 25
					},
					"class": "unspecified entity",
					"label": "os",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#f5f5f5",
					"background-opacity": 1,
					"background-image-opacity": "",
					"text-wrap": "wrap",
					"font-size": 11,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"ports": [],
					"background-image": "",
					"background-fit": "",
					"background-position-x": "",
					"background-position-y": "",
					"background-width": "",
					"background-height": "",
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 638.6630841035143,
					"y": 555.2325394010229
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			}
		],
		"edges": [
			{
				"data": {
					"id": "nwtE_d1ae8562-bb81-4d93-ac24-78b9def385a2",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"cardinality": 0,
					"source": "nwtN_6ffc1fdf-602f-4395-89b5-666b383a5059",
					"target": "nwtN_c152aeef-01df-49fc-9539-9e022e8d4c64",
					"portsource": "nwtN_6ffc1fdf-602f-4395-89b5-666b383a5059",
					"porttarget": "nwtN_c152aeef-01df-49fc-9539-9e022e8d4c64"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_925bc3d3-7dac-4ef3-b0d7-875c78be8fbe",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"cardinality": 0,
					"source": "nwtN_6ffc1fdf-602f-4395-89b5-666b383a5059",
					"target": "nwtN_fa99aac7-9500-4ead-82c3-90c72e9837f3",
					"portsource": "nwtN_6ffc1fdf-602f-4395-89b5-666b383a5059",
					"porttarget": "nwtN_fa99aac7-9500-4ead-82c3-90c72e9837f3"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_d32f598b-4b96-4403-87c4-870e99ee6bc1",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"cardinality": 0,
					"source": "nwtN_6ffc1fdf-602f-4395-89b5-666b383a5059",
					"target": "nwtN_6442ea6c-8a74-4948-ab60-2a2fc6b904f7",
					"portsource": "nwtN_6ffc1fdf-602f-4395-89b5-666b383a5059",
					"porttarget": "nwtN_6442ea6c-8a74-4948-ab60-2a2fc6b904f7"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_0f92d3d4-1849-4f4c-97f0-6504eb9831ac",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"cardinality": 0,
					"source": "nwtN_6ffc1fdf-602f-4395-89b5-666b383a5059",
					"target": "nwtN_395e3fed-fcd4-427a-909a-c01a7fc94607",
					"portsource": "nwtN_6ffc1fdf-602f-4395-89b5-666b383a5059",
					"porttarget": "nwtN_395e3fed-fcd4-427a-909a-c01a7fc94607"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_2b21b38a-8443-45ea-b067-d626a731fc32",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"cardinality": 0,
					"source": "nwtN_fa99aac7-9500-4ead-82c3-90c72e9837f3",
					"target": "nwtN_10cd6cfc-6d2a-48b1-82e1-29d16dadd069",
					"portsource": "nwtN_fa99aac7-9500-4ead-82c3-90c72e9837f3",
					"porttarget": "nwtN_10cd6cfc-6d2a-48b1-82e1-29d16dadd069"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_cc83602e-928e-4d47-b60e-9ec94b93f9bd",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"cardinality": 0,
					"source": "nwtN_10cd6cfc-6d2a-48b1-82e1-29d16dadd069",
					"target": "nwtN_ec240627-f567-42b4-a9c6-4564450e9946",
					"portsource": "nwtN_10cd6cfc-6d2a-48b1-82e1-29d16dadd069",
					"porttarget": "nwtN_ec240627-f567-42b4-a9c6-4564450e9946"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_66f2d0d1-0542-450a-b6ae-f1c8bfa19e23",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"cardinality": 0,
					"source": "nwtN_ec240627-f567-42b4-a9c6-4564450e9946",
					"target": "nwtN_da2a2f8c-a185-4ffd-b577-f2c5a7680a37",
					"portsource": "nwtN_ec240627-f567-42b4-a9c6-4564450e9946",
					"porttarget": "nwtN_da2a2f8c-a185-4ffd-b577-f2c5a7680a37"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_83bd7195-5e2f-49c8-87f1-b54d74309928",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"cardinality": 0,
					"source": "nwtN_10cd6cfc-6d2a-48b1-82e1-29d16dadd069",
					"target": "nwtN_da2a2f8c-a185-4ffd-b577-f2c5a7680a37",
					"portsource": "nwtN_10cd6cfc-6d2a-48b1-82e1-29d16dadd069",
					"porttarget": "nwtN_da2a2f8c-a185-4ffd-b577-f2c5a7680a37"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_5e327bfd-b03d-440c-8825-0f03c01c7426",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"cardinality": 0,
					"source": "nwtN_da2a2f8c-a185-4ffd-b577-f2c5a7680a37",
					"target": "nwtN_2f088c82-aac6-4e48-8fde-8574a3390d24",
					"portsource": "nwtN_da2a2f8c-a185-4ffd-b577-f2c5a7680a37",
					"porttarget": "nwtN_2f088c82-aac6-4e48-8fde-8574a3390d24"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_93128cfa-29d1-4669-8496-85227b119c1f",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"cardinality": 0,
					"source": "nwtN_395e3fed-fcd4-427a-909a-c01a7fc94607",
					"target": "nwtN_fc4a5aa8-171f-41bd-8ab3-5eee343c4f9f",
					"portsource": "nwtN_395e3fed-fcd4-427a-909a-c01a7fc94607",
					"porttarget": "nwtN_fc4a5aa8-171f-41bd-8ab3-5eee343c4f9f"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_95f18b40-fe6a-482c-9ff2-5296ad1923c0",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#a8a8a8",
					"width": 1.25,
					"cardinality": 0,
					"source": "nwtN_395e3fed-fcd4-427a-909a-c01a7fc94607",
					"target": "nwtN_88c73829-9d76-4b03-ab27-335fd9581531",
					"portsource": "nwtN_395e3fed-fcd4-427a-909a-c01a7fc94607",
					"porttarget": "nwtN_88c73829-9d76-4b03-ab27-335fd9581531"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			}
		]
	},
	"style": [
	    {
	      "selector": "node",
	      "style": {
	        "shape": "rectangle",
	        "text-halign": "center",
	        "text-valign": "center",
	        "background-color": "#ffffff",
	        "opacity": "1",
	        "border-color": "#555555"
	      }
	    },
	    {
	      "selector": "node[class = 'simple chemical']",
	      "style": {
	        "shape": "ellipse",
	        "background-color": "#c7eae5"
	      }
	    },
	    {
	      "selector": "node[class = 'macromolecule']",
	      "style": {
	        "shape": "roundrectangle",
	        "background-color": "#dfc27d"
	      }
	    },
	    {
	      "selector": "node[class = 'unspecified entity']",
	      "style": {
	        "shape": "ellipse",
	        "background-color": "#f5f5f5"
	      }
	    },	    
	    {
	      "selector": ":parent",
	      "style": {
	        "background-opacity": "0.333",
	        "text-valign": "bottom",
	        "shape": "barrel",
	        "text-margin-y": "2px",
	        "font-weight" : "bold",
	        "border-color": "#555555"
	      }
	    },
	    {
	      "selector": "node:selected",
	      "style": {
	        "background-color": "#33ff00",
	        "border-color": "#22ee00"
	      }
	    },
	    {
	      "selector": "edge",
	      "style": {
	        "curve-style": "bezier",
	        "width": "2px",
	        "line-color": "rgb(58,126,207)",
	        "opacity": "1",
        	"target-arrow-shape": "triangle",
        	"target-arrow-color": "rgb(58,126,207)",
	      }
	    },
	    {
	      "selector": "edge:selected",
	      "style": {
	        "line-color": "#33ff00",
	        "font-size": "13px",
	        "text-opacity": "1",
	        "text-rotation": "autorotate",
	        "color": "#33ff00",
	        "font-weight": "bold",
	        "text-background-shape": "roundrectangle",
	        "text-background-opacity": "1",
	        "text-background-padding": "2px",
	        "target-arrow-color": "#33ff00",
	        "target-arrow-shape": "triangle",
	      }
	    }
	  ],
	"zoomingEnabled": true,
	"userZoomingEnabled": true,
	"zoom": 1.7784699371350863,
	"minZoom": 0.125,
	"maxZoom": 16,
	"panningEnabled": true,
	"userPanningEnabled": true,
	"pan": {
		"x": -246.2736846361503,
		"y": -69.57913264371261
	},
	"boxSelectionEnabled": true,
	"renderer": {
		"name": "canvas"
	},
	"wheelSensitivity": 0.1,
	"motionBlur": true
}