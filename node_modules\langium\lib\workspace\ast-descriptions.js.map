{"version": 3, "file": "ast-descriptions.js", "sourceRoot": "", "sources": ["../../src/workspace/ast-descriptions.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAQhF,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAC7D,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AACnD,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AACjF,OAAO,EAAE,iBAAiB,EAAE,MAAM,uBAAuB,CAAC;AAC1D,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAC9D,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AAqBjD,MAAM,OAAO,iCAAiC;IAK1C,YAAY,QAA6B;QACrC,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC,SAAS,CAAC,cAAc,CAAC;QACxD,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC;IACzD,CAAC;IAED,iBAAiB,CAAC,IAAa,EAAE,IAAwB,EAAE,QAA0B;QACjF,MAAM,GAAG,GAAG,QAAQ,aAAR,QAAQ,cAAR,QAAQ,GAAI,WAAW,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,aAAJ,IAAI,cAAJ,IAAI,IAAJ,IAAI,GAAK,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,EAAC;QACzC,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACtD,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,MAAM,IAAI,KAAK,CAAC,gBAAgB,IAAI,eAAe,CAAC,CAAC;QACzD,CAAC;QACD,IAAI,eAA4C,CAAC;QACjD,MAAM,iBAAiB,GAAG,GAAG,EAAE,kBAAC,eAAe,aAAf,eAAe,cAAf,eAAe,IAAf,eAAe,GAAK,iBAAiB,CAAC,MAAA,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,mCAAI,IAAI,CAAC,QAAQ,CAAC,IAAA,CAAC;QAC5H,OAAO;YACH,IAAI;YACJ,IAAI;YACJ,IAAI,WAAW;gBACX,OAAO,iBAAiB,EAAE,CAAC;YAC/B,CAAC;YACD,gBAAgB,EAAE,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC;YAClD,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,WAAW,EAAE,GAAG,CAAC,GAAG;YACpB,IAAI;SACP,CAAC;IACN,CAAC;CAEJ;AAqCD,MAAM,OAAO,mCAAmC;IAI5C,YAAY,QAA6B;QACrC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC,cAAc,CAAC;IACzD,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,QAAyB,EAAE,WAAW,GAAG,iBAAiB,CAAC,IAAI;QACpF,MAAM,KAAK,GAA2B,EAAE,CAAC;QACzC,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC;QAC5C,KAAK,MAAM,OAAO,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxC,MAAM,iBAAiB,CAAC,WAAW,CAAC,CAAC;YACrC,gBAAgB,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACpF,6FAA6F;gBAC7F,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;gBACpD,IAAI,WAAW,EAAE,CAAC;oBACd,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC5B,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAES,iBAAiB,CAAC,OAAsB;QAC9C,MAAM,eAAe,GAAG,OAAO,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC3D,MAAM,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC;QAC9C,IAAI,CAAC,eAAe,IAAI,CAAC,UAAU,EAAE,CAAC;YAClC,OAAO,SAAS,CAAC;QACrB,CAAC;QACD,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC;QAClD,OAAO;YACH,SAAS,EAAE,MAAM;YACjB,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC;YAC9D,SAAS,EAAE,eAAe,CAAC,WAAW;YACtC,UAAU,EAAE,eAAe,CAAC,IAAI;YAChC,OAAO,EAAE,iBAAiB,CAAC,UAAU,CAAC;YACtC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,WAAW,EAAE,MAAM,CAAC;SAC9D,CAAC;IACN,CAAC;CAEJ"}