{"version": 3, "sources": ["../../../src/internals.ts", "../../../src/rendering-util/render.ts"], "sourcesContent": ["import { getConfig } from './config.js';\nimport common from './diagrams/common/common.js';\nimport { log } from './logger.js';\nimport { insertCluster } from './rendering-util/rendering-elements/clusters.js';\nimport {\n  insertEdge,\n  insertEdgeLabel,\n  positionEdgeLabel,\n} from './rendering-util/rendering-elements/edges.js';\nimport insertMarkers from './rendering-util/rendering-elements/markers.js';\nimport { insertNode } from './rendering-util/rendering-elements/nodes.js';\nimport { labelHelper } from './rendering-util/rendering-elements/shapes/util.js';\nimport { interpolateToCurve } from './utils.js';\n\n/**\n * Internal helpers for mermaid\n * @deprecated - This should not be used by external packages, as the definitions will change without notice.\n */\nexport const internalHelpers = {\n  common,\n  getConfig,\n  insertCluster,\n  insertEdge,\n  insertEdgeLabel,\n  insertMarkers,\n  insertNode,\n  interpolateToCurve,\n  labelHelper,\n  log,\n  positionEdgeLabel,\n};\n\nexport type InternalHelpers = typeof internalHelpers;\n", "import type { SVG } from '../diagram-api/types.js';\nimport type { InternalHelpers } from '../internals.js';\nimport { internalHelpers } from '../internals.js';\nimport { log } from '../logger.js';\nimport type { LayoutData } from './types.js';\n\nexport interface RenderOptions {\n  algorithm?: string;\n}\n\nexport interface LayoutAlgorithm {\n  render(\n    layoutData: LayoutData,\n    svg: SVG,\n    helpers: InternalHelpers,\n    options?: RenderOptions\n  ): Promise<void>;\n}\n\nexport type LayoutLoader = () => Promise<LayoutAlgorithm>;\nexport interface LayoutLoaderDefinition {\n  name: string;\n  loader: LayoutLoader;\n  algorithm?: string;\n}\n\nconst layoutAlgorithms: Record<string, LayoutLoaderDefinition> = {};\n\nexport const registerLayoutLoaders = (loaders: LayoutLoaderDefinition[]) => {\n  for (const loader of loaders) {\n    layoutAlgorithms[loader.name] = loader;\n  }\n};\n\n// TODO: Should we load dagre without lazy loading?\nconst registerDefaultLayoutLoaders = () => {\n  registerLayoutLoaders([\n    {\n      name: 'dagre',\n      loader: async () => await import('./layout-algorithms/dagre/index.js'),\n    },\n  ]);\n};\n\nregisterDefaultLayoutLoaders();\n\nexport const render = async (data4Layout: LayoutData, svg: SVG) => {\n  if (!(data4Layout.layoutAlgorithm in layoutAlgorithms)) {\n    throw new Error(`Unknown layout algorithm: ${data4Layout.layoutAlgorithm}`);\n  }\n\n  const layoutDefinition = layoutAlgorithms[data4Layout.layoutAlgorithm];\n  const layoutRenderer = await layoutDefinition.loader();\n  return layoutRenderer.render(data4Layout, svg, internalHelpers, {\n    algorithm: layoutDefinition.algorithm,\n  });\n};\n\n/**\n * Get the registered layout algorithm. If the algorithm is not registered, use the fallback algorithm.\n */\nexport const getRegisteredLayoutAlgorithm = (algorithm = '', { fallback = 'dagre' } = {}) => {\n  if (algorithm in layoutAlgorithms) {\n    return algorithm;\n  }\n  if (fallback in layoutAlgorithms) {\n    log.warn(`Layout algorithm ${algorithm} is not registered. Using ${fallback} as fallback.`);\n    return fallback;\n  }\n  throw new Error(`Both layout algorithms ${algorithm} and ${fallback} are not registered.`);\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAkBO,IAAM,kBAAkB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACJA,IAAM,mBAA2D,CAAC;AAE3D,IAAM,wBAAwB,wBAAC,YAAsC;AAC1E,aAAW,UAAU,SAAS;AAC5B,qBAAiB,OAAO,IAAI,IAAI;AAAA,EAClC;AACF,GAJqC;AAOrC,IAAM,+BAA+B,6BAAM;AACzC,wBAAsB;AAAA,IACpB;AAAA,MACE,MAAM;AAAA,MACN,QAAQ,mCAAY,MAAM,OAAO,sBAAoC,GAA7D;AAAA,IACV;AAAA,EACF,CAAC;AACH,GAPqC;AASrC,6BAA6B;AAEtB,IAAM,SAAS,8BAAO,aAAyB,QAAa;AACjE,MAAI,EAAE,YAAY,mBAAmB,mBAAmB;AACtD,UAAM,IAAI,MAAM,6BAA6B,YAAY,eAAe,EAAE;AAAA,EAC5E;AAEA,QAAM,mBAAmB,iBAAiB,YAAY,eAAe;AACrE,QAAM,iBAAiB,MAAM,iBAAiB,OAAO;AACrD,SAAO,eAAe,OAAO,aAAa,KAAK,iBAAiB;AAAA,IAC9D,WAAW,iBAAiB;AAAA,EAC9B,CAAC;AACH,GAVsB;AAef,IAAM,+BAA+B,wBAAC,YAAY,IAAI,EAAE,WAAW,QAAQ,IAAI,CAAC,MAAM;AAC3F,MAAI,aAAa,kBAAkB;AACjC,WAAO;AAAA,EACT;AACA,MAAI,YAAY,kBAAkB;AAChC,QAAI,KAAK,oBAAoB,SAAS,6BAA6B,QAAQ,eAAe;AAC1F,WAAO;AAAA,EACT;AACA,QAAM,IAAI,MAAM,0BAA0B,SAAS,QAAQ,QAAQ,sBAAsB;AAC3F,GAT4C;", "names": []}