{"rules": {"arrow-parens": true, "class-name": true, "indent": [true, "spaces", 2], "prefer-const": true, "no-duplicate-variable": true, "no-eval": true, "no-internal-module": true, "no-trailing-whitespace": false, "no-var-keyword": true, "one-line": [true, "check-open-brace", "check-whitespace"], "quotemark": [true, "single", "avoid-escape"], "semicolon": [true, "always"], "trailing-comma": [true, "multiline"], "triple-equals": [true, "allow-null-check"], "typedef-whitespace": [true, {"call-signature": "nospace", "index-signature": "nospace", "parameter": "nospace", "property-declaration": "nospace", "variable-declaration": "nospace"}], "variable-name": [true, "ban-keywords"], "whitespace": [true, "check-branch", "check-decl", "check-operator", "check-separator", "check-type"]}}