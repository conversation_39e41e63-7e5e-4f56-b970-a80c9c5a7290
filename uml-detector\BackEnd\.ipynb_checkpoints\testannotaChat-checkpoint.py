import cv2
import matplotlib.pyplot as plt

# Charger l'image
image_path = "2.png"  # Chemin de ton image
annotation_path = "2.txt"  # Fichier contenant les annotations YOLO

# Charger l'image avec OpenCV
image = cv2.imread(image_path)
height, width, _ = image.shape

# Lire les annotations
with open(annotation_path, "r") as file:
    lines = file.readlines()

for line in lines:
    elements = line.strip().split()
    class_id = int(elements[0])  # ID de la classe
    x_center = float(elements[1]) * width
    y_center = float(elements[2]) * height
    box_width = float(elements[3]) * width
    box_height = float(elements[4]) * height

    # Calculer les coins du rectangle
    x1 = int(x_center - box_width / 2)
    y1 = int(y_center - box_height / 2)
    x2 = int(x_center + box_width / 2)
    y2 = int(y_center + box_height / 2)

    # Dessiner les bounding boxes
    color = (0, 255, 0) if class_id == 1 else (255, 0, 0) if class_id == 0 else (0, 0, 255)
    cv2.rectangle(image, (x1, y1), (x2, y2), color, 2)

# Afficher l'image annotée
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
plt.axis("off")
plt.show()
