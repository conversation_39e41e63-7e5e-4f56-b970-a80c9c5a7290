{"extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended"], "plugins": ["@typescript-eslint"], "rules": {"arrow-parens": ["error", "always"], "prefer-const": "error", "no-eval": "error", "no-trailing-spaces": "error", "no-var": "error", "quotes": ["error", "single", {"allowTemplateLiterals": true}], "semi": "error", "comma-dangle": ["error", "always-multiline"], "eqeqeq": "error", "no-useless-escape": "off", "@typescript-eslint/indent": ["error", 2], "@typescript-eslint/no-inferrable-types": "off", "@typescript-eslint/no-unused-vars": "error"}}