=== DÉMARRAGE DÉTECTION ===
Exécution du modèle 1 (PT)...
Résultats modèle 1: 1 éléments traités
Modèle 1: 2 boîtes détectées
  Détection: class, confiance: 0.94
  [OK] Classe acceptée avec confiance 0.94 >= 0.25
  Détection: arrow, confiance: 0.91
  [OK] Flèche acceptée avec confiance 0.91 >= 0.4

Exécution du modèle 2 (ONNX)...
Passage de l'image brute (numpy array) au modèle ONNX...
Résultats modèle 2: 1 éléments traités
  Nombre de détections: 2
  Classe détectée: endpoin
  Classe détectée: endpoin
Modèle 2: 2 boîtes détectées
  Box 0: coords=386,297,426,336 class_idx=3 conf=0.43
  Détection: endpoin, confiance: 0.43
  Box 1: coords=82,332,122,372 class_idx=3 conf=0.37
  Détection: endpoin, confiance: 0.37

Traitement des relations entre classes...

Résumé des détections:
  Modèle 1: {'class': 1, 'arrow': 1}
  Modèle 2: {'endpoin': 2}

=== FIN DÉTECTION ===
