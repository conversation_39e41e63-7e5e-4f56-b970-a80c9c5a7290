/******************************************************************************
 * Copyright 2021 TypeFox GmbH
 * This program and the accompanying materials are made available under the
 * terms of the MIT License, which is available in the project root.
 *
 * @module langium
 */
export * from './default-module.js';
export * from './dependency-injection.js';
export * from './service-registry.js';
export * from './services.js';
export * from './syntax-tree.js';
export * from './documentation/index.js';
export * from './languages/index.js';
export * from './parser/index.js';
export * from './references/index.js';
export * from './serializer/index.js';
export * from './utils/index.js';
export * from './validation/index.js';
export * from './workspace/index.js';
import * as GrammarAST from './languages/generated/ast.js';
import type { Grammar } from './languages/generated/ast.js';
export { Grammar, GrammarAST };
//# sourceMappingURL=index.d.ts.map