{"version": 3, "file": "index.esm2017.js", "sources": ["../../src/performance.ts", "../../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  trace,\n  FirebasePerformance,\n  // The PerformanceTrace type has not changed between modular and non-modular packages.\n  PerformanceTrace\n} from '@firebase/performance';\nimport { FirebasePerformance as FirebasePerformanceCompat } from '@firebase/performance-types';\nimport { FirebaseApp, _FirebaseService } from '@firebase/app-compat';\n\nexport class PerformanceCompatImpl\n  implements FirebasePerformanceCompat, _FirebaseService\n{\n  constructor(\n    public app: FirebaseApp,\n    readonly _delegate: FirebasePerformance\n  ) {}\n\n  get instrumentationEnabled(): boolean {\n    return this._delegate.instrumentationEnabled;\n  }\n\n  set instrumentationEnabled(val: boolean) {\n    this._delegate.instrumentationEnabled = val;\n  }\n\n  get dataCollectionEnabled(): boolean {\n    return this._delegate.dataCollectionEnabled;\n  }\n\n  set dataCollectionEnabled(val: boolean) {\n    this._delegate.dataCollectionEnabled = val;\n  }\n\n  trace(traceName: string): PerformanceTrace {\n    return trace(this._delegate, traceName);\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase, { _FirebaseNamespace } from '@firebase/app-compat';\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType\n} from '@firebase/component';\nimport { PerformanceCompatImpl } from './performance';\nimport { name as packageName, version } from '../package.json';\nimport { FirebasePerformance as FirebasePerformanceCompat } from '@firebase/performance-types';\n\nfunction registerPerformanceCompat(firebaseInstance: _FirebaseNamespace): void {\n  firebaseInstance.INTERNAL.registerComponent(\n    new Component(\n      'performance-compat',\n      performanceFactory,\n      ComponentType.PUBLIC\n    )\n  );\n\n  firebaseInstance.registerVersion(packageName, version);\n}\n\nfunction performanceFactory(\n  container: ComponentContainer\n): PerformanceCompatImpl {\n  const app = container.getProvider('app-compat').getImmediate();\n  // The following call will always succeed.\n  const performance = container.getProvider('performance').getImmediate();\n\n  return new PerformanceCompatImpl(app, performance);\n}\n\nregisterPerformanceCompat(firebase as _FirebaseNamespace);\n\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    performance: {\n      (app?: FirebaseApp): FirebasePerformanceCompat;\n    };\n  }\n  interface FirebaseApp {\n    performance(): FirebasePerformanceCompat;\n  }\n}\n"], "names": ["packageName"], "mappings": ";;;;AAAA;;;;;;;;;;;;;;;AAeG;MAWU,qBAAqB,CAAA;IAGhC,WACS,CAAA,GAAgB,EACd,SAA8B,EAAA;QADhC,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;QACd,IAAS,CAAA,SAAA,GAAT,SAAS,CAAqB;KACrC;AAEJ,IAAA,IAAI,sBAAsB,GAAA;AACxB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC;KAC9C;IAED,IAAI,sBAAsB,CAAC,GAAY,EAAA;AACrC,QAAA,IAAI,CAAC,SAAS,CAAC,sBAAsB,GAAG,GAAG,CAAC;KAC7C;AAED,IAAA,IAAI,qBAAqB,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC;KAC7C;IAED,IAAI,qBAAqB,CAAC,GAAY,EAAA;AACpC,QAAA,IAAI,CAAC,SAAS,CAAC,qBAAqB,GAAG,GAAG,CAAC;KAC5C;AAED,IAAA,KAAK,CAAC,SAAiB,EAAA;QACrB,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;KACzC;AACF;;;;;ACrDD;;;;;;;;;;;;;;;AAeG;AAYH,SAAS,yBAAyB,CAAC,gBAAoC,EAAA;AACrE,IAAA,gBAAgB,CAAC,QAAQ,CAAC,iBAAiB,CACzC,IAAI,SAAS,CACX,oBAAoB,EACpB,kBAAkB,EAAA,QAAA,4BAEnB,CACF,CAAC;AAEF,IAAA,gBAAgB,CAAC,eAAe,CAACA,IAAW,EAAE,OAAO,CAAC,CAAC;AACzD,CAAC;AAED,SAAS,kBAAkB,CACzB,SAA6B,EAAA;IAE7B,MAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,YAAY,EAAE,CAAC;;IAE/D,MAAM,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,YAAY,EAAE,CAAC;AAExE,IAAA,OAAO,IAAI,qBAAqB,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;AACrD,CAAC;AAED,yBAAyB,CAAC,QAA8B,CAAC"}