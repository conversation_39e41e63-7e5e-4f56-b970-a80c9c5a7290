{"version": 3, "sources": ["../../../src/diagrams/timeline/parser/timeline.jison", "../../../src/diagrams/timeline/timelineDb.js", "../../../src/diagrams/timeline/svgDraw.js", "../../../src/diagrams/timeline/timelineRenderer.ts", "../../../src/diagrams/timeline/styles.js", "../../../src/diagrams/timeline/timeline-definition.ts"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[6,8,10,11,12,14,16,17,20,21],$V1=[1,9],$V2=[1,10],$V3=[1,11],$V4=[1,12],$V5=[1,13],$V6=[1,16],$V7=[1,17];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"start\":3,\"timeline\":4,\"document\":5,\"EOF\":6,\"line\":7,\"SPACE\":8,\"statement\":9,\"NEWLINE\":10,\"title\":11,\"acc_title\":12,\"acc_title_value\":13,\"acc_descr\":14,\"acc_descr_value\":15,\"acc_descr_multiline_value\":16,\"section\":17,\"period_statement\":18,\"event_statement\":19,\"period\":20,\"event\":21,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",4:\"timeline\",6:\"EOF\",8:\"SPACE\",10:\"NEWLINE\",11:\"title\",12:\"acc_title\",13:\"acc_title_value\",14:\"acc_descr\",15:\"acc_descr_value\",16:\"acc_descr_multiline_value\",17:\"section\",20:\"period\",21:\"event\"},\nproductions_: [0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[9,1],[9,2],[9,2],[9,1],[9,1],[9,1],[9,1],[18,1],[19,1]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 1:\n return $$[$0-1]; \nbreak;\ncase 2:\n this.$ = [] \nbreak;\ncase 3:\n$$[$0-1].push($$[$0]);this.$ = $$[$0-1]\nbreak;\ncase 4: case 5:\n this.$ = $$[$0] \nbreak;\ncase 6: case 7:\n this.$=[];\nbreak;\ncase 8:\nyy.getCommonDb().setDiagramTitle($$[$0].substr(6));this.$=$$[$0].substr(6);\nbreak;\ncase 9:\n this.$=$$[$0].trim();yy.getCommonDb().setAccTitle(this.$); \nbreak;\ncase 10: case 11:\n this.$=$$[$0].trim();yy.getCommonDb().setAccDescription(this.$); \nbreak;\ncase 12:\nyy.addSection($$[$0].substr(8));this.$=$$[$0].substr(8);\nbreak;\ncase 15:\nyy.addTask($$[$0],0,'');this.$=$$[$0];\nbreak;\ncase 16:\nyy.addEvent($$[$0].substr(2));this.$=$$[$0];\nbreak;\n}\n},\ntable: [{3:1,4:[1,2]},{1:[3]},o($V0,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:$V1,12:$V2,14:$V3,16:$V4,17:$V5,18:14,19:15,20:$V6,21:$V7},o($V0,[2,7],{1:[2,1]}),o($V0,[2,3]),{9:18,11:$V1,12:$V2,14:$V3,16:$V4,17:$V5,18:14,19:15,20:$V6,21:$V7},o($V0,[2,5]),o($V0,[2,6]),o($V0,[2,8]),{13:[1,19]},{15:[1,20]},o($V0,[2,11]),o($V0,[2,12]),o($V0,[2,13]),o($V0,[2,14]),o($V0,[2,15]),o($V0,[2,16]),o($V0,[2,4]),o($V0,[2,9]),o($V0,[2,10])],\ndefaultActions: {},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {\"case-insensitive\":true},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0:/* skip comments */\nbreak;\ncase 1:/* skip comments */\nbreak;\ncase 2:return 10;\nbreak;\ncase 3:/* skip whitespace */\nbreak;\ncase 4:/* skip comments */\nbreak;\ncase 5:return 4;\nbreak;\ncase 6:return 11;\nbreak;\ncase 7: this.begin(\"acc_title\");return 12; \nbreak;\ncase 8: this.popState(); return \"acc_title_value\"; \nbreak;\ncase 9: this.begin(\"acc_descr\");return 14; \nbreak;\ncase 10: this.popState(); return \"acc_descr_value\"; \nbreak;\ncase 11: this.begin(\"acc_descr_multiline\");\nbreak;\ncase 12: this.popState(); \nbreak;\ncase 13:return \"acc_descr_multiline_value\";\nbreak;\ncase 14:return 17;\nbreak;\ncase 15:return 21;\nbreak;\ncase 16:return 20;\nbreak;\ncase 17:return 6;\nbreak;\ncase 18:return 'INVALID';\nbreak;\n}\n},\nrules: [/^(?:%(?!\\{)[^\\n]*)/i,/^(?:[^\\}]%%[^\\n]*)/i,/^(?:[\\n]+)/i,/^(?:\\s+)/i,/^(?:#[^\\n]*)/i,/^(?:timeline\\b)/i,/^(?:title\\s[^\\n]+)/i,/^(?:accTitle\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*\\{\\s*)/i,/^(?:[\\}])/i,/^(?:[^\\}]*)/i,/^(?:section\\s[^:\\n]+)/i,/^(?::\\s[^:\\n]+)/i,/^(?:[^#:\\n]+)/i,/^(?:$)/i,/^(?:.)/i],\nconditions: {\"acc_descr_multiline\":{\"rules\":[12,13],\"inclusive\":false},\"acc_descr\":{\"rules\":[10],\"inclusive\":false},\"acc_title\":{\"rules\":[8],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,2,3,4,5,6,7,9,11,14,15,16,17,18],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "import * as commonDb from '../common/commonDb.js';\nlet currentSection = '';\nlet currentTaskId = 0;\n\nconst sections = [];\nconst tasks = [];\nconst rawTasks = [];\n\nexport const getCommonDb = () => commonDb;\n\nexport const clear = function () {\n  sections.length = 0;\n  tasks.length = 0;\n  currentSection = '';\n  rawTasks.length = 0;\n  commonDb.clear();\n};\n\nexport const addSection = function (txt) {\n  currentSection = txt;\n  sections.push(txt);\n};\n\nexport const getSections = function () {\n  return sections;\n};\n\nexport const getTasks = function () {\n  let allItemsProcessed = compileTasks();\n  const maxDepth = 100;\n  let iterationCount = 0;\n  while (!allItemsProcessed && iterationCount < maxDepth) {\n    allItemsProcessed = compileTasks();\n    iterationCount++;\n  }\n\n  tasks.push(...rawTasks);\n\n  return tasks;\n};\n\nexport const addTask = function (period, length, event) {\n  const rawTask = {\n    id: currentTaskId++,\n    section: currentSection,\n    type: currentSection,\n    task: period,\n    score: length ? length : 0,\n    //if event is defined, then add it the events array\n    events: event ? [event] : [],\n  };\n  rawTasks.push(rawTask);\n};\n\nexport const addEvent = function (event) {\n  // fetch current task with currentTaskId\n  const currentTask = rawTasks.find((task) => task.id === currentTaskId - 1);\n  //add event to the events array\n  currentTask.events.push(event);\n};\n\nexport const addTaskOrg = function (descr) {\n  const newTask = {\n    section: currentSection,\n    type: currentSection,\n    description: descr,\n    task: descr,\n    classes: [],\n  };\n  tasks.push(newTask);\n};\n\n/**\n * Compiles the raw tasks into a list of tasks with events\n * @returns {boolean} true if all items are processed\n * @private\n * @memberof timelineDb\n */\nconst compileTasks = function () {\n  const compileTask = function (pos) {\n    return rawTasks[pos].processed;\n  };\n\n  let allProcessed = true;\n  for (const [i, rawTask] of rawTasks.entries()) {\n    compileTask(i);\n\n    allProcessed = allProcessed && rawTask.processed;\n  }\n  return allProcessed;\n};\n\nexport default {\n  clear,\n  getCommonDb,\n  addSection,\n  getSections,\n  getTasks,\n  addTask,\n  addTaskOrg,\n  addEvent,\n};\n", "import { arc as d3arc, select } from 'd3';\nconst MAX_SECTIONS = 12;\n\nexport const drawRect = function (elem, rectData) {\n  const rectElem = elem.append('rect');\n  rectElem.attr('x', rectData.x);\n  rectElem.attr('y', rectData.y);\n  rectElem.attr('fill', rectData.fill);\n  rectElem.attr('stroke', rectData.stroke);\n  rectElem.attr('width', rectData.width);\n  rectElem.attr('height', rectData.height);\n  rectElem.attr('rx', rectData.rx);\n  rectElem.attr('ry', rectData.ry);\n\n  if (rectData.class !== undefined) {\n    rectElem.attr('class', rectData.class);\n  }\n\n  return rectElem;\n};\n\nexport const drawFace = function (element, faceData) {\n  const radius = 15;\n  const circleElement = element\n    .append('circle')\n    .attr('cx', faceData.cx)\n    .attr('cy', faceData.cy)\n    .attr('class', 'face')\n    .attr('r', radius)\n    .attr('stroke-width', 2)\n    .attr('overflow', 'visible');\n\n  const face = element.append('g');\n\n  //left eye\n  face\n    .append('circle')\n    .attr('cx', faceData.cx - radius / 3)\n    .attr('cy', faceData.cy - radius / 3)\n    .attr('r', 1.5)\n    .attr('stroke-width', 2)\n    .attr('fill', '#666')\n    .attr('stroke', '#666');\n\n  //right eye\n  face\n    .append('circle')\n    .attr('cx', faceData.cx + radius / 3)\n    .attr('cy', faceData.cy - radius / 3)\n    .attr('r', 1.5)\n    .attr('stroke-width', 2)\n    .attr('fill', '#666')\n    .attr('stroke', '#666');\n\n  /** @param {any} face */\n  function smile(face) {\n    const arc = d3arc()\n      .startAngle(Math.PI / 2)\n      .endAngle(3 * (Math.PI / 2))\n      .innerRadius(radius / 2)\n      .outerRadius(radius / 2.2);\n    //mouth\n    face\n      .append('path')\n      .attr('class', 'mouth')\n      .attr('d', arc)\n      .attr('transform', 'translate(' + faceData.cx + ',' + (faceData.cy + 2) + ')');\n  }\n\n  /** @param {any} face */\n  function sad(face) {\n    const arc = d3arc()\n      .startAngle((3 * Math.PI) / 2)\n      .endAngle(5 * (Math.PI / 2))\n      .innerRadius(radius / 2)\n      .outerRadius(radius / 2.2);\n    //mouth\n    face\n      .append('path')\n      .attr('class', 'mouth')\n      .attr('d', arc)\n      .attr('transform', 'translate(' + faceData.cx + ',' + (faceData.cy + 7) + ')');\n  }\n\n  /** @param {any} face */\n  function ambivalent(face) {\n    face\n      .append('line')\n      .attr('class', 'mouth')\n      .attr('stroke', 2)\n      .attr('x1', faceData.cx - 5)\n      .attr('y1', faceData.cy + 7)\n      .attr('x2', faceData.cx + 5)\n      .attr('y2', faceData.cy + 7)\n      .attr('class', 'mouth')\n      .attr('stroke-width', '1px')\n      .attr('stroke', '#666');\n  }\n\n  if (faceData.score > 3) {\n    smile(face);\n  } else if (faceData.score < 3) {\n    sad(face);\n  } else {\n    ambivalent(face);\n  }\n\n  return circleElement;\n};\n\nexport const drawCircle = function (element, circleData) {\n  const circleElement = element.append('circle');\n  circleElement.attr('cx', circleData.cx);\n  circleElement.attr('cy', circleData.cy);\n  circleElement.attr('class', 'actor-' + circleData.pos);\n  circleElement.attr('fill', circleData.fill);\n  circleElement.attr('stroke', circleData.stroke);\n  circleElement.attr('r', circleData.r);\n\n  if (circleElement.class !== undefined) {\n    circleElement.attr('class', circleElement.class);\n  }\n\n  if (circleData.title !== undefined) {\n    circleElement.append('title').text(circleData.title);\n  }\n\n  return circleElement;\n};\n\nexport const drawText = function (elem, textData) {\n  // Remove and ignore br:s\n  const nText = textData.text.replace(/<br\\s*\\/?>/gi, ' ');\n\n  const textElem = elem.append('text');\n  textElem.attr('x', textData.x);\n  textElem.attr('y', textData.y);\n  textElem.attr('class', 'legend');\n\n  textElem.style('text-anchor', textData.anchor);\n\n  if (textData.class !== undefined) {\n    textElem.attr('class', textData.class);\n  }\n\n  const span = textElem.append('tspan');\n  span.attr('x', textData.x + textData.textMargin * 2);\n  span.text(nText);\n\n  return textElem;\n};\n\nexport const drawLabel = function (elem, txtObject) {\n  /**\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} cut\n   */\n  function genPoints(x, y, width, height, cut) {\n    return (\n      x +\n      ',' +\n      y +\n      ' ' +\n      (x + width) +\n      ',' +\n      y +\n      ' ' +\n      (x + width) +\n      ',' +\n      (y + height - cut) +\n      ' ' +\n      (x + width - cut * 1.2) +\n      ',' +\n      (y + height) +\n      ' ' +\n      x +\n      ',' +\n      (y + height)\n    );\n  }\n  const polygon = elem.append('polygon');\n  polygon.attr('points', genPoints(txtObject.x, txtObject.y, 50, 20, 7));\n  polygon.attr('class', 'labelBox');\n\n  txtObject.y = txtObject.y + txtObject.labelMargin;\n  txtObject.x = txtObject.x + 0.5 * txtObject.labelMargin;\n  drawText(elem, txtObject);\n};\n\nexport const drawSection = function (elem, section, conf) {\n  const g = elem.append('g');\n\n  const rect = getNoteRect();\n  rect.x = section.x;\n  rect.y = section.y;\n  rect.fill = section.fill;\n  rect.width = conf.width;\n  rect.height = conf.height;\n  rect.class = 'journey-section section-type-' + section.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect(g, rect);\n\n  _drawTextCandidateFunc(conf)(\n    section.text,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: 'journey-section section-type-' + section.num },\n    conf,\n    section.colour\n  );\n};\n\nlet taskCount = -1;\n/**\n * Draws an actor in the diagram with the attached line\n *\n * @param {any} elem The HTML element\n * @param {any} task The task to render\n * @param {any} conf The global configuration\n */\nexport const drawTask = function (elem, task, conf) {\n  const center = task.x + conf.width / 2;\n  const g = elem.append('g');\n  taskCount++;\n  const maxHeight = 300 + 5 * 30;\n  g.append('line')\n    .attr('id', 'task' + taskCount)\n    .attr('x1', center)\n    .attr('y1', task.y)\n    .attr('x2', center)\n    .attr('y2', maxHeight)\n    .attr('class', 'task-line')\n    .attr('stroke-width', '1px')\n    .attr('stroke-dasharray', '4 2')\n    .attr('stroke', '#666');\n\n  drawFace(g, {\n    cx: center,\n    cy: 300 + (5 - task.score) * 30,\n    score: task.score,\n  });\n\n  const rect = getNoteRect();\n  rect.x = task.x;\n  rect.y = task.y;\n  rect.fill = task.fill;\n  rect.width = conf.width;\n  rect.height = conf.height;\n  rect.class = 'task task-type-' + task.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect(g, rect);\n\n  _drawTextCandidateFunc(conf)(\n    task.task,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: 'task' },\n    conf,\n    task.colour\n  );\n};\n\n/**\n * Draws a background rectangle\n *\n * @param {any} elem The html element\n * @param {any} bounds The bounds of the drawing\n */\nexport const drawBackgroundRect = function (elem, bounds) {\n  const rectElem = drawRect(elem, {\n    x: bounds.startx,\n    y: bounds.starty,\n    width: bounds.stopx - bounds.startx,\n    height: bounds.stopy - bounds.starty,\n    fill: bounds.fill,\n    class: 'rect',\n  });\n  rectElem.lower();\n};\n\nexport const getTextObj = function () {\n  return {\n    x: 0,\n    y: 0,\n    fill: undefined,\n    'text-anchor': 'start',\n    width: 100,\n    height: 100,\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n  };\n};\n\nexport const getNoteRect = function () {\n  return {\n    x: 0,\n    y: 0,\n    width: 100,\n    anchor: 'start',\n    height: 100,\n    rx: 0,\n    ry: 0,\n  };\n};\n\nconst _drawTextCandidateFunc = (function () {\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} colour\n   */\n  function byText(content, g, x, y, width, height, textAttrs, colour) {\n    const text = g\n      .append('text')\n      .attr('x', x + width / 2)\n      .attr('y', y + height / 2 + 5)\n      .style('font-color', colour)\n      .style('text-anchor', 'middle')\n      .text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} conf\n   * @param {any} colour\n   */\n  function byTspan(content, g, x, y, width, height, textAttrs, conf, colour) {\n    const { taskFontSize, taskFontFamily } = conf;\n\n    const lines = content.split(/<br\\s*\\/?>/gi);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * taskFontSize - (taskFontSize * (lines.length - 1)) / 2;\n      const text = g\n        .append('text')\n        .attr('x', x + width / 2)\n        .attr('y', y)\n        .attr('fill', colour)\n        .style('text-anchor', 'middle')\n        .style('font-size', taskFontSize)\n        .style('font-family', taskFontFamily);\n      text\n        .append('tspan')\n        .attr('x', x + width / 2)\n        .attr('dy', dy)\n        .text(lines[i]);\n\n      text\n        .attr('y', y + height / 2.0)\n        .attr('dominant-baseline', 'central')\n        .attr('alignment-baseline', 'central');\n\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} conf\n   */\n  function byFo(content, g, x, y, width, height, textAttrs, conf) {\n    const body = g.append('switch');\n    const f = body\n      .append('foreignObject')\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', width)\n      .attr('height', height)\n      .attr('position', 'fixed');\n\n    const text = f\n      .append('xhtml:div')\n      .style('display', 'table')\n      .style('height', '100%')\n      .style('width', '100%');\n\n    text\n      .append('div')\n      .attr('class', 'label')\n      .style('display', 'table-cell')\n      .style('text-align', 'center')\n      .style('vertical-align', 'middle')\n      .text(content);\n\n    byTspan(content, body, x, y, width, height, textAttrs, conf);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   * @param {any} toText\n   * @param {any} fromTextAttrsDict\n   */\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (key in fromTextAttrsDict) {\n        // noinspection JSUnfilteredForInLoop\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n\n  return function (conf) {\n    return conf.textPlacement === 'fo' ? byFo : conf.textPlacement === 'old' ? byText : byTspan;\n  };\n})();\n\nconst initGraphics = function (graphics) {\n  graphics\n    .append('defs')\n    .append('marker')\n    .attr('id', 'arrowhead')\n    .attr('refX', 5)\n    .attr('refY', 2)\n    .attr('markerWidth', 6)\n    .attr('markerHeight', 4)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 0,0 V 4 L6,2 Z'); // this is actual shape for arrowhead\n};\n\n/**\n * @param {string} text The text to be wrapped\n * @param {number} width The max width of the text\n */\nfunction wrap(text, width) {\n  text.each(function () {\n    var text = select(this),\n      words = text\n        .text()\n        .split(/(\\s+|<br>)/)\n        .reverse(),\n      word,\n      line = [],\n      lineHeight = 1.1, // ems\n      y = text.attr('y'),\n      dy = parseFloat(text.attr('dy')),\n      tspan = text\n        .text(null)\n        .append('tspan')\n        .attr('x', 0)\n        .attr('y', y)\n        .attr('dy', dy + 'em');\n    for (let j = 0; j < words.length; j++) {\n      word = words[words.length - 1 - j];\n      line.push(word);\n      tspan.text(line.join(' ').trim());\n      if (tspan.node().getComputedTextLength() > width || word === '<br>') {\n        line.pop();\n        tspan.text(line.join(' ').trim());\n        if (word === '<br>') {\n          line = [''];\n        } else {\n          line = [word];\n        }\n\n        tspan = text\n          .append('tspan')\n          .attr('x', 0)\n          .attr('y', y)\n          .attr('dy', lineHeight + 'em')\n          .text(word);\n      }\n    }\n  });\n}\n\nexport const drawNode = function (elem, node, fullSection, conf) {\n  const section = (fullSection % MAX_SECTIONS) - 1;\n  const nodeElem = elem.append('g');\n  node.section = section;\n  nodeElem.attr(\n    'class',\n    (node.class ? node.class + ' ' : '') + 'timeline-node ' + ('section-' + section)\n  );\n  const bkgElem = nodeElem.append('g');\n\n  // Create the wrapped text element\n  const textElem = nodeElem.append('g');\n\n  const txt = textElem\n    .append('text')\n    .text(node.descr)\n    .attr('dy', '1em')\n    .attr('alignment-baseline', 'middle')\n    .attr('dominant-baseline', 'middle')\n    .attr('text-anchor', 'middle')\n    .call(wrap, node.width);\n  const bbox = txt.node().getBBox();\n  const fontSize = conf.fontSize?.replace ? conf.fontSize.replace('px', '') : conf.fontSize;\n  node.height = bbox.height + fontSize * 1.1 * 0.5 + node.padding;\n  node.height = Math.max(node.height, node.maxHeight);\n  node.width = node.width + 2 * node.padding;\n\n  textElem.attr('transform', 'translate(' + node.width / 2 + ', ' + node.padding / 2 + ')');\n\n  // Create the background element\n  defaultBkg(bkgElem, node, section, conf);\n\n  return node;\n};\n\nexport const getVirtualNodeHeight = function (elem, node, conf) {\n  const textElem = elem.append('g');\n  const txt = textElem\n    .append('text')\n    .text(node.descr)\n    .attr('dy', '1em')\n    .attr('alignment-baseline', 'middle')\n    .attr('dominant-baseline', 'middle')\n    .attr('text-anchor', 'middle')\n    .call(wrap, node.width);\n  const bbox = txt.node().getBBox();\n  const fontSize = conf.fontSize?.replace ? conf.fontSize.replace('px', '') : conf.fontSize;\n  textElem.remove();\n  return bbox.height + fontSize * 1.1 * 0.5 + node.padding;\n};\n\nconst defaultBkg = function (elem, node, section) {\n  const rd = 5;\n  elem\n    .append('path')\n    .attr('id', 'node-' + node.id)\n    .attr('class', 'node-bkg node-' + node.type)\n    .attr(\n      'd',\n      `M0 ${node.height - rd} v${-node.height + 2 * rd} q0,-5 5,-5 h${\n        node.width - 2 * rd\n      } q5,0 5,5 v${node.height - rd} H0 Z`\n    );\n\n  elem\n    .append('line')\n    .attr('class', 'node-line-' + section)\n    .attr('x1', 0)\n    .attr('y1', node.height)\n    .attr('x2', node.width)\n    .attr('y2', node.height);\n};\n\nexport default {\n  drawRect,\n  drawCircle,\n  drawSection,\n  drawText,\n  drawLabel,\n  drawTask,\n  drawBackgroundRect,\n  getTextObj,\n  getNoteRect,\n  initGraphics,\n  drawNode,\n  getVirtualNodeHeight,\n};\n", "// @ts-nocheck - don't check until handle it\nimport type { Selection } from 'd3';\nimport { select } from 'd3';\nimport svgDraw from './svgDraw.js';\nimport { log } from '../../logger.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { setupGraphViewbox } from '../../setupGraphViewbox.js';\nimport type { Diagram } from '../../Diagram.js';\nimport type { MermaidConfig } from '../../config.type.js';\n\ninterface Block<TDesc, TSection> {\n  number: number;\n  descr: TDesc;\n  section: TSection;\n  width: number;\n  padding: number;\n  maxHeight: number;\n}\n\ninterface TimelineTask {\n  id: number;\n  section: string;\n  type: string;\n  task: string;\n  score: number;\n  events: string[];\n}\nexport const draw = function (text: string, id: string, version: string, diagObj: Diagram) {\n  //1. Fetch the configuration\n  const conf = getConfig();\n  // @ts-expect-error - wrong config?\n  const LEFT_MARGIN = conf.leftMargin ?? 50;\n\n  log.debug('timeline', diagObj.db);\n\n  const securityLevel = conf.securityLevel;\n  // Handle root and Document for when rendering in sandbox mode\n  let sandboxElement;\n  if (securityLevel === 'sandbox') {\n    sandboxElement = select('#i' + id);\n  }\n  const root =\n    securityLevel === 'sandbox'\n      ? select(sandboxElement.nodes()[0].contentDocument.body)\n      : select('body');\n\n  const svg = root.select('#' + id);\n\n  svg.append('g');\n\n  //4. Fetch the diagram data\n  // @ts-expect-error - db not typed yet\n  const tasks: TimelineTask[] = diagObj.db.getTasks();\n  // @ts-expect-error - db not typed yet\n  const title = diagObj.db.getCommonDb().getDiagramTitle();\n  log.debug('task', tasks);\n\n  //5. Initialize the diagram\n  svgDraw.initGraphics(svg);\n\n  // fetch Sections\n  // @ts-expect-error - db not typed yet\n  const sections: string[] = diagObj.db.getSections();\n  log.debug('sections', sections);\n\n  let maxSectionHeight = 0;\n  let maxTaskHeight = 0;\n  //let sectionBeginX = 0;\n  let depthY = 0;\n  let sectionBeginY = 0;\n  let masterX = 50 + LEFT_MARGIN;\n  //sectionBeginX = masterX;\n  let masterY = 50;\n  sectionBeginY = 50;\n  //draw sections\n  let sectionNumber = 0;\n  let hasSections = true;\n\n  //Calculate the max height of the sections\n  sections.forEach(function (section: string) {\n    const sectionNode: Block<string, number> = {\n      number: sectionNumber,\n      descr: section,\n      section: sectionNumber,\n      width: 150,\n      padding: 20,\n      maxHeight: maxSectionHeight,\n    };\n    const sectionHeight = svgDraw.getVirtualNodeHeight(svg, sectionNode, conf);\n    log.debug('sectionHeight before draw', sectionHeight);\n    maxSectionHeight = Math.max(maxSectionHeight, sectionHeight + 20);\n  });\n\n  //tasks length and maxEventCount\n  let maxEventCount = 0;\n  let maxEventLineLength = 0;\n  log.debug('tasks.length', tasks.length);\n  //calculate max task height\n  // for loop till tasks.length\n\n  for (const [i, task] of tasks.entries()) {\n    const taskNode: Block<TimelineTask, string> = {\n      number: i,\n      descr: task,\n      section: task.section,\n      width: 150,\n      padding: 20,\n      maxHeight: maxTaskHeight,\n    };\n    const taskHeight = svgDraw.getVirtualNodeHeight(svg, taskNode, conf);\n    log.debug('taskHeight before draw', taskHeight);\n    maxTaskHeight = Math.max(maxTaskHeight, taskHeight + 20);\n\n    //calculate maxEventCount\n    maxEventCount = Math.max(maxEventCount, task.events.length);\n    //calculate maxEventLineLength\n    let maxEventLineLengthTemp = 0;\n    for (const event of task.events) {\n      const eventNode = {\n        descr: event,\n        section: task.section,\n        number: task.section,\n        width: 150,\n        padding: 20,\n        maxHeight: 50,\n      };\n      maxEventLineLengthTemp += svgDraw.getVirtualNodeHeight(svg, eventNode, conf);\n    }\n    maxEventLineLength = Math.max(maxEventLineLength, maxEventLineLengthTemp);\n  }\n\n  log.debug('maxSectionHeight before draw', maxSectionHeight);\n  log.debug('maxTaskHeight before draw', maxTaskHeight);\n\n  if (sections && sections.length > 0) {\n    sections.forEach((section) => {\n      //filter task where tasks.section == section\n      const tasksForSection = tasks.filter((task) => task.section === section);\n\n      const sectionNode: Block<string, number> = {\n        number: sectionNumber,\n        descr: section,\n        section: sectionNumber,\n        width: 200 * Math.max(tasksForSection.length, 1) - 50,\n        padding: 20,\n        maxHeight: maxSectionHeight,\n      };\n      log.debug('sectionNode', sectionNode);\n      const sectionNodeWrapper = svg.append('g');\n      const node = svgDraw.drawNode(sectionNodeWrapper, sectionNode, sectionNumber, conf);\n      log.debug('sectionNode output', node);\n\n      sectionNodeWrapper.attr('transform', `translate(${masterX}, ${sectionBeginY})`);\n\n      masterY += maxSectionHeight + 50;\n\n      //draw tasks for this section\n      if (tasksForSection.length > 0) {\n        drawTasks(\n          svg,\n          tasksForSection,\n          sectionNumber,\n          masterX,\n          masterY,\n          maxTaskHeight,\n          conf,\n          maxEventCount,\n          maxEventLineLength,\n          maxSectionHeight,\n          false\n        );\n      }\n      // todo replace with total width of section and its tasks\n      masterX += 200 * Math.max(tasksForSection.length, 1);\n\n      masterY = sectionBeginY;\n      sectionNumber++;\n    });\n  } else {\n    //draw tasks\n    hasSections = false;\n    drawTasks(\n      svg,\n      tasks,\n      sectionNumber,\n      masterX,\n      masterY,\n      maxTaskHeight,\n      conf,\n      maxEventCount,\n      maxEventLineLength,\n      maxSectionHeight,\n      true\n    );\n  }\n\n  // Get BBox of the diagram\n  const box = svg.node().getBBox();\n  log.debug('bounds', box);\n\n  if (title) {\n    svg\n      .append('text')\n      .text(title)\n      .attr('x', box.width / 2 - LEFT_MARGIN)\n      .attr('font-size', '4ex')\n      .attr('font-weight', 'bold')\n      .attr('y', 20);\n  }\n  //5. Draw the diagram\n  depthY = hasSections ? maxSectionHeight + maxTaskHeight + 150 : maxTaskHeight + 100;\n\n  const lineWrapper = svg.append('g').attr('class', 'lineWrapper');\n  // Draw activity line\n  lineWrapper\n    .append('line')\n    .attr('x1', LEFT_MARGIN)\n    .attr('y1', depthY) // One section head + one task + margins\n    .attr('x2', box.width + 3 * LEFT_MARGIN) // Subtract stroke width so arrow point is retained\n    .attr('y2', depthY)\n    .attr('stroke-width', 4)\n    .attr('stroke', 'black')\n    .attr('marker-end', 'url(#arrowhead)');\n\n  // Setup the view box and size of the svg element\n  setupGraphViewbox(\n    undefined,\n    svg,\n    conf.timeline?.padding ?? 50,\n    conf.timeline?.useMaxWidth ?? false\n  );\n\n  // addSVGAccessibilityFields(diagObj.db, diagram, id);\n};\n\nexport const drawTasks = function (\n  diagram: Selection<SVGElement, unknown, null, undefined>,\n  tasks: TimelineTask[],\n  sectionColor: number,\n  masterX: number,\n  masterY: number,\n  maxTaskHeight: number,\n  conf: MermaidConfig,\n  maxEventCount: number,\n  maxEventLineLength: number,\n  maxSectionHeight: number,\n  isWithoutSections: boolean\n) {\n  // Draw the tasks\n  for (const task of tasks) {\n    // create node from task\n    const taskNode = {\n      descr: task.task,\n      section: sectionColor,\n      number: sectionColor,\n      width: 150,\n      padding: 20,\n      maxHeight: maxTaskHeight,\n    };\n\n    log.debug('taskNode', taskNode);\n    // create task wrapper\n\n    const taskWrapper = diagram.append('g').attr('class', 'taskWrapper');\n    const node = svgDraw.drawNode(taskWrapper, taskNode, sectionColor, conf);\n    const taskHeight = node.height;\n    //log task height\n    log.debug('taskHeight after draw', taskHeight);\n    taskWrapper.attr('transform', `translate(${masterX}, ${masterY})`);\n\n    // update max task height\n    maxTaskHeight = Math.max(maxTaskHeight, taskHeight);\n\n    // if task has events, draw them\n    if (task.events) {\n      // draw a line between the task and the events\n      const lineWrapper = diagram.append('g').attr('class', 'lineWrapper');\n      let lineLength = maxTaskHeight;\n      //add margin to task\n      masterY += 100;\n      lineLength =\n        lineLength + drawEvents(diagram, task.events, sectionColor, masterX, masterY, conf);\n      masterY -= 100;\n\n      lineWrapper\n        .append('line')\n        .attr('x1', masterX + 190 / 2)\n        .attr('y1', masterY + maxTaskHeight) // One section head + one task + margins\n        .attr('x2', masterX + 190 / 2) // Subtract stroke width so arrow point is retained\n        .attr(\n          'y2',\n          masterY +\n            maxTaskHeight +\n            (isWithoutSections ? maxTaskHeight : maxSectionHeight) +\n            maxEventLineLength +\n            120\n        )\n        .attr('stroke-width', 2)\n        .attr('stroke', 'black')\n        .attr('marker-end', 'url(#arrowhead)')\n        .attr('stroke-dasharray', '5,5');\n    }\n\n    masterX = masterX + 200;\n    if (isWithoutSections && !conf.timeline?.disableMulticolor) {\n      sectionColor++;\n    }\n  }\n\n  // reset Y coordinate for next section\n  masterY = masterY - 10;\n};\n\nexport const drawEvents = function (\n  diagram: Selection<SVGElement, unknown, null, undefined>,\n  events: string[],\n  sectionColor: number,\n  masterX: number,\n  masterY: number,\n  conf: MermaidConfig\n) {\n  let maxEventHeight = 0;\n  const eventBeginY = masterY;\n  masterY = masterY + 100;\n  // Draw the events\n  for (const event of events) {\n    // create node from event\n    const eventNode: Block<string, number> = {\n      descr: event,\n      section: sectionColor,\n      number: sectionColor,\n      width: 150,\n      padding: 20,\n      maxHeight: 50,\n    };\n\n    //log task node\n    log.debug('eventNode', eventNode);\n    // create event wrapper\n    const eventWrapper = diagram.append('g').attr('class', 'eventWrapper');\n    const node = svgDraw.drawNode(eventWrapper, eventNode, sectionColor, conf);\n    const eventHeight = node.height;\n    maxEventHeight = maxEventHeight + eventHeight;\n    eventWrapper.attr('transform', `translate(${masterX}, ${masterY})`);\n    masterY = masterY + 10 + eventHeight;\n  }\n  // set masterY back to eventBeginY\n  masterY = eventBeginY;\n  return maxEventHeight;\n};\n\nexport default {\n  setConf: () => {\n    // no-op\n  },\n  draw,\n};\n", "import { darken, lighten, isDark } from 'khroma';\n\nconst genSections = (options) => {\n  let sections = '';\n\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    options['lineColor' + i] = options['lineColor' + i] || options['cScaleInv' + i];\n    if (isDark(options['lineColor' + i])) {\n      options['lineColor' + i] = lighten(options['lineColor' + i], 20);\n    } else {\n      options['lineColor' + i] = darken(options['lineColor' + i], 20);\n    }\n  }\n\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    const sw = '' + (17 - 3 * i);\n    sections += `\n    .section-${i - 1} rect, .section-${i - 1} path, .section-${i - 1} circle, .section-${\n      i - 1\n    } path  {\n      fill: ${options['cScale' + i]};\n    }\n    .section-${i - 1} text {\n     fill: ${options['cScaleLabel' + i]};\n    }\n    .node-icon-${i - 1} {\n      font-size: 40px;\n      color: ${options['cScaleLabel' + i]};\n    }\n    .section-edge-${i - 1}{\n      stroke: ${options['cScale' + i]};\n    }\n    .edge-depth-${i - 1}{\n      stroke-width: ${sw};\n    }\n    .section-${i - 1} line {\n      stroke: ${options['cScaleInv' + i]} ;\n      stroke-width: 3;\n    }\n\n    .lineWrapper line{\n      stroke: ${options['cScaleLabel' + i]} ;\n    }\n\n    .disabled, .disabled circle, .disabled text {\n      fill: lightgray;\n    }\n    .disabled text {\n      fill: #efefef;\n    }\n    `;\n  }\n  return sections;\n};\n\nconst getStyles = (options) =>\n  `\n  .edge {\n    stroke-width: 3;\n  }\n  ${genSections(options)}\n  .section-root rect, .section-root path, .section-root circle  {\n    fill: ${options.git0};\n  }\n  .section-root text {\n    fill: ${options.gitBranchLabel0};\n  }\n  .icon-container {\n    height:100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .edge {\n    fill: none;\n  }\n  .eventWrapper  {\n   filter: brightness(120%);\n  }\n`;\nexport default getStyles;\n", "// @ts-ignore: JISON doesn't support types\nimport parser from './parser/timeline.jison';\nimport * as db from './timelineDb.js';\nimport renderer from './timelineRenderer.js';\nimport styles from './styles.js';\n\nexport const diagram = {\n  db,\n  renderer,\n  parser,\n  styles,\n};\n"], "mappings": "kKAyEA,IAAIA,EAAU,UAAU,CACxB,IAAIC,EAAEC,EAAA,SAASC,EAAEC,EAAEH,EAAEI,EAAE,CAAC,IAAIJ,EAAEA,GAAG,CAAC,EAAEI,EAAEF,EAAE,OAAOE,IAAIJ,EAAEE,EAAEE,CAAC,CAAC,EAAED,EAAE,CAAC,OAAOH,CAAC,EAAhE,KAAkEK,EAAI,CAAC,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAChLb,EAAS,CAAC,MAAOE,EAAA,UAAkB,CAAE,EAApB,SACrB,GAAI,CAAC,EACL,SAAU,CAAC,MAAQ,EAAE,MAAQ,EAAE,SAAW,EAAE,SAAW,EAAE,IAAM,EAAE,KAAO,EAAE,MAAQ,EAAE,UAAY,EAAE,QAAU,GAAG,MAAQ,GAAG,UAAY,GAAG,gBAAkB,GAAG,UAAY,GAAG,gBAAkB,GAAG,0BAA4B,GAAG,QAAU,GAAG,iBAAmB,GAAG,gBAAkB,GAAG,OAAS,GAAG,MAAQ,GAAG,QAAU,EAAE,KAAO,CAAC,EACpU,WAAY,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,GAAG,UAAU,GAAG,QAAQ,GAAG,YAAY,GAAG,kBAAkB,GAAG,YAAY,GAAG,kBAAkB,GAAG,4BAA4B,GAAG,UAAU,GAAG,SAAS,GAAG,OAAO,EACxN,aAAc,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAClH,cAAeA,EAAA,SAAmBY,EAAQC,EAAQC,EAAUC,EAAIC,EAAyBC,EAAiBC,EAAiB,CAG3H,IAAIC,EAAKF,EAAG,OAAS,EACrB,OAAQD,EAAS,CACjB,IAAK,GACJ,OAAOC,EAAGE,EAAG,CAAC,EAEf,IAAK,GACJ,KAAK,EAAI,CAAC,EACX,MACA,IAAK,GACLF,EAAGE,EAAG,CAAC,EAAE,KAAKF,EAAGE,CAAE,CAAC,EAAE,KAAK,EAAIF,EAAGE,EAAG,CAAC,EACtC,MACA,IAAK,GAAG,IAAK,GACZ,KAAK,EAAIF,EAAGE,CAAE,EACf,MACA,IAAK,GAAG,IAAK,GACZ,KAAK,EAAE,CAAC,EACT,MACA,IAAK,GACLJ,EAAG,YAAY,EAAE,gBAAgBE,EAAGE,CAAE,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,EAAEF,EAAGE,CAAE,EAAE,OAAO,CAAC,EACzE,MACA,IAAK,GACJ,KAAK,EAAEF,EAAGE,CAAE,EAAE,KAAK,EAAEJ,EAAG,YAAY,EAAE,YAAY,KAAK,CAAC,EACzD,MACA,IAAK,IAAI,IAAK,IACb,KAAK,EAAEE,EAAGE,CAAE,EAAE,KAAK,EAAEJ,EAAG,YAAY,EAAE,kBAAkB,KAAK,CAAC,EAC/D,MACA,IAAK,IACLA,EAAG,WAAWE,EAAGE,CAAE,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,EAAEF,EAAGE,CAAE,EAAE,OAAO,CAAC,EACtD,MACA,IAAK,IACLJ,EAAG,QAAQE,EAAGE,CAAE,EAAE,EAAE,EAAE,EAAE,KAAK,EAAEF,EAAGE,CAAE,EACpC,MACA,IAAK,IACLJ,EAAG,SAASE,EAAGE,CAAE,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,EAAEF,EAAGE,CAAE,EAC1C,KACA,CACA,EAvCe,aAwCf,MAAO,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEpB,EAAEK,EAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,CAAG,EAAEZ,EAAEK,EAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,CAAG,EAAEZ,EAAEK,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEL,EAAEK,EAAI,CAAC,EAAE,EAAE,CAAC,CAAC,EACnb,eAAgB,CAAC,EACjB,WAAYJ,EAAA,SAAqBoB,EAAKC,EAAM,CACxC,GAAIA,EAAK,YACL,KAAK,MAAMD,CAAG,MACX,CACH,IAAIE,EAAQ,IAAI,MAAMF,CAAG,EACzB,MAAAE,EAAM,KAAOD,EACPC,CACV,CACJ,EARY,cASZ,MAAOtB,EAAA,SAAeuB,EAAO,CACzB,IAAIC,EAAO,KAAMC,EAAQ,CAAC,CAAC,EAAGC,EAAS,CAAC,EAAGC,EAAS,CAAC,IAAI,EAAGC,EAAS,CAAC,EAAGC,EAAQ,KAAK,MAAOjB,EAAS,GAAIE,EAAW,EAAGD,EAAS,EAAGiB,EAAa,EAAGC,GAAS,EAAGC,EAAM,EAClKC,EAAOL,EAAO,MAAM,KAAK,UAAW,CAAC,EACrCM,EAAQ,OAAO,OAAO,KAAK,KAAK,EAChCC,EAAc,CAAE,GAAI,CAAC,CAAE,EAC3B,QAASlC,KAAK,KAAK,GACX,OAAO,UAAU,eAAe,KAAK,KAAK,GAAIA,CAAC,IAC/CkC,EAAY,GAAGlC,CAAC,EAAI,KAAK,GAAGA,CAAC,GAGrCiC,EAAM,SAASX,EAAOY,EAAY,EAAE,EACpCA,EAAY,GAAG,MAAQD,EACvBC,EAAY,GAAG,OAAS,KACpB,OAAOD,EAAM,OAAU,MACvBA,EAAM,OAAS,CAAC,GAEpB,IAAIE,EAAQF,EAAM,OAClBN,EAAO,KAAKQ,CAAK,EACjB,IAAIC,EAASH,EAAM,SAAWA,EAAM,QAAQ,OACxC,OAAOC,EAAY,GAAG,YAAe,WACrC,KAAK,WAAaA,EAAY,GAAG,WAEjC,KAAK,WAAa,OAAO,eAAe,IAAI,EAAE,WAElD,SAASG,GAASC,EAAG,CACjBd,EAAM,OAASA,EAAM,OAAS,EAAIc,EAClCZ,EAAO,OAASA,EAAO,OAASY,EAChCX,EAAO,OAASA,EAAO,OAASW,CACpC,CAJSvC,EAAAsC,GAAA,YAKD,SAASE,IAAM,CACf,IAAIC,EACJ,OAAAA,EAAQf,EAAO,IAAI,GAAKQ,EAAM,IAAI,GAAKF,EACnC,OAAOS,GAAU,WACbA,aAAiB,QACjBf,EAASe,EACTA,EAAQf,EAAO,IAAI,GAEvBe,EAAQjB,EAAK,SAASiB,CAAK,GAAKA,GAE7BA,CACX,CAXazC,EAAAwC,GAAA,OAajB,QADIE,EAAQC,EAAgBC,EAAOC,EAAQC,GAAGC,EAAGC,EAAQ,CAAC,EAAGC,EAAGC,EAAKC,GAAUC,IAClE,CAUT,GATAR,EAAQnB,EAAMA,EAAM,OAAS,CAAC,EAC1B,KAAK,eAAemB,CAAK,EACzBC,EAAS,KAAK,eAAeD,CAAK,IAE9BF,IAAW,MAAQ,OAAOA,EAAU,OACpCA,EAASF,GAAI,GAEjBK,EAAShB,EAAMe,CAAK,GAAKf,EAAMe,CAAK,EAAEF,CAAM,GAE5C,OAAOG,EAAW,KAAe,CAACA,EAAO,QAAU,CAACA,EAAO,CAAC,EAAG,CAC/D,IAAIQ,EAAS,GACbD,EAAW,CAAC,EACZ,IAAKH,KAAKpB,EAAMe,CAAK,EACb,KAAK,WAAWK,CAAC,GAAKA,EAAIlB,IAC1BqB,EAAS,KAAK,IAAO,KAAK,WAAWH,CAAC,EAAI,GAAI,EAGlDf,EAAM,aACNmB,EAAS,wBAA0BvC,EAAW,GAAK;AAAA,EAAQoB,EAAM,aAAa,EAAI;AAAA,YAAiBkB,EAAS,KAAK,IAAI,EAAI,WAAc,KAAK,WAAWV,CAAM,GAAKA,GAAU,IAE5KW,EAAS,wBAA0BvC,EAAW,GAAK,iBAAmB4B,GAAUV,EAAM,eAAiB,KAAQ,KAAK,WAAWU,CAAM,GAAKA,GAAU,KAExJ,KAAK,WAAWW,EAAQ,CACpB,KAAMnB,EAAM,MACZ,MAAO,KAAK,WAAWQ,CAAM,GAAKA,EAClC,KAAMR,EAAM,SACZ,IAAKE,EACL,SAAUgB,CACd,CAAC,CACL,CACA,GAAIP,EAAO,CAAC,YAAa,OAASA,EAAO,OAAS,EAC9C,MAAM,IAAI,MAAM,oDAAsDD,EAAQ,YAAcF,CAAM,EAEtG,OAAQG,EAAO,CAAC,EAAG,CACnB,IAAK,GACDpB,EAAM,KAAKiB,CAAM,EACjBf,EAAO,KAAKO,EAAM,MAAM,EACxBN,EAAO,KAAKM,EAAM,MAAM,EACxBT,EAAM,KAAKoB,EAAO,CAAC,CAAC,EACpBH,EAAS,KACJC,GASDD,EAASC,EACTA,EAAiB,OATjB9B,EAASqB,EAAM,OACftB,EAASsB,EAAM,OACfpB,EAAWoB,EAAM,SACjBE,EAAQF,EAAM,OACVJ,EAAa,GACbA,KAMR,MACJ,IAAK,GAwBD,GAvBAoB,EAAM,KAAK,aAAaL,EAAO,CAAC,CAAC,EAAE,CAAC,EACpCG,EAAM,EAAIrB,EAAOA,EAAO,OAASuB,CAAG,EACpCF,EAAM,GAAK,CACP,WAAYpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,WAC/C,UAAWtB,EAAOA,EAAO,OAAS,CAAC,EAAE,UACrC,aAAcA,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,aACjD,YAAatB,EAAOA,EAAO,OAAS,CAAC,EAAE,WAC3C,EACIS,IACAW,EAAM,GAAG,MAAQ,CACbpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,MAAM,CAAC,EAC1CtB,EAAOA,EAAO,OAAS,CAAC,EAAE,MAAM,CAAC,CACrC,GAEJmB,EAAI,KAAK,cAAc,MAAMC,EAAO,CAChCpC,EACAC,EACAC,EACAqB,EAAY,GACZU,EAAO,CAAC,EACRlB,EACAC,CACJ,EAAE,OAAOK,CAAI,CAAC,EACV,OAAOc,EAAM,IACb,OAAOA,EAEPG,IACAzB,EAAQA,EAAM,MAAM,EAAG,GAAKyB,EAAM,CAAC,EACnCvB,EAASA,EAAO,MAAM,EAAG,GAAKuB,CAAG,EACjCtB,EAASA,EAAO,MAAM,EAAG,GAAKsB,CAAG,GAErCzB,EAAM,KAAK,KAAK,aAAaoB,EAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAC1ClB,EAAO,KAAKqB,EAAM,CAAC,EACnBpB,EAAO,KAAKoB,EAAM,EAAE,EACpBG,GAAWtB,EAAMJ,EAAMA,EAAM,OAAS,CAAC,CAAC,EAAEA,EAAMA,EAAM,OAAS,CAAC,CAAC,EACjEA,EAAM,KAAK0B,EAAQ,EACnB,MACJ,IAAK,GACD,MAAO,EACX,CACJ,CACA,MAAO,EACX,EA3IO,QA2IN,EAGGjB,EAAS,UAAU,CACvB,IAAIA,EAAS,CAEb,IAAI,EAEJ,WAAWlC,EAAA,SAAoBoB,EAAKC,EAAM,CAClC,GAAI,KAAK,GAAG,OACR,KAAK,GAAG,OAAO,WAAWD,EAAKC,CAAI,MAEnC,OAAM,IAAI,MAAMD,CAAG,CAE3B,EANO,cASX,SAASpB,EAAA,SAAUuB,EAAOR,EAAI,CACtB,YAAK,GAAKA,GAAM,KAAK,IAAM,CAAC,EAC5B,KAAK,OAASQ,EACd,KAAK,MAAQ,KAAK,WAAa,KAAK,KAAO,GAC3C,KAAK,SAAW,KAAK,OAAS,EAC9B,KAAK,OAAS,KAAK,QAAU,KAAK,MAAQ,GAC1C,KAAK,eAAiB,CAAC,SAAS,EAChC,KAAK,OAAS,CACV,WAAY,EACZ,aAAc,EACd,UAAW,EACX,YAAa,CACjB,EACI,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAAC,EAAE,CAAC,GAE5B,KAAK,OAAS,EACP,IACX,EAlBK,YAqBT,MAAMvB,EAAA,UAAY,CACV,IAAIsD,EAAK,KAAK,OAAO,CAAC,EACtB,KAAK,QAAUA,EACf,KAAK,SACL,KAAK,SACL,KAAK,OAASA,EACd,KAAK,SAAWA,EAChB,IAAIC,EAAQD,EAAG,MAAM,iBAAiB,EACtC,OAAIC,GACA,KAAK,WACL,KAAK,OAAO,aAEZ,KAAK,OAAO,cAEZ,KAAK,QAAQ,QACb,KAAK,OAAO,MAAM,CAAC,IAGvB,KAAK,OAAS,KAAK,OAAO,MAAM,CAAC,EAC1BD,CACX,EApBE,SAuBN,MAAMtD,EAAA,SAAUsD,EAAI,CACZ,IAAIJ,EAAMI,EAAG,OACTC,EAAQD,EAAG,MAAM,eAAe,EAEpC,KAAK,OAASA,EAAK,KAAK,OACxB,KAAK,OAAS,KAAK,OAAO,OAAO,EAAG,KAAK,OAAO,OAASJ,CAAG,EAE5D,KAAK,QAAUA,EACf,IAAIM,EAAW,KAAK,MAAM,MAAM,eAAe,EAC/C,KAAK,MAAQ,KAAK,MAAM,OAAO,EAAG,KAAK,MAAM,OAAS,CAAC,EACvD,KAAK,QAAU,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,CAAC,EAEzDD,EAAM,OAAS,IACf,KAAK,UAAYA,EAAM,OAAS,GAEpC,IAAIR,EAAI,KAAK,OAAO,MAEpB,YAAK,OAAS,CACV,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,aAC1B,YAAaQ,GACRA,EAAM,SAAWC,EAAS,OAAS,KAAK,OAAO,aAAe,GAC5DA,EAASA,EAAS,OAASD,EAAM,MAAM,EAAE,OAASA,EAAM,CAAC,EAAE,OAChE,KAAK,OAAO,aAAeL,CACjC,EAEI,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAACH,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,KAAK,OAASG,CAAG,GAEvD,KAAK,OAAS,KAAK,OAAO,OACnB,IACX,EAhCE,SAmCN,KAAKlD,EAAA,UAAY,CACT,YAAK,MAAQ,GACN,IACX,EAHC,QAML,OAAOA,EAAA,UAAY,CACX,GAAI,KAAK,QAAQ,gBACb,KAAK,WAAa,OAElB,QAAO,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAAqI,KAAK,aAAa,EAAG,CAC9N,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACf,CAAC,EAGL,OAAO,IACX,EAZG,UAeP,KAAKA,EAAA,SAAUuC,EAAG,CACV,KAAK,MAAM,KAAK,MAAM,MAAMA,CAAC,CAAC,CAClC,EAFC,QAKL,UAAUvC,EAAA,UAAY,CACd,IAAIyD,EAAO,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,KAAK,MAAM,MAAM,EACzE,OAAQA,EAAK,OAAS,GAAK,MAAM,IAAMA,EAAK,OAAO,GAAG,EAAE,QAAQ,MAAO,EAAE,CAC7E,EAHM,aAMV,cAAczD,EAAA,UAAY,CAClB,IAAI0D,EAAO,KAAK,MAChB,OAAIA,EAAK,OAAS,KACdA,GAAQ,KAAK,OAAO,OAAO,EAAG,GAAGA,EAAK,MAAM,IAExCA,EAAK,OAAO,EAAE,EAAE,GAAKA,EAAK,OAAS,GAAK,MAAQ,KAAK,QAAQ,MAAO,EAAE,CAClF,EANU,iBASd,aAAa1D,EAAA,UAAY,CACjB,IAAI2D,EAAM,KAAK,UAAU,EACrBC,EAAI,IAAI,MAAMD,EAAI,OAAS,CAAC,EAAE,KAAK,GAAG,EAC1C,OAAOA,EAAM,KAAK,cAAc,EAAI;AAAA,EAAOC,EAAI,GACnD,EAJS,gBAOb,WAAW5D,EAAA,SAAS6D,EAAOC,EAAc,CACjC,IAAIrB,EACAc,EACAQ,EAwDJ,GAtDI,KAAK,QAAQ,kBAEbA,EAAS,CACL,SAAU,KAAK,SACf,OAAQ,CACJ,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,UAChB,aAAc,KAAK,OAAO,aAC1B,YAAa,KAAK,OAAO,WAC7B,EACA,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,QAAS,KAAK,QACd,QAAS,KAAK,QACd,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,OAAQ,KAAK,OACb,GAAI,KAAK,GACT,eAAgB,KAAK,eAAe,MAAM,CAAC,EAC3C,KAAM,KAAK,IACf,EACI,KAAK,QAAQ,SACbA,EAAO,OAAO,MAAQ,KAAK,OAAO,MAAM,MAAM,CAAC,IAIvDR,EAAQM,EAAM,CAAC,EAAE,MAAM,iBAAiB,EACpCN,IACA,KAAK,UAAYA,EAAM,QAE3B,KAAK,OAAS,CACV,WAAY,KAAK,OAAO,UACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,YAC1B,YAAaA,EACAA,EAAMA,EAAM,OAAS,CAAC,EAAE,OAASA,EAAMA,EAAM,OAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,OAC5E,KAAK,OAAO,YAAcM,EAAM,CAAC,EAAE,MACpD,EACA,KAAK,QAAUA,EAAM,CAAC,EACtB,KAAK,OAASA,EAAM,CAAC,EACrB,KAAK,QAAUA,EACf,KAAK,OAAS,KAAK,OAAO,OACtB,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAAC,KAAK,OAAQ,KAAK,QAAU,KAAK,MAAM,GAEhE,KAAK,MAAQ,GACb,KAAK,WAAa,GAClB,KAAK,OAAS,KAAK,OAAO,MAAMA,EAAM,CAAC,EAAE,MAAM,EAC/C,KAAK,SAAWA,EAAM,CAAC,EACvBpB,EAAQ,KAAK,cAAc,KAAK,KAAM,KAAK,GAAI,KAAMqB,EAAc,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAClH,KAAK,MAAQ,KAAK,SAClB,KAAK,KAAO,IAEZrB,EACA,OAAOA,EACJ,GAAI,KAAK,WAAY,CAExB,QAASxC,KAAK8D,EACV,KAAK9D,CAAC,EAAI8D,EAAO9D,CAAC,EAEtB,MAAO,EACX,CACA,MAAO,EACX,EArEO,cAwEX,KAAKD,EAAA,UAAY,CACT,GAAI,KAAK,KACL,OAAO,KAAK,IAEX,KAAK,SACN,KAAK,KAAO,IAGhB,IAAIyC,EACAoB,EACAG,EACAC,EACC,KAAK,QACN,KAAK,OAAS,GACd,KAAK,MAAQ,IAGjB,QADIC,EAAQ,KAAK,cAAc,EACtBC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAE9B,GADAH,EAAY,KAAK,OAAO,MAAM,KAAK,MAAME,EAAMC,CAAC,CAAC,CAAC,EAC9CH,IAAc,CAACH,GAASG,EAAU,CAAC,EAAE,OAASH,EAAM,CAAC,EAAE,SAGvD,GAFAA,EAAQG,EACRC,EAAQE,EACJ,KAAK,QAAQ,gBAAiB,CAE9B,GADA1B,EAAQ,KAAK,WAAWuB,EAAWE,EAAMC,CAAC,CAAC,EACvC1B,IAAU,GACV,OAAOA,EACJ,GAAI,KAAK,WAAY,CACxBoB,EAAQ,GACR,QACJ,KAEI,OAAO,EAEf,SAAW,CAAC,KAAK,QAAQ,KACrB,MAIZ,OAAIA,GACApB,EAAQ,KAAK,WAAWoB,EAAOK,EAAMD,CAAK,CAAC,EACvCxB,IAAU,GACHA,EAGJ,IAEP,KAAK,SAAW,GACT,KAAK,IAEL,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAA2B,KAAK,aAAa,EAAG,CACpH,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACf,CAAC,CAET,EAvDC,QA0DL,IAAIzC,EAAA,UAAgB,CACZ,IAAI+C,EAAI,KAAK,KAAK,EAClB,OAAIA,GAGO,KAAK,IAAI,CAExB,EAPA,OAUJ,MAAM/C,EAAA,SAAgBoE,EAAW,CACzB,KAAK,eAAe,KAAKA,CAAS,CACtC,EAFE,SAKN,SAASpE,EAAA,UAAqB,CACtB,IAAIuC,EAAI,KAAK,eAAe,OAAS,EACrC,OAAIA,EAAI,EACG,KAAK,eAAe,IAAI,EAExB,KAAK,eAAe,CAAC,CAEpC,EAPK,YAUT,cAAcvC,EAAA,UAA0B,CAChC,OAAI,KAAK,eAAe,QAAU,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,EACzE,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAAE,MAErE,KAAK,WAAW,QAAW,KAE1C,EANU,iBASd,SAASA,EAAA,SAAmBuC,EAAG,CAEvB,OADAA,EAAI,KAAK,eAAe,OAAS,EAAI,KAAK,IAAIA,GAAK,CAAC,EAChDA,GAAK,EACE,KAAK,eAAeA,CAAC,EAErB,SAEf,EAPK,YAUT,UAAUvC,EAAA,SAAoBoE,EAAW,CACjC,KAAK,MAAMA,CAAS,CACxB,EAFM,aAKV,eAAepE,EAAA,UAA0B,CACjC,OAAO,KAAK,eAAe,MAC/B,EAFW,kBAGf,QAAS,CAAC,mBAAmB,EAAI,EACjC,cAAeA,EAAA,SAAmBe,EAAGsD,EAAIC,EAA0BC,EAAU,CAC7E,IAAIC,EAAQD,EACZ,OAAOD,EAA2B,CAClC,IAAK,GACL,MACA,IAAK,GACL,MACA,IAAK,GAAE,MAAO,IAEd,IAAK,GACL,MACA,IAAK,GACL,MACA,IAAK,GAAE,MAAO,GAEd,IAAK,GAAE,MAAO,IAEd,IAAK,GAAG,YAAK,MAAM,WAAW,EAAS,GACvC,MACA,IAAK,GAAG,YAAK,SAAS,EAAU,kBAChC,MACA,IAAK,GAAG,YAAK,MAAM,WAAW,EAAS,GACvC,MACA,IAAK,IAAI,YAAK,SAAS,EAAU,kBACjC,MACA,IAAK,IAAI,KAAK,MAAM,qBAAqB,EACzC,MACA,IAAK,IAAI,KAAK,SAAS,EACvB,MACA,IAAK,IAAG,MAAO,4BAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,GAEf,IAAK,IAAG,MAAO,SAEf,CACA,EA1Ce,aA2Cf,MAAO,CAAC,sBAAsB,sBAAsB,cAAc,YAAY,gBAAgB,mBAAmB,sBAAsB,wBAAwB,wBAAwB,wBAAwB,wBAAwB,yBAAyB,aAAa,eAAe,yBAAyB,mBAAmB,iBAAiB,UAAU,SAAS,EAC5W,WAAY,CAAC,oBAAsB,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,CAAC,EAAE,UAAY,EAAK,EAAE,QAAU,CAAC,MAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAI,CAAC,CAC1O,EACA,OAAOpC,CACP,EAAG,EACHpC,EAAO,MAAQoC,EACf,SAASuC,GAAU,CACjB,KAAK,GAAK,CAAC,CACb,CAFS,OAAAzE,EAAAyE,EAAA,UAGTA,EAAO,UAAY3E,EAAOA,EAAO,OAAS2E,EACnC,IAAIA,CACX,EAAG,EACF3E,EAAO,OAASA,EAEhB,IAAO4E,GAAQC,EC9oBhB,IAAAC,EAAA,GAAAC,GAAAD,EAAA,cAAAE,GAAA,eAAAC,GAAA,YAAAC,GAAA,eAAAC,GAAA,UAAAC,GAAA,YAAAC,GAAA,gBAAAC,GAAA,gBAAAC,GAAA,aAAAC,KACA,IAAIC,EAAiB,GACjBC,GAAgB,EAEdC,EAAW,CAAC,EACZC,EAAQ,CAAC,EACTC,EAAW,CAAC,EAELC,GAAcC,EAAA,IAAMC,GAAN,eAEdC,GAAQF,EAAA,UAAY,CAC/BJ,EAAS,OAAS,EAClBC,EAAM,OAAS,EACfH,EAAiB,GACjBI,EAAS,OAAS,EACTI,GAAM,CACjB,EANqB,SAQRC,GAAaH,EAAA,SAAUI,EAAK,CACvCV,EAAiBU,EACjBR,EAAS,KAAKQ,CAAG,CACnB,EAH0B,cAKbC,GAAcL,EAAA,UAAY,CACrC,OAAOJ,CACT,EAF2B,eAIdU,GAAWN,EAAA,UAAY,CAClC,IAAIO,EAAoBC,GAAa,EAC/BC,EAAW,IACbC,EAAiB,EACrB,KAAO,CAACH,GAAqBG,EAAiBD,GAC5CF,EAAoBC,GAAa,EACjCE,IAGF,OAAAb,EAAM,KAAK,GAAGC,CAAQ,EAEfD,CACT,EAZwB,YAcXc,GAAUX,EAAA,SAAUY,EAAQC,EAAQC,EAAO,CACtD,IAAMC,EAAU,CACd,GAAIpB,KACJ,QAASD,EACT,KAAMA,EACN,KAAMkB,EACN,MAAOC,GAAkB,EAEzB,OAAQC,EAAQ,CAACA,CAAK,EAAI,CAAC,CAC7B,EACAhB,EAAS,KAAKiB,CAAO,CACvB,EAXuB,WAaVC,GAAWhB,EAAA,SAAUc,EAAO,CAEnBhB,EAAS,KAAMmB,GAASA,EAAK,KAAOtB,GAAgB,CAAC,EAE7D,OAAO,KAAKmB,CAAK,CAC/B,EALwB,YAOXI,GAAalB,EAAA,SAAUmB,EAAO,CACzC,IAAMC,EAAU,CACd,QAAS1B,EACT,KAAMA,EACN,YAAayB,EACb,KAAMA,EACN,QAAS,CAAC,CACZ,EACAtB,EAAM,KAAKuB,CAAO,CACpB,EAT0B,cAiBpBZ,GAAeR,EAAA,UAAY,CAC/B,IAAMqB,EAAcrB,EAAA,SAAUsB,EAAK,CACjC,OAAOxB,EAASwB,CAAG,EAAE,SACvB,EAFoB,eAIhBC,EAAe,GACnB,OAAW,CAACC,EAAGT,CAAO,IAAKjB,EAAS,QAAQ,EAC1CuB,EAAYG,CAAC,EAEbD,EAAeA,GAAgBR,EAAQ,UAEzC,OAAOQ,CACT,EAZqB,gBAcdE,GAAQ,CACb,MAAAvB,GACA,YAAAH,GACA,WAAAI,GACA,YAAAE,GACA,SAAAC,GACA,QAAAK,GACA,WAAAO,GACA,SAAAF,EACF,ECpGA,IAAMU,GAAe,GAERC,EAAWC,EAAA,SAAUC,EAAMC,EAAU,CAChD,IAAMC,EAAWF,EAAK,OAAO,MAAM,EACnC,OAAAE,EAAS,KAAK,IAAKD,EAAS,CAAC,EAC7BC,EAAS,KAAK,IAAKD,EAAS,CAAC,EAC7BC,EAAS,KAAK,OAAQD,EAAS,IAAI,EACnCC,EAAS,KAAK,SAAUD,EAAS,MAAM,EACvCC,EAAS,KAAK,QAASD,EAAS,KAAK,EACrCC,EAAS,KAAK,SAAUD,EAAS,MAAM,EACvCC,EAAS,KAAK,KAAMD,EAAS,EAAE,EAC/BC,EAAS,KAAK,KAAMD,EAAS,EAAE,EAE3BA,EAAS,QAAU,QACrBC,EAAS,KAAK,QAASD,EAAS,KAAK,EAGhCC,CACT,EAhBwB,YAkBXC,GAAWJ,EAAA,SAAUK,EAASC,EAAU,CAEnD,IAAMC,EAAgBF,EACnB,OAAO,QAAQ,EACf,KAAK,KAAMC,EAAS,EAAE,EACtB,KAAK,KAAMA,EAAS,EAAE,EACtB,KAAK,QAAS,MAAM,EACpB,KAAK,IAAK,EAAM,EAChB,KAAK,eAAgB,CAAC,EACtB,KAAK,WAAY,SAAS,EAEvBE,EAAOH,EAAQ,OAAO,GAAG,EAG/BG,EACG,OAAO,QAAQ,EACf,KAAK,KAAMF,EAAS,GAAK,GAAS,CAAC,EACnC,KAAK,KAAMA,EAAS,GAAK,GAAS,CAAC,EACnC,KAAK,IAAK,GAAG,EACb,KAAK,eAAgB,CAAC,EACtB,KAAK,OAAQ,MAAM,EACnB,KAAK,SAAU,MAAM,EAGxBE,EACG,OAAO,QAAQ,EACf,KAAK,KAAMF,EAAS,GAAK,GAAS,CAAC,EACnC,KAAK,KAAMA,EAAS,GAAK,GAAS,CAAC,EACnC,KAAK,IAAK,GAAG,EACb,KAAK,eAAgB,CAAC,EACtB,KAAK,OAAQ,MAAM,EACnB,KAAK,SAAU,MAAM,EAGxB,SAASG,EAAMD,EAAM,CACnB,IAAME,EAAMC,EAAM,EACf,WAAW,KAAK,GAAK,CAAC,EACtB,SAAS,GAAK,KAAK,GAAK,EAAE,EAC1B,YAAY,GAAU,EACtB,YAAY,kBAAY,EAE3BH,EACG,OAAO,MAAM,EACb,KAAK,QAAS,OAAO,EACrB,KAAK,IAAKE,CAAG,EACb,KAAK,YAAa,aAAeJ,EAAS,GAAK,KAAOA,EAAS,GAAK,GAAK,GAAG,CACjF,CAZSN,EAAAS,EAAA,SAeT,SAASG,EAAIJ,EAAM,CACjB,IAAME,EAAMC,EAAM,EACf,WAAY,EAAI,KAAK,GAAM,CAAC,EAC5B,SAAS,GAAK,KAAK,GAAK,EAAE,EAC1B,YAAY,GAAU,EACtB,YAAY,kBAAY,EAE3BH,EACG,OAAO,MAAM,EACb,KAAK,QAAS,OAAO,EACrB,KAAK,IAAKE,CAAG,EACb,KAAK,YAAa,aAAeJ,EAAS,GAAK,KAAOA,EAAS,GAAK,GAAK,GAAG,CACjF,CAZSN,EAAAY,EAAA,OAeT,SAASC,EAAWL,EAAM,CACxBA,EACG,OAAO,MAAM,EACb,KAAK,QAAS,OAAO,EACrB,KAAK,SAAU,CAAC,EAChB,KAAK,KAAMF,EAAS,GAAK,CAAC,EAC1B,KAAK,KAAMA,EAAS,GAAK,CAAC,EAC1B,KAAK,KAAMA,EAAS,GAAK,CAAC,EAC1B,KAAK,KAAMA,EAAS,GAAK,CAAC,EAC1B,KAAK,QAAS,OAAO,EACrB,KAAK,eAAgB,KAAK,EAC1B,KAAK,SAAU,MAAM,CAC1B,CAZS,OAAAN,EAAAa,EAAA,cAcLP,EAAS,MAAQ,EACnBG,EAAMD,CAAI,EACDF,EAAS,MAAQ,EAC1BM,EAAIJ,CAAI,EAERK,EAAWL,CAAI,EAGVD,CACT,EAvFwB,YAyFXO,GAAad,EAAA,SAAUK,EAASU,EAAY,CACvD,IAAMR,EAAgBF,EAAQ,OAAO,QAAQ,EAC7C,OAAAE,EAAc,KAAK,KAAMQ,EAAW,EAAE,EACtCR,EAAc,KAAK,KAAMQ,EAAW,EAAE,EACtCR,EAAc,KAAK,QAAS,SAAWQ,EAAW,GAAG,EACrDR,EAAc,KAAK,OAAQQ,EAAW,IAAI,EAC1CR,EAAc,KAAK,SAAUQ,EAAW,MAAM,EAC9CR,EAAc,KAAK,IAAKQ,EAAW,CAAC,EAEhCR,EAAc,QAAU,QAC1BA,EAAc,KAAK,QAASA,EAAc,KAAK,EAG7CQ,EAAW,QAAU,QACvBR,EAAc,OAAO,OAAO,EAAE,KAAKQ,EAAW,KAAK,EAG9CR,CACT,EAlB0B,cAoBbS,GAAWhB,EAAA,SAAUC,EAAMgB,EAAU,CAEhD,IAAMC,EAAQD,EAAS,KAAK,QAAQ,eAAgB,GAAG,EAEjDE,EAAWlB,EAAK,OAAO,MAAM,EACnCkB,EAAS,KAAK,IAAKF,EAAS,CAAC,EAC7BE,EAAS,KAAK,IAAKF,EAAS,CAAC,EAC7BE,EAAS,KAAK,QAAS,QAAQ,EAE/BA,EAAS,MAAM,cAAeF,EAAS,MAAM,EAEzCA,EAAS,QAAU,QACrBE,EAAS,KAAK,QAASF,EAAS,KAAK,EAGvC,IAAMG,EAAOD,EAAS,OAAO,OAAO,EACpC,OAAAC,EAAK,KAAK,IAAKH,EAAS,EAAIA,EAAS,WAAa,CAAC,EACnDG,EAAK,KAAKF,CAAK,EAERC,CACT,EApBwB,YAsBXE,GAAYrB,EAAA,SAAUC,EAAMqB,EAAW,CAQlD,SAASC,EAAUC,EAAGC,EAAGC,EAAOC,EAAQC,EAAK,CAC3C,OACEJ,EACA,IACAC,EACA,KACCD,EAAIE,GACL,IACAD,EACA,KACCD,EAAIE,GACL,KACCD,EAAIE,EAASC,GACd,KACCJ,EAAIE,EAAQE,EAAM,KACnB,KACCH,EAAIE,GACL,IACAH,EACA,KACCC,EAAIE,EAET,CAtBS3B,EAAAuB,EAAA,aAuBT,IAAMM,EAAU5B,EAAK,OAAO,SAAS,EACrC4B,EAAQ,KAAK,SAAUN,EAAUD,EAAU,EAAGA,EAAU,EAAG,GAAI,GAAI,CAAC,CAAC,EACrEO,EAAQ,KAAK,QAAS,UAAU,EAEhCP,EAAU,EAAIA,EAAU,EAAIA,EAAU,YACtCA,EAAU,EAAIA,EAAU,EAAI,GAAMA,EAAU,YAC5CN,GAASf,EAAMqB,CAAS,CAC1B,EAtCyB,aAwCZQ,GAAc9B,EAAA,SAAUC,EAAM8B,EAASC,EAAM,CACxD,IAAMC,EAAIhC,EAAK,OAAO,GAAG,EAEnBiC,EAAOC,GAAY,EACzBD,EAAK,EAAIH,EAAQ,EACjBG,EAAK,EAAIH,EAAQ,EACjBG,EAAK,KAAOH,EAAQ,KACpBG,EAAK,MAAQF,EAAK,MAClBE,EAAK,OAASF,EAAK,OACnBE,EAAK,MAAQ,gCAAkCH,EAAQ,IACvDG,EAAK,GAAK,EACVA,EAAK,GAAK,EACVnC,EAASkC,EAAGC,CAAI,EAEhBE,GAAuBJ,CAAI,EACzBD,EAAQ,KACRE,EACAC,EAAK,EACLA,EAAK,EACLA,EAAK,MACLA,EAAK,OACL,CAAE,MAAO,gCAAkCH,EAAQ,GAAI,EACvDC,EACAD,EAAQ,MACV,CACF,EAzB2B,eA2BvBM,GAAY,GAQHC,GAAWtC,EAAA,SAAUC,EAAMsC,EAAMP,EAAM,CAClD,IAAMQ,EAASD,EAAK,EAAIP,EAAK,MAAQ,EAC/BC,EAAIhC,EAAK,OAAO,GAAG,EACzBoC,KACA,IAAMI,EAAY,IAAM,EAAI,GAC5BR,EAAE,OAAO,MAAM,EACZ,KAAK,KAAM,OAASI,EAAS,EAC7B,KAAK,KAAMG,CAAM,EACjB,KAAK,KAAMD,EAAK,CAAC,EACjB,KAAK,KAAMC,CAAM,EACjB,KAAK,KAAMC,CAAS,EACpB,KAAK,QAAS,WAAW,EACzB,KAAK,eAAgB,KAAK,EAC1B,KAAK,mBAAoB,KAAK,EAC9B,KAAK,SAAU,MAAM,EAExBrC,GAAS6B,EAAG,CACV,GAAIO,EACJ,GAAI,KAAO,EAAID,EAAK,OAAS,GAC7B,MAAOA,EAAK,KACd,CAAC,EAED,IAAML,EAAOC,GAAY,EACzBD,EAAK,EAAIK,EAAK,EACdL,EAAK,EAAIK,EAAK,EACdL,EAAK,KAAOK,EAAK,KACjBL,EAAK,MAAQF,EAAK,MAClBE,EAAK,OAASF,EAAK,OACnBE,EAAK,MAAQ,kBAAoBK,EAAK,IACtCL,EAAK,GAAK,EACVA,EAAK,GAAK,EACVnC,EAASkC,EAAGC,CAAI,EAEhBE,GAAuBJ,CAAI,EACzBO,EAAK,KACLN,EACAC,EAAK,EACLA,EAAK,EACLA,EAAK,MACLA,EAAK,OACL,CAAE,MAAO,MAAO,EAChBF,EACAO,EAAK,MACP,CACF,EA5CwB,YAoDXG,GAAqB1C,EAAA,SAAUC,EAAM0C,EAAQ,CACvC5C,EAASE,EAAM,CAC9B,EAAG0C,EAAO,OACV,EAAGA,EAAO,OACV,MAAOA,EAAO,MAAQA,EAAO,OAC7B,OAAQA,EAAO,MAAQA,EAAO,OAC9B,KAAMA,EAAO,KACb,MAAO,MACT,CAAC,EACQ,MAAM,CACjB,EAVkC,sBAYrBC,GAAa5C,EAAA,UAAY,CACpC,MAAO,CACL,EAAG,EACH,EAAG,EACH,KAAM,OACN,cAAe,QACf,MAAO,IACP,OAAQ,IACR,WAAY,EACZ,GAAI,EACJ,GAAI,CACN,CACF,EAZ0B,cAcbmC,GAAcnC,EAAA,UAAY,CACrC,MAAO,CACL,EAAG,EACH,EAAG,EACH,MAAO,IACP,OAAQ,QACR,OAAQ,IACR,GAAI,EACJ,GAAI,CACN,CACF,EAV2B,eAYrBoC,GAA0B,UAAY,CAW1C,SAASS,EAAOC,EAASb,EAAGT,EAAGC,EAAGC,EAAOC,EAAQoB,EAAWC,EAAQ,CAClE,IAAMC,EAAOhB,EACV,OAAO,MAAM,EACb,KAAK,IAAKT,EAAIE,EAAQ,CAAC,EACvB,KAAK,IAAKD,EAAIE,EAAS,EAAI,CAAC,EAC5B,MAAM,aAAcqB,CAAM,EAC1B,MAAM,cAAe,QAAQ,EAC7B,KAAKF,CAAO,EACfI,EAAcD,EAAMF,CAAS,CAC/B,CATS/C,EAAA6C,EAAA,UAsBT,SAASM,EAAQL,EAASb,EAAGT,EAAGC,EAAGC,EAAOC,EAAQoB,EAAWf,EAAMgB,EAAQ,CACzE,GAAM,CAAE,aAAAI,EAAc,eAAAC,CAAe,EAAIrB,EAEnCsB,EAAQR,EAAQ,MAAM,cAAc,EAC1C,QAASS,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAAK,CACrC,IAAMC,EAAKD,EAAIH,EAAgBA,GAAgBE,EAAM,OAAS,GAAM,EAC9DL,EAAOhB,EACV,OAAO,MAAM,EACb,KAAK,IAAKT,EAAIE,EAAQ,CAAC,EACvB,KAAK,IAAKD,CAAC,EACX,KAAK,OAAQuB,CAAM,EACnB,MAAM,cAAe,QAAQ,EAC7B,MAAM,YAAaI,CAAY,EAC/B,MAAM,cAAeC,CAAc,EACtCJ,EACG,OAAO,OAAO,EACd,KAAK,IAAKzB,EAAIE,EAAQ,CAAC,EACvB,KAAK,KAAM8B,CAAE,EACb,KAAKF,EAAMC,CAAC,CAAC,EAEhBN,EACG,KAAK,IAAKxB,EAAIE,EAAS,CAAG,EAC1B,KAAK,oBAAqB,SAAS,EACnC,KAAK,qBAAsB,SAAS,EAEvCuB,EAAcD,EAAMF,CAAS,CAC/B,CACF,CA3BS/C,EAAAmD,EAAA,WAuCT,SAASM,EAAKX,EAASb,EAAGT,EAAGC,EAAGC,EAAOC,EAAQoB,EAAWf,EAAM,CAC9D,IAAM0B,EAAOzB,EAAE,OAAO,QAAQ,EASxBgB,EARIS,EACP,OAAO,eAAe,EACtB,KAAK,IAAKlC,CAAC,EACX,KAAK,IAAKC,CAAC,EACX,KAAK,QAASC,CAAK,EACnB,KAAK,SAAUC,CAAM,EACrB,KAAK,WAAY,OAAO,EAGxB,OAAO,WAAW,EAClB,MAAM,UAAW,OAAO,EACxB,MAAM,SAAU,MAAM,EACtB,MAAM,QAAS,MAAM,EAExBsB,EACG,OAAO,KAAK,EACZ,KAAK,QAAS,OAAO,EACrB,MAAM,UAAW,YAAY,EAC7B,MAAM,aAAc,QAAQ,EAC5B,MAAM,iBAAkB,QAAQ,EAChC,KAAKH,CAAO,EAEfK,EAAQL,EAASY,EAAMlC,EAAGC,EAAGC,EAAOC,EAAQoB,EAAWf,CAAI,EAC3DkB,EAAcD,EAAMF,CAAS,CAC/B,CA1BS/C,EAAAyD,EAAA,QAgCT,SAASP,EAAcS,EAAQC,EAAmB,CAChD,QAAWC,KAAOD,EACZC,KAAOD,GAETD,EAAO,KAAKE,EAAKD,EAAkBC,CAAG,CAAC,CAG7C,CAPS,OAAA7D,EAAAkD,EAAA,iBASF,SAAUlB,EAAM,CACrB,OAAOA,EAAK,gBAAkB,KAAOyB,EAAOzB,EAAK,gBAAkB,MAAQa,EAASM,CACtF,CACF,EAAG,EAEGW,GAAe9D,EAAA,SAAU+D,EAAU,CACvCA,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAM,WAAW,EACtB,KAAK,OAAQ,CAAC,EACd,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,CAAC,EACrB,KAAK,eAAgB,CAAC,EACtB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,kBAAkB,CACjC,EAZqB,gBAkBrB,SAASC,GAAKf,EAAMvB,EAAO,CACzBuB,EAAK,KAAK,UAAY,CACpB,IAAIA,EAAOgB,EAAO,IAAI,EACpBC,EAAQjB,EACL,KAAK,EACL,MAAM,YAAY,EAClB,QAAQ,EACXkB,EACAC,EAAO,CAAC,EACRC,EAAa,IACb5C,EAAIwB,EAAK,KAAK,GAAG,EACjBO,EAAK,WAAWP,EAAK,KAAK,IAAI,CAAC,EAC/BqB,EAAQrB,EACL,KAAK,IAAI,EACT,OAAO,OAAO,EACd,KAAK,IAAK,CAAC,EACX,KAAK,IAAKxB,CAAC,EACX,KAAK,KAAM+B,EAAK,IAAI,EACzB,QAASe,EAAI,EAAGA,EAAIL,EAAM,OAAQK,IAChCJ,EAAOD,EAAMA,EAAM,OAAS,EAAIK,CAAC,EACjCH,EAAK,KAAKD,CAAI,EACdG,EAAM,KAAKF,EAAK,KAAK,GAAG,EAAE,KAAK,CAAC,GAC5BE,EAAM,KAAK,EAAE,sBAAsB,EAAI5C,GAASyC,IAAS,UAC3DC,EAAK,IAAI,EACTE,EAAM,KAAKF,EAAK,KAAK,GAAG,EAAE,KAAK,CAAC,EAC5BD,IAAS,OACXC,EAAO,CAAC,EAAE,EAEVA,EAAO,CAACD,CAAI,EAGdG,EAAQrB,EACL,OAAO,OAAO,EACd,KAAK,IAAK,CAAC,EACX,KAAK,IAAKxB,CAAC,EACX,KAAK,KAAM4C,EAAa,IAAI,EAC5B,KAAKF,CAAI,EAGlB,CAAC,CACH,CAxCSnE,EAAAgE,GAAA,QA0CF,IAAMQ,GAAWxE,EAAA,SAAUC,EAAMwE,EAAMC,EAAa1C,EAAM,CAC/D,IAAMD,EAAW2C,EAAc5E,GAAgB,EACzC6E,EAAW1E,EAAK,OAAO,GAAG,EAChCwE,EAAK,QAAU1C,EACf4C,EAAS,KACP,SACCF,EAAK,MAAQA,EAAK,MAAQ,IAAM,IAAM,kBAAoB,WAAa1C,EAC1E,EACA,IAAM6C,EAAUD,EAAS,OAAO,GAAG,EAG7BxD,EAAWwD,EAAS,OAAO,GAAG,EAU9BE,EARM1D,EACT,OAAO,MAAM,EACb,KAAKsD,EAAK,KAAK,EACf,KAAK,KAAM,KAAK,EAChB,KAAK,qBAAsB,QAAQ,EACnC,KAAK,oBAAqB,QAAQ,EAClC,KAAK,cAAe,QAAQ,EAC5B,KAAKT,GAAMS,EAAK,KAAK,EACP,KAAK,EAAE,QAAQ,EAC1BK,EAAW9C,EAAK,UAAU,QAAUA,EAAK,SAAS,QAAQ,KAAM,EAAE,EAAIA,EAAK,SACjF,OAAAyC,EAAK,OAASI,EAAK,OAASC,EAAW,IAAM,GAAML,EAAK,QACxDA,EAAK,OAAS,KAAK,IAAIA,EAAK,OAAQA,EAAK,SAAS,EAClDA,EAAK,MAAQA,EAAK,MAAQ,EAAIA,EAAK,QAEnCtD,EAAS,KAAK,YAAa,aAAesD,EAAK,MAAQ,EAAI,KAAOA,EAAK,QAAU,EAAI,GAAG,EAGxFM,GAAWH,EAASH,EAAM1C,EAASC,CAAI,EAEhCyC,CACT,EAjCwB,YAmCXO,GAAuBhF,EAAA,SAAUC,EAAMwE,EAAMzC,EAAM,CAC9D,IAAMb,EAAWlB,EAAK,OAAO,GAAG,EAS1B4E,EARM1D,EACT,OAAO,MAAM,EACb,KAAKsD,EAAK,KAAK,EACf,KAAK,KAAM,KAAK,EAChB,KAAK,qBAAsB,QAAQ,EACnC,KAAK,oBAAqB,QAAQ,EAClC,KAAK,cAAe,QAAQ,EAC5B,KAAKT,GAAMS,EAAK,KAAK,EACP,KAAK,EAAE,QAAQ,EAC1BK,EAAW9C,EAAK,UAAU,QAAUA,EAAK,SAAS,QAAQ,KAAM,EAAE,EAAIA,EAAK,SACjF,OAAAb,EAAS,OAAO,EACT0D,EAAK,OAASC,EAAW,IAAM,GAAML,EAAK,OACnD,EAdoC,wBAgB9BM,GAAa/E,EAAA,SAAUC,EAAMwE,EAAM1C,EAAS,CAEhD9B,EACG,OAAO,MAAM,EACb,KAAK,KAAM,QAAUwE,EAAK,EAAE,EAC5B,KAAK,QAAS,iBAAmBA,EAAK,IAAI,EAC1C,KACC,IACA,MAAMA,EAAK,OAAS,CAAE,KAAK,CAACA,EAAK,OAAS,EAAI,CAAE,gBAC9CA,EAAK,MAAQ,EAAI,CACnB,cAAcA,EAAK,OAAS,CAAE,OAChC,EAEFxE,EACG,OAAO,MAAM,EACb,KAAK,QAAS,aAAe8B,CAAO,EACpC,KAAK,KAAM,CAAC,EACZ,KAAK,KAAM0C,EAAK,MAAM,EACtB,KAAK,KAAMA,EAAK,KAAK,EACrB,KAAK,KAAMA,EAAK,MAAM,CAC3B,EApBmB,cAsBZQ,EAAQ,CACb,SAAAlF,EACA,WAAAe,GACA,YAAAgB,GACA,SAAAd,GACA,UAAAK,GACA,SAAAiB,GACA,mBAAAI,GACA,WAAAE,GACA,YAAAT,GACA,aAAA2B,GACA,SAAAU,GACA,qBAAAQ,EACF,EC1iBO,IAAME,GAAOC,EAAA,SAAUC,EAAcC,EAAYC,EAAiBC,EAAkB,CAEzF,IAAMC,EAAOC,GAAU,EAEjBC,EAAcF,EAAK,YAAc,GAEvCG,EAAI,MAAM,WAAYJ,EAAQ,EAAE,EAEhC,IAAMK,EAAgBJ,EAAK,cAEvBK,EACAD,IAAkB,YACpBC,EAAiBC,EAAO,KAAOT,CAAE,GAOnC,IAAMU,GAJJH,IAAkB,UACdE,EAAOD,EAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,EACrDC,EAAO,MAAM,GAEF,OAAO,IAAMT,CAAE,EAEhCU,EAAI,OAAO,GAAG,EAId,IAAMC,EAAwBT,EAAQ,GAAG,SAAS,EAE5CU,EAAQV,EAAQ,GAAG,YAAY,EAAE,gBAAgB,EACvDI,EAAI,MAAM,OAAQK,CAAK,EAGvBE,EAAQ,aAAaH,CAAG,EAIxB,IAAMI,EAAqBZ,EAAQ,GAAG,YAAY,EAClDI,EAAI,MAAM,WAAYQ,CAAQ,EAE9B,IAAIC,EAAmB,EACnBC,EAAgB,EAEhBC,EAAS,EACTC,EAAgB,EAChBC,EAAU,GAAKd,EAEfe,EAAU,GACdF,EAAgB,GAEhB,IAAIG,EAAgB,EAChBC,EAAc,GAGlBR,EAAS,QAAQ,SAAUS,EAAiB,CAC1C,IAAMC,EAAqC,CACzC,OAAQH,EACR,MAAOE,EACP,QAASF,EACT,MAAO,IACP,QAAS,GACT,UAAWN,CACb,EACMU,EAAgBZ,EAAQ,qBAAqBH,EAAKc,EAAarB,CAAI,EACzEG,EAAI,MAAM,4BAA6BmB,CAAa,EACpDV,EAAmB,KAAK,IAAIA,EAAkBU,EAAgB,EAAE,CAClE,CAAC,EAGD,IAAIC,EAAgB,EAChBC,EAAqB,EACzBrB,EAAI,MAAM,eAAgBK,EAAM,MAAM,EAItC,OAAW,CAACiB,EAAGC,CAAI,IAAKlB,EAAM,QAAQ,EAAG,CACvC,IAAMmB,EAAwC,CAC5C,OAAQF,EACR,MAAOC,EACP,QAASA,EAAK,QACd,MAAO,IACP,QAAS,GACT,UAAWb,CACb,EACMe,EAAalB,EAAQ,qBAAqBH,EAAKoB,EAAU3B,CAAI,EACnEG,EAAI,MAAM,yBAA0ByB,CAAU,EAC9Cf,EAAgB,KAAK,IAAIA,EAAee,EAAa,EAAE,EAGvDL,EAAgB,KAAK,IAAIA,EAAeG,EAAK,OAAO,MAAM,EAE1D,IAAIG,EAAyB,EAC7B,QAAWC,KAASJ,EAAK,OAAQ,CAC/B,IAAMK,EAAY,CAChB,MAAOD,EACP,QAASJ,EAAK,QACd,OAAQA,EAAK,QACb,MAAO,IACP,QAAS,GACT,UAAW,EACb,EACAG,GAA0BnB,EAAQ,qBAAqBH,EAAKwB,EAAW/B,CAAI,CAC7E,CACAwB,EAAqB,KAAK,IAAIA,EAAoBK,CAAsB,CAC1E,CAEA1B,EAAI,MAAM,+BAAgCS,CAAgB,EAC1DT,EAAI,MAAM,4BAA6BU,CAAa,EAEhDF,GAAYA,EAAS,OAAS,EAChCA,EAAS,QAASS,GAAY,CAE5B,IAAMY,EAAkBxB,EAAM,OAAQkB,GAASA,EAAK,UAAYN,CAAO,EAEjEC,EAAqC,CACzC,OAAQH,EACR,MAAOE,EACP,QAASF,EACT,MAAO,IAAM,KAAK,IAAIc,EAAgB,OAAQ,CAAC,EAAI,GACnD,QAAS,GACT,UAAWpB,CACb,EACAT,EAAI,MAAM,cAAekB,CAAW,EACpC,IAAMY,EAAqB1B,EAAI,OAAO,GAAG,EACnC2B,EAAOxB,EAAQ,SAASuB,EAAoBZ,EAAaH,EAAelB,CAAI,EAClFG,EAAI,MAAM,qBAAsB+B,CAAI,EAEpCD,EAAmB,KAAK,YAAa,aAAajB,CAAO,KAAKD,CAAa,GAAG,EAE9EE,GAAWL,EAAmB,GAG1BoB,EAAgB,OAAS,GAC3BG,GACE5B,EACAyB,EACAd,EACAF,EACAC,EACAJ,EACAb,EACAuB,EACAC,EACAZ,EACA,EACF,EAGFI,GAAW,IAAM,KAAK,IAAIgB,EAAgB,OAAQ,CAAC,EAEnDf,EAAUF,EACVG,GACF,CAAC,GAGDC,EAAc,GACdgB,GACE5B,EACAC,EACAU,EACAF,EACAC,EACAJ,EACAb,EACAuB,EACAC,EACAZ,EACA,EACF,GAIF,IAAMwB,EAAM7B,EAAI,KAAK,EAAE,QAAQ,EAC/BJ,EAAI,MAAM,SAAUiC,CAAG,EAEnB3B,GACFF,EACG,OAAO,MAAM,EACb,KAAKE,CAAK,EACV,KAAK,IAAK2B,EAAI,MAAQ,EAAIlC,CAAW,EACrC,KAAK,YAAa,KAAK,EACvB,KAAK,cAAe,MAAM,EAC1B,KAAK,IAAK,EAAE,EAGjBY,EAASK,EAAcP,EAAmBC,EAAgB,IAAMA,EAAgB,IAE5DN,EAAI,OAAO,GAAG,EAAE,KAAK,QAAS,aAAa,EAG5D,OAAO,MAAM,EACb,KAAK,KAAML,CAAW,EACtB,KAAK,KAAMY,CAAM,EACjB,KAAK,KAAMsB,EAAI,MAAQ,EAAIlC,CAAW,EACtC,KAAK,KAAMY,CAAM,EACjB,KAAK,eAAgB,CAAC,EACtB,KAAK,SAAU,OAAO,EACtB,KAAK,aAAc,iBAAiB,EAGvCuB,GACE,OACA9B,EACAP,EAAK,UAAU,SAAW,GAC1BA,EAAK,UAAU,aAAe,EAChC,CAGF,EA9MoB,QAgNPmC,GAAYxC,EAAA,SACvB2C,EACA9B,EACA+B,EACAvB,EACAC,EACAJ,EACAb,EACAuB,EACAC,EACAZ,EACA4B,EACA,CAEA,QAAWd,KAAQlB,EAAO,CAExB,IAAMmB,EAAW,CACf,MAAOD,EAAK,KACZ,QAASa,EACT,OAAQA,EACR,MAAO,IACP,QAAS,GACT,UAAW1B,CACb,EAEAV,EAAI,MAAM,WAAYwB,CAAQ,EAG9B,IAAMc,EAAcH,EAAQ,OAAO,GAAG,EAAE,KAAK,QAAS,aAAa,EAE7DV,EADOlB,EAAQ,SAAS+B,EAAad,EAAUY,EAAcvC,CAAI,EAC/C,OASxB,GAPAG,EAAI,MAAM,wBAAyByB,CAAU,EAC7Ca,EAAY,KAAK,YAAa,aAAazB,CAAO,KAAKC,CAAO,GAAG,EAGjEJ,EAAgB,KAAK,IAAIA,EAAee,CAAU,EAG9CF,EAAK,OAAQ,CAEf,IAAMgB,EAAcJ,EAAQ,OAAO,GAAG,EAAE,KAAK,QAAS,aAAa,EAC/DK,EAAa9B,EAEjBI,GAAW,IACX0B,EACEA,EAAaC,GAAWN,EAASZ,EAAK,OAAQa,EAAcvB,EAASC,EAASjB,CAAI,EACpFiB,GAAW,IAEXyB,EACG,OAAO,MAAM,EACb,KAAK,KAAM1B,EAAU,IAAM,CAAC,EAC5B,KAAK,KAAMC,EAAUJ,CAAa,EAClC,KAAK,KAAMG,EAAU,IAAM,CAAC,EAC5B,KACC,KACAC,EACEJ,GACC2B,EAAoB3B,EAAgBD,GACrCY,EACA,GACJ,EACC,KAAK,eAAgB,CAAC,EACtB,KAAK,SAAU,OAAO,EACtB,KAAK,aAAc,iBAAiB,EACpC,KAAK,mBAAoB,KAAK,CACnC,CAEAR,EAAUA,EAAU,IAChBwB,GAAqB,CAACxC,EAAK,UAAU,mBACvCuC,GAEJ,CAGAtB,EAAUA,EAAU,EACtB,EA5EyB,aA8EZ2B,GAAajD,EAAA,SACxB2C,EACAO,EACAN,EACAvB,EACAC,EACAjB,EACA,CACA,IAAI8C,EAAiB,EACfC,EAAc9B,EACpBA,EAAUA,EAAU,IAEpB,QAAWa,KAASe,EAAQ,CAE1B,IAAMd,EAAmC,CACvC,MAAOD,EACP,QAASS,EACT,OAAQA,EACR,MAAO,IACP,QAAS,GACT,UAAW,EACb,EAGApC,EAAI,MAAM,YAAa4B,CAAS,EAEhC,IAAMiB,EAAeV,EAAQ,OAAO,GAAG,EAAE,KAAK,QAAS,cAAc,EAE/DW,EADOvC,EAAQ,SAASsC,EAAcjB,EAAWQ,EAAcvC,CAAI,EAChD,OACzB8C,EAAiBA,EAAiBG,EAClCD,EAAa,KAAK,YAAa,aAAahC,CAAO,KAAKC,CAAO,GAAG,EAClEA,EAAUA,EAAU,GAAKgC,CAC3B,CAEA,OAAAhC,EAAU8B,EACHD,CACT,EApC0B,cAsCnBI,GAAQ,CACb,QAASvD,EAAA,IAAM,CAEf,EAFS,WAGT,KAAAD,EACF,EClWA,IAAMyD,GAAcC,EAACC,GAAY,CAC/B,IAAIC,EAAW,GAEf,QAASC,EAAI,EAAGA,EAAIF,EAAQ,kBAAmBE,IAC7CF,EAAQ,YAAcE,CAAC,EAAIF,EAAQ,YAAcE,CAAC,GAAKF,EAAQ,YAAcE,CAAC,EAC1EC,GAAOH,EAAQ,YAAcE,CAAC,CAAC,EACjCF,EAAQ,YAAcE,CAAC,EAAIE,GAAQJ,EAAQ,YAAcE,CAAC,EAAG,EAAE,EAE/DF,EAAQ,YAAcE,CAAC,EAAIG,GAAOL,EAAQ,YAAcE,CAAC,EAAG,EAAE,EAIlE,QAASA,EAAI,EAAGA,EAAIF,EAAQ,kBAAmBE,IAAK,CAClD,IAAMI,EAAK,IAAM,GAAK,EAAIJ,GAC1BD,GAAY;AAAA,eACDC,EAAI,CAAC,mBAAmBA,EAAI,CAAC,mBAAmBA,EAAI,CAAC,qBAC9DA,EAAI,CACN;AAAA,cACUF,EAAQ,SAAWE,CAAC,CAAC;AAAA;AAAA,eAEpBA,EAAI,CAAC;AAAA,aACPF,EAAQ,cAAgBE,CAAC,CAAC;AAAA;AAAA,iBAEtBA,EAAI,CAAC;AAAA;AAAA,eAEPF,EAAQ,cAAgBE,CAAC,CAAC;AAAA;AAAA,oBAErBA,EAAI,CAAC;AAAA,gBACTF,EAAQ,SAAWE,CAAC,CAAC;AAAA;AAAA,kBAEnBA,EAAI,CAAC;AAAA,sBACDI,CAAE;AAAA;AAAA,eAETJ,EAAI,CAAC;AAAA,gBACJF,EAAQ,YAAcE,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,gBAKxBF,EAAQ,cAAgBE,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAUxC,CACA,OAAOD,CACT,EAnDoB,eAqDdM,GAAYR,EAACC,GACjB;AAAA;AAAA;AAAA;AAAA,IAIEF,GAAYE,CAAO,CAAC;AAAA;AAAA,YAEZA,EAAQ,IAAI;AAAA;AAAA;AAAA,YAGZA,EAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAVjB,aAyBXQ,GAAQD,GC1ER,IAAME,GAAU,CACrB,GAAAC,EACA,SAAAC,GACA,OAAAC,GACA,OAAAC,EACF", "names": ["parser", "o", "__name", "k", "v", "l", "$V0", "$V1", "$V2", "$V3", "$V4", "$V5", "$V6", "$V7", "yytext", "yyleng", "y<PERSON><PERSON>o", "yy", "yystate", "$$", "_$", "$0", "str", "hash", "error", "input", "self", "stack", "tstack", "vstack", "lstack", "table", "recovering", "TERROR", "EOF", "args", "lexer", "sharedState", "yyloc", "ranges", "popStack", "n", "lex", "token", "symbol", "preErrorSymbol", "state", "action", "a", "r", "yyval", "p", "len", "newState", "expected", "errStr", "ch", "lines", "oldLines", "past", "next", "pre", "c", "match", "indexed_rule", "backup", "tempMatch", "index", "rules", "i", "condition", "yy_", "$avoiding_name_collisions", "YY_START", "YYSTATE", "<PERSON><PERSON><PERSON>", "timeline_default", "parser", "timelineDb_exports", "__export", "addEvent", "addSection", "addTask", "addTaskOrg", "clear", "timelineDb_default", "getCommonDb", "getSections", "getTasks", "currentSection", "currentTaskId", "sections", "tasks", "rawTasks", "getCommonDb", "__name", "commonDb_exports", "clear", "addSection", "txt", "getSections", "getTasks", "allItemsProcessed", "compileTasks", "max<PERSON><PERSON><PERSON>", "iterationCount", "addTask", "period", "length", "event", "rawTask", "addEvent", "task", "addTaskOrg", "descr", "newTask", "compileTask", "pos", "allProcessed", "i", "timelineDb_default", "MAX_SECTIONS", "drawRect", "__name", "elem", "rectData", "rectElem", "drawFace", "element", "faceData", "circleElement", "face", "smile", "arc", "arc_default", "sad", "ambivalent", "drawCircle", "circleData", "drawText", "textData", "nText", "textElem", "span", "drawLabel", "txtObject", "genPoints", "x", "y", "width", "height", "cut", "polygon", "drawSection", "section", "conf", "g", "rect", "getNoteRect", "_drawTextCandidateFunc", "taskCount", "drawTask", "task", "center", "maxHeight", "drawBackgroundRect", "bounds", "getTextObj", "byText", "content", "textAttrs", "colour", "text", "_setTextAttrs", "byTspan", "taskFontSize", "taskFontFamily", "lines", "i", "dy", "byFo", "body", "toText", "fromTextAttrsDict", "key", "initGraphics", "graphics", "wrap", "select_default", "words", "word", "line", "lineHeight", "tspan", "j", "drawNode", "node", "fullSection", "nodeElem", "bkgElem", "bbox", "fontSize", "defaultBkg", "getVirtualNodeHeight", "svgDraw_default", "draw", "__name", "text", "id", "version", "diagObj", "conf", "getConfig", "LEFT_MARGIN", "log", "securityLevel", "sandboxElement", "select_default", "svg", "tasks", "title", "svgDraw_default", "sections", "maxSectionHeight", "maxTaskHeight", "depthY", "sectionBeginY", "masterX", "masterY", "sectionNumber", "hasSections", "section", "sectionNode", "sectionHeight", "maxEventCount", "maxEventLine<PERSON>ength", "i", "task", "taskNode", "taskHeight", "maxEventLineLengthTemp", "event", "eventNode", "tasksForSection", "sectionNodeWrapper", "node", "drawTasks", "box", "setupGraphViewbox", "diagram", "sectionColor", "isWithoutSections", "taskWrapper", "lineWrapper", "lineLength", "drawEvents", "events", "maxEventHeight", "eventBeginY", "eventWrapper", "eventHeight", "timelineRenderer_default", "genSections", "__name", "options", "sections", "i", "is_dark_default", "lighten_default", "darken_default", "sw", "getStyles", "styles_default", "diagram", "timelineDb_exports", "timelineRenderer_default", "timeline_default", "styles_default"]}