{"version": 3, "sources": ["../../../src/utils/imperativeState.ts"], "sourcesContent": ["/**\n * Resettable state storage.\n * @example\n * ```\n * const state = new ImperativeState(() => ({\n *   foo: undefined as string | undefined,\n *   bar: [] as number[],\n *   baz: 1 as number | undefined,\n * }));\n *\n * state.records.foo = \"hi\";\n * console.log(state.records.foo); // prints \"hi\";\n * state.reset();\n * console.log(state.records.foo); // prints \"default\";\n *\n * // typeof state.records:\n * // {\n * //   foo: string | undefined, // actual: undefined\n * //   bar: number[],           // actual: []\n * //   baz: number | undefined, // actual: 1\n * // }\n * ```\n */\nexport class ImperativeState<S> {\n  public records: S;\n\n  /**\n   * @param init - Function that creates the default state.\n   */\n  constructor(private init: () => S) {\n    this.records = this.init();\n  }\n\n  reset() {\n    this.records = this.init();\n  }\n}\n"], "mappings": ";;;;;AAuBO,IAAM,kBAAN,MAAyB;AAAA;AAAA;AAAA;AAAA,EAM9B,YAAoB,MAAe;AAAf;AAClB,SAAK,UAAU,KAAK,KAAK;AAAA,EAC3B;AAAA,EA/BF,OAuBgC;AAAA;AAAA;AAAA,EAU9B,QAAQ;AACN,SAAK,UAAU,KAAK,KAAK;AAAA,EAC3B;AACF;", "names": []}