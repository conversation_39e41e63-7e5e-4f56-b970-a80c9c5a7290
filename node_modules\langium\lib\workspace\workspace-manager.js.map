{"version": 3, "file": "workspace-manager.js", "sourceRoot": "", "sources": ["../../src/workspace/workspace-manager.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAMhF,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAC7D,OAAO,EAAE,QAAQ,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AACxE,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AAyDtD,MAAM,OAAO,uBAAuB;IAYhC,YAAY,QAAmC;QAV/C,wBAAmB,GAAiB,EAAE,CAAC;QAOpB,WAAM,GAAG,IAAI,QAAQ,EAAQ,CAAC;QAI7C,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAC;QAChD,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC5D,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,SAAS,CAAC,eAAe,CAAC;QAC1D,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC,SAAS,CAAC,kBAAkB,CAAC;QAChE,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC;IAClD,CAAC;IAED,IAAI,KAAK;QACL,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;IAC/B,CAAC;IAED,IAAI,gBAAgB;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,UAAU,CAAC,MAAwB;;QAC/B,IAAI,CAAC,OAAO,GAAG,MAAA,MAAM,CAAC,gBAAgB,mCAAI,SAAS,CAAC;IACxD,CAAC;IAED,WAAW,CAAC,OAA0B;QAClC,kEAAkE;QAClE,iGAAiG;QACjG,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,WAAC,OAAA,IAAI,CAAC,mBAAmB,CAAC,MAAA,IAAI,CAAC,OAAO,mCAAI,EAAE,EAAE,KAAK,CAAC,CAAA,EAAA,CAAC,CAAC;IAC1F,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,OAA0B,EAAE,WAAW,GAAG,iBAAiB,CAAC,IAAI;QACtF,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACrD,6FAA6F;QAC7F,uEAAuE;QACvE,MAAM,iBAAiB,CAAC,WAAW,CAAC,CAAC;QACrC,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC;IACvF,CAAC;IAED;;;OAGG;IACO,KAAK,CAAC,cAAc,CAAC,OAA0B;QACrD,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;QAChG,MAAM,SAAS,GAAsB,EAAE,CAAC;QACxC,MAAM,SAAS,GAAG,CAAC,QAAyB,EAAE,EAAE;YAC5C,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACnD,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAChD,CAAC;QACL,CAAC,CAAC;QACF,0EAA0E;QAC1E,wHAAwH;QACxH,sGAAsG;QACtG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QACvD,MAAM,OAAO,CAAC,GAAG,CACb,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAA2B,CAAC;aACpE,GAAG,CAAC,KAAK,EAAC,KAAK,EAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,KAAK,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC,CACpF,CAAC;QACF,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACtB,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;OAIG;IACO,uBAAuB,CAAC,QAA2B,EAAE,UAA+C;QAC1G,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACO,aAAa,CAAC,eAAgC;QACpD,OAAO,GAAG,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;IAC1C,CAAC;IAED;;;OAGG;IACO,KAAK,CAAC,cAAc,CAAC,eAAgC,EAAE,UAAe,EAAE,cAAwB,EAAE,SAA8C;QACtJ,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QACxE,MAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAC,KAAK,EAAC,EAAE;YACxC,IAAI,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,KAAK,EAAE,cAAc,CAAC,EAAE,CAAC;gBAC5D,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;oBACpB,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,KAAK,CAAC,GAAG,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC;gBACrF,CAAC;qBAAM,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;oBACtB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBAC5E,SAAS,CAAC,QAAQ,CAAC,CAAC;gBACxB,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC,CAAC;IACR,CAAC;IAED;;OAEG;IACO,YAAY,CAAC,gBAAiC,EAAE,KAAqB,EAAE,cAAwB;QACrG,MAAM,IAAI,GAAG,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACvB,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;YACpB,OAAO,IAAI,KAAK,cAAc,IAAI,IAAI,KAAK,KAAK,CAAC;QACrD,CAAC;aAAM,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;YACtB,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5C,OAAO,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;CAEJ"}