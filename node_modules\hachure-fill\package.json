{"name": "hachure-fill", "version": "0.5.2", "description": "Fill a polygon with lines ", "main": "bin/hachure.js", "types": "bin/hachure.d.ts", "type": "module", "scripts": {"build": "npm run lint && npm run build-ts", "build-ts": "rm -rf bin && tsc", "lint": "eslint --ext ts src"}, "repository": {"type": "git", "url": "git+https://github.com/pshihn/hachure-fill.git"}, "keywords": ["hachure", "lines", "polygon", "plotter"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/pshihn/hachure-fill/issues"}, "homepage": "https://github.com/pshihn/hachure-fill#readme", "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "esbuild": "^0.17.18", "eslint": "^8.39.0", "typescript": "^5.0.4"}}