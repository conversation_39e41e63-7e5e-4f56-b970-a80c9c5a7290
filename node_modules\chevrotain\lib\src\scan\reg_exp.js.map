{"version": 3, "file": "reg_exp.js", "sourceRoot": "", "sources": ["../../../src/scan/reg_exp.ts"], "names": [], "mappings": "AAAA,OAAO,EAGL,iBAAiB,GAKlB,MAAM,2BAA2B,CAAC;AACnC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AAC5E,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAC/D,OAAO,EAAW,YAAY,EAAE,MAAM,qBAAqB,CAAC;AAC5D,OAAO,EAAE,wBAAwB,EAAE,kBAAkB,EAAE,MAAM,YAAY,CAAC;AAE1E,MAAM,sBAAsB,GAC1B,+DAA+D,CAAC;AAClE,MAAM,CAAC,MAAM,2BAA2B,GACtC,mDAAmD,CAAC;AAEtD,MAAM,UAAU,6BAA6B,CAC3C,MAAc,EACd,mBAAmB,GAAG,KAAK;IAE3B,IAAI;QACF,MAAM,GAAG,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;QACjC,MAAM,UAAU,GAAG,yBAAyB,CAC1C,GAAG,CAAC,KAAK,EACT,EAAE,EACF,GAAG,CAAC,KAAK,CAAC,UAAU,CACrB,CAAC;QACF,OAAO,UAAU,CAAC;KACnB;IAAC,OAAO,CAAC,EAAE;QACV,0BAA0B;QAC1B,sEAAsE;QACtE,uFAAuF;QACvF,IAAI,CAAC,CAAC,OAAO,KAAK,sBAAsB,EAAE;YACxC,IAAI,mBAAmB,EAAE;gBACvB,aAAa,CACX,GAAG,2BAA2B,EAAE;oBAC9B,2BAA2B,MAAM,CAAC,QAAQ,EAAE,MAAM;oBAClD,wDAAwD;oBACxD,6DAA6D;oBAC7D,6FAA6F,CAChG,CAAC;aACH;SACF;aAAM;YACL,IAAI,SAAS,GAAG,EAAE,CAAC;YACnB,IAAI,mBAAmB,EAAE;gBACvB,SAAS;oBACP,+DAA+D;wBAC/D,iGAAiG,CAAC;aACrG;YACD,WAAW,CACT,GAAG,2BAA2B,IAAI;gBAChC,uBAAuB,MAAM,CAAC,QAAQ,EAAE,MAAM;gBAC9C,iDAAiD;gBACjD,4EAA4E;gBAC5E,SAAS,CACZ,CAAC;SACH;KACF;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,MAAM,UAAU,yBAAyB,CACvC,GAAY,EACZ,MAAsC,EACtC,UAAmB;IAEnB,QAAQ,GAAG,CAAC,IAAI,EAAE;QAChB,KAAK,aAAa;YAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACzC,yBAAyB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;aAC7D;YACD,MAAM;QACR,KAAK,aAAa;YAChB,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;YACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAEtB,uDAAuD;gBACvD,QAAQ,IAAI,CAAC,IAAI,EAAE;oBACjB,KAAK,WAAW,CAAC;oBACjB,gEAAgE;oBAChE,yEAAyE;oBACzE,yFAAyF;oBACzF,KAAK,oBAAoB,CAAC;oBAC1B,oDAAoD;oBACpD,KAAK,WAAW,CAAC;oBACjB,KAAK,mBAAmB,CAAC;oBACzB,KAAK,aAAa,CAAC;oBACnB,KAAK,cAAc,CAAC;oBACpB,KAAK,iBAAiB;wBACpB,SAAS;iBACZ;gBAED,MAAM,IAAI,GAAG,IAAI,CAAC;gBAClB,QAAQ,IAAI,CAAC,IAAI,EAAE;oBACjB,KAAK,WAAW;wBACd,uBAAuB,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;wBACxD,MAAM;oBACR,KAAK,KAAK;wBACR,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;4BAC5B,MAAM,KAAK,CAAC,sBAAsB,CAAC,CAAC;yBACrC;wBACD,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,EAAE;4BAC3B,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gCAC5B,uBAAuB,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;6BACnD;iCAAM;gCACL,QAAQ;gCACR,MAAM,KAAK,GAAG,IAAW,CAAC;gCAC1B,qCAAqC;gCACrC,IAAI,UAAU,KAAK,IAAI,EAAE;oCACvB,KACE,IAAI,SAAS,GAAG,KAAK,CAAC,IAAI,EAC1B,SAAS,IAAI,KAAK,CAAC,EAAE,EACrB,SAAS,EAAE,EACX;wCACA,uBAAuB,CAAC,SAAS,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;qCACxD;iCACF;gCACD,uEAAuE;qCAClE;oCACH,4BAA4B;oCAC5B,KACE,IAAI,SAAS,GAAG,KAAK,CAAC,IAAI,EAC1B,SAAS,IAAI,KAAK,CAAC,EAAE,IAAI,SAAS,GAAG,kBAAkB,EACvD,SAAS,EAAE,EACX;wCACA,uBAAuB,CAAC,SAAS,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;qCACxD;oCAED,yFAAyF;oCACzF,IAAI,KAAK,CAAC,EAAE,IAAI,kBAAkB,EAAE;wCAClC,MAAM,WAAW,GACf,KAAK,CAAC,IAAI,IAAI,kBAAkB;4CAC9B,CAAC,CAAC,KAAK,CAAC,IAAI;4CACZ,CAAC,CAAC,kBAAkB,CAAC;wCACzB,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC;wCAC7B,MAAM,SAAS,GAAG,wBAAwB,CAAC,WAAW,CAAC,CAAC;wCACxD,MAAM,SAAS,GAAG,wBAAwB,CAAC,WAAW,CAAC,CAAC;wCAExD,KACE,IAAI,UAAU,GAAG,SAAS,EAC1B,UAAU,IAAI,SAAS,EACvB,UAAU,EAAE,EACZ;4CACA,MAAM,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC;yCACjC;qCACF;iCACF;6BACF;wBACH,CAAC,CAAC,CAAC;wBACH,MAAM;oBACR,KAAK,OAAO;wBACV,yBAAyB,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;wBAC1D,MAAM;oBACR,0BAA0B;oBAC1B;wBACE,MAAM,KAAK,CAAC,sBAAsB,CAAC,CAAC;iBACvC;gBAED,2FAA2F;gBAC3F,MAAM,oBAAoB,GACxB,IAAI,CAAC,UAAU,KAAK,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,KAAK,CAAC,CAAC;gBACjE;gBACE,uDAAuD;gBACvD,kDAAkD;gBAClD,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC;oBAC1D,uFAAuF;oBACvF,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,oBAAoB,KAAK,KAAK,CAAC,EACzD;oBACA,MAAM;iBACP;aACF;YACD,MAAM;QACR,0BAA0B;QAC1B;YACE,MAAM,KAAK,CAAC,uBAAuB,CAAC,CAAC;KACxC;IAED,0CAA0C;IAC1C,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC;AACxB,CAAC;AAED,SAAS,uBAAuB,CAC9B,IAAY,EACZ,MAAsC,EACtC,UAAmB;IAEnB,MAAM,gBAAgB,GAAG,wBAAwB,CAAC,IAAI,CAAC,CAAC;IACxD,MAAM,CAAC,gBAAgB,CAAC,GAAG,gBAAgB,CAAC;IAE5C,IAAI,UAAU,KAAK,IAAI,EAAE;QACvB,gBAAgB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KAChC;AACH,CAAC;AAED,SAAS,gBAAgB,CACvB,IAAY,EACZ,MAAsC;IAEtC,MAAM,IAAI,GAAG,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IACvC,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;IACrC,0BAA0B;IAC1B,IAAI,SAAS,KAAK,IAAI,EAAE;QACtB,MAAM,gBAAgB,GAAG,wBAAwB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3E,MAAM,CAAC,gBAAgB,CAAC,GAAG,gBAAgB,CAAC;KAC7C;SAAM;QACL,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACrC,IAAI,SAAS,KAAK,IAAI,EAAE;YACtB,MAAM,gBAAgB,GAAG,wBAAwB,CAC/C,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CACxB,CAAC;YACF,MAAM,CAAC,gBAAgB,CAAC,GAAG,gBAAgB,CAAC;SAC7C;KACF;AACH,CAAC;AAED,SAAS,QAAQ,CAAC,OAAY,EAAE,eAAyB;IACvD,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,WAAW,EAAE,EAAE;QACzC,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACnC,OAAO,QAAQ,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;SAC/C;aAAM;YACL,QAAQ;YACR,MAAM,KAAK,GAAQ,WAAW,CAAC;YAC/B,OAAO,CACL,IAAI,CACF,eAAe,EACf,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,IAAI,UAAU,IAAI,UAAU,IAAI,KAAK,CAAC,EAAE,CACnE,KAAK,SAAS,CAChB,CAAC;SACH;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,eAAe,CAAC,GAAQ;IAC/B,MAAM,UAAU,GAAI,GAAY,CAAC,UAAU,CAAC;IAC5C,IAAI,UAAU,IAAI,UAAU,CAAC,OAAO,KAAK,CAAC,EAAE;QAC1C,OAAO,IAAI,CAAC;KACb;IAED,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;QACd,OAAO,KAAK,CAAC;KACd;IAED,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;QACvB,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,eAAe,CAAC;QACnC,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACjC,CAAC;AAED,MAAM,cAAe,SAAQ,iBAAiB;IAG5C,YAAoB,eAAyB;QAC3C,KAAK,EAAE,CAAC;QADU,oBAAe,GAAf,eAAe,CAAU;QAF7C,UAAK,GAAY,KAAK,CAAC;IAIvB,CAAC;IAED,aAAa,CAAC,IAAa;QACzB,6BAA6B;QAC7B,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;YACvB,OAAO;SACR;QAED,wEAAwE;QACxE,wGAAwG;QACxG,QAAQ,IAAI,CAAC,IAAI,EAAE;YACjB,KAAK,WAAW;gBACd,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBAC1B,OAAO;YACT,KAAK,mBAAmB;gBACtB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;gBAClC,OAAO;SACV;QAED,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED,cAAc,CAAC,IAAe;QAC5B,IAAI,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;YAC9C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;SACnB;IACH,CAAC;IAED,QAAQ,CAAC,IAAS;QAChB,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,SAAS,EAAE;gBACtD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;aACnB;SACF;aAAM;YACL,IAAI,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,SAAS,EAAE;gBACtD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;aACnB;SACF;IACH,CAAC;CACF;AAED,MAAM,UAAU,gBAAgB,CAC9B,SAAmB,EACnB,OAAwB;IAExB,IAAI,OAAO,YAAY,MAAM,EAAE;QAC7B,MAAM,GAAG,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;QAClC,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,SAAS,CAAC,CAAC;QACrD,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC1B,OAAO,cAAc,CAAC,KAAK,CAAC;KAC7B;SAAM;QACL,OAAO,CACL,IAAI,CAAM,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;YAC1B,OAAO,QAAQ,CAAC,SAAS,EAAW,IAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,KAAK,SAAS,CACjB,CAAC;KACH;AACH,CAAC"}