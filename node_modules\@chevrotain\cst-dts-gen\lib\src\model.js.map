{"version": 3, "file": "model.js", "sourceRoot": "", "sources": ["../../src/model.ts"], "names": [], "mappings": "AAaA,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAC5D,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AAExE,MAAM,UAAU,UAAU,CACxB,WAAiC;IAEjC,MAAM,SAAS,GAAG,IAAI,0BAA0B,EAAE,CAAC;IACnD,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;IACrC,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5D,CAAC;AAwBD,MAAM,0BAA2B,SAAQ,WAAW;IAClD,SAAS,CAAC,IAAU;QAClB,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEpD,MAAM,OAAO,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;QAC9D,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,YAAY,EAAE,EAAE;YACtD,MAAM,WAAW,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;YAExD,0DAA0D;YAC1D,kBAAkB;YAClB,IAAI,YAAY,GAAsB,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACpD,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBACpB,YAAY,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;aAC1C;YAED,OAAO;gBACL,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,YAAY;gBAClB,QAAQ,EAAE,WAAW;aACI,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,UAAU;SACvB,CAAC;IACJ,CAAC;IAED,gBAAgB,CAAC,IAAiB;QAChC,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAC7E,CAAC;IAED,WAAW,CAAC,IAAY;QACtB,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAC7E,CAAC;IAED,eAAe,CAAC,IAAgB;QAC9B,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAC7E,CAAC;IAED,wBAAwB,CAAC,IAAyB;QAChD,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC;IAED,qCAAqC,CACnC,IAAsC;QAEtC,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC;YAC5C,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;YACjC,SAAS,EAAE,IAAI;YACf,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC;SAC9B,CAAC,CAAC;IACL,CAAC;IAED,4BAA4B,CAAC,IAA6B;QACxD,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,UAAU,EAAE;YACpD,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC,MAAM,CAAC;YACR,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;YACjC,SAAS,EAAE,IAAI;YACf,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC;SAC9B,CAAC,CAAC;IACL,CAAC;IAED,gBAAgB,CAAC,IAAiB;QAChC,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAC7E,CAAC;IAED,aAAa,CAAC,IAAc;QAC1B,OAAO;YACL;gBACE,YAAY,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI;gBAClD,SAAS,EAAE,KAAK;gBAChB,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC;aACpB;SACF,CAAC;IACJ,CAAC;IAED,gBAAgB,CAAC,IAAiB;QAChC,OAAO;YACL;gBACE,YAAY,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,eAAe;gBAChD,SAAS,EAAE,KAAK;gBAChB,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC;aACpB;SACF,CAAC;IACJ,CAAC;IAEO,wBAAwB,CAC9B,UAAyB,EACzB,QAAuC;QAEvC,OAAO,GAAG,CACR,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAC1B,CAAC,UAAU,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,EAAE,QAAQ,CAAyB,CACzE,CAAC;IACJ,CAAC;IAEO,SAAS,CAAC,UAAyB;QACzC,OAAO,OAAO,CACZ,GAAG,CACD,UAAU,EACV,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAA2B,CACjE,CACF,CAAC;IACJ,CAAC;CACF;AAQD,SAAS,OAAO,CACd,UAA8C;IAE9C,IAAI,UAAU,YAAY,WAAW,EAAE;QACrC,OAAO;YACL,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,UAAU,CAAC,cAAc,CAAC,IAAI;SACrC,CAAC;KACH;IAED,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;AAC3B,CAAC"}