{"version": 3, "file": "looksahead.js", "sourceRoot": "", "sources": ["../../../../../src/parse/parser/traits/looksahead.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,WAAW,CAAC;AACzC,OAAO,EAAE,qBAAqB,EAAE,MAAM,cAAc,CAAC;AAMrD,OAAO,EACL,gBAAgB,EAChB,oBAAoB,EACpB,2BAA2B,EAC3B,QAAQ,EACR,YAAY,EACZ,UAAU,EACV,MAAM,GACP,MAAM,uBAAuB,CAAC;AAE/B,OAAO,EAEL,WAAW,EACX,oBAAoB,GAOrB,MAAM,kBAAkB,CAAC;AAC1B,OAAO,EAAE,oBAAoB,EAAE,MAAM,gCAAgC,CAAC;AAEtE;;GAEG;AACH,MAAM,OAAO,UAAU;IAMrB,cAAc,CAAC,MAAqB;QAClC,IAAI,CAAC,oBAAoB,GAAG,GAAG,CAAC,MAAM,EAAE,sBAAsB,CAAC;YAC7D,CAAC,CAAE,MAAM,CAAC,oBAAgC,CAAC,0DAA0D;YACrG,CAAC,CAAC,qBAAqB,CAAC,oBAAoB,CAAC;QAE/C,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,MAAM,EAAE,cAAc,CAAC;YAC7C,CAAC,CAAE,MAAM,CAAC,YAAuB,CAAC,0DAA0D;YAC5F,CAAC,CAAC,qBAAqB,CAAC,YAAY,CAAC;QAEvC,IAAI,CAAC,iBAAiB,GAAG,GAAG,CAAC,MAAM,EAAE,mBAAmB,CAAC;YACvD,CAAC,CAAE,MAAM,CAAC,iBAAwC,CAAC,0DAA0D;YAC7G,CAAC,CAAC,IAAI,oBAAoB,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;QAElE,IAAI,CAAC,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;IACvC,CAAC;IAED,4BAA4B,CAAsB,KAAa;QAC7D,OAAO,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,EAAE;YAC1B,IAAI,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC,IAAI,iBAAiB,EAAE,GAAG,EAAE;gBACtD,MAAM,EACJ,WAAW,EACX,UAAU,EACV,MAAM,EACN,mBAAmB,EACnB,gCAAgC,EAChC,uBAAuB,GACxB,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;gBAE7B,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,EAAE,EAAE;oBAChC,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;oBACvD,IAAI,CAAC,UAAU,CAAC,GAAG,oBAAoB,CAAC,QAAQ,CAAC,GAAG,OAAO,EAAE,EAAE,GAAG,EAAE;wBAClE,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,4BAA4B,CAAC;4BACjE,cAAc,EAAE,QAAQ,CAAC,GAAG;4BAC5B,IAAI,EAAE,QAAQ;4BACd,YAAY,EAAE,QAAQ,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY;4BACxD,aAAa,EAAE,QAAQ,CAAC,aAAa;4BACrC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;yBAChD,CAAC,CAAC;wBAEH,MAAM,GAAG,GAAG,2BAA2B,CACrC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,EACvC,MAAM,EACN,QAAQ,CAAC,GAAG,CACb,CAAC;wBACF,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;oBACnC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBAEH,OAAO,CAAC,UAAU,EAAE,CAAC,QAAQ,EAAE,EAAE;oBAC/B,IAAI,CAAC,oBAAoB,CACvB,QAAQ,EACR,QAAQ,CAAC,GAAG,EACZ,QAAQ,EACR,YAAY,EACZ,QAAQ,CAAC,YAAY,EACrB,oBAAoB,CAAC,QAAQ,CAAC,CAC/B,CAAC;gBACJ,CAAC,CAAC,CAAC;gBAEH,OAAO,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,EAAE;oBAC3B,IAAI,CAAC,oBAAoB,CACvB,QAAQ,EACR,QAAQ,CAAC,GAAG,EACZ,UAAU,EACV,QAAQ,EACR,QAAQ,CAAC,YAAY,EACrB,oBAAoB,CAAC,QAAQ,CAAC,CAC/B,CAAC;gBACJ,CAAC,CAAC,CAAC;gBAEH,OAAO,CAAC,mBAAmB,EAAE,CAAC,QAAQ,EAAE,EAAE;oBACxC,IAAI,CAAC,oBAAoB,CACvB,QAAQ,EACR,QAAQ,CAAC,GAAG,EACZ,gBAAgB,EAChB,qBAAqB,EACrB,QAAQ,CAAC,YAAY,EACrB,oBAAoB,CAAC,QAAQ,CAAC,CAC/B,CAAC;gBACJ,CAAC,CAAC,CAAC;gBAEH,OAAO,CAAC,gCAAgC,EAAE,CAAC,QAAQ,EAAE,EAAE;oBACrD,IAAI,CAAC,oBAAoB,CACvB,QAAQ,EACR,QAAQ,CAAC,GAAG,EACZ,oBAAoB,EACpB,kCAAkC,EAClC,QAAQ,CAAC,YAAY,EACrB,oBAAoB,CAAC,QAAQ,CAAC,CAC/B,CAAC;gBACJ,CAAC,CAAC,CAAC;gBAEH,OAAO,CAAC,uBAAuB,EAAE,CAAC,QAAQ,EAAE,EAAE;oBAC5C,IAAI,CAAC,oBAAoB,CACvB,QAAQ,EACR,QAAQ,CAAC,GAAG,EACZ,YAAY,EACZ,yBAAyB,EACzB,QAAQ,CAAC,YAAY,EACrB,oBAAoB,CAAC,QAAQ,CAAC,CAC/B,CAAC;gBACJ,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,oBAAoB,CAElB,IAAU,EACV,cAAsB,EACtB,OAAe,EACf,QAAgC,EAChC,gBAAoC,EACpC,aAAqB;QAErB,IAAI,CAAC,UAAU,CACb,GAAG,aAAa,GAAG,cAAc,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,EAAE,EAC/D,GAAG,EAAE;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,yBAAyB,CAAC;gBAC9D,cAAc;gBACd,IAAI;gBACJ,YAAY,EAAE,gBAAgB,IAAI,IAAI,CAAC,YAAY;gBACnD,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;gBAC/C,QAAQ;aACT,CAAC,CAAC;YACH,MAAM,GAAG,GAAG,2BAA2B,CACrC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EACnC,OAAO,EACP,cAAc,CACf,CAAC;YACF,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QACnC,CAAC,CACF,CAAC;IACJ,CAAC;IAED,sFAAsF;IACtF,2BAA2B,CAEzB,YAAoB,EACpB,UAAkB;QAElB,MAAM,iBAAiB,GAAQ,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACnE,OAAO,2BAA2B,CAChC,iBAAiB,EACjB,YAAY,EACZ,UAAU,CACX,CAAC;IACJ,CAAC;IAED,kBAAkB,CAAsB,GAAW;QACjD,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3C,CAAC;IAED,0BAA0B;IAC1B,cAAc,CAAsB,GAAW,EAAE,KAAe;QAC9D,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC3C,CAAC;CACF;AAED,MAAM,0BAA2B,SAAQ,WAAW;IAApD;;QACS,eAAU,GAOb;YACF,MAAM,EAAE,EAAE;YACV,WAAW,EAAE,EAAE;YACf,UAAU,EAAE,EAAE;YACd,uBAAuB,EAAE,EAAE;YAC3B,mBAAmB,EAAE,EAAE;YACvB,gCAAgC,EAAE,EAAE;SACrC,CAAC;IAsCJ,CAAC;IApCC,KAAK;QACH,IAAI,CAAC,UAAU,GAAG;YAChB,MAAM,EAAE,EAAE;YACV,WAAW,EAAE,EAAE;YACf,UAAU,EAAE,EAAE;YACd,uBAAuB,EAAE,EAAE;YAC3B,mBAAmB,EAAE,EAAE;YACvB,gCAAgC,EAAE,EAAE;SACrC,CAAC;IACJ,CAAC;IAEM,WAAW,CAAC,MAAc;QAC/B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACtC,CAAC;IAEM,4BAA4B,CAAC,OAAgC;QAClE,IAAI,CAAC,UAAU,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACxD,CAAC;IAEM,wBAAwB,CAAC,UAA+B;QAC7D,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACvD,CAAC;IAEM,qCAAqC,CAC1C,aAA+C;QAE/C,IAAI,CAAC,UAAU,CAAC,gCAAgC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACvE,CAAC;IAEM,eAAe,CAAC,IAAgB;QACrC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAEM,gBAAgB,CAAC,EAAe;QACrC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACvC,CAAC;CACF;AAED,MAAM,gBAAgB,GAAG,IAAI,0BAA0B,EAAE,CAAC;AAC1D,MAAM,UAAU,cAAc,CAAC,IAAU;IAQvC,gBAAgB,CAAC,KAAK,EAAE,CAAC;IACzB,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IAC9B,MAAM,UAAU,GAAG,gBAAgB,CAAC,UAAU,CAAC;IAC/C,6BAA6B;IAC7B,gBAAgB,CAAC,KAAK,EAAE,CAAC;IACzB,OAAY,UAAU,CAAC;AACzB,CAAC"}