{"version": 3, "sources": ["../../../src/language/pie/tokenBuilder.ts", "../../../src/language/pie/valueConverter.ts", "../../../src/language/pie/module.ts"], "sourcesContent": ["import { AbstractMermaidTokenBuilder } from '../common/index.js';\n\nexport class PieTokenBuilder extends AbstractMermaidTokenBuilder {\n  public constructor() {\n    super(['pie', 'showData']);\n  }\n}\n", "import type { CstNode, GrammarAST, ValueType } from 'langium';\n\nimport { AbstractMermaidValueConverter } from '../common/index.js';\n\nexport class PieValueConverter extends AbstractMermaidValueConverter {\n  protected runCustomConverter(\n    rule: GrammarAST.AbstractRule,\n    input: string,\n    _cstNode: CstNode\n  ): ValueType | undefined {\n    if (rule.name !== 'PIE_SECTION_LABEL') {\n      return undefined;\n    }\n    return input.replace(/\"/g, '').trim();\n  }\n}\n", "import type {\n  DefaultSharedCoreModuleContext,\n  LangiumCoreServices,\n  LangiumSharedCoreServices,\n  Module,\n  PartialLangiumCoreServices,\n} from 'langium';\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject,\n} from 'langium';\n\nimport { MermaidGeneratedSharedModule, PieGeneratedModule } from '../generated/module.js';\nimport { PieTokenBuilder } from './tokenBuilder.js';\nimport { PieValueConverter } from './valueConverter.js';\n\n/**\n * Declaration of `Pie` services.\n */\ninterface PieAddedServices {\n  parser: {\n    TokenBuilder: PieTokenBuilder;\n    ValueConverter: PieValueConverter;\n  };\n}\n\n/**\n * Union of Langium default services and `Pie` services.\n */\nexport type PieServices = LangiumCoreServices & PieAddedServices;\n\n/**\n * Dependency injection module that overrides Langium default services and\n * contributes the declared `Pie` services.\n */\nexport const PieModule: Module<PieServices, PartialLangiumCoreServices & PieAddedServices> = {\n  parser: {\n    TokenBuilder: () => new PieTokenBuilder(),\n    ValueConverter: () => new PieValueConverter(),\n  },\n};\n\n/**\n * Create the full set of services required by Langium.\n *\n * First inject the shared services by merging two modules:\n *  - Langium default shared services\n *  - Services generated by langium-cli\n *\n * Then inject the language-specific services by merging three modules:\n *  - Langium default language-specific services\n *  - Services generated by langium-cli\n *  - Services specified in this file\n * @param context - Optional module context with the LSP connection\n * @returns An object wrapping the shared services and the language-specific services\n */\nexport function createPieServices(context: DefaultSharedCoreModuleContext = EmptyFileSystem): {\n  shared: LangiumSharedCoreServices;\n  Pie: PieServices;\n} {\n  const shared: LangiumSharedCoreServices = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Pie: PieServices = inject(\n    createDefaultCoreModule({ shared }),\n    PieGeneratedModule,\n    PieModule\n  );\n  shared.ServiceRegistry.register(Pie);\n  return { shared, Pie };\n}\n"], "mappings": "mGAEO,IAAMA,EAAN,cAA8BC,CAA4B,CAFjE,MAEiE,CAAAC,EAAA,wBACxD,aAAc,CACnB,MAAM,CAAC,MAAO,UAAU,CAAC,CAC3B,CACF,ECFO,IAAMC,EAAN,cAAgCC,CAA8B,CAJrE,MAIqE,CAAAC,EAAA,0BACzD,mBACRC,EACAC,EACAC,EACuB,CACvB,GAAIF,EAAK,OAAS,oBAGlB,OAAOC,EAAM,QAAQ,KAAM,EAAE,EAAE,KAAK,CACtC,CACF,ECsBO,IAAME,EAAgF,CAC3F,OAAQ,CACN,aAAcC,EAAA,IAAM,IAAIC,EAAV,gBACd,eAAgBD,EAAA,IAAM,IAAIE,EAAV,iBAClB,CACF,EAgBO,SAASC,EAAkBC,EAA0CC,EAG1E,CACA,IAAMC,EAAoCC,EACxCC,EAA8BJ,CAAO,EACrCK,CACF,EACMC,EAAmBH,EACvBI,EAAwB,CAAE,OAAAL,CAAO,CAAC,EAClCM,EACAb,CACF,EACA,OAAAO,EAAO,gBAAgB,SAASI,CAAG,EAC5B,CAAE,OAAAJ,EAAQ,IAAAI,CAAI,CACvB,CAfgBV,EAAAG,EAAA", "names": ["PieTokenBuilder", "AbstractMermaidTokenBuilder", "__name", "PieValueConverter", "AbstractMermaidValueConverter", "__name", "rule", "input", "_cstNode", "PieModule", "__name", "PieTokenBuilder", "PieValueConverter", "createPieServices", "context", "EmptyFileSystem", "shared", "inject", "createDefaultSharedCoreModule", "MermaidGeneratedSharedModule", "Pie", "createDefaultCoreModule", "PieGeneratedModule"]}