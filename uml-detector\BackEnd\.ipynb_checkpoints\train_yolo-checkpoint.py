import torch
import pandas as pd
import matplotlib.pyplot as plt
from ultralytics import YOLO

# Liste des modèles à tester
models = ["yolov8n.pt", "yolov8s.pt", "yolov8m.pt", "yolov8l.pt", "yolov8x.pt"]

# Paramètres d'entraînement
data_yaml = "dataset/data.yaml"  # Assure-toi que ton fichier YAML est correct
epochs = 50
img_size = 640

# Stockage des résultats
results = []

# Entraînement et évaluation pour chaque modèle
for model_name in models:
    print(f"\n🔄 Entraînement de {model_name}...\n")

    # Charger le modèle
    model = YOLO(model_name)

    # Entraîner le modèle
    metrics = model.train(data=data_yaml, epochs=epochs, imgsz=img_size)

    # Obtenir les résultats après entraînement
    metrics_dict = model.val()  # Validation du modèle

    # Extraire les métriques importantes
    map50 = metrics_dict.results_dict["metrics/mAP_50(B)"]  # mAP@50
    precision = metrics_dict.results_dict["metrics/precision(B)"]
    recall = metrics_dict.results_dict["metrics/recall(B)"]

    print(f"✅ {model_name} : mAP@50 = {map50:.4f}, Precision = {precision:.4f}, Recall = {recall:.4f}")

    # Ajouter aux résultats
    results.append([model_name, map50, precision, recall])

# Convertir en DataFrame Pandas pour analyse
df = pd.DataFrame(results, columns=["Modèle", "mAP@50", "Précision", "Rappel"])
print("\n📊 Résumé des résultats :\n", df)

# 🔥 Visualisation des résultats
plt.figure(figsize=(10, 6))
plt.plot(df["Modèle"], df["mAP@50"], marker="o", label="mAP@50")
plt.plot(df["Modèle"], df["Précision"], marker="s", label="Précision")
plt.plot(df["Modèle"], df["Rappel"], marker="^", label="Rappel")
plt.xlabel("Modèles YOLOv8")
plt.ylabel("Score")
plt.title("Comparaison des modèles YOLOv8 sur UML Detection")
plt.legend()
plt.grid()
plt.show()
