/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M16 3v2.107", key: "gq8xun" }],
  [
    "path",
    {
      d: "M17 9c1 3 2.5 3.5 3.5 4.5A5 5 0 0 1 22 17a5 5 0 0 1-10 0c0-.3 0-.6.1-.9a2 2 0 1 0 3.3-2C13 11.5 16 9 17 9",
      key: "1l2pih"
    }
  ],
  [
    "path",
    { d: "M21 8.274V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.938", key: "jrnqjp" }
  ],
  ["path", { d: "M3 15h5.253", key: "xqg7rb" }],
  ["path", { d: "M3 9h8.228", key: "1ppb70" }],
  ["path", { d: "M8 15v6", key: "1stoo3" }],
  ["path", { d: "M8 3v6", key: "vlvjmk" }]
];
const BrickWallFire = createLucideIcon("brick-wall-fire", __iconNode);

export { __iconNode, BrickWallFire as default };
//# sourceMappingURL=brick-wall-fire.js.map
