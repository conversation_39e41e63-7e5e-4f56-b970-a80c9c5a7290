{"version": 3, "file": "index.esm2017.js", "sources": ["../../src/util/constants.ts", "../../src/interfaces/internal-message-payload.ts", "../../src/helpers/array-base64-translator.ts", "../../src/helpers/migrate-old-database.ts", "../../src/internals/idb-manager.ts", "../../src/util/errors.ts", "../../src/internals/requests.ts", "../../src/internals/token-manager.ts", "../../src/helpers/externalizePayload.ts", "../../src/helpers/is-console-message.ts", "../../src/helpers/logToFirelog.ts", "../../src/helpers/extract-app-config.ts", "../../src/messaging-service.ts", "../../src/helpers/registerDefaultSw.ts", "../../src/helpers/updateSwReg.ts", "../../src/helpers/updateVapidKey.ts", "../../src/api/getToken.ts", "../../src/helpers/logToScion.ts", "../../src/listeners/window-listener.ts", "../../src/helpers/register.ts", "../../src/api/isSupported.ts", "../../src/api/deleteToken.ts", "../../src/api/onMessage.ts", "../../src/api.ts", "../../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const DEFAULT_SW_PATH = '/firebase-messaging-sw.js';\nexport const DEFAULT_SW_SCOPE = '/firebase-cloud-messaging-push-scope';\n\nexport const DEFAULT_VAPID_KEY =\n  'BDOU99-h67HcA6JeFXHbSNMu7e2yNNu3RzoMj8TM4W88jITfq7ZmPvIM1Iv-4_l2LxQcYwhqby2xGpWwzjfAnG4';\n\nexport const ENDPOINT = 'https://fcmregistrations.googleapis.com/v1';\n\n/** Key of FCM Payload in Notification's data field. */\nexport const FCM_MSG = 'FCM_MSG';\n\nexport const CONSOLE_CAMPAIGN_ID = 'google.c.a.c_id';\nexport const CONSOLE_CAMPAIGN_NAME = 'google.c.a.c_l';\nexport const CONSOLE_CAMPAIGN_TIME = 'google.c.a.ts';\n/** Set to '1' if Analytics is enabled for the campaign */\nexport const CONSOLE_CAMPAIGN_ANALYTICS_ENABLED = 'google.c.a.e';\nexport const TAG = 'FirebaseMessaging: ';\nexport const MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST = 1000;\nexport const MAX_RETRIES = 3;\nexport const LOG_INTERVAL_IN_MS = 86400000; //24 hour\nexport const DEFAULT_BACKOFF_TIME_MS = 5000;\nexport const DEFAULT_REGISTRATION_TIMEOUT = 10000;\n\n// FCM log source name registered at Firelog: 'FCM_CLIENT_EVENT_LOGGING'. It uniquely identifies\n// FCM's logging configuration.\nexport const FCM_LOG_SOURCE = 1249;\n\n// Defined as in proto/messaging_event.proto. Neglecting fields that are supported.\nexport const SDK_PLATFORM_WEB = 3;\nexport const EVENT_MESSAGE_DELIVERED = 1;\n\nexport enum MessageType {\n  DATA_MESSAGE = 1,\n  DISPLAY_NOTIFICATION = 3\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n * in compliance with the License. You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under the License\n * is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n * or implied. See the License for the specific language governing permissions and limitations under\n * the License.\n */\n\nimport {\n  CONSOLE_CAMPAIGN_ANALYTICS_ENABLED,\n  CONSOLE_CAMPAIGN_ID,\n  CONSOLE_CAMPAIGN_NAME,\n  CONSOLE_CAMPAIGN_TIME\n} from '../util/constants';\n\nexport interface MessagePayloadInternal {\n  notification?: NotificationPayloadInternal;\n  data?: unknown;\n  fcmOptions?: FcmOptionsInternal;\n  messageType?: MessageType;\n  isFirebaseMessaging?: boolean;\n  from: string;\n  fcmMessageId: string;\n  productId: number;\n  // eslint-disable-next-line camelcase\n  collapse_key: string;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/Notification/actions\ninterface NotificationAction {\n  action: string;\n  icon?: string;\n  title: string;\n}\n\n/**\n * This interface defines experimental properties of NotificationOptions, that are not part of\n * the interface in the generated DOM types at https://github.com/microsoft/TypeScript-DOM-lib-generator/blob/179bdd84a944933a3103f29c2274c9f5a857b693/baselines/dom.generated.d.ts#L1012\n * https://developer.mozilla.org/en-US/docs/Web/API/Notification\n */\ninterface NotificationOptionsExperimental extends NotificationOptions {\n  readonly maxActions?: number;\n  readonly actions?: NotificationAction[];\n  readonly image?: string;\n  readonly renotify?: boolean;\n  readonly timestamp?: EpochTimeStamp;\n  readonly vibrate?: VibratePattern;\n}\n\nexport interface NotificationPayloadInternal\n  extends NotificationOptionsExperimental {\n  title: string;\n  // Supported in the Legacy Send API.\n  // See:https://firebase.google.com/docs/cloud-messaging/xmpp-server-ref.\n  // eslint-disable-next-line camelcase\n  click_action?: string;\n  icon?: string;\n}\n\n// Defined in\n// https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages#webpushfcmoptions. Note\n// that the keys are sent to the clients in snake cases which we need to convert to camel so it can\n// be exposed as a type to match the Firebase API convention.\nexport interface FcmOptionsInternal {\n  link?: string;\n\n  // eslint-disable-next-line camelcase\n  analytics_label?: string;\n}\n\nexport enum MessageType {\n  PUSH_RECEIVED = 'push-received',\n  NOTIFICATION_CLICKED = 'notification-clicked'\n}\n\n/** Additional data of a message sent from the FN Console. */\nexport interface ConsoleMessageData {\n  [CONSOLE_CAMPAIGN_ID]: string;\n  [CONSOLE_CAMPAIGN_TIME]: string;\n  [CONSOLE_CAMPAIGN_NAME]?: string;\n  [CONSOLE_CAMPAIGN_ANALYTICS_ENABLED]?: '1';\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function arrayToBase64(array: Uint8Array | ArrayBuffer): string {\n  const uint8Array = new Uint8Array(array);\n  const base64String = btoa(String.fromCharCode(...uint8Array));\n  return base64String.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n\nexport function base64ToArray(base64String: string): Uint8Array {\n  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);\n  const base64 = (base64String + padding)\n    .replace(/\\-/g, '+')\n    .replace(/_/g, '/');\n\n  const rawData = atob(base64);\n  const outputArray = new Uint8Array(rawData.length);\n\n  for (let i = 0; i < rawData.length; ++i) {\n    outputArray[i] = rawData.charCodeAt(i);\n  }\n  return outputArray;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { deleteDB, openDB } from 'idb';\n\nimport { TokenDetails } from '../interfaces/token-details';\nimport { arrayToBase64 } from './array-base64-translator';\n\n// https://github.com/firebase/firebase-js-sdk/blob/7857c212f944a2a9eb421fd4cb7370181bc034b5/packages/messaging/src/interfaces/token-details.ts\nexport interface V2TokenDetails {\n  fcmToken: string;\n  swScope: string;\n  vapidKey: string | Uint8Array;\n  subscription: PushSubscription;\n  fcmSenderId: string;\n  fcmPushSet: string;\n  createTime?: number;\n  endpoint?: string;\n  auth?: string;\n  p256dh?: string;\n}\n\n// https://github.com/firebase/firebase-js-sdk/blob/6b5b15ce4ea3df5df5df8a8b33a4e41e249c7715/packages/messaging/src/interfaces/token-details.ts\nexport interface V3TokenDetails {\n  fcmToken: string;\n  swScope: string;\n  vapidKey: Uint8Array;\n  fcmSenderId: string;\n  fcmPushSet: string;\n  endpoint: string;\n  auth: ArrayBuffer;\n  p256dh: ArrayBuffer;\n  createTime: number;\n}\n\n// https://github.com/firebase/firebase-js-sdk/blob/9567dba664732f681fa7fe60f5b7032bb1daf4c9/packages/messaging/src/interfaces/token-details.ts\nexport interface V4TokenDetails {\n  fcmToken: string;\n  swScope: string;\n  vapidKey: Uint8Array;\n  fcmSenderId: string;\n  endpoint: string;\n  auth: ArrayBufferLike;\n  p256dh: ArrayBufferLike;\n  createTime: number;\n}\n\nconst OLD_DB_NAME = 'fcm_token_details_db';\n/**\n * The last DB version of 'fcm_token_details_db' was 4. This is one higher, so that the upgrade\n * callback is called for all versions of the old DB.\n */\nconst OLD_DB_VERSION = 5;\nconst OLD_OBJECT_STORE_NAME = 'fcm_token_object_Store';\n\nexport async function migrateOldDatabase(\n  senderId: string\n): Promise<TokenDetails | null> {\n  if ('databases' in indexedDB) {\n    // indexedDb.databases() is an IndexedDB v3 API and does not exist in all browsers. TODO: Remove\n    // typecast when it lands in TS types.\n    const databases = await (\n      indexedDB as {\n        databases(): Promise<Array<{ name: string; version: number }>>;\n      }\n    ).databases();\n    const dbNames = databases.map(db => db.name);\n\n    if (!dbNames.includes(OLD_DB_NAME)) {\n      // old DB didn't exist, no need to open.\n      return null;\n    }\n  }\n\n  let tokenDetails: TokenDetails | null = null;\n\n  const db = await openDB(OLD_DB_NAME, OLD_DB_VERSION, {\n    upgrade: async (db, oldVersion, newVersion, upgradeTransaction) => {\n      if (oldVersion < 2) {\n        // Database too old, skip migration.\n        return;\n      }\n\n      if (!db.objectStoreNames.contains(OLD_OBJECT_STORE_NAME)) {\n        // Database did not exist. Nothing to do.\n        return;\n      }\n\n      const objectStore = upgradeTransaction.objectStore(OLD_OBJECT_STORE_NAME);\n      const value = await objectStore.index('fcmSenderId').get(senderId);\n      await objectStore.clear();\n\n      if (!value) {\n        // No entry in the database, nothing to migrate.\n        return;\n      }\n\n      if (oldVersion === 2) {\n        const oldDetails = value as V2TokenDetails;\n\n        if (!oldDetails.auth || !oldDetails.p256dh || !oldDetails.endpoint) {\n          return;\n        }\n\n        tokenDetails = {\n          token: oldDetails.fcmToken,\n          createTime: oldDetails.createTime ?? Date.now(),\n          subscriptionOptions: {\n            auth: oldDetails.auth,\n            p256dh: oldDetails.p256dh,\n            endpoint: oldDetails.endpoint,\n            swScope: oldDetails.swScope,\n            vapidKey:\n              typeof oldDetails.vapidKey === 'string'\n                ? oldDetails.vapidKey\n                : arrayToBase64(oldDetails.vapidKey)\n          }\n        };\n      } else if (oldVersion === 3) {\n        const oldDetails = value as V3TokenDetails;\n\n        tokenDetails = {\n          token: oldDetails.fcmToken,\n          createTime: oldDetails.createTime,\n          subscriptionOptions: {\n            auth: arrayToBase64(oldDetails.auth),\n            p256dh: arrayToBase64(oldDetails.p256dh),\n            endpoint: oldDetails.endpoint,\n            swScope: oldDetails.swScope,\n            vapidKey: arrayToBase64(oldDetails.vapidKey)\n          }\n        };\n      } else if (oldVersion === 4) {\n        const oldDetails = value as V4TokenDetails;\n\n        tokenDetails = {\n          token: oldDetails.fcmToken,\n          createTime: oldDetails.createTime,\n          subscriptionOptions: {\n            auth: arrayToBase64(oldDetails.auth),\n            p256dh: arrayToBase64(oldDetails.p256dh),\n            endpoint: oldDetails.endpoint,\n            swScope: oldDetails.swScope,\n            vapidKey: arrayToBase64(oldDetails.vapidKey)\n          }\n        };\n      }\n    }\n  });\n  db.close();\n\n  // Delete all old databases.\n  await deleteDB(OLD_DB_NAME);\n  await deleteDB('fcm_vapid_details_db');\n  await deleteDB('undefined');\n\n  return checkTokenDetails(tokenDetails) ? tokenDetails : null;\n}\n\nfunction checkTokenDetails(\n  tokenDetails: TokenDetails | null\n): tokenDetails is TokenDetails {\n  if (!tokenDetails || !tokenDetails.subscriptionOptions) {\n    return false;\n  }\n  const { subscriptionOptions } = tokenDetails;\n  return (\n    typeof tokenDetails.createTime === 'number' &&\n    tokenDetails.createTime > 0 &&\n    typeof tokenDetails.token === 'string' &&\n    tokenDetails.token.length > 0 &&\n    typeof subscriptionOptions.auth === 'string' &&\n    subscriptionOptions.auth.length > 0 &&\n    typeof subscriptionOptions.p256dh === 'string' &&\n    subscriptionOptions.p256dh.length > 0 &&\n    typeof subscriptionOptions.endpoint === 'string' &&\n    subscriptionOptions.endpoint.length > 0 &&\n    typeof subscriptionOptions.swScope === 'string' &&\n    subscriptionOptions.swScope.length > 0 &&\n    typeof subscriptionOptions.vapidKey === 'string' &&\n    subscriptionOptions.vapidKey.length > 0\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DBSchema, IDBPDatabase, deleteDB, openDB } from 'idb';\n\nimport { FirebaseInternalDependencies } from '../interfaces/internal-dependencies';\nimport { TokenDetails } from '../interfaces/token-details';\nimport { migrateOldDatabase } from '../helpers/migrate-old-database';\n\n// Exported for tests.\nexport const DATABASE_NAME = 'firebase-messaging-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-messaging-store';\n\ninterface MessagingDB extends DBSchema {\n  'firebase-messaging-store': {\n    key: string;\n    value: TokenDetails;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<MessagingDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<MessagingDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (upgradeDb, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through behavior is what we want,\n        // because if there are multiple versions between the old version and the current version, we\n        // want ALL the migrations that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            upgradeDb.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n\n/** Gets record(s) from the objectStore that match the given key. */\nexport async function dbGet(\n  firebaseDependencies: FirebaseInternalDependencies\n): Promise<TokenDetails | undefined> {\n  const key = getKey(firebaseDependencies);\n  const db = await getDbPromise();\n  const tokenDetails = (await db\n    .transaction(OBJECT_STORE_NAME)\n    .objectStore(OBJECT_STORE_NAME)\n    .get(key)) as TokenDetails;\n\n  if (tokenDetails) {\n    return tokenDetails;\n  } else {\n    // Check if there is a tokenDetails object in the old DB.\n    const oldTokenDetails = await migrateOldDatabase(\n      firebaseDependencies.appConfig.senderId\n    );\n    if (oldTokenDetails) {\n      await dbSet(firebaseDependencies, oldTokenDetails);\n      return oldTokenDetails;\n    }\n  }\n}\n\n/** Assigns or overwrites the record for the given key with the given value. */\nexport async function dbSet(\n  firebaseDependencies: FirebaseInternalDependencies,\n  tokenDetails: TokenDetails\n): Promise<TokenDetails> {\n  const key = getKey(firebaseDependencies);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).put(tokenDetails, key);\n  await tx.done;\n  return tokenDetails;\n}\n\n/** Removes record(s) from the objectStore that match the given key. */\nexport async function dbRemove(\n  firebaseDependencies: FirebaseInternalDependencies\n): Promise<void> {\n  const key = getKey(firebaseDependencies);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n  await tx.done;\n}\n\n/** Deletes the DB. Useful for tests. */\nexport async function dbDelete(): Promise<void> {\n  if (dbPromise) {\n    (await dbPromise).close();\n    await deleteDB(DATABASE_NAME);\n    dbPromise = null;\n  }\n}\n\nfunction getKey({ appConfig }: FirebaseInternalDependencies): string {\n  return appConfig.appId;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum ErrorCode {\n  MISSING_APP_CONFIG_VALUES = 'missing-app-config-values',\n  AVAILABLE_IN_WINDOW = 'only-available-in-window',\n  AVAILABLE_IN_SW = 'only-available-in-sw',\n  PERMISSION_DEFAULT = 'permission-default',\n  PERMISSION_BLOCKED = 'permission-blocked',\n  UNSUPPORTED_BROWSER = 'unsupported-browser',\n  INDEXED_DB_UNSUPPORTED = 'indexed-db-unsupported',\n  FAILED_DEFAULT_REGISTRATION = 'failed-service-worker-registration',\n  TOKEN_SUBSCRIBE_FAILED = 'token-subscribe-failed',\n  TOKEN_SUBSCRIBE_NO_TOKEN = 'token-subscribe-no-token',\n  TOKEN_UNSUBSCRIBE_FAILED = 'token-unsubscribe-failed',\n  TOKEN_UPDATE_FAILED = 'token-update-failed',\n  TOKEN_UPDATE_NO_TOKEN = 'token-update-no-token',\n  INVALID_BG_HANDLER = 'invalid-bg-handler',\n  USE_SW_AFTER_GET_TOKEN = 'use-sw-after-get-token',\n  INVALID_SW_REGISTRATION = 'invalid-sw-registration',\n  USE_VAPID_KEY_AFTER_GET_TOKEN = 'use-vapid-key-after-get-token',\n  INVALID_VAPID_KEY = 'invalid-vapid-key'\n}\n\nexport const ERROR_MAP: ErrorMap<ErrorCode> = {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]:\n    'Missing App configuration value: \"{$valueName}\"',\n  [ErrorCode.AVAILABLE_IN_WINDOW]:\n    'This method is available in a Window context.',\n  [ErrorCode.AVAILABLE_IN_SW]:\n    'This method is available in a service worker context.',\n  [ErrorCode.PERMISSION_DEFAULT]:\n    'The notification permission was not granted and dismissed instead.',\n  [ErrorCode.PERMISSION_BLOCKED]:\n    'The notification permission was not granted and blocked instead.',\n  [ErrorCode.UNSUPPORTED_BROWSER]:\n    \"This browser doesn't support the API's required to use the Firebase SDK.\",\n  [ErrorCode.INDEXED_DB_UNSUPPORTED]:\n    \"This browser doesn't support indexedDb.open() (ex. Safari iFrame, Firefox Private Browsing, etc)\",\n  [ErrorCode.FAILED_DEFAULT_REGISTRATION]:\n    'We are unable to register the default service worker. {$browserErrorMessage}',\n  [ErrorCode.TOKEN_SUBSCRIBE_FAILED]:\n    'A problem occurred while subscribing the user to FCM: {$errorInfo}',\n  [ErrorCode.TOKEN_SUBSCRIBE_NO_TOKEN]:\n    'FCM returned no token when subscribing the user to push.',\n  [ErrorCode.TOKEN_UNSUBSCRIBE_FAILED]:\n    'A problem occurred while unsubscribing the ' +\n    'user from FCM: {$errorInfo}',\n  [ErrorCode.TOKEN_UPDATE_FAILED]:\n    'A problem occurred while updating the user from FCM: {$errorInfo}',\n  [ErrorCode.TOKEN_UPDATE_NO_TOKEN]:\n    'FCM returned no token when updating the user to push.',\n  [ErrorCode.USE_SW_AFTER_GET_TOKEN]:\n    'The useServiceWorker() method may only be called once and must be ' +\n    'called before calling getToken() to ensure your service worker is used.',\n  [ErrorCode.INVALID_SW_REGISTRATION]:\n    'The input to useServiceWorker() must be a ServiceWorkerRegistration.',\n  [ErrorCode.INVALID_BG_HANDLER]:\n    'The input to setBackgroundMessageHandler() must be a function.',\n  [ErrorCode.INVALID_VAPID_KEY]: 'The public VAPID key must be a string.',\n  [ErrorCode.USE_VAPID_KEY_AFTER_GET_TOKEN]:\n    'The usePublicVapidKey() method may only be called once and must be ' +\n    'called before calling getToken() to ensure your VAPID key is used.'\n};\n\ninterface ErrorParams {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]: {\n    valueName: string;\n  };\n  [ErrorCode.FAILED_DEFAULT_REGISTRATION]: { browserErrorMessage: string };\n  [ErrorCode.TOKEN_SUBSCRIBE_FAILED]: { errorInfo: string };\n  [ErrorCode.TOKEN_UNSUBSCRIBE_FAILED]: { errorInfo: string };\n  [ErrorCode.TOKEN_UPDATE_FAILED]: { errorInfo: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  'messaging',\n  'Messaging',\n  ERROR_MAP\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DEFAULT_VAPID_KEY, ENDPOINT } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { SubscriptionOptions, TokenDetails } from '../interfaces/token-details';\n\nimport { AppConfig } from '../interfaces/app-config';\nimport { FirebaseInternalDependencies } from '../interfaces/internal-dependencies';\n\nexport interface ApiResponse {\n  token?: string;\n  error?: { message: string };\n}\n\nexport interface ApiRequestBody {\n  web: {\n    endpoint: string;\n    p256dh: string;\n    auth: string;\n    applicationPubKey?: string;\n  };\n}\n\nexport async function requestGetToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  subscriptionOptions: SubscriptionOptions\n): Promise<string> {\n  const headers = await getHeaders(firebaseDependencies);\n  const body = getBody(subscriptionOptions);\n\n  const subscribeOptions = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  let responseData: ApiResponse;\n  try {\n    const response = await fetch(\n      getEndpoint(firebaseDependencies.appConfig),\n      subscribeOptions\n    );\n    responseData = await response.json();\n  } catch (err) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_SUBSCRIBE_FAILED, {\n      errorInfo: (err as Error)?.toString()\n    });\n  }\n\n  if (responseData.error) {\n    const message = responseData.error.message;\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_SUBSCRIBE_FAILED, {\n      errorInfo: message\n    });\n  }\n\n  if (!responseData.token) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_SUBSCRIBE_NO_TOKEN);\n  }\n\n  return responseData.token;\n}\n\nexport async function requestUpdateToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  tokenDetails: TokenDetails\n): Promise<string> {\n  const headers = await getHeaders(firebaseDependencies);\n  const body = getBody(tokenDetails.subscriptionOptions!);\n\n  const updateOptions = {\n    method: 'PATCH',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  let responseData: ApiResponse;\n  try {\n    const response = await fetch(\n      `${getEndpoint(firebaseDependencies.appConfig)}/${tokenDetails.token}`,\n      updateOptions\n    );\n    responseData = await response.json();\n  } catch (err) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UPDATE_FAILED, {\n      errorInfo: (err as Error)?.toString()\n    });\n  }\n\n  if (responseData.error) {\n    const message = responseData.error.message;\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UPDATE_FAILED, {\n      errorInfo: message\n    });\n  }\n\n  if (!responseData.token) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UPDATE_NO_TOKEN);\n  }\n\n  return responseData.token;\n}\n\nexport async function requestDeleteToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  token: string\n): Promise<void> {\n  const headers = await getHeaders(firebaseDependencies);\n\n  const unsubscribeOptions = {\n    method: 'DELETE',\n    headers\n  };\n\n  try {\n    const response = await fetch(\n      `${getEndpoint(firebaseDependencies.appConfig)}/${token}`,\n      unsubscribeOptions\n    );\n    const responseData: ApiResponse = await response.json();\n    if (responseData.error) {\n      const message = responseData.error.message;\n      throw ERROR_FACTORY.create(ErrorCode.TOKEN_UNSUBSCRIBE_FAILED, {\n        errorInfo: message\n      });\n    }\n  } catch (err) {\n    throw ERROR_FACTORY.create(ErrorCode.TOKEN_UNSUBSCRIBE_FAILED, {\n      errorInfo: (err as Error)?.toString()\n    });\n  }\n}\n\nfunction getEndpoint({ projectId }: AppConfig): string {\n  return `${ENDPOINT}/projects/${projectId!}/registrations`;\n}\n\nasync function getHeaders({\n  appConfig,\n  installations\n}: FirebaseInternalDependencies): Promise<Headers> {\n  const authToken = await installations.getToken();\n\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': appConfig.apiKey!,\n    'x-goog-firebase-installations-auth': `FIS ${authToken}`\n  });\n}\n\nfunction getBody({\n  p256dh,\n  auth,\n  endpoint,\n  vapidKey\n}: SubscriptionOptions): ApiRequestBody {\n  const body: ApiRequestBody = {\n    web: {\n      endpoint,\n      auth,\n      p256dh\n    }\n  };\n\n  if (vapidKey !== DEFAULT_VAPID_KEY) {\n    body.web.applicationPubKey = vapidKey;\n  }\n\n  return body;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SubscriptionOptions, TokenDetails } from '../interfaces/token-details';\nimport {\n  arrayToBase64,\n  base64ToArray\n} from '../helpers/array-base64-translator';\nimport { dbGet, dbRemove, dbSet } from './idb-manager';\nimport {\n  requestDeleteToken,\n  requestGetToken,\n  requestUpdateToken\n} from './requests';\n\nimport { FirebaseInternalDependencies } from '../interfaces/internal-dependencies';\nimport { MessagingService } from '../messaging-service';\n\n// UpdateRegistration will be called once every week.\nconst TOKEN_EXPIRATION_MS = 7 * 24 * 60 * 60 * 1000; // 7 days\n\nexport async function getTokenInternal(\n  messaging: MessagingService\n): Promise<string> {\n  const pushSubscription = await getPushSubscription(\n    messaging.swRegistration!,\n    messaging.vapidKey!\n  );\n\n  const subscriptionOptions: SubscriptionOptions = {\n    vapidKey: messaging.vapidKey!,\n    swScope: messaging.swRegistration!.scope,\n    endpoint: pushSubscription.endpoint,\n    auth: arrayToBase64(pushSubscription.getKey('auth')!),\n    p256dh: arrayToBase64(pushSubscription.getKey('p256dh')!)\n  };\n\n  const tokenDetails = await dbGet(messaging.firebaseDependencies);\n  if (!tokenDetails) {\n    // No token, get a new one.\n    return getNewToken(messaging.firebaseDependencies, subscriptionOptions);\n  } else if (\n    !isTokenValid(tokenDetails.subscriptionOptions!, subscriptionOptions)\n  ) {\n    // Invalid token, get a new one.\n    try {\n      await requestDeleteToken(\n        messaging.firebaseDependencies!,\n        tokenDetails.token\n      );\n    } catch (e) {\n      // Suppress errors because of #2364\n      console.warn(e);\n    }\n\n    return getNewToken(messaging.firebaseDependencies!, subscriptionOptions);\n  } else if (Date.now() >= tokenDetails.createTime + TOKEN_EXPIRATION_MS) {\n    // Weekly token refresh\n    return updateToken(messaging, {\n      token: tokenDetails.token,\n      createTime: Date.now(),\n      subscriptionOptions\n    });\n  } else {\n    // Valid token, nothing to do.\n    return tokenDetails.token;\n  }\n}\n\n/**\n * This method deletes the token from the database, unsubscribes the token from FCM, and unregisters\n * the push subscription if it exists.\n */\nexport async function deleteTokenInternal(\n  messaging: MessagingService\n): Promise<boolean> {\n  const tokenDetails = await dbGet(messaging.firebaseDependencies);\n  if (tokenDetails) {\n    await requestDeleteToken(\n      messaging.firebaseDependencies,\n      tokenDetails.token\n    );\n    await dbRemove(messaging.firebaseDependencies);\n  }\n\n  // Unsubscribe from the push subscription.\n  const pushSubscription =\n    await messaging.swRegistration!.pushManager.getSubscription();\n  if (pushSubscription) {\n    return pushSubscription.unsubscribe();\n  }\n\n  // If there's no SW, consider it a success.\n  return true;\n}\n\nasync function updateToken(\n  messaging: MessagingService,\n  tokenDetails: TokenDetails\n): Promise<string> {\n  try {\n    const updatedToken = await requestUpdateToken(\n      messaging.firebaseDependencies,\n      tokenDetails\n    );\n\n    const updatedTokenDetails: TokenDetails = {\n      ...tokenDetails,\n      token: updatedToken,\n      createTime: Date.now()\n    };\n\n    await dbSet(messaging.firebaseDependencies, updatedTokenDetails);\n    return updatedToken;\n  } catch (e) {\n    throw e;\n  }\n}\n\nasync function getNewToken(\n  firebaseDependencies: FirebaseInternalDependencies,\n  subscriptionOptions: SubscriptionOptions\n): Promise<string> {\n  const token = await requestGetToken(\n    firebaseDependencies,\n    subscriptionOptions\n  );\n  const tokenDetails: TokenDetails = {\n    token,\n    createTime: Date.now(),\n    subscriptionOptions\n  };\n  await dbSet(firebaseDependencies, tokenDetails);\n  return tokenDetails.token;\n}\n\n/**\n * Gets a PushSubscription for the current user.\n */\nasync function getPushSubscription(\n  swRegistration: ServiceWorkerRegistration,\n  vapidKey: string\n): Promise<PushSubscription> {\n  const subscription = await swRegistration.pushManager.getSubscription();\n  if (subscription) {\n    return subscription;\n  }\n\n  return swRegistration.pushManager.subscribe({\n    userVisibleOnly: true,\n    // Chrome <= 75 doesn't support base64-encoded VAPID key. For backward compatibility, VAPID key\n    // submitted to pushManager#subscribe must be of type Uint8Array.\n    applicationServerKey: base64ToArray(vapidKey)\n  });\n}\n\n/**\n * Checks if the saved tokenDetails object matches the configuration provided.\n */\nfunction isTokenValid(\n  dbOptions: SubscriptionOptions,\n  currentOptions: SubscriptionOptions\n): boolean {\n  const isVapidKeyEqual = currentOptions.vapidKey === dbOptions.vapidKey;\n  const isEndpointEqual = currentOptions.endpoint === dbOptions.endpoint;\n  const isAuthEqual = currentOptions.auth === dbOptions.auth;\n  const isP256dhEqual = currentOptions.p256dh === dbOptions.p256dh;\n\n  return isVapidKeyEqual && isEndpointEqual && isAuthEqual && isP256dhEqual;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MessagePayload } from '../interfaces/public-types';\nimport { MessagePayloadInternal } from '../interfaces/internal-message-payload';\n\nexport function externalizePayload(\n  internalPayload: MessagePayloadInternal\n): MessagePayload {\n  const payload: MessagePayload = {\n    from: internalPayload.from,\n    // eslint-disable-next-line camelcase\n    collapseKey: internalPayload.collapse_key,\n    // eslint-disable-next-line camelcase\n    messageId: internalPayload.fcmMessageId\n  } as MessagePayload;\n\n  propagateNotificationPayload(payload, internalPayload);\n  propagateDataPayload(payload, internalPayload);\n  propagateFcmOptions(payload, internalPayload);\n\n  return payload;\n}\n\nfunction propagateNotificationPayload(\n  payload: MessagePayload,\n  messagePayloadInternal: MessagePayloadInternal\n): void {\n  if (!messagePayloadInternal.notification) {\n    return;\n  }\n\n  payload.notification = {};\n\n  const title = messagePayloadInternal.notification!.title;\n  if (!!title) {\n    payload.notification!.title = title;\n  }\n\n  const body = messagePayloadInternal.notification!.body;\n  if (!!body) {\n    payload.notification!.body = body;\n  }\n\n  const image = messagePayloadInternal.notification!.image;\n  if (!!image) {\n    payload.notification!.image = image;\n  }\n\n  const icon = messagePayloadInternal.notification!.icon;\n  if (!!icon) {\n    payload.notification!.icon = icon;\n  }\n}\n\nfunction propagateDataPayload(\n  payload: MessagePayload,\n  messagePayloadInternal: MessagePayloadInternal\n): void {\n  if (!messagePayloadInternal.data) {\n    return;\n  }\n\n  payload.data = messagePayloadInternal.data as { [key: string]: string };\n}\n\nfunction propagateFcmOptions(\n  payload: MessagePayload,\n  messagePayloadInternal: MessagePayloadInternal\n): void {\n  // fcmOptions.link value is written into notification.click_action. see more in b/232072111\n  if (\n    !messagePayloadInternal.fcmOptions &&\n    !messagePayloadInternal.notification?.click_action\n  ) {\n    return;\n  }\n\n  payload.fcmOptions = {};\n\n  const link =\n    messagePayloadInternal.fcmOptions?.link ??\n    messagePayloadInternal.notification?.click_action;\n\n  if (!!link) {\n    payload.fcmOptions!.link = link;\n  }\n\n  // eslint-disable-next-line camelcase\n  const analyticsLabel = messagePayloadInternal.fcmOptions?.analytics_label;\n  if (!!analyticsLabel) {\n    payload.fcmOptions!.analyticsLabel = analyticsLabel;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSOLE_CAMPAIGN_ID } from '../util/constants';\nimport { ConsoleMessageData } from '../interfaces/internal-message-payload';\n\nexport function isConsoleMessage(data: unknown): data is ConsoleMessageData {\n  // This message has a campaign ID, meaning it was sent using the Firebase Console.\n  return typeof data === 'object' && !!data && CONSOLE_CAMPAIGN_ID in data;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  DEFAULT_BACKOFF_TIME_MS,\n  EVENT_MESSAGE_DELIVERED,\n  FCM_LOG_SOURCE,\n  LOG_INTERVAL_IN_MS,\n  MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST,\n  MAX_RETRIES,\n  MessageType,\n  SDK_PLATFORM_WEB\n} from '../util/constants';\nimport {\n  FcmEvent,\n  LogEvent,\n  LogRequest,\n  LogResponse,\n  ComplianceData\n} from '../interfaces/logging-types';\n\nimport { MessagePayloadInternal } from '../interfaces/internal-message-payload';\nimport { MessagingService } from '../messaging-service';\n\nconst LOG_ENDPOINT = 'https://play.google.com/log?format=json_proto3';\n\nconst FCM_TRANSPORT_KEY = _mergeStrings(\n  'AzSCbw63g1R0nCw85jG8',\n  'Iaya3yLKwmgvh7cF0q4'\n);\n\nexport function startLoggingService(messaging: MessagingService): void {\n  if (!messaging.isLogServiceStarted) {\n    _processQueue(messaging, LOG_INTERVAL_IN_MS);\n    messaging.isLogServiceStarted = true;\n  }\n}\n\n/**\n *\n * @param messaging the messaging instance.\n * @param offsetInMs this method execute after `offsetInMs` elapsed .\n */\nexport function _processQueue(\n  messaging: MessagingService,\n  offsetInMs: number\n): void {\n  setTimeout(async () => {\n    if (!messaging.deliveryMetricsExportedToBigQueryEnabled) {\n      // flush events and terminate logging service\n      messaging.logEvents = [];\n      messaging.isLogServiceStarted = false;\n\n      return;\n    }\n\n    if (!messaging.logEvents.length) {\n      return _processQueue(messaging, LOG_INTERVAL_IN_MS);\n    }\n\n    await _dispatchLogEvents(messaging);\n  }, offsetInMs);\n}\n\nexport async function _dispatchLogEvents(\n  messaging: MessagingService\n): Promise<void> {\n  for (\n    let i = 0, n = messaging.logEvents.length;\n    i < n;\n    i += MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST\n  ) {\n    const logRequest = _createLogRequest(\n      messaging.logEvents.slice(i, i + MAX_NUMBER_OF_EVENTS_PER_LOG_REQUEST)\n    );\n\n    let retryCount = 0,\n      response = {} as Response;\n\n    do {\n      try {\n        response = await fetch(\n          LOG_ENDPOINT.concat('&key=', FCM_TRANSPORT_KEY),\n          {\n            method: 'POST',\n            body: JSON.stringify(logRequest)\n          }\n        );\n\n        // don't retry on 200s or non retriable errors\n        if (response.ok || (!response.ok && !isRetriableError(response))) {\n          break;\n        }\n\n        if (!response.ok && isRetriableError(response)) {\n          // rethrow to retry with quota\n          throw new Error(\n            'a retriable Non-200 code is returned in fetch to Firelog endpoint. Retry'\n          );\n        }\n      } catch (error) {\n        const isLastAttempt = retryCount === MAX_RETRIES;\n        if (isLastAttempt) {\n          // existing the do-while interactive retry logic because retry quota has reached.\n          break;\n        }\n      }\n\n      let delayInMs: number;\n      try {\n        delayInMs = Number(\n          ((await response.json()) as LogResponse).nextRequestWaitMillis\n        );\n      } catch (e) {\n        delayInMs = DEFAULT_BACKOFF_TIME_MS;\n      }\n\n      await new Promise(resolve => setTimeout(resolve, delayInMs));\n\n      retryCount++;\n    } while (retryCount < MAX_RETRIES);\n  }\n\n  messaging.logEvents = [];\n  // schedule for next logging\n  _processQueue(messaging, LOG_INTERVAL_IN_MS);\n}\n\nfunction isRetriableError(response: Response): boolean {\n  const httpStatus = response.status;\n\n  return (\n    httpStatus === 429 ||\n    httpStatus === 500 ||\n    httpStatus === 503 ||\n    httpStatus === 504\n  );\n}\n\nexport async function stageLog(\n  messaging: MessagingService,\n  internalPayload: MessagePayloadInternal\n): Promise<void> {\n  const fcmEvent = createFcmEvent(\n    internalPayload,\n    await messaging.firebaseDependencies.installations.getId()\n  );\n\n  createAndEnqueueLogEvent(messaging, fcmEvent, internalPayload.productId);\n}\n\nfunction createFcmEvent(\n  internalPayload: MessagePayloadInternal,\n  fid: string\n): FcmEvent {\n  const fcmEvent = {} as FcmEvent;\n\n  /* eslint-disable camelcase */\n  // some fields should always be non-null. Still check to ensure.\n  if (!!internalPayload.from) {\n    fcmEvent.project_number = internalPayload.from;\n  }\n\n  if (!!internalPayload.fcmMessageId) {\n    fcmEvent.message_id = internalPayload.fcmMessageId;\n  }\n\n  fcmEvent.instance_id = fid;\n\n  if (!!internalPayload.notification) {\n    fcmEvent.message_type = MessageType.DISPLAY_NOTIFICATION.toString();\n  } else {\n    fcmEvent.message_type = MessageType.DATA_MESSAGE.toString();\n  }\n\n  fcmEvent.sdk_platform = SDK_PLATFORM_WEB.toString();\n  fcmEvent.package_name = self.origin.replace(/(^\\w+:|^)\\/\\//, '');\n\n  if (!!internalPayload.collapse_key) {\n    fcmEvent.collapse_key = internalPayload.collapse_key;\n  }\n\n  fcmEvent.event = EVENT_MESSAGE_DELIVERED.toString();\n\n  if (!!internalPayload.fcmOptions?.analytics_label) {\n    fcmEvent.analytics_label = internalPayload.fcmOptions?.analytics_label;\n  }\n\n  /* eslint-enable camelcase */\n  return fcmEvent;\n}\n\nfunction createAndEnqueueLogEvent(\n  messaging: MessagingService,\n  fcmEvent: FcmEvent,\n  productId: number\n): void {\n  const logEvent = {} as LogEvent;\n\n  /* eslint-disable camelcase */\n  logEvent.event_time_ms = Math.floor(Date.now()).toString();\n  logEvent.source_extension_json_proto3 = JSON.stringify({\n    messaging_client_event: fcmEvent\n  });\n\n  if (!!productId) {\n    logEvent.compliance_data = buildComplianceData(productId);\n  }\n  // eslint-disable-next-line camelcase\n\n  messaging.logEvents.push(logEvent);\n}\n\nfunction buildComplianceData(productId: number): ComplianceData {\n  const complianceData: ComplianceData = {\n    privacy_context: {\n      prequest: {\n        origin_associated_product_id: productId\n      }\n    }\n  };\n\n  return complianceData;\n}\n\nexport function _createLogRequest(logEventQueue: LogEvent[]): LogRequest {\n  const logRequest = {} as LogRequest;\n\n  /* eslint-disable camelcase */\n  logRequest.log_source = FCM_LOG_SOURCE.toString();\n  logRequest.log_event = logEventQueue;\n  /* eslint-enable camelcase */\n\n  return logRequest;\n}\n\nexport function _mergeStrings(s1: string, s2: string): string {\n  const resultArray = [];\n  for (let i = 0; i < s1.length; i++) {\n    resultArray.push(s1.charAt(i));\n    if (i < s2.length) {\n      resultArray.push(s2.charAt(i));\n    }\n  }\n\n  return resultArray.join('');\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { FirebaseApp, FirebaseOptions } from '@firebase/app';\n\nimport { AppConfig } from '../interfaces/app-config';\nimport { FirebaseError } from '@firebase/util';\n\nexport function extractAppConfig(app: FirebaseApp): AppConfig {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration Object');\n  }\n\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n\n  // Required app config keys\n  const configKeys: ReadonlyArray<keyof FirebaseOptions> = [\n    'projectId',\n    'apiKey',\n    'appId',\n    'messagingSenderId'\n  ];\n\n  const { options } = app;\n  for (const keyName of configKeys) {\n    if (!options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n\n  return {\n    appName: app.name,\n    projectId: options.projectId!,\n    apiKey: options.apiKey!,\n    appId: options.appId!,\n    senderId: options.messagingSenderId!\n  };\n}\n\nfunction getMissingValueError(valueName: string): FirebaseError {\n  return ERROR_FACTORY.create(ErrorCode.MISSING_APP_CONFIG_VALUES, {\n    valueName\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport { MessagePayload, NextFn, Observer } from './interfaces/public-types';\n\nimport { FirebaseAnalyticsInternalName } from '@firebase/analytics-interop-types';\nimport { FirebaseInternalDependencies } from './interfaces/internal-dependencies';\nimport { LogEvent } from './interfaces/logging-types';\nimport { Provider } from '@firebase/component';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\nimport { extractAppConfig } from './helpers/extract-app-config';\n\nexport class MessagingService implements _FirebaseService {\n  readonly app!: FirebaseApp;\n  readonly firebaseDependencies!: FirebaseInternalDependencies;\n\n  swRegistration?: ServiceWorkerRegistration;\n  vapidKey?: string;\n  // logging is only done with end user consent. Default to false.\n  deliveryMetricsExportedToBigQueryEnabled: boolean = false;\n\n  onBackgroundMessageHandler:\n    | NextFn<MessagePayload>\n    | Observer<MessagePayload>\n    | null = null;\n\n  onMessageHandler: NextFn<MessagePayload> | Observer<MessagePayload> | null =\n    null;\n\n  logEvents: LogEvent[] = [];\n  isLogServiceStarted: boolean = false;\n\n  constructor(\n    app: FirebaseApp,\n    installations: _FirebaseInstallationsInternal,\n    analyticsProvider: Provider<FirebaseAnalyticsInternalName>\n  ) {\n    const appConfig = extractAppConfig(app);\n\n    this.firebaseDependencies = {\n      app,\n      appConfig,\n      installations,\n      analyticsProvider\n    };\n  }\n\n  _delete(): Promise<void> {\n    return Promise.resolve();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  DEFAULT_REGISTRATION_TIMEOUT,\n  DEFAULT_SW_PATH,\n  DEFAULT_SW_SCOPE\n} from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport { MessagingService } from '../messaging-service';\n\nexport async function registerDefaultSw(\n  messaging: MessagingService\n): Promise<void> {\n  try {\n    messaging.swRegistration = await navigator.serviceWorker.register(\n      DEFAULT_SW_PATH,\n      {\n        scope: DEFAULT_SW_SCOPE\n      }\n    );\n\n    // The timing when browser updates sw when sw has an update is unreliable from experiment. It\n    // leads to version conflict when the SDK upgrades to a newer version in the main page, but sw\n    // is stuck with the old version. For example,\n    // https://github.com/firebase/firebase-js-sdk/issues/2590 The following line reliably updates\n    // sw if there was an update.\n    messaging.swRegistration.update().catch(() => {\n      /* it is non blocking and we don't care if it failed */\n    });\n    await waitForRegistrationActive(messaging.swRegistration);\n  } catch (e) {\n    throw ERROR_FACTORY.create(ErrorCode.FAILED_DEFAULT_REGISTRATION, {\n      browserErrorMessage: (e as Error)?.message\n    });\n  }\n}\n\n/**\n * Waits for registration to become active. MDN documentation claims that\n * a service worker registration should be ready to use after awaiting\n * navigator.serviceWorker.register() but that doesn't seem to be the case in\n * practice, causing the SDK to throw errors when calling\n * swRegistration.pushManager.subscribe() too soon after register(). The only\n * solution seems to be waiting for the service worker registration `state`\n * to become \"active\".\n */\nasync function waitForRegistrationActive(\n  registration: ServiceWorkerRegistration\n): Promise<void> {\n  return new Promise<void>((resolve, reject) => {\n    const rejectTimeout = setTimeout(\n      () =>\n        reject(\n          new Error(\n            `Service worker not registered after ${DEFAULT_REGISTRATION_TIMEOUT} ms`\n          )\n        ),\n      DEFAULT_REGISTRATION_TIMEOUT\n    );\n    const incomingSw = registration.installing || registration.waiting;\n    if (registration.active) {\n      clearTimeout(rejectTimeout);\n      resolve();\n    } else if (incomingSw) {\n      incomingSw.onstatechange = ev => {\n        if ((ev.target as ServiceWorker)?.state === 'activated') {\n          incomingSw.onstatechange = null;\n          clearTimeout(rejectTimeout);\n          resolve();\n        }\n      };\n    } else {\n      clearTimeout(rejectTimeout);\n      reject(new Error('No incoming service worker found.'));\n    }\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport { MessagingService } from '../messaging-service';\nimport { registerDefaultSw } from './registerDefaultSw';\n\nexport async function updateSwReg(\n  messaging: MessagingService,\n  swRegistration?: ServiceWorkerRegistration | undefined\n): Promise<void> {\n  if (!swRegistration && !messaging.swRegistration) {\n    await registerDefaultSw(messaging);\n  }\n\n  if (!swRegistration && !!messaging.swRegistration) {\n    return;\n  }\n\n  if (!(swRegistration instanceof ServiceWorkerRegistration)) {\n    throw ERROR_FACTORY.create(ErrorCode.INVALID_SW_REGISTRATION);\n  }\n\n  messaging.swRegistration = swRegistration;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DEFAULT_VAPID_KEY } from '../util/constants';\nimport { MessagingService } from '../messaging-service';\n\nexport async function updateVapidKey(\n  messaging: MessagingService,\n  vapidKey?: string | undefined\n): Promise<void> {\n  if (!!vapidKey) {\n    messaging.vapidKey = vapidKey;\n  } else if (!messaging.vapidKey) {\n    messaging.vapidKey = DEFAULT_VAPID_KEY;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport { MessagingService } from '../messaging-service';\nimport { getTokenInternal } from '../internals/token-manager';\nimport { updateSwReg } from '../helpers/updateSwReg';\nimport { updateVapidKey } from '../helpers/updateVapidKey';\nimport { GetTokenOptions } from '../interfaces/public-types';\n\nexport async function getToken(\n  messaging: MessagingService,\n  options?: GetTokenOptions\n): Promise<string> {\n  if (!navigator) {\n    throw ERROR_FACTORY.create(ErrorCode.AVAILABLE_IN_WINDOW);\n  }\n\n  if (Notification.permission === 'default') {\n    await Notification.requestPermission();\n  }\n\n  if (Notification.permission !== 'granted') {\n    throw ERROR_FACTORY.create(ErrorCode.PERMISSION_BLOCKED);\n  }\n\n  await updateVapidKey(messaging, options?.vapidKey);\n  await updateSwReg(messaging, options?.serviceWorkerRegistration);\n\n  return getTokenInternal(messaging);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CONSOLE_CAMPAIGN_ID,\n  CONSOLE_CAMPAIGN_NAME,\n  CONSOLE_CAMPAIGN_TIME\n} from '../util/constants';\nimport {\n  ConsoleMessageData,\n  MessageType\n} from '../interfaces/internal-message-payload';\n\nimport { MessagingService } from '../messaging-service';\n\nexport async function logToScion(\n  messaging: MessagingService,\n  messageType: MessageType,\n  data: ConsoleMessageData\n): Promise<void> {\n  const eventType = getEventType(messageType);\n  const analytics =\n    await messaging.firebaseDependencies.analyticsProvider.get();\n  analytics.logEvent(eventType, {\n    /* eslint-disable camelcase */\n    message_id: data[CONSOLE_CAMPAIGN_ID],\n    message_name: data[CONSOLE_CAMPAIGN_NAME],\n    message_time: data[CONSOLE_CAMPAIGN_TIME],\n    message_device_time: Math.floor(Date.now() / 1000)\n    /* eslint-enable camelcase */\n  });\n}\n\nfunction getEventType(messageType: MessageType): string {\n  switch (messageType) {\n    case MessageType.NOTIFICATION_CLICKED:\n      return 'notification_open';\n    case MessageType.PUSH_RECEIVED:\n      return 'notification_foreground';\n    default:\n      throw new Error();\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  MessagePayloadInternal,\n  MessageType\n} from '../interfaces/internal-message-payload';\n\nimport { CONSOLE_CAMPAIGN_ANALYTICS_ENABLED } from '../util/constants';\nimport { MessagingService } from '../messaging-service';\nimport { externalizePayload } from '../helpers/externalizePayload';\nimport { isConsoleMessage } from '../helpers/is-console-message';\nimport { logToScion } from '../helpers/logToScion';\n\nexport async function messageEventListener(\n  messaging: MessagingService,\n  event: MessageEvent\n): Promise<void> {\n  const internalPayload = event.data as MessagePayloadInternal;\n\n  if (!internalPayload.isFirebaseMessaging) {\n    return;\n  }\n\n  if (\n    messaging.onMessageHandler &&\n    internalPayload.messageType === MessageType.PUSH_RECEIVED\n  ) {\n    if (typeof messaging.onMessageHandler === 'function') {\n      messaging.onMessageHandler(externalizePayload(internalPayload));\n    } else {\n      messaging.onMessageHandler.next(externalizePayload(internalPayload));\n    }\n  }\n\n  // Log to Scion if applicable\n  const dataPayload = internalPayload.data;\n  if (\n    isConsoleMessage(dataPayload) &&\n    dataPayload[CONSOLE_CAMPAIGN_ANALYTICS_ENABLED] === '1'\n  ) {\n    await logToScion(messaging, internalPayload.messageType!, dataPayload);\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType,\n  InstanceFactory\n} from '@firebase/component';\nimport {\n  onNotificationClick,\n  onPush,\n  onSubChange\n} from '../listeners/sw-listeners';\n\nimport { GetTokenOptions } from '../interfaces/public-types';\nimport { MessagingInternal } from '@firebase/messaging-interop-types';\nimport { MessagingService } from '../messaging-service';\nimport { ServiceWorkerGlobalScope } from '../util/sw-types';\nimport { _registerComponent, registerVersion } from '@firebase/app';\nimport { getToken } from '../api/getToken';\nimport { messageEventListener } from '../listeners/window-listener';\n\nimport { name, version } from '../../package.json';\n\nconst WindowMessagingFactory: InstanceFactory<'messaging'> = (\n  container: ComponentContainer\n) => {\n  const messaging = new MessagingService(\n    container.getProvider('app').getImmediate(),\n    container.getProvider('installations-internal').getImmediate(),\n    container.getProvider('analytics-internal')\n  );\n\n  navigator.serviceWorker.addEventListener('message', e =>\n    messageEventListener(messaging as MessagingService, e)\n  );\n\n  return messaging;\n};\n\nconst WindowMessagingInternalFactory: InstanceFactory<'messaging-internal'> = (\n  container: ComponentContainer\n) => {\n  const messaging = container\n    .getProvider('messaging')\n    .getImmediate() as MessagingService;\n\n  const messagingInternal: MessagingInternal = {\n    getToken: (options?: GetTokenOptions) => getToken(messaging, options)\n  };\n\n  return messagingInternal;\n};\n\ndeclare const self: ServiceWorkerGlobalScope;\nconst SwMessagingFactory: InstanceFactory<'messaging'> = (\n  container: ComponentContainer\n) => {\n  const messaging = new MessagingService(\n    container.getProvider('app').getImmediate(),\n    container.getProvider('installations-internal').getImmediate(),\n    container.getProvider('analytics-internal')\n  );\n\n  self.addEventListener('push', e => {\n    e.waitUntil(onPush(e, messaging as MessagingService));\n  });\n  self.addEventListener('pushsubscriptionchange', e => {\n    e.waitUntil(onSubChange(e, messaging as MessagingService));\n  });\n  self.addEventListener('notificationclick', e => {\n    e.waitUntil(onNotificationClick(e));\n  });\n\n  return messaging;\n};\n\nexport function registerMessagingInWindow(): void {\n  _registerComponent(\n    new Component('messaging', WindowMessagingFactory, ComponentType.PUBLIC)\n  );\n\n  _registerComponent(\n    new Component(\n      'messaging-internal',\n      WindowMessagingInternalFactory,\n      ComponentType.PRIVATE\n    )\n  );\n\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n\n/**\n * The messaging instance registered in sw is named differently than that of in client. This is\n * because both `registerMessagingInWindow` and `registerMessagingInSw` would be called in\n * `messaging-compat` and component with the same name can only be registered once.\n */\nexport function registerMessagingInSw(): void {\n  _registerComponent(\n    new Component('messaging-sw', SwMessagingFactory, ComponentType.PUBLIC)\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  areCookiesEnabled,\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable\n} from '@firebase/util';\n\n/**\n * Checks if all required APIs exist in the browser.\n * @returns a Promise that resolves to a boolean.\n *\n * @public\n */\nexport async function isWindowSupported(): Promise<boolean> {\n  try {\n    // This throws if open() is unsupported, so adding it to the conditional\n    // statement below can cause an uncaught error.\n    await validateIndexedDBOpenable();\n  } catch (e) {\n    return false;\n  }\n  // firebase-js-sdk/issues/2393 reveals that idb#open in Safari iframe and Firefox private browsing\n  // might be prohibited to run. In these contexts, an error would be thrown during the messaging\n  // instantiating phase, informing the developers to import/call isSupported for special handling.\n  return (\n    typeof window !== 'undefined' &&\n    isIndexedDBAvailable() &&\n    areCookiesEnabled() &&\n    'serviceWorker' in navigator &&\n    'PushManager' in window &&\n    'Notification' in window &&\n    'fetch' in window &&\n    ServiceWorkerRegistration.prototype.hasOwnProperty('showNotification') &&\n    PushSubscription.prototype.hasOwnProperty('getKey')\n  );\n}\n\n/**\n * Checks whether all required APIs exist within SW Context\n * @returns a Promise that resolves to a boolean.\n *\n * @public\n */\nexport async function isSwSupported(): Promise<boolean> {\n  // firebase-js-sdk/issues/2393 reveals that idb#open in Safari iframe and Firefox private browsing\n  // might be prohibited to run. In these contexts, an error would be thrown during the messaging\n  // instantiating phase, informing the developers to import/call isSupported for special handling.\n  return (\n    isIndexedDBAvailable() &&\n    (await validateIndexedDBOpenable()) &&\n    'PushManager' in self &&\n    'Notification' in self &&\n    ServiceWorkerRegistration.prototype.hasOwnProperty('showNotification') &&\n    PushSubscription.prototype.hasOwnProperty('getKey')\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport { MessagingService } from '../messaging-service';\nimport { deleteTokenInternal } from '../internals/token-manager';\nimport { registerDefaultSw } from '../helpers/registerDefaultSw';\n\nexport async function deleteToken(\n  messaging: MessagingService\n): Promise<boolean> {\n  if (!navigator) {\n    throw ERROR_FACTORY.create(ErrorCode.AVAILABLE_IN_WINDOW);\n  }\n\n  if (!messaging.swRegistration) {\n    await registerDefaultSw(messaging);\n  }\n\n  return deleteTokenInternal(messaging);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nimport {\n  MessagePayload,\n  NextFn,\n  Observer,\n  Unsubscribe\n} from '../interfaces/public-types';\nimport { MessagingService } from '../messaging-service';\n\nexport function onMessage(\n  messaging: MessagingService,\n  nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n): Unsubscribe {\n  if (!navigator) {\n    throw ERROR_FACTORY.create(ErrorCode.AVAILABLE_IN_WINDOW);\n  }\n\n  messaging.onMessageHandler = nextOrObserver;\n\n  return () => {\n    messaging.onMessageHandler = null;\n  };\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from './util/errors';\nimport { FirebaseApp, _getProvider, getApp } from '@firebase/app';\nimport {\n  GetTokenOptions,\n  MessagePayload,\n  Messaging\n} from './interfaces/public-types';\nimport {\n  NextFn,\n  Observer,\n  Unsubscribe,\n  getModularInstance\n} from '@firebase/util';\nimport { isSwSupported, isWindowSupported } from './api/isSupported';\n\nimport { MessagingService } from './messaging-service';\nimport { deleteToken as _deleteToken } from './api/deleteToken';\nimport { getToken as _getToken } from './api/getToken';\nimport { onBackgroundMessage as _onBackgroundMessage } from './api/onBackgroundMessage';\nimport { onMessage as _onMessage } from './api/onMessage';\nimport { _setDeliveryMetricsExportedToBigQueryEnabled } from './api/setDeliveryMetricsExportedToBigQueryEnabled';\n\n/**\n * Retrieves a Firebase Cloud Messaging instance.\n *\n * @returns The Firebase Cloud Messaging instance associated with the provided firebase app.\n *\n * @public\n */\nexport function getMessagingInWindow(app: FirebaseApp = getApp()): Messaging {\n  // Conscious decision to make this async check non-blocking during the messaging instance\n  // initialization phase for performance consideration. An error would be thrown latter for\n  // developer's information. Developers can then choose to import and call `isSupported` for\n  // special handling.\n  isWindowSupported().then(\n    isSupported => {\n      // If `isWindowSupported()` resolved, but returned false.\n      if (!isSupported) {\n        throw ERROR_FACTORY.create(ErrorCode.UNSUPPORTED_BROWSER);\n      }\n    },\n    _ => {\n      // If `isWindowSupported()` rejected.\n      throw ERROR_FACTORY.create(ErrorCode.INDEXED_DB_UNSUPPORTED);\n    }\n  );\n  return _getProvider(getModularInstance(app), 'messaging').getImmediate();\n}\n\n/**\n * Retrieves a Firebase Cloud Messaging instance.\n *\n * @returns The Firebase Cloud Messaging instance associated with the provided firebase app.\n *\n * @public\n */\nexport function getMessagingInSw(app: FirebaseApp = getApp()): Messaging {\n  // Conscious decision to make this async check non-blocking during the messaging instance\n  // initialization phase for performance consideration. An error would be thrown latter for\n  // developer's information. Developers can then choose to import and call `isSupported` for\n  // special handling.\n  isSwSupported().then(\n    isSupported => {\n      // If `isSwSupported()` resolved, but returned false.\n      if (!isSupported) {\n        throw ERROR_FACTORY.create(ErrorCode.UNSUPPORTED_BROWSER);\n      }\n    },\n    _ => {\n      // If `isSwSupported()` rejected.\n      throw ERROR_FACTORY.create(ErrorCode.INDEXED_DB_UNSUPPORTED);\n    }\n  );\n  return _getProvider(getModularInstance(app), 'messaging-sw').getImmediate();\n}\n\n/**\n * Subscribes the {@link Messaging} instance to push notifications. Returns a Firebase Cloud\n * Messaging registration token that can be used to send push messages to that {@link Messaging}\n * instance.\n *\n * If notification permission isn't already granted, this method asks the user for permission. The\n * returned promise rejects if the user does not allow the app to show notifications.\n *\n * @param messaging - The {@link Messaging} instance.\n * @param options - Provides an optional vapid key and an optional service worker registration.\n *\n * @returns The promise resolves with an FCM registration token.\n *\n * @public\n */\nexport async function getToken(\n  messaging: Messaging,\n  options?: GetTokenOptions\n): Promise<string> {\n  messaging = getModularInstance(messaging);\n  return _getToken(messaging as MessagingService, options);\n}\n\n/**\n * Deletes the registration token associated with this {@link Messaging} instance and unsubscribes\n * the {@link Messaging} instance from the push subscription.\n *\n * @param messaging - The {@link Messaging} instance.\n *\n * @returns The promise resolves when the token has been successfully deleted.\n *\n * @public\n */\nexport function deleteToken(messaging: Messaging): Promise<boolean> {\n  messaging = getModularInstance(messaging);\n  return _deleteToken(messaging as MessagingService);\n}\n\n/**\n * When a push message is received and the user is currently on a page for your origin, the\n * message is passed to the page and an `onMessage()` event is dispatched with the payload of\n * the push message.\n *\n *\n * @param messaging - The {@link Messaging} instance.\n * @param nextOrObserver - This function, or observer object with `next` defined,\n *     is called when a message is received and the user is currently viewing your page.\n * @returns To stop listening for messages execute this returned function.\n *\n * @public\n */\nexport function onMessage(\n  messaging: Messaging,\n  nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n): Unsubscribe {\n  messaging = getModularInstance(messaging);\n  return _onMessage(messaging as MessagingService, nextOrObserver);\n}\n\n/**\n * Called when a message is received while the app is in the background. An app is considered to be\n * in the background if no active window is displayed.\n *\n * @param messaging - The {@link Messaging} instance.\n * @param nextOrObserver - This function, or observer object with `next` defined, is called when a\n * message is received and the app is currently in the background.\n *\n * @returns To stop listening for messages execute this returned function\n *\n * @public\n */\nexport function onBackgroundMessage(\n  messaging: Messaging,\n  nextOrObserver: NextFn<MessagePayload> | Observer<MessagePayload>\n): Unsubscribe {\n  messaging = getModularInstance(messaging);\n  return _onBackgroundMessage(messaging as MessagingService, nextOrObserver);\n}\n\n/**\n * Enables or disables Firebase Cloud Messaging message delivery metrics export to BigQuery. By\n * default, message delivery metrics are not exported to BigQuery. Use this method to enable or\n * disable the export at runtime.\n *\n * @param messaging - The `FirebaseMessaging` instance.\n * @param enable - Whether Firebase Cloud Messaging should export message delivery metrics to\n * BigQuery.\n *\n * @public\n */\nexport function experimentalSetDeliveryMetricsExportedToBigQueryEnabled(\n  messaging: Messaging,\n  enable: boolean\n): void {\n  messaging = getModularInstance(messaging);\n  return _setDeliveryMetricsExportedToBigQueryEnabled(messaging, enable);\n}\n", "/**\n * The Firebase Cloud Messaging Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport '@firebase/installations';\n\nimport { Messaging } from './interfaces/public-types';\nimport { registerMessagingInWindow } from './helpers/register';\n\nexport {\n  getToken,\n  deleteToken,\n  onMessage,\n  getMessagingInWindow as getMessaging\n} from './api';\nexport { isWindowSupported as isSupported } from './api/isSupported';\nexport * from './interfaces/public-types';\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    'messaging': Messaging;\n  }\n}\n\nregisterMessagingInWindow();\n"], "names": ["MessageType", "getToken", "deleteToken", "onMessage", "_getToken", "_deleteToken", "_onMessage"], "mappings": ";;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAEI,MAAM,eAAe,GAAG,2BAA2B,CAAC;AACpD,MAAM,gBAAgB,GAAG,sCAAsC,CAAC;AAEhE,MAAM,iBAAiB,GAC5B,yFAAyF,CAAC;AAErF,MAAM,QAAQ,GAAG,4CAA4C,CAAC;AAK9D,MAAM,mBAAmB,GAAG,iBAAiB,CAAC;AAC9C,MAAM,qBAAqB,GAAG,gBAAgB,CAAC;AAC/C,MAAM,qBAAqB,GAAG,eAAe,CAAC;AACrD;AACO,MAAM,kCAAkC,GAAG,cAAc,CAAC;AAM1D,MAAM,4BAA4B,GAAG,KAAK,CAAC;AAUlD,IAAYA,aAGX,CAAA;AAHD,CAAA,UAAY,WAAW,EAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAgB,CAAA;AAChB,IAAA,WAAA,CAAA,WAAA,CAAA,sBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,sBAAwB,CAAA;AAC1B,CAAC,EAHWA,aAAW,KAAXA,aAAW,GAGtB,EAAA,CAAA,CAAA;;ACnDD;;;;;;;;;;;;;AAaG;AAgEH,IAAY,WAGX,CAAA;AAHD,CAAA,UAAY,WAAW,EAAA;AACrB,IAAA,WAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;AAC/B,IAAA,WAAA,CAAA,sBAAA,CAAA,GAAA,sBAA6C,CAAA;AAC/C,CAAC,EAHW,WAAW,KAAX,WAAW,GAGtB,EAAA,CAAA,CAAA;;AChFD;;;;;;;;;;;;;;;AAeG;AAEG,SAAU,aAAa,CAAC,KAA+B,EAAA;AAC3D,IAAA,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;AACzC,IAAA,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC;IAC9D,OAAO,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAChF,CAAC;AAEK,SAAU,aAAa,CAAC,YAAoB,EAAA;IAChD,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AAChE,IAAA,MAAM,MAAM,GAAG,CAAC,YAAY,GAAG,OAAO;AACnC,SAAA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AACnB,SAAA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAEtB,IAAA,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7B,MAAM,WAAW,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAEnD,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;QACvC,WAAW,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;KACxC;AACD,IAAA,OAAO,WAAW,CAAC;AACrB;;ACpCA;;;;;;;;;;;;;;;AAeG;AA8CH,MAAM,WAAW,GAAG,sBAAsB,CAAC;AAC3C;;;AAGG;AACH,MAAM,cAAc,GAAG,CAAC,CAAC;AACzB,MAAM,qBAAqB,GAAG,wBAAwB,CAAC;AAEhD,eAAe,kBAAkB,CACtC,QAAgB,EAAA;AAEhB,IAAA,IAAI,WAAW,IAAI,SAAS,EAAE;;;AAG5B,QAAA,MAAM,SAAS,GAAG,MAChB,SAGD,CAAC,SAAS,EAAE,CAAC;AACd,QAAA,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;;AAElC,YAAA,OAAO,IAAI,CAAC;SACb;KACF;IAED,IAAI,YAAY,GAAwB,IAAI,CAAC;IAE7C,MAAM,EAAE,GAAG,MAAM,MAAM,CAAC,WAAW,EAAE,cAAc,EAAE;QACnD,OAAO,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,UAAU,EAAE,kBAAkB,KAAI;;AAChE,YAAA,IAAI,UAAU,GAAG,CAAC,EAAE;;gBAElB,OAAO;aACR;YAED,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC,qBAAqB,CAAC,EAAE;;gBAExD,OAAO;aACR;YAED,MAAM,WAAW,GAAG,kBAAkB,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;AAC1E,YAAA,MAAM,KAAK,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACnE,YAAA,MAAM,WAAW,CAAC,KAAK,EAAE,CAAC;YAE1B,IAAI,CAAC,KAAK,EAAE;;gBAEV,OAAO;aACR;AAED,YAAA,IAAI,UAAU,KAAK,CAAC,EAAE;gBACpB,MAAM,UAAU,GAAG,KAAuB,CAAC;AAE3C,gBAAA,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;oBAClE,OAAO;iBACR;AAED,gBAAA,YAAY,GAAG;oBACb,KAAK,EAAE,UAAU,CAAC,QAAQ;oBAC1B,UAAU,EAAE,MAAA,UAAU,CAAC,UAAU,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,IAAI,CAAC,GAAG,EAAE;AAC/C,oBAAA,mBAAmB,EAAE;wBACnB,IAAI,EAAE,UAAU,CAAC,IAAI;wBACrB,MAAM,EAAE,UAAU,CAAC,MAAM;wBACzB,QAAQ,EAAE,UAAU,CAAC,QAAQ;wBAC7B,OAAO,EAAE,UAAU,CAAC,OAAO;AAC3B,wBAAA,QAAQ,EACN,OAAO,UAAU,CAAC,QAAQ,KAAK,QAAQ;8BACnC,UAAU,CAAC,QAAQ;AACrB,8BAAE,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC;AACzC,qBAAA;iBACF,CAAC;aACH;AAAM,iBAAA,IAAI,UAAU,KAAK,CAAC,EAAE;gBAC3B,MAAM,UAAU,GAAG,KAAuB,CAAC;AAE3C,gBAAA,YAAY,GAAG;oBACb,KAAK,EAAE,UAAU,CAAC,QAAQ;oBAC1B,UAAU,EAAE,UAAU,CAAC,UAAU;AACjC,oBAAA,mBAAmB,EAAE;AACnB,wBAAA,IAAI,EAAE,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC;AACpC,wBAAA,MAAM,EAAE,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC;wBACxC,QAAQ,EAAE,UAAU,CAAC,QAAQ;wBAC7B,OAAO,EAAE,UAAU,CAAC,OAAO;AAC3B,wBAAA,QAAQ,EAAE,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC;AAC7C,qBAAA;iBACF,CAAC;aACH;AAAM,iBAAA,IAAI,UAAU,KAAK,CAAC,EAAE;gBAC3B,MAAM,UAAU,GAAG,KAAuB,CAAC;AAE3C,gBAAA,YAAY,GAAG;oBACb,KAAK,EAAE,UAAU,CAAC,QAAQ;oBAC1B,UAAU,EAAE,UAAU,CAAC,UAAU;AACjC,oBAAA,mBAAmB,EAAE;AACnB,wBAAA,IAAI,EAAE,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC;AACpC,wBAAA,MAAM,EAAE,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC;wBACxC,QAAQ,EAAE,UAAU,CAAC,QAAQ;wBAC7B,OAAO,EAAE,UAAU,CAAC,OAAO;AAC3B,wBAAA,QAAQ,EAAE,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC;AAC7C,qBAAA;iBACF,CAAC;aACH;SACF;AACF,KAAA,CAAC,CAAC;IACH,EAAE,CAAC,KAAK,EAAE,CAAC;;AAGX,IAAA,MAAM,QAAQ,CAAC,WAAW,CAAC,CAAC;AAC5B,IAAA,MAAM,QAAQ,CAAC,sBAAsB,CAAC,CAAC;AACvC,IAAA,MAAM,QAAQ,CAAC,WAAW,CAAC,CAAC;AAE5B,IAAA,OAAO,iBAAiB,CAAC,YAAY,CAAC,GAAG,YAAY,GAAG,IAAI,CAAC;AAC/D,CAAC;AAED,SAAS,iBAAiB,CACxB,YAAiC,EAAA;IAEjC,IAAI,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE;AACtD,QAAA,OAAO,KAAK,CAAC;KACd;AACD,IAAA,MAAM,EAAE,mBAAmB,EAAE,GAAG,YAAY,CAAC;AAC7C,IAAA,QACE,OAAO,YAAY,CAAC,UAAU,KAAK,QAAQ;QAC3C,YAAY,CAAC,UAAU,GAAG,CAAC;AAC3B,QAAA,OAAO,YAAY,CAAC,KAAK,KAAK,QAAQ;AACtC,QAAA,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;AAC7B,QAAA,OAAO,mBAAmB,CAAC,IAAI,KAAK,QAAQ;AAC5C,QAAA,mBAAmB,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;AACnC,QAAA,OAAO,mBAAmB,CAAC,MAAM,KAAK,QAAQ;AAC9C,QAAA,mBAAmB,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC;AACrC,QAAA,OAAO,mBAAmB,CAAC,QAAQ,KAAK,QAAQ;AAChD,QAAA,mBAAmB,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;AACvC,QAAA,OAAO,mBAAmB,CAAC,OAAO,KAAK,QAAQ;AAC/C,QAAA,mBAAmB,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;AACtC,QAAA,OAAO,mBAAmB,CAAC,QAAQ,KAAK,QAAQ;AAChD,QAAA,mBAAmB,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EACvC;AACJ;;ACpMA;;;;;;;;;;;;;;;AAeG;AAQH;AACO,MAAM,aAAa,GAAG,6BAA6B,CAAC;AAC3D,MAAM,gBAAgB,GAAG,CAAC,CAAC;AAC3B,MAAM,iBAAiB,GAAG,0BAA0B,CAAC;AASrD,IAAI,SAAS,GAA8C,IAAI,CAAC;AAChE,SAAS,YAAY,GAAA;IACnB,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,SAAS,GAAG,MAAM,CAAC,aAAa,EAAE,gBAAgB,EAAE;AAClD,YAAA,OAAO,EAAE,CAAC,SAAS,EAAE,UAAU,KAAI;;;;;gBAKjC,QAAQ,UAAU;AAChB,oBAAA,KAAK,CAAC;AACJ,wBAAA,SAAS,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;iBAClD;aACF;AACF,SAAA,CAAC,CAAC;KACJ;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;AACO,eAAe,KAAK,CACzB,oBAAkD,EAAA;AAElD,IAAA,MAAM,GAAG,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACzC,IAAA,MAAM,EAAE,GAAG,MAAM,YAAY,EAAE,CAAC;AAChC,IAAA,MAAM,YAAY,IAAI,MAAM,EAAE;SAC3B,WAAW,CAAC,iBAAiB,CAAC;SAC9B,WAAW,CAAC,iBAAiB,CAAC;AAC9B,SAAA,GAAG,CAAC,GAAG,CAAC,CAAiB,CAAC;IAE7B,IAAI,YAAY,EAAE;AAChB,QAAA,OAAO,YAAY,CAAC;KACrB;SAAM;;QAEL,MAAM,eAAe,GAAG,MAAM,kBAAkB,CAC9C,oBAAoB,CAAC,SAAS,CAAC,QAAQ,CACxC,CAAC;QACF,IAAI,eAAe,EAAE;AACnB,YAAA,MAAM,KAAK,CAAC,oBAAoB,EAAE,eAAe,CAAC,CAAC;AACnD,YAAA,OAAO,eAAe,CAAC;SACxB;KACF;AACH,CAAC;AAED;AACO,eAAe,KAAK,CACzB,oBAAkD,EAClD,YAA0B,EAAA;AAE1B,IAAA,MAAM,GAAG,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACzC,IAAA,MAAM,EAAE,GAAG,MAAM,YAAY,EAAE,CAAC;IAChC,MAAM,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;AAC1D,IAAA,MAAM,EAAE,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;IAC/D,MAAM,EAAE,CAAC,IAAI,CAAC;AACd,IAAA,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;AACO,eAAe,QAAQ,CAC5B,oBAAkD,EAAA;AAElD,IAAA,MAAM,GAAG,GAAG,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACzC,IAAA,MAAM,EAAE,GAAG,MAAM,YAAY,EAAE,CAAC;IAChC,MAAM,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;IAC1D,MAAM,EAAE,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACpD,MAAM,EAAE,CAAC,IAAI,CAAC;AAChB,CAAC;AAWD,SAAS,MAAM,CAAC,EAAE,SAAS,EAAgC,EAAA;IACzD,OAAO,SAAS,CAAC,KAAK,CAAC;AACzB;;AClHA;;;;;;;;;;;;;;;AAeG;AAyBI,MAAM,SAAS,GAAwB;AAC5C,IAAA,CAAA,2BAAA,6CACE,iDAAiD;AACnD,IAAA,CAAA,0BAAA,uCACE,+CAA+C;AACjD,IAAA,CAAA,sBAAA,mCACE,uDAAuD;AACzD,IAAA,CAAA,oBAAA,sCACE,oEAAoE;AACtE,IAAA,CAAA,oBAAA,sCACE,kEAAkE;AACpE,IAAA,CAAA,qBAAA,uCACE,0EAA0E;AAC5E,IAAA,CAAA,wBAAA,0CACE,kGAAkG;AACpG,IAAA,CAAA,oCAAA,+CACE,8EAA8E;AAChF,IAAA,CAAA,wBAAA,0CACE,oEAAoE;AACtE,IAAA,CAAA,0BAAA,4CACE,0DAA0D;AAC5D,IAAA,CAAA,0BAAA,4CACE,6CAA6C;QAC7C,6BAA6B;AAC/B,IAAA,CAAA,qBAAA,uCACE,mEAAmE;AACrE,IAAA,CAAA,uBAAA,yCACE,uDAAuD;AACzD,IAAA,CAAA,wBAAA,0CACE,oEAAoE;QACpE,yEAAyE;AAC3E,IAAA,CAAA,yBAAA,2CACE,sEAAsE;AACxE,IAAA,CAAA,oBAAA,sCACE,gEAAgE;AAClE,IAAA,CAAA,mBAAA,qCAA+B,wCAAwC;AACvE,IAAA,CAAA,+BAAA,iDACE,qEAAqE;QACrE,oEAAoE;CACvE,CAAC;AAYK,MAAM,aAAa,GAAG,IAAI,YAAY,CAC3C,WAAW,EACX,WAAW,EACX,SAAS,CACV;;AC/FD;;;;;;;;;;;;;;;AAeG;AAuBI,eAAe,eAAe,CACnC,oBAAkD,EAClD,mBAAwC,EAAA;AAExC,IAAA,MAAM,OAAO,GAAG,MAAM,UAAU,CAAC,oBAAoB,CAAC,CAAC;AACvD,IAAA,MAAM,IAAI,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAE1C,IAAA,MAAM,gBAAgB,GAAG;AACvB,QAAA,MAAM,EAAE,MAAM;QACd,OAAO;AACP,QAAA,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KAC3B,CAAC;AAEF,IAAA,IAAI,YAAyB,CAAC;AAC9B,IAAA,IAAI;AACF,QAAA,MAAM,QAAQ,GAAG,MAAM,KAAK,CAC1B,WAAW,CAAC,oBAAoB,CAAC,SAAS,CAAC,EAC3C,gBAAgB,CACjB,CAAC;AACF,QAAA,YAAY,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;KACtC;IAAC,OAAO,GAAG,EAAE;QACZ,MAAM,aAAa,CAAC,MAAM,CAAmC,wBAAA,yCAAA;YAC3D,SAAS,EAAG,GAAa,KAAb,IAAA,IAAA,GAAG,uBAAH,GAAG,CAAY,QAAQ,EAAE;AACtC,SAAA,CAAC,CAAC;KACJ;AAED,IAAA,IAAI,YAAY,CAAC,KAAK,EAAE;AACtB,QAAA,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC;QAC3C,MAAM,aAAa,CAAC,MAAM,CAAmC,wBAAA,yCAAA;AAC3D,YAAA,SAAS,EAAE,OAAO;AACnB,SAAA,CAAC,CAAC;KACJ;AAED,IAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AACvB,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,0BAAA,0CAAoC,CAAC;KAChE;IAED,OAAO,YAAY,CAAC,KAAK,CAAC;AAC5B,CAAC;AAEM,eAAe,kBAAkB,CACtC,oBAAkD,EAClD,YAA0B,EAAA;AAE1B,IAAA,MAAM,OAAO,GAAG,MAAM,UAAU,CAAC,oBAAoB,CAAC,CAAC;IACvD,MAAM,IAAI,GAAG,OAAO,CAAC,YAAY,CAAC,mBAAoB,CAAC,CAAC;AAExD,IAAA,MAAM,aAAa,GAAG;AACpB,QAAA,MAAM,EAAE,OAAO;QACf,OAAO;AACP,QAAA,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KAC3B,CAAC;AAEF,IAAA,IAAI,YAAyB,CAAC;AAC9B,IAAA,IAAI;QACF,MAAM,QAAQ,GAAG,MAAM,KAAK,CAC1B,CAAG,EAAA,WAAW,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAA,CAAA,EAAI,YAAY,CAAC,KAAK,EAAE,EACtE,aAAa,CACd,CAAC;AACF,QAAA,YAAY,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;KACtC;IAAC,OAAO,GAAG,EAAE;QACZ,MAAM,aAAa,CAAC,MAAM,CAAgC,qBAAA,sCAAA;YACxD,SAAS,EAAG,GAAa,KAAb,IAAA,IAAA,GAAG,uBAAH,GAAG,CAAY,QAAQ,EAAE;AACtC,SAAA,CAAC,CAAC;KACJ;AAED,IAAA,IAAI,YAAY,CAAC,KAAK,EAAE;AACtB,QAAA,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC;QAC3C,MAAM,aAAa,CAAC,MAAM,CAAgC,qBAAA,sCAAA;AACxD,YAAA,SAAS,EAAE,OAAO;AACnB,SAAA,CAAC,CAAC;KACJ;AAED,IAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AACvB,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,uBAAA,uCAAiC,CAAC;KAC7D;IAED,OAAO,YAAY,CAAC,KAAK,CAAC;AAC5B,CAAC;AAEM,eAAe,kBAAkB,CACtC,oBAAkD,EAClD,KAAa,EAAA;AAEb,IAAA,MAAM,OAAO,GAAG,MAAM,UAAU,CAAC,oBAAoB,CAAC,CAAC;AAEvD,IAAA,MAAM,kBAAkB,GAAG;AACzB,QAAA,MAAM,EAAE,QAAQ;QAChB,OAAO;KACR,CAAC;AAEF,IAAA,IAAI;AACF,QAAA,MAAM,QAAQ,GAAG,MAAM,KAAK,CAC1B,CAAA,EAAG,WAAW,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAI,CAAA,EAAA,KAAK,EAAE,EACzD,kBAAkB,CACnB,CAAC;AACF,QAAA,MAAM,YAAY,GAAgB,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;AACxD,QAAA,IAAI,YAAY,CAAC,KAAK,EAAE;AACtB,YAAA,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC;YAC3C,MAAM,aAAa,CAAC,MAAM,CAAqC,0BAAA,2CAAA;AAC7D,gBAAA,SAAS,EAAE,OAAO;AACnB,aAAA,CAAC,CAAC;SACJ;KACF;IAAC,OAAO,GAAG,EAAE;QACZ,MAAM,aAAa,CAAC,MAAM,CAAqC,0BAAA,2CAAA;YAC7D,SAAS,EAAG,GAAa,KAAb,IAAA,IAAA,GAAG,uBAAH,GAAG,CAAY,QAAQ,EAAE;AACtC,SAAA,CAAC,CAAC;KACJ;AACH,CAAC;AAED,SAAS,WAAW,CAAC,EAAE,SAAS,EAAa,EAAA;AAC3C,IAAA,OAAO,CAAG,EAAA,QAAQ,CAAa,UAAA,EAAA,SAAU,gBAAgB,CAAC;AAC5D,CAAC;AAED,eAAe,UAAU,CAAC,EACxB,SAAS,EACT,aAAa,EACgB,EAAA;AAC7B,IAAA,MAAM,SAAS,GAAG,MAAM,aAAa,CAAC,QAAQ,EAAE,CAAC;IAEjD,OAAO,IAAI,OAAO,CAAC;AACjB,QAAA,cAAc,EAAE,kBAAkB;AAClC,QAAA,MAAM,EAAE,kBAAkB;QAC1B,gBAAgB,EAAE,SAAS,CAAC,MAAO;QACnC,oCAAoC,EAAE,CAAO,IAAA,EAAA,SAAS,CAAE,CAAA;AACzD,KAAA,CAAC,CAAC;AACL,CAAC;AAED,SAAS,OAAO,CAAC,EACf,MAAM,EACN,IAAI,EACJ,QAAQ,EACR,QAAQ,EACY,EAAA;AACpB,IAAA,MAAM,IAAI,GAAmB;AAC3B,QAAA,GAAG,EAAE;YACH,QAAQ;YACR,IAAI;YACJ,MAAM;AACP,SAAA;KACF,CAAC;AAEF,IAAA,IAAI,QAAQ,KAAK,iBAAiB,EAAE;AAClC,QAAA,IAAI,CAAC,GAAG,CAAC,iBAAiB,GAAG,QAAQ,CAAC;KACvC;AAED,IAAA,OAAO,IAAI,CAAC;AACd;;ACzLA;;;;;;;;;;;;;;;AAeG;AAiBH;AACA,MAAM,mBAAmB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAE7C,eAAe,gBAAgB,CACpC,SAA2B,EAAA;AAE3B,IAAA,MAAM,gBAAgB,GAAG,MAAM,mBAAmB,CAChD,SAAS,CAAC,cAAe,EACzB,SAAS,CAAC,QAAS,CACpB,CAAC;AAEF,IAAA,MAAM,mBAAmB,GAAwB;QAC/C,QAAQ,EAAE,SAAS,CAAC,QAAS;AAC7B,QAAA,OAAO,EAAE,SAAS,CAAC,cAAe,CAAC,KAAK;QACxC,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;QACnC,IAAI,EAAE,aAAa,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAE,CAAC;QACrD,MAAM,EAAE,aAAa,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAE,CAAC;KAC1D,CAAC;IAEF,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;IACjE,IAAI,CAAC,YAAY,EAAE;;QAEjB,OAAO,WAAW,CAAC,SAAS,CAAC,oBAAoB,EAAE,mBAAmB,CAAC,CAAC;KACzE;SAAM,IACL,CAAC,YAAY,CAAC,YAAY,CAAC,mBAAoB,EAAE,mBAAmB,CAAC,EACrE;;AAEA,QAAA,IAAI;YACF,MAAM,kBAAkB,CACtB,SAAS,CAAC,oBAAqB,EAC/B,YAAY,CAAC,KAAK,CACnB,CAAC;SACH;QAAC,OAAO,CAAC,EAAE;;AAEV,YAAA,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACjB;QAED,OAAO,WAAW,CAAC,SAAS,CAAC,oBAAqB,EAAE,mBAAmB,CAAC,CAAC;KAC1E;SAAM,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,YAAY,CAAC,UAAU,GAAG,mBAAmB,EAAE;;QAEtE,OAAO,WAAW,CAAC,SAAS,EAAE;YAC5B,KAAK,EAAE,YAAY,CAAC,KAAK;AACzB,YAAA,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE;YACtB,mBAAmB;AACpB,SAAA,CAAC,CAAC;KACJ;SAAM;;QAEL,OAAO,YAAY,CAAC,KAAK,CAAC;KAC3B;AACH,CAAC;AAED;;;AAGG;AACI,eAAe,mBAAmB,CACvC,SAA2B,EAAA;IAE3B,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;IACjE,IAAI,YAAY,EAAE;QAChB,MAAM,kBAAkB,CACtB,SAAS,CAAC,oBAAoB,EAC9B,YAAY,CAAC,KAAK,CACnB,CAAC;AACF,QAAA,MAAM,QAAQ,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;KAChD;;IAGD,MAAM,gBAAgB,GACpB,MAAM,SAAS,CAAC,cAAe,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC;IAChE,IAAI,gBAAgB,EAAE;AACpB,QAAA,OAAO,gBAAgB,CAAC,WAAW,EAAE,CAAC;KACvC;;AAGD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED,eAAe,WAAW,CACxB,SAA2B,EAC3B,YAA0B,EAAA;AAE1B,IAAA,IAAI;QACF,MAAM,YAAY,GAAG,MAAM,kBAAkB,CAC3C,SAAS,CAAC,oBAAoB,EAC9B,YAAY,CACb,CAAC;AAEF,QAAA,MAAM,mBAAmB,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACpB,YAAY,CAAA,EAAA,EACf,KAAK,EAAE,YAAY,EACnB,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE,GACvB,CAAC;QAEF,MAAM,KAAK,CAAC,SAAS,CAAC,oBAAoB,EAAE,mBAAmB,CAAC,CAAC;AACjE,QAAA,OAAO,YAAY,CAAC;KACrB;IAAC,OAAO,CAAC,EAAE;AACV,QAAA,MAAM,CAAC,CAAC;KACT;AACH,CAAC;AAED,eAAe,WAAW,CACxB,oBAAkD,EAClD,mBAAwC,EAAA;IAExC,MAAM,KAAK,GAAG,MAAM,eAAe,CACjC,oBAAoB,EACpB,mBAAmB,CACpB,CAAC;AACF,IAAA,MAAM,YAAY,GAAiB;QACjC,KAAK;AACL,QAAA,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE;QACtB,mBAAmB;KACpB,CAAC;AACF,IAAA,MAAM,KAAK,CAAC,oBAAoB,EAAE,YAAY,CAAC,CAAC;IAChD,OAAO,YAAY,CAAC,KAAK,CAAC;AAC5B,CAAC;AAED;;AAEG;AACH,eAAe,mBAAmB,CAChC,cAAyC,EACzC,QAAgB,EAAA;IAEhB,MAAM,YAAY,GAAG,MAAM,cAAc,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC;IACxE,IAAI,YAAY,EAAE;AAChB,QAAA,OAAO,YAAY,CAAC;KACrB;AAED,IAAA,OAAO,cAAc,CAAC,WAAW,CAAC,SAAS,CAAC;AAC1C,QAAA,eAAe,EAAE,IAAI;;;AAGrB,QAAA,oBAAoB,EAAE,aAAa,CAAC,QAAQ,CAAC;AAC9C,KAAA,CAAC,CAAC;AACL,CAAC;AAED;;AAEG;AACH,SAAS,YAAY,CACnB,SAA8B,EAC9B,cAAmC,EAAA;IAEnC,MAAM,eAAe,GAAG,cAAc,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,CAAC;IACvE,MAAM,eAAe,GAAG,cAAc,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,CAAC;IACvE,MAAM,WAAW,GAAG,cAAc,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAAC;IAC3D,MAAM,aAAa,GAAG,cAAc,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,CAAC;AAEjE,IAAA,OAAO,eAAe,IAAI,eAAe,IAAI,WAAW,IAAI,aAAa,CAAC;AAC5E;;ACvLA;;;;;;;;;;;;;;;AAeG;AAKG,SAAU,kBAAkB,CAChC,eAAuC,EAAA;AAEvC,IAAA,MAAM,OAAO,GAAmB;QAC9B,IAAI,EAAE,eAAe,CAAC,IAAI;;QAE1B,WAAW,EAAE,eAAe,CAAC,YAAY;;QAEzC,SAAS,EAAE,eAAe,CAAC,YAAY;KACtB,CAAC;AAEpB,IAAA,4BAA4B,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;AACvD,IAAA,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;AAC/C,IAAA,mBAAmB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;AAE9C,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,4BAA4B,CACnC,OAAuB,EACvB,sBAA8C,EAAA;AAE9C,IAAA,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE;QACxC,OAAO;KACR;AAED,IAAA,OAAO,CAAC,YAAY,GAAG,EAAE,CAAC;AAE1B,IAAA,MAAM,KAAK,GAAG,sBAAsB,CAAC,YAAa,CAAC,KAAK,CAAC;AACzD,IAAA,IAAI,CAAC,CAAC,KAAK,EAAE;AACX,QAAA,OAAO,CAAC,YAAa,CAAC,KAAK,GAAG,KAAK,CAAC;KACrC;AAED,IAAA,MAAM,IAAI,GAAG,sBAAsB,CAAC,YAAa,CAAC,IAAI,CAAC;AACvD,IAAA,IAAI,CAAC,CAAC,IAAI,EAAE;AACV,QAAA,OAAO,CAAC,YAAa,CAAC,IAAI,GAAG,IAAI,CAAC;KACnC;AAED,IAAA,MAAM,KAAK,GAAG,sBAAsB,CAAC,YAAa,CAAC,KAAK,CAAC;AACzD,IAAA,IAAI,CAAC,CAAC,KAAK,EAAE;AACX,QAAA,OAAO,CAAC,YAAa,CAAC,KAAK,GAAG,KAAK,CAAC;KACrC;AAED,IAAA,MAAM,IAAI,GAAG,sBAAsB,CAAC,YAAa,CAAC,IAAI,CAAC;AACvD,IAAA,IAAI,CAAC,CAAC,IAAI,EAAE;AACV,QAAA,OAAO,CAAC,YAAa,CAAC,IAAI,GAAG,IAAI,CAAC;KACnC;AACH,CAAC;AAED,SAAS,oBAAoB,CAC3B,OAAuB,EACvB,sBAA8C,EAAA;AAE9C,IAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE;QAChC,OAAO;KACR;AAED,IAAA,OAAO,CAAC,IAAI,GAAG,sBAAsB,CAAC,IAAiC,CAAC;AAC1E,CAAC;AAED,SAAS,mBAAmB,CAC1B,OAAuB,EACvB,sBAA8C,EAAA;;;IAG9C,IACE,CAAC,sBAAsB,CAAC,UAAU;QAClC,EAAC,MAAA,sBAAsB,CAAC,YAAY,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAY,CAAA,EAClD;QACA,OAAO;KACR;AAED,IAAA,OAAO,CAAC,UAAU,GAAG,EAAE,CAAC;AAExB,IAAA,MAAM,IAAI,GACR,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,sBAAsB,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GACvC,MAAA,sBAAsB,CAAC,YAAY,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,YAAY,CAAC;AAEpD,IAAA,IAAI,CAAC,CAAC,IAAI,EAAE;AACV,QAAA,OAAO,CAAC,UAAW,CAAC,IAAI,GAAG,IAAI,CAAC;KACjC;;IAGD,MAAM,cAAc,GAAG,CAAA,EAAA,GAAA,sBAAsB,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC;AAC1E,IAAA,IAAI,CAAC,CAAC,cAAc,EAAE;AACpB,QAAA,OAAO,CAAC,UAAW,CAAC,cAAc,GAAG,cAAc,CAAC;KACrD;AACH;;AC3GA;;;;;;;;;;;;;;;AAeG;AAKG,SAAU,gBAAgB,CAAC,IAAa,EAAA;;AAE5C,IAAA,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,IAAI,mBAAmB,IAAI,IAAI,CAAC;AAC3E;;ACvBA;;;;;;;;;;;;;;;AAeG;AAyBuB,aAAa,CACrC,sBAAsB,EACtB,qBAAqB,EACrB;AA+Mc,SAAA,aAAa,CAAC,EAAU,EAAE,EAAU,EAAA;IAClD,MAAM,WAAW,GAAG,EAAE,CAAC;AACvB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAClC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B,QAAA,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE;YACjB,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SAChC;KACF;AAED,IAAA,OAAO,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC9B;;ACpQA;;;;;;;;;;;;;;;AAeG;AAQG,SAAU,gBAAgB,CAAC,GAAgB,EAAA;IAC/C,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;AACxB,QAAA,MAAM,oBAAoB,CAAC,0BAA0B,CAAC,CAAC;KACxD;AAED,IAAA,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;AACb,QAAA,MAAM,oBAAoB,CAAC,UAAU,CAAC,CAAC;KACxC;;AAGD,IAAA,MAAM,UAAU,GAAyC;QACvD,WAAW;QACX,QAAQ;QACR,OAAO;QACP,mBAAmB;KACpB,CAAC;AAEF,IAAA,MAAM,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC;AACxB,IAAA,KAAK,MAAM,OAAO,IAAI,UAAU,EAAE;AAChC,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AACrB,YAAA,MAAM,oBAAoB,CAAC,OAAO,CAAC,CAAC;SACrC;KACF;IAED,OAAO;QACL,OAAO,EAAE,GAAG,CAAC,IAAI;QACjB,SAAS,EAAE,OAAO,CAAC,SAAU;QAC7B,MAAM,EAAE,OAAO,CAAC,MAAO;QACvB,KAAK,EAAE,OAAO,CAAC,KAAM;QACrB,QAAQ,EAAE,OAAO,CAAC,iBAAkB;KACrC,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAAC,SAAiB,EAAA;IAC7C,OAAO,aAAa,CAAC,MAAM,CAAsC,2BAAA,4CAAA;QAC/D,SAAS;AACV,KAAA,CAAC,CAAC;AACL;;AC5DA;;;;;;;;;;;;;;;AAeG;MAYU,gBAAgB,CAAA;AAoB3B,IAAA,WAAA,CACE,GAAgB,EAChB,aAA6C,EAC7C,iBAA0D,EAAA;;QAhB5D,IAAwC,CAAA,wCAAA,GAAY,KAAK,CAAC;QAE1D,IAA0B,CAAA,0BAAA,GAGf,IAAI,CAAC;QAEhB,IAAgB,CAAA,gBAAA,GACd,IAAI,CAAC;QAEP,IAAS,CAAA,SAAA,GAAe,EAAE,CAAC;QAC3B,IAAmB,CAAA,mBAAA,GAAY,KAAK,CAAC;AAOnC,QAAA,MAAM,SAAS,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;QAExC,IAAI,CAAC,oBAAoB,GAAG;YAC1B,GAAG;YACH,SAAS;YACT,aAAa;YACb,iBAAiB;SAClB,CAAC;KACH;IAED,OAAO,GAAA;AACL,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;AACF;;ACjED;;;;;;;;;;;;;;;AAeG;AAWI,eAAe,iBAAiB,CACrC,SAA2B,EAAA;AAE3B,IAAA,IAAI;QACF,SAAS,CAAC,cAAc,GAAG,MAAM,SAAS,CAAC,aAAa,CAAC,QAAQ,CAC/D,eAAe,EACf;AACE,YAAA,KAAK,EAAE,gBAAgB;AACxB,SAAA,CACF,CAAC;;;;;;QAOF,SAAS,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,MAAK;;AAE7C,SAAC,CAAC,CAAC;AACH,QAAA,MAAM,yBAAyB,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;KAC3D;IAAC,OAAO,CAAC,EAAE;QACV,MAAM,aAAa,CAAC,MAAM,CAAwC,oCAAA,8CAAA;AAChE,YAAA,mBAAmB,EAAG,CAAW,KAAA,IAAA,IAAX,CAAC,KAAD,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAC,CAAY,OAAO;AAC3C,SAAA,CAAC,CAAC;KACJ;AACH,CAAC;AAED;;;;;;;;AAQG;AACH,eAAe,yBAAyB,CACtC,YAAuC,EAAA;IAEvC,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,KAAI;QAC3C,MAAM,aAAa,GAAG,UAAU,CAC9B,MACE,MAAM,CACJ,IAAI,KAAK,CACP,CAAA,oCAAA,EAAuC,4BAA4B,CAAK,GAAA,CAAA,CACzE,CACF,EACH,4BAA4B,CAC7B,CAAC;QACF,MAAM,UAAU,GAAG,YAAY,CAAC,UAAU,IAAI,YAAY,CAAC,OAAO,CAAC;AACnE,QAAA,IAAI,YAAY,CAAC,MAAM,EAAE;YACvB,YAAY,CAAC,aAAa,CAAC,CAAC;AAC5B,YAAA,OAAO,EAAE,CAAC;SACX;aAAM,IAAI,UAAU,EAAE;AACrB,YAAA,UAAU,CAAC,aAAa,GAAG,EAAE,IAAG;;gBAC9B,IAAI,CAAA,CAAC,EAAA,GAAA,EAAE,CAAC,MAAwB,0CAAE,KAAK,MAAK,WAAW,EAAE;AACvD,oBAAA,UAAU,CAAC,aAAa,GAAG,IAAI,CAAC;oBAChC,YAAY,CAAC,aAAa,CAAC,CAAC;AAC5B,oBAAA,OAAO,EAAE,CAAC;iBACX;AACH,aAAC,CAAC;SACH;aAAM;YACL,YAAY,CAAC,aAAa,CAAC,CAAC;AAC5B,YAAA,MAAM,CAAC,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC,CAAC;SACxD;AACH,KAAC,CAAC,CAAC;AACL;;AC5FA;;;;;;;;;;;;;;;AAeG;AAOI,eAAe,WAAW,CAC/B,SAA2B,EAC3B,cAAsD,EAAA;IAEtD,IAAI,CAAC,cAAc,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE;AAChD,QAAA,MAAM,iBAAiB,CAAC,SAAS,CAAC,CAAC;KACpC;IAED,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC,SAAS,CAAC,cAAc,EAAE;QACjD,OAAO;KACR;AAED,IAAA,IAAI,EAAE,cAAc,YAAY,yBAAyB,CAAC,EAAE;AAC1D,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,yBAAA,yCAAmC,CAAC;KAC/D;AAED,IAAA,SAAS,CAAC,cAAc,GAAG,cAAc,CAAC;AAC5C;;ACvCA;;;;;;;;;;;;;;;AAeG;AAKI,eAAe,cAAc,CAClC,SAA2B,EAC3B,QAA6B,EAAA;AAE7B,IAAA,IAAI,CAAC,CAAC,QAAQ,EAAE;AACd,QAAA,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAC;KAC/B;AAAM,SAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;AAC9B,QAAA,SAAS,CAAC,QAAQ,GAAG,iBAAiB,CAAC;KACxC;AACH;;AC7BA;;;;;;;;;;;;;;;AAeG;AAUI,eAAeC,UAAQ,CAC5B,SAA2B,EAC3B,OAAyB,EAAA;IAEzB,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,0BAAA,qCAA+B,CAAC;KAC3D;AAED,IAAA,IAAI,YAAY,CAAC,UAAU,KAAK,SAAS,EAAE;AACzC,QAAA,MAAM,YAAY,CAAC,iBAAiB,EAAE,CAAC;KACxC;AAED,IAAA,IAAI,YAAY,CAAC,UAAU,KAAK,SAAS,EAAE;AACzC,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,oBAAA,oCAA8B,CAAC;KAC1D;AAED,IAAA,MAAM,cAAc,CAAC,SAAS,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,QAAQ,CAAC,CAAC;AACnD,IAAA,MAAM,WAAW,CAAC,SAAS,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,yBAAyB,CAAC,CAAC;AAEjE,IAAA,OAAO,gBAAgB,CAAC,SAAS,CAAC,CAAC;AACrC;;AC7CA;;;;;;;;;;;;;;;AAeG;AAcI,eAAe,UAAU,CAC9B,SAA2B,EAC3B,WAAwB,EACxB,IAAwB,EAAA;AAExB,IAAA,MAAM,SAAS,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC;IAC5C,MAAM,SAAS,GACb,MAAM,SAAS,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC;AAC/D,IAAA,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE;;AAE5B,QAAA,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC;AACrC,QAAA,YAAY,EAAE,IAAI,CAAC,qBAAqB,CAAC;AACzC,QAAA,YAAY,EAAE,IAAI,CAAC,qBAAqB,CAAC;QACzC,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;;AAEnD,KAAA,CAAC,CAAC;AACL,CAAC;AAED,SAAS,YAAY,CAAC,WAAwB,EAAA;IAC5C,QAAQ,WAAW;QACjB,KAAK,WAAW,CAAC,oBAAoB;AACnC,YAAA,OAAO,mBAAmB,CAAC;QAC7B,KAAK,WAAW,CAAC,aAAa;AAC5B,YAAA,OAAO,yBAAyB,CAAC;AACnC,QAAA;YACE,MAAM,IAAI,KAAK,EAAE,CAAC;KACrB;AACH;;ACxDA;;;;;;;;;;;;;;;AAeG;AAaI,eAAe,oBAAoB,CACxC,SAA2B,EAC3B,KAAmB,EAAA;AAEnB,IAAA,MAAM,eAAe,GAAG,KAAK,CAAC,IAA8B,CAAC;AAE7D,IAAA,IAAI,CAAC,eAAe,CAAC,mBAAmB,EAAE;QACxC,OAAO;KACR;IAED,IACE,SAAS,CAAC,gBAAgB;AAC1B,QAAA,eAAe,CAAC,WAAW,KAAK,WAAW,CAAC,aAAa,EACzD;AACA,QAAA,IAAI,OAAO,SAAS,CAAC,gBAAgB,KAAK,UAAU,EAAE;YACpD,SAAS,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC,CAAC;SACjE;aAAM;YACL,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC,CAAC;SACtE;KACF;;AAGD,IAAA,MAAM,WAAW,GAAG,eAAe,CAAC,IAAI,CAAC;IACzC,IACE,gBAAgB,CAAC,WAAW,CAAC;AAC7B,QAAA,WAAW,CAAC,kCAAkC,CAAC,KAAK,GAAG,EACvD;QACA,MAAM,UAAU,CAAC,SAAS,EAAE,eAAe,CAAC,WAAY,EAAE,WAAW,CAAC,CAAC;KACxE;AACH;;;;;ACzDA;;;;;;;;;;;;;;;AAeG;AAwBH,MAAM,sBAAsB,GAAiC,CAC3D,SAA6B,KAC3B;AACF,IAAA,MAAM,SAAS,GAAG,IAAI,gBAAgB,CACpC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,EAC3C,SAAS,CAAC,WAAW,CAAC,wBAAwB,CAAC,CAAC,YAAY,EAAE,EAC9D,SAAS,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAC5C,CAAC;AAEF,IAAA,SAAS,CAAC,aAAa,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,IACnD,oBAAoB,CAAC,SAA6B,EAAE,CAAC,CAAC,CACvD,CAAC;AAEF,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF,MAAM,8BAA8B,GAA0C,CAC5E,SAA6B,KAC3B;IACF,MAAM,SAAS,GAAG,SAAS;SACxB,WAAW,CAAC,WAAW,CAAC;AACxB,SAAA,YAAY,EAAsB,CAAC;AAEtC,IAAA,MAAM,iBAAiB,GAAsB;QAC3C,QAAQ,EAAE,CAAC,OAAyB,KAAKA,UAAQ,CAAC,SAAS,EAAE,OAAO,CAAC;KACtE,CAAC;AAEF,IAAA,OAAO,iBAAiB,CAAC;AAC3B,CAAC,CAAC;SAyBc,yBAAyB,GAAA;IACvC,kBAAkB,CAChB,IAAI,SAAS,CAAC,WAAW,EAAE,sBAAsB,EAAuB,QAAA,4BAAA,CACzE,CAAC;IAEF,kBAAkB,CAChB,IAAI,SAAS,CACX,oBAAoB,EACpB,8BAA8B,EAE/B,SAAA,6BAAA,CACF,CAAC;AAEF,IAAA,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;;AAE/B,IAAA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,SAAkB,CAAC,CAAC;AACrD;;AC5GA;;;;;;;;;;;;;;;AAeG;AAQH;;;;;AAKG;AACI,eAAe,iBAAiB,GAAA;AACrC,IAAA,IAAI;;;QAGF,MAAM,yBAAyB,EAAE,CAAC;KACnC;IAAC,OAAO,CAAC,EAAE;AACV,QAAA,OAAO,KAAK,CAAC;KACd;;;;AAID,IAAA,QACE,OAAO,MAAM,KAAK,WAAW;AAC7B,QAAA,oBAAoB,EAAE;AACtB,QAAA,iBAAiB,EAAE;AACnB,QAAA,eAAe,IAAI,SAAS;AAC5B,QAAA,aAAa,IAAI,MAAM;AACvB,QAAA,cAAc,IAAI,MAAM;AACxB,QAAA,OAAO,IAAI,MAAM;AACjB,QAAA,yBAAyB,CAAC,SAAS,CAAC,cAAc,CAAC,kBAAkB,CAAC;QACtE,gBAAgB,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,EACnD;AACJ;;ACnDA;;;;;;;;;;;;;;;AAeG;AAQI,eAAeC,aAAW,CAC/B,SAA2B,EAAA;IAE3B,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,0BAAA,qCAA+B,CAAC;KAC3D;AAED,IAAA,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE;AAC7B,QAAA,MAAM,iBAAiB,CAAC,SAAS,CAAC,CAAC;KACpC;AAED,IAAA,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;AACxC;;ACnCA;;;;;;;;;;;;;;;AAeG;AAYa,SAAAC,WAAS,CACvB,SAA2B,EAC3B,cAAiE,EAAA;IAEjE,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,0BAAA,qCAA+B,CAAC;KAC3D;AAED,IAAA,SAAS,CAAC,gBAAgB,GAAG,cAAc,CAAC;AAE5C,IAAA,OAAO,MAAK;AACV,QAAA,SAAS,CAAC,gBAAgB,GAAG,IAAI,CAAC;AACpC,KAAC,CAAC;AACJ;;ACxCA;;;;;;;;;;;;;;;AAeG;AAwBH;;;;;;AAMG;AACa,SAAA,oBAAoB,CAAC,GAAA,GAAmB,MAAM,EAAE,EAAA;;;;;AAK9D,IAAA,iBAAiB,EAAE,CAAC,IAAI,CACtB,WAAW,IAAG;;QAEZ,IAAI,CAAC,WAAW,EAAE;AAChB,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,qBAAA,qCAA+B,CAAC;SAC3D;KACF,EACD,CAAC,IAAG;;AAEF,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,wBAAA,wCAAkC,CAAC;AAC/D,KAAC,CACF,CAAC;AACF,IAAA,OAAO,YAAY,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC,CAAC,YAAY,EAAE,CAAC;AAC3E,CAAC;AA6BD;;;;;;;;;;;;;;AAcG;AACI,eAAe,QAAQ,CAC5B,SAAoB,EACpB,OAAyB,EAAA;AAEzB,IAAA,SAAS,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;AAC1C,IAAA,OAAOC,UAAS,CAAC,SAA6B,EAAE,OAAO,CAAC,CAAC;AAC3D,CAAC;AAED;;;;;;;;;AASG;AACG,SAAU,WAAW,CAAC,SAAoB,EAAA;AAC9C,IAAA,SAAS,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;AAC1C,IAAA,OAAOC,aAAY,CAAC,SAA6B,CAAC,CAAC;AACrD,CAAC;AAED;;;;;;;;;;;;AAYG;AACa,SAAA,SAAS,CACvB,SAAoB,EACpB,cAAiE,EAAA;AAEjE,IAAA,SAAS,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;AAC1C,IAAA,OAAOC,WAAU,CAAC,SAA6B,EAAE,cAAc,CAAC,CAAC;AACnE;;ACtJA;;;;;AAKG;AAuCH,yBAAyB,EAAE;;;;"}