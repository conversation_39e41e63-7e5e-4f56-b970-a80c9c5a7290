import type Channels from './channels';
type ALPHA = {
    a: number;
};
type RGB = {
    r: number;
    g: number;
    b: number;
};
type RGBA = RGB & ALPHA;
type HSL = {
    h: number;
    s: number;
    l: number;
};
type HSLA = HSL & ALPHA;
type CHANNEL = 'r' | 'g' | 'b' | 'h' | 's' | 'l' | 'a';
type CHANNELS = Record<CHANNEL, number>;
export type { Channels };
export type { ALPHA, RGB, RGBA, HSL, HSLA, CHANNEL, CHANNELS };
