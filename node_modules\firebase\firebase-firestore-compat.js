((e,t)=>{"object"==typeof exports&&"undefined"!=typeof module?t(require("@firebase/app-compat"),require("@firebase/app")):"function"==typeof define&&define.amd?define(["@firebase/app-compat","@firebase/app"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).firebase,e.firebase.INTERNAL.modularAPIs)})(this,function(Fd,Vd){try{!(function(){function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}let i=n(Fd),s=()=>{},a=function(t){var r=[];let n=0;for(let i=0;i<t.length;i++){let e=t.charCodeAt(i);e<128?r[n++]=e:(e<2048?r[n++]=e>>6|192:(55296==(64512&e)&&i+1<t.length&&56320==(64512&t.charCodeAt(i+1))?(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++i)),r[n++]=e>>18|240,r[n++]=e>>12&63|128):r[n++]=e>>12|224,r[n++]=e>>6&63|128),r[n++]=63&e|128)}return r},B={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(r,e){if(!Array.isArray(r))throw Error("encodeByteArray takes an array as a parameter");this.init_();var n=e?this.byteToCharMapWebSafe_:this.byteToCharMap_,i=[];for(let h=0;h<r.length;h+=3){var s=r[h],a=h+1<r.length,o=a?r[h+1]:0,l=h+2<r.length,u=l?r[h+2]:0;let e=(15&o)<<2|u>>6,t=63&u;l||(t=64,a)||(e=64),i.push(n[s>>2],n[(3&s)<<4|o>>4],n[e],n[t])}return i.join("")},encodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(e):this.encodeByteArray(a(e),t)},decodeString(r,n){if(this.HAS_NATIVE_SUPPORT&&!n)return atob(r);{var i=this.decodeStringToByteArray(r,n);var s=[];let e=0,t=0;for(;e<i.length;){var a,o,l,u=i[e++];u<128?s[t++]=String.fromCharCode(u):191<u&&u<224?(a=i[e++],s[t++]=String.fromCharCode((31&u)<<6|63&a)):239<u&&u<365?(a=((7&u)<<18|(63&i[e++])<<12|(63&i[e++])<<6|63&i[e++])-65536,s[t++]=String.fromCharCode(55296+(a>>10)),s[t++]=String.fromCharCode(56320+(1023&a))):(o=i[e++],l=i[e++],s[t++]=String.fromCharCode((15&u)<<12|(63&o)<<6|63&l))}return s.join("");return}},decodeStringToByteArray(e,t){this.init_();var r=t?this.charToByteMapWebSafe_:this.charToByteMap_,n=[];for(let l=0;l<e.length;){var i=r[e.charAt(l++)],s=l<e.length?r[e.charAt(l)]:0,a=++l<e.length?r[e.charAt(l)]:64,o=++l<e.length?r[e.charAt(l)]:64;if(++l,null==i||null==s||null==a||null==o)throw new q;n.push(i<<2|s>>4),64!==a&&(n.push(s<<4&240|a>>2),64!==o)&&n.push(a<<6&192|o)}return n},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),(this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e)>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};class q extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}let z=function(e){var t=a(e);return B.encodeByteArray(t,!0)},j=function(e){return z(e).replace(/\./g,"")},K=function(e){try{return B.decodeString(e,!0)}catch(e){console.error("base64Decode failed: ",e)}return null};function G(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("Unable to locate global object.")}let Q=()=>G().__FIREBASE_DEFAULTS__,$=()=>{var e;return"undefined"!=typeof process&&void 0!==process.env&&(e=process.env.__FIREBASE_DEFAULTS__)?JSON.parse(e):void 0},H=()=>{if("undefined"!=typeof document){let e;try{e=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(e){return}var t=e&&K(e[1]);return t&&JSON.parse(t)}},W=()=>{try{return s()||Q()||$()||H()}catch(e){console.info("Unable to get __FIREBASE_DEFAULTS__ due to: "+e)}};function Y(){return"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent?navigator.userAgent:""}function J(){return!(()=>{var e=null==(e=W())?void 0:e.forceEnvironment;if("node"===e)return 1;if("browser"!==e)try{return"[object process]"===Object.prototype.toString.call(global.process)}catch(e){}})()&&navigator.userAgent&&navigator.userAgent.includes("Safari")&&!navigator.userAgent.includes("Chrome")}class X extends Error{constructor(e,t,r){super(t),this.code=e,this.customData=r,this.name="FirebaseError",Object.setPrototypeOf(this,X.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,Z.prototype.create)}}class Z{constructor(e,t,r){this.service=e,this.serviceName=t,this.errors=r}create(e,...t){var n,r=t[0]||{},i=this.service+"/"+e,s=this.errors[e],s=s?(n=r,s.replace(ee,(e,t)=>{var r=n[t];return null!=r?String(r):`<${t}?>`})):"Error",s=this.serviceName+`: ${s} (${i}).`;return new X(i,s,r)}}let ee=/\{\$([^}]+)}/g;function te(e,t){if(e!==t){var r,n,i=Object.keys(e),s=Object.keys(t);for(r of i){if(!s.includes(r))return!1;var a=e[r],o=t[r];if(re(a)&&re(o)){if(!te(a,o))return!1}else if(a!==o)return!1}for(n of s)if(!i.includes(n))return!1}return!0}function re(e){return null!==e&&"object"==typeof e}function _(e){return e&&e._delegate?e._delegate:e}class ne{constructor(e,t,r){this.name=e,this.instanceFactory=t,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}var c;(t=c=c||{})[t.DEBUG=0]="DEBUG",t[t.VERBOSE=1]="VERBOSE",t[t.INFO=2]="INFO",t[t.WARN=3]="WARN",t[t.ERROR=4]="ERROR",t[t.SILENT=5]="SILENT";let ie={debug:c.DEBUG,verbose:c.VERBOSE,info:c.INFO,warn:c.WARN,error:c.ERROR,silent:c.SILENT},se=c.INFO,ae={[c.DEBUG]:"log",[c.VERBOSE]:"log",[c.INFO]:"info",[c.WARN]:"warn",[c.ERROR]:"error"},oe=(e,t,...r)=>{if(!(t<e.logLevel)){var n=(new Date).toISOString(),i=ae[t];if(!i)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[i](`[${n}]  ${e.name}:`,...r)}};var le,ue,pr,yr,vr,wr,_r,br,Ir,Tr,m,he,e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},Er=(!(function(){var e,t,s;function r(){this.blockSize=-1,this.blockSize=64,this.g=Array(4),this.B=Array(this.blockSize),this.o=this.h=0,this.s()}function n(){}function a(e,t,r){r=r||0;var n=Array(16);if("string"==typeof t)for(var i=0;i<16;++i)n[i]=t.charCodeAt(r++)|t.charCodeAt(r++)<<8|t.charCodeAt(r++)<<16|t.charCodeAt(r++)<<24;else for(i=0;i<16;++i)n[i]=t[r++]|t[r++]<<8|t[r++]<<16|t[r++]<<24;t=e.g[0],r=e.g[1];var i=e.g[2],s=e.g[3],a=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=r+((a=t+(s^r&(i^s))+n[0]+3614090360&4294967295)<<7&4294967295|a>>>25))+((a=s+(i^t&(r^i))+n[1]+3905402710&4294967295)<<12&4294967295|a>>>20))+((a=i+(r^s&(t^r))+n[2]+606105819&4294967295)<<17&4294967295|a>>>15))+((a=r+(t^i&(s^t))+n[3]+3250441966&4294967295)<<22&4294967295|a>>>10))+((a=t+(s^r&(i^s))+n[4]+4118548399&4294967295)<<7&4294967295|a>>>25))+((a=s+(i^t&(r^i))+n[5]+1200080426&4294967295)<<12&4294967295|a>>>20))+((a=i+(r^s&(t^r))+n[6]+2821735955&4294967295)<<17&4294967295|a>>>15))+((a=r+(t^i&(s^t))+n[7]+4249261313&4294967295)<<22&4294967295|a>>>10))+((a=t+(s^r&(i^s))+n[8]+1770035416&4294967295)<<7&4294967295|a>>>25))+((a=s+(i^t&(r^i))+n[9]+2336552879&4294967295)<<12&4294967295|a>>>20))+((a=i+(r^s&(t^r))+n[10]+4294925233&4294967295)<<17&4294967295|a>>>15))+((a=r+(t^i&(s^t))+n[11]+2304563134&4294967295)<<22&4294967295|a>>>10))+((a=t+(s^r&(i^s))+n[12]+1804603682&4294967295)<<7&4294967295|a>>>25))+((a=s+(i^t&(r^i))+n[13]+4254626195&4294967295)<<12&4294967295|a>>>20))+((a=i+(r^s&(t^r))+n[14]+2792965006&4294967295)<<17&4294967295|a>>>15))+((a=r+(t^i&(s^t))+n[15]+1236535329&4294967295)<<22&4294967295|a>>>10))+((a=t+(i^s&(r^i))+n[1]+4129170786&4294967295)<<5&4294967295|a>>>27))+((a=s+(r^i&(t^r))+n[6]+3225465664&4294967295)<<9&4294967295|a>>>23))+((a=i+(t^r&(s^t))+n[11]+643717713&4294967295)<<14&4294967295|a>>>18))+((a=r+(s^t&(i^s))+n[0]+3921069994&4294967295)<<20&4294967295|a>>>12))+((a=t+(i^s&(r^i))+n[5]+3593408605&4294967295)<<5&4294967295|a>>>27))+((a=s+(r^i&(t^r))+n[10]+38016083&4294967295)<<9&4294967295|a>>>23))+((a=i+(t^r&(s^t))+n[15]+3634488961&4294967295)<<14&4294967295|a>>>18))+((a=r+(s^t&(i^s))+n[4]+3889429448&4294967295)<<20&4294967295|a>>>12))+((a=t+(i^s&(r^i))+n[9]+568446438&4294967295)<<5&4294967295|a>>>27))+((a=s+(r^i&(t^r))+n[14]+3275163606&4294967295)<<9&4294967295|a>>>23))+((a=i+(t^r&(s^t))+n[3]+4107603335&4294967295)<<14&4294967295|a>>>18))+((a=r+(s^t&(i^s))+n[8]+1163531501&4294967295)<<20&4294967295|a>>>12))+((a=t+(i^s&(r^i))+n[13]+2850285829&4294967295)<<5&4294967295|a>>>27))+((a=s+(r^i&(t^r))+n[2]+4243563512&4294967295)<<9&4294967295|a>>>23))+((a=i+(t^r&(s^t))+n[7]+1735328473&4294967295)<<14&4294967295|a>>>18))+((a=r+(s^t&(i^s))+n[12]+2368359562&4294967295)<<20&4294967295|a>>>12))+((a=t+(r^i^s)+n[5]+4294588738&4294967295)<<4&4294967295|a>>>28))+((a=s+(t^r^i)+n[8]+2272392833&4294967295)<<11&4294967295|a>>>21))+((a=i+(s^t^r)+n[11]+1839030562&4294967295)<<16&4294967295|a>>>16))+((a=r+(i^s^t)+n[14]+4259657740&4294967295)<<23&4294967295|a>>>9))+((a=t+(r^i^s)+n[1]+2763975236&4294967295)<<4&4294967295|a>>>28))+((a=s+(t^r^i)+n[4]+1272893353&4294967295)<<11&4294967295|a>>>21))+((a=i+(s^t^r)+n[7]+4139469664&4294967295)<<16&4294967295|a>>>16))+((a=r+(i^s^t)+n[10]+3200236656&4294967295)<<23&4294967295|a>>>9))+((a=t+(r^i^s)+n[13]+681279174&4294967295)<<4&4294967295|a>>>28))+((a=s+(t^r^i)+n[0]+3936430074&4294967295)<<11&4294967295|a>>>21))+((a=i+(s^t^r)+n[3]+3572445317&4294967295)<<16&4294967295|a>>>16))+((a=r+(i^s^t)+n[6]+76029189&4294967295)<<23&4294967295|a>>>9))+((a=t+(r^i^s)+n[9]+3654602809&4294967295)<<4&4294967295|a>>>28))+((a=s+(t^r^i)+n[12]+3873151461&4294967295)<<11&4294967295|a>>>21))+((a=i+(s^t^r)+n[15]+530742520&4294967295)<<16&4294967295|a>>>16))+((a=r+(i^s^t)+n[2]+3299628645&4294967295)<<23&4294967295|a>>>9))+((a=t+(i^(r|~s))+n[0]+4096336452&4294967295)<<6&4294967295|a>>>26))+((a=s+(r^(t|~i))+n[7]+1126891415&4294967295)<<10&4294967295|a>>>22))+((a=i+(t^(s|~r))+n[14]+2878612391&4294967295)<<15&4294967295|a>>>17))+((a=r+(s^(i|~t))+n[5]+4237533241&4294967295)<<21&4294967295|a>>>11))+((a=t+(i^(r|~s))+n[12]+1700485571&4294967295)<<6&4294967295|a>>>26))+((a=s+(r^(t|~i))+n[3]+2399980690&4294967295)<<10&4294967295|a>>>22))+((a=i+(t^(s|~r))+n[10]+4293915773&4294967295)<<15&4294967295|a>>>17))+((a=r+(s^(i|~t))+n[1]+2240044497&4294967295)<<21&4294967295|a>>>11))+((a=t+(i^(r|~s))+n[8]+1873313359&4294967295)<<6&4294967295|a>>>26))+((a=s+(r^(t|~i))+n[15]+4264355552&4294967295)<<10&4294967295|a>>>22))+((a=i+(t^(s|~r))+n[6]+2734768916&4294967295)<<15&4294967295|a>>>17))+((a=r+(s^(i|~t))+n[13]+1309151649&4294967295)<<21&4294967295|a>>>11))+((s=(t=r+((a=t+(i^(r|~s))+n[4]+4149444226&4294967295)<<6&4294967295|a>>>26))+((a=s+(r^(t|~i))+n[11]+3174756917&4294967295)<<10&4294967295|a>>>22))^((i=s+((a=i+(t^(s|~r))+n[2]+718787259&4294967295)<<15&4294967295|a>>>17))|~t))+n[9]+3951481745&4294967295;e.g[0]=e.g[0]+t&4294967295,e.g[1]=e.g[1]+(i+(a<<21&4294967295|a>>>11))&4294967295,e.g[2]=e.g[2]+i&4294967295,e.g[3]=e.g[3]+s&4294967295}function u(e,t){this.h=t;for(var r=[],n=!0,i=e.length-1;0<=i;i--){var s=0|e[i];n&&s==t||(r[i]=s,n=!1)}this.g=r}t=r,s=function(){this.blockSize=-1},n.prototype=s.prototype,t.D=s.prototype,t.prototype=new n,(t.prototype.constructor=t).C=function(e,t,r){for(var n=Array(arguments.length-2),i=2;i<arguments.length;i++)n[i-2]=arguments[i];return s.prototype[t].apply(e,n)},r.prototype.s=function(){this.g[0]=1732584193,this.g[1]=4023233417,this.g[2]=2562383102,this.g[3]=271733878,this.o=this.h=0},r.prototype.u=function(e,t){for(var r=(t=void 0===t?e.length:t)-this.blockSize,n=this.B,i=this.h,s=0;s<t;){if(0==i)for(;s<=r;)a(this,e,s),s+=this.blockSize;if("string"==typeof e){for(;s<t;)if(n[i++]=e.charCodeAt(s++),i==this.blockSize){a(this,n),i=0;break}}else for(;s<t;)if(n[i++]=e[s++],i==this.blockSize){a(this,n),i=0;break}}this.h=i,this.o+=t},r.prototype.v=function(){var e=Array((this.h<56?this.blockSize:2*this.blockSize)-this.h);e[0]=128;for(var t=1;t<e.length-8;++t)e[t]=0;for(var r=8*this.o,t=e.length-8;t<e.length;++t)e[t]=255&r,r/=256;for(this.u(e),e=Array(16),t=r=0;t<4;++t)for(var n=0;n<32;n+=8)e[r++]=this.g[t]>>>n&255;return e};var i={};function o(e){return-128<=e&&e<128?(t=e,r=function(e){return new u([0|e],e<0?-1:0)},n=i,Object.prototype.hasOwnProperty.call(n,t)?n[t]:n[t]=r(t)):new u([0|e],e<0?-1:0);var t,r,n}function h(e){if(isNaN(e)||!isFinite(e))return c;if(e<0)return m(h(-e));for(var t=[],r=1,n=0;r<=e;n++)t[n]=e/r|0,r*=4294967296;return new u(t,0)}var c=o(0),l=o(1),d=o(16777216);function f(e){if(0==e.h){for(var t=0;t<e.g.length;t++)if(0!=e.g[t])return;return 1}}function g(e){return-1==e.h}function m(e){for(var t=e.g.length,r=[],n=0;n<t;n++)r[n]=~e.g[n];return new u(r,~e.h).add(l)}function p(e,t){return e.add(m(t))}function y(e,t){for(;(65535&e[t])!=e[t];)e[t+1]+=e[t]>>>16,e[t]&=65535,t++}function v(e,t){this.g=e,this.h=t}function w(e,t){if(f(t))throw Error("division by zero");if(f(e))return new v(c,c);if(g(e))return t=w(m(e),t),new v(m(t.g),m(t.h));if(g(t))return t=w(e,m(t)),new v(m(t.g),t.h);if(30<e.g.length){if(g(e)||g(t))throw Error("slowDivide_ only works with positive integers.");for(var r=l,n=t;n.l(e)<=0;)r=_(r),n=_(n);for(var i=b(r,1),s=b(n,1),n=b(n,2),r=b(r,2);!f(n);){var a=s.add(n);a.l(e)<=0&&(i=i.add(r),s=a),n=b(n,1),r=b(r,1)}return t=p(e,i.j(t)),new v(i,t)}for(i=c;0<=e.l(t);){for(r=Math.max(1,Math.floor(e.m()/t.m())),n=(n=Math.ceil(Math.log(r)/Math.LN2))<=48?1:Math.pow(2,n-48),a=(s=h(r)).j(t);g(a)||0<a.l(e);)a=(s=h(r-=n)).j(t);f(s)&&(s=l),i=i.add(s),e=p(e,a)}return new v(i,e)}function _(e){for(var t=e.g.length+1,r=[],n=0;n<t;n++)r[n]=e.i(n)<<1|e.i(n-1)>>>31;return new u(r,e.h)}function b(e,t){var r=t>>5;t%=32;for(var n=e.g.length-r,i=[],s=0;s<n;s++)i[s]=0<t?e.i(s+r)>>>t|e.i(s+r+1)<<32-t:e.i(s+r);return new u(i,e.h)}(e=u.prototype).m=function(){if(g(this))return-m(this).m();for(var e=0,t=1,r=0;r<this.g.length;r++){var n=this.i(r);e+=(0<=n?n:4294967296+n)*t,t*=4294967296}return e},e.toString=function(e){if((e=e||10)<2||36<e)throw Error("radix out of range: "+e);if(f(this))return"0";if(g(this))return"-"+m(this).toString(e);for(var t=h(Math.pow(e,6)),r=this,n="";;){var i=w(r,t).g,s=((0<(r=p(r,i.j(t))).g.length?r.g[0]:r.h)>>>0).toString(e);if(f(r=i))return s+n;for(;s.length<6;)s="0"+s;n=s+n}},e.i=function(e){return e<0?0:e<this.g.length?this.g[e]:this.h},e.l=function(e){return g(e=p(this,e))?-1:f(e)?0:1},e.abs=function(){return g(this)?m(this):this},e.add=function(e){for(var t=Math.max(this.g.length,e.g.length),r=[],n=0,i=0;i<=t;i++){var s=n+(65535&this.i(i))+(65535&e.i(i)),a=(s>>>16)+(this.i(i)>>>16)+(e.i(i)>>>16),n=a>>>16;r[i]=(a&=65535)<<16|(s&=65535)}return new u(r,-2147483648&r[r.length-1]?-1:0)},e.j=function(e){if(f(this)||f(e))return c;if(g(this))return g(e)?m(this).j(m(e)):m(m(this).j(e));if(g(e))return m(this.j(m(e)));if(this.l(d)<0&&e.l(d)<0)return h(this.m()*e.m());for(var t=this.g.length+e.g.length,r=[],n=0;n<2*t;n++)r[n]=0;for(n=0;n<this.g.length;n++)for(var i=0;i<e.g.length;i++){var s=this.i(n)>>>16,a=65535&this.i(n),o=e.i(i)>>>16,l=65535&e.i(i);r[2*n+2*i]+=a*l,y(r,2*n+2*i),r[2*n+2*i+1]+=s*l,y(r,2*n+2*i+1),r[2*n+2*i+1]+=a*o,y(r,2*n+2*i+1),r[2*n+2*i+2]+=s*o,y(r,2*n+2*i+2)}for(n=0;n<t;n++)r[n]=r[2*n+1]<<16|r[2*n];for(n=t;n<2*t;n++)r[n]=0;return new u(r,0)},e.A=function(e){return w(this,e).h},e.and=function(e){for(var t=Math.max(this.g.length,e.g.length),r=[],n=0;n<t;n++)r[n]=this.i(n)&e.i(n);return new u(r,this.h&e.h)},e.or=function(e){for(var t=Math.max(this.g.length,e.g.length),r=[],n=0;n<t;n++)r[n]=this.i(n)|e.i(n);return new u(r,this.h|e.h)},e.xor=function(e){for(var t=Math.max(this.g.length,e.g.length),r=[],n=0;n<t;n++)r[n]=this.i(n)^e.i(n);return new u(r,this.h^e.h)},r.prototype.digest=r.prototype.v,r.prototype.reset=r.prototype.s,r.prototype.update=r.prototype.u,ue=r,u.prototype.multiply=u.prototype.j,u.prototype.modulo=u.prototype.A,u.prototype.compare=u.prototype.l,u.prototype.toNumber=u.prototype.m,u.prototype.getBits=u.prototype.i,u.fromNumber=h,u.fromString=function e(t,r){if(0==t.length)throw Error("number format error: empty string");if((r=r||10)<2||36<r)throw Error("radix out of range: "+r);if("-"==t.charAt(0))return m(e(t.substring(1),r));if(0<=t.indexOf("-"))throw Error('number format error: interior "-" character');for(var n=h(Math.pow(r,8)),i=c,s=0;s<t.length;s+=8)var a=Math.min(8,t.length-s),o=parseInt(t.substring(s,s+a),r),i=(a<8?(a=h(Math.pow(r,a)),i.j(a)):i=i.j(n)).add(h(o));return i},le=u}).apply(void 0!==e?e:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{}),"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{});!(function(){var e,N="function"==typeof Object.defineProperties?Object.defineProperty:function(e,t,r){return e!=Array.prototype&&e!=Object.prototype&&(e[t]=r.value),e};var k=(e=>{e=["object"==typeof globalThis&&globalThis,e,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof Er&&Er];for(var t=0;t<e.length;++t){var r=e[t];if(r&&r.Math==Math)return r}throw Error("Cannot find global object")})(this);var t="Array.prototype.values",r=function(e){return e||function(){return r=function(e,t){return t},(t=this)instanceof String&&(t+=""),n=0,i=!1,(e={next:function(){var e;return!i&&n<t.length?(e=n++,{value:r(e,t[e]),done:!1}):{done:i=!0,value:void 0}}})[Symbol.iterator]=function(){return e},e;var t,r,n,i,e}};if(r)e:{var n=k;t=t.split(".");for(var i=0;i<t.length-1;i++){var P=t[i];if(!(P in n))break e;n=n[P]}(r=r(i=n[t=t[t.length-1]]))!=i&&null!=r&&N(n,t,{configurable:!0,writable:!0,value:r})}var U=U||{},R=this||self;function B(e){var t=typeof e;return"array"==(t="object"!=t?t:e?Array.isArray(e)?"array":t:"null")||"object"==t&&"number"==typeof e.length}function u(e){var t=typeof e;return"object"==t&&null!=e||"function"==t}function q(e,t,r){return e.call.apply(e.bind,arguments)}function z(t,r,e){var n;if(t)return 2<arguments.length?(n=Array.prototype.slice.call(arguments,2),function(){var e=Array.prototype.slice.call(arguments);return Array.prototype.unshift.apply(e,n),t.apply(r,e)}):function(){return t.apply(r,arguments)};throw Error()}function p(e,t,r){return(p=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?q:z).apply(null,arguments)}function j(t){var r=Array.prototype.slice.call(arguments,1);return function(){var e=r.slice();return e.push.apply(e,arguments),t.apply(this,e)}}function s(e,s){function t(){}t.prototype=s.prototype,e.aa=s.prototype,e.prototype=new t,(e.prototype.constructor=e).Qb=function(e,t,r){for(var n=Array(arguments.length-2),i=2;i<arguments.length;i++)n[i-2]=arguments[i];return s.prototype[t].apply(e,n)}}function K(t){var r=t.length;if(0<r){var n=Array(r);for(let e=0;e<r;e++)n[e]=t[e];return n}return[]}function G(t){for(let e=1;e<arguments.length;e++){var r=arguments[e];if(B(r)){var n=t.length||0,i=r.length||0;t.length=n+i;for(let e=0;e<i;e++)t[n+e]=r[e]}else t.push(r)}}function M(e){return/^[\s\xa0]*$/.test(e)}function a(){var e=R.navigator;return(e=e&&e.userAgent)||""}function Q(e){return Q[" "](e),e}Q[" "]=function(){};var $=!(-1==a().indexOf("Gecko")||-1!=a().toLowerCase().indexOf("webkit")&&-1==a().indexOf("Edge")||-1!=a().indexOf("Trident")||-1!=a().indexOf("MSIE")||-1!=a().indexOf("Edge"));function H(e,t,r){for(var n in e)t.call(r,e[n],n,e)}function W(e){var t,r={};for(t in e)r[t]=e[t];return r}let Y="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function J(t){let r,n;for(let i=1;i<arguments.length;i++){for(r in n=arguments[i])t[r]=n[r];for(let e=0;e<Y.length;e++)r=Y[e],Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}}var X=new class{constructor(e,t){this.i=e,this.j=t,this.h=0,this.g=null}get(){let e;return 0<this.h?(this.h--,e=this.g,this.g=e.next,e.next=null):e=this.i(),e}}(()=>new Z,e=>e.reset());class Z{constructor(){this.next=this.g=this.h=null}set(e,t){this.h=e,this.g=t,this.next=null}reset(){this.next=this.g=this.h=null}}let o,l=!1,ee=new class{constructor(){this.h=this.g=null}add(e,t){var r=X.get();r.set(e,t),this.h?this.h.next=r:this.g=r,this.h=r}},te=()=>{let e=R.Promise.resolve(void 0);o=()=>{e.then(re)}};var re=()=>{for(var e;e=(()=>{let e=ee,t=null;return e.g&&(t=e.g,e.g=e.g.next,e.g||(e.h=null),t.next=null),t})();){try{e.h.call(e.g)}catch(e){(e=>{R.setTimeout(()=>{throw e},0)})(e)}var t=X;t.j(e),t.h<100&&(t.h++,e.next=t.g,t.g=e)}l=!1};function h(){this.s=this.s,this.C=this.C}function c(e,t){this.type=e,this.g=this.target=t,this.defaultPrevented=!1}h.prototype.s=!1,h.prototype.ma=function(){this.s||(this.s=!0,this.N())},h.prototype.N=function(){if(this.C)for(;this.C.length;)this.C.shift()()},c.prototype.h=function(){this.defaultPrevented=!0};var ne=(()=>{if(!R.addEventListener||!Object.defineProperty)return!1;var e=!1,t=Object.defineProperty({},"passive",{get:function(){e=!0}});try{var r=()=>{};R.addEventListener("test",r,t),R.removeEventListener("test",r,t)}catch(e){}return e})();function d(e,t){if(c.call(this,e?e.type:""),this.relatedTarget=this.g=this.target=null,this.button=this.screenY=this.screenX=this.clientY=this.clientX=0,this.key="",this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1,this.state=null,this.pointerId=0,this.pointerType="",this.i=null,e){var r=this.type=e.type,n=e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:null;if(this.target=e.target||e.srcElement,this.g=t,t=e.relatedTarget){if($){e:{try{Q(t.nodeName);var i=!0;break e}catch(e){}i=!1}i||(t=null)}}else"mouseover"==r?t=e.fromElement:"mouseout"==r&&(t=e.toElement);this.relatedTarget=t,n?(this.clientX=void 0!==n.clientX?n.clientX:n.pageX,this.clientY=void 0!==n.clientY?n.clientY:n.pageY,this.screenX=n.screenX||0,this.screenY=n.screenY||0):(this.clientX=void 0!==e.clientX?e.clientX:e.pageX,this.clientY=void 0!==e.clientY?e.clientY:e.pageY,this.screenX=e.screenX||0,this.screenY=e.screenY||0),this.button=e.button,this.key=e.key||"",this.ctrlKey=e.ctrlKey,this.altKey=e.altKey,this.shiftKey=e.shiftKey,this.metaKey=e.metaKey,this.pointerId=e.pointerId||0,this.pointerType="string"==typeof e.pointerType?e.pointerType:ie[e.pointerType]||"",this.state=e.state,(this.i=e).defaultPrevented&&d.aa.h.call(this)}}s(d,c);var ie={2:"touch",3:"pen",4:"mouse"},f=(d.prototype.h=function(){d.aa.h.call(this);var e=this.i;e.preventDefault?e.preventDefault():e.returnValue=!1},"closure_listenable_"+(1e6*Math.random()|0)),se=0;function ae(e,t,r,n,i){this.listener=e,this.proxy=null,this.src=t,this.type=r,this.capture=!!n,this.ha=i,this.key=++se,this.da=this.fa=!1}function oe(e){e.da=!0,e.listener=null,e.proxy=null,e.src=null,e.ha=null}function le(e){this.src=e,this.g={},this.h=0}function ue(e,t){var r,n,i,s=t.type;s in e.g&&(r=e.g[s],(i=0<=(n=Array.prototype.indexOf.call(r,t,void 0)))&&Array.prototype.splice.call(r,n,1),i)&&(oe(t),0==e.g[s].length)&&(delete e.g[s],e.h--)}function he(e,t,r,n){for(var i=0;i<e.length;++i){var s=e[i];if(!s.da&&s.listener==t&&s.capture==!!r&&s.ha==n)return i}return-1}le.prototype.add=function(e,t,r,n,i){var s=e.toString(),a=((e=this.g[s])||(e=this.g[s]=[],this.h++),he(e,t,n,i));return-1<a?(t=e[a],r||(t.fa=!1)):((t=new ae(t,this.src,s,!!n,i)).fa=r,e.push(t)),t};var ce="closure_lm_"+(1e6*Math.random()|0),de={};function fe(e,t,r,n,i){if(n&&n.once)return function e(t,r,n,i,s){if(Array.isArray(r)){for(var a=0;a<r.length;a++)e(t,r[a],n,i,s);return null}n=_e(n);return t&&t[f]?t.L(r,n,u(i)?!!i.capture:!!i,s):ge(t,r,n,!0,i,s)}(e,t,r,n,i);if(Array.isArray(t)){for(var s=0;s<t.length;s++)fe(e,t[s],r,n,i);return null}return r=_e(r),e&&e[f]?e.K(t,r,u(n)?!!n.capture:!!n,i):ge(e,t,r,!1,n,i)}function ge(e,t,r,n,i,s){if(!t)throw Error("Invalid event type");var a=u(i)?!!i.capture:!!i,o=ve(e);if(o||(e[ce]=o=new le(e)),!(r=o.add(t,r,n,a,s)).proxy)if(n=(()=>{let r=ye;return function e(t){return r.call(e.src,e.listener,t)}})(),(r.proxy=n).src=e,n.listener=r,e.addEventListener)void 0===(i=ne?i:a)&&(i=!1),e.addEventListener(t.toString(),n,i);else if(e.attachEvent)e.attachEvent(pe(t.toString()),n);else{if(!e.addListener||!e.removeListener)throw Error("addEventListener and attachEvent are unavailable.");e.addListener(n)}return r}function me(e){var t,r,n;"number"!=typeof e&&e&&!e.da&&((t=e.src)&&t[f]?ue(t.i,e):(r=e.type,n=e.proxy,t.removeEventListener?t.removeEventListener(r,n,e.capture):t.detachEvent?t.detachEvent(pe(r),n):t.addListener&&t.removeListener&&t.removeListener(n),(r=ve(t))?(ue(r,e),0==r.h&&(r.src=null,t[ce]=null)):oe(e)))}function pe(e){return e in de?de[e]:de[e]="on"+e}function ye(e,t){var r,n;return e=!!e.da||(t=new d(t,this),r=e.listener,n=e.ha||e.src,e.fa&&me(e),r.call(n,t))}function ve(e){return(e=e[ce])instanceof le?e:null}var we="__closure_events_fn_"+(1e9*Math.random()>>>0);function _e(t){return"function"==typeof t?t:(t[we]||(t[we]=function(e){return t.handleEvent(e)}),t[we])}function g(){h.call(this),this.i=new le(this),(this.M=this).F=null}function m(e,t){var r,n=e.F;if(n)for(r=[];n;n=n.F)r.push(n);if(e=e.M,n=t.type||t,"string"==typeof t?t=new c(t,e):t instanceof c?t.target=t.target||e:(a=t,J(t=new c(n,e),a)),a=!0,r)for(var i=r.length-1;0<=i;i--)var s=t.g=r[i],a=be(s,n,!0,t)&&a;if(a=be(s=t.g=e,n,!0,t)&&a,a=be(s,n,!1,t)&&a,r)for(i=0;i<r.length;i++)a=be(s=t.g=r[i],n,!1,t)&&a}function be(e,t,r,n){if(!(t=e.i.g[String(t)]))return!0;t=t.concat();for(var i=!0,s=0;s<t.length;++s){var a,o,l=t[s];l&&!l.da&&l.capture==r&&(a=l.listener,o=l.ha||l.src,l.fa&&ue(e.i,l),i=!1!==a.call(o,n)&&i)}return i&&!n.defaultPrevented}function Ie(e,t,r){if("function"==typeof e)r&&(e=p(e,r));else{if(!e||"function"!=typeof e.handleEvent)throw Error("Invalid listener argument");e=p(e.handleEvent,e)}return 2147483647<Number(t)?-1:R.setTimeout(e,t||0)}s(g,h),g.prototype[f]=!0,g.prototype.removeEventListener=function(e,t,r,n){!function e(t,r,n,i,s){if(Array.isArray(r))for(var a=0;a<r.length;a++)e(t,r[a],n,i,s);else i=u(i)?!!i.capture:!!i,n=_e(n),t&&t[f]?(t=t.i,(r=String(r).toString())in t.g&&-1<(n=he(a=t.g[r],n,i,s))&&(oe(a[n]),Array.prototype.splice.call(a,n,1),0==a.length)&&(delete t.g[r],t.h--)):(t=t&&ve(t))&&(r=t.g[r.toString()],n=(t=-1)<(t=r?he(r,n,i,s):t)?r[t]:null)&&me(n)}(this,e,t,r,n)},g.prototype.N=function(){if(g.aa.N.call(this),this.i){var e,t=this.i;for(e in t.g){for(var r=t.g[e],n=0;n<r.length;n++)oe(r[n]);delete t.g[e],t.h--}}this.F=null},g.prototype.K=function(e,t,r,n){return this.i.add(String(e),t,!1,r,n)},g.prototype.L=function(e,t,r,n){return this.i.add(String(e),t,!0,r,n)};class Te extends h{constructor(e,t){super(),this.m=e,this.l=t,this.h=null,this.i=!1,this.g=null}j(e){this.h=arguments,this.g?this.i=!0:function e(t){t.g=Ie(()=>{t.g=null,t.i&&(t.i=!1,e(t))},t.l);var r=t.h;t.h=null,t.m.apply(null,r)}(this)}N(){super.N(),this.g&&(R.clearTimeout(this.g),this.g=null,this.i=!1,this.h=null)}}function y(e){h.call(this),this.h=e,this.g={}}s(y,h);var Ee=[];function Se(e){H(e.g,function(e,t){this.g.hasOwnProperty(t)&&me(e)},e),e.g={}}y.prototype.N=function(){y.aa.N.call(this),Se(this)},y.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented")};var xe=R.JSON.stringify,Ae=R.JSON.parse,Ce=class{stringify(e){return R.JSON.stringify(e,void 0)}parse(e){return R.JSON.parse(e,void 0)}};function De(){}function Ne(e){return e.h||(e.h=e.i())}function ke(){}De.prototype.h=null;var Re={OPEN:"a",kb:"b",Ja:"c",wb:"d"};function Me(){c.call(this,"d")}function Oe(){c.call(this,"c")}s(Me,c),s(Oe,c);var v={},Le=null;function Fe(){return Le=Le||new g}function Ve(e){c.call(this,v.La,e)}function Pe(){var e=Fe();m(e,new Ve(e))}function Ue(e,t){c.call(this,v.STAT_EVENT,e),this.stat=t}function O(e){var t=Fe();m(t,new Ue(t,e))}function Be(e,t){c.call(this,v.Ma,e),this.size=t}function qe(e,t){if("function"!=typeof e)throw Error("Fn must not be null and must be a function");return R.setTimeout(function(){e()},t)}function ze(){this.g=!0}function L(e,t,r,n){e.info(function(){return"XMLHTTP TEXT ("+t+"): "+((e,t)=>{if(!e.g)return t;if(!t)return null;try{var r=JSON.parse(t);if(r)for(e=0;e<r.length;e++)if(Array.isArray(r[e])){var n=r[e];if(!(n.length<2)){var i=n[1];if(Array.isArray(i)&&!(i.length<1)){var s=i[0];if("noop"!=s&&"stop"!=s&&"close"!=s)for(var a=1;a<i.length;a++)i[a]=""}}}return xe(r)}catch(e){return t}})(e,r)+(n?" "+n:"")})}v.La="serverreachability",s(Ve,c),v.STAT_EVENT="statevent",s(Ue,c),v.Ma="timingevent",s(Be,c),ze.prototype.xa=function(){this.g=!1},ze.prototype.info=function(){};var je={NO_ERROR:0,gb:1,tb:2,sb:3,nb:4,rb:5,ub:6,Ia:7,TIMEOUT:8,xb:9},Ke={lb:"complete",Hb:"success",Ja:"error",Ia:"abort",zb:"ready",Ab:"readystatechange",TIMEOUT:"timeout",vb:"incrementaldata",yb:"progress",ob:"downloadprogress",Pb:"uploadprogress"};function Ge(){}function w(e,t,r,n){this.j=e,this.i=t,this.l=r,this.R=n||1,this.U=new y(this),this.I=45e3,this.H=null,this.o=!1,this.m=this.A=this.v=this.L=this.F=this.S=this.B=null,this.D=[],this.g=null,this.C=0,this.s=this.u=null,this.X=-1,this.J=!1,this.O=0,this.M=null,this.W=this.K=this.T=this.P=!1,this.h=new Qe}function Qe(){this.i=null,this.g="",this.h=!1}s(Ge,De),Ge.prototype.g=function(){return new XMLHttpRequest},Ge.prototype.i=function(){return{}};var $e=new Ge,He={},We={};function Ye(e,t,r){e.L=1,e.v=yt(b(t)),e.m=r,e.P=!0,Je(e,null)}function Je(e,t){e.F=Date.now(),Ze(e),e.A=b(e.v);var r=e.A,n=e.R,i=(Array.isArray(n)||(n=[String(n)]),Nt(r.i,"t",n),e.C=0,r=e.j.J,e.h=new Qe,e.g=cr(e.j,r?t:null,!e.m),0<e.O&&(e.M=new Te(p(e.Y,e,e.g),e.O)),t=e.U,r=e.g,n=e.ca,"readystatechange");Array.isArray(i)||(i&&(Ee[0]=i.toString()),i=Ee);for(var a,o,l,u,h,c,s=0;s<i.length;s++){var d=fe(r,i[s],n||t.handleEvent,!1,t.h||t);if(!d)break;t.g[d.key]=d}t=e.H?W(e.H):{},e.m?(e.u||(e.u="POST"),t["Content-Type"]="application/x-www-form-urlencoded",e.g.ea(e.A,e.u,e.m,t)):(e.u="GET",e.g.ea(e.A,e.u,null,t)),Pe(),a=e.i,o=e.u,l=e.A,u=e.l,h=e.R,c=e.m,a.info(function(){if(a.g)if(c)for(var e="",t=c.split("&"),r=0;r<t.length;r++){var n,i,s=t[r].split("=");1<s.length&&(n=s[0],s=s[1],e=2<=(i=n.split("_")).length&&"type"==i[1]?e+(n+"=")+s+"&":e+(n+"=redacted&"))}else e=null;else e=c;return"XMLHTTP REQ ("+u+") [attempt "+h+"]: "+o+"\n"+l+"\n"+e})}function Xe(e){return e.g&&"GET"==e.u&&2!=e.L&&e.j.Ca}function Ze(e){e.S=Date.now()+e.I,et(e,e.I)}function et(e,t){if(null!=e.B)throw Error("WatchDog timer not null");e.B=qe(p(e.ba,e),t)}function tt(e){e.B&&(R.clearTimeout(e.B),e.B=null)}function rt(e){0==e.j.G||e.J||or(e.j,e)}function F(e){tt(e);var t=e.M;t&&"function"==typeof t.ma&&t.ma(),e.M=null,Se(e.U),e.g&&(t=e.g,e.g=null,t.abort(),t.ma())}function nt(e,t){try{var r=e.j;if(0!=r.G&&(r.g==e||lt(r.h,e)))if(!e.K&&lt(r.h,e)&&3==r.G){try{var n=r.Da.g.parse(t)}catch(e){n=null}if(Array.isArray(n)&&3==n.length){var i=n;if(0==i[0]){e:if(!r.u){if(r.g){if(!(r.g.F+3e3<e.F))break e;ar(r),Yt(r)}nr(r),O(18)}}else r.za=i[1],0<r.za-r.T&&i[2]<37500&&r.F&&0==r.v&&!r.C&&(r.C=qe(p(r.Za,r),6e3));if(ot(r.h)<=1&&r.ca){try{r.ca()}catch(e){}r.ca=void 0}}else A(r,11)}else if(!e.K&&r.g!=e||ar(r),!M(t))for(i=r.Da.g.parse(t),t=0;t<i.length;t++){var s,a,o,l,u,h,c,d,f,g,m=i[t];r.T=m[0],m=m[1],2==r.G?"c"==m[0]?(r.K=m[1],r.ia=m[2],null!=(s=m[3])&&(r.la=s,r.j.info("VER="+r.la)),null!=(a=m[4])&&(r.Aa=a,r.j.info("SVER="+r.Aa)),null!=(o=m[5])&&"number"==typeof o&&0<o&&(n=1.5*o,r.L=n,r.j.info("backChannelRequestTimeoutMs_="+n)),n=r,(l=e.g)&&(!(u=l.g?l.g.getResponseHeader("X-Client-Wire-Protocol"):null)||(h=n.h).g||-1==u.indexOf("spdy")&&-1==u.indexOf("quic")&&-1==u.indexOf("h2")||(h.j=h.l,h.g=new Set,h.h&&(ut(h,h.h),h.h=null)),n.D)&&(c=l.g?l.g.getResponseHeader("X-HTTP-Session-Id"):null)&&(n.ya=c,I(n.I,n.D,c)),r.G=3,r.l&&r.l.ua(),r.ba&&(r.R=Date.now()-e.F,r.j.info("Handshake RTT: "+r.R+"ms")),d=e,(n=r).qa=hr(n,n.J?n.ia:null,n.W),d.K?(ht(n.h,d),f=d,(g=n.L)&&(f.I=g),f.B&&(tt(f),Ze(f)),n.g=d):rr(n),0<r.i.length&&Xt(r)):"stop"!=m[0]&&"close"!=m[0]||A(r,7):3==r.G&&("stop"==m[0]||"close"==m[0]?"stop"==m[0]?A(r,7):Wt(r):"noop"!=m[0]&&r.l&&r.l.ta(m),r.v=0)}Pe()}catch(e){}}w.prototype.ca=function(e){e=e.target;var t=this.M;t&&3==V(e)?t.j():this.Y(e)},w.prototype.Y=function(e){try{if(e==this.g)e:{var t=V(this.g),r=this.g.Ba();this.g.Z();if(!(t<3)&&(3!=t||this.g&&(this.h.h||this.g.oa()||Qt(this.g)))){this.J||4!=t||7==r||Pe(),tt(this);var n=this.g.Z();this.X=n;t:if(Xe(this)){var i=Qt(this.g),s=(e="",i.length),a=4==V(this.g);if(!this.h.i){if("undefined"==typeof TextDecoder){F(this),rt(this);var o="";break t}this.h.i=new R.TextDecoder}for(r=0;r<s;r++)this.h.h=!0,e+=this.h.i.decode(i[r],{stream:!(a&&r==s-1)});i.length=0,this.h.g+=e,this.C=0,o=this.h.g}else o=this.g.oa();if(this.o=200==n,S=this.i,x=this.u,A=this.A,C=this.l,D=this.R,N=t,k=n,S.info(function(){return"XMLHTTP RESP ("+C+") [ attempt "+D+"]: "+x+"\n"+A+"\n"+N+" "+k}),this.o){if(this.T&&!this.K){t:{if(this.g){var l,u=this.g;if((l=u.g?u.g.getResponseHeader("X-HTTP-Initial-Response"):null)&&!M(l)){var h=l;break t}}h=null}if(!(n=h)){this.o=!1,this.s=3,O(12),F(this),rt(this);break e}L(this.i,this.l,n,"Initial handshake response via X-HTTP-Initial-Response"),this.K=!0,nt(this,n)}if(this.P){for(var c,d,n=!0;!this.J&&this.C<o.length;){if(I=o,E=T=void 0,T=(b=this).C,(c=-1==(E=I.indexOf("\n",T))?We:(T=Number(I.substring(T,E)),isNaN(T)?He:(E+=1)+T>I.length?We:(I=I.slice(E,E+T),b.C=E+T,I)))==We){4==t&&(this.s=4,O(14),n=!1),L(this.i,this.l,null,"[Incomplete Response]");break}if(c==He){this.s=4,O(15),L(this.i,this.l,o,"[Invalid Chunk]"),n=!1;break}L(this.i,this.l,c,null),nt(this,c)}Xe(this)&&0!=this.C&&(this.h.g=this.h.g.slice(this.C),this.C=0),4!=t||0!=o.length||this.h.h||(this.s=1,O(16),n=!1),this.o=this.o&&n,n?0<o.length&&!this.W&&(this.W=!0,(d=this.j).g==this)&&d.ba&&!d.M&&(d.j.info("Great, no buffering proxy detected. Bytes received: "+o.length),ir(d),d.M=!0,O(11)):(L(this.i,this.l,o,"[Invalid Chunked Response]"),F(this),rt(this))}else L(this.i,this.l,o,null),nt(this,o);4==t&&F(this),this.o&&!this.J&&(4==t?or(this.j,this):(this.o=!1,Ze(this)))}else{{var f=this.g;var g,m,p,y={};f=(f.g&&2<=V(f)&&f.g.getAllResponseHeaders()||"").split("\r\n");for(let e=0;e<f.length;e++)M(f[e])||(g=(e=>{for(var t=1,r=(e=e.split(":"),[]);0<t&&e.length;)r.push(e.shift()),t--;return e.length&&r.push(e.join(":")),r})(f[e]),m=g[0],"string"==typeof(g=g[1])&&(g=g.trim(),p=y[m]||[],(y[m]=p).push(g)));var v,w=y,_=function(e){return e.join(", ")};for(v in w)_.call(void 0,w[v],v,w)}400==n&&0<o.indexOf("Unknown SID")?(this.s=3,O(12)):(this.s=0,O(13)),F(this),rt(this)}}}}catch(e){}var b,I,T,E,S,x,A,C,D,N,k},w.prototype.cancel=function(){this.J=!0,F(this)},w.prototype.ba=function(){this.B=null;var e,t,r=Date.now();0<=r-this.S?(e=this.i,t=this.A,e.info(function(){return"TIMEOUT: "+t}),2!=this.L&&(Pe(),O(17)),F(this),this.s=2,rt(this)):et(this,this.S-r)};var it=class{constructor(e,t){this.g=e,this.map=t}};function st(e){this.l=e||10,e=R.PerformanceNavigationTiming?0<(e=R.performance.getEntriesByType("navigation")).length&&("hq"==e[0].nextHopProtocol||"h2"==e[0].nextHopProtocol):!!(R.chrome&&R.chrome.loadTimes&&R.chrome.loadTimes()&&R.chrome.loadTimes().wasFetchedViaSpdy),this.j=e?this.l:1,this.g=null,1<this.j&&(this.g=new Set),this.h=null,this.i=[]}function at(e){return e.h||e.g&&e.g.size>=e.j}function ot(e){return e.h?1:e.g?e.g.size:0}function lt(e,t){return e.h?e.h==t:e.g&&e.g.has(t)}function ut(e,t){e.g?e.g.add(t):e.h=t}function ht(e,t){e.h&&e.h==t?e.h=null:e.g&&e.g.has(t)&&e.g.delete(t)}function ct(t){if(null!=t.h)return t.i.concat(t.h.D);if(null==t.g||0===t.g.size)return K(t.i);{let e=t.i;for(var r of t.g.values())e=e.concat(r.D);return e}}function dt(e,t){if(e.forEach&&"function"==typeof e.forEach)e.forEach(t,void 0);else if(B(e)||"string"==typeof e)Array.prototype.forEach.call(e,t,void 0);else for(var r=(e=>{if(e.na&&"function"==typeof e.na)return e.na();if(!e.V||"function"!=typeof e.V){if("undefined"!=typeof Map&&e instanceof Map)return Array.from(e.keys());if(!("undefined"!=typeof Set&&e instanceof Set)){if(B(e)||"string"==typeof e){var t=[];e=e.length;for(var r=0;r<e;r++)t.push(r)}else for(var n in t=[],r=0,e)t[r++]=n;return t}}})(e),n=(e=>{if(e.V&&"function"==typeof e.V)return e.V();if("undefined"!=typeof Map&&e instanceof Map||"undefined"!=typeof Set&&e instanceof Set)return Array.from(e.values());if("string"==typeof e)return e.split("");if(B(e))for(var t=[],r=e.length,n=0;n<r;n++)t.push(e[n]);else for(n in t=[],r=0,e)t[r++]=e[n];return t})(e),i=n.length,s=0;s<i;s++)t.call(void 0,n[s],r&&r[s],e)}st.prototype.cancel=function(){if(this.i=ct(this),this.h)this.h.cancel(),this.h=null;else if(this.g&&0!==this.g.size){for(var e of this.g.values())e.cancel();this.g.clear()}};var ft=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function _(e){var t,r;this.g=this.o=this.j="",this.s=null,this.m=this.l="",this.h=!1,e instanceof _?(this.h=e.h,gt(this,e.j),this.o=e.o,this.g=e.g,mt(this,e.s),this.l=e.l,t=e.i,(r=new At).i=t.i,t.g&&(r.g=new Map(t.g),r.h=t.h),pt(this,r),this.m=e.m):e&&(t=String(e).match(ft))?(this.h=!1,gt(this,t[1]||"",!0),this.o=vt(t[2]||""),this.g=vt(t[3]||"",!0),mt(this,t[4]),this.l=vt(t[5]||"",!0),pt(this,t[6]||"",!0),this.m=vt(t[7]||"")):(this.h=!1,this.i=new At(null,this.h))}function b(e){return new _(e)}function gt(e,t,r){e.j=r?vt(t,!0):t,e.j&&(e.j=e.j.replace(/:$/,""))}function mt(e,t){if(t){if(t=Number(t),isNaN(t)||t<0)throw Error("Bad port number "+t);e.s=t}else e.s=null}function pt(e,t,r){var n,i;t instanceof At?(e.i=t,n=e.i,(i=e.h)&&!n.j&&(T(n),n.i=null,n.g.forEach(function(e,t){var r=t.toLowerCase();t!=r&&(Ct(this,t),Nt(this,r,e))},n)),n.j=i):(r||(t=wt(t,St)),e.i=new At(t,e.h))}function I(e,t,r){e.i.set(t,r)}function yt(e){return I(e,"zx",Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^Date.now()).toString(36)),e}function vt(e,t){return e?t?decodeURI(e.replace(/%25/g,"%2525")):decodeURIComponent(e):""}function wt(e,t,r){return"string"==typeof e?(e=encodeURI(e).replace(t,_t),e=r?e.replace(/%25([0-9a-fA-F]{2})/g,"%$1"):e):null}function _t(e){return"%"+((e=e.charCodeAt(0))>>4&15).toString(16)+(15&e).toString(16)}_.prototype.toString=function(){var e=[],t=this.j,r=(t&&e.push(wt(t,It,!0),":"),this.g);return!r&&"file"!=t||(e.push("//"),(t=this.o)&&e.push(wt(t,It,!0),"@"),e.push(encodeURIComponent(String(r)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),null==(r=this.s))||e.push(":",String(r)),(r=this.l)&&(this.g&&"/"!=r.charAt(0)&&e.push("/"),e.push(wt(r,"/"==r.charAt(0)?Et:Tt,!0))),(r=this.i.toString())&&e.push("?",r),(r=this.m)&&e.push("#",wt(r,xt)),e.join("")};var bt,It=/[#\/\?@]/g,Tt=/[#\?:]/g,Et=/[#\?]/g,St=/[#\?@]/g,xt=/#/g;function At(e,t){this.h=this.g=null,this.i=e||null,this.j=!!t}function T(r){if(!r.g&&(r.g=new Map,r.h=0,r.i)){var e=r.i,t=function(e,t){r.add(decodeURIComponent(e.replace(/\+/g," ")),t)};if(e){e=e.split("&");for(var n=0;n<e.length;n++){var i,s=e[n].indexOf("="),a=null;0<=s?(i=e[n].substring(0,s),a=e[n].substring(s+1)):i=e[n],t(i,a?decodeURIComponent(a.replace(/\+/g," ")):"")}}}}function Ct(e,t){T(e),t=E(e,t),e.g.has(t)&&(e.i=null,e.h-=e.g.get(t).length,e.g.delete(t))}function Dt(e,t){return T(e),t=E(e,t),e.g.has(t)}function Nt(e,t,r){Ct(e,t),0<r.length&&(e.i=null,e.g.set(E(e,t),K(r)),e.h+=r.length)}function E(e,t){return t=String(t),t=e.j?t.toLowerCase():t}function S(e,t,r,n,i){try{i&&(i.onload=null,i.onerror=null,i.onabort=null,i.ontimeout=null),n(r)}catch(e){}}function kt(){this.g=new Ce}function Rt(e){this.l=e.Ub||null,this.j=e.eb||!1}function Mt(e,t){g.call(this),this.D=e,this.o=t,this.m=void 0,this.status=this.readyState=0,this.responseType=this.responseText=this.response=this.statusText="",this.onreadystatechange=null,this.u=new Headers,this.h=null,this.B="GET",this.A="",this.g=!1,this.v=this.j=this.l=null}function Ot(e){e.j.read().then(e.Pa.bind(e)).catch(e.ga.bind(e))}function Lt(e){e.readyState=4,e.l=null,e.j=null,e.v=null,Ft(e)}function Ft(e){e.onreadystatechange&&e.onreadystatechange.call(e)}function Vt(e){let r="";return H(e,function(e,t){r=(r=r+t+":")+e+"\r\n"}),r}function Pt(e,t,r){e:{for(n in r){var n=!1;break e}n=!0}n||(r=Vt(r),"string"==typeof e?null!=r&&encodeURIComponent(String(r)):I(e,t,r))}function x(e){g.call(this),this.headers=new Map,this.o=e||null,this.h=!1,this.v=this.g=null,this.D="",this.m=0,this.l="",this.j=this.B=this.u=this.A=!1,this.I=null,this.H="",this.J=!1}(e=At.prototype).add=function(e,t){T(this),this.i=null,e=E(this,e);var r=this.g.get(e);return r||this.g.set(e,r=[]),r.push(t),this.h+=1,this},e.forEach=function(r,n){T(this),this.g.forEach(function(e,t){e.forEach(function(e){r.call(n,e,t,this)},this)},this)},e.na=function(){T(this);var t=Array.from(this.g.values()),r=Array.from(this.g.keys()),n=[];for(let s=0;s<r.length;s++){var i=t[s];for(let e=0;e<i.length;e++)n.push(r[s])}return n},e.V=function(t){T(this);let r=[];if("string"==typeof t)Dt(this,t)&&(r=r.concat(this.g.get(E(this,t))));else{t=Array.from(this.g.values());for(let e=0;e<t.length;e++)r=r.concat(t[e])}return r},e.set=function(e,t){return T(this),this.i=null,Dt(this,e=E(this,e))&&(this.h-=this.g.get(e).length),this.g.set(e,[t]),this.h+=1,this},e.get=function(e,t){return e&&0<(e=this.V(e)).length?String(e[0]):t},e.toString=function(){if(this.i)return this.i;if(!this.g)return"";for(var e=[],t=Array.from(this.g.keys()),r=0;r<t.length;r++)for(var n=t[r],i=encodeURIComponent(String(n)),s=this.V(n),n=0;n<s.length;n++){var a=i;""!==s[n]&&(a+="="+encodeURIComponent(String(s[n]))),e.push(a)}return this.i=e.join("&")},s(Rt,De),Rt.prototype.g=function(){return new Mt(this.l,this.j)},Rt.prototype.i=(bt={},function(){return bt}),s(Mt,g),(e=Mt.prototype).open=function(e,t){if(0!=this.readyState)throw this.abort(),Error("Error reopening a connection");this.B=e,this.A=t,this.readyState=1,Ft(this)},e.send=function(e){if(1!=this.readyState)throw this.abort(),Error("need to call open() first. ");this.g=!0;var t={headers:this.u,method:this.B,credentials:this.m,cache:void 0};e&&(t.body=e),(this.D||R).fetch(new Request(this.A,t)).then(this.Sa.bind(this),this.ga.bind(this))},e.abort=function(){this.response=this.responseText="",this.u=new Headers,this.status=0,this.j&&this.j.cancel("Request was aborted.").catch(()=>{}),1<=this.readyState&&this.g&&4!=this.readyState&&(this.g=!1,Lt(this)),this.readyState=0},e.Sa=function(e){if(this.g&&(this.l=e,this.h||(this.status=this.l.status,this.statusText=this.l.statusText,this.h=e.headers,this.readyState=2,Ft(this)),this.g)&&(this.readyState=3,Ft(this),this.g))if("arraybuffer"===this.responseType)e.arrayBuffer().then(this.Qa.bind(this),this.ga.bind(this));else if(void 0!==R.ReadableStream&&"body"in e){if(this.j=e.body.getReader(),this.o){if(this.responseType)throw Error('responseType must be empty for "streamBinaryChunks" mode responses.');this.response=[]}else this.response=this.responseText="",this.v=new TextDecoder;Ot(this)}else e.text().then(this.Ra.bind(this),this.ga.bind(this))},e.Pa=function(e){var t;this.g&&(this.o&&e.value?this.response.push(e.value):!this.o&&(t=e.value||new Uint8Array(0),t=this.v.decode(t,{stream:!e.done}))&&(this.response=this.responseText+=t),(e.done?Lt:Ft)(this),3==this.readyState)&&Ot(this)},e.Ra=function(e){this.g&&(this.response=this.responseText=e,Lt(this))},e.Qa=function(e){this.g&&(this.response=e,Lt(this))},e.ga=function(){this.g&&Lt(this)},e.setRequestHeader=function(e,t){this.u.append(e,t)},e.getResponseHeader=function(e){return this.h&&this.h.get(e.toLowerCase())||""},e.getAllResponseHeaders=function(){if(!this.h)return"";for(var e=[],t=this.h.entries(),r=t.next();!r.done;)r=r.value,e.push(r[0]+": "+r[1]),r=t.next();return e.join("\r\n")},Object.defineProperty(Mt.prototype,"withCredentials",{get:function(){return"include"===this.m},set:function(e){this.m=e?"include":"same-origin"}}),s(x,g);var Ut=/^https?$/i,Bt=["POST","PUT"];function qt(e,t){e.h=!1,e.g&&(e.j=!0,e.g.abort(),e.j=!1),e.l=t,e.m=5,zt(e),Kt(e)}function zt(e){e.A||(e.A=!0,m(e,"complete"),m(e,"error"))}function jt(e){if(e.h&&void 0!==U&&(!e.v[1]||4!=V(e)||2!=e.Z()))if(e.u&&4==V(e))Ie(e.Ea,0,e);else if(m(e,"readystatechange"),4==V(e)){e.h=!1;try{var t,r,n,i=e.Z();switch(i){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var s=!0;break;default:s=!1}if((t=s)||((r=0===i)&&(!(n=String(e.D).match(ft)[1]||null)&&R.self&&R.self.location&&(n=R.self.location.protocol.slice(0,-1)),r=!Ut.test(n?n.toLowerCase():"")),t=r),t)m(e,"complete"),m(e,"success");else{e.m=6;try{var a=2<V(e)?e.g.statusText:""}catch(e){a=""}e.l=a+" ["+e.Z()+"]",zt(e)}}finally{Kt(e)}}}function Kt(e,t){if(e.g){Gt(e);var r=e.g,n=e.v[0]?()=>{}:null;e.g=null,e.v=null,t||m(e,"ready");try{r.onreadystatechange=n}catch(e){}}}function Gt(e){e.I&&(R.clearTimeout(e.I),e.I=null)}function V(e){return e.g?e.g.readyState:0}function Qt(e){try{if(e.g){if("response"in e.g)return e.g.response;switch(e.H){case"":case"text":return e.g.responseText;case"arraybuffer":if("mozResponseArrayBuffer"in e.g)return e.g.mozResponseArrayBuffer}}return null}catch(e){return null}}function $t(e,t,r){return r&&r.internalChannelParams&&r.internalChannelParams[e]||t}function Ht(e){this.Aa=0,this.i=[],this.j=new ze,this.ia=this.qa=this.I=this.W=this.g=this.ya=this.D=this.H=this.m=this.S=this.o=null,this.Ya=this.U=0,this.Va=$t("failFast",!1,e),this.F=this.C=this.u=this.s=this.l=null,this.X=!0,this.za=this.T=-1,this.Y=this.v=this.B=0,this.Ta=$t("baseRetryDelayMs",5e3,e),this.cb=$t("retryDelaySeedMs",1e4,e),this.Wa=$t("forwardChannelMaxRetries",2,e),this.wa=$t("forwardChannelRequestTimeoutMs",2e4,e),this.pa=e&&e.xmlHttpFactory||void 0,this.Xa=e&&e.Tb||void 0,this.Ca=e&&e.useFetchStreams||!1,this.L=void 0,this.J=e&&e.supportsCrossDomainXhr||!1,this.K="",this.h=new st(e&&e.concurrentRequestLimit),this.Da=new kt,this.P=e&&e.fastHandshake||!1,this.O=e&&e.encodeInitMessageHeaders||!1,this.P&&this.O&&(this.O=!1),this.Ua=e&&e.Rb||!1,e&&e.xa&&this.j.xa(),e&&e.forceLongPolling&&(this.X=!1),this.ba=!this.P&&this.X&&e&&e.detectBufferingProxy||!1,this.ja=void 0,e&&e.longPollingTimeout&&0<e.longPollingTimeout&&(this.ja=e.longPollingTimeout),this.ca=void 0,this.R=0,this.M=!1,this.ka=this.A=null}function Wt(e){if(Jt(e),3==e.G){var t=e.U++,r=b(e.I);if(I(r,"SID",e.K),I(r,"RID",t),I(r,"TYPE","terminate"),er(e,r),(t=new w(e,e.j,t)).L=2,t.v=yt(b(r)),r=!1,R.navigator&&R.navigator.sendBeacon)try{r=R.navigator.sendBeacon(t.v.toString(),"")}catch(e){}!r&&R.Image&&((new Image).src=t.v,r=!0),r||(t.g=cr(t.j,null),t.g.ea(t.v)),t.F=Date.now(),Ze(t)}ur(e)}function Yt(e){e.g&&(ir(e),e.g.cancel(),e.g=null)}function Jt(e){Yt(e),e.u&&(R.clearTimeout(e.u),e.u=null),ar(e),e.h.cancel(),e.s&&("number"==typeof e.s&&R.clearTimeout(e.s),e.s=null)}function Xt(e){var t;at(e.h)||e.s||(e.s=!0,t=e.Ga,o||te(),l||(o(),l=!0),ee.add(t,e),e.B=0)}function Zt(e,t){var r=t?t.l:e.U++,n=b(e.I);I(n,"SID",e.K),I(n,"RID",r),I(n,"AID",e.T),er(e,n),e.m&&e.o&&Pt(n,e.m,e.o),r=new w(e,e.j,r,e.B+1),null===e.m&&(r.H=e.o),t&&(e.i=t.D.concat(e.i)),t=tr(e,r,1e3),r.I=Math.round(.5*e.wa)+Math.round(.5*e.wa*Math.random()),ut(e.h,r),Ye(r,n,t)}function er(e,r){e.H&&H(e.H,function(e,t){I(r,t,e)}),e.l&&dt({},function(e,t){I(r,t,e)})}function tr(e,t,i){i=Math.min(e.i.length,i);var s=e.l?p(e.l.Na,e.l,e):null;e:{let r=e.i,n=-1;for(;;){var a=["count="+i];-1==n?0<i?(n=r[0].g,a.push("ofs="+n)):n=0:a.push("ofs="+n);let e=!0;for(let t=0;t<i;t++){var o=r[t].g,l=r[t].map;if((o-=n)<0)n=Math.max(0,r[t].g-100),e=!1;else try{((e,n,t)=>{let i=t||"";try{dt(e,function(e,t){let r=e;u(e)&&(r=xe(e)),n.push(i+t+"="+encodeURIComponent(r))})}catch(e){throw n.push(i+"type="+encodeURIComponent("_badmap")),e}})(l,a,"req"+o+"_")}catch(e){s&&s(l)}}if(e){s=a.join("&");break e}}}return e=e.i.splice(0,i),t.D=e,s}function rr(e){var t;e.g||e.u||(e.Y=1,t=e.Fa,o||te(),l||(o(),l=!0),ee.add(t,e),e.v=0)}function nr(e){return!(e.g||e.u||3<=e.v)&&(e.Y++,e.u=qe(p(e.Fa,e),lr(e,e.v)),e.v++,1)}function ir(e){null!=e.A&&(R.clearTimeout(e.A),e.A=null)}function sr(e){e.g=new w(e,e.j,"rpc",e.Y),null===e.m&&(e.g.H=e.o),e.g.O=0;var t=b(e.qa),r=(I(t,"RID","rpc"),I(t,"SID",e.K),I(t,"AID",e.T),I(t,"CI",e.F?"0":"1"),!e.F&&e.ja&&I(t,"TO",e.ja),I(t,"TYPE","xmlhttp"),er(e,t),e.m&&e.o&&Pt(t,e.m,e.o),e.L&&(e.g.I=e.L),e.g);e=e.ia,r.L=1,r.v=yt(b(t)),r.m=null,r.P=!0,Je(r,e)}function ar(e){null!=e.C&&(R.clearTimeout(e.C),e.C=null)}function or(e,t){var r,n,i,s=null;if(e.g==t){ar(e),ir(e),e.g=null;var a=2}else{if(!lt(e.h,t))return;s=t.D,ht(e.h,t),a=1}if(0!=e.G)if(t.o)(1==a?(s=t.m?t.m.length:0,t=Date.now()-t.F,r=e.B,m(a=Fe(),new Be(a,s)),Xt):rr)(e);else if(3==(r=t.s)||0==r&&0<t.X||(1!=a||(i=t,ot((n=e).h)>=n.h.j-(n.s?1:0))||(n.s?(n.i=i.D.concat(n.i),0):1==n.G||2==n.G||n.B>=(n.Va?0:n.Wa)||(n.s=qe(p(n.Ga,n,i),lr(n,n.B)),n.B++,0)))&&(2!=a||!nr(e)))switch(s&&0<s.length&&(t=e.h,t.i=t.i.concat(s)),r){case 1:A(e,5);break;case 4:A(e,10);break;case 3:A(e,6);break;default:A(e,2)}}function lr(e,t){let r=e.Ta+Math.floor(Math.random()*e.cb);return e.isActive()||(r*=2),r*t}function A(e,t){var r,n,i;e.j.info("Error code "+t),2==t?(r=p(e.fb,e),n=!(i=e.Xa),i=new _(i||"//www.google.com/images/cleardot.gif"),R.location&&"http"==R.location.protocol||gt(i,"https"),yt(i),(n?(t,r)=>{var n=new ze;if(R.Image){let e=new Image;e.onload=j(S,n,"TestLoadImage: loaded",!0,r,e),e.onerror=j(S,n,"TestLoadImage: error",!1,r,e),e.onabort=j(S,n,"TestLoadImage: abort",!1,r,e),e.ontimeout=j(S,n,"TestLoadImage: timeout",!1,r,e),R.setTimeout(function(){e.ontimeout&&e.ontimeout()},1e4),e.src=t}else r(!1)}:(e,t)=>{let r=new ze,n=new AbortController,i=setTimeout(()=>{n.abort(),S(r,0,!1,t)},1e4);fetch(e,{signal:n.signal}).then(e=>{clearTimeout(i),e.ok?S(r,0,!0,t):S(r,0,!1,t)}).catch(()=>{clearTimeout(i),S(r,0,!1,t)})})(i.toString(),r)):O(2),e.G=0,e.l&&e.l.sa(t),ur(e),Jt(e)}function ur(e){var t;e.G=0,e.ka=[],e.l&&(0==(t=ct(e.h)).length&&0==e.i.length||(G(e.ka,t),G(e.ka,e.i),e.h.i.length=0,K(e.i),e.i.length=0),e.l.ra())}function hr(e,t,r){var n,i,s=r instanceof _?b(r):new _(r);return""!=s.g?(t&&(s.g=t+"."+s.g),mt(s,s.s)):(s=(n=R.location).protocol,t=t?t+"."+n.hostname:n.hostname,n=+n.port,i=new _(null),s&&gt(i,s),t&&(i.g=t),n&&mt(i,n),r&&(i.l=r),s=i),r=e.D,t=e.ya,r&&t&&I(s,r,t),I(s,"VER",e.la),er(e,s),s}function cr(e,t,r){if(t&&!e.J)throw Error("Can't create secondary domain capable XhrIo object.");return(t=e.Ca&&!e.pa?new x(new Rt({eb:r})):new x(e.pa)).Ha(e.J),t}function dr(){}function fr(){}function C(e,t){g.call(this),this.g=new Ht(t),this.l=e,this.h=t&&t.messageUrlParams||null,e=t&&t.messageHeaders||null,t&&t.clientProtocolHeaderRequired&&(e?e["X-Client-Protocol"]="webchannel":e={"X-Client-Protocol":"webchannel"}),this.g.o=e,e=t&&t.initMessageHeaders||null,t&&t.messageContentType&&(e?e["X-WebChannel-Content-Type"]=t.messageContentType:e={"X-WebChannel-Content-Type":t.messageContentType}),t&&t.va&&(e?e["X-WebChannel-Client-Profile"]=t.va:e={"X-WebChannel-Client-Profile":t.va}),this.g.S=e,(e=t&&t.Sb)&&!M(e)&&(this.g.m=e),this.v=t&&t.supportsCrossDomainXhr||!1,this.u=t&&t.sendRawJson||!1,(t=t&&t.httpSessionIdParam)&&!M(t)&&(this.g.D=t,null!==(e=this.h))&&t in e&&t in(e=this.h)&&delete e[t],this.j=new D(this)}function gr(e){Me.call(this),e.__headers__&&(this.headers=e.__headers__,this.statusCode=e.__status__,delete e.__headers__,delete e.__status__);var t=e.__sm__;if(t){e:{for(var r in t){e=r;break e}e=void 0}(this.i=e)&&(e=this.i,t=null!==t&&e in t?t[e]:void 0),this.data=t}else this.data=e}function mr(){Oe.call(this),this.status=1}function D(e){this.g=e}(e=x.prototype).Ha=function(e){this.J=e},e.ea=function(e,t,r,n){if(this.g)throw Error("[goog.net.XhrIo] Object is active with another request="+this.D+"; newUri="+e);t=t?t.toUpperCase():"GET",this.D=e,this.l="",this.m=0,this.A=!1,this.h=!0,this.g=(this.o||$e).g(),this.v=this.o?Ne(this.o):Ne($e),this.g.onreadystatechange=p(this.Ea,this);try{this.B=!0,this.g.open(t,String(e),!0),this.B=!1}catch(e){return void qt(this,e)}if(e=r||"",r=new Map(this.headers),n)if(Object.getPrototypeOf(n)===Object.prototype)for(var i in n)r.set(i,n[i]);else{if("function"!=typeof n.keys||"function"!=typeof n.get)throw Error("Unknown input type for opt_headers: "+String(n));for(var s of n.keys())r.set(s,n.get(s))}n=Array.from(r.keys()).find(e=>"content-type"==e.toLowerCase()),i=R.FormData&&e instanceof R.FormData,0<=Array.prototype.indexOf.call(Bt,t,void 0)&&!n&&!i&&r.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");for(var[a,o]of r)this.g.setRequestHeader(a,o);this.H&&(this.g.responseType=this.H),"withCredentials"in this.g&&this.g.withCredentials!==this.J&&(this.g.withCredentials=this.J);try{Gt(this),this.u=!0,this.g.send(e),this.u=!1}catch(e){qt(this,e)}},e.abort=function(e){this.g&&this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1,this.m=e||7,m(this,"complete"),m(this,"abort"),Kt(this))},e.N=function(){this.g&&(this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1),Kt(this,!0)),x.aa.N.call(this)},e.Ea=function(){this.s||(this.B||this.u||this.j?jt(this):this.bb())},e.bb=function(){jt(this)},e.isActive=function(){return!!this.g},e.Z=function(){try{return 2<V(this)?this.g.status:-1}catch(e){return-1}},e.oa=function(){try{return this.g?this.g.responseText:""}catch(e){return""}},e.Oa=function(e){var t;if(this.g)return t=this.g.responseText,e&&0==t.indexOf(e)&&(t=t.substring(e.length)),Ae(t)},e.Ba=function(){return this.m},e.Ka=function(){return"string"==typeof this.l?this.l:String(this.l)},(e=Ht.prototype).la=8,e.G=1,e.connect=function(e,t,r,n){O(0),this.W=e,this.H=t||{},r&&void 0!==n&&(this.H.OSID=r,this.H.OAID=n),this.F=this.X,this.I=hr(this,null,this.W),Xt(this)},e.Ga=function(t){if(this.s)if(this.s=null,1==this.G){if(!t){this.U=Math.floor(1e5*Math.random()),t=this.U++;var r=new w(this,this.j,t);let e=this.o;if(this.S&&(e?J(e=W(e),this.S):e=this.S),null!==this.m||this.O||(r.H=e,e=null),this.P)e:{for(var n=0,i=0;i<this.i.length;i++){var s=this.i[i];if("__data__"in s.map&&"string"==typeof(s=s.map.__data__)?s=s.length:s=void 0,void 0===s)break;if(4096<(n+=s)){n=i;break e}if(4096===n||i===this.i.length-1){n=i+1;break e}}n=1e3}else n=1e3;n=tr(this,r,n),I(i=b(this.I),"RID",t),I(i,"CVER",22),this.D&&I(i,"X-HTTP-Session-Id",this.D),er(this,i),e&&(this.O?n="headers="+encodeURIComponent(String(Vt(e)))+"&"+n:this.m&&Pt(i,this.m,e)),ut(this.h,r),this.Ua&&I(i,"TYPE","init"),this.P?(I(i,"$req",n),I(i,"SID","null"),r.T=!0,Ye(r,i,null)):Ye(r,i,n),this.G=2}}else 3==this.G&&(t?Zt(this,t):0==this.i.length||at(this.h)||Zt(this))},e.Fa=function(){var e;this.u=null,sr(this),this.ba&&!(this.M||null==this.g||this.R<=0)&&(e=2*this.R,this.j.info("BP detection timer enabled: "+e),this.A=qe(p(this.ab,this),e))},e.ab=function(){this.A&&(this.A=null,this.j.info("BP detection timeout reached."),this.j.info("Buffering proxy detected and switch to long-polling!"),this.F=!1,this.M=!0,O(10),Yt(this),sr(this))},e.Za=function(){null!=this.C&&(this.C=null,Yt(this),nr(this),O(19))},e.fb=function(e){e?(this.j.info("Successfully pinged google.com"),O(2)):(this.j.info("Failed to ping google.com"),O(1))},e.isActive=function(){return!!this.l&&this.l.isActive(this)},(e=dr.prototype).ua=function(){},e.ta=function(){},e.sa=function(){},e.ra=function(){},e.isActive=function(){return!0},e.Na=function(){},fr.prototype.g=function(e,t){return new C(e,t)},s(C,g),C.prototype.m=function(){this.g.l=this.j,this.v&&(this.g.J=!0),this.g.connect(this.l,this.h||void 0)},C.prototype.close=function(){Wt(this.g)},C.prototype.o=function(e){var t,r=this.g;"string"==typeof e?((t={}).__data__=e,e=t):this.u&&((t={}).__data__=xe(e),e=t),r.i.push(new it(r.Ya++,e)),3==r.G&&Xt(r)},C.prototype.N=function(){this.g.l=null,delete this.j,Wt(this.g),delete this.g,C.aa.N.call(this)},s(gr,Me),s(mr,Oe),s(D,dr),D.prototype.ua=function(){m(this.g,"a")},D.prototype.ta=function(e){m(this.g,new gr(e))},D.prototype.sa=function(e){m(this.g,new mr)},D.prototype.ra=function(){m(this.g,"b")},fr.prototype.createWebChannel=fr.prototype.g,C.prototype.send=C.prototype.o,C.prototype.open=C.prototype.m,Tr=function(){return new fr},Ir=Fe,br=v,_r={mb:0,pb:1,qb:2,Jb:3,Ob:4,Lb:5,Mb:6,Kb:7,Ib:8,Nb:9,PROXY:10,NOPROXY:11,Gb:12,Cb:13,Db:14,Bb:15,Eb:16,Fb:17,ib:18,hb:19,jb:20},je.NO_ERROR=0,je.TIMEOUT=8,je.HTTP_ERROR=6,wr=je,Ke.COMPLETE="complete",vr=Ke,(ke.EventType=Re).OPEN="a",Re.CLOSE="b",Re.ERROR="c",Re.MESSAGE="d",g.prototype.listen=g.prototype.K,yr=ke,x.prototype.listenOnce=x.prototype.L,x.prototype.getLastError=x.prototype.Ka,x.prototype.getLastErrorCode=x.prototype.Ba,x.prototype.getStatus=x.prototype.Z,x.prototype.getResponseJson=x.prototype.Oa,x.prototype.getResponseText=x.prototype.oa,x.prototype.send=x.prototype.ea,x.prototype.setWithCredentials=x.prototype.Ha,pr=x}).apply(void 0!==Er?Er:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});let ce="@firebase/firestore",de="4.7.11";class u{constructor(e){this.uid=e}isAuthenticated(){return null!=this.uid}toKey(){return this.isAuthenticated()?"uid:"+this.uid:"anonymous-user"}isEqual(e){return e.uid===this.uid}}u.UNAUTHENTICATED=new u(null),u.GOOGLE_CREDENTIALS=new u("google-credentials-uid"),u.FIRST_PARTY=new u("first-party-uid"),u.MOCK_USER=new u("mock-user");let fe="11.6.1",ge=new class{constructor(e){this.name=e,this._logLevel=se,this._logHandler=oe,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in c))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?ie[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,c.DEBUG,...e),this._logHandler(this,c.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,c.VERBOSE,...e),this._logHandler(this,c.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,c.INFO,...e),this._logHandler(this,c.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,c.WARN,...e),this._logHandler(this,c.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,c.ERROR,...e),this._logHandler(this,c.ERROR,...e)}}("@firebase/firestore");function me(){return ge.logLevel}function p(e,...t){var r;ge.logLevel<=c.DEBUG&&(r=t.map(ye),ge.debug(`Firestore (${fe}): `+e,...r))}function d(e,...t){var r;ge.logLevel<=c.ERROR&&(r=t.map(ye),ge.error(`Firestore (${fe}): `+e,...r))}function pe(e,...t){var r;ge.logLevel<=c.WARN&&(r=t.map(ye),ge.warn(`Firestore (${fe}): `+e,...r))}function ye(t){if("string"==typeof t)return t;try{return JSON.stringify(t)}catch(e){return t}}function E(e,t,r){let n="Unexpected state";"string"==typeof t?n=t:r=t,ve(e,n,r)}function ve(e,t,r){let n=`FIRESTORE (${fe}) INTERNAL ASSERTION FAILED: ${t} (ID: ${e.toString(16)})`;if(void 0!==r)try{n+=" CONTEXT: "+JSON.stringify(r)}catch(e){n+=" CONTEXT: "+r}throw d(n),new Error(n)}function y(e,t,r,n){let i="Unexpected state";"string"==typeof r?i=r:n=r,e||ve(t,i,n)}let b={OK:"ok",CANCELLED:"cancelled",UNKNOWN:"unknown",INVALID_ARGUMENT:"invalid-argument",DEADLINE_EXCEEDED:"deadline-exceeded",NOT_FOUND:"not-found",ALREADY_EXISTS:"already-exists",PERMISSION_DENIED:"permission-denied",UNAUTHENTICATED:"unauthenticated",RESOURCE_EXHAUSTED:"resource-exhausted",FAILED_PRECONDITION:"failed-precondition",ABORTED:"aborted",OUT_OF_RANGE:"out-of-range",UNIMPLEMENTED:"unimplemented",INTERNAL:"internal",UNAVAILABLE:"unavailable",DATA_LOSS:"data-loss"};class I extends X{constructor(e,t){super(e,t),this.code=e,this.message=t,this.toString=()=>`${this.name}: [code=${this.code}]: `+this.message}}class f{constructor(){this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}}class we{constructor(e,t){this.user=t,this.type="OAuth",this.headers=new Map,this.headers.set("Authorization","Bearer "+e)}}class _e{getToken(){return Promise.resolve(null)}invalidateToken(){}start(e,t){e.enqueueRetryable(()=>t(u.UNAUTHENTICATED))}shutdown(){}}class be{constructor(e){this.token=e,this.changeListener=null}getToken(){return Promise.resolve(this.token)}invalidateToken(){}start(e,t){this.changeListener=t,e.enqueueRetryable(()=>t(this.token.user))}shutdown(){this.changeListener=null}}class Ie{constructor(e){this.t=e,this.currentUser=u.UNAUTHENTICATED,this.i=0,this.forceRefresh=!1,this.auth=null}start(t,r){y(void 0===this.o,42304);let n=this.i,i=e=>this.i!==n?(n=this.i,r(e)):Promise.resolve(),s=new f,a=(this.o=()=>{this.i++,this.currentUser=this.u(),s.resolve(),s=new f,t.enqueueRetryable(()=>i(this.currentUser))},()=>{let e=s;t.enqueueRetryable(async()=>{await e.promise,await i(this.currentUser)})}),o=e=>{p("FirebaseAuthCredentialsProvider","Auth detected"),this.auth=e,this.o&&(this.auth.addAuthTokenListener(this.o),a())};this.t.onInit(e=>o(e)),setTimeout(()=>{var e;this.auth||((e=this.t.getImmediate({optional:!0}))?o(e):(p("FirebaseAuthCredentialsProvider","Auth not yet detected"),s.resolve(),s=new f))},0),a()}getToken(){let t=this.i,e=this.forceRefresh;return this.forceRefresh=!1,this.auth?this.auth.getToken(e).then(e=>this.i!==t?(p("FirebaseAuthCredentialsProvider","getToken aborted due to token change."),this.getToken()):e?(y("string"==typeof e.accessToken,31837,{l:e}),new we(e.accessToken,this.currentUser)):null):Promise.resolve(null)}invalidateToken(){this.forceRefresh=!0}shutdown(){this.auth&&this.o&&this.auth.removeAuthTokenListener(this.o),this.o=void 0}u(){var e=this.auth&&this.auth.getUid();return y(null===e||"string"==typeof e,2055,{h:e}),new u(e)}}class Te{constructor(e,t,r){this.P=e,this.T=t,this.I=r,this.type="FirstParty",this.user=u.FIRST_PARTY,this.A=new Map}R(){return this.I?this.I():null}get headers(){this.A.set("X-Goog-AuthUser",this.P);var e=this.R();return e&&this.A.set("Authorization",e),this.T&&this.A.set("X-Goog-Iam-Authorization-Token",this.T),this.A}}class Ee{constructor(e,t,r){this.P=e,this.T=t,this.I=r}getToken(){return Promise.resolve(new Te(this.P,this.T,this.I))}start(e,t){e.enqueueRetryable(()=>t(u.FIRST_PARTY))}shutdown(){}invalidateToken(){}}class Se{constructor(e){this.value=e,this.type="AppCheck",this.headers=new Map,e&&0<e.length&&this.headers.set("x-firebase-appcheck",this.value)}}class xe{constructor(e,t){this.V=t,this.forceRefresh=!1,this.appCheck=null,this.m=null,this.p=null,Vd._isFirebaseServerApp(e)&&e.settings.appCheckToken&&(this.p=e.settings.appCheckToken)}start(t,r){y(void 0===this.o,3512);let n=e=>{null!=e.error&&p("FirebaseAppCheckTokenProvider","Error getting App Check token; using placeholder token instead. Error: "+e.error.message);var t=e.token!==this.m;return this.m=e.token,p("FirebaseAppCheckTokenProvider",`Received ${t?"new":"existing"} token.`),t?r(e.token):Promise.resolve()},i=(this.o=e=>{t.enqueueRetryable(()=>n(e))},e=>{p("FirebaseAppCheckTokenProvider","AppCheck detected"),this.appCheck=e,this.o&&this.appCheck.addTokenListener(this.o)});this.V.onInit(e=>i(e)),setTimeout(()=>{var e;this.appCheck||((e=this.V.getImmediate({optional:!0}))?i(e):p("FirebaseAppCheckTokenProvider","AppCheck not yet detected"))},0)}getToken(){var e;return this.p?Promise.resolve(new Se(this.p)):(e=this.forceRefresh,this.forceRefresh=!1,this.appCheck?this.appCheck.getToken(e).then(e=>e?(y("string"==typeof e.token,44558,{tokenResult:e}),this.m=e.token,new Se(e.token)):null):Promise.resolve(null))}invalidateToken(){this.forceRefresh=!0}shutdown(){this.appCheck&&this.o&&this.appCheck.removeTokenListener(this.o),this.o=void 0}}function Ae(){return new TextEncoder}class Ce{static newId(){var t=62*Math.floor(256/62);let r="";for(;r.length<20;){var n=(t=>{var r="undefined"!=typeof self&&(self.crypto||self.msCrypto),n=new Uint8Array(t);if(r&&"function"==typeof r.getRandomValues)r.getRandomValues(n);else for(let e=0;e<t;e++)n[e]=Math.floor(256*Math.random());return n})(40);for(let e=0;e<n.length;++e)r.length<20&&n[e]<t&&(r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".charAt(n[e]%62))}return r}}function S(e,t){return e<t?-1:t<e?1:0}function De(e,t){let r=0;for(;r<e.length&&r<t.length;){var n,i=e.codePointAt(r),s=t.codePointAt(r);if(i!==s)return!(i<128&&s<128)&&0!==(n=((e,t)=>{for(let r=0;r<e.length&&r<t.length;++r)if(e[r]!==t[r])return S(e[r],t[r]);return S(e.length,t.length)})((n=Ae()).encode(Ne(e,r)),n.encode(Ne(t,r))))?n:S(i,s);r+=65535<i?2:1}return S(e.length,t.length)}function Ne(e,t){return 65535<e.codePointAt(t)?e.substring(t,t+2):e.substring(t,t+1)}function ke(e,r,n){return e.length===r.length&&e.every((e,t)=>n(e,r[t]))}function Re(e){return e+"\0"}let Me=-62135596800;class h{static now(){return h.fromMillis(Date.now())}static fromDate(e){return h.fromMillis(e.getTime())}static fromMillis(e){var t=Math.floor(e/1e3),r=Math.floor(1e6*(e-1e3*t));return new h(t,r)}constructor(e,t){if(this.seconds=e,(this.nanoseconds=t)<0)throw new I(b.INVALID_ARGUMENT,"Timestamp nanoseconds out of range: "+t);if(1e9<=t)throw new I(b.INVALID_ARGUMENT,"Timestamp nanoseconds out of range: "+t);if(e<Me)throw new I(b.INVALID_ARGUMENT,"Timestamp seconds out of range: "+e);if(253402300800<=e)throw new I(b.INVALID_ARGUMENT,"Timestamp seconds out of range: "+e)}toDate(){return new Date(this.toMillis())}toMillis(){return 1e3*this.seconds+this.nanoseconds/1e6}_compareTo(e){return this.seconds===e.seconds?S(this.nanoseconds,e.nanoseconds):S(this.seconds,e.seconds)}isEqual(e){return e.seconds===this.seconds&&e.nanoseconds===this.nanoseconds}toString(){return"Timestamp(seconds="+this.seconds+", nanoseconds="+this.nanoseconds+")"}toJSON(){return{seconds:this.seconds,nanoseconds:this.nanoseconds}}valueOf(){var e=this.seconds-Me;return String(e).padStart(12,"0")+"."+String(this.nanoseconds).padStart(9,"0")}}class g{static fromTimestamp(e){return new g(e)}static min(){return new g(new h(0,0))}static max(){return new g(new h(253402300799,999999999))}constructor(e){this.timestamp=e}compareTo(e){return this.timestamp._compareTo(e.timestamp)}isEqual(e){return this.timestamp.isEqual(e.timestamp)}toMicroseconds(){return 1e6*this.timestamp.seconds+this.timestamp.nanoseconds/1e3}toString(){return"SnapshotVersion("+this.timestamp.toString()+")"}toTimestamp(){return this.timestamp}}let Oe="__name__";class Le{constructor(e,t,r){void 0===t?t=0:t>e.length&&E(637,{offset:t,range:e.length}),void 0===r?r=e.length-t:r>e.length-t&&E(1746,{length:r,range:e.length-t}),this.segments=e,this.offset=t,this.len=r}get length(){return this.len}isEqual(e){return 0===Le.comparator(this,e)}child(e){let t=this.segments.slice(this.offset,this.limit());return e instanceof Le?e.forEach(e=>{t.push(e)}):t.push(e),this.construct(t)}limit(){return this.offset+this.length}popFirst(e){return this.construct(this.segments,this.offset+(e=void 0===e?1:e),this.length-e)}popLast(){return this.construct(this.segments,this.offset,this.length-1)}firstSegment(){return this.segments[this.offset]}lastSegment(){return this.get(this.length-1)}get(e){return this.segments[this.offset+e]}isEmpty(){return 0===this.length}isPrefixOf(e){if(e.length<this.length)return!1;for(let t=0;t<this.length;t++)if(this.get(t)!==e.get(t))return!1;return!0}isImmediateParentOf(e){if(this.length+1!==e.length)return!1;for(let t=0;t<this.length;t++)if(this.get(t)!==e.get(t))return!1;return!0}forEach(e){for(let t=this.offset,r=this.limit();t<r;t++)e(this.segments[t])}toArray(){return this.segments.slice(this.offset,this.limit())}static comparator(t,r){let e=Math.min(t.length,r.length);for(let n=0;n<e;n++){let e=Le.compareSegments(t.get(n),r.get(n));if(0!==e)return e}return S(t.length,r.length)}static compareSegments(e,t){var r=Le.isNumericId(e),n=Le.isNumericId(t);return r&&!n?-1:!r&&n?1:r&&n?Le.extractNumericId(e).compare(Le.extractNumericId(t)):De(e,t)}static isNumericId(e){return e.startsWith("__id")&&e.endsWith("__")}static extractNumericId(e){return le.fromString(e.substring(4,e.length-2))}}class T extends Le{construct(e,t,r){return new T(e,t,r)}canonicalString(){return this.toArray().join("/")}toString(){return this.canonicalString()}toUriEncodedString(){return this.toArray().map(encodeURIComponent).join("/")}static fromString(...e){var t,r=[];for(t of e){if(0<=t.indexOf("//"))throw new I(b.INVALID_ARGUMENT,`Invalid segment (${t}). Paths must not contain // in them.`);r.push(...t.split("/").filter(e=>0<e.length))}return new T(r)}static emptyPath(){return new T([])}}let Fe=/^[_a-zA-Z][_a-zA-Z0-9]*$/;class v extends Le{construct(e,t,r){return new v(e,t,r)}static isValidIdentifier(e){return Fe.test(e)}canonicalString(){return this.toArray().map(e=>(e=e.replace(/\\/g,"\\\\").replace(/`/g,"\\`"),e=v.isValidIdentifier(e)?e:"`"+e+"`")).join(".")}toString(){return this.canonicalString()}isKeyField(){return 1===this.length&&this.get(0)===Oe}static keyField(){return new v([Oe])}static fromServerFormat(t){let e=[],r="",n=0;var i=()=>{if(0===r.length)throw new I(b.INVALID_ARGUMENT,`Invalid field path (${t}). Paths must not be empty, begin with '.', end with '.', or contain '..'`);e.push(r),r=""};let s=!1;for(;n<t.length;){let e=t[n];if("\\"===e){if(n+1===t.length)throw new I(b.INVALID_ARGUMENT,"Path has trailing escape character: "+t);let e=t[n+1];if("\\"!==e&&"."!==e&&"`"!==e)throw new I(b.INVALID_ARGUMENT,"Path has invalid escape sequence: "+t);r+=e,n+=2}else"`"===e?s=!s:"."!==e||s?r+=e:i(),n++}if(i(),s)throw new I(b.INVALID_ARGUMENT,"Unterminated ` in path: "+t);return new v(e)}static emptyPath(){return new v([])}}class x{constructor(e){this.path=e}static fromPath(e){return new x(T.fromString(e))}static fromName(e){return new x(T.fromString(e).popFirst(5))}static empty(){return new x(T.emptyPath())}get collectionGroup(){return this.path.popLast().lastSegment()}hasCollectionId(e){return 2<=this.path.length&&this.path.get(this.path.length-2)===e}getCollectionGroup(){return this.path.get(this.path.length-2)}getCollectionPath(){return this.path.popLast()}isEqual(e){return null!==e&&0===T.comparator(this.path,e.path)}toString(){return this.path.toString()}static comparator(e,t){return T.comparator(e.path,t.path)}static isDocumentKey(e){return e.length%2==0}static fromSegments(e){return new x(new T(e.slice()))}}let Ve=-1;class Pe{constructor(e,t,r,n){this.indexId=e,this.collectionGroup=t,this.fields=r,this.indexState=n}}function Ue(e){return e.fields.find(e=>2===e.kind)}function Be(e){return e.fields.filter(e=>2!==e.kind)}Pe.UNKNOWN_ID=-1;class qe{constructor(e,t){this.fieldPath=e,this.kind=t}}class ze{constructor(e,t){this.sequenceNumber=e,this.offset=t}static empty(){return new ze(0,Ge.min())}}function je(e,t){var r=e.toTimestamp().seconds,n=e.toTimestamp().nanoseconds+1,r=g.fromTimestamp(1e9===n?new h(r+1,0):new h(r,n));return new Ge(r,x.empty(),t)}function Ke(e){return new Ge(e.readTime,e.key,Ve)}class Ge{constructor(e,t,r){this.readTime=e,this.documentKey=t,this.largestBatchId=r}static min(){return new Ge(g.min(),x.empty(),Ve)}static max(){return new Ge(g.max(),x.empty(),Ve)}}function Qe(e,t){var r=e.readTime.compareTo(t.readTime);return 0!==r||0!==(r=x.comparator(e.documentKey,t.documentKey))?r:S(e.largestBatchId,t.largestBatchId)}let $e="The current tab is not in the required state to perform this operation. It might be necessary to refresh the browser tab.";class He{constructor(){this.onCommittedListeners=[]}addOnCommittedListener(e){this.onCommittedListeners.push(e)}raiseOnCommittedEvent(){this.onCommittedListeners.forEach(e=>e())}}async function We(e){if(e.code!==b.FAILED_PRECONDITION||e.message!==$e)throw e;p("LocalStore","Unexpectedly lost primary lease")}class w{constructor(e){this.nextCallback=null,this.catchCallback=null,this.result=void 0,this.error=void 0,this.isDone=!1,this.callbackAttached=!1,e(e=>{this.isDone=!0,this.result=e,this.nextCallback&&this.nextCallback(e)},e=>{this.isDone=!0,this.error=e,this.catchCallback&&this.catchCallback(e)})}catch(e){return this.next(void 0,e)}next(n,i){return this.callbackAttached&&E(59440),this.callbackAttached=!0,this.isDone?this.error?this.wrapFailure(i,this.error):this.wrapSuccess(n,this.result):new w((t,r)=>{this.nextCallback=e=>{this.wrapSuccess(n,e).next(t,r)},this.catchCallback=e=>{this.wrapFailure(i,e).next(t,r)}})}toPromise(){return new Promise((e,t)=>{this.next(e,t)})}wrapUserFunction(e){try{var t=e();return t instanceof w?t:w.resolve(t)}catch(e){return w.reject(e)}}wrapSuccess(e,t){return e?this.wrapUserFunction(()=>e(t)):w.resolve(t)}wrapFailure(e,t){return e?this.wrapUserFunction(()=>e(t)):w.reject(t)}static resolve(r){return new w((e,t)=>{e(r)})}static reject(r){return new w((e,t)=>{t(r)})}static waitFor(e){return new w((t,r)=>{let n=0,i=0,s=!1;e.forEach(e=>{++n,e.next(()=>{++i,s&&i===n&&t()},e=>r(e))}),s=!0,i===n&&t()})}static or(e){let t=w.resolve(!1);for(let r of e)t=t.next(e=>e?w.resolve(e):r());return t}static forEach(e,r){let n=[];return e.forEach((e,t)=>{n.push(r.call(this,e,t))}),this.waitFor(n)}static mapArray(o,l){return new w((r,n)=>{let i=o.length,s=new Array(i),a=0;for(let e=0;e<i;e++){let t=e;l(o[t]).next(e=>{s[t]=e,++a===i&&r(s)},e=>n(e))}})}static doWhile(n,i){return new w((e,t)=>{let r=()=>{!0===n()?i().next(()=>{r()},t):e()};r()})}}let Ye="SimpleDb";class Je{static open(e,t,r,n){try{return new Je(t,e.transaction(n,r))}catch(e){throw new tt(t,e)}}constructor(r,e){this.action=r,this.transaction=e,this.aborted=!1,this.S=new f,this.transaction.oncomplete=()=>{this.S.resolve()},this.transaction.onabort=()=>{e.error?this.S.reject(new tt(r,e.error)):this.S.resolve()},this.transaction.onerror=e=>{var t=at(e.target.error);this.S.reject(new tt(r,t))}}get D(){return this.S.promise}abort(e){e&&this.S.reject(e),this.aborted||(p(Ye,"Aborting transaction:",e?e.message:"Client-initiated abort"),this.aborted=!0,this.transaction.abort())}v(){var e=this.transaction;this.aborted||"function"!=typeof e.commit||e.commit()}store(e){var t=this.transaction.objectStore(e);return new nt(t)}}class Xe{static delete(e){return p(Ye,"Removing database:",e),it(G().indexedDB.deleteDatabase(e)).toPromise()}static C(){var e,t,r;return!(!(()=>{try{return"object"==typeof indexedDB}catch(e){}})()||!Xe.F()&&(e=Y(),t=0<(t=Xe.M(e))&&t<10,r=0<(r=Ze(e))&&r<4.5,0<e.indexOf("MSIE ")||0<e.indexOf("Trident/")||0<e.indexOf("Edge/")||t||r))}static F(){var e;return"undefined"!=typeof process&&"YES"===(null==(e=process.__PRIVATE_env)?void 0:e.O)}static N(e,t){return e.store(t)}static M(e){var t=e.match(/i(?:phone|pad|pod) os ([\d_]+)/i),t=t?t[1].split("_").slice(0,2).join("."):"-1";return Number(t)}constructor(e,t,r){this.name=e,this.version=t,this.B=r,this.L=null,12.2===Xe.M(Y())&&d("Firestore persistence suffers from a bug in iOS 12.2 Safari that may cause your app to stop working. See https://stackoverflow.com/q/56496296/110915 for details and a potential workaround.")}async k(s){return this.db||(p(Ye,"Opening database:",this.name),this.db=await new Promise((r,n)=>{let i=indexedDB.open(this.name,this.version);i.onsuccess=e=>{var t=e.target.result;r(t)},i.onblocked=()=>{n(new tt(s,"Cannot upgrade IndexedDB schema while another tab is open. Close all tabs that access Firestore and reload this page to proceed."))},i.onerror=e=>{var t=e.target.error;"VersionError"===t.name?n(new I(b.FAILED_PRECONDITION,"A newer version of the Firestore SDK was previously used and so the persisted data is not compatible with the version of the SDK you are now using. The SDK will operate with persistence disabled. If you need persistence, please re-upgrade to a newer version of the SDK or else clear the persisted IndexedDB data for your app to start fresh.")):"InvalidStateError"===t.name?n(new I(b.FAILED_PRECONDITION,"Unable to open an IndexedDB connection. This could be due to running in a private browsing session on a browser whose private browsing sessions do not support IndexedDB: "+t)):n(new tt(s,t))},i.onupgradeneeded=e=>{p(Ye,'Database "'+this.name+'" requires upgrade from version:',e.oldVersion);var t=e.target.result;if(null!==this.L&&this.L!==e.oldVersion)throw new Error(`refusing to open IndexedDB database due to potential corruption of the IndexedDB database data; this corruption could be caused by clicking the "clear site data" button in a web browser; try reloading the web page to re-initialize the IndexedDB database: lastClosedDbVersion=${this.L}, event.oldVersion=${e.oldVersion}, event.newVersion=${e.newVersion}, db.version=`+t.version);this.B.q(t,i.transaction,e.oldVersion,this.version).next(()=>{p(Ye,"Database upgrade to version "+this.version+" complete")})}}),this.db.addEventListener("close",e=>{var t=e.target;this.L=t.version},{passive:!0})),this.$&&(this.db.onversionchange=e=>this.$(e)),this.db}U(t){this.$=t,this.db&&(this.db.onversionchange=e=>t(e))}async runTransaction(r,e,n,i){var s="readonly"===e;let a=0;for(;;){++a;try{this.db=await this.k(r);let t=Je.open(this.db,r,s?"readonly":"readwrite",n),e=i(t).next(e=>(t.v(),e)).catch(e=>(t.abort(e),w.reject(e))).toPromise();return e.catch(()=>{}),await t.D,e}catch(r){let e=r,t="FirebaseError"!==e.name&&a<3;if(p(Ye,"Transaction failed with error:",e.message,"Retrying:",t),this.close(),!t)return Promise.reject(e)}}}close(){this.db&&this.db.close(),this.db=void 0}}function Ze(e){var t=e.match(/Android ([\d.]+)/i),t=t?t[1].split(".").slice(0,2).join("."):"-1";return Number(t)}class et{constructor(e){this.K=e,this.W=!1,this.G=null}get isDone(){return this.W}get j(){return this.G}set cursor(e){this.K=e}done(){this.W=!0}H(e){this.G=e}delete(){return it(this.K.delete())}}class tt extends I{constructor(e,t){super(b.UNAVAILABLE,`IndexedDB transaction '${e}' failed: `+t),this.name="IndexedDbTransactionError"}}function rt(e){return"IndexedDbTransactionError"===e.name}class nt{constructor(e){this.store=e}put(e,t){let r;return it(r=void 0!==t?(p(Ye,"PUT",this.store.name,e,t),this.store.put(t,e)):(p(Ye,"PUT",this.store.name,"<auto-key>",e),this.store.put(e)))}add(e){return p(Ye,"ADD",this.store.name,e,e),it(this.store.add(e))}get(t){return it(this.store.get(t)).next(e=>(void 0===e&&(e=null),p(Ye,"GET",this.store.name,t,e),e))}delete(e){return p(Ye,"DELETE",this.store.name,e),it(this.store.delete(e))}count(){return p(Ye,"COUNT",this.store.name),it(this.store.count())}J(e,t){var n=this.options(e,t),r=n.index?this.store.index(n.index):this.store;if("function"==typeof r.getAll){let e=r.getAll(n.range);return new w((t,r)=>{e.onerror=e=>{r(e.target.error)},e.onsuccess=e=>{t(e.target.result)}})}{let e=this.cursor(n),r=[];return this.Y(e,(e,t)=>{r.push(t)}).next(()=>r)}}Z(e,t){let n=this.store.getAll(e,null===t?void 0:t);return new w((t,r)=>{n.onerror=e=>{r(e.target.error)},n.onsuccess=e=>{t(e.target.result)}})}X(e,t){p(Ye,"DELETE ALL",this.store.name);var r=this.options(e,t),r=(r.ee=!1,this.cursor(r));return this.Y(r,(e,t,r)=>r.delete())}te(e,t){let r;t?r=e:(r={},t=e);var n=this.cursor(r);return this.Y(n,t)}ne(i){let e=this.cursor({});return new w((r,n)=>{e.onerror=e=>{var t=at(e.target.error);n(t)},e.onsuccess=e=>{let t=e.target.result;t?i(t.primaryKey,t.value).next(e=>{e?t.continue():r()}):r()}})}Y(e,s){let a=[];return new w((i,t)=>{e.onerror=e=>{t(e.target.error)},e.onsuccess=e=>{var n=e.target.result;if(n){let t=new et(n),r=s(n.primaryKey,n.value,t);if(r instanceof w){let e=r.catch(e=>(t.done(),w.reject(e)));a.push(e)}t.isDone?i():null===t.j?n.continue():n.continue(t.j)}else i()}}).next(()=>w.waitFor(a))}options(e,t){let r;return void 0!==e&&("string"==typeof e?r=e:t=e),{index:r,range:t}}cursor(e){let t="next";var r;return e.reverse&&(t="prev"),e.index?(r=this.store.index(e.index),e.ee?r.openKeyCursor(e.range,t):r.openCursor(e.range,t)):this.store.openCursor(e.range,t)}}function it(e){return new w((r,n)=>{e.onsuccess=e=>{var t=e.target.result;r(t)},e.onerror=e=>{var t=at(e.target.error);n(t)}})}let st=!1;function at(e){let t=Xe.M(Y());if(12.2<=t&&t<13){let t="An internal error was encountered in the Indexed Database server";if(0<=e.message.indexOf(t)){let e=new I("internal",`IOS_INDEXEDDB_BUG1: IndexedDb has thrown '${t}'. This is likely due to an unavoidable bug in iOS. See https://stackoverflow.com/q/56496296/110915 for details and a potential workaround.`);return st||(st=!0,setTimeout(()=>{throw e},0)),e}}return e}let ot="IndexBackfiller";class lt{constructor(e,t){this.asyncQueue=e,this.re=t,this.task=null}start(){this.ie(15e3)}stop(){this.task&&(this.task.cancel(),this.task=null)}get started(){return null!==this.task}ie(e){p(ot,`Scheduled in ${e}ms`),this.task=this.asyncQueue.enqueueAfterDelay("index_backfill",e,async()=>{this.task=null;try{var e=await this.re.se();p(ot,"Documents written: "+e)}catch(e){rt(e)?p(ot,"Ignoring IndexedDB error during index backfill: ",e):await We(e)}await this.ie(6e4)})}}class ut{constructor(e,t){this.localStore=e,this.persistence=t}async se(t=50){return this.persistence.runTransaction("Backfill Indexes","readwrite-primary",e=>this.oe(e,t))}oe(e,t){let r=new Set,n=t,i=!0;return w.doWhile(()=>!0===i&&0<n,()=>this.localStore.indexManager.getNextCollectionGroupToUpdate(e).next(t=>{if(null!==t&&!r.has(t))return p(ot,"Processing collection: "+t),this._e(e,t,n).next(e=>{n-=e,r.add(t)});i=!1})).next(()=>t-n)}_e(n,i,e){return this.localStore.indexManager.getMinOffsetFromCollectionGroup(n,i).next(r=>this.localStore.localDocuments.getNextDocuments(n,i,r,e).next(e=>{let t=e.changes;return this.localStore.indexManager.updateIndexEntries(n,t).next(()=>this.ae(r,e)).next(e=>(p(ot,"Updating offset: "+e),this.localStore.indexManager.updateCollectionGroup(n,i,e))).next(()=>t.size)}))}ae(e,t){let n=e;return t.changes.forEach((e,t)=>{var r=Ke(t);0<Qe(r,n)&&(n=r)}),new Ge(n.readTime,n.documentKey,Math.max(t.batchId,e.largestBatchId))}}class ht{constructor(e,t){this.previousValue=e,t&&(t.sequenceNumberHandler=e=>this.ue(e),this.ce=e=>t.writeSequenceNumber(e))}ue(e){return this.previousValue=Math.max(e,this.previousValue),this.previousValue}next(){var e=++this.previousValue;return this.ce&&this.ce(e),e}}let ct=ht.le=-1;function dt(e){return null==e}function ft(e){return 0===e&&1/e==-1/0}function gt(e){return"number"==typeof e&&Number.isInteger(e)&&!ft(e)&&e<=Number.MAX_SAFE_INTEGER&&e>=Number.MIN_SAFE_INTEGER}let mt="";function o(e){let t="";for(let r=0;r<e.length;r++)0<t.length&&(t=pt(t)),t=((t,e)=>{let r=e,n=t.length;for(let i=0;i<n;i++){let e=t.charAt(i);switch(e){case"\0":r+="";break;case mt:r+="";break;default:r+=e}}return r})(e.get(r),t);return pt(t)}function pt(e){return e+mt+""}function yt(r){let e=r.length;if(y(2<=e,64408,{path:r}),2===e)return y(r.charAt(0)===mt&&""===r.charAt(1),56145,{path:r}),T.emptyPath();var __PRIVATE_lastReasonableEscapeIndex=e-2,n=[];let i="";for(let a=0;a<e;){let t=r.indexOf(mt,a);switch((t<0||t>__PRIVATE_lastReasonableEscapeIndex)&&E(50515,{path:r}),r.charAt(t+1)){case"":var s=r.substring(a,t);let e;0===i.length?e=s:(i+=s,e=i,i=""),n.push(e);break;case"":i=i+r.substring(a,t)+"\0";break;case"":i+=r.substring(a,t+1);break;default:E(61167,{path:r})}a=t+2}return new T(n)}let vt="remoteDocuments",wt="owner",_t="owner",bt="mutationQueues",It="mutations",Tt="batchId",Et="userMutationsIndex",St=["userId","batchId"];function xt(e,t){return[e,o(t)]}function At(e,t,r){return[e,o(t),r]}let Ct={},Dt="documentMutations",Nt="remoteDocumentsV14",kt=["prefixPath","collectionGroup","readTime","documentId"],Rt="documentKeyIndex",Mt=["prefixPath","collectionGroup","documentId"],Ot="collectionGroupIndex",Lt=["collectionGroup","readTime","prefixPath","documentId"],Ft="remoteDocumentGlobal",Vt="remoteDocumentGlobalKey",Pt="targets",Ut="queryTargetsIndex",Bt=["canonicalId","targetId"],qt="targetDocuments",zt=["targetId","path"],jt="documentTargetsIndex",Kt=["path","targetId"],Gt="targetGlobalKey",Qt="targetGlobal",$t="collectionParents",Ht=["collectionId","parent"],Wt="clientMetadata",Yt="bundles",Jt="namedQueries",Xt="indexConfiguration",Zt="collectionGroupIndex",er="indexState",tr=["indexId","uid"],rr="sequenceNumberIndex",nr=["uid","sequenceNumber"],ir="indexEntries",sr=["indexId","uid","arrayValue","directionalValue","orderedDocumentKey","documentKey"],ar="documentKeyIndex",or=["indexId","uid","orderedDocumentKey"],lr="documentOverlays",ur=["userId","collectionPath","documentId"],hr="collectionPathOverlayIndex",cr=["userId","collectionPath","largestBatchId"],dr="collectionGroupOverlayIndex",fr=["userId","collectionGroup","largestBatchId"],gr="globals",mr=[bt,It,Dt,vt,Pt,wt,Qt,qt,Wt,Ft,$t,Yt,Jt],Sr=[...mr,lr],xr=[bt,It,Dt,Nt,Pt,wt,Qt,qt,Wt,Ft,$t,Yt,Jt,lr],Ar=xr,Cr=[...Ar,Xt,er,ir],Dr=Cr,Nr=[...Cr,gr];class kr extends He{constructor(e,t){super(),this.he=e,this.currentSequenceNumber=t}}function r(e,t){var r=e;return Xe.N(r.he,t)}function Rr(e){let t=0;for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t++;return t}function Mr(e,t){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t(r,e[r])}function Or(e){for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t))return!1;return!0}class A{constructor(e,t){this.comparator=e,this.root=t||Fr.EMPTY}insert(e,t){return new A(this.comparator,this.root.insert(e,t,this.comparator).copy(null,null,Fr.BLACK,null,null))}remove(e){return new A(this.comparator,this.root.remove(e,this.comparator).copy(null,null,Fr.BLACK,null,null))}get(e){let t=this.root;for(;!t.isEmpty();){var r=this.comparator(e,t.key);if(0===r)return t.value;r<0?t=t.left:0<r&&(t=t.right)}return null}indexOf(e){let t=0,r=this.root;for(;!r.isEmpty();){var n=this.comparator(e,r.key);if(0===n)return t+r.left.size;r=n<0?r.left:(t+=r.left.size+1,r.right)}return-1}isEmpty(){return this.root.isEmpty()}get size(){return this.root.size}minKey(){return this.root.minKey()}maxKey(){return this.root.maxKey()}inorderTraversal(e){return this.root.inorderTraversal(e)}forEach(r){this.inorderTraversal((e,t)=>(r(e,t),!1))}toString(){let r=[];return this.inorderTraversal((e,t)=>(r.push(e+":"+t),!1)),`{${r.join(", ")}}`}reverseTraversal(e){return this.root.reverseTraversal(e)}getIterator(){return new Lr(this.root,null,this.comparator,!1)}getIteratorFrom(e){return new Lr(this.root,e,this.comparator,!1)}getReverseIterator(){return new Lr(this.root,null,this.comparator,!0)}getReverseIteratorFrom(e){return new Lr(this.root,e,this.comparator,!0)}}class Lr{constructor(e,t,r,n){this.isReverse=n,this.nodeStack=[];let i=1;for(;!e.isEmpty();)if(i=t?r(e.key,t):1,t&&n&&(i*=-1),i<0)e=this.isReverse?e.left:e.right;else{if(0===i){this.nodeStack.push(e);break}this.nodeStack.push(e),e=this.isReverse?e.right:e.left}}getNext(){let e=this.nodeStack.pop();var t={key:e.key,value:e.value};if(this.isReverse)for(e=e.left;!e.isEmpty();)this.nodeStack.push(e),e=e.right;else for(e=e.right;!e.isEmpty();)this.nodeStack.push(e),e=e.left;return t}hasNext(){return 0<this.nodeStack.length}peek(){var e;return 0===this.nodeStack.length?null:{key:(e=this.nodeStack[this.nodeStack.length-1]).key,value:e.value}}}class Fr{constructor(e,t,r,n,i){this.key=e,this.value=t,this.color=null!=r?r:Fr.RED,this.left=null!=n?n:Fr.EMPTY,this.right=null!=i?i:Fr.EMPTY,this.size=this.left.size+1+this.right.size}copy(e,t,r,n,i){return new Fr(null!=e?e:this.key,null!=t?t:this.value,null!=r?r:this.color,null!=n?n:this.left,null!=i?i:this.right)}isEmpty(){return!1}inorderTraversal(e){return this.left.inorderTraversal(e)||e(this.key,this.value)||this.right.inorderTraversal(e)}reverseTraversal(e){return this.right.reverseTraversal(e)||e(this.key,this.value)||this.left.reverseTraversal(e)}min(){return this.left.isEmpty()?this:this.left.min()}minKey(){return this.min().key}maxKey(){return this.right.isEmpty()?this.key:this.right.maxKey()}insert(e,t,r){var n=this,i=r(e,n.key);return(n=i<0?n.copy(null,null,null,n.left.insert(e,t,r),null):0===i?n.copy(null,t,null,null,null):n.copy(null,null,null,null,n.right.insert(e,t,r))).fixUp()}removeMin(){if(this.left.isEmpty())return Fr.EMPTY;let e=this;return(e=(e=e.left.isRed()||e.left.left.isRed()?e:e.moveRedLeft()).copy(null,null,null,e.left.removeMin(),null)).fixUp()}remove(e,t){let r,n=this;if(t(e,n.key)<0)n=(n=n.left.isEmpty()||n.left.isRed()||n.left.left.isRed()?n:n.moveRedLeft()).copy(null,null,null,n.left.remove(e,t),null);else{if(0===t(e,(n=(n=n.left.isRed()?n.rotateRight():n).right.isEmpty()||n.right.isRed()||n.right.left.isRed()?n:n.moveRedRight()).key)){if(n.right.isEmpty())return Fr.EMPTY;r=n.right.min(),n=n.copy(r.key,r.value,null,null,n.right.removeMin())}n=n.copy(null,null,null,null,n.right.remove(e,t))}return n.fixUp()}isRed(){return this.color}fixUp(){let e=this;return e=(e=(e=e.right.isRed()&&!e.left.isRed()?e.rotateLeft():e).left.isRed()&&e.left.left.isRed()?e.rotateRight():e).left.isRed()&&e.right.isRed()?e.colorFlip():e}moveRedLeft(){let e=this.colorFlip();return e=e.right.left.isRed()?(e=(e=e.copy(null,null,null,null,e.right.rotateRight())).rotateLeft()).colorFlip():e}moveRedRight(){let e=this.colorFlip();return e=e.left.left.isRed()?(e=e.rotateRight()).colorFlip():e}rotateLeft(){var e=this.copy(null,null,Fr.RED,null,this.right.left);return this.right.copy(null,null,this.color,e,null)}rotateRight(){var e=this.copy(null,null,Fr.RED,this.left.right,null);return this.left.copy(null,null,this.color,null,e)}colorFlip(){var e=this.left.copy(null,null,!this.left.color,null,null),t=this.right.copy(null,null,!this.right.color,null,null);return this.copy(null,null,!this.color,e,t)}checkMaxDepth(){var e=this.check();return Math.pow(2,e)<=this.size+1}check(){if(this.isRed()&&this.left.isRed())throw E(43730,{key:this.key,value:this.value});if(this.right.isRed())throw E(14113,{key:this.key,value:this.value});var e=this.left.check();if(e!==this.right.check())throw E(27949);return e+(this.isRed()?0:1)}}Fr.EMPTY=null,Fr.RED=!0,Fr.BLACK=!1,Fr.EMPTY=new class{constructor(){this.size=0}get key(){throw E(57766)}get value(){throw E(16141)}get color(){throw E(16727)}get left(){throw E(29726)}get right(){throw E(36894)}copy(e,t,r,n,i){return this}insert(e,t,r){return new Fr(e,t)}remove(e,t){return this}isEmpty(){return!0}inorderTraversal(e){return!1}reverseTraversal(e){return!1}minKey(){return null}maxKey(){return null}isRed(){return!1}checkMaxDepth(){return!0}check(){return 0}};class C{constructor(e){this.comparator=e,this.data=new A(this.comparator)}has(e){return null!==this.data.get(e)}first(){return this.data.minKey()}last(){return this.data.maxKey()}get size(){return this.data.size}indexOf(e){return this.data.indexOf(e)}forEach(r){this.data.inorderTraversal((e,t)=>(r(e),!1))}forEachInRange(e,t){for(var r=this.data.getIteratorFrom(e[0]);r.hasNext();){var n=r.getNext();if(0<=this.comparator(n.key,e[1]))return;t(n.key)}}forEachWhile(e,t){for(var r=void 0!==t?this.data.getIteratorFrom(t):this.data.getIterator();r.hasNext();)if(!e(r.getNext().key))return}firstAfterOrEqual(e){var t=this.data.getIteratorFrom(e);return t.hasNext()?t.getNext().key:null}getIterator(){return new Vr(this.data.getIterator())}getIteratorFrom(e){return new Vr(this.data.getIteratorFrom(e))}add(e){return this.copy(this.data.remove(e).insert(e,!0))}delete(e){return this.has(e)?this.copy(this.data.remove(e)):this}isEmpty(){return this.data.isEmpty()}unionWith(e){let t=this;return t.size<e.size&&(t=e,e=this),e.forEach(e=>{t=t.add(e)}),t}isEqual(e){if(!(e instanceof C))return!1;if(this.size!==e.size)return!1;for(var r=this.data.getIterator(),n=e.data.getIterator();r.hasNext();){let e=r.getNext().key,t=n.getNext().key;if(0!==this.comparator(e,t))return!1}return!0}toArray(){let t=[];return this.forEach(e=>{t.push(e)}),t}toString(){let t=[];return this.forEach(e=>t.push(e)),"SortedSet("+t.toString()+")"}copy(e){var t=new C(this.comparator);return t.data=e,t}}class Vr{constructor(e){this.iter=e}getNext(){return this.iter.getNext().key}hasNext(){return this.iter.hasNext()}}function Pr(e){return e.hasNext()?e.getNext():void 0}class Ur{constructor(e){(this.fields=e).sort(v.comparator)}static empty(){return new Ur([])}unionWith(e){let t=new C(v.comparator);for(let e of this.fields)t=t.add(e);for(var r of e)t=t.add(r);return new Ur(t.toArray())}covers(e){for(var t of this.fields)if(t.isPrefixOf(e))return!0;return!1}isEqual(e){return ke(this.fields,e.fields,(e,t)=>e.isEqual(t))}}class Br extends Error{constructor(){super(...arguments),this.name="Base64DecodeError"}}class D{constructor(e){this.binaryString=e}static fromBase64String(e){var t=(e=>{try{return atob(e)}catch(e){throw"undefined"!=typeof DOMException&&e instanceof DOMException?new Br("Invalid base64 string: "+e):e}})(e);return new D(t)}static fromUint8Array(e){var t=(e=>{let t="";for(let r=0;r<e.length;++r)t+=String.fromCharCode(e[r]);return t})(e);return new D(t)}[Symbol.iterator](){let e=0;return{next:()=>e<this.binaryString.length?{value:this.binaryString.charCodeAt(e++),done:!1}:{value:void 0,done:!0}}}toBase64(){return e=this.binaryString,btoa(e);var e}toUint8Array(){var e=this.binaryString,t=new Uint8Array(e.length);for(let r=0;r<e.length;r++)t[r]=e.charCodeAt(r);return t}approximateByteSize(){return 2*this.binaryString.length}compareTo(e){return S(this.binaryString,e.binaryString)}isEqual(e){return this.binaryString===e.binaryString}}D.EMPTY_BYTE_STRING=new D("");let qr=new RegExp(/^\d{4}-\d\d-\d\dT\d\d:\d\d:\d\d(?:\.(\d+))?Z$/);function zr(t){if(y(!!t,39018),"string"!=typeof t)return{seconds:N(t.seconds),nanos:N(t.nanos)};{let e=0;var r=qr.exec(t),r=(y(!!r,46558,{timestamp:t}),r[1]&&(r=((r=r[1])+"000000000").substr(0,9),e=Number(r)),new Date(t));return{seconds:Math.floor(r.getTime()/1e3),nanos:e}}}function N(e){return"number"==typeof e?e:"string"==typeof e?Number(e):0}function jr(e){return"string"==typeof e?D.fromBase64String(e):D.fromUint8Array(e)}let Kr="server_timestamp",Gr="__type__",Qr="__previous_value__",$r="__local_write_time__";function Hr(e){var t;return(null==(t=((null==(t=null==e?void 0:e.mapValue)?void 0:t.fields)||{})[Gr])?void 0:t.stringValue)===Kr}function Wr(e){var t=e.mapValue.fields[Qr];return Hr(t)?Wr(t):t}function Yr(e){var t=zr(e.mapValue.fields[$r].timestampValue);return new h(t.seconds,t.nanos)}class Jr{constructor(e,t,r,n,i,s,a,o,l){this.databaseId=e,this.appId=t,this.persistenceKey=r,this.host=n,this.ssl=i,this.forceLongPolling=s,this.autoDetectLongPolling=a,this.longPollingOptions=o,this.useFetchStreams=l}}let Xr="(default)";class Zr{constructor(e,t){this.projectId=e,this.database=t||Xr}static empty(){return new Zr("","")}get isDefaultDatabase(){return this.database===Xr}isEqual(e){return e instanceof Zr&&e.projectId===this.projectId&&e.database===this.database}}let en="__type__",tn="__max__",rn={mapValue:{fields:{__type__:{stringValue:tn}}}},nn="__vector__",sn="value",an={nullValue:"NULL_VALUE"};function on(e){return"nullValue"in e?0:"booleanValue"in e?1:"integerValue"in e||"doubleValue"in e?2:"timestampValue"in e?3:"stringValue"in e?5:"bytesValue"in e?6:"referenceValue"in e?7:"geoPointValue"in e?8:"arrayValue"in e?9:"mapValue"in e?Hr(e)?4:In(e)?9007199254740991:_n(e)?10:11:E(28295,{value:e})}function ln(t,r){if(t===r)return!0;var n,i,s,a,o,e=on(t);if(e!==on(r))return!1;switch(e){case 0:case 9007199254740991:return!0;case 1:return t.booleanValue===r.booleanValue;case 4:return Yr(t).isEqual(Yr(r));case 3:return s=r,"string"==typeof(i=t).timestampValue&&"string"==typeof s.timestampValue&&i.timestampValue.length===s.timestampValue.length?i.timestampValue===s.timestampValue:(a=zr(i.timestampValue),o=zr(s.timestampValue),a.seconds===o.seconds&&a.nanos===o.nanos);case 5:return t.stringValue===r.stringValue;case 6:return i=r,jr(t.bytesValue).isEqual(jr(i.bytesValue));case 7:return t.referenceValue===r.referenceValue;case 8:return s=r,N((n=t).geoPointValue.latitude)===N(s.geoPointValue.latitude)&&N(n.geoPointValue.longitude)===N(s.geoPointValue.longitude);case 2:return n=r,"integerValue"in(u=t)&&"integerValue"in n?N(u.integerValue)===N(n.integerValue):"doubleValue"in u&&"doubleValue"in n&&((a=N(u.doubleValue))===(o=N(n.doubleValue))?ft(a)===ft(o):isNaN(a)&&isNaN(o));case 9:return ke(t.arrayValue.values||[],r.arrayValue.values||[],ln);case 10:case 11:var l=t,u=r,h=l.mapValue.fields||{},c=u.mapValue.fields||{};if(Rr(h)!==Rr(c))return!1;for(let e in h)if(h.hasOwnProperty(e)&&(void 0===c[e]||!ln(h[e],c[e])))return!1;return!0;default:return E(52216,{left:t})}}function un(e,t){return void 0!==(e.values||[]).find(e=>ln(e,t))}function hn(e,n){if(e===n)return 0;var i,s,a,o,l,u,h,c,d=on(e),t=on(n);if(d!==t)return S(d,t);switch(d){case 0:case 9007199254740991:return 0;case 1:return S(e.booleanValue,n.booleanValue);case 2:return u=n,h=N((l=e).integerValue||l.doubleValue),c=N(u.integerValue||u.doubleValue),h<c?-1:c<h?1:h===c?0:isNaN(h)?isNaN(c)?0:-1:1;case 3:return cn(e.timestampValue,n.timestampValue);case 4:return cn(Yr(e),Yr(n));case 5:return De(e.stringValue,n.stringValue);case 6:return l=e.bytesValue,u=n.bytesValue,h=jr(l),c=jr(u),h.compareTo(c);case 7:var f=e.referenceValue,g=n.referenceValue,m=f.split("/"),p=g.split("/");for(let t=0;t<m.length&&t<p.length;t++){let e=S(m[t],p[t]);if(0!==e)return e}return S(m.length,p.length);case 8:return f=e.geoPointValue,g=n.geoPointValue,0!==(o=S(N(f.latitude),N(g.latitude)))?o:S(N(f.longitude),N(g.longitude));case 9:return dn(e.arrayValue,n.arrayValue);case 10:return y=e.mapValue,i=n.mapValue,o=y.fields||{},s=i.fields||{},o=null==(o=o[sn])?void 0:o.arrayValue,s=null==(s=s[sn])?void 0:s.arrayValue,0!==(a=S((null==(a=null==o?void 0:o.values)?void 0:a.length)||0,(null==(a=null==s?void 0:s.values)?void 0:a.length)||0))?a:dn(o,s);case 11:var y=e.mapValue,v=n.mapValue;if(y===rn.mapValue&&v===rn.mapValue)return 0;if(y===rn.mapValue)return 1;if(v===rn.mapValue)return-1;var w=y.fields||{},_=Object.keys(w),b=v.fields||{},I=Object.keys(b);_.sort(),I.sort();for(let r=0;r<_.length&&r<I.length;++r){let e=De(_[r],I[r]);if(0!==e)return e;var T=hn(w[_[r]],b[I[r]]);if(0!==T)return T}return S(_.length,I.length);default:throw E(23264,{Pe:d})}}function cn(e,t){var r,n,i;return"string"==typeof e&&"string"==typeof t&&e.length===t.length?S(e,t):(r=zr(e),n=zr(t),0!==(i=S(r.seconds,n.seconds))?i:S(r.nanos,n.nanos))}function dn(e,t){var r=e.values||[],n=t.values||[];for(let i=0;i<r.length&&i<n.length;++i){let e=hn(r[i],n[i]);if(e)return e}return S(r.length,n.length)}function fn(e){return function s(e){return"nullValue"in e?"null":"booleanValue"in e?""+e.booleanValue:"integerValue"in e?""+e.integerValue:"doubleValue"in e?""+e.doubleValue:"timestampValue"in e?(e=>{let t=zr(e);return`time(${t.seconds},${t.nanos})`})(e.timestampValue):"stringValue"in e?e.stringValue:"bytesValue"in e?(e=>jr(e).toBase64())(e.bytesValue):"referenceValue"in e?(e=>x.fromName(e).toString())(e.referenceValue):"geoPointValue"in e?(e=>`geo(${e.latitude},${e.longitude})`)(e.geoPointValue):"arrayValue"in e?(e=>{let t="[",r=!0;for(var n of e.values||[])r?r=!1:t+=",",t+=s(n);return t+"]"})(e.arrayValue):"mapValue"in e?(e=>{let t=Object.keys(e.fields||{}).sort(),r="{",n=!0;for(var i of t)n?n=!1:r+=",",r+=i+":"+s(e.fields[i]);return r+"}"})(e.mapValue):E(61005,{value:e})}(e)}function gn(e,t){return{referenceValue:`projects/${e.projectId}/databases/${e.database}/documents/`+t.path.canonicalString()}}function mn(e){return!!e&&"integerValue"in e}function pn(e){return!!e&&"arrayValue"in e}function yn(e){return e&&"nullValue"in e}function vn(e){return e&&"doubleValue"in e&&isNaN(Number(e.doubleValue))}function wn(e){return e&&"mapValue"in e}function _n(e){var t;return(null==(t=((null==(t=null==e?void 0:e.mapValue)?void 0:t.fields)||{})[en])?void 0:t.stringValue)===nn}function bn(t){if(t.geoPointValue)return{geoPointValue:Object.assign({},t.geoPointValue)};if(t.timestampValue&&"object"==typeof t.timestampValue)return{timestampValue:Object.assign({},t.timestampValue)};if(t.mapValue){let r={mapValue:{fields:{}}};return Mr(t.mapValue.fields,(e,t)=>r.mapValue.fields[e]=bn(t)),r}if(t.arrayValue){var r={arrayValue:{values:[]}};for(let e=0;e<(t.arrayValue.values||[]).length;++e)r.arrayValue.values[e]=bn(t.arrayValue.values[e]);return r}return Object.assign({},t)}function In(e){return(((e.mapValue||{}).fields||{}).__type__||{}).stringValue===tn}let Tn={mapValue:{fields:{[en]:{stringValue:nn},[sn]:{arrayValue:{}}}}};function En(e,t){var r=hn(e.value,t.value);return 0!==r?r:e.inclusive&&!t.inclusive?-1:!e.inclusive&&t.inclusive?1:0}function Sn(e,t){var r=hn(e.value,t.value);return 0!==r?r:e.inclusive&&!t.inclusive?1:!e.inclusive&&t.inclusive?-1:0}class xn{constructor(e){this.value=e}static empty(){return new xn({mapValue:{}})}field(r){if(r.isEmpty())return this.value;{let e=this.value;for(let t=0;t<r.length-1;++t)if(!wn(e=(e.mapValue.fields||{})[r.get(t)]))return null;return(e=(e.mapValue.fields||{})[r.lastSegment()])||null}}set(e,t){this.getFieldsMap(e.popLast())[e.lastSegment()]=bn(t)}setAll(e){let r=v.emptyPath(),n={},i=[];e.forEach((e,t)=>{if(!r.isImmediateParentOf(t)){let e=this.getFieldsMap(r);this.applyChanges(e,n,i),n={},i=[],r=t.popLast()}e?n[t.lastSegment()]=bn(e):i.push(t.lastSegment())});var t=this.getFieldsMap(r);this.applyChanges(t,n,i)}delete(e){var t=this.field(e.popLast());wn(t)&&t.mapValue.fields&&delete t.mapValue.fields[e.lastSegment()]}isEqual(e){return ln(this.value,e.value)}getFieldsMap(t){let r=this.value;r.mapValue.fields||(r.mapValue={fields:{}});for(let n=0;n<t.length;++n){let e=r.mapValue.fields[t.get(n)];wn(e)&&e.mapValue.fields||(e={mapValue:{fields:{}}},r.mapValue.fields[t.get(n)]=e),r=e}return r.mapValue.fields}applyChanges(r,e,t){Mr(e,(e,t)=>r[e]=t);for(let e of t)delete r[e]}clone(){return new xn(bn(this.value))}}class k{constructor(e,t,r,n,i,s,a){this.key=e,this.documentType=t,this.version=r,this.readTime=n,this.createTime=i,this.data=s,this.documentState=a}static newInvalidDocument(e){return new k(e,0,g.min(),g.min(),g.min(),xn.empty(),0)}static newFoundDocument(e,t,r,n){return new k(e,1,t,g.min(),r,n,0)}static newNoDocument(e,t){return new k(e,2,t,g.min(),g.min(),xn.empty(),0)}static newUnknownDocument(e,t){return new k(e,3,t,g.min(),g.min(),xn.empty(),2)}convertToFoundDocument(e,t){return!this.createTime.isEqual(g.min())||2!==this.documentType&&0!==this.documentType||(this.createTime=e),this.version=e,this.documentType=1,this.data=t,this.documentState=0,this}convertToNoDocument(e){return this.version=e,this.documentType=2,this.data=xn.empty(),this.documentState=0,this}convertToUnknownDocument(e){return this.version=e,this.documentType=3,this.data=xn.empty(),this.documentState=2,this}setHasCommittedMutations(){return this.documentState=2,this}setHasLocalMutations(){return this.documentState=1,this.version=g.min(),this}setReadTime(e){return this.readTime=e,this}get hasLocalMutations(){return 1===this.documentState}get hasCommittedMutations(){return 2===this.documentState}get hasPendingWrites(){return this.hasLocalMutations||this.hasCommittedMutations}isValidDocument(){return 0!==this.documentType}isFoundDocument(){return 1===this.documentType}isNoDocument(){return 2===this.documentType}isUnknownDocument(){return 3===this.documentType}isEqual(e){return e instanceof k&&this.key.isEqual(e.key)&&this.version.isEqual(e.version)&&this.documentType===e.documentType&&this.documentState===e.documentState&&this.data.isEqual(e.data)}mutableCopy(){return new k(this.key,this.documentType,this.version,this.readTime,this.createTime,this.data.clone(),this.documentState)}toString(){return`Document(${this.key}, ${this.version}, ${JSON.stringify(this.data.value)}, {createTime: ${this.createTime}}), {documentType: ${this.documentType}}), {documentState: ${this.documentState}})`}}class An{constructor(e,t){this.position=e,this.inclusive=t}}function Cn(e,t,r){let n=0;for(let a=0;a<e.position.length;a++){var i=t[a],s=e.position[a];if(n=i.field.isKeyField()?x.comparator(x.fromName(s.referenceValue),r.key):hn(s,r.data.field(i.field)),"desc"===i.dir&&(n*=-1),0!==n)break}return n}function Dn(e,t){if(null===e)return null===t;if(null===t)return!1;if(e.inclusive!==t.inclusive||e.position.length!==t.position.length)return!1;for(let r=0;r<e.position.length;r++)if(!ln(e.position[r],t.position[r]))return!1;return!0}class Nn{constructor(e,t="asc"){this.field=e,this.dir=t}}class kn{}class R extends kn{constructor(e,t,r){super(),this.field=e,this.op=t,this.value=r}static create(e,t,r){return e.isKeyField()?"in"===t||"not-in"===t?this.createKeyFieldInFilter(e,t,r):new Pn(e,t,r):"array-contains"===t?new zn(e,r):"in"===t?new jn(e,r):"not-in"===t?new Kn(e,r):"array-contains-any"===t?new Gn(e,r):new R(e,t,r)}static createKeyFieldInFilter(e,t,r){return new("in"===t?Un:Bn)(e,r)}matches(e){var t=e.data.field(this.field);return"!="===this.op?null!==t&&void 0===t.nullValue&&this.matchesComparison(hn(t,this.value)):null!==t&&on(this.value)===on(t)&&this.matchesComparison(hn(t,this.value))}matchesComparison(e){switch(this.op){case"<":return e<0;case"<=":return e<=0;case"==":return 0===e;case"!=":return 0!==e;case">":return 0<e;case">=":return 0<=e;default:return E(47266,{operator:this.op})}}isInequality(){return 0<=["<","<=",">",">=","!=","not-in"].indexOf(this.op)}getFlattenedFilters(){return[this]}getFilters(){return[this]}}class M extends kn{constructor(e,t){super(),this.filters=e,this.op=t,this.Te=null}static create(e,t){return new M(e,t)}matches(t){return Rn(this)?void 0===this.filters.find(e=>!e.matches(t)):void 0!==this.filters.find(e=>e.matches(t))}getFlattenedFilters(){return null===this.Te&&(this.Te=this.filters.reduce((e,t)=>e.concat(t.getFlattenedFilters()),[])),this.Te}getFilters(){return Object.assign([],this.filters)}}function Rn(e){return"and"===e.op}function Mn(e){return"or"===e.op}function On(e){return Ln(e)&&Rn(e)}function Ln(e){for(var t of e.filters)if(t instanceof M)return!1;return!0}function Fn(e,t){var r=e.filters.concat(t);return M.create(r,e.op)}function Vn(e){return e instanceof R?`${(t=e).field.canonicalString()} ${t.op} `+fn(t.value):e instanceof M?(t=e).op.toString()+" {"+t.getFilters().map(Vn).join(" ,")+"}":"Filter";var t}class Pn extends R{constructor(e,t,r){super(e,t,r),this.key=x.fromName(r.referenceValue)}matches(e){var t=x.comparator(e.key,this.key);return this.matchesComparison(t)}}class Un extends R{constructor(e,t){super(e,"in",t),this.keys=qn(0,t)}matches(t){return this.keys.some(e=>e.isEqual(t.key))}}class Bn extends R{constructor(e,t){super(e,"not-in",t),this.keys=qn(0,t)}matches(t){return!this.keys.some(e=>e.isEqual(t.key))}}function qn(e,t){var r;return((null==(r=t.arrayValue)?void 0:r.values)||[]).map(e=>x.fromName(e.referenceValue))}class zn extends R{constructor(e,t){super(e,"array-contains",t)}matches(e){var t=e.data.field(this.field);return pn(t)&&un(t.arrayValue,this.value)}}class jn extends R{constructor(e,t){super(e,"in",t)}matches(e){var t=e.data.field(this.field);return null!==t&&un(this.value.arrayValue,t)}}class Kn extends R{constructor(e,t){super(e,"not-in",t)}matches(e){var t;return!un(this.value.arrayValue,{nullValue:"NULL_VALUE"})&&null!==(t=e.data.field(this.field))&&void 0===t.nullValue&&!un(this.value.arrayValue,t)}}class Gn extends R{constructor(e,t){super(e,"array-contains-any",t)}matches(e){var t=e.data.field(this.field);return!(!pn(t)||!t.arrayValue.values)&&t.arrayValue.values.some(e=>un(this.value.arrayValue,e))}}class Qn{constructor(e,t=null,r=[],n=[],i=null,s=null,a=null){this.path=e,this.collectionGroup=t,this.orderBy=r,this.filters=n,this.limit=i,this.startAt=s,this.endAt=a,this.Ie=null}}function $n(e,t=null,r=[],n=[],i=null,s=null,a=null){return new Qn(e,t,r,n,i,s,a)}function Hn(e){var t=e;if(null===t.Ie){let e=t.path.canonicalString();null!==t.collectionGroup&&(e+="|cg:"+t.collectionGroup),e=(e=(e+="|f:")+t.filters.map(e=>function t(e){var r;return e instanceof R?e.field.canonicalString()+e.op.toString()+fn(e.value):On(e)?e.filters.map(e=>t(e)).join(","):(r=e.filters.map(e=>t(e)).join(","),e.op+`(${r})`)}(e)).join(",")+"|ob:")+t.orderBy.map(e=>(e=e).field.canonicalString()+e.dir).join(","),dt(t.limit)||(e=(e+="|l:")+t.limit),t.startAt&&(e=(e=(e+="|lb:")+(t.startAt.inclusive?"b:":"a:"))+t.startAt.position.map(e=>fn(e)).join(",")),t.endAt&&(e=(e=(e+="|ub:")+(t.endAt.inclusive?"a:":"b:"))+t.endAt.position.map(e=>fn(e)).join(",")),t.Ie=e}return t.Ie}function Wn(e,t){if(e.limit!==t.limit)return!1;if(e.orderBy.length!==t.orderBy.length)return!1;for(let i=0;i<e.orderBy.length;i++)if(r=e.orderBy[i],n=t.orderBy[i],r.dir!==n.dir||!r.field.isEqual(n.field))return!1;var r,n;if(e.filters.length!==t.filters.length)return!1;for(let s=0;s<e.filters.length;s++)if(!function n(e,t){return e instanceof R?(r=e,(s=t)instanceof R&&r.op===s.op&&r.field.isEqual(s.field)&&ln(r.value,s.value)):e instanceof M?(r=e,(i=t)instanceof M&&r.op===i.op&&r.filters.length===i.filters.length&&r.filters.reduce((e,t,r)=>e&&n(t,i.filters[r]),!0)):void E(19439);var i,r,s}(e.filters[s],t.filters[s]))return!1;return e.collectionGroup===t.collectionGroup&&!!e.path.isEqual(t.path)&&!!Dn(e.startAt,t.startAt)&&Dn(e.endAt,t.endAt)}function Yn(e){return x.isDocumentKey(e.path)&&null===e.collectionGroup&&0===e.filters.length}function Jn(e,t){return e.filters.filter(e=>e instanceof R&&e.field.isEqual(t))}function Xn(e,r,n){let i=an,s=!0;for(let n of Jn(e,r)){let e=an,t=!0;switch(n.op){case"<":case"<=":e="nullValue"in(a=n.value)?an:"booleanValue"in a?{booleanValue:!1}:"integerValue"in a||"doubleValue"in a?{doubleValue:NaN}:"timestampValue"in a?{timestampValue:{seconds:Number.MIN_SAFE_INTEGER}}:"stringValue"in a?{stringValue:""}:"bytesValue"in a?{bytesValue:""}:"referenceValue"in a?gn(Zr.empty(),x.empty()):"geoPointValue"in a?{geoPointValue:{latitude:-90,longitude:-180}}:"arrayValue"in a?{arrayValue:{}}:"mapValue"in a?_n(a)?Tn:{mapValue:{}}:E(35942,{value:a});break;case"==":case"in":case">=":e=n.value;break;case">":e=n.value,t=!1;break;case"!=":case"not-in":e=an}En({value:i,inclusive:s},{value:e,inclusive:t})<0&&(i=e,s=t)}var a;if(null!==n)for(let t=0;t<e.orderBy.length;++t)if(e.orderBy[t].field.isEqual(r)){let e=n.position[t];En({value:i,inclusive:s},{value:e,inclusive:n.inclusive})<0&&(i=e,s=n.inclusive);break}return{value:i,inclusive:s}}function Zn(e,r,n){let i=rn,s=!0;for(let n of Jn(e,r)){let e=rn,t=!0;switch(n.op){case">=":case">":e="nullValue"in(a=n.value)?{booleanValue:!1}:"booleanValue"in a?{doubleValue:NaN}:"integerValue"in a||"doubleValue"in a?{timestampValue:{seconds:Number.MIN_SAFE_INTEGER}}:"timestampValue"in a?{stringValue:""}:"stringValue"in a?{bytesValue:""}:"bytesValue"in a?gn(Zr.empty(),x.empty()):"referenceValue"in a?{geoPointValue:{latitude:-90,longitude:-180}}:"geoPointValue"in a?{arrayValue:{}}:"arrayValue"in a?Tn:"mapValue"in a?_n(a)?{mapValue:{}}:rn:E(61959,{value:a}),t=!1;break;case"==":case"in":case"<=":e=n.value;break;case"<":e=n.value,t=!1;break;case"!=":case"not-in":e=rn}0<Sn({value:i,inclusive:s},{value:e,inclusive:t})&&(i=e,s=t)}var a;if(null!==n)for(let t=0;t<e.orderBy.length;++t)if(e.orderBy[t].field.isEqual(r)){let e=n.position[t];0<Sn({value:i,inclusive:s},{value:e,inclusive:n.inclusive})&&(i=e,s=n.inclusive);break}return{value:i,inclusive:s}}class ei{constructor(e,t=null,r=[],n=[],i=null,s="F",a=null,o=null){this.path=e,this.collectionGroup=t,this.explicitOrderBy=r,this.filters=n,this.limit=i,this.limitType=s,this.startAt=a,this.endAt=o,this.Ee=null,this.de=null,this.Ae=null,this.startAt,this.endAt}}function ti(e,t,r,n,i,s,a,o){return new ei(e,t,r,n,i,s,a,o)}function ri(e){return new ei(e)}function ni(e){return 0===e.filters.length&&null===e.limit&&null==e.startAt&&null==e.endAt&&(0===e.explicitOrderBy.length||1===e.explicitOrderBy.length&&e.explicitOrderBy[0].field.isKeyField())}function ii(e){return null!==e.collectionGroup}function si(e){let n=e;if(null===n.Ee){n.Ee=[];let t=new Set;for(var i of n.explicitOrderBy)n.Ee.push(i),t.add(i.field.canonicalString());let r=0<n.explicitOrderBy.length?n.explicitOrderBy[n.explicitOrderBy.length-1].dir:"asc",e=(e=>{let t=new C(v.comparator);return e.filters.forEach(e=>{e.getFlattenedFilters().forEach(e=>{e.isInequality()&&(t=t.add(e.field))})}),t})(n);e.forEach(e=>{t.has(e.canonicalString())||e.isKeyField()||n.Ee.push(new Nn(e,r))}),t.has(v.keyField().canonicalString())||n.Ee.push(new Nn(v.keyField(),r))}return n.Ee}function ai(e){var t=e;return t.de||(t.de=((e,t)=>{if("F"===e.limitType)return $n(e.path,e.collectionGroup,t,e.filters,e.limit,e.startAt,e.endAt);t=t.map(e=>{var t="desc"===e.dir?"asc":"desc";return new Nn(e.field,t)});var r=e.endAt?new An(e.endAt.position,e.endAt.inclusive):null,n=e.startAt?new An(e.startAt.position,e.startAt.inclusive):null;return $n(e.path,e.collectionGroup,t,e.filters,e.limit,r,n)})(t,si(e))),t.de}function oi(e,t){var r=e.filters.concat([t]);return new ei(e.path,e.collectionGroup,e.explicitOrderBy.slice(),r,e.limit,e.limitType,e.startAt,e.endAt)}function li(e,t,r){return new ei(e.path,e.collectionGroup,e.explicitOrderBy.slice(),e.filters.slice(),t,r,e.startAt,e.endAt)}function ui(e,t){return Wn(ai(e),ai(t))&&e.limitType===t.limitType}function hi(e){return Hn(ai(e))+"|lt:"+e.limitType}function ci(e){return`Query(target=${(e=>{let t=e.path.canonicalString();return null!==e.collectionGroup&&(t+=" collectionGroup="+e.collectionGroup),0<e.filters.length&&(t+=`, filters: [${e.filters.map(e=>Vn(e)).join(", ")}]`),dt(e.limit)||(t+=", limit: "+e.limit),0<e.orderBy.length&&(t+=`, orderBy: [${e.orderBy.map(e=>`${(e=e).field.canonicalString()} (${e.dir})`).join(", ")}]`),e.startAt&&(t=(t=(t+=", startAt: ")+(e.startAt.inclusive?"b:":"a:"))+e.startAt.position.map(e=>fn(e)).join(",")),`Target(${t=e.endAt?(t=(t+=", endAt: ")+(e.endAt.inclusive?"a:":"b:"))+e.endAt.position.map(e=>fn(e)).join(","):t})`})(ai(e))}; limitType=${e.limitType})`}function di(e,t){return t.isFoundDocument()&&(s=e,o=(a=t).key.path,null!==s.collectionGroup?a.key.hasCollectionId(s.collectionGroup)&&s.path.isPrefixOf(o):x.isDocumentKey(s.path)?s.path.isEqual(o):s.path.isImmediateParentOf(o))&&((e,t)=>{for(var r of si(e))if(!r.field.isKeyField()&&null===t.data.field(r.field))return;return 1})(e,t)&&((e,t)=>{for(var r of e.filters)if(!r.matches(t))return;return 1})(e,t)&&(a=t,!(s=e).startAt||(n=s.startAt,r=si(s),i=Cn(n,r,a),n.inclusive?i<=0:i<0))&&(!s.endAt||(r=s.endAt,n=si(s),i=Cn(r,n,a),r.inclusive?0<=i:0<i));var r,n,i,s,a,o}function fi(e){return e.collectionGroup||(e.path.length%2==1?e.path.lastSegment():e.path.get(e.path.length-2))}function gi(e){return(t,r)=>{let n=!1;for(var i of si(e)){let e=((e,t,r)=>{var n=e.field.isKeyField()?x.comparator(t.key,r.key):((e,t,r)=>{var n=t.data.field(e),i=r.data.field(e);return null!==n&&null!==i?hn(n,i):E(42886)})(e.field,t,r);switch(e.dir){case"asc":return n;case"desc":return-1*n;default:return E(19790,{direction:e.dir})}})(i,t,r);if(0!==e)return e;n=n||i.field.isKeyField()}return 0}}class mi{constructor(e,t){this.mapKeyFn=e,this.equalsFn=t,this.inner={},this.innerSize=0}get(r){let e=this.mapKeyFn(r),n=this.inner[e];if(void 0!==n)for(let[e,t]of n)if(this.equalsFn(e,r))return t}has(e){return void 0!==this.get(e)}set(t,r){var e=this.mapKeyFn(t),n=this.inner[e];if(void 0===n)this.inner[e]=[[t,r]];else{for(let e=0;e<n.length;e++)if(this.equalsFn(n[e][0],t))return void(n[e]=[t,r]);n.push([t,r])}this.innerSize++}delete(t){var r=this.mapKeyFn(t),n=this.inner[r];if(void 0!==n)for(let e=0;e<n.length;e++)if(this.equalsFn(n[e][0],t))return 1===n.length?delete this.inner[r]:n.splice(e,1),this.innerSize--,!0;return!1}forEach(n){Mr(this.inner,(e,t)=>{for(let[e,r]of t)n(e,r)})}isEmpty(){return Or(this.inner)}size(){return this.innerSize}}let pi=new A(x.comparator);let yi=new A(x.comparator);function vi(...e){let t=yi;for(var r of e)t=t.insert(r.key,r);return t}function wi(e){let r=yi;return e.forEach((e,t)=>r=r.insert(e,t.overlayedDocument)),r}function _i(){return new mi(e=>e.toString(),(e,t)=>e.isEqual(t))}let bi=new A(x.comparator),Ii=new C(x.comparator);function O(...e){let t=Ii;for(var r of e)t=t.add(r);return t}let Ti=new C(S);function Ei(e,t){if(e.useProto3Json){if(isNaN(t))return{doubleValue:"NaN"};if(t===1/0)return{doubleValue:"Infinity"};if(t===-1/0)return{doubleValue:"-Infinity"}}return{doubleValue:ft(t)?"-0":t}}function Si(e){return{integerValue:""+e}}function xi(e,t){return gt(t)?Si(t):Ei(e,t)}class Ai{constructor(){this._=void 0}}function Ci(e,t){return e instanceof Oi?mn(e=t)||(e=e)&&"doubleValue"in e?t:{integerValue:0}:null}class Di extends Ai{}class Ni extends Ai{constructor(e){super(),this.elements=e}}function ki(e,t){var r=Fi(t);for(let t of e.elements)r.some(e=>ln(e,t))||r.push(t);return{arrayValue:{values:r}}}class Ri extends Ai{constructor(e){super(),this.elements=e}}function Mi(e,t){let r=Fi(t);for(let t of e.elements)r=r.filter(e=>!ln(e,t));return{arrayValue:{values:r}}}class Oi extends Ai{constructor(e,t){super(),this.serializer=e,this.Re=t}}function Li(e){return N(e.integerValue||e.doubleValue)}function Fi(e){return pn(e)&&e.arrayValue.values?e.arrayValue.values.slice():[]}class Vi{constructor(e,t){this.field=e,this.transform=t}}class Pi{constructor(e,t){this.version=e,this.transformResults=t}}class L{constructor(e,t){this.updateTime=e,this.exists=t}static none(){return new L}static exists(e){return new L(void 0,e)}static updateTime(e){return new L(e)}get isNone(){return void 0===this.updateTime&&void 0===this.exists}isEqual(e){return this.exists===e.exists&&(this.updateTime?!!e.updateTime&&this.updateTime.isEqual(e.updateTime):!e.updateTime)}}function Ui(e,t){return void 0!==e.updateTime?t.isFoundDocument()&&t.version.isEqual(e.updateTime):void 0===e.exists||e.exists===t.isFoundDocument()}class Bi{}function qi(e,r){if(!e.hasLocalMutations||r&&0===r.fields.length)return null;if(null===r)return e.isNoDocument()?new Wi(e.key,L.none()):new Ki(e.key,e.data,L.none());{var n,i=e.data,s=xn.empty();let t=new C(v.comparator);for(n of r.fields)if(!t.has(n)){let e=i.field(n);null===e&&1<n.length&&(n=n.popLast(),e=i.field(n)),null===e?s.delete(n):s.set(n,e),t=t.add(n)}return new Gi(e.key,s,new Ur(t.toArray()),L.none())}}function zi(e,t,r,n){return e instanceof Ki?(s=t,a=r,o=n,Ui((i=e).precondition,s)?(l=i.value.clone(),u=Hi(i.fieldTransforms,o,s),l.setAll(u),s.convertToFoundDocument(s.version,l).setHasLocalMutations(),null):a):e instanceof Gi?(i=t,o=r,s=n,Ui((a=e).precondition,i)?(u=Hi(a.fieldTransforms,s,i),(l=i.data).setAll(Qi(a)),l.setAll(u),i.convertToFoundDocument(i.version,l).setHasLocalMutations(),null===o?null:o.unionWith(a.fieldMask.fields).unionWith(a.fieldTransforms.map(e=>e.field))):o):(n=t,t=r,Ui(e.precondition,n)?(n.convertToNoDocument(n.version).setHasLocalMutations(),null):t);var i,s,a,o,l,u}function ji(e,t){return e.type===t.type&&!!e.key.isEqual(t.key)&&!!e.precondition.isEqual(t.precondition)&&(r=e.fieldTransforms,n=t.fieldTransforms,!!(void 0===r&&void 0===n||r&&n&&ke(r,n,(e,t)=>(t=t,(e=e).field.isEqual(t.field)&&(e=e.transform,t=t.transform,e instanceof Ni&&t instanceof Ni||e instanceof Ri&&t instanceof Ri?ke(e.elements,t.elements,ln):e instanceof Oi&&t instanceof Oi?ln(e.Re,t.Re):e instanceof Di&&t instanceof Di)))))&&(0===e.type?e.value.isEqual(t.value):1!==e.type||e.data.isEqual(t.data)&&e.fieldMask.isEqual(t.fieldMask));var r,n}class Ki extends Bi{constructor(e,t,r,n=[]){super(),this.key=e,this.value=t,this.precondition=r,this.fieldTransforms=n,this.type=0}getFieldMask(){return null}}class Gi extends Bi{constructor(e,t,r,n,i=[]){super(),this.key=e,this.data=t,this.fieldMask=r,this.precondition=n,this.fieldTransforms=i,this.type=1}getFieldMask(){return this.fieldMask}}function Qi(r){let n=new Map;return r.fieldMask.fields.forEach(e=>{var t;e.isEmpty()||(t=r.data.field(e),n.set(e,t))}),n}function $i(e,t,r){var n,i,s,a=new Map;y(e.length===r.length,32656,{Ve:r.length,me:e.length});for(let h=0;h<r.length;h++){var o=e[h],l=o.transform,u=t.data.field(o.field);a.set(o.field,(n=l,i=u,s=r[h],n instanceof Ni?ki(n,i):n instanceof Ri?Mi(n,i):s))}return a}function Hi(e,r,n){var i,s,a,o,l,u,h,c=new Map;for(i of e){let e=i.transform,t=n.data.field(i.field);c.set(i.field,(s=e,a=t,o=r,h=u=l=void 0,s instanceof Di?(o=o,u=a,h={fields:{[Gr]:{stringValue:Kr},[$r]:{timestampValue:{seconds:o.seconds,nanos:o.nanoseconds}}}},(u=u&&Hr(u)?Wr(u):u)&&(h.fields[Qr]=u),{mapValue:h}):s instanceof Ni?ki(s,a):s instanceof Ri?Mi(s,a):(h=Ci(o=s,a),l=Li(h)+Li(o.Re),mn(h)&&mn(o.Re)?Si(l):Ei(o.serializer,l))))}return c}class Wi extends Bi{constructor(e,t){super(),this.key=e,this.precondition=t,this.type=2,this.fieldTransforms=[]}getFieldMask(){return null}}class Yi extends Bi{constructor(e,t){super(),this.key=e,this.precondition=t,this.type=3,this.fieldTransforms=[]}getFieldMask(){return null}}class Ji{constructor(e,t,r,n){this.batchId=e,this.localWriteTime=t,this.baseMutations=r,this.mutations=n}applyToRemoteDocument(e,t){var r,n,i,s,a,o,l,u=t.mutationResults;for(let c=0;c<this.mutations.length;c++){var h=this.mutations[c];h.key.isEqual(e.key)&&(r=h,n=e,i=u[c],l=h=o=a=s=void 0,r instanceof Ki?(a=n,o=i,h=(s=r).value.clone(),l=$i(s.fieldTransforms,a,o.transformResults),h.setAll(l),a.convertToFoundDocument(o.version,h).setHasCommittedMutations()):r instanceof Gi?(s=n,a=i,Ui((o=r).precondition,s)?(l=$i(o.fieldTransforms,s,a.transformResults),(h=s.data).setAll(Qi(o)),h.setAll(l),s.convertToFoundDocument(a.version,h).setHasCommittedMutations()):s.convertToUnknownDocument(a.version)):n.convertToNoDocument(i.version).setHasCommittedMutations())}}applyToLocalView(e,t){for(var r of this.baseMutations)r.key.isEqual(e.key)&&(t=zi(r,e,t,this.localWriteTime));for(var n of this.mutations)n.key.isEqual(e.key)&&(t=zi(n,e,t,this.localWriteTime));return t}applyToLocalDocumentSet(n,i){let s=_i();return this.mutations.forEach(e=>{var t=n.get(e.key),r=t.overlayedDocument,t=this.applyToLocalView(r,t.mutatedFields),t=qi(r,i.has(e.key)?null:t);null!==t&&s.set(e.key,t),r.isValidDocument()||r.convertToNoDocument(g.min())}),s}keys(){return this.mutations.reduce((e,t)=>e.add(t.key),O())}isEqual(e){return this.batchId===e.batchId&&ke(this.mutations,e.mutations,(e,t)=>ji(e,t))&&ke(this.baseMutations,e.baseMutations,(e,t)=>ji(e,t))}}class Xi{constructor(e,t,r,n){this.batch=e,this.commitVersion=t,this.mutationResults=r,this.docVersions=n}static from(e,t,r){y(e.mutations.length===r.length,58842,{fe:e.mutations.length,ge:r.length});let n=bi;var i=e.mutations;for(let s=0;s<i.length;s++)n=n.insert(i[s].key,r[s].version);return new Xi(e,t,r,n)}}class Zi{constructor(e,t){this.largestBatchId=e,this.mutation=t}getKey(){return this.mutation.key}isEqual(e){return null!==e&&this.mutation===e.mutation}toString(){return`Overlay{
      largestBatchId: ${this.largestBatchId},
      mutation: ${this.mutation.toString()}
    }`}}class es{constructor(e,t){this.count=e,this.unchangedNames=t}}function ts(e){switch(e){case b.OK:return E(64938);case b.CANCELLED:case b.UNKNOWN:case b.DEADLINE_EXCEEDED:case b.RESOURCE_EXHAUSTED:case b.INTERNAL:case b.UNAVAILABLE:case b.UNAUTHENTICATED:return!1;case b.INVALID_ARGUMENT:case b.NOT_FOUND:case b.ALREADY_EXISTS:case b.PERMISSION_DENIED:case b.FAILED_PRECONDITION:case b.ABORTED:case b.OUT_OF_RANGE:case b.UNIMPLEMENTED:case b.DATA_LOSS:return!0;default:return E(15467,{code:e})}}function rs(e){if(void 0===e)return d("GRPC error has no .code"),b.UNKNOWN;switch(e){case m.OK:return b.OK;case m.CANCELLED:return b.CANCELLED;case m.UNKNOWN:return b.UNKNOWN;case m.DEADLINE_EXCEEDED:return b.DEADLINE_EXCEEDED;case m.RESOURCE_EXHAUSTED:return b.RESOURCE_EXHAUSTED;case m.INTERNAL:return b.INTERNAL;case m.UNAVAILABLE:return b.UNAVAILABLE;case m.UNAUTHENTICATED:return b.UNAUTHENTICATED;case m.INVALID_ARGUMENT:return b.INVALID_ARGUMENT;case m.NOT_FOUND:return b.NOT_FOUND;case m.ALREADY_EXISTS:return b.ALREADY_EXISTS;case m.PERMISSION_DENIED:return b.PERMISSION_DENIED;case m.FAILED_PRECONDITION:return b.FAILED_PRECONDITION;case m.ABORTED:return b.ABORTED;case m.OUT_OF_RANGE:return b.OUT_OF_RANGE;case m.UNIMPLEMENTED:return b.UNIMPLEMENTED;case m.DATA_LOSS:return b.DATA_LOSS;default:return E(39323,{code:e})}}(e=m=m||{})[e.OK=0]="OK",e[e.CANCELLED=1]="CANCELLED",e[e.UNKNOWN=2]="UNKNOWN",e[e.INVALID_ARGUMENT=3]="INVALID_ARGUMENT",e[e.DEADLINE_EXCEEDED=4]="DEADLINE_EXCEEDED",e[e.NOT_FOUND=5]="NOT_FOUND",e[e.ALREADY_EXISTS=6]="ALREADY_EXISTS",e[e.PERMISSION_DENIED=7]="PERMISSION_DENIED",e[e.UNAUTHENTICATED=16]="UNAUTHENTICATED",e[e.RESOURCE_EXHAUSTED=8]="RESOURCE_EXHAUSTED",e[e.FAILED_PRECONDITION=9]="FAILED_PRECONDITION",e[e.ABORTED=10]="ABORTED",e[e.OUT_OF_RANGE=11]="OUT_OF_RANGE",e[e.UNIMPLEMENTED=12]="UNIMPLEMENTED",e[e.INTERNAL=13]="INTERNAL",e[e.UNAVAILABLE=14]="UNAVAILABLE",e[e.DATA_LOSS=15]="DATA_LOSS";let ns=new le([4294967295,4294967295],0);function is(e){var t=Ae().encode(e),r=new ue;return r.update(t),new Uint8Array(r.digest())}function ss(e){var t=new DataView(e.buffer),r=t.getUint32(0,!0),n=t.getUint32(4,!0),i=t.getUint32(8,!0),t=t.getUint32(12,!0);return[new le([r,n],0),new le([i,t],0)]}class as{constructor(e,t,r){if(this.bitmap=e,this.padding=t,this.hashCount=r,t<0||8<=t)throw new os("Invalid padding: "+t);if(r<0)throw new os("Invalid hash count: "+r);if(0<e.length&&0===this.hashCount)throw new os("Invalid hash count: "+r);if(0===e.length&&0!==t)throw new os("Invalid padding when bitmap length is 0: "+t);this.pe=8*e.length-t,this.ye=le.fromNumber(this.pe)}we(e,t,r){let n=e.add(t.multiply(le.fromNumber(r)));return(n=1===n.compare(ns)?new le([n.getBits(0),n.getBits(1)],0):n).modulo(this.ye).toNumber()}be(e){return!!(this.bitmap[Math.floor(e/8)]&1<<e%8)}mightContain(e){if(0===this.pe)return!1;let t=is(e),[r,n]=ss(t);for(let i=0;i<this.hashCount;i++){let e=this.we(r,n,i);if(!this.be(e))return!1}return!0}static create(e,t,r){let n=e%8==0?0:8-e%8,i=new Uint8Array(Math.ceil(e/8)),s=new as(i,n,t);return r.forEach(e=>s.insert(e)),s}insert(i){if(0!==this.pe){let e=is(i),[t,r]=ss(e);for(let n=0;n<this.hashCount;n++){let e=this.we(t,r,n);this.Se(e)}}}Se(e){var t=Math.floor(e/8);this.bitmap[t]|=1<<e%8}}class os extends Error{constructor(){super(...arguments),this.name="BloomFilterError"}}class ls{constructor(e,t,r,n,i){this.snapshotVersion=e,this.targetChanges=t,this.targetMismatches=r,this.documentUpdates=n,this.resolvedLimboDocuments=i}static createSynthesizedRemoteEventForCurrentChange(e,t,r){var n=new Map;return n.set(e,us.createSynthesizedTargetChangeForCurrentChange(e,t,r)),new ls(g.min(),n,new A(S),pi,O())}}class us{constructor(e,t,r,n,i){this.resumeToken=e,this.current=t,this.addedDocuments=r,this.modifiedDocuments=n,this.removedDocuments=i}static createSynthesizedTargetChangeForCurrentChange(e,t,r){return new us(r,t,O(),O(),O())}}class hs{constructor(e,t,r,n){this.De=e,this.removedTargetIds=t,this.key=r,this.ve=n}}class cs{constructor(e,t){this.targetId=e,this.Ce=t}}class ds{constructor(e,t,r=D.EMPTY_BYTE_STRING,n=null){this.state=e,this.targetIds=t,this.resumeToken=r,this.cause=n}}class fs{constructor(){this.Fe=0,this.Me=ps(),this.xe=D.EMPTY_BYTE_STRING,this.Oe=!1,this.Ne=!0}get current(){return this.Oe}get resumeToken(){return this.xe}get Be(){return 0!==this.Fe}get Le(){return this.Ne}ke(e){0<e.approximateByteSize()&&(this.Ne=!0,this.xe=e)}qe(){let r=O(),n=O(),i=O();return this.Me.forEach((e,t)=>{switch(t){case 0:r=r.add(e);break;case 2:n=n.add(e);break;case 1:i=i.add(e);break;default:E(38017,{changeType:t})}}),new us(this.xe,this.Oe,r,n,i)}Qe(){this.Ne=!1,this.Me=ps()}$e(e,t){this.Ne=!0,this.Me=this.Me.insert(e,t)}Ue(e){this.Ne=!0,this.Me=this.Me.remove(e)}Ke(){this.Fe+=1}We(){--this.Fe,y(0<=this.Fe,3241,{Fe:this.Fe})}Ge(){this.Ne=!0,this.Oe=!0}}class gs{constructor(e){this.ze=e,this.je=new Map,this.He=pi,this.Je=ms(),this.Ye=ms(),this.Ze=new A(S)}Xe(e){for(var t of e.De)e.ve&&e.ve.isFoundDocument()?this.et(t,e.ve):this.tt(t,e.key,e.ve);for(var r of e.removedTargetIds)this.tt(r,e.key,e.ve)}nt(r){this.forEachTarget(r,e=>{var t=this.rt(e);switch(r.state){case 0:this.it(e)&&t.ke(r.resumeToken);break;case 1:t.We(),t.Be||t.Qe(),t.ke(r.resumeToken);break;case 2:t.We(),t.Be||this.removeTarget(e);break;case 3:this.it(e)&&(t.Ge(),t.ke(r.resumeToken));break;case 4:this.it(e)&&(this.st(e),t.ke(r.resumeToken));break;default:E(56790,{state:r.state})}})}forEachTarget(e,r){0<e.targetIds.length?e.targetIds.forEach(r):this.je.forEach((e,t)=>{this.it(t)&&r(t)})}ot(n){let i=n.targetId,e=n.Ce.count,t=this._t(i);if(t){var r=t.target;if(Yn(r))if(0===e){let e=new x(r.path);this.tt(i,e,k.newNoDocument(e,g.min()))}else y(1===e,20013,{expectedCount:e});else{let r=this.ut(i);if(r!==e){let e=this.ct(n),t=e?this.lt(e,n,r):1;if(0!==t){this.st(i);let e=2===t?"TargetPurposeExistenceFilterMismatchBloom":"TargetPurposeExistenceFilterMismatch";this.Ze=this.Ze.insert(i,e)}}}}}ct(e){var t=e.Ce.unchangedNames;if(!t||!t.bits)return null;var{bits:{bitmap:t="",padding:r=0},hashCount:n=0}=t;let i,s;try{i=jr(t).toUint8Array()}catch(e){if(e instanceof Br)return pe("Decoding the base64 bloom filter in existence filter failed ("+e.message+"); ignoring the bloom filter and falling back to full re-query."),null;throw e}try{s=new as(i,r,n)}catch(e){return pe(e instanceof os?"BloomFilter error: ":"Applying bloom filter failed: ",e),null}return 0===s.pe?null:s}lt(e,t,r){return t.Ce.count===r-this.Tt(e,t.targetId)?0:2}Tt(r,n){var e=this.ze.getRemoteKeysForTarget(n);let i=0;return e.forEach(e=>{var t=this.ze.Pt(),t=`projects/${t.projectId}/databases/${t.database}/documents/`+e.path.canonicalString();r.mightContain(t)||(this.tt(n,e,null),i++)}),i}It(n){let i=new Map,s=(this.je.forEach((e,t)=>{var r=this._t(t);if(r){if(e.current&&Yn(r.target)){let e=new x(r.target.path);this.Et(e).has(t)||this.dt(t,e)||this.tt(t,e,k.newNoDocument(e,n))}e.Le&&(i.set(t,e.qe()),e.Qe())}}),O());this.Ye.forEach((e,t)=>{let r=!0;t.forEachWhile(e=>{var t=this._t(e);return!t||"TargetPurposeLimboResolution"===t.purpose||(r=!1)}),r&&(s=s.add(e))}),this.He.forEach((e,t)=>t.setReadTime(n));var e=new ls(n,i,this.Ze,this.He,s);return this.He=pi,this.Je=ms(),this.Ye=ms(),this.Ze=new A(S),e}et(e,t){var r;this.it(e)&&(r=this.dt(e,t.key)?2:0,this.rt(e).$e(t.key,r),this.He=this.He.insert(t.key,t),this.Je=this.Je.insert(t.key,this.Et(t.key).add(e)),this.Ye=this.Ye.insert(t.key,this.At(t.key).add(e)))}tt(e,t,r){var n;this.it(e)&&(n=this.rt(e),this.dt(e,t)?n.$e(t,1):n.Ue(t),this.Ye=this.Ye.insert(t,this.At(t).delete(e)),this.Ye=this.Ye.insert(t,this.At(t).add(e)),r)&&(this.He=this.He.insert(t,r))}removeTarget(e){this.je.delete(e)}ut(e){var t=this.rt(e).qe();return this.ze.getRemoteKeysForTarget(e).size+t.addedDocuments.size-t.removedDocuments.size}Ke(e){this.rt(e).Ke()}rt(e){let t=this.je.get(e);return t||(t=new fs,this.je.set(e,t)),t}At(e){let t=this.Ye.get(e);return t||(t=new C(S),this.Ye=this.Ye.insert(e,t)),t}Et(e){let t=this.Je.get(e);return t||(t=new C(S),this.Je=this.Je.insert(e,t)),t}it(e){var t=null!==this._t(e);return t||p("WatchChangeAggregator","Detected inactive target",e),t}_t(e){var t=this.je.get(e);return t&&t.Be?null:this.ze.Rt(e)}st(t){this.je.set(t,new fs),this.ze.getRemoteKeysForTarget(t).forEach(e=>{this.tt(t,e,null)})}dt(e,t){return this.ze.getRemoteKeysForTarget(e).has(t)}}function ms(){return new A(x.comparator)}function ps(){return new A(x.comparator)}let ys={asc:"ASCENDING",desc:"DESCENDING"},vs={"<":"LESS_THAN","<=":"LESS_THAN_OR_EQUAL",">":"GREATER_THAN",">=":"GREATER_THAN_OR_EQUAL","==":"EQUAL","!=":"NOT_EQUAL","array-contains":"ARRAY_CONTAINS",in:"IN","not-in":"NOT_IN","array-contains-any":"ARRAY_CONTAINS_ANY"},ws={and:"AND",or:"OR"};class _s{constructor(e,t){this.databaseId=e,this.useProto3Json=t}}function bs(e,t){return e.useProto3Json||dt(t)?t:{value:t}}function Is(e,t){return e.useProto3Json?`${new Date(1e3*t.seconds).toISOString().replace(/\.\d*/,"").replace("Z","")}.${("000000000"+t.nanoseconds).slice(-9)}Z`:{seconds:""+t.seconds,nanos:t.nanoseconds}}function Ts(e,t){return e.useProto3Json?t.toBase64():t.toUint8Array()}function F(e){return y(!!e,49232),g.fromTimestamp((t=zr(e),new h(t.seconds,t.nanos)));var t}function Es(e,t){return Ss(e,t).canonicalString()}function Ss(e,t){e=e;var r=new T(["projects",e.projectId,"databases",e.database]).child("documents");return void 0===t?r:r.child(t)}function xs(e){var t=T.fromString(e);return y(Qs(t),10190,{key:t.toString()}),t}function As(e,t){return Es(e.databaseId,t.path)}function Cs(e,t){var r=xs(t);if(r.get(1)!==e.databaseId.projectId)throw new I(b.INVALID_ARGUMENT,"Tried to deserialize key from different project: "+r.get(1)+" vs "+e.databaseId.projectId);if(r.get(3)!==e.databaseId.database)throw new I(b.INVALID_ARGUMENT,"Tried to deserialize key from different database: "+r.get(3)+" vs "+e.databaseId.database);return new x(Rs(r))}function Ds(e,t){return Es(e.databaseId,t)}function Ns(e){var t=xs(e);return 4===t.length?T.emptyPath():Rs(t)}function ks(e){return new T(["projects",e.databaseId.projectId,"databases",e.databaseId.database]).canonicalString()}function Rs(e){return y(4<e.length&&"documents"===e.get(4),29091,{key:e.toString()}),e.popFirst(5)}function Ms(e,t,r){return{name:As(e,t),fields:r.value.mapValue.fields}}function Os(e,t,r){var n=Cs(e,t.name),i=F(t.updateTime),s=t.createTime?F(t.createTime):g.min(),a=new xn({mapValue:{fields:t.fields}}),n=k.newFoundDocument(n,i,s,a);return r&&n.setHasCommittedMutations(),r?n.setHasCommittedMutations():n}function Ls(e,t){let r;if(t instanceof Ki)r={update:Ms(e,t.key,t.value)};else if(t instanceof Wi)r={delete:As(e,t.key)};else if(t instanceof Gi)r={update:Ms(e,t.key,t.data),updateMask:(e=>{let t=[];return e.fields.forEach(e=>t.push(e.canonicalString())),{fieldPaths:t}})(t.fieldMask)};else{if(!(t instanceof Yi))return E(16599,{ft:t.type});r={verify:As(e,t.key)}}return 0<t.fieldTransforms.length&&(r.updateTransforms=t.fieldTransforms.map(e=>{var t=e.transform;if(t instanceof Di)return{fieldPath:e.field.canonicalString(),setToServerValue:"REQUEST_TIME"};if(t instanceof Ni)return{fieldPath:e.field.canonicalString(),appendMissingElements:{values:t.elements}};if(t instanceof Ri)return{fieldPath:e.field.canonicalString(),removeAllFromArray:{values:t.elements}};if(t instanceof Oi)return{fieldPath:e.field.canonicalString(),increment:t.Re};throw E(20930,{transform:e.transform})})),t.precondition.isNone||(r.currentDocument=(e=e,void 0!==(t=t.precondition).updateTime?{updateTime:(n=t.updateTime,Is(e,n.toTimestamp()))}:void 0!==t.exists?{exists:t.exists}:E(27497))),r;var n}function Fs(i,t){let r=t.currentDocument?void 0!==(s=t.currentDocument).updateTime?L.updateTime(F(s.updateTime)):void 0!==s.exists?L.exists(s.exists):L.none():L.none(),n=t.updateTransforms?t.updateTransforms.map(r=>{{var e=i;let t=null;if("setToServerValue"in r)y("REQUEST_TIME"===r.setToServerValue,16630,{proto:r}),t=new Di;else if("appendMissingElements"in r){let e=r.appendMissingElements.values||[];t=new Ni(e)}else if("removeAllFromArray"in r){let e=r.removeAllFromArray.values||[];t=new Ri(e)}else"increment"in r?t=new Oi(e,r.increment):E(16584,{proto:r});var n=v.fromServerFormat(r.fieldPath);return new Vi(n,t)}}):[];var s,a;if(t.update){t.update.name;var o=Cs(i,t.update.name),l=new xn({mapValue:{fields:t.update.fields}});if(t.updateMask){s=t.updateMask,a=s.fieldPaths||[];let e=new Ur(a.map(e=>v.fromServerFormat(e)));return new Gi(o,l,e,r,n)}return new Ki(o,l,r,n)}if(t.delete){let e=Cs(i,t.delete);return new Wi(e,r)}if(t.verify){let e=Cs(i,t.verify);return new Yi(e,r)}return E(1463,{proto:t})}function Vs(e,n){return e&&0<e.length?(y(void 0!==n,14353),e.map(t=>{{var r=n;let e=t.updateTime?F(t.updateTime):F(r);return e.isEqual(g.min())&&(e=F(r)),new Pi(e,t.transformResults||[])}})):[]}function Ps(e,t){return{documents:[Ds(e,t.path)]}}function Us(e,t){var r={structuredQuery:{}},n=t.path;let i;null!==t.collectionGroup?(i=n,r.structuredQuery.from=[{collectionId:t.collectionGroup,allDescendants:!0}]):(i=n.popLast(),r.structuredQuery.from=[{collectionId:n.lastSegment()}]),r.parent=Ds(e,i);n=(e=>{if(0!==e.length)return function r(e){return e instanceof R?(e=>{if("=="===e.op){if(vn(e.value))return{unaryFilter:{field:Ks(e.field),op:"IS_NAN"}};if(yn(e.value))return{unaryFilter:{field:Ks(e.field),op:"IS_NULL"}}}else if("!="===e.op){if(vn(e.value))return{unaryFilter:{field:Ks(e.field),op:"IS_NOT_NAN"}};if(yn(e.value))return{unaryFilter:{field:Ks(e.field),op:"IS_NOT_NULL"}}}return{fieldFilter:{field:Ks(e.field),op:zs(e.op),value:e.value}}})(e):e instanceof M?(e=>{let t=e.getFilters().map(e=>r(e));return 1===t.length?t[0]:{compositeFilter:{op:js(e.op),filters:t}}})(e):E(54877,{filter:e})}(M.create(e,"and"))})(t.filters),n&&(r.structuredQuery.where=n),n=(e=>{if(0!==e.length)return e.map(e=>({field:Ks((e=e).field),direction:(e=e.dir,ys[e])}))})(t.orderBy),n&&(r.structuredQuery.orderBy=n),n=bs(e,t.limit);return null!==n&&(r.structuredQuery.limit=n),t.startAt&&(r.structuredQuery.startAt={before:(e=t.startAt).inclusive,values:e.position}),t.endAt&&(r.structuredQuery.endAt={before:!(e=t.endAt).inclusive,values:e.position}),{gt:r,parent:i}}function Bs(e){let t=Ns(e.parent);var r,n=e.structuredQuery,i=n.from?n.from.length:0;let s=null;if(0<i){y(1===i,65062);let e=n.from[0];e.allDescendants?s=e.collectionId:t=t.child(e.collectionId)}let a=[],o=(n.where&&(a=(e=n.where,(i=function t(e){return void 0!==e.unaryFilter?(i=>{switch(i.unaryFilter.op){case"IS_NAN":let e=Gs(i.unaryFilter.field);return R.create(e,"==",{doubleValue:NaN});case"IS_NULL":let t=Gs(i.unaryFilter.field);return R.create(t,"==",{nullValue:"NULL_VALUE"});case"IS_NOT_NAN":let r=Gs(i.unaryFilter.field);return R.create(r,"!=",{doubleValue:NaN});case"IS_NOT_NULL":let n=Gs(i.unaryFilter.field);return R.create(n,"!=",{nullValue:"NULL_VALUE"});case"OPERATOR_UNSPECIFIED":return E(61313);default:return E(60726)}})(e):void 0!==e.fieldFilter?(e=>R.create(Gs(e.fieldFilter.field),(e=>{switch(e){case"EQUAL":return"==";case"NOT_EQUAL":return"!=";case"GREATER_THAN":return">";case"GREATER_THAN_OR_EQUAL":return">=";case"LESS_THAN":return"<";case"LESS_THAN_OR_EQUAL":return"<=";case"ARRAY_CONTAINS":return"array-contains";case"IN":return"in";case"NOT_IN":return"not-in";case"ARRAY_CONTAINS_ANY":return"array-contains-any";case"OPERATOR_UNSPECIFIED":return E(58110);default:return E(50506)}})(e.fieldFilter.op),e.fieldFilter.value))(e):void 0!==e.compositeFilter?(e=>M.create(e.compositeFilter.filters.map(e=>t(e)),(e=>{switch(e){case"AND":return"and";case"OR":return"or";default:return E(1026)}})(e.compositeFilter.op)))(e):E(30097,{filter:e})}(e))instanceof M&&On(i)?i.getFilters():[i])),[]),l=(n.orderBy&&(o=n.orderBy.map(e=>(e=e,new Nn(Gs(e.field),(e=>{switch(e){case"ASCENDING":return"asc";case"DESCENDING":return"desc";default:return}})(e.direction))))),null),u=(n.limit&&(l=(e=n.limit,dt(i="object"==typeof e?e.value:e)?null:i)),null),h=(n.startAt&&(u=(e=n.startAt,i=!!e.before,r=e.values||[],new An(r,i))),null);return n.endAt&&(h=(e=n.endAt,r=!e.before,i=e.values||[],new An(i,r))),ti(t,s,o,a,l,"F",u,h)}function qs(e,t){var r=(e=>{switch(e){case"TargetPurposeListen":return null;case"TargetPurposeExistenceFilterMismatch":return"existence-filter-mismatch";case"TargetPurposeExistenceFilterMismatchBloom":return"existence-filter-mismatch-bloom";case"TargetPurposeLimboResolution":return"limbo-document";default:return E(28987,{purpose:e})}})(t.purpose);return null==r?null:{"goog-listen-tags":r}}function zs(e){return vs[e]}function js(e){return ws[e]}function Ks(e){return{fieldPath:e.canonicalString()}}function Gs(e){return v.fromServerFormat(e.fieldPath)}function Qs(e){return 4<=e.length&&"projects"===e.get(0)&&"databases"===e.get(2)}class $s{constructor(e,t,r,n,i=g.min(),s=g.min(),a=D.EMPTY_BYTE_STRING,o=null){this.target=e,this.targetId=t,this.purpose=r,this.sequenceNumber=n,this.snapshotVersion=i,this.lastLimboFreeSnapshotVersion=s,this.resumeToken=a,this.expectedCount=o}withSequenceNumber(e){return new $s(this.target,this.targetId,this.purpose,e,this.snapshotVersion,this.lastLimboFreeSnapshotVersion,this.resumeToken,this.expectedCount)}withResumeToken(e,t){return new $s(this.target,this.targetId,this.purpose,this.sequenceNumber,t,this.lastLimboFreeSnapshotVersion,e,null)}withExpectedCount(e){return new $s(this.target,this.targetId,this.purpose,this.sequenceNumber,this.snapshotVersion,this.lastLimboFreeSnapshotVersion,this.resumeToken,e)}withLastLimboFreeSnapshotVersion(e){return new $s(this.target,this.targetId,this.purpose,this.sequenceNumber,this.snapshotVersion,e,this.resumeToken,this.expectedCount)}}class Hs{constructor(e){this.wt=e}}function Ws(e,t){var r,n=t.key,i={prefixPath:n.getCollectionPath().popLast().toArray(),collectionGroup:n.collectionGroup,documentId:n.path.lastSegment(),readTime:Ys(t.readTime),hasCommittedMutations:t.hasCommittedMutations};if(t.isFoundDocument())i.document={name:As(e=e.wt,(r=t).key),fields:r.data.value.mapValue.fields,updateTime:Is(e,r.version.toTimestamp()),createTime:Is(e,r.createTime.toTimestamp())};else if(t.isNoDocument())i.noDocument={path:n.path.toArray(),readTime:Js(t.version)};else{if(!t.isUnknownDocument())return E(57904,{document:t});i.unknownDocument={path:n.path.toArray(),version:Js(t.version)}}return i}function Ys(e){var t=e.toTimestamp();return[t.seconds,t.nanoseconds]}function Js(e){var t=e.toTimestamp();return{seconds:t.seconds,nanoseconds:t.nanoseconds}}function Xs(e){var t=new h(e.seconds,e.nanoseconds);return g.fromTimestamp(t)}function Zs(t,r){let e=(r.baseMutations||[]).map(e=>Fs(t.wt,e));for(let s=0;s<r.mutations.length-1;++s){let t=r.mutations[s];if(s+1<r.mutations.length&&void 0!==r.mutations[s+1].transform){let e=r.mutations[s+1];t.updateTransforms=e.transform.fieldTransforms,r.mutations.splice(s+1,1),++s}}let n=r.mutations.map(e=>Fs(t.wt,e)),i=h.fromMillis(r.localWriteTimeMs);return new Ji(r.batchId,i,e,n)}function ea(e){var t,r=Xs(e.readTime),n=void 0!==e.lastLimboFreeSnapshotVersion?Xs(e.lastLimboFreeSnapshotVersion):g.min(),i=void 0!==e.query.documents?(t=e.query,y(1===(i=t.documents.length),1966,{count:i}),ai(ri(Ns(t.documents[0])))):ai(Bs(e.query));return new $s(i,e.targetId,"TargetPurposeListen",e.lastListenSequenceNumber,r,n,D.fromBase64String(e.resumeToken))}function ta(e,t){var r=Js(t.snapshotVersion),n=Js(t.lastLimboFreeSnapshotVersion),i=Yn(t.target)?Ps(e.wt,t.target):Us(e.wt,t.target).gt,s=t.resumeToken.toBase64();return{targetId:t.targetId,canonicalId:Hn(t.target),readTime:r,resumeToken:s,lastListenSequenceNumber:t.sequenceNumber,lastLimboFreeSnapshotVersion:n,query:i}}function ra(e){var t=Bs({parent:e.parent,structuredQuery:e.structuredQuery});return"LAST"===e.limitType?li(t,t.limit,"L"):t}function na(e,t){return new Zi(t.largestBatchId,Fs(e.wt,t.overlayMutation))}function ia(e,t){var r=t.path.lastSegment();return[e,o(t.path.popLast()),r]}function sa(e,t,r,n){return{indexId:e,uid:t,sequenceNumber:r,readTime:Js(n.readTime),documentKey:o(n.documentKey.path),largestBatchId:n.largestBatchId}}class aa{getBundleMetadata(e,t){return oa(e).get(t).next(e=>{if(e)return{id:(e=e).bundleId,createTime:Xs(e.createTime),version:e.version}})}saveBundleMetadata(e,t){return oa(e).put({bundleId:(e=t).id,createTime:Js(F(e.createTime)),version:e.version})}getNamedQuery(e,t){return la(e).get(t).next(e=>{if(e)return{name:(e=e).name,query:ra(e.bundledQuery),readTime:Xs(e.readTime)}})}saveNamedQuery(e,t){return la(e).put({name:(e=t).name,readTime:Js(F(e.readTime)),bundledQuery:e.bundledQuery})}}function oa(e){return r(e,Yt)}function la(e){return r(e,Jt)}class ua{constructor(e,t){this.serializer=e,this.userId=t}static bt(e,t){var r=t.uid||"";return new ua(e,r)}getOverlay(e,t){return ha(e).get(ia(this.userId,t)).next(e=>e?na(this.serializer,e):null)}getOverlays(e,t){let r=_i();return w.forEach(t,t=>this.getOverlay(e,t).next(e=>{null!==e&&r.set(t,e)})).next(()=>r)}saveOverlays(n,i,e){let s=[];return e.forEach((e,t)=>{var r=new Zi(i,t);s.push(this.St(n,r))}),w.waitFor(s)}removeOverlaysForBatchId(r,e,n){let t=new Set,i=(e.forEach(e=>t.add(o(e.getCollectionPath()))),[]);return t.forEach(e=>{var t=IDBKeyRange.bound([this.userId,e,n],[this.userId,e,n+1],!1,!0);i.push(ha(r).X(hr,t))}),w.waitFor(i)}getOverlaysForCollection(e,t,r){let n=_i(),i=o(t),s=IDBKeyRange.bound([this.userId,i,r],[this.userId,i,Number.POSITIVE_INFINITY],!0);return ha(e).J(hr,s).next(e=>{for(var t of e){let e=na(this.serializer,t);n.set(e.getKey(),e)}return n})}getOverlaysForCollectionGroup(e,t,r,i){let s=_i(),a;var n=IDBKeyRange.bound([this.userId,t,r],[this.userId,t,Number.POSITIVE_INFINITY],!0);return ha(e).te({index:dr,range:n},(e,t,r)=>{var n=na(this.serializer,t);s.size()<i||n.largestBatchId===a?(s.set(n.getKey(),n),a=n.largestBatchId):r.done()}).next(()=>s)}St(e,t){return ha(e).put(((e,t,r)=>{var[,n,i]=ia(t,r.mutation.key);return{userId:t,collectionPath:n,documentId:i,collectionGroup:r.mutation.key.getCollectionGroup(),largestBatchId:r.largestBatchId,overlayMutation:Ls(e.wt,r.mutation)}})(this.serializer,this.userId,t))}}function ha(e){return r(e,lr)}class ca{Dt(e){return r(e,gr)}getSessionToken(e){return this.Dt(e).get("sessionToken").next(e=>{var t=null==e?void 0:e.value;return t?D.fromUint8Array(t):D.EMPTY_BYTE_STRING})}setSessionToken(e,t){return this.Dt(e).put({name:"sessionToken",value:t.toUint8Array()})}}class da{constructor(){}vt(e,t){this.Ct(e,t),t.Ft()}Ct(t,r){if("nullValue"in t)this.Mt(r,5);else if("booleanValue"in t)this.Mt(r,10),r.xt(t.booleanValue?1:0);else if("integerValue"in t)this.Mt(r,15),r.xt(N(t.integerValue));else if("doubleValue"in t){var e=N(t.doubleValue);isNaN(e)?this.Mt(r,13):(this.Mt(r,15),ft(e)?r.xt(0):r.xt(e))}else if("timestampValue"in t){let e=t.timestampValue;this.Mt(r,20),"string"==typeof e&&(e=zr(e)),r.Ot(""+(e.seconds||"")),r.xt(e.nanos||0)}else"stringValue"in t?(this.Nt(t.stringValue,r),this.Bt(r)):"bytesValue"in t?(this.Mt(r,30),r.Lt(jr(t.bytesValue)),this.Bt(r)):"referenceValue"in t?this.kt(t.referenceValue,r):"geoPointValue"in t?(e=t.geoPointValue,this.Mt(r,45),r.xt(e.latitude||0),r.xt(e.longitude||0)):"mapValue"in t?In(t)?this.Mt(r,Number.MAX_SAFE_INTEGER):_n(t)?this.qt(t.mapValue,r):(this.Qt(t.mapValue,r),this.Bt(r)):"arrayValue"in t?(this.$t(t.arrayValue,r),this.Bt(r)):E(19022,{Ut:t})}Nt(e,t){this.Mt(t,25),this.Kt(e,t)}Kt(e,t){t.Ot(e)}Qt(e,t){var r=e.fields||{};this.Mt(t,55);for(let e of Object.keys(r))this.Nt(e,t),this.Ct(r[e],t)}qt(e,t){var r=e.fields||{},n=(this.Mt(t,53),sn),i=(null==(i=null==(i=r[n].arrayValue)?void 0:i.values)?void 0:i.length)||0;this.Mt(t,15),t.xt(N(i)),this.Nt(n,t),this.Ct(r[n],t)}$t(e,t){var r=e.values||[];this.Mt(t,50);for(let e of r)this.Ct(e,t)}kt(e,t){this.Mt(t,37),x.fromName(e).path.forEach(e=>{this.Mt(t,60),this.Kt(e,t)})}Mt(e,t){e.xt(t)}Bt(e){e.xt(2)}}da.Wt=new da;function fa(e){var t=64-(e=>{let t=0;for(let n=0;n<8;++n){var r=(e=>{if(0===e)return 8;let t=0;return e>>4||(t+=4,e<<=4),e>>6||(t+=2,e<<=2),e>>7||(t+=1),t})(255&e[n]);if(t+=r,8!==r)break}return t})(e);return Math.ceil(t/8)}class ga{constructor(){this.buffer=new Uint8Array(1024),this.position=0}Gt(e){var t=e[Symbol.iterator]();let r=t.next();for(;!r.done;)this.zt(r.value),r=t.next();this.jt()}Ht(e){var t=e[Symbol.iterator]();let r=t.next();for(;!r.done;)this.Jt(r.value),r=t.next();this.Yt()}Zt(e){for(var t of e){let e=t.charCodeAt(0);if(e<128)this.zt(e);else if(e<2048)this.zt(960|e>>>6),this.zt(128|63&e);else if(t<"\ud800"||"\udbff"<t)this.zt(480|e>>>12),this.zt(128|63&e>>>6),this.zt(128|63&e);else{let e=t.codePointAt(0);this.zt(240|e>>>18),this.zt(128|63&e>>>12),this.zt(128|63&e>>>6),this.zt(128|63&e)}}this.jt()}Xt(e){for(var t of e){let e=t.charCodeAt(0);if(e<128)this.Jt(e);else if(e<2048)this.Jt(960|e>>>6),this.Jt(128|63&e);else if(t<"\ud800"||"\udbff"<t)this.Jt(480|e>>>12),this.Jt(128|63&e>>>6),this.Jt(128|63&e);else{let e=t.codePointAt(0);this.Jt(240|e>>>18),this.Jt(128|63&e>>>12),this.Jt(128|63&e>>>6),this.Jt(128|63&e)}}this.Yt()}en(e){var t=this.tn(e),r=fa(t);this.nn(1+r),this.buffer[this.position++]=255&r;for(let n=t.length-r;n<t.length;++n)this.buffer[this.position++]=255&t[n]}rn(e){var t=this.tn(e),r=fa(t);this.nn(1+r),this.buffer[this.position++]=~(255&r);for(let n=t.length-r;n<t.length;++n)this.buffer[this.position++]=~(255&t[n])}sn(){this._n(255),this._n(255)}an(){this.un(255),this.un(255)}reset(){this.position=0}seed(e){this.nn(e.length),this.buffer.set(e,this.position),this.position+=e.length}cn(){return this.buffer.slice(0,this.position)}tn(e){e=e,(t=new DataView(new ArrayBuffer(8))).setFloat64(0,e,!1);var t,r=new Uint8Array(t.buffer),n=!!(128&r[0]);r[0]^=n?255:128;for(let i=1;i<r.length;++i)r[i]^=n?255:0;return r}zt(e){var t=255&e;0==t?(this._n(0),this._n(255)):255==t?(this._n(255),this._n(0)):this._n(t)}Jt(e){var t=255&e;0==t?(this.un(0),this.un(255)):255==t?(this.un(255),this.un(0)):this.un(e)}jt(){this._n(0),this._n(1)}Yt(){this.un(0),this.un(1)}_n(e){this.nn(1),this.buffer[this.position++]=e}un(e){this.nn(1),this.buffer[this.position++]=~e}nn(e){var t=e+this.position;if(!(t<=this.buffer.length)){let e=2*this.buffer.length;e<t&&(e=t);t=new Uint8Array(e);t.set(this.buffer),this.buffer=t}}}class ma{constructor(e){this.ln=e}Lt(e){this.ln.Gt(e)}Ot(e){this.ln.Zt(e)}xt(e){this.ln.en(e)}Ft(){this.ln.sn()}}class pa{constructor(e){this.ln=e}Lt(e){this.ln.Ht(e)}Ot(e){this.ln.Xt(e)}xt(e){this.ln.rn(e)}Ft(){this.ln.an()}}class ya{constructor(){this.ln=new ga,this.hn=new ma(this.ln),this.Pn=new pa(this.ln)}seed(e){this.ln.seed(e)}Tn(e){return 0===e?this.hn:this.Pn}cn(){return this.ln.cn()}reset(){this.ln.reset()}}class va{constructor(e,t,r,n){this.indexId=e,this.documentKey=t,this.arrayValue=r,this.directionalValue=n}In(){var e=this.directionalValue.length,t=0===e||255===this.directionalValue[e-1]?e+1:e,r=new Uint8Array(t);return r.set(this.directionalValue,0),t!==e?r.set([0],this.directionalValue.length):++r[r.length-1],new va(this.indexId,this.documentKey,this.arrayValue,r)}}function wa(e,t){var r=e.indexId-t.indexId;return 0!=r||0!==(r=_a(e.arrayValue,t.arrayValue))||0!==(r=_a(e.directionalValue,t.directionalValue))?r:x.comparator(e.documentKey,t.documentKey)}function _a(e,t){for(let n=0;n<e.length&&n<t.length;++n){var r=e[n]-t[n];if(0!=r)return r}return e.length-t.length}class ba{constructor(e){this.En=new C((e,t)=>v.comparator(e.field,t.field)),this.collectionId=null!=e.collectionGroup?e.collectionGroup:e.path.lastSegment(),this.dn=e.orderBy,this.An=[];for(var t of e.filters){let e=t;e.isInequality()?this.En=this.En.add(e):this.An.push(e)}}get Rn(){return 1<this.En.size}Vn(e){if(y(e.collectionGroup===this.collectionId,49279),this.Rn)return!1;let t=Ue(e);if(void 0!==t&&!this.mn(t))return!1;var r=Be(e);let n=new Set,i=0,s=0;for(;i<r.length&&this.mn(r[i]);++i)n=n.add(r[i].fieldPath.canonicalString());if(i!==r.length){if(0<this.En.size){let t=this.En.getIterator().getNext();if(!n.has(t.field.canonicalString())){let e=r[i];if(!this.fn(t,e)||!this.gn(this.dn[s++],e))return!1}++i}for(;i<r.length;++i){let e=r[i];if(s>=this.dn.length||!this.gn(this.dn[s++],e))return!1}}return!0}pn(){if(this.Rn)return null;let e=new C(v.comparator);var t,r,n=[];for(t of this.An)t.field.isKeyField()||("array-contains"===t.op||"array-contains-any"===t.op?n.push(new qe(t.field,2)):e.has(t.field)||(e=e.add(t.field),n.push(new qe(t.field,0))));for(r of this.dn)r.field.isKeyField()||e.has(r.field)||(e=e.add(r.field),n.push(new qe(r.field,"asc"===r.dir?0:1)));return new Pe(Pe.UNKNOWN_ID,this.collectionId,n,ze.empty())}mn(e){for(var t of this.An)if(this.fn(t,e))return!0;return!1}fn(e,t){var r;return!(void 0===e||!e.field.isEqual(t.fieldPath))&&(r="array-contains"===e.op||"array-contains-any"===e.op,2===t.kind==r)}gn(e,t){return!!e.field.isEqual(t.fieldPath)&&(0===t.kind&&"asc"===e.dir||1===t.kind&&"desc"===e.dir)}}function Ia(e){var t;return 0===e.getFilters().length?[]:(t=function t(e){if(y(e instanceof R||e instanceof M,34018),e instanceof R)return e;if(1===e.filters.length)return t(e.filters[0]);let r=e.filters.map(e=>t(e));let n=M.create(r,e.op);return n=Ca(n),Sa(n)?n:(y(n instanceof M,64498),y(Rn(n),40251),y(1<n.filters.length,57927),n.filters.reduce((e,t)=>xa(e,t)))}(function t(r){var n;if(y(r instanceof R||r instanceof M,20012),r instanceof R){if(r instanceof jn){let e=(null==(n=null==(n=r.value.arrayValue)?void 0:n.values)?void 0:n.map(e=>R.create(r.field,"==",e)))||[];return M.create(e,"or")}return r}let e=r.filters.map(e=>t(e));return M.create(e,r.op)}(e)),y(Sa(t),7391),Ta(t)||Ea(t)?[t]:t.getFilters())}function Ta(e){return e instanceof R}function Ea(e){return e instanceof M&&On(e)}function Sa(e){return Ta(e)||Ea(e)||(e=>{if(e instanceof M&&Mn(e)){for(var t of e.getFilters())if(!Ta(t)&&!Ea(t))return!1;return!0}return!1})(e)}function xa(e,t){var r,n;return y(e instanceof R||e instanceof M,38388),y(t instanceof R||t instanceof M,25473),Ca(e instanceof R?t instanceof R?(r=e,n=t,M.create([r,n],"and")):Aa(e,t):t instanceof R?Aa(t,e):((e,t)=>{if(y(0<e.filters.length&&0<t.filters.length,48005),Rn(e)&&Rn(t))return Fn(e,t.getFilters());let r=Mn(e)?e:t,n=Mn(e)?t:e,i=r.filters.map(e=>xa(e,n));return M.create(i,"or")})(e,t))}function Aa(t,e){var r;return Rn(e)?Fn(e,t.getFilters()):(r=e.filters.map(e=>xa(t,e)),M.create(r,"or"))}function Ca(t){if(y(t instanceof R||t instanceof M,11850),t instanceof R)return t;var e=t.getFilters();if(1===e.length)return Ca(e[0]);if(Ln(t))return t;let r=e.map(e=>Ca(e)),n=[];return r.forEach(e=>{e instanceof R?n.push(e):e instanceof M&&(e.op===t.op?n.push(...e.filters):n.push(e))}),1===n.length?n[0]:M.create(n,t.op)}class Da{constructor(){this.yn=new Na}addToCollectionParentIndex(e,t){return this.yn.add(t),w.resolve()}getCollectionParents(e,t){return w.resolve(this.yn.getEntries(t))}addFieldIndex(e,t){return w.resolve()}deleteFieldIndex(e,t){return w.resolve()}deleteAllFieldIndexes(e){return w.resolve()}createTargetIndexes(e,t){return w.resolve()}getDocumentsMatchingTarget(e,t){return w.resolve(null)}getIndexType(e,t){return w.resolve(0)}getFieldIndexes(e,t){return w.resolve([])}getNextCollectionGroupToUpdate(e){return w.resolve(null)}getMinOffset(e,t){return w.resolve(Ge.min())}getMinOffsetFromCollectionGroup(e,t){return w.resolve(Ge.min())}updateCollectionGroup(e,t,r){return w.resolve()}updateIndexEntries(e,t){return w.resolve()}}class Na{constructor(){this.index={}}add(e){var t=e.lastSegment(),r=e.popLast(),n=this.index[t]||new C(T.comparator),i=!n.has(r);return this.index[t]=n.add(r),i}has(e){var t=e.lastSegment(),r=e.popLast(),t=this.index[t];return t&&t.has(r)}getEntries(e){return(this.index[e]||new C(T.comparator)).toArray()}}let ka="IndexedDbIndexManager",Ra=new Uint8Array(0);class Ma{constructor(e,t){this.databaseId=t,this.wn=new Na,this.bn=new mi(e=>Hn(e),(e,t)=>Wn(e,t)),this.uid=e.uid||""}addToCollectionParentIndex(e,t){var r,n;return this.wn.has(t)?w.resolve():(n=t.lastSegment(),r=t.popLast(),e.addOnCommittedListener(()=>{this.wn.add(t)}),n={collectionId:n,parent:o(r)},Oa(e).put(n))}getCollectionParents(e,r){let n=[],t=IDBKeyRange.bound([r,""],[Re(r),""],!1,!0);return Oa(e).J(t).next(e=>{for(var t of e){if(t.collectionId!==r)break;n.push(yt(t.parent))}return n})}addFieldIndex(e,r){let t=Fa(e),n={indexId:r.indexId,collectionGroup:r.collectionGroup,fields:r.fields.map(e=>[e.fieldPath.canonicalString(),e.kind])};delete n.indexId;var i=t.add(n);if(r.indexState){let t=Va(e);return i.next(e=>{t.put(sa(e,this.uid,r.indexState.sequenceNumber,r.indexState.offset))})}return i.next()}deleteFieldIndex(e,t){let r=Fa(e),n=Va(e),i=La(e);return r.delete(t.indexId).next(()=>n.delete(IDBKeyRange.bound([t.indexId],[t.indexId+1],!1,!0))).next(()=>i.delete(IDBKeyRange.bound([t.indexId],[t.indexId+1],!1,!0)))}deleteAllFieldIndexes(e){let t=Fa(e),r=La(e),n=Va(e);return t.X().next(()=>r.X()).next(()=>n.X())}createTargetIndexes(r,e){return w.forEach(this.Sn(e),t=>this.getIndexType(r,t).next(e=>{if(0===e||1===e){let e=new ba(t).pn();if(null!=e)return this.addFieldIndex(r,e)}}))}getDocumentsMatchingTarget(e,c){let d=La(e),r=!0,n=new Map;return w.forEach(this.Sn(c),t=>this.Dn(e,t).next(e=>{r=r&&!!e,n.set(t,e)})).next(()=>{if(r){let u=O(),h=[];return w.forEach(n,(e,t)=>{p(ka,`Using index ${r=e,`id=${r.indexId}|cg=${r.collectionGroup}|f=`+r.fields.map(e=>e.fieldPath+":"+e.kind).join(",")} to execute `+Hn(c));var r,n=((t,e)=>{var r=Ue(e);if(void 0!==r)for(let e of Jn(t,r.fieldPath))switch(e.op){case"array-contains-any":return e.value.arrayValue.values||[];case"array-contains":return[e.value]}return null})(t,e),i=((t,r)=>{var n,i=new Map;for(n of Be(r))for(let e of Jn(t,n.fieldPath))switch(e.op){case"==":case"in":i.set(n.fieldPath.canonicalString(),e.value);break;case"not-in":case"!=":return i.set(n.fieldPath.canonicalString(),e.value),Array.from(i.values())}return null})(t,e),s=((t,e)=>{var r,n=[];let i=!0;for(r of Be(e)){let e=(0===r.kind?Xn:Zn)(t,r.fieldPath,t.startAt);n.push(e.value),i=i&&e.inclusive}return new An(n,i)})(t,e),a=((t,e)=>{var r,n=[];let i=!0;for(r of Be(e)){let e=(0===r.kind?Zn:Xn)(t,r.fieldPath,t.endAt);n.push(e.value),i=i&&e.inclusive}return new An(n,i)})(t,e),o=this.vn(e,t,s),l=this.vn(e,t,a),i=this.Cn(e,t,i),n=this.Fn(e.indexId,n,o,s.inclusive,l,a.inclusive,i);return w.forEach(n,e=>d.Z(e,c.limit).next(e=>{e.forEach(e=>{var t=x.fromSegments(e.documentKey);u.has(t)||(u=u.add(t),h.push(t))})}))}).next(()=>h)}return w.resolve(null)})}Sn(t){let e=this.bn.get(t);return e||(e=0===t.filters.length?[t]:Ia(M.create(t.filters,"and")).map(e=>$n(t.path,t.collectionGroup,t.orderBy,e.getFilters(),t.limit,t.startAt,t.endAt)),this.bn.set(t,e)),e}Fn(i,s,a,o,l,u,h){let e=(null!=s?s.length:1)*Math.max(a.length,l.length),c=e/(null!=s?s.length:1),d=[];for(let f=0;f<e;++f){let t=s?this.Mn(s[f/c]):Ra,e=this.xn(i,t,a[f%c],o),r=this.On(i,t,l[f%c],u),n=h.map(e=>this.xn(i,t,e,!0));d.push(...this.createRange(e,r,n))}return d}xn(e,t,r,n){var i=new va(e,x.empty(),t,r);return n?i:i.In()}On(e,t,r,n){var i=new va(e,x.empty(),t,r);return n?i.In():i}Dn(e,t){let n=new ba(t),r=null!=t.collectionGroup?t.collectionGroup:t.path.lastSegment();return this.getFieldIndexes(e,r).next(e=>{let t=null;for(var r of e)n.Vn(r)&&(!t||r.fields.length>t.fields.length)&&(t=r);return t})}getIndexType(e,t){let r=2,n=this.Sn(t);return w.forEach(n,t=>this.Dn(e,t).next(e=>{e?0!==r&&e.fields.length<(t=>{let r=new C(v.comparator),n=!1;for(var i of t.filters)for(let e of i.getFlattenedFilters())e.field.isKeyField()||("array-contains"===e.op||"array-contains-any"===e.op?n=!0:r=r.add(e.field));for(let n of t.orderBy)n.field.isKeyField()||(r=r.add(n.field));return r.size+(n?1:0)})(t)&&(r=1):r=0})).next(()=>null!==t.limit&&1<n.length&&2===r?1:r)}Nn(e,t){var r,n=new ya;for(r of Be(e)){let e=t.data.field(r.fieldPath);if(null==e)return null;var i=n.Tn(r.kind);da.Wt.vt(e,i)}return n.cn()}Mn(e){var t=new ya;return da.Wt.vt(e,t.Tn(0)),t.cn()}Bn(e,t){var r,n=new ya;return da.Wt.vt(gn(this.databaseId,t),n.Tn(0===(r=Be(e)).length?0:r[r.length-1].kind)),n.cn()}Cn(e,n,i){if(null===i)return[];let s=[],a=(s.push(new ya),0);for(var o of Be(e)){let t=i[a++];for(let r of s)if(this.Ln(n,o.fieldPath)&&pn(t))s=this.kn(s,o,t);else{let e=r.Tn(o.kind);da.Wt.vt(t,e)}}return this.qn(s)}vn(e,t,r){return this.Cn(e,t,r.position)}qn(e){var t=[];for(let r=0;r<e.length;++r)t[r]=e[r].cn();return t}kn(r,n,e){let i=[...r],s=[];for(let r of e.arrayValue.values||[])for(let t of i){let e=new ya;e.seed(t.cn()),da.Wt.vt(r,e.Tn(n.kind)),s.push(e)}return s}Ln(e,t){return!!e.filters.find(e=>e instanceof R&&e.field.isEqual(t)&&("in"===e.op||"not-in"===e.op))}getFieldIndexes(e,t){let r=Fa(e),n=Va(e);return(t?r.J(Zt,IDBKeyRange.bound(t,t)):r.J()).next(e=>{let s=[];return w.forEach(e,i=>n.get([i.indexId,this.uid]).next(e=>{var t,r,n;s.push((t=i,r=(e=e)?new ze(e.sequenceNumber,new Ge(Xs(e.readTime),new x(yt(e.documentKey)),e.largestBatchId)):ze.empty(),n=t.fields.map(([e,t])=>new qe(v.fromServerFormat(e),t)),new Pe(t.indexId,t.collectionGroup,n,r)))})).next(()=>s)})}getNextCollectionGroupToUpdate(e){return this.getFieldIndexes(e).next(e=>0===e.length?null:(e.sort((e,t)=>{var r=e.indexState.sequenceNumber-t.indexState.sequenceNumber;return 0!=r?r:S(e.collectionGroup,t.collectionGroup)}),e[0].collectionGroup))}updateCollectionGroup(e,r,n){let i=Fa(e),s=Va(e);return this.Qn(e).next(t=>i.J(Zt,IDBKeyRange.bound(r,r)).next(e=>w.forEach(e,e=>s.put(sa(e.indexId,this.uid,t,n)))))}updateIndexEntries(i,e){let r=new Map;return w.forEach(e,(t,n)=>{var e=r.get(t.collectionGroup);return(e?w.resolve(e):this.getFieldIndexes(i,t.collectionGroup)).next(e=>(r.set(t.collectionGroup,e),w.forEach(e,r=>this.$n(i,t,r).next(e=>{var t=this.Un(n,r);return e.isEqual(t)?w.resolve():this.Kn(i,n,r,e,t)}))))})}Wn(e,t,r,n){return La(e).put({indexId:n.indexId,uid:this.uid,arrayValue:n.arrayValue,directionalValue:n.directionalValue,orderedDocumentKey:this.Bn(r,t.key),documentKey:t.key.path.toArray()})}Gn(e,t,r,n){return La(e).delete([n.indexId,this.uid,n.arrayValue,n.directionalValue,this.Bn(r,t.key),t.key.path.toArray()])}$n(e,r,n){var t=La(e);let i=new C(wa);return t.te({index:ar,range:IDBKeyRange.only([n.indexId,this.uid,this.Bn(n,r)])},(e,t)=>{i=i.add(new va(n.indexId,r,t.arrayValue,t.directionalValue))}).next(()=>i)}Un(t,r){let n=new C(wa);var i=this.Nn(r,t);if(null!=i){let e=Ue(r);if(null!=e){var s=t.data.field(e.fieldPath);if(pn(s))for(let e of s.arrayValue.values||[])n=n.add(new va(r.indexId,t.key,this.Mn(e),i))}else n=n.add(new va(r.indexId,t.key,Ra,i))}return n}Kn(t,r,s,e,a){p(ka,"Updating index entries for document '%s'",r.key);let o=[];{var l=wa,u=e=>{o.push(this.Wn(t,r,s,e))},h=e=>{o.push(this.Gn(t,r,s,e))},c=e.getIterator(),d=a.getIterator();let n=Pr(c),i=Pr(d);for(;n||i;){let t=!1,r=!1;if(n&&i){let e=l(n,i);e<0?r=!0:0<e&&(t=!0)}else null!=n?r=!0:t=!0;t?(u(i),i=Pr(d)):r?(h(n),n=Pr(c)):(n=Pr(c),i=Pr(d))}}return w.waitFor(o)}Qn(e){let n=1;return Va(e).te({index:rr,reverse:!0,range:IDBKeyRange.upperBound([this.uid,Number.MAX_SAFE_INTEGER])},(e,t,r)=>{r.done(),n=t.sequenceNumber+1}).next(()=>n)}createRange(r,n,e){e=e.sort((e,t)=>wa(e,t)).filter((e,t,r)=>!t||0!==wa(e,r[t-1]));var i=[];i.push(r);for(let s of e){let e=wa(s,r),t=wa(s,n);if(0===e)i[0]=r.In();else if(0<e&&t<0)i.push(s),i.push(s.In());else if(0<t)break}i.push(n);let s=[];for(let a=0;a<i.length;a+=2){if(this.zn(i[a],i[a+1]))return[];let e=[i[a].indexId,this.uid,i[a].arrayValue,i[a].directionalValue,Ra,[]],t=[i[a+1].indexId,this.uid,i[a+1].arrayValue,i[a+1].directionalValue,Ra,[]];s.push(IDBKeyRange.bound(e,t))}return s}zn(e,t){return 0<wa(e,t)}getMinOffsetFromCollectionGroup(e,t){return this.getFieldIndexes(e,t).next(Pa)}getMinOffset(t,e){return w.mapArray(this.Sn(e),e=>this.Dn(t,e).next(e=>e||E(44426))).next(Pa)}}function Oa(e){return r(e,$t)}function La(e){return r(e,ir)}function Fa(e){return r(e,Xt)}function Va(e){return r(e,er)}function Pa(e){y(0!==e.length,28825);let t=e[0].indexState.offset,r=t.largestBatchId;for(let i=1;i<e.length;i++){var n=e[i].indexState.offset;Qe(n,t)<0&&(t=n),r<n.largestBatchId&&(r=n.largestBatchId)}return new Ge(t.readTime,t.documentKey,r)}let Ua={didRun:!1,sequenceNumbersCollected:0,targetsRemoved:0,documentsRemoved:0};class Ba{static withCacheSize(e){return new Ba(e,Ba.DEFAULT_COLLECTION_PERCENTILE,Ba.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT)}constructor(e,t,r){this.cacheSizeCollectionThreshold=e,this.percentileToCollect=t,this.maximumSequenceNumbersToCollect=r}}function qa(t,r,n){let e=t.store(It),i=t.store(Dt),s=[],a=IDBKeyRange.only(n.batchId),o=0;var l=e.te({range:a},(e,t,r)=>(o++,r.delete()));s.push(l.next(()=>{y(1===o,47070,{batchId:n.batchId})}));let u=[];for(let t of n.mutations){let e=At(r,t.key.path,n.batchId);s.push(i.delete(e)),u.push(t.key)}return w.waitFor(s).next(()=>u)}function za(e){if(!e)return 0;let t;if(e.document)t=e.document;else if(e.unknownDocument)t=e.unknownDocument;else{if(!e.noDocument)throw E(14731);t=e.noDocument}return JSON.stringify(t).length}Ba.DEFAULT_COLLECTION_PERCENTILE=10,Ba.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT=1e3,Ba.DEFAULT=new Ba(41943040,Ba.DEFAULT_COLLECTION_PERCENTILE,Ba.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT),Ba.DISABLED=new Ba(-1,0,0);class ja{constructor(e,t,r,n){this.userId=e,this.serializer=t,this.indexManager=r,this.referenceDelegate=n,this.jn={}}static bt(e,t,r,n){y(""!==e.uid,64387);var i=e.isAuthenticated()?e.uid:"";return new ja(i,t,r,n)}checkEmpty(e){let n=!0;var t=IDBKeyRange.bound([this.userId,Number.NEGATIVE_INFINITY],[this.userId,Number.POSITIVE_INFINITY]);return Ga(e).te({index:Et,range:t},(e,t,r)=>{n=!1,r.done()}).next(()=>n)}addMutationBatch(c,d,f,g){let m=Qa(c),p=Ga(c);return p.add({}).next(t=>{y("number"==typeof t,49019);let e=new Ji(t,d,f,g),r=(i=this.serializer,s=this.userId,a=e,o=a.baseMutations.map(e=>Ls(i.wt,e)),l=a.mutations.map(e=>Ls(i.wt,e)),{userId:s,batchId:a.batchId,localWriteTimeMs:a.localWriteTime.toMillis(),baseMutations:o,mutations:l}),n=[];var i,s,a,o,l;let u=new C((e,t)=>S(e.canonicalString(),t.canonicalString()));for(let h of g){let e=At(this.userId,h.key.path,t);u=u.add(h.key.path.popLast()),n.push(p.put(r)),n.push(m.put(e,Ct))}return u.forEach(e=>{n.push(this.indexManager.addToCollectionParentIndex(c,e))}),c.addOnCommittedListener(()=>{this.jn[t]=e.keys()}),w.waitFor(n).next(()=>e)})}lookupMutationBatch(e,t){return Ga(e).get(t).next(e=>e?(y(e.userId===this.userId,48,"Unexpected user for mutation batch",{userId:e.userId,batchId:t}),Zs(this.serializer,e)):null)}Hn(e,r){return this.jn[r]?w.resolve(this.jn[r]):this.lookupMutationBatch(e,r).next(e=>{var t;return e?(t=e.keys(),this.jn[r]=t):null})}getNextMutationBatchAfterBatchId(e,t){let n=t+1,r=IDBKeyRange.lowerBound([this.userId,n]),i=null;return Ga(e).te({index:Et,range:r},(e,t,r)=>{t.userId===this.userId&&(y(t.batchId>=n,47524,{Jn:n}),i=Zs(this.serializer,t)),r.done()}).next(()=>i)}getHighestUnacknowledgedBatchId(e){var t=IDBKeyRange.upperBound([this.userId,Number.POSITIVE_INFINITY]);let n=ct;return Ga(e).te({index:Et,range:t,reverse:!0},(e,t,r)=>{n=t.batchId,r.done()}).next(()=>n)}getAllMutationBatches(e){var t=IDBKeyRange.bound([this.userId,ct],[this.userId,Number.POSITIVE_INFINITY]);return Ga(e).J(Et,t).next(e=>e.map(e=>Zs(this.serializer,e)))}getAllMutationBatchesAffectingDocumentKey(o,l){let e=xt(this.userId,l.path),t=IDBKeyRange.lowerBound(e),u=[];return Qa(o).te({range:t},(t,e,r)=>{let[n,i,s]=t,a=yt(i);if(n===this.userId&&l.path.isEqual(a))return Ga(o).get(s).next(e=>{if(!e)throw E(61480,{Yn:t,batchId:s});y(e.userId===this.userId,10503,"Unexpected user for mutation batch",{userId:e.userId,batchId:s}),u.push(Zs(this.serializer,e))});r.done()}).next(()=>u)}getAllMutationBatchesAffectingDocumentKeys(t,e){let o=new C(S),r=[];return e.forEach(a=>{var e=xt(this.userId,a.path),e=IDBKeyRange.lowerBound(e),e=Qa(t).te({range:e},(e,t,r)=>{var[n,i,s]=e,i=yt(i);n===this.userId&&a.path.isEqual(i)?o=o.add(s):r.done()});r.push(e)}),w.waitFor(r).next(()=>this.Zn(t,o))}getAllMutationBatchesAffectingQuery(e,t){let a=t.path,o=a.length+1,r=xt(this.userId,a),n=IDBKeyRange.lowerBound(r),l=new C(S);return Qa(e).te({range:n},(e,t,r)=>{var[n,i,s]=e,i=yt(i);n===this.userId&&a.isPrefixOf(i)?i.length===o&&(l=l.add(s)):r.done()}).next(()=>this.Zn(e,l))}Zn(e,t){let r=[],n=[];return t.forEach(t=>{n.push(Ga(e).get(t).next(e=>{if(null===e)throw E(35274,{batchId:t});y(e.userId===this.userId,9748,"Unexpected user for mutation batch",{userId:e.userId,batchId:t}),r.push(Zs(this.serializer,e))}))}),w.waitFor(n).next(()=>r)}removeMutationBatch(t,r){return qa(t.he,this.userId,r).next(e=>(t.addOnCommittedListener(()=>{this.Xn(r.batchId)}),w.forEach(e,e=>this.referenceDelegate.markPotentiallyOrphaned(t,e))))}Xn(e){delete this.jn[e]}performConsistencyCheck(r){return this.checkEmpty(r).next(e=>{if(!e)return w.resolve();let t=IDBKeyRange.lowerBound([this.userId]),n=[];return Qa(r).te({range:t},(t,e,r)=>{if(t[0]===this.userId){let e=yt(t[1]);n.push(e)}else r.done()}).next(()=>{y(0===n.length,56720,{er:n.map(e=>e.canonicalString())})})})}containsKey(e,t){return Ka(e,this.userId,t)}tr(e){return $a(e).get(this.userId).next(e=>e||{userId:this.userId,lastAcknowledgedBatchId:ct,lastStreamToken:""})}}function Ka(e,s,t){let r=xt(s,t.path),a=r[1],n=IDBKeyRange.lowerBound(r),o=!1;return Qa(e).te({range:n,ee:!0},(e,t,r)=>{var[n,i,,]=e;n===s&&i===a&&(o=!0),r.done()}).next(()=>o)}function Ga(e){return r(e,It)}function Qa(e){return r(e,Dt)}function $a(e){return r(e,bt)}class Ha{constructor(e){this.nr=e}next(){return this.nr+=2,this.nr}static rr(){return new Ha(0)}static ir(){return new Ha(-1)}}class Wa{constructor(e,t){this.referenceDelegate=e,this.serializer=t}allocateTargetId(r){return this.sr(r).next(e=>{var t=new Ha(e.highestTargetId);return e.highestTargetId=t.next(),this._r(r,e).next(()=>e.highestTargetId)})}getLastRemoteSnapshotVersion(e){return this.sr(e).next(e=>g.fromTimestamp(new h(e.lastRemoteSnapshotVersion.seconds,e.lastRemoteSnapshotVersion.nanoseconds)))}getHighestSequenceNumber(e){return this.sr(e).next(e=>e.highestListenSequenceNumber)}setTargetsMetadata(t,r,n){return this.sr(t).next(e=>(e.highestListenSequenceNumber=r,n&&(e.lastRemoteSnapshotVersion=n.toTimestamp()),e.highestListenSequenceNumber<r&&(e.highestListenSequenceNumber=r),this._r(t,e)))}addTargetData(t,r){return this.ar(t,r).next(()=>this.sr(t).next(e=>(e.targetCount+=1,this.ur(r,e),this._r(t,e))))}updateTargetData(e,t){return this.ar(e,t)}removeTargetData(t,e){return this.removeMatchingKeysForTargetId(t,e.targetId).next(()=>Ya(t).delete(e.targetId)).next(()=>this.sr(t)).next(e=>(y(0<e.targetCount,8065),--e.targetCount,this._r(t,e)))}removeTargets(n,i,s){let a=0,o=[];return Ya(n).te((e,t)=>{var r=ea(t);r.sequenceNumber<=i&&null===s.get(r.targetId)&&(a++,o.push(this.removeTargetData(n,r)))}).next(()=>w.waitFor(o)).next(()=>a)}forEachTarget(e,n){return Ya(e).te((e,t)=>{var r=ea(t);n(r)})}sr(e){return Ja(e).get(Gt).next(e=>(y(null!==e,2888),e))}_r(e,t){return Ja(e).put(Gt,t)}ar(e,t){return Ya(e).put(ta(this.serializer,t))}ur(e,t){let r=!1;return e.targetId>t.highestTargetId&&(t.highestTargetId=e.targetId,r=!0),t.highestListenSequenceNumber<e.sequenceNumber&&(t.highestListenSequenceNumber=e.sequenceNumber,r=!0),r}getTargetCount(e){return this.sr(e).next(e=>e.targetCount)}getTargetData(e,i){var t=Hn(i),t=IDBKeyRange.bound([t,Number.NEGATIVE_INFINITY],[t,Number.POSITIVE_INFINITY]);let s=null;return Ya(e).te({range:t,index:Ut},(e,t,r)=>{var n=ea(t);Wn(i,n.target)&&(s=n,r.done())}).next(()=>s)}addMatchingKeys(r,e,n){let i=[],s=Xa(r);return e.forEach(e=>{var t=o(e.path);i.push(s.put({targetId:n,path:t})),i.push(this.referenceDelegate.addReference(r,n,e))}),w.waitFor(i)}removeMatchingKeys(r,e,n){let i=Xa(r);return w.forEach(e,e=>{var t=o(e.path);return w.waitFor([i.delete([n,t]),this.referenceDelegate.removeReference(r,n,e)])})}removeMatchingKeysForTargetId(e,t){var r=Xa(e),n=IDBKeyRange.bound([t],[t+1],!1,!0);return r.delete(n)}getMatchingKeysForTargetId(e,t){var r=IDBKeyRange.bound([t],[t+1],!1,!0),n=Xa(e);let i=O();return n.te({range:r,ee:!0},(e,t,r)=>{var n=yt(e[1]),n=new x(n);i=i.add(n)}).next(()=>i)}containsKey(e,t){var r=o(t.path),r=IDBKeyRange.bound([r],[Re(r)],!1,!0);let n=0;return Xa(e).te({index:jt,ee:!0,range:r},([e],t,r)=>{0!==e&&(n++,r.done())}).next(()=>0<n)}Rt(e,t){return Ya(e).get(t).next(e=>e?ea(e):null)}}function Ya(e){return r(e,Pt)}function Ja(e){return r(e,Qt)}function Xa(e){return r(e,qt)}let Za="LruGarbageCollector";function eo([e,t],[r,n]){var i=S(e,r);return 0===i?S(t,n):i}class to{constructor(e){this.cr=e,this.buffer=new C(eo),this.lr=0}hr(){return++this.lr}Pr(e){var t=[e,this.hr()];if(this.buffer.size<this.cr)this.buffer=this.buffer.add(t);else{let e=this.buffer.last();eo(t,e)<0&&(this.buffer=this.buffer.delete(e).add(t))}}get maxValue(){return this.buffer.last()[0]}}class ro{constructor(e,t,r){this.garbageCollector=e,this.asyncQueue=t,this.localStore=r,this.Tr=null}start(){-1!==this.garbageCollector.params.cacheSizeCollectionThreshold&&this.Ir(6e4)}stop(){this.Tr&&(this.Tr.cancel(),this.Tr=null)}get started(){return null!==this.Tr}Ir(e){p(Za,`Garbage collection scheduled in ${e}ms`),this.Tr=this.asyncQueue.enqueueAfterDelay("lru_garbage_collection",e,async()=>{this.Tr=null;try{await this.localStore.collectGarbage(this.garbageCollector)}catch(e){rt(e)?p(Za,"Ignoring IndexedDB error during garbage collection: ",e):await We(e)}await this.Ir(3e5)})}}class no{constructor(e,t){this.Er=e,this.params=t}calculateTargetCount(e,t){return this.Er.dr(e).next(e=>Math.floor(t/100*e))}nthSequenceNumber(e,t){if(0===t)return w.resolve(ht.le);let r=new to(t);return this.Er.forEachTarget(e,e=>r.Pr(e.sequenceNumber)).next(()=>this.Er.Ar(e,e=>r.Pr(e))).next(()=>r.maxValue)}removeTargets(e,t,r){return this.Er.removeTargets(e,t,r)}removeOrphanedDocuments(e,t){return this.Er.removeOrphanedDocuments(e,t)}collect(t,r){return-1===this.params.cacheSizeCollectionThreshold?(p("LruGarbageCollector","Garbage collection skipped; disabled"),w.resolve(Ua)):this.getCacheSize(t).next(e=>e<this.params.cacheSizeCollectionThreshold?(p("LruGarbageCollector",`Garbage collection skipped; Cache size ${e} is lower than threshold `+this.params.cacheSizeCollectionThreshold),Ua):this.Rr(t,r))}getCacheSize(e){return this.Er.getCacheSize(e)}Rr(t,r){let n,i,s,a,o,l,u,h=Date.now();return this.calculateTargetCount(t,this.params.percentileToCollect).next(e=>(i=e>this.params.maximumSequenceNumbersToCollect?(p("LruGarbageCollector",`Capping sequence numbers to collect down to the maximum of ${this.params.maximumSequenceNumbersToCollect} from `+e),this.params.maximumSequenceNumbersToCollect):e,a=Date.now(),this.nthSequenceNumber(t,i))).next(e=>(n=e,o=Date.now(),this.removeTargets(t,n,r))).next(e=>(s=e,l=Date.now(),this.removeOrphanedDocuments(t,n))).next(e=>(u=Date.now(),me()<=c.DEBUG&&p("LruGarbageCollector",`LRU Garbage Collection
	Counted targets in ${a-h}ms
	Determined least recently used ${i} in `+(o-a)+"ms\n"+`	Removed ${s} targets in `+(l-o)+"ms\n"+`	Removed ${e} documents in `+(u-l)+"ms\n"+`Total Duration: ${u-h}ms`),w.resolve({didRun:!0,sequenceNumbersCollected:i,targetsRemoved:s,documentsRemoved:e})))}}function io(e,t){return new no(e,t)}class so{constructor(e,t){this.db=e,this.garbageCollector=io(this,t)}dr(e){let r=this.Vr(e);return this.db.getTargetCache().getTargetCount(e).next(t=>r.next(e=>t+e))}Vr(e){let t=0;return this.Ar(e,e=>{t++}).next(()=>t)}forEachTarget(e,t){return this.db.getTargetCache().forEachTarget(e,t)}Ar(e,r){return this.mr(e,(e,t)=>r(t))}addReference(e,t,r){return ao(e,r)}removeReference(e,t,r){return ao(e,r)}removeTargets(e,t,r){return this.db.getTargetCache().removeTargets(e,t,r)}markPotentiallyOrphaned(e,t){return ao(e,t)}gr(e,r){{var n=e,i=r;let t=!1;return $a(n).ne(e=>Ka(n,e,i).next(e=>(e&&(t=!0),w.resolve(!e)))).next(()=>t)}}removeOrphanedDocuments(r,n){let i=this.db.getRemoteDocumentCache().newChangeBuffer(),s=[],a=0;return this.mr(r,(t,e)=>{if(e<=n){let e=this.gr(r,t).next(e=>{if(!e)return a++,i.getEntry(r,t).next(()=>(i.removeEntry(t,g.min()),Xa(r).delete([0,o(t.path)])))});s.push(e)}}).next(()=>w.waitFor(s)).next(()=>i.apply(r)).next(()=>a)}removeTarget(e,t){var r=t.withSequenceNumber(e.currentSequenceNumber);return this.db.getTargetCache().updateTargetData(e,r)}updateLimboDocument(e,t){return ao(e,t)}mr(e,n){var t=Xa(e);let i,s=ht.le;return t.te({index:jt},([e],{path:t,sequenceNumber:r})=>{0===e?(s!==ht.le&&n(new x(yt(i)),s),s=r,i=t):s=ht.le}).next(()=>{s!==ht.le&&n(new x(yt(i)),s)})}getCacheSize(e){return this.db.getRemoteDocumentCache().getSize(e)}}function ao(e,t){return Xa(e).put((e=e.currentSequenceNumber,{targetId:0,path:o(t.path),sequenceNumber:e}))}class oo{constructor(){this.changes=new mi(e=>e.toString(),(e,t)=>e.isEqual(t)),this.changesApplied=!1}addEntry(e){this.assertNotApplied(),this.changes.set(e.key,e)}removeEntry(e,t){this.assertNotApplied(),this.changes.set(e,k.newInvalidDocument(e).setReadTime(t))}getEntry(e,t){this.assertNotApplied();var r=this.changes.get(t);return void 0!==r?w.resolve(r):this.getFromCache(e,t)}getEntries(e,t){return this.getAllFromCache(e,t)}apply(e){return this.assertNotApplied(),this.changesApplied=!0,this.applyChanges(e)}assertNotApplied(){}}class lo{constructor(e){this.serializer=e}setIndexManager(e){this.indexManager=e}addEntry(e,t,r){return fo(e).put(r)}removeEntry(e,t,r){return fo(e).delete((e=r,[(n=t.path.toArray()).slice(0,n.length-2),n[n.length-2],Ys(e),n[n.length-1]]));var n}updateMetadata(t,r){return this.getMetadata(t).next(e=>(e.byteSize+=r,this.pr(t,e)))}getEntry(e,r){let n=k.newInvalidDocument(r);return fo(e).te({index:Rt,range:IDBKeyRange.only(go(r))},(e,t)=>{n=this.yr(r,t)}).next(()=>n)}wr(e,r){let n={size:0,document:k.newInvalidDocument(r)};return fo(e).te({index:Rt,range:IDBKeyRange.only(go(r))},(e,t)=>{n={document:this.yr(r,t),size:za(t)}}).next(()=>n)}getEntries(e,t){let n=pi;return this.br(e,t,(e,t)=>{var r=this.yr(e,t);n=n.insert(e,r)}).next(()=>n)}Sr(e,t){let n=pi,i=new A(x.comparator);return this.br(e,t,(e,t)=>{var r=this.yr(e,t);n=n.insert(e,r),i=i.insert(e,za(t))}).next(()=>({documents:n,Dr:i}))}br(e,t,i){if(t.isEmpty())return w.resolve();let r=new C(po),n=(t.forEach(e=>r=r.add(e)),IDBKeyRange.bound(go(r.first()),go(r.last()))),s=r.getIterator(),a=s.getNext();return fo(e).te({index:Rt,range:n},(e,t,r)=>{for(var n=x.fromSegments([...t.prefixPath,t.collectionGroup,t.documentId]);a&&po(a,n)<0;)i(a,null),a=s.getNext();a&&a.isEqual(n)&&(i(a,t),a=s.hasNext()?s.getNext():null),a?r.H(go(a)):r.done()}).next(()=>{for(;a;)i(a,null),a=s.hasNext()?s.getNext():null})}getDocumentsMatchingQuery(e,n,t,i,s){var r=n.path,a=[r.popLast().toArray(),r.lastSegment(),Ys(t.readTime),t.documentKey.path.isEmpty()?"":t.documentKey.path.lastSegment()],r=[r.popLast().toArray(),r.lastSegment(),[Number.MAX_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],""];return fo(e).J(IDBKeyRange.bound(a,r,!0)).next(e=>{null!=s&&s.incrementDocumentReadCount(e.length);let t=pi;for(let r of e){let e=this.yr(x.fromSegments(r.prefixPath.concat(r.collectionGroup,r.documentId)),r);e.isFoundDocument()&&(di(n,e)||i.has(e.key))&&(t=t.insert(e.key,e))}return t})}getAllFromCollectionGroup(e,t,r,i){let s=pi;var n=mo(t,r),a=mo(t,Ge.max());return fo(e).te({index:Ot,range:IDBKeyRange.bound(n,a,!0)},(e,t,r)=>{var n=this.yr(x.fromSegments(t.prefixPath.concat(t.collectionGroup,t.documentId)),t);(s=s.insert(n.key,n)).size===i&&r.done()}).next(()=>s)}newChangeBuffer(e){return new ho(this,!!e&&e.trackRemovals)}getSize(e){return this.getMetadata(e).next(e=>e.byteSize)}getMetadata(e){return co(e).get(Vt).next(e=>(y(!!e,20021),e))}pr(e,t){return co(e).put(Vt,t)}yr(e,t){if(t){let e=((e,r)=>{let n;if(r.document)n=Os(e.wt,r.document,!!r.hasCommittedMutations);else if(r.noDocument){let e=x.fromSegments(r.noDocument.path),t=Xs(r.noDocument.readTime);n=k.newNoDocument(e,t),r.hasCommittedMutations&&n.setHasCommittedMutations()}else{if(!r.unknownDocument)return E(56709);{let e=x.fromSegments(r.unknownDocument.path),t=Xs(r.unknownDocument.version);n=k.newUnknownDocument(e,t)}}return r.readTime&&n.setReadTime((e=r.readTime,t=new h(e[0],e[1]),g.fromTimestamp(t))),n;var t})(this.serializer,t);if(!e.isNoDocument()||!e.version.isEqual(g.min()))return e}return k.newInvalidDocument(e)}}function uo(e){return new lo(e)}class ho extends oo{constructor(e,t){super(),this.vr=e,this.trackRemovals=t,this.Cr=new mi(e=>e.toString(),(e,t)=>e.isEqual(t))}applyChanges(s){let a=[],o=0,l=new C((e,t)=>S(e.canonicalString(),t.canonicalString()));return this.changes.forEach((t,r)=>{var e=this.Cr.get(t);if(a.push(this.vr.removeEntry(s,t,e.readTime)),r.isValidDocument()){var n=Ws(this.vr.serializer,r),i=(l=l.add(t.path.popLast()),za(n));o+=i-e.size,a.push(this.vr.addEntry(s,t,n))}else if(o-=e.size,this.trackRemovals){let e=Ws(this.vr.serializer,r.convertToNoDocument(g.min()));a.push(this.vr.addEntry(s,t,e))}}),l.forEach(e=>{a.push(this.vr.indexManager.addToCollectionParentIndex(s,e))}),a.push(this.vr.updateMetadata(s,o)),w.waitFor(a)}getFromCache(e,t){return this.vr.wr(e,t).next(e=>(this.Cr.set(t,{size:e.size,readTime:e.document.readTime}),e.document))}getAllFromCache(e,t){return this.vr.Sr(e,t).next(({documents:r,Dr:e})=>(e.forEach((e,t)=>{this.Cr.set(e,{size:t,readTime:r.get(e).readTime})}),r))}}function co(e){return r(e,Ft)}function fo(e){return r(e,Nt)}function go(e){var t=e.path.toArray();return[t.slice(0,t.length-2),t[t.length-2],t[t.length-1]]}function mo(e,t){var r=t.documentKey.path.toArray();return[e,Ys(t.readTime),r.slice(0,r.length-2),0<r.length?r[r.length-1]:""]}function po(e,t){var r=e.path.toArray(),n=t.path.toArray();let i=0;for(let s=0;s<r.length-2&&s<n.length-2;++s)if(i=S(r[s],n[s]))return i;return(i=S(r.length,n.length))||(i=S(r[r.length-2],n[n.length-2]))||S(r[r.length-1],n[n.length-1])}class yo{constructor(e,t){this.overlayedDocument=e,this.mutatedFields=t}}class vo{constructor(e,t,r,n){this.remoteDocumentCache=e,this.mutationQueue=t,this.documentOverlayCache=r,this.indexManager=n}getDocument(t,r){let n=null;return this.documentOverlayCache.getOverlay(t,r).next(e=>(n=e,this.remoteDocumentCache.getEntry(t,r))).next(e=>(null!==n&&zi(n.mutation,e,Ur.empty(),h.now()),e))}getDocuments(t,e){return this.remoteDocumentCache.getEntries(t,e).next(e=>this.getLocalViewOfDocuments(t,e,O()).next(()=>e))}getLocalViewOfDocuments(e,t,r=O()){let n=_i();return this.populateOverlays(e,n,t).next(()=>this.computeViews(e,t,n,r).next(e=>{let r=vi();return e.forEach((e,t)=>{r=r.insert(e,t.overlayedDocument)}),r}))}getOverlayedDocuments(e,t){let r=_i();return this.populateOverlays(e,r,t).next(()=>this.computeViews(e,t,r,O()))}populateOverlays(e,r,t){let n=[];return t.forEach(e=>{r.has(e)||n.push(e)}),this.documentOverlayCache.getOverlays(e,n).next(e=>{e.forEach((e,t)=>{r.set(e,t)})})}computeViews(e,t,n,i){let s=pi,a=_i(),o=_i();return t.forEach((e,t)=>{var r=n.get(t.key);i.has(t.key)&&(void 0===r||r.mutation instanceof Gi)?s=s.insert(t.key,t):void 0!==r?(a.set(t.key,r.mutation.getFieldMask()),zi(r.mutation,t,r.mutation.getFieldMask(),h.now())):a.set(t.key,Ur.empty())}),this.recalculateAndSaveOverlays(e,s).next(e=>(e.forEach((e,t)=>a.set(e,t)),t.forEach((e,t)=>{var r;return o.set(e,new yo(t,null!=(r=a.get(e))?r:null))}),o))}recalculateAndSaveOverlays(a,o){let l=_i(),u=new A((e,t)=>e-t),h=O();return this.mutationQueue.getAllMutationBatchesAffectingDocumentKeys(a,o).next(e=>{for(let n of e)n.keys().forEach(e=>{var t,r=o.get(e);null!==r&&(t=l.get(e)||Ur.empty(),t=n.applyToLocalView(r,t),l.set(e,t),r=(u.get(n.batchId)||O()).add(e),u=u.insert(n.batchId,r))})}).next(()=>{for(var i=[],s=u.getReverseIterator();s.hasNext();){let e=s.getNext(),t=e.key,r=e.value,n=_i();r.forEach(e=>{var t;h.has(e)||(null!==(t=qi(o.get(e),l.get(e)))&&n.set(e,t),h=h.add(e))}),i.push(this.documentOverlayCache.saveOverlays(a,t,n))}return w.waitFor(i)}).next(()=>l)}recalculateAndSaveOverlaysForDocumentKeys(t,e){return this.remoteDocumentCache.getEntries(t,e).next(e=>this.recalculateAndSaveOverlays(t,e))}getDocumentsMatchingQuery(e,t,r,n){return i=t,x.isDocumentKey(i.path)&&null===i.collectionGroup&&0===i.filters.length?this.getDocumentsMatchingDocumentQuery(e,t.path):ii(t)?this.getDocumentsMatchingCollectionGroupQuery(e,t,r,n):this.getDocumentsMatchingCollectionQuery(e,t,r,n);var i}getNextDocuments(s,t,a,o){return this.remoteDocumentCache.getAllFromCollectionGroup(s,t,a,o).next(r=>{var e=0<o-r.size?this.documentOverlayCache.getOverlaysForCollectionGroup(s,t,a.largestBatchId,o-r.size):w.resolve(_i());let n=Ve,i=r;return e.next(e=>w.forEach(e,(t,e)=>(n<e.largestBatchId&&(n=e.largestBatchId),r.get(t)?w.resolve():this.remoteDocumentCache.getEntry(s,t).next(e=>{i=i.insert(t,e)}))).next(()=>this.populateOverlays(s,e,r)).next(()=>this.computeViews(s,i,e,O())).next(e=>({batchId:n,changes:wi(e)})))})}getDocumentsMatchingDocumentQuery(e,t){return this.getDocument(e,new x(t)).next(e=>{let t=vi();return t=e.isFoundDocument()?t.insert(e.key,e):t})}getDocumentsMatchingCollectionGroupQuery(n,i,s,a){let o=i.collectionGroup,l=vi();return this.indexManager.getCollectionParents(n,o).next(e=>w.forEach(e,e=>{t=i,e=e.child(o);var t,r=new ei(e,null,t.explicitOrderBy.slice(),t.filters.slice(),t.limit,t.limitType,t.startAt,t.endAt);return this.getDocumentsMatchingCollectionQuery(n,r,s,a).next(e=>{e.forEach((e,t)=>{l=l.insert(e,t)})})}).next(()=>l))}getDocumentsMatchingCollectionQuery(t,s,r,n){let a;return this.documentOverlayCache.getOverlaysForCollection(t,s.path,r.largestBatchId).next(e=>(a=e,this.remoteDocumentCache.getDocumentsMatchingQuery(t,s,r,a,n))).next(n=>{a.forEach((e,t)=>{var r=t.getKey();null===n.get(r)&&(n=n.insert(r,k.newInvalidDocument(r)))});let i=vi();return n.forEach((e,t)=>{var r=a.get(e);void 0!==r&&zi(r.mutation,t,Ur.empty(),h.now()),di(s,t)&&(i=i.insert(e,t))}),i})}}class wo{constructor(e){this.serializer=e,this.Fr=new Map,this.Mr=new Map}getBundleMetadata(e,t){return w.resolve(this.Fr.get(t))}saveBundleMetadata(e,t){return this.Fr.set(t.id,{id:t.id,version:t.version,createTime:F(t.createTime)}),w.resolve()}getNamedQuery(e,t){return w.resolve(this.Mr.get(t))}saveNamedQuery(e,t){return this.Mr.set(t.name,{name:(t=t).name,query:ra(t.bundledQuery),readTime:F(t.readTime)}),w.resolve()}}class _o{constructor(){this.overlays=new A(x.comparator),this.Or=new Map}getOverlay(e,t){return w.resolve(this.overlays.get(t))}getOverlays(e,t){let r=_i();return w.forEach(t,t=>this.getOverlay(e,t).next(e=>{null!==e&&r.set(t,e)})).next(()=>r)}saveOverlays(r,n,e){return e.forEach((e,t)=>{this.St(r,n,t)}),w.resolve()}removeOverlaysForBatchId(e,t,r){var n=this.Or.get(r);return void 0!==n&&(n.forEach(e=>this.overlays=this.overlays.remove(e)),this.Or.delete(r)),w.resolve()}getOverlaysForCollection(e,r,n){let i=_i(),s=r.length+1,t=new x(r.child("")),a=this.overlays.getIteratorFrom(t);for(;a.hasNext();){let e=a.getNext().value,t=e.getKey();if(!r.isPrefixOf(t.path))break;t.path.length===s&&e.largestBatchId>n&&i.set(e.getKey(),e)}return w.resolve(i)}getOverlaysForCollectionGroup(e,r,n,t){let i=new A((e,t)=>e-t);for(var s=this.overlays.getIterator();s.hasNext();){let t=s.getNext().value;if(t.getKey().getCollectionGroup()===r&&t.largestBatchId>n){let e=i.get(t.largestBatchId);null===e&&(e=_i(),i=i.insert(t.largestBatchId,e)),e.set(t.getKey(),t)}}let a=_i(),o=i.getIterator();for(;o.hasNext()&&(o.getNext().value.forEach((e,t)=>a.set(e,t)),!(a.size()>=t)););return w.resolve(a)}St(e,t,r){var n=this.overlays.get(r.key);if(null!==n){let e=this.Or.get(n.largestBatchId).delete(r.key);this.Or.set(n.largestBatchId,e)}this.overlays=this.overlays.insert(r.key,new Zi(t,r));let i=this.Or.get(t);void 0===i&&(i=O(),this.Or.set(t,i)),this.Or.set(t,i.add(r.key))}}class bo{constructor(){this.sessionToken=D.EMPTY_BYTE_STRING}getSessionToken(e){return w.resolve(this.sessionToken)}setSessionToken(e,t){return this.sessionToken=t,w.resolve()}}class Io{constructor(){this.Nr=new C(l.Br),this.Lr=new C(l.kr)}isEmpty(){return this.Nr.isEmpty()}addReference(e,t){var r=new l(e,t);this.Nr=this.Nr.add(r),this.Lr=this.Lr.add(r)}qr(e,t){e.forEach(e=>this.addReference(e,t))}removeReference(e,t){this.Qr(new l(e,t))}$r(e,t){e.forEach(e=>this.removeReference(e,t))}Ur(e){let t=new x(new T([])),r=new l(t,e),n=new l(t,e+1),i=[];return this.Lr.forEachInRange([r,n],e=>{this.Qr(e),i.push(e.key)}),i}Kr(){this.Nr.forEach(e=>this.Qr(e))}Qr(e){this.Nr=this.Nr.delete(e),this.Lr=this.Lr.delete(e)}Wr(e){var t=new x(new T([])),r=new l(t,e),t=new l(t,e+1);let n=O();return this.Lr.forEachInRange([r,t],e=>{n=n.add(e.key)}),n}containsKey(e){var t=new l(e,0),t=this.Nr.firstAfterOrEqual(t);return null!==t&&e.isEqual(t.key)}}class l{constructor(e,t){this.key=e,this.Gr=t}static Br(e,t){return x.comparator(e.key,t.key)||S(e.Gr,t.Gr)}static kr(e,t){return S(e.Gr,t.Gr)||x.comparator(e.key,t.key)}}class To{constructor(e,t){this.indexManager=e,this.referenceDelegate=t,this.mutationQueue=[],this.Jn=1,this.zr=new C(l.Br)}checkEmpty(e){return w.resolve(0===this.mutationQueue.length)}addMutationBatch(e,t,r,n){var i=this.Jn,s=(this.Jn++,0<this.mutationQueue.length&&this.mutationQueue[this.mutationQueue.length-1],new Ji(i,t,r,n));this.mutationQueue.push(s);for(let t of n)this.zr=this.zr.add(new l(t.key,i)),this.indexManager.addToCollectionParentIndex(e,t.key.path.popLast());return w.resolve(s)}lookupMutationBatch(e,t){return w.resolve(this.jr(t))}getNextMutationBatchAfterBatchId(e,t){var r=this.Hr(t+1),r=r<0?0:r;return w.resolve(this.mutationQueue.length>r?this.mutationQueue[r]:null)}getHighestUnacknowledgedBatchId(){return w.resolve(0===this.mutationQueue.length?ct:this.Jn-1)}getAllMutationBatches(e){return w.resolve(this.mutationQueue.slice())}getAllMutationBatchesAffectingDocumentKey(e,t){let r=new l(t,0),n=new l(t,Number.POSITIVE_INFINITY),i=[];return this.zr.forEachInRange([r,n],e=>{var t=this.jr(e.Gr);i.push(t)}),w.resolve(i)}getAllMutationBatchesAffectingDocumentKeys(e,t){let n=new C(S);return t.forEach(e=>{var t=new l(e,0),r=new l(e,Number.POSITIVE_INFINITY);this.zr.forEachInRange([t,r],e=>{n=n.add(e.Gr)})}),w.resolve(this.Jr(n))}getAllMutationBatchesAffectingQuery(e,t){let r=t.path,n=r.length+1,i=r;x.isDocumentKey(i)||(i=i.child(""));var s=new l(new x(i),0);let a=new C(S);return this.zr.forEachWhile(e=>{var t=e.key.path;return!!r.isPrefixOf(t)&&(t.length===n&&(a=a.add(e.Gr)),!0)},s),w.resolve(this.Jr(a))}Jr(e){let r=[];return e.forEach(e=>{var t=this.jr(e);null!==t&&r.push(t)}),r}removeMutationBatch(r,n){y(0===this.Yr(n.batchId,"removed"),55003),this.mutationQueue.shift();let i=this.zr;return w.forEach(n.mutations,e=>{var t=new l(e.key,n.batchId);return i=i.delete(t),this.referenceDelegate.markPotentiallyOrphaned(r,e.key)}).next(()=>{this.zr=i})}Xn(e){}containsKey(e,t){var r=new l(t,0),r=this.zr.firstAfterOrEqual(r);return w.resolve(t.isEqual(r&&r.key))}performConsistencyCheck(e){return this.mutationQueue.length,w.resolve()}Yr(e,t){return this.Hr(e)}Hr(e){return 0===this.mutationQueue.length?0:e-this.mutationQueue[0].batchId}jr(e){var t=this.Hr(e);return t<0||t>=this.mutationQueue.length?null:this.mutationQueue[t]}}class Eo{constructor(e){this.Zr=e,this.docs=new A(x.comparator),this.size=0}setIndexManager(e){this.indexManager=e}addEntry(e,t){var r=t.key,n=this.docs.get(r),n=n?n.size:0,i=this.Zr(t);return this.docs=this.docs.insert(r,{document:t.mutableCopy(),size:i}),this.size+=i-n,this.indexManager.addToCollectionParentIndex(e,r.path.popLast())}removeEntry(e){var t=this.docs.get(e);t&&(this.docs=this.docs.remove(e),this.size-=t.size)}getEntry(e,t){var r=this.docs.get(t);return w.resolve(r?r.document.mutableCopy():k.newInvalidDocument(t))}getEntries(e,t){let r=pi;return t.forEach(e=>{var t=this.docs.get(e);r=r.insert(e,t?t.document.mutableCopy():k.newInvalidDocument(e))}),w.resolve(r)}getDocumentsMatchingQuery(e,r,n,i){let s=pi,a=r.path,t=new x(a.child("__id-9223372036854775808__")),o=this.docs.getIteratorFrom(t);for(;o.hasNext();){let{key:e,value:{document:t}}=o.getNext();if(!a.isPrefixOf(e.path))break;e.path.length>a.length+1||Qe(Ke(t),n)<=0||(i.has(t.key)||di(r,t))&&(s=s.insert(t.key,t.mutableCopy()))}return w.resolve(s)}getAllFromCollectionGroup(e,t,r,n){E(9500)}Xr(e,t){return w.forEach(this.docs,e=>t(e))}newChangeBuffer(e){return new So(this)}getSize(e){return w.resolve(this.size)}}class So extends oo{constructor(e){super(),this.vr=e}applyChanges(r){let n=[];return this.changes.forEach((e,t)=>{t.isValidDocument()?n.push(this.vr.addEntry(r,t)):this.vr.removeEntry(e)}),w.waitFor(n)}getFromCache(e,t){return this.vr.getEntry(e,t)}getAllFromCache(e,t){return this.vr.getEntries(e,t)}}class xo{constructor(e){this.persistence=e,this.ei=new mi(e=>Hn(e),Wn),this.lastRemoteSnapshotVersion=g.min(),this.highestTargetId=0,this.ti=0,this.ni=new Io,this.targetCount=0,this.ri=Ha.rr()}forEachTarget(e,r){return this.ei.forEach((e,t)=>r(t)),w.resolve()}getLastRemoteSnapshotVersion(e){return w.resolve(this.lastRemoteSnapshotVersion)}getHighestSequenceNumber(e){return w.resolve(this.ti)}allocateTargetId(e){return this.highestTargetId=this.ri.next(),w.resolve(this.highestTargetId)}setTargetsMetadata(e,t,r){return r&&(this.lastRemoteSnapshotVersion=r),t>this.ti&&(this.ti=t),w.resolve()}ar(e){this.ei.set(e.target,e);var t=e.targetId;t>this.highestTargetId&&(this.ri=new Ha(t),this.highestTargetId=t),e.sequenceNumber>this.ti&&(this.ti=e.sequenceNumber)}addTargetData(e,t){return this.ar(t),this.targetCount+=1,w.resolve()}updateTargetData(e,t){return this.ar(t),w.resolve()}removeTargetData(e,t){return this.ei.delete(t.target),this.ni.Ur(t.targetId),--this.targetCount,w.resolve()}removeTargets(r,n,i){let s=0,a=[];return this.ei.forEach((e,t)=>{t.sequenceNumber<=n&&null===i.get(t.targetId)&&(this.ei.delete(e),a.push(this.removeMatchingKeysForTargetId(r,t.targetId)),s++)}),w.waitFor(a).next(()=>s)}getTargetCount(e){return w.resolve(this.targetCount)}getTargetData(e,t){var r=this.ei.get(t)||null;return w.resolve(r)}addMatchingKeys(e,t,r){return this.ni.qr(t,r),w.resolve()}removeMatchingKeys(t,e,r){this.ni.$r(e,r);let n=this.persistence.referenceDelegate,i=[];return n&&e.forEach(e=>{i.push(n.markPotentiallyOrphaned(t,e))}),w.waitFor(i)}removeMatchingKeysForTargetId(e,t){return this.ni.Ur(t),w.resolve()}getMatchingKeysForTargetId(e,t){var r=this.ni.Wr(t);return w.resolve(r)}containsKey(e,t){return w.resolve(this.ni.containsKey(t))}}class Ao{constructor(e,t){this.ii={},this.overlays={},this.si=new ht(0),this.oi=!1,this.oi=!0,this._i=new bo,this.referenceDelegate=e(this),this.ai=new xo(this),this.indexManager=new Da,this.remoteDocumentCache=(e=e=>this.referenceDelegate.ui(e),new Eo(e)),this.serializer=new Hs(t),this.ci=new wo(this.serializer)}start(){return Promise.resolve()}shutdown(){return this.oi=!1,Promise.resolve()}get started(){return this.oi}setDatabaseDeletedListener(){}setNetworkEnabled(){}getIndexManager(e){return this.indexManager}getDocumentOverlayCache(e){let t=this.overlays[e.toKey()];return t||(t=new _o,this.overlays[e.toKey()]=t),t}getMutationQueue(e,t){let r=this.ii[e.toKey()];return r||(r=new To(t,this.referenceDelegate),this.ii[e.toKey()]=r),r}getGlobalsCache(){return this._i}getTargetCache(){return this.ai}getRemoteDocumentCache(){return this.remoteDocumentCache}getBundleCache(){return this.ci}runTransaction(e,t,r){p("MemoryPersistence","Starting transaction:",e);let n=new Co(this.si.next());return this.referenceDelegate.li(),r(n).next(e=>this.referenceDelegate.hi(n).next(()=>e)).toPromise().then(e=>(n.raiseOnCommittedEvent(),e))}Pi(t,r){return w.or(Object.values(this.ii).map(e=>()=>e.containsKey(t,r)))}}class Co extends He{constructor(e){super(),this.currentSequenceNumber=e}}class Do{constructor(e){this.persistence=e,this.Ti=new Io,this.Ii=null}static Ei(e){return new Do(e)}get di(){if(this.Ii)return this.Ii;throw E(60996)}addReference(e,t,r){return this.Ti.addReference(r,t),this.di.delete(r.toString()),w.resolve()}removeReference(e,t,r){return this.Ti.removeReference(r,t),this.di.add(r.toString()),w.resolve()}markPotentiallyOrphaned(e,t){return this.di.add(t.toString()),w.resolve()}removeTarget(e,t){this.Ti.Ur(t.targetId).forEach(e=>this.di.add(e.toString()));let r=this.persistence.getTargetCache();return r.getMatchingKeysForTargetId(e,t.targetId).next(e=>{e.forEach(e=>this.di.add(e.toString()))}).next(()=>r.removeTargetData(e,t))}li(){this.Ii=new Set}hi(r){let n=this.persistence.getRemoteDocumentCache().newChangeBuffer();return w.forEach(this.di,e=>{let t=x.fromPath(e);return this.Ai(r,t).next(e=>{e||n.removeEntry(t,g.min())})}).next(()=>(this.Ii=null,n.apply(r)))}updateLimboDocument(e,t){return this.Ai(e,t).next(e=>{e?this.di.delete(t.toString()):this.di.add(t.toString())})}ui(e){return 0}Ai(e,t){return w.or([()=>w.resolve(this.Ti.containsKey(t)),()=>this.persistence.getTargetCache().containsKey(e,t),()=>this.persistence.Pi(e,t)])}}class No{constructor(e,t){this.persistence=e,this.Ri=new mi(e=>o(e.path),(e,t)=>e.isEqual(t)),this.garbageCollector=io(this,t)}static Ei(e,t){return new No(e,t)}li(){}hi(e){return w.resolve()}forEachTarget(e,t){return this.persistence.getTargetCache().forEachTarget(e,t)}dr(e){let r=this.Vr(e);return this.persistence.getTargetCache().getTargetCount(e).next(t=>r.next(e=>t+e))}Vr(e){let t=0;return this.Ar(e,e=>{t++}).next(()=>t)}Ar(r,n){return w.forEach(this.Ri,(e,t)=>this.gr(r,e,t).next(e=>e?w.resolve():n(t)))}removeTargets(e,t,r){return this.persistence.getTargetCache().removeTargets(e,t,r)}removeOrphanedDocuments(e,r){let n=0,t=this.persistence.getRemoteDocumentCache(),i=t.newChangeBuffer();return t.Xr(e,t=>this.gr(e,t,r).next(e=>{e||(n++,i.removeEntry(t,g.min()))})).next(()=>i.apply(e)).next(()=>n)}markPotentiallyOrphaned(e,t){return this.Ri.set(t,e.currentSequenceNumber),w.resolve()}removeTarget(e,t){var r=t.withSequenceNumber(e.currentSequenceNumber);return this.persistence.getTargetCache().updateTargetData(e,r)}addReference(e,t,r){return this.Ri.set(r,e.currentSequenceNumber),w.resolve()}removeReference(e,t,r){return this.Ri.set(r,e.currentSequenceNumber),w.resolve()}updateLimboDocument(e,t){return this.Ri.set(t,e.currentSequenceNumber),w.resolve()}ui(e){let t=e.key.toString().length;return e.isFoundDocument()&&(t+=function n(e){switch(on(e)){case 0:case 1:return 4;case 2:return 8;case 3:case 8:return 16;case 4:var t=Wr(e);return t?16+n(t):16;case 5:return 2*e.stringValue.length;case 6:return jr(e.bytesValue).approximateByteSize();case 7:return e.referenceValue.length;case 9:return(e.arrayValue.values||[]).reduce((e,t)=>e+n(t),0);case 10:case 11:{var i=e.mapValue;let r=0;return Mr(i.fields,(e,t)=>{r+=e.length+n(t)}),r}default:throw E(13486,{value:e})}}(e.data.value)),t}gr(e,t,r){return w.or([()=>this.persistence.Pi(e,t),()=>this.persistence.getTargetCache().containsKey(e,t),()=>{var e=this.Ri.get(t);return w.resolve(void 0!==e&&r<e)}])}getCacheSize(e){return this.persistence.getRemoteDocumentCache().getSize(e)}}class ko{constructor(e){this.serializer=e}q(t,e,r,n){let s=new Je("createOrUpgrade",e);var i;r<1&&1<=n&&(t.createObjectStore(wt),(i=t).createObjectStore(bt,{keyPath:"userId"}),i.createObjectStore(It,{keyPath:Tt,autoIncrement:!0}).createIndex(Et,St,{unique:!0}),i.createObjectStore(Dt),Ro(t),t.createObjectStore(vt));let a=w.resolve();return r<3&&3<=n&&(0!==r&&((i=t).deleteObjectStore(qt),i.deleteObjectStore(Pt),i.deleteObjectStore(Qt),Ro(t)),a=a.next(()=>{return e=s,t=e.store(Qt),r={highestTargetId:0,highestListenSequenceNumber:0,lastRemoteSnapshotVersion:g.min().toTimestamp(),targetCount:0},t.put(Gt,r);var e,t,r})),r<4&&4<=n&&(a=(a=0!==r?a.next(()=>{return n=t,(i=s).store(It).J().next(e=>{n.deleteObjectStore(It),n.createObjectStore(It,{keyPath:Tt,autoIncrement:!0}).createIndex(Et,St,{unique:!0});let t=i.store(It),r=e.map(e=>t.put(e));return w.waitFor(r)});var n,i}):a).next(()=>{t.createObjectStore(Wt,{keyPath:"clientId"})})),r<5&&5<=n&&(a=a.next(()=>this.Vi(s))),r<6&&6<=n&&(a=a.next(()=>(t.createObjectStore(Ft),this.mi(s)))),r<7&&7<=n&&(a=a.next(()=>this.fi(s))),r<8&&8<=n&&(a=a.next(()=>this.gi(t,s))),r<9&&9<=n&&(a=a.next(()=>{var e;(e=t).objectStoreNames.contains("remoteDocumentChanges")&&e.deleteObjectStore("remoteDocumentChanges")})),r<10&&10<=n&&(a=a.next(()=>this.pi(s))),r<11&&11<=n&&(a=a.next(()=>{t.createObjectStore(Yt,{keyPath:"bundleId"}),t.createObjectStore(Jt,{keyPath:"name"})})),r<12&&12<=n&&(a=a.next(()=>{var e;(e=t.createObjectStore(lr,{keyPath:ur})).createIndex(hr,cr,{unique:!1}),e.createIndex(dr,fr,{unique:!1})})),r<13&&13<=n&&(a=a.next(()=>{var e;(e=t.createObjectStore(Nt,{keyPath:kt})).createIndex(Rt,Mt),e.createIndex(Ot,Lt)}).next(()=>this.yi(t,s)).next(()=>t.deleteObjectStore(vt))),r<14&&14<=n&&(a=a.next(()=>this.wi(t,s))),r<15&&15<=n&&(a=a.next(()=>{var e;(e=t).createObjectStore(Xt,{keyPath:"indexId",autoIncrement:!0}).createIndex(Zt,"collectionGroup",{unique:!1}),e.createObjectStore(er,{keyPath:tr}).createIndex(rr,nr,{unique:!1}),e.createObjectStore(ir,{keyPath:sr}).createIndex(ar,or,{unique:!1})})),r<16&&16<=n&&(a=a.next(()=>{e.objectStore(er).clear()}).next(()=>{e.objectStore(ir).clear()})),a=r<17&&17<=n?a.next(()=>{t.createObjectStore(gr,{keyPath:"name"})}):a}mi(t){let r=0;return t.store(vt).te((e,t)=>{r+=za(t)}).next(()=>{var e={byteSize:r};return t.store(Ft).put(Vt,e)})}Vi(n){let e=n.store(bt),t=n.store(It);return e.J().next(e=>w.forEach(e,r=>{var e=IDBKeyRange.bound([r.userId,ct],[r.userId,r.lastAcknowledgedBatchId]);return t.J(Et,e).next(e=>w.forEach(e,e=>{y(e.userId===r.userId,18650,"Cannot process batch from unexpected user",{batchId:e.batchId});var t=Zs(this.serializer,e);return qa(n,r.userId,t).next(()=>{})}))}))}fi(e){let a=e.store(qt),t=e.store(vt);return e.store(Qt).get(Gt).next(i=>{let s=[];return t.te((e,t)=>{let r=new T(e),n=[0,o(r)];s.push(a.get(n).next(e=>e?w.resolve():(e=r,a.put({targetId:0,path:o(e),sequenceNumber:i.highestListenSequenceNumber}))))}).next(()=>w.waitFor(s))})}gi(e,t){e.createObjectStore($t,{keyPath:Ht});let n=t.store($t),i=new Na,s=r=>{if(i.add(r)){let e=r.lastSegment(),t=r.popLast();return n.put({collectionId:e,parent:o(t)})}};return t.store(vt).te({ee:!0},(e,t)=>{var r=new T(e);return s(r.popLast())}).next(()=>t.store(Dt).te({ee:!0},([,e],t)=>{var r=yt(e);return s(r.popLast())}))}pi(e){let n=e.store(Pt);return n.te((e,t)=>{var r=ea(t),r=ta(this.serializer,r);return n.put(r)})}yi(e,s){let t=s.store(vt),a=[];return t.te((e,t)=>{var r,n=s.store(Nt),i=((r=t).document?new x(T.fromString(r.document.name).popFirst(5)):r.noDocument?x.fromSegments(r.noDocument.path):r.unknownDocument?x.fromSegments(r.unknownDocument.path):E(36783)).path.toArray(),i={prefixPath:i.slice(0,i.length-2),collectionGroup:i[i.length-2],documentId:i[i.length-1],readTime:t.readTime||[0,0],unknownDocument:t.unknownDocument,noDocument:t.noDocument,document:t.document,hasCommittedMutations:!!t.hasCommittedMutations};a.push(n.put(i))}).next(()=>w.waitFor(a))}wi(e,s){let t=s.store(It),a=uo(this.serializer),o=new Ao(Do.Ei,this.serializer.wt);return t.J().next(e=>{let n=new Map;return e.forEach(e=>{let t,r=null!=(t=n.get(e.userId))?t:O();Zs(this.serializer,e).keys().forEach(e=>r=r.add(e)),n.set(e.userId,r)}),w.forEach(n,(e,t)=>{var r=new u(t),n=ua.bt(this.serializer,r),i=o.getIndexManager(r),r=ja.bt(r,this.serializer,i,o.referenceDelegate);return new vo(a,r,n,i).recalculateAndSaveOverlaysForDocumentKeys(new kr(s,ht.le),e).next()})})}}function Ro(e){e.createObjectStore(qt,{keyPath:zt}).createIndex(jt,Kt,{unique:!0}),e.createObjectStore(Pt,{keyPath:"targetId"}).createIndex(Ut,Bt,{unique:!0}),e.createObjectStore(Qt)}let Mo="IndexedDbPersistence",Oo="Failed to obtain exclusive access to the persistence layer. To allow shared access, multi-tab synchronization has to be enabled in all tabs. If you are using `experimentalForceOwningTab:true`, make sure that only one tab has persistence enabled at any given time.";class Lo{constructor(e,t,r,n,i,s,a,o,l,u,h=17){if(this.allowTabSynchronization=e,this.persistenceKey=t,this.clientId=r,this.bi=i,this.window=s,this.document=a,this.Si=l,this.Di=u,this.Ci=h,this.si=null,this.oi=!1,this.isPrimary=!1,this.networkEnabled=!0,this.Fi=null,this.inForeground=!1,this.Mi=null,this.xi=null,this.Oi=Number.NEGATIVE_INFINITY,this.Ni=e=>Promise.resolve(),!Lo.C())throw new I(b.UNIMPLEMENTED,"This platform is either missing IndexedDB or is known to have an incomplete implementation. Offline persistence has been disabled.");this.referenceDelegate=new so(this,n),this.Bi=t+"main",this.serializer=new Hs(o),this.Li=new Xe(this.Bi,this.Ci,new ko(this.serializer)),this._i=new ca,this.ai=new Wa(this.referenceDelegate,this.serializer),this.remoteDocumentCache=uo(this.serializer),this.ci=new aa,this.window&&this.window.localStorage?this.ki=this.window.localStorage:(this.ki=null,!1===u&&d(Mo,"LocalStorage is unavailable. As a result, persistence may not work reliably. In particular enablePersistence() could fail immediately after refreshing the page."))}start(){return this.qi().then(()=>{if(this.isPrimary||this.allowTabSynchronization)return this.Qi(),this.$i(),this.Ui(),this.runTransaction("getHighestListenSequenceNumber","readonly",e=>this.ai.getHighestSequenceNumber(e));throw new I(b.FAILED_PRECONDITION,Oo)}).then(e=>{this.si=new ht(e,this.Si)}).then(()=>{this.oi=!0}).catch(e=>(this.Li&&this.Li.close(),Promise.reject(e)))}Ki(t){return this.Ni=async e=>{if(this.started)return t(e)},t(this.isPrimary)}setDatabaseDeletedListener(t){this.Li.U(async e=>{null===e.newVersion&&await t()})}setNetworkEnabled(e){this.networkEnabled!==e&&(this.networkEnabled=e,this.bi.enqueueAndForget(async()=>{this.started&&await this.qi()}))}qi(){return this.runTransaction("updateClientMetadataAndTryBecomePrimary","readwrite",t=>Vo(t).put({clientId:this.clientId,updateTimeMs:Date.now(),networkEnabled:this.networkEnabled,inForeground:this.inForeground}).next(()=>{if(this.isPrimary)return this.Wi(t).next(e=>{e||(this.isPrimary=!1,this.bi.enqueueRetryable(()=>this.Ni(!1)))})}).next(()=>this.Gi(t)).next(e=>this.isPrimary&&!e?this.zi(t).next(()=>!1):!!e&&this.ji(t).next(()=>!0))).catch(e=>{if(rt(e))return p(Mo,"Failed to extend owner lease: ",e),this.isPrimary;if(this.allowTabSynchronization)return p(Mo,"Releasing owner lease after error during lease refresh",e),!1;throw e}).then(e=>{this.isPrimary!==e&&this.bi.enqueueRetryable(()=>this.Ni(e)),this.isPrimary=e})}Wi(e){return Fo(e).get(_t).next(e=>w.resolve(this.Hi(e)))}Ji(e){return Vo(e).delete(this.clientId)}async Yi(){if(this.isPrimary&&!this.Zi(this.Oi,18e5)){this.Oi=Date.now();var e=await this.runTransaction("maybeGarbageCollectMultiClientState","readwrite-primary",e=>{let n=r(e,Wt);return n.J().next(e=>{let t=this.Xi(e,18e5),r=e.filter(e=>-1===t.indexOf(e));return w.forEach(r,e=>n.delete(e.clientId)).next(()=>r)})}).catch(()=>[]);if(this.ki)for(var t of e)this.ki.removeItem(this.es(t.clientId))}}Ui(){this.xi=this.bi.enqueueAfterDelay("client_metadata_refresh",4e3,()=>this.qi().then(()=>this.Yi()).then(()=>this.Ui()))}Hi(e){return!!e&&e.ownerId===this.clientId}Gi(t){return this.Di?w.resolve(!0):Fo(t).get(_t).next(e=>{if(null!==e&&this.Zi(e.leaseTimestampMs,5e3)&&!this.ts(e.ownerId)){if(this.Hi(e)&&this.networkEnabled)return!0;if(!this.Hi(e)){if(e.allowTabSynchronization)return!1;throw new I(b.FAILED_PRECONDITION,Oo)}}return!(!this.networkEnabled||!this.inForeground)||Vo(t).J().next(e=>void 0===this.Xi(e,5e3).find(e=>{if(this.clientId!==e.clientId){var t=!this.networkEnabled&&e.networkEnabled,r=!this.inForeground&&e.inForeground,n=this.networkEnabled===e.networkEnabled;if(t||r&&n)return!0}return!1}))}).next(e=>(this.isPrimary!==e&&p(Mo,`Client ${e?"is":"is not"} eligible for a primary lease.`),e))}async shutdown(){this.oi=!1,this.ns(),this.xi&&(this.xi.cancel(),this.xi=null),this.rs(),this.ss(),await this.Li.runTransaction("shutdown","readwrite",[wt,Wt],e=>{let t=new kr(e,ht.le);return this.zi(t).next(()=>this.Ji(t))}),this.Li.close(),this._s()}Xi(e,t){return e.filter(e=>this.Zi(e.updateTimeMs,t)&&!this.ts(e.clientId))}us(){return this.runTransaction("getActiveClients","readonly",e=>Vo(e).J().next(e=>this.Xi(e,18e5).map(e=>e.clientId)))}get started(){return this.oi}getGlobalsCache(){return this._i}getMutationQueue(e,t){return ja.bt(e,this.serializer,t,this.referenceDelegate)}getTargetCache(){return this.ai}getRemoteDocumentCache(){return this.remoteDocumentCache}getIndexManager(e){return new Ma(e,this.serializer.wt.databaseId)}getDocumentOverlayCache(e){return ua.bt(this.serializer,e)}getBundleCache(){return this.ci}runTransaction(t,r,n){p(Mo,"Starting transaction:",t);var e,i="readonly"===r?"readonly":"readwrite",s=17===(e=this.Ci)?Nr:16===e?Dr:15===e?Cr:14===e?Ar:13===e?xr:12===e?Sr:11===e?mr:void E(60245);let a;return this.Li.runTransaction(t,i,s,e=>(a=new kr(e,this.si?this.si.next():ht.le),"readwrite-primary"===r?this.Wi(a).next(e=>!!e||this.Gi(a)).next(e=>{if(e)return n(a);throw d(`Failed to obtain primary lease for action '${t}'.`),this.isPrimary=!1,this.bi.enqueueRetryable(()=>this.Ni(!1)),new I(b.FAILED_PRECONDITION,$e)}).next(e=>this.ji(a).next(()=>e)):this.cs(a).next(()=>n(a)))).then(e=>(a.raiseOnCommittedEvent(),e))}cs(e){return Fo(e).get(_t).next(e=>{if(null!==e&&this.Zi(e.leaseTimestampMs,5e3)&&!this.ts(e.ownerId)&&!this.Hi(e)&&!(this.Di||this.allowTabSynchronization&&e.allowTabSynchronization))throw new I(b.FAILED_PRECONDITION,Oo)})}ji(e){var t={ownerId:this.clientId,allowTabSynchronization:this.allowTabSynchronization,leaseTimestampMs:Date.now()};return Fo(e).put(_t,t)}static C(){return Xe.C()}zi(e){let t=Fo(e);return t.get(_t).next(e=>this.Hi(e)?(p(Mo,"Releasing primary lease."),t.delete(_t)):w.resolve())}Zi(e,t){var r=Date.now();return!(e<r-t||r<e&&(d(`Detected an update time that is in the future: ${e} > `+r),1))}Qi(){null!==this.document&&"function"==typeof this.document.addEventListener&&(this.Mi=()=>{this.bi.enqueueAndForget(()=>(this.inForeground="visible"===this.document.visibilityState,this.qi()))},this.document.addEventListener("visibilitychange",this.Mi),this.inForeground="visible"===this.document.visibilityState)}rs(){this.Mi&&(this.document.removeEventListener("visibilitychange",this.Mi),this.Mi=null)}$i(){var e;"function"==typeof(null==(e=this.window)?void 0:e.addEventListener)&&(this.Fi=()=>{this.ns();var e=/(?:Version|Mobile)\/1[456]/;J()&&(navigator.appVersion.match(e)||navigator.userAgent.match(e))&&this.bi.enterRestrictedMode(!0),this.bi.enqueueAndForget(()=>this.shutdown())},this.window.addEventListener("pagehide",this.Fi))}ss(){this.Fi&&(this.window.removeEventListener("pagehide",this.Fi),this.Fi=null)}ts(e){var t;try{var r=null!==(null==(t=this.ki)?void 0:t.getItem(this.es(e)));return p(Mo,`Client '${e}' ${r?"is":"is not"} zombied in LocalStorage`),r}catch(e){return d(Mo,"Failed to get zombied client id.",e),!1}}ns(){if(this.ki)try{this.ki.setItem(this.es(this.clientId),String(Date.now()))}catch(e){d("Failed to set zombie client id.",e)}}_s(){if(this.ki)try{this.ki.removeItem(this.es(this.clientId))}catch(e){}}es(e){return`firestore_zombie_${this.persistenceKey}_`+e}}function Fo(e){return r(e,wt)}function Vo(e){return r(e,Wt)}function Po(e,t){let r=e.projectId;return e.isDefaultDatabase||(r+="."+e.database),"firestore/"+t+"/"+r+"/"}class Uo{constructor(e,t,r,n){this.targetId=e,this.fromCache=t,this.ls=r,this.hs=n}static Ps(e,t){let r=O(),n=O();for(let e of t.docChanges)switch(e.type){case 0:r=r.add(e.doc.key);break;case 1:n=n.add(e.doc.key)}return new Uo(e,t.fromCache,r,n)}}class Bo{constructor(){this._documentReadCount=0}get documentReadCount(){return this._documentReadCount}incrementDocumentReadCount(e){this._documentReadCount+=e}}class qo{constructor(){this.Ts=!1,this.Is=!1,this.Es=100,this.ds=J()?8:0<Ze(Y())?6:4}initialize(e,t){this.As=e,this.indexManager=t,this.Ts=!0}getDocumentsMatchingQuery(r,n,e,t){let i={result:null};return this.Rs(r,n).next(e=>{i.result=e}).next(()=>{if(!i.result)return this.Vs(r,n,t,e).next(e=>{i.result=e})}).next(()=>{if(!i.result){let t=new Bo;return this.fs(r,n,t).next(e=>{if(i.result=e,this.Is)return this.gs(r,n,t,e.size)})}}).next(()=>i.result)}gs(e,t,r,n){return r.documentReadCount<this.Es?(me()<=c.DEBUG&&p("QueryEngine","SDK will not create cache indexes for query:",ci(t),"since it only creates cache indexes for collection contains","more than or equal to",this.Es,"documents"),w.resolve()):(me()<=c.DEBUG&&p("QueryEngine","Query:",ci(t),"scans",r.documentReadCount,"local documents and returns",n,"documents as results."),r.documentReadCount>this.ds*n?(me()<=c.DEBUG&&p("QueryEngine","The SDK decides to create cache indexes for query:",ci(t),"as using cache indexes may help improve performance."),this.indexManager.createTargetIndexes(e,ai(t))):w.resolve())}Rs(i,s){if(ni(s))return w.resolve(null);let t=ai(s);return this.indexManager.getIndexType(i,t).next(e=>0===e?null:(null!==s.limit&&1===e&&(s=li(s,null,"F"),t=ai(s)),this.indexManager.getDocumentsMatchingTarget(i,t).next(e=>{let n=O(...e);return this.As.getDocuments(i,n).next(r=>this.indexManager.getMinOffset(i,t).next(e=>{var t=this.ps(s,r);return this.ys(s,t,n,e.readTime)?this.Rs(i,li(s,null,"F")):this.ws(i,t,s,e)}))})))}Vs(r,n,i,s){return ni(n)||s.isEqual(g.min())?w.resolve(null):this.As.getDocuments(r,i).next(e=>{var t=this.ps(n,e);return this.ys(n,t,i,s)?w.resolve(null):(me()<=c.DEBUG&&p("QueryEngine","Re-using previous result from %s to execute query: %s",s.toString(),ci(n)),this.ws(r,t,n,je(s,Ve)).next(e=>e))})}ps(r,e){let n=new C(gi(r));return e.forEach((e,t)=>{di(r,t)&&(n=n.add(t))}),n}ys(e,t,r,n){var i;return null!==e.limit&&(r.size!==t.size||!!(i="F"===e.limitType?t.last():t.first())&&(i.hasPendingWrites||0<i.version.compareTo(n)))}fs(e,t,r){return me()<=c.DEBUG&&p("QueryEngine","Using full collection scan to execute query:",ci(t)),this.As.getDocumentsMatchingQuery(e,t,Ge.min(),r)}ws(e,r,t,n){return this.As.getDocumentsMatchingQuery(e,t,n).next(t=>(r.forEach(e=>{t=t.insert(e.key,e)}),t))}}let zo="LocalStore",jo=3e8;class Ko{constructor(e,t,r,n){this.persistence=e,this.bs=t,this.serializer=n,this.Ss=new A(S),this.Ds=new mi(e=>Hn(e),Wn),this.vs=new Map,this.Cs=e.getRemoteDocumentCache(),this.ai=e.getTargetCache(),this.ci=e.getBundleCache(),this.Fs(r)}Fs(e){this.documentOverlayCache=this.persistence.getDocumentOverlayCache(e),this.indexManager=this.persistence.getIndexManager(e),this.mutationQueue=this.persistence.getMutationQueue(e,this.indexManager),this.localDocuments=new vo(this.Cs,this.mutationQueue,this.documentOverlayCache,this.indexManager),this.Cs.setIndexManager(this.indexManager),this.bs.initialize(this.localDocuments,this.indexManager)}collectGarbage(t){return this.persistence.runTransaction("Collect garbage","readwrite-primary",e=>t.collect(e,this.Ss))}}function Go(e,t,r,n){return new Ko(e,t,r,n)}async function Qo(e,t){let o=e;return o.persistence.runTransaction("Handle user change","readonly",s=>{let a;return o.mutationQueue.getAllMutationBatches(s).next(e=>(a=e,o.Fs(t),o.mutationQueue.getAllMutationBatches(s))).next(e=>{let t=[],r=[],n=O();for(let i of a){t.push(i.batchId);for(let e of i.mutations)n=n.add(e.key)}for(let i of e){r.push(i.batchId);for(let e of i.mutations)n=n.add(e.key)}return o.localDocuments.getDocuments(s,n).next(e=>({Ms:e,removedBatchIds:t,addedBatchIds:r}))})})}function $o(e,n){let i=e;return i.persistence.runTransaction("Acknowledge batch","readwrite-primary",e=>{let t=n.batch.keys(),r=i.Cs.newChangeBuffer({trackRemovals:!0});return((e,t,n,i)=>{let s=n.batch,r=s.keys(),a=w.resolve();return r.forEach(r=>{a=a.next(()=>i.getEntry(t,r)).next(e=>{var t=n.docVersions.get(r);y(null!==t,48541),e.version.compareTo(t)<0&&(s.applyToRemoteDocument(e,n),e.isValidDocument())&&(e.setReadTime(n.commitVersion),i.addEntry(e))})}),a.next(()=>e.mutationQueue.removeMutationBatch(t,s))})(i,e,n,r).next(()=>r.apply(e)).next(()=>i.mutationQueue.performConsistencyCheck(e)).next(()=>i.documentOverlayCache.removeOverlaysForBatchId(e,t,n.batch.batchId)).next(()=>i.localDocuments.recalculateAndSaveOverlaysForDocumentKeys(e,(e=>{let t=O();for(let r=0;r<e.mutationResults.length;++r)0<e.mutationResults[r].transformResults.length&&(t=t.add(e.batch.mutations[r].key));return t})(n))).next(()=>i.localDocuments.getDocuments(e,t))})}function Ho(e){let t=e;return t.persistence.runTransaction("Get last remote snapshot version","readonly",e=>t.ai.getLastRemoteSnapshotVersion(e))}function Wo(e,u){let h=e,c=u.snapshotVersion,d=h.Ss;return h.persistence.runTransaction("Apply remote event","readwrite-primary",o=>{let e=h.Cs.newChangeBuffer({trackRemovals:!0}),l=(d=h.Ss,[]),t=(u.targetChanges.forEach((t,r)=>{var n,i,s,a=d.get(r);if(a){l.push(h.ai.removeMatchingKeys(o,t.removedDocuments,r).next(()=>h.ai.addMatchingKeys(o,t.addedDocuments,r)));let e=a.withSequenceNumber(o.currentSequenceNumber);null!==u.targetMismatches.get(r)?e=e.withResumeToken(D.EMPTY_BYTE_STRING,g.min()).withLastLimboFreeSnapshotVersion(g.min()):0<t.resumeToken.approximateByteSize()&&(e=e.withResumeToken(t.resumeToken,c)),d=d.insert(r,e),n=a,i=e,s=t,(0===n.resumeToken.approximateByteSize()||i.snapshotVersion.toMicroseconds()-n.snapshotVersion.toMicroseconds()>=jo||0<s.addedDocuments.size+s.modifiedDocuments.size+s.removedDocuments.size)&&l.push(h.ai.updateTargetData(o,e))}}),pi),r=O();if(u.documentUpdates.forEach(e=>{u.resolvedLimboDocuments.has(e)&&l.push(h.persistence.referenceDelegate.updateLimboDocument(o,e))}),l.push(Yo(o,e,u.documentUpdates).next(e=>{t=e.xs,r=e.Os})),!c.isEqual(g.min())){let e=h.ai.getLastRemoteSnapshotVersion(o).next(e=>h.ai.setTargetsMetadata(o,o.currentSequenceNumber,c));l.push(e)}return w.waitFor(l).next(()=>e.apply(o)).next(()=>h.localDocuments.getLocalViewOfDocuments(o,t,r)).next(()=>t)}).then(e=>(h.Ss=d,e))}function Yo(e,s,t){let r=O(),a=O();return t.forEach(e=>r=r.add(e)),s.getEntries(e,r).next(n=>{let i=pi;return t.forEach((e,t)=>{var r=n.get(e);t.isFoundDocument()!==r.isFoundDocument()&&(a=a.add(e)),t.isNoDocument()&&t.version.isEqual(g.min())?(s.removeEntry(e,t.readTime),i=i.insert(e,t)):!r.isValidDocument()||0<t.version.compareTo(r.version)||0===t.version.compareTo(r.version)&&r.hasPendingWrites?(s.addEntry(t),i=i.insert(e,t)):p(zo,"Ignoring outdated watch update for ",e,". Current version:",r.version," Watch version:",t.version)}),{xs:i,Os:a}})}function Jo(e,n){let i=e;return i.persistence.runTransaction("Allocate target","readwrite",t=>{let r;return i.ai.getTargetData(t,n).next(e=>e?(r=e,w.resolve(r)):i.ai.allocateTargetId(t).next(e=>(r=new $s(n,e,"TargetPurposeListen",t.currentSequenceNumber),i.ai.addTargetData(t,r).next(()=>r))))}).then(e=>{var t=i.Ss.get(e.targetId);return(null===t||0<e.snapshotVersion.compareTo(t.snapshotVersion))&&(i.Ss=i.Ss.insert(e.targetId,e),i.Ds.set(n,e.targetId)),e})}async function Xo(e,t,r){let n=e,i=n.Ss.get(t),s=r?"readwrite":"readwrite-primary";try{r||await n.persistence.runTransaction("Release target",s,e=>n.persistence.referenceDelegate.removeTarget(e,i))}catch(e){if(!rt(e))throw e;p(zo,`Failed to update sequence numbers for target ${t}: `+e)}n.Ss=n.Ss.remove(t),n.Ds.delete(i.target)}function Zo(e,a,o){let l=e,u=g.min(),h=O();return l.persistence.runTransaction("Execute query","readwrite",t=>{return e=l,r=t,n=ai(a),(void 0!==(s=(i=e).Ds.get(n))?w.resolve(i.Ss.get(s)):i.ai.getTargetData(r,n)).next(e=>{if(e)return u=e.lastLimboFreeSnapshotVersion,l.ai.getMatchingKeysForTargetId(t,e.targetId).next(e=>{h=e})}).next(()=>l.bs.getDocumentsMatchingQuery(t,a,o?u:g.min(),o?h:O())).next(e=>(rl(l,fi(a),e),{documents:e,Ns:h}));var e,r,n,i,s})}function el(e,t){let r=e,n=r.ai,i=r.Ss.get(t);return i?Promise.resolve(i.target):r.persistence.runTransaction("Get target data","readonly",e=>n.Rt(e,t).next(e=>e?e.target:null))}function tl(e,t){let r=e,n=r.vs.get(t)||g.min();return r.persistence.runTransaction("Get new document changes","readonly",e=>r.Cs.getAllFromCollectionGroup(e,t,je(n,Ve),Number.MAX_SAFE_INTEGER)).then(e=>(rl(r,t,e),e))}function rl(e,t,r){let n=e.vs.get(t)||g.min();r.forEach((e,t)=>{0<t.readTime.compareTo(n)&&(n=t.readTime)}),e.vs.set(t,n)}let nl="firestore_clients";function il(e,t){return nl+`_${e}_`+t}let sl="firestore_mutations";function al(e,t,r){let n=sl+`_${e}_`+r;return t.isAuthenticated()&&(n+="_"+t.uid),n}let ol="firestore_targets";function ll(e,t){return ol+`_${e}_`+t}let ul="SharedClientState";class hl{constructor(e,t,r,n){this.user=e,this.batchId=t,this.state=r,this.error=n}static qs(e,t,r){var n=JSON.parse(r);let i,s="object"==typeof n&&-1!==["pending","acknowledged","rejected"].indexOf(n.state)&&(void 0===n.error||"object"==typeof n.error);return s&&n.error&&(s="string"==typeof n.error.message&&"string"==typeof n.error.code)&&(i=new I(n.error.code,n.error.message)),s?new hl(e,t,n.state,i):(d(ul,`Failed to parse mutation state for ID '${t}': `+r),null)}Qs(){var e={state:this.state,updateTimeMs:Date.now()};return this.error&&(e.error={code:this.error.code,message:this.error.message}),JSON.stringify(e)}}class cl{constructor(e,t,r){this.targetId=e,this.state=t,this.error=r}static qs(e,t){var r=JSON.parse(t);let n,i="object"==typeof r&&-1!==["not-current","current","rejected"].indexOf(r.state)&&(void 0===r.error||"object"==typeof r.error);return i&&r.error&&(i="string"==typeof r.error.message&&"string"==typeof r.error.code)&&(n=new I(r.error.code,r.error.message)),i?new cl(e,r.state,n):(d(ul,`Failed to parse target state for ID '${e}': `+t),null)}Qs(){var e={state:this.state,updateTimeMs:Date.now()};return this.error&&(e.error={code:this.error.code,message:this.error.message}),JSON.stringify(e)}}class dl{constructor(e,t){this.clientId=e,this.activeTargetIds=t}static qs(e,t){var r=JSON.parse(t);let n="object"==typeof r&&r.activeTargetIds instanceof Array,i=Ti;for(let s=0;n&&s<r.activeTargetIds.length;++s)n=gt(r.activeTargetIds[s]),i=i.add(r.activeTargetIds[s]);return n?new dl(e,i):(d(ul,`Failed to parse client data for instance '${e}': `+t),null)}}class fl{constructor(e,t){this.clientId=e,this.onlineState=t}static qs(e){var t=JSON.parse(e);return"object"==typeof t&&-1!==["Unknown","Online","Offline"].indexOf(t.onlineState)&&"string"==typeof t.clientId?new fl(t.clientId,t.onlineState):(d(ul,"Failed to parse online state: "+e),null)}}class gl{constructor(){this.activeTargetIds=Ti}$s(e){this.activeTargetIds=this.activeTargetIds.add(e)}Us(e){this.activeTargetIds=this.activeTargetIds.delete(e)}Qs(){var e={activeTargetIds:this.activeTargetIds.toArray(),updateTimeMs:Date.now()};return JSON.stringify(e)}}class ml{constructor(e,t,r,n,i){this.window=e,this.bi=t,this.persistenceKey=r,this.Ks=n,this.syncEngine=null,this.onlineStateHandler=null,this.sequenceNumberHandler=null,this.Ws=this.Gs.bind(this),this.zs=new A(S),this.started=!1,this.js=[];var s=r.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");this.storage=this.window.localStorage,this.currentUser=i,this.Hs=il(this.persistenceKey,this.Ks),this.Js="firestore_sequence_number_"+this.persistenceKey,this.zs=this.zs.insert(this.Ks,new gl),this.Ys=new RegExp(`^${nl}_${s}_([^_]*)$`),this.Zs=new RegExp(`^${sl}_${s}_(\\d+)(?:_(.*))?$`),this.Xs=new RegExp(`^${ol}_${s}_(\\d+)$`),this.eo="firestore_online_state_"+this.persistenceKey,this.no="firestore_bundle_loaded_v2_"+this.persistenceKey,this.window.addEventListener("storage",this.Ws)}static C(e){return!(!e||!e.localStorage)}async start(){let e=await this.syncEngine.us();for(let r of e)if(r!==this.Ks){let e=this.getItem(il(this.persistenceKey,r));var t;e&&(t=dl.qs(r,e))&&(this.zs=this.zs.insert(t.clientId,t))}this.ro();let r=this.storage.getItem(this.eo);if(r){let e=this.io(r);e&&this.so(e)}for(let e of this.js)this.Gs(e);this.js=[],this.window.addEventListener("pagehide",()=>this.shutdown()),this.started=!0}writeSequenceNumber(e){this.setItem(this.Js,JSON.stringify(e))}getAllActiveQueryTargets(){return this.oo(this.zs)}isActiveQueryTarget(r){let n=!1;return this.zs.forEach((e,t)=>{t.activeTargetIds.has(r)&&(n=!0)}),n}addPendingMutation(e){this._o(e,"pending")}updateMutationState(e,t,r){this._o(e,t,r),this.ao(e)}addLocalQueryTarget(t,e=!0){let r="not-current";if(this.isActiveQueryTarget(t)){let e=this.storage.getItem(ll(this.persistenceKey,t));var n;e&&(n=cl.qs(t,e))&&(r=n.state)}return e&&this.uo.$s(t),this.ro(),r}removeLocalQueryTarget(e){this.uo.Us(e),this.ro()}isLocalQueryTarget(e){return this.uo.activeTargetIds.has(e)}clearQueryState(e){this.removeItem(ll(this.persistenceKey,e))}updateQueryState(e,t,r){this.co(e,t,r)}handleUserChange(e,t,r){t.forEach(e=>{this.ao(e)}),this.currentUser=e,r.forEach(e=>{this.addPendingMutation(e)})}setOnlineState(e){this.lo(e)}notifyBundleLoaded(e){this.ho(e)}shutdown(){this.started&&(this.window.removeEventListener("storage",this.Ws),this.removeItem(this.Hs),this.started=!1)}getItem(e){var t=this.storage.getItem(e);return p(ul,"READ",e,t),t}setItem(e,t){p(ul,"SET",e,t),this.storage.setItem(e,t)}removeItem(e){p(ul,"REMOVE",e),this.storage.removeItem(e)}Gs(e){let t=e;t.storageArea===this.storage&&(p(ul,"EVENT",t.key,t.newValue),t.key===this.Hs?d("Received WebStorage notification for local change. Another client might have garbage-collected our state"):this.bi.enqueueRetryable(async()=>{if(this.started){if(null!==t.key){if(this.Ys.test(t.key))return null==t.newValue?(e=this.Po(t.key),this.To(e,null)):(e=this.Io(t.key,t.newValue))?this.To(e.clientId,e):void 0;if(this.Zs.test(t.key)){if(null!==t.newValue){var e=this.Eo(t.key,t.newValue);if(e)return this.Ao(e)}}else if(this.Xs.test(t.key)){if(null!==t.newValue){var e=this.Ro(t.key,t.newValue);if(e)return this.Vo(e)}}else if(t.key===this.eo){if(null!==t.newValue){var e=this.io(t.newValue);if(e)return this.so(e)}}else t.key===this.Js?(e=(e=>{let t=ht.le;if(null!=e)try{var r=JSON.parse(e);y("number"==typeof r,30636,{mo:e}),t=r}catch(e){d(ul,"Failed to read sequence number from WebStorage",e)}return t})(t.newValue))!==ht.le&&this.sequenceNumberHandler(e):t.key===this.no&&(e=this.fo(t.newValue),await Promise.all(e.map(e=>this.syncEngine.po(e))))}}else this.js.push(t)}))}get uo(){return this.zs.get(this.Ks)}ro(){this.setItem(this.Hs,this.uo.Qs())}_o(e,t,r){var n=new hl(this.currentUser,e,t,r),i=al(this.persistenceKey,this.currentUser,e);this.setItem(i,n.Qs())}ao(e){var t=al(this.persistenceKey,this.currentUser,e);this.removeItem(t)}lo(e){var t={clientId:this.Ks,onlineState:e};this.storage.setItem(this.eo,JSON.stringify(t))}co(e,t,r){var n=ll(this.persistenceKey,e),i=new cl(e,t,r);this.setItem(n,i.Qs())}ho(e){var t=JSON.stringify(Array.from(e));this.setItem(this.no,t)}Po(e){var t=this.Ys.exec(e);return t?t[1]:null}Io(e,t){var r=this.Po(e);return dl.qs(r,t)}Eo(e,t){var r=this.Zs.exec(e),n=Number(r[1]),r=void 0!==r[2]?r[2]:null;return hl.qs(new u(r),n,t)}Ro(e,t){var r=this.Xs.exec(e),r=Number(r[1]);return cl.qs(r,t)}io(e){return fl.qs(e)}fo(e){return JSON.parse(e)}async Ao(e){if(e.user.uid===this.currentUser.uid)return this.syncEngine.yo(e.batchId,e.state,e.error);p(ul,"Ignoring mutation for non-active user "+e.user.uid)}Vo(e){return this.syncEngine.wo(e.targetId,e.state,e.error)}To(e,t){let r=t?this.zs.insert(e,t):this.zs.remove(e),n=this.oo(this.zs),i=this.oo(r),s=[],a=[];return i.forEach(e=>{n.has(e)||s.push(e)}),n.forEach(e=>{i.has(e)||a.push(e)}),this.syncEngine.bo(s,a).then(()=>{this.zs=r})}so(e){this.zs.get(e.clientId)&&this.onlineStateHandler(e.onlineState)}oo(e){let r=Ti;return e.forEach((e,t)=>{r=r.unionWith(t.activeTargetIds)}),r}}class pl{constructor(){this.So=new gl,this.Do={},this.onlineStateHandler=null,this.sequenceNumberHandler=null}addPendingMutation(e){}updateMutationState(e,t,r){}addLocalQueryTarget(e,t=!0){return t&&this.So.$s(e),this.Do[e]||"not-current"}updateQueryState(e,t,r){this.Do[e]=t}removeLocalQueryTarget(e){this.So.Us(e)}isLocalQueryTarget(e){return this.So.activeTargetIds.has(e)}clearQueryState(e){delete this.Do[e]}getAllActiveQueryTargets(){return this.So.activeTargetIds}isActiveQueryTarget(e){return this.So.activeTargetIds.has(e)}start(){return this.So=new gl,Promise.resolve()}handleUserChange(e,t,r){}setOnlineState(e){}shutdown(){}writeSequenceNumber(e){}notifyBundleLoaded(e){}}class yl{vo(e){}shutdown(){}}let vl="ConnectivityMonitor";class wl{constructor(){this.Co=()=>this.Fo(),this.Mo=()=>this.xo(),this.Oo=[],this.No()}vo(e){this.Oo.push(e)}shutdown(){window.removeEventListener("online",this.Co),window.removeEventListener("offline",this.Mo)}No(){window.addEventListener("online",this.Co),window.addEventListener("offline",this.Mo)}Fo(){p(vl,"Network connectivity changed: AVAILABLE");for(var e of this.Oo)e(0)}xo(){p(vl,"Network connectivity changed: UNAVAILABLE");for(var e of this.Oo)e(1)}static C(){return"undefined"!=typeof window&&void 0!==window.addEventListener&&void 0!==window.removeEventListener}}let _l=null;function bl(){return null===_l?_l=268435456+Math.round(2147483648*Math.random()):_l++,"0x"+_l.toString(16)}let Il="RestConnection",Tl={BatchGetDocuments:"batchGet",Commit:"commit",RunQuery:"runQuery",RunAggregationQuery:"runAggregationQuery"};class El{constructor(e){this.Go=e.Go,this.zo=e.zo}jo(e){this.Ho=e}Jo(e){this.Yo=e}Zo(e){this.Xo=e}onMessage(e){this.e_=e}close(){this.zo()}send(e){this.Go(e)}t_(){this.Ho()}n_(){this.Yo()}r_(e){this.Xo(e)}i_(e){this.e_(e)}}let Sl="WebChannelConnection";class xl extends class{get Bo(){return!1}constructor(e){this.databaseInfo=e,this.databaseId=e.databaseId;var t=e.ssl?"https":"http",r=encodeURIComponent(this.databaseId.projectId),n=encodeURIComponent(this.databaseId.database);this.Lo=t+"://"+e.host,this.ko=`projects/${r}/databases/`+n,this.qo=this.databaseId.database===Xr?"project_id="+r:`project_id=${r}&database_id=`+n}Qo(t,e,r,n,i){let s=bl(),a=this.$o(t,e.toUriEncodedString());p(Il,`Sending RPC '${t}' ${s}:`,a,r);var o={"google-cloud-resource-prefix":this.ko,"x-goog-request-params":this.qo};return this.Uo(o,n,i),this.Ko(t,a,o,r).then(e=>(p(Il,`Received RPC '${t}' ${s}: `,e),e),e=>{throw pe(Il,`RPC '${t}' ${s} failed with error: `,e,"url: ",a,"request:",r),e})}Wo(e,t,r,n,i,s){return this.Qo(e,t,r,n,i)}Uo(r,e,t){r["X-Goog-Api-Client"]="gl-js/ fire/"+fe,r["Content-Type"]="text/plain",this.databaseInfo.appId&&(r["X-Firebase-GMPID"]=this.databaseInfo.appId),e&&e.headers.forEach((e,t)=>r[t]=e),t&&t.headers.forEach((e,t)=>r[t]=e)}$o(e,t){var r=Tl[e];return this.Lo+`/v1/${t}:`+r}terminate(){}}{constructor(e){super(e),this.forceLongPolling=e.forceLongPolling,this.autoDetectLongPolling=e.autoDetectLongPolling,this.useFetchStreams=e.useFetchStreams,this.longPollingOptions=e.longPollingOptions}Ko(l,t,r,n){let u=bl();return new Promise((s,a)=>{let o=new pr;o.setWithCredentials(!0),o.listenOnce(vr.COMPLETE,()=>{try{switch(o.getLastErrorCode()){case wr.NO_ERROR:var e=o.getResponseJson();p(Sl,`XHR for RPC '${l}' ${u} received:`,JSON.stringify(e)),s(e);break;case wr.TIMEOUT:p(Sl,`RPC '${l}' ${u} timed out`),a(new I(b.DEADLINE_EXCEEDED,"Request time out"));break;case wr.HTTP_ERROR:var t=o.getStatus();if(p(Sl,`RPC '${l}' ${u} failed with status:`,t,"response text:",o.getResponseText()),0<t){let e=o.getResponseJson();var r=null==(e=Array.isArray(e)?e[0]:e)?void 0:e.error;if(r&&r.status&&r.message){n=r.status,i=n.toLowerCase().replace(/_/g,"-");let e=0<=Object.values(b).indexOf(i)?i:b.UNKNOWN;a(new I(e,r.message))}else a(new I(b.UNKNOWN,"Server responded with status "+o.getStatus()))}else a(new I(b.UNAVAILABLE,"Connection failed."));break;default:E(9055,{s_:l,streamId:u,o_:o.getLastErrorCode(),__:o.getLastError()})}}finally{p(Sl,`RPC '${l}' ${u} completed.`)}var n,i});var e=JSON.stringify(n);p(Sl,`RPC '${l}' ${u} sending request:`,n),o.send(t,"POST",e,r,15)})}a_(i,e,t){let s=bl(),r=[this.Lo,"/","google.firestore.v1.Firestore","/",i,"/channel"],n=Tr(),a=Ir(),o={httpSessionIdParam:"gsessionid",initMessageHeaders:{},messageUrlParams:{database:`projects/${this.databaseId.projectId}/databases/`+this.databaseId.database},sendRawJson:!0,supportsCrossDomainXhr:!0,internalChannelParams:{forwardChannelRequestTimeoutMs:6e5},forceLongPolling:this.forceLongPolling,detectBufferingProxy:this.autoDetectLongPolling},l=this.longPollingOptions.timeoutSeconds;void 0!==l&&(o.longPollingTimeout=Math.round(1e3*l)),this.useFetchStreams&&(o.useFetchStreams=!0),this.Uo(o.initMessageHeaders,e,t),o.encodeInitMessageHeaders=!0;var u=r.join("");p(Sl,`Creating RPC '${i}' stream ${s}: `+u,o);let h=n.createWebChannel(u,o),c=!1,d=!1,f=new El({Go:e=>{d?p(Sl,`Not sending because RPC '${i}' stream ${s} is closed:`,e):(c||(p(Sl,`Opening RPC '${i}' stream ${s} transport.`),h.open(),c=!0),p(Sl,`RPC '${i}' stream ${s} sending:`,e),h.send(e))},zo:()=>h.close()}),g=(e,t,r)=>{e.listen(t,e=>{try{r(e)}catch(e){setTimeout(()=>{throw e},0)}})};return g(h,yr.EventType.OPEN,()=>{d||(p(Sl,`RPC '${i}' stream ${s} transport opened.`),f.t_())}),g(h,yr.EventType.CLOSE,()=>{d||(d=!0,p(Sl,`RPC '${i}' stream ${s} transport closed`),f.r_())}),g(h,yr.EventType.ERROR,e=>{d||(d=!0,pe(Sl,`RPC '${i}' stream ${s} transport errored. Name:`,e.name,"Message:",e.message),f.r_(new I(b.UNAVAILABLE,"The operation could not be completed")))}),g(h,yr.EventType.MESSAGE,e=>{if(!d){var t=e.data[0],n=(y(!!t,16349),t),n=(null==n?void 0:n.error)||(null==(n=n[0])?void 0:n.error);if(n){p(Sl,`RPC '${i}' stream ${s} received error:`,n);let e=n.status,t=(e=>{var t=m[e];if(void 0!==t)return rs(t)})(e),r=n.message;void 0===t&&(t=b.INTERNAL,r="Unknown error status: "+e+" with message "+n.message),d=!0,f.r_(new I(t,r)),h.close()}else p(Sl,`RPC '${i}' stream ${s} received:`,t),f.i_(t)}}),g(a,br.STAT_EVENT,e=>{e.stat===_r.PROXY?p(Sl,`RPC '${i}' stream ${s} detected buffering proxy`):e.stat===_r.NOPROXY&&p(Sl,`RPC '${i}' stream ${s} detected no buffering proxy`)}),setTimeout(()=>{f.n_()},0),f}}function Al(){return"undefined"!=typeof window?window:null}function Cl(){return"undefined"!=typeof document?document:null}function Dl(e){return new _s(e,!0)}class Nl{constructor(e,t,r=1e3,n=1.5,i=6e4){this.bi=e,this.timerId=t,this.u_=r,this.c_=n,this.l_=i,this.h_=0,this.P_=null,this.T_=Date.now(),this.reset()}reset(){this.h_=0}I_(){this.h_=this.l_}E_(e){this.cancel();var t=Math.floor(this.h_+this.d_()),r=Math.max(0,Date.now()-this.T_),n=Math.max(0,t-r);0<n&&p("ExponentialBackoff",`Backing off for ${n} ms (base delay: ${this.h_} ms, delay with jitter: ${t} ms, last attempt: ${r} ms ago)`),this.P_=this.bi.enqueueAfterDelay(this.timerId,n,()=>(this.T_=Date.now(),e())),this.h_*=this.c_,this.h_<this.u_&&(this.h_=this.u_),this.h_>this.l_&&(this.h_=this.l_)}A_(){null!==this.P_&&(this.P_.skipDelay(),this.P_=null)}cancel(){null!==this.P_&&(this.P_.cancel(),this.P_=null)}d_(){return(Math.random()-.5)*this.h_}}let kl="PersistentStream";class Rl{constructor(e,t,r,n,i,s,a,o){this.bi=e,this.R_=r,this.V_=n,this.connection=i,this.authCredentialsProvider=s,this.appCheckCredentialsProvider=a,this.listener=o,this.state=0,this.m_=0,this.f_=null,this.g_=null,this.stream=null,this.p_=0,this.y_=new Nl(e,t)}w_(){return 1===this.state||5===this.state||this.b_()}b_(){return 2===this.state||3===this.state}start(){this.p_=0,4!==this.state?this.auth():this.S_()}async stop(){this.w_()&&await this.close(0)}D_(){this.state=0,this.y_.reset()}v_(){this.b_()&&null===this.f_&&(this.f_=this.bi.enqueueAfterDelay(this.R_,6e4,()=>this.C_()))}F_(e){this.M_(),this.stream.send(e)}async C_(){if(this.b_())return this.close(0)}M_(){this.f_&&(this.f_.cancel(),this.f_=null)}x_(){this.g_&&(this.g_.cancel(),this.g_=null)}async close(e,t){this.M_(),this.x_(),this.y_.cancel(),this.m_++,4!==e?this.y_.reset():t&&t.code===b.RESOURCE_EXHAUSTED?(d(t.toString()),d("Using maximum backoff delay to prevent overloading the backend."),this.y_.I_()):t&&t.code===b.UNAUTHENTICATED&&3!==this.state&&(this.authCredentialsProvider.invalidateToken(),this.appCheckCredentialsProvider.invalidateToken()),null!==this.stream&&(this.O_(),this.stream.close(),this.stream=null),this.state=e,await this.listener.Zo(t)}O_(){}auth(){this.state=1;let e=this.N_(this.m_),r=this.m_;Promise.all([this.authCredentialsProvider.getToken(),this.appCheckCredentialsProvider.getToken()]).then(([e,t])=>{this.m_===r&&this.B_(e,t)},t=>{e(()=>{var e=new I(b.UNKNOWN,"Fetching auth token failed: "+t.message);return this.L_(e)})})}B_(e,t){let r=this.N_(this.m_);this.stream=this.k_(e,t),this.stream.jo(()=>{r(()=>this.listener.jo())}),this.stream.Jo(()=>{r(()=>(this.state=2,this.g_=this.bi.enqueueAfterDelay(this.V_,1e4,()=>(this.b_()&&(this.state=3),Promise.resolve())),this.listener.Jo()))}),this.stream.Zo(e=>{r(()=>this.L_(e))}),this.stream.onMessage(e=>{r(()=>1==++this.p_?this.q_(e):this.onNext(e))})}S_(){this.state=5,this.y_.E_(async()=>{this.state=0,this.start()})}L_(e){return p(kl,"close with error: "+e),this.stream=null,this.close(4,e)}N_(t){return e=>{this.bi.enqueueAndForget(()=>this.m_===t?e():(p(kl,"stream callback skipped by getCloseGuardedDispatcher."),Promise.resolve()))}}}class Ml extends Rl{constructor(e,t,r,n,i,s){super(e,"listen_stream_connection_backoff","listen_stream_idle","health_check_timeout",t,r,n,s),this.serializer=i}k_(e,t){return this.connection.a_("Listen",e,t)}q_(e){return this.onNext(e)}onNext(e){this.y_.reset();var t=((e,t)=>{let r;if("targetChange"in t){t.targetChange;var n="NO_CHANGE"===(l=t.targetChange.targetChangeType||"NO_CHANGE")?0:"ADD"===l?1:"REMOVE"===l?2:"CURRENT"===l?3:"RESET"===l?4:E(39313,{state:l}),i=t.targetChange.targetIds||[],s=(l=t.targetChange.resumeToken,e.useProto3Json?(y(void 0===l||"string"==typeof l,58123),D.fromBase64String(l||"")):(y(void 0===l||l instanceof Buffer||l instanceof Uint8Array,16193),D.fromUint8Array(l||new Uint8Array))),a=t.targetChange.cause,a=a&&(a=void 0===(l=a).code?b.UNKNOWN:rs(l.code),new I(a,l.message||""));r=new ds(n,i,s,a||null)}else if("documentChange"in t){t.documentChange;var n=t.documentChange,i=(n.document,n.document.name,n.document.updateTime,Cs(e,n.document.name)),s=F(n.document.updateTime),a=n.document.createTime?F(n.document.createTime):g.min(),o=new xn({mapValue:{fields:n.document.fields}}),i=k.newFoundDocument(i,s,a,o),s=n.targetIds||[],o=n.removedTargetIds||[];r=new hs(s,o,i.key,i)}else if("documentDelete"in t){t.documentDelete;n=t.documentDelete,s=(n.document,Cs(e,n.document)),o=n.readTime?F(n.readTime):g.min(),i=k.newNoDocument(s,o),s=n.removedTargetIds||[];r=new hs([],s,i.key,i)}else if("documentRemove"in t){t.documentRemove;o=t.documentRemove,n=(o.document,Cs(e,o.document)),s=o.removedTargetIds||[];r=new hs([],s,n,null)}else{if(!("filter"in t))return E(11601,{Vt:t});{t.filter;let e=t.filter;e.targetId;var{count:i=0,unchangedNames:o}=e,s=new es(i,o),n=e.targetId;r=new cs(n,s)}}var l;return r})(this.serializer,e),r="targetChange"in(e=e)&&(!(r=e.targetChange).targetIds||!r.targetIds.length)&&r.readTime?F(r.readTime):g.min();return this.listener.Q_(t,r)}U_(e){var t={},r=(t.database=ks(this.serializer),t.addTarget=((t,r)=>{var n;let e=r.target;if((n=Yn(e)?{documents:Ps(t,e)}:{query:Us(t,e).gt}).targetId=r.targetId,0<r.resumeToken.approximateByteSize()){n.resumeToken=Ts(t,r.resumeToken);let e=bs(t,r.expectedCount);null!==e&&(n.expectedCount=e)}else if(0<r.snapshotVersion.compareTo(g.min())){n.readTime=Is(t,r.snapshotVersion.toTimestamp());let e=bs(t,r.expectedCount);null!==e&&(n.expectedCount=e)}return n})(this.serializer,e),qs(this.serializer,e));r&&(t.labels=r),this.F_(t)}K_(e){var t={};t.database=ks(this.serializer),t.removeTarget=e,this.F_(t)}}class Ol extends Rl{constructor(e,t,r,n,i,s){super(e,"write_stream_connection_backoff","write_stream_idle","health_check_timeout",t,r,n,s),this.serializer=i}get W_(){return 0<this.p_}start(){this.lastStreamToken=void 0,super.start()}O_(){this.W_&&this.G_([])}k_(e,t){return this.connection.a_("Write",e,t)}q_(e){return y(!!e.streamToken,31322),this.lastStreamToken=e.streamToken,y(!e.writeResults||0===e.writeResults.length,55816),this.listener.z_()}onNext(e){y(!!e.streamToken,12678),this.lastStreamToken=e.streamToken,this.y_.reset();var t=Vs(e.writeResults,e.commitTime),r=F(e.commitTime);return this.listener.j_(r,t)}H_(){var e={};e.database=ks(this.serializer),this.F_(e)}G_(e){var t={streamToken:this.lastStreamToken,writes:e.map(e=>Ls(this.serializer,e))};this.F_(t)}}class Ll extends class{}{constructor(e,t,r,n){super(),this.authCredentials=e,this.appCheckCredentials=t,this.connection=r,this.serializer=n,this.J_=!1}Y_(){if(this.J_)throw new I(b.FAILED_PRECONDITION,"The client has already been terminated.")}Qo(r,n,i,s){return this.Y_(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then(([e,t])=>this.connection.Qo(r,Ss(n,i),s,e,t)).catch(e=>{throw"FirebaseError"===e.name?(e.code===b.UNAUTHENTICATED&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),e):new I(b.UNKNOWN,e.toString())})}Wo(r,n,i,s,a){return this.Y_(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then(([e,t])=>this.connection.Wo(r,Ss(n,i),s,e,t,a)).catch(e=>{throw"FirebaseError"===e.name?(e.code===b.UNAUTHENTICATED&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),e):new I(b.UNKNOWN,e.toString())})}terminate(){this.J_=!0,this.connection.terminate()}}class Fl{constructor(e,t){this.asyncQueue=e,this.onlineStateHandler=t,this.state="Unknown",this.Z_=0,this.X_=null,this.ea=!0}ta(){0===this.Z_&&(this.na("Unknown"),this.X_=this.asyncQueue.enqueueAfterDelay("online_state_timeout",1e4,()=>(this.X_=null,this.ra("Backend didn't respond within 10 seconds."),this.na("Offline"),Promise.resolve())))}ia(e){"Online"===this.state?this.na("Unknown"):(this.Z_++,1<=this.Z_&&(this.sa(),this.ra("Connection failed 1 times. Most recent error: "+e.toString()),this.na("Offline")))}set(e){this.sa(),this.Z_=0,"Online"===e&&(this.ea=!1),this.na(e)}na(e){e!==this.state&&(this.state=e,this.onlineStateHandler(e))}ra(e){var t=`Could not reach Cloud Firestore backend. ${e}
This typically indicates that your device does not have a healthy Internet connection at the moment. The client will operate in offline mode until it is able to successfully connect to the backend.`;this.ea?(d(t),this.ea=!1):p("OnlineStateTracker",t)}sa(){null!==this.X_&&(this.X_.cancel(),this.X_=null)}}let Vl="RemoteStore";class Pl{constructor(e,t,r,n,i){this.localStore=e,this.datastore=t,this.asyncQueue=r,this.remoteSyncer={},this.oa=[],this._a=new Map,this.aa=new Set,this.ua=[],this.ca=i,this.ca.vo(e=>{r.enqueueAndForget(async()=>{var e;$l(this)&&(p(Vl,"Restarting streams for network reachability change."),(e=this).aa.add(4),await Bl(e),e.la.set("Unknown"),e.aa.delete(4),await Ul(e))})}),this.la=new Fl(r,n)}}async function Ul(e){if($l(e))for(var t of e.ua)await t(!0)}async function Bl(e){for(var t of e.ua)await t(!1)}function ql(e,t){var r=e;r._a.has(t.targetId)||(r._a.set(t.targetId,t),Ql(r)?Gl(r):ru(r).b_()&&jl(r,t))}function zl(e,t){var r=e,n=ru(r);r._a.delete(t),n.b_()&&Kl(r,t),0===r._a.size&&(n.b_()?n.v_():$l(r)&&r.la.set("Unknown"))}function jl(e,t){var r;e.ha.Ke(t.targetId),(0<t.resumeToken.approximateByteSize()||0<t.snapshotVersion.compareTo(g.min()))&&(r=e.remoteSyncer.getRemoteKeysForTarget(t.targetId).size,t=t.withExpectedCount(r)),ru(e).U_(t)}function Kl(e,t){e.ha.Ke(t),ru(e).K_(t)}function Gl(t){t.ha=new gs({getRemoteKeysForTarget:e=>t.remoteSyncer.getRemoteKeysForTarget(e),Rt:e=>t._a.get(e)||null,Pt:()=>t.datastore.serializer.databaseId}),ru(t).start(),t.la.ta()}function Ql(e){return $l(e)&&!ru(e).w_()&&0<e._a.size}function $l(e){return 0===e.aa.size}function Hl(e){e.ha=void 0}async function Wl(e,t,r){if(!rt(t))throw t;e.aa.add(1),await Bl(e),e.la.set("Offline"),r=r||(()=>Ho(e.localStore)),e.asyncQueue.enqueueRetryable(async()=>{p(Vl,"Retrying IndexedDB access"),await r(),e.aa.delete(1),await Ul(e)})}function Yl(t,r){return r().catch(e=>Wl(t,e,r))}async function Jl(e){var t,r,n,i,s=e,a=nu(s);let o=0<s.oa.length?s.oa[s.oa.length-1].batchId:ct;for(;$l(i=s)&&i.oa.length<10;)try{let e=await((e,t)=>{let r=e;return r.persistence.runTransaction("Get next mutation batch","readonly",e=>(void 0===t&&(t=ct),r.mutationQueue.getNextMutationBatchAfterBatchId(e,t)))})(s.localStore,o);if(null===e){0===s.oa.length&&a.v_();break}o=e.batchId,t=s,r=e,n=void 0,t.oa.push(r),(n=nu(t)).b_()&&n.W_&&n.G_(r.mutations)}catch(e){await Wl(s,e)}Xl(s)&&Zl(s)}function Xl(e){return $l(e)&&!nu(e).w_()&&0<e.oa.length}function Zl(e){nu(e).start()}async function eu(e,t){var r=e,n=(r.asyncQueue.verifyOperationInProgress(),p(Vl,"RemoteStore received new credentials"),$l(r));r.aa.add(3),await Bl(r),n&&r.la.set("Unknown"),await r.remoteSyncer.handleCredentialChange(t),r.aa.delete(3),await Ul(r)}async function tu(e,t){var r=e;t?(r.aa.delete(2),await Ul(r)):(r.aa.add(2),await Bl(r),r.la.set("Unknown"))}function ru(t){return t.Pa||(t.Pa=(e=t.datastore,r=t.asyncQueue,n={jo:(async function(e){e.la.set("Online")}).bind(null,t),Jo:(async function(r){r._a.forEach((e,t)=>{jl(r,e)})}).bind(null,t),Zo:(async function(e,t){Hl(e),Ql(e)?(e.la.ia(t),Gl(e)):e.la.set("Unknown")}).bind(null,t),Q_:(async function(t,e,r){if(t.la.set("Online"),e instanceof ds&&2===e.state&&e.cause)try{var n,i=t,s=e,a=s.cause;for(n of s.targetIds)i._a.has(n)&&(await i.remoteSyncer.rejectListen(n,a),i._a.delete(n),i.ha.removeTarget(n))}catch(r){p(Vl,"Failed to remove targets %s: %s ",e.targetIds.join(","),r),await Wl(t,r)}else if(e instanceof hs?t.ha.Xe(e):e instanceof cs?t.ha.ot(e):t.ha.nt(e),!r.isEqual(g.min()))try{let e=await Ho(t.localStore);0<=r.compareTo(e)&&(l=r,(u=(o=t).ha.It(l)).targetChanges.forEach((e,t)=>{var r;0<e.resumeToken.approximateByteSize()&&(r=o._a.get(t))&&o._a.set(t,r.withResumeToken(e.resumeToken,l))}),u.targetMismatches.forEach((e,t)=>{var r=o._a.get(e);r&&(o._a.set(e,r.withResumeToken(D.EMPTY_BYTE_STRING,r.snapshotVersion)),Kl(o,e),r=new $s(r.target,e,t,r.sequenceNumber),jl(o,r))}),await o.remoteSyncer.applyRemoteEvent(u))}catch(e){p(Vl,"Failed to raise snapshot:",e),await Wl(t,e)}var o,l,u}).bind(null,t)},(i=e).Y_(),new Ml(r,i.connection,i.authCredentials,i.appCheckCredentials,i.serializer,n)),t.ua.push(async e=>{e?(t.Pa.D_(),Ql(t)?Gl(t):t.la.set("Unknown")):(await t.Pa.stop(),Hl(t))})),t.Pa;var e,r,n,i}function nu(t){return t.Ta||(t.Ta=(e=t.datastore,r=t.asyncQueue,n={jo:()=>Promise.resolve(),Jo:(async function(e){nu(e).H_()}).bind(null,t),Zo:(async function(e,t){if(t&&nu(e).W_){var r=e,n=t;if(ts(t=n.code)&&t!==b.ABORTED){let e=r.oa.shift();nu(r).D_(),await Yl(r,()=>r.remoteSyncer.rejectFailedWrite(e.batchId,n)),await Jl(r)}await 0}Xl(e)&&Zl(e)}).bind(null,t),z_:(async function(e){var t,r=nu(e);for(t of e.oa)r.G_(t.mutations)}).bind(null,t),j_:(async function(e,t,r){let n=e.oa.shift(),i=Xi.from(n,t,r);await Yl(e,()=>e.remoteSyncer.applySuccessfulWrite(i)),await Jl(e)}).bind(null,t)},(i=e).Y_(),new Ol(r,i.connection,i.authCredentials,i.appCheckCredentials,i.serializer,n)),t.ua.push(async e=>{e?(t.Ta.D_(),await Jl(t)):(await t.Ta.stop(),0<t.oa.length&&(p(Vl,`Stopping write stream with ${t.oa.length} pending writes`),t.oa=[]))})),t.Ta;var e,r,n,i}class iu{constructor(e,t,r,n,i){this.asyncQueue=e,this.timerId=t,this.targetTimeMs=r,this.op=n,this.removalCallback=i,this.deferred=new f,this.then=this.deferred.promise.then.bind(this.deferred.promise),this.deferred.promise.catch(e=>{})}get promise(){return this.deferred.promise}static createAndSchedule(e,t,r,n,i){var s=Date.now()+r,s=new iu(e,t,s,n,i);return s.start(r),s}start(e){this.timerHandle=setTimeout(()=>this.handleDelayElapsed(),e)}skipDelay(){return this.handleDelayElapsed()}cancel(e){null!==this.timerHandle&&(this.clearTimeout(),this.deferred.reject(new I(b.CANCELLED,"Operation cancelled"+(e?": "+e:""))))}handleDelayElapsed(){this.asyncQueue.enqueueAndForget(()=>null!==this.timerHandle?(this.clearTimeout(),this.op().then(e=>this.deferred.resolve(e))):Promise.resolve())}clearTimeout(){null!==this.timerHandle&&(this.removalCallback(this),clearTimeout(this.timerHandle),this.timerHandle=null)}}function su(e,t){if(d("AsyncQueue",t+": "+e),rt(e))return new I(b.UNAVAILABLE,t+": "+e);throw e}class au{static emptySet(e){return new au(e.comparator)}constructor(r){this.comparator=r?(e,t)=>r(e,t)||x.comparator(e.key,t.key):(e,t)=>x.comparator(e.key,t.key),this.keyedMap=vi(),this.sortedSet=new A(this.comparator)}has(e){return null!=this.keyedMap.get(e)}get(e){return this.keyedMap.get(e)}first(){return this.sortedSet.minKey()}last(){return this.sortedSet.maxKey()}isEmpty(){return this.sortedSet.isEmpty()}indexOf(e){var t=this.keyedMap.get(e);return t?this.sortedSet.indexOf(t):-1}get size(){return this.sortedSet.size}forEach(r){this.sortedSet.inorderTraversal((e,t)=>(r(e),!1))}add(e){var t=this.delete(e.key);return t.copy(t.keyedMap.insert(e.key,e),t.sortedSet.insert(e,null))}delete(e){var t=this.get(e);return t?this.copy(this.keyedMap.remove(e),this.sortedSet.remove(t)):this}isEqual(e){if(!(e instanceof au))return!1;if(this.size!==e.size)return!1;for(var r=this.sortedSet.getIterator(),n=e.sortedSet.getIterator();r.hasNext();){let e=r.getNext().key,t=n.getNext().key;if(!e.isEqual(t))return!1}return!0}toString(){let t=[];return this.forEach(e=>{t.push(e.toString())}),0===t.length?"DocumentSet ()":"DocumentSet (\n  "+t.join("  \n")+"\n)"}copy(e,t){var r=new au;return r.comparator=this.comparator,r.keyedMap=e,r.sortedSet=t,r}}class ou{constructor(){this.Ia=new A(x.comparator)}track(e){var t=e.doc.key,r=this.Ia.get(t);!r||0!==e.type&&3===r.type?this.Ia=this.Ia.insert(t,e):3===e.type&&1!==r.type?this.Ia=this.Ia.insert(t,{type:r.type,doc:e.doc}):2===e.type&&2===r.type?this.Ia=this.Ia.insert(t,{type:2,doc:e.doc}):2===e.type&&0===r.type?this.Ia=this.Ia.insert(t,{type:0,doc:e.doc}):1===e.type&&0===r.type?this.Ia=this.Ia.remove(t):1===e.type&&2===r.type?this.Ia=this.Ia.insert(t,{type:1,doc:r.doc}):0===e.type&&1===r.type?this.Ia=this.Ia.insert(t,{type:2,doc:e.doc}):E(63341,{Vt:e,Ea:r})}da(){let r=[];return this.Ia.inorderTraversal((e,t)=>{r.push(t)}),r}}class lu{constructor(e,t,r,n,i,s,a,o,l){this.query=e,this.docs=t,this.oldDocs=r,this.docChanges=n,this.mutatedKeys=i,this.fromCache=s,this.syncStateChanged=a,this.excludesMetadataChanges=o,this.hasCachedResults=l}static fromInitialDocuments(e,t,r,n,i){let s=[];return t.forEach(e=>{s.push({type:0,doc:e})}),new lu(e,t,au.emptySet(t),s,r,n,!0,!1,i)}get hasPendingWrites(){return!this.mutatedKeys.isEmpty()}isEqual(e){if(!(this.fromCache===e.fromCache&&this.hasCachedResults===e.hasCachedResults&&this.syncStateChanged===e.syncStateChanged&&this.mutatedKeys.isEqual(e.mutatedKeys)&&ui(this.query,e.query)&&this.docs.isEqual(e.docs)&&this.oldDocs.isEqual(e.oldDocs)))return!1;var t=this.docChanges,r=e.docChanges;if(t.length!==r.length)return!1;for(let n=0;n<t.length;n++)if(t[n].type!==r[n].type||!t[n].doc.isEqual(r[n].doc))return!1;return!0}}class uu{constructor(){this.Aa=void 0,this.Ra=[]}Va(){return this.Ra.some(e=>e.ma())}}class hu{constructor(){this.queries=cu(),this.onlineState="Unknown",this.fa=new Set}terminate(){var e,r,t,n;e=this,r=new I(b.ABORTED,"Firestore shutting down"),n=(t=e).queries,t.queries=cu(),n.forEach((e,t)=>{for(let e of t.Ra)e.onError(r)})}}function cu(){return new mi(e=>hi(e),ui)}async function du(t,r){let e=t,n=3;var i=r.query;let s=e.queries.get(i);s?!s.Va()&&r.ma()&&(n=2):(s=new uu,n=r.ma()?0:1);try{switch(n){case 0:s.Aa=await e.onListen(i,!0);break;case 1:s.Aa=await e.onListen(i,!1);break;case 2:await e.onFirstRemoteStoreListen(i)}}catch(t){let e=su(t,`Initialization of query '${ci(r.query)}' failed`);return void r.onError(e)}e.queries.set(i,s),s.Ra.push(r),r.ga(e.onlineState),s.Aa&&r.pa(s.Aa)&&gu(e)}async function fu(e,t){var r=e,n=t.query;let i=3;var s=r.queries.get(n);if(s){let e=s.Ra.indexOf(t);0<=e&&(s.Ra.splice(e,1),0===s.Ra.length?i=t.ma()?0:1:!s.Va()&&t.ma()&&(i=2))}switch(i){case 0:return r.queries.delete(n),r.onUnlisten(n,!0);case 1:return r.queries.delete(n),r.onUnlisten(n,!1);case 2:return r.onLastRemoteStoreUnlisten(n);default:return}}function gu(e){e.fa.forEach(e=>{e.next()})}(he=he||{}).ya="default",he.Cache="cache";class mu{constructor(e,t,r){this.query=e,this.wa=t,this.ba=!1,this.Sa=null,this.onlineState="Unknown",this.options=r||{}}pa(t){if(!this.options.includeMetadataChanges){let e=[];for(var r of t.docChanges)3!==r.type&&e.push(r);t=new lu(t.query,t.docs,t.oldDocs,e,t.mutatedKeys,t.fromCache,t.syncStateChanged,!0,t.hasCachedResults)}let e=!1;return this.ba?this.Da(t)&&(this.wa.next(t),e=!0):this.va(t,this.onlineState)&&(this.Ca(t),e=!0),this.Sa=t,e}onError(e){this.wa.error(e)}ga(e){this.onlineState=e;let t=!1;return this.Sa&&!this.ba&&this.va(this.Sa,e)&&(this.Ca(this.Sa),t=!0),t}va(e,t){return!e.fromCache||!this.ma()||(!this.options.Fa||!("Offline"!==t))&&(!e.docs.isEmpty()||e.hasCachedResults||"Offline"===t)}Da(e){var t;return 0<e.docChanges.length||(t=this.Sa&&this.Sa.hasPendingWrites!==e.hasPendingWrites,!(!e.syncStateChanged&&!t)&&!0===this.options.includeMetadataChanges)}Ca(e){e=lu.fromInitialDocuments(e.query,e.docs,e.mutatedKeys,e.fromCache,e.hasCachedResults),this.ba=!0,this.wa.next(e)}ma(){return this.options.source!==he.Cache}}class pu{constructor(e,t){this.Ma=e,this.byteLength=t}xa(){return"metadata"in this.Ma}}class yu{constructor(e){this.serializer=e}Bs(e){return Cs(this.serializer,e)}Ls(e){return e.metadata.exists?Os(this.serializer,e.document,!1):k.newNoDocument(this.Bs(e.metadata.name),this.ks(e.metadata.readTime))}ks(e){return F(e)}}class vu{constructor(e,t,r){this.Oa=e,this.localStore=t,this.serializer=r,this.queries=[],this.documents=[],this.collectionGroups=new Set,this.progress=wu(e)}Na(e){this.progress.bytesLoaded+=e.byteLength;let t=this.progress.documentsLoaded;var r;return e.Ma.namedQuery?this.queries.push(e.Ma.namedQuery):e.Ma.documentMetadata?(this.documents.push({metadata:e.Ma.documentMetadata}),e.Ma.documentMetadata.exists||++t,r=T.fromString(e.Ma.documentMetadata.name),this.collectionGroups.add(r.get(r.length-2))):e.Ma.document&&(this.documents[this.documents.length-1].document=e.Ma.document,++t),t!==this.progress.documentsLoaded?(this.progress.documentsLoaded=t,Object.assign({},this.progress)):null}Ba(e){let r=new Map,n=new yu(this.serializer);for(var i of e)if(i.metadata.queries){let e=n.Bs(i.metadata.name);for(let t of i.metadata.queries){var s=(r.get(t)||O()).add(e);r.set(t,s)}}return r}async complete(){let e=await(async(r,n,e,t)=>{let i=r,s=O(),a=pi;for(let r of e){let e=n.Bs(r.metadata.name),t=(r.document&&(s=s.add(e)),n.Ls(r));t.setReadTime(n.ks(r.metadata.readTime)),a=a.insert(e,t)}let o=i.Cs.newChangeBuffer({trackRemovals:!0}),l=await Jo(i,(r=t,ai(ri(T.fromString("__bundle__/docs/"+r)))));return i.persistence.runTransaction("Apply bundle documents","readwrite",t=>Yo(t,o,a).next(e=>(o.apply(t),e)).next(e=>i.ai.removeMatchingKeysForTargetId(t,l.targetId).next(()=>i.ai.addMatchingKeys(t,s,l.targetId)).next(()=>i.localDocuments.getLocalViewOfDocuments(t,e.xs,e.Os)).next(()=>e.xs)))})(this.localStore,new yu(this.serializer),this.documents,this.Oa.id),t=this.Ba(this.documents);for(let e of this.queries)await(async(e,r,n=O())=>{let i=await Jo(e,ai(ra(r.bundledQuery))),s=e;return s.persistence.runTransaction("Save named query","readwrite",e=>{var t=F(r.readTime);return 0<=i.snapshotVersion.compareTo(t)?s.ci.saveNamedQuery(e,r):(t=i.withResumeToken(D.EMPTY_BYTE_STRING,t),s.Ss=s.Ss.insert(t.targetId,t),s.ai.updateTargetData(e,t).next(()=>s.ai.removeMatchingKeysForTargetId(e,i.targetId)).next(()=>s.ai.addMatchingKeys(e,n,i.targetId)).next(()=>s.ci.saveNamedQuery(e,r)))})})(this.localStore,e,t.get(e.name));return this.progress.taskState="Success",{progress:this.progress,La:this.collectionGroups,ka:e}}}function wu(e){return{taskState:"Running",documentsLoaded:0,bytesLoaded:0,totalDocuments:e.totalDocuments,totalBytes:e.totalBytes}}class _u{constructor(e){this.key=e}}class bu{constructor(e){this.key=e}}class Iu{constructor(e,t){this.query=e,this.qa=t,this.Qa=null,this.hasCachedResults=!1,this.current=!1,this.$a=O(),this.mutatedKeys=O(),this.Ua=gi(e),this.Ka=new au(this.Ua)}get Wa(){return this.qa}Ga(e,t){let o=t?t.za:new ou,l=(t||this).Ka,u=(t||this).mutatedKeys,h=l,c=!1,d="F"===this.query.limitType&&l.size===this.query.limit?l.last():null,f="L"===this.query.limitType&&l.size===this.query.limit?l.first():null;if(e.inorderTraversal((e,t)=>{var r=l.get(e),n=di(this.query,t)?t:null,i=!!r&&this.mutatedKeys.has(r.key),s=!!n&&(n.hasLocalMutations||this.mutatedKeys.has(n.key)&&n.hasCommittedMutations);let a=!1;r&&n?r.data.isEqual(n.data)?i!==s&&(o.track({type:3,doc:n}),a=!0):!this.ja(r,n)&&(o.track({type:2,doc:n}),a=!0,d&&0<this.Ua(n,d)||f&&this.Ua(n,f)<0)&&(c=!0):!r&&n?(o.track({type:0,doc:n}),a=!0):r&&!n&&(o.track({type:1,doc:r}),a=!0,d||f)&&(c=!0),a&&(u=n?(h=h.add(n),s?u.add(e):u.delete(e)):(h=h.delete(e),u.delete(e)))}),null!==this.query.limit)for(;h.size>this.query.limit;){let e="F"===this.query.limitType?h.last():h.first();h=h.delete(e.key),u=u.delete(e.key),o.track({type:1,doc:e})}return{Ka:h,za:o,ys:c,mutatedKeys:u}}ja(e,t){return e.hasLocalMutations&&t.hasCommittedMutations&&!t.hasLocalMutations}applyChanges(e,t,r,n){var i=this.Ka,s=(this.Ka=e.Ka,this.mutatedKeys=e.mutatedKeys,e.za.da()),a=(s.sort((e,t)=>{return r=e.type,n=t.type,(i=e=>{switch(e){case 0:return 1;case 2:case 3:return 2;case 1:return 0;default:return E(20277,{Vt:e})}})(r)-i(n)||this.Ua(e.doc,t.doc);var r,n,i}),this.Ha(r),n=null!=n&&n,t&&!n?this.Ja():[]),o=0===this.$a.size&&this.current&&!n?1:0,l=o!==this.Qa;return this.Qa=o,0!==s.length||l?{snapshot:new lu(this.query,e.Ka,i,s,e.mutatedKeys,0==o,l,!1,!!r&&0<r.resumeToken.approximateByteSize()),Ya:a}:{Ya:a}}ga(e){return this.current&&"Offline"===e?(this.current=!1,this.applyChanges({Ka:this.Ka,za:new ou,mutatedKeys:this.mutatedKeys,ys:!1},!1)):{Ya:[]}}Za(e){return!this.qa.has(e)&&!!this.Ka.has(e)&&!this.Ka.get(e).hasLocalMutations}Ha(e){e&&(e.addedDocuments.forEach(e=>this.qa=this.qa.add(e)),e.modifiedDocuments.forEach(e=>{}),e.removedDocuments.forEach(e=>this.qa=this.qa.delete(e)),this.current=e.current)}Ja(){if(!this.current)return[];let t=this.$a,r=(this.$a=O(),this.Ka.forEach(e=>{this.Za(e.key)&&(this.$a=this.$a.add(e.key))}),[]);return t.forEach(e=>{this.$a.has(e)||r.push(new bu(e))}),this.$a.forEach(e=>{t.has(e)||r.push(new _u(e))}),r}Xa(e){this.qa=e.Ns,this.$a=O();var t=this.Ga(e.documents);return this.applyChanges(t,!0)}eu(){return lu.fromInitialDocuments(this.query,this.Ka,this.mutatedKeys,0===this.Qa,this.hasCachedResults)}}let Tu="SyncEngine";class Eu{constructor(e,t,r){this.query=e,this.targetId=t,this.view=r}}class Su{constructor(e){this.key=e,this.tu=!1}}class xu{constructor(e,t,r,n,i,s){this.localStore=e,this.remoteStore=t,this.eventManager=r,this.sharedClientState=n,this.currentUser=i,this.maxConcurrentLimboResolutions=s,this.nu={},this.ru=new mi(e=>hi(e),ui),this.iu=new Map,this.su=new Set,this.ou=new A(x.comparator),this._u=new Map,this.au=new Io,this.uu={},this.cu=new Map,this.lu=Ha.ir(),this.onlineState="Unknown",this.hu=void 0}get isPrimaryClient(){return!0===this.hu}}async function Au(e,t,r,n){var i=await Jo(e.localStore,ai(t)),s=i.targetId,a=e.sharedClientState.addLocalQueryTarget(s,r);let o;return n&&(o=await Cu(e,t,s,"current"===a,i.resumeToken)),e.isPrimaryClient&&r&&ql(e.remoteStore,i),o}async function Cu(n,e,t,r,i){n.Pu=(e,t,r)=>(async(e,t,r,n)=>{let i=t.view.Ga(r);i.ys&&(i=await Zo(e.localStore,t.query,!1).then(({documents:e})=>t.view.Ga(e,i)));var s=n&&n.targetChanges.get(t.targetId),a=n&&null!=n.targetMismatches.get(t.targetId),s=t.view.applyChanges(i,e.isPrimaryClient,s,a);return Pu(e,t.targetId,s.Ya),s.snapshot})(n,e,t,r);var s=await Zo(n.localStore,e,!0),a=new Iu(e,s.Ns),s=a.Ga(s.documents),o=us.createSynthesizedTargetChangeForCurrentChange(t,r&&"Offline"!==n.onlineState,i),s=a.applyChanges(s,n.isPrimaryClient,o),o=(Pu(n,t,s.Ya),new Eu(e,t,a));return n.ru.set(e,o),n.iu.has(t)?n.iu.get(t).push(e):n.iu.set(t,[e]),s.snapshot}async function Du(t,e,r){var n=Gu(t);try{let t=await((e,i)=>{let s=e,a=h.now(),o=i.reduce((e,t)=>e.add(t.key),O()),l,u;return s.persistence.runTransaction("Locally write mutations","readwrite",n=>{let t=pi,r=O();return s.Cs.getEntries(n,o).next(e=>{(t=e).forEach((e,t)=>{t.isValidDocument()||(r=r.add(e))})}).next(()=>s.localDocuments.getOverlayedDocuments(n,t)).next(e=>{l=e;var t=[];for(let r of i){let e=((e,r)=>{let n=null;for(var i of e.fieldTransforms){let e=r.data.field(i.field),t=Ci(i.transform,e||null);null!=t&&(n=null===n?xn.empty():n).set(i.field,t)}return n||null})(r,l.get(r.key).overlayedDocument);null!=e&&t.push(new Gi(r.key,e,function i(e){let s=[];return Mr(e.fields,(e,r)=>{var n=new v([e]);if(wn(r)){let t=i(r.mapValue).fields;if(0===t.length)s.push(n);else for(let e of t)s.push(n.child(e))}else s.push(n)}),new Ur(s)}(e.value.mapValue),L.exists(!0)))}return s.mutationQueue.addMutationBatch(n,a,t,i)}).next(e=>{var t=(u=e).applyToLocalDocumentSet(l,r);return s.documentOverlayCache.saveOverlays(n,e.batchId,t)})}).then(()=>({batchId:u.batchId,changes:wi(l)}))})(n.localStore,e);n.sharedClientState.addPendingMutation(t.batchId);{var i=n;var s=t.batchId;var a=r;let e=i.uu[i.currentUser.toKey()];e=(e=e||new A(S)).insert(s,a),i.uu[i.currentUser.toKey()]=e}await Bu(n,t.changes),await Jl(n.remoteStore)}catch(t){let e=su(t,"Failed to persist write");r.reject(e)}}async function Nu(e,t){let n=e;try{let e=await Wo(n.localStore,t);t.targetChanges.forEach((e,t)=>{var r=n._u.get(t);r&&(y(e.addedDocuments.size+e.modifiedDocuments.size+e.removedDocuments.size<=1,22616),0<e.addedDocuments.size?r.tu=!0:0<e.modifiedDocuments.size?y(r.tu,14607):0<e.removedDocuments.size&&(y(r.tu,42227),r.tu=!1))}),await Bu(n,e,t)}catch(e){await We(e)}}function ku(e,i,t){var s=e;if(s.isPrimaryClient&&0===t||!s.isPrimaryClient&&1===t){let n=[];s.ru.forEach((e,t)=>{var r=t.view.ga(i);r.snapshot&&n.push(r.snapshot)});{t=s.eventManager;var a=i;var o=t;o.onlineState=a;let r=!1;o.queries.forEach((e,t)=>{for(let e of t.Ra)e.ga(a)&&(r=!0)}),r&&gu(o)}n.length&&s.nu.Q_(n),s.onlineState=i,s.isPrimaryClient&&s.sharedClientState.setOnlineState(i)}}async function Ru(e,t,r){var n=e;try{let e=await((e,n)=>{let i=e;return i.persistence.runTransaction("Reject batch","readwrite-primary",t=>{let r;return i.mutationQueue.lookupMutationBatch(t,n).next(e=>(y(null!==e,37113),r=e.keys(),i.mutationQueue.removeMutationBatch(t,e))).next(()=>i.mutationQueue.performConsistencyCheck(t)).next(()=>i.documentOverlayCache.removeOverlaysForBatchId(t,r,n)).next(()=>i.localDocuments.recalculateAndSaveOverlaysForDocumentKeys(t,r)).next(()=>i.localDocuments.getDocuments(t,r))})})(n.localStore,t);Lu(n,t,r),Ou(n,t),n.sharedClientState.updateMutationState(t,"rejected",r),await Bu(n,e)}catch(r){await We(r)}}async function Mu(t,r){let n=t;$l(n.remoteStore)||p(Tu,"The network is disabled. The task returned by 'awaitPendingWrites()' will not complete until the network is enabled.");try{let e=await(e=>{let t=e;return t.persistence.runTransaction("Get highest unacknowledged batch id","readonly",e=>t.mutationQueue.getHighestUnacknowledgedBatchId(e))})(n.localStore);var i;e===ct?r.resolve():((i=n.cu.get(e)||[]).push(r),n.cu.set(e,i))}catch(t){let e=su(t,"Initialization of waitForPendingWrites() operation failed");r.reject(e)}}function Ou(e,t){(e.cu.get(t)||[]).forEach(e=>{e.resolve()}),e.cu.delete(t)}function Lu(e,t,r){var n=e;let i=n.uu[n.currentUser.toKey()];if(i){let e=i.get(t);e&&(r?e.reject(r):e.resolve(),i=i.remove(t)),n.uu[n.currentUser.toKey()]=i}}function Fu(t,e,r=null){t.sharedClientState.removeLocalQueryTarget(e);for(var n of t.iu.get(e))t.ru.delete(n),r&&t.nu.Tu(n,r);t.iu.delete(e),t.isPrimaryClient&&t.au.Ur(e).forEach(e=>{t.au.containsKey(e)||Vu(t,e)})}function Vu(e,t){e.su.delete(t.path.canonicalString());var r=e.ou.get(t);null!==r&&(zl(e.remoteStore,r),e.ou=e.ou.remove(t),e._u.delete(r),Uu(e))}function Pu(e,t,r){for(var n of r)n instanceof _u?(e.au.addReference(n.key,t),i=e,s=n,o=a=void 0,a=s.key,o=a.path.canonicalString(),i.ou.get(a)||i.su.has(o)||(p(Tu,"New document in limbo: "+a),i.su.add(o),Uu(i))):n instanceof bu?(p(Tu,"Document no longer in limbo: "+n.key),e.au.removeReference(n.key,t),e.au.containsKey(n.key)||Vu(e,n.key)):E(19791,{Iu:n});var i,s,a,o}function Uu(e){for(;0<e.su.size&&e.ou.size<e.maxConcurrentLimboResolutions;){var t=e.su.values().next().value,t=(e.su.delete(t),new x(T.fromString(t))),r=e.lu.next();e._u.set(r,new Su(t)),e.ou=e.ou.insert(t,r),ql(e.remoteStore,new $s(ai(ri(t.path)),r,"TargetPurposeLimboResolution",ht.le))}}async function Bu(e,t,i){let s=e,a=[],o=[],r=[];if(!s.ru.isEmpty()){s.ru.forEach((e,n)=>{r.push(s.Pu(n,t,i).then(t=>{var r;if((t||i)&&s.isPrimaryClient){let e=t?!t.fromCache:null==(r=null==i?void 0:i.targetChanges.get(n.targetId))?void 0:r.current;s.sharedClientState.updateQueryState(n.targetId,e?"current":"not-current")}if(t){a.push(t);let e=Uo.Ps(n.targetId,t);o.push(e)}}))}),await Promise.all(r),s.nu.Q_(a);{var n=s.localStore,l=o;let i=n;try{await i.persistence.runTransaction("notifyLocalViewChanges","readwrite",r=>w.forEach(l,t=>w.forEach(t.ls,e=>i.persistence.referenceDelegate.addReference(r,t.targetId,e)).next(()=>w.forEach(t.hs,e=>i.persistence.referenceDelegate.removeReference(r,t.targetId,e)))))}catch(n){if(!rt(n))throw n;p(zo,"Failed to update sequence numbers: "+n)}for(let e of l){let n=e.targetId;if(!e.fromCache){let e=i.Ss.get(n),t=e.snapshotVersion,r=e.withLastLimboFreeSnapshotVersion(t);i.Ss=i.Ss.insert(n,r)}}}}}async function qu(e,t,r,n){var i=e,s=await((e,r)=>{let n=e,i=n.mutationQueue;return n.persistence.runTransaction("Lookup mutation documents","readonly",t=>i.Hn(t,r).next(e=>e?n.localDocuments.getDocuments(t,e):w.resolve(null)))})(i.localStore,t);null!==s?("pending"===r?await Jl(i.remoteStore):"acknowledged"===r||"rejected"===r?(Lu(i,t,n||null),Ou(i,t),i.localStore.mutationQueue.Xn(t)):E(6720,"Unknown batchState",{Eu:r}),await Bu(i,s)):p(Tu,"Cannot apply mutation batch with id: "+t)}async function zu(r,e){var n,i,s,a=r,o=[],l=[];for(let r of e){let t,e=a.iu.get(r);if(e&&0!==e.length){t=await Jo(a.localStore,ai(e[0]));for(let r of e){let e=a.ru.get(r),t=(n=e,s=i=void 0,s=await Zo((i=a).localStore,n.query,!0),s=n.view.Xa(s),i.isPrimaryClient&&Pu(i,n.targetId,s.Ya),await s);t.snapshot&&l.push(t.snapshot)}}else{let e=await el(a.localStore,r);t=await Jo(a.localStore,e),await Cu(a,ju(e),r,!1,t.resumeToken)}o.push(t)}return a.nu.Q_(l),o}function ju(e){return ti(e.path,e.collectionGroup,e.orderBy,e.filters,e.limit,"F",e.startAt,e.endAt)}function Ku(e){var t=e;return t.remoteStore.remoteSyncer.applyRemoteEvent=Nu.bind(null,t),t.remoteStore.remoteSyncer.getRemoteKeysForTarget=(function(e,t){let n=e,r=n._u.get(t);if(r&&r.tu)return O().add(r.key);{let r=O(),e=n.iu.get(t);if(e)for(let t of e){let e=n.ru.get(t);r=r.unionWith(e.view.Wa)}return r}}).bind(null,t),t.remoteStore.remoteSyncer.rejectListen=(async function(e,n,t){let i=e,r=(i.sharedClientState.updateQueryState(n,"rejected",t),i._u.get(n)),s=r&&r.key;if(s){let e=new A(x.comparator),t=(e=e.insert(s,k.newNoDocument(s,g.min())),O().add(s)),r=new ls(g.min(),new Map,new A(S),e,t);await Nu(i,r),i.ou=i.ou.remove(s),i._u.delete(n),Uu(i)}else await Xo(i.localStore,n,!1).then(()=>Fu(i,n,t)).catch(We)}).bind(null,t),t.nu.Q_=(function(r,e){var n=r;let i=!1;for(let r of e){let e=r.query,t=n.queries.get(e);if(t){for(let e of t.Ra)e.pa(r)&&(i=!0);t.Aa=r}}i&&gu(n)}).bind(null,t.eventManager),t.nu.Tu=(function(e,t,r){var n=e,i=n.queries.get(t);if(i)for(let e of i.Ra)e.onError(r);n.queries.delete(t)}).bind(null,t.eventManager),t}function Gu(e){var t=e;return t.remoteStore.remoteSyncer.applySuccessfulWrite=(async function(e,t){var r=e,n=t.batch.batchId;try{let e=await $o(r.localStore,t);Lu(r,n,null),Ou(r,n),r.sharedClientState.updateMutationState(n,"acknowledged"),await Bu(r,e)}catch(e){await We(e)}}).bind(null,t),t.remoteStore.remoteSyncer.rejectFailedWrite=Ru.bind(null,t),t}function Qu(e,t,r){let n=e;(async(e,r,n)=>{try{var i=await r.getMetadata();if(await((e,t)=>{let r=e,n=F(t.createTime);return r.persistence.runTransaction("hasNewerBundle","readonly",e=>r.ci.getBundleMetadata(e,t.id)).then(e=>!!e&&0<=e.createTime.compareTo(n))})(e.localStore,i))return await r.close(),n._completeWith({taskState:"Success",documentsLoaded:i.totalDocuments,bytesLoaded:i.totalBytes,totalDocuments:i.totalDocuments,totalBytes:i.totalBytes}),Promise.resolve(new Set);n._updateProgress(wu(i));var s=new vu(i,e.localStore,r.serializer);let t=await r.du();for(;t;){let e=await s.Na(t);e&&n._updateProgress(e),t=await r.du()}var a=await s.complete();return await Bu(e,a.ka,void 0),await((e,t)=>{let r=e;return r.persistence.runTransaction("Save bundle","readwrite",e=>r.ci.saveBundleMetadata(e,t))})(e.localStore,i),n._completeWith(a.progress),Promise.resolve(a.La)}catch(e){return pe(Tu,"Loading bundle failed with "+e),n._failWith(e),Promise.resolve(new Set)}})(n,t,r).then(e=>{n.sharedClientState.notifyBundleLoaded(e)})}class $u{constructor(){this.kind="memory",this.synchronizeTabs=!1}async initialize(e){this.serializer=Dl(e.databaseInfo.databaseId),this.sharedClientState=this.Au(e),this.persistence=this.Ru(e),await this.persistence.start(),this.localStore=this.Vu(e),this.gcScheduler=this.mu(e,this.localStore),this.indexBackfillerScheduler=this.fu(e,this.localStore)}mu(e,t){return null}fu(e,t){return null}Vu(e){return Go(this.persistence,new qo,e.initialUser,this.serializer)}Ru(e){return new Ao(Do.Ei,this.serializer)}Au(e){return new pl}async terminate(){var e;null!=(e=this.gcScheduler)&&e.stop(),null!=(e=this.indexBackfillerScheduler)&&e.stop(),this.sharedClientState.shutdown(),await this.persistence.shutdown()}}$u.provider={build:()=>new $u};class Hu extends $u{constructor(e){super(),this.cacheSizeBytes=e}mu(e,t){y(this.persistence.referenceDelegate instanceof No,46915);var r=this.persistence.referenceDelegate.garbageCollector;return new ro(r,e.asyncQueue,t)}Ru(e){let t=void 0!==this.cacheSizeBytes?Ba.withCacheSize(this.cacheSizeBytes):Ba.DEFAULT;return new Ao(e=>No.Ei(e,t),this.serializer)}}class Wu extends $u{constructor(e,t,r){super(),this.gu=e,this.cacheSizeBytes=t,this.forceOwnership=r,this.kind="persistent",this.synchronizeTabs=!1}async initialize(e){await super.initialize(e),await this.gu.initialize(this,e),await Gu(this.gu.syncEngine),await Jl(this.gu.remoteStore),await this.persistence.Ki(()=>(this.gcScheduler&&!this.gcScheduler.started&&this.gcScheduler.start(),this.indexBackfillerScheduler&&!this.indexBackfillerScheduler.started&&this.indexBackfillerScheduler.start(),Promise.resolve()))}Vu(e){return Go(this.persistence,new qo,e.initialUser,this.serializer)}mu(e,t){var r=this.persistence.referenceDelegate.garbageCollector;return new ro(r,e.asyncQueue,t)}fu(e,t){var r=new ut(t,this.persistence);return new lt(e.asyncQueue,r)}Ru(e){var t=Po(e.databaseInfo.databaseId,e.databaseInfo.persistenceKey),r=void 0!==this.cacheSizeBytes?Ba.withCacheSize(this.cacheSizeBytes):Ba.DEFAULT;return new Lo(this.synchronizeTabs,t,e.clientId,r,e.asyncQueue,Al(),Cl(),this.serializer,this.sharedClientState,!!this.forceOwnership)}Au(e){return new pl}}class Yu extends Wu{constructor(e,t){super(e,t,!1),this.gu=e,this.cacheSizeBytes=t,this.synchronizeTabs=!0}async initialize(e){await super.initialize(e);var t=this.gu.syncEngine;this.sharedClientState instanceof ml&&(this.sharedClientState.syncEngine={yo:qu.bind(null,t),wo:(async function(e,r,n,t){var i=e;if(i.hu)p(Tu,"Ignoring unexpected query state notification.");else{var s=i.iu.get(r);if(s&&0<s.length)switch(n){case"current":case"not-current":{let e=await tl(i.localStore,fi(s[0])),t=ls.createSynthesizedRemoteEventForCurrentChange(r,"current"===n,D.EMPTY_BYTE_STRING);await Bu(i,e,t);break}case"rejected":await Xo(i.localStore,r,!0),Fu(i,r,t);break;default:E(64155,n)}}}).bind(null,t),bo:(async function(e,t,n){let i=Ku(e);if(i.hu){for(let r of t)if(i.iu.has(r)&&i.sharedClientState.isActiveQueryTarget(r))p(Tu,"Adding an already active target "+r);else{let e=await el(i.localStore,r),t=await Jo(i.localStore,e);await Cu(i,ju(e),t.targetId,!1,t.resumeToken),ql(i.remoteStore,t)}for(let r of n)i.iu.has(r)&&await Xo(i.localStore,r,!1).then(()=>{zl(i.remoteStore,r),Fu(i,r)}).catch(We)}}).bind(null,t),us:(function(e){return e.localStore.persistence.us()}).bind(null,t),po:(async function(e,t){let r=e;return tl(r.localStore,t).then(e=>Bu(r,e))}).bind(null,t)},await this.sharedClientState.start()),await this.persistence.Ki(async e=>{{var t=this.gu.syncEngine,s=e;let i=t;if(Ku(i),Gu(i),!0===s&&!0!==i.hu){let e=i.sharedClientState.getAllActiveQueryTargets(),t=await zu(i,e.toArray());i.hu=!0,await tu(i.remoteStore,!0);for(let e of t)ql(i.remoteStore,e)}else if(!1===s&&!1!==i.hu){let r=[],n=Promise.resolve();i.iu.forEach((e,t)=>{i.sharedClientState.isLocalQueryTarget(t)?r.push(t):n=n.then(()=>(Fu(i,t),Xo(i.localStore,t,!0))),zl(i.remoteStore,t)}),await n,await zu(i,r);{s=i;let r=s;r._u.forEach((e,t)=>{zl(r.remoteStore,t)}),r.au.Kr(),r._u=new Map,r.ou=new A(x.comparator)}i.hu=!1,await tu(i.remoteStore,!1)}}await 0,this.gcScheduler&&(e&&!this.gcScheduler.started?this.gcScheduler.start():e||this.gcScheduler.stop()),this.indexBackfillerScheduler&&(e&&!this.indexBackfillerScheduler.started?this.indexBackfillerScheduler.start():e||this.indexBackfillerScheduler.stop())})}Au(e){var t,r=Al();if(ml.C(r))return t=Po(e.databaseInfo.databaseId,e.databaseInfo.persistenceKey),new ml(r,e.asyncQueue,t,e.clientId,e.initialUser);throw new I(b.UNIMPLEMENTED,"IndexedDB persistence is only available on platforms that support LocalStorage.")}}class Ju{async initialize(e,t){this.localStore||(this.localStore=e.localStore,this.sharedClientState=e.sharedClientState,this.datastore=this.createDatastore(t),this.remoteStore=this.createRemoteStore(t),this.eventManager=this.createEventManager(t),this.syncEngine=this.createSyncEngine(t,!e.synchronizeTabs),this.sharedClientState.onlineStateHandler=e=>ku(this.syncEngine,e,1),this.remoteStore.remoteSyncer.handleCredentialChange=(async function(e,t){var r,n,i=e;if(!i.currentUser.isEqual(t)){p(Tu,"User change. New user:",t.toKey());let e=await Qo(i.localStore,t);i.currentUser=t,n="'waitForPendingWrites' promise is rejected due to a user change.",(r=i).cu.forEach(e=>{e.forEach(e=>{e.reject(new I(b.CANCELLED,n))})}),r.cu.clear(),i.sharedClientState.handleUserChange(t,e.removedBatchIds,e.addedBatchIds),await Bu(i,e.Ms)}}).bind(null,this.syncEngine),await tu(this.remoteStore,this.syncEngine.isPrimaryClient))}createEventManager(e){return new hu}createDatastore(e){var t,r,n,i=Dl(e.databaseInfo.databaseId),s=(t=e.databaseInfo,new xl(t));return t=e.authCredentials,e=e.appCheckCredentials,r=s,n=i,new Ll(t,e,r,n)}createRemoteStore(e){return t=this.localStore,r=this.datastore,e=e.asyncQueue,n=e=>ku(this.syncEngine,e,0),i=new(wl.C()?wl:yl),new Pl(t,r,e,n,i);var t,r,n,i}createSyncEngine(e,t){return r=this.localStore,n=this.remoteStore,i=this.eventManager,s=this.sharedClientState,a=e.initialUser,e=e.maxConcurrentLimboResolutions,t=t,o=new xu(r,n,i,s,a,e),t&&(o.hu=!0),o;var r,n,i,s,a,o}async terminate(){var e,t;e=this.remoteStore,t=e,p(Vl,"RemoteStore shutting down."),t.aa.add(5),await Bl(t),t.ca.shutdown(),await!t.la.set("Unknown"),null!=(t=this.datastore)&&t.terminate(),null!=(t=this.eventManager)&&t.terminate()}}function Xu(t,r=10240){let n=0;return{async read(){var e;return n<t.byteLength?(e={value:t.slice(n,n+r),done:!1},n+=r,e):{done:!0}},async cancel(){},releaseLock(){},closed:Promise.resolve()}}Ju.provider={build:()=>new Ju};class Zu{constructor(e){this.observer=e,this.muted=!1}next(e){this.muted||this.observer.next&&this.pu(this.observer.next,e)}error(e){this.muted||(this.observer.error?this.pu(this.observer.error,e):d("Uncaught Error in snapshot listener:",e.toString()))}yu(){this.muted=!0}pu(e,t){setTimeout(()=>{this.muted||e(t)},0)}}class eh{constructor(e,t){this.wu=e,this.serializer=t,this.metadata=new f,this.buffer=new Uint8Array,this.bu=new TextDecoder("utf-8"),this.Su().then(e=>{e&&e.xa()?this.metadata.resolve(e.Ma.metadata):this.metadata.reject(new Error(`The first element of the bundle is not a metadata, it is
             `+JSON.stringify(null==e?void 0:e.Ma)))},e=>this.metadata.reject(e))}close(){return this.wu.cancel()}async getMetadata(){return this.metadata.promise}async du(){return await this.getMetadata(),this.Su()}async Su(){var e,t,r=await this.Du();return null===r?null:(t=this.bu.decode(r),e=Number(t),isNaN(e)&&this.vu(`length string (${t}) is not valid number`),t=await this.Cu(e),new pu(JSON.parse(t),r.length+e))}Fu(){return this.buffer.findIndex(e=>e==="{".charCodeAt(0))}async Du(){for(;this.Fu()<0&&!await this.Mu(););var e,t;return 0===this.buffer.length?null:((e=this.Fu())<0&&this.vu("Reached the end of bundle when a length string is expected."),t=this.buffer.slice(0,e),this.buffer=this.buffer.slice(e),t)}async Cu(e){for(;this.buffer.length<e;)await this.Mu()&&this.vu("Reached the end of bundle when more is expected.");var t=this.bu.decode(this.buffer.slice(0,e));return this.buffer=this.buffer.slice(e),t}vu(e){throw this.wu.cancel(),new Error("Invalid bundle format: "+e)}async Mu(){var e,t=await this.wu.read();return t.done||((e=new Uint8Array(this.buffer.length+t.value.length)).set(this.buffer),e.set(t.value,this.buffer.length),this.buffer=e),t.done}}class th{constructor(e){this.datastore=e,this.readVersions=new Map,this.mutations=[],this.committed=!1,this.lastTransactionError=null,this.writtenDocs=new Set}async lookup(e){if(this.ensureCommitNotCalled(),0<this.mutations.length)throw this.lastTransactionError=new I(b.INVALID_ARGUMENT,"Firestore transactions require all reads to be executed before all writes."),this.lastTransactionError;var t=await(async(e,t)=>{let l=e,r={documents:t.map(e=>As(l.serializer,e))},n=await l.Wo("BatchGetDocuments",l.serializer.databaseId,T.emptyPath(),r,t.length),u=new Map,i=(n.forEach(e=>{t=l.serializer;var t,r,n,i,s,a,o="found"in(e=e)?(r=t,y(!!(n=e).found,43571),n.found.name,n.found.updateTime,i=Cs(r,n.found.name),s=F(n.found.updateTime),o=n.found.createTime?F(n.found.createTime):g.min(),a=new xn({mapValue:{fields:n.found.fields}}),k.newFoundDocument(i,s,o,a)):"missing"in e?(r=t,y(!!(n=e).missing,3894),y(!!n.readTime,22933),i=Cs(r,n.missing),s=F(n.readTime),k.newNoDocument(i,s)):E(7234,{result:e});u.set(o.key.toString(),o)}),[]);return t.forEach(e=>{var t=u.get(e.toString());y(!!t,55234,{key:e}),i.push(t)}),i})(this.datastore,e);return t.forEach(e=>this.recordVersion(e)),t}set(e,t){this.write(t.toMutation(e,this.precondition(e))),this.writtenDocs.add(e.toString())}update(e,t){try{this.write(t.toMutation(e,this.preconditionForUpdate(e)))}catch(e){this.lastTransactionError=e}this.writtenDocs.add(e.toString())}delete(e){this.write(new Wi(e,this.precondition(e))),this.writtenDocs.add(e.toString())}async commit(){if(this.ensureCommitNotCalled(),this.lastTransactionError)throw this.lastTransactionError;let t=this.readVersions;this.mutations.forEach(e=>{t.delete(e.key.toString())}),t.forEach((e,t)=>{var r=x.fromPath(t);this.mutations.push(new Yi(r,this.precondition(r)))});{var r=this.datastore,n=this.mutations;let t=r,e={writes:n.map(e=>Ls(t.serializer,e))};await t.Qo("Commit",t.serializer.databaseId,T.emptyPath(),e)}await 0,this.committed=!0}recordVersion(e){let t;if(e.isFoundDocument())t=e.version;else{if(!e.isNoDocument())throw E(50498,{xu:e.constructor.name});t=g.min()}var r=this.readVersions.get(e.key.toString());if(r){if(!t.isEqual(r))throw new I(b.ABORTED,"Document version changed between two reads.")}else this.readVersions.set(e.key.toString(),t)}precondition(e){var t=this.readVersions.get(e.toString());return!this.writtenDocs.has(e.toString())&&t?t.isEqual(g.min())?L.exists(!1):L.updateTime(t):L.none()}preconditionForUpdate(e){var t=this.readVersions.get(e.toString());if(this.writtenDocs.has(e.toString())||!t)return L.exists(!0);if(t.isEqual(g.min()))throw new I(b.INVALID_ARGUMENT,"Can't update a document that doesn't exist.");return L.updateTime(t)}write(e){this.ensureCommitNotCalled(),this.mutations.push(e)}ensureCommitNotCalled(){}}class rh{constructor(e,t,r,n,i){this.asyncQueue=e,this.datastore=t,this.options=r,this.updateFunction=n,this.deferred=i,this.Ou=r.maxAttempts,this.y_=new Nl(this.asyncQueue,"transaction_retry")}Nu(){--this.Ou,this.Bu()}Bu(){this.y_.E_(async()=>{let t=new th(this.datastore),e=this.Lu(t);e&&e.then(e=>{this.asyncQueue.enqueueAndForget(()=>t.commit().then(()=>{this.deferred.resolve(e)}).catch(e=>{this.ku(e)}))}).catch(e=>{this.ku(e)})})}Lu(e){try{var t=this.updateFunction(e);return!dt(t)&&t.catch&&t.then?t:(this.deferred.reject(Error("Transaction callback must return a Promise")),null)}catch(e){return this.deferred.reject(e),null}}ku(e){0<this.Ou&&this.qu(e)?(--this.Ou,this.asyncQueue.enqueueAndForget(()=>(this.Bu(),Promise.resolve()))):this.deferred.reject(e)}qu(e){var t;return"FirebaseError"===e.name&&("aborted"===(t=e.code)||"failed-precondition"===t||"already-exists"===t||!ts(t))}}let nh="FirestoreClient";class ih{constructor(e,t,r,n,i){this.authCredentials=e,this.appCheckCredentials=t,this.asyncQueue=r,this.databaseInfo=n,this.user=u.UNAUTHENTICATED,this.clientId=Ce.newId(),this.authCredentialListener=()=>Promise.resolve(),this.appCheckCredentialListener=()=>Promise.resolve(),this._uninitializedComponentsProvider=i,this.authCredentials.start(r,async e=>{p(nh,"Received user=",e.uid),await this.authCredentialListener(e),this.user=e}),this.appCheckCredentials.start(r,e=>(p(nh,"Received new app check token=",e),this.appCheckCredentialListener(e,this.user)))}get configuration(){return{asyncQueue:this.asyncQueue,databaseInfo:this.databaseInfo,clientId:this.clientId,authCredentials:this.authCredentials,appCheckCredentials:this.appCheckCredentials,initialUser:this.user,maxConcurrentLimboResolutions:100}}setCredentialChangeListener(e){this.authCredentialListener=e}setAppCheckTokenChangeListener(e){this.appCheckCredentialListener=e}terminate(){this.asyncQueue.enterRestrictedMode();let r=new f;return this.asyncQueue.enqueueAndForgetEvenWhileRestricted(async()=>{try{this._onlineComponents&&await this._onlineComponents.terminate(),this._offlineComponents&&await this._offlineComponents.terminate(),this.authCredentials.shutdown(),this.appCheckCredentials.shutdown(),r.resolve()}catch(e){var t=su(e,"Failed to shutdown persistence");r.reject(t)}}),r.promise}}async function sh(e,t){e.asyncQueue.verifyOperationInProgress(),p(nh,"Initializing OfflineComponentProvider");var r=e.configuration;await t.initialize(r);let n=r.initialUser;e.setCredentialChangeListener(async e=>{n.isEqual(e)||(await Qo(t.localStore,e),n=e)}),t.persistence.setDatabaseDeletedListener(()=>e.terminate()),e._offlineComponents=t}async function ah(e,r){e.asyncQueue.verifyOperationInProgress();var t=await oh(e);p(nh,"Initializing OnlineComponentProvider"),await r.initialize(t,e.configuration),e.setCredentialChangeListener(e=>eu(r.remoteStore,e)),e.setAppCheckTokenChangeListener((e,t)=>eu(r.remoteStore,t)),e._onlineComponents=r}async function oh(t){if(!t._offlineComponents)if(t._uninitializedComponentsProvider){p(nh,"Using user provided OfflineComponentProvider");try{await sh(t,t._uninitializedComponentsProvider._offline)}catch(e){var r=e;if(!("FirebaseError"===(n=r).name?n.code===b.FAILED_PRECONDITION||n.code===b.UNIMPLEMENTED:!("undefined"!=typeof DOMException&&n instanceof DOMException)||22===n.code||20===n.code||11===n.code))throw r;pe("Error using user provided cache. Falling back to memory cache: "+r),await sh(t,new $u)}}else p(nh,"Using default OfflineComponentProvider"),await sh(t,new Hu(void 0));var n;return t._offlineComponents}async function lh(e){return e._onlineComponents||(e._uninitializedComponentsProvider?(p(nh,"Using user provided OnlineComponentProvider"),await ah(e,e._uninitializedComponentsProvider._online)):(p(nh,"Using default OnlineComponentProvider"),await ah(e,new Ju))),e._onlineComponents}function uh(e){return oh(e).then(e=>e.persistence)}function hh(e){return oh(e).then(e=>e.localStore)}function ch(e){return lh(e).then(e=>e.remoteStore)}function dh(e){return lh(e).then(e=>e.syncEngine)}async function fh(e){var t=await lh(e),r=t.eventManager;return r.onListen=(async function(e,t,r=!0){var n=Ku(e);let i;var s=n.ru.get(t);return i=s?(n.sharedClientState.addLocalQueryTarget(s.targetId),s.view.eu()):await Au(n,t,r,!0)}).bind(null,t.syncEngine),r.onUnlisten=(async function(e,t,r){let n=e,i=n.ru.get(t),s=n.iu.get(i.targetId);1<s.length?(n.iu.set(i.targetId,s.filter(e=>!ui(e,t))),n.ru.delete(t)):n.isPrimaryClient?(n.sharedClientState.removeLocalQueryTarget(i.targetId),n.sharedClientState.isActiveQueryTarget(i.targetId)||await Xo(n.localStore,i.targetId,!1).then(()=>{n.sharedClientState.clearQueryState(i.targetId),r&&zl(n.remoteStore,i.targetId),Fu(n,i.targetId)}).catch(We)):(Fu(n,i.targetId),await Xo(n.localStore,i.targetId,!0))}).bind(null,t.syncEngine),r.onFirstRemoteStoreListen=(async function(e,t){await Au(Ku(e),t,!0,!1)}).bind(null,t.syncEngine),r.onLastRemoteStoreUnlisten=(async function(e,t){var r=e,n=r.ru.get(t),i=r.iu.get(n.targetId);r.isPrimaryClient&&1===i.length&&(r.sharedClientState.removeLocalQueryTarget(n.targetId),zl(r.remoteStore,n.targetId))}).bind(null,t.syncEngine),r}function gh(r){return r.asyncQueue.enqueue(async()=>{var e=await uh(r),t=await ch(r);return e.setNetworkEnabled(!1),(async e=>{var t=e;t.aa.add(0),await Bl(t),t.la.set("Offline")})(t)})}function mh(e,t){let r=new f;return e.asyncQueue.enqueueAndForget(async()=>(async(e,t,r)=>{try{var n=await((e,t)=>{let r=e;return r.persistence.runTransaction("read document","readonly",e=>r.localDocuments.getDocument(e,t))})(e,t);n.isFoundDocument()?r.resolve(n):n.isNoDocument()?r.resolve(null):r.reject(new I(b.UNAVAILABLE,"Failed to get document from cache. (However, this document may exist on the server. Run again without setting 'source' in the GetOptions to attempt to retrieve the document from the server.)"))}catch(e){n=su(e,`Failed to get document '${t} from cache`);r.reject(n)}})(await hh(e),t,r)),r.promise}function ph(e,t,u={}){let h=new f;return e.asyncQueue.enqueueAndForget(async()=>{{var i=await fh(e),s=e.asyncQueue,a=t,o=u,l=h;let r=new Zu({next:e=>{r.yu(),s.enqueueAndForget(()=>fu(i,n));var t=e.docs.has(a);!t&&e.fromCache?l.reject(new I(b.UNAVAILABLE,"Failed to get document because the client is offline.")):t&&e.fromCache&&o&&"server"===o.source?l.reject(new I(b.UNAVAILABLE,'Failed to get document from server. (However, this document does exist in the local cache. Run again without setting source to "server" to retrieve the cached document.)')):l.resolve(e)},error:e=>l.reject(e)}),n=new mu(ri(a.path),r,{includeMetadataChanges:!0,Fa:!0});return du(i,n)}}),h.promise}function yh(e,t){let r=new f;return e.asyncQueue.enqueueAndForget(async()=>(async(e,t,r)=>{try{var n=await Zo(e,t,!0),i=new Iu(t,n.Ns),s=i.Ga(n.documents),a=i.applyChanges(s,!1);r.resolve(a.snapshot)}catch(e){n=su(e,`Failed to execute query '${t} against cache`);r.reject(n)}})(await hh(e),t,r)),r.promise}function vh(o,l,u={}){let h=new f;return o.asyncQueue.enqueueAndForget(async()=>{{var n=await fh(o),i=o.asyncQueue,e=l,s=u,a=h;let t=new Zu({next:e=>{t.yu(),i.enqueueAndForget(()=>fu(n,r)),e.fromCache&&"server"===s.source?a.reject(new I(b.UNAVAILABLE,'Failed to get documents from server. (However, these documents may exist in the local cache. Run again without setting source to "server" to retrieve the cached documents.)')):a.resolve(e)},error:e=>a.reject(e)}),r=new mu(e,t,{includeMetadataChanges:!0,Fa:!0});return du(n,r)}}),h.promise}function wh(r,e){let n=new Zu(e);return r.asyncQueue.enqueueAndForget(async()=>{return e=await fh(r),t=n,e.fa.add(t),void t.next();var e,t}),()=>{n.yu(),r.asyncQueue.enqueueAndForget(async()=>{var e,t;e=await fh(r),t=n,e.fa.delete(t)})}}function _h(e,t,r,n){r=r,t=Dl(t),s="string"==typeof r?Ae().encode(r):r,r=((e,t)=>{if(e instanceof Uint8Array)return Xu(e,t);if(e instanceof ArrayBuffer)return Xu(new Uint8Array(e),t);if(e instanceof ReadableStream)return e.getReader();throw new Error("Source of `toByteStreamReader` has to be a ArrayBuffer or ReadableStream")})(s),t=t;let i=new eh(r,t);var s;e.asyncQueue.enqueueAndForget(async()=>{Qu(await dh(e),i,n)})}function bh(n,i){return n.asyncQueue.enqueue(async()=>{{var e=await hh(n),r=i;let t=e;return t.persistence.runTransaction("Get named query","readonly",e=>t.ci.getNamedQuery(e,r))}})}function Ih(e){var t={};return void 0!==e.timeoutSeconds&&(t.timeoutSeconds=e.timeoutSeconds),t}let Th=new Map;function Eh(e,t,r){if(!r)throw new I(b.INVALID_ARGUMENT,`Function ${e}() cannot be called with an empty ${t}.`)}function Sh(e,t,r,n){if(!0===t&&!0===n)throw new I(b.INVALID_ARGUMENT,e+` and ${r} cannot be used together.`)}function xh(e){if(!x.isDocumentKey(e))throw new I(b.INVALID_ARGUMENT,`Invalid document reference. Document references must have an even number of segments, but ${e} has ${e.length}.`)}function Ah(e){if(x.isDocumentKey(e))throw new I(b.INVALID_ARGUMENT,`Invalid collection reference. Collection references must have an odd number of segments, but ${e} has ${e.length}.`)}function Ch(e){var t,r;return void 0===e?"undefined":null===e?"null":"string"==typeof e?(20<e.length&&(e=e.substring(0,20)+"..."),JSON.stringify(e)):"number"==typeof e||"boolean"==typeof e?""+e:"object"==typeof e?e instanceof Array?"an array":(t=(r=e).constructor?r.constructor.name:null)?`a custom ${t} object`:"an object":"function"==typeof e?"a function":E(12329,{type:typeof e})}function V(e,t){if((e="_delegate"in e?e._delegate:e)instanceof t)return e;if(t.name===e.constructor.name)throw new I(b.INVALID_ARGUMENT,"Type does not match the expected instance. Did you pass a reference from a different Firestore SDK?");var r=Ch(e);throw new I(b.INVALID_ARGUMENT,`Expected type '${t.name}', but it was: `+r)}function Dh(e,t){if(t<=0)throw new I(b.INVALID_ARGUMENT,`Function ${e}() requires a positive number, but it was: ${t}.`)}let Nh="firestore.googleapis.com";class kh{constructor(e){var t;if(void 0===e.host){if(void 0!==e.ssl)throw new I(b.INVALID_ARGUMENT,"Can't provide ssl option if host option is not set");this.host=Nh,this.ssl=!0}else this.host=e.host,this.ssl=null==(t=e.ssl)||t;if(this.credentials=e.credentials,this.ignoreUndefinedProperties=!!e.ignoreUndefinedProperties,this.localCache=e.localCache,void 0===e.cacheSizeBytes)this.cacheSizeBytes=41943040;else{if(-1!==e.cacheSizeBytes&&e.cacheSizeBytes<1048576)throw new I(b.INVALID_ARGUMENT,"cacheSizeBytes must be at least 1048576");this.cacheSizeBytes=e.cacheSizeBytes}Sh("experimentalForceLongPolling",e.experimentalForceLongPolling,"experimentalAutoDetectLongPolling",e.experimentalAutoDetectLongPolling),this.experimentalForceLongPolling=!!e.experimentalForceLongPolling,this.experimentalForceLongPolling?this.experimentalAutoDetectLongPolling=!1:void 0===e.experimentalAutoDetectLongPolling?this.experimentalAutoDetectLongPolling=!0:this.experimentalAutoDetectLongPolling=!!e.experimentalAutoDetectLongPolling,this.experimentalLongPollingOptions=Ih(null!=(t=e.experimentalLongPollingOptions)?t:{});var r=this.experimentalLongPollingOptions;if(void 0!==r.timeoutSeconds){if(isNaN(r.timeoutSeconds))throw new I(b.INVALID_ARGUMENT,`invalid long polling timeout: ${r.timeoutSeconds} (must not be NaN)`);if(r.timeoutSeconds<5)throw new I(b.INVALID_ARGUMENT,`invalid long polling timeout: ${r.timeoutSeconds} (minimum allowed value is 5)`);if(30<r.timeoutSeconds)throw new I(b.INVALID_ARGUMENT,`invalid long polling timeout: ${r.timeoutSeconds} (maximum allowed value is 30)`)}this.useFetchStreams=!!e.useFetchStreams}isEqual(e){return this.host===e.host&&this.ssl===e.ssl&&this.credentials===e.credentials&&this.cacheSizeBytes===e.cacheSizeBytes&&this.experimentalForceLongPolling===e.experimentalForceLongPolling&&this.experimentalAutoDetectLongPolling===e.experimentalAutoDetectLongPolling&&(t=this.experimentalLongPollingOptions,r=e.experimentalLongPollingOptions,t.timeoutSeconds===r.timeoutSeconds)&&this.ignoreUndefinedProperties===e.ignoreUndefinedProperties&&this.useFetchStreams===e.useFetchStreams;var t,r}}class Rh{constructor(e,t,r,n){this._authCredentials=e,this._appCheckCredentials=t,this._databaseId=r,this._app=n,this.type="firestore-lite",this._persistenceKey="(lite)",this._settings=new kh({}),this._settingsFrozen=!1,this._emulatorOptions={},this._terminateTask="notTerminated"}get app(){if(this._app)return this._app;throw new I(b.FAILED_PRECONDITION,"Firestore was not initialized using the Firebase SDK. 'app' is not available")}get _initialized(){return this._settingsFrozen}get _terminated(){return"notTerminated"!==this._terminateTask}_setSettings(e){if(this._settingsFrozen)throw new I(b.FAILED_PRECONDITION,"Firestore has already been started and its settings can no longer be changed. You can only modify settings before calling any other methods on a Firestore object.");this._settings=new kh(e),this._emulatorOptions=e.emulatorOptions||{},void 0!==e.credentials&&(this._authCredentials=(e=>{if(!e)return new _e;switch(e.type){case"firstParty":return new Ee(e.sessionIndex||"0",e.iamToken||null,e.authTokenFactory||null);case"provider":return e.client;default:throw new I(b.INVALID_ARGUMENT,"makeAuthCredentialsProvider failed due to invalid credential type")}})(e.credentials))}_getSettings(){return this._settings}_getEmulatorOptions(){return this._emulatorOptions}_freezeSettings(){return this._settingsFrozen=!0,this._settings}_delete(){return"notTerminated"===this._terminateTask&&(this._terminateTask=this._terminate()),this._terminateTask}async _restart(){"notTerminated"===this._terminateTask?await this._terminate():this._terminateTask="notTerminated"}toJSON(){return{app:this._app,databaseId:this._databaseId,settings:this._settings}}_terminate(){return e=this,(t=Th.get(e))&&(p("ComponentProvider","Removing Datastore"),Th.delete(e),t.terminate()),Promise.resolve();var e,t}}function Mh(n,e,t,i={}){let s,r=(n=V(n,Rh))._getSettings(),a=Object.assign(Object.assign({},r),{emulatorOptions:n._getEmulatorOptions()}),o=e+":"+t;r.host!==Nh&&r.host!==o&&pe("Host has been set in both settings() and connectFirestoreEmulator(), emulator host will be used.");var l=Object.assign(Object.assign({},r),{host:o,ssl:!1,emulatorOptions:i});if(!te(l,a)&&(n._setSettings(l),i.mockUserToken)){let t,r;if("string"==typeof i.mockUserToken)t=i.mockUserToken,r=u.MOCK_USER;else{t=((e,t)=>{if(e.uid)throw new Error('The "uid" field is no longer supported by mockUserToken. Please use "sub" instead for Firebase Auth User ID.');var r=t||"demo-project",n=e.iat||0,i=e.sub||e.user_id;if(i)return r=Object.assign({iss:"https://securetoken.google.com/"+r,aud:r,iat:n,exp:n+3600,auth_time:n,sub:i,user_id:i,firebase:{sign_in_provider:"custom",identities:{}}},e),[j(JSON.stringify({alg:"none",type:"JWT"})),j(JSON.stringify(r)),""].join(".");throw new Error("mockUserToken must contain 'sub' or 'user_id' field!")})(i.mockUserToken,null==(s=n._app)?void 0:s.options.projectId);let e=i.mockUserToken.sub||i.mockUserToken.user_id;if(!e)throw new I(b.INVALID_ARGUMENT,"mockUserToken must contain 'sub' or 'user_id' field!");r=new u(e)}n._authCredentials=new be(new we(t,r))}}class Oh{constructor(e,t,r){this.converter=t,this._query=r,this.type="query",this.firestore=e}withConverter(e){return new Oh(this.firestore,e,this._query)}}class P{constructor(e,t,r){this.converter=t,this._key=r,this.type="document",this.firestore=e}get _path(){return this._key.path}get id(){return this._key.path.lastSegment()}get path(){return this._key.path.canonicalString()}get parent(){return new Lh(this.firestore,this.converter,this._key.path.popLast())}withConverter(e){return new P(this.firestore,e,this._key)}}class Lh extends Oh{constructor(e,t,r){super(e,t,ri(r)),this._path=r,this.type="collection"}get id(){return this._query.path.lastSegment()}get path(){return this._query.path.canonicalString()}get parent(){var e=this._path.popLast();return e.isEmpty()?null:new P(this.firestore,null,new x(e))}withConverter(e){return new Lh(this.firestore,e,this._path)}}function Fh(e,t,...r){var n;if(e=_(e),Eh("collection","path",t),e instanceof Rh)return Ah(n=T.fromString(t,...r)),new Lh(e,null,n);if(e instanceof P||e instanceof Lh)return Ah(n=e._path.child(T.fromString(t,...r))),new Lh(e.firestore,null,n);throw new I(b.INVALID_ARGUMENT,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore")}function Vh(e,t,...r){var n;if(e=_(e),Eh("doc","path",t=1===arguments.length?Ce.newId():t),e instanceof Rh)return xh(n=T.fromString(t,...r)),new P(e,null,new x(n));if(e instanceof P||e instanceof Lh)return xh(n=e._path.child(T.fromString(t,...r))),new P(e.firestore,e instanceof Lh?e.converter:null,new x(n));throw new I(b.INVALID_ARGUMENT,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore")}function Ph(e,t){return e=_(e),t=_(t),(e instanceof P||e instanceof Lh)&&(t instanceof P||t instanceof Lh)&&e.firestore===t.firestore&&e.path===t.path&&e.converter===t.converter}function Uh(e,t){return e=_(e),t=_(t),e instanceof Oh&&t instanceof Oh&&e.firestore===t.firestore&&ui(e._query,t._query)&&e.converter===t.converter}let Bh="AsyncQueue";class qh{constructor(e=Promise.resolve()){this.Qu=[],this.$u=!1,this.Uu=[],this.Ku=null,this.Wu=!1,this.Gu=!1,this.zu=[],this.y_=new Nl(this,"async_queue_retry"),this.ju=()=>{var e=Cl();e&&p(Bh,"Visibility state changed to "+e.visibilityState),this.y_.A_()},this.Hu=e;var t=Cl();t&&"function"==typeof t.addEventListener&&t.addEventListener("visibilitychange",this.ju)}get isShuttingDown(){return this.$u}enqueueAndForget(e){this.enqueue(e)}enqueueAndForgetEvenWhileRestricted(e){this.Ju(),this.Yu(e)}enterRestrictedMode(e){var t;this.$u||(this.$u=!0,this.Gu=e||!1,(t=Cl())&&"function"==typeof t.removeEventListener&&t.removeEventListener("visibilitychange",this.ju))}enqueue(e){if(this.Ju(),this.$u)return new Promise(()=>{});let t=new f;return this.Yu(()=>this.$u&&this.Gu?Promise.resolve():(e().then(t.resolve,t.reject),t.promise)).then(()=>t.promise)}enqueueRetryable(e){this.enqueueAndForget(()=>(this.Qu.push(e),this.Zu()))}async Zu(){if(0!==this.Qu.length){try{await this.Qu[0](),this.Qu.shift(),this.y_.reset()}catch(e){if(!rt(e))throw e;p(Bh,"Operation failed with retryable error: "+e)}0<this.Qu.length&&this.y_.E_(()=>this.Zu())}}Yu(e){var t=this.Hu.then(()=>(this.Wu=!0,e().catch(e=>{throw this.Ku=e,this.Wu=!1,d("INTERNAL UNHANDLED ERROR: ",zh(e)),e}).then(e=>(this.Wu=!1,e))));return this.Hu=t}enqueueAfterDelay(e,t,r){this.Ju(),-1<this.zu.indexOf(e)&&(t=0);var n=iu.createAndSchedule(this,e,t,r,e=>this.Xu(e));return this.Uu.push(n),n}Ju(){this.Ku&&E(47125,{ec:zh(this.Ku)})}verifyOperationInProgress(){}async tc(){for(var e;await(e=this.Hu),e!==this.Hu;);}nc(e){for(var t of this.Uu)if(t.timerId===e)return!0;return!1}rc(t){return this.tc().then(()=>{this.Uu.sort((e,t)=>e.targetTimeMs-t.targetTimeMs);for(var e of this.Uu)if(e.skipDelay(),"all"!==t&&e.timerId===t)break;return this.tc()})}sc(e){this.zu.push(e)}Xu(e){var t=this.Uu.indexOf(e);this.Uu.splice(t,1)}}function zh(e){let t=e.message||"";return t=e.stack?e.stack.includes(e.message)?e.stack:e.message+"\n"+e.stack:t}function jh(t){var r=t,t=["next","error","complete"];if("object"==typeof r&&null!==r){var n=r;for(let e of t)if(e in n&&"function"==typeof n[e])return 1}}class Kh{constructor(){this._progressObserver={},this._taskCompletionResolver=new f,this._lastProgress={taskState:"Running",totalBytes:0,totalDocuments:0,bytesLoaded:0,documentsLoaded:0}}onProgress(e,t,r){this._progressObserver={next:e,error:t,complete:r}}catch(e){return this._taskCompletionResolver.promise.catch(e)}then(e,t){return this._taskCompletionResolver.promise.then(e,t)}_completeWith(e){this._updateProgress(e),this._progressObserver.complete&&this._progressObserver.complete(),this._taskCompletionResolver.resolve(e)}_failWith(e){this._lastProgress.taskState="Error",this._progressObserver.next&&this._progressObserver.next(this._lastProgress),this._progressObserver.error&&this._progressObserver.error(e),this._taskCompletionResolver.reject(e)}_updateProgress(e){this._lastProgress=e,this._progressObserver.next&&this._progressObserver.next(e)}}var Gh,Qh,t,$h;class U extends Rh{constructor(e,t,r,n){super(e,t,r,n),this.type="firestore",this._queue=new qh,this._persistenceKey=(null==n?void 0:n.name)||"[DEFAULT]"}async _terminate(){var e;this._firestoreClient&&(e=this._firestoreClient.terminate(),this._queue=new qh(e),this._firestoreClient=void 0,await e)}}function Hh(e){if(e._terminated)throw new I(b.FAILED_PRECONDITION,"The client has already been terminated.");return e._firestoreClient||Wh(e),e._firestoreClient}function Wh(e){var t,r,n,i,s,a=e._freezeSettings(),o=(i=e._databaseId,t=(null==(o=e._app)?void 0:o.options.appId)||"",r=e._persistenceKey,n=a,new Jr(i,t,r,n.host,n.ssl,n.experimentalForceLongPolling,n.experimentalAutoDetectLongPolling,Ih(n.experimentalLongPollingOptions),n.useFetchStreams));e._componentsProvider||null!=(s=a.localCache)&&s._offlineComponentProvider&&null!=(s=a.localCache)&&s._onlineComponentProvider&&(e._componentsProvider={_offline:a.localCache._offlineComponentProvider,_online:a.localCache._onlineComponentProvider}),e._firestoreClient=new ih(e._authCredentials,e._appCheckCredentials,e._queue,o,e._componentsProvider&&(i=e._componentsProvider,s=null==i?void 0:i._online.build(),{_offline:null==i?void 0:i._offline.build(s),_online:s}))}function Yh(e,t,r){if((e=V(e,U))._firestoreClient||e._terminated)throw new I(b.FAILED_PRECONDITION,"Firestore has already been started and persistence can no longer be enabled. You can only enable persistence before calling any other methods on a Firestore object.");if(e._componentsProvider||e._getSettings().localCache)throw new I(b.FAILED_PRECONDITION,"SDK cache is already specified.");e._componentsProvider={_online:t,_offline:r},Wh(e)}function Jh(r){if(r._initialized&&!r._terminated)throw new I(b.FAILED_PRECONDITION,"Persistence can only be cleared before a Firestore instance is initialized or after it is terminated.");let n=new f;return r._queue.enqueueAndForgetEvenWhileRestricted(async()=>{try{e=Po(r._databaseId,r._persistenceKey),await(Xe.C()?(t=e+"main",void await Xe.delete(t)):Promise.resolve()),n.resolve()}catch(e){n.reject(e)}var e,t}),n.promise}function Xh(e){return(r=Hh(e=V(e,U))).asyncQueue.enqueue(async()=>{var e=await uh(r),t=await ch(r);return e.setNetworkEnabled(!0),(e=t).aa.delete(0),Ul(e)});var r}class Zh{constructor(e){this._byteString=e}static fromBase64String(e){try{return new Zh(D.fromBase64String(e))}catch(e){throw new I(b.INVALID_ARGUMENT,"Failed to construct data from Base64 string: "+e)}}static fromUint8Array(e){return new Zh(D.fromUint8Array(e))}toBase64(){return this._byteString.toBase64()}toUint8Array(){return this._byteString.toUint8Array()}toString(){return"Bytes(base64: "+this.toBase64()+")"}isEqual(e){return this._byteString.isEqual(e._byteString)}}class ec{constructor(...e){for(let t=0;t<e.length;++t)if(0===e[t].length)throw new I(b.INVALID_ARGUMENT,"Invalid field name at argument $(i + 1). Field names must not be empty.");this._internalPath=new v(e)}isEqual(e){return this._internalPath.isEqual(e._internalPath)}}class tc{constructor(e){this._methodName=e}}class rc{constructor(e,t){if(!isFinite(e)||e<-90||90<e)throw new I(b.INVALID_ARGUMENT,"Latitude must be a number between -90 and 90, but was: "+e);if(!isFinite(t)||t<-180||180<t)throw new I(b.INVALID_ARGUMENT,"Longitude must be a number between -180 and 180, but was: "+t);this._lat=e,this._long=t}get latitude(){return this._lat}get longitude(){return this._long}isEqual(e){return this._lat===e._lat&&this._long===e._long}toJSON(){return{latitude:this._lat,longitude:this._long}}_compareTo(e){return S(this._lat,e._lat)||S(this._long,e._long)}}class nc{constructor(e){this._values=(e||[]).map(e=>e)}toArray(){return this._values.map(e=>e)}isEqual(e){var t=this._values,r=e._values;if(t.length!==r.length)return!1;for(let n=0;n<t.length;++n)if(t[n]!==r[n])return!1;return!0}}let ic=/^__.*__$/;class sc{constructor(e,t,r){this.data=e,this.fieldMask=t,this.fieldTransforms=r}toMutation(e,t){return null!==this.fieldMask?new Gi(e,this.data,this.fieldMask,t,this.fieldTransforms):new Ki(e,this.data,t,this.fieldTransforms)}}class ac{constructor(e,t,r){this.data=e,this.fieldMask=t,this.fieldTransforms=r}toMutation(e,t){return new Gi(e,this.data,this.fieldMask,t,this.fieldTransforms)}}function oc(e){switch(e){case 0:case 2:case 1:return 1;case 3:case 4:return;default:throw E(40011,{oc:e})}}class lc{constructor(e,t,r,n,i,s){this.settings=e,this.databaseId=t,this.serializer=r,this.ignoreUndefinedProperties=n,void 0===i&&this._c(),this.fieldTransforms=i||[],this.fieldMask=s||[]}get path(){return this.settings.path}get oc(){return this.settings.oc}ac(e){return new lc(Object.assign(Object.assign({},this.settings),e),this.databaseId,this.serializer,this.ignoreUndefinedProperties,this.fieldTransforms,this.fieldMask)}uc(e){var t=null==(t=this.path)?void 0:t.child(e),t=this.ac({path:t,cc:!1});return t.lc(e),t}hc(e){var t=null==(t=this.path)?void 0:t.child(e),t=this.ac({path:t,cc:!1});return t._c(),t}Pc(e){return this.ac({path:void 0,cc:!0})}Tc(e){return Cc(e,this.settings.methodName,this.settings.Ic||!1,this.path,this.settings.Ec)}contains(t){return void 0!==this.fieldMask.find(e=>t.isPrefixOf(e))||void 0!==this.fieldTransforms.find(e=>t.isPrefixOf(e.field))}_c(){if(this.path)for(let e=0;e<this.path.length;e++)this.lc(this.path.get(e))}lc(e){if(0===e.length)throw this.Tc("Document fields must not be empty");if(oc(this.oc)&&ic.test(e))throw this.Tc('Document fields cannot begin and end with "__"')}}class uc{constructor(e,t,r){this.databaseId=e,this.ignoreUndefinedProperties=t,this.serializer=r||Dl(e)}dc(e,t,r,n=!1){return new lc({oc:e,methodName:t,Ec:r,path:v.emptyPath(),cc:!1,Ic:n},this.databaseId,this.serializer,this.ignoreUndefinedProperties)}}function hc(e){var t=e._freezeSettings(),r=Dl(e._databaseId);return new uc(e._databaseId,!!t.ignoreUndefinedProperties,r)}function cc(e,n,i,t,r,s={}){var a=e.dc(s.merge||s.mergeFields?2:0,n,i,r),o=(Ec("Data must be an object, but it was:",a,t),Ic(t,a));let l,u;if(s.merge)l=new Ur(a.fieldMask),u=a.fieldTransforms;else if(s.mergeFields){let t=[];for(let r of s.mergeFields){let e=Sc(n,r,i);if(!a.contains(e))throw new I(b.INVALID_ARGUMENT,`Field '${e}' is specified in your field mask but missing from your input data.`);Dc(t,e)||t.push(e)}l=new Ur(t),u=a.fieldTransforms.filter(e=>l.covers(e.field))}else l=null,u=a.fieldTransforms;return new sc(new xn(o),l,u)}class dc extends tc{_toFieldTransform(e){if(2!==e.oc)throw 1===e.oc?e.Tc(this._methodName+"() can only appear at the top level of your update data"):e.Tc(this._methodName+"() cannot be used with set() unless you pass {merge:true}");return e.fieldMask.push(e.path),null}isEqual(e){return e instanceof dc}}function fc(e,t,r){return new lc({oc:3,Ec:t.settings.Ec,methodName:e._methodName,cc:r},t.databaseId,t.serializer,t.ignoreUndefinedProperties)}class gc extends tc{_toFieldTransform(e){return new Vi(e.path,new Di)}isEqual(e){return e instanceof gc}}class mc extends tc{constructor(e,t){super(e),this.Ac=t}_toFieldTransform(e){let t=fc(this,e,!0),r=this.Ac.map(e=>bc(e,t)),n=new Ni(r);return new Vi(e.path,n)}isEqual(e){return e instanceof mc&&te(this.Ac,e.Ac)}}class pc extends tc{constructor(e,t){super(e),this.Ac=t}_toFieldTransform(e){let t=fc(this,e,!0),r=this.Ac.map(e=>bc(e,t)),n=new Ri(r);return new Vi(e.path,n)}isEqual(e){return e instanceof pc&&te(this.Ac,e.Ac)}}class yc extends tc{constructor(e,t){super(e),this.Rc=t}_toFieldTransform(e){var t=new Oi(e.serializer,xi(e.serializer,this.Rc));return new Vi(e.path,t)}isEqual(e){return e instanceof yc&&this.Rc===e.Rc}}function vc(e,i,s,t){let a=e.dc(1,i,s),o=(Ec("Data must be an object, but it was:",a,t),[]),l=xn.empty();Mr(t,(e,t)=>{var r=Ac(i,e,s),n=(t=_(t),a.hc(r));if(t instanceof dc)o.push(r);else{let e=bc(t,n);null!=e&&(o.push(r),l.set(r,e))}});var r=new Ur(o);return new ac(l,r,a.fieldTransforms)}function wc(e,t,r,n,i,s){var a=e.dc(1,t,r),o=[Sc(t,n,r)],l=[i];if(s.length%2!=0)throw new I(b.INVALID_ARGUMENT,`Function ${t}() needs to be called with an even number of arguments that alternate between field names and values.`);for(let f=0;f<s.length;f+=2)o.push(Sc(t,s[f])),l.push(s[f+1]);var u=[],h=xn.empty();for(let g=o.length-1;0<=g;--g)if(!Dc(u,o[g])){let t=o[g];var c=_(l[g]);let r=a.hc(t);if(c instanceof dc)u.push(t);else{let e=bc(c,r);null!=e&&(u.push(t),h.set(t,e))}}var d=new Ur(u);return new ac(h,d,a.fieldTransforms)}function _c(e,t,r,n=!1){return bc(r,e.dc(n?4:3,t))}function bc(e,r){if(Tc(e=_(e)))return Ec("Unsupported field value:",r,e),Ic(e,r);if(e instanceof tc){{var t=e;var n=r;if(!oc(n.oc))throw n.Tc(t._methodName+"() can only be used with update() and set()");if(!n.path)throw n.Tc(t._methodName+"() is not currently supported inside arrays");var i=t._toFieldTransform(n);i&&n.fieldTransforms.push(i)}return null}if(void 0===e&&r.ignoreUndefinedProperties)return null;if(r.path&&r.fieldMask.push(r.path),e instanceof Array){if(r.settings.cc&&4!==r.oc)throw r.Tc("Nested arrays are not supported");{var s,a=r,o=[];let t=0;for(s of e){let e=bc(s,a.Pc(t));null==e&&(e={nullValue:"NULL_VALUE"}),o.push(e),t++}return{arrayValue:{values:o}}}}var l,t=e,n=r;if(null===(t=_(t)))return{nullValue:"NULL_VALUE"};if("number"==typeof t)return xi(n.serializer,t);if("boolean"==typeof t)return{booleanValue:t};if("string"==typeof t)return{stringValue:t};if(t instanceof Date)return u=h.fromDate(t),{timestampValue:Is(n.serializer,u)};if(t instanceof h)return u=new h(t.seconds,1e3*Math.floor(t.nanoseconds/1e3)),{timestampValue:Is(n.serializer,u)};if(t instanceof rc)return{geoPointValue:{latitude:t.latitude,longitude:t.longitude}};if(t instanceof Zh)return{bytesValue:Ts(n.serializer,t._byteString)};if(t instanceof P){var u=n.databaseId,i=t.firestore._databaseId;if(i.isEqual(u))return{referenceValue:Es(t.firestore._databaseId||n.databaseId,t._key.path)};throw n.Tc(`Document reference is for database ${i.projectId}/${i.database} but should be for database ${u.projectId}/`+u.database)}if(t instanceof nc)return e=t,l=n,{mapValue:{fields:{[en]:{stringValue:nn},[sn]:{arrayValue:{values:e.toArray().map(e=>{if("number"!=typeof e)throw l.Tc("VectorValues must only contain numeric values.");return Ei(l.serializer,e)})}}}}};throw n.Tc("Unsupported field value: "+Ch(t))}function Ic(e,n){let i={};return Or(e)?n.path&&0<n.path.length&&n.fieldMask.push(n.path):Mr(e,(e,t)=>{var r=bc(t,n.uc(e));null!=r&&(i[e]=r)}),{mapValue:{fields:i}}}function Tc(e){return!("object"!=typeof e||null===e||e instanceof Array||e instanceof Date||e instanceof h||e instanceof rc||e instanceof Zh||e instanceof P||e instanceof tc||e instanceof nc)}function Ec(e,t,r){var n,i;if(!Tc(r)||"object"!=typeof(i=r)||null===i||Object.getPrototypeOf(i)!==Object.prototype&&null!==Object.getPrototypeOf(i))throw"an object"===(n=Ch(r))?t.Tc(e+" a custom object"):t.Tc(e+" "+n)}function Sc(e,t,r){if((t=_(t))instanceof ec)return t._internalPath;if("string"==typeof t)return Ac(e,t);throw Cc("Field path arguments must be of type string or ",e,!1,void 0,r)}let xc=new RegExp("[~\\*/\\[\\]]");function Ac(t,r,n){if(0<=r.search(xc))throw Cc(`Invalid field path (${r}). Paths must not contain '~', '*', '/', '[', or ']'`,t,!1,void 0,n);try{return new ec(...r.split("."))._internalPath}catch(e){throw Cc(`Invalid field path (${r}). Paths must not be empty, begin with '.', end with '.', or contain '..'`,t,!1,void 0,n)}}function Cc(e,t,r,n,i){var s=n&&!n.isEmpty(),a=void 0!==i;let o=`Function ${t}() called with invalid data`,l=(r&&(o+=" (via `toFirestore()`)"),o+=". ","");return(s||a)&&(l+=" (found",s&&(l+=" in field "+n),a&&(l+=" in document "+i),l+=")"),new I(b.INVALID_ARGUMENT,o+e+l)}function Dc(e,t){return e.some(e=>e.isEqual(t))}class Nc{constructor(e,t,r,n,i){this._firestore=e,this._userDataWriter=t,this._key=r,this._document=n,this._converter=i}get id(){return this._key.path.lastSegment()}get ref(){return new P(this._firestore,this._converter,this._key)}exists(){return null!==this._document}data(){var e;if(this._document)return this._converter?(e=new kc(this._firestore,this._userDataWriter,this._key,this._document,null),this._converter.fromFirestore(e)):this._userDataWriter.convertValue(this._document.data.value)}get(e){if(this._document){var t=this._document.data.field(Rc("DocumentSnapshot.get",e));if(null!==t)return this._userDataWriter.convertValue(t)}}}class kc extends Nc{data(){return super.data()}}function Rc(e,t){return"string"==typeof t?Ac(e,t):(t instanceof ec?t:t._delegate)._internalPath}function Mc(e){if("L"===e.limitType&&0===e.explicitOrderBy.length)throw new I(b.UNIMPLEMENTED,"limitToLast() queries require specifying at least one orderBy() clause")}class Oc{}class Lc extends Oc{}function Fc(e,t,...r){let n=[];t instanceof Oc&&n.push(t);var t=n=n.concat(r),i=t.filter(e=>!1).length,s=t.filter(e=>e instanceof Vc).length;if(1<i||0<i&&0<s)throw new I(b.INVALID_ARGUMENT,"InvalidQuery. When using composite filters, you cannot use more than one filter at the top level. Consider nesting the multiple filters within an `and(...)` statement. For example: change `query(query, where(...), or(...))` to `query(query, and(where(...), or(...)))`.");for(let t of n)e=t._apply(e);return e}class Vc extends Lc{constructor(e,t,r){super(),this._field=e,this._op=t,this._value=r,this.type="where"}static _create(e,t,r){return new Vc(e,t,r)}_apply(e){var t=this._parse(e);return Gc(e._query,t),new Oh(e.firestore,e.converter,oi(e._query,t))}_parse(e){var t=hc(e.firestore);{var n=e._query,i="where",s=t,a=e.firestore._databaseId,o=(e=this._field,this._op),l=this._value;let r;if(e.isKeyField()){if("array-contains"===o||"array-contains-any"===o)throw new I(b.INVALID_ARGUMENT,`Invalid Query. You can't perform '${o}' queries on documentId().`);if("in"===o||"not-in"===o){Kc(l,o);let e=[];for(let t of l)e.push(jc(a,n,t));r={arrayValue:{values:e}}}else r=jc(a,n,l)}else"in"!==o&&"not-in"!==o&&"array-contains-any"!==o||Kc(l,o),r=_c(s,i,l,"in"===o||"not-in"===o);return R.create(e,o,r)}}}(class extends Oc{});class Pc extends Lc{constructor(e,t){super(),this._field=e,this._direction=t,this.type="orderBy"}static _create(e,t){return new Pc(e,t)}_apply(e){var t=((e,t,r)=>{if(null!==e.startAt)throw new I(b.INVALID_ARGUMENT,"Invalid query. You must not call startAt() or startAfter() before calling orderBy().");if(null!==e.endAt)throw new I(b.INVALID_ARGUMENT,"Invalid query. You must not call endAt() or endBefore() before calling orderBy().");return new Nn(t,r)})(e._query,this._field,this._direction);return new Oh(e.firestore,e.converter,(t=(e=e._query).explicitOrderBy.concat([t]),new ei(e.path,e.collectionGroup,t,e.filters.slice(),e.limit,e.limitType,e.startAt,e.endAt)))}}class Uc extends Lc{constructor(e,t,r){super(),this.type=e,this._limit=t,this._limitType=r}static _create(e,t,r){return new Uc(e,t,r)}_apply(e){return new Oh(e.firestore,e.converter,li(e._query,this._limit,this._limitType))}}class Bc extends Lc{constructor(e,t,r){super(),this.type=e,this._docOrFields=t,this._inclusive=r}static _create(e,t,r){return new Bc(e,t,r)}_apply(e){var t,r=zc(e,this.type,this._docOrFields,this._inclusive);return new Oh(e.firestore,e.converter,(e=e._query,t=r,new ei(e.path,e.collectionGroup,e.explicitOrderBy.slice(),e.filters.slice(),e.limit,e.limitType,t,e.endAt)))}}class qc extends Lc{constructor(e,t,r){super(),this.type=e,this._docOrFields=t,this._inclusive=r}static _create(e,t,r){return new qc(e,t,r)}_apply(e){var t,r=zc(e,this.type,this._docOrFields,this._inclusive);return new Oh(e.firestore,e.converter,(e=e._query,t=r,new ei(e.path,e.collectionGroup,e.explicitOrderBy.slice(),e.filters.slice(),e.limit,e.limitType,e.startAt,t)))}}function zc(e,r,n,i){if(n[0]=_(n[0]),n[0]instanceof Nc){var s=e._query,a=e.firestore._databaseId,o=r,l=n[0]._document,u=i;if(!l)throw new I(b.NOT_FOUND,`Can't use a DocumentSnapshot that doesn't exist for ${o}().`);var h=[];for(let t of si(s))if(t.field.isKeyField())h.push(gn(a,l.key));else{let e=l.data.field(t.field);if(Hr(e))throw new I(b.INVALID_ARGUMENT,'Invalid query. You are trying to start or end a query using a document for which the field "'+t.field+'" is an uncommitted server timestamp. (Since the value of this field is unknown, you cannot start/end a query with it.)');if(null===e){let e=t.field.canonicalString();throw new I(b.INVALID_ARGUMENT,`Invalid query. You are trying to start or end a query using a document for which the field '${e}' (used as the orderBy) does not exist.`)}h.push(e)}return new An(h,u)}var t=hc(e.firestore),c=e._query,d=e.firestore._databaseId,f=t,g=r,m=n,o=i,p=c.explicitOrderBy;if(m.length>p.length)throw new I(b.INVALID_ARGUMENT,`Too many arguments provided to ${g}(). The number of arguments must be less than or equal to the number of orderBy() clauses`);var y=[];for(let w=0;w<m.length;w++){var v=m[w];if(p[w].field.isKeyField()){if("string"!=typeof v)throw new I(b.INVALID_ARGUMENT,`Invalid query. Expected a string for document ID in ${g}(), but got a `+typeof v);if(!ii(c)&&-1!==v.indexOf("/"))throw new I(b.INVALID_ARGUMENT,`Invalid query. When querying a collection and ordering by documentId(), the value passed to ${g}() must be a plain document ID, but '${v}' contains a slash.`);let e=c.path.child(T.fromString(v));if(!x.isDocumentKey(e))throw new I(b.INVALID_ARGUMENT,`Invalid query. When querying a collection group and ordering by documentId(), the value passed to ${g}() must result in a valid document path, but '${e}' is not because it contains an odd number of segments.`);let t=new x(e);y.push(gn(d,t))}else{let e=_c(f,g,v);y.push(e)}}return new An(y,o)}function jc(e,t,r){if("string"==typeof(r=_(r))){if(""===r)throw new I(b.INVALID_ARGUMENT,"Invalid query. When querying with documentId(), you must provide a valid document ID, but it was an empty string.");if(!ii(t)&&-1!==r.indexOf("/"))throw new I(b.INVALID_ARGUMENT,`Invalid query. When querying a collection by documentId(), you must provide a plain document ID, but '${r}' contains a '/' character.`);var n=t.path.child(T.fromString(r));if(x.isDocumentKey(n))return gn(e,new x(n));throw new I(b.INVALID_ARGUMENT,`Invalid query. When querying a collection group by documentId(), the value provided must result in a valid document path, but '${n}' is not because it has an odd number of segments (${n.length}).`)}if(r instanceof P)return gn(e,r._key);throw new I(b.INVALID_ARGUMENT,`Invalid query. When querying with documentId(), you must provide a valid string or a DocumentReference, but it was: ${Ch(r)}.`)}function Kc(e,t){if(!Array.isArray(e)||0===e.length)throw new I(b.INVALID_ARGUMENT,`Invalid Query. A non-empty array is required for '${t.toString()}' filters.`)}function Gc(e,t){var r=((t,r)=>{for(var n of t)for(let e of n.getFlattenedFilters())if(0<=r.indexOf(e.op))return e.op;return null})(e.filters,(e=>{switch(e){case"!=":return["!=","not-in"];case"array-contains-any":case"in":return["not-in"];case"not-in":return["array-contains-any","in","not-in","!="];default:return[]}})(t.op));if(null!==r)throw r===t.op?new I(b.INVALID_ARGUMENT,`Invalid query. You cannot use more than one '${t.op.toString()}' filter.`):new I(b.INVALID_ARGUMENT,`Invalid query. You cannot use '${t.op.toString()}' filters with '${r.toString()}' filters.`)}class Qc{convertValue(e,t="none"){switch(on(e)){case 0:return null;case 1:return e.booleanValue;case 2:return N(e.integerValue||e.doubleValue);case 3:return this.convertTimestamp(e.timestampValue);case 4:return this.convertServerTimestamp(e,t);case 5:return e.stringValue;case 6:return this.convertBytes(jr(e.bytesValue));case 7:return this.convertReference(e.referenceValue);case 8:return this.convertGeoPoint(e.geoPointValue);case 9:return this.convertArray(e.arrayValue,t);case 11:return this.convertObject(e.mapValue,t);case 10:return this.convertVectorValue(e.mapValue);default:throw E(62114,{value:e})}}convertObject(e,t){return this.convertObjectMap(e.fields,t)}convertObjectMap(e,r="none"){let n={};return Mr(e,(e,t)=>{n[e]=this.convertValue(t,r)}),n}convertVectorValue(e){var t=null==(t=null==(t=null==(t=e.fields)?void 0:t[sn].arrayValue)?void 0:t.values)?void 0:t.map(e=>N(e.doubleValue));return new nc(t)}convertGeoPoint(e){return new rc(N(e.latitude),N(e.longitude))}convertArray(e,t){return(e.values||[]).map(e=>this.convertValue(e,t))}convertServerTimestamp(e,t){switch(t){case"previous":var r=Wr(e);return null==r?null:this.convertValue(r,t);case"estimate":return this.convertTimestamp(Yr(e));default:return null}}convertTimestamp(e){var t=zr(e);return new h(t.seconds,t.nanos)}convertDocumentKey(e,t){var r=T.fromString(e),n=(y(Qs(r),9688,{name:e}),new Zr(r.get(1),r.get(3))),r=new x(r.popFirst(5));return n.isEqual(t)||d(`Document ${r} contains a document reference within a different database (${n.projectId}/${n.database}) which is not supported. It will be treated as a reference in the current database (${t.projectId}/${t.database}) instead.`),r}}function $c(e,t,r){return e?r&&(r.merge||r.mergeFields)?e.toFirestore(t,r):e.toFirestore(t):t}class Hc extends Qc{constructor(e){super(),this.firestore=e}convertBytes(e){return new Zh(e)}convertReference(e){var t=this.convertDocumentKey(e,this.firestore._databaseId);return new P(this.firestore,null,t)}}class Wc{constructor(e,t){this.hasPendingWrites=e,this.fromCache=t}isEqual(e){return this.hasPendingWrites===e.hasPendingWrites&&this.fromCache===e.fromCache}}class Yc extends Nc{constructor(e,t,r,n,i,s){super(e,t,r,n,s),this._firestore=e,this._firestoreImpl=e,this.metadata=i}exists(){return super.exists()}data(e={}){var t;if(this._document)return this._converter?(t=new Jc(this._firestore,this._userDataWriter,this._key,this._document,this.metadata,null),this._converter.fromFirestore(t,e)):this._userDataWriter.convertValue(this._document.data.value,e.serverTimestamps)}get(e,t={}){if(this._document){var r=this._document.data.field(Rc("DocumentSnapshot.get",e));if(null!==r)return this._userDataWriter.convertValue(r,t.serverTimestamps)}}}class Jc extends Yc{data(e={}){return super.data(e)}}class Xc{constructor(e,t,r,n){this._firestore=e,this._userDataWriter=t,this._snapshot=n,this.metadata=new Wc(n.hasPendingWrites,n.fromCache),this.query=r}get docs(){let t=[];return this.forEach(e=>t.push(e)),t}get size(){return this._snapshot.docs.size}get empty(){return 0===this.size}forEach(t,r){this._snapshot.docs.forEach(e=>{t.call(r,new Jc(this._firestore,this._userDataWriter,e.key,e,new Wc(this._snapshot.mutatedKeys.has(e.key),this._snapshot.fromCache),this.query.converter))})}docChanges(e={}){var t=!!e.includeMetadataChanges;if(t&&this._snapshot.excludesMetadataChanges)throw new I(b.INVALID_ARGUMENT,"To include metadata changes with your document changes, you must also pass { includeMetadataChanges:true } to onSnapshot().");return this._cachedChanges&&this._cachedChangesIncludeMetadataChanges===t||(this._cachedChanges=((s,t)=>{if(s._snapshot.oldDocs.isEmpty()){let r=0;return s._snapshot.docChanges.map(e=>{var t=new Jc(s._firestore,s._userDataWriter,e.doc.key,e.doc,new Wc(s._snapshot.mutatedKeys.has(e.doc.key),s._snapshot.fromCache),s.query.converter);return e.doc,{type:"added",doc:t,oldIndex:-1,newIndex:r++}})}{let i=s._snapshot.oldDocs;return s._snapshot.docChanges.filter(e=>t||3!==e.type).map(e=>{var t=new Jc(s._firestore,s._userDataWriter,e.doc.key,e.doc,new Wc(s._snapshot.mutatedKeys.has(e.doc.key),s._snapshot.fromCache),s.query.converter);let r=-1,n=-1;return 0!==e.type&&(r=i.indexOf(e.doc.key),i=i.delete(e.doc.key)),1!==e.type&&(i=i.add(e.doc),n=i.indexOf(e.doc.key)),{type:(e=>{switch(e){case 0:return"added";case 2:case 3:return"modified";case 1:return"removed";default:return E(61501,{type:e})}})(e.type),doc:t,oldIndex:r,newIndex:n}})}})(this,t),this._cachedChangesIncludeMetadataChanges=t),this._cachedChanges}}function Zc(e,t){return e instanceof Yc&&t instanceof Yc?e._firestore===t._firestore&&e._key.isEqual(t._key)&&(null===e._document?null===t._document:e._document.isEqual(t._document))&&e._converter===t._converter:e instanceof Xc&&t instanceof Xc&&e._firestore===t._firestore&&Uh(e.query,t.query)&&e.metadata.isEqual(t.metadata)&&e._snapshot.isEqual(t._snapshot)}class ed extends Qc{constructor(e){super(),this.firestore=e}convertBytes(e){return new Zh(e)}convertReference(e){var t=this.convertDocumentKey(e,this.firestore._databaseId);return new P(this.firestore,null,t)}}function td(e,t,r){e=V(e,P);var n=V(e.firestore,U),i=$c(e.converter,t,r);return id(n,[cc(hc(n),"setDoc",e._key,i,null!==e.converter,r).toMutation(e._key,L.none())])}function rd(e,t,r,...n){e=V(e,P);var i=V(e.firestore,U),s=hc(i);return id(i,[("string"==typeof(t=_(t))||t instanceof ec?wc(s,"updateDoc",e._key,t,r,n):vc(s,"updateDoc",e._key,t)).toMutation(e._key,L.exists(!0))])}function nd(n,...i){var t,r;n=_(n);let e={includeMetadataChanges:!1,source:"default"},s=0;"object"!=typeof i[s]||jh(i[s])||(e=i[s],s++);var a={includeMetadataChanges:e.includeMetadataChanges,source:e.source};if(jh(i[s])){let e=i[s];i[s]=null==(r=e.next)?void 0:r.bind(e),i[s+1]=null==(t=e.error)?void 0:t.bind(e),i[s+2]=null==(r=e.complete)?void 0:r.bind(e)}let o,l,u;if(n instanceof P)l=V(n.firestore,U),u=ri(n._key.path),o={next:e=>{i[s]&&i[s](sd(l,n,e))},error:i[s+1],complete:i[s+2]};else{let t=V(n,Oh),r=(l=V(t.firestore,U),u=t._query,new ed(l));o={next:e=>{i[s]&&i[s](new Xc(l,r,t,e))},error:i[s+1],complete:i[s+2]},Mc(n._query)}{var h=Hh(l),c=u,d=a,f=o;let e=new Zu(f),t=new mu(c,e,d);return h.asyncQueue.enqueueAndForget(async()=>du(await fh(h),t)),()=>{e.yu(),h.asyncQueue.enqueueAndForget(async()=>fu(await fh(h),t))}}}function id(t,r){{var n=Hh(t),i=r;let e=new f;return n.asyncQueue.enqueueAndForget(async()=>Du(await dh(n),i,e)),e.promise}}function sd(e,t,r){var n=r.docs.get(t._key),i=new ed(e);return new Yc(e,i,t._key,n,new Wc(r.hasPendingWrites,r.fromCache),t.converter)}let ad={maxAttempts:5};class od{constructor(e,t){this._firestore=e,this._commitHandler=t,this._mutations=[],this._committed=!1,this._dataReader=hc(e)}set(e,t,r){this._verifyNotCommitted();var n=ld(e,this._firestore),i=$c(n.converter,t,r),i=cc(this._dataReader,"WriteBatch.set",n._key,i,null!==n.converter,r);return this._mutations.push(i.toMutation(n._key,L.none())),this}update(e,t,r,...n){this._verifyNotCommitted();var i=ld(e,this._firestore),s="string"==typeof(t=_(t))||t instanceof ec?wc(this._dataReader,"WriteBatch.update",i._key,t,r,n):vc(this._dataReader,"WriteBatch.update",i._key,t);return this._mutations.push(s.toMutation(i._key,L.exists(!0))),this}delete(e){this._verifyNotCommitted();var t=ld(e,this._firestore);return this._mutations=this._mutations.concat(new Wi(t._key,L.none())),this}commit(){return this._verifyNotCommitted(),this._committed=!0,0<this._mutations.length?this._commitHandler(this._mutations):Promise.resolve()}_verifyNotCommitted(){if(this._committed)throw new I(b.FAILED_PRECONDITION,"A write batch can no longer be used after commit() has been called.")}}function ld(e,t){if((e=_(e)).firestore!==t)throw new I(b.INVALID_ARGUMENT,"Provided document reference is from a different Firestore instance.");return e}class ud extends class{constructor(e,t){this._firestore=e,this._transaction=t,this._dataReader=hc(e)}get(e){let r=ld(e,this._firestore),n=new Hc(this._firestore);return this._transaction.lookup([r._key]).then(e=>{if(!e||1!==e.length)return E(24041);var t=e[0];if(t.isFoundDocument())return new Nc(this._firestore,n,t.key,t,r.converter);if(t.isNoDocument())return new Nc(this._firestore,n,r._key,null,r.converter);throw E(18433,{doc:t})})}set(e,t,r){var n=ld(e,this._firestore),i=$c(n.converter,t,r),i=cc(this._dataReader,"Transaction.set",n._key,i,null!==n.converter,r);return this._transaction.set(n._key,i),this}update(e,t,r,...n){var i=ld(e,this._firestore),s="string"==typeof(t=_(t))||t instanceof ec?wc(this._dataReader,"Transaction.update",i._key,t,r,n):vc(this._dataReader,"Transaction.update",i._key,t);return this._transaction.update(i._key,s),this}delete(e){var t=ld(e,this._firestore);return this._transaction.delete(t._key),this}}{constructor(e,t){super(e,t),this._firestore=e}get(e){let t=ld(e,this._firestore),r=new ed(this._firestore);return super.get(e).then(e=>new Yc(this._firestore,r,t._key,e._document,new Wc(!1,!1),t.converter))}}function hd(r,n,e){r=V(r,U);var i=Object.assign(Object.assign({},ad),e);if(i.maxAttempts<1)throw new I(b.INVALID_ARGUMENT,"Max attempts must be at least 1");{var s=Hh(r),a=e=>n(new ud(r,e)),o=i;let t=new f;return s.asyncQueue.enqueueAndForget(async()=>{var e=await lh(s).then(e=>e.datastore);new rh(s.asyncQueue,e,o,a,t).Nu()}),t.promise}}Qh=!0,t=Vd.SDK_VERSION,fe=t,Vd._registerComponent(new ne("firestore",(e,{instanceIdentifier:t,options:r})=>{var n=e.getProvider("app").getImmediate(),n=new U(new Ie(e.getProvider("auth-internal")),new xe(n,e.getProvider("app-check-internal")),((e,t)=>{if(Object.prototype.hasOwnProperty.apply(e.options,["projectId"]))return new Zr(e.options.projectId,t);throw new I(b.INVALID_ARGUMENT,'"projectId" not provided in firebase.initializeApp.')})(n,t),n);return r=Object.assign({useFetchStreams:Qh},r),n._setSettings(r),n},"PUBLIC").setMultipleInstances(!0)),Vd.registerVersion(ce,de,Gh),Vd.registerVersion(ce,de,"esm2017");function cd(e,t){if(void 0===t)return{merge:!1};if(void 0!==t.mergeFields&&void 0!==t.merge)throw new I("invalid-argument",`Invalid options passed to function ${e}(): You cannot `+'specify both "merge" and "mergeFields".');return t}function dd(){if("undefined"==typeof Uint8Array)throw new I("unimplemented","Uint8Arrays are not available in this environment.")}function fd(){if("undefined"==typeof atob)throw new I("unimplemented","Blobs are unavailable in Firestore in this environment.")}class gd{constructor(e){this._delegate=e}static fromBase64String(e){return fd(),new gd(Zh.fromBase64String(e))}static fromUint8Array(e){return dd(),new gd(Zh.fromUint8Array(e))}toBase64(){return fd(),this._delegate.toBase64()}toUint8Array(){return dd(),this._delegate.toUint8Array()}isEqual(e){return this._delegate.isEqual(e._delegate)}toString(){return"Blob(base64: "+this.toBase64()+")"}}function md(e){var t=["next","error","complete"];if("object"==typeof e&&null!==e){var r,n=e;for(r of t)if(r in n&&"function"==typeof n[r])return 1}}class pd{enableIndexedDbPersistence(e,r){{e=e._delegate;var n={forceOwnership:r};pe("enableIndexedDbPersistence() will be deprecated in the future, you can use `FirestoreSettings.cache` instead.");let t=e._freezeSettings();return Yh(e,Ju.provider,{build:e=>new Wu(e,t.cacheSizeBytes,null==n?void 0:n.forceOwnership)}),Promise.resolve()}}enableMultiTabIndexedDbPersistence(e){return(async e=>{pe("enableMultiTabIndexedDbPersistence() will be deprecated in the future, you can use `FirestoreSettings.cache` instead.");let t=e._freezeSettings();Yh(e,Ju.provider,{build:e=>new Yu(e,t.cacheSizeBytes)})})(e._delegate)}clearIndexedDbPersistence(e){return Jh(e._delegate)}}class yd{constructor(e,t,r){this._delegate=t,this._persistenceProvider=r,this.INTERNAL={delete:()=>this.terminate()},e instanceof Zr||(this._appCompat=e)}get _databaseId(){return this._delegate._databaseId}settings(e){var t=this._delegate._getSettings();e.merge||t.host===e.host||pe("You are overriding the original host. If you did not intend to override your settings, use {merge: true}."),e.merge&&delete(e=Object.assign(Object.assign({},t),e)).merge,this._delegate._setSettings(e)}useEmulator(e,t,r={}){Mh(this._delegate,e,t,r)}enableNetwork(){return Xh(this._delegate)}disableNetwork(){return gh(Hh(V(this._delegate,U)))}enablePersistence(e){let t=!1,r=!1;return e&&(t=!!e.synchronizeTabs,r=!!e.experimentalForceOwningTab,Sh("synchronizeTabs",t,"experimentalForceOwningTab",r)),t?this._persistenceProvider.enableMultiTabIndexedDbPersistence(this):this._persistenceProvider.enableIndexedDbPersistence(this,r)}clearPersistence(){return this._persistenceProvider.clearIndexedDbPersistence(this)}terminate(){return this._appCompat&&(this._appCompat._removeServiceInstance("firestore-compat"),this._appCompat._removeServiceInstance("firestore")),this._delegate._delete()}waitForPendingWrites(){var t=this._delegate;{var r=Hh(t=V(t,U));let e=new f;return r.asyncQueue.enqueueAndForget(async()=>Mu(await dh(r),e)),e.promise}}onSnapshotsInSync(e){return t=this._delegate,e=e,wh(Hh(t=V(t,U)),jh(e)?e:{next:e});var t}get app(){if(this._appCompat)return this._appCompat;throw new I("failed-precondition","Firestore was not initialized using the Firebase SDK. 'app' is not available")}collection(e){try{return new kd(this,Fh(this._delegate,e))}catch(e){throw Td(e,"collection()","Firestore.collection()")}}doc(e){try{return new Id(this,Vh(this._delegate,e))}catch(e){throw Td(e,"doc()","Firestore.doc()")}}collectionGroup(e){try{return new Cd(this,((e,t)=>{if(e=V(e,Rh),Eh("collectionGroup","collection id",t),0<=t.indexOf("/"))throw new I(b.INVALID_ARGUMENT,`Invalid collection ID '${t}' passed to function collectionGroup(). Collection IDs must not contain '/'.`);return new Oh(e,null,(e=t,new ei(T.emptyPath(),e)))})(this._delegate,e))}catch(e){throw Td(e,"collectionGroup()","Firestore.collectionGroup()")}}runTransaction(t){return hd(this._delegate,e=>t(new wd(this,e)))}batch(){return Hh(this._delegate),new _d(new od(this._delegate,e=>id(this._delegate,e)))}loadBundle(e){return t=this._delegate,e=e,r=Hh(t=V(t,U)),n=new Kh,_h(r,t._databaseId,e,n),n;var t,r,n}namedQuery(e){return t=this._delegate,e=e,bh(Hh(t=V(t,U)),e).then(e=>e?new Oh(t,null,e.query):null).then(e=>e?new Cd(this,e):null);var t}}class vd extends Qc{constructor(e){super(),this.firestore=e}convertBytes(e){return new gd(new Zh(e))}convertReference(e){var t=this.convertDocumentKey(e,this.firestore._databaseId);return Id.forKey(t,this.firestore,null)}}class wd{constructor(e,t){this._firestore=e,this._delegate=t,this._userDataWriter=new vd(e)}get(e){let t=Rd(e);return this._delegate.get(t).then(e=>new xd(this._firestore,new Yc(this._firestore._delegate,this._userDataWriter,e._key,e._document,e.metadata,t.converter)))}set(e,t,r){var n=Rd(e);return r?(cd("Transaction.set",r),this._delegate.set(n,t,r)):this._delegate.set(n,t),this}update(e,t,r,...n){var i=Rd(e);return 2===arguments.length?this._delegate.update(i,t):this._delegate.update(i,t,r,...n),this}delete(e){var t=Rd(e);return this._delegate.delete(t),this}}class _d{constructor(e){this._delegate=e}set(e,t,r){var n=Rd(e);return r?(cd("WriteBatch.set",r),this._delegate.set(n,t,r)):this._delegate.set(n,t),this}update(e,t,r,...n){var i=Rd(e);return 2===arguments.length?this._delegate.update(i,t):this._delegate.update(i,t,r,...n),this}delete(e){var t=Rd(e);return this._delegate.delete(t),this}commit(){return this._delegate.commit()}}class bd{constructor(e,t,r){this._firestore=e,this._userDataWriter=t,this._delegate=r}fromFirestore(e,t){var r=new Jc(this._firestore._delegate,this._userDataWriter,e._key,e._document,e.metadata,null);return this._delegate.fromFirestore(new Ad(this._firestore,r),null!=t?t:{})}toFirestore(e,t){return t?this._delegate.toFirestore(e,t):this._delegate.toFirestore(e)}static getInstance(e,t){var r=bd.INSTANCES;let n=r.get(e),i=(n||(n=new WeakMap,r.set(e,n)),n.get(t));return i||(i=new bd(e,new vd(e),t),n.set(t,i)),i}}bd.INSTANCES=new WeakMap;class Id{constructor(e,t){this.firestore=e,this._delegate=t,this._userDataWriter=new vd(e)}static forPath(e,t,r){if(e.length%2!=0)throw new I("invalid-argument","Invalid document reference. Document references must have an even number of segments, but "+e.canonicalString()+" has "+e.length);return new Id(t,new P(t._delegate,r,new x(e)))}static forKey(e,t,r){return new Id(t,new P(t._delegate,r,e))}get id(){return this._delegate.id}get parent(){return new kd(this.firestore,this._delegate.parent)}get path(){return this._delegate.path}collection(e){try{return new kd(this.firestore,Fh(this._delegate,e))}catch(e){throw Td(e,"collection()","DocumentReference.collection()")}}isEqual(e){return(e=_(e))instanceof P&&Ph(this._delegate,e)}set(e,t){t=cd("DocumentReference.set",t);try{return t?td(this._delegate,e,t):td(this._delegate,e)}catch(e){throw Td(e,"setDoc()","DocumentReference.set()")}}update(e,t,...r){try{return 1===arguments.length?rd(this._delegate,e):rd(this._delegate,e,t,...r)}catch(e){throw Td(e,"updateDoc()","DocumentReference.update()")}}delete(){return id(V((e=this._delegate).firestore,U),[new Wi(e._key,L.none())]);var e}onSnapshot(...e){var t=Ed(e),r=Sd(e,e=>new xd(this.firestore,new Yc(this.firestore._delegate,this._userDataWriter,e._key,e._document,e.metadata,this._delegate.converter)));return nd(this._delegate,t,r)}get(e){let t;return(t=("cache"===(null==e?void 0:e.source)?t=>{t=V(t,P);let r=V(t.firestore,U),e=Hh(r),n=new ed(r);return mh(e,t._key).then(e=>new Yc(r,n,t._key,e,new Wc(null!==e&&e.hasLocalMutations,!0),t.converter))}:"server"===(null==e?void 0:e.source)?t=>{t=V(t,P);let r=V(t.firestore,U);return ph(Hh(r),t._key,{source:"server"}).then(e=>sd(r,t,e))}:t=>{t=V(t,P);let r=V(t.firestore,U);return ph(Hh(r),t._key).then(e=>sd(r,t,e))})(this._delegate)).then(e=>new xd(this.firestore,new Yc(this.firestore._delegate,this._userDataWriter,e._key,e._document,e.metadata,this._delegate.converter)))}withConverter(e){return new Id(this.firestore,e?this._delegate.withConverter(bd.getInstance(this.firestore,e)):this._delegate.withConverter(null))}}function Td(e,t,r){return e.message=e.message.replace(t,r),e}function Ed(e){for(var t of e)if("object"==typeof t&&!md(t))return t;return{}}function Sd(e,t){let r,n,i;return i=md(e[0])?e[0]:md(e[1])?e[1]:"function"==typeof e[0]?{next:e[0],error:e[1],complete:e[2]}:{next:e[1],error:e[2],complete:e[3]},{next:e=>{i.next&&i.next(t(e))},error:null==(r=i.error)?void 0:r.bind(i),complete:null==(n=i.complete)?void 0:n.bind(i)}}class xd{constructor(e,t){this._firestore=e,this._delegate=t}get ref(){return new Id(this._firestore,this._delegate.ref)}get id(){return this._delegate.id}get metadata(){return this._delegate.metadata}get exists(){return this._delegate.exists()}data(e){return this._delegate.data(e)}get(e,t){return this._delegate.get(e,t)}isEqual(e){return Zc(this._delegate,e._delegate)}}class Ad extends xd{data(e){var t=this._delegate.data(e);return this._delegate._converter||(e="Document in a QueryDocumentSnapshot should exist",void 0!==t)||E(57014,e),t}}class Cd{constructor(e,t){this.firestore=e,this._delegate=t,this._userDataWriter=new vd(e)}where(e,t,r){try{return new Cd(this.firestore,Fc(this._delegate,(n=r,i=t,s=Rc("where",e),Vc._create(s,i,n))))}catch(e){throw Td(e,/(orderBy|where)\(\)/,"Query.$1()")}var n,i,s}orderBy(e,t){try{return new Cd(this.firestore,Fc(this._delegate,([r,n="asc"]=[e,t],i=n,s=Rc("orderBy",r),Pc._create(s,i))))}catch(e){throw Td(e,/(orderBy|where)\(\)/,"Query.$1()")}var r,n,i,s}limit(e){try{return new Cd(this.firestore,Fc(this._delegate,(Dh("limit",t=e),Uc._create("limit",t,"F"))))}catch(e){throw Td(e,"limit()","Query.limit()")}var t}limitToLast(e){try{return new Cd(this.firestore,Fc(this._delegate,(Dh("limitToLast",t=e),Uc._create("limitToLast",t,"L"))))}catch(e){throw Td(e,"limitToLast()","Query.limitToLast()")}var t}startAt(...e){try{return new Cd(this.firestore,Fc(this._delegate,([...t]=[...e],Bc._create("startAt",t,!0))))}catch(e){throw Td(e,"startAt()","Query.startAt()")}var t}startAfter(...e){try{return new Cd(this.firestore,Fc(this._delegate,([...t]=[...e],Bc._create("startAfter",t,!1))))}catch(e){throw Td(e,"startAfter()","Query.startAfter()")}var t}endBefore(...e){try{return new Cd(this.firestore,Fc(this._delegate,([...t]=[...e],qc._create("endBefore",t,!1))))}catch(e){throw Td(e,"endBefore()","Query.endBefore()")}var t}endAt(...e){try{return new Cd(this.firestore,Fc(this._delegate,([...t]=[...e],qc._create("endAt",t,!0))))}catch(e){throw Td(e,"endAt()","Query.endAt()")}var t}isEqual(e){return Uh(this._delegate,e._delegate)}get(e){let t;return(t=("cache"===(null==e?void 0:e.source)?t=>{t=V(t,Oh);let r=V(t.firestore,U),e=Hh(r),n=new ed(r);return yh(e,t._query).then(e=>new Xc(r,n,t,e))}:"server"===(null==e?void 0:e.source)?t=>{t=V(t,Oh);let r=V(t.firestore,U),e=Hh(r),n=new ed(r);return vh(e,t._query,{source:"server"}).then(e=>new Xc(r,n,t,e))}:t=>{t=V(t,Oh);let r=V(t.firestore,U),e=Hh(r),n=new ed(r);return Mc(t._query),vh(e,t._query).then(e=>new Xc(r,n,t,e))})(this._delegate)).then(e=>new Nd(this.firestore,new Xc(this.firestore._delegate,this._userDataWriter,this._delegate,e._snapshot)))}onSnapshot(...e){var t=Ed(e),r=Sd(e,e=>new Nd(this.firestore,new Xc(this.firestore._delegate,this._userDataWriter,this._delegate,e._snapshot)));return nd(this._delegate,t,r)}withConverter(e){return new Cd(this.firestore,e?this._delegate.withConverter(bd.getInstance(this.firestore,e)):this._delegate.withConverter(null))}}class Dd{constructor(e,t){this._firestore=e,this._delegate=t}get type(){return this._delegate.type}get doc(){return new Ad(this._firestore,this._delegate.doc)}get oldIndex(){return this._delegate.oldIndex}get newIndex(){return this._delegate.newIndex}}class Nd{constructor(e,t){this._firestore=e,this._delegate=t}get query(){return new Cd(this._firestore,this._delegate.query)}get metadata(){return this._delegate.metadata}get size(){return this._delegate.size}get empty(){return this._delegate.empty}get docs(){return this._delegate.docs.map(e=>new Ad(this._firestore,e))}docChanges(e){return this._delegate.docChanges(e).map(e=>new Dd(this._firestore,e))}forEach(t,r){this._delegate.forEach(e=>{t.call(r,new Ad(this._firestore,e))})}isEqual(e){return Zc(this._delegate,e._delegate)}}class kd extends Cd{constructor(e,t){super(e,t),this.firestore=e,this._delegate=t}get id(){return this._delegate.id}get path(){return this._delegate.path}get parent(){var e=this._delegate.parent;return e?new Id(this.firestore,e):null}doc(e){try{return void 0===e?new Id(this.firestore,Vh(this._delegate)):new Id(this.firestore,Vh(this._delegate,e))}catch(e){throw Td(e,"doc()","CollectionReference.doc()")}}add(e){return((e,t)=>{let r=V(e.firestore,U),n=Vh(e),i=$c(e.converter,t);return id(r,[cc(hc(e.firestore),"addDoc",n._key,i,null!==e.converter,{}).toMutation(n._key,L.exists(!1))]).then(()=>n)})(this._delegate,e).then(e=>new Id(this.firestore,e))}isEqual(e){return Ph(this._delegate,e._delegate)}withConverter(e){return new kd(this.firestore,e?this._delegate.withConverter(bd.getInstance(this.firestore,e)):this._delegate.withConverter(null))}}function Rd(e){return V(e,P)}class Md{static serverTimestamp(){var e=new gc("serverTimestamp");return e._methodName="FieldValue.serverTimestamp",new Md(e)}static delete(){var e=new dc("deleteField");return e._methodName="FieldValue.delete",new Md(e)}static arrayUnion(...e){[...e]=[...e];var t=new mc("arrayUnion",e);return t._methodName="FieldValue.arrayUnion",new Md(t)}static arrayRemove(...e){[...e]=[...e];var t=new pc("arrayRemove",e);return t._methodName="FieldValue.arrayRemove",new Md(t)}static increment(e){e=e;var t=new yc("increment",e);return t._methodName="FieldValue.increment",new Md(t)}constructor(e){this._delegate=e}isEqual(e){return this._delegate.isEqual(e._delegate)}}let Od={Firestore:yd,GeoPoint:rc,Timestamp:h,Blob:gd,Transaction:wd,WriteBatch:_d,DocumentReference:Id,DocumentSnapshot:xd,Query:Cd,QueryDocumentSnapshot:Ad,QuerySnapshot:Nd,CollectionReference:kd,FieldPath:class Ld{constructor(...e){this._delegate=new ec(...e)}static documentId(){return new Ld(v.keyField().canonicalString())}isEqual(e){return(e=_(e))instanceof ec&&this._delegate._internalPath.isEqual(e._internalPath)}},FieldValue:Md,setLogLevel:function(e){e=e,ge.setLogLevel(e)},CACHE_SIZE_UNLIMITED:-1};t=i.default,$h=(e,t)=>new yd(e,t,new pd),t.INTERNAL.registerComponent(new ne("firestore-compat",e=>{var t=e.getProvider("app-compat").getImmediate(),r=e.getProvider("firestore").getImmediate();return $h(t,r)},"PUBLIC").setServiceProps(Object.assign({},Od))),t.registerVersion("@firebase/firestore-compat","0.3.46")}).apply(this,arguments)}catch(e){throw console.error(e),new Error("Cannot instantiate firebase-firestore-compat.js - be sure to load firebase-app.js first.")}});
//# sourceMappingURL=firebase-firestore-compat.js.map
