{"version": 3, "sources": ["../../../src/logger.ts", "../../../src/diagram-api/regexes.ts", "../../../src/errors.ts", "../../../src/diagram-api/detectType.ts", "../../../src/assignWithDepth.ts", "../../../src/themes/theme-base.js", "../../../src/themes/erDiagram-oldHardcodedValues.ts", "../../../src/themes/theme-helpers.js", "../../../src/themes/theme-dark.js", "../../../src/themes/theme-default.js", "../../../src/themes/theme-forest.js", "../../../src/themes/theme-neutral.js", "../../../src/themes/index.js", "../../../src/schemas/config.schema.yaml", "../../../src/defaultConfig.ts", "../../../src/utils/sanitizeDirective.ts", "../../../src/config.ts", "../../../src/diagrams/common/common.ts", "../../../src/setupGraphViewbox.js", "../../../src/styles.ts", "../../../src/diagrams/common/commonDb.ts", "../../../src/diagram-api/diagramAPI.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-empty-function */\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\n/* eslint-disable no-console */\nimport dayjs from 'dayjs';\n\nexport type LogLevel = 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'fatal';\n\nexport const LEVELS: Record<LogLevel, number> = {\n  trace: 0,\n  debug: 1,\n  info: 2,\n  warn: 3,\n  error: 4,\n  fatal: 5,\n};\n\nexport const log: Record<keyof typeof LEVELS, typeof console.log> = {\n  trace: (..._args: any[]) => {},\n  debug: (..._args: any[]) => {},\n  info: (..._args: any[]) => {},\n  warn: (..._args: any[]) => {},\n  error: (..._args: any[]) => {},\n  fatal: (..._args: any[]) => {},\n};\n\n/**\n * Sets a log level\n *\n * @param level - The level to set the logging to. Default is `\"fatal\"`\n */\nexport const setLogLevel = function (level: keyof typeof LEVELS | number = 'fatal') {\n  let numericLevel: number = LEVELS.fatal;\n  if (typeof level === 'string') {\n    if (level.toLowerCase() in LEVELS) {\n      numericLevel = LEVELS[level];\n    }\n  } else if (typeof level === 'number') {\n    numericLevel = level;\n  }\n  log.trace = () => {};\n  log.debug = () => {};\n  log.info = () => {};\n  log.warn = () => {};\n  log.error = () => {};\n  log.fatal = () => {};\n\n  if (numericLevel <= LEVELS.fatal) {\n    log.fatal = console.error\n      ? console.error.bind(console, format('FATAL'), 'color: orange')\n      : console.log.bind(console, '\\x1b[35m', format('FATAL'));\n  }\n  if (numericLevel <= LEVELS.error) {\n    log.error = console.error\n      ? console.error.bind(console, format('ERROR'), 'color: orange')\n      : console.log.bind(console, '\\x1b[31m', format('ERROR'));\n  }\n  if (numericLevel <= LEVELS.warn) {\n    log.warn = console.warn\n      ? console.warn.bind(console, format('WARN'), 'color: orange')\n      : console.log.bind(console, `\\x1b[33m`, format('WARN'));\n  }\n  if (numericLevel <= LEVELS.info) {\n    log.info = console.info\n      ? console.info.bind(console, format('INFO'), 'color: lightblue')\n      : console.log.bind(console, '\\x1b[34m', format('INFO'));\n  }\n  if (numericLevel <= LEVELS.debug) {\n    log.debug = console.debug\n      ? console.debug.bind(console, format('DEBUG'), 'color: lightgreen')\n      : console.log.bind(console, '\\x1b[32m', format('DEBUG'));\n  }\n  if (numericLevel <= LEVELS.trace) {\n    log.trace = console.debug\n      ? console.debug.bind(console, format('TRACE'), 'color: lightgreen')\n      : console.log.bind(console, '\\x1b[32m', format('TRACE'));\n  }\n};\n\n/**\n * Returns a format with the timestamp and the log level\n *\n * @param level - The level for the log format\n * @returns The format with the timestamp and log level\n */\nconst format = (level: Uppercase<LogLevel>): string => {\n  const time = dayjs().format('ss.SSS');\n  return `%c${time} : ${level} : `;\n};\n", "// Match Jekyll-style front matter blocks (https://jekyllrb.com/docs/front-matter/).\n// Based on regex used by <PERSON><PERSON><PERSON>: https://github.com/jekyll/jekyll/blob/6dd3cc21c40b98054851846425af06c64f9fb466/lib/jekyll/document.rb#L10\n// Note that JS doesn't support the \"\\A\" anchor, which means we can't use\n// multiline mode.\n// Relevant YAML spec: https://yaml.org/spec/1.2.2/#914-explicit-documents\nexport const frontMatterRegex = /^-{3}\\s*[\\n\\r](.*?)[\\n\\r]-{3}\\s*[\\n\\r]+/s;\n\nexport const directiveRegex =\n  /%{2}{\\s*(?:(\\w+)\\s*:|(\\w+))\\s*(?:(\\w+)|((?:(?!}%{2}).|\\r?\\n)*))?\\s*(?:}%{2})?/gi;\n\nexport const anyCommentRegex = /\\s*%%.*\\n/gm;\n", "export class UnknownDiagramError extends Error {\n  constructor(message: string) {\n    super(message);\n    this.name = 'UnknownDiagramError';\n  }\n}\n", "import type { MermaidConfig } from '../config.type.js';\nimport { log } from '../logger.js';\nimport type {\n  DetectorRecord,\n  DiagramDetector,\n  DiagramLoader,\n  ExternalDiagramDefinition,\n} from './types.js';\nimport { anyCommentRegex, directiveRegex, frontMatterRegex } from './regexes.js';\nimport { UnknownDiagramError } from '../errors.js';\n\nexport const detectors: Record<string, DetectorRecord> = {};\n\n/**\n * Detects the type of the graph text.\n *\n * Takes into consideration the possible existence of an `%%init` directive\n *\n * @param text - The text defining the graph. For example:\n *\n * ```mermaid\n *   %%{initialize: {\"startOnLoad\": true, logLevel: \"fatal\" }}%%\n *   graph LR\n *    a-->b\n *    b-->c\n *    c-->d\n *    d-->e\n *    e-->f\n *    f-->g\n *    g-->h\n * ```\n *\n * @param config - The mermaid config.\n * @returns A graph definition key\n */\nexport const detectType = function (text: string, config?: MermaidConfig): string {\n  text = text\n    .replace(frontMatterRegex, '')\n    .replace(directiveRegex, '')\n    .replace(anyCommentRegex, '\\n');\n  for (const [key, { detector }] of Object.entries(detectors)) {\n    const diagram = detector(text, config);\n    if (diagram) {\n      return key;\n    }\n  }\n\n  throw new UnknownDiagramError(\n    `No diagram type detected matching given configuration for text: ${text}`\n  );\n};\n\n/**\n * Registers lazy-loaded diagrams to Mermaid.\n *\n * The diagram function is loaded asynchronously, so that diagrams are only loaded\n * if the diagram is detected.\n *\n * @remarks\n * Please note that the order of diagram detectors is important.\n * The first detector to return `true` is the diagram that will be loaded\n * and used, so put more specific detectors at the beginning!\n *\n * @param diagrams - Diagrams to lazy load, and their detectors, in order of importance.\n */\nexport const registerLazyLoadedDiagrams = (...diagrams: ExternalDiagramDefinition[]) => {\n  for (const { id, detector, loader } of diagrams) {\n    addDetector(id, detector, loader);\n  }\n};\n\nexport const addDetector = (key: string, detector: DiagramDetector, loader?: DiagramLoader) => {\n  if (detectors[key]) {\n    log.warn(`Detector with key ${key} already exists. Overwriting.`);\n  }\n  detectors[key] = { detector, loader };\n  log.debug(`Detector with key ${key} added${loader ? ' with loader' : ''}`);\n};\n\nexport const getDiagramLoader = (key: string) => {\n  return detectors[key].loader;\n};\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\n\n/**\n * assignWithDepth Extends the functionality of {@link Object.assign} with the\n *   ability to merge arbitrary-depth objects For each key in src with path `k` (recursively)\n *   performs an Object.assign(dst[`k`], src[`k`]) with a slight change from the typical handling of\n *   undefined for dst[`k`]: instead of raising an error, dst[`k`] is auto-initialized to `{}` and\n *   effectively merged with src[`k`]<p> Additionally, dissimilar types will not clobber unless the\n *   config.clobber parameter === true. Example:\n *\n * ```\n * const config_0 = { foo: { bar: 'bar' }, bar: 'foo' };\n * const config_1 = { foo: 'foo', bar: 'bar' };\n * const result = assignWithDepth(config_0, config_1);\n * console.log(result);\n * //-> result: { foo: { bar: 'bar' }, bar: 'bar' }\n * ```\n *\n *   Traditional Object.assign would have clobbered foo in config_0 with foo in config_1. If src is a\n *   destructured array of objects and dst is not an array, assign<PERSON>ith<PERSON>epth will apply each element\n *   of src to dst in order.\n * @param dst - The destination of the merge\n * @param src - The source object(s) to merge into destination\n * @param config -\n * * depth: depth to traverse within src and dst for merging\n * * clobber: should dissimilar types clobber\n */\nconst assignWithDepth = (\n  dst: any,\n  src: any,\n  { depth = 2, clobber = false }: { depth?: number; clobber?: boolean } = {}\n): any => {\n  const config: { depth: number; clobber: boolean } = { depth, clobber };\n  if (Array.isArray(src) && !Array.isArray(dst)) {\n    src.forEach((s) => assignWithDepth(dst, s, config));\n    return dst;\n  } else if (Array.isArray(src) && Array.isArray(dst)) {\n    src.forEach((s) => {\n      if (!dst.includes(s)) {\n        dst.push(s);\n      }\n    });\n    return dst;\n  }\n  if (dst === undefined || depth <= 0) {\n    if (dst !== undefined && dst !== null && typeof dst === 'object' && typeof src === 'object') {\n      return Object.assign(dst, src);\n    } else {\n      return src;\n    }\n  }\n  if (src !== undefined && typeof dst === 'object' && typeof src === 'object') {\n    Object.keys(src).forEach((key) => {\n      if (\n        typeof src[key] === 'object' &&\n        (dst[key] === undefined || typeof dst[key] === 'object')\n      ) {\n        if (dst[key] === undefined) {\n          dst[key] = Array.isArray(src[key]) ? [] : {};\n        }\n        dst[key] = assignWithDepth(dst[key], src[key], { depth: depth - 1, clobber });\n      } else if (clobber || (typeof dst[key] !== 'object' && typeof src[key] !== 'object')) {\n        dst[key] = src[key];\n      }\n    });\n  }\n  return dst;\n};\n\nexport default assignWithDepth;\n", "import { adjust, darken, invert, isDark, lighten } from 'khroma';\nimport {\n  oldAttributeBackgroundColorEven,\n  oldAttributeBackgroundColorOdd,\n} from './erDiagram-oldHardcodedValues.js';\nimport { mkBorder } from './theme-helpers.js';\n\nclass Theme {\n  constructor() {\n    /** # Base variables */\n    /**\n     * - Background - used to know what the background color is of the diagram. This is used for\n     *   deducing colors for instance line color. Default value is #f4f4f4.\n     */\n    this.background = '#f4f4f4';\n\n    this.primaryColor = '#fff4dd';\n\n    this.noteBkgColor = '#fff5ad';\n    this.noteTextColor = '#333';\n\n    this.THEME_COLOR_LIMIT = 12;\n\n    // dark\n\n    this.fontFamily = '\"trebuchet ms\", verdana, arial, sans-serif';\n    this.fontSize = '16px';\n  }\n  updateColors() {\n    // The || is to make sure that if the variable has been defined by a user override that value is to be used\n\n    /* Main */\n    this.primaryTextColor = this.primaryTextColor || (this.darkMode ? '#eee' : '#333'); // invert(this.primaryColor);\n    this.secondaryColor = this.secondaryColor || adjust(this.primaryColor, { h: -120 });\n    this.tertiaryColor = this.tertiaryColor || adjust(this.primaryColor, { h: 180, l: 5 });\n\n    this.primaryBorderColor = this.primaryBorderColor || mkBorder(this.primaryColor, this.darkMode);\n    this.secondaryBorderColor =\n      this.secondaryBorderColor || mkBorder(this.secondaryColor, this.darkMode);\n    this.tertiaryBorderColor =\n      this.tertiaryBorderColor || mkBorder(this.tertiaryColor, this.darkMode);\n    this.noteBorderColor = this.noteBorderColor || mkBorder(this.noteBkgColor, this.darkMode);\n    this.noteBkgColor = this.noteBkgColor || '#fff5ad';\n    this.noteTextColor = this.noteTextColor || '#333';\n\n    this.secondaryTextColor = this.secondaryTextColor || invert(this.secondaryColor);\n    this.tertiaryTextColor = this.tertiaryTextColor || invert(this.tertiaryColor);\n    this.lineColor = this.lineColor || invert(this.background);\n    this.arrowheadColor = this.arrowheadColor || invert(this.background);\n    this.textColor = this.textColor || this.primaryTextColor;\n\n    // TODO: should this instead default to secondaryBorderColor?\n    this.border2 = this.border2 || this.tertiaryBorderColor;\n\n    /* Flowchart variables */\n    this.nodeBkg = this.nodeBkg || this.primaryColor;\n    this.mainBkg = this.mainBkg || this.primaryColor;\n    this.nodeBorder = this.nodeBorder || this.primaryBorderColor;\n    this.clusterBkg = this.clusterBkg || this.tertiaryColor;\n    this.clusterBorder = this.clusterBorder || this.tertiaryBorderColor;\n    this.defaultLinkColor = this.defaultLinkColor || this.lineColor;\n    this.titleColor = this.titleColor || this.tertiaryTextColor;\n    this.edgeLabelBackground =\n      this.edgeLabelBackground ||\n      (this.darkMode ? darken(this.secondaryColor, 30) : this.secondaryColor);\n    this.nodeTextColor = this.nodeTextColor || this.primaryTextColor;\n    /* Sequence Diagram variables */\n\n    // this.actorBorder = lighten(this.border1, 0.5);\n    this.actorBorder = this.actorBorder || this.primaryBorderColor;\n    this.actorBkg = this.actorBkg || this.mainBkg;\n    this.actorTextColor = this.actorTextColor || this.primaryTextColor;\n    this.actorLineColor = this.actorLineColor || this.actorBorder;\n    this.labelBoxBkgColor = this.labelBoxBkgColor || this.actorBkg;\n    this.signalColor = this.signalColor || this.textColor;\n    this.signalTextColor = this.signalTextColor || this.textColor;\n    this.labelBoxBorderColor = this.labelBoxBorderColor || this.actorBorder;\n    this.labelTextColor = this.labelTextColor || this.actorTextColor;\n    this.loopTextColor = this.loopTextColor || this.actorTextColor;\n    this.activationBorderColor = this.activationBorderColor || darken(this.secondaryColor, 10);\n    this.activationBkgColor = this.activationBkgColor || this.secondaryColor;\n    this.sequenceNumberColor = this.sequenceNumberColor || invert(this.lineColor);\n\n    /* Gantt chart variables */\n\n    this.sectionBkgColor = this.sectionBkgColor || this.tertiaryColor;\n    this.altSectionBkgColor = this.altSectionBkgColor || 'white';\n    this.sectionBkgColor = this.sectionBkgColor || this.secondaryColor;\n    this.sectionBkgColor2 = this.sectionBkgColor2 || this.primaryColor;\n    this.excludeBkgColor = this.excludeBkgColor || '#eeeeee';\n    this.taskBorderColor = this.taskBorderColor || this.primaryBorderColor;\n    this.taskBkgColor = this.taskBkgColor || this.primaryColor;\n    this.activeTaskBorderColor = this.activeTaskBorderColor || this.primaryColor;\n    this.activeTaskBkgColor = this.activeTaskBkgColor || lighten(this.primaryColor, 23);\n    this.gridColor = this.gridColor || 'lightgrey';\n    this.doneTaskBkgColor = this.doneTaskBkgColor || 'lightgrey';\n    this.doneTaskBorderColor = this.doneTaskBorderColor || 'grey';\n    this.critBorderColor = this.critBorderColor || '#ff8888';\n    this.critBkgColor = this.critBkgColor || 'red';\n    this.todayLineColor = this.todayLineColor || 'red';\n    this.taskTextColor = this.taskTextColor || this.textColor;\n    this.taskTextOutsideColor = this.taskTextOutsideColor || this.textColor;\n    this.taskTextLightColor = this.taskTextLightColor || this.textColor;\n    this.taskTextColor = this.taskTextColor || this.primaryTextColor;\n    this.taskTextDarkColor = this.taskTextDarkColor || this.textColor;\n    this.taskTextClickableColor = this.taskTextClickableColor || '#003163';\n\n    /* Sequence Diagram variables */\n\n    this.personBorder = this.personBorder || this.primaryBorderColor;\n    this.personBkg = this.personBkg || this.mainBkg;\n\n    /* ER diagram */\n\n    if (this.darkMode) {\n      this.rowOdd = this.rowOdd || darken(this.mainBkg, 5) || '#ffffff';\n      this.rowEven = this.rowEven || darken(this.mainBkg, 10);\n    } else {\n      this.rowOdd = this.rowOdd || lighten(this.mainBkg, 75) || '#ffffff';\n      this.rowEven = this.rowEven || lighten(this.mainBkg, 5);\n    }\n\n    /* state colors */\n    this.transitionColor = this.transitionColor || this.lineColor;\n    this.transitionLabelColor = this.transitionLabelColor || this.textColor;\n    /* The color of the text tables of the states*/\n    this.stateLabelColor = this.stateLabelColor || this.stateBkg || this.primaryTextColor;\n\n    this.stateBkg = this.stateBkg || this.mainBkg;\n    this.labelBackgroundColor = this.labelBackgroundColor || this.stateBkg;\n    this.compositeBackground = this.compositeBackground || this.background || this.tertiaryColor;\n    this.altBackground = this.altBackground || this.tertiaryColor;\n    this.compositeTitleBackground = this.compositeTitleBackground || this.mainBkg;\n    this.compositeBorder = this.compositeBorder || this.nodeBorder;\n    this.innerEndBackground = this.nodeBorder;\n    this.errorBkgColor = this.errorBkgColor || this.tertiaryColor;\n    this.errorTextColor = this.errorTextColor || this.tertiaryTextColor;\n    this.transitionColor = this.transitionColor || this.lineColor;\n    this.specialStateColor = this.lineColor;\n\n    /* Color Scale */\n    /* Each color-set will have a background, a foreground and a border color */\n    this.cScale0 = this.cScale0 || this.primaryColor;\n    this.cScale1 = this.cScale1 || this.secondaryColor;\n    this.cScale2 = this.cScale2 || this.tertiaryColor;\n    this.cScale3 = this.cScale3 || adjust(this.primaryColor, { h: 30 });\n    this.cScale4 = this.cScale4 || adjust(this.primaryColor, { h: 60 });\n    this.cScale5 = this.cScale5 || adjust(this.primaryColor, { h: 90 });\n    this.cScale6 = this.cScale6 || adjust(this.primaryColor, { h: 120 });\n    this.cScale7 = this.cScale7 || adjust(this.primaryColor, { h: 150 });\n    this.cScale8 = this.cScale8 || adjust(this.primaryColor, { h: 210, l: 150 });\n    this.cScale9 = this.cScale9 || adjust(this.primaryColor, { h: 270 });\n    this.cScale10 = this.cScale10 || adjust(this.primaryColor, { h: 300 });\n    this.cScale11 = this.cScale11 || adjust(this.primaryColor, { h: 330 });\n    if (this.darkMode) {\n      for (let i = 0; i < this.THEME_COLOR_LIMIT; i++) {\n        this['cScale' + i] = darken(this['cScale' + i], 75);\n      }\n    } else {\n      for (let i = 0; i < this.THEME_COLOR_LIMIT; i++) {\n        this['cScale' + i] = darken(this['cScale' + i], 25);\n      }\n    }\n\n    // Setup the inverted color for the set\n    for (let i = 0; i < this.THEME_COLOR_LIMIT; i++) {\n      this['cScaleInv' + i] = this['cScaleInv' + i] || invert(this['cScale' + i]);\n    }\n    // Setup the peer color for the set, useful for borders\n    for (let i = 0; i < this.THEME_COLOR_LIMIT; i++) {\n      if (this.darkMode) {\n        this['cScalePeer' + i] = this['cScalePeer' + i] || lighten(this['cScale' + i], 10);\n      } else {\n        this['cScalePeer' + i] = this['cScalePeer' + i] || darken(this['cScale' + i], 10);\n      }\n    }\n\n    // Setup the label color for the set\n    this.scaleLabelColor = this.scaleLabelColor || this.labelTextColor;\n\n    for (let i = 0; i < this.THEME_COLOR_LIMIT; i++) {\n      this['cScaleLabel' + i] = this['cScaleLabel' + i] || this.scaleLabelColor;\n    }\n\n    const multiplier = this.darkMode ? -4 : -1;\n    for (let i = 0; i < 5; i++) {\n      this['surface' + i] =\n        this['surface' + i] ||\n        adjust(this.mainBkg, { h: 180, s: -15, l: multiplier * (5 + i * 3) });\n      this['surfacePeer' + i] =\n        this['surfacePeer' + i] ||\n        adjust(this.mainBkg, { h: 180, s: -15, l: multiplier * (8 + i * 3) });\n    }\n\n    /* class */\n    this.classText = this.classText || this.textColor;\n\n    /* user-journey */\n    this.fillType0 = this.fillType0 || this.primaryColor;\n    this.fillType1 = this.fillType1 || this.secondaryColor;\n    this.fillType2 = this.fillType2 || adjust(this.primaryColor, { h: 64 });\n    this.fillType3 = this.fillType3 || adjust(this.secondaryColor, { h: 64 });\n    this.fillType4 = this.fillType4 || adjust(this.primaryColor, { h: -64 });\n    this.fillType5 = this.fillType5 || adjust(this.secondaryColor, { h: -64 });\n    this.fillType6 = this.fillType6 || adjust(this.primaryColor, { h: 128 });\n    this.fillType7 = this.fillType7 || adjust(this.secondaryColor, { h: 128 });\n\n    /* pie */\n    this.pie1 = this.pie1 || this.primaryColor;\n    this.pie2 = this.pie2 || this.secondaryColor;\n    this.pie3 = this.pie3 || this.tertiaryColor;\n    this.pie4 = this.pie4 || adjust(this.primaryColor, { l: -10 });\n    this.pie5 = this.pie5 || adjust(this.secondaryColor, { l: -10 });\n    this.pie6 = this.pie6 || adjust(this.tertiaryColor, { l: -10 });\n    this.pie7 = this.pie7 || adjust(this.primaryColor, { h: +60, l: -10 });\n    this.pie8 = this.pie8 || adjust(this.primaryColor, { h: -60, l: -10 });\n    this.pie9 = this.pie9 || adjust(this.primaryColor, { h: 120, l: 0 });\n    this.pie10 = this.pie10 || adjust(this.primaryColor, { h: +60, l: -20 });\n    this.pie11 = this.pie11 || adjust(this.primaryColor, { h: -60, l: -20 });\n    this.pie12 = this.pie12 || adjust(this.primaryColor, { h: 120, l: -10 });\n    this.pieTitleTextSize = this.pieTitleTextSize || '25px';\n    this.pieTitleTextColor = this.pieTitleTextColor || this.taskTextDarkColor;\n    this.pieSectionTextSize = this.pieSectionTextSize || '17px';\n    this.pieSectionTextColor = this.pieSectionTextColor || this.textColor;\n    this.pieLegendTextSize = this.pieLegendTextSize || '17px';\n    this.pieLegendTextColor = this.pieLegendTextColor || this.taskTextDarkColor;\n    this.pieStrokeColor = this.pieStrokeColor || 'black';\n    this.pieStrokeWidth = this.pieStrokeWidth || '2px';\n    this.pieOuterStrokeWidth = this.pieOuterStrokeWidth || '2px';\n    this.pieOuterStrokeColor = this.pieOuterStrokeColor || 'black';\n    this.pieOpacity = this.pieOpacity || '0.7';\n\n    /* radar */\n    this.radar = {\n      axisColor: this.radar?.axisColor || this.lineColor,\n      axisStrokeWidth: this.radar?.axisStrokeWidth || 2,\n      axisLabelFontSize: this.radar?.axisLabelFontSize || 12,\n      curveOpacity: this.radar?.curveOpacity || 0.5,\n      curveStrokeWidth: this.radar?.curveStrokeWidth || 2,\n      graticuleColor: this.radar?.graticuleColor || '#DEDEDE',\n      graticuleStrokeWidth: this.radar?.graticuleStrokeWidth || 1,\n      graticuleOpacity: this.radar?.graticuleOpacity || 0.3,\n      legendBoxSize: this.radar?.legendBoxSize || 12,\n      legendFontSize: this.radar?.legendFontSize || 12,\n    };\n\n    /* architecture */\n    this.archEdgeColor = this.archEdgeColor || '#777';\n    this.archEdgeArrowColor = this.archEdgeArrowColor || '#777';\n    this.archEdgeWidth = this.archEdgeWidth || '3';\n    this.archGroupBorderColor = this.archGroupBorderColor || '#000';\n    this.archGroupBorderWidth = this.archGroupBorderWidth || '2px';\n\n    /* quadrant-graph */\n    this.quadrant1Fill = this.quadrant1Fill || this.primaryColor;\n    this.quadrant2Fill = this.quadrant2Fill || adjust(this.primaryColor, { r: 5, g: 5, b: 5 });\n    this.quadrant3Fill = this.quadrant3Fill || adjust(this.primaryColor, { r: 10, g: 10, b: 10 });\n    this.quadrant4Fill = this.quadrant4Fill || adjust(this.primaryColor, { r: 15, g: 15, b: 15 });\n    this.quadrant1TextFill = this.quadrant1TextFill || this.primaryTextColor;\n    this.quadrant2TextFill =\n      this.quadrant2TextFill || adjust(this.primaryTextColor, { r: -5, g: -5, b: -5 });\n    this.quadrant3TextFill =\n      this.quadrant3TextFill || adjust(this.primaryTextColor, { r: -10, g: -10, b: -10 });\n    this.quadrant4TextFill =\n      this.quadrant4TextFill || adjust(this.primaryTextColor, { r: -15, g: -15, b: -15 });\n    this.quadrantPointFill =\n      this.quadrantPointFill || isDark(this.quadrant1Fill)\n        ? lighten(this.quadrant1Fill)\n        : darken(this.quadrant1Fill);\n    this.quadrantPointTextFill = this.quadrantPointTextFill || this.primaryTextColor;\n    this.quadrantXAxisTextFill = this.quadrantXAxisTextFill || this.primaryTextColor;\n    this.quadrantYAxisTextFill = this.quadrantYAxisTextFill || this.primaryTextColor;\n    this.quadrantInternalBorderStrokeFill =\n      this.quadrantInternalBorderStrokeFill || this.primaryBorderColor;\n    this.quadrantExternalBorderStrokeFill =\n      this.quadrantExternalBorderStrokeFill || this.primaryBorderColor;\n    this.quadrantTitleFill = this.quadrantTitleFill || this.primaryTextColor;\n\n    /* xychart */\n    this.xyChart = {\n      backgroundColor: this.xyChart?.backgroundColor || this.background,\n      titleColor: this.xyChart?.titleColor || this.primaryTextColor,\n      xAxisTitleColor: this.xyChart?.xAxisTitleColor || this.primaryTextColor,\n      xAxisLabelColor: this.xyChart?.xAxisLabelColor || this.primaryTextColor,\n      xAxisTickColor: this.xyChart?.xAxisTickColor || this.primaryTextColor,\n      xAxisLineColor: this.xyChart?.xAxisLineColor || this.primaryTextColor,\n      yAxisTitleColor: this.xyChart?.yAxisTitleColor || this.primaryTextColor,\n      yAxisLabelColor: this.xyChart?.yAxisLabelColor || this.primaryTextColor,\n      yAxisTickColor: this.xyChart?.yAxisTickColor || this.primaryTextColor,\n      yAxisLineColor: this.xyChart?.yAxisLineColor || this.primaryTextColor,\n      plotColorPalette:\n        this.xyChart?.plotColorPalette ||\n        '#FFF4DD,#FFD8B1,#FFA07A,#ECEFF1,#D6DBDF,#C3E0A8,#FFB6A4,#FFD74D,#738FA7,#FFFFF0',\n    };\n\n    /* requirement-diagram */\n    this.requirementBackground = this.requirementBackground || this.primaryColor;\n    this.requirementBorderColor = this.requirementBorderColor || this.primaryBorderColor;\n    this.requirementBorderSize = this.requirementBorderSize || '1';\n    this.requirementTextColor = this.requirementTextColor || this.primaryTextColor;\n    this.relationColor = this.relationColor || this.lineColor;\n    this.relationLabelBackground =\n      this.relationLabelBackground ||\n      (this.darkMode ? darken(this.secondaryColor, 30) : this.secondaryColor);\n    this.relationLabelColor = this.relationLabelColor || this.actorTextColor;\n\n    /* git */\n    this.git0 = this.git0 || this.primaryColor;\n    this.git1 = this.git1 || this.secondaryColor;\n    this.git2 = this.git2 || this.tertiaryColor;\n    this.git3 = this.git3 || adjust(this.primaryColor, { h: -30 });\n    this.git4 = this.git4 || adjust(this.primaryColor, { h: -60 });\n    this.git5 = this.git5 || adjust(this.primaryColor, { h: -90 });\n    this.git6 = this.git6 || adjust(this.primaryColor, { h: +60 });\n    this.git7 = this.git7 || adjust(this.primaryColor, { h: +120 });\n    if (this.darkMode) {\n      this.git0 = lighten(this.git0, 25);\n      this.git1 = lighten(this.git1, 25);\n      this.git2 = lighten(this.git2, 25);\n      this.git3 = lighten(this.git3, 25);\n      this.git4 = lighten(this.git4, 25);\n      this.git5 = lighten(this.git5, 25);\n      this.git6 = lighten(this.git6, 25);\n      this.git7 = lighten(this.git7, 25);\n    } else {\n      this.git0 = darken(this.git0, 25);\n      this.git1 = darken(this.git1, 25);\n      this.git2 = darken(this.git2, 25);\n      this.git3 = darken(this.git3, 25);\n      this.git4 = darken(this.git4, 25);\n      this.git5 = darken(this.git5, 25);\n      this.git6 = darken(this.git6, 25);\n      this.git7 = darken(this.git7, 25);\n    }\n    this.gitInv0 = this.gitInv0 || invert(this.git0);\n    this.gitInv1 = this.gitInv1 || invert(this.git1);\n    this.gitInv2 = this.gitInv2 || invert(this.git2);\n    this.gitInv3 = this.gitInv3 || invert(this.git3);\n    this.gitInv4 = this.gitInv4 || invert(this.git4);\n    this.gitInv5 = this.gitInv5 || invert(this.git5);\n    this.gitInv6 = this.gitInv6 || invert(this.git6);\n    this.gitInv7 = this.gitInv7 || invert(this.git7);\n    this.branchLabelColor =\n      this.branchLabelColor || (this.darkMode ? 'black' : this.labelTextColor);\n    this.gitBranchLabel0 = this.gitBranchLabel0 || this.branchLabelColor;\n    this.gitBranchLabel1 = this.gitBranchLabel1 || this.branchLabelColor;\n    this.gitBranchLabel2 = this.gitBranchLabel2 || this.branchLabelColor;\n    this.gitBranchLabel3 = this.gitBranchLabel3 || this.branchLabelColor;\n    this.gitBranchLabel4 = this.gitBranchLabel4 || this.branchLabelColor;\n    this.gitBranchLabel5 = this.gitBranchLabel5 || this.branchLabelColor;\n    this.gitBranchLabel6 = this.gitBranchLabel6 || this.branchLabelColor;\n    this.gitBranchLabel7 = this.gitBranchLabel7 || this.branchLabelColor;\n\n    this.tagLabelColor = this.tagLabelColor || this.primaryTextColor;\n    this.tagLabelBackground = this.tagLabelBackground || this.primaryColor;\n    this.tagLabelBorder = this.tagBorder || this.primaryBorderColor;\n    this.tagLabelFontSize = this.tagLabelFontSize || '10px';\n    this.commitLabelColor = this.commitLabelColor || this.secondaryTextColor;\n    this.commitLabelBackground = this.commitLabelBackground || this.secondaryColor;\n    this.commitLabelFontSize = this.commitLabelFontSize || '10px';\n\n    /* -------------------------------------------------- */\n    /* EntityRelationship diagrams                        */\n\n    this.attributeBackgroundColorOdd =\n      this.attributeBackgroundColorOdd || oldAttributeBackgroundColorOdd;\n    this.attributeBackgroundColorEven =\n      this.attributeBackgroundColorEven || oldAttributeBackgroundColorEven;\n    /* -------------------------------------------------- */\n  }\n  calculate(overrides) {\n    if (typeof overrides !== 'object') {\n      // Calculate colors form base colors\n      this.updateColors();\n      return;\n    }\n\n    const keys = Object.keys(overrides);\n\n    // Copy values from overrides, this is mainly for base colors\n    keys.forEach((k) => {\n      this[k] = overrides[k];\n    });\n\n    // Calculate colors form base colors\n    this.updateColors();\n    // Copy values from overrides again in case of an override of derived value\n    keys.forEach((k) => {\n      this[k] = overrides[k];\n    });\n  }\n}\n\nexport const getThemeVariables = (userOverrides) => {\n  const theme = new Theme();\n  theme.calculate(userOverrides);\n  return theme;\n};\n", "/**\n * Values that have been hardcoded in src/diagrams/er/styles.js. These can be used by\n *   theme-_._ files to maintain display styles until themes, styles, renderers are revised. --\n *   2022-09-22\n */\nexport const oldAttributeBackgroundColorOdd = '#ffffff';\nexport const oldAttributeBackgroundColorEven = '#f2f2f2';\n", "import { adjust } from 'khroma';\n\nexport const mkBorder = (col, darkMode) =>\n  darkMode ? adjust(col, { s: -40, l: 10 }) : adjust(col, { s: -40, l: -10 });\n", "import { adjust, darken, invert, isDark, lighten, rgba } from 'khroma';\nimport { mkBorder } from './theme-helpers.js';\n\nclass Theme {\n  constructor() {\n    this.background = '#333';\n    this.primaryColor = '#1f2020';\n    this.secondaryColor = lighten(this.primaryColor, 16);\n    this.tertiaryColor = adjust(this.primaryColor, { h: -160 });\n    this.primaryBorderColor = invert(this.background);\n    this.secondaryBorderColor = mkBorder(this.secondaryColor, this.darkMode);\n    this.tertiaryBorderColor = mkBorder(this.tertiaryColor, this.darkMode);\n    this.primaryTextColor = invert(this.primaryColor);\n    this.secondaryTextColor = invert(this.secondaryColor);\n    this.tertiaryTextColor = invert(this.tertiaryColor);\n    this.lineColor = invert(this.background);\n    this.textColor = invert(this.background);\n\n    this.mainBkg = '#1f2020';\n    this.secondBkg = 'calculated';\n    this.mainContrastColor = 'lightgrey';\n    this.darkTextColor = lighten(invert('#323D47'), 10);\n    this.lineColor = 'calculated';\n    this.border1 = '#ccc';\n    this.border2 = rgba(255, 255, 255, 0.25);\n    this.arrowheadColor = 'calculated';\n    this.fontFamily = '\"trebuchet ms\", verdana, arial, sans-serif';\n    this.fontSize = '16px';\n    this.labelBackground = '#181818';\n    this.textColor = '#ccc';\n    this.THEME_COLOR_LIMIT = 12;\n\n    /* Flowchart variables */\n    this.nodeBkg = 'calculated';\n    this.nodeBorder = 'calculated';\n    this.clusterBkg = 'calculated';\n    this.clusterBorder = 'calculated';\n    this.defaultLinkColor = 'calculated';\n    this.titleColor = '#F9FFFE';\n    this.edgeLabelBackground = 'calculated';\n\n    /* Sequence Diagram variables */\n\n    this.actorBorder = 'calculated';\n    this.actorBkg = 'calculated';\n    this.actorTextColor = 'calculated';\n    this.actorLineColor = 'calculated';\n    this.signalColor = 'calculated';\n    this.signalTextColor = 'calculated';\n    this.labelBoxBkgColor = 'calculated';\n    this.labelBoxBorderColor = 'calculated';\n    this.labelTextColor = 'calculated';\n    this.loopTextColor = 'calculated';\n    this.noteBorderColor = 'calculated';\n    this.noteBkgColor = '#fff5ad';\n    this.noteTextColor = 'calculated';\n    this.activationBorderColor = 'calculated';\n    this.activationBkgColor = 'calculated';\n    this.sequenceNumberColor = 'black';\n\n    /* Gantt chart variables */\n\n    this.sectionBkgColor = darken('#EAE8D9', 30);\n    this.altSectionBkgColor = 'calculated';\n    this.sectionBkgColor2 = '#EAE8D9';\n    this.excludeBkgColor = darken(this.sectionBkgColor, 10);\n    this.taskBorderColor = rgba(255, 255, 255, 70);\n    this.taskBkgColor = 'calculated';\n    this.taskTextColor = 'calculated';\n    this.taskTextLightColor = 'calculated';\n    this.taskTextOutsideColor = 'calculated';\n    this.taskTextClickableColor = '#003163';\n    this.activeTaskBorderColor = rgba(255, 255, 255, 50);\n    this.activeTaskBkgColor = '#81B1DB';\n    this.gridColor = 'calculated';\n    this.doneTaskBkgColor = 'calculated';\n    this.doneTaskBorderColor = 'grey';\n    this.critBorderColor = '#E83737';\n    this.critBkgColor = '#E83737';\n    this.taskTextDarkColor = 'calculated';\n    this.todayLineColor = '#DB5757';\n\n    /* C4 Context Diagram variables */\n    this.personBorder = this.primaryBorderColor;\n    this.personBkg = this.mainBkg;\n\n    /* Architecture Diagram variables */\n    this.archEdgeColor = 'calculated';\n    this.archEdgeArrowColor = 'calculated';\n    this.archEdgeWidth = '3';\n    this.archGroupBorderColor = this.primaryBorderColor;\n    this.archGroupBorderWidth = '2px';\n\n    /* Entity Relationship variables */\n    this.rowOdd = this.rowOdd || lighten(this.mainBkg, 5) || '#ffffff';\n    this.rowEven = this.rowEven || darken(this.mainBkg, 10);\n\n    /* state colors */\n    this.labelColor = 'calculated';\n\n    this.errorBkgColor = '#a44141';\n    this.errorTextColor = '#ddd';\n  }\n  updateColors() {\n    this.secondBkg = lighten(this.mainBkg, 16);\n    this.lineColor = this.mainContrastColor;\n    this.arrowheadColor = this.mainContrastColor;\n    /* Flowchart variables */\n\n    this.nodeBkg = this.mainBkg;\n    this.nodeBorder = this.border1;\n    this.clusterBkg = this.secondBkg;\n    this.clusterBorder = this.border2;\n    this.defaultLinkColor = this.lineColor;\n    this.edgeLabelBackground = lighten(this.labelBackground, 25);\n\n    /* Sequence Diagram variables */\n\n    this.actorBorder = this.border1;\n    this.actorBkg = this.mainBkg;\n    this.actorTextColor = this.mainContrastColor;\n    this.actorLineColor = this.actorBorder;\n    this.signalColor = this.mainContrastColor;\n    this.signalTextColor = this.mainContrastColor;\n    this.labelBoxBkgColor = this.actorBkg;\n    this.labelBoxBorderColor = this.actorBorder;\n    this.labelTextColor = this.mainContrastColor;\n    this.loopTextColor = this.mainContrastColor;\n    this.noteBorderColor = this.secondaryBorderColor;\n    this.noteBkgColor = this.secondBkg;\n    this.noteTextColor = this.secondaryTextColor;\n    this.activationBorderColor = this.border1;\n    this.activationBkgColor = this.secondBkg;\n\n    /* Gantt chart variables */\n\n    this.altSectionBkgColor = this.background;\n    this.taskBkgColor = lighten(this.mainBkg, 23);\n    this.taskTextColor = this.darkTextColor;\n    this.taskTextLightColor = this.mainContrastColor;\n    this.taskTextOutsideColor = this.taskTextLightColor;\n    this.gridColor = this.mainContrastColor;\n    this.doneTaskBkgColor = this.mainContrastColor;\n    this.taskTextDarkColor = this.darkTextColor;\n\n    /* Architecture Diagram variables */\n    this.archEdgeColor = this.lineColor;\n    this.archEdgeArrowColor = this.lineColor;\n\n    /* state colors */\n    this.transitionColor = this.transitionColor || this.lineColor;\n    this.transitionLabelColor = this.transitionLabelColor || this.textColor;\n    this.stateLabelColor = this.stateLabelColor || this.stateBkg || this.primaryTextColor;\n    this.stateBkg = this.stateBkg || this.mainBkg;\n    this.labelBackgroundColor = this.labelBackgroundColor || this.stateBkg;\n    this.compositeBackground = this.compositeBackground || this.background || this.tertiaryColor;\n    this.altBackground = this.altBackground || '#555';\n    this.compositeTitleBackground = this.compositeTitleBackground || this.mainBkg;\n    this.compositeBorder = this.compositeBorder || this.nodeBorder;\n    this.innerEndBackground = this.primaryBorderColor;\n    this.specialStateColor = '#f4f4f4'; // this.lineColor;\n\n    this.errorBkgColor = this.errorBkgColor || this.tertiaryColor;\n    this.errorTextColor = this.errorTextColor || this.tertiaryTextColor;\n\n    this.fillType0 = this.primaryColor;\n    this.fillType1 = this.secondaryColor;\n    this.fillType2 = adjust(this.primaryColor, { h: 64 });\n    this.fillType3 = adjust(this.secondaryColor, { h: 64 });\n    this.fillType4 = adjust(this.primaryColor, { h: -64 });\n    this.fillType5 = adjust(this.secondaryColor, { h: -64 });\n    this.fillType6 = adjust(this.primaryColor, { h: 128 });\n    this.fillType7 = adjust(this.secondaryColor, { h: 128 });\n\n    /* cScale */\n    this.cScale1 = this.cScale1 || '#0b0000';\n    this.cScale2 = this.cScale2 || '#4d1037';\n    this.cScale3 = this.cScale3 || '#3f5258';\n    this.cScale4 = this.cScale4 || '#4f2f1b';\n    this.cScale5 = this.cScale5 || '#6e0a0a';\n    this.cScale6 = this.cScale6 || '#3b0048';\n    this.cScale7 = this.cScale7 || '#995a01';\n    this.cScale8 = this.cScale8 || '#154706';\n    this.cScale9 = this.cScale9 || '#161722';\n    this.cScale10 = this.cScale10 || '#00296f';\n    this.cScale11 = this.cScale11 || '#01629c';\n    this.cScale12 = this.cScale12 || '#010029';\n\n    /* Color Scale */\n    /* Each color-set will have a background, a foreground and a border color */\n    this.cScale0 = this.cScale0 || this.primaryColor;\n    this.cScale1 = this.cScale1 || this.secondaryColor;\n    this.cScale2 = this.cScale2 || this.tertiaryColor;\n    this.cScale3 = this.cScale3 || adjust(this.primaryColor, { h: 30 });\n    this.cScale4 = this.cScale4 || adjust(this.primaryColor, { h: 60 });\n    this.cScale5 = this.cScale5 || adjust(this.primaryColor, { h: 90 });\n    this.cScale6 = this.cScale6 || adjust(this.primaryColor, { h: 120 });\n    this.cScale7 = this.cScale7 || adjust(this.primaryColor, { h: 150 });\n    this.cScale8 = this.cScale8 || adjust(this.primaryColor, { h: 210 });\n    this.cScale9 = this.cScale9 || adjust(this.primaryColor, { h: 270 });\n    this.cScale10 = this.cScale10 || adjust(this.primaryColor, { h: 300 });\n    this.cScale11 = this.cScale11 || adjust(this.primaryColor, { h: 330 });\n\n    // Setup the inverted color for the set\n    for (let i = 0; i < this.THEME_COLOR_LIMIT; i++) {\n      this['cScaleInv' + i] = this['cScaleInv' + i] || invert(this['cScale' + i]);\n    }\n    // Setup the peer color for the set, useful for borders\n    for (let i = 0; i < this.THEME_COLOR_LIMIT; i++) {\n      this['cScalePeer' + i] = this['cScalePeer' + i] || lighten(this['cScale' + i], 10);\n    }\n\n    for (let i = 0; i < 5; i++) {\n      this['surface' + i] =\n        this['surface' + i] || adjust(this.mainBkg, { h: 30, s: -30, l: -(-10 + i * 4) });\n      this['surfacePeer' + i] =\n        this['surfacePeer' + i] || adjust(this.mainBkg, { h: 30, s: -30, l: -(-7 + i * 4) });\n    }\n\n    // Setup the label color for the set\n    this.scaleLabelColor = this.scaleLabelColor || (this.darkMode ? 'black' : this.labelTextColor);\n\n    for (let i = 0; i < this.THEME_COLOR_LIMIT; i++) {\n      this['cScaleLabel' + i] = this['cScaleLabel' + i] || this.scaleLabelColor;\n    }\n\n    /* Pie diagram */\n    for (let i = 0; i < this.THEME_COLOR_LIMIT; i++) {\n      this['pie' + i] = this['cScale' + i];\n    }\n    this.pieTitleTextSize = this.pieTitleTextSize || '25px';\n    this.pieTitleTextColor = this.pieTitleTextColor || this.taskTextDarkColor;\n    this.pieSectionTextSize = this.pieSectionTextSize || '17px';\n    this.pieSectionTextColor = this.pieSectionTextColor || this.textColor;\n    this.pieLegendTextSize = this.pieLegendTextSize || '17px';\n    this.pieLegendTextColor = this.pieLegendTextColor || this.taskTextDarkColor;\n    this.pieStrokeColor = this.pieStrokeColor || 'black';\n    this.pieStrokeWidth = this.pieStrokeWidth || '2px';\n    this.pieOuterStrokeWidth = this.pieOuterStrokeWidth || '2px';\n    this.pieOuterStrokeColor = this.pieOuterStrokeColor || 'black';\n    this.pieOpacity = this.pieOpacity || '0.7';\n\n    /* quadrant-graph */\n    this.quadrant1Fill = this.quadrant1Fill || this.primaryColor;\n    this.quadrant2Fill = this.quadrant2Fill || adjust(this.primaryColor, { r: 5, g: 5, b: 5 });\n    this.quadrant3Fill = this.quadrant3Fill || adjust(this.primaryColor, { r: 10, g: 10, b: 10 });\n    this.quadrant4Fill = this.quadrant4Fill || adjust(this.primaryColor, { r: 15, g: 15, b: 15 });\n    this.quadrant1TextFill = this.quadrant1TextFill || this.primaryTextColor;\n    this.quadrant2TextFill =\n      this.quadrant2TextFill || adjust(this.primaryTextColor, { r: -5, g: -5, b: -5 });\n    this.quadrant3TextFill =\n      this.quadrant3TextFill || adjust(this.primaryTextColor, { r: -10, g: -10, b: -10 });\n    this.quadrant4TextFill =\n      this.quadrant4TextFill || adjust(this.primaryTextColor, { r: -15, g: -15, b: -15 });\n    this.quadrantPointFill =\n      this.quadrantPointFill || isDark(this.quadrant1Fill)\n        ? lighten(this.quadrant1Fill)\n        : darken(this.quadrant1Fill);\n    this.quadrantPointTextFill = this.quadrantPointTextFill || this.primaryTextColor;\n    this.quadrantXAxisTextFill = this.quadrantXAxisTextFill || this.primaryTextColor;\n    this.quadrantYAxisTextFill = this.quadrantYAxisTextFill || this.primaryTextColor;\n    this.quadrantInternalBorderStrokeFill =\n      this.quadrantInternalBorderStrokeFill || this.primaryBorderColor;\n    this.quadrantExternalBorderStrokeFill =\n      this.quadrantExternalBorderStrokeFill || this.primaryBorderColor;\n    this.quadrantTitleFill = this.quadrantTitleFill || this.primaryTextColor;\n\n    /* xychart */\n    this.xyChart = {\n      backgroundColor: this.xyChart?.backgroundColor || this.background,\n      titleColor: this.xyChart?.titleColor || this.primaryTextColor,\n      xAxisTitleColor: this.xyChart?.xAxisTitleColor || this.primaryTextColor,\n      xAxisLabelColor: this.xyChart?.xAxisLabelColor || this.primaryTextColor,\n      xAxisTickColor: this.xyChart?.xAxisTickColor || this.primaryTextColor,\n      xAxisLineColor: this.xyChart?.xAxisLineColor || this.primaryTextColor,\n      yAxisTitleColor: this.xyChart?.yAxisTitleColor || this.primaryTextColor,\n      yAxisLabelColor: this.xyChart?.yAxisLabelColor || this.primaryTextColor,\n      yAxisTickColor: this.xyChart?.yAxisTickColor || this.primaryTextColor,\n      yAxisLineColor: this.xyChart?.yAxisLineColor || this.primaryTextColor,\n      plotColorPalette:\n        this.xyChart?.plotColorPalette ||\n        '#3498db,#2ecc71,#e74c3c,#f1c40f,#bdc3c7,#ffffff,#34495e,#9b59b6,#1abc9c,#e67e22',\n    };\n\n    this.packet = {\n      startByteColor: this.primaryTextColor,\n      endByteColor: this.primaryTextColor,\n      labelColor: this.primaryTextColor,\n      titleColor: this.primaryTextColor,\n      blockStrokeColor: this.primaryTextColor,\n      blockFillColor: this.background,\n    };\n\n    /* radar */\n    this.radar = {\n      axisColor: this.radar?.axisColor || this.lineColor,\n      axisStrokeWidth: this.radar?.axisStrokeWidth || 2,\n      axisLabelFontSize: this.radar?.axisLabelFontSize || 12,\n      curveOpacity: this.radar?.curveOpacity || 0.5,\n      curveStrokeWidth: this.radar?.curveStrokeWidth || 2,\n      graticuleColor: this.radar?.graticuleColor || '#DEDEDE',\n      graticuleStrokeWidth: this.radar?.graticuleStrokeWidth || 1,\n      graticuleOpacity: this.radar?.graticuleOpacity || 0.3,\n      legendBoxSize: this.radar?.legendBoxSize || 12,\n      legendFontSize: this.radar?.legendFontSize || 12,\n    };\n\n    /* class */\n    this.classText = this.primaryTextColor;\n\n    /* requirement-diagram */\n    this.requirementBackground = this.requirementBackground || this.primaryColor;\n    this.requirementBorderColor = this.requirementBorderColor || this.primaryBorderColor;\n    this.requirementBorderSize = this.requirementBorderSize || '1';\n    this.requirementTextColor = this.requirementTextColor || this.primaryTextColor;\n    this.relationColor = this.relationColor || this.lineColor;\n    this.relationLabelBackground =\n      this.relationLabelBackground ||\n      (this.darkMode ? darken(this.secondaryColor, 30) : this.secondaryColor);\n    this.relationLabelColor = this.relationLabelColor || this.actorTextColor;\n\n    /* git */\n    this.git0 = lighten(this.secondaryColor, 20);\n    this.git1 = lighten(this.pie2 || this.secondaryColor, 20);\n    this.git2 = lighten(this.pie3 || this.tertiaryColor, 20);\n    this.git3 = lighten(this.pie4 || adjust(this.primaryColor, { h: -30 }), 20);\n    this.git4 = lighten(this.pie5 || adjust(this.primaryColor, { h: -60 }), 20);\n    this.git5 = lighten(this.pie6 || adjust(this.primaryColor, { h: -90 }), 10);\n    this.git6 = lighten(this.pie7 || adjust(this.primaryColor, { h: +60 }), 10);\n    this.git7 = lighten(this.pie8 || adjust(this.primaryColor, { h: +120 }), 20);\n    this.gitInv0 = this.gitInv0 || invert(this.git0);\n    this.gitInv1 = this.gitInv1 || invert(this.git1);\n    this.gitInv2 = this.gitInv2 || invert(this.git2);\n    this.gitInv3 = this.gitInv3 || invert(this.git3);\n    this.gitInv4 = this.gitInv4 || invert(this.git4);\n    this.gitInv5 = this.gitInv5 || invert(this.git5);\n    this.gitInv6 = this.gitInv6 || invert(this.git6);\n    this.gitInv7 = this.gitInv7 || invert(this.git7);\n    this.gitBranchLabel0 = this.gitBranchLabel0 || invert(this.labelTextColor);\n    this.gitBranchLabel1 = this.gitBranchLabel1 || this.labelTextColor;\n    this.gitBranchLabel2 = this.gitBranchLabel2 || this.labelTextColor;\n    this.gitBranchLabel3 = this.gitBranchLabel3 || invert(this.labelTextColor);\n    this.gitBranchLabel4 = this.gitBranchLabel4 || this.labelTextColor;\n    this.gitBranchLabel5 = this.gitBranchLabel5 || this.labelTextColor;\n    this.gitBranchLabel6 = this.gitBranchLabel6 || this.labelTextColor;\n    this.gitBranchLabel7 = this.gitBranchLabel7 || this.labelTextColor;\n\n    this.tagLabelColor = this.tagLabelColor || this.primaryTextColor;\n    this.tagLabelBackground = this.tagLabelBackground || this.primaryColor;\n    this.tagLabelBorder = this.tagBorder || this.primaryBorderColor;\n    this.tagLabelFontSize = this.tagLabelFontSize || '10px';\n    this.commitLabelColor = this.commitLabelColor || this.secondaryTextColor;\n    this.commitLabelBackground = this.commitLabelBackground || this.secondaryColor;\n    this.commitLabelFontSize = this.commitLabelFontSize || '10px';\n\n    /* -------------------------------------------------- */\n    /* EntityRelationship diagrams                        */\n\n    this.attributeBackgroundColorOdd =\n      this.attributeBackgroundColorOdd || lighten(this.background, 12);\n    this.attributeBackgroundColorEven =\n      this.attributeBackgroundColorEven || lighten(this.background, 2);\n    /* -------------------------------------------------- */\n\n    this.nodeBorder = this.nodeBorder || '#999';\n  }\n  calculate(overrides) {\n    if (typeof overrides !== 'object') {\n      // Calculate colors form base colors\n      this.updateColors();\n      return;\n    }\n\n    const keys = Object.keys(overrides);\n\n    // Copy values from overrides, this is mainly for base colors\n    keys.forEach((k) => {\n      this[k] = overrides[k];\n    });\n\n    // Calculate colors form base colors\n    this.updateColors();\n    // Copy values from overrides again in case of an override of derived value\n    keys.forEach((k) => {\n      this[k] = overrides[k];\n    });\n  }\n}\n\nexport const getThemeVariables = (userOverrides) => {\n  const theme = new Theme();\n  theme.calculate(userOverrides);\n  return theme;\n};\n", "import { invert, lighten, rgba, adjust, darken, isDark } from 'khroma';\nimport { mkBorder } from './theme-helpers.js';\nimport {\n  oldAttributeBackgroundColorEven,\n  oldAttributeBackgroundColorOdd,\n} from './erDiagram-oldHardcodedValues.js';\n\nclass Theme {\n  constructor() {\n    /* Base variables */\n    this.background = '#f4f4f4';\n    this.primaryColor = '#ECECFF';\n\n    this.secondaryColor = adjust(this.primaryColor, { h: 120 });\n    this.secondaryColor = '#ffffde';\n    this.tertiaryColor = adjust(this.primaryColor, { h: -160 });\n    this.primaryBorderColor = mkBorder(this.primaryColor, this.darkMode);\n    this.secondaryBorderColor = mkBorder(this.secondaryColor, this.darkMode);\n    this.tertiaryBorderColor = mkBorder(this.tertiaryColor, this.darkMode);\n    // this.noteBorderColor = mkBorder(this.noteBkgColor, this.darkMode);\n\n    this.primaryTextColor = invert(this.primaryColor);\n    this.secondaryTextColor = invert(this.secondaryColor);\n    this.tertiaryTextColor = invert(this.tertiaryColor);\n    this.lineColor = invert(this.background);\n    this.textColor = invert(this.background);\n\n    this.background = 'white';\n    this.mainBkg = '#ECECFF';\n    this.secondBkg = '#ffffde';\n    this.lineColor = '#333333';\n    this.border1 = '#9370DB';\n    this.border2 = '#aaaa33';\n    this.arrowheadColor = '#333333';\n    this.fontFamily = '\"trebuchet ms\", verdana, arial, sans-serif';\n    this.fontSize = '16px';\n    this.labelBackground = 'rgba(232,232,232, 0.8)';\n    this.textColor = '#333';\n    this.THEME_COLOR_LIMIT = 12;\n\n    /* Flowchart variables */\n\n    this.nodeBkg = 'calculated';\n    this.nodeBorder = 'calculated';\n    this.clusterBkg = 'calculated';\n    this.clusterBorder = 'calculated';\n    this.defaultLinkColor = 'calculated';\n    this.titleColor = 'calculated';\n    this.edgeLabelBackground = 'calculated';\n\n    /* Sequence Diagram variables */\n\n    this.actorBorder = 'calculated';\n    this.actorBkg = 'calculated';\n    this.actorTextColor = 'black';\n    this.actorLineColor = 'calculated';\n    this.signalColor = 'calculated';\n    this.signalTextColor = 'calculated';\n    this.labelBoxBkgColor = 'calculated';\n    this.labelBoxBorderColor = 'calculated';\n    this.labelTextColor = 'calculated';\n    this.loopTextColor = 'calculated';\n    this.noteBorderColor = 'calculated';\n    this.noteBkgColor = '#fff5ad';\n    this.noteTextColor = 'calculated';\n    this.activationBorderColor = '#666';\n    this.activationBkgColor = '#f4f4f4';\n    this.sequenceNumberColor = 'white';\n\n    /* Gantt chart variables */\n\n    this.sectionBkgColor = 'calculated';\n    this.altSectionBkgColor = 'calculated';\n    this.sectionBkgColor2 = 'calculated';\n    this.excludeBkgColor = '#eeeeee';\n    this.taskBorderColor = 'calculated';\n    this.taskBkgColor = 'calculated';\n    this.taskTextLightColor = 'calculated';\n    this.taskTextColor = this.taskTextLightColor;\n    this.taskTextDarkColor = 'calculated';\n    this.taskTextOutsideColor = this.taskTextDarkColor;\n    this.taskTextClickableColor = 'calculated';\n    this.activeTaskBorderColor = 'calculated';\n    this.activeTaskBkgColor = 'calculated';\n    this.gridColor = 'calculated';\n    this.doneTaskBkgColor = 'calculated';\n    this.doneTaskBorderColor = 'calculated';\n    this.critBorderColor = 'calculated';\n    this.critBkgColor = 'calculated';\n    this.todayLineColor = 'calculated';\n\n    this.sectionBkgColor = rgba(102, 102, 255, 0.49);\n    this.altSectionBkgColor = 'white';\n    this.sectionBkgColor2 = '#fff400';\n    this.taskBorderColor = '#534fbc';\n    this.taskBkgColor = '#8a90dd';\n    this.taskTextLightColor = 'white';\n    this.taskTextColor = 'calculated';\n    this.taskTextDarkColor = 'black';\n    this.taskTextOutsideColor = 'calculated';\n    this.taskTextClickableColor = '#003163';\n    this.activeTaskBorderColor = '#534fbc';\n    this.activeTaskBkgColor = '#bfc7ff';\n    this.gridColor = 'lightgrey';\n    this.doneTaskBkgColor = 'lightgrey';\n    this.doneTaskBorderColor = 'grey';\n    this.critBorderColor = '#ff8888';\n    this.critBkgColor = 'red';\n    this.todayLineColor = 'red';\n\n    /* C4 Context Diagram variables */\n    this.personBorder = this.primaryBorderColor;\n    this.personBkg = this.mainBkg;\n\n    /* Architecture Diagram variables */\n    this.archEdgeColor = 'calculated';\n    this.archEdgeArrowColor = 'calculated';\n    this.archEdgeWidth = '3';\n    this.archGroupBorderColor = this.primaryBorderColor;\n    this.archGroupBorderWidth = '2px';\n\n    /* Entity Relationship variables */\n    this.rowOdd = 'calculated';\n    this.rowEven = 'calculated';\n\n    /* state colors */\n    this.labelColor = 'black';\n    this.errorBkgColor = '#552222';\n    this.errorTextColor = '#552222';\n    this.updateColors();\n  }\n  updateColors() {\n    /* Color Scale */\n    /* Each color-set will have a background, a foreground and a border color */\n\n    this.cScale0 = this.cScale0 || this.primaryColor;\n    this.cScale1 = this.cScale1 || this.secondaryColor;\n    this.cScale2 = this.cScale2 || this.tertiaryColor;\n    this.cScale3 = this.cScale3 || adjust(this.primaryColor, { h: 30 });\n    this.cScale4 = this.cScale4 || adjust(this.primaryColor, { h: 60 });\n    this.cScale5 = this.cScale5 || adjust(this.primaryColor, { h: 90 });\n    this.cScale6 = this.cScale6 || adjust(this.primaryColor, { h: 120 });\n    this.cScale7 = this.cScale7 || adjust(this.primaryColor, { h: 150 });\n    this.cScale8 = this.cScale8 || adjust(this.primaryColor, { h: 210 });\n    this.cScale9 = this.cScale9 || adjust(this.primaryColor, { h: 270 });\n    this.cScale10 = this.cScale10 || adjust(this.primaryColor, { h: 300 });\n    this.cScale11 = this.cScale11 || adjust(this.primaryColor, { h: 330 });\n    this['cScalePeer' + 1] = this['cScalePeer' + 1] || darken(this.secondaryColor, 45);\n    this['cScalePeer' + 2] = this['cScalePeer' + 2] || darken(this.tertiaryColor, 40);\n    for (let i = 0; i < this.THEME_COLOR_LIMIT; i++) {\n      // Setup the peer color for the set, useful for borders\n      this['cScale' + i] = darken(this['cScale' + i], 10);\n      this['cScalePeer' + i] = this['cScalePeer' + i] || darken(this['cScale' + i], 25);\n    }\n    // Setup the inverted color for the set\n    for (let i = 0; i < this.THEME_COLOR_LIMIT; i++) {\n      this['cScaleInv' + i] = this['cScaleInv' + i] || adjust(this['cScale' + i], { h: 180 });\n    }\n\n    for (let i = 0; i < 5; i++) {\n      this['surface' + i] = this['surface' + i] || adjust(this.mainBkg, { h: 30, l: -(5 + i * 5) });\n      this['surfacePeer' + i] =\n        this['surfacePeer' + i] || adjust(this.mainBkg, { h: 30, l: -(7 + i * 5) });\n    }\n    // Setup the label color for the set\n    this.scaleLabelColor =\n      this.scaleLabelColor !== 'calculated' && this.scaleLabelColor\n        ? this.scaleLabelColor\n        : this.labelTextColor;\n\n    if (this.labelTextColor !== 'calculated') {\n      this.cScaleLabel0 = this.cScaleLabel0 || invert(this.labelTextColor);\n      this.cScaleLabel3 = this.cScaleLabel3 || invert(this.labelTextColor);\n      for (let i = 0; i < this.THEME_COLOR_LIMIT; i++) {\n        this['cScaleLabel' + i] = this['cScaleLabel' + i] || this.labelTextColor;\n      }\n    }\n\n    /* Flowchart variables */\n    this.nodeBkg = this.mainBkg;\n    this.nodeBorder = this.border1; // border 1\n    this.clusterBkg = this.secondBkg;\n    this.clusterBorder = this.border2;\n    this.defaultLinkColor = this.lineColor;\n    this.titleColor = this.textColor;\n    this.edgeLabelBackground = this.labelBackground;\n\n    /* Sequence Diagram variables */\n\n    // this.actorBorder = lighten(this.border1, 0.5);\n    this.actorBorder = lighten(this.border1, 23);\n    this.actorBkg = this.mainBkg;\n    this.labelBoxBkgColor = this.actorBkg;\n    this.signalColor = this.textColor;\n    this.signalTextColor = this.textColor;\n    this.labelBoxBorderColor = this.actorBorder;\n    this.labelTextColor = this.actorTextColor;\n    this.loopTextColor = this.actorTextColor;\n    this.noteBorderColor = this.border2;\n    this.noteTextColor = this.actorTextColor;\n    this.actorLineColor = this.actorBorder;\n\n    /* Gantt chart variables */\n\n    this.taskTextColor = this.taskTextLightColor;\n    this.taskTextOutsideColor = this.taskTextDarkColor;\n\n    /* Architecture Diagram variables */\n    this.archEdgeColor = this.lineColor;\n    this.archEdgeArrowColor = this.lineColor;\n\n    /* Entity Relationship variables */\n    this.rowOdd = this.rowOdd || lighten(this.primaryColor, 75) || '#ffffff';\n    this.rowEven = this.rowEven || lighten(this.primaryColor, 1);\n    /* state colors */\n    this.transitionColor = this.transitionColor || this.lineColor;\n    this.transitionLabelColor = this.transitionLabelColor || this.textColor;\n    this.stateLabelColor = this.stateLabelColor || this.stateBkg || this.primaryTextColor;\n\n    this.stateBkg = this.stateBkg || this.mainBkg;\n    this.labelBackgroundColor = this.labelBackgroundColor || this.stateBkg;\n    this.compositeBackground = this.compositeBackground || this.background || this.tertiaryColor;\n    this.altBackground = this.altBackground || '#f0f0f0';\n    this.compositeTitleBackground = this.compositeTitleBackground || this.mainBkg;\n    this.compositeBorder = this.compositeBorder || this.nodeBorder;\n    this.innerEndBackground = this.nodeBorder;\n    this.specialStateColor = this.lineColor;\n\n    this.errorBkgColor = this.errorBkgColor || this.tertiaryColor;\n    this.errorTextColor = this.errorTextColor || this.tertiaryTextColor;\n    this.transitionColor = this.transitionColor || this.lineColor;\n    /* class */\n    this.classText = this.primaryTextColor;\n    /* journey */\n    this.fillType0 = this.primaryColor;\n    this.fillType1 = this.secondaryColor;\n    this.fillType2 = adjust(this.primaryColor, { h: 64 });\n    this.fillType3 = adjust(this.secondaryColor, { h: 64 });\n    this.fillType4 = adjust(this.primaryColor, { h: -64 });\n    this.fillType5 = adjust(this.secondaryColor, { h: -64 });\n    this.fillType6 = adjust(this.primaryColor, { h: 128 });\n    this.fillType7 = adjust(this.secondaryColor, { h: 128 });\n\n    /* pie */\n    this.pie1 = this.pie1 || this.primaryColor;\n    this.pie2 = this.pie2 || this.secondaryColor;\n    this.pie3 = this.pie3 || adjust(this.tertiaryColor, { l: -40 });\n    this.pie4 = this.pie4 || adjust(this.primaryColor, { l: -10 });\n    this.pie5 = this.pie5 || adjust(this.secondaryColor, { l: -30 });\n    this.pie6 = this.pie6 || adjust(this.tertiaryColor, { l: -20 });\n    this.pie7 = this.pie7 || adjust(this.primaryColor, { h: +60, l: -20 });\n    this.pie8 = this.pie8 || adjust(this.primaryColor, { h: -60, l: -40 });\n    this.pie9 = this.pie9 || adjust(this.primaryColor, { h: 120, l: -40 });\n    this.pie10 = this.pie10 || adjust(this.primaryColor, { h: +60, l: -40 });\n    this.pie11 = this.pie11 || adjust(this.primaryColor, { h: -90, l: -40 });\n    this.pie12 = this.pie12 || adjust(this.primaryColor, { h: 120, l: -30 });\n    this.pieTitleTextSize = this.pieTitleTextSize || '25px';\n    this.pieTitleTextColor = this.pieTitleTextColor || this.taskTextDarkColor;\n    this.pieSectionTextSize = this.pieSectionTextSize || '17px';\n    this.pieSectionTextColor = this.pieSectionTextColor || this.textColor;\n    this.pieLegendTextSize = this.pieLegendTextSize || '17px';\n    this.pieLegendTextColor = this.pieLegendTextColor || this.taskTextDarkColor;\n    this.pieStrokeColor = this.pieStrokeColor || 'black';\n    this.pieStrokeWidth = this.pieStrokeWidth || '2px';\n    this.pieOuterStrokeWidth = this.pieOuterStrokeWidth || '2px';\n    this.pieOuterStrokeColor = this.pieOuterStrokeColor || 'black';\n    this.pieOpacity = this.pieOpacity || '0.7';\n\n    /* quadrant-graph */\n    this.quadrant1Fill = this.quadrant1Fill || this.primaryColor;\n    this.quadrant2Fill = this.quadrant2Fill || adjust(this.primaryColor, { r: 5, g: 5, b: 5 });\n    this.quadrant3Fill = this.quadrant3Fill || adjust(this.primaryColor, { r: 10, g: 10, b: 10 });\n    this.quadrant4Fill = this.quadrant4Fill || adjust(this.primaryColor, { r: 15, g: 15, b: 15 });\n    this.quadrant1TextFill = this.quadrant1TextFill || this.primaryTextColor;\n    this.quadrant2TextFill =\n      this.quadrant2TextFill || adjust(this.primaryTextColor, { r: -5, g: -5, b: -5 });\n    this.quadrant3TextFill =\n      this.quadrant3TextFill || adjust(this.primaryTextColor, { r: -10, g: -10, b: -10 });\n    this.quadrant4TextFill =\n      this.quadrant4TextFill || adjust(this.primaryTextColor, { r: -15, g: -15, b: -15 });\n    this.quadrantPointFill =\n      this.quadrantPointFill || isDark(this.quadrant1Fill)\n        ? lighten(this.quadrant1Fill)\n        : darken(this.quadrant1Fill);\n    this.quadrantPointTextFill = this.quadrantPointTextFill || this.primaryTextColor;\n    this.quadrantXAxisTextFill = this.quadrantXAxisTextFill || this.primaryTextColor;\n    this.quadrantYAxisTextFill = this.quadrantYAxisTextFill || this.primaryTextColor;\n    this.quadrantInternalBorderStrokeFill =\n      this.quadrantInternalBorderStrokeFill || this.primaryBorderColor;\n    this.quadrantExternalBorderStrokeFill =\n      this.quadrantExternalBorderStrokeFill || this.primaryBorderColor;\n    this.quadrantTitleFill = this.quadrantTitleFill || this.primaryTextColor;\n\n    /* radar */\n    this.radar = {\n      axisColor: this.radar?.axisColor || this.lineColor,\n      axisStrokeWidth: this.radar?.axisStrokeWidth || 2,\n      axisLabelFontSize: this.radar?.axisLabelFontSize || 12,\n      curveOpacity: this.radar?.curveOpacity || 0.5,\n      curveStrokeWidth: this.radar?.curveStrokeWidth || 2,\n      graticuleColor: this.radar?.graticuleColor || '#DEDEDE',\n      graticuleStrokeWidth: this.radar?.graticuleStrokeWidth || 1,\n      graticuleOpacity: this.radar?.graticuleOpacity || 0.3,\n      legendBoxSize: this.radar?.legendBoxSize || 12,\n      legendFontSize: this.radar?.legendFontSize || 12,\n    };\n\n    /* xychart */\n    this.xyChart = {\n      backgroundColor: this.xyChart?.backgroundColor || this.background,\n      titleColor: this.xyChart?.titleColor || this.primaryTextColor,\n      xAxisTitleColor: this.xyChart?.xAxisTitleColor || this.primaryTextColor,\n      xAxisLabelColor: this.xyChart?.xAxisLabelColor || this.primaryTextColor,\n      xAxisTickColor: this.xyChart?.xAxisTickColor || this.primaryTextColor,\n      xAxisLineColor: this.xyChart?.xAxisLineColor || this.primaryTextColor,\n      yAxisTitleColor: this.xyChart?.yAxisTitleColor || this.primaryTextColor,\n      yAxisLabelColor: this.xyChart?.yAxisLabelColor || this.primaryTextColor,\n      yAxisTickColor: this.xyChart?.yAxisTickColor || this.primaryTextColor,\n      yAxisLineColor: this.xyChart?.yAxisLineColor || this.primaryTextColor,\n      plotColorPalette:\n        this.xyChart?.plotColorPalette ||\n        '#ECECFF,#8493A6,#FFC3A0,#DCDDE1,#B8E994,#D1A36F,#C3CDE6,#FFB6C1,#496078,#F8F3E3',\n    };\n\n    /* requirement-diagram */\n    this.requirementBackground = this.requirementBackground || this.primaryColor;\n    this.requirementBorderColor = this.requirementBorderColor || this.primaryBorderColor;\n    this.requirementBorderSize = this.requirementBorderSize || '1';\n    this.requirementTextColor = this.requirementTextColor || this.primaryTextColor;\n    this.relationColor = this.relationColor || this.lineColor;\n    this.relationLabelBackground = this.relationLabelBackground || this.labelBackground;\n    this.relationLabelColor = this.relationLabelColor || this.actorTextColor;\n\n    /* git */\n    this.git0 = this.git0 || this.primaryColor;\n    this.git1 = this.git1 || this.secondaryColor;\n    this.git2 = this.git2 || this.tertiaryColor;\n    this.git3 = this.git3 || adjust(this.primaryColor, { h: -30 });\n    this.git4 = this.git4 || adjust(this.primaryColor, { h: -60 });\n    this.git5 = this.git5 || adjust(this.primaryColor, { h: -90 });\n    this.git6 = this.git6 || adjust(this.primaryColor, { h: +60 });\n    this.git7 = this.git7 || adjust(this.primaryColor, { h: +120 });\n    if (this.darkMode) {\n      this.git0 = lighten(this.git0, 25);\n      this.git1 = lighten(this.git1, 25);\n      this.git2 = lighten(this.git2, 25);\n      this.git3 = lighten(this.git3, 25);\n      this.git4 = lighten(this.git4, 25);\n      this.git5 = lighten(this.git5, 25);\n      this.git6 = lighten(this.git6, 25);\n      this.git7 = lighten(this.git7, 25);\n    } else {\n      this.git0 = darken(this.git0, 25);\n      this.git1 = darken(this.git1, 25);\n      this.git2 = darken(this.git2, 25);\n      this.git3 = darken(this.git3, 25);\n      this.git4 = darken(this.git4, 25);\n      this.git5 = darken(this.git5, 25);\n      this.git6 = darken(this.git6, 25);\n      this.git7 = darken(this.git7, 25);\n    }\n    this.gitInv0 = this.gitInv0 || darken(invert(this.git0), 25);\n    this.gitInv1 = this.gitInv1 || invert(this.git1);\n    this.gitInv2 = this.gitInv2 || invert(this.git2);\n    this.gitInv3 = this.gitInv3 || invert(this.git3);\n    this.gitInv4 = this.gitInv4 || invert(this.git4);\n    this.gitInv5 = this.gitInv5 || invert(this.git5);\n    this.gitInv6 = this.gitInv6 || invert(this.git6);\n    this.gitInv7 = this.gitInv7 || invert(this.git7);\n    this.gitBranchLabel0 = this.gitBranchLabel0 || invert(this.labelTextColor);\n    this.gitBranchLabel1 = this.gitBranchLabel1 || this.labelTextColor;\n    this.gitBranchLabel2 = this.gitBranchLabel2 || this.labelTextColor;\n    this.gitBranchLabel3 = this.gitBranchLabel3 || invert(this.labelTextColor);\n    this.gitBranchLabel4 = this.gitBranchLabel4 || this.labelTextColor;\n    this.gitBranchLabel5 = this.gitBranchLabel5 || this.labelTextColor;\n    this.gitBranchLabel6 = this.gitBranchLabel6 || this.labelTextColor;\n    this.gitBranchLabel7 = this.gitBranchLabel7 || this.labelTextColor;\n\n    this.tagLabelColor = this.tagLabelColor || this.primaryTextColor;\n    this.tagLabelBackground = this.tagLabelBackground || this.primaryColor;\n    this.tagLabelBorder = this.tagBorder || this.primaryBorderColor;\n    this.tagLabelFontSize = this.tagLabelFontSize || '10px';\n    this.commitLabelColor = this.commitLabelColor || this.secondaryTextColor;\n    this.commitLabelBackground = this.commitLabelBackground || this.secondaryColor;\n    this.commitLabelFontSize = this.commitLabelFontSize || '10px';\n\n    /* -------------------------------------------------- */\n    /* EntityRelationship diagrams                        */\n\n    this.attributeBackgroundColorOdd =\n      this.attributeBackgroundColorOdd || oldAttributeBackgroundColorOdd;\n    this.attributeBackgroundColorEven =\n      this.attributeBackgroundColorEven || oldAttributeBackgroundColorEven;\n    /* -------------------------------------------------- */\n  }\n  calculate(overrides) {\n    // for all keys in this object, if it is 'calculated' then set it to undefined\n    Object.keys(this).forEach((k) => {\n      if (this[k] === 'calculated') {\n        this[k] = undefined;\n      }\n    });\n\n    if (typeof overrides !== 'object') {\n      // Calculate colors form base colors\n      this.updateColors();\n      return;\n    }\n\n    const keys = Object.keys(overrides);\n\n    // Copy values from overrides, this is mainly for base colors\n    keys.forEach((k) => {\n      this[k] = overrides[k];\n    });\n\n    // Calculate colors form base colors\n    this.updateColors();\n    // Copy values from overrides again in case of an override of derived value\n    keys.forEach((k) => {\n      this[k] = overrides[k];\n    });\n  }\n}\n\nexport const getThemeVariables = (userOverrides) => {\n  const theme = new Theme();\n  theme.calculate(userOverrides);\n  return theme;\n};\n", "import { adjust, darken, invert, isDark, lighten } from 'khroma';\nimport {\n  oldAttributeBackgroundColorEven,\n  oldAttributeBackgroundColorOdd,\n} from './erDiagram-oldHardcodedValues.js';\nimport { mkBorder } from './theme-helpers.js';\n\nclass Theme {\n  constructor() {\n    /* Base vales */\n    this.background = '#f4f4f4';\n    this.primaryColor = '#cde498';\n    this.secondaryColor = '#cdffb2';\n    this.background = 'white';\n    this.mainBkg = '#cde498';\n    this.secondBkg = '#cdffb2';\n    this.lineColor = 'green';\n    this.border1 = '#13540c';\n    this.border2 = '#6eaa49';\n    this.arrowheadColor = 'green';\n    this.fontFamily = '\"trebuchet ms\", verdana, arial, sans-serif';\n    this.fontSize = '16px';\n\n    this.tertiaryColor = lighten('#cde498', 10);\n    this.primaryBorderColor = mkBorder(this.primaryColor, this.darkMode);\n    this.secondaryBorderColor = mkBorder(this.secondaryColor, this.darkMode);\n    this.tertiaryBorderColor = mkBorder(this.tertiaryColor, this.darkMode);\n    this.primaryTextColor = invert(this.primaryColor);\n    this.secondaryTextColor = invert(this.secondaryColor);\n    this.tertiaryTextColor = invert(this.primaryColor);\n    this.lineColor = invert(this.background);\n    this.textColor = invert(this.background);\n    this.THEME_COLOR_LIMIT = 12;\n\n    /* Flowchart variables */\n    this.nodeBkg = 'calculated';\n    this.nodeBorder = 'calculated';\n    this.clusterBkg = 'calculated';\n    this.clusterBorder = 'calculated';\n    this.defaultLinkColor = 'calculated';\n    this.titleColor = '#333';\n    this.edgeLabelBackground = '#e8e8e8';\n\n    /* Sequence Diagram variables */\n\n    this.actorBorder = 'calculated';\n    this.actorBkg = 'calculated';\n    this.actorTextColor = 'black';\n    this.actorLineColor = 'calculated';\n    this.signalColor = '#333';\n    this.signalTextColor = '#333';\n    this.labelBoxBkgColor = 'calculated';\n    this.labelBoxBorderColor = '#326932';\n    this.labelTextColor = 'calculated';\n    this.loopTextColor = 'calculated';\n    this.noteBorderColor = 'calculated';\n    this.noteBkgColor = '#fff5ad';\n    this.noteTextColor = 'calculated';\n    this.activationBorderColor = '#666';\n    this.activationBkgColor = '#f4f4f4';\n    this.sequenceNumberColor = 'white';\n\n    /* Gantt chart variables */\n\n    this.sectionBkgColor = '#6eaa49';\n    this.altSectionBkgColor = 'white';\n    this.sectionBkgColor2 = '#6eaa49';\n    this.excludeBkgColor = '#eeeeee';\n    this.taskBorderColor = 'calculated';\n    this.taskBkgColor = '#487e3a';\n    this.taskTextLightColor = 'white';\n    this.taskTextColor = 'calculated';\n    this.taskTextDarkColor = 'black';\n    this.taskTextOutsideColor = 'calculated';\n    this.taskTextClickableColor = '#003163';\n    this.activeTaskBorderColor = 'calculated';\n    this.activeTaskBkgColor = 'calculated';\n    this.gridColor = 'lightgrey';\n    this.doneTaskBkgColor = 'lightgrey';\n    this.doneTaskBorderColor = 'grey';\n    this.critBorderColor = '#ff8888';\n    this.critBkgColor = 'red';\n    this.todayLineColor = 'red';\n\n    /* C4 Context Diagram variables */\n    this.personBorder = this.primaryBorderColor;\n    this.personBkg = this.mainBkg;\n\n    /* Architecture Diagram variables */\n    this.archEdgeColor = 'calculated';\n    this.archEdgeArrowColor = 'calculated';\n    this.archEdgeWidth = '3';\n    this.archGroupBorderColor = this.primaryBorderColor;\n    this.archGroupBorderWidth = '2px';\n\n    /* state colors */\n    this.labelColor = 'black';\n\n    this.errorBkgColor = '#552222';\n    this.errorTextColor = '#552222';\n  }\n  updateColors() {\n    /* Sequence Diagram variables */\n    this.actorBorder = darken(this.mainBkg, 20);\n    this.actorBkg = this.mainBkg;\n    this.labelBoxBkgColor = this.actorBkg;\n    this.labelTextColor = this.actorTextColor;\n    this.loopTextColor = this.actorTextColor;\n    this.noteBorderColor = this.border2;\n    this.noteTextColor = this.actorTextColor;\n    this.actorLineColor = this.actorBorder;\n\n    /* Each color-set will have a background, a foreground and a border color */\n    this.cScale0 = this.cScale0 || this.primaryColor;\n    this.cScale1 = this.cScale1 || this.secondaryColor;\n    this.cScale2 = this.cScale2 || this.tertiaryColor;\n    this.cScale3 = this.cScale3 || adjust(this.primaryColor, { h: 30 });\n    this.cScale4 = this.cScale4 || adjust(this.primaryColor, { h: 60 });\n    this.cScale5 = this.cScale5 || adjust(this.primaryColor, { h: 90 });\n    this.cScale6 = this.cScale6 || adjust(this.primaryColor, { h: 120 });\n    this.cScale7 = this.cScale7 || adjust(this.primaryColor, { h: 150 });\n    this.cScale8 = this.cScale8 || adjust(this.primaryColor, { h: 210 });\n    this.cScale9 = this.cScale9 || adjust(this.primaryColor, { h: 270 });\n    this.cScale10 = this.cScale10 || adjust(this.primaryColor, { h: 300 });\n    this.cScale11 = this.cScale11 || adjust(this.primaryColor, { h: 330 });\n    this['cScalePeer' + 1] = this['cScalePeer' + 1] || darken(this.secondaryColor, 45);\n    this['cScalePeer' + 2] = this['cScalePeer' + 2] || darken(this.tertiaryColor, 40);\n    for (let i = 0; i < this.THEME_COLOR_LIMIT; i++) {\n      // Setup the peer color for the set, useful for borders\n      this['cScale' + i] = darken(this['cScale' + i], 10);\n      this['cScalePeer' + i] = this['cScalePeer' + i] || darken(this['cScale' + i], 25);\n    }\n\n    // Setup the inverted color for the set\n    for (let i = 0; i < this.THEME_COLOR_LIMIT; i++) {\n      this['cScaleInv' + i] = this['cScaleInv' + i] || adjust(this['cScale' + i], { h: 180 });\n    }\n\n    // Setup the label color for the set\n    this.scaleLabelColor =\n      this.scaleLabelColor !== 'calculated' && this.scaleLabelColor\n        ? this.scaleLabelColor\n        : this.labelTextColor;\n\n    for (let i = 0; i < this.THEME_COLOR_LIMIT; i++) {\n      this['cScaleLabel' + i] = this['cScaleLabel' + i] || this.scaleLabelColor;\n    }\n\n    for (let i = 0; i < 5; i++) {\n      this['surface' + i] =\n        this['surface' + i] || adjust(this.mainBkg, { h: 30, s: -30, l: -(5 + i * 5) });\n      this['surfacePeer' + i] =\n        this['surfacePeer' + i] || adjust(this.mainBkg, { h: 30, s: -30, l: -(8 + i * 5) });\n    }\n\n    /* Flowchart variables */\n\n    this.nodeBkg = this.mainBkg;\n    this.nodeBorder = this.border1;\n    this.clusterBkg = this.secondBkg;\n    this.clusterBorder = this.border2;\n    this.defaultLinkColor = this.lineColor;\n\n    /* Gantt chart variables */\n\n    this.taskBorderColor = this.border1;\n    this.taskTextColor = this.taskTextLightColor;\n    this.taskTextOutsideColor = this.taskTextDarkColor;\n    this.activeTaskBorderColor = this.taskBorderColor;\n    this.activeTaskBkgColor = this.mainBkg;\n\n    /* Architecture Diagram variables */\n    this.archEdgeColor = this.lineColor;\n    this.archEdgeArrowColor = this.lineColor;\n\n    /* ER diagram */\n    this.rowOdd = this.rowOdd || lighten(this.mainBkg, 75) || '#ffffff';\n    this.rowEven = this.rowEven || lighten(this.mainBkg, 20);\n\n    /* state colors */\n    this.transitionColor = this.transitionColor || this.lineColor;\n    this.transitionLabelColor = this.transitionLabelColor || this.textColor;\n    this.stateLabelColor = this.stateLabelColor || this.stateBkg || this.primaryTextColor;\n\n    this.stateBkg = this.stateBkg || this.mainBkg;\n    this.labelBackgroundColor = this.labelBackgroundColor || this.stateBkg;\n    this.compositeBackground = this.compositeBackground || this.background || this.tertiaryColor;\n    this.altBackground = this.altBackground || '#f0f0f0';\n    this.compositeTitleBackground = this.compositeTitleBackground || this.mainBkg;\n    this.compositeBorder = this.compositeBorder || this.nodeBorder;\n    this.innerEndBackground = this.primaryBorderColor;\n    this.specialStateColor = this.lineColor;\n\n    this.errorBkgColor = this.errorBkgColor || this.tertiaryColor;\n    this.errorTextColor = this.errorTextColor || this.tertiaryTextColor;\n    this.transitionColor = this.transitionColor || this.lineColor;\n    /* class */\n    this.classText = this.primaryTextColor;\n    /* journey */\n    this.fillType0 = this.primaryColor;\n    this.fillType1 = this.secondaryColor;\n    this.fillType2 = adjust(this.primaryColor, { h: 64 });\n    this.fillType3 = adjust(this.secondaryColor, { h: 64 });\n    this.fillType4 = adjust(this.primaryColor, { h: -64 });\n    this.fillType5 = adjust(this.secondaryColor, { h: -64 });\n    this.fillType6 = adjust(this.primaryColor, { h: 128 });\n    this.fillType7 = adjust(this.secondaryColor, { h: 128 });\n\n    /* pie */\n    this.pie1 = this.pie1 || this.primaryColor;\n    this.pie2 = this.pie2 || this.secondaryColor;\n    this.pie3 = this.pie3 || this.tertiaryColor;\n    this.pie4 = this.pie4 || adjust(this.primaryColor, { l: -30 });\n    this.pie5 = this.pie5 || adjust(this.secondaryColor, { l: -30 });\n    this.pie6 = this.pie6 || adjust(this.tertiaryColor, { h: +40, l: -40 });\n    this.pie7 = this.pie7 || adjust(this.primaryColor, { h: +60, l: -10 });\n    this.pie8 = this.pie8 || adjust(this.primaryColor, { h: -60, l: -10 });\n    this.pie9 = this.pie9 || adjust(this.primaryColor, { h: 120, l: 0 });\n    this.pie10 = this.pie10 || adjust(this.primaryColor, { h: +60, l: -50 });\n    this.pie11 = this.pie11 || adjust(this.primaryColor, { h: -60, l: -50 });\n    this.pie12 = this.pie12 || adjust(this.primaryColor, { h: 120, l: -50 });\n    this.pieTitleTextSize = this.pieTitleTextSize || '25px';\n    this.pieTitleTextColor = this.pieTitleTextColor || this.taskTextDarkColor;\n    this.pieSectionTextSize = this.pieSectionTextSize || '17px';\n    this.pieSectionTextColor = this.pieSectionTextColor || this.textColor;\n    this.pieLegendTextSize = this.pieLegendTextSize || '17px';\n    this.pieLegendTextColor = this.pieLegendTextColor || this.taskTextDarkColor;\n    this.pieStrokeColor = this.pieStrokeColor || 'black';\n    this.pieStrokeWidth = this.pieStrokeWidth || '2px';\n    this.pieOuterStrokeWidth = this.pieOuterStrokeWidth || '2px';\n    this.pieOuterStrokeColor = this.pieOuterStrokeColor || 'black';\n    this.pieOpacity = this.pieOpacity || '0.7';\n\n    /* quadrant-graph */\n    this.quadrant1Fill = this.quadrant1Fill || this.primaryColor;\n    this.quadrant2Fill = this.quadrant2Fill || adjust(this.primaryColor, { r: 5, g: 5, b: 5 });\n    this.quadrant3Fill = this.quadrant3Fill || adjust(this.primaryColor, { r: 10, g: 10, b: 10 });\n    this.quadrant4Fill = this.quadrant4Fill || adjust(this.primaryColor, { r: 15, g: 15, b: 15 });\n    this.quadrant1TextFill = this.quadrant1TextFill || this.primaryTextColor;\n    this.quadrant2TextFill =\n      this.quadrant2TextFill || adjust(this.primaryTextColor, { r: -5, g: -5, b: -5 });\n    this.quadrant3TextFill =\n      this.quadrant3TextFill || adjust(this.primaryTextColor, { r: -10, g: -10, b: -10 });\n    this.quadrant4TextFill =\n      this.quadrant4TextFill || adjust(this.primaryTextColor, { r: -15, g: -15, b: -15 });\n    this.quadrantPointFill =\n      this.quadrantPointFill || isDark(this.quadrant1Fill)\n        ? lighten(this.quadrant1Fill)\n        : darken(this.quadrant1Fill);\n    this.quadrantPointTextFill = this.quadrantPointTextFill || this.primaryTextColor;\n    this.quadrantXAxisTextFill = this.quadrantXAxisTextFill || this.primaryTextColor;\n    this.quadrantYAxisTextFill = this.quadrantYAxisTextFill || this.primaryTextColor;\n    this.quadrantInternalBorderStrokeFill =\n      this.quadrantInternalBorderStrokeFill || this.primaryBorderColor;\n    this.quadrantExternalBorderStrokeFill =\n      this.quadrantExternalBorderStrokeFill || this.primaryBorderColor;\n    this.quadrantTitleFill = this.quadrantTitleFill || this.primaryTextColor;\n\n    this.packet = {\n      startByteColor: this.primaryTextColor,\n      endByteColor: this.primaryTextColor,\n      labelColor: this.primaryTextColor,\n      titleColor: this.primaryTextColor,\n      blockStrokeColor: this.primaryTextColor,\n      blockFillColor: this.mainBkg,\n    };\n\n    /* radar */\n    this.radar = {\n      axisColor: this.radar?.axisColor || this.lineColor,\n      axisStrokeWidth: this.radar?.axisStrokeWidth || 2,\n      axisLabelFontSize: this.radar?.axisLabelFontSize || 12,\n      curveOpacity: this.radar?.curveOpacity || 0.5,\n      curveStrokeWidth: this.radar?.curveStrokeWidth || 2,\n      graticuleColor: this.radar?.graticuleColor || '#DEDEDE',\n      graticuleStrokeWidth: this.radar?.graticuleStrokeWidth || 1,\n      graticuleOpacity: this.radar?.graticuleOpacity || 0.3,\n      legendBoxSize: this.radar?.legendBoxSize || 12,\n      legendFontSize: this.radar?.legendFontSize || 12,\n    };\n\n    /* xychart */\n    this.xyChart = {\n      backgroundColor: this.xyChart?.backgroundColor || this.background,\n      titleColor: this.xyChart?.titleColor || this.primaryTextColor,\n      xAxisTitleColor: this.xyChart?.xAxisTitleColor || this.primaryTextColor,\n      xAxisLabelColor: this.xyChart?.xAxisLabelColor || this.primaryTextColor,\n      xAxisTickColor: this.xyChart?.xAxisTickColor || this.primaryTextColor,\n      xAxisLineColor: this.xyChart?.xAxisLineColor || this.primaryTextColor,\n      yAxisTitleColor: this.xyChart?.yAxisTitleColor || this.primaryTextColor,\n      yAxisLabelColor: this.xyChart?.yAxisLabelColor || this.primaryTextColor,\n      yAxisTickColor: this.xyChart?.yAxisTickColor || this.primaryTextColor,\n      yAxisLineColor: this.xyChart?.yAxisLineColor || this.primaryTextColor,\n      plotColorPalette:\n        this.xyChart?.plotColorPalette ||\n        '#CDE498,#FF6B6B,#A0D2DB,#D7BDE2,#F0F0F0,#FFC3A0,#7FD8BE,#FF9A8B,#FAF3E0,#FFF176',\n    };\n\n    /* requirement-diagram */\n    this.requirementBackground = this.requirementBackground || this.primaryColor;\n    this.requirementBorderColor = this.requirementBorderColor || this.primaryBorderColor;\n    this.requirementBorderSize = this.requirementBorderSize || '1';\n    this.requirementTextColor = this.requirementTextColor || this.primaryTextColor;\n    this.relationColor = this.relationColor || this.lineColor;\n    this.relationLabelBackground = this.relationLabelBackground || this.edgeLabelBackground;\n    this.relationLabelColor = this.relationLabelColor || this.actorTextColor;\n\n    /* git */\n    this.git0 = this.git0 || this.primaryColor;\n    this.git1 = this.git1 || this.secondaryColor;\n    this.git2 = this.git2 || this.tertiaryColor;\n    this.git3 = this.git3 || adjust(this.primaryColor, { h: -30 });\n    this.git4 = this.git4 || adjust(this.primaryColor, { h: -60 });\n    this.git5 = this.git5 || adjust(this.primaryColor, { h: -90 });\n    this.git6 = this.git6 || adjust(this.primaryColor, { h: +60 });\n    this.git7 = this.git7 || adjust(this.primaryColor, { h: +120 });\n    if (this.darkMode) {\n      this.git0 = lighten(this.git0, 25);\n      this.git1 = lighten(this.git1, 25);\n      this.git2 = lighten(this.git2, 25);\n      this.git3 = lighten(this.git3, 25);\n      this.git4 = lighten(this.git4, 25);\n      this.git5 = lighten(this.git5, 25);\n      this.git6 = lighten(this.git6, 25);\n      this.git7 = lighten(this.git7, 25);\n    } else {\n      this.git0 = darken(this.git0, 25);\n      this.git1 = darken(this.git1, 25);\n      this.git2 = darken(this.git2, 25);\n      this.git3 = darken(this.git3, 25);\n      this.git4 = darken(this.git4, 25);\n      this.git5 = darken(this.git5, 25);\n      this.git6 = darken(this.git6, 25);\n      this.git7 = darken(this.git7, 25);\n    }\n    this.gitInv0 = this.gitInv0 || invert(this.git0);\n    this.gitInv1 = this.gitInv1 || invert(this.git1);\n    this.gitInv2 = this.gitInv2 || invert(this.git2);\n    this.gitInv3 = this.gitInv3 || invert(this.git3);\n    this.gitInv4 = this.gitInv4 || invert(this.git4);\n    this.gitInv5 = this.gitInv5 || invert(this.git5);\n    this.gitInv6 = this.gitInv6 || invert(this.git6);\n    this.gitInv7 = this.gitInv7 || invert(this.git7);\n    this.gitBranchLabel0 = this.gitBranchLabel0 || invert(this.labelTextColor);\n    this.gitBranchLabel1 = this.gitBranchLabel1 || this.labelTextColor;\n    this.gitBranchLabel2 = this.gitBranchLabel2 || this.labelTextColor;\n    this.gitBranchLabel3 = this.gitBranchLabel3 || invert(this.labelTextColor);\n    this.gitBranchLabel4 = this.gitBranchLabel4 || this.labelTextColor;\n    this.gitBranchLabel5 = this.gitBranchLabel5 || this.labelTextColor;\n    this.gitBranchLabel6 = this.gitBranchLabel6 || this.labelTextColor;\n    this.gitBranchLabel7 = this.gitBranchLabel7 || this.labelTextColor;\n\n    this.tagLabelColor = this.tagLabelColor || this.primaryTextColor;\n    this.tagLabelBackground = this.tagLabelBackground || this.primaryColor;\n    this.tagLabelBorder = this.tagBorder || this.primaryBorderColor;\n    this.tagLabelFontSize = this.tagLabelFontSize || '10px';\n    this.commitLabelColor = this.commitLabelColor || this.secondaryTextColor;\n    this.commitLabelBackground = this.commitLabelBackground || this.secondaryColor;\n    this.commitLabelFontSize = this.commitLabelFontSize || '10px';\n\n    /* -------------------------------------------------- */\n    /* EntityRelationship diagrams                        */\n\n    this.attributeBackgroundColorOdd =\n      this.attributeBackgroundColorOdd || oldAttributeBackgroundColorOdd;\n    this.attributeBackgroundColorEven =\n      this.attributeBackgroundColorEven || oldAttributeBackgroundColorEven;\n    /* -------------------------------------------------- */\n  }\n  calculate(overrides) {\n    if (typeof overrides !== 'object') {\n      // Calculate colors form base colors\n      this.updateColors();\n      return;\n    }\n\n    const keys = Object.keys(overrides);\n\n    // Copy values from overrides, this is mainly for base colors\n    keys.forEach((k) => {\n      this[k] = overrides[k];\n    });\n\n    // Calculate colors form base colors\n    this.updateColors();\n    // Copy values from overrides again in case of an override of derived value\n    keys.forEach((k) => {\n      this[k] = overrides[k];\n    });\n  }\n}\n\nexport const getThemeVariables = (userOverrides) => {\n  const theme = new Theme();\n  theme.calculate(userOverrides);\n  return theme;\n};\n", "import { invert, darken, lighten, adjust, isDark } from 'khroma';\nimport { mkBorder } from './theme-helpers.js';\nimport {\n  oldAttributeBackgroundColorEven,\n  oldAttributeBackgroundColorOdd,\n} from './erDiagram-oldHardcodedValues.js';\n\n// const Color = require ( 'khroma/dist/color' ).default\n// Color.format.hex.stringify(Color.parse('hsl(210, 66.6666666667%, 95%)')); // => \"#EAF2FB\"\n\nclass Theme {\n  constructor() {\n    this.primaryColor = '#eee';\n    this.contrast = '#707070';\n    this.secondaryColor = lighten(this.contrast, 55);\n    this.background = '#ffffff';\n\n    // this.secondaryColor = adjust(this.primaryColor, { h: 120 });\n    this.tertiaryColor = adjust(this.primaryColor, { h: -160 });\n    this.primaryBorderColor = mkBorder(this.primaryColor, this.darkMode);\n    this.secondaryBorderColor = mkBorder(this.secondaryColor, this.darkMode);\n    this.tertiaryBorderColor = mkBorder(this.tertiaryColor, this.darkMode);\n    // this.noteBorderColor = mkBorder(this.noteBkgColor, this.darkMode);\n\n    this.primaryTextColor = invert(this.primaryColor);\n    this.secondaryTextColor = invert(this.secondaryColor);\n    this.tertiaryTextColor = invert(this.tertiaryColor);\n    this.lineColor = invert(this.background);\n    this.textColor = invert(this.background);\n\n    // this.altBackground = lighten(this.contrast, 55);\n    this.mainBkg = '#eee';\n    this.secondBkg = 'calculated';\n    this.lineColor = '#666';\n    this.border1 = '#999';\n    this.border2 = 'calculated';\n    this.note = '#ffa';\n    this.text = '#333';\n    this.critical = '#d42';\n    this.done = '#bbb';\n    this.arrowheadColor = '#333333';\n    this.fontFamily = '\"trebuchet ms\", verdana, arial, sans-serif';\n    this.fontSize = '16px';\n    this.THEME_COLOR_LIMIT = 12;\n\n    /* Flowchart variables */\n\n    this.nodeBkg = 'calculated';\n    this.nodeBorder = 'calculated';\n    this.clusterBkg = 'calculated';\n    this.clusterBorder = 'calculated';\n    this.defaultLinkColor = 'calculated';\n    this.titleColor = 'calculated';\n    this.edgeLabelBackground = 'white';\n\n    /* Sequence Diagram variables */\n\n    this.actorBorder = 'calculated';\n    this.actorBkg = 'calculated';\n    this.actorTextColor = 'calculated';\n    this.actorLineColor = this.actorBorder;\n    this.signalColor = 'calculated';\n    this.signalTextColor = 'calculated';\n    this.labelBoxBkgColor = 'calculated';\n    this.labelBoxBorderColor = 'calculated';\n    this.labelTextColor = 'calculated';\n    this.loopTextColor = 'calculated';\n    this.noteBorderColor = 'calculated';\n    this.noteBkgColor = 'calculated';\n    this.noteTextColor = 'calculated';\n    this.activationBorderColor = '#666';\n    this.activationBkgColor = '#f4f4f4';\n    this.sequenceNumberColor = 'white';\n\n    /* Gantt chart variables */\n\n    this.sectionBkgColor = 'calculated';\n    this.altSectionBkgColor = 'white';\n    this.sectionBkgColor2 = 'calculated';\n    this.excludeBkgColor = '#eeeeee';\n    this.taskBorderColor = 'calculated';\n    this.taskBkgColor = 'calculated';\n    this.taskTextLightColor = 'white';\n    this.taskTextColor = 'calculated';\n    this.taskTextDarkColor = 'calculated';\n    this.taskTextOutsideColor = 'calculated';\n    this.taskTextClickableColor = '#003163';\n    this.activeTaskBorderColor = 'calculated';\n    this.activeTaskBkgColor = 'calculated';\n    this.gridColor = 'calculated';\n    this.doneTaskBkgColor = 'calculated';\n    this.doneTaskBorderColor = 'calculated';\n    this.critBkgColor = 'calculated';\n    this.critBorderColor = 'calculated';\n    this.todayLineColor = 'calculated';\n\n    /* C4 Context Diagram variables */\n    this.personBorder = this.primaryBorderColor;\n    this.personBkg = this.mainBkg;\n\n    /* Architecture Diagram variables */\n    this.archEdgeColor = 'calculated';\n    this.archEdgeArrowColor = 'calculated';\n    this.archEdgeWidth = '3';\n    this.archGroupBorderColor = this.primaryBorderColor;\n    this.archGroupBorderWidth = '2px';\n\n    /* ER diagram */\n    this.rowOdd = this.rowOdd || lighten(this.mainBkg, 75) || '#ffffff';\n    this.rowEven = this.rowEven || '#f4f4f4';\n\n    /* state colors */\n    this.labelColor = 'black';\n\n    this.errorBkgColor = '#552222';\n    this.errorTextColor = '#552222';\n  }\n  updateColors() {\n    this.secondBkg = lighten(this.contrast, 55);\n    this.border2 = this.contrast;\n\n    /* Sequence Diagram variables */\n\n    this.actorBorder = lighten(this.border1, 23);\n    this.actorBkg = this.mainBkg;\n    this.actorTextColor = this.text;\n    this.actorLineColor = this.actorBorder;\n    this.signalColor = this.text;\n    this.signalTextColor = this.text;\n    this.labelBoxBkgColor = this.actorBkg;\n    this.labelBoxBorderColor = this.actorBorder;\n    this.labelTextColor = this.text;\n    this.loopTextColor = this.text;\n    this.noteBorderColor = '#999';\n    this.noteBkgColor = '#666';\n    this.noteTextColor = '#fff';\n\n    /* Color Scale */\n    /* Each color-set will have a background, a foreground and a border color */\n\n    this.cScale0 = this.cScale0 || '#555';\n    this.cScale1 = this.cScale1 || '#F4F4F4';\n    this.cScale2 = this.cScale2 || '#555';\n    this.cScale3 = this.cScale3 || '#BBB';\n    this.cScale4 = this.cScale4 || '#777';\n    this.cScale5 = this.cScale5 || '#999';\n    this.cScale6 = this.cScale6 || '#DDD';\n    this.cScale7 = this.cScale7 || '#FFF';\n    this.cScale8 = this.cScale8 || '#DDD';\n    this.cScale9 = this.cScale9 || '#BBB';\n    this.cScale10 = this.cScale10 || '#999';\n    this.cScale11 = this.cScale11 || '#777';\n\n    // Setup the inverted color for the set\n    for (let i = 0; i < this.THEME_COLOR_LIMIT; i++) {\n      this['cScaleInv' + i] = this['cScaleInv' + i] || invert(this['cScale' + i]);\n    }\n    // Setup the peer color for the set, useful for borders\n    for (let i = 0; i < this.THEME_COLOR_LIMIT; i++) {\n      if (this.darkMode) {\n        this['cScalePeer' + i] = this['cScalePeer' + i] || lighten(this['cScale' + i], 10);\n      } else {\n        this['cScalePeer' + i] = this['cScalePeer' + i] || darken(this['cScale' + i], 10);\n      }\n    }\n\n    // Setup the label color for the set\n    this.scaleLabelColor = this.scaleLabelColor || (this.darkMode ? 'black' : this.labelTextColor);\n\n    this.cScaleLabel0 = this.cScaleLabel0 || this.cScale1;\n    this.cScaleLabel2 = this.cScaleLabel2 || this.cScale1;\n    for (let i = 0; i < this.THEME_COLOR_LIMIT; i++) {\n      this['cScaleLabel' + i] = this['cScaleLabel' + i] || this.scaleLabelColor;\n    }\n\n    for (let i = 0; i < 5; i++) {\n      this['surface' + i] = this['surface' + i] || adjust(this.mainBkg, { l: -(5 + i * 5) });\n      this['surfacePeer' + i] =\n        this['surfacePeer' + i] || adjust(this.mainBkg, { l: -(8 + i * 5) });\n    }\n\n    /* Flowchart variables */\n\n    this.nodeBkg = this.mainBkg;\n    this.nodeBorder = this.border1;\n    this.clusterBkg = this.secondBkg;\n    this.clusterBorder = this.border2;\n    this.defaultLinkColor = this.lineColor;\n    this.titleColor = this.text;\n\n    /* Gantt chart variables */\n\n    this.sectionBkgColor = lighten(this.contrast, 30);\n    this.sectionBkgColor2 = lighten(this.contrast, 30);\n\n    this.taskBorderColor = darken(this.contrast, 10);\n\n    this.taskBkgColor = this.contrast;\n    this.taskTextColor = this.taskTextLightColor;\n    this.taskTextDarkColor = this.text;\n    this.taskTextOutsideColor = this.taskTextDarkColor;\n    this.activeTaskBorderColor = this.taskBorderColor;\n    this.activeTaskBkgColor = this.mainBkg;\n    this.gridColor = lighten(this.border1, 30);\n\n    this.doneTaskBkgColor = this.done;\n    this.doneTaskBorderColor = this.lineColor;\n    this.critBkgColor = this.critical;\n    this.critBorderColor = darken(this.critBkgColor, 10);\n\n    this.todayLineColor = this.critBkgColor;\n\n    /* Architecture Diagram variables */\n    this.archEdgeColor = this.lineColor;\n    this.archEdgeArrowColor = this.lineColor;\n\n    /* state colors */\n    this.transitionColor = this.transitionColor || '#000';\n    this.transitionLabelColor = this.transitionLabelColor || this.textColor;\n    this.stateLabelColor = this.stateLabelColor || this.stateBkg || this.primaryTextColor;\n\n    this.stateBkg = this.stateBkg || this.mainBkg;\n    this.labelBackgroundColor = this.labelBackgroundColor || this.stateBkg;\n    this.compositeBackground = this.compositeBackground || this.background || this.tertiaryColor;\n    this.altBackground = this.altBackground || '#f4f4f4';\n    this.compositeTitleBackground = this.compositeTitleBackground || this.mainBkg;\n    this.stateBorder = this.stateBorder || '#000';\n    this.innerEndBackground = this.primaryBorderColor;\n    this.specialStateColor = '#222';\n\n    this.errorBkgColor = this.errorBkgColor || this.tertiaryColor;\n    this.errorTextColor = this.errorTextColor || this.tertiaryTextColor;\n\n    /* class */\n    this.classText = this.primaryTextColor;\n    /* journey */\n    this.fillType0 = this.primaryColor;\n    this.fillType1 = this.secondaryColor;\n    this.fillType2 = adjust(this.primaryColor, { h: 64 });\n    this.fillType3 = adjust(this.secondaryColor, { h: 64 });\n    this.fillType4 = adjust(this.primaryColor, { h: -64 });\n    this.fillType5 = adjust(this.secondaryColor, { h: -64 });\n    this.fillType6 = adjust(this.primaryColor, { h: 128 });\n    this.fillType7 = adjust(this.secondaryColor, { h: 128 });\n\n    // /* pie */\n    /* Pie diagram */\n    for (let i = 0; i < this.THEME_COLOR_LIMIT; i++) {\n      this['pie' + i] = this['cScale' + i];\n    }\n    this.pie12 = this.pie0;\n    this.pieTitleTextSize = this.pieTitleTextSize || '25px';\n    this.pieTitleTextColor = this.pieTitleTextColor || this.taskTextDarkColor;\n    this.pieSectionTextSize = this.pieSectionTextSize || '17px';\n    this.pieSectionTextColor = this.pieSectionTextColor || this.textColor;\n    this.pieLegendTextSize = this.pieLegendTextSize || '17px';\n    this.pieLegendTextColor = this.pieLegendTextColor || this.taskTextDarkColor;\n    this.pieStrokeColor = this.pieStrokeColor || 'black';\n    this.pieStrokeWidth = this.pieStrokeWidth || '2px';\n    this.pieOuterStrokeWidth = this.pieOuterStrokeWidth || '2px';\n    this.pieOuterStrokeColor = this.pieOuterStrokeColor || 'black';\n    this.pieOpacity = this.pieOpacity || '0.7';\n\n    /* quadrant-graph */\n    this.quadrant1Fill = this.quadrant1Fill || this.primaryColor;\n    this.quadrant2Fill = this.quadrant2Fill || adjust(this.primaryColor, { r: 5, g: 5, b: 5 });\n    this.quadrant3Fill = this.quadrant3Fill || adjust(this.primaryColor, { r: 10, g: 10, b: 10 });\n    this.quadrant4Fill = this.quadrant4Fill || adjust(this.primaryColor, { r: 15, g: 15, b: 15 });\n    this.quadrant1TextFill = this.quadrant1TextFill || this.primaryTextColor;\n    this.quadrant2TextFill =\n      this.quadrant2TextFill || adjust(this.primaryTextColor, { r: -5, g: -5, b: -5 });\n    this.quadrant3TextFill =\n      this.quadrant3TextFill || adjust(this.primaryTextColor, { r: -10, g: -10, b: -10 });\n    this.quadrant4TextFill =\n      this.quadrant4TextFill || adjust(this.primaryTextColor, { r: -15, g: -15, b: -15 });\n    this.quadrantPointFill =\n      this.quadrantPointFill || isDark(this.quadrant1Fill)\n        ? lighten(this.quadrant1Fill)\n        : darken(this.quadrant1Fill);\n    this.quadrantPointTextFill = this.quadrantPointTextFill || this.primaryTextColor;\n    this.quadrantXAxisTextFill = this.quadrantXAxisTextFill || this.primaryTextColor;\n    this.quadrantYAxisTextFill = this.quadrantYAxisTextFill || this.primaryTextColor;\n    this.quadrantInternalBorderStrokeFill =\n      this.quadrantInternalBorderStrokeFill || this.primaryBorderColor;\n    this.quadrantExternalBorderStrokeFill =\n      this.quadrantExternalBorderStrokeFill || this.primaryBorderColor;\n    this.quadrantTitleFill = this.quadrantTitleFill || this.primaryTextColor;\n\n    /* xychart */\n    this.xyChart = {\n      backgroundColor: this.xyChart?.backgroundColor || this.background,\n      titleColor: this.xyChart?.titleColor || this.primaryTextColor,\n      xAxisTitleColor: this.xyChart?.xAxisTitleColor || this.primaryTextColor,\n      xAxisLabelColor: this.xyChart?.xAxisLabelColor || this.primaryTextColor,\n      xAxisTickColor: this.xyChart?.xAxisTickColor || this.primaryTextColor,\n      xAxisLineColor: this.xyChart?.xAxisLineColor || this.primaryTextColor,\n      yAxisTitleColor: this.xyChart?.yAxisTitleColor || this.primaryTextColor,\n      yAxisLabelColor: this.xyChart?.yAxisLabelColor || this.primaryTextColor,\n      yAxisTickColor: this.xyChart?.yAxisTickColor || this.primaryTextColor,\n      yAxisLineColor: this.xyChart?.yAxisLineColor || this.primaryTextColor,\n      plotColorPalette:\n        this.xyChart?.plotColorPalette ||\n        '#EEE,#6BB8E4,#8ACB88,#C7ACD6,#E8DCC2,#FFB2A8,#FFF380,#7E8D91,#FFD8B1,#FAF3E0',\n    };\n\n    /* radar */\n    this.radar = {\n      axisColor: this.radar?.axisColor || this.lineColor,\n      axisStrokeWidth: this.radar?.axisStrokeWidth || 2,\n      axisLabelFontSize: this.radar?.axisLabelFontSize || 12,\n      curveOpacity: this.radar?.curveOpacity || 0.5,\n      curveStrokeWidth: this.radar?.curveStrokeWidth || 2,\n      graticuleColor: this.radar?.graticuleColor || '#DEDEDE',\n      graticuleStrokeWidth: this.radar?.graticuleStrokeWidth || 1,\n      graticuleOpacity: this.radar?.graticuleOpacity || 0.3,\n      legendBoxSize: this.radar?.legendBoxSize || 12,\n      legendFontSize: this.radar?.legendFontSize || 12,\n    };\n\n    /* requirement-diagram */\n    this.requirementBackground = this.requirementBackground || this.primaryColor;\n    this.requirementBorderColor = this.requirementBorderColor || this.primaryBorderColor;\n    this.requirementBorderSize = this.requirementBorderSize || '1';\n    this.requirementTextColor = this.requirementTextColor || this.primaryTextColor;\n    this.relationColor = this.relationColor || this.lineColor;\n    this.relationLabelBackground = this.relationLabelBackground || this.edgeLabelBackground;\n    this.relationLabelColor = this.relationLabelColor || this.actorTextColor;\n\n    /* git */\n    this.git0 = darken(this.pie1, 25) || this.primaryColor;\n    this.git1 = this.pie2 || this.secondaryColor;\n    this.git2 = this.pie3 || this.tertiaryColor;\n    this.git3 = this.pie4 || adjust(this.primaryColor, { h: -30 });\n    this.git4 = this.pie5 || adjust(this.primaryColor, { h: -60 });\n    this.git5 = this.pie6 || adjust(this.primaryColor, { h: -90 });\n    this.git6 = this.pie7 || adjust(this.primaryColor, { h: +60 });\n    this.git7 = this.pie8 || adjust(this.primaryColor, { h: +120 });\n\n    this.gitInv0 = this.gitInv0 || invert(this.git0);\n    this.gitInv1 = this.gitInv1 || invert(this.git1);\n    this.gitInv2 = this.gitInv2 || invert(this.git2);\n    this.gitInv3 = this.gitInv3 || invert(this.git3);\n    this.gitInv4 = this.gitInv4 || invert(this.git4);\n    this.gitInv5 = this.gitInv5 || invert(this.git5);\n    this.gitInv6 = this.gitInv6 || invert(this.git6);\n    this.gitInv7 = this.gitInv7 || invert(this.git7);\n\n    this.branchLabelColor = this.branchLabelColor || this.labelTextColor;\n    this.gitBranchLabel0 = this.branchLabelColor;\n    this.gitBranchLabel1 = 'white';\n    this.gitBranchLabel2 = this.branchLabelColor;\n    this.gitBranchLabel3 = 'white';\n    this.gitBranchLabel4 = this.branchLabelColor;\n    this.gitBranchLabel5 = this.branchLabelColor;\n    this.gitBranchLabel6 = this.branchLabelColor;\n    this.gitBranchLabel7 = this.branchLabelColor;\n\n    this.tagLabelColor = this.tagLabelColor || this.primaryTextColor;\n    this.tagLabelBackground = this.tagLabelBackground || this.primaryColor;\n    this.tagLabelBorder = this.tagBorder || this.primaryBorderColor;\n    this.tagLabelFontSize = this.tagLabelFontSize || '10px';\n    this.commitLabelColor = this.commitLabelColor || this.secondaryTextColor;\n    this.commitLabelBackground = this.commitLabelBackground || this.secondaryColor;\n    this.commitLabelFontSize = this.commitLabelFontSize || '10px';\n\n    /* -------------------------------------------------- */\n    /* EntityRelationship diagrams                        */\n\n    this.attributeBackgroundColorOdd =\n      this.attributeBackgroundColorOdd || oldAttributeBackgroundColorOdd;\n    this.attributeBackgroundColorEven =\n      this.attributeBackgroundColorEven || oldAttributeBackgroundColorEven;\n    /* -------------------------------------------------- */\n  }\n  calculate(overrides) {\n    if (typeof overrides !== 'object') {\n      // Calculate colors form base colors\n      this.updateColors();\n      return;\n    }\n\n    const keys = Object.keys(overrides);\n\n    // Copy values from overrides, this is mainly for base colors\n    keys.forEach((k) => {\n      this[k] = overrides[k];\n    });\n\n    // Calculate colors form base colors\n    this.updateColors();\n    // Copy values from overrides again in case of an override of derived value\n    keys.forEach((k) => {\n      this[k] = overrides[k];\n    });\n  }\n}\n\nexport const getThemeVariables = (userOverrides) => {\n  const theme = new Theme();\n  theme.calculate(userOverrides);\n  return theme;\n};\n", "import { getThemeVariables as baseThemeVariables } from './theme-base.js';\nimport { getThemeVariables as darkThemeVariables } from './theme-dark.js';\nimport { getThemeVariables as defaultThemeVariables } from './theme-default.js';\nimport { getThemeVariables as forestThemeVariables } from './theme-forest.js';\nimport { getThemeVariables as neutralThemeVariables } from './theme-neutral.js';\n\nexport default {\n  base: {\n    getThemeVariables: baseThemeVariables,\n  },\n  dark: {\n    getThemeVariables: darkThemeVariables,\n  },\n  default: {\n    getThemeVariables: defaultThemeVariables,\n  },\n  forest: {\n    getThemeVariables: forestThemeVariables,\n  },\n  neutral: {\n    getThemeVariables: neutralThemeVariables,\n  },\n};\n", "export default {\n  \"flowchart\": {\n    \"useMaxWidth\": true,\n    \"titleTopMargin\": 25,\n    \"subGraphTitleMargin\": {\n      \"top\": 0,\n      \"bottom\": 0\n    },\n    \"diagramPadding\": 8,\n    \"htmlLabels\": true,\n    \"nodeSpacing\": 50,\n    \"rankSpacing\": 50,\n    \"curve\": \"basis\",\n    \"padding\": 15,\n    \"defaultRenderer\": \"dagre-wrapper\",\n    \"wrappingWidth\": 200\n  },\n  \"sequence\": {\n    \"useMaxWidth\": true,\n    \"hideUnusedParticipants\": false,\n    \"activationWidth\": 10,\n    \"diagramMarginX\": 50,\n    \"diagramMarginY\": 10,\n    \"actorMargin\": 50,\n    \"width\": 150,\n    \"height\": 65,\n    \"boxMargin\": 10,\n    \"boxTextMargin\": 5,\n    \"noteMargin\": 10,\n    \"messageMargin\": 35,\n    \"messageAlign\": \"center\",\n    \"mirrorActors\": true,\n    \"forceMenus\": false,\n    \"bottomMarginAdj\": 1,\n    \"rightAngles\": false,\n    \"showSequenceNumbers\": false,\n    \"actorFontSize\": 14,\n    \"actorFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"actorFontWeight\": 400,\n    \"noteFontSize\": 14,\n    \"noteFontFamily\": \"\\\"trebuchet ms\\\", verdana, arial, sans-serif\",\n    \"noteFontWeight\": 400,\n    \"noteAlign\": \"center\",\n    \"messageFontSize\": 16,\n    \"messageFontFamily\": \"\\\"trebuchet ms\\\", verdana, arial, sans-serif\",\n    \"messageFontWeight\": 400,\n    \"wrap\": false,\n    \"wrapPadding\": 10,\n    \"labelBoxWidth\": 50,\n    \"labelBoxHeight\": 20\n  },\n  \"gantt\": {\n    \"useMaxWidth\": true,\n    \"titleTopMargin\": 25,\n    \"barHeight\": 20,\n    \"barGap\": 4,\n    \"topPadding\": 50,\n    \"rightPadding\": 75,\n    \"leftPadding\": 75,\n    \"gridLineStartPadding\": 35,\n    \"fontSize\": 11,\n    \"sectionFontSize\": 11,\n    \"numberSectionStyles\": 4,\n    \"axisFormat\": \"%Y-%m-%d\",\n    \"topAxis\": false,\n    \"displayMode\": \"\",\n    \"weekday\": \"sunday\"\n  },\n  \"journey\": {\n    \"useMaxWidth\": true,\n    \"diagramMarginX\": 50,\n    \"diagramMarginY\": 10,\n    \"leftMargin\": 150,\n    \"width\": 150,\n    \"height\": 50,\n    \"boxMargin\": 10,\n    \"boxTextMargin\": 5,\n    \"noteMargin\": 10,\n    \"messageMargin\": 35,\n    \"messageAlign\": \"center\",\n    \"bottomMarginAdj\": 1,\n    \"rightAngles\": false,\n    \"taskFontSize\": 14,\n    \"taskFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"taskMargin\": 50,\n    \"activationWidth\": 10,\n    \"textPlacement\": \"fo\",\n    \"actorColours\": [\n      \"#8FBC8F\",\n      \"#7CFC00\",\n      \"#00FFFF\",\n      \"#20B2AA\",\n      \"#B0E0E6\",\n      \"#FFFFE0\"\n    ],\n    \"sectionFills\": [\n      \"#191970\",\n      \"#8B008B\",\n      \"#4B0082\",\n      \"#2F4F4F\",\n      \"#800000\",\n      \"#8B4513\",\n      \"#00008B\"\n    ],\n    \"sectionColours\": [\n      \"#fff\"\n    ]\n  },\n  \"class\": {\n    \"useMaxWidth\": true,\n    \"titleTopMargin\": 25,\n    \"arrowMarkerAbsolute\": false,\n    \"dividerMargin\": 10,\n    \"padding\": 5,\n    \"textHeight\": 10,\n    \"defaultRenderer\": \"dagre-wrapper\",\n    \"htmlLabels\": false,\n    \"hideEmptyMembersBox\": false\n  },\n  \"state\": {\n    \"useMaxWidth\": true,\n    \"titleTopMargin\": 25,\n    \"dividerMargin\": 10,\n    \"sizeUnit\": 5,\n    \"padding\": 8,\n    \"textHeight\": 10,\n    \"titleShift\": -15,\n    \"noteMargin\": 10,\n    \"forkWidth\": 70,\n    \"forkHeight\": 7,\n    \"miniPadding\": 2,\n    \"fontSizeFactor\": 5.02,\n    \"fontSize\": 24,\n    \"labelHeight\": 16,\n    \"edgeLengthFactor\": \"20\",\n    \"compositTitleSize\": 35,\n    \"radius\": 5,\n    \"defaultRenderer\": \"dagre-wrapper\"\n  },\n  \"er\": {\n    \"useMaxWidth\": true,\n    \"titleTopMargin\": 25,\n    \"diagramPadding\": 20,\n    \"layoutDirection\": \"TB\",\n    \"minEntityWidth\": 100,\n    \"minEntityHeight\": 75,\n    \"entityPadding\": 15,\n    \"nodeSpacing\": 140,\n    \"rankSpacing\": 80,\n    \"stroke\": \"gray\",\n    \"fill\": \"honeydew\",\n    \"fontSize\": 12\n  },\n  \"pie\": {\n    \"useMaxWidth\": true,\n    \"textPosition\": 0.75\n  },\n  \"quadrantChart\": {\n    \"useMaxWidth\": true,\n    \"chartWidth\": 500,\n    \"chartHeight\": 500,\n    \"titleFontSize\": 20,\n    \"titlePadding\": 10,\n    \"quadrantPadding\": 5,\n    \"xAxisLabelPadding\": 5,\n    \"yAxisLabelPadding\": 5,\n    \"xAxisLabelFontSize\": 16,\n    \"yAxisLabelFontSize\": 16,\n    \"quadrantLabelFontSize\": 16,\n    \"quadrantTextTopPadding\": 5,\n    \"pointTextPadding\": 5,\n    \"pointLabelFontSize\": 12,\n    \"pointRadius\": 5,\n    \"xAxisPosition\": \"top\",\n    \"yAxisPosition\": \"left\",\n    \"quadrantInternalBorderStrokeWidth\": 1,\n    \"quadrantExternalBorderStrokeWidth\": 2\n  },\n  \"xyChart\": {\n    \"useMaxWidth\": true,\n    \"width\": 700,\n    \"height\": 500,\n    \"titleFontSize\": 20,\n    \"titlePadding\": 10,\n    \"showTitle\": true,\n    \"xAxis\": {\n      \"$ref\": \"#/$defs/XYChartAxisConfig\",\n      \"showLabel\": true,\n      \"labelFontSize\": 14,\n      \"labelPadding\": 5,\n      \"showTitle\": true,\n      \"titleFontSize\": 16,\n      \"titlePadding\": 5,\n      \"showTick\": true,\n      \"tickLength\": 5,\n      \"tickWidth\": 2,\n      \"showAxisLine\": true,\n      \"axisLineWidth\": 2\n    },\n    \"yAxis\": {\n      \"$ref\": \"#/$defs/XYChartAxisConfig\",\n      \"showLabel\": true,\n      \"labelFontSize\": 14,\n      \"labelPadding\": 5,\n      \"showTitle\": true,\n      \"titleFontSize\": 16,\n      \"titlePadding\": 5,\n      \"showTick\": true,\n      \"tickLength\": 5,\n      \"tickWidth\": 2,\n      \"showAxisLine\": true,\n      \"axisLineWidth\": 2\n    },\n    \"chartOrientation\": \"vertical\",\n    \"plotReservedSpacePercent\": 50\n  },\n  \"requirement\": {\n    \"useMaxWidth\": true,\n    \"rect_fill\": \"#f9f9f9\",\n    \"text_color\": \"#333\",\n    \"rect_border_size\": \"0.5px\",\n    \"rect_border_color\": \"#bbb\",\n    \"rect_min_width\": 200,\n    \"rect_min_height\": 200,\n    \"fontSize\": 14,\n    \"rect_padding\": 10,\n    \"line_height\": 20\n  },\n  \"mindmap\": {\n    \"useMaxWidth\": true,\n    \"padding\": 10,\n    \"maxNodeWidth\": 200\n  },\n  \"kanban\": {\n    \"useMaxWidth\": true,\n    \"padding\": 8,\n    \"sectionWidth\": 200,\n    \"ticketBaseUrl\": \"\"\n  },\n  \"timeline\": {\n    \"useMaxWidth\": true,\n    \"diagramMarginX\": 50,\n    \"diagramMarginY\": 10,\n    \"leftMargin\": 150,\n    \"width\": 150,\n    \"height\": 50,\n    \"boxMargin\": 10,\n    \"boxTextMargin\": 5,\n    \"noteMargin\": 10,\n    \"messageMargin\": 35,\n    \"messageAlign\": \"center\",\n    \"bottomMarginAdj\": 1,\n    \"rightAngles\": false,\n    \"taskFontSize\": 14,\n    \"taskFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"taskMargin\": 50,\n    \"activationWidth\": 10,\n    \"textPlacement\": \"fo\",\n    \"actorColours\": [\n      \"#8FBC8F\",\n      \"#7CFC00\",\n      \"#00FFFF\",\n      \"#20B2AA\",\n      \"#B0E0E6\",\n      \"#FFFFE0\"\n    ],\n    \"sectionFills\": [\n      \"#191970\",\n      \"#8B008B\",\n      \"#4B0082\",\n      \"#2F4F4F\",\n      \"#800000\",\n      \"#8B4513\",\n      \"#00008B\"\n    ],\n    \"sectionColours\": [\n      \"#fff\"\n    ],\n    \"disableMulticolor\": false\n  },\n  \"gitGraph\": {\n    \"useMaxWidth\": true,\n    \"titleTopMargin\": 25,\n    \"diagramPadding\": 8,\n    \"nodeLabel\": {\n      \"width\": 75,\n      \"height\": 100,\n      \"x\": -25,\n      \"y\": 0\n    },\n    \"mainBranchName\": \"main\",\n    \"mainBranchOrder\": 0,\n    \"showCommitLabel\": true,\n    \"showBranches\": true,\n    \"rotateCommitLabel\": true,\n    \"parallelCommits\": false,\n    \"arrowMarkerAbsolute\": false\n  },\n  \"c4\": {\n    \"useMaxWidth\": true,\n    \"diagramMarginX\": 50,\n    \"diagramMarginY\": 10,\n    \"c4ShapeMargin\": 50,\n    \"c4ShapePadding\": 20,\n    \"width\": 216,\n    \"height\": 60,\n    \"boxMargin\": 10,\n    \"c4ShapeInRow\": 4,\n    \"nextLinePaddingX\": 0,\n    \"c4BoundaryInRow\": 2,\n    \"personFontSize\": 14,\n    \"personFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"personFontWeight\": \"normal\",\n    \"external_personFontSize\": 14,\n    \"external_personFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"external_personFontWeight\": \"normal\",\n    \"systemFontSize\": 14,\n    \"systemFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"systemFontWeight\": \"normal\",\n    \"external_systemFontSize\": 14,\n    \"external_systemFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"external_systemFontWeight\": \"normal\",\n    \"system_dbFontSize\": 14,\n    \"system_dbFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"system_dbFontWeight\": \"normal\",\n    \"external_system_dbFontSize\": 14,\n    \"external_system_dbFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"external_system_dbFontWeight\": \"normal\",\n    \"system_queueFontSize\": 14,\n    \"system_queueFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"system_queueFontWeight\": \"normal\",\n    \"external_system_queueFontSize\": 14,\n    \"external_system_queueFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"external_system_queueFontWeight\": \"normal\",\n    \"boundaryFontSize\": 14,\n    \"boundaryFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"boundaryFontWeight\": \"normal\",\n    \"messageFontSize\": 12,\n    \"messageFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"messageFontWeight\": \"normal\",\n    \"containerFontSize\": 14,\n    \"containerFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"containerFontWeight\": \"normal\",\n    \"external_containerFontSize\": 14,\n    \"external_containerFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"external_containerFontWeight\": \"normal\",\n    \"container_dbFontSize\": 14,\n    \"container_dbFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"container_dbFontWeight\": \"normal\",\n    \"external_container_dbFontSize\": 14,\n    \"external_container_dbFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"external_container_dbFontWeight\": \"normal\",\n    \"container_queueFontSize\": 14,\n    \"container_queueFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"container_queueFontWeight\": \"normal\",\n    \"external_container_queueFontSize\": 14,\n    \"external_container_queueFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"external_container_queueFontWeight\": \"normal\",\n    \"componentFontSize\": 14,\n    \"componentFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"componentFontWeight\": \"normal\",\n    \"external_componentFontSize\": 14,\n    \"external_componentFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"external_componentFontWeight\": \"normal\",\n    \"component_dbFontSize\": 14,\n    \"component_dbFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"component_dbFontWeight\": \"normal\",\n    \"external_component_dbFontSize\": 14,\n    \"external_component_dbFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"external_component_dbFontWeight\": \"normal\",\n    \"component_queueFontSize\": 14,\n    \"component_queueFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"component_queueFontWeight\": \"normal\",\n    \"external_component_queueFontSize\": 14,\n    \"external_component_queueFontFamily\": \"\\\"Open Sans\\\", sans-serif\",\n    \"external_component_queueFontWeight\": \"normal\",\n    \"wrap\": true,\n    \"wrapPadding\": 10,\n    \"person_bg_color\": \"#08427B\",\n    \"person_border_color\": \"#073B6F\",\n    \"external_person_bg_color\": \"#686868\",\n    \"external_person_border_color\": \"#8A8A8A\",\n    \"system_bg_color\": \"#1168BD\",\n    \"system_border_color\": \"#3C7FC0\",\n    \"system_db_bg_color\": \"#1168BD\",\n    \"system_db_border_color\": \"#3C7FC0\",\n    \"system_queue_bg_color\": \"#1168BD\",\n    \"system_queue_border_color\": \"#3C7FC0\",\n    \"external_system_bg_color\": \"#999999\",\n    \"external_system_border_color\": \"#8A8A8A\",\n    \"external_system_db_bg_color\": \"#999999\",\n    \"external_system_db_border_color\": \"#8A8A8A\",\n    \"external_system_queue_bg_color\": \"#999999\",\n    \"external_system_queue_border_color\": \"#8A8A8A\",\n    \"container_bg_color\": \"#438DD5\",\n    \"container_border_color\": \"#3C7FC0\",\n    \"container_db_bg_color\": \"#438DD5\",\n    \"container_db_border_color\": \"#3C7FC0\",\n    \"container_queue_bg_color\": \"#438DD5\",\n    \"container_queue_border_color\": \"#3C7FC0\",\n    \"external_container_bg_color\": \"#B3B3B3\",\n    \"external_container_border_color\": \"#A6A6A6\",\n    \"external_container_db_bg_color\": \"#B3B3B3\",\n    \"external_container_db_border_color\": \"#A6A6A6\",\n    \"external_container_queue_bg_color\": \"#B3B3B3\",\n    \"external_container_queue_border_color\": \"#A6A6A6\",\n    \"component_bg_color\": \"#85BBF0\",\n    \"component_border_color\": \"#78A8D8\",\n    \"component_db_bg_color\": \"#85BBF0\",\n    \"component_db_border_color\": \"#78A8D8\",\n    \"component_queue_bg_color\": \"#85BBF0\",\n    \"component_queue_border_color\": \"#78A8D8\",\n    \"external_component_bg_color\": \"#CCCCCC\",\n    \"external_component_border_color\": \"#BFBFBF\",\n    \"external_component_db_bg_color\": \"#CCCCCC\",\n    \"external_component_db_border_color\": \"#BFBFBF\",\n    \"external_component_queue_bg_color\": \"#CCCCCC\",\n    \"external_component_queue_border_color\": \"#BFBFBF\"\n  },\n  \"sankey\": {\n    \"useMaxWidth\": true,\n    \"width\": 600,\n    \"height\": 400,\n    \"linkColor\": \"gradient\",\n    \"nodeAlignment\": \"justify\",\n    \"showValues\": true,\n    \"prefix\": \"\",\n    \"suffix\": \"\"\n  },\n  \"block\": {\n    \"useMaxWidth\": true,\n    \"padding\": 8\n  },\n  \"packet\": {\n    \"useMaxWidth\": true,\n    \"rowHeight\": 32,\n    \"bitWidth\": 32,\n    \"bitsPerRow\": 32,\n    \"showBits\": true,\n    \"paddingX\": 5,\n    \"paddingY\": 5\n  },\n  \"architecture\": {\n    \"useMaxWidth\": true,\n    \"padding\": 40,\n    \"iconSize\": 80,\n    \"fontSize\": 16\n  },\n  \"radar\": {\n    \"useMaxWidth\": true,\n    \"width\": 600,\n    \"height\": 600,\n    \"marginTop\": 50,\n    \"marginRight\": 50,\n    \"marginBottom\": 50,\n    \"marginLeft\": 50,\n    \"axisScaleFactor\": 1,\n    \"axisLabelFactor\": 1.05,\n    \"curveTension\": 0.17\n  },\n  \"theme\": \"default\",\n  \"look\": \"classic\",\n  \"handDrawnSeed\": 0,\n  \"layout\": \"dagre\",\n  \"maxTextSize\": 50000,\n  \"maxEdges\": 500,\n  \"darkMode\": false,\n  \"fontFamily\": \"\\\"trebuchet ms\\\", verdana, arial, sans-serif;\",\n  \"logLevel\": 5,\n  \"securityLevel\": \"strict\",\n  \"startOnLoad\": true,\n  \"arrowMarkerAbsolute\": false,\n  \"secure\": [\n    \"secure\",\n    \"securityLevel\",\n    \"startOnLoad\",\n    \"maxTextSize\",\n    \"suppressErrorRendering\",\n    \"maxEdges\"\n  ],\n  \"legacyMathML\": false,\n  \"forceLegacyMathML\": false,\n  \"deterministicIds\": false,\n  \"fontSize\": 16,\n  \"markdownAutoWrap\": true,\n  \"suppressErrorRendering\": false\n};", "import type { Required<PERSON>eep } from 'type-fest';\n\nimport theme from './themes/index.js';\nimport type { MermaidConfig } from './config.type.js';\n\n// Uses our custom Vite jsonSchemaPlugin to load only the default values from\n// our JSON Schema\n// @ts-expect-error This file is automatically generated via a custom Vite plugin\nimport defaultConfigJson from './schemas/config.schema.yaml?only-defaults=true';\n\n/**\n * Default mermaid configuration options.\n *\n * Please see the Mermaid config JSON Schema for the default JSON values.\n * Non-JSON JS default values are listed in this file, e.g. functions, or\n * `undefined` (explicitly set so that `config<PERSON><PERSON><PERSON>` finds them).\n */\nconst config: RequiredDeep<MermaidConfig> = {\n  ...defaultConfigJson,\n  // Set, even though they're `undefined` so that `config<PERSON><PERSON>s` finds these keys\n  // TODO: Should we replace these with `null` so that they can go in the JSON Schema?\n  deterministicIDSeed: undefined,\n  elk: {\n    // mergeEdges is needed here to be considered\n    mergeEdges: false,\n    nodePlacementStrategy: 'BRANDES_KOEPF',\n  },\n  themeCSS: undefined,\n\n  // add non-JSON default config values\n  themeVariables: theme.default.getThemeVariables(),\n  sequence: {\n    ...defaultConfigJson.sequence,\n    messageFont: function () {\n      return {\n        fontFamily: this.messageFontFamily,\n        fontSize: this.messageFontSize,\n        fontWeight: this.messageFontWeight,\n      };\n    },\n    noteFont: function () {\n      return {\n        fontFamily: this.noteFontFamily,\n        fontSize: this.noteFontSize,\n        fontWeight: this.noteFontWeight,\n      };\n    },\n    actorFont: function () {\n      return {\n        fontFamily: this.actorFontFamily,\n        fontSize: this.actorFontSize,\n        fontWeight: this.actorFontWeight,\n      };\n    },\n  },\n  class: {\n    hideEmptyMembersBox: false,\n  },\n  gantt: {\n    ...defaultConfigJson.gantt,\n    tickInterval: undefined,\n    useWidth: undefined, // can probably be removed since `configKeys` already includes this\n  },\n  c4: {\n    ...defaultConfigJson.c4,\n    useWidth: undefined,\n    personFont: function () {\n      return {\n        fontFamily: this.personFontFamily,\n        fontSize: this.personFontSize,\n        fontWeight: this.personFontWeight,\n      };\n    },\n\n    external_personFont: function () {\n      return {\n        fontFamily: this.external_personFontFamily,\n        fontSize: this.external_personFontSize,\n        fontWeight: this.external_personFontWeight,\n      };\n    },\n\n    systemFont: function () {\n      return {\n        fontFamily: this.systemFontFamily,\n        fontSize: this.systemFontSize,\n        fontWeight: this.systemFontWeight,\n      };\n    },\n\n    external_systemFont: function () {\n      return {\n        fontFamily: this.external_systemFontFamily,\n        fontSize: this.external_systemFontSize,\n        fontWeight: this.external_systemFontWeight,\n      };\n    },\n\n    system_dbFont: function () {\n      return {\n        fontFamily: this.system_dbFontFamily,\n        fontSize: this.system_dbFontSize,\n        fontWeight: this.system_dbFontWeight,\n      };\n    },\n\n    external_system_dbFont: function () {\n      return {\n        fontFamily: this.external_system_dbFontFamily,\n        fontSize: this.external_system_dbFontSize,\n        fontWeight: this.external_system_dbFontWeight,\n      };\n    },\n\n    system_queueFont: function () {\n      return {\n        fontFamily: this.system_queueFontFamily,\n        fontSize: this.system_queueFontSize,\n        fontWeight: this.system_queueFontWeight,\n      };\n    },\n\n    external_system_queueFont: function () {\n      return {\n        fontFamily: this.external_system_queueFontFamily,\n        fontSize: this.external_system_queueFontSize,\n        fontWeight: this.external_system_queueFontWeight,\n      };\n    },\n\n    containerFont: function () {\n      return {\n        fontFamily: this.containerFontFamily,\n        fontSize: this.containerFontSize,\n        fontWeight: this.containerFontWeight,\n      };\n    },\n\n    external_containerFont: function () {\n      return {\n        fontFamily: this.external_containerFontFamily,\n        fontSize: this.external_containerFontSize,\n        fontWeight: this.external_containerFontWeight,\n      };\n    },\n\n    container_dbFont: function () {\n      return {\n        fontFamily: this.container_dbFontFamily,\n        fontSize: this.container_dbFontSize,\n        fontWeight: this.container_dbFontWeight,\n      };\n    },\n\n    external_container_dbFont: function () {\n      return {\n        fontFamily: this.external_container_dbFontFamily,\n        fontSize: this.external_container_dbFontSize,\n        fontWeight: this.external_container_dbFontWeight,\n      };\n    },\n\n    container_queueFont: function () {\n      return {\n        fontFamily: this.container_queueFontFamily,\n        fontSize: this.container_queueFontSize,\n        fontWeight: this.container_queueFontWeight,\n      };\n    },\n\n    external_container_queueFont: function () {\n      return {\n        fontFamily: this.external_container_queueFontFamily,\n        fontSize: this.external_container_queueFontSize,\n        fontWeight: this.external_container_queueFontWeight,\n      };\n    },\n\n    componentFont: function () {\n      return {\n        fontFamily: this.componentFontFamily,\n        fontSize: this.componentFontSize,\n        fontWeight: this.componentFontWeight,\n      };\n    },\n\n    external_componentFont: function () {\n      return {\n        fontFamily: this.external_componentFontFamily,\n        fontSize: this.external_componentFontSize,\n        fontWeight: this.external_componentFontWeight,\n      };\n    },\n\n    component_dbFont: function () {\n      return {\n        fontFamily: this.component_dbFontFamily,\n        fontSize: this.component_dbFontSize,\n        fontWeight: this.component_dbFontWeight,\n      };\n    },\n\n    external_component_dbFont: function () {\n      return {\n        fontFamily: this.external_component_dbFontFamily,\n        fontSize: this.external_component_dbFontSize,\n        fontWeight: this.external_component_dbFontWeight,\n      };\n    },\n\n    component_queueFont: function () {\n      return {\n        fontFamily: this.component_queueFontFamily,\n        fontSize: this.component_queueFontSize,\n        fontWeight: this.component_queueFontWeight,\n      };\n    },\n\n    external_component_queueFont: function () {\n      return {\n        fontFamily: this.external_component_queueFontFamily,\n        fontSize: this.external_component_queueFontSize,\n        fontWeight: this.external_component_queueFontWeight,\n      };\n    },\n\n    boundaryFont: function () {\n      return {\n        fontFamily: this.boundaryFontFamily,\n        fontSize: this.boundaryFontSize,\n        fontWeight: this.boundaryFontWeight,\n      };\n    },\n\n    messageFont: function () {\n      return {\n        fontFamily: this.messageFontFamily,\n        fontSize: this.messageFontSize,\n        fontWeight: this.messageFontWeight,\n      };\n    },\n  },\n  pie: {\n    ...defaultConfigJson.pie,\n    useWidth: 984,\n  },\n  xyChart: {\n    ...defaultConfigJson.xyChart,\n    useWidth: undefined,\n  },\n  requirement: {\n    ...defaultConfigJson.requirement,\n    useWidth: undefined,\n  },\n  packet: {\n    ...defaultConfigJson.packet,\n  },\n  radar: {\n    ...defaultConfigJson.radar,\n  },\n};\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst keyify = (obj: any, prefix = ''): string[] =>\n  Object.keys(obj).reduce((res: string[], el): string[] => {\n    if (Array.isArray(obj[el])) {\n      return res;\n    } else if (typeof obj[el] === 'object' && obj[el] !== null) {\n      return [...res, prefix + el, ...keyify(obj[el], '')];\n    }\n    return [...res, prefix + el];\n  }, []);\n\nexport const configKeys = new Set<string>(keyify(config, ''));\nexport default config;\n", "import { configKeys } from '../defaultConfig.js';\nimport { log } from '../logger.js';\n\n/**\n * Sanitizes directive objects\n *\n * @param args - Directive's JSON\n */\nexport const sanitizeDirective = (args: any): void => {\n  log.debug('sanitizeDirective called with', args);\n\n  // Return if not an object\n  if (typeof args !== 'object' || args == null) {\n    return;\n  }\n\n  // Sanitize each element if an array\n  if (Array.isArray(args)) {\n    args.forEach((arg) => sanitizeDirective(arg));\n    return;\n  }\n\n  // Sanitize each key if an object\n  for (const key of Object.keys(args)) {\n    log.debug('Checking key', key);\n    if (\n      key.startsWith('__') ||\n      key.includes('proto') ||\n      key.includes('constr') ||\n      !configKeys.has(key) ||\n      args[key] == null\n    ) {\n      log.debug('sanitize deleting key: ', key);\n      delete args[key];\n      continue;\n    }\n\n    // Recurse if an object\n    if (typeof args[key] === 'object') {\n      log.debug('sanitizing object', key);\n      sanitizeDirective(args[key]);\n      continue;\n    }\n\n    const cssMatchers = ['themeCSS', 'fontFamily', 'altFontFamily'];\n    for (const cssKey of cssMatchers) {\n      if (key.includes(cssKey)) {\n        log.debug('sanitizing css option', key);\n        args[key] = sanitizeCss(args[key]);\n      }\n    }\n  }\n\n  if (args.themeVariables) {\n    for (const k of Object.keys(args.themeVariables)) {\n      const val = args.themeVariables[k];\n      if (val?.match && !val.match(/^[\\d \"#%(),.;A-Za-z]+$/)) {\n        args.themeVariables[k] = '';\n      }\n    }\n  }\n  log.debug('After sanitization', args);\n};\n\nexport const sanitizeCss = (str: string): string => {\n  let startCnt = 0;\n  let endCnt = 0;\n\n  for (const element of str) {\n    if (startCnt < endCnt) {\n      return '{ /* ERROR: Unbalanced CSS */ }';\n    }\n    if (element === '{') {\n      startCnt++;\n    } else if (element === '}') {\n      endCnt++;\n    }\n  }\n  if (startCnt !== endCnt) {\n    return '{ /* ERROR: Unbalanced CSS */ }';\n  }\n  // Todo add more checks here\n  return str;\n};\n", "import assignWithDepth from './assignWithDepth.js';\nimport { log } from './logger.js';\nimport theme from './themes/index.js';\nimport config from './defaultConfig.js';\nimport type { MermaidConfig } from './config.type.js';\nimport { sanitizeDirective } from './utils/sanitizeDirective.js';\n\nexport const defaultConfig: MermaidConfig = Object.freeze(config);\n\nlet siteConfig: MermaidConfig = assignWithDepth({}, defaultConfig);\nlet configFromInitialize: MermaidConfig;\nlet directives: MermaidConfig[] = [];\nlet currentConfig: MermaidConfig = assignWithDepth({}, defaultConfig);\n\nexport const updateCurrentConfig = (siteCfg: MermaidConfig, _directives: MermaidConfig[]) => {\n  // start with config being the siteConfig\n  let cfg: MermaidConfig = assignWithDepth({}, siteCfg);\n  // let sCfg = assignWithDepth(defaultConfig, siteConfigDelta);\n\n  // Join directives\n  let sumOfDirectives: MermaidConfig = {};\n  for (const d of _directives) {\n    sanitize(d);\n    // Apply the data from the directive where the overrides the themeVariables\n    sumOfDirectives = assignWithDepth(sumOfDirectives, d);\n  }\n\n  cfg = assignWithDepth(cfg, sumOfDirectives);\n\n  if (sumOfDirectives.theme && sumOfDirectives.theme in theme) {\n    const tmpConfigFromInitialize = assignWithDepth({}, configFromInitialize);\n    const themeVariables = assignWithDepth(\n      tmpConfigFromInitialize.themeVariables || {},\n      sumOfDirectives.themeVariables\n    );\n    if (cfg.theme && cfg.theme in theme) {\n      cfg.themeVariables = theme[cfg.theme as keyof typeof theme].getThemeVariables(themeVariables);\n    }\n  }\n\n  currentConfig = cfg;\n  checkConfig(currentConfig);\n  return currentConfig;\n};\n\n/**\n * ## setSiteConfig\n *\n * | Function      | Description                           | Type        | Values                                  |\n * | ------------- | ------------------------------------- | ----------- | --------------------------------------- |\n * | setSiteConfig | Sets the siteConfig to desired values | Put Request | Any Values, except ones in secure array |\n *\n * **Notes:** Sets the siteConfig. The siteConfig is a protected configuration for repeat use. Calls\n * to reset() will reset the currentConfig to siteConfig. Calls to reset(configApi.defaultConfig)\n * will reset siteConfig and currentConfig to the defaultConfig Note: currentConfig is set in this\n * function _Default value: At default, will mirror Global Config_\n *\n * @param conf - The base currentConfig to use as siteConfig\n * @returns The new siteConfig\n */\nexport const setSiteConfig = (conf: MermaidConfig): MermaidConfig => {\n  siteConfig = assignWithDepth({}, defaultConfig);\n  siteConfig = assignWithDepth(siteConfig, conf);\n\n  // @ts-ignore: TODO Fix ts errors\n  if (conf.theme && theme[conf.theme]) {\n    // @ts-ignore: TODO Fix ts errors\n    siteConfig.themeVariables = theme[conf.theme].getThemeVariables(conf.themeVariables);\n  }\n\n  updateCurrentConfig(siteConfig, directives);\n  return siteConfig;\n};\n\nexport const saveConfigFromInitialize = (conf: MermaidConfig): void => {\n  configFromInitialize = assignWithDepth({}, conf);\n};\n\nexport const updateSiteConfig = (conf: MermaidConfig): MermaidConfig => {\n  siteConfig = assignWithDepth(siteConfig, conf);\n  updateCurrentConfig(siteConfig, directives);\n\n  return siteConfig;\n};\n/**\n * ## getSiteConfig\n *\n * | Function      | Description                                       | Type        | Values                           |\n * | ------------- | ------------------------------------------------- | ----------- | -------------------------------- |\n * | setSiteConfig | Returns the current siteConfig base configuration | Get Request | Returns Any Values in siteConfig |\n *\n * **Notes**: Returns **any** values in siteConfig.\n *\n * @returns The siteConfig\n */\nexport const getSiteConfig = (): MermaidConfig => {\n  return assignWithDepth({}, siteConfig);\n};\n/**\n * ## setConfig\n *\n * | Function      | Description                           | Type        | Values                                  |\n * | ------------- | ------------------------------------- | ----------- | --------------------------------------- |\n * | setSiteConfig | Sets the siteConfig to desired values | Put Request | Any Values, except ones in secure array |\n *\n * **Notes**: Sets the currentConfig. The parameter conf is sanitized based on the siteConfig.secure\n * keys. Any values found in conf with key found in siteConfig.secure will be replaced with the\n * corresponding siteConfig value.\n *\n * @param conf - The potential currentConfig\n * @returns The currentConfig merged with the sanitized conf\n */\nexport const setConfig = (conf: MermaidConfig): MermaidConfig => {\n  checkConfig(conf);\n  assignWithDepth(currentConfig, conf);\n\n  return getConfig();\n};\n\n/**\n * ## getConfig\n *\n * | Function  | Description               | Type        | Return Values                  |\n * | --------- | ------------------------- | ----------- | ------------------------------ |\n * | getConfig | Obtains the currentConfig | Get Request | Any Values from current Config |\n *\n * **Notes**: Avoid calling this function repeatedly. Instead, store the result in a variable and use it, and pass it down to function calls.\n *\n * @returns The currentConfig\n */\nexport const getConfig = (): MermaidConfig => {\n  return assignWithDepth({}, currentConfig);\n};\n/**\n * ## sanitize\n *\n * | Function | Description                            | Type        | Values |\n * | -------- | -------------------------------------- | ----------- | ------ |\n * | sanitize | Sets the siteConfig to desired values. | Put Request | None   |\n *\n * Ensures options parameter does not attempt to override siteConfig secure keys **Notes**: modifies\n * options in-place\n *\n * @param options - The potential setConfig parameter\n */\nexport const sanitize = (options: any) => {\n  if (!options) {\n    return;\n  }\n  // Checking that options are not in the list of excluded options\n  ['secure', ...(siteConfig.secure ?? [])].forEach((key) => {\n    if (Object.hasOwn(options, key)) {\n      // DO NOT attempt to print options[key] within `${}` as a malicious script\n      // can exploit the logger's attempt to stringify the value and execute arbitrary code\n      log.debug(`Denied attempt to modify a secure key ${key}`, options[key]);\n      delete options[key];\n    }\n  });\n\n  // Check that there no attempts of prototype pollution\n  Object.keys(options).forEach((key) => {\n    if (key.startsWith('__')) {\n      delete options[key];\n    }\n  });\n  // Check that there no attempts of xss, there should be no tags at all in the directive\n  // blocking data urls as base64 urls can contain svg's with inline script tags\n  Object.keys(options).forEach((key) => {\n    if (\n      typeof options[key] === 'string' &&\n      (options[key].includes('<') ||\n        options[key].includes('>') ||\n        options[key].includes('url(data:'))\n    ) {\n      delete options[key];\n    }\n    if (typeof options[key] === 'object') {\n      sanitize(options[key]);\n    }\n  });\n};\n\n/**\n * Pushes in a directive to the configuration\n *\n * @param directive - The directive to push in\n */\nexport const addDirective = (directive: MermaidConfig) => {\n  sanitizeDirective(directive);\n\n  // If the directive has a fontFamily, but no themeVariables, add the fontFamily to the themeVariables\n  if (directive.fontFamily && !directive.themeVariables?.fontFamily) {\n    directive.themeVariables = {\n      ...directive.themeVariables,\n      fontFamily: directive.fontFamily,\n    };\n  }\n\n  directives.push(directive);\n  updateCurrentConfig(siteConfig, directives);\n};\n\n/**\n * ## reset\n *\n * | Function | Description                  | Type        | Required | Values |\n * | -------- | ---------------------------- | ----------- | -------- | ------ |\n * | reset    | Resets currentConfig to conf | Put Request | Required | None   |\n *\n * ## conf\n *\n * | Parameter | Description                                                    | Type       | Required | Values                                       |\n * | --------- | -------------------------------------------------------------- | ---------- | -------- | -------------------------------------------- |\n * | conf      | base set of values, which currentConfig could be **reset** to. | Dictionary | Required | Any Values, with respect to the secure Array |\n *\n * **Notes**: (default: current siteConfig ) (optional, default `getSiteConfig()`)\n *\n * @param config - base set of values, which currentConfig could be **reset** to.\n * Defaults to the current siteConfig (e.g returned by {@link getSiteConfig}).\n */\nexport const reset = (config = siteConfig): void => {\n  // Replace current config with siteConfig\n  directives = [];\n  updateCurrentConfig(config, directives);\n};\n\nconst ConfigWarning = {\n  LAZY_LOAD_DEPRECATED:\n    'The configuration options lazyLoadedDiagrams and loadExternalDiagramsAtStartup are deprecated. Please use registerExternalDiagrams instead.',\n} as const;\n\ntype ConfigWarningStrings = keyof typeof ConfigWarning;\nconst issuedWarnings: Partial<Record<ConfigWarningStrings, boolean>> = {};\nconst issueWarning = (warning: ConfigWarningStrings) => {\n  if (issuedWarnings[warning]) {\n    return;\n  }\n  log.warn(ConfigWarning[warning]);\n  issuedWarnings[warning] = true;\n};\n\nconst checkConfig = (config: MermaidConfig) => {\n  if (!config) {\n    return;\n  }\n  // @ts-expect-error Properties were removed in v10. Warning should exist.\n  if (config.lazyLoadedDiagrams || config.loadExternalDiagramsAtStartup) {\n    issueWarning('LAZY_LOAD_DEPRECATED');\n  }\n};\n", "import DOMPurify from 'dompurify';\nimport type { MermaidConfig } from '../../config.type.js';\n\n// Remove and ignore br:s\nexport const lineBreakRegex = /<br\\s*\\/?>/gi;\n\n/**\n * Gets the rows of lines in a string\n *\n * @param s - The string to check the lines for\n * @returns The rows in that string\n */\nexport const getRows = (s?: string): string[] => {\n  if (!s) {\n    return [''];\n  }\n  const str = breakToPlaceholder(s).replace(/\\\\n/g, '#br#');\n  return str.split('#br#');\n};\n\nconst setupDompurifyHooksIfNotSetup = (() => {\n  let setup = false;\n\n  return () => {\n    if (!setup) {\n      setupDompurifyHooks();\n      setup = true;\n    }\n  };\n})();\n\nfunction setupDompurifyHooks() {\n  const TEMPORARY_ATTRIBUTE = 'data-temp-href-target';\n\n  DOMPurify.addHook('beforeSanitizeAttributes', (node) => {\n    if (node instanceof Element && node.tagName === 'A' && node.hasAttribute('target')) {\n      node.setAttribute(TEMPORARY_ATTRIBUTE, node.getAttribute('target') ?? '');\n    }\n  });\n\n  DOMPurify.addHook('afterSanitizeAttributes', (node) => {\n    if (node instanceof Element && node.tagName === 'A' && node.hasAttribute(TEMPORARY_ATTRIBUTE)) {\n      node.setAttribute('target', node.getAttribute(TEMPORARY_ATTRIBUTE) ?? '');\n      node.removeAttribute(TEMPORARY_ATTRIBUTE);\n      if (node.getAttribute('target') === '_blank') {\n        node.setAttribute('rel', 'noopener');\n      }\n    }\n  });\n}\n\n/**\n * Removes script tags from a text\n *\n * @param txt - The text to sanitize\n * @returns The safer text\n */\nexport const removeScript = (txt: string): string => {\n  setupDompurifyHooksIfNotSetup();\n\n  const sanitizedText = DOMPurify.sanitize(txt);\n\n  return sanitizedText;\n};\n\nconst sanitizeMore = (text: string, config: MermaidConfig) => {\n  if (config.flowchart?.htmlLabels !== false) {\n    const level = config.securityLevel;\n    if (level === 'antiscript' || level === 'strict') {\n      text = removeScript(text);\n    } else if (level !== 'loose') {\n      text = breakToPlaceholder(text);\n      text = text.replace(/</g, '&lt;').replace(/>/g, '&gt;');\n      text = text.replace(/=/g, '&equals;');\n      text = placeholderToBreak(text);\n    }\n  }\n  return text;\n};\n\nexport const sanitizeText = (text: string, config: MermaidConfig): string => {\n  if (!text) {\n    return text;\n  }\n  if (config.dompurifyConfig) {\n    text = DOMPurify.sanitize(sanitizeMore(text, config), config.dompurifyConfig).toString();\n  } else {\n    text = DOMPurify.sanitize(sanitizeMore(text, config), {\n      FORBID_TAGS: ['style'],\n    }).toString();\n  }\n  return text;\n};\n\nexport const sanitizeTextOrArray = (\n  a: string | string[] | string[][],\n  config: MermaidConfig\n): string | string[] => {\n  if (typeof a === 'string') {\n    return sanitizeText(a, config);\n  }\n  // TODO: Refactor to avoid flat.\n  return a.flat().map((x: string) => sanitizeText(x, config));\n};\n\n/**\n * Whether or not a text has any line breaks\n *\n * @param text - The text to test\n * @returns Whether or not the text has breaks\n */\nexport const hasBreaks = (text: string): boolean => {\n  return lineBreakRegex.test(text);\n};\n\n/**\n * Splits on <br> tags\n *\n * @param text - Text to split\n * @returns List of lines as strings\n */\nexport const splitBreaks = (text: string): string[] => {\n  return text.split(lineBreakRegex);\n};\n\n/**\n * Converts placeholders to line breaks in HTML\n *\n * @param s - HTML with placeholders\n * @returns HTML with breaks instead of placeholders\n */\nconst placeholderToBreak = (s: string): string => {\n  return s.replace(/#br#/g, '<br/>');\n};\n\n/**\n * Opposite of `placeholderToBreak`, converts breaks to placeholders\n *\n * @param s - HTML string\n * @returns String with placeholders\n */\nconst breakToPlaceholder = (s: string): string => {\n  return s.replace(lineBreakRegex, '#br#');\n};\n\n/**\n * Gets the current URL\n *\n * @param useAbsolute - Whether to return the absolute URL or not\n * @returns The current URL\n */\nconst getUrl = (useAbsolute: boolean): string => {\n  let url = '';\n  if (useAbsolute) {\n    url =\n      window.location.protocol +\n      '//' +\n      window.location.host +\n      window.location.pathname +\n      window.location.search;\n    url = url.replaceAll(/\\(/g, '\\\\(');\n    url = url.replaceAll(/\\)/g, '\\\\)');\n  }\n\n  return url;\n};\n\n/**\n * Converts a string/boolean into a boolean\n *\n * @param val - String or boolean to convert\n * @returns The result from the input\n */\nexport const evaluate = (val?: string | boolean): boolean =>\n  val === false || ['false', 'null', '0'].includes(String(val).trim().toLowerCase()) ? false : true;\n\n/**\n * Wrapper around Math.max which removes non-numeric values\n * Returns the larger of a set of supplied numeric expressions.\n * @param values - Numeric expressions to be evaluated\n * @returns The smaller value\n */\nexport const getMax = function (...values: number[]): number {\n  const newValues: number[] = values.filter((value) => {\n    return !isNaN(value);\n  });\n  return Math.max(...newValues);\n};\n\n/**\n * Wrapper around Math.min which removes non-numeric values\n * Returns the smaller of a set of supplied numeric expressions.\n * @param values - Numeric expressions to be evaluated\n * @returns The smaller value\n */\nexport const getMin = function (...values: number[]): number {\n  const newValues: number[] = values.filter((value) => {\n    return !isNaN(value);\n  });\n  return Math.min(...newValues);\n};\n\n/**\n * Makes generics in typescript syntax\n *\n * @example\n * Array of array of strings in typescript syntax\n *\n * ```js\n * // returns \"Array<Array<string>>\"\n * parseGenericTypes('Array~Array~string~~');\n * ```\n * @param text - The text to convert\n * @returns The converted string\n */\nexport const parseGenericTypes = function (input: string): string {\n  const inputSets = input.split(/(,)/);\n  const output = [];\n\n  for (let i = 0; i < inputSets.length; i++) {\n    let thisSet = inputSets[i];\n\n    // if the original input included a value such as \"~K, V~\"\", these will be split into\n    // an array of [\"~K\",\",\",\" V~\"].\n    // This means that on each call of processSet, there will only be 1 ~ present\n    // To account for this, if we encounter a \",\", we are checking the previous and next sets in the array\n    // to see if they contain matching ~'s\n    // in which case we are assuming that they should be rejoined and sent to be processed\n    if (thisSet === ',' && i > 0 && i + 1 < inputSets.length) {\n      const previousSet = inputSets[i - 1];\n      const nextSet = inputSets[i + 1];\n\n      if (shouldCombineSets(previousSet, nextSet)) {\n        thisSet = previousSet + ',' + nextSet;\n        i++; // Move the index forward to skip the next iteration since we're combining sets\n        output.pop();\n      }\n    }\n\n    output.push(processSet(thisSet));\n  }\n\n  return output.join('');\n};\n\nexport const countOccurrence = (string: string, substring: string): number => {\n  return Math.max(0, string.split(substring).length - 1);\n};\n\nconst shouldCombineSets = (previousSet: string, nextSet: string): boolean => {\n  const prevCount = countOccurrence(previousSet, '~');\n  const nextCount = countOccurrence(nextSet, '~');\n\n  return prevCount === 1 && nextCount === 1;\n};\n\nconst processSet = (input: string): string => {\n  const tildeCount = countOccurrence(input, '~');\n  let hasStartingTilde = false;\n\n  if (tildeCount <= 1) {\n    return input;\n  }\n\n  // If there is an odd number of tildes, and the input starts with a tilde, we need to remove it and add it back in later\n  if (tildeCount % 2 !== 0 && input.startsWith('~')) {\n    input = input.substring(1);\n    hasStartingTilde = true;\n  }\n\n  const chars = [...input];\n\n  let first = chars.indexOf('~');\n  let last = chars.lastIndexOf('~');\n\n  while (first !== -1 && last !== -1 && first !== last) {\n    chars[first] = '<';\n    chars[last] = '>';\n\n    first = chars.indexOf('~');\n    last = chars.lastIndexOf('~');\n  }\n\n  // Add the starting tilde back in if we removed it\n  if (hasStartingTilde) {\n    chars.unshift('~');\n  }\n\n  return chars.join('');\n};\n\n// TODO: find a better method for detecting support. This interface was added in the MathML 4 spec.\n// Firefox versions between [4,71] (0.47%) and Safari versions between [5,13.4] (0.17%) don't have this interface implemented but MathML is supported\nexport const isMathMLSupported = () => window.MathMLElement !== undefined;\n\nexport const katexRegex = /\\$\\$(.*)\\$\\$/g;\n\n/**\n * Whether or not a text has KaTeX delimiters\n *\n * @param text - The text to test\n * @returns Whether or not the text has KaTeX delimiters\n */\nexport const hasKatex = (text: string): boolean => (text.match(katexRegex)?.length ?? 0) > 0;\n\n/**\n * Computes the minimum dimensions needed to display a div containing MathML\n *\n * @param text - The text to test\n * @param config - Configuration for Mermaid\n * @returns Object containing \\{width, height\\}\n */\nexport const calculateMathMLDimensions = async (text: string, config: MermaidConfig) => {\n  text = await renderKatex(text, config);\n  const divElem = document.createElement('div');\n  divElem.innerHTML = text;\n  divElem.id = 'katex-temp';\n  divElem.style.visibility = 'hidden';\n  divElem.style.position = 'absolute';\n  divElem.style.top = '0';\n  const body = document.querySelector('body');\n  body?.insertAdjacentElement('beforeend', divElem);\n  const dim = { width: divElem.clientWidth, height: divElem.clientHeight };\n  divElem.remove();\n  return dim;\n};\n\n/**\n * Attempts to render and return the KaTeX portion of a string with MathML\n *\n * @param text - The text to test\n * @param config - Configuration for Mermaid\n * @returns String containing MathML if KaTeX is supported, or an error message if it is not and stylesheets aren't present\n */\nexport const renderKatex = async (text: string, config: MermaidConfig): Promise<string> => {\n  if (!hasKatex(text)) {\n    return text;\n  }\n\n  if (!(isMathMLSupported() || config.legacyMathML || config.forceLegacyMathML)) {\n    return text.replace(katexRegex, 'MathML is unsupported in this environment.');\n  }\n\n  const { default: katex } = await import('katex');\n  const outputMode =\n    config.forceLegacyMathML || (!isMathMLSupported() && config.legacyMathML)\n      ? 'htmlAndMathml'\n      : 'mathml';\n  return text\n    .split(lineBreakRegex)\n    .map((line) =>\n      hasKatex(line)\n        ? `<div style=\"display: flex; align-items: center; justify-content: center; white-space: nowrap;\">${line}</div>`\n        : `<div>${line}</div>`\n    )\n    .join('')\n    .replace(katexRegex, (_, c) =>\n      katex\n        .renderToString(c, {\n          throwOnError: true,\n          displayMode: true,\n          output: outputMode,\n        })\n        .replace(/\\n/g, ' ')\n        .replace(/<annotation.*<\\/annotation>/g, '')\n    );\n};\n\nexport default {\n  getRows,\n  sanitizeText,\n  sanitizeTextOrArray,\n  hasBreaks,\n  splitBreaks,\n  lineBreakRegex,\n  removeScript,\n  getUrl,\n  evaluate,\n  getMax,\n  getMin,\n};\n", "import { log } from './logger.js';\n\n/**\n * Applies d3 attributes\n *\n * @param {any} d3Elem D3 Element to apply the attributes onto\n * @param {[string, string][]} attrs Object.keys equivalent format of key to value mapping of attributes\n */\nconst d3Attrs = function (d3Elem, attrs) {\n  for (let attr of attrs) {\n    d3Elem.attr(attr[0], attr[1]);\n  }\n};\n\n/**\n * Gives attributes for an SVG's size given arguments\n *\n * @param {number} height The height of the SVG\n * @param {number} width The width of the SVG\n * @param {boolean} useMaxWidth Whether or not to use max-width and set width to 100%\n * @returns {Map<'height' | 'width' | 'style', string>} Attributes for the SVG\n */\nexport const calculateSvgSizeAttrs = function (height, width, useMaxWidth) {\n  let attrs = new Map();\n  if (useMaxWidth) {\n    attrs.set('width', '100%');\n    attrs.set('style', `max-width: ${width}px;`);\n  } else {\n    attrs.set('height', height);\n    attrs.set('width', width);\n  }\n  return attrs;\n};\n\n/**\n * Applies attributes from `calculateSvgSizeAttrs`\n *\n * @param {import('./diagram-api/types.js').SVG} svgElem The SVG Element to configure\n * @param {number} height The height of the SVG\n * @param {number} width The width of the SVG\n * @param {boolean} useMaxWidth Whether or not to use max-width and set width to 100%\n */\nexport const configureSvgSize = function (svgElem, height, width, useMaxWidth) {\n  const attrs = calculateSvgSizeAttrs(height, width, useMaxWidth);\n  d3Attrs(svgElem, attrs);\n};\n\n// TODO v11: Remove the graph parameter. It is not used.\nexport const setupGraphViewbox = function (graph, svgElem, padding, useMaxWidth) {\n  const svgBounds = svgElem.node().getBBox();\n  const sWidth = svgBounds.width;\n  const sHeight = svgBounds.height;\n\n  log.info(`SVG bounds: ${sWidth}x${sHeight}`, svgBounds);\n\n  let width = 0;\n  let height = 0;\n  log.info(`Graph bounds: ${width}x${height}`, graph);\n\n  width = sWidth + padding * 2;\n  height = sHeight + padding * 2;\n\n  log.info(`Calculated bounds: ${width}x${height}`);\n  configureSvgSize(svgElem, height, width, useMaxWidth);\n\n  // Ensure the viewBox includes the whole svgBounds area with extra space for padding\n  const vBox = `${svgBounds.x - padding} ${svgBounds.y - padding} ${\n    svgBounds.width + 2 * padding\n  } ${svgBounds.height + 2 * padding}`;\n\n  svgElem.attr('viewBox', vBox);\n};\n", "import type { FlowChartStyleOptions } from './diagrams/flowchart/styles.js';\nimport { log } from './logger.js';\nimport type { DiagramStylesProvider } from './diagram-api/types.js';\n\nconst themes: Record<string, DiagramStylesProvider> = {};\n\nconst getStyles = (\n  type: string,\n  userStyles: string,\n  options: {\n    fontFamily: string;\n    fontSize: string;\n    textColor: string;\n    errorBkgColor: string;\n    errorTextColor: string;\n    lineColor: string;\n  } & FlowChartStyleOptions\n) => {\n  let diagramStyles = '';\n  if (type in themes && themes[type]) {\n    diagramStyles = themes[type](options);\n  } else {\n    log.warn(`No theme found for ${type}`);\n  }\n  return ` & {\n    font-family: ${options.fontFamily};\n    font-size: ${options.fontSize};\n    fill: ${options.textColor}\n  }\n  @keyframes edge-animation-frame {\n    from {\n      stroke-dashoffset: 0;\n    }\n  }\n  @keyframes dash {\n    to {\n      stroke-dashoffset: 0;\n    }\n  }\n  & .edge-animation-slow {\n    stroke-dasharray: 9,5 !important;\n    stroke-dashoffset: 900;\n    animation: dash 50s linear infinite;\n    stroke-linecap: round;\n  }\n  & .edge-animation-fast {\n    stroke-dasharray: 9,5 !important;\n    stroke-dashoffset: 900;\n    animation: dash 20s linear infinite;\n    stroke-linecap: round;\n  }\n  /* Classes common for multiple diagrams */\n\n  & .error-icon {\n    fill: ${options.errorBkgColor};\n  }\n  & .error-text {\n    fill: ${options.errorTextColor};\n    stroke: ${options.errorTextColor};\n  }\n\n  & .edge-thickness-normal {\n    stroke-width: 1px;\n  }\n  & .edge-thickness-thick {\n    stroke-width: 3.5px\n  }\n  & .edge-pattern-solid {\n    stroke-dasharray: 0;\n  }\n  & .edge-thickness-invisible {\n    stroke-width: 0;\n    fill: none;\n  }\n  & .edge-pattern-dashed{\n    stroke-dasharray: 3;\n  }\n  .edge-pattern-dotted {\n    stroke-dasharray: 2;\n  }\n\n  & .marker {\n    fill: ${options.lineColor};\n    stroke: ${options.lineColor};\n  }\n  & .marker.cross {\n    stroke: ${options.lineColor};\n  }\n\n  & svg {\n    font-family: ${options.fontFamily};\n    font-size: ${options.fontSize};\n  }\n   & p {\n    margin: 0\n   }\n\n  ${diagramStyles}\n\n  ${userStyles}\n`;\n};\n\nexport const addStylesForDiagram = (type: string, diagramTheme?: DiagramStylesProvider): void => {\n  if (diagramTheme !== undefined) {\n    themes[type] = diagramTheme;\n  }\n};\n\nexport default getStyles;\n", "import { sanitizeText as _sanitizeText } from './common.js';\nimport { getConfig } from '../../config.js';\n\nlet accTitle = '';\nlet diagramTitle = '';\nlet accDescription = '';\n\nconst sanitizeText = (txt: string): string => _sanitizeText(txt, getConfig());\n\nexport const clear = (): void => {\n  accTitle = '';\n  accDescription = '';\n  diagramTitle = '';\n};\n\nexport const setAccTitle = (txt: string): void => {\n  accTitle = sanitizeText(txt).replace(/^\\s+/g, '');\n};\n\nexport const getAccTitle = (): string => accTitle;\n\nexport const setAccDescription = (txt: string): void => {\n  accDescription = sanitizeText(txt).replace(/\\n\\s+/g, '\\n');\n};\n\nexport const getAccDescription = (): string => accDescription;\n\nexport const setDiagramTitle = (txt: string): void => {\n  diagramTitle = sanitizeText(txt);\n};\n\nexport const getDiagramTitle = (): string => diagramTitle;\n", "import { addDetector } from './detectType.js';\nimport { log as _log, setLogLevel as _setLogLevel } from '../logger.js';\nimport {\n  getConfig as _getConfig,\n  setConfig as _setConfig,\n  defaultConfig as _defaultConfig,\n  setSiteConfig as _setSiteConfig,\n} from '../config.js';\nimport { sanitizeText as _sanitizeText } from '../diagrams/common/common.js';\nimport { setupGraphViewbox as _setupGraphViewbox } from '../setupGraphViewbox.js';\nimport { addStylesForDiagram } from '../styles.js';\nimport type { DiagramDefinition, DiagramDetector } from './types.js';\nimport * as _commonDb from '../diagrams/common/commonDb.js';\n\n/*\n  Packaging and exposing resources for external diagrams so that they can import\n  diagramAPI and have access to select parts of mermaid common code required to\n  create diagrams working like the internal diagrams.\n*/\nexport const log = _log;\nexport const setLogLevel = _setLogLevel;\nexport const getConfig = _getConfig;\nexport const setConfig = _setConfig;\nexport const defaultConfig = _defaultConfig;\nexport const setSiteConfig = _setSiteConfig;\nexport const sanitizeText = (text: string) => _sanitizeText(text, getConfig());\nexport const setupGraphViewbox = _setupGraphViewbox;\nexport const getCommonDb = () => {\n  return _commonDb;\n};\n\nconst diagrams: Record<string, DiagramDefinition> = {};\nexport type Detectors = Record<string, DiagramDetector>;\n\n/**\n * Registers the given diagram with Mermaid.\n *\n * Can be used for third-party custom diagrams.\n *\n * @param id - A unique ID for the given diagram.\n * @param diagram - The diagram definition.\n * @param detector - Function that returns `true` if a given mermaid text is this diagram definition.\n */\nexport const registerDiagram = (\n  id: string,\n  diagram: DiagramDefinition,\n  detector?: DiagramDetector\n) => {\n  if (diagrams[id]) {\n    log.warn(`Diagram with id ${id} already registered. Overwriting.`);\n  }\n  diagrams[id] = diagram;\n  if (detector) {\n    addDetector(id, detector);\n  }\n  addStylesForDiagram(id, diagram.styles);\n\n  diagram.injectUtils?.(\n    log,\n    setLogLevel,\n    getConfig,\n    sanitizeText,\n    setupGraphViewbox,\n    getCommonDb(),\n    () => {\n      // parseDirective is removed in https://github.com/mermaid-js/mermaid/pull/4759.\n      // This is a no-op for legacy support.\n    }\n  );\n};\n\nexport const getDiagram = (name: string): DiagramDefinition => {\n  if (name in diagrams) {\n    return diagrams[name];\n  }\n  throw new DiagramNotFoundError(name);\n};\n\nexport class DiagramNotFoundError extends Error {\n  constructor(name: string) {\n    super(`Diagram ${name} not found.`);\n  }\n}\n"], "mappings": ";;;;;;;;AAIA,OAAO,WAAW;AAIX,IAAM,SAAmC;AAAA,EAC9C,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AACT;AAEO,IAAM,MAAuD;AAAA,EAClE,OAAO,2BAAI,UAAiB;AAAA,EAAC,GAAtB;AAAA,EACP,OAAO,2BAAI,UAAiB;AAAA,EAAC,GAAtB;AAAA,EACP,MAAM,2BAAI,UAAiB;AAAA,EAAC,GAAtB;AAAA,EACN,MAAM,2BAAI,UAAiB;AAAA,EAAC,GAAtB;AAAA,EACN,OAAO,2BAAI,UAAiB;AAAA,EAAC,GAAtB;AAAA,EACP,OAAO,2BAAI,UAAiB;AAAA,EAAC,GAAtB;AACT;AAOO,IAAM,cAAc,gCAAU,QAAsC,SAAS;AAClF,MAAI,eAAuB,OAAO;AAClC,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,MAAM,YAAY,KAAK,QAAQ;AACjC,qBAAe,OAAO,KAAK;AAAA,IAC7B;AAAA,EACF,WAAW,OAAO,UAAU,UAAU;AACpC,mBAAe;AAAA,EACjB;AACA,MAAI,QAAQ,MAAM;AAAA,EAAC;AACnB,MAAI,QAAQ,MAAM;AAAA,EAAC;AACnB,MAAI,OAAO,MAAM;AAAA,EAAC;AAClB,MAAI,OAAO,MAAM;AAAA,EAAC;AAClB,MAAI,QAAQ,MAAM;AAAA,EAAC;AACnB,MAAI,QAAQ,MAAM;AAAA,EAAC;AAEnB,MAAI,gBAAgB,OAAO,OAAO;AAChC,QAAI,QAAQ,QAAQ,QAChB,QAAQ,MAAM,KAAK,SAAS,OAAO,OAAO,GAAG,eAAe,IAC5D,QAAQ,IAAI,KAAK,SAAS,YAAY,OAAO,OAAO,CAAC;AAAA,EAC3D;AACA,MAAI,gBAAgB,OAAO,OAAO;AAChC,QAAI,QAAQ,QAAQ,QAChB,QAAQ,MAAM,KAAK,SAAS,OAAO,OAAO,GAAG,eAAe,IAC5D,QAAQ,IAAI,KAAK,SAAS,YAAY,OAAO,OAAO,CAAC;AAAA,EAC3D;AACA,MAAI,gBAAgB,OAAO,MAAM;AAC/B,QAAI,OAAO,QAAQ,OACf,QAAQ,KAAK,KAAK,SAAS,OAAO,MAAM,GAAG,eAAe,IAC1D,QAAQ,IAAI,KAAK,SAAS,YAAY,OAAO,MAAM,CAAC;AAAA,EAC1D;AACA,MAAI,gBAAgB,OAAO,MAAM;AAC/B,QAAI,OAAO,QAAQ,OACf,QAAQ,KAAK,KAAK,SAAS,OAAO,MAAM,GAAG,kBAAkB,IAC7D,QAAQ,IAAI,KAAK,SAAS,YAAY,OAAO,MAAM,CAAC;AAAA,EAC1D;AACA,MAAI,gBAAgB,OAAO,OAAO;AAChC,QAAI,QAAQ,QAAQ,QAChB,QAAQ,MAAM,KAAK,SAAS,OAAO,OAAO,GAAG,mBAAmB,IAChE,QAAQ,IAAI,KAAK,SAAS,YAAY,OAAO,OAAO,CAAC;AAAA,EAC3D;AACA,MAAI,gBAAgB,OAAO,OAAO;AAChC,QAAI,QAAQ,QAAQ,QAChB,QAAQ,MAAM,KAAK,SAAS,OAAO,OAAO,GAAG,mBAAmB,IAChE,QAAQ,IAAI,KAAK,SAAS,YAAY,OAAO,OAAO,CAAC;AAAA,EAC3D;AACF,GA9C2B;AAsD3B,IAAM,SAAS,wBAAC,UAAuC;AACrD,QAAM,OAAO,MAAM,EAAE,OAAO,QAAQ;AACpC,SAAO,KAAK,IAAI,MAAM,KAAK;AAC7B,GAHe;;;AChFR,IAAM,mBAAmB;AAEzB,IAAM,iBACX;AAEK,IAAM,kBAAkB;;;ACVxB,IAAM,sBAAN,cAAkC,MAAM;AAAA,EAA/C,OAA+C;AAAA;AAAA;AAAA,EAC7C,YAAY,SAAiB;AAC3B,UAAM,OAAO;AACb,SAAK,OAAO;AAAA,EACd;AACF;;;ACMO,IAAM,YAA4C,CAAC;AAwBnD,IAAM,aAAa,gCAAU,MAAcA,SAAgC;AAChF,SAAO,KACJ,QAAQ,kBAAkB,EAAE,EAC5B,QAAQ,gBAAgB,EAAE,EAC1B,QAAQ,iBAAiB,IAAI;AAChC,aAAW,CAAC,KAAK,EAAE,SAAS,CAAC,KAAK,OAAO,QAAQ,SAAS,GAAG;AAC3D,UAAM,UAAU,SAAS,MAAMA,OAAM;AACrC,QAAI,SAAS;AACX,aAAO;AAAA,IACT;AAAA,EACF;AAEA,QAAM,IAAI;AAAA,IACR,mEAAmE,IAAI;AAAA,EACzE;AACF,GAf0B;AA8BnB,IAAM,6BAA6B,2BAAIC,cAA0C;AACtF,aAAW,EAAE,IAAI,UAAU,OAAO,KAAKA,WAAU;AAC/C,gBAAY,IAAI,UAAU,MAAM;AAAA,EAClC;AACF,GAJ0C;AAMnC,IAAM,cAAc,wBAAC,KAAa,UAA2B,WAA2B;AAC7F,MAAI,UAAU,GAAG,GAAG;AAClB,QAAI,KAAK,qBAAqB,GAAG,+BAA+B;AAAA,EAClE;AACA,YAAU,GAAG,IAAI,EAAE,UAAU,OAAO;AACpC,MAAI,MAAM,qBAAqB,GAAG,SAAS,SAAS,iBAAiB,EAAE,EAAE;AAC3E,GAN2B;AAQpB,IAAM,mBAAmB,wBAAC,QAAgB;AAC/C,SAAO,UAAU,GAAG,EAAE;AACxB,GAFgC;;;ACpDhC,IAAM,kBAAkB,wBACtB,KACA,KACA,EAAE,QAAQ,GAAG,UAAU,MAAM,IAA2C,CAAC,MACjE;AACR,QAAMC,UAA8C,EAAE,OAAO,QAAQ;AACrE,MAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,QAAQ,GAAG,GAAG;AAC7C,QAAI,QAAQ,CAAC,MAAM,gBAAgB,KAAK,GAAGA,OAAM,CAAC;AAClD,WAAO;AAAA,EACT,WAAW,MAAM,QAAQ,GAAG,KAAK,MAAM,QAAQ,GAAG,GAAG;AACnD,QAAI,QAAQ,CAAC,MAAM;AACjB,UAAI,CAAC,IAAI,SAAS,CAAC,GAAG;AACpB,YAAI,KAAK,CAAC;AAAA,MACZ;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,UAAa,SAAS,GAAG;AACnC,QAAI,QAAQ,UAAa,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,UAAU;AAC3F,aAAO,OAAO,OAAO,KAAK,GAAG;AAAA,IAC/B,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,QAAQ,UAAa,OAAO,QAAQ,YAAY,OAAO,QAAQ,UAAU;AAC3E,WAAO,KAAK,GAAG,EAAE,QAAQ,CAAC,QAAQ;AAChC,UACE,OAAO,IAAI,GAAG,MAAM,aACnB,IAAI,GAAG,MAAM,UAAa,OAAO,IAAI,GAAG,MAAM,WAC/C;AACA,YAAI,IAAI,GAAG,MAAM,QAAW;AAC1B,cAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,QAC7C;AACA,YAAI,GAAG,IAAI,gBAAgB,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,EAAE,OAAO,QAAQ,GAAG,QAAQ,CAAC;AAAA,MAC9E,WAAW,WAAY,OAAO,IAAI,GAAG,MAAM,YAAY,OAAO,IAAI,GAAG,MAAM,UAAW;AACpF,YAAI,GAAG,IAAI,IAAI,GAAG;AAAA,MACpB;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT,GAxCwB;AA0CxB,IAAO,0BAAQ;;;ACrEf,SAAS,UAAAC,SAAQ,QAAQ,QAAQ,QAAQ,eAAe;;;ACKjD,IAAM,iCAAiC;AACvC,IAAM,kCAAkC;;;ACN/C,SAAS,cAAc;AAEhB,IAAM,WAAW,wBAAC,KAAK,aAC5B,WAAW,OAAO,KAAK,EAAE,GAAG,KAAK,GAAG,GAAG,CAAC,IAAI,OAAO,KAAK,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC,GADpD;;;AFKxB,IAAM,QAAN,MAAY;AAAA,EAPZ,OAOY;AAAA;AAAA;AAAA,EACV,cAAc;AAMZ,SAAK,aAAa;AAElB,SAAK,eAAe;AAEpB,SAAK,eAAe;AACpB,SAAK,gBAAgB;AAErB,SAAK,oBAAoB;AAIzB,SAAK,aAAa;AAClB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,eAAe;AAIb,SAAK,mBAAmB,KAAK,qBAAqB,KAAK,WAAW,SAAS;AAC3E,SAAK,iBAAiB,KAAK,kBAAkBC,QAAO,KAAK,cAAc,EAAE,GAAG,KAAK,CAAC;AAClF,SAAK,gBAAgB,KAAK,iBAAiBA,QAAO,KAAK,cAAc,EAAE,GAAG,KAAK,GAAG,EAAE,CAAC;AAErF,SAAK,qBAAqB,KAAK,sBAAsB,SAAS,KAAK,cAAc,KAAK,QAAQ;AAC9F,SAAK,uBACH,KAAK,wBAAwB,SAAS,KAAK,gBAAgB,KAAK,QAAQ;AAC1E,SAAK,sBACH,KAAK,uBAAuB,SAAS,KAAK,eAAe,KAAK,QAAQ;AACxE,SAAK,kBAAkB,KAAK,mBAAmB,SAAS,KAAK,cAAc,KAAK,QAAQ;AACxF,SAAK,eAAe,KAAK,gBAAgB;AACzC,SAAK,gBAAgB,KAAK,iBAAiB;AAE3C,SAAK,qBAAqB,KAAK,sBAAsB,OAAO,KAAK,cAAc;AAC/E,SAAK,oBAAoB,KAAK,qBAAqB,OAAO,KAAK,aAAa;AAC5E,SAAK,YAAY,KAAK,aAAa,OAAO,KAAK,UAAU;AACzD,SAAK,iBAAiB,KAAK,kBAAkB,OAAO,KAAK,UAAU;AACnE,SAAK,YAAY,KAAK,aAAa,KAAK;AAGxC,SAAK,UAAU,KAAK,WAAW,KAAK;AAGpC,SAAK,UAAU,KAAK,WAAW,KAAK;AACpC,SAAK,UAAU,KAAK,WAAW,KAAK;AACpC,SAAK,aAAa,KAAK,cAAc,KAAK;AAC1C,SAAK,aAAa,KAAK,cAAc,KAAK;AAC1C,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,mBAAmB,KAAK,oBAAoB,KAAK;AACtD,SAAK,aAAa,KAAK,cAAc,KAAK;AAC1C,SAAK,sBACH,KAAK,wBACJ,KAAK,WAAW,OAAO,KAAK,gBAAgB,EAAE,IAAI,KAAK;AAC1D,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAIhD,SAAK,cAAc,KAAK,eAAe,KAAK;AAC5C,SAAK,WAAW,KAAK,YAAY,KAAK;AACtC,SAAK,iBAAiB,KAAK,kBAAkB,KAAK;AAClD,SAAK,iBAAiB,KAAK,kBAAkB,KAAK;AAClD,SAAK,mBAAmB,KAAK,oBAAoB,KAAK;AACtD,SAAK,cAAc,KAAK,eAAe,KAAK;AAC5C,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,sBAAsB,KAAK,uBAAuB,KAAK;AAC5D,SAAK,iBAAiB,KAAK,kBAAkB,KAAK;AAClD,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,wBAAwB,KAAK,yBAAyB,OAAO,KAAK,gBAAgB,EAAE;AACzF,SAAK,qBAAqB,KAAK,sBAAsB,KAAK;AAC1D,SAAK,sBAAsB,KAAK,uBAAuB,OAAO,KAAK,SAAS;AAI5E,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,qBAAqB,KAAK,sBAAsB;AACrD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,mBAAmB,KAAK,oBAAoB,KAAK;AACtD,SAAK,kBAAkB,KAAK,mBAAmB;AAC/C,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,eAAe,KAAK,gBAAgB,KAAK;AAC9C,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,qBAAqB,KAAK,sBAAsB,QAAQ,KAAK,cAAc,EAAE;AAClF,SAAK,YAAY,KAAK,aAAa;AACnC,SAAK,mBAAmB,KAAK,oBAAoB;AACjD,SAAK,sBAAsB,KAAK,uBAAuB;AACvD,SAAK,kBAAkB,KAAK,mBAAmB;AAC/C,SAAK,eAAe,KAAK,gBAAgB;AACzC,SAAK,iBAAiB,KAAK,kBAAkB;AAC7C,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,uBAAuB,KAAK,wBAAwB,KAAK;AAC9D,SAAK,qBAAqB,KAAK,sBAAsB,KAAK;AAC1D,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,oBAAoB,KAAK,qBAAqB,KAAK;AACxD,SAAK,yBAAyB,KAAK,0BAA0B;AAI7D,SAAK,eAAe,KAAK,gBAAgB,KAAK;AAC9C,SAAK,YAAY,KAAK,aAAa,KAAK;AAIxC,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,KAAK,UAAU,OAAO,KAAK,SAAS,CAAC,KAAK;AACxD,WAAK,UAAU,KAAK,WAAW,OAAO,KAAK,SAAS,EAAE;AAAA,IACxD,OAAO;AACL,WAAK,SAAS,KAAK,UAAU,QAAQ,KAAK,SAAS,EAAE,KAAK;AAC1D,WAAK,UAAU,KAAK,WAAW,QAAQ,KAAK,SAAS,CAAC;AAAA,IACxD;AAGA,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,uBAAuB,KAAK,wBAAwB,KAAK;AAE9D,SAAK,kBAAkB,KAAK,mBAAmB,KAAK,YAAY,KAAK;AAErE,SAAK,WAAW,KAAK,YAAY,KAAK;AACtC,SAAK,uBAAuB,KAAK,wBAAwB,KAAK;AAC9D,SAAK,sBAAsB,KAAK,uBAAuB,KAAK,cAAc,KAAK;AAC/E,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,2BAA2B,KAAK,4BAA4B,KAAK;AACtE,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,qBAAqB,KAAK;AAC/B,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,iBAAiB,KAAK,kBAAkB,KAAK;AAClD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,oBAAoB,KAAK;AAI9B,SAAK,UAAU,KAAK,WAAW,KAAK;AACpC,SAAK,UAAU,KAAK,WAAW,KAAK;AACpC,SAAK,UAAU,KAAK,WAAW,KAAK;AACpC,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,CAAC;AAClE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,CAAC;AAClE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,CAAC;AAClE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACnE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACnE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC;AAC3E,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACnE,SAAK,WAAW,KAAK,YAAYA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACrE,SAAK,WAAW,KAAK,YAAYA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACrE,QAAI,KAAK,UAAU;AACjB,eAAS,IAAI,GAAG,IAAI,KAAK,mBAAmB,KAAK;AAC/C,aAAK,WAAW,CAAC,IAAI,OAAO,KAAK,WAAW,CAAC,GAAG,EAAE;AAAA,MACpD;AAAA,IACF,OAAO;AACL,eAAS,IAAI,GAAG,IAAI,KAAK,mBAAmB,KAAK;AAC/C,aAAK,WAAW,CAAC,IAAI,OAAO,KAAK,WAAW,CAAC,GAAG,EAAE;AAAA,MACpD;AAAA,IACF;AAGA,aAAS,IAAI,GAAG,IAAI,KAAK,mBAAmB,KAAK;AAC/C,WAAK,cAAc,CAAC,IAAI,KAAK,cAAc,CAAC,KAAK,OAAO,KAAK,WAAW,CAAC,CAAC;AAAA,IAC5E;AAEA,aAAS,IAAI,GAAG,IAAI,KAAK,mBAAmB,KAAK;AAC/C,UAAI,KAAK,UAAU;AACjB,aAAK,eAAe,CAAC,IAAI,KAAK,eAAe,CAAC,KAAK,QAAQ,KAAK,WAAW,CAAC,GAAG,EAAE;AAAA,MACnF,OAAO;AACL,aAAK,eAAe,CAAC,IAAI,KAAK,eAAe,CAAC,KAAK,OAAO,KAAK,WAAW,CAAC,GAAG,EAAE;AAAA,MAClF;AAAA,IACF;AAGA,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AAEpD,aAAS,IAAI,GAAG,IAAI,KAAK,mBAAmB,KAAK;AAC/C,WAAK,gBAAgB,CAAC,IAAI,KAAK,gBAAgB,CAAC,KAAK,KAAK;AAAA,IAC5D;AAEA,UAAM,aAAa,KAAK,WAAW,KAAK;AACxC,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,WAAK,YAAY,CAAC,IAChB,KAAK,YAAY,CAAC,KAClBA,QAAO,KAAK,SAAS,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,cAAc,IAAI,IAAI,GAAG,CAAC;AACtE,WAAK,gBAAgB,CAAC,IACpB,KAAK,gBAAgB,CAAC,KACtBA,QAAO,KAAK,SAAS,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,cAAc,IAAI,IAAI,GAAG,CAAC;AAAA,IACxE;AAGA,SAAK,YAAY,KAAK,aAAa,KAAK;AAGxC,SAAK,YAAY,KAAK,aAAa,KAAK;AACxC,SAAK,YAAY,KAAK,aAAa,KAAK;AACxC,SAAK,YAAY,KAAK,aAAaA,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,CAAC;AACtE,SAAK,YAAY,KAAK,aAAaA,QAAO,KAAK,gBAAgB,EAAE,GAAG,GAAG,CAAC;AACxE,SAAK,YAAY,KAAK,aAAaA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACvE,SAAK,YAAY,KAAK,aAAaA,QAAO,KAAK,gBAAgB,EAAE,GAAG,IAAI,CAAC;AACzE,SAAK,YAAY,KAAK,aAAaA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACvE,SAAK,YAAY,KAAK,aAAaA,QAAO,KAAK,gBAAgB,EAAE,GAAG,IAAI,CAAC;AAGzE,SAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,SAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,SAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AAC7D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,gBAAgB,EAAE,GAAG,IAAI,CAAC;AAC/D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,eAAe,EAAE,GAAG,IAAI,CAAC;AAC9D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAK,GAAG,IAAI,CAAC;AACrE,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC;AACrE,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,KAAK,GAAG,EAAE,CAAC;AACnE,SAAK,QAAQ,KAAK,SAASA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAK,GAAG,IAAI,CAAC;AACvE,SAAK,QAAQ,KAAK,SAASA,QAAO,KAAK,cAAc,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC;AACvE,SAAK,QAAQ,KAAK,SAASA,QAAO,KAAK,cAAc,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC;AACvE,SAAK,mBAAmB,KAAK,oBAAoB;AACjD,SAAK,oBAAoB,KAAK,qBAAqB,KAAK;AACxD,SAAK,qBAAqB,KAAK,sBAAsB;AACrD,SAAK,sBAAsB,KAAK,uBAAuB,KAAK;AAC5D,SAAK,oBAAoB,KAAK,qBAAqB;AACnD,SAAK,qBAAqB,KAAK,sBAAsB,KAAK;AAC1D,SAAK,iBAAiB,KAAK,kBAAkB;AAC7C,SAAK,iBAAiB,KAAK,kBAAkB;AAC7C,SAAK,sBAAsB,KAAK,uBAAuB;AACvD,SAAK,sBAAsB,KAAK,uBAAuB;AACvD,SAAK,aAAa,KAAK,cAAc;AAGrC,SAAK,QAAQ;AAAA,MACX,WAAW,KAAK,OAAO,aAAa,KAAK;AAAA,MACzC,iBAAiB,KAAK,OAAO,mBAAmB;AAAA,MAChD,mBAAmB,KAAK,OAAO,qBAAqB;AAAA,MACpD,cAAc,KAAK,OAAO,gBAAgB;AAAA,MAC1C,kBAAkB,KAAK,OAAO,oBAAoB;AAAA,MAClD,gBAAgB,KAAK,OAAO,kBAAkB;AAAA,MAC9C,sBAAsB,KAAK,OAAO,wBAAwB;AAAA,MAC1D,kBAAkB,KAAK,OAAO,oBAAoB;AAAA,MAClD,eAAe,KAAK,OAAO,iBAAiB;AAAA,MAC5C,gBAAgB,KAAK,OAAO,kBAAkB;AAAA,IAChD;AAGA,SAAK,gBAAgB,KAAK,iBAAiB;AAC3C,SAAK,qBAAqB,KAAK,sBAAsB;AACrD,SAAK,gBAAgB,KAAK,iBAAiB;AAC3C,SAAK,uBAAuB,KAAK,wBAAwB;AACzD,SAAK,uBAAuB,KAAK,wBAAwB;AAGzD,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,gBAAgB,KAAK,iBAAiBA,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;AACzF,SAAK,gBAAgB,KAAK,iBAAiBA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;AAC5F,SAAK,gBAAgB,KAAK,iBAAiBA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;AAC5F,SAAK,oBAAoB,KAAK,qBAAqB,KAAK;AACxD,SAAK,oBACH,KAAK,qBAAqBA,QAAO,KAAK,kBAAkB,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;AACjF,SAAK,oBACH,KAAK,qBAAqBA,QAAO,KAAK,kBAAkB,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;AACpF,SAAK,oBACH,KAAK,qBAAqBA,QAAO,KAAK,kBAAkB,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;AACpF,SAAK,oBACH,KAAK,qBAAqB,OAAO,KAAK,aAAa,IAC/C,QAAQ,KAAK,aAAa,IAC1B,OAAO,KAAK,aAAa;AAC/B,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,mCACH,KAAK,oCAAoC,KAAK;AAChD,SAAK,mCACH,KAAK,oCAAoC,KAAK;AAChD,SAAK,oBAAoB,KAAK,qBAAqB,KAAK;AAGxD,SAAK,UAAU;AAAA,MACb,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,YAAY,KAAK,SAAS,cAAc,KAAK;AAAA,MAC7C,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,gBAAgB,KAAK,SAAS,kBAAkB,KAAK;AAAA,MACrD,gBAAgB,KAAK,SAAS,kBAAkB,KAAK;AAAA,MACrD,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,gBAAgB,KAAK,SAAS,kBAAkB,KAAK;AAAA,MACrD,gBAAgB,KAAK,SAAS,kBAAkB,KAAK;AAAA,MACrD,kBACE,KAAK,SAAS,oBACd;AAAA,IACJ;AAGA,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,yBAAyB,KAAK,0BAA0B,KAAK;AAClE,SAAK,wBAAwB,KAAK,yBAAyB;AAC3D,SAAK,uBAAuB,KAAK,wBAAwB,KAAK;AAC9D,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,0BACH,KAAK,4BACJ,KAAK,WAAW,OAAO,KAAK,gBAAgB,EAAE,IAAI,KAAK;AAC1D,SAAK,qBAAqB,KAAK,sBAAsB,KAAK;AAG1D,SAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,SAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,SAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AAC7D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AAC7D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AAC7D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,GAAI,CAAC;AAC7D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAK,CAAC;AAC9D,QAAI,KAAK,UAAU;AACjB,WAAK,OAAO,QAAQ,KAAK,MAAM,EAAE;AACjC,WAAK,OAAO,QAAQ,KAAK,MAAM,EAAE;AACjC,WAAK,OAAO,QAAQ,KAAK,MAAM,EAAE;AACjC,WAAK,OAAO,QAAQ,KAAK,MAAM,EAAE;AACjC,WAAK,OAAO,QAAQ,KAAK,MAAM,EAAE;AACjC,WAAK,OAAO,QAAQ,KAAK,MAAM,EAAE;AACjC,WAAK,OAAO,QAAQ,KAAK,MAAM,EAAE;AACjC,WAAK,OAAO,QAAQ,KAAK,MAAM,EAAE;AAAA,IACnC,OAAO;AACL,WAAK,OAAO,OAAO,KAAK,MAAM,EAAE;AAChC,WAAK,OAAO,OAAO,KAAK,MAAM,EAAE;AAChC,WAAK,OAAO,OAAO,KAAK,MAAM,EAAE;AAChC,WAAK,OAAO,OAAO,KAAK,MAAM,EAAE;AAChC,WAAK,OAAO,OAAO,KAAK,MAAM,EAAE;AAChC,WAAK,OAAO,OAAO,KAAK,MAAM,EAAE;AAChC,WAAK,OAAO,OAAO,KAAK,MAAM,EAAE;AAChC,WAAK,OAAO,OAAO,KAAK,MAAM,EAAE;AAAA,IAClC;AACA,SAAK,UAAU,KAAK,WAAW,OAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAW,OAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAW,OAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAW,OAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAW,OAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAW,OAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAW,OAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAW,OAAO,KAAK,IAAI;AAC/C,SAAK,mBACH,KAAK,qBAAqB,KAAK,WAAW,UAAU,KAAK;AAC3D,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AAEpD,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,qBAAqB,KAAK,sBAAsB,KAAK;AAC1D,SAAK,iBAAiB,KAAK,aAAa,KAAK;AAC7C,SAAK,mBAAmB,KAAK,oBAAoB;AACjD,SAAK,mBAAmB,KAAK,oBAAoB,KAAK;AACtD,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,sBAAsB,KAAK,uBAAuB;AAKvD,SAAK,8BACH,KAAK,+BAA+B;AACtC,SAAK,+BACH,KAAK,gCAAgC;AAAA,EAEzC;AAAA,EACA,UAAU,WAAW;AACnB,QAAI,OAAO,cAAc,UAAU;AAEjC,WAAK,aAAa;AAClB;AAAA,IACF;AAEA,UAAM,OAAO,OAAO,KAAK,SAAS;AAGlC,SAAK,QAAQ,CAAC,MAAM;AAClB,WAAK,CAAC,IAAI,UAAU,CAAC;AAAA,IACvB,CAAC;AAGD,SAAK,aAAa;AAElB,SAAK,QAAQ,CAAC,MAAM;AAClB,WAAK,CAAC,IAAI,UAAU,CAAC;AAAA,IACvB,CAAC;AAAA,EACH;AACF;AAEO,IAAM,oBAAoB,wBAAC,kBAAkB;AAClD,QAAM,QAAQ,IAAI,MAAM;AACxB,QAAM,UAAU,aAAa;AAC7B,SAAO;AACT,GAJiC;;;AGzYjC,SAAS,UAAAC,SAAQ,UAAAC,SAAQ,UAAAC,SAAQ,UAAAC,SAAQ,WAAAC,UAAS,YAAY;AAG9D,IAAMC,SAAN,MAAY;AAAA,EAHZ,OAGY;AAAA;AAAA;AAAA,EACV,cAAc;AACZ,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,iBAAiBC,SAAQ,KAAK,cAAc,EAAE;AACnD,SAAK,gBAAgBC,QAAO,KAAK,cAAc,EAAE,GAAG,KAAK,CAAC;AAC1D,SAAK,qBAAqBC,QAAO,KAAK,UAAU;AAChD,SAAK,uBAAuB,SAAS,KAAK,gBAAgB,KAAK,QAAQ;AACvE,SAAK,sBAAsB,SAAS,KAAK,eAAe,KAAK,QAAQ;AACrE,SAAK,mBAAmBA,QAAO,KAAK,YAAY;AAChD,SAAK,qBAAqBA,QAAO,KAAK,cAAc;AACpD,SAAK,oBAAoBA,QAAO,KAAK,aAAa;AAClD,SAAK,YAAYA,QAAO,KAAK,UAAU;AACvC,SAAK,YAAYA,QAAO,KAAK,UAAU;AAEvC,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,oBAAoB;AACzB,SAAK,gBAAgBF,SAAQE,QAAO,SAAS,GAAG,EAAE;AAClD,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,UAAU,KAAK,KAAK,KAAK,KAAK,IAAI;AACvC,SAAK,iBAAiB;AACtB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,kBAAkB;AACvB,SAAK,YAAY;AACjB,SAAK,oBAAoB;AAGzB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,gBAAgB;AACrB,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,SAAK,sBAAsB;AAI3B,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AACxB,SAAK,sBAAsB;AAC3B,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,wBAAwB;AAC7B,SAAK,qBAAqB;AAC1B,SAAK,sBAAsB;AAI3B,SAAK,kBAAkBC,QAAO,WAAW,EAAE;AAC3C,SAAK,qBAAqB;AAC1B,SAAK,mBAAmB;AACxB,SAAK,kBAAkBA,QAAO,KAAK,iBAAiB,EAAE;AACtD,SAAK,kBAAkB,KAAK,KAAK,KAAK,KAAK,EAAE;AAC7C,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,qBAAqB;AAC1B,SAAK,uBAAuB;AAC5B,SAAK,yBAAyB;AAC9B,SAAK,wBAAwB,KAAK,KAAK,KAAK,KAAK,EAAE;AACnD,SAAK,qBAAqB;AAC1B,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,sBAAsB;AAC3B,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,oBAAoB;AACzB,SAAK,iBAAiB;AAGtB,SAAK,eAAe,KAAK;AACzB,SAAK,YAAY,KAAK;AAGtB,SAAK,gBAAgB;AACrB,SAAK,qBAAqB;AAC1B,SAAK,gBAAgB;AACrB,SAAK,uBAAuB,KAAK;AACjC,SAAK,uBAAuB;AAG5B,SAAK,SAAS,KAAK,UAAUH,SAAQ,KAAK,SAAS,CAAC,KAAK;AACzD,SAAK,UAAU,KAAK,WAAWG,QAAO,KAAK,SAAS,EAAE;AAGtD,SAAK,aAAa;AAElB,SAAK,gBAAgB;AACrB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,eAAe;AACb,SAAK,YAAYH,SAAQ,KAAK,SAAS,EAAE;AACzC,SAAK,YAAY,KAAK;AACtB,SAAK,iBAAiB,KAAK;AAG3B,SAAK,UAAU,KAAK;AACpB,SAAK,aAAa,KAAK;AACvB,SAAK,aAAa,KAAK;AACvB,SAAK,gBAAgB,KAAK;AAC1B,SAAK,mBAAmB,KAAK;AAC7B,SAAK,sBAAsBA,SAAQ,KAAK,iBAAiB,EAAE;AAI3D,SAAK,cAAc,KAAK;AACxB,SAAK,WAAW,KAAK;AACrB,SAAK,iBAAiB,KAAK;AAC3B,SAAK,iBAAiB,KAAK;AAC3B,SAAK,cAAc,KAAK;AACxB,SAAK,kBAAkB,KAAK;AAC5B,SAAK,mBAAmB,KAAK;AAC7B,SAAK,sBAAsB,KAAK;AAChC,SAAK,iBAAiB,KAAK;AAC3B,SAAK,gBAAgB,KAAK;AAC1B,SAAK,kBAAkB,KAAK;AAC5B,SAAK,eAAe,KAAK;AACzB,SAAK,gBAAgB,KAAK;AAC1B,SAAK,wBAAwB,KAAK;AAClC,SAAK,qBAAqB,KAAK;AAI/B,SAAK,qBAAqB,KAAK;AAC/B,SAAK,eAAeA,SAAQ,KAAK,SAAS,EAAE;AAC5C,SAAK,gBAAgB,KAAK;AAC1B,SAAK,qBAAqB,KAAK;AAC/B,SAAK,uBAAuB,KAAK;AACjC,SAAK,YAAY,KAAK;AACtB,SAAK,mBAAmB,KAAK;AAC7B,SAAK,oBAAoB,KAAK;AAG9B,SAAK,gBAAgB,KAAK;AAC1B,SAAK,qBAAqB,KAAK;AAG/B,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,uBAAuB,KAAK,wBAAwB,KAAK;AAC9D,SAAK,kBAAkB,KAAK,mBAAmB,KAAK,YAAY,KAAK;AACrE,SAAK,WAAW,KAAK,YAAY,KAAK;AACtC,SAAK,uBAAuB,KAAK,wBAAwB,KAAK;AAC9D,SAAK,sBAAsB,KAAK,uBAAuB,KAAK,cAAc,KAAK;AAC/E,SAAK,gBAAgB,KAAK,iBAAiB;AAC3C,SAAK,2BAA2B,KAAK,4BAA4B,KAAK;AACtE,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,qBAAqB,KAAK;AAC/B,SAAK,oBAAoB;AAEzB,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,iBAAiB,KAAK,kBAAkB,KAAK;AAElD,SAAK,YAAY,KAAK;AACtB,SAAK,YAAY,KAAK;AACtB,SAAK,YAAYC,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,CAAC;AACpD,SAAK,YAAYA,QAAO,KAAK,gBAAgB,EAAE,GAAG,GAAG,CAAC;AACtD,SAAK,YAAYA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACrD,SAAK,YAAYA,QAAO,KAAK,gBAAgB,EAAE,GAAG,IAAI,CAAC;AACvD,SAAK,YAAYA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACrD,SAAK,YAAYA,QAAO,KAAK,gBAAgB,EAAE,GAAG,IAAI,CAAC;AAGvD,SAAK,UAAU,KAAK,WAAW;AAC/B,SAAK,UAAU,KAAK,WAAW;AAC/B,SAAK,UAAU,KAAK,WAAW;AAC/B,SAAK,UAAU,KAAK,WAAW;AAC/B,SAAK,UAAU,KAAK,WAAW;AAC/B,SAAK,UAAU,KAAK,WAAW;AAC/B,SAAK,UAAU,KAAK,WAAW;AAC/B,SAAK,UAAU,KAAK,WAAW;AAC/B,SAAK,UAAU,KAAK,WAAW;AAC/B,SAAK,WAAW,KAAK,YAAY;AACjC,SAAK,WAAW,KAAK,YAAY;AACjC,SAAK,WAAW,KAAK,YAAY;AAIjC,SAAK,UAAU,KAAK,WAAW,KAAK;AACpC,SAAK,UAAU,KAAK,WAAW,KAAK;AACpC,SAAK,UAAU,KAAK,WAAW,KAAK;AACpC,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,CAAC;AAClE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,CAAC;AAClE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,CAAC;AAClE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACnE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACnE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACnE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACnE,SAAK,WAAW,KAAK,YAAYA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACrE,SAAK,WAAW,KAAK,YAAYA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AAGrE,aAAS,IAAI,GAAG,IAAI,KAAK,mBAAmB,KAAK;AAC/C,WAAK,cAAc,CAAC,IAAI,KAAK,cAAc,CAAC,KAAKC,QAAO,KAAK,WAAW,CAAC,CAAC;AAAA,IAC5E;AAEA,aAAS,IAAI,GAAG,IAAI,KAAK,mBAAmB,KAAK;AAC/C,WAAK,eAAe,CAAC,IAAI,KAAK,eAAe,CAAC,KAAKF,SAAQ,KAAK,WAAW,CAAC,GAAG,EAAE;AAAA,IACnF;AAEA,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,WAAK,YAAY,CAAC,IAChB,KAAK,YAAY,CAAC,KAAKC,QAAO,KAAK,SAAS,EAAE,GAAG,IAAI,GAAG,KAAK,GAAG,EAAE,MAAM,IAAI,GAAG,CAAC;AAClF,WAAK,gBAAgB,CAAC,IACpB,KAAK,gBAAgB,CAAC,KAAKA,QAAO,KAAK,SAAS,EAAE,GAAG,IAAI,GAAG,KAAK,GAAG,EAAE,KAAK,IAAI,GAAG,CAAC;AAAA,IACvF;AAGA,SAAK,kBAAkB,KAAK,oBAAoB,KAAK,WAAW,UAAU,KAAK;AAE/E,aAAS,IAAI,GAAG,IAAI,KAAK,mBAAmB,KAAK;AAC/C,WAAK,gBAAgB,CAAC,IAAI,KAAK,gBAAgB,CAAC,KAAK,KAAK;AAAA,IAC5D;AAGA,aAAS,IAAI,GAAG,IAAI,KAAK,mBAAmB,KAAK;AAC/C,WAAK,QAAQ,CAAC,IAAI,KAAK,WAAW,CAAC;AAAA,IACrC;AACA,SAAK,mBAAmB,KAAK,oBAAoB;AACjD,SAAK,oBAAoB,KAAK,qBAAqB,KAAK;AACxD,SAAK,qBAAqB,KAAK,sBAAsB;AACrD,SAAK,sBAAsB,KAAK,uBAAuB,KAAK;AAC5D,SAAK,oBAAoB,KAAK,qBAAqB;AACnD,SAAK,qBAAqB,KAAK,sBAAsB,KAAK;AAC1D,SAAK,iBAAiB,KAAK,kBAAkB;AAC7C,SAAK,iBAAiB,KAAK,kBAAkB;AAC7C,SAAK,sBAAsB,KAAK,uBAAuB;AACvD,SAAK,sBAAsB,KAAK,uBAAuB;AACvD,SAAK,aAAa,KAAK,cAAc;AAGrC,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,gBAAgB,KAAK,iBAAiBA,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;AACzF,SAAK,gBAAgB,KAAK,iBAAiBA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;AAC5F,SAAK,gBAAgB,KAAK,iBAAiBA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;AAC5F,SAAK,oBAAoB,KAAK,qBAAqB,KAAK;AACxD,SAAK,oBACH,KAAK,qBAAqBA,QAAO,KAAK,kBAAkB,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;AACjF,SAAK,oBACH,KAAK,qBAAqBA,QAAO,KAAK,kBAAkB,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;AACpF,SAAK,oBACH,KAAK,qBAAqBA,QAAO,KAAK,kBAAkB,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;AACpF,SAAK,oBACH,KAAK,qBAAqBG,QAAO,KAAK,aAAa,IAC/CJ,SAAQ,KAAK,aAAa,IAC1BG,QAAO,KAAK,aAAa;AAC/B,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,mCACH,KAAK,oCAAoC,KAAK;AAChD,SAAK,mCACH,KAAK,oCAAoC,KAAK;AAChD,SAAK,oBAAoB,KAAK,qBAAqB,KAAK;AAGxD,SAAK,UAAU;AAAA,MACb,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,YAAY,KAAK,SAAS,cAAc,KAAK;AAAA,MAC7C,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,gBAAgB,KAAK,SAAS,kBAAkB,KAAK;AAAA,MACrD,gBAAgB,KAAK,SAAS,kBAAkB,KAAK;AAAA,MACrD,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,gBAAgB,KAAK,SAAS,kBAAkB,KAAK;AAAA,MACrD,gBAAgB,KAAK,SAAS,kBAAkB,KAAK;AAAA,MACrD,kBACE,KAAK,SAAS,oBACd;AAAA,IACJ;AAEA,SAAK,SAAS;AAAA,MACZ,gBAAgB,KAAK;AAAA,MACrB,cAAc,KAAK;AAAA,MACnB,YAAY,KAAK;AAAA,MACjB,YAAY,KAAK;AAAA,MACjB,kBAAkB,KAAK;AAAA,MACvB,gBAAgB,KAAK;AAAA,IACvB;AAGA,SAAK,QAAQ;AAAA,MACX,WAAW,KAAK,OAAO,aAAa,KAAK;AAAA,MACzC,iBAAiB,KAAK,OAAO,mBAAmB;AAAA,MAChD,mBAAmB,KAAK,OAAO,qBAAqB;AAAA,MACpD,cAAc,KAAK,OAAO,gBAAgB;AAAA,MAC1C,kBAAkB,KAAK,OAAO,oBAAoB;AAAA,MAClD,gBAAgB,KAAK,OAAO,kBAAkB;AAAA,MAC9C,sBAAsB,KAAK,OAAO,wBAAwB;AAAA,MAC1D,kBAAkB,KAAK,OAAO,oBAAoB;AAAA,MAClD,eAAe,KAAK,OAAO,iBAAiB;AAAA,MAC5C,gBAAgB,KAAK,OAAO,kBAAkB;AAAA,IAChD;AAGA,SAAK,YAAY,KAAK;AAGtB,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,yBAAyB,KAAK,0BAA0B,KAAK;AAClE,SAAK,wBAAwB,KAAK,yBAAyB;AAC3D,SAAK,uBAAuB,KAAK,wBAAwB,KAAK;AAC9D,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,0BACH,KAAK,4BACJ,KAAK,WAAWA,QAAO,KAAK,gBAAgB,EAAE,IAAI,KAAK;AAC1D,SAAK,qBAAqB,KAAK,sBAAsB,KAAK;AAG1D,SAAK,OAAOH,SAAQ,KAAK,gBAAgB,EAAE;AAC3C,SAAK,OAAOA,SAAQ,KAAK,QAAQ,KAAK,gBAAgB,EAAE;AACxD,SAAK,OAAOA,SAAQ,KAAK,QAAQ,KAAK,eAAe,EAAE;AACvD,SAAK,OAAOA,SAAQ,KAAK,QAAQC,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE;AAC1E,SAAK,OAAOD,SAAQ,KAAK,QAAQC,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE;AAC1E,SAAK,OAAOD,SAAQ,KAAK,QAAQC,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE;AAC1E,SAAK,OAAOD,SAAQ,KAAK,QAAQC,QAAO,KAAK,cAAc,EAAE,GAAG,GAAI,CAAC,GAAG,EAAE;AAC1E,SAAK,OAAOD,SAAQ,KAAK,QAAQC,QAAO,KAAK,cAAc,EAAE,GAAG,IAAK,CAAC,GAAG,EAAE;AAC3E,SAAK,UAAU,KAAK,WAAWC,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,kBAAkB,KAAK,mBAAmBA,QAAO,KAAK,cAAc;AACzE,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmBA,QAAO,KAAK,cAAc;AACzE,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AAEpD,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,qBAAqB,KAAK,sBAAsB,KAAK;AAC1D,SAAK,iBAAiB,KAAK,aAAa,KAAK;AAC7C,SAAK,mBAAmB,KAAK,oBAAoB;AACjD,SAAK,mBAAmB,KAAK,oBAAoB,KAAK;AACtD,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,sBAAsB,KAAK,uBAAuB;AAKvD,SAAK,8BACH,KAAK,+BAA+BF,SAAQ,KAAK,YAAY,EAAE;AACjE,SAAK,+BACH,KAAK,gCAAgCA,SAAQ,KAAK,YAAY,CAAC;AAGjE,SAAK,aAAa,KAAK,cAAc;AAAA,EACvC;AAAA,EACA,UAAU,WAAW;AACnB,QAAI,OAAO,cAAc,UAAU;AAEjC,WAAK,aAAa;AAClB;AAAA,IACF;AAEA,UAAM,OAAO,OAAO,KAAK,SAAS;AAGlC,SAAK,QAAQ,CAAC,MAAM;AAClB,WAAK,CAAC,IAAI,UAAU,CAAC;AAAA,IACvB,CAAC;AAGD,SAAK,aAAa;AAElB,SAAK,QAAQ,CAAC,MAAM;AAClB,WAAK,CAAC,IAAI,UAAU,CAAC;AAAA,IACvB,CAAC;AAAA,EACH;AACF;AAEO,IAAMK,qBAAoB,wBAAC,kBAAkB;AAClD,QAAM,QAAQ,IAAIN,OAAM;AACxB,QAAM,UAAU,aAAa;AAC7B,SAAO;AACT,GAJiC;;;ACrYjC,SAAS,UAAAO,SAAQ,WAAAC,UAAS,QAAAC,OAAM,UAAAC,SAAQ,UAAAC,SAAQ,UAAAC,eAAc;AAO9D,IAAMC,SAAN,MAAY;AAAA,EAPZ,OAOY;AAAA;AAAA;AAAA,EACV,cAAc;AAEZ,SAAK,aAAa;AAClB,SAAK,eAAe;AAEpB,SAAK,iBAAiBC,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AAC1D,SAAK,iBAAiB;AACtB,SAAK,gBAAgBA,QAAO,KAAK,cAAc,EAAE,GAAG,KAAK,CAAC;AAC1D,SAAK,qBAAqB,SAAS,KAAK,cAAc,KAAK,QAAQ;AACnE,SAAK,uBAAuB,SAAS,KAAK,gBAAgB,KAAK,QAAQ;AACvE,SAAK,sBAAsB,SAAS,KAAK,eAAe,KAAK,QAAQ;AAGrE,SAAK,mBAAmBC,QAAO,KAAK,YAAY;AAChD,SAAK,qBAAqBA,QAAO,KAAK,cAAc;AACpD,SAAK,oBAAoBA,QAAO,KAAK,aAAa;AAClD,SAAK,YAAYA,QAAO,KAAK,UAAU;AACvC,SAAK,YAAYA,QAAO,KAAK,UAAU;AAEvC,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,kBAAkB;AACvB,SAAK,YAAY;AACjB,SAAK,oBAAoB;AAIzB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,gBAAgB;AACrB,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,SAAK,sBAAsB;AAI3B,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AACxB,SAAK,sBAAsB;AAC3B,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,wBAAwB;AAC7B,SAAK,qBAAqB;AAC1B,SAAK,sBAAsB;AAI3B,SAAK,kBAAkB;AACvB,SAAK,qBAAqB;AAC1B,SAAK,mBAAmB;AACxB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,qBAAqB;AAC1B,SAAK,gBAAgB,KAAK;AAC1B,SAAK,oBAAoB;AACzB,SAAK,uBAAuB,KAAK;AACjC,SAAK,yBAAyB;AAC9B,SAAK,wBAAwB;AAC7B,SAAK,qBAAqB;AAC1B,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,sBAAsB;AAC3B,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,iBAAiB;AAEtB,SAAK,kBAAkBC,MAAK,KAAK,KAAK,KAAK,IAAI;AAC/C,SAAK,qBAAqB;AAC1B,SAAK,mBAAmB;AACxB,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,qBAAqB;AAC1B,SAAK,gBAAgB;AACrB,SAAK,oBAAoB;AACzB,SAAK,uBAAuB;AAC5B,SAAK,yBAAyB;AAC9B,SAAK,wBAAwB;AAC7B,SAAK,qBAAqB;AAC1B,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,sBAAsB;AAC3B,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,iBAAiB;AAGtB,SAAK,eAAe,KAAK;AACzB,SAAK,YAAY,KAAK;AAGtB,SAAK,gBAAgB;AACrB,SAAK,qBAAqB;AAC1B,SAAK,gBAAgB;AACrB,SAAK,uBAAuB,KAAK;AACjC,SAAK,uBAAuB;AAG5B,SAAK,SAAS;AACd,SAAK,UAAU;AAGf,SAAK,aAAa;AAClB,SAAK,gBAAgB;AACrB,SAAK,iBAAiB;AACtB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,eAAe;AAIb,SAAK,UAAU,KAAK,WAAW,KAAK;AACpC,SAAK,UAAU,KAAK,WAAW,KAAK;AACpC,SAAK,UAAU,KAAK,WAAW,KAAK;AACpC,SAAK,UAAU,KAAK,WAAWF,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,CAAC;AAClE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,CAAC;AAClE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,CAAC;AAClE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACnE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACnE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACnE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACnE,SAAK,WAAW,KAAK,YAAYA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACrE,SAAK,WAAW,KAAK,YAAYA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACrE,SAAK,aAAgB,IAAI,KAAK,aAAgB,KAAKG,QAAO,KAAK,gBAAgB,EAAE;AACjF,SAAK,aAAgB,IAAI,KAAK,aAAgB,KAAKA,QAAO,KAAK,eAAe,EAAE;AAChF,aAAS,IAAI,GAAG,IAAI,KAAK,mBAAmB,KAAK;AAE/C,WAAK,WAAW,CAAC,IAAIA,QAAO,KAAK,WAAW,CAAC,GAAG,EAAE;AAClD,WAAK,eAAe,CAAC,IAAI,KAAK,eAAe,CAAC,KAAKA,QAAO,KAAK,WAAW,CAAC,GAAG,EAAE;AAAA,IAClF;AAEA,aAAS,IAAI,GAAG,IAAI,KAAK,mBAAmB,KAAK;AAC/C,WAAK,cAAc,CAAC,IAAI,KAAK,cAAc,CAAC,KAAKH,QAAO,KAAK,WAAW,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;AAAA,IACxF;AAEA,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,WAAK,YAAY,CAAC,IAAI,KAAK,YAAY,CAAC,KAAKA,QAAO,KAAK,SAAS,EAAE,GAAG,IAAI,GAAG,EAAE,IAAI,IAAI,GAAG,CAAC;AAC5F,WAAK,gBAAgB,CAAC,IACpB,KAAK,gBAAgB,CAAC,KAAKA,QAAO,KAAK,SAAS,EAAE,GAAG,IAAI,GAAG,EAAE,IAAI,IAAI,GAAG,CAAC;AAAA,IAC9E;AAEA,SAAK,kBACH,KAAK,oBAAoB,gBAAgB,KAAK,kBAC1C,KAAK,kBACL,KAAK;AAEX,QAAI,KAAK,mBAAmB,cAAc;AACxC,WAAK,eAAe,KAAK,gBAAgBC,QAAO,KAAK,cAAc;AACnE,WAAK,eAAe,KAAK,gBAAgBA,QAAO,KAAK,cAAc;AACnE,eAAS,IAAI,GAAG,IAAI,KAAK,mBAAmB,KAAK;AAC/C,aAAK,gBAAgB,CAAC,IAAI,KAAK,gBAAgB,CAAC,KAAK,KAAK;AAAA,MAC5D;AAAA,IACF;AAGA,SAAK,UAAU,KAAK;AACpB,SAAK,aAAa,KAAK;AACvB,SAAK,aAAa,KAAK;AACvB,SAAK,gBAAgB,KAAK;AAC1B,SAAK,mBAAmB,KAAK;AAC7B,SAAK,aAAa,KAAK;AACvB,SAAK,sBAAsB,KAAK;AAKhC,SAAK,cAAcG,SAAQ,KAAK,SAAS,EAAE;AAC3C,SAAK,WAAW,KAAK;AACrB,SAAK,mBAAmB,KAAK;AAC7B,SAAK,cAAc,KAAK;AACxB,SAAK,kBAAkB,KAAK;AAC5B,SAAK,sBAAsB,KAAK;AAChC,SAAK,iBAAiB,KAAK;AAC3B,SAAK,gBAAgB,KAAK;AAC1B,SAAK,kBAAkB,KAAK;AAC5B,SAAK,gBAAgB,KAAK;AAC1B,SAAK,iBAAiB,KAAK;AAI3B,SAAK,gBAAgB,KAAK;AAC1B,SAAK,uBAAuB,KAAK;AAGjC,SAAK,gBAAgB,KAAK;AAC1B,SAAK,qBAAqB,KAAK;AAG/B,SAAK,SAAS,KAAK,UAAUA,SAAQ,KAAK,cAAc,EAAE,KAAK;AAC/D,SAAK,UAAU,KAAK,WAAWA,SAAQ,KAAK,cAAc,CAAC;AAE3D,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,uBAAuB,KAAK,wBAAwB,KAAK;AAC9D,SAAK,kBAAkB,KAAK,mBAAmB,KAAK,YAAY,KAAK;AAErE,SAAK,WAAW,KAAK,YAAY,KAAK;AACtC,SAAK,uBAAuB,KAAK,wBAAwB,KAAK;AAC9D,SAAK,sBAAsB,KAAK,uBAAuB,KAAK,cAAc,KAAK;AAC/E,SAAK,gBAAgB,KAAK,iBAAiB;AAC3C,SAAK,2BAA2B,KAAK,4BAA4B,KAAK;AACtE,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,qBAAqB,KAAK;AAC/B,SAAK,oBAAoB,KAAK;AAE9B,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,iBAAiB,KAAK,kBAAkB,KAAK;AAClD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AAEpD,SAAK,YAAY,KAAK;AAEtB,SAAK,YAAY,KAAK;AACtB,SAAK,YAAY,KAAK;AACtB,SAAK,YAAYJ,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,CAAC;AACpD,SAAK,YAAYA,QAAO,KAAK,gBAAgB,EAAE,GAAG,GAAG,CAAC;AACtD,SAAK,YAAYA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACrD,SAAK,YAAYA,QAAO,KAAK,gBAAgB,EAAE,GAAG,IAAI,CAAC;AACvD,SAAK,YAAYA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACrD,SAAK,YAAYA,QAAO,KAAK,gBAAgB,EAAE,GAAG,IAAI,CAAC;AAGvD,SAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,SAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,eAAe,EAAE,GAAG,IAAI,CAAC;AAC9D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AAC7D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,gBAAgB,EAAE,GAAG,IAAI,CAAC;AAC/D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,eAAe,EAAE,GAAG,IAAI,CAAC;AAC9D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAK,GAAG,IAAI,CAAC;AACrE,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC;AACrE,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC;AACrE,SAAK,QAAQ,KAAK,SAASA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAK,GAAG,IAAI,CAAC;AACvE,SAAK,QAAQ,KAAK,SAASA,QAAO,KAAK,cAAc,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC;AACvE,SAAK,QAAQ,KAAK,SAASA,QAAO,KAAK,cAAc,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC;AACvE,SAAK,mBAAmB,KAAK,oBAAoB;AACjD,SAAK,oBAAoB,KAAK,qBAAqB,KAAK;AACxD,SAAK,qBAAqB,KAAK,sBAAsB;AACrD,SAAK,sBAAsB,KAAK,uBAAuB,KAAK;AAC5D,SAAK,oBAAoB,KAAK,qBAAqB;AACnD,SAAK,qBAAqB,KAAK,sBAAsB,KAAK;AAC1D,SAAK,iBAAiB,KAAK,kBAAkB;AAC7C,SAAK,iBAAiB,KAAK,kBAAkB;AAC7C,SAAK,sBAAsB,KAAK,uBAAuB;AACvD,SAAK,sBAAsB,KAAK,uBAAuB;AACvD,SAAK,aAAa,KAAK,cAAc;AAGrC,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,gBAAgB,KAAK,iBAAiBA,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;AACzF,SAAK,gBAAgB,KAAK,iBAAiBA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;AAC5F,SAAK,gBAAgB,KAAK,iBAAiBA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;AAC5F,SAAK,oBAAoB,KAAK,qBAAqB,KAAK;AACxD,SAAK,oBACH,KAAK,qBAAqBA,QAAO,KAAK,kBAAkB,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;AACjF,SAAK,oBACH,KAAK,qBAAqBA,QAAO,KAAK,kBAAkB,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;AACpF,SAAK,oBACH,KAAK,qBAAqBA,QAAO,KAAK,kBAAkB,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;AACpF,SAAK,oBACH,KAAK,qBAAqBK,QAAO,KAAK,aAAa,IAC/CD,SAAQ,KAAK,aAAa,IAC1BD,QAAO,KAAK,aAAa;AAC/B,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,mCACH,KAAK,oCAAoC,KAAK;AAChD,SAAK,mCACH,KAAK,oCAAoC,KAAK;AAChD,SAAK,oBAAoB,KAAK,qBAAqB,KAAK;AAGxD,SAAK,QAAQ;AAAA,MACX,WAAW,KAAK,OAAO,aAAa,KAAK;AAAA,MACzC,iBAAiB,KAAK,OAAO,mBAAmB;AAAA,MAChD,mBAAmB,KAAK,OAAO,qBAAqB;AAAA,MACpD,cAAc,KAAK,OAAO,gBAAgB;AAAA,MAC1C,kBAAkB,KAAK,OAAO,oBAAoB;AAAA,MAClD,gBAAgB,KAAK,OAAO,kBAAkB;AAAA,MAC9C,sBAAsB,KAAK,OAAO,wBAAwB;AAAA,MAC1D,kBAAkB,KAAK,OAAO,oBAAoB;AAAA,MAClD,eAAe,KAAK,OAAO,iBAAiB;AAAA,MAC5C,gBAAgB,KAAK,OAAO,kBAAkB;AAAA,IAChD;AAGA,SAAK,UAAU;AAAA,MACb,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,YAAY,KAAK,SAAS,cAAc,KAAK;AAAA,MAC7C,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,gBAAgB,KAAK,SAAS,kBAAkB,KAAK;AAAA,MACrD,gBAAgB,KAAK,SAAS,kBAAkB,KAAK;AAAA,MACrD,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,gBAAgB,KAAK,SAAS,kBAAkB,KAAK;AAAA,MACrD,gBAAgB,KAAK,SAAS,kBAAkB,KAAK;AAAA,MACrD,kBACE,KAAK,SAAS,oBACd;AAAA,IACJ;AAGA,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,yBAAyB,KAAK,0BAA0B,KAAK;AAClE,SAAK,wBAAwB,KAAK,yBAAyB;AAC3D,SAAK,uBAAuB,KAAK,wBAAwB,KAAK;AAC9D,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,0BAA0B,KAAK,2BAA2B,KAAK;AACpE,SAAK,qBAAqB,KAAK,sBAAsB,KAAK;AAG1D,SAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,SAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,SAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,SAAK,OAAO,KAAK,QAAQH,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AAC7D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AAC7D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AAC7D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,GAAI,CAAC;AAC7D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAK,CAAC;AAC9D,QAAI,KAAK,UAAU;AACjB,WAAK,OAAOI,SAAQ,KAAK,MAAM,EAAE;AACjC,WAAK,OAAOA,SAAQ,KAAK,MAAM,EAAE;AACjC,WAAK,OAAOA,SAAQ,KAAK,MAAM,EAAE;AACjC,WAAK,OAAOA,SAAQ,KAAK,MAAM,EAAE;AACjC,WAAK,OAAOA,SAAQ,KAAK,MAAM,EAAE;AACjC,WAAK,OAAOA,SAAQ,KAAK,MAAM,EAAE;AACjC,WAAK,OAAOA,SAAQ,KAAK,MAAM,EAAE;AACjC,WAAK,OAAOA,SAAQ,KAAK,MAAM,EAAE;AAAA,IACnC,OAAO;AACL,WAAK,OAAOD,QAAO,KAAK,MAAM,EAAE;AAChC,WAAK,OAAOA,QAAO,KAAK,MAAM,EAAE;AAChC,WAAK,OAAOA,QAAO,KAAK,MAAM,EAAE;AAChC,WAAK,OAAOA,QAAO,KAAK,MAAM,EAAE;AAChC,WAAK,OAAOA,QAAO,KAAK,MAAM,EAAE;AAChC,WAAK,OAAOA,QAAO,KAAK,MAAM,EAAE;AAChC,WAAK,OAAOA,QAAO,KAAK,MAAM,EAAE;AAChC,WAAK,OAAOA,QAAO,KAAK,MAAM,EAAE;AAAA,IAClC;AACA,SAAK,UAAU,KAAK,WAAWA,QAAOF,QAAO,KAAK,IAAI,GAAG,EAAE;AAC3D,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,kBAAkB,KAAK,mBAAmBA,QAAO,KAAK,cAAc;AACzE,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmBA,QAAO,KAAK,cAAc;AACzE,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AAEpD,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,qBAAqB,KAAK,sBAAsB,KAAK;AAC1D,SAAK,iBAAiB,KAAK,aAAa,KAAK;AAC7C,SAAK,mBAAmB,KAAK,oBAAoB;AACjD,SAAK,mBAAmB,KAAK,oBAAoB,KAAK;AACtD,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,sBAAsB,KAAK,uBAAuB;AAKvD,SAAK,8BACH,KAAK,+BAA+B;AACtC,SAAK,+BACH,KAAK,gCAAgC;AAAA,EAEzC;AAAA,EACA,UAAU,WAAW;AAEnB,WAAO,KAAK,IAAI,EAAE,QAAQ,CAAC,MAAM;AAC/B,UAAI,KAAK,CAAC,MAAM,cAAc;AAC5B,aAAK,CAAC,IAAI;AAAA,MACZ;AAAA,IACF,CAAC;AAED,QAAI,OAAO,cAAc,UAAU;AAEjC,WAAK,aAAa;AAClB;AAAA,IACF;AAEA,UAAM,OAAO,OAAO,KAAK,SAAS;AAGlC,SAAK,QAAQ,CAAC,MAAM;AAClB,WAAK,CAAC,IAAI,UAAU,CAAC;AAAA,IACvB,CAAC;AAGD,SAAK,aAAa;AAElB,SAAK,QAAQ,CAAC,MAAM;AAClB,WAAK,CAAC,IAAI,UAAU,CAAC;AAAA,IACvB,CAAC;AAAA,EACH;AACF;AAEO,IAAMK,qBAAoB,wBAAC,kBAAkB;AAClD,QAAM,QAAQ,IAAIP,OAAM;AACxB,QAAM,UAAU,aAAa;AAC7B,SAAO;AACT,GAJiC;;;ACzajC,SAAS,UAAAQ,SAAQ,UAAAC,SAAQ,UAAAC,SAAQ,UAAAC,SAAQ,WAAAC,gBAAe;AAOxD,IAAMC,SAAN,MAAY;AAAA,EAPZ,OAOY;AAAA;AAAA;AAAA,EACV,cAAc;AAEZ,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,iBAAiB;AACtB,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,SAAK,aAAa;AAClB,SAAK,WAAW;AAEhB,SAAK,gBAAgBC,SAAQ,WAAW,EAAE;AAC1C,SAAK,qBAAqB,SAAS,KAAK,cAAc,KAAK,QAAQ;AACnE,SAAK,uBAAuB,SAAS,KAAK,gBAAgB,KAAK,QAAQ;AACvE,SAAK,sBAAsB,SAAS,KAAK,eAAe,KAAK,QAAQ;AACrE,SAAK,mBAAmBC,QAAO,KAAK,YAAY;AAChD,SAAK,qBAAqBA,QAAO,KAAK,cAAc;AACpD,SAAK,oBAAoBA,QAAO,KAAK,YAAY;AACjD,SAAK,YAAYA,QAAO,KAAK,UAAU;AACvC,SAAK,YAAYA,QAAO,KAAK,UAAU;AACvC,SAAK,oBAAoB;AAGzB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,gBAAgB;AACrB,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,SAAK,sBAAsB;AAI3B,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AACxB,SAAK,sBAAsB;AAC3B,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,wBAAwB;AAC7B,SAAK,qBAAqB;AAC1B,SAAK,sBAAsB;AAI3B,SAAK,kBAAkB;AACvB,SAAK,qBAAqB;AAC1B,SAAK,mBAAmB;AACxB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,qBAAqB;AAC1B,SAAK,gBAAgB;AACrB,SAAK,oBAAoB;AACzB,SAAK,uBAAuB;AAC5B,SAAK,yBAAyB;AAC9B,SAAK,wBAAwB;AAC7B,SAAK,qBAAqB;AAC1B,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,sBAAsB;AAC3B,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,iBAAiB;AAGtB,SAAK,eAAe,KAAK;AACzB,SAAK,YAAY,KAAK;AAGtB,SAAK,gBAAgB;AACrB,SAAK,qBAAqB;AAC1B,SAAK,gBAAgB;AACrB,SAAK,uBAAuB,KAAK;AACjC,SAAK,uBAAuB;AAG5B,SAAK,aAAa;AAElB,SAAK,gBAAgB;AACrB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,eAAe;AAEb,SAAK,cAAcC,QAAO,KAAK,SAAS,EAAE;AAC1C,SAAK,WAAW,KAAK;AACrB,SAAK,mBAAmB,KAAK;AAC7B,SAAK,iBAAiB,KAAK;AAC3B,SAAK,gBAAgB,KAAK;AAC1B,SAAK,kBAAkB,KAAK;AAC5B,SAAK,gBAAgB,KAAK;AAC1B,SAAK,iBAAiB,KAAK;AAG3B,SAAK,UAAU,KAAK,WAAW,KAAK;AACpC,SAAK,UAAU,KAAK,WAAW,KAAK;AACpC,SAAK,UAAU,KAAK,WAAW,KAAK;AACpC,SAAK,UAAU,KAAK,WAAWC,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,CAAC;AAClE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,CAAC;AAClE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,CAAC;AAClE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACnE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACnE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACnE,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACnE,SAAK,WAAW,KAAK,YAAYA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACrE,SAAK,WAAW,KAAK,YAAYA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACrE,SAAK,aAAgB,IAAI,KAAK,aAAgB,KAAKD,QAAO,KAAK,gBAAgB,EAAE;AACjF,SAAK,aAAgB,IAAI,KAAK,aAAgB,KAAKA,QAAO,KAAK,eAAe,EAAE;AAChF,aAAS,IAAI,GAAG,IAAI,KAAK,mBAAmB,KAAK;AAE/C,WAAK,WAAW,CAAC,IAAIA,QAAO,KAAK,WAAW,CAAC,GAAG,EAAE;AAClD,WAAK,eAAe,CAAC,IAAI,KAAK,eAAe,CAAC,KAAKA,QAAO,KAAK,WAAW,CAAC,GAAG,EAAE;AAAA,IAClF;AAGA,aAAS,IAAI,GAAG,IAAI,KAAK,mBAAmB,KAAK;AAC/C,WAAK,cAAc,CAAC,IAAI,KAAK,cAAc,CAAC,KAAKC,QAAO,KAAK,WAAW,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;AAAA,IACxF;AAGA,SAAK,kBACH,KAAK,oBAAoB,gBAAgB,KAAK,kBAC1C,KAAK,kBACL,KAAK;AAEX,aAAS,IAAI,GAAG,IAAI,KAAK,mBAAmB,KAAK;AAC/C,WAAK,gBAAgB,CAAC,IAAI,KAAK,gBAAgB,CAAC,KAAK,KAAK;AAAA,IAC5D;AAEA,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,WAAK,YAAY,CAAC,IAChB,KAAK,YAAY,CAAC,KAAKA,QAAO,KAAK,SAAS,EAAE,GAAG,IAAI,GAAG,KAAK,GAAG,EAAE,IAAI,IAAI,GAAG,CAAC;AAChF,WAAK,gBAAgB,CAAC,IACpB,KAAK,gBAAgB,CAAC,KAAKA,QAAO,KAAK,SAAS,EAAE,GAAG,IAAI,GAAG,KAAK,GAAG,EAAE,IAAI,IAAI,GAAG,CAAC;AAAA,IACtF;AAIA,SAAK,UAAU,KAAK;AACpB,SAAK,aAAa,KAAK;AACvB,SAAK,aAAa,KAAK;AACvB,SAAK,gBAAgB,KAAK;AAC1B,SAAK,mBAAmB,KAAK;AAI7B,SAAK,kBAAkB,KAAK;AAC5B,SAAK,gBAAgB,KAAK;AAC1B,SAAK,uBAAuB,KAAK;AACjC,SAAK,wBAAwB,KAAK;AAClC,SAAK,qBAAqB,KAAK;AAG/B,SAAK,gBAAgB,KAAK;AAC1B,SAAK,qBAAqB,KAAK;AAG/B,SAAK,SAAS,KAAK,UAAUH,SAAQ,KAAK,SAAS,EAAE,KAAK;AAC1D,SAAK,UAAU,KAAK,WAAWA,SAAQ,KAAK,SAAS,EAAE;AAGvD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,uBAAuB,KAAK,wBAAwB,KAAK;AAC9D,SAAK,kBAAkB,KAAK,mBAAmB,KAAK,YAAY,KAAK;AAErE,SAAK,WAAW,KAAK,YAAY,KAAK;AACtC,SAAK,uBAAuB,KAAK,wBAAwB,KAAK;AAC9D,SAAK,sBAAsB,KAAK,uBAAuB,KAAK,cAAc,KAAK;AAC/E,SAAK,gBAAgB,KAAK,iBAAiB;AAC3C,SAAK,2BAA2B,KAAK,4BAA4B,KAAK;AACtE,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,qBAAqB,KAAK;AAC/B,SAAK,oBAAoB,KAAK;AAE9B,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,iBAAiB,KAAK,kBAAkB,KAAK;AAClD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AAEpD,SAAK,YAAY,KAAK;AAEtB,SAAK,YAAY,KAAK;AACtB,SAAK,YAAY,KAAK;AACtB,SAAK,YAAYG,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,CAAC;AACpD,SAAK,YAAYA,QAAO,KAAK,gBAAgB,EAAE,GAAG,GAAG,CAAC;AACtD,SAAK,YAAYA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACrD,SAAK,YAAYA,QAAO,KAAK,gBAAgB,EAAE,GAAG,IAAI,CAAC;AACvD,SAAK,YAAYA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACrD,SAAK,YAAYA,QAAO,KAAK,gBAAgB,EAAE,GAAG,IAAI,CAAC;AAGvD,SAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,SAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,SAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AAC7D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,gBAAgB,EAAE,GAAG,IAAI,CAAC;AAC/D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,eAAe,EAAE,GAAG,IAAK,GAAG,IAAI,CAAC;AACtE,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAK,GAAG,IAAI,CAAC;AACrE,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC;AACrE,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,KAAK,GAAG,EAAE,CAAC;AACnE,SAAK,QAAQ,KAAK,SAASA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAK,GAAG,IAAI,CAAC;AACvE,SAAK,QAAQ,KAAK,SAASA,QAAO,KAAK,cAAc,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC;AACvE,SAAK,QAAQ,KAAK,SAASA,QAAO,KAAK,cAAc,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC;AACvE,SAAK,mBAAmB,KAAK,oBAAoB;AACjD,SAAK,oBAAoB,KAAK,qBAAqB,KAAK;AACxD,SAAK,qBAAqB,KAAK,sBAAsB;AACrD,SAAK,sBAAsB,KAAK,uBAAuB,KAAK;AAC5D,SAAK,oBAAoB,KAAK,qBAAqB;AACnD,SAAK,qBAAqB,KAAK,sBAAsB,KAAK;AAC1D,SAAK,iBAAiB,KAAK,kBAAkB;AAC7C,SAAK,iBAAiB,KAAK,kBAAkB;AAC7C,SAAK,sBAAsB,KAAK,uBAAuB;AACvD,SAAK,sBAAsB,KAAK,uBAAuB;AACvD,SAAK,aAAa,KAAK,cAAc;AAGrC,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,gBAAgB,KAAK,iBAAiBA,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;AACzF,SAAK,gBAAgB,KAAK,iBAAiBA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;AAC5F,SAAK,gBAAgB,KAAK,iBAAiBA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;AAC5F,SAAK,oBAAoB,KAAK,qBAAqB,KAAK;AACxD,SAAK,oBACH,KAAK,qBAAqBA,QAAO,KAAK,kBAAkB,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;AACjF,SAAK,oBACH,KAAK,qBAAqBA,QAAO,KAAK,kBAAkB,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;AACpF,SAAK,oBACH,KAAK,qBAAqBA,QAAO,KAAK,kBAAkB,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;AACpF,SAAK,oBACH,KAAK,qBAAqBC,QAAO,KAAK,aAAa,IAC/CJ,SAAQ,KAAK,aAAa,IAC1BE,QAAO,KAAK,aAAa;AAC/B,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,mCACH,KAAK,oCAAoC,KAAK;AAChD,SAAK,mCACH,KAAK,oCAAoC,KAAK;AAChD,SAAK,oBAAoB,KAAK,qBAAqB,KAAK;AAExD,SAAK,SAAS;AAAA,MACZ,gBAAgB,KAAK;AAAA,MACrB,cAAc,KAAK;AAAA,MACnB,YAAY,KAAK;AAAA,MACjB,YAAY,KAAK;AAAA,MACjB,kBAAkB,KAAK;AAAA,MACvB,gBAAgB,KAAK;AAAA,IACvB;AAGA,SAAK,QAAQ;AAAA,MACX,WAAW,KAAK,OAAO,aAAa,KAAK;AAAA,MACzC,iBAAiB,KAAK,OAAO,mBAAmB;AAAA,MAChD,mBAAmB,KAAK,OAAO,qBAAqB;AAAA,MACpD,cAAc,KAAK,OAAO,gBAAgB;AAAA,MAC1C,kBAAkB,KAAK,OAAO,oBAAoB;AAAA,MAClD,gBAAgB,KAAK,OAAO,kBAAkB;AAAA,MAC9C,sBAAsB,KAAK,OAAO,wBAAwB;AAAA,MAC1D,kBAAkB,KAAK,OAAO,oBAAoB;AAAA,MAClD,eAAe,KAAK,OAAO,iBAAiB;AAAA,MAC5C,gBAAgB,KAAK,OAAO,kBAAkB;AAAA,IAChD;AAGA,SAAK,UAAU;AAAA,MACb,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,YAAY,KAAK,SAAS,cAAc,KAAK;AAAA,MAC7C,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,gBAAgB,KAAK,SAAS,kBAAkB,KAAK;AAAA,MACrD,gBAAgB,KAAK,SAAS,kBAAkB,KAAK;AAAA,MACrD,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,gBAAgB,KAAK,SAAS,kBAAkB,KAAK;AAAA,MACrD,gBAAgB,KAAK,SAAS,kBAAkB,KAAK;AAAA,MACrD,kBACE,KAAK,SAAS,oBACd;AAAA,IACJ;AAGA,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,yBAAyB,KAAK,0BAA0B,KAAK;AAClE,SAAK,wBAAwB,KAAK,yBAAyB;AAC3D,SAAK,uBAAuB,KAAK,wBAAwB,KAAK;AAC9D,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,0BAA0B,KAAK,2BAA2B,KAAK;AACpE,SAAK,qBAAqB,KAAK,sBAAsB,KAAK;AAG1D,SAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,SAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,SAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,SAAK,OAAO,KAAK,QAAQC,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AAC7D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AAC7D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AAC7D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,GAAI,CAAC;AAC7D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAK,CAAC;AAC9D,QAAI,KAAK,UAAU;AACjB,WAAK,OAAOH,SAAQ,KAAK,MAAM,EAAE;AACjC,WAAK,OAAOA,SAAQ,KAAK,MAAM,EAAE;AACjC,WAAK,OAAOA,SAAQ,KAAK,MAAM,EAAE;AACjC,WAAK,OAAOA,SAAQ,KAAK,MAAM,EAAE;AACjC,WAAK,OAAOA,SAAQ,KAAK,MAAM,EAAE;AACjC,WAAK,OAAOA,SAAQ,KAAK,MAAM,EAAE;AACjC,WAAK,OAAOA,SAAQ,KAAK,MAAM,EAAE;AACjC,WAAK,OAAOA,SAAQ,KAAK,MAAM,EAAE;AAAA,IACnC,OAAO;AACL,WAAK,OAAOE,QAAO,KAAK,MAAM,EAAE;AAChC,WAAK,OAAOA,QAAO,KAAK,MAAM,EAAE;AAChC,WAAK,OAAOA,QAAO,KAAK,MAAM,EAAE;AAChC,WAAK,OAAOA,QAAO,KAAK,MAAM,EAAE;AAChC,WAAK,OAAOA,QAAO,KAAK,MAAM,EAAE;AAChC,WAAK,OAAOA,QAAO,KAAK,MAAM,EAAE;AAChC,WAAK,OAAOA,QAAO,KAAK,MAAM,EAAE;AAChC,WAAK,OAAOA,QAAO,KAAK,MAAM,EAAE;AAAA,IAClC;AACA,SAAK,UAAU,KAAK,WAAWD,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,kBAAkB,KAAK,mBAAmBA,QAAO,KAAK,cAAc;AACzE,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmBA,QAAO,KAAK,cAAc;AACzE,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AACpD,SAAK,kBAAkB,KAAK,mBAAmB,KAAK;AAEpD,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,qBAAqB,KAAK,sBAAsB,KAAK;AAC1D,SAAK,iBAAiB,KAAK,aAAa,KAAK;AAC7C,SAAK,mBAAmB,KAAK,oBAAoB;AACjD,SAAK,mBAAmB,KAAK,oBAAoB,KAAK;AACtD,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,sBAAsB,KAAK,uBAAuB;AAKvD,SAAK,8BACH,KAAK,+BAA+B;AACtC,SAAK,+BACH,KAAK,gCAAgC;AAAA,EAEzC;AAAA,EACA,UAAU,WAAW;AACnB,QAAI,OAAO,cAAc,UAAU;AAEjC,WAAK,aAAa;AAClB;AAAA,IACF;AAEA,UAAM,OAAO,OAAO,KAAK,SAAS;AAGlC,SAAK,QAAQ,CAAC,MAAM;AAClB,WAAK,CAAC,IAAI,UAAU,CAAC;AAAA,IACvB,CAAC;AAGD,SAAK,aAAa;AAElB,SAAK,QAAQ,CAAC,MAAM;AAClB,WAAK,CAAC,IAAI,UAAU,CAAC;AAAA,IACvB,CAAC;AAAA,EACH;AACF;AAEO,IAAMI,qBAAoB,wBAAC,kBAAkB;AAClD,QAAM,QAAQ,IAAIN,OAAM;AACxB,QAAM,UAAU,aAAa;AAC7B,SAAO;AACT,GAJiC;;;ACxYjC,SAAS,UAAAO,SAAQ,UAAAC,SAAQ,WAAAC,UAAS,UAAAC,SAAQ,UAAAC,eAAc;AAUxD,IAAMC,SAAN,MAAY;AAAA,EAVZ,OAUY;AAAA;AAAA;AAAA,EACV,cAAc;AACZ,SAAK,eAAe;AACpB,SAAK,WAAW;AAChB,SAAK,iBAAiBC,SAAQ,KAAK,UAAU,EAAE;AAC/C,SAAK,aAAa;AAGlB,SAAK,gBAAgBC,QAAO,KAAK,cAAc,EAAE,GAAG,KAAK,CAAC;AAC1D,SAAK,qBAAqB,SAAS,KAAK,cAAc,KAAK,QAAQ;AACnE,SAAK,uBAAuB,SAAS,KAAK,gBAAgB,KAAK,QAAQ;AACvE,SAAK,sBAAsB,SAAS,KAAK,eAAe,KAAK,QAAQ;AAGrE,SAAK,mBAAmBC,QAAO,KAAK,YAAY;AAChD,SAAK,qBAAqBA,QAAO,KAAK,cAAc;AACpD,SAAK,oBAAoBA,QAAO,KAAK,aAAa;AAClD,SAAK,YAAYA,QAAO,KAAK,UAAU;AACvC,SAAK,YAAYA,QAAO,KAAK,UAAU;AAGvC,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,iBAAiB;AACtB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,oBAAoB;AAIzB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,gBAAgB;AACrB,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,SAAK,sBAAsB;AAI3B,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,iBAAiB;AACtB,SAAK,iBAAiB,KAAK;AAC3B,SAAK,cAAc;AACnB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AACxB,SAAK,sBAAsB;AAC3B,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,wBAAwB;AAC7B,SAAK,qBAAqB;AAC1B,SAAK,sBAAsB;AAI3B,SAAK,kBAAkB;AACvB,SAAK,qBAAqB;AAC1B,SAAK,mBAAmB;AACxB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,qBAAqB;AAC1B,SAAK,gBAAgB;AACrB,SAAK,oBAAoB;AACzB,SAAK,uBAAuB;AAC5B,SAAK,yBAAyB;AAC9B,SAAK,wBAAwB;AAC7B,SAAK,qBAAqB;AAC1B,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,sBAAsB;AAC3B,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AAGtB,SAAK,eAAe,KAAK;AACzB,SAAK,YAAY,KAAK;AAGtB,SAAK,gBAAgB;AACrB,SAAK,qBAAqB;AAC1B,SAAK,gBAAgB;AACrB,SAAK,uBAAuB,KAAK;AACjC,SAAK,uBAAuB;AAG5B,SAAK,SAAS,KAAK,UAAUF,SAAQ,KAAK,SAAS,EAAE,KAAK;AAC1D,SAAK,UAAU,KAAK,WAAW;AAG/B,SAAK,aAAa;AAElB,SAAK,gBAAgB;AACrB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,eAAe;AACb,SAAK,YAAYA,SAAQ,KAAK,UAAU,EAAE;AAC1C,SAAK,UAAU,KAAK;AAIpB,SAAK,cAAcA,SAAQ,KAAK,SAAS,EAAE;AAC3C,SAAK,WAAW,KAAK;AACrB,SAAK,iBAAiB,KAAK;AAC3B,SAAK,iBAAiB,KAAK;AAC3B,SAAK,cAAc,KAAK;AACxB,SAAK,kBAAkB,KAAK;AAC5B,SAAK,mBAAmB,KAAK;AAC7B,SAAK,sBAAsB,KAAK;AAChC,SAAK,iBAAiB,KAAK;AAC3B,SAAK,gBAAgB,KAAK;AAC1B,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,gBAAgB;AAKrB,SAAK,UAAU,KAAK,WAAW;AAC/B,SAAK,UAAU,KAAK,WAAW;AAC/B,SAAK,UAAU,KAAK,WAAW;AAC/B,SAAK,UAAU,KAAK,WAAW;AAC/B,SAAK,UAAU,KAAK,WAAW;AAC/B,SAAK,UAAU,KAAK,WAAW;AAC/B,SAAK,UAAU,KAAK,WAAW;AAC/B,SAAK,UAAU,KAAK,WAAW;AAC/B,SAAK,UAAU,KAAK,WAAW;AAC/B,SAAK,UAAU,KAAK,WAAW;AAC/B,SAAK,WAAW,KAAK,YAAY;AACjC,SAAK,WAAW,KAAK,YAAY;AAGjC,aAAS,IAAI,GAAG,IAAI,KAAK,mBAAmB,KAAK;AAC/C,WAAK,cAAc,CAAC,IAAI,KAAK,cAAc,CAAC,KAAKE,QAAO,KAAK,WAAW,CAAC,CAAC;AAAA,IAC5E;AAEA,aAAS,IAAI,GAAG,IAAI,KAAK,mBAAmB,KAAK;AAC/C,UAAI,KAAK,UAAU;AACjB,aAAK,eAAe,CAAC,IAAI,KAAK,eAAe,CAAC,KAAKF,SAAQ,KAAK,WAAW,CAAC,GAAG,EAAE;AAAA,MACnF,OAAO;AACL,aAAK,eAAe,CAAC,IAAI,KAAK,eAAe,CAAC,KAAKG,QAAO,KAAK,WAAW,CAAC,GAAG,EAAE;AAAA,MAClF;AAAA,IACF;AAGA,SAAK,kBAAkB,KAAK,oBAAoB,KAAK,WAAW,UAAU,KAAK;AAE/E,SAAK,eAAe,KAAK,gBAAgB,KAAK;AAC9C,SAAK,eAAe,KAAK,gBAAgB,KAAK;AAC9C,aAAS,IAAI,GAAG,IAAI,KAAK,mBAAmB,KAAK;AAC/C,WAAK,gBAAgB,CAAC,IAAI,KAAK,gBAAgB,CAAC,KAAK,KAAK;AAAA,IAC5D;AAEA,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,WAAK,YAAY,CAAC,IAAI,KAAK,YAAY,CAAC,KAAKF,QAAO,KAAK,SAAS,EAAE,GAAG,EAAE,IAAI,IAAI,GAAG,CAAC;AACrF,WAAK,gBAAgB,CAAC,IACpB,KAAK,gBAAgB,CAAC,KAAKA,QAAO,KAAK,SAAS,EAAE,GAAG,EAAE,IAAI,IAAI,GAAG,CAAC;AAAA,IACvE;AAIA,SAAK,UAAU,KAAK;AACpB,SAAK,aAAa,KAAK;AACvB,SAAK,aAAa,KAAK;AACvB,SAAK,gBAAgB,KAAK;AAC1B,SAAK,mBAAmB,KAAK;AAC7B,SAAK,aAAa,KAAK;AAIvB,SAAK,kBAAkBD,SAAQ,KAAK,UAAU,EAAE;AAChD,SAAK,mBAAmBA,SAAQ,KAAK,UAAU,EAAE;AAEjD,SAAK,kBAAkBG,QAAO,KAAK,UAAU,EAAE;AAE/C,SAAK,eAAe,KAAK;AACzB,SAAK,gBAAgB,KAAK;AAC1B,SAAK,oBAAoB,KAAK;AAC9B,SAAK,uBAAuB,KAAK;AACjC,SAAK,wBAAwB,KAAK;AAClC,SAAK,qBAAqB,KAAK;AAC/B,SAAK,YAAYH,SAAQ,KAAK,SAAS,EAAE;AAEzC,SAAK,mBAAmB,KAAK;AAC7B,SAAK,sBAAsB,KAAK;AAChC,SAAK,eAAe,KAAK;AACzB,SAAK,kBAAkBG,QAAO,KAAK,cAAc,EAAE;AAEnD,SAAK,iBAAiB,KAAK;AAG3B,SAAK,gBAAgB,KAAK;AAC1B,SAAK,qBAAqB,KAAK;AAG/B,SAAK,kBAAkB,KAAK,mBAAmB;AAC/C,SAAK,uBAAuB,KAAK,wBAAwB,KAAK;AAC9D,SAAK,kBAAkB,KAAK,mBAAmB,KAAK,YAAY,KAAK;AAErE,SAAK,WAAW,KAAK,YAAY,KAAK;AACtC,SAAK,uBAAuB,KAAK,wBAAwB,KAAK;AAC9D,SAAK,sBAAsB,KAAK,uBAAuB,KAAK,cAAc,KAAK;AAC/E,SAAK,gBAAgB,KAAK,iBAAiB;AAC3C,SAAK,2BAA2B,KAAK,4BAA4B,KAAK;AACtE,SAAK,cAAc,KAAK,eAAe;AACvC,SAAK,qBAAqB,KAAK;AAC/B,SAAK,oBAAoB;AAEzB,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,iBAAiB,KAAK,kBAAkB,KAAK;AAGlD,SAAK,YAAY,KAAK;AAEtB,SAAK,YAAY,KAAK;AACtB,SAAK,YAAY,KAAK;AACtB,SAAK,YAAYF,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,CAAC;AACpD,SAAK,YAAYA,QAAO,KAAK,gBAAgB,EAAE,GAAG,GAAG,CAAC;AACtD,SAAK,YAAYA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACrD,SAAK,YAAYA,QAAO,KAAK,gBAAgB,EAAE,GAAG,IAAI,CAAC;AACvD,SAAK,YAAYA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AACrD,SAAK,YAAYA,QAAO,KAAK,gBAAgB,EAAE,GAAG,IAAI,CAAC;AAIvD,aAAS,IAAI,GAAG,IAAI,KAAK,mBAAmB,KAAK;AAC/C,WAAK,QAAQ,CAAC,IAAI,KAAK,WAAW,CAAC;AAAA,IACrC;AACA,SAAK,QAAQ,KAAK;AAClB,SAAK,mBAAmB,KAAK,oBAAoB;AACjD,SAAK,oBAAoB,KAAK,qBAAqB,KAAK;AACxD,SAAK,qBAAqB,KAAK,sBAAsB;AACrD,SAAK,sBAAsB,KAAK,uBAAuB,KAAK;AAC5D,SAAK,oBAAoB,KAAK,qBAAqB;AACnD,SAAK,qBAAqB,KAAK,sBAAsB,KAAK;AAC1D,SAAK,iBAAiB,KAAK,kBAAkB;AAC7C,SAAK,iBAAiB,KAAK,kBAAkB;AAC7C,SAAK,sBAAsB,KAAK,uBAAuB;AACvD,SAAK,sBAAsB,KAAK,uBAAuB;AACvD,SAAK,aAAa,KAAK,cAAc;AAGrC,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,gBAAgB,KAAK,iBAAiBA,QAAO,KAAK,cAAc,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;AACzF,SAAK,gBAAgB,KAAK,iBAAiBA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;AAC5F,SAAK,gBAAgB,KAAK,iBAAiBA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;AAC5F,SAAK,oBAAoB,KAAK,qBAAqB,KAAK;AACxD,SAAK,oBACH,KAAK,qBAAqBA,QAAO,KAAK,kBAAkB,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;AACjF,SAAK,oBACH,KAAK,qBAAqBA,QAAO,KAAK,kBAAkB,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;AACpF,SAAK,oBACH,KAAK,qBAAqBA,QAAO,KAAK,kBAAkB,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;AACpF,SAAK,oBACH,KAAK,qBAAqBG,QAAO,KAAK,aAAa,IAC/CJ,SAAQ,KAAK,aAAa,IAC1BG,QAAO,KAAK,aAAa;AAC/B,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,mCACH,KAAK,oCAAoC,KAAK;AAChD,SAAK,mCACH,KAAK,oCAAoC,KAAK;AAChD,SAAK,oBAAoB,KAAK,qBAAqB,KAAK;AAGxD,SAAK,UAAU;AAAA,MACb,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,YAAY,KAAK,SAAS,cAAc,KAAK;AAAA,MAC7C,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,gBAAgB,KAAK,SAAS,kBAAkB,KAAK;AAAA,MACrD,gBAAgB,KAAK,SAAS,kBAAkB,KAAK;AAAA,MACrD,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,iBAAiB,KAAK,SAAS,mBAAmB,KAAK;AAAA,MACvD,gBAAgB,KAAK,SAAS,kBAAkB,KAAK;AAAA,MACrD,gBAAgB,KAAK,SAAS,kBAAkB,KAAK;AAAA,MACrD,kBACE,KAAK,SAAS,oBACd;AAAA,IACJ;AAGA,SAAK,QAAQ;AAAA,MACX,WAAW,KAAK,OAAO,aAAa,KAAK;AAAA,MACzC,iBAAiB,KAAK,OAAO,mBAAmB;AAAA,MAChD,mBAAmB,KAAK,OAAO,qBAAqB;AAAA,MACpD,cAAc,KAAK,OAAO,gBAAgB;AAAA,MAC1C,kBAAkB,KAAK,OAAO,oBAAoB;AAAA,MAClD,gBAAgB,KAAK,OAAO,kBAAkB;AAAA,MAC9C,sBAAsB,KAAK,OAAO,wBAAwB;AAAA,MAC1D,kBAAkB,KAAK,OAAO,oBAAoB;AAAA,MAClD,eAAe,KAAK,OAAO,iBAAiB;AAAA,MAC5C,gBAAgB,KAAK,OAAO,kBAAkB;AAAA,IAChD;AAGA,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,yBAAyB,KAAK,0BAA0B,KAAK;AAClE,SAAK,wBAAwB,KAAK,yBAAyB;AAC3D,SAAK,uBAAuB,KAAK,wBAAwB,KAAK;AAC9D,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,0BAA0B,KAAK,2BAA2B,KAAK;AACpE,SAAK,qBAAqB,KAAK,sBAAsB,KAAK;AAG1D,SAAK,OAAOA,QAAO,KAAK,MAAM,EAAE,KAAK,KAAK;AAC1C,SAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,SAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,SAAK,OAAO,KAAK,QAAQF,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AAC7D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AAC7D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAI,CAAC;AAC7D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,GAAI,CAAC;AAC7D,SAAK,OAAO,KAAK,QAAQA,QAAO,KAAK,cAAc,EAAE,GAAG,IAAK,CAAC;AAE9D,SAAK,UAAU,KAAK,WAAWC,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAC/C,SAAK,UAAU,KAAK,WAAWA,QAAO,KAAK,IAAI;AAE/C,SAAK,mBAAmB,KAAK,oBAAoB,KAAK;AACtD,SAAK,kBAAkB,KAAK;AAC5B,SAAK,kBAAkB;AACvB,SAAK,kBAAkB,KAAK;AAC5B,SAAK,kBAAkB;AACvB,SAAK,kBAAkB,KAAK;AAC5B,SAAK,kBAAkB,KAAK;AAC5B,SAAK,kBAAkB,KAAK;AAC5B,SAAK,kBAAkB,KAAK;AAE5B,SAAK,gBAAgB,KAAK,iBAAiB,KAAK;AAChD,SAAK,qBAAqB,KAAK,sBAAsB,KAAK;AAC1D,SAAK,iBAAiB,KAAK,aAAa,KAAK;AAC7C,SAAK,mBAAmB,KAAK,oBAAoB;AACjD,SAAK,mBAAmB,KAAK,oBAAoB,KAAK;AACtD,SAAK,wBAAwB,KAAK,yBAAyB,KAAK;AAChE,SAAK,sBAAsB,KAAK,uBAAuB;AAKvD,SAAK,8BACH,KAAK,+BAA+B;AACtC,SAAK,+BACH,KAAK,gCAAgC;AAAA,EAEzC;AAAA,EACA,UAAU,WAAW;AACnB,QAAI,OAAO,cAAc,UAAU;AAEjC,WAAK,aAAa;AAClB;AAAA,IACF;AAEA,UAAM,OAAO,OAAO,KAAK,SAAS;AAGlC,SAAK,QAAQ,CAAC,MAAM;AAClB,WAAK,CAAC,IAAI,UAAU,CAAC;AAAA,IACvB,CAAC;AAGD,SAAK,aAAa;AAElB,SAAK,QAAQ,CAAC,MAAM;AAClB,WAAK,CAAC,IAAI,UAAU,CAAC;AAAA,IACvB,CAAC;AAAA,EACH;AACF;AAEO,IAAMG,qBAAoB,wBAAC,kBAAkB;AAClD,QAAM,QAAQ,IAAIN,OAAM;AACxB,QAAM,UAAU,aAAa;AAC7B,SAAO;AACT,GAJiC;;;ACvYjC,IAAO,iBAAQ;AAAA,EACb,MAAM;AAAA,IACJ;AAAA,EACF;AAAA,EACA,MAAM;AAAA,IACJ,mBAAmBO;AAAA,EACrB;AAAA,EACA,SAAS;AAAA,IACP,mBAAmBA;AAAA,EACrB;AAAA,EACA,QAAQ;AAAA,IACN,mBAAmBA;AAAA,EACrB;AAAA,EACA,SAAS;AAAA,IACP,mBAAmBA;AAAA,EACrB;AACF;;;ACtBA,IAAO,wBAAQ;AAAA,EACb,aAAa;AAAA,IACX,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,uBAAuB;AAAA,MACrB,OAAO;AAAA,MACP,UAAU;AAAA,IACZ;AAAA,IACA,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,eAAe;AAAA,IACf,eAAe;AAAA,IACf,SAAS;AAAA,IACT,WAAW;AAAA,IACX,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,EACnB;AAAA,EACA,YAAY;AAAA,IACV,eAAe;AAAA,IACf,0BAA0B;AAAA,IAC1B,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,EACpB;AAAA,EACA,SAAS;AAAA,IACP,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,UAAU;AAAA,IACV,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,wBAAwB;AAAA,IACxB,YAAY;AAAA,IACZ,mBAAmB;AAAA,IACnB,uBAAuB;AAAA,IACvB,cAAc;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,IACf,WAAW;AAAA,EACb;AAAA,EACA,WAAW;AAAA,IACT,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,cAAc;AAAA,IACd,uBAAuB;AAAA,EACzB;AAAA,EACA,SAAS;AAAA,IACP,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,cAAc;AAAA,IACd,aAAa;AAAA,IACb,cAAc;AAAA,IACd,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,UAAU;AAAA,IACV,mBAAmB;AAAA,EACrB;AAAA,EACA,MAAM;AAAA,IACJ,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,eAAe;AAAA,IACf,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,YAAY;AAAA,EACd;AAAA,EACA,OAAO;AAAA,IACL,eAAe;AAAA,IACf,gBAAgB;AAAA,EAClB;AAAA,EACA,iBAAiB;AAAA,IACf,eAAe;AAAA,IACf,cAAc;AAAA,IACd,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,sBAAsB;AAAA,IACtB,sBAAsB;AAAA,IACtB,yBAAyB;AAAA,IACzB,0BAA0B;AAAA,IAC1B,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,IACtB,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,qCAAqC;AAAA,IACrC,qCAAqC;AAAA,EACvC;AAAA,EACA,WAAW;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,IACT,UAAU;AAAA,IACV,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,IACnB;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,IACnB;AAAA,IACA,oBAAoB;AAAA,IACpB,4BAA4B;AAAA,EAC9B;AAAA,EACA,eAAe;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,eAAe;AAAA,EACjB;AAAA,EACA,WAAW;AAAA,IACT,eAAe;AAAA,IACf,WAAW;AAAA,IACX,gBAAgB;AAAA,EAClB;AAAA,EACA,UAAU;AAAA,IACR,eAAe;AAAA,IACf,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,EACnB;AAAA,EACA,YAAY;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,EACvB;AAAA,EACA,YAAY;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,aAAa;AAAA,MACX,SAAS;AAAA,MACT,UAAU;AAAA,MACV,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,mBAAmB;AAAA,IACnB,uBAAuB;AAAA,EACzB;AAAA,EACA,MAAM;AAAA,IACJ,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,2BAA2B;AAAA,IAC3B,6BAA6B;AAAA,IAC7B,6BAA6B;AAAA,IAC7B,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,2BAA2B;AAAA,IAC3B,6BAA6B;AAAA,IAC7B,6BAA6B;AAAA,IAC7B,qBAAqB;AAAA,IACrB,uBAAuB;AAAA,IACvB,uBAAuB;AAAA,IACvB,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,gCAAgC;AAAA,IAChC,wBAAwB;AAAA,IACxB,0BAA0B;AAAA,IAC1B,0BAA0B;AAAA,IAC1B,iCAAiC;AAAA,IACjC,mCAAmC;AAAA,IACnC,mCAAmC;AAAA,IACnC,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,IACtB,sBAAsB;AAAA,IACtB,mBAAmB;AAAA,IACnB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,uBAAuB;AAAA,IACvB,uBAAuB;AAAA,IACvB,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,gCAAgC;AAAA,IAChC,wBAAwB;AAAA,IACxB,0BAA0B;AAAA,IAC1B,0BAA0B;AAAA,IAC1B,iCAAiC;AAAA,IACjC,mCAAmC;AAAA,IACnC,mCAAmC;AAAA,IACnC,2BAA2B;AAAA,IAC3B,6BAA6B;AAAA,IAC7B,6BAA6B;AAAA,IAC7B,oCAAoC;AAAA,IACpC,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,qBAAqB;AAAA,IACrB,uBAAuB;AAAA,IACvB,uBAAuB;AAAA,IACvB,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,gCAAgC;AAAA,IAChC,wBAAwB;AAAA,IACxB,0BAA0B;AAAA,IAC1B,0BAA0B;AAAA,IAC1B,iCAAiC;AAAA,IACjC,mCAAmC;AAAA,IACnC,mCAAmC;AAAA,IACnC,2BAA2B;AAAA,IAC3B,6BAA6B;AAAA,IAC7B,6BAA6B;AAAA,IAC7B,oCAAoC;AAAA,IACpC,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,uBAAuB;AAAA,IACvB,4BAA4B;AAAA,IAC5B,gCAAgC;AAAA,IAChC,mBAAmB;AAAA,IACnB,uBAAuB;AAAA,IACvB,sBAAsB;AAAA,IACtB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,gCAAgC;AAAA,IAChC,+BAA+B;AAAA,IAC/B,mCAAmC;AAAA,IACnC,kCAAkC;AAAA,IAClC,sCAAsC;AAAA,IACtC,sBAAsB;AAAA,IACtB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,gCAAgC;AAAA,IAChC,+BAA+B;AAAA,IAC/B,mCAAmC;AAAA,IACnC,kCAAkC;AAAA,IAClC,sCAAsC;AAAA,IACtC,qCAAqC;AAAA,IACrC,yCAAyC;AAAA,IACzC,sBAAsB;AAAA,IACtB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,gCAAgC;AAAA,IAChC,+BAA+B;AAAA,IAC/B,mCAAmC;AAAA,IACnC,kCAAkC;AAAA,IAClC,sCAAsC;AAAA,IACtC,qCAAqC;AAAA,IACrC,yCAAyC;AAAA,EAC3C;AAAA,EACA,UAAU;AAAA,IACR,eAAe;AAAA,IACf,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,UAAU;AAAA,IACV,UAAU;AAAA,EACZ;AAAA,EACA,SAAS;AAAA,IACP,eAAe;AAAA,IACf,WAAW;AAAA,EACb;AAAA,EACA,UAAU;AAAA,IACR,eAAe;AAAA,IACf,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,gBAAgB;AAAA,IACd,eAAe;AAAA,IACf,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,SAAS;AAAA,IACP,eAAe;AAAA,IACf,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,EAClB;AAAA,EACA,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,iBAAiB;AAAA,EACjB,UAAU;AAAA,EACV,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,eAAe;AAAA,EACf,uBAAuB;AAAA,EACvB,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,oBAAoB;AAAA,EACpB,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,0BAA0B;AAC5B;;;ACrdA,IAAM,SAAsC;AAAA,EAC1C,GAAG;AAAA;AAAA;AAAA,EAGH,qBAAqB;AAAA,EACrB,KAAK;AAAA;AAAA,IAEH,YAAY;AAAA,IACZ,uBAAuB;AAAA,EACzB;AAAA,EACA,UAAU;AAAA;AAAA,EAGV,gBAAgB,eAAM,QAAQ,kBAAkB;AAAA,EAChD,UAAU;AAAA,IACR,GAAG,sBAAkB;AAAA,IACrB,aAAa,kCAAY;AACvB,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GANa;AAAA,IAOb,UAAU,kCAAY;AACpB,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GANU;AAAA,IAOV,WAAW,kCAAY;AACrB,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GANW;AAAA,EAOb;AAAA,EACA,OAAO;AAAA,IACL,qBAAqB;AAAA,EACvB;AAAA,EACA,OAAO;AAAA,IACL,GAAG,sBAAkB;AAAA,IACrB,cAAc;AAAA,IACd,UAAU;AAAA;AAAA,EACZ;AAAA,EACA,IAAI;AAAA,IACF,GAAG,sBAAkB;AAAA,IACrB,UAAU;AAAA,IACV,YAAY,kCAAY;AACtB,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GANY;AAAA,IAQZ,qBAAqB,kCAAY;AAC/B,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GANqB;AAAA,IAQrB,YAAY,kCAAY;AACtB,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GANY;AAAA,IAQZ,qBAAqB,kCAAY;AAC/B,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GANqB;AAAA,IAQrB,eAAe,kCAAY;AACzB,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GANe;AAAA,IAQf,wBAAwB,kCAAY;AAClC,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GANwB;AAAA,IAQxB,kBAAkB,kCAAY;AAC5B,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GANkB;AAAA,IAQlB,2BAA2B,kCAAY;AACrC,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GAN2B;AAAA,IAQ3B,eAAe,kCAAY;AACzB,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GANe;AAAA,IAQf,wBAAwB,kCAAY;AAClC,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GANwB;AAAA,IAQxB,kBAAkB,kCAAY;AAC5B,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GANkB;AAAA,IAQlB,2BAA2B,kCAAY;AACrC,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GAN2B;AAAA,IAQ3B,qBAAqB,kCAAY;AAC/B,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GANqB;AAAA,IAQrB,8BAA8B,kCAAY;AACxC,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GAN8B;AAAA,IAQ9B,eAAe,kCAAY;AACzB,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GANe;AAAA,IAQf,wBAAwB,kCAAY;AAClC,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GANwB;AAAA,IAQxB,kBAAkB,kCAAY;AAC5B,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GANkB;AAAA,IAQlB,2BAA2B,kCAAY;AACrC,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GAN2B;AAAA,IAQ3B,qBAAqB,kCAAY;AAC/B,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GANqB;AAAA,IAQrB,8BAA8B,kCAAY;AACxC,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GAN8B;AAAA,IAQ9B,cAAc,kCAAY;AACxB,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GANc;AAAA,IAQd,aAAa,kCAAY;AACvB,aAAO;AAAA,QACL,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,QACf,YAAY,KAAK;AAAA,MACnB;AAAA,IACF,GANa;AAAA,EAOf;AAAA,EACA,KAAK;AAAA,IACH,GAAG,sBAAkB;AAAA,IACrB,UAAU;AAAA,EACZ;AAAA,EACA,SAAS;AAAA,IACP,GAAG,sBAAkB;AAAA,IACrB,UAAU;AAAA,EACZ;AAAA,EACA,aAAa;AAAA,IACX,GAAG,sBAAkB;AAAA,IACrB,UAAU;AAAA,EACZ;AAAA,EACA,QAAQ;AAAA,IACN,GAAG,sBAAkB;AAAA,EACvB;AAAA,EACA,OAAO;AAAA,IACL,GAAG,sBAAkB;AAAA,EACvB;AACF;AAGA,IAAM,SAAS,wBAAC,KAAU,SAAS,OACjC,OAAO,KAAK,GAAG,EAAE,OAAO,CAAC,KAAe,OAAiB;AACvD,MAAI,MAAM,QAAQ,IAAI,EAAE,CAAC,GAAG;AAC1B,WAAO;AAAA,EACT,WAAW,OAAO,IAAI,EAAE,MAAM,YAAY,IAAI,EAAE,MAAM,MAAM;AAC1D,WAAO,CAAC,GAAG,KAAK,SAAS,IAAI,GAAG,OAAO,IAAI,EAAE,GAAG,EAAE,CAAC;AAAA,EACrD;AACA,SAAO,CAAC,GAAG,KAAK,SAAS,EAAE;AAC7B,GAAG,CAAC,CAAC,GARQ;AAUR,IAAM,aAAa,IAAI,IAAY,OAAO,QAAQ,EAAE,CAAC;AAC5D,IAAO,wBAAQ;;;AC1QR,IAAM,oBAAoB,wBAAC,SAAoB;AACpD,MAAI,MAAM,iCAAiC,IAAI;AAG/C,MAAI,OAAO,SAAS,YAAY,QAAQ,MAAM;AAC5C;AAAA,EACF;AAGA,MAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,SAAK,QAAQ,CAAC,QAAQ,kBAAkB,GAAG,CAAC;AAC5C;AAAA,EACF;AAGA,aAAW,OAAO,OAAO,KAAK,IAAI,GAAG;AACnC,QAAI,MAAM,gBAAgB,GAAG;AAC7B,QACE,IAAI,WAAW,IAAI,KACnB,IAAI,SAAS,OAAO,KACpB,IAAI,SAAS,QAAQ,KACrB,CAAC,WAAW,IAAI,GAAG,KACnB,KAAK,GAAG,KAAK,MACb;AACA,UAAI,MAAM,2BAA2B,GAAG;AACxC,aAAO,KAAK,GAAG;AACf;AAAA,IACF;AAGA,QAAI,OAAO,KAAK,GAAG,MAAM,UAAU;AACjC,UAAI,MAAM,qBAAqB,GAAG;AAClC,wBAAkB,KAAK,GAAG,CAAC;AAC3B;AAAA,IACF;AAEA,UAAM,cAAc,CAAC,YAAY,cAAc,eAAe;AAC9D,eAAW,UAAU,aAAa;AAChC,UAAI,IAAI,SAAS,MAAM,GAAG;AACxB,YAAI,MAAM,yBAAyB,GAAG;AACtC,aAAK,GAAG,IAAI,YAAY,KAAK,GAAG,CAAC;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAEA,MAAI,KAAK,gBAAgB;AACvB,eAAW,KAAK,OAAO,KAAK,KAAK,cAAc,GAAG;AAChD,YAAM,MAAM,KAAK,eAAe,CAAC;AACjC,UAAI,KAAK,SAAS,CAAC,IAAI,MAAM,wBAAwB,GAAG;AACtD,aAAK,eAAe,CAAC,IAAI;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACA,MAAI,MAAM,sBAAsB,IAAI;AACtC,GAtDiC;AAwD1B,IAAM,cAAc,wBAAC,QAAwB;AAClD,MAAI,WAAW;AACf,MAAI,SAAS;AAEb,aAAW,WAAW,KAAK;AACzB,QAAI,WAAW,QAAQ;AACrB,aAAO;AAAA,IACT;AACA,QAAI,YAAY,KAAK;AACnB;AAAA,IACF,WAAW,YAAY,KAAK;AAC1B;AAAA,IACF;AAAA,EACF;AACA,MAAI,aAAa,QAAQ;AACvB,WAAO;AAAA,EACT;AAEA,SAAO;AACT,GAnB2B;;;ACzDpB,IAAM,gBAA+B,OAAO,OAAO,qBAAM;AAEhE,IAAI,aAA4B,wBAAgB,CAAC,GAAG,aAAa;AACjE,IAAI;AACJ,IAAI,aAA8B,CAAC;AACnC,IAAI,gBAA+B,wBAAgB,CAAC,GAAG,aAAa;AAE7D,IAAM,sBAAsB,wBAAC,SAAwB,gBAAiC;AAE3F,MAAI,MAAqB,wBAAgB,CAAC,GAAG,OAAO;AAIpD,MAAI,kBAAiC,CAAC;AACtC,aAAW,KAAK,aAAa;AAC3B,aAAS,CAAC;AAEV,sBAAkB,wBAAgB,iBAAiB,CAAC;AAAA,EACtD;AAEA,QAAM,wBAAgB,KAAK,eAAe;AAE1C,MAAI,gBAAgB,SAAS,gBAAgB,SAAS,gBAAO;AAC3D,UAAM,0BAA0B,wBAAgB,CAAC,GAAG,oBAAoB;AACxE,UAAM,iBAAiB;AAAA,MACrB,wBAAwB,kBAAkB,CAAC;AAAA,MAC3C,gBAAgB;AAAA,IAClB;AACA,QAAI,IAAI,SAAS,IAAI,SAAS,gBAAO;AACnC,UAAI,iBAAiB,eAAM,IAAI,KAA2B,EAAE,kBAAkB,cAAc;AAAA,IAC9F;AAAA,EACF;AAEA,kBAAgB;AAChB,cAAY,aAAa;AACzB,SAAO;AACT,GA7BmC;AA8C5B,IAAM,gBAAgB,wBAAC,SAAuC;AACnE,eAAa,wBAAgB,CAAC,GAAG,aAAa;AAC9C,eAAa,wBAAgB,YAAY,IAAI;AAG7C,MAAI,KAAK,SAAS,eAAM,KAAK,KAAK,GAAG;AAEnC,eAAW,iBAAiB,eAAM,KAAK,KAAK,EAAE,kBAAkB,KAAK,cAAc;AAAA,EACrF;AAEA,sBAAoB,YAAY,UAAU;AAC1C,SAAO;AACT,GAZ6B;AActB,IAAM,2BAA2B,wBAAC,SAA8B;AACrE,yBAAuB,wBAAgB,CAAC,GAAG,IAAI;AACjD,GAFwC;AAIjC,IAAM,mBAAmB,wBAAC,SAAuC;AACtE,eAAa,wBAAgB,YAAY,IAAI;AAC7C,sBAAoB,YAAY,UAAU;AAE1C,SAAO;AACT,GALgC;AAiBzB,IAAM,gBAAgB,6BAAqB;AAChD,SAAO,wBAAgB,CAAC,GAAG,UAAU;AACvC,GAF6B;AAiBtB,IAAM,YAAY,wBAAC,SAAuC;AAC/D,cAAY,IAAI;AAChB,0BAAgB,eAAe,IAAI;AAEnC,SAAO,UAAU;AACnB,GALyB;AAkBlB,IAAM,YAAY,6BAAqB;AAC5C,SAAO,wBAAgB,CAAC,GAAG,aAAa;AAC1C,GAFyB;AAelB,IAAM,WAAW,wBAAC,YAAiB;AACxC,MAAI,CAAC,SAAS;AACZ;AAAA,EACF;AAEA,GAAC,UAAU,GAAI,WAAW,UAAU,CAAC,CAAE,EAAE,QAAQ,CAAC,QAAQ;AACxD,QAAI,OAAO,OAAO,SAAS,GAAG,GAAG;AAG/B,UAAI,MAAM,yCAAyC,GAAG,IAAI,QAAQ,GAAG,CAAC;AACtE,aAAO,QAAQ,GAAG;AAAA,IACpB;AAAA,EACF,CAAC;AAGD,SAAO,KAAK,OAAO,EAAE,QAAQ,CAAC,QAAQ;AACpC,QAAI,IAAI,WAAW,IAAI,GAAG;AACxB,aAAO,QAAQ,GAAG;AAAA,IACpB;AAAA,EACF,CAAC;AAGD,SAAO,KAAK,OAAO,EAAE,QAAQ,CAAC,QAAQ;AACpC,QACE,OAAO,QAAQ,GAAG,MAAM,aACvB,QAAQ,GAAG,EAAE,SAAS,GAAG,KACxB,QAAQ,GAAG,EAAE,SAAS,GAAG,KACzB,QAAQ,GAAG,EAAE,SAAS,WAAW,IACnC;AACA,aAAO,QAAQ,GAAG;AAAA,IACpB;AACA,QAAI,OAAO,QAAQ,GAAG,MAAM,UAAU;AACpC,eAAS,QAAQ,GAAG,CAAC;AAAA,IACvB;AAAA,EACF,CAAC;AACH,GAnCwB;AA0CjB,IAAM,eAAe,wBAAC,cAA6B;AACxD,oBAAkB,SAAS;AAG3B,MAAI,UAAU,cAAc,CAAC,UAAU,gBAAgB,YAAY;AACjE,cAAU,iBAAiB;AAAA,MACzB,GAAG,UAAU;AAAA,MACb,YAAY,UAAU;AAAA,IACxB;AAAA,EACF;AAEA,aAAW,KAAK,SAAS;AACzB,sBAAoB,YAAY,UAAU;AAC5C,GAb4B;AAiCrB,IAAM,QAAQ,wBAACC,UAAS,eAAqB;AAElD,eAAa,CAAC;AACd,sBAAoBA,SAAQ,UAAU;AACxC,GAJqB;AAMrB,IAAM,gBAAgB;AAAA,EACpB,sBACE;AACJ;AAGA,IAAM,iBAAiE,CAAC;AACxE,IAAM,eAAe,wBAAC,YAAkC;AACtD,MAAI,eAAe,OAAO,GAAG;AAC3B;AAAA,EACF;AACA,MAAI,KAAK,cAAc,OAAO,CAAC;AAC/B,iBAAe,OAAO,IAAI;AAC5B,GANqB;AAQrB,IAAM,cAAc,wBAACA,YAA0B;AAC7C,MAAI,CAACA,SAAQ;AACX;AAAA,EACF;AAEA,MAAIA,QAAO,sBAAsBA,QAAO,+BAA+B;AACrE,iBAAa,sBAAsB;AAAA,EACrC;AACF,GARoB;;;ACjPpB,OAAO,eAAe;AAIf,IAAM,iBAAiB;AAQvB,IAAM,UAAU,wBAAC,MAAyB;AAC/C,MAAI,CAAC,GAAG;AACN,WAAO,CAAC,EAAE;AAAA,EACZ;AACA,QAAM,MAAM,mBAAmB,CAAC,EAAE,QAAQ,QAAQ,MAAM;AACxD,SAAO,IAAI,MAAM,MAAM;AACzB,GANuB;AAQvB,IAAM,gCAAiC,uBAAM;AAC3C,MAAI,QAAQ;AAEZ,SAAO,MAAM;AACX,QAAI,CAAC,OAAO;AACV,0BAAoB;AACpB,cAAQ;AAAA,IACV;AAAA,EACF;AACF,GAAG;AAEH,SAAS,sBAAsB;AAC7B,QAAM,sBAAsB;AAE5B,YAAU,QAAQ,4BAA4B,CAAC,SAAS;AACtD,QAAI,gBAAgB,WAAW,KAAK,YAAY,OAAO,KAAK,aAAa,QAAQ,GAAG;AAClF,WAAK,aAAa,qBAAqB,KAAK,aAAa,QAAQ,KAAK,EAAE;AAAA,IAC1E;AAAA,EACF,CAAC;AAED,YAAU,QAAQ,2BAA2B,CAAC,SAAS;AACrD,QAAI,gBAAgB,WAAW,KAAK,YAAY,OAAO,KAAK,aAAa,mBAAmB,GAAG;AAC7F,WAAK,aAAa,UAAU,KAAK,aAAa,mBAAmB,KAAK,EAAE;AACxE,WAAK,gBAAgB,mBAAmB;AACxC,UAAI,KAAK,aAAa,QAAQ,MAAM,UAAU;AAC5C,aAAK,aAAa,OAAO,UAAU;AAAA,MACrC;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAlBS;AA0BF,IAAM,eAAe,wBAAC,QAAwB;AACnD,gCAA8B;AAE9B,QAAM,gBAAgB,UAAU,SAAS,GAAG;AAE5C,SAAO;AACT,GAN4B;AAQ5B,IAAM,eAAe,wBAAC,MAAcC,YAA0B;AAC5D,MAAIA,QAAO,WAAW,eAAe,OAAO;AAC1C,UAAM,QAAQA,QAAO;AACrB,QAAI,UAAU,gBAAgB,UAAU,UAAU;AAChD,aAAO,aAAa,IAAI;AAAA,IAC1B,WAAW,UAAU,SAAS;AAC5B,aAAO,mBAAmB,IAAI;AAC9B,aAAO,KAAK,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,MAAM;AACtD,aAAO,KAAK,QAAQ,MAAM,UAAU;AACpC,aAAO,mBAAmB,IAAI;AAAA,IAChC;AAAA,EACF;AACA,SAAO;AACT,GAbqB;AAed,IAAM,eAAe,wBAAC,MAAcA,YAAkC;AAC3E,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,MAAIA,QAAO,iBAAiB;AAC1B,WAAO,UAAU,SAAS,aAAa,MAAMA,OAAM,GAAGA,QAAO,eAAe,EAAE,SAAS;AAAA,EACzF,OAAO;AACL,WAAO,UAAU,SAAS,aAAa,MAAMA,OAAM,GAAG;AAAA,MACpD,aAAa,CAAC,OAAO;AAAA,IACvB,CAAC,EAAE,SAAS;AAAA,EACd;AACA,SAAO;AACT,GAZ4B;AAcrB,IAAM,sBAAsB,wBACjC,GACAA,YACsB;AACtB,MAAI,OAAO,MAAM,UAAU;AACzB,WAAO,aAAa,GAAGA,OAAM;AAAA,EAC/B;AAEA,SAAO,EAAE,KAAK,EAAE,IAAI,CAAC,MAAc,aAAa,GAAGA,OAAM,CAAC;AAC5D,GATmC;AAiB5B,IAAM,YAAY,wBAAC,SAA0B;AAClD,SAAO,eAAe,KAAK,IAAI;AACjC,GAFyB;AAUlB,IAAM,cAAc,wBAAC,SAA2B;AACrD,SAAO,KAAK,MAAM,cAAc;AAClC,GAF2B;AAU3B,IAAM,qBAAqB,wBAAC,MAAsB;AAChD,SAAO,EAAE,QAAQ,SAAS,OAAO;AACnC,GAF2B;AAU3B,IAAM,qBAAqB,wBAAC,MAAsB;AAChD,SAAO,EAAE,QAAQ,gBAAgB,MAAM;AACzC,GAF2B;AAU3B,IAAM,SAAS,wBAAC,gBAAiC;AAC/C,MAAI,MAAM;AACV,MAAI,aAAa;AACf,UACE,OAAO,SAAS,WAChB,OACA,OAAO,SAAS,OAChB,OAAO,SAAS,WAChB,OAAO,SAAS;AAClB,UAAM,IAAI,WAAW,OAAO,KAAK;AACjC,UAAM,IAAI,WAAW,OAAO,KAAK;AAAA,EACnC;AAEA,SAAO;AACT,GAde;AAsBR,IAAM,WAAW,wBAAC,QACvB,QAAQ,SAAS,CAAC,SAAS,QAAQ,GAAG,EAAE,SAAS,OAAO,GAAG,EAAE,KAAK,EAAE,YAAY,CAAC,IAAI,QAAQ,MADvE;AASjB,IAAM,SAAS,mCAAa,QAA0B;AAC3D,QAAM,YAAsB,OAAO,OAAO,CAAC,UAAU;AACnD,WAAO,CAAC,MAAM,KAAK;AAAA,EACrB,CAAC;AACD,SAAO,KAAK,IAAI,GAAG,SAAS;AAC9B,GALsB;AAaf,IAAM,SAAS,mCAAa,QAA0B;AAC3D,QAAM,YAAsB,OAAO,OAAO,CAAC,UAAU;AACnD,WAAO,CAAC,MAAM,KAAK;AAAA,EACrB,CAAC;AACD,SAAO,KAAK,IAAI,GAAG,SAAS;AAC9B,GALsB;AAoBf,IAAM,oBAAoB,gCAAU,OAAuB;AAChE,QAAM,YAAY,MAAM,MAAM,KAAK;AACnC,QAAM,SAAS,CAAC;AAEhB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,UAAU,UAAU,CAAC;AAQzB,QAAI,YAAY,OAAO,IAAI,KAAK,IAAI,IAAI,UAAU,QAAQ;AACxD,YAAM,cAAc,UAAU,IAAI,CAAC;AACnC,YAAM,UAAU,UAAU,IAAI,CAAC;AAE/B,UAAI,kBAAkB,aAAa,OAAO,GAAG;AAC3C,kBAAU,cAAc,MAAM;AAC9B;AACA,eAAO,IAAI;AAAA,MACb;AAAA,IACF;AAEA,WAAO,KAAK,WAAW,OAAO,CAAC;AAAA,EACjC;AAEA,SAAO,OAAO,KAAK,EAAE;AACvB,GA5BiC;AA8B1B,IAAM,kBAAkB,wBAAC,QAAgB,cAA8B;AAC5E,SAAO,KAAK,IAAI,GAAG,OAAO,MAAM,SAAS,EAAE,SAAS,CAAC;AACvD,GAF+B;AAI/B,IAAM,oBAAoB,wBAAC,aAAqB,YAA6B;AAC3E,QAAM,YAAY,gBAAgB,aAAa,GAAG;AAClD,QAAM,YAAY,gBAAgB,SAAS,GAAG;AAE9C,SAAO,cAAc,KAAK,cAAc;AAC1C,GAL0B;AAO1B,IAAM,aAAa,wBAAC,UAA0B;AAC5C,QAAM,aAAa,gBAAgB,OAAO,GAAG;AAC7C,MAAI,mBAAmB;AAEvB,MAAI,cAAc,GAAG;AACnB,WAAO;AAAA,EACT;AAGA,MAAI,aAAa,MAAM,KAAK,MAAM,WAAW,GAAG,GAAG;AACjD,YAAQ,MAAM,UAAU,CAAC;AACzB,uBAAmB;AAAA,EACrB;AAEA,QAAM,QAAQ,CAAC,GAAG,KAAK;AAEvB,MAAI,QAAQ,MAAM,QAAQ,GAAG;AAC7B,MAAI,OAAO,MAAM,YAAY,GAAG;AAEhC,SAAO,UAAU,MAAM,SAAS,MAAM,UAAU,MAAM;AACpD,UAAM,KAAK,IAAI;AACf,UAAM,IAAI,IAAI;AAEd,YAAQ,MAAM,QAAQ,GAAG;AACzB,WAAO,MAAM,YAAY,GAAG;AAAA,EAC9B;AAGA,MAAI,kBAAkB;AACpB,UAAM,QAAQ,GAAG;AAAA,EACnB;AAEA,SAAO,MAAM,KAAK,EAAE;AACtB,GAjCmB;AAqCZ,IAAM,oBAAoB,6BAAM,OAAO,kBAAkB,QAA/B;AAE1B,IAAM,aAAa;AAQnB,IAAM,WAAW,wBAAC,UAA2B,KAAK,MAAM,UAAU,GAAG,UAAU,KAAK,GAAnE;AASjB,IAAM,4BAA4B,8BAAO,MAAcA,YAA0B;AACtF,SAAO,MAAM,YAAY,MAAMA,OAAM;AACrC,QAAM,UAAU,SAAS,cAAc,KAAK;AAC5C,UAAQ,YAAY;AACpB,UAAQ,KAAK;AACb,UAAQ,MAAM,aAAa;AAC3B,UAAQ,MAAM,WAAW;AACzB,UAAQ,MAAM,MAAM;AACpB,QAAM,OAAO,SAAS,cAAc,MAAM;AAC1C,QAAM,sBAAsB,aAAa,OAAO;AAChD,QAAM,MAAM,EAAE,OAAO,QAAQ,aAAa,QAAQ,QAAQ,aAAa;AACvE,UAAQ,OAAO;AACf,SAAO;AACT,GAbyC;AAsBlC,IAAM,cAAc,8BAAO,MAAcA,YAA2C;AACzF,MAAI,CAAC,SAAS,IAAI,GAAG;AACnB,WAAO;AAAA,EACT;AAEA,MAAI,EAAE,kBAAkB,KAAKA,QAAO,gBAAgBA,QAAO,oBAAoB;AAC7E,WAAO,KAAK,QAAQ,YAAY,4CAA4C;AAAA,EAC9E;AAEA,QAAM,EAAE,SAAS,MAAM,IAAI,MAAM,OAAO,OAAO;AAC/C,QAAM,aACJA,QAAO,qBAAsB,CAAC,kBAAkB,KAAKA,QAAO,eACxD,kBACA;AACN,SAAO,KACJ,MAAM,cAAc,EACpB;AAAA,IAAI,CAAC,SACJ,SAAS,IAAI,IACT,kGAAkG,IAAI,WACtG,QAAQ,IAAI;AAAA,EAClB,EACC,KAAK,EAAE,EACP;AAAA,IAAQ;AAAA,IAAY,CAAC,GAAG,MACvB,MACG,eAAe,GAAG;AAAA,MACjB,cAAc;AAAA,MACd,aAAa;AAAA,MACb,QAAQ;AAAA,IACV,CAAC,EACA,QAAQ,OAAO,GAAG,EAClB,QAAQ,gCAAgC,EAAE;AAAA,EAC/C;AACJ,GAhC2B;AAkC3B,IAAO,iBAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACpXA,IAAM,UAAU,gCAAU,QAAQ,OAAO;AACvC,WAAS,QAAQ,OAAO;AACtB,WAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,EAC9B;AACF,GAJgB;AAcT,IAAM,wBAAwB,gCAAU,QAAQ,OAAO,aAAa;AACzE,MAAI,QAAQ,oBAAI,IAAI;AACpB,MAAI,aAAa;AACf,UAAM,IAAI,SAAS,MAAM;AACzB,UAAM,IAAI,SAAS,cAAc,KAAK,KAAK;AAAA,EAC7C,OAAO;AACL,UAAM,IAAI,UAAU,MAAM;AAC1B,UAAM,IAAI,SAAS,KAAK;AAAA,EAC1B;AACA,SAAO;AACT,GAVqC;AAoB9B,IAAM,mBAAmB,gCAAU,SAAS,QAAQ,OAAO,aAAa;AAC7E,QAAM,QAAQ,sBAAsB,QAAQ,OAAO,WAAW;AAC9D,UAAQ,SAAS,KAAK;AACxB,GAHgC;AAMzB,IAAM,oBAAoB,gCAAU,OAAO,SAAS,SAAS,aAAa;AAC/E,QAAM,YAAY,QAAQ,KAAK,EAAE,QAAQ;AACzC,QAAM,SAAS,UAAU;AACzB,QAAM,UAAU,UAAU;AAE1B,MAAI,KAAK,eAAe,MAAM,IAAI,OAAO,IAAI,SAAS;AAEtD,MAAI,QAAQ;AACZ,MAAI,SAAS;AACb,MAAI,KAAK,iBAAiB,KAAK,IAAI,MAAM,IAAI,KAAK;AAElD,UAAQ,SAAS,UAAU;AAC3B,WAAS,UAAU,UAAU;AAE7B,MAAI,KAAK,sBAAsB,KAAK,IAAI,MAAM,EAAE;AAChD,mBAAiB,SAAS,QAAQ,OAAO,WAAW;AAGpD,QAAM,OAAO,GAAG,UAAU,IAAI,OAAO,IAAI,UAAU,IAAI,OAAO,IAC5D,UAAU,QAAQ,IAAI,OACxB,IAAI,UAAU,SAAS,IAAI,OAAO;AAElC,UAAQ,KAAK,WAAW,IAAI;AAC9B,GAvBiC;;;AC5CjC,IAAM,SAAgD,CAAC;AAEvD,IAAM,YAAY,wBAChB,MACA,YACA,YAQG;AACH,MAAI,gBAAgB;AACpB,MAAI,QAAQ,UAAU,OAAO,IAAI,GAAG;AAClC,oBAAgB,OAAO,IAAI,EAAE,OAAO;AAAA,EACtC,OAAO;AACL,QAAI,KAAK,sBAAsB,IAAI,EAAE;AAAA,EACvC;AACA,SAAO;AAAA,mBACU,QAAQ,UAAU;AAAA,iBACpB,QAAQ,QAAQ;AAAA,YACrB,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YA2BjB,QAAQ,aAAa;AAAA;AAAA;AAAA,YAGrB,QAAQ,cAAc;AAAA,cACpB,QAAQ,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAwBxB,QAAQ,SAAS;AAAA,cACf,QAAQ,SAAS;AAAA;AAAA;AAAA,cAGjB,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA,mBAIZ,QAAQ,UAAU;AAAA,iBACpB,QAAQ,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAM7B,aAAa;AAAA;AAAA,IAEb,UAAU;AAAA;AAEd,GA/FkB;AAiGX,IAAM,sBAAsB,wBAAC,MAAc,iBAA+C;AAC/F,MAAI,iBAAiB,QAAW;AAC9B,WAAO,IAAI,IAAI;AAAA,EACjB;AACF,GAJmC;AAMnC,IAAO,iBAAQ;;;AC7Gf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,IAAI,WAAW;AACf,IAAI,eAAe;AACnB,IAAI,iBAAiB;AAErB,IAAMC,gBAAe,wBAAC,QAAwB,aAAc,KAAK,UAAU,CAAC,GAAvD;AAEd,IAAM,QAAQ,6BAAY;AAC/B,aAAW;AACX,mBAAiB;AACjB,iBAAe;AACjB,GAJqB;AAMd,IAAM,cAAc,wBAAC,QAAsB;AAChD,aAAWA,cAAa,GAAG,EAAE,QAAQ,SAAS,EAAE;AAClD,GAF2B;AAIpB,IAAM,cAAc,6BAAc,UAAd;AAEpB,IAAM,oBAAoB,wBAAC,QAAsB;AACtD,mBAAiBA,cAAa,GAAG,EAAE,QAAQ,UAAU,IAAI;AAC3D,GAFiC;AAI1B,IAAM,oBAAoB,6BAAc,gBAAd;AAE1B,IAAM,kBAAkB,wBAAC,QAAsB;AACpD,iBAAeA,cAAa,GAAG;AACjC,GAF+B;AAIxB,IAAM,kBAAkB,6BAAc,cAAd;;;ACZxB,IAAMC,OAAM;AACZ,IAAMC,eAAc;AACpB,IAAMC,aAAY;AAClB,IAAMC,aAAY;AAClB,IAAMC,iBAAgB;AAEtB,IAAMC,gBAAe,wBAAC,SAAiB,aAAc,MAAMC,WAAU,CAAC,GAAjD;AACrB,IAAMC,qBAAoB;AAC1B,IAAM,cAAc,6BAAM;AAC/B,SAAO;AACT,GAF2B;AAI3B,IAAM,WAA8C,CAAC;AAY9C,IAAM,kBAAkB,wBAC7B,IACA,SACA,aACG;AACH,MAAI,SAAS,EAAE,GAAG;AAChB,IAAAC,KAAI,KAAK,mBAAmB,EAAE,mCAAmC;AAAA,EACnE;AACA,WAAS,EAAE,IAAI;AACf,MAAI,UAAU;AACZ,gBAAY,IAAI,QAAQ;AAAA,EAC1B;AACA,sBAAoB,IAAI,QAAQ,MAAM;AAEtC,UAAQ;AAAA,IACNA;AAAA,IACAC;AAAA,IACAH;AAAA,IACAD;AAAA,IACAE;AAAA,IACA,YAAY;AAAA,IACZ,MAAM;AAAA,IAGN;AAAA,EACF;AACF,GA1B+B;AA4BxB,IAAM,aAAa,wBAAC,SAAoC;AAC7D,MAAI,QAAQ,UAAU;AACpB,WAAO,SAAS,IAAI;AAAA,EACtB;AACA,QAAM,IAAI,qBAAqB,IAAI;AACrC,GAL0B;AAOnB,IAAM,uBAAN,cAAmC,MAAM;AAAA,EA9EhD,OA8EgD;AAAA;AAAA;AAAA,EAC9C,YAAY,MAAc;AACxB,UAAM,WAAW,IAAI,aAAa;AAAA,EACpC;AACF;", "names": ["config", "diagrams", "config", "adjust", "adjust", "adjust", "darken", "invert", "isDark", "lighten", "Theme", "lighten", "adjust", "invert", "darken", "isDark", "getThemeVariables", "invert", "lighten", "rgba", "adjust", "darken", "isDark", "Theme", "adjust", "invert", "rgba", "darken", "lighten", "isDark", "getThemeVariables", "adjust", "darken", "invert", "isDark", "lighten", "Theme", "lighten", "invert", "darken", "adjust", "isDark", "getThemeVariables", "invert", "darken", "lighten", "adjust", "isDark", "Theme", "lighten", "adjust", "invert", "darken", "isDark", "getThemeVariables", "getThemeVariables", "config", "config", "sanitizeText", "log", "setLogLevel", "getConfig", "setConfig", "defaultConfig", "sanitizeText", "getConfig", "setupGraphViewbox", "log", "setLogLevel"]}