import{a as s}from"./chunk-TYETAW3D.mjs";import"./chunk-ZWYQHTDX.mjs";import"./chunk-DDW4HWGY.mjs";import"./chunk-NA3436M7.mjs";import"./chunk-RRF4A5XS.mjs";import{a as p}from"./chunk-HQLFZTFY.mjs";import{a}from"./chunk-MEBTFSOL.mjs";import{M as n,b as o}from"./chunk-ZKYS2E5M.mjs";import"./chunk-SWO474TD.mjs";import"./chunk-237OD7E6.mjs";import"./chunk-JVB3IFOF.mjs";import"./chunk-5ZJXQJOJ.mjs";import"./chunk-YPUTD6PB.mjs";import"./chunk-6BY5RJGC.mjs";import{a as r}from"./chunk-GTKDMUJJ.mjs";var m={parse:r(async t=>{let e=await s("info",t);o.debug(e)},"parse")};var c={version:p.version},D=r(()=>c.version,"getVersion"),f={getVersion:D};var y=r((t,e,d)=>{o.debug(`rendering info diagram
`+t);let i=a(e);n(i,100,400,!0),i.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${d}`)},"draw"),g={draw:y};var T={parser:m,db:f,renderer:g};export{T as diagram};
