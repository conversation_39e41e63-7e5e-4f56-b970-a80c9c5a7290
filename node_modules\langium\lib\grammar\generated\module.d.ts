/******************************************************************************
 * This file was generated by langium-cli 3.3.0.
 * DO NOT EDIT MANUALLY!
 ******************************************************************************/
import type { Module } from '../../dependency-injection.js';
import type { LangiumSharedCoreServices, LangiumCoreServices, LangiumGeneratedCoreServices, LangiumGeneratedSharedCoreServices } from '../../services.js';
import type { IParserConfig } from '../../parser/parser-config.js';
export declare const LangiumGrammarLanguageMetaData: {
    readonly languageId: "langium";
    readonly fileExtensions: readonly [".langium"];
    readonly caseInsensitive: false;
    readonly mode: "development";
};
export declare const LangiumGrammarParserConfig: IParserConfig;
export declare const LangiumGrammarGeneratedSharedModule: Module<LangiumSharedCoreServices, LangiumGeneratedSharedCoreServices>;
export declare const LangiumGrammarGeneratedModule: Module<LangiumCoreServices, LangiumGeneratedCoreServices>;
//# sourceMappingURL=module.d.ts.map