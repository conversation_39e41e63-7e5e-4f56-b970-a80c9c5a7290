# install-pkg

[![NPM version](https://img.shields.io/npm/v/@antfu/install-pkg?color=a1b858&label=)](https://www.npmjs.com/package/@antfu/install-pkg)

Install package programmatically. Detect package managers automatically (`npm`, `yarn`, `bun` and `pnpm`).

```bash
npm i @antfu/install-pkg
```

```ts
import { installPackage } from '@antfu/install-pkg'

await installPackage('vite', { silent: true })
```

## Sponsors

<p align="center">
  <a href="https://cdn.jsdelivr.net/gh/antfu/static/sponsors.svg">
    <img src='https://cdn.jsdelivr.net/gh/antfu/static/sponsors.svg'/>
  </a>
</p>

## License

[MIT](./LICENSE) License © 2021 [<PERSON>](https://github.com/antfu)
