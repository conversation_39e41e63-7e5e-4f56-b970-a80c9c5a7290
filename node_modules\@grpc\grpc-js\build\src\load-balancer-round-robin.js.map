{"version": 3, "file": "load-balancer-round-robin.js", "sourceRoot": "", "sources": ["../../src/load-balancer-round-robin.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH,mDAKyB;AACzB,6DAAyD;AACzD,qCAOkB;AAClB,6DAG8B;AAC9B,qCAAqC;AACrC,2CAA2C;AAM3C,MAAM,WAAW,GAAG,aAAa,CAAC;AAElC,SAAS,KAAK,CAAC,IAAY;IACzB,OAAO,CAAC,KAAK,CAAC,wBAAY,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;AACvD,CAAC;AAED,MAAM,SAAS,GAAG,aAAa,CAAC;AAEhC,MAAM,6BAA6B;IACjC,mBAAmB;QACjB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,gBAAe,CAAC;IAEhB,YAAY;QACV,OAAO;YACL,CAAC,SAAS,CAAC,EAAE,EAAE;SAChB,CAAC;IACJ,CAAC;IAED,8DAA8D;IAC9D,MAAM,CAAC,cAAc,CAAC,GAAQ;QAC5B,OAAO,IAAI,6BAA6B,EAAE,CAAC;IAC7C,CAAC;CACF;AAED,MAAM,gBAAgB;IACpB,YACmB,cAAqC,EAC9C,YAAY,CAAC;QADJ,mBAAc,GAAd,cAAc,CAAuB;QAC9C,cAAS,GAAT,SAAS,CAAI;IACpB,CAAC;IAEJ,IAAI,CAAC,QAAkB;QACrB,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7D,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;QACnE,OAAO;YACL,cAAc,EAAE,uBAAc,CAAC,QAAQ;YACvC,UAAU,EAAE,gBAAgB;YAC5B,MAAM,EAAE,IAAI;YACZ,aAAa,EAAE,IAAI;YACnB,WAAW,EAAE,IAAI;SAClB,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC7C,CAAC;CACF;AAED,MAAa,sBAAsB;IAWjC,YAA6B,oBAA0C;QAA1C,yBAAoB,GAApB,oBAAoB,CAAsB;QAV/D,gBAAW,GAA0B,EAAE,CAAC;QAExC,iBAAY,GAAsB,sCAAiB,CAAC,IAAI,CAAC;QAIzD,uBAAkB,GAA4B,IAAI,CAAC;QAEnD,cAAS,GAAkB,IAAI,CAAC;QAGtC,IAAI,CAAC,uBAAuB,GAAG,CAC7B,UAA+B,EAC/B,aAAgC,EAChC,QAA2B,EAC3B,aAAqB,EACrB,YAAqB,EACrB,EAAE;YACF,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,IACE,QAAQ,KAAK,sCAAiB,CAAC,iBAAiB;gBAChD,QAAQ,KAAK,sCAAiB,CAAC,IAAI,EACnC,CAAC;gBACD,IAAI,YAAY,EAAE,CAAC;oBACjB,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC;gBAChC,CAAC;gBACD,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,EAAE,CAAC;gBAChD,UAAU,CAAC,eAAe,EAAE,CAAC;YAC/B,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;IAEO,yBAAyB,CAAC,KAAwB;QACxD,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAC5B,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,oBAAoB,EAAE,KAAK,KAAK,CAC1D,CAAC,MAAM,CAAC;IACX,CAAC;IAEO,uBAAuB;QAC7B,IAAI,IAAI,CAAC,yBAAyB,CAAC,sCAAiB,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;YAChE,MAAM,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAC9C,UAAU,CAAC,EAAE,CACX,UAAU,CAAC,oBAAoB,EAAE,KAAK,sCAAiB,CAAC,KAAK,CAChE,CAAC;YACF,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,IAAI,IAAI,CAAC,kBAAkB,KAAK,IAAI,EAAE,CAAC;gBACrC,KAAK,GAAG,gBAAgB,CAAC,OAAO,CAC9B,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,CAC7C,CAAC;gBACF,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;oBACd,KAAK,GAAG,CAAC,CAAC;gBACZ,CAAC;YACH,CAAC;YACD,IAAI,CAAC,WAAW,CACd,sCAAiB,CAAC,KAAK,EACvB,IAAI,gBAAgB,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAC9C,CAAC;QACJ,CAAC;aAAM,IACL,IAAI,CAAC,yBAAyB,CAAC,sCAAiB,CAAC,UAAU,CAAC,GAAG,CAAC,EAChE,CAAC;YACD,IAAI,CAAC,WAAW,CAAC,sCAAiB,CAAC,UAAU,EAAE,IAAI,oBAAW,CAAC,IAAI,CAAC,CAAC,CAAC;QACxE,CAAC;aAAM,IACL,IAAI,CAAC,yBAAyB,CAAC,sCAAiB,CAAC,iBAAiB,CAAC,GAAG,CAAC,EACvE,CAAC;YACD,IAAI,CAAC,WAAW,CACd,sCAAiB,CAAC,iBAAiB,EACnC,IAAI,0BAAiB,CAAC,EAAC,OAAO,EAAE,0CAA0C,IAAI,CAAC,SAAS,EAAE,EAAC,CAAC,CAC7F,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,CAAC,sCAAiB,CAAC,IAAI,EAAE,IAAI,oBAAW,CAAC,IAAI,CAAC,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,QAA2B,EAAE,MAAc;QAC7D,KAAK,CACH,sCAAiB,CAAC,IAAI,CAAC,YAAY,CAAC;YAClC,MAAM;YACN,sCAAiB,CAAC,QAAQ,CAAC,CAC9B,CAAC;QACF,IAAI,QAAQ,KAAK,sCAAiB,CAAC,KAAK,EAAE,CAAC;YACzC,IAAI,CAAC,kBAAkB,GAAG,MAA0B,CAAC;QACvD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACjC,CAAC;QACD,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;QAC7B,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC1D,CAAC;IAEO,mBAAmB;QACzB,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YAC1C,UAAU,CAAC,+BAA+B,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACzE,UAAU,CAAC,KAAK,EAAE,CAAC;YACnB,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAC3C,UAAU,CAAC,cAAc,EAAE,CAC5B,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IACxB,CAAC;IAED,iBAAiB,CACf,WAAgC,EAChC,QAA6B;QAE7B,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,KAAK,CACH,0BAA0B;YACxB,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAA,8CAAyB,EAAC,OAAO,CAAC,CAAC,CACjE,CAAC;QACF,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAC3C,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,OAAO,EAAE,EAAE,CAAC,CACxD,CAAC;QACF,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YAC1C,UAAU,CAAC,GAAG,EAAE,CAAC;YACjB,UAAU,CAAC,4BAA4B,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACtE,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC;YACxE,MAAM,eAAe,GAAG,UAAU,CAAC,oBAAoB,EAAE,CAAC;YAC1D,IACE,eAAe,KAAK,sCAAiB,CAAC,IAAI;gBAC1C,eAAe,KAAK,sCAAiB,CAAC,iBAAiB,EACvD,CAAC;gBACD,UAAU,CAAC,eAAe,EAAE,CAAC;YAC/B,CAAC;QACH,CAAC;QACD,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED,QAAQ;QACN,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YAC1C,UAAU,CAAC,eAAe,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IACD,YAAY;QACV;0BACkB;IACpB,CAAC;IACD,OAAO;QACL,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IACD,WAAW;QACT,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AA9ID,wDA8IC;AAED,SAAgB,KAAK;IACnB,IAAA,wCAAwB,EACtB,SAAS,EACT,sBAAsB,EACtB,6BAA6B,CAC9B,CAAC;AACJ,CAAC;AAND,sBAMC"}