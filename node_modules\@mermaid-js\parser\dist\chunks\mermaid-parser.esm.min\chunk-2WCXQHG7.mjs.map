{"version": 3, "sources": ["../../../src/language/architecture/tokenBuilder.ts", "../../../src/language/architecture/valueConverter.ts", "../../../src/language/architecture/module.ts"], "sourcesContent": ["import { AbstractMermaidTokenBuilder } from '../common/index.js';\n\nexport class ArchitectureTokenBuilder extends AbstractMermaidTokenBuilder {\n  public constructor() {\n    super(['architecture']);\n  }\n}\n", "import type { CstNode, GrammarAST, ValueType } from 'langium';\n\nimport { AbstractMermaidValueConverter } from '../common/index.js';\n\nexport class ArchitectureValueConverter extends AbstractMermaidValueConverter {\n  protected runCustomConverter(\n    rule: GrammarAST.AbstractRule,\n    input: string,\n    _cstNode: CstNode\n  ): ValueType | undefined {\n    if (rule.name === 'ARCH_ICON') {\n      return input.replace(/[()]/g, '').trim();\n    } else if (rule.name === 'ARCH_TEXT_ICON') {\n      return input.replace(/[\"()]/g, '');\n    } else if (rule.name === 'ARCH_TITLE') {\n      return input.replace(/[[\\]]/g, '').trim();\n    }\n    return undefined;\n  }\n}\n", "import type {\n  DefaultSharedCoreModuleContext,\n  LangiumCoreServices,\n  LangiumSharedCoreServices,\n  Module,\n  PartialLangiumCoreServices,\n} from 'langium';\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject,\n} from 'langium';\n\nimport { MermaidGeneratedSharedModule, ArchitectureGeneratedModule } from '../generated/module.js';\nimport { ArchitectureTokenBuilder } from './tokenBuilder.js';\nimport { ArchitectureValueConverter } from './valueConverter.js';\n\n/**\n * Declaration of `Architecture` services.\n */\ninterface ArchitectureAddedServices {\n  parser: {\n    TokenBuilder: ArchitectureTokenBuilder;\n    ValueConverter: ArchitectureValueConverter;\n  };\n}\n\n/**\n * Union of Langium default services and `Architecture` services.\n */\nexport type ArchitectureServices = LangiumCoreServices & ArchitectureAddedServices;\n\n/**\n * Dependency injection module that overrides Langium default services and\n * contributes the declared `Architecture` services.\n */\nexport const ArchitectureModule: Module<\n  ArchitectureServices,\n  PartialLangiumCoreServices & ArchitectureAddedServices\n> = {\n  parser: {\n    TokenBuilder: () => new ArchitectureTokenBuilder(),\n    ValueConverter: () => new ArchitectureValueConverter(),\n  },\n};\n\n/**\n * Create the full set of services required by Langium.\n *\n * First inject the shared services by merging two modules:\n *  - Langium default shared services\n *  - Services generated by langium-cli\n *\n * Then inject the language-specific services by merging three modules:\n *  - Langium default language-specific services\n *  - Services generated by langium-cli\n *  - Services specified in this file\n * @param context - Optional module context with the LSP connection\n * @returns An object wrapping the shared services and the language-specific services\n */\nexport function createArchitectureServices(\n  context: DefaultSharedCoreModuleContext = EmptyFileSystem\n): {\n  shared: LangiumSharedCoreServices;\n  Architecture: ArchitectureServices;\n} {\n  const shared: LangiumSharedCoreServices = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Architecture: ArchitectureServices = inject(\n    createDefaultCoreModule({ shared }),\n    ArchitectureGeneratedModule,\n    ArchitectureModule\n  );\n  shared.ServiceRegistry.register(Architecture);\n  return { shared, Architecture };\n}\n"], "mappings": "wGAEO,IAAMA,EAAN,cAAuCC,CAA4B,CAF1E,MAE0E,CAAAC,EAAA,iCACjE,aAAc,CACnB,MAAM,CAAC,cAAc,CAAC,CACxB,CACF,ECFO,IAAMC,EAAN,cAAyCC,CAA8B,CAJ9E,MAI8E,CAAAC,EAAA,mCAClE,mBACRC,EACAC,EACAC,EACuB,CACvB,GAAIF,EAAK,OAAS,YAChB,OAAOC,EAAM,QAAQ,QAAS,EAAE,EAAE,KAAK,EAClC,GAAID,EAAK,OAAS,iBACvB,OAAOC,EAAM,QAAQ,SAAU,EAAE,EAC5B,GAAID,EAAK,OAAS,aACvB,OAAOC,EAAM,QAAQ,SAAU,EAAE,EAAE,KAAK,CAG5C,CACF,ECkBO,IAAME,EAGT,CACF,OAAQ,CACN,aAAcC,EAAA,IAAM,IAAIC,EAAV,gBACd,eAAgBD,EAAA,IAAM,IAAIE,EAAV,iBAClB,CACF,EAgBO,SAASC,EACdC,EAA0CC,EAI1C,CACA,IAAMC,EAAoCC,EACxCC,EAA8BJ,CAAO,EACrCK,CACF,EACMC,EAAqCH,EACzCI,EAAwB,CAAE,OAAAL,CAAO,CAAC,EAClCM,EACAb,CACF,EACA,OAAAO,EAAO,gBAAgB,SAASI,CAAY,EACrC,CAAE,OAAAJ,EAAQ,aAAAI,CAAa,CAChC,CAjBgBV,EAAAG,EAAA", "names": ["ArchitectureTokenBuilder", "AbstractMermaidTokenBuilder", "__name", "ArchitectureValueConverter", "AbstractMermaidValueConverter", "__name", "rule", "input", "_cstNode", "ArchitectureModule", "__name", "ArchitectureTokenBuilder", "ArchitectureValueConverter", "createArchitectureServices", "context", "EmptyFileSystem", "shared", "inject", "createDefaultSharedCoreModule", "MermaidGeneratedSharedModule", "Architecture", "createDefaultCoreModule", "ArchitectureGeneratedModule"]}