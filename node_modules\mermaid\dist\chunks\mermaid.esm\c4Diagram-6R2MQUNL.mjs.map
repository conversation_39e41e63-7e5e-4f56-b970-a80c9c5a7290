{"version": 3, "sources": ["../../../src/diagrams/c4/parser/c4Diagram.jison", "../../../src/diagrams/c4/c4Db.js", "../../../src/diagrams/c4/svgDraw.js", "../../../src/diagrams/c4/c4Renderer.js", "../../../src/diagrams/c4/styles.js", "../../../src/diagrams/c4/c4Diagram.ts"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[1,24],$V1=[1,25],$V2=[1,26],$V3=[1,27],$V4=[1,28],$V5=[1,63],$V6=[1,64],$V7=[1,65],$V8=[1,66],$V9=[1,67],$Va=[1,68],$Vb=[1,69],$Vc=[1,29],$Vd=[1,30],$Ve=[1,31],$Vf=[1,32],$Vg=[1,33],$Vh=[1,34],$Vi=[1,35],$Vj=[1,36],$Vk=[1,37],$Vl=[1,38],$Vm=[1,39],$Vn=[1,40],$Vo=[1,41],$Vp=[1,42],$Vq=[1,43],$Vr=[1,44],$Vs=[1,45],$Vt=[1,46],$Vu=[1,47],$Vv=[1,48],$Vw=[1,50],$Vx=[1,51],$Vy=[1,52],$Vz=[1,53],$VA=[1,54],$VB=[1,55],$VC=[1,56],$VD=[1,57],$VE=[1,58],$VF=[1,59],$VG=[1,60],$VH=[14,42],$VI=[14,34,36,37,38,39,40,41,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74],$VJ=[12,14,34,36,37,38,39,40,41,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74],$VK=[1,82],$VL=[1,83],$VM=[1,84],$VN=[1,85],$VO=[12,14,42],$VP=[12,14,33,42],$VQ=[12,14,33,42,76,77,79,80],$VR=[12,33],$VS=[34,36,37,38,39,40,41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"start\":3,\"mermaidDoc\":4,\"direction\":5,\"direction_tb\":6,\"direction_bt\":7,\"direction_rl\":8,\"direction_lr\":9,\"graphConfig\":10,\"C4_CONTEXT\":11,\"NEWLINE\":12,\"statements\":13,\"EOF\":14,\"C4_CONTAINER\":15,\"C4_COMPONENT\":16,\"C4_DYNAMIC\":17,\"C4_DEPLOYMENT\":18,\"otherStatements\":19,\"diagramStatements\":20,\"otherStatement\":21,\"title\":22,\"accDescription\":23,\"acc_title\":24,\"acc_title_value\":25,\"acc_descr\":26,\"acc_descr_value\":27,\"acc_descr_multiline_value\":28,\"boundaryStatement\":29,\"boundaryStartStatement\":30,\"boundaryStopStatement\":31,\"boundaryStart\":32,\"LBRACE\":33,\"ENTERPRISE_BOUNDARY\":34,\"attributes\":35,\"SYSTEM_BOUNDARY\":36,\"BOUNDARY\":37,\"CONTAINER_BOUNDARY\":38,\"NODE\":39,\"NODE_L\":40,\"NODE_R\":41,\"RBRACE\":42,\"diagramStatement\":43,\"PERSON\":44,\"PERSON_EXT\":45,\"SYSTEM\":46,\"SYSTEM_DB\":47,\"SYSTEM_QUEUE\":48,\"SYSTEM_EXT\":49,\"SYSTEM_EXT_DB\":50,\"SYSTEM_EXT_QUEUE\":51,\"CONTAINER\":52,\"CONTAINER_DB\":53,\"CONTAINER_QUEUE\":54,\"CONTAINER_EXT\":55,\"CONTAINER_EXT_DB\":56,\"CONTAINER_EXT_QUEUE\":57,\"COMPONENT\":58,\"COMPONENT_DB\":59,\"COMPONENT_QUEUE\":60,\"COMPONENT_EXT\":61,\"COMPONENT_EXT_DB\":62,\"COMPONENT_EXT_QUEUE\":63,\"REL\":64,\"BIREL\":65,\"REL_U\":66,\"REL_D\":67,\"REL_L\":68,\"REL_R\":69,\"REL_B\":70,\"REL_INDEX\":71,\"UPDATE_EL_STYLE\":72,\"UPDATE_REL_STYLE\":73,\"UPDATE_LAYOUT_CONFIG\":74,\"attribute\":75,\"STR\":76,\"STR_KEY\":77,\"STR_VALUE\":78,\"ATTRIBUTE\":79,\"ATTRIBUTE_EMPTY\":80,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",6:\"direction_tb\",7:\"direction_bt\",8:\"direction_rl\",9:\"direction_lr\",11:\"C4_CONTEXT\",12:\"NEWLINE\",14:\"EOF\",15:\"C4_CONTAINER\",16:\"C4_COMPONENT\",17:\"C4_DYNAMIC\",18:\"C4_DEPLOYMENT\",22:\"title\",23:\"accDescription\",24:\"acc_title\",25:\"acc_title_value\",26:\"acc_descr\",27:\"acc_descr_value\",28:\"acc_descr_multiline_value\",33:\"LBRACE\",34:\"ENTERPRISE_BOUNDARY\",36:\"SYSTEM_BOUNDARY\",37:\"BOUNDARY\",38:\"CONTAINER_BOUNDARY\",39:\"NODE\",40:\"NODE_L\",41:\"NODE_R\",42:\"RBRACE\",44:\"PERSON\",45:\"PERSON_EXT\",46:\"SYSTEM\",47:\"SYSTEM_DB\",48:\"SYSTEM_QUEUE\",49:\"SYSTEM_EXT\",50:\"SYSTEM_EXT_DB\",51:\"SYSTEM_EXT_QUEUE\",52:\"CONTAINER\",53:\"CONTAINER_DB\",54:\"CONTAINER_QUEUE\",55:\"CONTAINER_EXT\",56:\"CONTAINER_EXT_DB\",57:\"CONTAINER_EXT_QUEUE\",58:\"COMPONENT\",59:\"COMPONENT_DB\",60:\"COMPONENT_QUEUE\",61:\"COMPONENT_EXT\",62:\"COMPONENT_EXT_DB\",63:\"COMPONENT_EXT_QUEUE\",64:\"REL\",65:\"BIREL\",66:\"REL_U\",67:\"REL_D\",68:\"REL_L\",69:\"REL_R\",70:\"REL_B\",71:\"REL_INDEX\",72:\"UPDATE_EL_STYLE\",73:\"UPDATE_REL_STYLE\",74:\"UPDATE_LAYOUT_CONFIG\",76:\"STR\",77:\"STR_KEY\",78:\"STR_VALUE\",79:\"ATTRIBUTE\",80:\"ATTRIBUTE_EMPTY\"},\nproductions_: [0,[3,1],[3,1],[5,1],[5,1],[5,1],[5,1],[4,1],[10,4],[10,4],[10,4],[10,4],[10,4],[13,1],[13,1],[13,2],[19,1],[19,2],[19,3],[21,1],[21,1],[21,2],[21,2],[21,1],[29,3],[30,3],[30,3],[30,4],[32,2],[32,2],[32,2],[32,2],[32,2],[32,2],[32,2],[31,1],[20,1],[20,2],[20,3],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,1],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[35,1],[35,2],[75,1],[75,2],[75,1],[75,1]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 3:\n yy.setDirection('TB');\nbreak;\ncase 4:\n yy.setDirection('BT');\nbreak;\ncase 5:\n yy.setDirection('RL');\nbreak;\ncase 6:\n yy.setDirection('LR');\nbreak;\ncase 8: case 9: case 10: case 11: case 12:\nyy.setC4Type($$[$0-3])\nbreak;\ncase 19:\nyy.setTitle($$[$0].substring(6));this.$=$$[$0].substring(6);\nbreak;\ncase 20:\nyy.setAccDescription($$[$0].substring(15));this.$=$$[$0].substring(15);\nbreak;\ncase 21:\n this.$=$$[$0].trim();yy.setTitle(this.$); \nbreak;\ncase 22: case 23:\n this.$=$$[$0].trim();yy.setAccDescription(this.$); \nbreak;\ncase 28:\n$$[$0].splice(2, 0, 'ENTERPRISE'); yy.addPersonOrSystemBoundary(...$$[$0]); this.$=$$[$0];\nbreak;\ncase 29:\n$$[$0].splice(2, 0, 'SYSTEM'); yy.addPersonOrSystemBoundary(...$$[$0]); this.$=$$[$0];\nbreak;\ncase 30:\nyy.addPersonOrSystemBoundary(...$$[$0]); this.$=$$[$0];\nbreak;\ncase 31:\n$$[$0].splice(2, 0, 'CONTAINER'); yy.addContainerBoundary(...$$[$0]); this.$=$$[$0];\nbreak;\ncase 32:\nyy.addDeploymentNode('node', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 33:\nyy.addDeploymentNode('nodeL', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 34:\nyy.addDeploymentNode('nodeR', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 35:\n yy.popBoundaryParseStack() \nbreak;\ncase 39:\nyy.addPersonOrSystem('person', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 40:\nyy.addPersonOrSystem('external_person', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 41:\nyy.addPersonOrSystem('system', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 42:\nyy.addPersonOrSystem('system_db', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 43:\nyy.addPersonOrSystem('system_queue', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 44:\nyy.addPersonOrSystem('external_system', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 45:\nyy.addPersonOrSystem('external_system_db', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 46:\nyy.addPersonOrSystem('external_system_queue', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 47:\nyy.addContainer('container', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 48:\nyy.addContainer('container_db', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 49:\nyy.addContainer('container_queue', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 50:\nyy.addContainer('external_container', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 51:\nyy.addContainer('external_container_db', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 52:\nyy.addContainer('external_container_queue', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 53:\nyy.addComponent('component', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 54:\nyy.addComponent('component_db', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 55:\nyy.addComponent('component_queue', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 56:\nyy.addComponent('external_component', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 57:\nyy.addComponent('external_component_db', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 58:\nyy.addComponent('external_component_queue', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 60:\nyy.addRel('rel', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 61:\nyy.addRel('birel', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 62:\nyy.addRel('rel_u', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 63:\nyy.addRel('rel_d', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 64:\nyy.addRel('rel_l', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 65:\nyy.addRel('rel_r', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 66:\nyy.addRel('rel_b', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 67:\n$$[$0].splice(0, 1); yy.addRel('rel', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 68:\nyy.updateElStyle('update_el_style', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 69:\nyy.updateRelStyle('update_rel_style', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 70:\nyy.updateLayoutConfig('update_layout_config', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 71:\n this.$ = [$$[$0]]; \nbreak;\ncase 72:\n $$[$0].unshift($$[$0-1]); this.$=$$[$0];\nbreak;\ncase 73: case 75:\n  this.$ = $$[$0].trim(); \nbreak;\ncase 74:\n let kv={}; kv[$$[$0-1].trim()]=$$[$0].trim(); this.$=kv; \nbreak;\ncase 76:\n  this.$ = \"\"; \nbreak;\n}\n},\ntable: [{3:1,4:2,5:3,6:[1,5],7:[1,6],8:[1,7],9:[1,8],10:4,11:[1,9],15:[1,10],16:[1,11],17:[1,12],18:[1,13]},{1:[3]},{1:[2,1]},{1:[2,2]},{1:[2,7]},{1:[2,3]},{1:[2,4]},{1:[2,5]},{1:[2,6]},{12:[1,14]},{12:[1,15]},{12:[1,16]},{12:[1,17]},{12:[1,18]},{13:19,19:20,20:21,21:22,22:$V0,23:$V1,24:$V2,26:$V3,28:$V4,29:49,30:61,32:62,34:$V5,36:$V6,37:$V7,38:$V8,39:$V9,40:$Va,41:$Vb,43:23,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi,51:$Vj,52:$Vk,53:$Vl,54:$Vm,55:$Vn,56:$Vo,57:$Vp,58:$Vq,59:$Vr,60:$Vs,61:$Vt,62:$Vu,63:$Vv,64:$Vw,65:$Vx,66:$Vy,67:$Vz,68:$VA,69:$VB,70:$VC,71:$VD,72:$VE,73:$VF,74:$VG},{13:70,19:20,20:21,21:22,22:$V0,23:$V1,24:$V2,26:$V3,28:$V4,29:49,30:61,32:62,34:$V5,36:$V6,37:$V7,38:$V8,39:$V9,40:$Va,41:$Vb,43:23,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi,51:$Vj,52:$Vk,53:$Vl,54:$Vm,55:$Vn,56:$Vo,57:$Vp,58:$Vq,59:$Vr,60:$Vs,61:$Vt,62:$Vu,63:$Vv,64:$Vw,65:$Vx,66:$Vy,67:$Vz,68:$VA,69:$VB,70:$VC,71:$VD,72:$VE,73:$VF,74:$VG},{13:71,19:20,20:21,21:22,22:$V0,23:$V1,24:$V2,26:$V3,28:$V4,29:49,30:61,32:62,34:$V5,36:$V6,37:$V7,38:$V8,39:$V9,40:$Va,41:$Vb,43:23,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi,51:$Vj,52:$Vk,53:$Vl,54:$Vm,55:$Vn,56:$Vo,57:$Vp,58:$Vq,59:$Vr,60:$Vs,61:$Vt,62:$Vu,63:$Vv,64:$Vw,65:$Vx,66:$Vy,67:$Vz,68:$VA,69:$VB,70:$VC,71:$VD,72:$VE,73:$VF,74:$VG},{13:72,19:20,20:21,21:22,22:$V0,23:$V1,24:$V2,26:$V3,28:$V4,29:49,30:61,32:62,34:$V5,36:$V6,37:$V7,38:$V8,39:$V9,40:$Va,41:$Vb,43:23,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi,51:$Vj,52:$Vk,53:$Vl,54:$Vm,55:$Vn,56:$Vo,57:$Vp,58:$Vq,59:$Vr,60:$Vs,61:$Vt,62:$Vu,63:$Vv,64:$Vw,65:$Vx,66:$Vy,67:$Vz,68:$VA,69:$VB,70:$VC,71:$VD,72:$VE,73:$VF,74:$VG},{13:73,19:20,20:21,21:22,22:$V0,23:$V1,24:$V2,26:$V3,28:$V4,29:49,30:61,32:62,34:$V5,36:$V6,37:$V7,38:$V8,39:$V9,40:$Va,41:$Vb,43:23,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi,51:$Vj,52:$Vk,53:$Vl,54:$Vm,55:$Vn,56:$Vo,57:$Vp,58:$Vq,59:$Vr,60:$Vs,61:$Vt,62:$Vu,63:$Vv,64:$Vw,65:$Vx,66:$Vy,67:$Vz,68:$VA,69:$VB,70:$VC,71:$VD,72:$VE,73:$VF,74:$VG},{14:[1,74]},o($VH,[2,13],{43:23,29:49,30:61,32:62,20:75,34:$V5,36:$V6,37:$V7,38:$V8,39:$V9,40:$Va,41:$Vb,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi,51:$Vj,52:$Vk,53:$Vl,54:$Vm,55:$Vn,56:$Vo,57:$Vp,58:$Vq,59:$Vr,60:$Vs,61:$Vt,62:$Vu,63:$Vv,64:$Vw,65:$Vx,66:$Vy,67:$Vz,68:$VA,69:$VB,70:$VC,71:$VD,72:$VE,73:$VF,74:$VG}),o($VH,[2,14]),o($VI,[2,16],{12:[1,76]}),o($VH,[2,36],{12:[1,77]}),o($VJ,[2,19]),o($VJ,[2,20]),{25:[1,78]},{27:[1,79]},o($VJ,[2,23]),{35:80,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:86,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:87,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:88,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:89,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:90,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:91,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:92,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:93,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:94,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:95,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:96,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:97,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:98,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:99,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:100,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:101,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:102,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:103,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:104,75:81,76:$VK,77:$VL,79:$VM,80:$VN},o($VO,[2,59]),{35:105,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:106,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:107,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:108,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:109,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:110,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:111,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:112,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:113,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:114,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:115,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{20:116,29:49,30:61,32:62,34:$V5,36:$V6,37:$V7,38:$V8,39:$V9,40:$Va,41:$Vb,43:23,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi,51:$Vj,52:$Vk,53:$Vl,54:$Vm,55:$Vn,56:$Vo,57:$Vp,58:$Vq,59:$Vr,60:$Vs,61:$Vt,62:$Vu,63:$Vv,64:$Vw,65:$Vx,66:$Vy,67:$Vz,68:$VA,69:$VB,70:$VC,71:$VD,72:$VE,73:$VF,74:$VG},{12:[1,118],33:[1,117]},{35:119,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:120,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:121,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:122,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:123,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:124,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:125,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{14:[1,126]},{14:[1,127]},{14:[1,128]},{14:[1,129]},{1:[2,8]},o($VH,[2,15]),o($VI,[2,17],{21:22,19:130,22:$V0,23:$V1,24:$V2,26:$V3,28:$V4}),o($VH,[2,37],{19:20,20:21,21:22,43:23,29:49,30:61,32:62,13:131,22:$V0,23:$V1,24:$V2,26:$V3,28:$V4,34:$V5,36:$V6,37:$V7,38:$V8,39:$V9,40:$Va,41:$Vb,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi,51:$Vj,52:$Vk,53:$Vl,54:$Vm,55:$Vn,56:$Vo,57:$Vp,58:$Vq,59:$Vr,60:$Vs,61:$Vt,62:$Vu,63:$Vv,64:$Vw,65:$Vx,66:$Vy,67:$Vz,68:$VA,69:$VB,70:$VC,71:$VD,72:$VE,73:$VF,74:$VG}),o($VJ,[2,21]),o($VJ,[2,22]),o($VO,[2,39]),o($VP,[2,71],{75:81,35:132,76:$VK,77:$VL,79:$VM,80:$VN}),o($VQ,[2,73]),{78:[1,133]},o($VQ,[2,75]),o($VQ,[2,76]),o($VO,[2,40]),o($VO,[2,41]),o($VO,[2,42]),o($VO,[2,43]),o($VO,[2,44]),o($VO,[2,45]),o($VO,[2,46]),o($VO,[2,47]),o($VO,[2,48]),o($VO,[2,49]),o($VO,[2,50]),o($VO,[2,51]),o($VO,[2,52]),o($VO,[2,53]),o($VO,[2,54]),o($VO,[2,55]),o($VO,[2,56]),o($VO,[2,57]),o($VO,[2,58]),o($VO,[2,60]),o($VO,[2,61]),o($VO,[2,62]),o($VO,[2,63]),o($VO,[2,64]),o($VO,[2,65]),o($VO,[2,66]),o($VO,[2,67]),o($VO,[2,68]),o($VO,[2,69]),o($VO,[2,70]),{31:134,42:[1,135]},{12:[1,136]},{33:[1,137]},o($VR,[2,28]),o($VR,[2,29]),o($VR,[2,30]),o($VR,[2,31]),o($VR,[2,32]),o($VR,[2,33]),o($VR,[2,34]),{1:[2,9]},{1:[2,10]},{1:[2,11]},{1:[2,12]},o($VI,[2,18]),o($VH,[2,38]),o($VP,[2,72]),o($VQ,[2,74]),o($VO,[2,24]),o($VO,[2,35]),o($VS,[2,25]),o($VS,[2,26],{12:[1,138]}),o($VS,[2,27])],\ndefaultActions: {2:[2,1],3:[2,2],4:[2,7],5:[2,3],6:[2,4],7:[2,5],8:[2,6],74:[2,8],126:[2,9],127:[2,10],128:[2,11],129:[2,12]},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0:return 6;\nbreak;\ncase 1:return 7;\nbreak;\ncase 2:return 8;\nbreak;\ncase 3:return 9;\nbreak;\ncase 4:return 22;\nbreak;\ncase 5:return 23;\nbreak;\ncase 6: this.begin(\"acc_title\");return 24; \nbreak;\ncase 7: this.popState(); return \"acc_title_value\"; \nbreak;\ncase 8: this.begin(\"acc_descr\");return 26; \nbreak;\ncase 9: this.popState(); return \"acc_descr_value\"; \nbreak;\ncase 10: this.begin(\"acc_descr_multiline\");\nbreak;\ncase 11: this.popState(); \nbreak;\ncase 12:return \"acc_descr_multiline_value\";\nbreak;\ncase 13:/* skip comments */\nbreak;\ncase 14:c            /* skip comments */\nbreak;\ncase 15:return 12;\nbreak;\ncase 16:/* skip whitespace */\nbreak;\ncase 17:return 11;\nbreak;\ncase 18:return 15;\nbreak;\ncase 19:return 16;\nbreak;\ncase 20:return 17;\nbreak;\ncase 21:return 18;\nbreak;\ncase 22: this.begin(\"person_ext\"); return 45;\nbreak;\ncase 23: this.begin(\"person\"); return 44;\nbreak;\ncase 24: this.begin(\"system_ext_queue\"); return 51;\nbreak;\ncase 25: this.begin(\"system_ext_db\"); return 50;\nbreak;\ncase 26: this.begin(\"system_ext\"); return 49;\nbreak;\ncase 27: this.begin(\"system_queue\"); return 48;\nbreak;\ncase 28: this.begin(\"system_db\"); return 47;\nbreak;\ncase 29: this.begin(\"system\"); return 46;\nbreak;\ncase 30: this.begin(\"boundary\"); return 37;\nbreak;\ncase 31: this.begin(\"enterprise_boundary\"); return 34;\nbreak;\ncase 32: this.begin(\"system_boundary\"); return 36;\nbreak;\ncase 33: this.begin(\"container_ext_queue\"); return 57;\nbreak;\ncase 34: this.begin(\"container_ext_db\"); return 56;\nbreak;\ncase 35: this.begin(\"container_ext\"); return 55;\nbreak;\ncase 36: this.begin(\"container_queue\"); return 54;\nbreak;\ncase 37: this.begin(\"container_db\"); return 53;\nbreak;\ncase 38: this.begin(\"container\"); return 52;\nbreak;\ncase 39: this.begin(\"container_boundary\"); return 38;\nbreak;\ncase 40: this.begin(\"component_ext_queue\"); return 63;\nbreak;\ncase 41: this.begin(\"component_ext_db\"); return 62;\nbreak;\ncase 42: this.begin(\"component_ext\"); return 61;\nbreak;\ncase 43: this.begin(\"component_queue\"); return 60;\nbreak;\ncase 44: this.begin(\"component_db\"); return 59;\nbreak;\ncase 45: this.begin(\"component\"); return 58;\nbreak;\ncase 46: this.begin(\"node\"); return 39;\nbreak;\ncase 47: this.begin(\"node\"); return 39;\nbreak;\ncase 48: this.begin(\"node_l\"); return 40;\nbreak;\ncase 49: this.begin(\"node_r\"); return 41;\nbreak;\ncase 50: this.begin(\"rel\"); return 64;\nbreak;\ncase 51: this.begin(\"birel\"); return 65;\nbreak;\ncase 52: this.begin(\"rel_u\"); return 66;\nbreak;\ncase 53: this.begin(\"rel_u\"); return 66;\nbreak;\ncase 54: this.begin(\"rel_d\"); return 67;\nbreak;\ncase 55: this.begin(\"rel_d\"); return 67;\nbreak;\ncase 56: this.begin(\"rel_l\"); return 68;\nbreak;\ncase 57: this.begin(\"rel_l\"); return 68;\nbreak;\ncase 58: this.begin(\"rel_r\"); return 69;\nbreak;\ncase 59: this.begin(\"rel_r\"); return 69;\nbreak;\ncase 60: this.begin(\"rel_b\"); return 70;\nbreak;\ncase 61: this.begin(\"rel_index\"); return 71;\nbreak;\ncase 62: this.begin(\"update_el_style\"); return 72;\nbreak;\ncase 63: this.begin(\"update_rel_style\"); return 73;\nbreak;\ncase 64: this.begin(\"update_layout_config\"); return 74;\nbreak;\ncase 65:return \"EOF_IN_STRUCT\";\nbreak;\ncase 66: this.begin(\"attribute\"); return \"ATTRIBUTE_EMPTY\";\nbreak;\ncase 67: this.begin(\"attribute\"); \nbreak;\ncase 68: this.popState();this.popState();\nbreak;\ncase 69: return 80;\nbreak;\ncase 70: \nbreak;\ncase 71: return 80;\nbreak;\ncase 72: this.begin(\"string\");\nbreak;\ncase 73:  this.popState(); \nbreak;\ncase 74: return \"STR\";\nbreak;\ncase 75: this.begin(\"string_kv\");\nbreak;\ncase 76: this.begin(\"string_kv_key\"); return \"STR_KEY\";\nbreak;\ncase 77: this.popState(); this.begin(\"string_kv_value\"); \nbreak;\ncase 78: return \"STR_VALUE\";\nbreak;\ncase 79: this.popState(); this.popState(); \nbreak;\ncase 80: return \"STR\";\nbreak;\ncase 81: /* this.begin(\"lbrace\"); */ return \"LBRACE\";\nbreak;\ncase 82: /* this.popState(); */ return \"RBRACE\";\nbreak;\ncase 83:return 'SPACE';\nbreak;\ncase 84:return 'EOL';\nbreak;\ncase 85:return 14;\nbreak;\n}\n},\nrules: [/^(?:.*direction\\s+TB[^\\n]*)/,/^(?:.*direction\\s+BT[^\\n]*)/,/^(?:.*direction\\s+RL[^\\n]*)/,/^(?:.*direction\\s+LR[^\\n]*)/,/^(?:title\\s[^#\\n;]+)/,/^(?:accDescription\\s[^#\\n;]+)/,/^(?:accTitle\\s*:\\s*)/,/^(?:(?!\\n||)*[^\\n]*)/,/^(?:accDescr\\s*:\\s*)/,/^(?:(?!\\n||)*[^\\n]*)/,/^(?:accDescr\\s*\\{\\s*)/,/^(?:[\\}])/,/^(?:[^\\}]*)/,/^(?:%%(?!\\{)*[^\\n]*(\\r?\\n?)+)/,/^(?:%%[^\\n]*(\\r?\\n)*)/,/^(?:\\s*(\\r?\\n)+)/,/^(?:\\s+)/,/^(?:C4Context\\b)/,/^(?:C4Container\\b)/,/^(?:C4Component\\b)/,/^(?:C4Dynamic\\b)/,/^(?:C4Deployment\\b)/,/^(?:Person_Ext\\b)/,/^(?:Person\\b)/,/^(?:SystemQueue_Ext\\b)/,/^(?:SystemDb_Ext\\b)/,/^(?:System_Ext\\b)/,/^(?:SystemQueue\\b)/,/^(?:SystemDb\\b)/,/^(?:System\\b)/,/^(?:Boundary\\b)/,/^(?:Enterprise_Boundary\\b)/,/^(?:System_Boundary\\b)/,/^(?:ContainerQueue_Ext\\b)/,/^(?:ContainerDb_Ext\\b)/,/^(?:Container_Ext\\b)/,/^(?:ContainerQueue\\b)/,/^(?:ContainerDb\\b)/,/^(?:Container\\b)/,/^(?:Container_Boundary\\b)/,/^(?:ComponentQueue_Ext\\b)/,/^(?:ComponentDb_Ext\\b)/,/^(?:Component_Ext\\b)/,/^(?:ComponentQueue\\b)/,/^(?:ComponentDb\\b)/,/^(?:Component\\b)/,/^(?:Deployment_Node\\b)/,/^(?:Node\\b)/,/^(?:Node_L\\b)/,/^(?:Node_R\\b)/,/^(?:Rel\\b)/,/^(?:BiRel\\b)/,/^(?:Rel_Up\\b)/,/^(?:Rel_U\\b)/,/^(?:Rel_Down\\b)/,/^(?:Rel_D\\b)/,/^(?:Rel_Left\\b)/,/^(?:Rel_L\\b)/,/^(?:Rel_Right\\b)/,/^(?:Rel_R\\b)/,/^(?:Rel_Back\\b)/,/^(?:RelIndex\\b)/,/^(?:UpdateElementStyle\\b)/,/^(?:UpdateRelStyle\\b)/,/^(?:UpdateLayoutConfig\\b)/,/^(?:$)/,/^(?:[(][ ]*[,])/,/^(?:[(])/,/^(?:[)])/,/^(?:,,)/,/^(?:,)/,/^(?:[ ]*[\"][\"])/,/^(?:[ ]*[\"])/,/^(?:[\"])/,/^(?:[^\"]*)/,/^(?:[ ]*[\\$])/,/^(?:[^=]*)/,/^(?:[=][ ]*[\"])/,/^(?:[^\"]+)/,/^(?:[\"])/,/^(?:[^,]+)/,/^(?:\\{)/,/^(?:\\})/,/^(?:[\\s]+)/,/^(?:[\\n\\r]+)/,/^(?:$)/],\nconditions: {\"acc_descr_multiline\":{\"rules\":[11,12],\"inclusive\":false},\"acc_descr\":{\"rules\":[9],\"inclusive\":false},\"acc_title\":{\"rules\":[7],\"inclusive\":false},\"string_kv_value\":{\"rules\":[78,79],\"inclusive\":false},\"string_kv_key\":{\"rules\":[77],\"inclusive\":false},\"string_kv\":{\"rules\":[76],\"inclusive\":false},\"string\":{\"rules\":[73,74],\"inclusive\":false},\"attribute\":{\"rules\":[68,69,70,71,72,75,80],\"inclusive\":false},\"update_layout_config\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"update_rel_style\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"update_el_style\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"rel_b\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"rel_r\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"rel_l\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"rel_d\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"rel_u\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"rel_bi\":{\"rules\":[],\"inclusive\":false},\"rel\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"node_r\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"node_l\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"node\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"index\":{\"rules\":[],\"inclusive\":false},\"rel_index\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"component_ext_queue\":{\"rules\":[],\"inclusive\":false},\"component_ext_db\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"component_ext\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"component_queue\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"component_db\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"component\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"container_boundary\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"container_ext_queue\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"container_ext_db\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"container_ext\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"container_queue\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"container_db\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"container\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"birel\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"system_boundary\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"enterprise_boundary\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"boundary\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"system_ext_queue\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"system_ext_db\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"system_ext\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"system_queue\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"system_db\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"system\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"person_ext\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"person\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,2,3,4,5,6,8,10,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,81,82,83,84,85],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { sanitizeText } from '../common/common.js';\nimport {\n  setAccTitle,\n  getAccTitle,\n  getAccDescription,\n  setAccDescription,\n} from '../common/commonDb.js';\n\nlet c4ShapeArray = [];\nlet boundaryParseStack = [''];\nlet currentBoundaryParse = 'global';\nlet parentBoundaryParse = '';\nlet boundaries = [\n  {\n    alias: 'global',\n    label: { text: 'global' },\n    type: { text: 'global' },\n    tags: null,\n    link: null,\n    parentBoundary: '',\n  },\n];\nlet rels = [];\nlet title = '';\nlet wrapEnabled = false;\nlet c4ShapeInRow = 4;\nlet c4BoundaryInRow = 2;\nvar c4Type;\n\nexport const getC4Type = function () {\n  return c4Type;\n};\n\nexport const setC4Type = function (c4TypeParam) {\n  let sanitizedText = sanitizeText(c4TypeParam, getConfig());\n  c4Type = sanitizedText;\n};\n\n//type, from, to, label, ?techn, ?descr, ?sprite, ?tags, $link\nexport const addRel = function (type, from, to, label, techn, descr, sprite, tags, link) {\n  // Don't allow label nulling\n  if (\n    type === undefined ||\n    type === null ||\n    from === undefined ||\n    from === null ||\n    to === undefined ||\n    to === null ||\n    label === undefined ||\n    label === null\n  ) {\n    return;\n  }\n\n  let rel = {};\n  const old = rels.find((rel) => rel.from === from && rel.to === to);\n  if (old) {\n    rel = old;\n  } else {\n    rels.push(rel);\n  }\n\n  rel.type = type;\n  rel.from = from;\n  rel.to = to;\n  rel.label = { text: label };\n\n  if (techn === undefined || techn === null) {\n    rel.techn = { text: '' };\n  } else {\n    if (typeof techn === 'object') {\n      let [key, value] = Object.entries(techn)[0];\n      rel[key] = { text: value };\n    } else {\n      rel.techn = { text: techn };\n    }\n  }\n\n  if (descr === undefined || descr === null) {\n    rel.descr = { text: '' };\n  } else {\n    if (typeof descr === 'object') {\n      let [key, value] = Object.entries(descr)[0];\n      rel[key] = { text: value };\n    } else {\n      rel.descr = { text: descr };\n    }\n  }\n\n  if (typeof sprite === 'object') {\n    let [key, value] = Object.entries(sprite)[0];\n    rel[key] = value;\n  } else {\n    rel.sprite = sprite;\n  }\n  if (typeof tags === 'object') {\n    let [key, value] = Object.entries(tags)[0];\n    rel[key] = value;\n  } else {\n    rel.tags = tags;\n  }\n  if (typeof link === 'object') {\n    let [key, value] = Object.entries(link)[0];\n    rel[key] = value;\n  } else {\n    rel.link = link;\n  }\n  rel.wrap = autoWrap();\n};\n\n//type, alias, label, ?descr, ?sprite, ?tags, $link\nexport const addPersonOrSystem = function (typeC4Shape, alias, label, descr, sprite, tags, link) {\n  // Don't allow label nulling\n  if (alias === null || label === null) {\n    return;\n  }\n\n  let personOrSystem = {};\n  const old = c4ShapeArray.find((personOrSystem) => personOrSystem.alias === alias);\n  if (old && alias === old.alias) {\n    personOrSystem = old;\n  } else {\n    personOrSystem.alias = alias;\n    c4ShapeArray.push(personOrSystem);\n  }\n\n  // Don't allow null labels, either\n  if (label === undefined || label === null) {\n    personOrSystem.label = { text: '' };\n  } else {\n    personOrSystem.label = { text: label };\n  }\n\n  if (descr === undefined || descr === null) {\n    personOrSystem.descr = { text: '' };\n  } else {\n    if (typeof descr === 'object') {\n      let [key, value] = Object.entries(descr)[0];\n      personOrSystem[key] = { text: value };\n    } else {\n      personOrSystem.descr = { text: descr };\n    }\n  }\n\n  if (typeof sprite === 'object') {\n    let [key, value] = Object.entries(sprite)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.sprite = sprite;\n  }\n  if (typeof tags === 'object') {\n    let [key, value] = Object.entries(tags)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.tags = tags;\n  }\n  if (typeof link === 'object') {\n    let [key, value] = Object.entries(link)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.link = link;\n  }\n  personOrSystem.typeC4Shape = { text: typeC4Shape };\n  personOrSystem.parentBoundary = currentBoundaryParse;\n  personOrSystem.wrap = autoWrap();\n};\n\n//type, alias, label, ?techn, ?descr ?sprite, ?tags, $link\nexport const addContainer = function (typeC4Shape, alias, label, techn, descr, sprite, tags, link) {\n  // Don't allow label nulling\n  if (alias === null || label === null) {\n    return;\n  }\n\n  let container = {};\n  const old = c4ShapeArray.find((container) => container.alias === alias);\n  if (old && alias === old.alias) {\n    container = old;\n  } else {\n    container.alias = alias;\n    c4ShapeArray.push(container);\n  }\n\n  // Don't allow null labels, either\n  if (label === undefined || label === null) {\n    container.label = { text: '' };\n  } else {\n    container.label = { text: label };\n  }\n\n  if (techn === undefined || techn === null) {\n    container.techn = { text: '' };\n  } else {\n    if (typeof techn === 'object') {\n      let [key, value] = Object.entries(techn)[0];\n      container[key] = { text: value };\n    } else {\n      container.techn = { text: techn };\n    }\n  }\n\n  if (descr === undefined || descr === null) {\n    container.descr = { text: '' };\n  } else {\n    if (typeof descr === 'object') {\n      let [key, value] = Object.entries(descr)[0];\n      container[key] = { text: value };\n    } else {\n      container.descr = { text: descr };\n    }\n  }\n\n  if (typeof sprite === 'object') {\n    let [key, value] = Object.entries(sprite)[0];\n    container[key] = value;\n  } else {\n    container.sprite = sprite;\n  }\n  if (typeof tags === 'object') {\n    let [key, value] = Object.entries(tags)[0];\n    container[key] = value;\n  } else {\n    container.tags = tags;\n  }\n  if (typeof link === 'object') {\n    let [key, value] = Object.entries(link)[0];\n    container[key] = value;\n  } else {\n    container.link = link;\n  }\n  container.wrap = autoWrap();\n  container.typeC4Shape = { text: typeC4Shape };\n  container.parentBoundary = currentBoundaryParse;\n};\n\n//type, alias, label, ?techn, ?descr ?sprite, ?tags, $link\nexport const addComponent = function (typeC4Shape, alias, label, techn, descr, sprite, tags, link) {\n  // Don't allow label nulling\n  if (alias === null || label === null) {\n    return;\n  }\n\n  let component = {};\n  const old = c4ShapeArray.find((component) => component.alias === alias);\n  if (old && alias === old.alias) {\n    component = old;\n  } else {\n    component.alias = alias;\n    c4ShapeArray.push(component);\n  }\n\n  // Don't allow null labels, either\n  if (label === undefined || label === null) {\n    component.label = { text: '' };\n  } else {\n    component.label = { text: label };\n  }\n\n  if (techn === undefined || techn === null) {\n    component.techn = { text: '' };\n  } else {\n    if (typeof techn === 'object') {\n      let [key, value] = Object.entries(techn)[0];\n      component[key] = { text: value };\n    } else {\n      component.techn = { text: techn };\n    }\n  }\n\n  if (descr === undefined || descr === null) {\n    component.descr = { text: '' };\n  } else {\n    if (typeof descr === 'object') {\n      let [key, value] = Object.entries(descr)[0];\n      component[key] = { text: value };\n    } else {\n      component.descr = { text: descr };\n    }\n  }\n\n  if (typeof sprite === 'object') {\n    let [key, value] = Object.entries(sprite)[0];\n    component[key] = value;\n  } else {\n    component.sprite = sprite;\n  }\n  if (typeof tags === 'object') {\n    let [key, value] = Object.entries(tags)[0];\n    component[key] = value;\n  } else {\n    component.tags = tags;\n  }\n  if (typeof link === 'object') {\n    let [key, value] = Object.entries(link)[0];\n    component[key] = value;\n  } else {\n    component.link = link;\n  }\n  component.wrap = autoWrap();\n  component.typeC4Shape = { text: typeC4Shape };\n  component.parentBoundary = currentBoundaryParse;\n};\n\n//alias, label, ?type, ?tags, $link\nexport const addPersonOrSystemBoundary = function (alias, label, type, tags, link) {\n  // if (parentBoundary === null) return;\n\n  // Don't allow label nulling\n  if (alias === null || label === null) {\n    return;\n  }\n\n  let boundary = {};\n  const old = boundaries.find((boundary) => boundary.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n\n  // Don't allow null labels, either\n  if (label === undefined || label === null) {\n    boundary.label = { text: '' };\n  } else {\n    boundary.label = { text: label };\n  }\n\n  if (type === undefined || type === null) {\n    boundary.type = { text: 'system' };\n  } else {\n    if (typeof type === 'object') {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n\n  if (typeof tags === 'object') {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === 'object') {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n};\n\n//alias, label, ?type, ?tags, $link\nexport const addContainerBoundary = function (alias, label, type, tags, link) {\n  // if (parentBoundary === null) return;\n\n  // Don't allow label nulling\n  if (alias === null || label === null) {\n    return;\n  }\n\n  let boundary = {};\n  const old = boundaries.find((boundary) => boundary.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n\n  // Don't allow null labels, either\n  if (label === undefined || label === null) {\n    boundary.label = { text: '' };\n  } else {\n    boundary.label = { text: label };\n  }\n\n  if (type === undefined || type === null) {\n    boundary.type = { text: 'container' };\n  } else {\n    if (typeof type === 'object') {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n\n  if (typeof tags === 'object') {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === 'object') {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n};\n\n//alias, label, ?type, ?descr, ?sprite, ?tags, $link\nexport const addDeploymentNode = function (\n  nodeType,\n  alias,\n  label,\n  type,\n  descr,\n  sprite,\n  tags,\n  link\n) {\n  // if (parentBoundary === null) return;\n\n  // Don't allow label nulling\n  if (alias === null || label === null) {\n    return;\n  }\n\n  let boundary = {};\n  const old = boundaries.find((boundary) => boundary.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n\n  // Don't allow null labels, either\n  if (label === undefined || label === null) {\n    boundary.label = { text: '' };\n  } else {\n    boundary.label = { text: label };\n  }\n\n  if (type === undefined || type === null) {\n    boundary.type = { text: 'node' };\n  } else {\n    if (typeof type === 'object') {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n\n  if (descr === undefined || descr === null) {\n    boundary.descr = { text: '' };\n  } else {\n    if (typeof descr === 'object') {\n      let [key, value] = Object.entries(descr)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.descr = { text: descr };\n    }\n  }\n\n  if (typeof tags === 'object') {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === 'object') {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.nodeType = nodeType;\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n};\n\nexport const popBoundaryParseStack = function () {\n  currentBoundaryParse = parentBoundaryParse;\n  boundaryParseStack.pop();\n  parentBoundaryParse = boundaryParseStack.pop();\n  boundaryParseStack.push(parentBoundaryParse);\n};\n\n//elementName, ?bgColor, ?fontColor, ?borderColor, ?shadowing, ?shape, ?sprite, ?techn, ?legendText, ?legendSprite\nexport const updateElStyle = function (\n  typeC4Shape,\n  elementName,\n  bgColor,\n  fontColor,\n  borderColor,\n  shadowing,\n  shape,\n  sprite,\n  techn,\n  legendText,\n  legendSprite\n) {\n  let old = c4ShapeArray.find((element) => element.alias === elementName);\n  if (old === undefined) {\n    old = boundaries.find((element) => element.alias === elementName);\n    if (old === undefined) {\n      return;\n    }\n  }\n  if (bgColor !== undefined && bgColor !== null) {\n    if (typeof bgColor === 'object') {\n      let [key, value] = Object.entries(bgColor)[0];\n      old[key] = value;\n    } else {\n      old.bgColor = bgColor;\n    }\n  }\n  if (fontColor !== undefined && fontColor !== null) {\n    if (typeof fontColor === 'object') {\n      let [key, value] = Object.entries(fontColor)[0];\n      old[key] = value;\n    } else {\n      old.fontColor = fontColor;\n    }\n  }\n  if (borderColor !== undefined && borderColor !== null) {\n    if (typeof borderColor === 'object') {\n      let [key, value] = Object.entries(borderColor)[0];\n      old[key] = value;\n    } else {\n      old.borderColor = borderColor;\n    }\n  }\n  if (shadowing !== undefined && shadowing !== null) {\n    if (typeof shadowing === 'object') {\n      let [key, value] = Object.entries(shadowing)[0];\n      old[key] = value;\n    } else {\n      old.shadowing = shadowing;\n    }\n  }\n  if (shape !== undefined && shape !== null) {\n    if (typeof shape === 'object') {\n      let [key, value] = Object.entries(shape)[0];\n      old[key] = value;\n    } else {\n      old.shape = shape;\n    }\n  }\n  if (sprite !== undefined && sprite !== null) {\n    if (typeof sprite === 'object') {\n      let [key, value] = Object.entries(sprite)[0];\n      old[key] = value;\n    } else {\n      old.sprite = sprite;\n    }\n  }\n  if (techn !== undefined && techn !== null) {\n    if (typeof techn === 'object') {\n      let [key, value] = Object.entries(techn)[0];\n      old[key] = value;\n    } else {\n      old.techn = techn;\n    }\n  }\n  if (legendText !== undefined && legendText !== null) {\n    if (typeof legendText === 'object') {\n      let [key, value] = Object.entries(legendText)[0];\n      old[key] = value;\n    } else {\n      old.legendText = legendText;\n    }\n  }\n  if (legendSprite !== undefined && legendSprite !== null) {\n    if (typeof legendSprite === 'object') {\n      let [key, value] = Object.entries(legendSprite)[0];\n      old[key] = value;\n    } else {\n      old.legendSprite = legendSprite;\n    }\n  }\n};\n\n//textColor, lineColor, ?offsetX, ?offsetY\nexport const updateRelStyle = function (\n  typeC4Shape,\n  from,\n  to,\n  textColor,\n  lineColor,\n  offsetX,\n  offsetY\n) {\n  const old = rels.find((rel) => rel.from === from && rel.to === to);\n  if (old === undefined) {\n    return;\n  }\n  if (textColor !== undefined && textColor !== null) {\n    if (typeof textColor === 'object') {\n      let [key, value] = Object.entries(textColor)[0];\n      old[key] = value;\n    } else {\n      old.textColor = textColor;\n    }\n  }\n  if (lineColor !== undefined && lineColor !== null) {\n    if (typeof lineColor === 'object') {\n      let [key, value] = Object.entries(lineColor)[0];\n      old[key] = value;\n    } else {\n      old.lineColor = lineColor;\n    }\n  }\n  if (offsetX !== undefined && offsetX !== null) {\n    if (typeof offsetX === 'object') {\n      let [key, value] = Object.entries(offsetX)[0];\n      old[key] = parseInt(value);\n    } else {\n      old.offsetX = parseInt(offsetX);\n    }\n  }\n  if (offsetY !== undefined && offsetY !== null) {\n    if (typeof offsetY === 'object') {\n      let [key, value] = Object.entries(offsetY)[0];\n      old[key] = parseInt(value);\n    } else {\n      old.offsetY = parseInt(offsetY);\n    }\n  }\n};\n\n//?c4ShapeInRow, ?c4BoundaryInRow\nexport const updateLayoutConfig = function (typeC4Shape, c4ShapeInRowParam, c4BoundaryInRowParam) {\n  let c4ShapeInRowValue = c4ShapeInRow;\n  let c4BoundaryInRowValue = c4BoundaryInRow;\n\n  if (typeof c4ShapeInRowParam === 'object') {\n    const value = Object.values(c4ShapeInRowParam)[0];\n    c4ShapeInRowValue = parseInt(value);\n  } else {\n    c4ShapeInRowValue = parseInt(c4ShapeInRowParam);\n  }\n  if (typeof c4BoundaryInRowParam === 'object') {\n    const value = Object.values(c4BoundaryInRowParam)[0];\n    c4BoundaryInRowValue = parseInt(value);\n  } else {\n    c4BoundaryInRowValue = parseInt(c4BoundaryInRowParam);\n  }\n\n  if (c4ShapeInRowValue >= 1) {\n    c4ShapeInRow = c4ShapeInRowValue;\n  }\n  if (c4BoundaryInRowValue >= 1) {\n    c4BoundaryInRow = c4BoundaryInRowValue;\n  }\n};\n\nexport const getC4ShapeInRow = function () {\n  return c4ShapeInRow;\n};\nexport const getC4BoundaryInRow = function () {\n  return c4BoundaryInRow;\n};\nexport const getCurrentBoundaryParse = function () {\n  return currentBoundaryParse;\n};\n\nexport const getParentBoundaryParse = function () {\n  return parentBoundaryParse;\n};\n\nexport const getC4ShapeArray = function (parentBoundary) {\n  if (parentBoundary === undefined || parentBoundary === null) {\n    return c4ShapeArray;\n  } else {\n    return c4ShapeArray.filter((personOrSystem) => {\n      return personOrSystem.parentBoundary === parentBoundary;\n    });\n  }\n};\nexport const getC4Shape = function (alias) {\n  return c4ShapeArray.find((personOrSystem) => personOrSystem.alias === alias);\n};\nexport const getC4ShapeKeys = function (parentBoundary) {\n  return Object.keys(getC4ShapeArray(parentBoundary));\n};\n\nexport const getBoundaries = function (parentBoundary) {\n  if (parentBoundary === undefined || parentBoundary === null) {\n    return boundaries;\n  } else {\n    return boundaries.filter((boundary) => boundary.parentBoundary === parentBoundary);\n  }\n};\n\n/**\n * @deprecated Use {@link getBoundaries} instead\n */\nexport const getBoundarys = getBoundaries;\n\nexport const getRels = function () {\n  return rels;\n};\n\nexport const getTitle = function () {\n  return title;\n};\n\nexport const setWrap = function (wrapSetting) {\n  wrapEnabled = wrapSetting;\n};\n\nexport const autoWrap = function () {\n  return wrapEnabled;\n};\n\nexport const clear = function () {\n  c4ShapeArray = [];\n  boundaries = [\n    {\n      alias: 'global',\n      label: { text: 'global' },\n      type: { text: 'global' },\n      tags: null,\n      link: null,\n      parentBoundary: '',\n    },\n  ];\n  parentBoundaryParse = '';\n  currentBoundaryParse = 'global';\n  boundaryParseStack = [''];\n  rels = [];\n\n  boundaryParseStack = [''];\n  title = '';\n  wrapEnabled = false;\n  c4ShapeInRow = 4;\n  c4BoundaryInRow = 2;\n};\n\nexport const LINETYPE = {\n  SOLID: 0,\n  DOTTED: 1,\n  NOTE: 2,\n  SOLID_CROSS: 3,\n  DOTTED_CROSS: 4,\n  SOLID_OPEN: 5,\n  DOTTED_OPEN: 6,\n  LOOP_START: 10,\n  LOOP_END: 11,\n  ALT_START: 12,\n  ALT_ELSE: 13,\n  ALT_END: 14,\n  OPT_START: 15,\n  OPT_END: 16,\n  ACTIVE_START: 17,\n  ACTIVE_END: 18,\n  PAR_START: 19,\n  PAR_AND: 20,\n  PAR_END: 21,\n  RECT_START: 22,\n  RECT_END: 23,\n  SOLID_POINT: 24,\n  DOTTED_POINT: 25,\n};\n\nexport const ARROWTYPE = {\n  FILLED: 0,\n  OPEN: 1,\n};\n\nexport const PLACEMENT = {\n  LEFTOF: 0,\n  RIGHTOF: 1,\n  OVER: 2,\n};\n\nexport const setTitle = function (txt) {\n  let sanitizedText = sanitizeText(txt, getConfig());\n  title = sanitizedText;\n};\n\nexport default {\n  addPersonOrSystem,\n  addPersonOrSystemBoundary,\n  addContainer,\n  addContainerBoundary,\n  addComponent,\n  addDeploymentNode,\n  popBoundaryParseStack,\n  addRel,\n  updateElStyle,\n  updateRelStyle,\n  updateLayoutConfig,\n  autoWrap,\n  setWrap,\n  getC4ShapeArray,\n  getC4Shape,\n  getC4ShapeKeys,\n  getBoundaries,\n  getBoundarys,\n  getCurrentBoundaryParse,\n  getParentBoundaryParse,\n  getRels,\n  getTitle,\n  getC4Type,\n  getC4ShapeInRow,\n  getC4BoundaryInRow,\n  setAccTitle,\n  getAccTitle,\n  getAccDescription,\n  setAccDescription,\n  getConfig: () => getConfig().c4,\n  clear,\n  LINETYPE,\n  ARROWTYPE,\n  PLACEMENT,\n  setTitle,\n  setC4Type,\n  // apply,\n};\n", "import common from '../common/common.js';\nimport * as svgDrawCommon from '../common/svgDrawCommon.js';\nimport { sanitizeUrl } from '@braintree/sanitize-url';\n\nexport const drawRect = function (elem, rectData) {\n  return svgDrawCommon.drawRect(elem, rectData);\n};\n\nexport const drawImage = function (elem, width, height, x, y, link) {\n  const imageElem = elem.append('image');\n  imageElem.attr('width', width);\n  imageElem.attr('height', height);\n  imageElem.attr('x', x);\n  imageElem.attr('y', y);\n  let sanitizedLink = link.startsWith('data:image/png;base64') ? link : sanitizeUrl(link);\n  imageElem.attr('xlink:href', sanitizedLink);\n};\n\nexport const drawRels = (elem, rels, conf) => {\n  const relsElem = elem.append('g');\n  let i = 0;\n  for (let rel of rels) {\n    let textColor = rel.textColor ? rel.textColor : '#444444';\n    let strokeColor = rel.lineColor ? rel.lineColor : '#444444';\n    let offsetX = rel.offsetX ? parseInt(rel.offsetX) : 0;\n    let offsetY = rel.offsetY ? parseInt(rel.offsetY) : 0;\n\n    let url = '';\n    if (i === 0) {\n      let line = relsElem.append('line');\n      line.attr('x1', rel.startPoint.x);\n      line.attr('y1', rel.startPoint.y);\n      line.attr('x2', rel.endPoint.x);\n      line.attr('y2', rel.endPoint.y);\n\n      line.attr('stroke-width', '1');\n      line.attr('stroke', strokeColor);\n      line.style('fill', 'none');\n      if (rel.type !== 'rel_b') {\n        line.attr('marker-end', 'url(' + url + '#arrowhead)');\n      }\n      if (rel.type === 'birel' || rel.type === 'rel_b') {\n        line.attr('marker-start', 'url(' + url + '#arrowend)');\n      }\n      i = -1;\n    } else {\n      let line = relsElem.append('path');\n      line\n        .attr('fill', 'none')\n        .attr('stroke-width', '1')\n        .attr('stroke', strokeColor)\n        .attr(\n          'd',\n          'Mstartx,starty Qcontrolx,controly stopx,stopy '\n            .replaceAll('startx', rel.startPoint.x)\n            .replaceAll('starty', rel.startPoint.y)\n            .replaceAll(\n              'controlx',\n              rel.startPoint.x +\n                (rel.endPoint.x - rel.startPoint.x) / 2 -\n                (rel.endPoint.x - rel.startPoint.x) / 4\n            )\n            .replaceAll('controly', rel.startPoint.y + (rel.endPoint.y - rel.startPoint.y) / 2)\n            .replaceAll('stopx', rel.endPoint.x)\n            .replaceAll('stopy', rel.endPoint.y)\n        );\n      if (rel.type !== 'rel_b') {\n        line.attr('marker-end', 'url(' + url + '#arrowhead)');\n      }\n      if (rel.type === 'birel' || rel.type === 'rel_b') {\n        line.attr('marker-start', 'url(' + url + '#arrowend)');\n      }\n    }\n\n    let messageConf = conf.messageFont();\n    _drawTextCandidateFunc(conf)(\n      rel.label.text,\n      relsElem,\n      Math.min(rel.startPoint.x, rel.endPoint.x) +\n        Math.abs(rel.endPoint.x - rel.startPoint.x) / 2 +\n        offsetX,\n      Math.min(rel.startPoint.y, rel.endPoint.y) +\n        Math.abs(rel.endPoint.y - rel.startPoint.y) / 2 +\n        offsetY,\n      rel.label.width,\n      rel.label.height,\n      { fill: textColor },\n      messageConf\n    );\n\n    if (rel.techn && rel.techn.text !== '') {\n      messageConf = conf.messageFont();\n      _drawTextCandidateFunc(conf)(\n        '[' + rel.techn.text + ']',\n        relsElem,\n        Math.min(rel.startPoint.x, rel.endPoint.x) +\n          Math.abs(rel.endPoint.x - rel.startPoint.x) / 2 +\n          offsetX,\n        Math.min(rel.startPoint.y, rel.endPoint.y) +\n          Math.abs(rel.endPoint.y - rel.startPoint.y) / 2 +\n          conf.messageFontSize +\n          5 +\n          offsetY,\n        Math.max(rel.label.width, rel.techn.width),\n        rel.techn.height,\n        { fill: textColor, 'font-style': 'italic' },\n        messageConf\n      );\n    }\n  }\n};\n\n/**\n * Draws an boundary in the diagram\n *\n * @param {any} elem - The diagram we'll draw to.\n * @param {any} boundary - The boundary to draw.\n * @param {any} conf - DrawText implementation discriminator object\n */\nconst drawBoundary = function (elem, boundary, conf) {\n  const boundaryElem = elem.append('g');\n\n  let fillColor = boundary.bgColor ? boundary.bgColor : 'none';\n  let strokeColor = boundary.borderColor ? boundary.borderColor : '#444444';\n  let fontColor = boundary.fontColor ? boundary.fontColor : 'black';\n\n  let attrsValue = { 'stroke-width': 1.0, 'stroke-dasharray': '7.0,7.0' };\n  if (boundary.nodeType) {\n    attrsValue = { 'stroke-width': 1.0 };\n  }\n  let rectData = {\n    x: boundary.x,\n    y: boundary.y,\n    fill: fillColor,\n    stroke: strokeColor,\n    width: boundary.width,\n    height: boundary.height,\n    rx: 2.5,\n    ry: 2.5,\n    attrs: attrsValue,\n  };\n\n  drawRect(boundaryElem, rectData);\n\n  // draw label\n  let boundaryConf = conf.boundaryFont();\n  boundaryConf.fontWeight = 'bold';\n  boundaryConf.fontSize = boundaryConf.fontSize + 2;\n  boundaryConf.fontColor = fontColor;\n  _drawTextCandidateFunc(conf)(\n    boundary.label.text,\n    boundaryElem,\n    boundary.x,\n    boundary.y + boundary.label.Y,\n    boundary.width,\n    boundary.height,\n    { fill: '#444444' },\n    boundaryConf\n  );\n\n  // draw type\n  if (boundary.type && boundary.type.text !== '') {\n    boundaryConf = conf.boundaryFont();\n    boundaryConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf)(\n      boundary.type.text,\n      boundaryElem,\n      boundary.x,\n      boundary.y + boundary.type.Y,\n      boundary.width,\n      boundary.height,\n      { fill: '#444444' },\n      boundaryConf\n    );\n  }\n\n  // draw descr\n  if (boundary.descr && boundary.descr.text !== '') {\n    boundaryConf = conf.boundaryFont();\n    boundaryConf.fontSize = boundaryConf.fontSize - 2;\n    boundaryConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf)(\n      boundary.descr.text,\n      boundaryElem,\n      boundary.x,\n      boundary.y + boundary.descr.Y,\n      boundary.width,\n      boundary.height,\n      { fill: '#444444' },\n      boundaryConf\n    );\n  }\n};\n\nexport const drawC4Shape = function (elem, c4Shape, conf) {\n  let fillColor = c4Shape.bgColor ? c4Shape.bgColor : conf[c4Shape.typeC4Shape.text + '_bg_color'];\n  let strokeColor = c4Shape.borderColor\n    ? c4Shape.borderColor\n    : conf[c4Shape.typeC4Shape.text + '_border_color'];\n  let fontColor = c4Shape.fontColor ? c4Shape.fontColor : '#FFFFFF';\n\n  let personImg =\n    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=';\n  switch (c4Shape.typeC4Shape.text) {\n    case 'person':\n      personImg =\n        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=';\n      break;\n    case 'external_person':\n      personImg =\n        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAAB6ElEQVR4Xu2YLY+EMBCG9+dWr0aj0Wg0Go1Go0+j8Xdv2uTCvv1gpt0ebHKPuhDaeW4605Z9mJvx4AdXUyTUdd08z+u6flmWZRnHsWkafk9DptAwDPu+f0eAYtu2PEaGWuj5fCIZrBAC2eLBAnRCsEkkxmeaJp7iDJ2QMDdHsLg8SxKFEJaAo8lAXnmuOFIhTMpxxKATebo4UiFknuNo4OniSIXQyRxEA3YsnjGCVEjVXD7yLUAqxBGUyPv/Y4W2beMgGuS7kVQIBycH0fD+oi5pezQETxdHKmQKGk1eQEYldK+jw5GxPfZ9z7Mk0Qnhf1W1m3w//EUn5BDmSZsbR44QQLBEqrBHqOrmSKaQAxdnLArCrxZcM7A7ZKs4ioRq8LFC+NpC3WCBJsvpVw5edm9iEXFuyNfxXAgSwfrFQ1c0iNda8AdejvUgnktOtJQQxmcfFzGglc5WVCj7oDgFqU18boeFSs52CUh8LE8BIVQDT1ABrB0HtgSEYlX5doJnCwv9TXocKCaKbnwhdDKPq4lf3SwU3HLq4V/+WYhHVMa/3b4IlfyikAduCkcBc7mQ3/z/Qq/cTuikhkzB12Ae/mcJC9U+Vo8Ej1gWAtgbeGgFsAMHr50BIWOLCbezvhpBFUdY6EJuJ/QDW0XoMX60zZ0AAAAASUVORK5CYII=';\n      break;\n  }\n\n  const c4ShapeElem = elem.append('g');\n  c4ShapeElem.attr('class', 'person-man');\n\n  // <rect fill=\"#08427B\" height=\"119.2188\" rx=\"2.5\" ry=\"2.5\" stroke=\"#073B6F\" stroke-width=\"0.5\" width=\"110\" x=\"120\" y=\"7\"/>\n  // draw rect of c4Shape\n  const rect = svgDrawCommon.getNoteRect();\n\n  switch (c4Shape.typeC4Shape.text) {\n    case 'person':\n    case 'external_person':\n    case 'system':\n    case 'external_system':\n    case 'container':\n    case 'external_container':\n    case 'component':\n    case 'external_component':\n      rect.x = c4Shape.x;\n      rect.y = c4Shape.y;\n      rect.fill = fillColor;\n      rect.width = c4Shape.width;\n      rect.height = c4Shape.height;\n      rect.stroke = strokeColor;\n      rect.rx = 2.5;\n      rect.ry = 2.5;\n      rect.attrs = { 'stroke-width': 0.5 };\n      drawRect(c4ShapeElem, rect);\n      break;\n    case 'system_db':\n    case 'external_system_db':\n    case 'container_db':\n    case 'external_container_db':\n    case 'component_db':\n    case 'external_component_db':\n      c4ShapeElem\n        .append('path')\n        .attr('fill', fillColor)\n        .attr('stroke-width', '0.5')\n        .attr('stroke', strokeColor)\n        .attr(\n          'd',\n          'Mstartx,startyc0,-10 half,-10 half,-10c0,0 half,0 half,10l0,heightc0,10 -half,10 -half,10c0,0 -half,0 -half,-10l0,-height'\n            .replaceAll('startx', c4Shape.x)\n            .replaceAll('starty', c4Shape.y)\n            .replaceAll('half', c4Shape.width / 2)\n            .replaceAll('height', c4Shape.height)\n        );\n      c4ShapeElem\n        .append('path')\n        .attr('fill', 'none')\n        .attr('stroke-width', '0.5')\n        .attr('stroke', strokeColor)\n        .attr(\n          'd',\n          'Mstartx,startyc0,10 half,10 half,10c0,0 half,0 half,-10'\n            .replaceAll('startx', c4Shape.x)\n            .replaceAll('starty', c4Shape.y)\n            .replaceAll('half', c4Shape.width / 2)\n        );\n      break;\n    case 'system_queue':\n    case 'external_system_queue':\n    case 'container_queue':\n    case 'external_container_queue':\n    case 'component_queue':\n    case 'external_component_queue':\n      c4ShapeElem\n        .append('path')\n        .attr('fill', fillColor)\n        .attr('stroke-width', '0.5')\n        .attr('stroke', strokeColor)\n        .attr(\n          'd',\n          'Mstartx,startylwidth,0c5,0 5,half 5,halfc0,0 0,half -5,halfl-width,0c-5,0 -5,-half -5,-halfc0,0 0,-half 5,-half'\n            .replaceAll('startx', c4Shape.x)\n            .replaceAll('starty', c4Shape.y)\n            .replaceAll('width', c4Shape.width)\n            .replaceAll('half', c4Shape.height / 2)\n        );\n      c4ShapeElem\n        .append('path')\n        .attr('fill', 'none')\n        .attr('stroke-width', '0.5')\n        .attr('stroke', strokeColor)\n        .attr(\n          'd',\n          'Mstartx,startyc-5,0 -5,half -5,halfc0,half 5,half 5,half'\n            .replaceAll('startx', c4Shape.x + c4Shape.width)\n            .replaceAll('starty', c4Shape.y)\n            .replaceAll('half', c4Shape.height / 2)\n        );\n      break;\n  }\n\n  // draw type of c4Shape\n  let c4ShapeFontConf = getC4ShapeFont(conf, c4Shape.typeC4Shape.text);\n  c4ShapeElem\n    .append('text')\n    .attr('fill', fontColor)\n    .attr('font-family', c4ShapeFontConf.fontFamily)\n    .attr('font-size', c4ShapeFontConf.fontSize - 2)\n    .attr('font-style', 'italic')\n    .attr('lengthAdjust', 'spacing')\n    .attr('textLength', c4Shape.typeC4Shape.width)\n    .attr('x', c4Shape.x + c4Shape.width / 2 - c4Shape.typeC4Shape.width / 2)\n    .attr('y', c4Shape.y + c4Shape.typeC4Shape.Y)\n    .text('<<' + c4Shape.typeC4Shape.text + '>>');\n\n  // draw image/sprite\n  switch (c4Shape.typeC4Shape.text) {\n    case 'person':\n    case 'external_person':\n      drawImage(\n        c4ShapeElem,\n        48,\n        48,\n        c4Shape.x + c4Shape.width / 2 - 24,\n        c4Shape.y + c4Shape.image.Y,\n        personImg\n      );\n      break;\n  }\n\n  // draw label\n  let textFontConf = conf[c4Shape.typeC4Shape.text + 'Font']();\n  textFontConf.fontWeight = 'bold';\n  textFontConf.fontSize = textFontConf.fontSize + 2;\n  textFontConf.fontColor = fontColor;\n  _drawTextCandidateFunc(conf)(\n    c4Shape.label.text,\n    c4ShapeElem,\n    c4Shape.x,\n    c4Shape.y + c4Shape.label.Y,\n    c4Shape.width,\n    c4Shape.height,\n    { fill: fontColor },\n    textFontConf\n  );\n\n  // draw techn/type\n  textFontConf = conf[c4Shape.typeC4Shape.text + 'Font']();\n  textFontConf.fontColor = fontColor;\n\n  if (c4Shape.techn && c4Shape.techn?.text !== '') {\n    _drawTextCandidateFunc(conf)(\n      c4Shape.techn.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.techn.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor, 'font-style': 'italic' },\n      textFontConf\n    );\n  } else if (c4Shape.type && c4Shape.type.text !== '') {\n    _drawTextCandidateFunc(conf)(\n      c4Shape.type.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.type.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor, 'font-style': 'italic' },\n      textFontConf\n    );\n  }\n\n  // draw descr\n  if (c4Shape.descr && c4Shape.descr.text !== '') {\n    textFontConf = conf.personFont();\n    textFontConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf)(\n      c4Shape.descr.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.descr.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor },\n      textFontConf\n    );\n  }\n\n  return c4Shape.height;\n};\n\nexport const insertDatabaseIcon = function (elem) {\n  elem\n    .append('defs')\n    .append('symbol')\n    .attr('id', 'database')\n    .attr('fill-rule', 'evenodd')\n    .attr('clip-rule', 'evenodd')\n    .append('path')\n    .attr('transform', 'scale(.5)')\n    .attr(\n      'd',\n      'M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z'\n    );\n};\n\nexport const insertComputerIcon = function (elem) {\n  elem\n    .append('defs')\n    .append('symbol')\n    .attr('id', 'computer')\n    .attr('width', '24')\n    .attr('height', '24')\n    .append('path')\n    .attr('transform', 'scale(.5)')\n    .attr(\n      'd',\n      'M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z'\n    );\n};\n\nexport const insertClockIcon = function (elem) {\n  elem\n    .append('defs')\n    .append('symbol')\n    .attr('id', 'clock')\n    .attr('width', '24')\n    .attr('height', '24')\n    .append('path')\n    .attr('transform', 'scale(.5)')\n    .attr(\n      'd',\n      'M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z'\n    );\n};\n\n/**\n * Setup arrow head and define the marker. The result is appended to the svg.\n *\n * @param elem\n */\nexport const insertArrowHead = function (elem) {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', 'arrowhead')\n    .attr('refX', 9)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 12)\n    .attr('markerHeight', 12)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 0 0 L 10 5 L 0 10 z'); // this is actual shape for arrowhead\n};\n\nexport const insertArrowEnd = function (elem) {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', 'arrowend')\n    .attr('refX', 1)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 12)\n    .attr('markerHeight', 12)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 10 0 L 0 5 L 10 10 z'); // this is actual shape for arrowhead\n};\n\n/**\n * Setup arrow head and define the marker. The result is appended to the svg.\n *\n * @param {any} elem\n */\nexport const insertArrowFilledHead = function (elem) {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', 'filled-head')\n    .attr('refX', 18)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L14,7 L9,1 Z');\n};\n\n/**\n * Setup node number. The result is appended to the svg.\n *\n * @param {any} elem\n */\nexport const insertDynamicNumber = function (elem) {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', 'sequencenumber')\n    .attr('refX', 15)\n    .attr('refY', 15)\n    .attr('markerWidth', 60)\n    .attr('markerHeight', 40)\n    .attr('orient', 'auto')\n    .append('circle')\n    .attr('cx', 15)\n    .attr('cy', 15)\n    .attr('r', 6);\n  // .style(\"fill\", '#f00');\n};\n\n/**\n * Setup arrow head and define the marker. The result is appended to the svg.\n *\n * @param {any} elem\n */\nexport const insertArrowCrossHead = function (elem) {\n  const defs = elem.append('defs');\n  const marker = defs\n    .append('marker')\n    .attr('id', 'crosshead')\n    .attr('markerWidth', 15)\n    .attr('markerHeight', 8)\n    .attr('orient', 'auto')\n    .attr('refX', 16)\n    .attr('refY', 4);\n\n  // The arrow\n  marker\n    .append('path')\n    .attr('fill', 'black')\n    .attr('stroke', '#000000')\n    .style('stroke-dasharray', '0, 0')\n    .attr('stroke-width', '1px')\n    .attr('d', 'M 9,2 V 6 L16,4 Z');\n\n  // The cross\n  marker\n    .append('path')\n    .attr('fill', 'none')\n    .attr('stroke', '#000000')\n    .style('stroke-dasharray', '0, 0')\n    .attr('stroke-width', '1px')\n    .attr('d', 'M 0,1 L 6,7 M 6,1 L 0,7');\n  // this is actual shape for arrowhead\n};\n\nconst getC4ShapeFont = (cnf, typeC4Shape) => {\n  return {\n    fontFamily: cnf[typeC4Shape + 'FontFamily'],\n    fontSize: cnf[typeC4Shape + 'FontSize'],\n    fontWeight: cnf[typeC4Shape + 'FontWeight'],\n  };\n};\n\nconst _drawTextCandidateFunc = (function () {\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   */\n  function byText(content, g, x, y, width, height, textAttrs) {\n    const text = g\n      .append('text')\n      .attr('x', x + width / 2)\n      .attr('y', y + height / 2 + 5)\n      .style('text-anchor', 'middle')\n      .text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} conf\n   */\n  function byTspan(content, g, x, y, width, height, textAttrs, conf) {\n    const { fontSize, fontFamily, fontWeight } = conf;\n\n    const lines = content.split(common.lineBreakRegex);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * fontSize - (fontSize * (lines.length - 1)) / 2;\n      const text = g\n        .append('text')\n        .attr('x', x + width / 2)\n        .attr('y', y)\n        .style('text-anchor', 'middle')\n        .attr('dominant-baseline', 'middle')\n        .style('font-size', fontSize)\n        .style('font-weight', fontWeight)\n        .style('font-family', fontFamily);\n      text\n        .append('tspan')\n        // .attr('x', x + width / 2)\n        .attr('dy', dy)\n        .text(lines[i])\n        // .attr('y', y + height / 2)\n        .attr('alignment-baseline', 'mathematical');\n\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} conf\n   */\n  function byFo(content, g, x, y, width, height, textAttrs, conf) {\n    const s = g.append('switch');\n    const f = s\n      .append('foreignObject')\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', width)\n      .attr('height', height);\n\n    const text = f\n      .append('xhtml:div')\n      .style('display', 'table')\n      .style('height', '100%')\n      .style('width', '100%');\n\n    text\n      .append('div')\n      .style('display', 'table-cell')\n      .style('text-align', 'center')\n      .style('vertical-align', 'middle')\n      .text(content);\n\n    byTspan(content, s, x, y, width, height, textAttrs, conf);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   * @param {any} toText\n   * @param {any} fromTextAttrsDict\n   */\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (fromTextAttrsDict.hasOwnProperty(key)) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n\n  return function (conf) {\n    return conf.textPlacement === 'fo' ? byFo : conf.textPlacement === 'old' ? byText : byTspan;\n  };\n})();\n\nexport default {\n  drawRect,\n  drawBoundary,\n  drawC4Shape,\n  drawRels,\n  drawImage,\n  insertArrowHead,\n  insertArrowEnd,\n  insertArrowFilledHead,\n  insertDynamicNumber,\n  insertArrowCrossHead,\n  insertDatabaseIcon,\n  insertComputerIcon,\n  insertClockIcon,\n};\n\n// cspell:ignoreRegExp /'Mstartx.*/g\n", "import { select } from 'd3';\nimport svgDraw from './svgDraw.js';\nimport { log } from '../../logger.js';\nimport { parser } from './parser/c4Diagram.jison';\nimport common from '../common/common.js';\nimport c4Db from './c4Db.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport assignWithDepth from '../../assignWithDepth.js';\nimport { wrapLabel, calculateTextWidth, calculateTextHeight } from '../../utils.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\n\nlet globalBoundaryMaxX = 0,\n  globalBoundaryMaxY = 0;\n\nlet c4ShapeInRow = 4;\nlet c4BoundaryInRow = 2;\n\nparser.yy = c4Db;\n\nlet conf = {};\n\nclass Bounds {\n  constructor(diagObj) {\n    this.name = '';\n    this.data = {};\n    this.data.startx = undefined;\n    this.data.stopx = undefined;\n    this.data.starty = undefined;\n    this.data.stopy = undefined;\n    this.data.widthLimit = undefined;\n\n    this.nextData = {};\n    this.nextData.startx = undefined;\n    this.nextData.stopx = undefined;\n    this.nextData.starty = undefined;\n    this.nextData.stopy = undefined;\n    this.nextData.cnt = 0;\n\n    setConf(diagObj.db.getConfig());\n  }\n\n  setData(startx, stopx, starty, stopy) {\n    this.nextData.startx = this.data.startx = startx;\n    this.nextData.stopx = this.data.stopx = stopx;\n    this.nextData.starty = this.data.starty = starty;\n    this.nextData.stopy = this.data.stopy = stopy;\n  }\n\n  updateVal(obj, key, val, fun) {\n    if (obj[key] === undefined) {\n      obj[key] = val;\n    } else {\n      obj[key] = fun(val, obj[key]);\n    }\n  }\n\n  insert(c4Shape) {\n    this.nextData.cnt = this.nextData.cnt + 1;\n    let _startx =\n      this.nextData.startx === this.nextData.stopx\n        ? this.nextData.stopx + c4Shape.margin\n        : this.nextData.stopx + c4Shape.margin * 2;\n    let _stopx = _startx + c4Shape.width;\n    let _starty = this.nextData.starty + c4Shape.margin * 2;\n    let _stopy = _starty + c4Shape.height;\n    if (\n      _startx >= this.data.widthLimit ||\n      _stopx >= this.data.widthLimit ||\n      this.nextData.cnt > c4ShapeInRow\n    ) {\n      _startx = this.nextData.startx + c4Shape.margin + conf.nextLinePaddingX;\n      _starty = this.nextData.stopy + c4Shape.margin * 2;\n\n      this.nextData.stopx = _stopx = _startx + c4Shape.width;\n      this.nextData.starty = this.nextData.stopy;\n      this.nextData.stopy = _stopy = _starty + c4Shape.height;\n      this.nextData.cnt = 1;\n    }\n\n    c4Shape.x = _startx;\n    c4Shape.y = _starty;\n\n    this.updateVal(this.data, 'startx', _startx, Math.min);\n    this.updateVal(this.data, 'starty', _starty, Math.min);\n    this.updateVal(this.data, 'stopx', _stopx, Math.max);\n    this.updateVal(this.data, 'stopy', _stopy, Math.max);\n\n    this.updateVal(this.nextData, 'startx', _startx, Math.min);\n    this.updateVal(this.nextData, 'starty', _starty, Math.min);\n    this.updateVal(this.nextData, 'stopx', _stopx, Math.max);\n    this.updateVal(this.nextData, 'stopy', _stopy, Math.max);\n  }\n\n  init(diagObj) {\n    this.name = '';\n    this.data = {\n      startx: undefined,\n      stopx: undefined,\n      starty: undefined,\n      stopy: undefined,\n      widthLimit: undefined,\n    };\n    this.nextData = {\n      startx: undefined,\n      stopx: undefined,\n      starty: undefined,\n      stopy: undefined,\n      cnt: 0,\n    };\n    setConf(diagObj.db.getConfig());\n  }\n\n  bumpLastMargin(margin) {\n    this.data.stopx += margin;\n    this.data.stopy += margin;\n  }\n}\n\nexport const setConf = function (cnf) {\n  assignWithDepth(conf, cnf);\n\n  if (cnf.fontFamily) {\n    conf.personFontFamily = conf.systemFontFamily = conf.messageFontFamily = cnf.fontFamily;\n  }\n  if (cnf.fontSize) {\n    conf.personFontSize = conf.systemFontSize = conf.messageFontSize = cnf.fontSize;\n  }\n  if (cnf.fontWeight) {\n    conf.personFontWeight = conf.systemFontWeight = conf.messageFontWeight = cnf.fontWeight;\n  }\n};\n\nconst c4ShapeFont = (cnf, typeC4Shape) => {\n  return {\n    fontFamily: cnf[typeC4Shape + 'FontFamily'],\n    fontSize: cnf[typeC4Shape + 'FontSize'],\n    fontWeight: cnf[typeC4Shape + 'FontWeight'],\n  };\n};\n\nconst boundaryFont = (cnf) => {\n  return {\n    fontFamily: cnf.boundaryFontFamily,\n    fontSize: cnf.boundaryFontSize,\n    fontWeight: cnf.boundaryFontWeight,\n  };\n};\n\nconst messageFont = (cnf) => {\n  return {\n    fontFamily: cnf.messageFontFamily,\n    fontSize: cnf.messageFontSize,\n    fontWeight: cnf.messageFontWeight,\n  };\n};\n\n/**\n * @param textType\n * @param c4Shape\n * @param c4ShapeTextWrap\n * @param textConf\n * @param textLimitWidth\n */\nfunction calcC4ShapeTextWH(textType, c4Shape, c4ShapeTextWrap, textConf, textLimitWidth) {\n  if (!c4Shape[textType].width) {\n    if (c4ShapeTextWrap) {\n      c4Shape[textType].text = wrapLabel(c4Shape[textType].text, textLimitWidth, textConf);\n      c4Shape[textType].textLines = c4Shape[textType].text.split(common.lineBreakRegex).length;\n      // c4Shape[textType].width = calculateTextWidth(c4Shape[textType].text, textConf);\n      c4Shape[textType].width = textLimitWidth;\n      // c4Shape[textType].height = c4Shape[textType].textLines * textConf.fontSize;\n      c4Shape[textType].height = calculateTextHeight(c4Shape[textType].text, textConf);\n    } else {\n      let lines = c4Shape[textType].text.split(common.lineBreakRegex);\n      c4Shape[textType].textLines = lines.length;\n      let lineHeight = 0;\n      c4Shape[textType].height = 0;\n      c4Shape[textType].width = 0;\n      for (const line of lines) {\n        c4Shape[textType].width = Math.max(\n          calculateTextWidth(line, textConf),\n          c4Shape[textType].width\n        );\n        lineHeight = calculateTextHeight(line, textConf);\n        c4Shape[textType].height = c4Shape[textType].height + lineHeight;\n      }\n      // c4Shapes[textType].height = c4Shapes[textType].textLines * textConf.fontSize;\n    }\n  }\n}\n\nexport const drawBoundary = function (diagram, boundary, bounds) {\n  boundary.x = bounds.data.startx;\n  boundary.y = bounds.data.starty;\n  boundary.width = bounds.data.stopx - bounds.data.startx;\n  boundary.height = bounds.data.stopy - bounds.data.starty;\n\n  boundary.label.y = conf.c4ShapeMargin - 35;\n\n  let boundaryTextWrap = boundary.wrap && conf.wrap;\n  let boundaryLabelConf = boundaryFont(conf);\n  boundaryLabelConf.fontSize = boundaryLabelConf.fontSize + 2;\n  boundaryLabelConf.fontWeight = 'bold';\n  let textLimitWidth = calculateTextWidth(boundary.label.text, boundaryLabelConf);\n  calcC4ShapeTextWH('label', boundary, boundaryTextWrap, boundaryLabelConf, textLimitWidth);\n\n  svgDraw.drawBoundary(diagram, boundary, conf);\n};\n\nexport const drawC4ShapeArray = function (currentBounds, diagram, c4ShapeArray, c4ShapeKeys) {\n  // Upper Y is relative point\n  let Y = 0;\n  // Draw the c4ShapeArray\n  for (const c4ShapeKey of c4ShapeKeys) {\n    Y = 0;\n    const c4Shape = c4ShapeArray[c4ShapeKey];\n\n    // calc c4 shape type width and height\n\n    let c4ShapeTypeConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n    c4ShapeTypeConf.fontSize = c4ShapeTypeConf.fontSize - 2;\n    c4Shape.typeC4Shape.width = calculateTextWidth(\n      '«' + c4Shape.typeC4Shape.text + '»',\n      c4ShapeTypeConf\n    );\n    c4Shape.typeC4Shape.height = c4ShapeTypeConf.fontSize + 2;\n    c4Shape.typeC4Shape.Y = conf.c4ShapePadding;\n    Y = c4Shape.typeC4Shape.Y + c4Shape.typeC4Shape.height - 4;\n\n    // set image width and height c4Shape.x + c4Shape.width / 2 - 24, c4Shape.y + 28\n    // let imageWidth = 0,\n    //   imageHeight = 0,\n    //   imageY = 0;\n    //\n    c4Shape.image = { width: 0, height: 0, Y: 0 };\n    switch (c4Shape.typeC4Shape.text) {\n      case 'person':\n      case 'external_person':\n        c4Shape.image.width = 48;\n        c4Shape.image.height = 48;\n        c4Shape.image.Y = Y;\n        Y = c4Shape.image.Y + c4Shape.image.height;\n        break;\n    }\n    if (c4Shape.sprite) {\n      c4Shape.image.width = 48;\n      c4Shape.image.height = 48;\n      c4Shape.image.Y = Y;\n      Y = c4Shape.image.Y + c4Shape.image.height;\n    }\n\n    // Y = conf.c4ShapePadding + c4Shape.image.height;\n\n    let c4ShapeTextWrap = c4Shape.wrap && conf.wrap;\n    let textLimitWidth = conf.width - conf.c4ShapePadding * 2;\n\n    let c4ShapeLabelConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n    c4ShapeLabelConf.fontSize = c4ShapeLabelConf.fontSize + 2;\n    c4ShapeLabelConf.fontWeight = 'bold';\n    calcC4ShapeTextWH('label', c4Shape, c4ShapeTextWrap, c4ShapeLabelConf, textLimitWidth);\n    c4Shape.label.Y = Y + 8;\n    Y = c4Shape.label.Y + c4Shape.label.height;\n\n    if (c4Shape.type && c4Shape.type.text !== '') {\n      c4Shape.type.text = '[' + c4Shape.type.text + ']';\n      let c4ShapeTypeConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n      calcC4ShapeTextWH('type', c4Shape, c4ShapeTextWrap, c4ShapeTypeConf, textLimitWidth);\n      c4Shape.type.Y = Y + 5;\n      Y = c4Shape.type.Y + c4Shape.type.height;\n    } else if (c4Shape.techn && c4Shape.techn.text !== '') {\n      c4Shape.techn.text = '[' + c4Shape.techn.text + ']';\n      let c4ShapeTechnConf = c4ShapeFont(conf, c4Shape.techn.text);\n      calcC4ShapeTextWH('techn', c4Shape, c4ShapeTextWrap, c4ShapeTechnConf, textLimitWidth);\n      c4Shape.techn.Y = Y + 5;\n      Y = c4Shape.techn.Y + c4Shape.techn.height;\n    }\n\n    let rectHeight = Y;\n    let rectWidth = c4Shape.label.width;\n\n    if (c4Shape.descr && c4Shape.descr.text !== '') {\n      let c4ShapeDescrConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n      calcC4ShapeTextWH('descr', c4Shape, c4ShapeTextWrap, c4ShapeDescrConf, textLimitWidth);\n      c4Shape.descr.Y = Y + 20;\n      Y = c4Shape.descr.Y + c4Shape.descr.height;\n\n      rectWidth = Math.max(c4Shape.label.width, c4Shape.descr.width);\n      rectHeight = Y - c4Shape.descr.textLines * 5;\n    }\n\n    rectWidth = rectWidth + conf.c4ShapePadding;\n    // let rectHeight =\n\n    c4Shape.width = Math.max(c4Shape.width || conf.width, rectWidth, conf.width);\n    c4Shape.height = Math.max(c4Shape.height || conf.height, rectHeight, conf.height);\n    c4Shape.margin = c4Shape.margin || conf.c4ShapeMargin;\n\n    currentBounds.insert(c4Shape);\n\n    svgDraw.drawC4Shape(diagram, c4Shape, conf);\n  }\n\n  currentBounds.bumpLastMargin(conf.c4ShapeMargin);\n};\n\nclass Point {\n  constructor(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n}\n\n/* * *\n * Get the intersection of the line between the center point of a rectangle and a point outside the rectangle.\n * Algorithm idea.\n * Using a point outside the rectangle as the coordinate origin, the graph is divided into four quadrants, and each quadrant is divided into two cases, with separate treatment on the coordinate axes\n * 1. The case of coordinate axes.\n * 1. The case of the negative x-axis\n * 2. The case of the positive x-axis\n * 3. The case of the positive y-axis\n * 4. The negative y-axis case\n * 2. Quadrant cases.\n * 2.1. first quadrant: the case where the line intersects the left side of the rectangle; the case where it intersects the lower side of the rectangle\n * 2.2. second quadrant: the case where the line intersects the right side of the rectangle; the case where it intersects the lower edge of the rectangle\n * 2.3. third quadrant: the case where the line intersects the right side of the rectangle; the case where it intersects the upper edge of the rectangle\n * 2.4. fourth quadrant: the case where the line intersects the left side of the rectangle; the case where it intersects the upper side of the rectangle\n *\n */\nlet getIntersectPoint = function (fromNode, endPoint) {\n  let x1 = fromNode.x;\n\n  let y1 = fromNode.y;\n\n  let x2 = endPoint.x;\n\n  let y2 = endPoint.y;\n\n  let fromCenterX = x1 + fromNode.width / 2;\n\n  let fromCenterY = y1 + fromNode.height / 2;\n\n  let dx = Math.abs(x1 - x2);\n\n  let dy = Math.abs(y1 - y2);\n\n  let tanDYX = dy / dx;\n\n  let fromDYX = fromNode.height / fromNode.width;\n\n  let returnPoint = null;\n\n  if (y1 == y2 && x1 < x2) {\n    returnPoint = new Point(x1 + fromNode.width, fromCenterY);\n  } else if (y1 == y2 && x1 > x2) {\n    returnPoint = new Point(x1, fromCenterY);\n  } else if (x1 == x2 && y1 < y2) {\n    returnPoint = new Point(fromCenterX, y1 + fromNode.height);\n  } else if (x1 == x2 && y1 > y2) {\n    returnPoint = new Point(fromCenterX, y1);\n  }\n\n  if (x1 > x2 && y1 < y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1, fromCenterY + (tanDYX * fromNode.width) / 2);\n    } else {\n      returnPoint = new Point(\n        fromCenterX - ((dx / dy) * fromNode.height) / 2,\n        y1 + fromNode.height\n      );\n    }\n  } else if (x1 < x2 && y1 < y2) {\n    //\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1 + fromNode.width, fromCenterY + (tanDYX * fromNode.width) / 2);\n    } else {\n      returnPoint = new Point(\n        fromCenterX + ((dx / dy) * fromNode.height) / 2,\n        y1 + fromNode.height\n      );\n    }\n  } else if (x1 < x2 && y1 > y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1 + fromNode.width, fromCenterY - (tanDYX * fromNode.width) / 2);\n    } else {\n      returnPoint = new Point(fromCenterX + ((fromNode.height / 2) * dx) / dy, y1);\n    }\n  } else if (x1 > x2 && y1 > y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1, fromCenterY - (fromNode.width / 2) * tanDYX);\n    } else {\n      returnPoint = new Point(fromCenterX - ((fromNode.height / 2) * dx) / dy, y1);\n    }\n  }\n  return returnPoint;\n};\n\nlet getIntersectPoints = function (fromNode, endNode) {\n  let endIntersectPoint = { x: 0, y: 0 };\n  endIntersectPoint.x = endNode.x + endNode.width / 2;\n  endIntersectPoint.y = endNode.y + endNode.height / 2;\n  let startPoint = getIntersectPoint(fromNode, endIntersectPoint);\n\n  endIntersectPoint.x = fromNode.x + fromNode.width / 2;\n  endIntersectPoint.y = fromNode.y + fromNode.height / 2;\n  let endPoint = getIntersectPoint(endNode, endIntersectPoint);\n  return { startPoint: startPoint, endPoint: endPoint };\n};\n\nexport const drawRels = function (diagram, rels, getC4ShapeObj, diagObj) {\n  let i = 0;\n  for (let rel of rels) {\n    i = i + 1;\n    let relTextWrap = rel.wrap && conf.wrap;\n    let relConf = messageFont(conf);\n    let diagramType = diagObj.db.getC4Type();\n    if (diagramType === 'C4Dynamic') {\n      rel.label.text = i + ': ' + rel.label.text;\n    }\n    let textLimitWidth = calculateTextWidth(rel.label.text, relConf);\n    calcC4ShapeTextWH('label', rel, relTextWrap, relConf, textLimitWidth);\n\n    if (rel.techn && rel.techn.text !== '') {\n      textLimitWidth = calculateTextWidth(rel.techn.text, relConf);\n      calcC4ShapeTextWH('techn', rel, relTextWrap, relConf, textLimitWidth);\n    }\n\n    if (rel.descr && rel.descr.text !== '') {\n      textLimitWidth = calculateTextWidth(rel.descr.text, relConf);\n      calcC4ShapeTextWH('descr', rel, relTextWrap, relConf, textLimitWidth);\n    }\n\n    let fromNode = getC4ShapeObj(rel.from);\n    let endNode = getC4ShapeObj(rel.to);\n    let points = getIntersectPoints(fromNode, endNode);\n    rel.startPoint = points.startPoint;\n    rel.endPoint = points.endPoint;\n  }\n  svgDraw.drawRels(diagram, rels, conf);\n};\n\n/**\n * @param diagram\n * @param parentBoundaryAlias\n * @param parentBounds\n * @param currentBoundaries\n * @param diagObj\n */\nfunction drawInsideBoundary(\n  diagram,\n  parentBoundaryAlias,\n  parentBounds,\n  currentBoundaries,\n  diagObj\n) {\n  let currentBounds = new Bounds(diagObj);\n  // Calculate the width limit of the boundary.  label/type 的长度，\n  currentBounds.data.widthLimit =\n    parentBounds.data.widthLimit / Math.min(c4BoundaryInRow, currentBoundaries.length);\n  // Math.min(\n  //   conf.width * conf.c4ShapeInRow + conf.c4ShapeMargin * conf.c4ShapeInRow * 2,\n  //   parentBounds.data.widthLimit / Math.min(conf.c4BoundaryInRow, currentBoundaries.length)\n  // );\n  for (let [i, currentBoundary] of currentBoundaries.entries()) {\n    let Y = 0;\n    currentBoundary.image = { width: 0, height: 0, Y: 0 };\n    if (currentBoundary.sprite) {\n      currentBoundary.image.width = 48;\n      currentBoundary.image.height = 48;\n      currentBoundary.image.Y = Y;\n      Y = currentBoundary.image.Y + currentBoundary.image.height;\n    }\n\n    let currentBoundaryTextWrap = currentBoundary.wrap && conf.wrap;\n\n    let currentBoundaryLabelConf = boundaryFont(conf);\n    currentBoundaryLabelConf.fontSize = currentBoundaryLabelConf.fontSize + 2;\n    currentBoundaryLabelConf.fontWeight = 'bold';\n    calcC4ShapeTextWH(\n      'label',\n      currentBoundary,\n      currentBoundaryTextWrap,\n      currentBoundaryLabelConf,\n      currentBounds.data.widthLimit\n    );\n    currentBoundary.label.Y = Y + 8;\n    Y = currentBoundary.label.Y + currentBoundary.label.height;\n\n    if (currentBoundary.type && currentBoundary.type.text !== '') {\n      currentBoundary.type.text = '[' + currentBoundary.type.text + ']';\n      let currentBoundaryTypeConf = boundaryFont(conf);\n      calcC4ShapeTextWH(\n        'type',\n        currentBoundary,\n        currentBoundaryTextWrap,\n        currentBoundaryTypeConf,\n        currentBounds.data.widthLimit\n      );\n      currentBoundary.type.Y = Y + 5;\n      Y = currentBoundary.type.Y + currentBoundary.type.height;\n    }\n\n    if (currentBoundary.descr && currentBoundary.descr.text !== '') {\n      let currentBoundaryDescrConf = boundaryFont(conf);\n      currentBoundaryDescrConf.fontSize = currentBoundaryDescrConf.fontSize - 2;\n      calcC4ShapeTextWH(\n        'descr',\n        currentBoundary,\n        currentBoundaryTextWrap,\n        currentBoundaryDescrConf,\n        currentBounds.data.widthLimit\n      );\n      currentBoundary.descr.Y = Y + 20;\n      Y = currentBoundary.descr.Y + currentBoundary.descr.height;\n    }\n\n    if (i == 0 || i % c4BoundaryInRow === 0) {\n      // Calculate the drawing start point of the currentBoundaries.\n      let _x = parentBounds.data.startx + conf.diagramMarginX;\n      let _y = parentBounds.data.stopy + conf.diagramMarginY + Y;\n\n      currentBounds.setData(_x, _x, _y, _y);\n    } else {\n      // Calculate the drawing start point of the currentBoundaries.\n      let _x =\n        currentBounds.data.stopx !== currentBounds.data.startx\n          ? currentBounds.data.stopx + conf.diagramMarginX\n          : currentBounds.data.startx;\n      let _y = currentBounds.data.starty;\n\n      currentBounds.setData(_x, _x, _y, _y);\n    }\n    currentBounds.name = currentBoundary.alias;\n    let currentPersonOrSystemArray = diagObj.db.getC4ShapeArray(currentBoundary.alias);\n    let currentPersonOrSystemKeys = diagObj.db.getC4ShapeKeys(currentBoundary.alias);\n\n    if (currentPersonOrSystemKeys.length > 0) {\n      drawC4ShapeArray(\n        currentBounds,\n        diagram,\n        currentPersonOrSystemArray,\n        currentPersonOrSystemKeys\n      );\n    }\n    parentBoundaryAlias = currentBoundary.alias;\n    let nextCurrentBoundaries = diagObj.db.getBoundarys(parentBoundaryAlias);\n\n    if (nextCurrentBoundaries.length > 0) {\n      // draw boundary inside currentBoundary\n      drawInsideBoundary(\n        diagram,\n        parentBoundaryAlias,\n        currentBounds,\n        nextCurrentBoundaries,\n        diagObj\n      );\n    }\n    // draw boundary\n    if (currentBoundary.alias !== 'global') {\n      drawBoundary(diagram, currentBoundary, currentBounds);\n    }\n    parentBounds.data.stopy = Math.max(\n      currentBounds.data.stopy + conf.c4ShapeMargin,\n      parentBounds.data.stopy\n    );\n    parentBounds.data.stopx = Math.max(\n      currentBounds.data.stopx + conf.c4ShapeMargin,\n      parentBounds.data.stopx\n    );\n    globalBoundaryMaxX = Math.max(globalBoundaryMaxX, parentBounds.data.stopx);\n    globalBoundaryMaxY = Math.max(globalBoundaryMaxY, parentBounds.data.stopy);\n  }\n}\n\n/**\n * Draws a sequenceDiagram in the tag with id: id based on the graph definition in text.\n *\n * @param {any} _text\n * @param {any} id\n * @param {any} _version\n * @param diagObj\n */\nexport const draw = function (_text, id, _version, diagObj) {\n  conf = getConfig().c4;\n  const securityLevel = getConfig().securityLevel;\n  // Handle root and Document for when rendering in sandbox mode\n  let sandboxElement;\n  if (securityLevel === 'sandbox') {\n    sandboxElement = select('#i' + id);\n  }\n  const root =\n    securityLevel === 'sandbox'\n      ? select(sandboxElement.nodes()[0].contentDocument.body)\n      : select('body');\n\n  let db = diagObj.db;\n\n  diagObj.db.setWrap(conf.wrap);\n\n  c4ShapeInRow = db.getC4ShapeInRow();\n  c4BoundaryInRow = db.getC4BoundaryInRow();\n\n  log.debug(`C:${JSON.stringify(conf, null, 2)}`);\n\n  const diagram =\n    securityLevel === 'sandbox' ? root.select(`[id=\"${id}\"]`) : select(`[id=\"${id}\"]`);\n\n  svgDraw.insertComputerIcon(diagram);\n  svgDraw.insertDatabaseIcon(diagram);\n  svgDraw.insertClockIcon(diagram);\n\n  let screenBounds = new Bounds(diagObj);\n\n  screenBounds.setData(\n    conf.diagramMarginX,\n    conf.diagramMarginX,\n    conf.diagramMarginY,\n    conf.diagramMarginY\n  );\n\n  screenBounds.data.widthLimit = screen.availWidth;\n  globalBoundaryMaxX = conf.diagramMarginX;\n  globalBoundaryMaxY = conf.diagramMarginY;\n\n  const title = diagObj.db.getTitle();\n  let currentBoundaries = diagObj.db.getBoundarys('');\n  // switch (c4type) {\n  //   case 'C4Context':\n  drawInsideBoundary(diagram, '', screenBounds, currentBoundaries, diagObj);\n  //     break;\n  // }\n\n  // The arrow head definition is attached to the svg once\n  svgDraw.insertArrowHead(diagram);\n  svgDraw.insertArrowEnd(diagram);\n  svgDraw.insertArrowCrossHead(diagram);\n  svgDraw.insertArrowFilledHead(diagram);\n\n  drawRels(diagram, diagObj.db.getRels(), diagObj.db.getC4Shape, diagObj);\n\n  screenBounds.data.stopx = globalBoundaryMaxX;\n  screenBounds.data.stopy = globalBoundaryMaxY;\n\n  const box = screenBounds.data;\n\n  // Make sure the height of the diagram supports long menus.\n  let boxHeight = box.stopy - box.starty;\n\n  let height = boxHeight + 2 * conf.diagramMarginY;\n\n  // Make sure the width of the diagram supports wide menus.\n  let boxWidth = box.stopx - box.startx;\n  const width = boxWidth + 2 * conf.diagramMarginX;\n\n  if (title) {\n    diagram\n      .append('text')\n      .text(title)\n      .attr('x', (box.stopx - box.startx) / 2 - 4 * conf.diagramMarginX)\n      .attr('y', box.starty + conf.diagramMarginY);\n  }\n\n  configureSvgSize(diagram, height, width, conf.useMaxWidth);\n\n  const extraVertForTitle = title ? 60 : 0;\n  diagram.attr(\n    'viewBox',\n    box.startx -\n      conf.diagramMarginX +\n      ' -' +\n      (conf.diagramMarginY + extraVertForTitle) +\n      ' ' +\n      width +\n      ' ' +\n      (height + extraVertForTitle)\n  );\n\n  log.debug(`models:`, box);\n};\n\nexport default {\n  drawPersonOrSystemArray: drawC4ShapeArray,\n  drawBoundary,\n  setConf,\n  draw,\n};\n", "const getStyles = (options) =>\n  `.person {\n    stroke: ${options.personBorder};\n    fill: ${options.personBkg};\n  }\n`;\n\nexport default getStyles;\n", "// @ts-ignore: JISON doesn't support types\nimport parser from './parser/c4Diagram.jison';\nimport db from './c4Db.js';\nimport renderer from './c4Renderer.js';\nimport styles from './styles.js';\nimport type { MermaidConfig } from '../../config.type.js';\nimport type { DiagramDefinition } from '../../diagram-api/types.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  db,\n  renderer,\n  styles,\n  init: ({ c4, wrap }: MermaidConfig) => {\n    renderer.setConf(c4);\n    db.setWrap(wrap);\n  },\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA,IAAI,SAAU,WAAU;AACxB,MAAI,IAAE,gCAAS,GAAE,GAAEA,IAAE,GAAE;AAAC,SAAIA,KAAEA,MAAG,CAAC,GAAE,IAAE,EAAE,QAAO,KAAIA,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE;AAAC,WAAOA;AAAA,EAAC,GAAhE,MAAkE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,IAAG,EAAE,GAAE,MAAI,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,IAAG,EAAE,GAAE,MAAI,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE;AACzhC,MAAIC,UAAS;AAAA,IAAC,OAAO,gCAAS,QAAS;AAAA,IAAE,GAApB;AAAA,IACrB,IAAI,CAAC;AAAA,IACL,UAAU,EAAC,SAAQ,GAAE,SAAQ,GAAE,cAAa,GAAE,aAAY,GAAE,gBAAe,GAAE,gBAAe,GAAE,gBAAe,GAAE,gBAAe,GAAE,eAAc,IAAG,cAAa,IAAG,WAAU,IAAG,cAAa,IAAG,OAAM,IAAG,gBAAe,IAAG,gBAAe,IAAG,cAAa,IAAG,iBAAgB,IAAG,mBAAkB,IAAG,qBAAoB,IAAG,kBAAiB,IAAG,SAAQ,IAAG,kBAAiB,IAAG,aAAY,IAAG,mBAAkB,IAAG,aAAY,IAAG,mBAAkB,IAAG,6BAA4B,IAAG,qBAAoB,IAAG,0BAAyB,IAAG,yBAAwB,IAAG,iBAAgB,IAAG,UAAS,IAAG,uBAAsB,IAAG,cAAa,IAAG,mBAAkB,IAAG,YAAW,IAAG,sBAAqB,IAAG,QAAO,IAAG,UAAS,IAAG,UAAS,IAAG,UAAS,IAAG,oBAAmB,IAAG,UAAS,IAAG,cAAa,IAAG,UAAS,IAAG,aAAY,IAAG,gBAAe,IAAG,cAAa,IAAG,iBAAgB,IAAG,oBAAmB,IAAG,aAAY,IAAG,gBAAe,IAAG,mBAAkB,IAAG,iBAAgB,IAAG,oBAAmB,IAAG,uBAAsB,IAAG,aAAY,IAAG,gBAAe,IAAG,mBAAkB,IAAG,iBAAgB,IAAG,oBAAmB,IAAG,uBAAsB,IAAG,OAAM,IAAG,SAAQ,IAAG,SAAQ,IAAG,SAAQ,IAAG,SAAQ,IAAG,SAAQ,IAAG,SAAQ,IAAG,aAAY,IAAG,mBAAkB,IAAG,oBAAmB,IAAG,wBAAuB,IAAG,aAAY,IAAG,OAAM,IAAG,WAAU,IAAG,aAAY,IAAG,aAAY,IAAG,mBAAkB,IAAG,WAAU,GAAE,QAAO,EAAC;AAAA,IACt2C,YAAY,EAAC,GAAE,SAAQ,GAAE,gBAAe,GAAE,gBAAe,GAAE,gBAAe,GAAE,gBAAe,IAAG,cAAa,IAAG,WAAU,IAAG,OAAM,IAAG,gBAAe,IAAG,gBAAe,IAAG,cAAa,IAAG,iBAAgB,IAAG,SAAQ,IAAG,kBAAiB,IAAG,aAAY,IAAG,mBAAkB,IAAG,aAAY,IAAG,mBAAkB,IAAG,6BAA4B,IAAG,UAAS,IAAG,uBAAsB,IAAG,mBAAkB,IAAG,YAAW,IAAG,sBAAqB,IAAG,QAAO,IAAG,UAAS,IAAG,UAAS,IAAG,UAAS,IAAG,UAAS,IAAG,cAAa,IAAG,UAAS,IAAG,aAAY,IAAG,gBAAe,IAAG,cAAa,IAAG,iBAAgB,IAAG,oBAAmB,IAAG,aAAY,IAAG,gBAAe,IAAG,mBAAkB,IAAG,iBAAgB,IAAG,oBAAmB,IAAG,uBAAsB,IAAG,aAAY,IAAG,gBAAe,IAAG,mBAAkB,IAAG,iBAAgB,IAAG,oBAAmB,IAAG,uBAAsB,IAAG,OAAM,IAAG,SAAQ,IAAG,SAAQ,IAAG,SAAQ,IAAG,SAAQ,IAAG,SAAQ,IAAG,SAAQ,IAAG,aAAY,IAAG,mBAAkB,IAAG,oBAAmB,IAAG,wBAAuB,IAAG,OAAM,IAAG,WAAU,IAAG,aAAY,IAAG,aAAY,IAAG,kBAAiB;AAAA,IACrjC,cAAc,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC;AAAA,IAC7hB,eAAe,gCAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAyB,IAAiB,IAAiB;AAG3H,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAS;AAAA,QACjB,KAAK;AACJ,aAAG,aAAa,IAAI;AACrB;AAAA,QACA,KAAK;AACJ,aAAG,aAAa,IAAI;AACrB;AAAA,QACA,KAAK;AACJ,aAAG,aAAa,IAAI;AACrB;AAAA,QACA,KAAK;AACJ,aAAG,aAAa,IAAI;AACrB;AAAA,QACA,KAAK;AAAA,QAAG,KAAK;AAAA,QAAG,KAAK;AAAA,QAAI,KAAK;AAAA,QAAI,KAAK;AACvC,aAAG,UAAU,GAAG,KAAG,CAAC,CAAC;AACrB;AAAA,QACA,KAAK;AACL,aAAG,SAAS,GAAG,EAAE,EAAE,UAAU,CAAC,CAAC;AAAE,eAAK,IAAE,GAAG,EAAE,EAAE,UAAU,CAAC;AAC1D;AAAA,QACA,KAAK;AACL,aAAG,kBAAkB,GAAG,EAAE,EAAE,UAAU,EAAE,CAAC;AAAE,eAAK,IAAE,GAAG,EAAE,EAAE,UAAU,EAAE;AACrE;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,EAAE,EAAE,KAAK;AAAE,aAAG,SAAS,KAAK,CAAC;AACxC;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AACb,eAAK,IAAE,GAAG,EAAE,EAAE,KAAK;AAAE,aAAG,kBAAkB,KAAK,CAAC;AACjD;AAAA,QACA,KAAK;AACL,aAAG,EAAE,EAAE,OAAO,GAAG,GAAG,YAAY;AAAG,aAAG,0BAA0B,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACxF;AAAA,QACA,KAAK;AACL,aAAG,EAAE,EAAE,OAAO,GAAG,GAAG,QAAQ;AAAG,aAAG,0BAA0B,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACpF;AAAA,QACA,KAAK;AACL,aAAG,0BAA0B,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACrD;AAAA,QACA,KAAK;AACL,aAAG,EAAE,EAAE,OAAO,GAAG,GAAG,WAAW;AAAG,aAAG,qBAAqB,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AAClF;AAAA,QACA,KAAK;AACL,aAAG,kBAAkB,QAAQ,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACrD;AAAA,QACA,KAAK;AACL,aAAG,kBAAkB,SAAS,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACtD;AAAA,QACA,KAAK;AACL,aAAG,kBAAkB,SAAS,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACtD;AAAA,QACA,KAAK;AACJ,aAAG,sBAAsB;AAC1B;AAAA,QACA,KAAK;AACL,aAAG,kBAAkB,UAAU,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACvD;AAAA,QACA,KAAK;AACL,aAAG,kBAAkB,mBAAmB,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AAChE;AAAA,QACA,KAAK;AACL,aAAG,kBAAkB,UAAU,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACvD;AAAA,QACA,KAAK;AACL,aAAG,kBAAkB,aAAa,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AAC1D;AAAA,QACA,KAAK;AACL,aAAG,kBAAkB,gBAAgB,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AAC7D;AAAA,QACA,KAAK;AACL,aAAG,kBAAkB,mBAAmB,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AAChE;AAAA,QACA,KAAK;AACL,aAAG,kBAAkB,sBAAsB,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACnE;AAAA,QACA,KAAK;AACL,aAAG,kBAAkB,yBAAyB,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACtE;AAAA,QACA,KAAK;AACL,aAAG,aAAa,aAAa,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACrD;AAAA,QACA,KAAK;AACL,aAAG,aAAa,gBAAgB,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACxD;AAAA,QACA,KAAK;AACL,aAAG,aAAa,mBAAmB,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AAC3D;AAAA,QACA,KAAK;AACL,aAAG,aAAa,sBAAsB,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AAC9D;AAAA,QACA,KAAK;AACL,aAAG,aAAa,yBAAyB,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACjE;AAAA,QACA,KAAK;AACL,aAAG,aAAa,4BAA4B,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACpE;AAAA,QACA,KAAK;AACL,aAAG,aAAa,aAAa,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACrD;AAAA,QACA,KAAK;AACL,aAAG,aAAa,gBAAgB,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACxD;AAAA,QACA,KAAK;AACL,aAAG,aAAa,mBAAmB,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AAC3D;AAAA,QACA,KAAK;AACL,aAAG,aAAa,sBAAsB,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AAC9D;AAAA,QACA,KAAK;AACL,aAAG,aAAa,yBAAyB,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACjE;AAAA,QACA,KAAK;AACL,aAAG,aAAa,4BAA4B,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACpE;AAAA,QACA,KAAK;AACL,aAAG,OAAO,OAAO,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACzC;AAAA,QACA,KAAK;AACL,aAAG,OAAO,SAAS,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AAC3C;AAAA,QACA,KAAK;AACL,aAAG,OAAO,SAAS,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AAC3C;AAAA,QACA,KAAK;AACL,aAAG,OAAO,SAAS,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AAC3C;AAAA,QACA,KAAK;AACL,aAAG,OAAO,SAAS,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AAC3C;AAAA,QACA,KAAK;AACL,aAAG,OAAO,SAAS,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AAC3C;AAAA,QACA,KAAK;AACL,aAAG,OAAO,SAAS,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AAC3C;AAAA,QACA,KAAK;AACL,aAAG,EAAE,EAAE,OAAO,GAAG,CAAC;AAAG,aAAG,OAAO,OAAO,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AAC9D;AAAA,QACA,KAAK;AACL,aAAG,cAAc,mBAAmB,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AAC5D;AAAA,QACA,KAAK;AACL,aAAG,eAAe,oBAAoB,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AAC9D;AAAA,QACA,KAAK;AACL,aAAG,mBAAmB,wBAAwB,GAAG,GAAG,EAAE,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACtE;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,CAAC,GAAG,EAAE,CAAC;AACjB;AAAA,QACA,KAAK;AACJ,aAAG,EAAE,EAAE,QAAQ,GAAG,KAAG,CAAC,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACvC;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AACZ,eAAK,IAAI,GAAG,EAAE,EAAE,KAAK;AACvB;AAAA,QACA,KAAK;AACJ,cAAI,KAAG,CAAC;AAAG,aAAG,GAAG,KAAG,CAAC,EAAE,KAAK,CAAC,IAAE,GAAG,EAAE,EAAE,KAAK;AAAG,eAAK,IAAE;AACtD;AAAA,QACA,KAAK;AACH,eAAK,IAAI;AACX;AAAA,MACA;AAAA,IACA,GArKe;AAAA,IAsKf,OAAO,CAAC,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,IAAG,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,KAAI,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,GAAE,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAC1rL,gBAAgB,EAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,KAAI,CAAC,GAAE,CAAC,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,EAAC;AAAA,IAC5H,YAAY,gCAAS,WAAY,KAAK,MAAM;AACxC,UAAI,KAAK,aAAa;AAClB,aAAK,MAAM,GAAG;AAAA,MAClB,OAAO;AACH,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;AAAA,MACV;AAAA,IACJ,GARY;AAAA,IASZ,OAAO,gCAAS,MAAM,OAAO;AACzB,UAAI,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAIC,SAAQ,OAAO,OAAO,KAAK,KAAK;AACpC,UAAI,cAAc,EAAE,IAAI,CAAC,EAAE;AAC3B,eAAS,KAAK,KAAK,IAAI;AACnB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AAClD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;AAAA,QACjC;AAAA,MACJ;AACA,MAAAA,OAAM,SAAS,OAAO,YAAY,EAAE;AACpC,kBAAY,GAAG,QAAQA;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAOA,OAAM,UAAU,aAAa;AACpC,QAAAA,OAAM,SAAS,CAAC;AAAA,MACpB;AACA,UAAI,QAAQA,OAAM;AAClB,aAAO,KAAK,KAAK;AACjB,UAAI,SAASA,OAAM,WAAWA,OAAM,QAAQ;AAC5C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACjD,aAAK,aAAa,YAAY,GAAG;AAAA,MACrC,OAAO;AACH,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;AAAA,MAClD;AACA,eAAS,SAAS,GAAG;AACjB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;AAAA,MACpC;AAJS;AAKD,eAAS,MAAM;AACf,YAAI;AACJ,gBAAQ,OAAO,IAAI,KAAKA,OAAM,IAAI,KAAK;AACvC,YAAI,OAAO,UAAU,UAAU;AAC3B,cAAI,iBAAiB,OAAO;AACxB,qBAAS;AACT,oBAAQ,OAAO,IAAI;AAAA,UACvB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,QACpC;AACA,eAAO;AAAA,MACX;AAXa;AAYjB,UAAI,QAAQ,gBAAgB,OAAO,QAAQ,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACT,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC5B,mBAAS,KAAK,eAAe,KAAK;AAAA,QACtC,OAAO;AACH,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACjD,qBAAS,IAAI;AAAA,UACjB;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,QAChD;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AAC/D,cAAI,SAAS;AACb,qBAAW,CAAC;AACZ,eAAK,KAAK,MAAM,KAAK,GAAG;AACpB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AAClC,uBAAS,KAAK,MAAO,KAAK,WAAW,CAAC,IAAI,GAAI;AAAA,YAClD;AAAA,UACJ;AACA,cAAIA,OAAM,cAAc;AACpB,qBAAS,0BAA0B,WAAW,KAAK,QAAQA,OAAM,aAAa,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAc,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UAChL,OAAO;AACH,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAQ,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UACxJ;AACA,eAAK,WAAW,QAAQ;AAAA,YACpB,MAAMA,OAAM;AAAA,YACZ,OAAO,KAAK,WAAW,MAAM,KAAK;AAAA,YAClC,MAAMA,OAAM;AAAA,YACZ,KAAK;AAAA,YACL;AAAA,UACJ,CAAC;AAAA,QACL;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACjD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;AAAA,QACtG;AACA,gBAAQ,OAAO,CAAC,GAAG;AAAA,UACnB,KAAK;AACD,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAKA,OAAM,MAAM;AACxB,mBAAO,KAAKA,OAAM,MAAM;AACxB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACT,gBAAI,CAAC,gBAAgB;AACjB,uBAASA,OAAM;AACf,uBAASA,OAAM;AACf,yBAAWA,OAAM;AACjB,sBAAQA,OAAM;AACd,kBAAI,aAAa,GAAG;AAChB;AAAA,cACJ;AAAA,YACJ,OAAO;AACH,uBAAS;AACT,+BAAiB;AAAA,YACrB;AACA;AAAA,UACJ,KAAK;AACD,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;AAAA,cACP,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,YAC3C;AACA,gBAAI,QAAQ;AACR,oBAAM,GAAG,QAAQ;AAAA,gBACb,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;AAAA,gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,cACrC;AAAA,YACJ;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;AAAA,cAChC;AAAA,cACA;AAAA,cACA;AAAA,cACA,YAAY;AAAA,cACZ,OAAO,CAAC;AAAA,cACR;AAAA,cACA;AAAA,YACJ,EAAE,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC1B,qBAAO;AAAA,YACX;AACA,gBAAI,KAAK;AACL,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AAAA,YACrC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;AAAA,UACJ,KAAK;AACD,mBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX,GA3IO;AAAA,EA2IN;AAED,MAAI,QAAS,2BAAU;AACvB,QAAIA,SAAS;AAAA,MAEb,KAAI;AAAA,MAEJ,YAAW,gCAAS,WAAW,KAAK,MAAM;AAClC,YAAI,KAAK,GAAG,QAAQ;AAChB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;AAAA,QACvC,OAAO;AACH,gBAAM,IAAI,MAAM,GAAG;AAAA,QACvB;AAAA,MACJ,GANO;AAAA;AAAA,MASX,UAAS,gCAAU,OAAO,IAAI;AACtB,aAAK,KAAK,MAAM,KAAK,MAAM,CAAC;AAC5B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;AAAA,UACV,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,WAAW;AAAA,UACX,aAAa;AAAA,QACjB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,GAAE,CAAC;AAAA,QAC5B;AACA,aAAK,SAAS;AACd,eAAO;AAAA,MACX,GAlBK;AAAA;AAAA,MAqBT,OAAM,kCAAY;AACV,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACP,eAAK;AACL,eAAK,OAAO;AAAA,QAChB,OAAO;AACH,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,MAAM,CAAC;AAAA,QACvB;AAEA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;AAAA,MACX,GApBE;AAAA;AAAA,MAuBN,OAAM,gCAAU,IAAI;AACZ,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AAEpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAE5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAE7D,YAAI,MAAM,SAAS,GAAG;AAClB,eAAK,YAAY,MAAM,SAAS;AAAA,QACpC;AACA,YAAI,IAAI,KAAK,OAAO;AAEpB,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,SACR,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAC5D,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAChE,KAAK,OAAO,eAAe;AAAA,QACjC;AAEA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;AAAA,QACvD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;AAAA,MACX,GAhCE;AAAA;AAAA,MAmCN,MAAK,kCAAY;AACT,aAAK,QAAQ;AACb,eAAO;AAAA,MACX,GAHC;AAAA;AAAA,MAML,QAAO,kCAAY;AACX,YAAI,KAAK,QAAQ,iBAAiB;AAC9B,eAAK,aAAa;AAAA,QACtB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAa,GAAG;AAAA,YAC9N,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QAEL;AACA,eAAO;AAAA,MACX,GAZG;AAAA;AAAA,MAeP,MAAK,gCAAU,GAAG;AACV,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,MAClC,GAFC;AAAA;AAAA,MAKL,WAAU,kCAAY;AACd,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAM,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,MAC7E,GAHM;AAAA;AAAA,MAMV,eAAc,kCAAY;AAClB,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AAClB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAG,KAAK,MAAM;AAAA,QAChD;AACA,gBAAQ,KAAK,OAAO,GAAE,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;AAAA,MAClF,GANU;AAAA;AAAA,MASd,cAAa,kCAAY;AACjB,YAAI,MAAM,KAAK,UAAU;AACzB,YAAIC,KAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAc,IAAI,OAAOA,KAAI;AAAA,MACnD,GAJS;AAAA;AAAA,MAOb,YAAW,gCAAS,OAAO,cAAc;AACjC,YAAI,OACA,OACA;AAEJ,YAAI,KAAK,QAAQ,iBAAiB;AAE9B,mBAAS;AAAA,YACL,UAAU,KAAK;AAAA,YACf,QAAQ;AAAA,cACJ,YAAY,KAAK,OAAO;AAAA,cACxB,WAAW,KAAK;AAAA,cAChB,cAAc,KAAK,OAAO;AAAA,cAC1B,aAAa,KAAK,OAAO;AAAA,YAC7B;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,IAAI,KAAK;AAAA,YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;AAAA,YAC3C,MAAM,KAAK;AAAA,UACf;AACA,cAAI,KAAK,QAAQ,QAAQ;AACrB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;AAAA,UACnD;AAAA,QACJ;AAEA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACP,eAAK,YAAY,MAAM;AAAA,QAC3B;AACA,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,QACA,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAC5E,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;AAAA,QACpD;AACA,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;AAAA,QAChE;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC1B,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,OAAO;AACP,iBAAO;AAAA,QACX,WAAW,KAAK,YAAY;AAExB,mBAAS,KAAK,QAAQ;AAClB,iBAAK,CAAC,IAAI,OAAO,CAAC;AAAA,UACtB;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,GArEO;AAAA;AAAA,MAwEX,MAAK,kCAAY;AACT,YAAI,KAAK,MAAM;AACX,iBAAO,KAAK;AAAA,QAChB;AACA,YAAI,CAAC,KAAK,QAAQ;AACd,eAAK,OAAO;AAAA,QAChB;AAEA,YAAI,OACA,OACA,WACA;AACJ,YAAI,CAAC,KAAK,OAAO;AACb,eAAK,SAAS;AACd,eAAK,QAAQ;AAAA,QACjB;AACA,YAAI,QAAQ,KAAK,cAAc;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAChE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAC9B,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACjB,uBAAO;AAAA,cACX,WAAW,KAAK,YAAY;AACxB,wBAAQ;AACR;AAAA,cACJ,OAAO;AAEH,uBAAO;AAAA,cACX;AAAA,YACJ,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC3B;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,OAAO;AACP,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACjB,mBAAO;AAAA,UACX;AAEA,iBAAO;AAAA,QACX;AACA,YAAI,KAAK,WAAW,IAAI;AACpB,iBAAO,KAAK;AAAA,QAChB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAa,GAAG;AAAA,YACpH,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QACL;AAAA,MACJ,GAvDC;AAAA;AAAA,MA0DL,KAAI,gCAAS,MAAO;AACZ,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,GAAG;AACH,iBAAO;AAAA,QACX,OAAO;AACH,iBAAO,KAAK,IAAI;AAAA,QACpB;AAAA,MACJ,GAPA;AAAA;AAAA,MAUJ,OAAM,gCAAS,MAAO,WAAW;AACzB,aAAK,eAAe,KAAK,SAAS;AAAA,MACtC,GAFE;AAAA;AAAA,MAKN,UAAS,gCAAS,WAAY;AACtB,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACP,iBAAO,KAAK,eAAe,IAAI;AAAA,QACnC,OAAO;AACH,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,eAAc,gCAAS,gBAAiB;AAChC,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACnF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;AAAA,QAChF,OAAO;AACH,iBAAO,KAAK,WAAW,SAAS,EAAE;AAAA,QACtC;AAAA,MACJ,GANU;AAAA;AAAA,MASd,UAAS,gCAAS,SAAU,GAAG;AACvB,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACR,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC,OAAO;AACH,iBAAO;AAAA,QACX;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,WAAU,gCAAS,UAAW,WAAW;AACjC,aAAK,MAAM,SAAS;AAAA,MACxB,GAFM;AAAA;AAAA,MAKV,gBAAe,gCAAS,iBAAiB;AACjC,eAAO,KAAK,eAAe;AAAA,MAC/B,GAFW;AAAA,MAGf,SAAS,CAAC;AAAA,MACV,eAAe,gCAAS,UAAU,IAAG,KAAI,2BAA0B,UAAU;AAC7E,YAAI,UAAQ;AACZ,gBAAO,2BAA2B;AAAA,UAClC,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,WAAW;AAAE,mBAAO;AACvC;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAG,mBAAO;AAChC;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,WAAW;AAAE,mBAAO;AACvC;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAG,mBAAO;AAChC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,qBAAqB;AACzC;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AACvB;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAG;AACR;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,YAAY;AAAG,mBAAO;AAC1C;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,QAAQ;AAAG,mBAAO;AACtC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,kBAAkB;AAAG,mBAAO;AAChD;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,eAAe;AAAG,mBAAO;AAC7C;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,YAAY;AAAG,mBAAO;AAC1C;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,cAAc;AAAG,mBAAO;AAC5C;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,WAAW;AAAG,mBAAO;AACzC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,QAAQ;AAAG,mBAAO;AACtC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,UAAU;AAAG,mBAAO;AACxC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,qBAAqB;AAAG,mBAAO;AACnD;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,iBAAiB;AAAG,mBAAO;AAC/C;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,qBAAqB;AAAG,mBAAO;AACnD;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,kBAAkB;AAAG,mBAAO;AAChD;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,eAAe;AAAG,mBAAO;AAC7C;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,iBAAiB;AAAG,mBAAO;AAC/C;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,cAAc;AAAG,mBAAO;AAC5C;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,WAAW;AAAG,mBAAO;AACzC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,oBAAoB;AAAG,mBAAO;AAClD;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,qBAAqB;AAAG,mBAAO;AACnD;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,kBAAkB;AAAG,mBAAO;AAChD;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,eAAe;AAAG,mBAAO;AAC7C;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,iBAAiB;AAAG,mBAAO;AAC/C;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,cAAc;AAAG,mBAAO;AAC5C;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,WAAW;AAAG,mBAAO;AACzC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAG,mBAAO;AACpC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,MAAM;AAAG,mBAAO;AACpC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,QAAQ;AAAG,mBAAO;AACtC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,QAAQ;AAAG,mBAAO;AACtC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,KAAK;AAAG,mBAAO;AACnC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,OAAO;AAAG,mBAAO;AACrC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,OAAO;AAAG,mBAAO;AACrC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,OAAO;AAAG,mBAAO;AACrC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,OAAO;AAAG,mBAAO;AACrC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,OAAO;AAAG,mBAAO;AACrC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,OAAO;AAAG,mBAAO;AACrC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,OAAO;AAAG,mBAAO;AACrC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,OAAO;AAAG,mBAAO;AACrC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,OAAO;AAAG,mBAAO;AACrC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,OAAO;AAAG,mBAAO;AACrC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,WAAW;AAAG,mBAAO;AACzC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,iBAAiB;AAAG,mBAAO;AAC/C;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,kBAAkB;AAAG,mBAAO;AAChD;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,sBAAsB;AAAG,mBAAO;AACpD;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,WAAW;AAAG,mBAAO;AACzC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,WAAW;AAC/B;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAE,iBAAK,SAAS;AACvC;AAAA,UACA,KAAK;AAAI,mBAAO;AAChB;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAI,mBAAO;AAChB;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,QAAQ;AAC5B;AAAA,UACA,KAAK;AAAK,iBAAK,SAAS;AACxB;AAAA,UACA,KAAK;AAAI,mBAAO;AAChB;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,WAAW;AAC/B;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,eAAe;AAAG,mBAAO;AAC7C;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,iBAAK,MAAM,iBAAiB;AACtD;AAAA,UACA,KAAK;AAAI,mBAAO;AAChB;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,iBAAK,SAAS;AACxC;AAAA,UACA,KAAK;AAAI,mBAAO;AAChB;AAAA,UACA,KAAK;AAAgC,mBAAO;AAC5C;AAAA,UACA,KAAK;AAA2B,mBAAO;AACvC;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,QACA;AAAA,MACA,GAhLe;AAAA,MAiLf,OAAO,CAAC,+BAA8B,+BAA8B,+BAA8B,+BAA8B,wBAAuB,iCAAgC,wBAAuB,wBAAuB,wBAAuB,wBAAuB,yBAAwB,aAAY,eAAc,iCAAgC,yBAAwB,oBAAmB,YAAW,oBAAmB,sBAAqB,sBAAqB,oBAAmB,uBAAsB,qBAAoB,iBAAgB,0BAAyB,uBAAsB,qBAAoB,sBAAqB,mBAAkB,iBAAgB,mBAAkB,8BAA6B,0BAAyB,6BAA4B,0BAAyB,wBAAuB,yBAAwB,sBAAqB,oBAAmB,6BAA4B,6BAA4B,0BAAyB,wBAAuB,yBAAwB,sBAAqB,oBAAmB,0BAAyB,eAAc,iBAAgB,iBAAgB,cAAa,gBAAe,iBAAgB,gBAAe,mBAAkB,gBAAe,mBAAkB,gBAAe,oBAAmB,gBAAe,mBAAkB,mBAAkB,6BAA4B,yBAAwB,6BAA4B,UAAS,mBAAkB,YAAW,YAAW,WAAU,UAAS,mBAAkB,gBAAe,YAAW,cAAa,iBAAgB,cAAa,mBAAkB,cAAa,YAAW,cAAa,WAAU,WAAU,cAAa,gBAAe,QAAQ;AAAA,MAC9nD,YAAY,EAAC,uBAAsB,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,CAAC,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,CAAC,GAAE,aAAY,MAAK,GAAE,mBAAkB,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,iBAAgB,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,UAAS,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,wBAAuB,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,oBAAmB,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,mBAAkB,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,UAAS,EAAC,SAAQ,CAAC,GAAE,aAAY,MAAK,GAAE,OAAM,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,UAAS,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,UAAS,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,QAAO,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,uBAAsB,EAAC,SAAQ,CAAC,GAAE,aAAY,MAAK,GAAE,oBAAmB,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,iBAAgB,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,mBAAkB,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,gBAAe,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,sBAAqB,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,uBAAsB,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,oBAAmB,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,iBAAgB,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,mBAAkB,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,gBAAe,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,mBAAkB,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,uBAAsB,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,YAAW,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,oBAAmB,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,iBAAgB,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,cAAa,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,gBAAe,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,UAAS,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,cAAa,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,UAAS,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,WAAU,EAAC,SAAQ,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,KAAI,EAAC;AAAA,IACxxF;AACA,WAAOD;AAAA,EACP,EAAG;AACH,EAAAD,QAAO,QAAQ;AACf,WAAS,SAAU;AACjB,SAAK,KAAK,CAAC;AAAA,EACb;AAFS;AAGT,SAAO,YAAYA;AAAO,EAAAA,QAAO,SAAS;AAC1C,SAAO,IAAI;AACX,EAAG;AACF,OAAO,SAAS;AAEhB,IAAO,oBAAQ;;;ACx4BhB,IAAI,eAAe,CAAC;AACpB,IAAI,qBAAqB,CAAC,EAAE;AAC5B,IAAI,uBAAuB;AAC3B,IAAI,sBAAsB;AAC1B,IAAI,aAAa;AAAA,EACf;AAAA,IACE,OAAO;AAAA,IACP,OAAO,EAAE,MAAM,SAAS;AAAA,IACxB,MAAM,EAAE,MAAM,SAAS;AAAA,IACvB,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,EAClB;AACF;AACA,IAAI,OAAO,CAAC;AACZ,IAAI,QAAQ;AACZ,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,kBAAkB;AACtB,IAAI;AAEG,IAAM,YAAY,kCAAY;AACnC,SAAO;AACT,GAFyB;AAIlB,IAAM,YAAY,gCAAU,aAAa;AAC9C,MAAI,gBAAgB,aAAa,aAAa,UAAU,CAAC;AACzD,WAAS;AACX,GAHyB;AAMlB,IAAM,SAAS,gCAAU,MAAM,MAAM,IAAI,OAAO,OAAO,OAAO,QAAQ,MAAM,MAAM;AAEvF,MACE,SAAS,UACT,SAAS,QACT,SAAS,UACT,SAAS,QACT,OAAO,UACP,OAAO,QACP,UAAU,UACV,UAAU,MACV;AACA;AAAA,EACF;AAEA,MAAI,MAAM,CAAC;AACX,QAAM,MAAM,KAAK,KAAK,CAACG,SAAQA,KAAI,SAAS,QAAQA,KAAI,OAAO,EAAE;AACjE,MAAI,KAAK;AACP,UAAM;AAAA,EACR,OAAO;AACL,SAAK,KAAK,GAAG;AAAA,EACf;AAEA,MAAI,OAAO;AACX,MAAI,OAAO;AACX,MAAI,KAAK;AACT,MAAI,QAAQ,EAAE,MAAM,MAAM;AAE1B,MAAI,UAAU,UAAa,UAAU,MAAM;AACzC,QAAI,QAAQ,EAAE,MAAM,GAAG;AAAA,EACzB,OAAO;AACL,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAC1C,UAAI,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IAC3B,OAAO;AACL,UAAI,QAAQ,EAAE,MAAM,MAAM;AAAA,IAC5B;AAAA,EACF;AAEA,MAAI,UAAU,UAAa,UAAU,MAAM;AACzC,QAAI,QAAQ,EAAE,MAAM,GAAG;AAAA,EACzB,OAAO;AACL,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAC1C,UAAI,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IAC3B,OAAO;AACL,UAAI,QAAQ,EAAE,MAAM,MAAM;AAAA,IAC5B;AAAA,EACF;AAEA,MAAI,OAAO,WAAW,UAAU;AAC9B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,MAAM,EAAE,CAAC;AAC3C,QAAI,GAAG,IAAI;AAAA,EACb,OAAO;AACL,QAAI,SAAS;AAAA,EACf;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,QAAI,GAAG,IAAI;AAAA,EACb,OAAO;AACL,QAAI,OAAO;AAAA,EACb;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,QAAI,GAAG,IAAI;AAAA,EACb,OAAO;AACL,QAAI,OAAO;AAAA,EACb;AACA,MAAI,OAAO,SAAS;AACtB,GArEsB;AAwEf,IAAM,oBAAoB,gCAAU,aAAa,OAAO,OAAO,OAAO,QAAQ,MAAM,MAAM;AAE/F,MAAI,UAAU,QAAQ,UAAU,MAAM;AACpC;AAAA,EACF;AAEA,MAAI,iBAAiB,CAAC;AACtB,QAAM,MAAM,aAAa,KAAK,CAACC,oBAAmBA,gBAAe,UAAU,KAAK;AAChF,MAAI,OAAO,UAAU,IAAI,OAAO;AAC9B,qBAAiB;AAAA,EACnB,OAAO;AACL,mBAAe,QAAQ;AACvB,iBAAa,KAAK,cAAc;AAAA,EAClC;AAGA,MAAI,UAAU,UAAa,UAAU,MAAM;AACzC,mBAAe,QAAQ,EAAE,MAAM,GAAG;AAAA,EACpC,OAAO;AACL,mBAAe,QAAQ,EAAE,MAAM,MAAM;AAAA,EACvC;AAEA,MAAI,UAAU,UAAa,UAAU,MAAM;AACzC,mBAAe,QAAQ,EAAE,MAAM,GAAG;AAAA,EACpC,OAAO;AACL,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAC1C,qBAAe,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IACtC,OAAO;AACL,qBAAe,QAAQ,EAAE,MAAM,MAAM;AAAA,IACvC;AAAA,EACF;AAEA,MAAI,OAAO,WAAW,UAAU;AAC9B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,MAAM,EAAE,CAAC;AAC3C,mBAAe,GAAG,IAAI;AAAA,EACxB,OAAO;AACL,mBAAe,SAAS;AAAA,EAC1B;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,mBAAe,GAAG,IAAI;AAAA,EACxB,OAAO;AACL,mBAAe,OAAO;AAAA,EACxB;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,mBAAe,GAAG,IAAI;AAAA,EACxB,OAAO;AACL,mBAAe,OAAO;AAAA,EACxB;AACA,iBAAe,cAAc,EAAE,MAAM,YAAY;AACjD,iBAAe,iBAAiB;AAChC,iBAAe,OAAO,SAAS;AACjC,GAtDiC;AAyD1B,IAAM,eAAe,gCAAU,aAAa,OAAO,OAAO,OAAO,OAAO,QAAQ,MAAM,MAAM;AAEjG,MAAI,UAAU,QAAQ,UAAU,MAAM;AACpC;AAAA,EACF;AAEA,MAAI,YAAY,CAAC;AACjB,QAAM,MAAM,aAAa,KAAK,CAACC,eAAcA,WAAU,UAAU,KAAK;AACtE,MAAI,OAAO,UAAU,IAAI,OAAO;AAC9B,gBAAY;AAAA,EACd,OAAO;AACL,cAAU,QAAQ;AAClB,iBAAa,KAAK,SAAS;AAAA,EAC7B;AAGA,MAAI,UAAU,UAAa,UAAU,MAAM;AACzC,cAAU,QAAQ,EAAE,MAAM,GAAG;AAAA,EAC/B,OAAO;AACL,cAAU,QAAQ,EAAE,MAAM,MAAM;AAAA,EAClC;AAEA,MAAI,UAAU,UAAa,UAAU,MAAM;AACzC,cAAU,QAAQ,EAAE,MAAM,GAAG;AAAA,EAC/B,OAAO;AACL,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAC1C,gBAAU,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IACjC,OAAO;AACL,gBAAU,QAAQ,EAAE,MAAM,MAAM;AAAA,IAClC;AAAA,EACF;AAEA,MAAI,UAAU,UAAa,UAAU,MAAM;AACzC,cAAU,QAAQ,EAAE,MAAM,GAAG;AAAA,EAC/B,OAAO;AACL,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAC1C,gBAAU,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IACjC,OAAO;AACL,gBAAU,QAAQ,EAAE,MAAM,MAAM;AAAA,IAClC;AAAA,EACF;AAEA,MAAI,OAAO,WAAW,UAAU;AAC9B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,MAAM,EAAE,CAAC;AAC3C,cAAU,GAAG,IAAI;AAAA,EACnB,OAAO;AACL,cAAU,SAAS;AAAA,EACrB;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,cAAU,GAAG,IAAI;AAAA,EACnB,OAAO;AACL,cAAU,OAAO;AAAA,EACnB;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,cAAU,GAAG,IAAI;AAAA,EACnB,OAAO;AACL,cAAU,OAAO;AAAA,EACnB;AACA,YAAU,OAAO,SAAS;AAC1B,YAAU,cAAc,EAAE,MAAM,YAAY;AAC5C,YAAU,iBAAiB;AAC7B,GAjE4B;AAoErB,IAAM,eAAe,gCAAU,aAAa,OAAO,OAAO,OAAO,OAAO,QAAQ,MAAM,MAAM;AAEjG,MAAI,UAAU,QAAQ,UAAU,MAAM;AACpC;AAAA,EACF;AAEA,MAAI,YAAY,CAAC;AACjB,QAAM,MAAM,aAAa,KAAK,CAACC,eAAcA,WAAU,UAAU,KAAK;AACtE,MAAI,OAAO,UAAU,IAAI,OAAO;AAC9B,gBAAY;AAAA,EACd,OAAO;AACL,cAAU,QAAQ;AAClB,iBAAa,KAAK,SAAS;AAAA,EAC7B;AAGA,MAAI,UAAU,UAAa,UAAU,MAAM;AACzC,cAAU,QAAQ,EAAE,MAAM,GAAG;AAAA,EAC/B,OAAO;AACL,cAAU,QAAQ,EAAE,MAAM,MAAM;AAAA,EAClC;AAEA,MAAI,UAAU,UAAa,UAAU,MAAM;AACzC,cAAU,QAAQ,EAAE,MAAM,GAAG;AAAA,EAC/B,OAAO;AACL,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAC1C,gBAAU,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IACjC,OAAO;AACL,gBAAU,QAAQ,EAAE,MAAM,MAAM;AAAA,IAClC;AAAA,EACF;AAEA,MAAI,UAAU,UAAa,UAAU,MAAM;AACzC,cAAU,QAAQ,EAAE,MAAM,GAAG;AAAA,EAC/B,OAAO;AACL,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAC1C,gBAAU,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IACjC,OAAO;AACL,gBAAU,QAAQ,EAAE,MAAM,MAAM;AAAA,IAClC;AAAA,EACF;AAEA,MAAI,OAAO,WAAW,UAAU;AAC9B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,MAAM,EAAE,CAAC;AAC3C,cAAU,GAAG,IAAI;AAAA,EACnB,OAAO;AACL,cAAU,SAAS;AAAA,EACrB;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,cAAU,GAAG,IAAI;AAAA,EACnB,OAAO;AACL,cAAU,OAAO;AAAA,EACnB;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,cAAU,GAAG,IAAI;AAAA,EACnB,OAAO;AACL,cAAU,OAAO;AAAA,EACnB;AACA,YAAU,OAAO,SAAS;AAC1B,YAAU,cAAc,EAAE,MAAM,YAAY;AAC5C,YAAU,iBAAiB;AAC7B,GAjE4B;AAoErB,IAAM,4BAA4B,gCAAU,OAAO,OAAO,MAAM,MAAM,MAAM;AAIjF,MAAI,UAAU,QAAQ,UAAU,MAAM;AACpC;AAAA,EACF;AAEA,MAAI,WAAW,CAAC;AAChB,QAAM,MAAM,WAAW,KAAK,CAACC,cAAaA,UAAS,UAAU,KAAK;AAClE,MAAI,OAAO,UAAU,IAAI,OAAO;AAC9B,eAAW;AAAA,EACb,OAAO;AACL,aAAS,QAAQ;AACjB,eAAW,KAAK,QAAQ;AAAA,EAC1B;AAGA,MAAI,UAAU,UAAa,UAAU,MAAM;AACzC,aAAS,QAAQ,EAAE,MAAM,GAAG;AAAA,EAC9B,OAAO;AACL,aAAS,QAAQ,EAAE,MAAM,MAAM;AAAA,EACjC;AAEA,MAAI,SAAS,UAAa,SAAS,MAAM;AACvC,aAAS,OAAO,EAAE,MAAM,SAAS;AAAA,EACnC,OAAO;AACL,QAAI,OAAO,SAAS,UAAU;AAC5B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,eAAS,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IAChC,OAAO;AACL,eAAS,OAAO,EAAE,MAAM,KAAK;AAAA,IAC/B;AAAA,EACF;AAEA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,aAAS,GAAG,IAAI;AAAA,EAClB,OAAO;AACL,aAAS,OAAO;AAAA,EAClB;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,aAAS,GAAG,IAAI;AAAA,EAClB,OAAO;AACL,aAAS,OAAO;AAAA,EAClB;AACA,WAAS,iBAAiB;AAC1B,WAAS,OAAO,SAAS;AAEzB,wBAAsB;AACtB,yBAAuB;AACvB,qBAAmB,KAAK,mBAAmB;AAC7C,GArDyC;AAwDlC,IAAM,uBAAuB,gCAAU,OAAO,OAAO,MAAM,MAAM,MAAM;AAI5E,MAAI,UAAU,QAAQ,UAAU,MAAM;AACpC;AAAA,EACF;AAEA,MAAI,WAAW,CAAC;AAChB,QAAM,MAAM,WAAW,KAAK,CAACA,cAAaA,UAAS,UAAU,KAAK;AAClE,MAAI,OAAO,UAAU,IAAI,OAAO;AAC9B,eAAW;AAAA,EACb,OAAO;AACL,aAAS,QAAQ;AACjB,eAAW,KAAK,QAAQ;AAAA,EAC1B;AAGA,MAAI,UAAU,UAAa,UAAU,MAAM;AACzC,aAAS,QAAQ,EAAE,MAAM,GAAG;AAAA,EAC9B,OAAO;AACL,aAAS,QAAQ,EAAE,MAAM,MAAM;AAAA,EACjC;AAEA,MAAI,SAAS,UAAa,SAAS,MAAM;AACvC,aAAS,OAAO,EAAE,MAAM,YAAY;AAAA,EACtC,OAAO;AACL,QAAI,OAAO,SAAS,UAAU;AAC5B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,eAAS,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IAChC,OAAO;AACL,eAAS,OAAO,EAAE,MAAM,KAAK;AAAA,IAC/B;AAAA,EACF;AAEA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,aAAS,GAAG,IAAI;AAAA,EAClB,OAAO;AACL,aAAS,OAAO;AAAA,EAClB;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,aAAS,GAAG,IAAI;AAAA,EAClB,OAAO;AACL,aAAS,OAAO;AAAA,EAClB;AACA,WAAS,iBAAiB;AAC1B,WAAS,OAAO,SAAS;AAEzB,wBAAsB;AACtB,yBAAuB;AACvB,qBAAmB,KAAK,mBAAmB;AAC7C,GArDoC;AAwD7B,IAAM,oBAAoB,gCAC/B,UACA,OACA,OACA,MACA,OACA,QACA,MACA,MACA;AAIA,MAAI,UAAU,QAAQ,UAAU,MAAM;AACpC;AAAA,EACF;AAEA,MAAI,WAAW,CAAC;AAChB,QAAM,MAAM,WAAW,KAAK,CAACA,cAAaA,UAAS,UAAU,KAAK;AAClE,MAAI,OAAO,UAAU,IAAI,OAAO;AAC9B,eAAW;AAAA,EACb,OAAO;AACL,aAAS,QAAQ;AACjB,eAAW,KAAK,QAAQ;AAAA,EAC1B;AAGA,MAAI,UAAU,UAAa,UAAU,MAAM;AACzC,aAAS,QAAQ,EAAE,MAAM,GAAG;AAAA,EAC9B,OAAO;AACL,aAAS,QAAQ,EAAE,MAAM,MAAM;AAAA,EACjC;AAEA,MAAI,SAAS,UAAa,SAAS,MAAM;AACvC,aAAS,OAAO,EAAE,MAAM,OAAO;AAAA,EACjC,OAAO;AACL,QAAI,OAAO,SAAS,UAAU;AAC5B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,eAAS,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IAChC,OAAO;AACL,eAAS,OAAO,EAAE,MAAM,KAAK;AAAA,IAC/B;AAAA,EACF;AAEA,MAAI,UAAU,UAAa,UAAU,MAAM;AACzC,aAAS,QAAQ,EAAE,MAAM,GAAG;AAAA,EAC9B,OAAO;AACL,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAC1C,eAAS,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IAChC,OAAO;AACL,eAAS,QAAQ,EAAE,MAAM,MAAM;AAAA,IACjC;AAAA,EACF;AAEA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,aAAS,GAAG,IAAI;AAAA,EAClB,OAAO;AACL,aAAS,OAAO;AAAA,EAClB;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,aAAS,GAAG,IAAI;AAAA,EAClB,OAAO;AACL,aAAS,OAAO;AAAA,EAClB;AACA,WAAS,WAAW;AACpB,WAAS,iBAAiB;AAC1B,WAAS,OAAO,SAAS;AAEzB,wBAAsB;AACtB,yBAAuB;AACvB,qBAAmB,KAAK,mBAAmB;AAC7C,GA1EiC;AA4E1B,IAAM,wBAAwB,kCAAY;AAC/C,yBAAuB;AACvB,qBAAmB,IAAI;AACvB,wBAAsB,mBAAmB,IAAI;AAC7C,qBAAmB,KAAK,mBAAmB;AAC7C,GALqC;AAQ9B,IAAM,gBAAgB,gCAC3B,aACA,aACA,SACA,WACA,aACA,WACA,OACA,QACA,OACA,YACA,cACA;AACA,MAAI,MAAM,aAAa,KAAK,CAAC,YAAY,QAAQ,UAAU,WAAW;AACtE,MAAI,QAAQ,QAAW;AACrB,UAAM,WAAW,KAAK,CAAC,YAAY,QAAQ,UAAU,WAAW;AAChE,QAAI,QAAQ,QAAW;AACrB;AAAA,IACF;AAAA,EACF;AACA,MAAI,YAAY,UAAa,YAAY,MAAM;AAC7C,QAAI,OAAO,YAAY,UAAU;AAC/B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,OAAO,EAAE,CAAC;AAC5C,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,UAAU;AAAA,IAChB;AAAA,EACF;AACA,MAAI,cAAc,UAAa,cAAc,MAAM;AACjD,QAAI,OAAO,cAAc,UAAU;AACjC,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,SAAS,EAAE,CAAC;AAC9C,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,YAAY;AAAA,IAClB;AAAA,EACF;AACA,MAAI,gBAAgB,UAAa,gBAAgB,MAAM;AACrD,QAAI,OAAO,gBAAgB,UAAU;AACnC,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,WAAW,EAAE,CAAC;AAChD,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,cAAc;AAAA,IACpB;AAAA,EACF;AACA,MAAI,cAAc,UAAa,cAAc,MAAM;AACjD,QAAI,OAAO,cAAc,UAAU;AACjC,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,SAAS,EAAE,CAAC;AAC9C,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,YAAY;AAAA,IAClB;AAAA,EACF;AACA,MAAI,UAAU,UAAa,UAAU,MAAM;AACzC,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAC1C,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,QAAQ;AAAA,IACd;AAAA,EACF;AACA,MAAI,WAAW,UAAa,WAAW,MAAM;AAC3C,QAAI,OAAO,WAAW,UAAU;AAC9B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,MAAM,EAAE,CAAC;AAC3C,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,SAAS;AAAA,IACf;AAAA,EACF;AACA,MAAI,UAAU,UAAa,UAAU,MAAM;AACzC,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAC1C,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,QAAQ;AAAA,IACd;AAAA,EACF;AACA,MAAI,eAAe,UAAa,eAAe,MAAM;AACnD,QAAI,OAAO,eAAe,UAAU;AAClC,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,UAAU,EAAE,CAAC;AAC/C,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,aAAa;AAAA,IACnB;AAAA,EACF;AACA,MAAI,iBAAiB,UAAa,iBAAiB,MAAM;AACvD,QAAI,OAAO,iBAAiB,UAAU;AACpC,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,YAAY,EAAE,CAAC;AACjD,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,eAAe;AAAA,IACrB;AAAA,EACF;AACF,GA5F6B;AA+FtB,IAAM,iBAAiB,gCAC5B,aACA,MACA,IACA,WACA,WACA,SACA,SACA;AACA,QAAM,MAAM,KAAK,KAAK,CAAC,QAAQ,IAAI,SAAS,QAAQ,IAAI,OAAO,EAAE;AACjE,MAAI,QAAQ,QAAW;AACrB;AAAA,EACF;AACA,MAAI,cAAc,UAAa,cAAc,MAAM;AACjD,QAAI,OAAO,cAAc,UAAU;AACjC,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,SAAS,EAAE,CAAC;AAC9C,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,YAAY;AAAA,IAClB;AAAA,EACF;AACA,MAAI,cAAc,UAAa,cAAc,MAAM;AACjD,QAAI,OAAO,cAAc,UAAU;AACjC,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,SAAS,EAAE,CAAC;AAC9C,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,YAAY;AAAA,IAClB;AAAA,EACF;AACA,MAAI,YAAY,UAAa,YAAY,MAAM;AAC7C,QAAI,OAAO,YAAY,UAAU;AAC/B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,OAAO,EAAE,CAAC;AAC5C,UAAI,GAAG,IAAI,SAAS,KAAK;AAAA,IAC3B,OAAO;AACL,UAAI,UAAU,SAAS,OAAO;AAAA,IAChC;AAAA,EACF;AACA,MAAI,YAAY,UAAa,YAAY,MAAM;AAC7C,QAAI,OAAO,YAAY,UAAU;AAC/B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,OAAO,EAAE,CAAC;AAC5C,UAAI,GAAG,IAAI,SAAS,KAAK;AAAA,IAC3B,OAAO;AACL,UAAI,UAAU,SAAS,OAAO;AAAA,IAChC;AAAA,EACF;AACF,GA7C8B;AAgDvB,IAAM,qBAAqB,gCAAU,aAAa,mBAAmB,sBAAsB;AAChG,MAAI,oBAAoB;AACxB,MAAI,uBAAuB;AAE3B,MAAI,OAAO,sBAAsB,UAAU;AACzC,UAAM,QAAQ,OAAO,OAAO,iBAAiB,EAAE,CAAC;AAChD,wBAAoB,SAAS,KAAK;AAAA,EACpC,OAAO;AACL,wBAAoB,SAAS,iBAAiB;AAAA,EAChD;AACA,MAAI,OAAO,yBAAyB,UAAU;AAC5C,UAAM,QAAQ,OAAO,OAAO,oBAAoB,EAAE,CAAC;AACnD,2BAAuB,SAAS,KAAK;AAAA,EACvC,OAAO;AACL,2BAAuB,SAAS,oBAAoB;AAAA,EACtD;AAEA,MAAI,qBAAqB,GAAG;AAC1B,mBAAe;AAAA,EACjB;AACA,MAAI,wBAAwB,GAAG;AAC7B,sBAAkB;AAAA,EACpB;AACF,GAvBkC;AAyB3B,IAAM,kBAAkB,kCAAY;AACzC,SAAO;AACT,GAF+B;AAGxB,IAAM,qBAAqB,kCAAY;AAC5C,SAAO;AACT,GAFkC;AAG3B,IAAM,0BAA0B,kCAAY;AACjD,SAAO;AACT,GAFuC;AAIhC,IAAM,yBAAyB,kCAAY;AAChD,SAAO;AACT,GAFsC;AAI/B,IAAM,kBAAkB,gCAAU,gBAAgB;AACvD,MAAI,mBAAmB,UAAa,mBAAmB,MAAM;AAC3D,WAAO;AAAA,EACT,OAAO;AACL,WAAO,aAAa,OAAO,CAAC,mBAAmB;AAC7C,aAAO,eAAe,mBAAmB;AAAA,IAC3C,CAAC;AAAA,EACH;AACF,GAR+B;AASxB,IAAM,aAAa,gCAAU,OAAO;AACzC,SAAO,aAAa,KAAK,CAAC,mBAAmB,eAAe,UAAU,KAAK;AAC7E,GAF0B;AAGnB,IAAM,iBAAiB,gCAAU,gBAAgB;AACtD,SAAO,OAAO,KAAK,gBAAgB,cAAc,CAAC;AACpD,GAF8B;AAIvB,IAAM,gBAAgB,gCAAU,gBAAgB;AACrD,MAAI,mBAAmB,UAAa,mBAAmB,MAAM;AAC3D,WAAO;AAAA,EACT,OAAO;AACL,WAAO,WAAW,OAAO,CAAC,aAAa,SAAS,mBAAmB,cAAc;AAAA,EACnF;AACF,GAN6B;AAWtB,IAAM,eAAe;AAErB,IAAM,UAAU,kCAAY;AACjC,SAAO;AACT,GAFuB;AAIhB,IAAM,WAAW,kCAAY;AAClC,SAAO;AACT,GAFwB;AAIjB,IAAM,UAAU,gCAAU,aAAa;AAC5C,gBAAc;AAChB,GAFuB;AAIhB,IAAM,WAAW,kCAAY;AAClC,SAAO;AACT,GAFwB;AAIjB,IAAM,QAAQ,kCAAY;AAC/B,iBAAe,CAAC;AAChB,eAAa;AAAA,IACX;AAAA,MACE,OAAO;AAAA,MACP,OAAO,EAAE,MAAM,SAAS;AAAA,MACxB,MAAM,EAAE,MAAM,SAAS;AAAA,MACvB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,gBAAgB;AAAA,IAClB;AAAA,EACF;AACA,wBAAsB;AACtB,yBAAuB;AACvB,uBAAqB,CAAC,EAAE;AACxB,SAAO,CAAC;AAER,uBAAqB,CAAC,EAAE;AACxB,UAAQ;AACR,gBAAc;AACd,iBAAe;AACf,oBAAkB;AACpB,GAtBqB;AAwBd,IAAM,WAAW;AAAA,EACtB,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,aAAa;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,cAAc;AAChB;AAEO,IAAM,YAAY;AAAA,EACvB,QAAQ;AAAA,EACR,MAAM;AACR;AAEO,IAAM,YAAY;AAAA,EACvB,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,MAAM;AACR;AAEO,IAAM,WAAW,gCAAU,KAAK;AACrC,MAAI,gBAAgB,aAAa,KAAK,UAAU,CAAC;AACjD,UAAQ;AACV,GAHwB;AAKxB,IAAO,eAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW,6BAAM,UAAU,EAAE,IAAlB;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAEF;;;AC9zBA,0BAA4B;AAErB,IAAMC,YAAW,gCAAU,MAAM,UAAU;AAChD,SAAqB,SAAS,MAAM,QAAQ;AAC9C,GAFwB;AAIjB,IAAM,YAAY,gCAAU,MAAM,OAAO,QAAQ,GAAG,GAAG,MAAM;AAClE,QAAM,YAAY,KAAK,OAAO,OAAO;AACrC,YAAU,KAAK,SAAS,KAAK;AAC7B,YAAU,KAAK,UAAU,MAAM;AAC/B,YAAU,KAAK,KAAK,CAAC;AACrB,YAAU,KAAK,KAAK,CAAC;AACrB,MAAI,gBAAgB,KAAK,WAAW,uBAAuB,IAAI,WAAO,iCAAY,IAAI;AACtF,YAAU,KAAK,cAAc,aAAa;AAC5C,GARyB;AAUlB,IAAM,WAAW,wBAAC,MAAMC,OAAMC,UAAS;AAC5C,QAAM,WAAW,KAAK,OAAO,GAAG;AAChC,MAAI,IAAI;AACR,WAAS,OAAOD,OAAM;AACpB,QAAI,YAAY,IAAI,YAAY,IAAI,YAAY;AAChD,QAAI,cAAc,IAAI,YAAY,IAAI,YAAY;AAClD,QAAI,UAAU,IAAI,UAAU,SAAS,IAAI,OAAO,IAAI;AACpD,QAAI,UAAU,IAAI,UAAU,SAAS,IAAI,OAAO,IAAI;AAEpD,QAAI,MAAM;AACV,QAAI,MAAM,GAAG;AACX,UAAI,OAAO,SAAS,OAAO,MAAM;AACjC,WAAK,KAAK,MAAM,IAAI,WAAW,CAAC;AAChC,WAAK,KAAK,MAAM,IAAI,WAAW,CAAC;AAChC,WAAK,KAAK,MAAM,IAAI,SAAS,CAAC;AAC9B,WAAK,KAAK,MAAM,IAAI,SAAS,CAAC;AAE9B,WAAK,KAAK,gBAAgB,GAAG;AAC7B,WAAK,KAAK,UAAU,WAAW;AAC/B,WAAK,MAAM,QAAQ,MAAM;AACzB,UAAI,IAAI,SAAS,SAAS;AACxB,aAAK,KAAK,cAAc,SAAS,MAAM,aAAa;AAAA,MACtD;AACA,UAAI,IAAI,SAAS,WAAW,IAAI,SAAS,SAAS;AAChD,aAAK,KAAK,gBAAgB,SAAS,MAAM,YAAY;AAAA,MACvD;AACA,UAAI;AAAA,IACN,OAAO;AACL,UAAI,OAAO,SAAS,OAAO,MAAM;AACjC,WACG,KAAK,QAAQ,MAAM,EACnB,KAAK,gBAAgB,GAAG,EACxB,KAAK,UAAU,WAAW,EAC1B;AAAA,QACC;AAAA,QACA,iDACG,WAAW,UAAU,IAAI,WAAW,CAAC,EACrC,WAAW,UAAU,IAAI,WAAW,CAAC,EACrC;AAAA,UACC;AAAA,UACA,IAAI,WAAW,KACZ,IAAI,SAAS,IAAI,IAAI,WAAW,KAAK,KACrC,IAAI,SAAS,IAAI,IAAI,WAAW,KAAK;AAAA,QAC1C,EACC,WAAW,YAAY,IAAI,WAAW,KAAK,IAAI,SAAS,IAAI,IAAI,WAAW,KAAK,CAAC,EACjF,WAAW,SAAS,IAAI,SAAS,CAAC,EAClC,WAAW,SAAS,IAAI,SAAS,CAAC;AAAA,MACvC;AACF,UAAI,IAAI,SAAS,SAAS;AACxB,aAAK,KAAK,cAAc,SAAS,MAAM,aAAa;AAAA,MACtD;AACA,UAAI,IAAI,SAAS,WAAW,IAAI,SAAS,SAAS;AAChD,aAAK,KAAK,gBAAgB,SAAS,MAAM,YAAY;AAAA,MACvD;AAAA,IACF;AAEA,QAAI,cAAcC,MAAK,YAAY;AACnC,2BAAuBA,KAAI;AAAA,MACzB,IAAI,MAAM;AAAA,MACV;AAAA,MACA,KAAK,IAAI,IAAI,WAAW,GAAG,IAAI,SAAS,CAAC,IACvC,KAAK,IAAI,IAAI,SAAS,IAAI,IAAI,WAAW,CAAC,IAAI,IAC9C;AAAA,MACF,KAAK,IAAI,IAAI,WAAW,GAAG,IAAI,SAAS,CAAC,IACvC,KAAK,IAAI,IAAI,SAAS,IAAI,IAAI,WAAW,CAAC,IAAI,IAC9C;AAAA,MACF,IAAI,MAAM;AAAA,MACV,IAAI,MAAM;AAAA,MACV,EAAE,MAAM,UAAU;AAAA,MAClB;AAAA,IACF;AAEA,QAAI,IAAI,SAAS,IAAI,MAAM,SAAS,IAAI;AACtC,oBAAcA,MAAK,YAAY;AAC/B,6BAAuBA,KAAI;AAAA,QACzB,MAAM,IAAI,MAAM,OAAO;AAAA,QACvB;AAAA,QACA,KAAK,IAAI,IAAI,WAAW,GAAG,IAAI,SAAS,CAAC,IACvC,KAAK,IAAI,IAAI,SAAS,IAAI,IAAI,WAAW,CAAC,IAAI,IAC9C;AAAA,QACF,KAAK,IAAI,IAAI,WAAW,GAAG,IAAI,SAAS,CAAC,IACvC,KAAK,IAAI,IAAI,SAAS,IAAI,IAAI,WAAW,CAAC,IAAI,IAC9CA,MAAK,kBACL,IACA;AAAA,QACF,KAAK,IAAI,IAAI,MAAM,OAAO,IAAI,MAAM,KAAK;AAAA,QACzC,IAAI,MAAM;AAAA,QACV,EAAE,MAAM,WAAW,cAAc,SAAS;AAAA,QAC1C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF,GA5FwB;AAqGxB,IAAM,eAAe,gCAAU,MAAM,UAAUA,OAAM;AACnD,QAAM,eAAe,KAAK,OAAO,GAAG;AAEpC,MAAI,YAAY,SAAS,UAAU,SAAS,UAAU;AACtD,MAAI,cAAc,SAAS,cAAc,SAAS,cAAc;AAChE,MAAI,YAAY,SAAS,YAAY,SAAS,YAAY;AAE1D,MAAI,aAAa,EAAE,gBAAgB,GAAK,oBAAoB,UAAU;AACtE,MAAI,SAAS,UAAU;AACrB,iBAAa,EAAE,gBAAgB,EAAI;AAAA,EACrC;AACA,MAAI,WAAW;AAAA,IACb,GAAG,SAAS;AAAA,IACZ,GAAG,SAAS;AAAA,IACZ,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,OAAO,SAAS;AAAA,IAChB,QAAQ,SAAS;AAAA,IACjB,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,OAAO;AAAA,EACT;AAEA,EAAAF,UAAS,cAAc,QAAQ;AAG/B,MAAI,eAAeE,MAAK,aAAa;AACrC,eAAa,aAAa;AAC1B,eAAa,WAAW,aAAa,WAAW;AAChD,eAAa,YAAY;AACzB,yBAAuBA,KAAI;AAAA,IACzB,SAAS,MAAM;AAAA,IACf;AAAA,IACA,SAAS;AAAA,IACT,SAAS,IAAI,SAAS,MAAM;AAAA,IAC5B,SAAS;AAAA,IACT,SAAS;AAAA,IACT,EAAE,MAAM,UAAU;AAAA,IAClB;AAAA,EACF;AAGA,MAAI,SAAS,QAAQ,SAAS,KAAK,SAAS,IAAI;AAC9C,mBAAeA,MAAK,aAAa;AACjC,iBAAa,YAAY;AACzB,2BAAuBA,KAAI;AAAA,MACzB,SAAS,KAAK;AAAA,MACd;AAAA,MACA,SAAS;AAAA,MACT,SAAS,IAAI,SAAS,KAAK;AAAA,MAC3B,SAAS;AAAA,MACT,SAAS;AAAA,MACT,EAAE,MAAM,UAAU;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AAGA,MAAI,SAAS,SAAS,SAAS,MAAM,SAAS,IAAI;AAChD,mBAAeA,MAAK,aAAa;AACjC,iBAAa,WAAW,aAAa,WAAW;AAChD,iBAAa,YAAY;AACzB,2BAAuBA,KAAI;AAAA,MACzB,SAAS,MAAM;AAAA,MACf;AAAA,MACA,SAAS;AAAA,MACT,SAAS,IAAI,SAAS,MAAM;AAAA,MAC5B,SAAS;AAAA,MACT,SAAS;AAAA,MACT,EAAE,MAAM,UAAU;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACF,GAzEqB;AA2Ed,IAAM,cAAc,gCAAU,MAAM,SAASA,OAAM;AACxD,MAAI,YAAY,QAAQ,UAAU,QAAQ,UAAUA,MAAK,QAAQ,YAAY,OAAO,WAAW;AAC/F,MAAI,cAAc,QAAQ,cACtB,QAAQ,cACRA,MAAK,QAAQ,YAAY,OAAO,eAAe;AACnD,MAAI,YAAY,QAAQ,YAAY,QAAQ,YAAY;AAExD,MAAI,YACF;AACF,UAAQ,QAAQ,YAAY,MAAM;AAAA,IAChC,KAAK;AACH,kBACE;AACF;AAAA,IACF,KAAK;AACH,kBACE;AACF;AAAA,EACJ;AAEA,QAAM,cAAc,KAAK,OAAO,GAAG;AACnC,cAAY,KAAK,SAAS,YAAY;AAItC,QAAM,OAAqB,YAAY;AAEvC,UAAQ,QAAQ,YAAY,MAAM;AAAA,IAChC,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,WAAK,IAAI,QAAQ;AACjB,WAAK,IAAI,QAAQ;AACjB,WAAK,OAAO;AACZ,WAAK,QAAQ,QAAQ;AACrB,WAAK,SAAS,QAAQ;AACtB,WAAK,SAAS;AACd,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,QAAQ,EAAE,gBAAgB,IAAI;AACnC,MAAAF,UAAS,aAAa,IAAI;AAC1B;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,kBACG,OAAO,MAAM,EACb,KAAK,QAAQ,SAAS,EACtB,KAAK,gBAAgB,KAAK,EAC1B,KAAK,UAAU,WAAW,EAC1B;AAAA,QACC;AAAA,QACA,4HACG,WAAW,UAAU,QAAQ,CAAC,EAC9B,WAAW,UAAU,QAAQ,CAAC,EAC9B,WAAW,QAAQ,QAAQ,QAAQ,CAAC,EACpC,WAAW,UAAU,QAAQ,MAAM;AAAA,MACxC;AACF,kBACG,OAAO,MAAM,EACb,KAAK,QAAQ,MAAM,EACnB,KAAK,gBAAgB,KAAK,EAC1B,KAAK,UAAU,WAAW,EAC1B;AAAA,QACC;AAAA,QACA,0DACG,WAAW,UAAU,QAAQ,CAAC,EAC9B,WAAW,UAAU,QAAQ,CAAC,EAC9B,WAAW,QAAQ,QAAQ,QAAQ,CAAC;AAAA,MACzC;AACF;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,kBACG,OAAO,MAAM,EACb,KAAK,QAAQ,SAAS,EACtB,KAAK,gBAAgB,KAAK,EAC1B,KAAK,UAAU,WAAW,EAC1B;AAAA,QACC;AAAA,QACA,kHACG,WAAW,UAAU,QAAQ,CAAC,EAC9B,WAAW,UAAU,QAAQ,CAAC,EAC9B,WAAW,SAAS,QAAQ,KAAK,EACjC,WAAW,QAAQ,QAAQ,SAAS,CAAC;AAAA,MAC1C;AACF,kBACG,OAAO,MAAM,EACb,KAAK,QAAQ,MAAM,EACnB,KAAK,gBAAgB,KAAK,EAC1B,KAAK,UAAU,WAAW,EAC1B;AAAA,QACC;AAAA,QACA,2DACG,WAAW,UAAU,QAAQ,IAAI,QAAQ,KAAK,EAC9C,WAAW,UAAU,QAAQ,CAAC,EAC9B,WAAW,QAAQ,QAAQ,SAAS,CAAC;AAAA,MAC1C;AACF;AAAA,EACJ;AAGA,MAAI,kBAAkB,eAAeE,OAAM,QAAQ,YAAY,IAAI;AACnE,cACG,OAAO,MAAM,EACb,KAAK,QAAQ,SAAS,EACtB,KAAK,eAAe,gBAAgB,UAAU,EAC9C,KAAK,aAAa,gBAAgB,WAAW,CAAC,EAC9C,KAAK,cAAc,QAAQ,EAC3B,KAAK,gBAAgB,SAAS,EAC9B,KAAK,cAAc,QAAQ,YAAY,KAAK,EAC5C,KAAK,KAAK,QAAQ,IAAI,QAAQ,QAAQ,IAAI,QAAQ,YAAY,QAAQ,CAAC,EACvE,KAAK,KAAK,QAAQ,IAAI,QAAQ,YAAY,CAAC,EAC3C,KAAK,OAAO,QAAQ,YAAY,OAAO,IAAI;AAG9C,UAAQ,QAAQ,YAAY,MAAM;AAAA,IAChC,KAAK;AAAA,IACL,KAAK;AACH;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ,IAAI,QAAQ,QAAQ,IAAI;AAAA,QAChC,QAAQ,IAAI,QAAQ,MAAM;AAAA,QAC1B;AAAA,MACF;AACA;AAAA,EACJ;AAGA,MAAI,eAAeA,MAAK,QAAQ,YAAY,OAAO,MAAM,EAAE;AAC3D,eAAa,aAAa;AAC1B,eAAa,WAAW,aAAa,WAAW;AAChD,eAAa,YAAY;AACzB,yBAAuBA,KAAI;AAAA,IACzB,QAAQ,MAAM;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ,IAAI,QAAQ,MAAM;AAAA,IAC1B,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,EAAE,MAAM,UAAU;AAAA,IAClB;AAAA,EACF;AAGA,iBAAeA,MAAK,QAAQ,YAAY,OAAO,MAAM,EAAE;AACvD,eAAa,YAAY;AAEzB,MAAI,QAAQ,SAAS,QAAQ,OAAO,SAAS,IAAI;AAC/C,2BAAuBA,KAAI;AAAA,MACzB,QAAQ,MAAM;AAAA,MACd;AAAA,MACA,QAAQ;AAAA,MACR,QAAQ,IAAI,QAAQ,MAAM;AAAA,MAC1B,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,EAAE,MAAM,WAAW,cAAc,SAAS;AAAA,MAC1C;AAAA,IACF;AAAA,EACF,WAAW,QAAQ,QAAQ,QAAQ,KAAK,SAAS,IAAI;AACnD,2BAAuBA,KAAI;AAAA,MACzB,QAAQ,KAAK;AAAA,MACb;AAAA,MACA,QAAQ;AAAA,MACR,QAAQ,IAAI,QAAQ,KAAK;AAAA,MACzB,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,EAAE,MAAM,WAAW,cAAc,SAAS;AAAA,MAC1C;AAAA,IACF;AAAA,EACF;AAGA,MAAI,QAAQ,SAAS,QAAQ,MAAM,SAAS,IAAI;AAC9C,mBAAeA,MAAK,WAAW;AAC/B,iBAAa,YAAY;AACzB,2BAAuBA,KAAI;AAAA,MACzB,QAAQ,MAAM;AAAA,MACd;AAAA,MACA,QAAQ;AAAA,MACR,QAAQ,IAAI,QAAQ,MAAM;AAAA,MAC1B,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,EAAE,MAAM,UAAU;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AAEA,SAAO,QAAQ;AACjB,GA3M2B;AA6MpB,IAAM,qBAAqB,gCAAU,MAAM;AAChD,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,UAAU,EACrB,KAAK,aAAa,SAAS,EAC3B,KAAK,aAAa,SAAS,EAC3B,OAAO,MAAM,EACb,KAAK,aAAa,WAAW,EAC7B;AAAA,IACC;AAAA,IACA;AAAA,EACF;AACJ,GAbkC;AAe3B,IAAM,qBAAqB,gCAAU,MAAM;AAChD,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,UAAU,EACrB,KAAK,SAAS,IAAI,EAClB,KAAK,UAAU,IAAI,EACnB,OAAO,MAAM,EACb,KAAK,aAAa,WAAW,EAC7B;AAAA,IACC;AAAA,IACA;AAAA,EACF;AACJ,GAbkC;AAe3B,IAAM,kBAAkB,gCAAU,MAAM;AAC7C,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,OAAO,EAClB,KAAK,SAAS,IAAI,EAClB,KAAK,UAAU,IAAI,EACnB,OAAO,MAAM,EACb,KAAK,aAAa,WAAW,EAC7B;AAAA,IACC;AAAA,IACA;AAAA,EACF;AACJ,GAb+B;AAoBxB,IAAM,kBAAkB,gCAAU,MAAM;AAC7C,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,WAAW,EACtB,KAAK,QAAQ,CAAC,EACd,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,gBAAgB,EACpC,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,uBAAuB;AACtC,GAb+B;AAexB,IAAM,iBAAiB,gCAAU,MAAM;AAC5C,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,UAAU,EACrB,KAAK,QAAQ,CAAC,EACd,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,gBAAgB,EACpC,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,wBAAwB;AACvC,GAb8B;AAoBvB,IAAM,wBAAwB,gCAAU,MAAM;AACnD,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,aAAa,EACxB,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,2BAA2B;AAC1C,GAZqC;AAmB9B,IAAM,sBAAsB,gCAAU,MAAM;AACjD,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,gBAAgB,EAC3B,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,EAAE,EACf,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,QAAQ,EACf,KAAK,MAAM,EAAE,EACb,KAAK,MAAM,EAAE,EACb,KAAK,KAAK,CAAC;AAEhB,GAfmC;AAsB5B,IAAM,uBAAuB,gCAAU,MAAM;AAClD,QAAM,OAAO,KAAK,OAAO,MAAM;AAC/B,QAAM,SAAS,KACZ,OAAO,QAAQ,EACf,KAAK,MAAM,WAAW,EACtB,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,CAAC,EACtB,KAAK,UAAU,MAAM,EACrB,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,CAAC;AAGjB,SACG,OAAO,MAAM,EACb,KAAK,QAAQ,OAAO,EACpB,KAAK,UAAU,SAAS,EACxB,MAAM,oBAAoB,MAAM,EAChC,KAAK,gBAAgB,KAAK,EAC1B,KAAK,KAAK,mBAAmB;AAGhC,SACG,OAAO,MAAM,EACb,KAAK,QAAQ,MAAM,EACnB,KAAK,UAAU,SAAS,EACxB,MAAM,oBAAoB,MAAM,EAChC,KAAK,gBAAgB,KAAK,EAC1B,KAAK,KAAK,yBAAyB;AAExC,GA7BoC;AA+BpC,IAAM,iBAAiB,wBAAC,KAAK,gBAAgB;AAC3C,SAAO;AAAA,IACL,YAAY,IAAI,cAAc,YAAY;AAAA,IAC1C,UAAU,IAAI,cAAc,UAAU;AAAA,IACtC,YAAY,IAAI,cAAc,YAAY;AAAA,EAC5C;AACF,GANuB;AAQvB,IAAM,yBAA0B,2BAAY;AAU1C,WAAS,OAAO,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW;AAC1D,UAAM,OAAO,EACV,OAAO,MAAM,EACb,KAAK,KAAK,IAAI,QAAQ,CAAC,EACvB,KAAK,KAAK,IAAI,SAAS,IAAI,CAAC,EAC5B,MAAM,eAAe,QAAQ,EAC7B,KAAK,OAAO;AACf,kBAAc,MAAM,SAAS;AAAA,EAC/B;AARS;AAoBT,WAAS,QAAQ,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAWA,OAAM;AACjE,UAAM,EAAE,UAAU,YAAY,WAAW,IAAIA;AAE7C,UAAM,QAAQ,QAAQ,MAAM,eAAO,cAAc;AACjD,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,KAAK,IAAI,WAAY,YAAY,MAAM,SAAS,KAAM;AAC5D,YAAM,OAAO,EACV,OAAO,MAAM,EACb,KAAK,KAAK,IAAI,QAAQ,CAAC,EACvB,KAAK,KAAK,CAAC,EACX,MAAM,eAAe,QAAQ,EAC7B,KAAK,qBAAqB,QAAQ,EAClC,MAAM,aAAa,QAAQ,EAC3B,MAAM,eAAe,UAAU,EAC/B,MAAM,eAAe,UAAU;AAClC,WACG,OAAO,OAAO,EAEd,KAAK,MAAM,EAAE,EACb,KAAK,MAAM,CAAC,CAAC,EAEb,KAAK,sBAAsB,cAAc;AAE5C,oBAAc,MAAM,SAAS;AAAA,IAC/B;AAAA,EACF;AAzBS;AAqCT,WAAS,KAAK,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAWA,OAAM;AAC9D,UAAM,IAAI,EAAE,OAAO,QAAQ;AAC3B,UAAM,IAAI,EACP,OAAO,eAAe,EACtB,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,KAAK,SAAS,KAAK,EACnB,KAAK,UAAU,MAAM;AAExB,UAAM,OAAO,EACV,OAAO,WAAW,EAClB,MAAM,WAAW,OAAO,EACxB,MAAM,UAAU,MAAM,EACtB,MAAM,SAAS,MAAM;AAExB,SACG,OAAO,KAAK,EACZ,MAAM,WAAW,YAAY,EAC7B,MAAM,cAAc,QAAQ,EAC5B,MAAM,kBAAkB,QAAQ,EAChC,KAAK,OAAO;AAEf,YAAQ,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAWA,KAAI;AACxD,kBAAc,MAAM,SAAS;AAAA,EAC/B;AAxBS;AA8BT,WAAS,cAAc,QAAQ,mBAAmB;AAChD,eAAW,OAAO,mBAAmB;AACnC,UAAI,kBAAkB,eAAe,GAAG,GAAG;AACzC,eAAO,KAAK,KAAK,kBAAkB,GAAG,CAAC;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AANS;AAQT,SAAO,SAAUA,OAAM;AACrB,WAAOA,MAAK,kBAAkB,OAAO,OAAOA,MAAK,kBAAkB,QAAQ,SAAS;AAAA,EACtF;AACF,EAAG;AAEH,IAAO,kBAAQ;AAAA,EACb,UAAAF;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACrqBA,IAAI,qBAAqB;AAAzB,IACE,qBAAqB;AAEvB,IAAIG,gBAAe;AACnB,IAAIC,mBAAkB;AAEtB,OAAO,KAAK;AAEZ,IAAI,OAAO,CAAC;AAEZ,IAAM,SAAN,MAAa;AAAA,EArBb,OAqBa;AAAA;AAAA;AAAA,EACX,YAAY,SAAS;AACnB,SAAK,OAAO;AACZ,SAAK,OAAO,CAAC;AACb,SAAK,KAAK,SAAS;AACnB,SAAK,KAAK,QAAQ;AAClB,SAAK,KAAK,SAAS;AACnB,SAAK,KAAK,QAAQ;AAClB,SAAK,KAAK,aAAa;AAEvB,SAAK,WAAW,CAAC;AACjB,SAAK,SAAS,SAAS;AACvB,SAAK,SAAS,QAAQ;AACtB,SAAK,SAAS,SAAS;AACvB,SAAK,SAAS,QAAQ;AACtB,SAAK,SAAS,MAAM;AAEpB,YAAQ,QAAQ,GAAG,UAAU,CAAC;AAAA,EAChC;AAAA,EAEA,QAAQ,QAAQ,OAAO,QAAQ,OAAO;AACpC,SAAK,SAAS,SAAS,KAAK,KAAK,SAAS;AAC1C,SAAK,SAAS,QAAQ,KAAK,KAAK,QAAQ;AACxC,SAAK,SAAS,SAAS,KAAK,KAAK,SAAS;AAC1C,SAAK,SAAS,QAAQ,KAAK,KAAK,QAAQ;AAAA,EAC1C;AAAA,EAEA,UAAU,KAAK,KAAK,KAAK,KAAK;AAC5B,QAAI,IAAI,GAAG,MAAM,QAAW;AAC1B,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,CAAC;AAAA,IAC9B;AAAA,EACF;AAAA,EAEA,OAAO,SAAS;AACd,SAAK,SAAS,MAAM,KAAK,SAAS,MAAM;AACxC,QAAI,UACF,KAAK,SAAS,WAAW,KAAK,SAAS,QACnC,KAAK,SAAS,QAAQ,QAAQ,SAC9B,KAAK,SAAS,QAAQ,QAAQ,SAAS;AAC7C,QAAI,SAAS,UAAU,QAAQ;AAC/B,QAAI,UAAU,KAAK,SAAS,SAAS,QAAQ,SAAS;AACtD,QAAI,SAAS,UAAU,QAAQ;AAC/B,QACE,WAAW,KAAK,KAAK,cACrB,UAAU,KAAK,KAAK,cACpB,KAAK,SAAS,MAAMD,eACpB;AACA,gBAAU,KAAK,SAAS,SAAS,QAAQ,SAAS,KAAK;AACvD,gBAAU,KAAK,SAAS,QAAQ,QAAQ,SAAS;AAEjD,WAAK,SAAS,QAAQ,SAAS,UAAU,QAAQ;AACjD,WAAK,SAAS,SAAS,KAAK,SAAS;AACrC,WAAK,SAAS,QAAQ,SAAS,UAAU,QAAQ;AACjD,WAAK,SAAS,MAAM;AAAA,IACtB;AAEA,YAAQ,IAAI;AACZ,YAAQ,IAAI;AAEZ,SAAK,UAAU,KAAK,MAAM,UAAU,SAAS,KAAK,GAAG;AACrD,SAAK,UAAU,KAAK,MAAM,UAAU,SAAS,KAAK,GAAG;AACrD,SAAK,UAAU,KAAK,MAAM,SAAS,QAAQ,KAAK,GAAG;AACnD,SAAK,UAAU,KAAK,MAAM,SAAS,QAAQ,KAAK,GAAG;AAEnD,SAAK,UAAU,KAAK,UAAU,UAAU,SAAS,KAAK,GAAG;AACzD,SAAK,UAAU,KAAK,UAAU,UAAU,SAAS,KAAK,GAAG;AACzD,SAAK,UAAU,KAAK,UAAU,SAAS,QAAQ,KAAK,GAAG;AACvD,SAAK,UAAU,KAAK,UAAU,SAAS,QAAQ,KAAK,GAAG;AAAA,EACzD;AAAA,EAEA,KAAK,SAAS;AACZ,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AACA,SAAK,WAAW;AAAA,MACd,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,IACP;AACA,YAAQ,QAAQ,GAAG,UAAU,CAAC;AAAA,EAChC;AAAA,EAEA,eAAe,QAAQ;AACrB,SAAK,KAAK,SAAS;AACnB,SAAK,KAAK,SAAS;AAAA,EACrB;AACF;AAEO,IAAM,UAAU,gCAAU,KAAK;AACpC,0BAAgB,MAAM,GAAG;AAEzB,MAAI,IAAI,YAAY;AAClB,SAAK,mBAAmB,KAAK,mBAAmB,KAAK,oBAAoB,IAAI;AAAA,EAC/E;AACA,MAAI,IAAI,UAAU;AAChB,SAAK,iBAAiB,KAAK,iBAAiB,KAAK,kBAAkB,IAAI;AAAA,EACzE;AACA,MAAI,IAAI,YAAY;AAClB,SAAK,mBAAmB,KAAK,mBAAmB,KAAK,oBAAoB,IAAI;AAAA,EAC/E;AACF,GAZuB;AAcvB,IAAM,cAAc,wBAAC,KAAK,gBAAgB;AACxC,SAAO;AAAA,IACL,YAAY,IAAI,cAAc,YAAY;AAAA,IAC1C,UAAU,IAAI,cAAc,UAAU;AAAA,IACtC,YAAY,IAAI,cAAc,YAAY;AAAA,EAC5C;AACF,GANoB;AAQpB,IAAM,eAAe,wBAAC,QAAQ;AAC5B,SAAO;AAAA,IACL,YAAY,IAAI;AAAA,IAChB,UAAU,IAAI;AAAA,IACd,YAAY,IAAI;AAAA,EAClB;AACF,GANqB;AAQrB,IAAM,cAAc,wBAAC,QAAQ;AAC3B,SAAO;AAAA,IACL,YAAY,IAAI;AAAA,IAChB,UAAU,IAAI;AAAA,IACd,YAAY,IAAI;AAAA,EAClB;AACF,GANoB;AAepB,SAAS,kBAAkB,UAAU,SAAS,iBAAiB,UAAU,gBAAgB;AACvF,MAAI,CAAC,QAAQ,QAAQ,EAAE,OAAO;AAC5B,QAAI,iBAAiB;AACnB,cAAQ,QAAQ,EAAE,OAAO,UAAU,QAAQ,QAAQ,EAAE,MAAM,gBAAgB,QAAQ;AACnF,cAAQ,QAAQ,EAAE,YAAY,QAAQ,QAAQ,EAAE,KAAK,MAAM,eAAO,cAAc,EAAE;AAElF,cAAQ,QAAQ,EAAE,QAAQ;AAE1B,cAAQ,QAAQ,EAAE,SAAS,oBAAoB,QAAQ,QAAQ,EAAE,MAAM,QAAQ;AAAA,IACjF,OAAO;AACL,UAAI,QAAQ,QAAQ,QAAQ,EAAE,KAAK,MAAM,eAAO,cAAc;AAC9D,cAAQ,QAAQ,EAAE,YAAY,MAAM;AACpC,UAAI,aAAa;AACjB,cAAQ,QAAQ,EAAE,SAAS;AAC3B,cAAQ,QAAQ,EAAE,QAAQ;AAC1B,iBAAW,QAAQ,OAAO;AACxB,gBAAQ,QAAQ,EAAE,QAAQ,KAAK;AAAA,UAC7B,mBAAmB,MAAM,QAAQ;AAAA,UACjC,QAAQ,QAAQ,EAAE;AAAA,QACpB;AACA,qBAAa,oBAAoB,MAAM,QAAQ;AAC/C,gBAAQ,QAAQ,EAAE,SAAS,QAAQ,QAAQ,EAAE,SAAS;AAAA,MACxD;AAAA,IAEF;AAAA,EACF;AACF;AA1BS;AA4BF,IAAME,gBAAe,gCAAUC,UAAS,UAAU,QAAQ;AAC/D,WAAS,IAAI,OAAO,KAAK;AACzB,WAAS,IAAI,OAAO,KAAK;AACzB,WAAS,QAAQ,OAAO,KAAK,QAAQ,OAAO,KAAK;AACjD,WAAS,SAAS,OAAO,KAAK,QAAQ,OAAO,KAAK;AAElD,WAAS,MAAM,IAAI,KAAK,gBAAgB;AAExC,MAAI,mBAAmB,SAAS,QAAQ,KAAK;AAC7C,MAAI,oBAAoB,aAAa,IAAI;AACzC,oBAAkB,WAAW,kBAAkB,WAAW;AAC1D,oBAAkB,aAAa;AAC/B,MAAI,iBAAiB,mBAAmB,SAAS,MAAM,MAAM,iBAAiB;AAC9E,oBAAkB,SAAS,UAAU,kBAAkB,mBAAmB,cAAc;AAExF,kBAAQ,aAAaA,UAAS,UAAU,IAAI;AAC9C,GAhB4B;AAkBrB,IAAM,mBAAmB,gCAAU,eAAeA,UAASC,eAAc,aAAa;AAE3F,MAAI,IAAI;AAER,aAAW,cAAc,aAAa;AACpC,QAAI;AACJ,UAAM,UAAUA,cAAa,UAAU;AAIvC,QAAI,kBAAkB,YAAY,MAAM,QAAQ,YAAY,IAAI;AAChE,oBAAgB,WAAW,gBAAgB,WAAW;AACtD,YAAQ,YAAY,QAAQ;AAAA,MAC1B,SAAM,QAAQ,YAAY,OAAO;AAAA,MACjC;AAAA,IACF;AACA,YAAQ,YAAY,SAAS,gBAAgB,WAAW;AACxD,YAAQ,YAAY,IAAI,KAAK;AAC7B,QAAI,QAAQ,YAAY,IAAI,QAAQ,YAAY,SAAS;AAOzD,YAAQ,QAAQ,EAAE,OAAO,GAAG,QAAQ,GAAG,GAAG,EAAE;AAC5C,YAAQ,QAAQ,YAAY,MAAM;AAAA,MAChC,KAAK;AAAA,MACL,KAAK;AACH,gBAAQ,MAAM,QAAQ;AACtB,gBAAQ,MAAM,SAAS;AACvB,gBAAQ,MAAM,IAAI;AAClB,YAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM;AACpC;AAAA,IACJ;AACA,QAAI,QAAQ,QAAQ;AAClB,cAAQ,MAAM,QAAQ;AACtB,cAAQ,MAAM,SAAS;AACvB,cAAQ,MAAM,IAAI;AAClB,UAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM;AAAA,IACtC;AAIA,QAAI,kBAAkB,QAAQ,QAAQ,KAAK;AAC3C,QAAI,iBAAiB,KAAK,QAAQ,KAAK,iBAAiB;AAExD,QAAI,mBAAmB,YAAY,MAAM,QAAQ,YAAY,IAAI;AACjE,qBAAiB,WAAW,iBAAiB,WAAW;AACxD,qBAAiB,aAAa;AAC9B,sBAAkB,SAAS,SAAS,iBAAiB,kBAAkB,cAAc;AACrF,YAAQ,MAAM,IAAI,IAAI;AACtB,QAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM;AAEpC,QAAI,QAAQ,QAAQ,QAAQ,KAAK,SAAS,IAAI;AAC5C,cAAQ,KAAK,OAAO,MAAM,QAAQ,KAAK,OAAO;AAC9C,UAAIC,mBAAkB,YAAY,MAAM,QAAQ,YAAY,IAAI;AAChE,wBAAkB,QAAQ,SAAS,iBAAiBA,kBAAiB,cAAc;AACnF,cAAQ,KAAK,IAAI,IAAI;AACrB,UAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK;AAAA,IACpC,WAAW,QAAQ,SAAS,QAAQ,MAAM,SAAS,IAAI;AACrD,cAAQ,MAAM,OAAO,MAAM,QAAQ,MAAM,OAAO;AAChD,UAAI,mBAAmB,YAAY,MAAM,QAAQ,MAAM,IAAI;AAC3D,wBAAkB,SAAS,SAAS,iBAAiB,kBAAkB,cAAc;AACrF,cAAQ,MAAM,IAAI,IAAI;AACtB,UAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM;AAAA,IACtC;AAEA,QAAI,aAAa;AACjB,QAAI,YAAY,QAAQ,MAAM;AAE9B,QAAI,QAAQ,SAAS,QAAQ,MAAM,SAAS,IAAI;AAC9C,UAAI,mBAAmB,YAAY,MAAM,QAAQ,YAAY,IAAI;AACjE,wBAAkB,SAAS,SAAS,iBAAiB,kBAAkB,cAAc;AACrF,cAAQ,MAAM,IAAI,IAAI;AACtB,UAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM;AAEpC,kBAAY,KAAK,IAAI,QAAQ,MAAM,OAAO,QAAQ,MAAM,KAAK;AAC7D,mBAAa,IAAI,QAAQ,MAAM,YAAY;AAAA,IAC7C;AAEA,gBAAY,YAAY,KAAK;AAG7B,YAAQ,QAAQ,KAAK,IAAI,QAAQ,SAAS,KAAK,OAAO,WAAW,KAAK,KAAK;AAC3E,YAAQ,SAAS,KAAK,IAAI,QAAQ,UAAU,KAAK,QAAQ,YAAY,KAAK,MAAM;AAChF,YAAQ,SAAS,QAAQ,UAAU,KAAK;AAExC,kBAAc,OAAO,OAAO;AAE5B,oBAAQ,YAAYF,UAAS,SAAS,IAAI;AAAA,EAC5C;AAEA,gBAAc,eAAe,KAAK,aAAa;AACjD,GA9FgC;AAgGhC,IAAM,QAAN,MAAY;AAAA,EAjTZ,OAiTY;AAAA;AAAA;AAAA,EACV,YAAY,GAAG,GAAG;AAChB,SAAK,IAAI;AACT,SAAK,IAAI;AAAA,EACX;AACF;AAkBA,IAAI,oBAAoB,gCAAU,UAAU,UAAU;AACpD,MAAI,KAAK,SAAS;AAElB,MAAI,KAAK,SAAS;AAElB,MAAI,KAAK,SAAS;AAElB,MAAI,KAAK,SAAS;AAElB,MAAI,cAAc,KAAK,SAAS,QAAQ;AAExC,MAAI,cAAc,KAAK,SAAS,SAAS;AAEzC,MAAI,KAAK,KAAK,IAAI,KAAK,EAAE;AAEzB,MAAI,KAAK,KAAK,IAAI,KAAK,EAAE;AAEzB,MAAI,SAAS,KAAK;AAElB,MAAI,UAAU,SAAS,SAAS,SAAS;AAEzC,MAAI,cAAc;AAElB,MAAI,MAAM,MAAM,KAAK,IAAI;AACvB,kBAAc,IAAI,MAAM,KAAK,SAAS,OAAO,WAAW;AAAA,EAC1D,WAAW,MAAM,MAAM,KAAK,IAAI;AAC9B,kBAAc,IAAI,MAAM,IAAI,WAAW;AAAA,EACzC,WAAW,MAAM,MAAM,KAAK,IAAI;AAC9B,kBAAc,IAAI,MAAM,aAAa,KAAK,SAAS,MAAM;AAAA,EAC3D,WAAW,MAAM,MAAM,KAAK,IAAI;AAC9B,kBAAc,IAAI,MAAM,aAAa,EAAE;AAAA,EACzC;AAEA,MAAI,KAAK,MAAM,KAAK,IAAI;AACtB,QAAI,WAAW,QAAQ;AACrB,oBAAc,IAAI,MAAM,IAAI,cAAe,SAAS,SAAS,QAAS,CAAC;AAAA,IACzE,OAAO;AACL,oBAAc,IAAI;AAAA,QAChB,cAAgB,KAAK,KAAM,SAAS,SAAU;AAAA,QAC9C,KAAK,SAAS;AAAA,MAChB;AAAA,IACF;AAAA,EACF,WAAW,KAAK,MAAM,KAAK,IAAI;AAE7B,QAAI,WAAW,QAAQ;AACrB,oBAAc,IAAI,MAAM,KAAK,SAAS,OAAO,cAAe,SAAS,SAAS,QAAS,CAAC;AAAA,IAC1F,OAAO;AACL,oBAAc,IAAI;AAAA,QAChB,cAAgB,KAAK,KAAM,SAAS,SAAU;AAAA,QAC9C,KAAK,SAAS;AAAA,MAChB;AAAA,IACF;AAAA,EACF,WAAW,KAAK,MAAM,KAAK,IAAI;AAC7B,QAAI,WAAW,QAAQ;AACrB,oBAAc,IAAI,MAAM,KAAK,SAAS,OAAO,cAAe,SAAS,SAAS,QAAS,CAAC;AAAA,IAC1F,OAAO;AACL,oBAAc,IAAI,MAAM,cAAgB,SAAS,SAAS,IAAK,KAAM,IAAI,EAAE;AAAA,IAC7E;AAAA,EACF,WAAW,KAAK,MAAM,KAAK,IAAI;AAC7B,QAAI,WAAW,QAAQ;AACrB,oBAAc,IAAI,MAAM,IAAI,cAAe,SAAS,QAAQ,IAAK,MAAM;AAAA,IACzE,OAAO;AACL,oBAAc,IAAI,MAAM,cAAgB,SAAS,SAAS,IAAK,KAAM,IAAI,EAAE;AAAA,IAC7E;AAAA,EACF;AACA,SAAO;AACT,GAlEwB;AAoExB,IAAI,qBAAqB,gCAAU,UAAU,SAAS;AACpD,MAAI,oBAAoB,EAAE,GAAG,GAAG,GAAG,EAAE;AACrC,oBAAkB,IAAI,QAAQ,IAAI,QAAQ,QAAQ;AAClD,oBAAkB,IAAI,QAAQ,IAAI,QAAQ,SAAS;AACnD,MAAI,aAAa,kBAAkB,UAAU,iBAAiB;AAE9D,oBAAkB,IAAI,SAAS,IAAI,SAAS,QAAQ;AACpD,oBAAkB,IAAI,SAAS,IAAI,SAAS,SAAS;AACrD,MAAI,WAAW,kBAAkB,SAAS,iBAAiB;AAC3D,SAAO,EAAE,YAAwB,SAAmB;AACtD,GAVyB;AAYlB,IAAMG,YAAW,gCAAUH,UAASI,OAAM,eAAe,SAAS;AACvE,MAAI,IAAI;AACR,WAAS,OAAOA,OAAM;AACpB,QAAI,IAAI;AACR,QAAI,cAAc,IAAI,QAAQ,KAAK;AACnC,QAAI,UAAU,YAAY,IAAI;AAC9B,QAAI,cAAc,QAAQ,GAAG,UAAU;AACvC,QAAI,gBAAgB,aAAa;AAC/B,UAAI,MAAM,OAAO,IAAI,OAAO,IAAI,MAAM;AAAA,IACxC;AACA,QAAI,iBAAiB,mBAAmB,IAAI,MAAM,MAAM,OAAO;AAC/D,sBAAkB,SAAS,KAAK,aAAa,SAAS,cAAc;AAEpE,QAAI,IAAI,SAAS,IAAI,MAAM,SAAS,IAAI;AACtC,uBAAiB,mBAAmB,IAAI,MAAM,MAAM,OAAO;AAC3D,wBAAkB,SAAS,KAAK,aAAa,SAAS,cAAc;AAAA,IACtE;AAEA,QAAI,IAAI,SAAS,IAAI,MAAM,SAAS,IAAI;AACtC,uBAAiB,mBAAmB,IAAI,MAAM,MAAM,OAAO;AAC3D,wBAAkB,SAAS,KAAK,aAAa,SAAS,cAAc;AAAA,IACtE;AAEA,QAAI,WAAW,cAAc,IAAI,IAAI;AACrC,QAAI,UAAU,cAAc,IAAI,EAAE;AAClC,QAAI,SAAS,mBAAmB,UAAU,OAAO;AACjD,QAAI,aAAa,OAAO;AACxB,QAAI,WAAW,OAAO;AAAA,EACxB;AACA,kBAAQ,SAASJ,UAASI,OAAM,IAAI;AACtC,GA9BwB;AAuCxB,SAAS,mBACPJ,UACA,qBACA,cACA,mBACA,SACA;AACA,MAAI,gBAAgB,IAAI,OAAO,OAAO;AAEtC,gBAAc,KAAK,aACjB,aAAa,KAAK,aAAa,KAAK,IAAIF,kBAAiB,kBAAkB,MAAM;AAKnF,WAAS,CAAC,GAAG,eAAe,KAAK,kBAAkB,QAAQ,GAAG;AAC5D,QAAI,IAAI;AACR,oBAAgB,QAAQ,EAAE,OAAO,GAAG,QAAQ,GAAG,GAAG,EAAE;AACpD,QAAI,gBAAgB,QAAQ;AAC1B,sBAAgB,MAAM,QAAQ;AAC9B,sBAAgB,MAAM,SAAS;AAC/B,sBAAgB,MAAM,IAAI;AAC1B,UAAI,gBAAgB,MAAM,IAAI,gBAAgB,MAAM;AAAA,IACtD;AAEA,QAAI,0BAA0B,gBAAgB,QAAQ,KAAK;AAE3D,QAAI,2BAA2B,aAAa,IAAI;AAChD,6BAAyB,WAAW,yBAAyB,WAAW;AACxE,6BAAyB,aAAa;AACtC;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,KAAK;AAAA,IACrB;AACA,oBAAgB,MAAM,IAAI,IAAI;AAC9B,QAAI,gBAAgB,MAAM,IAAI,gBAAgB,MAAM;AAEpD,QAAI,gBAAgB,QAAQ,gBAAgB,KAAK,SAAS,IAAI;AAC5D,sBAAgB,KAAK,OAAO,MAAM,gBAAgB,KAAK,OAAO;AAC9D,UAAI,0BAA0B,aAAa,IAAI;AAC/C;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,KAAK;AAAA,MACrB;AACA,sBAAgB,KAAK,IAAI,IAAI;AAC7B,UAAI,gBAAgB,KAAK,IAAI,gBAAgB,KAAK;AAAA,IACpD;AAEA,QAAI,gBAAgB,SAAS,gBAAgB,MAAM,SAAS,IAAI;AAC9D,UAAI,2BAA2B,aAAa,IAAI;AAChD,+BAAyB,WAAW,yBAAyB,WAAW;AACxE;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,KAAK;AAAA,MACrB;AACA,sBAAgB,MAAM,IAAI,IAAI;AAC9B,UAAI,gBAAgB,MAAM,IAAI,gBAAgB,MAAM;AAAA,IACtD;AAEA,QAAI,KAAK,KAAK,IAAIA,qBAAoB,GAAG;AAEvC,UAAI,KAAK,aAAa,KAAK,SAAS,KAAK;AACzC,UAAI,KAAK,aAAa,KAAK,QAAQ,KAAK,iBAAiB;AAEzD,oBAAc,QAAQ,IAAI,IAAI,IAAI,EAAE;AAAA,IACtC,OAAO;AAEL,UAAI,KACF,cAAc,KAAK,UAAU,cAAc,KAAK,SAC5C,cAAc,KAAK,QAAQ,KAAK,iBAChC,cAAc,KAAK;AACzB,UAAI,KAAK,cAAc,KAAK;AAE5B,oBAAc,QAAQ,IAAI,IAAI,IAAI,EAAE;AAAA,IACtC;AACA,kBAAc,OAAO,gBAAgB;AACrC,QAAI,6BAA6B,QAAQ,GAAG,gBAAgB,gBAAgB,KAAK;AACjF,QAAI,4BAA4B,QAAQ,GAAG,eAAe,gBAAgB,KAAK;AAE/E,QAAI,0BAA0B,SAAS,GAAG;AACxC;AAAA,QACE;AAAA,QACAE;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,0BAAsB,gBAAgB;AACtC,QAAI,wBAAwB,QAAQ,GAAG,aAAa,mBAAmB;AAEvE,QAAI,sBAAsB,SAAS,GAAG;AAEpC;AAAA,QACEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAEA,QAAI,gBAAgB,UAAU,UAAU;AACtC,MAAAD,cAAaC,UAAS,iBAAiB,aAAa;AAAA,IACtD;AACA,iBAAa,KAAK,QAAQ,KAAK;AAAA,MAC7B,cAAc,KAAK,QAAQ,KAAK;AAAA,MAChC,aAAa,KAAK;AAAA,IACpB;AACA,iBAAa,KAAK,QAAQ,KAAK;AAAA,MAC7B,cAAc,KAAK,QAAQ,KAAK;AAAA,MAChC,aAAa,KAAK;AAAA,IACpB;AACA,yBAAqB,KAAK,IAAI,oBAAoB,aAAa,KAAK,KAAK;AACzE,yBAAqB,KAAK,IAAI,oBAAoB,aAAa,KAAK,KAAK;AAAA,EAC3E;AACF;AA5HS;AAsIF,IAAM,OAAO,gCAAU,OAAO,IAAI,UAAU,SAAS;AAC1D,SAAO,UAAU,EAAE;AACnB,QAAM,gBAAgB,UAAU,EAAE;AAElC,MAAI;AACJ,MAAI,kBAAkB,WAAW;AAC/B,qBAAiB,eAAO,OAAO,EAAE;AAAA,EACnC;AACA,QAAM,OACJ,kBAAkB,YACd,eAAO,eAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,IACrD,eAAO,MAAM;AAEnB,MAAI,KAAK,QAAQ;AAEjB,UAAQ,GAAG,QAAQ,KAAK,IAAI;AAE5B,EAAAH,gBAAe,GAAG,gBAAgB;AAClC,EAAAC,mBAAkB,GAAG,mBAAmB;AAExC,MAAI,MAAM,KAAK,KAAK,UAAU,MAAM,MAAM,CAAC,CAAC,EAAE;AAE9C,QAAME,WACJ,kBAAkB,YAAY,KAAK,OAAO,QAAQ,EAAE,IAAI,IAAI,eAAO,QAAQ,EAAE,IAAI;AAEnF,kBAAQ,mBAAmBA,QAAO;AAClC,kBAAQ,mBAAmBA,QAAO;AAClC,kBAAQ,gBAAgBA,QAAO;AAE/B,MAAI,eAAe,IAAI,OAAO,OAAO;AAErC,eAAa;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAEA,eAAa,KAAK,aAAa,OAAO;AACtC,uBAAqB,KAAK;AAC1B,uBAAqB,KAAK;AAE1B,QAAMK,SAAQ,QAAQ,GAAG,SAAS;AAClC,MAAI,oBAAoB,QAAQ,GAAG,aAAa,EAAE;AAGlD,qBAAmBL,UAAS,IAAI,cAAc,mBAAmB,OAAO;AAKxE,kBAAQ,gBAAgBA,QAAO;AAC/B,kBAAQ,eAAeA,QAAO;AAC9B,kBAAQ,qBAAqBA,QAAO;AACpC,kBAAQ,sBAAsBA,QAAO;AAErC,EAAAG,UAASH,UAAS,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,YAAY,OAAO;AAEtE,eAAa,KAAK,QAAQ;AAC1B,eAAa,KAAK,QAAQ;AAE1B,QAAM,MAAM,aAAa;AAGzB,MAAI,YAAY,IAAI,QAAQ,IAAI;AAEhC,MAAI,SAAS,YAAY,IAAI,KAAK;AAGlC,MAAI,WAAW,IAAI,QAAQ,IAAI;AAC/B,QAAM,QAAQ,WAAW,IAAI,KAAK;AAElC,MAAIK,QAAO;AACT,IAAAL,SACG,OAAO,MAAM,EACb,KAAKK,MAAK,EACV,KAAK,MAAM,IAAI,QAAQ,IAAI,UAAU,IAAI,IAAI,KAAK,cAAc,EAChE,KAAK,KAAK,IAAI,SAAS,KAAK,cAAc;AAAA,EAC/C;AAEA,mBAAiBL,UAAS,QAAQ,OAAO,KAAK,WAAW;AAEzD,QAAM,oBAAoBK,SAAQ,KAAK;AACvC,EAAAL,SAAQ;AAAA,IACN;AAAA,IACA,IAAI,SACF,KAAK,iBACL,QACC,KAAK,iBAAiB,qBACvB,MACA,QACA,OACC,SAAS;AAAA,EACd;AAEA,MAAI,MAAM,WAAW,GAAG;AAC1B,GAhGoB;AAkGpB,IAAO,qBAAQ;AAAA,EACb,yBAAyB;AAAA,EACzB,cAAAD;AAAA,EACA;AAAA,EACA;AACF;;;AC5qBA,IAAM,YAAY,wBAAC,YACjB;AAAA,cACY,QAAQ,YAAY;AAAA,YACtB,QAAQ,SAAS;AAAA;AAAA,GAHX;AAOlB,IAAO,iBAAQ;;;ACCR,IAAM,UAA6B;AAAA,EACxC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM,wBAAC,EAAE,IAAI,KAAK,MAAqB;AACrC,uBAAS,QAAQ,EAAE;AACnB,iBAAG,QAAQ,IAAI;AAAA,EACjB,GAHM;AAIR;", "names": ["o", "parser", "lexer", "c", "rel", "personOrSystem", "container", "component", "boundary", "drawRect", "rels", "conf", "c4ShapeInRow", "c4BoundaryInRow", "drawBoundary", "diagram", "c4ShapeArray", "c4ShapeTypeConf", "drawRels", "rels", "title"]}