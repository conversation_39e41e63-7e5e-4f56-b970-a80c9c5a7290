{"version": 3, "sources": ["../../../src/diagrams/info/infoParser.ts", "../../../src/diagrams/info/infoDb.ts", "../../../src/diagrams/info/infoRenderer.ts", "../../../src/diagrams/info/infoDiagram.ts"], "sourcesContent": ["import type { Info } from '@mermaid-js/parser';\nimport { parse } from '@mermaid-js/parser';\nimport type { ParserDefinition } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\n\nexport const parser: ParserDefinition = {\n  parse: async (input: string): Promise<void> => {\n    const ast: Info = await parse('info', input);\n    log.debug(ast);\n  },\n};\n", "import type { InfoFields, InfoDB } from './infoTypes.js';\nimport packageJson from '../../../package.json' assert { type: 'json' };\n\nexport const DEFAULT_INFO_DB: InfoFields = { version: packageJson.version } as const;\n\nexport const getVersion = (): string => DEFAULT_INFO_DB.version;\n\nexport const db: InfoDB = {\n  getVersion,\n};\n", "import type { DrawDefinition, SVG, SVGGroup } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport { selectSvgElement } from '../../rendering-util/selectSvgElement.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\n\n/**\n * Draws a an info picture in the tag with id: id based on the graph definition in text.\n *\n * @param text - The text of the diagram.\n * @param id - The id of the diagram which will be used as a DOM element id.\n * @param version - MermaidJS version.\n */\nconst draw: DrawDefinition = (text, id, version) => {\n  log.debug('rendering info diagram\\n' + text);\n\n  const svg: SVG = selectSvgElement(id);\n  configureSvgSize(svg, 100, 400, true);\n\n  const group: SVGGroup = svg.append('g');\n  group\n    .append('text')\n    .attr('x', 100)\n    .attr('y', 40)\n    .attr('class', 'version')\n    .attr('font-size', 32)\n    .style('text-anchor', 'middle')\n    .text(`v${version}`);\n};\n\nexport const renderer = { draw };\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\nimport { parser } from './infoParser.js';\nimport { db } from './infoDb.js';\nimport { renderer } from './infoRenderer.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  db,\n  renderer,\n};\n"], "mappings": "ifAKO,IAAMA,EAA2B,CACtC,MAAOC,EAAA,MAAOC,GAAiC,CAC7C,IAAMC,EAAY,MAAMC,EAAM,OAAQF,CAAK,EAC3CG,EAAI,MAAMF,CAAG,CACf,EAHO,QAIT,ECPO,IAAMG,EAA8B,CAAE,QAASC,EAAY,OAAQ,EAE7DC,EAAaC,EAAA,IAAcH,EAAgB,QAA9B,cAEbI,EAAa,CACxB,WAAAF,CACF,ECGA,IAAMG,EAAuBC,EAAA,CAACC,EAAMC,EAAIC,IAAY,CAClDC,EAAI,MAAM;AAAA,EAA6BH,CAAI,EAE3C,IAAMI,EAAWC,EAAiBJ,CAAE,EACpCK,EAAiBF,EAAK,IAAK,IAAK,EAAI,EAEZA,EAAI,OAAO,GAAG,EAEnC,OAAO,MAAM,EACb,KAAK,IAAK,GAAG,EACb,KAAK,IAAK,EAAE,EACZ,KAAK,QAAS,SAAS,EACvB,KAAK,YAAa,EAAE,EACpB,MAAM,cAAe,QAAQ,EAC7B,KAAK,IAAIF,CAAO,EAAE,CACvB,EAf6B,QAiBhBK,EAAW,CAAE,KAAAT,CAAK,ECxBxB,IAAMU,EAA6B,CACxC,OAAAC,EACA,GAAAC,EACA,SAAAC,CACF", "names": ["parser", "__name", "input", "ast", "parse", "log", "DEFAULT_INFO_DB", "package_default", "getVersion", "__name", "db", "draw", "__name", "text", "id", "version", "log", "svg", "selectSvgElement", "configureSvgSize", "renderer", "diagram", "parser", "db", "renderer"]}