import { CallHierarchyItem, CallHierarchyPrepareParams, CallHierarchyIncomingCallsParams, CallHierarchyIncomingCall, CallHierarchyOutgoingCallsParams, CallHierarchyOutgoingCall, Disposable } from 'vscode-languageserver-protocol';
import type { Feature, _Languages, ServerRequestHandler } from './server';
/**
 * Shape of the call hierarchy feature
 *
 * @since 3.16.0
 */
export interface CallHierarchy {
    callHierarchy: {
        onPrepare(handler: <PERSON><PERSON><PERSON><PERSON>Handler<CallHierarchyPrepareParams, CallHierarchyItem[] | null, never, void>): Disposable;
        onIncomingCalls(handler: <PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON><CallHierarchyIncomingCallsParams, CallHierarchyIncomingCall[] | null, CallHierarchyIncomingCall[], void>): Disposable;
        onOutgoingCalls(handler: <PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON><CallHierarchyOutgoingCallsParams, CallHierarchyOutgoingCall[] | null, CallHierarchyOutgoingCall[], void>): Disposable;
    };
}
export declare const CallHierarchyFeature: Feature<_Languages, CallHierarchy>;
