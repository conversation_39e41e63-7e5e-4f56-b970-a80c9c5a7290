{"name": "@antfu/install-pkg", "type": "module", "version": "1.0.0", "description": "Install package programmatically.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/install-pkg#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/install-pkg.git"}, "bugs": {"url": "https://github.com/antfu/install-pkg/issues"}, "sideEffects": false, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "dependencies": {"package-manager-detector": "^0.2.8", "tinyexec": "^0.3.2"}, "devDependencies": {"@antfu/eslint-config": "^3.12.2", "@antfu/ni": "^23.2.0", "@types/node": "^22.10.5", "bumpp": "^9.10.0", "eslint": "^9.17.0", "publint": "^0.3.0", "tsup": "^8.3.5", "tsx": "^4.19.2", "typescript": "^5.7.3"}, "scripts": {"dev": "nr build --watch", "start": "tsx src/index.ts", "build": "tsup src/index.ts --format cjs,esm --dts --no-splitting", "release": "bumpp --commit --push --tag && pnpm publish", "lint": "eslint ."}}