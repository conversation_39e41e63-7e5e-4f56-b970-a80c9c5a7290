{"version": 3, "file": "cst-node-builder.js", "sourceRoot": "", "sources": ["../../src/parser/cst-node-builder.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAMhF,OAAO,EAAE,QAAQ,EAAE,MAAM,6BAA6B,CAAC;AACvD,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;AAErD,MAAM,OAAO,cAAc;IAA3B;QAGY,cAAS,GAA2B,EAAE,CAAC;IAwFnD,CAAC;IAtFG,IAAI,OAAO;;QACP,OAAO,MAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,mCAAI,IAAI,CAAC,QAAQ,CAAC;IACtE,CAAC;IAED,aAAa,CAAC,KAAa;QACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;QAC3C,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,kBAAkB,CAAC,OAAwB;QACvC,MAAM,aAAa,GAAG,IAAI,oBAAoB,EAAE,CAAC;QACjD,aAAa,CAAC,aAAa,GAAG,OAAO,CAAC;QACtC,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;QACnC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACzC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACnC,OAAO,aAAa,CAAC;IACzB,CAAC;IAED,aAAa,CAAC,KAAa,EAAE,OAAyB;QAClD,MAAM,QAAQ,GAAG,IAAI,eAAe,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC;QAC5H,QAAQ,CAAC,aAAa,GAAG,OAAO,CAAC;QACjC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC9B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpC,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,UAAU,CAAC,IAAa;QACpB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;QAC9B,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC3C,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;gBACb,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACpC,CAAC;QACL,CAAC;IACL,CAAC;IAED,cAAc,CAAC,MAAgB;QAC3B,MAAM,KAAK,GAAkB,EAAE,CAAC;QAChC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YACzB,MAAM,QAAQ,GAAG,IAAI,eAAe,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YACxH,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC9B,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzB,CAAC;QACD,IAAI,OAAO,GAAqB,IAAI,CAAC,OAAO,CAAC;QAC7C,IAAI,KAAK,GAAG,KAAK,CAAC;QAClB,4EAA4E;QAC5E,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;YAC/B,OAAO;QACX,CAAC;QACD,2CAA2C;QAC3C,4FAA4F;QAC5F,OAAO,OAAO,CAAC,SAAS,EAAE,CAAC;YACvB,MAAM,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACzD,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;gBACZ,+CAA+C;gBAC/C,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC;gBACrD,KAAK,GAAG,IAAI,CAAC;gBACb,MAAM;YACV,CAAC;YACD,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC;QAChC,CAAC;QACD,0EAA0E;QAC1E,uEAAuE;QACvE,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,CAAC;QAC5C,CAAC;IACL,CAAC;IAED,SAAS,CAAC,IAA+D;QACrE,MAAM,OAAO,GAAY,IAAI,CAAC,OAAO,CAAC;QACtC,8FAA8F;QAC9F,gEAAgE;QAChE,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;YACjC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAY,IAAI,CAAC;QACzC,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;QAClC,sCAAsC;QACtC,uCAAuC;QACvC,IAAI,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,OAAO,CAAC,MAAM,MAAK,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC1B,CAAC;IACL,CAAC;CACJ;AAED,MAAM,OAAgB,eAAe;IAWjC,2CAA2C;IAC3C,IAAI,MAAM;QACN,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,+CAA+C;IAC/C,IAAI,OAAO;QACP,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,IAAI,MAAM;QACN,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,IAAI,OAAO;;QACP,MAAM,IAAI,GAAG,OAAO,CAAA,MAAA,IAAI,CAAC,QAAQ,0CAAE,KAAK,CAAA,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAA,IAAI,CAAC,SAAS,0CAAE,OAAO,CAAC;QAChG,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC/D,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,IAAI,OAAO,CAAC,KAA0B;QAClC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;IAED,yCAAyC;IACzC,IAAI,OAAO;QACP,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAI,IAAI;QACJ,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IAC/D,CAAC;CACJ;AAED,MAAM,OAAO,eAAgB,SAAQ,eAAe;IAChD,IAAI,MAAM;QACN,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAI,MAAM;QACN,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAI,GAAG;QACH,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;IACvC,CAAC;IAED,IAAa,MAAM;QACf,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAI,SAAS;QACT,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,IAAI,KAAK;QACL,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAQD,YAAY,MAAc,EAAE,MAAc,EAAE,KAAY,EAAE,SAAoB,EAAE,MAAM,GAAG,KAAK;QAC1F,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;CACJ;AAED,MAAM,OAAO,oBAAqB,SAAQ,eAAe;IAAzD;;QACa,YAAO,GAAc,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAqD7D,CAAC;IAlDG,yCAAyC;IACzC,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAI,MAAM;;QACN,OAAO,MAAA,MAAA,IAAI,CAAC,kBAAkB,0CAAE,MAAM,mCAAI,CAAC,CAAC;IAChD,CAAC;IAED,IAAI,MAAM;QACN,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;IAClC,CAAC;IAED,IAAI,GAAG;;QACH,OAAO,MAAA,MAAA,IAAI,CAAC,iBAAiB,0CAAE,GAAG,mCAAI,CAAC,CAAC;IAC5C,CAAC;IAED,IAAI,KAAK;QACL,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACxC,IAAI,SAAS,IAAI,QAAQ,EAAE,CAAC;YACxB,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;gBACjC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,SAAS,CAAC;gBACxC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,QAAQ,CAAC;gBACtC,IAAI,CAAC,WAAW,GAAG,EAAE,KAAK,EAAE,UAAU,CAAC,KAAK,EAAE,GAAG,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;YACvI,CAAC;YACD,OAAO,IAAI,CAAC,WAAW,CAAC;QAC5B,CAAC;aAAM,CAAC;YACJ,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QACxE,CAAC;IACL,CAAC;IAED,IAAY,kBAAkB;QAC1B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAC/B,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBAChB,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED,IAAY,iBAAiB;QACzB,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBAChB,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACjD,CAAC;CACJ;AAED,MAAM,gBAAiB,SAAQ,KAAc;IAGzC,YAAY,MAAwB;QAChC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,SAAS,CAAC,CAAC;IAC5D,CAAC;IAEQ,IAAI,CAAC,GAAG,KAAgB;QAC7B,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACvB,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;IAChC,CAAC;IAEQ,OAAO,CAAC,GAAG,KAAgB;QAChC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACvB,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,CAAC;IACnC,CAAC;IAEQ,MAAM,CAAC,KAAa,EAAE,KAAa,EAAE,GAAG,KAAgB;QAC7D,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACvB,OAAO,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC,CAAC;IAChD,CAAC;IAEO,UAAU,CAAC,KAAgB;QAC/B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACL,IAAK,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;QACpD,CAAC;IACL,CAAC;CACJ;AAED,MAAM,OAAO,eAAgB,SAAQ,oBAAoB;IAGrD,IAAa,IAAI;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IACvD,CAAC;IAED,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,YAAY,KAAc;QACtB,KAAK,EAAE,CAAC;QAXJ,UAAK,GAAG,EAAE,CAAC;QAYf,IAAI,CAAC,KAAK,GAAG,KAAK,aAAL,KAAK,cAAL,KAAK,GAAI,EAAE,CAAC;IAC7B,CAAC;CACJ"}