{"version": 3, "sources": ["../../../src/rendering-util/createText.ts", "../../../src/rendering-util/handle-markdown-text.ts", "../../../src/rendering-util/splitText.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\n// @ts-nocheck TODO: Fix types\nimport { getConfig } from '../diagram-api/diagramAPI.js';\nimport common, { hasKatex, renderKatex } from '../diagrams/common/common.js';\nimport { select } from 'd3';\nimport type { MermaidConfig } from '../config.type.js';\nimport type { SVGGroup } from '../diagram-api/types.js';\nimport type { D3TSpanElement, D3TextElement } from '../diagrams/common/commonTypes.js';\nimport { log } from '../logger.js';\nimport { markdownToHTML, markdownToLines } from '../rendering-util/handle-markdown-text.js';\nimport { decodeEntities } from '../utils.js';\nimport { splitLineToFitWidth } from './splitText.js';\nimport type { MarkdownLine, MarkdownWord } from './types.js';\n\nfunction applyStyle(dom, styleFn) {\n  if (styleFn) {\n    dom.attr('style', styleFn);\n  }\n}\n\nasync function addHtmlSpan(element, node, width, classes, addBackground = false) {\n  const fo = element.append('foreignObject');\n  // This is not the final width but used in order to make sure the foreign\n  // object in firefox gets a width at all. The final width is fetched from the div\n  fo.attr('width', `${10 * width}px`);\n  fo.attr('height', `${10 * width}px`);\n\n  const div = fo.append('xhtml:div');\n  let label = node.label;\n  if (node.label && hasKatex(node.label)) {\n    label = await renderKatex(node.label.replace(common.lineBreakRegex, '\\n'), getConfig());\n  }\n  const labelClass = node.isNode ? 'nodeLabel' : 'edgeLabel';\n  const span = div.append('span');\n  span.html(label);\n  applyStyle(span, node.labelStyle);\n  span.attr('class', `${labelClass} ${classes}`);\n\n  applyStyle(div, node.labelStyle);\n  div.style('display', 'table-cell');\n  div.style('white-space', 'nowrap');\n  div.style('line-height', '1.5');\n  div.style('max-width', width + 'px');\n  div.style('text-align', 'center');\n  div.attr('xmlns', 'http://www.w3.org/1999/xhtml');\n  if (addBackground) {\n    div.attr('class', 'labelBkg');\n  }\n\n  let bbox = div.node().getBoundingClientRect();\n  if (bbox.width === width) {\n    div.style('display', 'table');\n    div.style('white-space', 'break-spaces');\n    div.style('width', width + 'px');\n    bbox = div.node().getBoundingClientRect();\n  }\n\n  // fo.style('width', bbox.width);\n  // fo.style('height', bbox.height);\n\n  return fo.node();\n}\n\n/**\n * Creates a tspan element with the specified attributes for text positioning.\n *\n * @param textElement - The parent text element to append the tspan element.\n * @param lineIndex - The index of the current line in the structuredText array.\n * @param lineHeight - The line height value for the text.\n * @returns The created tspan element.\n */\nfunction createTspan(textElement: any, lineIndex: number, lineHeight: number) {\n  return textElement\n    .append('tspan')\n    .attr('class', 'text-outer-tspan')\n    .attr('x', 0)\n    .attr('y', lineIndex * lineHeight - 0.1 + 'em')\n    .attr('dy', lineHeight + 'em');\n}\n\nfunction computeWidthOfText(parentNode: any, lineHeight: number, line: MarkdownLine): number {\n  const testElement = parentNode.append('text');\n  const testSpan = createTspan(testElement, 1, lineHeight);\n  updateTextContentAndStyles(testSpan, line);\n  const textLength = testSpan.node().getComputedTextLength();\n  testElement.remove();\n  return textLength;\n}\n\nexport function computeDimensionOfText(\n  parentNode: SVGGroup,\n  lineHeight: number,\n  text: string\n): DOMRect | undefined {\n  const testElement: D3TextElement = parentNode.append('text');\n  const testSpan: D3TSpanElement = createTspan(testElement, 1, lineHeight);\n  updateTextContentAndStyles(testSpan, [{ content: text, type: 'normal' }]);\n  const textDimension: DOMRect | undefined = testSpan.node()?.getBoundingClientRect();\n  if (textDimension) {\n    testElement.remove();\n  }\n  return textDimension;\n}\n\n/**\n * Creates a formatted text element by breaking lines and applying styles based on\n * the given structuredText.\n *\n * @param width - The maximum allowed width of the text.\n * @param g - The parent group element to append the formatted text.\n * @param structuredText - The structured text data to format.\n * @param addBackground - Whether to add a background to the text.\n */\nfunction createFormattedText(\n  width: number,\n  g: any,\n  structuredText: MarkdownWord[][],\n  addBackground = false\n) {\n  const lineHeight = 1.1;\n  const labelGroup = g.append('g');\n  const bkg = labelGroup.insert('rect').attr('class', 'background').attr('style', 'stroke: none');\n  const textElement = labelGroup.append('text').attr('y', '-10.1');\n  let lineIndex = 0;\n  for (const line of structuredText) {\n    /**\n     * Preprocess raw string content of line data\n     * Creating an array of strings pre-split to satisfy width limit\n     */\n    const checkWidth = (line: MarkdownLine) =>\n      computeWidthOfText(labelGroup, lineHeight, line) <= width;\n    const linesUnderWidth = checkWidth(line) ? [line] : splitLineToFitWidth(line, checkWidth);\n    /** Add each prepared line as a tspan to the parent node */\n    for (const preparedLine of linesUnderWidth) {\n      const tspan = createTspan(textElement, lineIndex, lineHeight);\n      updateTextContentAndStyles(tspan, preparedLine);\n      lineIndex++;\n    }\n  }\n  if (addBackground) {\n    const bbox = textElement.node().getBBox();\n    const padding = 2;\n    bkg\n      .attr('x', bbox.x - padding)\n      .attr('y', bbox.y - padding)\n      .attr('width', bbox.width + 2 * padding)\n      .attr('height', bbox.height + 2 * padding);\n\n    return labelGroup.node();\n  } else {\n    return textElement.node();\n  }\n}\n\n/**\n * Updates the text content and styles of the given tspan element based on the\n * provided wrappedLine data.\n *\n * @param tspan - The tspan element to update.\n * @param wrappedLine - The line data to apply to the tspan element.\n */\nfunction updateTextContentAndStyles(tspan: any, wrappedLine: MarkdownWord[]) {\n  tspan.text('');\n\n  wrappedLine.forEach((word, index) => {\n    const innerTspan = tspan\n      .append('tspan')\n      .attr('font-style', word.type === 'em' ? 'italic' : 'normal')\n      .attr('class', 'text-inner-tspan')\n      .attr('font-weight', word.type === 'strong' ? 'bold' : 'normal');\n    if (index === 0) {\n      innerTspan.text(word.content);\n    } else {\n      // TODO: check what joiner to use.\n      innerTspan.text(' ' + word.content);\n    }\n  });\n}\n\n/**\n * Convert fontawesome labels into fontawesome icons by using a regex pattern\n * @param text - The raw string to convert\n * @returns string with fontawesome icons as i tags\n */\nexport function replaceIconSubstring(text: string) {\n  // The letters 'bklrs' stand for possible endings of the fontawesome prefix (e.g. 'fab' for brands, 'fak' for fa-kit) // cspell: disable-line\n  return text.replace(\n    /fa[bklrs]?:fa-[\\w-]+/g, // cspell: disable-line\n    (s) => `<i class='${s.replace(':', ' ')}'></i>`\n  );\n}\n\n// Note when using from flowcharts converting the API isNode means classes should be set accordingly. When using htmlLabels => to sett classes to'nodeLabel' when isNode=true otherwise 'edgeLabel'\n// When not using htmlLabels => to set classes to 'title-row' when isTitle=true otherwise 'title-row'\nexport const createText = async (\n  el,\n  text = '',\n  {\n    style = '',\n    isTitle = false,\n    classes = '',\n    useHtmlLabels = true,\n    isNode = true,\n    width = 200,\n    addSvgBackground = false,\n  } = {},\n  config?: MermaidConfig\n) => {\n  log.debug(\n    'XYZ createText',\n    text,\n    style,\n    isTitle,\n    classes,\n    useHtmlLabels,\n    isNode,\n    'addSvgBackground: ',\n    addSvgBackground\n  );\n  if (useHtmlLabels) {\n    // TODO: addHtmlLabel accepts a labelStyle. Do we possibly have that?\n\n    const htmlText = markdownToHTML(text, config);\n    const decodedReplacedText = replaceIconSubstring(decodeEntities(htmlText));\n\n    //for Katex the text could contain escaped characters, \\\\relax that should be transformed to \\relax\n    const inputForKatex = text.replace(/\\\\\\\\/g, '\\\\');\n\n    const node = {\n      isNode,\n      label: hasKatex(text) ? inputForKatex : decodedReplacedText,\n      labelStyle: style.replace('fill:', 'color:'),\n    };\n    const vertexNode = await addHtmlSpan(el, node, width, classes, addSvgBackground);\n    return vertexNode;\n  } else {\n    //sometimes the user might add br tags with 1 or more spaces in between, so we need to replace them with <br/>\n    const sanitizeBR = text.replace(/<br\\s*\\/?>/g, '<br/>');\n    const structuredText = markdownToLines(sanitizeBR.replace('<br>', '<br/>'), config);\n    const svgLabel = createFormattedText(\n      width,\n      el,\n      structuredText,\n      text ? addSvgBackground : false\n    );\n    if (isNode) {\n      if (/stroke:/.exec(style)) {\n        style = style.replace('stroke:', 'lineColor:');\n      }\n\n      const nodeLabelTextStyle = style\n        .replace(/stroke:[^;]+;?/g, '')\n        .replace(/stroke-width:[^;]+;?/g, '')\n        .replace(/fill:[^;]+;?/g, '')\n        .replace(/color:/g, 'fill:');\n      select(svgLabel).attr('style', nodeLabelTextStyle);\n      // svgLabel.setAttribute('style', style);\n    } else {\n      //On style, assume `stroke`, `stroke-width` are used for edge path, so remove them\n      // remove `fill`\n      //  use  `background` as `fill` for label rect,\n\n      const edgeLabelRectStyle = style\n        .replace(/stroke:[^;]+;?/g, '')\n        .replace(/stroke-width:[^;]+;?/g, '')\n        .replace(/fill:[^;]+;?/g, '')\n        .replace(/background:/g, 'fill:');\n      select(svgLabel)\n        .select('rect')\n        .attr('style', edgeLabelRectStyle.replace(/background:/g, 'fill:'));\n\n      // for text, update fill color with `color`\n      const edgeLabelTextStyle = style\n        .replace(/stroke:[^;]+;?/g, '')\n        .replace(/stroke-width:[^;]+;?/g, '')\n        .replace(/fill:[^;]+;?/g, '')\n        .replace(/color:/g, 'fill:');\n      select(svgLabel).select('text').attr('style', edgeLabelTextStyle);\n    }\n    return svgLabel;\n  }\n};\n", "import type { MarkedToken, Token } from 'marked';\nimport { marked } from 'marked';\nimport { dedent } from 'ts-dedent';\nimport type { MarkdownLine, MarkdownWordType } from './types.js';\nimport type { MermaidConfig } from '../config.type.js';\n\n/**\n * @param markdown - markdown to process\n * @returns processed markdown\n */\nfunction preprocessMarkdown(markdown: string, { markdownAutoWrap }: MermaidConfig): string {\n  //Replace <br/>with \\n\n  const withoutBR = markdown.replace(/<br\\/>/g, '\\n');\n  // Replace multiple newlines with a single newline\n  const withoutMultipleNewlines = withoutBR.replace(/\\n{2,}/g, '\\n');\n  // Remove extra spaces at the beginning of each line\n  const withoutExtraSpaces = dedent(withoutMultipleNewlines);\n  if (markdownAutoWrap === false) {\n    return withoutExtraSpaces.replace(/ /g, '&nbsp;');\n  }\n  return withoutExtraSpaces;\n}\n\n/**\n * @param markdown - markdown to split into lines\n */\nexport function markdownToLines(markdown: string, config: MermaidConfig = {}): MarkdownLine[] {\n  const preprocessedMarkdown = preprocessMarkdown(markdown, config);\n  const nodes = marked.lexer(preprocessedMarkdown);\n  const lines: MarkdownLine[] = [[]];\n  let currentLine = 0;\n\n  function processNode(node: MarkedToken, parentType: MarkdownWordType = 'normal') {\n    if (node.type === 'text') {\n      const textLines = node.text.split('\\n');\n      textLines.forEach((textLine, index) => {\n        if (index !== 0) {\n          currentLine++;\n          lines.push([]);\n        }\n        textLine.split(' ').forEach((word) => {\n          word = word.replace(/&#39;/g, `'`);\n          if (word) {\n            lines[currentLine].push({ content: word, type: parentType });\n          }\n        });\n      });\n    } else if (node.type === 'strong' || node.type === 'em') {\n      node.tokens.forEach((contentNode) => {\n        processNode(contentNode as MarkedToken, node.type);\n      });\n    } else if (node.type === 'html') {\n      lines[currentLine].push({ content: node.text, type: 'normal' });\n    }\n  }\n\n  nodes.forEach((treeNode) => {\n    if (treeNode.type === 'paragraph') {\n      treeNode.tokens?.forEach((contentNode) => {\n        processNode(contentNode as MarkedToken);\n      });\n    } else if (treeNode.type === 'html') {\n      lines[currentLine].push({ content: treeNode.text, type: 'normal' });\n    }\n  });\n\n  return lines;\n}\n\nexport function markdownToHTML(markdown: string, { markdownAutoWrap }: MermaidConfig = {}) {\n  const nodes = marked.lexer(markdown);\n\n  function output(node: Token): string {\n    if (node.type === 'text') {\n      if (markdownAutoWrap === false) {\n        return node.text.replace(/\\n */g, '<br/>').replace(/ /g, '&nbsp;');\n      }\n      return node.text.replace(/\\n */g, '<br/>');\n    } else if (node.type === 'strong') {\n      return `<strong>${node.tokens?.map(output).join('')}</strong>`;\n    } else if (node.type === 'em') {\n      return `<em>${node.tokens?.map(output).join('')}</em>`;\n    } else if (node.type === 'paragraph') {\n      return `<p>${node.tokens?.map(output).join('')}</p>`;\n    } else if (node.type === 'space') {\n      return '';\n    } else if (node.type === 'html') {\n      return `${node.text}`;\n    } else if (node.type === 'escape') {\n      return node.text;\n    }\n    return `Unsupported markdown: ${node.type}`;\n  }\n\n  return nodes.map(output).join('');\n}\n", "import type { CheckFitFunction, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MarkdownWordType } from './types.js';\n\n/**\n * Splits a string into graphemes if available, otherwise characters.\n */\nexport function splitTextToChars(text: string): string[] {\n  if (Intl.Segmenter) {\n    return [...new Intl.Segmenter().segment(text)].map((s) => s.segment);\n  }\n  return [...text];\n}\n\n/**\n * Splits a string into words by using `Intl.Segmenter` if available, or splitting by ' '.\n * `Intl.Segmenter` uses the default locale, which might be different across browsers.\n */\nexport function splitLineToWords(text: string): string[] {\n  if (Intl.Segmenter) {\n    return [...new Intl.Segmenter(undefined, { granularity: 'word' }).segment(text)].map(\n      (s) => s.segment\n    );\n  }\n  // Split by ' ' removes the ' 's from the result.\n  const words = text.split(' ');\n  // Add the ' 's back to the result.\n  const wordsWithSpaces = words.flatMap((s) => [s, ' ']).filter((s) => s);\n  // Remove last space.\n  wordsWithSpaces.pop();\n  return wordsWithSpaces;\n}\n\n/**\n * Splits a word into two parts, the first part fits the width and the remaining part.\n * @param checkFit - Function to check if word fits\n * @param word - Word to split\n * @returns [first part of word that fits, rest of word]\n */\nexport function splitWordToFitWidth(\n  checkFit: CheckFitFunction,\n  word: MarkdownWord\n): [MarkdownWord, MarkdownWord] {\n  const characters = splitTextToChars(word.content);\n  return splitWordToFitWidthRecursion(checkFit, [], characters, word.type);\n}\n\nfunction splitWordToFitWidthRecursion(\n  checkFit: CheckFitFunction,\n  usedChars: string[],\n  remainingChars: string[],\n  type: MarkdownWordType\n): [MarkdownWord, MarkdownWord] {\n  if (remainingChars.length === 0) {\n    return [\n      { content: usedChars.join(''), type },\n      { content: '', type },\n    ];\n  }\n  const [nextChar, ...rest] = remainingChars;\n  const newWord = [...usedChars, nextChar];\n  if (checkFit([{ content: newWord.join(''), type }])) {\n    return splitWordToFitWidthRecursion(checkFit, newWord, rest, type);\n  }\n  if (usedChars.length === 0 && nextChar) {\n    // If the first character does not fit, split it anyway\n    usedChars.push(nextChar);\n    remainingChars.shift();\n  }\n  return [\n    { content: usedChars.join(''), type },\n    { content: remainingChars.join(''), type },\n  ];\n}\n\n/**\n * Splits a line into multiple lines that satisfy the checkFit function.\n * @param line - Line to split\n * @param checkFit - Function to check if line fits\n * @returns Array of lines that fit\n */\nexport function splitLineToFitWidth(\n  line: MarkdownLine,\n  checkFit: CheckFitFunction\n): MarkdownLine[] {\n  if (line.some(({ content }) => content.includes('\\n'))) {\n    throw new Error('splitLineToFitWidth does not support newlines in the line');\n  }\n  return splitLineToFitWidthRecursion(line, checkFit);\n}\n\nfunction splitLineToFitWidthRecursion(\n  words: MarkdownWord[],\n  checkFit: CheckFitFunction,\n  lines: MarkdownLine[] = [],\n  newLine: MarkdownLine = []\n): MarkdownLine[] {\n  // Return if there is nothing left to split\n  if (words.length === 0) {\n    // If there is a new line, add it to the lines\n    if (newLine.length > 0) {\n      lines.push(newLine);\n    }\n    return lines.length > 0 ? lines : [];\n  }\n  let joiner = '';\n  if (words[0].content === ' ') {\n    joiner = ' ';\n    words.shift();\n  }\n  const nextWord: MarkdownWord = words.shift() ?? { content: ' ', type: 'normal' };\n  const lineWithNextWord: MarkdownLine = [...newLine];\n  if (joiner !== '') {\n    lineWithNextWord.push({ content: joiner, type: 'normal' });\n  }\n  lineWithNextWord.push(nextWord);\n\n  if (checkFit(lineWithNextWord)) {\n    // nextWord fits, so we can add it to the new line and continue\n    return splitLineToFitWidthRecursion(words, checkFit, lines, lineWithNextWord);\n  }\n\n  // nextWord doesn't fit, so we need to split it\n  if (newLine.length > 0) {\n    // There was text in newLine, so add it to lines and push nextWord back into words.\n    lines.push(newLine);\n    words.unshift(nextWord);\n  } else if (nextWord.content) {\n    // There was no text in newLine, so we need to split nextWord\n    const [line, rest] = splitWordToFitWidth(checkFit, nextWord);\n    lines.push([line]);\n    if (rest.content) {\n      words.unshift(rest);\n    }\n  }\n  return splitLineToFitWidthRecursion(words, checkFit, lines);\n}\n"], "mappings": ";;;;;;;;;;;;;AAIA,SAAS,cAAc;;;ACHvB,SAAS,cAAc;AACvB,SAAS,cAAc;AAQvB,SAAS,mBAAmB,UAAkB,EAAE,iBAAiB,GAA0B;AAEzF,QAAM,YAAY,SAAS,QAAQ,WAAW,IAAI;AAElD,QAAM,0BAA0B,UAAU,QAAQ,WAAW,IAAI;AAEjE,QAAM,qBAAqB,OAAO,uBAAuB;AACzD,MAAI,qBAAqB,OAAO;AAC9B,WAAO,mBAAmB,QAAQ,MAAM,QAAQ;AAAA,EAClD;AACA,SAAO;AACT;AAXS;AAgBF,SAAS,gBAAgB,UAAkB,SAAwB,CAAC,GAAmB;AAC5F,QAAM,uBAAuB,mBAAmB,UAAU,MAAM;AAChE,QAAM,QAAQ,OAAO,MAAM,oBAAoB;AAC/C,QAAM,QAAwB,CAAC,CAAC,CAAC;AACjC,MAAI,cAAc;AAElB,WAAS,YAAY,MAAmB,aAA+B,UAAU;AAC/E,QAAI,KAAK,SAAS,QAAQ;AACxB,YAAM,YAAY,KAAK,KAAK,MAAM,IAAI;AACtC,gBAAU,QAAQ,CAAC,UAAU,UAAU;AACrC,YAAI,UAAU,GAAG;AACf;AACA,gBAAM,KAAK,CAAC,CAAC;AAAA,QACf;AACA,iBAAS,MAAM,GAAG,EAAE,QAAQ,CAAC,SAAS;AACpC,iBAAO,KAAK,QAAQ,UAAU,GAAG;AACjC,cAAI,MAAM;AACR,kBAAM,WAAW,EAAE,KAAK,EAAE,SAAS,MAAM,MAAM,WAAW,CAAC;AAAA,UAC7D;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,WAAW,KAAK,SAAS,YAAY,KAAK,SAAS,MAAM;AACvD,WAAK,OAAO,QAAQ,CAAC,gBAAgB;AACnC,oBAAY,aAA4B,KAAK,IAAI;AAAA,MACnD,CAAC;AAAA,IACH,WAAW,KAAK,SAAS,QAAQ;AAC/B,YAAM,WAAW,EAAE,KAAK,EAAE,SAAS,KAAK,MAAM,MAAM,SAAS,CAAC;AAAA,IAChE;AAAA,EACF;AAtBS;AAwBT,QAAM,QAAQ,CAAC,aAAa;AAC1B,QAAI,SAAS,SAAS,aAAa;AACjC,eAAS,QAAQ,QAAQ,CAAC,gBAAgB;AACxC,oBAAY,WAA0B;AAAA,MACxC,CAAC;AAAA,IACH,WAAW,SAAS,SAAS,QAAQ;AACnC,YAAM,WAAW,EAAE,KAAK,EAAE,SAAS,SAAS,MAAM,MAAM,SAAS,CAAC;AAAA,IACpE;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAzCgB;AA2CT,SAAS,eAAe,UAAkB,EAAE,iBAAiB,IAAmB,CAAC,GAAG;AACzF,QAAM,QAAQ,OAAO,MAAM,QAAQ;AAEnC,WAAS,OAAO,MAAqB;AACnC,QAAI,KAAK,SAAS,QAAQ;AACxB,UAAI,qBAAqB,OAAO;AAC9B,eAAO,KAAK,KAAK,QAAQ,SAAS,OAAO,EAAE,QAAQ,MAAM,QAAQ;AAAA,MACnE;AACA,aAAO,KAAK,KAAK,QAAQ,SAAS,OAAO;AAAA,IAC3C,WAAW,KAAK,SAAS,UAAU;AACjC,aAAO,WAAW,KAAK,QAAQ,IAAI,MAAM,EAAE,KAAK,EAAE,CAAC;AAAA,IACrD,WAAW,KAAK,SAAS,MAAM;AAC7B,aAAO,OAAO,KAAK,QAAQ,IAAI,MAAM,EAAE,KAAK,EAAE,CAAC;AAAA,IACjD,WAAW,KAAK,SAAS,aAAa;AACpC,aAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,EAAE,KAAK,EAAE,CAAC;AAAA,IAChD,WAAW,KAAK,SAAS,SAAS;AAChC,aAAO;AAAA,IACT,WAAW,KAAK,SAAS,QAAQ;AAC/B,aAAO,GAAG,KAAK,IAAI;AAAA,IACrB,WAAW,KAAK,SAAS,UAAU;AACjC,aAAO,KAAK;AAAA,IACd;AACA,WAAO,yBAAyB,KAAK,IAAI;AAAA,EAC3C;AApBS;AAsBT,SAAO,MAAM,IAAI,MAAM,EAAE,KAAK,EAAE;AAClC;AA1BgB;;;AChET,SAAS,iBAAiB,MAAwB;AACvD,MAAI,KAAK,WAAW;AAClB,WAAO,CAAC,GAAG,IAAI,KAAK,UAAU,EAAE,QAAQ,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO;AAAA,EACrE;AACA,SAAO,CAAC,GAAG,IAAI;AACjB;AALgB;AAgCT,SAAS,oBACd,UACA,MAC8B;AAC9B,QAAM,aAAa,iBAAiB,KAAK,OAAO;AAChD,SAAO,6BAA6B,UAAU,CAAC,GAAG,YAAY,KAAK,IAAI;AACzE;AANgB;AAQhB,SAAS,6BACP,UACA,WACA,gBACA,MAC8B;AAC9B,MAAI,eAAe,WAAW,GAAG;AAC/B,WAAO;AAAA,MACL,EAAE,SAAS,UAAU,KAAK,EAAE,GAAG,KAAK;AAAA,MACpC,EAAE,SAAS,IAAI,KAAK;AAAA,IACtB;AAAA,EACF;AACA,QAAM,CAAC,UAAU,GAAG,IAAI,IAAI;AAC5B,QAAM,UAAU,CAAC,GAAG,WAAW,QAAQ;AACvC,MAAI,SAAS,CAAC,EAAE,SAAS,QAAQ,KAAK,EAAE,GAAG,KAAK,CAAC,CAAC,GAAG;AACnD,WAAO,6BAA6B,UAAU,SAAS,MAAM,IAAI;AAAA,EACnE;AACA,MAAI,UAAU,WAAW,KAAK,UAAU;AAEtC,cAAU,KAAK,QAAQ;AACvB,mBAAe,MAAM;AAAA,EACvB;AACA,SAAO;AAAA,IACL,EAAE,SAAS,UAAU,KAAK,EAAE,GAAG,KAAK;AAAA,IACpC,EAAE,SAAS,eAAe,KAAK,EAAE,GAAG,KAAK;AAAA,EAC3C;AACF;AA1BS;AAkCF,SAAS,oBACd,MACA,UACgB;AAChB,MAAI,KAAK,KAAK,CAAC,EAAE,QAAQ,MAAM,QAAQ,SAAS,IAAI,CAAC,GAAG;AACtD,UAAM,IAAI,MAAM,2DAA2D;AAAA,EAC7E;AACA,SAAO,6BAA6B,MAAM,QAAQ;AACpD;AARgB;AAUhB,SAAS,6BACP,OACA,UACA,QAAwB,CAAC,GACzB,UAAwB,CAAC,GACT;AAEhB,MAAI,MAAM,WAAW,GAAG;AAEtB,QAAI,QAAQ,SAAS,GAAG;AACtB,YAAM,KAAK,OAAO;AAAA,IACpB;AACA,WAAO,MAAM,SAAS,IAAI,QAAQ,CAAC;AAAA,EACrC;AACA,MAAI,SAAS;AACb,MAAI,MAAM,CAAC,EAAE,YAAY,KAAK;AAC5B,aAAS;AACT,UAAM,MAAM;AAAA,EACd;AACA,QAAM,WAAyB,MAAM,MAAM,KAAK,EAAE,SAAS,KAAK,MAAM,SAAS;AAC/E,QAAM,mBAAiC,CAAC,GAAG,OAAO;AAClD,MAAI,WAAW,IAAI;AACjB,qBAAiB,KAAK,EAAE,SAAS,QAAQ,MAAM,SAAS,CAAC;AAAA,EAC3D;AACA,mBAAiB,KAAK,QAAQ;AAE9B,MAAI,SAAS,gBAAgB,GAAG;AAE9B,WAAO,6BAA6B,OAAO,UAAU,OAAO,gBAAgB;AAAA,EAC9E;AAGA,MAAI,QAAQ,SAAS,GAAG;AAEtB,UAAM,KAAK,OAAO;AAClB,UAAM,QAAQ,QAAQ;AAAA,EACxB,WAAW,SAAS,SAAS;AAE3B,UAAM,CAAC,MAAM,IAAI,IAAI,oBAAoB,UAAU,QAAQ;AAC3D,UAAM,KAAK,CAAC,IAAI,CAAC;AACjB,QAAI,KAAK,SAAS;AAChB,YAAM,QAAQ,IAAI;AAAA,IACpB;AAAA,EACF;AACA,SAAO,6BAA6B,OAAO,UAAU,KAAK;AAC5D;AA7CS;;;AF3ET,SAAS,WAAW,KAAK,SAAS;AAChC,MAAI,SAAS;AACX,QAAI,KAAK,SAAS,OAAO;AAAA,EAC3B;AACF;AAJS;AAMT,eAAe,YAAY,SAAS,MAAM,OAAO,SAAS,gBAAgB,OAAO;AAC/E,QAAM,KAAK,QAAQ,OAAO,eAAe;AAGzC,KAAG,KAAK,SAAS,GAAG,KAAK,KAAK,IAAI;AAClC,KAAG,KAAK,UAAU,GAAG,KAAK,KAAK,IAAI;AAEnC,QAAM,MAAM,GAAG,OAAO,WAAW;AACjC,MAAI,QAAQ,KAAK;AACjB,MAAI,KAAK,SAAS,SAAS,KAAK,KAAK,GAAG;AACtC,YAAQ,MAAM,YAAY,KAAK,MAAM,QAAQ,eAAO,gBAAgB,IAAI,GAAG,UAAU,CAAC;AAAA,EACxF;AACA,QAAM,aAAa,KAAK,SAAS,cAAc;AAC/C,QAAM,OAAO,IAAI,OAAO,MAAM;AAC9B,OAAK,KAAK,KAAK;AACf,aAAW,MAAM,KAAK,UAAU;AAChC,OAAK,KAAK,SAAS,GAAG,UAAU,IAAI,OAAO,EAAE;AAE7C,aAAW,KAAK,KAAK,UAAU;AAC/B,MAAI,MAAM,WAAW,YAAY;AACjC,MAAI,MAAM,eAAe,QAAQ;AACjC,MAAI,MAAM,eAAe,KAAK;AAC9B,MAAI,MAAM,aAAa,QAAQ,IAAI;AACnC,MAAI,MAAM,cAAc,QAAQ;AAChC,MAAI,KAAK,SAAS,8BAA8B;AAChD,MAAI,eAAe;AACjB,QAAI,KAAK,SAAS,UAAU;AAAA,EAC9B;AAEA,MAAI,OAAO,IAAI,KAAK,EAAE,sBAAsB;AAC5C,MAAI,KAAK,UAAU,OAAO;AACxB,QAAI,MAAM,WAAW,OAAO;AAC5B,QAAI,MAAM,eAAe,cAAc;AACvC,QAAI,MAAM,SAAS,QAAQ,IAAI;AAC/B,WAAO,IAAI,KAAK,EAAE,sBAAsB;AAAA,EAC1C;AAKA,SAAO,GAAG,KAAK;AACjB;AAzCe;AAmDf,SAAS,YAAY,aAAkB,WAAmB,YAAoB;AAC5E,SAAO,YACJ,OAAO,OAAO,EACd,KAAK,SAAS,kBAAkB,EAChC,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,YAAY,aAAa,MAAM,IAAI,EAC7C,KAAK,MAAM,aAAa,IAAI;AACjC;AAPS;AAST,SAAS,mBAAmB,YAAiB,YAAoB,MAA4B;AAC3F,QAAM,cAAc,WAAW,OAAO,MAAM;AAC5C,QAAM,WAAW,YAAY,aAAa,GAAG,UAAU;AACvD,6BAA2B,UAAU,IAAI;AACzC,QAAM,aAAa,SAAS,KAAK,EAAE,sBAAsB;AACzD,cAAY,OAAO;AACnB,SAAO;AACT;AAPS;AASF,SAAS,uBACd,YACA,YACA,MACqB;AACrB,QAAM,cAA6B,WAAW,OAAO,MAAM;AAC3D,QAAM,WAA2B,YAAY,aAAa,GAAG,UAAU;AACvE,6BAA2B,UAAU,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,CAAC;AACxE,QAAM,gBAAqC,SAAS,KAAK,GAAG,sBAAsB;AAClF,MAAI,eAAe;AACjB,gBAAY,OAAO;AAAA,EACrB;AACA,SAAO;AACT;AAbgB;AAwBhB,SAAS,oBACP,OACA,GACA,gBACA,gBAAgB,OAChB;AACA,QAAM,aAAa;AACnB,QAAM,aAAa,EAAE,OAAO,GAAG;AAC/B,QAAM,MAAM,WAAW,OAAO,MAAM,EAAE,KAAK,SAAS,YAAY,EAAE,KAAK,SAAS,cAAc;AAC9F,QAAM,cAAc,WAAW,OAAO,MAAM,EAAE,KAAK,KAAK,OAAO;AAC/D,MAAI,YAAY;AAChB,aAAW,QAAQ,gBAAgB;AAKjC,UAAM,aAAa,wBAACA,UAClB,mBAAmB,YAAY,YAAYA,KAAI,KAAK,OADnC;AAEnB,UAAM,kBAAkB,WAAW,IAAI,IAAI,CAAC,IAAI,IAAI,oBAAoB,MAAM,UAAU;AAExF,eAAW,gBAAgB,iBAAiB;AAC1C,YAAM,QAAQ,YAAY,aAAa,WAAW,UAAU;AAC5D,iCAA2B,OAAO,YAAY;AAC9C;AAAA,IACF;AAAA,EACF;AACA,MAAI,eAAe;AACjB,UAAM,OAAO,YAAY,KAAK,EAAE,QAAQ;AACxC,UAAM,UAAU;AAChB,QACG,KAAK,KAAK,KAAK,IAAI,OAAO,EAC1B,KAAK,KAAK,KAAK,IAAI,OAAO,EAC1B,KAAK,SAAS,KAAK,QAAQ,IAAI,OAAO,EACtC,KAAK,UAAU,KAAK,SAAS,IAAI,OAAO;AAE3C,WAAO,WAAW,KAAK;AAAA,EACzB,OAAO;AACL,WAAO,YAAY,KAAK;AAAA,EAC1B;AACF;AAvCS;AAgDT,SAAS,2BAA2B,OAAY,aAA6B;AAC3E,QAAM,KAAK,EAAE;AAEb,cAAY,QAAQ,CAAC,MAAM,UAAU;AACnC,UAAM,aAAa,MAChB,OAAO,OAAO,EACd,KAAK,cAAc,KAAK,SAAS,OAAO,WAAW,QAAQ,EAC3D,KAAK,SAAS,kBAAkB,EAChC,KAAK,eAAe,KAAK,SAAS,WAAW,SAAS,QAAQ;AACjE,QAAI,UAAU,GAAG;AACf,iBAAW,KAAK,KAAK,OAAO;AAAA,IAC9B,OAAO;AAEL,iBAAW,KAAK,MAAM,KAAK,OAAO;AAAA,IACpC;AAAA,EACF,CAAC;AACH;AAhBS;AAuBF,SAAS,qBAAqB,MAAc;AAEjD,SAAO,KAAK;AAAA,IACV;AAAA;AAAA,IACA,CAAC,MAAM,aAAa,EAAE,QAAQ,KAAK,GAAG,CAAC;AAAA,EACzC;AACF;AANgB;AAUT,IAAM,aAAa,8BACxB,IACA,OAAO,IACP;AAAA,EACE,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,mBAAmB;AACrB,IAAI,CAAC,GACL,WACG;AACH,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,MAAI,eAAe;AAGjB,UAAM,WAAW,eAAe,MAAM,MAAM;AAC5C,UAAM,sBAAsB,qBAAqB,eAAe,QAAQ,CAAC;AAGzE,UAAM,gBAAgB,KAAK,QAAQ,SAAS,IAAI;AAEhD,UAAM,OAAO;AAAA,MACX;AAAA,MACA,OAAO,SAAS,IAAI,IAAI,gBAAgB;AAAA,MACxC,YAAY,MAAM,QAAQ,SAAS,QAAQ;AAAA,IAC7C;AACA,UAAM,aAAa,MAAM,YAAY,IAAI,MAAM,OAAO,SAAS,gBAAgB;AAC/E,WAAO;AAAA,EACT,OAAO;AAEL,UAAM,aAAa,KAAK,QAAQ,eAAe,OAAO;AACtD,UAAM,iBAAiB,gBAAgB,WAAW,QAAQ,QAAQ,OAAO,GAAG,MAAM;AAClF,UAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO,mBAAmB;AAAA,IAC5B;AACA,QAAI,QAAQ;AACV,UAAI,UAAU,KAAK,KAAK,GAAG;AACzB,gBAAQ,MAAM,QAAQ,WAAW,YAAY;AAAA,MAC/C;AAEA,YAAM,qBAAqB,MACxB,QAAQ,mBAAmB,EAAE,EAC7B,QAAQ,yBAAyB,EAAE,EACnC,QAAQ,iBAAiB,EAAE,EAC3B,QAAQ,WAAW,OAAO;AAC7B,aAAO,QAAQ,EAAE,KAAK,SAAS,kBAAkB;AAAA,IAEnD,OAAO;AAKL,YAAM,qBAAqB,MACxB,QAAQ,mBAAmB,EAAE,EAC7B,QAAQ,yBAAyB,EAAE,EACnC,QAAQ,iBAAiB,EAAE,EAC3B,QAAQ,gBAAgB,OAAO;AAClC,aAAO,QAAQ,EACZ,OAAO,MAAM,EACb,KAAK,SAAS,mBAAmB,QAAQ,gBAAgB,OAAO,CAAC;AAGpE,YAAM,qBAAqB,MACxB,QAAQ,mBAAmB,EAAE,EAC7B,QAAQ,yBAAyB,EAAE,EACnC,QAAQ,iBAAiB,EAAE,EAC3B,QAAQ,WAAW,OAAO;AAC7B,aAAO,QAAQ,EAAE,OAAO,MAAM,EAAE,KAAK,SAAS,kBAAkB;AAAA,IAClE;AACA,WAAO;AAAA,EACT;AACF,GAvF0B;", "names": ["line"]}