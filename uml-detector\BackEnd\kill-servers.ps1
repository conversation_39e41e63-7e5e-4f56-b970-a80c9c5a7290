# Tuer tous les processus Python (pour le serveur FastAPI)
Get-Process -Name python* | Where-Object { $_.MainWindowTitle -match "server.py" -or $_.CommandLine -match "server.py" } | Stop-Process -Force

# Tuer tous les processus Node.js (pour les serveurs langium/vscode)
Get-Process -Name node* | Where-Object { $_.MainWindowTitle -match "server" -or $_.CommandLine -match "server" } | Stop-Process -Force

Write-Host "Tous les serveurs ont été arrêtés."