import React, { createContext, useContext, useState, useEffect } from 'react';
import { collection, addDoc, deleteDoc, doc, query, where, orderBy, onSnapshot, getDocs, writeBatch } from 'firebase/firestore';
import { db } from '../config';
import { useAuth } from './AuthContext';
import { HistoryItem, HistoryContextType } from '../components/types/HistoryTypes';

const HistoryContext = createContext<HistoryContextType | undefined>(undefined);

export function useHistory() {
  const context = useContext(HistoryContext);
  if (!context) {
    throw new Error('useHistory must be used within a HistoryProvider');
  }
  return context;
}

export const HistoryProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [historyItems, setHistoryItems] = useState<HistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const { currentUser } = useAuth();

  // Écouter les changements en temps réel
  useEffect(() => {
    if (!currentUser) {
      setHistoryItems([]);
      setLoading(false);
      return;
    }

    const q = query(
      collection(db, 'history'),
      where('userId', '==', currentUser.uid),
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const items = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate()
      })) as HistoryItem[];
      
      setHistoryItems(items);
      setLoading(false);
    });

    return () => unsubscribe();
  }, [currentUser]);

  const addHistoryItem = async (item: Omit<HistoryItem, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (!currentUser) return;
    
    try {
      await addDoc(collection(db, 'history'), {
        ...item,
        userId: currentUser.uid,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    } catch (error) {
      console.error('Erreur lors de l\'ajout à l\'historique:', error);
    }
  };

  const deleteHistoryItem = async (id: string) => {
    try {
      await deleteDoc(doc(db, 'history', id));
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
    }
  };

  const loadHistoryItem = async (id: string): Promise<void> => {
    const item = historyItems.find(item => item.id === id);
    if (item) {
      // Créer un événement personnalisé pour charger l'élément
      const event = new CustomEvent('loadHistoryItem', { detail: item });
      window.dispatchEvent(event);
      // Ne pas retourner l'item
    }
    // Ne rien retourner (void)
  };

  const searchHistory = (query: string): HistoryItem[] => {
    const lowerQuery = query.toLowerCase();
    
    // Séparer les résultats en deux groupes : correspondances dans le titre et dans le texte
    const titleMatches = historyItems.filter(item => 
      item.title.toLowerCase().includes(lowerQuery)
    );
    
    // Trouver les correspondances uniquement dans le texte (qui ne sont pas déjà dans titleMatches)
    const textMatches = historyItems.filter(item => 
      !item.title.toLowerCase().includes(lowerQuery) && 
      item.extractedText.toLowerCase().includes(lowerQuery)
    );
    
    // Concaténer les deux groupes pour prioriser les correspondances de titre
    return [...titleMatches, ...textMatches];
  };

  const deleteAllUserHistory = async () => {
    if (!currentUser) return;
    
    try {
      setLoading(true);
      
      // Créer une requête pour obtenir tous les documents d'historique de l'utilisateur actuel
      const q = query(
        collection(db, 'history'),
        where('userId', '==', currentUser.uid)
      );
      
      // Obtenir tous les documents
      const querySnapshot = await getDocs(q);
      
      // Utiliser un batch pour supprimer tous les documents en une seule opération
      const batch = writeBatch(db);
      
      querySnapshot.forEach((document) => {
        batch.delete(doc(db, 'history', document.id));
      });
      
      // Exécuter le batch
      await batch.commit();
      
      console.log('Tout l\'historique a été supprimé avec succès');
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'historique:', error);
    } finally {
      setLoading(false);
    }
  };

  // Mettre à jour l'objet value pour inclure la nouvelle fonction
  const value = {
    historyItems,
    addHistoryItem,
    deleteHistoryItem,
    deleteAllUserHistory, // Ajouter la nouvelle fonction ici
    loadHistoryItem,
    searchHistory,
    loading
  };

  return (
    <HistoryContext.Provider value={value}>
      {children}
    </HistoryContext.Provider>
  );
};
