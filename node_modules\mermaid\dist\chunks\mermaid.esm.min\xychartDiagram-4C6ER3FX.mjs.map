{"version": 3, "sources": ["../../../src/diagrams/xychart/parser/xychart.jison", "../../../src/diagrams/xychart/chartBuilder/interfaces.ts", "../../../src/diagrams/xychart/chartBuilder/textDimensionCalculator.ts", "../../../src/diagrams/xychart/chartBuilder/components/axis/baseAxis.ts", "../../../src/diagrams/xychart/chartBuilder/components/axis/bandAxis.ts", "../../../src/diagrams/xychart/chartBuilder/components/axis/linearAxis.ts", "../../../src/diagrams/xychart/chartBuilder/components/axis/index.ts", "../../../src/diagrams/xychart/chartBuilder/components/chartTitle.ts", "../../../src/diagrams/xychart/chartBuilder/components/plot/linePlot.ts", "../../../src/diagrams/xychart/chartBuilder/components/plot/barPlot.ts", "../../../src/diagrams/xychart/chartBuilder/components/plot/index.ts", "../../../src/diagrams/xychart/chartBuilder/orchestrator.ts", "../../../src/diagrams/xychart/chartBuilder/index.ts", "../../../src/diagrams/xychart/xychartDb.ts", "../../../src/diagrams/xychart/xychartRenderer.ts", "../../../src/diagrams/xychart/xychartDiagram.ts"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[1,10,12,14,16,18,19,21,23],$V1=[2,6],$V2=[1,3],$V3=[1,5],$V4=[1,6],$V5=[1,7],$V6=[1,5,10,12,14,16,18,19,21,23,34,35,36],$V7=[1,25],$V8=[1,26],$V9=[1,28],$Va=[1,29],$Vb=[1,30],$Vc=[1,31],$Vd=[1,32],$Ve=[1,33],$Vf=[1,34],$Vg=[1,35],$Vh=[1,36],$Vi=[1,37],$Vj=[1,43],$Vk=[1,42],$Vl=[1,47],$Vm=[1,50],$Vn=[1,10,12,14,16,18,19,21,23,34,35,36],$Vo=[1,10,12,14,16,18,19,21,23,24,26,27,28,34,35,36],$Vp=[1,10,12,14,16,18,19,21,23,24,26,27,28,34,35,36,41,42,43,44,45,46,47,48,49,50],$Vq=[1,64];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"start\":3,\"eol\":4,\"XYCHART\":5,\"chartConfig\":6,\"document\":7,\"CHART_ORIENTATION\":8,\"statement\":9,\"title\":10,\"text\":11,\"X_AXIS\":12,\"parseXAxis\":13,\"Y_AXIS\":14,\"parseYAxis\":15,\"LINE\":16,\"plotData\":17,\"BAR\":18,\"acc_title\":19,\"acc_title_value\":20,\"acc_descr\":21,\"acc_descr_value\":22,\"acc_descr_multiline_value\":23,\"SQUARE_BRACES_START\":24,\"commaSeparatedNumbers\":25,\"SQUARE_BRACES_END\":26,\"NUMBER_WITH_DECIMAL\":27,\"COMMA\":28,\"xAxisData\":29,\"bandData\":30,\"ARROW_DELIMITER\":31,\"commaSeparatedTexts\":32,\"yAxisData\":33,\"NEWLINE\":34,\"SEMI\":35,\"EOF\":36,\"alphaNum\":37,\"STR\":38,\"MD_STR\":39,\"alphaNumToken\":40,\"AMP\":41,\"NUM\":42,\"ALPHA\":43,\"PLUS\":44,\"EQUALS\":45,\"MULT\":46,\"DOT\":47,\"BRKT\":48,\"MINUS\":49,\"UNDERSCORE\":50,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",5:\"XYCHART\",8:\"CHART_ORIENTATION\",10:\"title\",12:\"X_AXIS\",14:\"Y_AXIS\",16:\"LINE\",18:\"BAR\",19:\"acc_title\",20:\"acc_title_value\",21:\"acc_descr\",22:\"acc_descr_value\",23:\"acc_descr_multiline_value\",24:\"SQUARE_BRACES_START\",26:\"SQUARE_BRACES_END\",27:\"NUMBER_WITH_DECIMAL\",28:\"COMMA\",31:\"ARROW_DELIMITER\",34:\"NEWLINE\",35:\"SEMI\",36:\"EOF\",38:\"STR\",39:\"MD_STR\",41:\"AMP\",42:\"NUM\",43:\"ALPHA\",44:\"PLUS\",45:\"EQUALS\",46:\"MULT\",47:\"DOT\",48:\"BRKT\",49:\"MINUS\",50:\"UNDERSCORE\"},\nproductions_: [0,[3,2],[3,3],[3,2],[3,1],[6,1],[7,0],[7,2],[9,2],[9,2],[9,2],[9,2],[9,2],[9,3],[9,2],[9,3],[9,2],[9,2],[9,1],[17,3],[25,3],[25,1],[13,1],[13,2],[13,1],[29,1],[29,3],[30,3],[32,3],[32,1],[15,1],[15,2],[15,1],[33,3],[4,1],[4,1],[4,1],[11,1],[11,1],[11,1],[37,1],[37,2],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 5:\n yy.setOrientation($$[$0]); \nbreak;\ncase 9:\n yy.setDiagramTitle($$[$0].text.trim()); \nbreak;\ncase 12:\n yy.setLineData({text: '', type: 'text'}, $$[$0]); \nbreak;\ncase 13:\n yy.setLineData($$[$0-1], $$[$0]); \nbreak;\ncase 14:\n yy.setBarData({text: '', type: 'text'}, $$[$0]); \nbreak;\ncase 15:\n yy.setBarData($$[$0-1], $$[$0]); \nbreak;\ncase 16:\n this.$=$$[$0].trim();yy.setAccTitle(this.$); \nbreak;\ncase 17: case 18:\n this.$=$$[$0].trim();yy.setAccDescription(this.$); \nbreak;\ncase 19:\n this.$ = $$[$0-1] \nbreak;\ncase 20:\n this.$ = [Number($$[$0-2]), ...$$[$0]] \nbreak;\ncase 21:\n this.$ = [Number($$[$0])] \nbreak;\ncase 22:\nyy.setXAxisTitle($$[$0]);\nbreak;\ncase 23:\nyy.setXAxisTitle($$[$0-1]);\nbreak;\ncase 24:\nyy.setXAxisTitle({type: 'text', text: ''});\nbreak;\ncase 25:\nyy.setXAxisBand($$[$0]);\nbreak;\ncase 26:\nyy.setXAxisRangeData(Number($$[$0-2]), Number($$[$0]));\nbreak;\ncase 27:\nthis.$ = $$[$0-1]\nbreak;\ncase 28:\n this.$ = [$$[$0-2], ...$$[$0]] \nbreak;\ncase 29:\n this.$ = [$$[$0]] \nbreak;\ncase 30:\nyy.setYAxisTitle($$[$0]);\nbreak;\ncase 31:\nyy.setYAxisTitle($$[$0-1]);\nbreak;\ncase 32:\nyy.setYAxisTitle({type: \"text\", text: \"\"});\nbreak;\ncase 33:\nyy.setYAxisRangeData(Number($$[$0-2]), Number($$[$0]));\nbreak;\ncase 37:\n this.$={text:$$[$0], type: 'text'};\nbreak;\ncase 38:\n this.$={text: $$[$0], type: 'text'};\nbreak;\ncase 39:\n this.$={text: $$[$0], type: 'markdown'};\nbreak;\ncase 40:\nthis.$=$$[$0];\nbreak;\ncase 41:\nthis.$=$$[$0-1]+''+$$[$0];\nbreak;\n}\n},\ntable: [o($V0,$V1,{3:1,4:2,7:4,5:$V2,34:$V3,35:$V4,36:$V5}),{1:[3]},o($V0,$V1,{4:2,7:4,3:8,5:$V2,34:$V3,35:$V4,36:$V5}),o($V0,$V1,{4:2,7:4,6:9,3:10,5:$V2,8:[1,11],34:$V3,35:$V4,36:$V5}),{1:[2,4],9:12,10:[1,13],12:[1,14],14:[1,15],16:[1,16],18:[1,17],19:[1,18],21:[1,19],23:[1,20]},o($V6,[2,34]),o($V6,[2,35]),o($V6,[2,36]),{1:[2,1]},o($V0,$V1,{4:2,7:4,3:21,5:$V2,34:$V3,35:$V4,36:$V5}),{1:[2,3]},o($V6,[2,5]),o($V0,[2,7],{4:22,34:$V3,35:$V4,36:$V5}),{11:23,37:24,38:$V7,39:$V8,40:27,41:$V9,42:$Va,43:$Vb,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi},{11:39,13:38,24:$Vj,27:$Vk,29:40,30:41,37:24,38:$V7,39:$V8,40:27,41:$V9,42:$Va,43:$Vb,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi},{11:45,15:44,27:$Vl,33:46,37:24,38:$V7,39:$V8,40:27,41:$V9,42:$Va,43:$Vb,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi},{11:49,17:48,24:$Vm,37:24,38:$V7,39:$V8,40:27,41:$V9,42:$Va,43:$Vb,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi},{11:52,17:51,24:$Vm,37:24,38:$V7,39:$V8,40:27,41:$V9,42:$Va,43:$Vb,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi},{20:[1,53]},{22:[1,54]},o($Vn,[2,18]),{1:[2,2]},o($Vn,[2,8]),o($Vn,[2,9]),o($Vo,[2,37],{40:55,41:$V9,42:$Va,43:$Vb,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi}),o($Vo,[2,38]),o($Vo,[2,39]),o($Vp,[2,40]),o($Vp,[2,42]),o($Vp,[2,43]),o($Vp,[2,44]),o($Vp,[2,45]),o($Vp,[2,46]),o($Vp,[2,47]),o($Vp,[2,48]),o($Vp,[2,49]),o($Vp,[2,50]),o($Vp,[2,51]),o($Vn,[2,10]),o($Vn,[2,22],{30:41,29:56,24:$Vj,27:$Vk}),o($Vn,[2,24]),o($Vn,[2,25]),{31:[1,57]},{11:59,32:58,37:24,38:$V7,39:$V8,40:27,41:$V9,42:$Va,43:$Vb,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi},o($Vn,[2,11]),o($Vn,[2,30],{33:60,27:$Vl}),o($Vn,[2,32]),{31:[1,61]},o($Vn,[2,12]),{17:62,24:$Vm},{25:63,27:$Vq},o($Vn,[2,14]),{17:65,24:$Vm},o($Vn,[2,16]),o($Vn,[2,17]),o($Vp,[2,41]),o($Vn,[2,23]),{27:[1,66]},{26:[1,67]},{26:[2,29],28:[1,68]},o($Vn,[2,31]),{27:[1,69]},o($Vn,[2,13]),{26:[1,70]},{26:[2,21],28:[1,71]},o($Vn,[2,15]),o($Vn,[2,26]),o($Vn,[2,27]),{11:59,32:72,37:24,38:$V7,39:$V8,40:27,41:$V9,42:$Va,43:$Vb,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi},o($Vn,[2,33]),o($Vn,[2,19]),{25:73,27:$Vq},{26:[2,28]},{26:[2,20]}],\ndefaultActions: {8:[2,1],10:[2,3],21:[2,2],72:[2,28],73:[2,20]},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {\"case-insensitive\":true},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0:/* skip comments */\nbreak;\ncase 1:/* skip comments */\nbreak;\ncase 2: this.popState(); return 34; \nbreak;\ncase 3: this.popState(); return 34; \nbreak;\ncase 4:return 34;\nbreak;\ncase 5:/* do nothing */\nbreak;\ncase 6: return 10; \nbreak;\ncase 7: this.pushState(\"acc_title\");return 19; \nbreak;\ncase 8: this.popState(); return \"acc_title_value\"; \nbreak;\ncase 9: this.pushState(\"acc_descr\");return 21; \nbreak;\ncase 10: this.popState(); return \"acc_descr_value\"; \nbreak;\ncase 11: this.pushState(\"acc_descr_multiline\");\nbreak;\ncase 12: this.popState(); \nbreak;\ncase 13: return \"acc_descr_multiline_value\"; \nbreak;\ncase 14:return 5;\nbreak;\ncase 15:return 8\nbreak;\ncase 16: this.pushState(\"axis_data\"); return \"X_AXIS\"; \nbreak;\ncase 17: this.pushState(\"axis_data\"); return \"Y_AXIS\"; \nbreak;\ncase 18: this.pushState(\"axis_band_data\"); return 24; \nbreak;\ncase 19: return 31; \nbreak;\ncase 20: this.pushState(\"data\"); return 16; \nbreak;\ncase 21: this.pushState(\"data\"); return 18; \nbreak;\ncase 22: this.pushState(\"data_inner\"); return 24; \nbreak;\ncase 23: return 27; \nbreak;\ncase 24: this.popState(); return 26; \nbreak;\ncase 25: this.popState(); \nbreak;\ncase 26:this.pushState(\"string\");\nbreak;\ncase 27:this.popState();\nbreak;\ncase 28:return \"STR\";\nbreak;\ncase 29:return 24\nbreak;\ncase 30:return 26\nbreak;\ncase 31:return 43;\nbreak;\ncase 32:return 'COLON';\nbreak;\ncase 33:return 44;\nbreak;\ncase 34:return 28;\nbreak;\ncase 35:return 45;\nbreak;\ncase 36:return 46;\nbreak;\ncase 37:return 48;\nbreak;\ncase 38:return 50;\nbreak;\ncase 39:return 47;\nbreak;\ncase 40:return 41;\nbreak;\ncase 41:return 49;\nbreak;\ncase 42:return 42;\nbreak;\ncase 43:/* skip */\nbreak;\ncase 44:return 35;\nbreak;\ncase 45:return 36;\nbreak;\n}\n},\nrules: [/^(?:%%(?!\\{)[^\\n]*)/i,/^(?:[^\\}]%%[^\\n]*)/i,/^(?:(\\r?\\n))/i,/^(?:(\\r?\\n))/i,/^(?:[\\n\\r]+)/i,/^(?:%%[^\\n]*)/i,/^(?:title\\b)/i,/^(?:accTitle\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*\\{\\s*)/i,/^(?:\\{)/i,/^(?:[^\\}]*)/i,/^(?:xychart-beta\\b)/i,/^(?:(?:vertical|horizontal))/i,/^(?:x-axis\\b)/i,/^(?:y-axis\\b)/i,/^(?:\\[)/i,/^(?:-->)/i,/^(?:line\\b)/i,/^(?:bar\\b)/i,/^(?:\\[)/i,/^(?:[+-]?(?:\\d+(?:\\.\\d+)?|\\.\\d+))/i,/^(?:\\])/i,/^(?:(?:`\\)                                    \\{ this\\.pushState\\(md_string\\); \\}\\n<md_string>\\(\\?:\\(\\?!`\"\\)\\.\\)\\+                  \\{ return MD_STR; \\}\\n<md_string>\\(\\?:`))/i,/^(?:[\"])/i,/^(?:[\"])/i,/^(?:[^\"]*)/i,/^(?:\\[)/i,/^(?:\\])/i,/^(?:[A-Za-z]+)/i,/^(?::)/i,/^(?:\\+)/i,/^(?:,)/i,/^(?:=)/i,/^(?:\\*)/i,/^(?:#)/i,/^(?:[\\_])/i,/^(?:\\.)/i,/^(?:&)/i,/^(?:-)/i,/^(?:[0-9]+)/i,/^(?:\\s+)/i,/^(?:;)/i,/^(?:$)/i],\nconditions: {\"data_inner\":{\"rules\":[0,1,4,5,6,7,9,11,14,15,16,17,20,21,23,24,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],\"inclusive\":true},\"data\":{\"rules\":[0,1,3,4,5,6,7,9,11,14,15,16,17,20,21,22,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],\"inclusive\":true},\"axis_band_data\":{\"rules\":[0,1,4,5,6,7,9,11,14,15,16,17,20,21,24,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],\"inclusive\":true},\"axis_data\":{\"rules\":[0,1,2,4,5,6,7,9,11,14,15,16,17,18,19,20,21,23,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],\"inclusive\":true},\"acc_descr_multiline\":{\"rules\":[12,13],\"inclusive\":false},\"acc_descr\":{\"rules\":[10],\"inclusive\":false},\"acc_title\":{\"rules\":[8],\"inclusive\":false},\"title\":{\"rules\":[],\"inclusive\":false},\"md_string\":{\"rules\":[],\"inclusive\":false},\"string\":{\"rules\":[27,28],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,4,5,6,7,9,11,14,15,16,17,20,21,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "export interface XYChartAxisThemeConfig {\n  titleColor: string;\n  labelColor: string;\n  tickColor: string;\n  axisLineColor: string;\n}\n\nexport interface XYChartThemeConfig {\n  backgroundColor: string;\n  titleColor: string;\n  xAxisLabelColor: string;\n  xAxisTitleColor: string;\n  xAxisTickColor: string;\n  xAxisLineColor: string;\n  yAxisLabelColor: string;\n  yAxisTitleColor: string;\n  yAxisTickColor: string;\n  yAxisLineColor: string;\n  plotColorPalette: string;\n}\n\nexport interface ChartComponent {\n  calculateSpace(availableSpace: Dimension): Dimension;\n  setBoundingBoxXY(point: Point): void;\n  getDrawableElements(): DrawableElem[];\n}\n\nexport type SimplePlotDataType = [string, number][];\n\nexport interface LinePlotData {\n  type: 'line';\n  strokeFill: string;\n  strokeWidth: number;\n  data: SimplePlotDataType;\n}\n\nexport interface BarPlotData {\n  type: 'bar';\n  fill: string;\n  data: SimplePlotDataType;\n}\n\nexport type PlotData = LinePlotData | BarPlotData;\n\nexport function isBarPlot(data: PlotData): data is BarPlotData {\n  return data.type === 'bar';\n}\n\nexport interface BandAxisDataType {\n  type: 'band';\n  title: string;\n  categories: string[];\n}\n\nexport interface LinearAxisDataType {\n  type: 'linear';\n  title: string;\n  min: number;\n  max: number;\n}\n\nexport type AxisDataType = LinearAxisDataType | BandAxisDataType;\n\nexport function isBandAxisData(data: AxisDataType): data is BandAxisDataType {\n  return data.type === 'band';\n}\n\nexport function isLinearAxisData(data: AxisDataType): data is LinearAxisDataType {\n  return data.type === 'linear';\n}\n\n/**\n * For now we are keeping this configs as we are removing the required fields while generating the config.type.ts file\n * we should remove `XYChartAxisConfig` and `XYChartConfig` after we started using required fields\n */\nexport interface XYChartAxisConfig {\n  showLabel: boolean;\n  labelFontSize: number;\n  labelPadding: number;\n  showTitle: boolean;\n  titleFontSize: number;\n  titlePadding: number;\n  showTick: boolean;\n  tickLength: number;\n  tickWidth: number;\n  showAxisLine: boolean;\n  axisLineWidth: number;\n}\n\nexport interface XYChartConfig {\n  width: number;\n  height: number;\n  titleFontSize: number;\n  titlePadding: number;\n  showTitle: boolean;\n  xAxis: XYChartAxisConfig;\n  yAxis: XYChartAxisConfig;\n  chartOrientation: 'vertical' | 'horizontal';\n  plotReservedSpacePercent: number;\n}\n\nexport interface XYChartData {\n  xAxis: AxisDataType;\n  yAxis: AxisDataType;\n  title: string;\n  plots: PlotData[];\n}\n\nexport interface Dimension {\n  width: number;\n  height: number;\n}\n\nexport interface BoundingRect extends Point, Dimension {}\n\nexport interface Point {\n  x: number;\n  y: number;\n}\n\nexport type TextHorizontalPos = 'left' | 'center' | 'right';\nexport type TextVerticalPos = 'top' | 'middle';\n\nexport interface RectElem extends Point {\n  width: number;\n  height: number;\n  fill: string;\n  strokeWidth: number;\n  strokeFill: string;\n}\n\nexport interface TextElem extends Point {\n  text: string;\n  fill: string;\n  verticalPos: TextVerticalPos;\n  horizontalPos: TextHorizontalPos;\n  fontSize: number;\n  rotation: number;\n}\n\nexport interface PathElem {\n  path: string;\n  fill?: string;\n  strokeWidth: number;\n  strokeFill: string;\n}\n\nexport type DrawableElem =\n  | {\n      groupTexts: string[];\n      type: 'rect';\n      data: RectElem[];\n    }\n  | {\n      groupTexts: string[];\n      type: 'text';\n      data: TextElem[];\n    }\n  | {\n      groupTexts: string[];\n      type: 'path';\n      data: PathElem[];\n    };\n", "import type { SVGGroup } from '../../../diagram-api/types.js';\nimport { computeDimensionOfText } from '../../../rendering-util/createText.js';\nimport type { Dimension } from './interfaces.js';\n\nexport interface TextDimensionCalculator {\n  getMaxDimension(texts: string[], fontSize: number): Dimension;\n}\n\nexport class TextDimensionCalculatorWithFont implements TextDimensionCalculator {\n  constructor(private parentGroup: SVGGroup) {}\n  getMaxDimension(texts: string[], fontSize: number): Dimension {\n    if (!this.parentGroup) {\n      return {\n        width: texts.reduce((acc, cur) => Math.max(cur.length, acc), 0) * fontSize,\n        height: fontSize,\n      };\n    }\n\n    const dimension: Dimension = {\n      width: 0,\n      height: 0,\n    };\n\n    const elem = this.parentGroup\n      .append('g')\n      .attr('visibility', 'hidden')\n      .attr('font-size', fontSize);\n\n    for (const t of texts) {\n      const bbox = computeDimensionOfText(elem, 1, t);\n      const width = bbox ? bbox.width : t.length * fontSize;\n      const height = bbox ? bbox.height : fontSize;\n      dimension.width = Math.max(dimension.width, width);\n      dimension.height = Math.max(dimension.height, height);\n    }\n    elem.remove();\n    return dimension;\n  }\n}\n", "import type {\n  BoundingRect,\n  Dimension,\n  DrawableElem,\n  Point,\n  XYChartAxisConfig,\n  XYChartAxisThemeConfig,\n} from '../../interfaces.js';\nimport type { TextDimensionCalculator } from '../../textDimensionCalculator.js';\nimport type { Axis, AxisPosition } from './index.js';\n\nconst BAR_WIDTH_TO_TICK_WIDTH_RATIO = 0.7;\nconst MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL = 0.2;\n\nexport abstract class BaseAxis implements Axis {\n  protected boundingRect: BoundingRect = { x: 0, y: 0, width: 0, height: 0 };\n  protected axisPosition: AxisPosition = 'left';\n  private range: [number, number];\n  protected showTitle = false;\n  protected showLabel = false;\n  protected showTick = false;\n  protected showAxisLine = false;\n  protected outerPadding = 0;\n  protected titleTextHeight = 0;\n  protected labelTextHeight = 0;\n\n  constructor(\n    protected axisConfig: XYChartAxisConfig,\n    protected title: string,\n    protected textDimensionCalculator: TextDimensionCalculator,\n    protected axisThemeConfig: XYChartAxisThemeConfig\n  ) {\n    this.range = [0, 10];\n    this.boundingRect = { x: 0, y: 0, width: 0, height: 0 };\n    this.axisPosition = 'left';\n  }\n\n  setRange(range: [number, number]): void {\n    this.range = range;\n    if (this.axisPosition === 'left' || this.axisPosition === 'right') {\n      this.boundingRect.height = range[1] - range[0];\n    } else {\n      this.boundingRect.width = range[1] - range[0];\n    }\n    this.recalculateScale();\n  }\n\n  getRange(): [number, number] {\n    return [this.range[0] + this.outerPadding, this.range[1] - this.outerPadding];\n  }\n\n  setAxisPosition(axisPosition: AxisPosition): void {\n    this.axisPosition = axisPosition;\n    this.setRange(this.range);\n  }\n\n  abstract getScaleValue(value: number | string): number;\n\n  abstract recalculateScale(): void;\n\n  abstract getTickValues(): (string | number)[];\n\n  getTickDistance(): number {\n    const range = this.getRange();\n    return Math.abs(range[0] - range[1]) / this.getTickValues().length;\n  }\n\n  getAxisOuterPadding(): number {\n    return this.outerPadding;\n  }\n\n  private getLabelDimension(): Dimension {\n    return this.textDimensionCalculator.getMaxDimension(\n      this.getTickValues().map((tick) => tick.toString()),\n      this.axisConfig.labelFontSize\n    );\n  }\n\n  recalculateOuterPaddingToDrawBar(): void {\n    if (BAR_WIDTH_TO_TICK_WIDTH_RATIO * this.getTickDistance() > this.outerPadding * 2) {\n      this.outerPadding = Math.floor((BAR_WIDTH_TO_TICK_WIDTH_RATIO * this.getTickDistance()) / 2);\n    }\n    this.recalculateScale();\n  }\n\n  private calculateSpaceIfDrawnHorizontally(availableSpace: Dimension) {\n    let availableHeight = availableSpace.height;\n    if (this.axisConfig.showAxisLine && availableHeight > this.axisConfig.axisLineWidth) {\n      availableHeight -= this.axisConfig.axisLineWidth;\n      this.showAxisLine = true;\n    }\n    if (this.axisConfig.showLabel) {\n      const spaceRequired = this.getLabelDimension();\n      const maxPadding = MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL * availableSpace.width;\n      this.outerPadding = Math.min(spaceRequired.width / 2, maxPadding);\n\n      const heightRequired = spaceRequired.height + this.axisConfig.labelPadding * 2;\n      this.labelTextHeight = spaceRequired.height;\n      if (heightRequired <= availableHeight) {\n        availableHeight -= heightRequired;\n        this.showLabel = true;\n      }\n    }\n    if (this.axisConfig.showTick && availableHeight >= this.axisConfig.tickLength) {\n      this.showTick = true;\n      availableHeight -= this.axisConfig.tickLength;\n    }\n    if (this.axisConfig.showTitle && this.title) {\n      const spaceRequired = this.textDimensionCalculator.getMaxDimension(\n        [this.title],\n        this.axisConfig.titleFontSize\n      );\n      const heightRequired = spaceRequired.height + this.axisConfig.titlePadding * 2;\n      this.titleTextHeight = spaceRequired.height;\n      if (heightRequired <= availableHeight) {\n        availableHeight -= heightRequired;\n        this.showTitle = true;\n      }\n    }\n    this.boundingRect.width = availableSpace.width;\n    this.boundingRect.height = availableSpace.height - availableHeight;\n  }\n\n  private calculateSpaceIfDrawnVertical(availableSpace: Dimension) {\n    let availableWidth = availableSpace.width;\n    if (this.axisConfig.showAxisLine && availableWidth > this.axisConfig.axisLineWidth) {\n      availableWidth -= this.axisConfig.axisLineWidth;\n      this.showAxisLine = true;\n    }\n    if (this.axisConfig.showLabel) {\n      const spaceRequired = this.getLabelDimension();\n      const maxPadding = MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL * availableSpace.height;\n      this.outerPadding = Math.min(spaceRequired.height / 2, maxPadding);\n      const widthRequired = spaceRequired.width + this.axisConfig.labelPadding * 2;\n      if (widthRequired <= availableWidth) {\n        availableWidth -= widthRequired;\n        this.showLabel = true;\n      }\n    }\n    if (this.axisConfig.showTick && availableWidth >= this.axisConfig.tickLength) {\n      this.showTick = true;\n      availableWidth -= this.axisConfig.tickLength;\n    }\n    if (this.axisConfig.showTitle && this.title) {\n      const spaceRequired = this.textDimensionCalculator.getMaxDimension(\n        [this.title],\n        this.axisConfig.titleFontSize\n      );\n      const widthRequired = spaceRequired.height + this.axisConfig.titlePadding * 2;\n      this.titleTextHeight = spaceRequired.height;\n      if (widthRequired <= availableWidth) {\n        availableWidth -= widthRequired;\n        this.showTitle = true;\n      }\n    }\n    this.boundingRect.width = availableSpace.width - availableWidth;\n    this.boundingRect.height = availableSpace.height;\n  }\n\n  calculateSpace(availableSpace: Dimension): Dimension {\n    if (this.axisPosition === 'left' || this.axisPosition === 'right') {\n      this.calculateSpaceIfDrawnVertical(availableSpace);\n    } else {\n      this.calculateSpaceIfDrawnHorizontally(availableSpace);\n    }\n    this.recalculateScale();\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height,\n    };\n  }\n\n  setBoundingBoxXY(point: Point): void {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n\n  private getDrawableElementsForLeftAxis(): DrawableElem[] {\n    const drawableElement: DrawableElem[] = [];\n    if (this.showAxisLine) {\n      const x = this.boundingRect.x + this.boundingRect.width - this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: 'path',\n        groupTexts: ['left-axis', 'axisl-line'],\n        data: [\n          {\n            path: `M ${x},${this.boundingRect.y} L ${x},${\n              this.boundingRect.y + this.boundingRect.height\n            } `,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth,\n          },\n        ],\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: 'text',\n        groupTexts: ['left-axis', 'label'],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x:\n            this.boundingRect.x +\n            this.boundingRect.width -\n            (this.showLabel ? this.axisConfig.labelPadding : 0) -\n            (this.showTick ? this.axisConfig.tickLength : 0) -\n            (this.showAxisLine ? this.axisConfig.axisLineWidth : 0),\n          y: this.getScaleValue(tick),\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: 'middle',\n          horizontalPos: 'right',\n        })),\n      });\n    }\n    if (this.showTick) {\n      const x =\n        this.boundingRect.x +\n        this.boundingRect.width -\n        (this.showAxisLine ? this.axisConfig.axisLineWidth : 0);\n      drawableElement.push({\n        type: 'path',\n        groupTexts: ['left-axis', 'ticks'],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${x},${this.getScaleValue(tick)} L ${\n            x - this.axisConfig.tickLength\n          },${this.getScaleValue(tick)}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth,\n        })),\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: 'text',\n        groupTexts: ['left-axis', 'title'],\n        data: [\n          {\n            text: this.title,\n            x: this.boundingRect.x + this.axisConfig.titlePadding,\n            y: this.boundingRect.y + this.boundingRect.height / 2,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 270,\n            verticalPos: 'top',\n            horizontalPos: 'center',\n          },\n        ],\n      });\n    }\n    return drawableElement;\n  }\n  private getDrawableElementsForBottomAxis(): DrawableElem[] {\n    const drawableElement: DrawableElem[] = [];\n    if (this.showAxisLine) {\n      const y = this.boundingRect.y + this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: 'path',\n        groupTexts: ['bottom-axis', 'axis-line'],\n        data: [\n          {\n            path: `M ${this.boundingRect.x},${y} L ${\n              this.boundingRect.x + this.boundingRect.width\n            },${y}`,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth,\n          },\n        ],\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: 'text',\n        groupTexts: ['bottom-axis', 'label'],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.getScaleValue(tick),\n          y:\n            this.boundingRect.y +\n            this.axisConfig.labelPadding +\n            (this.showTick ? this.axisConfig.tickLength : 0) +\n            (this.showAxisLine ? this.axisConfig.axisLineWidth : 0),\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: 'top',\n          horizontalPos: 'center',\n        })),\n      });\n    }\n    if (this.showTick) {\n      const y = this.boundingRect.y + (this.showAxisLine ? this.axisConfig.axisLineWidth : 0);\n      drawableElement.push({\n        type: 'path',\n        groupTexts: ['bottom-axis', 'ticks'],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${this.getScaleValue(tick)},${y} L ${this.getScaleValue(tick)},${\n            y + this.axisConfig.tickLength\n          }`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth,\n        })),\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: 'text',\n        groupTexts: ['bottom-axis', 'title'],\n        data: [\n          {\n            text: this.title,\n            x: this.range[0] + (this.range[1] - this.range[0]) / 2,\n            y:\n              this.boundingRect.y +\n              this.boundingRect.height -\n              this.axisConfig.titlePadding -\n              this.titleTextHeight,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 0,\n            verticalPos: 'top',\n            horizontalPos: 'center',\n          },\n        ],\n      });\n    }\n    return drawableElement;\n  }\n  private getDrawableElementsForTopAxis(): DrawableElem[] {\n    const drawableElement: DrawableElem[] = [];\n    if (this.showAxisLine) {\n      const y = this.boundingRect.y + this.boundingRect.height - this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: 'path',\n        groupTexts: ['top-axis', 'axis-line'],\n        data: [\n          {\n            path: `M ${this.boundingRect.x},${y} L ${\n              this.boundingRect.x + this.boundingRect.width\n            },${y}`,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth,\n          },\n        ],\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: 'text',\n        groupTexts: ['top-axis', 'label'],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.getScaleValue(tick),\n          y:\n            this.boundingRect.y +\n            (this.showTitle ? this.titleTextHeight + this.axisConfig.titlePadding * 2 : 0) +\n            this.axisConfig.labelPadding,\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: 'top',\n          horizontalPos: 'center',\n        })),\n      });\n    }\n    if (this.showTick) {\n      const y = this.boundingRect.y;\n      drawableElement.push({\n        type: 'path',\n        groupTexts: ['top-axis', 'ticks'],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${this.getScaleValue(tick)},${\n            y + this.boundingRect.height - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0)\n          } L ${this.getScaleValue(tick)},${\n            y +\n            this.boundingRect.height -\n            this.axisConfig.tickLength -\n            (this.showAxisLine ? this.axisConfig.axisLineWidth : 0)\n          }`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth,\n        })),\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: 'text',\n        groupTexts: ['top-axis', 'title'],\n        data: [\n          {\n            text: this.title,\n            x: this.boundingRect.x + this.boundingRect.width / 2,\n            y: this.boundingRect.y + this.axisConfig.titlePadding,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 0,\n            verticalPos: 'top',\n            horizontalPos: 'center',\n          },\n        ],\n      });\n    }\n    return drawableElement;\n  }\n\n  getDrawableElements(): DrawableElem[] {\n    if (this.axisPosition === 'left') {\n      return this.getDrawableElementsForLeftAxis();\n    }\n    if (this.axisPosition === 'right') {\n      throw Error('Drawing of right axis is not implemented');\n    }\n    if (this.axisPosition === 'bottom') {\n      return this.getDrawableElementsForBottomAxis();\n    }\n    if (this.axisPosition === 'top') {\n      return this.getDrawableElementsForTopAxis();\n    }\n    return [];\n  }\n}\n", "import type { ScaleBand } from 'd3';\nimport { scaleBand } from 'd3';\nimport { log } from '../../../../../logger.js';\nimport type { TextDimensionCalculator } from '../../textDimensionCalculator.js';\nimport { BaseAxis } from './baseAxis.js';\nimport type { XYChartAxisThemeConfig, XYChartAxisConfig } from '../../interfaces.js';\n\nexport class BandAxis extends BaseAxis {\n  private scale: ScaleBand<string>;\n  private categories: string[];\n\n  constructor(\n    axisConfig: XYChartAxisConfig,\n    axisThemeConfig: XYChartAxisThemeConfig,\n    categories: string[],\n    title: string,\n    textDimensionCalculator: TextDimensionCalculator\n  ) {\n    super(axisConfig, title, textDimensionCalculator, axisThemeConfig);\n    this.categories = categories;\n    this.scale = scaleBand().domain(this.categories).range(this.getRange());\n  }\n\n  setRange(range: [number, number]): void {\n    super.setRange(range);\n  }\n\n  recalculateScale(): void {\n    this.scale = scaleBand()\n      .domain(this.categories)\n      .range(this.getRange())\n      .paddingInner(1)\n      .paddingOuter(0)\n      .align(0.5);\n    log.trace('BandAxis axis final categories, range: ', this.categories, this.getRange());\n  }\n\n  getTickValues(): (string | number)[] {\n    return this.categories;\n  }\n\n  getScaleValue(value: string): number {\n    return this.scale(value) ?? this.getRange()[0];\n  }\n}\n", "import type { ScaleLinear } from 'd3';\nimport { scaleLinear } from 'd3';\nimport type { TextDimensionCalculator } from '../../textDimensionCalculator.js';\nimport { BaseAxis } from './baseAxis.js';\nimport type { XYChartAxisThemeConfig, XYChartAxisConfig } from '../../interfaces.js';\n\nexport class LinearAxis extends BaseAxis {\n  private scale: ScaleLinear<number, number>;\n  private domain: [number, number];\n\n  constructor(\n    axisConfig: XYChartAxisConfig,\n    axisThemeConfig: XYChartAxisThemeConfig,\n    domain: [number, number],\n    title: string,\n    textDimensionCalculator: TextDimensionCalculator\n  ) {\n    super(axisConfig, title, textDimensionCalculator, axisThemeConfig);\n    this.domain = domain;\n    this.scale = scaleLinear().domain(this.domain).range(this.getRange());\n  }\n\n  getTickValues(): (string | number)[] {\n    return this.scale.ticks();\n  }\n\n  recalculateScale(): void {\n    const domain = [...this.domain]; // copy the array so if reverse is called two times it should not cancel the reverse effect\n    if (this.axisPosition === 'left') {\n      domain.reverse(); // since y-axis in svg start from top\n    }\n    this.scale = scaleLinear().domain(domain).range(this.getRange());\n  }\n\n  getScaleValue(value: number): number {\n    return this.scale(value);\n  }\n}\n", "import type { SVGGroup } from '../../../../../diagram-api/types.js';\nimport type {\n  AxisDataType,\n  ChartComponent,\n  XYChartAxisConfig,\n  XYChartAxisThemeConfig,\n} from '../../interfaces.js';\nimport { isBandAxisData } from '../../interfaces.js';\nimport { TextDimensionCalculatorWithFont } from '../../textDimensionCalculator.js';\nimport { BandAxis } from './bandAxis.js';\nimport { LinearAxis } from './linearAxis.js';\n\nexport type AxisPosition = 'left' | 'right' | 'top' | 'bottom';\n\nexport interface Axis extends ChartComponent {\n  getScaleValue(value: string | number): number;\n  setAxisPosition(axisPosition: AxisPosition): void;\n  getAxisOuterPadding(): number;\n  getTickDistance(): number;\n  recalculateOuterPaddingToDrawBar(): void;\n  setRange(range: [number, number]): void;\n}\n\nexport function getAxis(\n  data: AxisDataType,\n  axisConfig: XYChartAxisConfig,\n  axisThemeConfig: XYChartAxisThemeConfig,\n  tmpSVGGroup: SVGGroup\n): Axis {\n  const textDimensionCalculator = new TextDimensionCalculatorWithFont(tmpSVGGroup);\n  if (isBandAxisData(data)) {\n    return new BandAxis(\n      axisConfig,\n      axisThemeConfig,\n      data.categories,\n      data.title,\n      textDimensionCalculator\n    );\n  }\n  return new LinearAxis(\n    axisConfig,\n    axisThemeConfig,\n    [data.min, data.max],\n    data.title,\n    textDimensionCalculator\n  );\n}\n", "import type { SVGGroup } from '../../../../diagram-api/types.js';\nimport type {\n  BoundingRect,\n  ChartComponent,\n  Dimension,\n  DrawableElem,\n  Point,\n  XYChartConfig,\n  XYChartData,\n  XYChartThemeConfig,\n} from '../interfaces.js';\nimport type { TextDimensionCalculator } from '../textDimensionCalculator.js';\nimport { TextDimensionCalculatorWithFont } from '../textDimensionCalculator.js';\n\nexport class ChartTitle implements ChartComponent {\n  private boundingRect: BoundingRect;\n  private showChartTitle: boolean;\n  constructor(\n    private textDimensionCalculator: TextDimensionCalculator,\n    private chartConfig: XYChartConfig,\n    private chartData: XYChartData,\n    private chartThemeConfig: XYChartThemeConfig\n  ) {\n    this.boundingRect = {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0,\n    };\n    this.showChartTitle = false;\n  }\n  setBoundingBoxXY(point: Point): void {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  calculateSpace(availableSpace: Dimension): Dimension {\n    const titleDimension = this.textDimensionCalculator.getMaxDimension(\n      [this.chartData.title],\n      this.chartConfig.titleFontSize\n    );\n    const widthRequired = Math.max(titleDimension.width, availableSpace.width);\n    const heightRequired = titleDimension.height + 2 * this.chartConfig.titlePadding;\n    if (\n      titleDimension.width <= widthRequired &&\n      titleDimension.height <= heightRequired &&\n      this.chartConfig.showTitle &&\n      this.chartData.title\n    ) {\n      this.boundingRect.width = widthRequired;\n      this.boundingRect.height = heightRequired;\n      this.showChartTitle = true;\n    }\n\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height,\n    };\n  }\n  getDrawableElements(): DrawableElem[] {\n    const drawableElem: DrawableElem[] = [];\n    if (this.showChartTitle) {\n      drawableElem.push({\n        groupTexts: ['chart-title'],\n        type: 'text',\n        data: [\n          {\n            fontSize: this.chartConfig.titleFontSize,\n            text: this.chartData.title,\n            verticalPos: 'middle',\n            horizontalPos: 'center',\n            x: this.boundingRect.x + this.boundingRect.width / 2,\n            y: this.boundingRect.y + this.boundingRect.height / 2,\n            fill: this.chartThemeConfig.titleColor,\n            rotation: 0,\n          },\n        ],\n      });\n    }\n    return drawableElem;\n  }\n}\n\nexport function getChartTitleComponent(\n  chartConfig: XYChartConfig,\n  chartData: XYChartData,\n  chartThemeConfig: XYChartThemeConfig,\n  tmpSVGGroup: SVGGroup\n): ChartComponent {\n  const textDimensionCalculator = new TextDimensionCalculatorWithFont(tmpSVGGroup);\n  return new ChartTitle(textDimensionCalculator, chartConfig, chartData, chartThemeConfig);\n}\n", "import { line } from 'd3';\nimport type { DrawableElem, LinePlotData, XYChartConfig } from '../../interfaces.js';\nimport type { Axis } from '../axis/index.js';\n\nexport class LinePlot {\n  constructor(\n    private plotData: LinePlotData,\n    private xAxis: Axis,\n    private yAxis: Axis,\n    private orientation: XYChartConfig['chartOrientation'],\n    private plotIndex: number\n  ) {}\n\n  getDrawableElement(): DrawableElem[] {\n    const finalData: [number, number][] = this.plotData.data.map((d) => [\n      this.xAxis.getScaleValue(d[0]),\n      this.yAxis.getScaleValue(d[1]),\n    ]);\n\n    let path: string | null;\n    if (this.orientation === 'horizontal') {\n      path = line()\n        .y((d) => d[0])\n        .x((d) => d[1])(finalData);\n    } else {\n      path = line()\n        .x((d) => d[0])\n        .y((d) => d[1])(finalData);\n    }\n    if (!path) {\n      return [];\n    }\n    return [\n      {\n        groupTexts: ['plot', `line-plot-${this.plotIndex}`],\n        type: 'path',\n        data: [\n          {\n            path,\n            strokeFill: this.plotData.strokeFill,\n            strokeWidth: this.plotData.strokeWidth,\n          },\n        ],\n      },\n    ];\n  }\n}\n", "import type { BarPlotData, BoundingRect, DrawableElem, XYChartConfig } from '../../interfaces.js';\nimport type { Axis } from '../axis/index.js';\n\nexport class BarPlot {\n  constructor(\n    private barData: BarPlotData,\n    private boundingRect: BoundingRect,\n    private xAxis: Axis,\n    private yAxis: Axis,\n    private orientation: XYChartConfig['chartOrientation'],\n    private plotIndex: number\n  ) {}\n\n  getDrawableElement(): DrawableElem[] {\n    const finalData: [number, number][] = this.barData.data.map((d) => [\n      this.xAxis.getScaleValue(d[0]),\n      this.yAxis.getScaleValue(d[1]),\n    ]);\n\n    const barPaddingPercent = 0.05;\n\n    const barWidth =\n      Math.min(this.xAxis.getAxisOuterPadding() * 2, this.xAxis.getTickDistance()) *\n      (1 - barPaddingPercent);\n    const barWidthHalf = barWidth / 2;\n\n    if (this.orientation === 'horizontal') {\n      return [\n        {\n          groupTexts: ['plot', `bar-plot-${this.plotIndex}`],\n          type: 'rect',\n          data: finalData.map((data) => ({\n            x: this.boundingRect.x,\n            y: data[0] - barWidthHalf,\n            height: barWidth,\n            width: data[1] - this.boundingRect.x,\n            fill: this.barData.fill,\n            strokeWidth: 0,\n            strokeFill: this.barData.fill,\n          })),\n        },\n      ];\n    }\n    return [\n      {\n        groupTexts: ['plot', `bar-plot-${this.plotIndex}`],\n        type: 'rect',\n        data: finalData.map((data) => ({\n          x: data[0] - barWidthHalf,\n          y: data[1],\n          width: barWidth,\n          height: this.boundingRect.y + this.boundingRect.height - data[1],\n          fill: this.barData.fill,\n          strokeWidth: 0,\n          strokeFill: this.barData.fill,\n        })),\n      },\n    ];\n  }\n}\n", "import type {\n  XY<PERSON>hartData,\n  Dimension,\n  BoundingRect,\n  DrawableElem,\n  Point,\n  XYChartThemeConfig,\n  XYChartConfig,\n} from '../../interfaces.js';\nimport type { Axis } from '../axis/index.js';\nimport type { ChartComponent } from '../../interfaces.js';\nimport { LinePlot } from './linePlot.js';\nimport { BarPlot } from './barPlot.js';\n\nexport interface Plot extends ChartComponent {\n  setAxes(xAxis: Axis, yAxis: Axis): void;\n}\n\nexport class BasePlot implements Plot {\n  private boundingRect: BoundingRect;\n  private xAxis?: Axis;\n  private yAxis?: Axis;\n\n  constructor(\n    private chartConfig: XYChartConfig,\n    private chartData: XYChartData,\n    private chartThemeConfig: XYChartThemeConfig\n  ) {\n    this.boundingRect = {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0,\n    };\n  }\n  setAxes(xAxis: Axis, yAxis: Axis) {\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n  }\n  setBoundingBoxXY(point: Point): void {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  calculateSpace(availableSpace: Dimension): Dimension {\n    this.boundingRect.width = availableSpace.width;\n    this.boundingRect.height = availableSpace.height;\n\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height,\n    };\n  }\n  getDrawableElements(): DrawableElem[] {\n    if (!(this.xAxis && this.yAxis)) {\n      throw Error('Axes must be passed to render Plots');\n    }\n    const drawableElem: DrawableElem[] = [];\n    for (const [i, plot] of this.chartData.plots.entries()) {\n      switch (plot.type) {\n        case 'line':\n          {\n            const linePlot = new LinePlot(\n              plot,\n              this.xAxis,\n              this.yAxis,\n              this.chartConfig.chartOrientation,\n              i\n            );\n            drawableElem.push(...linePlot.getDrawableElement());\n          }\n          break;\n        case 'bar':\n          {\n            const barPlot = new BarPlot(\n              plot,\n              this.boundingRect,\n              this.xAxis,\n              this.yAxis,\n              this.chartConfig.chartOrientation,\n              i\n            );\n            drawableElem.push(...barPlot.getDrawableElement());\n          }\n          break;\n      }\n    }\n    return drawableElem;\n  }\n}\n\nexport function getPlotComponent(\n  chartConfig: XYChartConfig,\n  chartData: XYChartData,\n  chartThemeConfig: XYChartThemeConfig\n): Plot {\n  return new BasePlot(chartConfig, chartData, chartThemeConfig);\n}\n", "import type { SVGGroup } from '../../../diagram-api/types.js';\nimport type { Axis } from './components/axis/index.js';\nimport { getAxis } from './components/axis/index.js';\nimport { getChartTitleComponent } from './components/chartTitle.js';\nimport type { Plot } from './components/plot/index.js';\nimport { getPlotComponent } from './components/plot/index.js';\nimport type {\n  ChartComponent,\n  DrawableElem,\n  XYChartConfig,\n  XYChartData,\n  XYChartThemeConfig,\n} from './interfaces.js';\nimport { isBarPlot } from './interfaces.js';\n\nexport class Orchestrator {\n  private componentStore: {\n    title: ChartComponent;\n    plot: Plot;\n    xAxis: Axis;\n    yAxis: Axis;\n  };\n  constructor(\n    private chartConfig: XYChartConfig,\n    private chartData: XYChartData,\n    chartThemeConfig: XYChartThemeConfig,\n    tmpSVGGroup: SVGGroup\n  ) {\n    this.componentStore = {\n      title: getChartTitleComponent(chartConfig, chartData, chartThemeConfig, tmpSVGGroup),\n      plot: getPlotComponent(chartConfig, chartData, chartThemeConfig),\n      xAxis: getAxis(\n        chartData.xAxis,\n        chartConfig.xAxis,\n        {\n          titleColor: chartThemeConfig.xAxisTitleColor,\n          labelColor: chartThemeConfig.xAxisLabelColor,\n          tickColor: chartThemeConfig.xAxisTickColor,\n          axisLineColor: chartThemeConfig.xAxisLineColor,\n        },\n        tmpSVGGroup\n      ),\n      yAxis: getAxis(\n        chartData.yAxis,\n        chartConfig.yAxis,\n        {\n          titleColor: chartThemeConfig.yAxisTitleColor,\n          labelColor: chartThemeConfig.yAxisLabelColor,\n          tickColor: chartThemeConfig.yAxisTickColor,\n          axisLineColor: chartThemeConfig.yAxisLineColor,\n        },\n        tmpSVGGroup\n      ),\n    };\n  }\n\n  private calculateVerticalSpace() {\n    let availableWidth = this.chartConfig.width;\n    let availableHeight = this.chartConfig.height;\n    let plotX = 0;\n    let plotY = 0;\n    let chartWidth = Math.floor((availableWidth * this.chartConfig.plotReservedSpacePercent) / 100);\n    let chartHeight = Math.floor(\n      (availableHeight * this.chartConfig.plotReservedSpacePercent) / 100\n    );\n    let spaceUsed = this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight,\n    });\n    availableWidth -= spaceUsed.width;\n    availableHeight -= spaceUsed.height;\n\n    spaceUsed = this.componentStore.title.calculateSpace({\n      width: this.chartConfig.width,\n      height: availableHeight,\n    });\n    plotY = spaceUsed.height;\n    availableHeight -= spaceUsed.height;\n    this.componentStore.xAxis.setAxisPosition('bottom');\n    spaceUsed = this.componentStore.xAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight,\n    });\n    availableHeight -= spaceUsed.height;\n    this.componentStore.yAxis.setAxisPosition('left');\n    spaceUsed = this.componentStore.yAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight,\n    });\n    plotX = spaceUsed.width;\n    availableWidth -= spaceUsed.width;\n    if (availableWidth > 0) {\n      chartWidth += availableWidth;\n      availableWidth = 0;\n    }\n    if (availableHeight > 0) {\n      chartHeight += availableHeight;\n      availableHeight = 0;\n    }\n    this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight,\n    });\n\n    this.componentStore.plot.setBoundingBoxXY({ x: plotX, y: plotY });\n    this.componentStore.xAxis.setRange([plotX, plotX + chartWidth]);\n    this.componentStore.xAxis.setBoundingBoxXY({ x: plotX, y: plotY + chartHeight });\n    this.componentStore.yAxis.setRange([plotY, plotY + chartHeight]);\n    this.componentStore.yAxis.setBoundingBoxXY({ x: 0, y: plotY });\n    if (this.chartData.plots.some((p) => isBarPlot(p))) {\n      this.componentStore.xAxis.recalculateOuterPaddingToDrawBar();\n    }\n  }\n\n  private calculateHorizontalSpace() {\n    let availableWidth = this.chartConfig.width;\n    let availableHeight = this.chartConfig.height;\n    let titleYEnd = 0;\n    let plotX = 0;\n    let plotY = 0;\n    let chartWidth = Math.floor((availableWidth * this.chartConfig.plotReservedSpacePercent) / 100);\n    let chartHeight = Math.floor(\n      (availableHeight * this.chartConfig.plotReservedSpacePercent) / 100\n    );\n    let spaceUsed = this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight,\n    });\n    availableWidth -= spaceUsed.width;\n    availableHeight -= spaceUsed.height;\n\n    spaceUsed = this.componentStore.title.calculateSpace({\n      width: this.chartConfig.width,\n      height: availableHeight,\n    });\n    titleYEnd = spaceUsed.height;\n    availableHeight -= spaceUsed.height;\n    this.componentStore.xAxis.setAxisPosition('left');\n    spaceUsed = this.componentStore.xAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight,\n    });\n    availableWidth -= spaceUsed.width;\n    plotX = spaceUsed.width;\n    this.componentStore.yAxis.setAxisPosition('top');\n    spaceUsed = this.componentStore.yAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight,\n    });\n    availableHeight -= spaceUsed.height;\n    plotY = titleYEnd + spaceUsed.height;\n    if (availableWidth > 0) {\n      chartWidth += availableWidth;\n      availableWidth = 0;\n    }\n    if (availableHeight > 0) {\n      chartHeight += availableHeight;\n      availableHeight = 0;\n    }\n    this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight,\n    });\n\n    this.componentStore.plot.setBoundingBoxXY({ x: plotX, y: plotY });\n    this.componentStore.yAxis.setRange([plotX, plotX + chartWidth]);\n    this.componentStore.yAxis.setBoundingBoxXY({ x: plotX, y: titleYEnd });\n    this.componentStore.xAxis.setRange([plotY, plotY + chartHeight]);\n    this.componentStore.xAxis.setBoundingBoxXY({ x: 0, y: plotY });\n    if (this.chartData.plots.some((p) => isBarPlot(p))) {\n      this.componentStore.xAxis.recalculateOuterPaddingToDrawBar();\n    }\n  }\n\n  private calculateSpace() {\n    if (this.chartConfig.chartOrientation === 'horizontal') {\n      this.calculateHorizontalSpace();\n    } else {\n      this.calculateVerticalSpace();\n    }\n  }\n\n  getDrawableElement() {\n    this.calculateSpace();\n    const drawableElem: DrawableElem[] = [];\n    this.componentStore.plot.setAxes(this.componentStore.xAxis, this.componentStore.yAxis);\n    for (const component of Object.values(this.componentStore)) {\n      drawableElem.push(...component.getDrawableElements());\n    }\n    return drawableElem;\n  }\n}\n", "import type { SVGGroup } from '../../../diagram-api/types.js';\nimport type { DrawableElem, XYChartConfig, XYChartData, XYChartThemeConfig } from './interfaces.js';\nimport { Orchestrator } from './orchestrator.js';\n\nexport class XY<PERSON>hartBuilder {\n  static build(\n    config: XYChartConfig,\n    chartData: XYChartData,\n    chartThemeConfig: XYChartThemeConfig,\n    tmpSVGGroup: SVGGroup\n  ): DrawableElem[] {\n    const orchestrator = new Orchestrator(config, chartData, chartThemeConfig, tmpSVGGroup);\n    return orchestrator.getDrawableElement();\n  }\n}\n", "import * as configApi from '../../config.js';\nimport defaultConfig from '../../defaultConfig.js';\nimport type { SVGGroup } from '../../diagram-api/types.js';\nimport { getThemeVariables } from '../../themes/theme-default.js';\nimport { cleanAndMerge } from '../../utils.js';\nimport { sanitizeText } from '../common/common.js';\nimport {\n  clear as commonClear,\n  getAccDescription,\n  getAccTitle,\n  getDiagramTitle,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n} from '../common/commonDb.js';\nimport { XYChartBuilder } from './chartBuilder/index.js';\nimport type {\n  DrawableElem,\n  SimplePlotDataType,\n  XYChartConfig,\n  XYChartData,\n  XYChartThemeConfig,\n} from './chartBuilder/interfaces.js';\nimport { isBandAxisData, isLinearAxisData } from './chartBuilder/interfaces.js';\n\nlet plotIndex = 0;\n\nlet tmpSVGGroup: SVGGroup;\n\nlet xyChartConfig: XYChartConfig = getChartDefaultConfig();\nlet xyChartThemeConfig: XYChartThemeConfig = getChartDefaultThemeConfig();\nlet xyChartData: XYChartData = getChartDefaultData();\nlet plotColorPalette = xyChartThemeConfig.plotColorPalette.split(',').map((color) => color.trim());\nlet hasSetXAxis = false;\nlet hasSetYAxis = false;\n\ninterface NormalTextType {\n  type: 'text';\n  text: string;\n}\n\nfunction getChartDefaultThemeConfig(): XYChartThemeConfig {\n  const defaultThemeVariables = getThemeVariables();\n  const config = configApi.getConfig();\n  return cleanAndMerge(defaultThemeVariables.xyChart, config.themeVariables.xyChart);\n}\nfunction getChartDefaultConfig(): XYChartConfig {\n  const config = configApi.getConfig();\n  return cleanAndMerge<XYChartConfig>(\n    defaultConfig.xyChart as XYChartConfig,\n    config.xyChart as XYChartConfig\n  );\n}\n\nfunction getChartDefaultData(): XYChartData {\n  return {\n    yAxis: {\n      type: 'linear',\n      title: '',\n      min: Infinity,\n      max: -Infinity,\n    },\n    xAxis: {\n      type: 'band',\n      title: '',\n      categories: [],\n    },\n    title: '',\n    plots: [],\n  };\n}\n\nfunction textSanitizer(text: string) {\n  const config = configApi.getConfig();\n  return sanitizeText(text.trim(), config);\n}\n\nfunction setTmpSVGG(SVGG: SVGGroup) {\n  tmpSVGGroup = SVGG;\n}\nfunction setOrientation(orientation: string) {\n  if (orientation === 'horizontal') {\n    xyChartConfig.chartOrientation = 'horizontal';\n  } else {\n    xyChartConfig.chartOrientation = 'vertical';\n  }\n}\nfunction setXAxisTitle(title: NormalTextType) {\n  xyChartData.xAxis.title = textSanitizer(title.text);\n}\nfunction setXAxisRangeData(min: number, max: number) {\n  xyChartData.xAxis = { type: 'linear', title: xyChartData.xAxis.title, min, max };\n  hasSetXAxis = true;\n}\nfunction setXAxisBand(categories: NormalTextType[]) {\n  xyChartData.xAxis = {\n    type: 'band',\n    title: xyChartData.xAxis.title,\n    categories: categories.map((c) => textSanitizer(c.text)),\n  };\n  hasSetXAxis = true;\n}\nfunction setYAxisTitle(title: NormalTextType) {\n  xyChartData.yAxis.title = textSanitizer(title.text);\n}\nfunction setYAxisRangeData(min: number, max: number) {\n  xyChartData.yAxis = { type: 'linear', title: xyChartData.yAxis.title, min, max };\n  hasSetYAxis = true;\n}\n\n// this function does not set `hasSetYAxis` as there can be multiple data so we should calculate the range accordingly\nfunction setYAxisRangeFromPlotData(data: number[]) {\n  const minValue = Math.min(...data);\n  const maxValue = Math.max(...data);\n  const prevMinValue = isLinearAxisData(xyChartData.yAxis) ? xyChartData.yAxis.min : Infinity;\n  const prevMaxValue = isLinearAxisData(xyChartData.yAxis) ? xyChartData.yAxis.max : -Infinity;\n  xyChartData.yAxis = {\n    type: 'linear',\n    title: xyChartData.yAxis.title,\n    min: Math.min(prevMinValue, minValue),\n    max: Math.max(prevMaxValue, maxValue),\n  };\n}\n\nfunction transformDataWithoutCategory(data: number[]): SimplePlotDataType {\n  let retData: SimplePlotDataType = [];\n  if (data.length === 0) {\n    return retData;\n  }\n  if (!hasSetXAxis) {\n    const prevMinValue = isLinearAxisData(xyChartData.xAxis) ? xyChartData.xAxis.min : Infinity;\n    const prevMaxValue = isLinearAxisData(xyChartData.xAxis) ? xyChartData.xAxis.max : -Infinity;\n    setXAxisRangeData(Math.min(prevMinValue, 1), Math.max(prevMaxValue, data.length));\n  }\n  if (!hasSetYAxis) {\n    setYAxisRangeFromPlotData(data);\n  }\n\n  if (isBandAxisData(xyChartData.xAxis)) {\n    retData = xyChartData.xAxis.categories.map((c, i) => [c, data[i]]);\n  }\n\n  if (isLinearAxisData(xyChartData.xAxis)) {\n    const min = xyChartData.xAxis.min;\n    const max = xyChartData.xAxis.max;\n    const step = (max - min) / (data.length - 1);\n    const categories: string[] = [];\n    for (let i = min; i <= max; i += step) {\n      categories.push(`${i}`);\n    }\n    retData = categories.map((c, i) => [c, data[i]]);\n  }\n\n  return retData;\n}\n\nfunction getPlotColorFromPalette(plotIndex: number): string {\n  return plotColorPalette[plotIndex === 0 ? 0 : plotIndex % plotColorPalette.length];\n}\n\nfunction setLineData(title: NormalTextType, data: number[]) {\n  const plotData = transformDataWithoutCategory(data);\n  xyChartData.plots.push({\n    type: 'line',\n    strokeFill: getPlotColorFromPalette(plotIndex),\n    strokeWidth: 2,\n    data: plotData,\n  });\n  plotIndex++;\n}\n\nfunction setBarData(title: NormalTextType, data: number[]) {\n  const plotData = transformDataWithoutCategory(data);\n  xyChartData.plots.push({\n    type: 'bar',\n    fill: getPlotColorFromPalette(plotIndex),\n    data: plotData,\n  });\n  plotIndex++;\n}\n\nfunction getDrawableElem(): DrawableElem[] {\n  if (xyChartData.plots.length === 0) {\n    throw Error('No Plot to render, please provide a plot with some data');\n  }\n  xyChartData.title = getDiagramTitle();\n  return XYChartBuilder.build(xyChartConfig, xyChartData, xyChartThemeConfig, tmpSVGGroup);\n}\n\nfunction getChartThemeConfig() {\n  return xyChartThemeConfig;\n}\n\nfunction getChartConfig() {\n  return xyChartConfig;\n}\n\nconst clear = function () {\n  commonClear();\n  plotIndex = 0;\n  xyChartConfig = getChartDefaultConfig();\n  xyChartData = getChartDefaultData();\n  xyChartThemeConfig = getChartDefaultThemeConfig();\n  plotColorPalette = xyChartThemeConfig.plotColorPalette.split(',').map((color) => color.trim());\n  hasSetXAxis = false;\n  hasSetYAxis = false;\n};\n\nexport default {\n  getDrawableElem,\n  clear,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription,\n  setOrientation,\n  setXAxisTitle,\n  setXAxisRangeData,\n  setXAxisBand,\n  setYAxisTitle,\n  setYAxisRangeData,\n  setLineData,\n  setBarData,\n  setTmpSVGG,\n  getChartThemeConfig,\n  getChartConfig,\n};\n", "import type { Diagram } from '../../Diagram.js';\nimport { log } from '../../logger.js';\nimport { selectSvgElement } from '../../rendering-util/selectSvgElement.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\nimport type {\n  DrawableElem,\n  TextElem,\n  TextHorizontalPos,\n  TextVerticalPos,\n} from './chartBuilder/interfaces.js';\nimport type XYChartDB from './xychartDb.js';\n\nexport const draw = (txt: string, id: string, _version: string, diagObj: Diagram) => {\n  const db = diagObj.db as typeof XYChartDB;\n  const themeConfig = db.getChartThemeConfig();\n  const chartConfig = db.getChartConfig();\n  function getDominantBaseLine(horizontalPos: TextVerticalPos) {\n    return horizontalPos === 'top' ? 'text-before-edge' : 'middle';\n  }\n\n  function getTextAnchor(verticalPos: TextHorizontalPos) {\n    return verticalPos === 'left' ? 'start' : verticalPos === 'right' ? 'end' : 'middle';\n  }\n\n  function getTextTransformation(data: TextElem) {\n    return `translate(${data.x}, ${data.y}) rotate(${data.rotation || 0})`;\n  }\n\n  log.debug('Rendering xychart chart\\n' + txt);\n\n  const svg = selectSvgElement(id);\n  const group = svg.append('g').attr('class', 'main');\n  const background = group\n    .append('rect')\n    .attr('width', chartConfig.width)\n    .attr('height', chartConfig.height)\n    .attr('class', 'background');\n\n  // @ts-ignore: TODO Fix ts errors\n  configureSvgSize(svg, chartConfig.height, chartConfig.width, true);\n\n  svg.attr('viewBox', `0 0 ${chartConfig.width} ${chartConfig.height}`);\n\n  background.attr('fill', themeConfig.backgroundColor);\n\n  db.setTmpSVGG(svg.append('g').attr('class', 'mermaid-tmp-group'));\n\n  const shapes: DrawableElem[] = db.getDrawableElem();\n\n  const groups: Record<string, any> = {};\n\n  function getGroup(gList: string[]) {\n    let elem = group;\n    let prefix = '';\n    for (const [i] of gList.entries()) {\n      let parent = group;\n      if (i > 0 && groups[prefix]) {\n        parent = groups[prefix];\n      }\n      prefix += gList[i];\n      elem = groups[prefix];\n      if (!elem) {\n        elem = groups[prefix] = parent.append('g').attr('class', gList[i]);\n      }\n    }\n    return elem;\n  }\n\n  for (const shape of shapes) {\n    if (shape.data.length === 0) {\n      continue;\n    }\n\n    const shapeGroup = getGroup(shape.groupTexts);\n\n    switch (shape.type) {\n      case 'rect':\n        shapeGroup\n          .selectAll('rect')\n          .data(shape.data)\n          .enter()\n          .append('rect')\n          .attr('x', (data) => data.x)\n          .attr('y', (data) => data.y)\n          .attr('width', (data) => data.width)\n          .attr('height', (data) => data.height)\n          .attr('fill', (data) => data.fill)\n          .attr('stroke', (data) => data.strokeFill)\n          .attr('stroke-width', (data) => data.strokeWidth);\n        break;\n      case 'text':\n        shapeGroup\n          .selectAll('text')\n          .data(shape.data)\n          .enter()\n          .append('text')\n          .attr('x', 0)\n          .attr('y', 0)\n          .attr('fill', (data) => data.fill)\n          .attr('font-size', (data) => data.fontSize)\n          .attr('dominant-baseline', (data) => getDominantBaseLine(data.verticalPos))\n          .attr('text-anchor', (data) => getTextAnchor(data.horizontalPos))\n          .attr('transform', (data) => getTextTransformation(data))\n          .text((data) => data.text);\n        break;\n      case 'path':\n        shapeGroup\n          .selectAll('path')\n          .data(shape.data)\n          .enter()\n          .append('path')\n          .attr('d', (data) => data.path)\n          .attr('fill', (data) => (data.fill ? data.fill : 'none'))\n          .attr('stroke', (data) => data.strokeFill)\n          .attr('stroke-width', (data) => data.strokeWidth);\n        break;\n    }\n  }\n};\n\nexport default {\n  draw,\n};\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\n// @ts-ignore: <PERSON><PERSON> doesn't support types.\nimport parser from './parser/xychart.jison';\nimport db from './xychartDb.js';\nimport renderer from './xychartRenderer.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  db,\n  renderer,\n};\n"], "mappings": "oYAyEA,IAAIA,GAAU,UAAU,CACxB,IAAIC,EAAEC,EAAA,SAASC,EAAEC,EAAEH,EAAEI,EAAE,CAAC,IAAIJ,EAAEA,GAAG,CAAC,EAAEI,EAAEF,EAAE,OAAOE,IAAIJ,EAAEE,EAAEE,CAAC,CAAC,EAAED,EAAE,CAAC,OAAOH,CAAC,EAAhE,KAAkEK,EAAI,CAAC,EAAE,GAAG,GAAG,GAAG,<PERSON>G,<PERSON>G,GAAG,GAAG,EAAE,EAAEC,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,<PERSON>G,<PERSON>G,<PERSON>G,GAAG,GAAG,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,GAAI,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,EAAI,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAC3iBhC,GAAS,CAAC,MAAOE,EAAA,UAAkB,CAAE,EAApB,SACrB,GAAI,CAAC,EACL,SAAU,CAAC,MAAQ,EAAE,MAAQ,EAAE,IAAM,EAAE,QAAU,EAAE,YAAc,EAAE,SAAW,EAAE,kBAAoB,EAAE,UAAY,EAAE,MAAQ,GAAG,KAAO,GAAG,OAAS,GAAG,WAAa,GAAG,OAAS,GAAG,WAAa,GAAG,KAAO,GAAG,SAAW,GAAG,IAAM,GAAG,UAAY,GAAG,gBAAkB,GAAG,UAAY,GAAG,gBAAkB,GAAG,0BAA4B,GAAG,oBAAsB,GAAG,sBAAwB,GAAG,kBAAoB,GAAG,oBAAsB,GAAG,MAAQ,GAAG,UAAY,GAAG,SAAW,GAAG,gBAAkB,GAAG,oBAAsB,GAAG,UAAY,GAAG,QAAU,GAAG,KAAO,GAAG,IAAM,GAAG,SAAW,GAAG,IAAM,GAAG,OAAS,GAAG,cAAgB,GAAG,IAAM,GAAG,IAAM,GAAG,MAAQ,GAAG,KAAO,GAAG,OAAS,GAAG,KAAO,GAAG,IAAM,GAAG,KAAO,GAAG,MAAQ,GAAG,WAAa,GAAG,QAAU,EAAE,KAAO,CAAC,EACvuB,WAAY,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,oBAAoB,GAAG,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAG,OAAO,GAAG,MAAM,GAAG,YAAY,GAAG,kBAAkB,GAAG,YAAY,GAAG,kBAAkB,GAAG,4BAA4B,GAAG,sBAAsB,GAAG,oBAAoB,GAAG,sBAAsB,GAAG,QAAQ,GAAG,kBAAkB,GAAG,UAAU,GAAG,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,SAAS,GAAG,MAAM,GAAG,MAAM,GAAG,QAAQ,GAAG,OAAO,GAAG,SAAS,GAAG,OAAO,GAAG,MAAM,GAAG,OAAO,GAAG,QAAQ,GAAG,YAAY,EAC9d,aAAc,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAChW,cAAeA,EAAA,SAAmB+B,EAAQC,EAAQC,EAAUC,EAAIC,EAAyBC,EAAiBC,EAAiB,CAG3H,IAAIC,EAAKF,EAAG,OAAS,EACrB,OAAQD,EAAS,CACjB,IAAK,GACJD,EAAG,eAAeE,EAAGE,CAAE,CAAC,EACzB,MACA,IAAK,GACJJ,EAAG,gBAAgBE,EAAGE,CAAE,EAAE,KAAK,KAAK,CAAC,EACtC,MACA,IAAK,IACJJ,EAAG,YAAY,CAAC,KAAM,GAAI,KAAM,MAAM,EAAGE,EAAGE,CAAE,CAAC,EAChD,MACA,IAAK,IACJJ,EAAG,YAAYE,EAAGE,EAAG,CAAC,EAAGF,EAAGE,CAAE,CAAC,EAChC,MACA,IAAK,IACJJ,EAAG,WAAW,CAAC,KAAM,GAAI,KAAM,MAAM,EAAGE,EAAGE,CAAE,CAAC,EAC/C,MACA,IAAK,IACJJ,EAAG,WAAWE,EAAGE,EAAG,CAAC,EAAGF,EAAGE,CAAE,CAAC,EAC/B,MACA,IAAK,IACJ,KAAK,EAAEF,EAAGE,CAAE,EAAE,KAAK,EAAEJ,EAAG,YAAY,KAAK,CAAC,EAC3C,MACA,IAAK,IAAI,IAAK,IACb,KAAK,EAAEE,EAAGE,CAAE,EAAE,KAAK,EAAEJ,EAAG,kBAAkB,KAAK,CAAC,EACjD,MACA,IAAK,IACJ,KAAK,EAAIE,EAAGE,EAAG,CAAC,EACjB,MACA,IAAK,IACJ,KAAK,EAAI,CAAC,OAAOF,EAAGE,EAAG,CAAC,CAAC,EAAG,GAAGF,EAAGE,CAAE,CAAC,EACtC,MACA,IAAK,IACJ,KAAK,EAAI,CAAC,OAAOF,EAAGE,CAAE,CAAC,CAAC,EACzB,MACA,IAAK,IACLJ,EAAG,cAAcE,EAAGE,CAAE,CAAC,EACvB,MACA,IAAK,IACLJ,EAAG,cAAcE,EAAGE,EAAG,CAAC,CAAC,EACzB,MACA,IAAK,IACLJ,EAAG,cAAc,CAAC,KAAM,OAAQ,KAAM,EAAE,CAAC,EACzC,MACA,IAAK,IACLA,EAAG,aAAaE,EAAGE,CAAE,CAAC,EACtB,MACA,IAAK,IACLJ,EAAG,kBAAkB,OAAOE,EAAGE,EAAG,CAAC,CAAC,EAAG,OAAOF,EAAGE,CAAE,CAAC,CAAC,EACrD,MACA,IAAK,IACL,KAAK,EAAIF,EAAGE,EAAG,CAAC,EAChB,MACA,IAAK,IACJ,KAAK,EAAI,CAACF,EAAGE,EAAG,CAAC,EAAG,GAAGF,EAAGE,CAAE,CAAC,EAC9B,MACA,IAAK,IACJ,KAAK,EAAI,CAACF,EAAGE,CAAE,CAAC,EACjB,MACA,IAAK,IACLJ,EAAG,cAAcE,EAAGE,CAAE,CAAC,EACvB,MACA,IAAK,IACLJ,EAAG,cAAcE,EAAGE,EAAG,CAAC,CAAC,EACzB,MACA,IAAK,IACLJ,EAAG,cAAc,CAAC,KAAM,OAAQ,KAAM,EAAE,CAAC,EACzC,MACA,IAAK,IACLA,EAAG,kBAAkB,OAAOE,EAAGE,EAAG,CAAC,CAAC,EAAG,OAAOF,EAAGE,CAAE,CAAC,CAAC,EACrD,MACA,IAAK,IACJ,KAAK,EAAE,CAAC,KAAKF,EAAGE,CAAE,EAAG,KAAM,MAAM,EAClC,MACA,IAAK,IACJ,KAAK,EAAE,CAAC,KAAMF,EAAGE,CAAE,EAAG,KAAM,MAAM,EACnC,MACA,IAAK,IACJ,KAAK,EAAE,CAAC,KAAMF,EAAGE,CAAE,EAAG,KAAM,UAAU,EACvC,MACA,IAAK,IACL,KAAK,EAAEF,EAAGE,CAAE,EACZ,MACA,IAAK,IACL,KAAK,EAAEF,EAAGE,EAAG,CAAC,EAAE,GAAGF,EAAGE,CAAE,EACxB,KACA,CACA,EA1Fe,aA2Ff,MAAO,CAACvC,EAAEK,EAAIC,EAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAEC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEV,EAAEK,EAAIC,EAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAEC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAEV,EAAEK,EAAIC,EAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAEC,EAAI,EAAE,CAAC,EAAE,EAAE,EAAE,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEV,EAAEW,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEX,EAAEW,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEX,EAAEW,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEX,EAAEK,EAAIC,EAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAEC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEV,EAAEW,EAAI,CAAC,EAAE,CAAC,CAAC,EAAEX,EAAEK,EAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,GAAGG,EAAI,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGE,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,GAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGb,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGG,GAAI,GAAG,GAAG,GAAG,GAAG,GAAGd,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGI,EAAI,GAAG,GAAG,GAAGf,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGI,EAAI,GAAG,GAAG,GAAGf,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAEvB,EAAE4B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE5B,EAAE4B,EAAI,CAAC,EAAE,CAAC,CAAC,EAAE5B,EAAE4B,EAAI,CAAC,EAAE,CAAC,CAAC,EAAE5B,EAAE6B,GAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAGf,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAEvB,EAAE6B,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE7B,EAAE6B,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE7B,EAAE8B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE9B,EAAE8B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE9B,EAAE8B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE9B,EAAE8B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE9B,EAAE8B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE9B,EAAE8B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE9B,EAAE8B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE9B,EAAE8B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE9B,EAAE8B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE9B,EAAE8B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE9B,EAAE8B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE9B,EAAE4B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE5B,EAAE4B,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGJ,EAAI,GAAGC,EAAG,CAAC,EAAEzB,EAAE4B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE5B,EAAE4B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGhB,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAEvB,EAAE4B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE5B,EAAE4B,EAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAGF,EAAG,CAAC,EAAE1B,EAAE4B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE5B,EAAE4B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAGD,CAAG,EAAE,CAAC,GAAG,GAAG,GAAGI,EAAG,EAAE/B,EAAE4B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAGD,CAAG,EAAE3B,EAAE4B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE5B,EAAE4B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE5B,EAAE8B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE9B,EAAE4B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE5B,EAAE4B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE5B,EAAE4B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE5B,EAAE4B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE5B,EAAE4B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE5B,EAAE4B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGhB,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAEvB,EAAE4B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE5B,EAAE4B,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAGG,EAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EACzlE,eAAgB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAC9D,WAAY9B,EAAA,SAAqBuC,EAAKC,EAAM,CACxC,GAAIA,EAAK,YACL,KAAK,MAAMD,CAAG,MACX,CACH,IAAIE,EAAQ,IAAI,MAAMF,CAAG,EACzB,MAAAE,EAAM,KAAOD,EACPC,CACV,CACJ,EARY,cASZ,MAAOzC,EAAA,SAAe0C,EAAO,CACzB,IAAIC,EAAO,KAAMC,EAAQ,CAAC,CAAC,EAAGC,EAAS,CAAC,EAAGC,EAAS,CAAC,IAAI,EAAGC,EAAS,CAAC,EAAGC,EAAQ,KAAK,MAAOjB,EAAS,GAAIE,EAAW,EAAGD,GAAS,EAAGiB,GAAa,EAAGC,GAAS,EAAGC,GAAM,EAClKC,GAAOL,EAAO,MAAM,KAAK,UAAW,CAAC,EACrCM,EAAQ,OAAO,OAAO,KAAK,KAAK,EAChCC,EAAc,CAAE,GAAI,CAAC,CAAE,EAC3B,QAASrD,MAAK,KAAK,GACX,OAAO,UAAU,eAAe,KAAK,KAAK,GAAIA,EAAC,IAC/CqD,EAAY,GAAGrD,EAAC,EAAI,KAAK,GAAGA,EAAC,GAGrCoD,EAAM,SAASX,EAAOY,EAAY,EAAE,EACpCA,EAAY,GAAG,MAAQD,EACvBC,EAAY,GAAG,OAAS,KACpB,OAAOD,EAAM,OAAU,MACvBA,EAAM,OAAS,CAAC,GAEpB,IAAIE,GAAQF,EAAM,OAClBN,EAAO,KAAKQ,EAAK,EACjB,IAAIC,GAASH,EAAM,SAAWA,EAAM,QAAQ,OACxC,OAAOC,EAAY,GAAG,YAAe,WACrC,KAAK,WAAaA,EAAY,GAAG,WAEjC,KAAK,WAAa,OAAO,eAAe,IAAI,EAAE,WAElD,SAASG,GAASC,EAAG,CACjBd,EAAM,OAASA,EAAM,OAAS,EAAIc,EAClCZ,EAAO,OAASA,EAAO,OAASY,EAChCX,EAAO,OAASA,EAAO,OAASW,CACpC,CAJS1D,EAAAyD,GAAA,YAKD,SAASE,IAAM,CACf,IAAIC,EACJ,OAAAA,EAAQf,EAAO,IAAI,GAAKQ,EAAM,IAAI,GAAKF,GACnC,OAAOS,GAAU,WACbA,aAAiB,QACjBf,EAASe,EACTA,EAAQf,EAAO,IAAI,GAEvBe,EAAQjB,EAAK,SAASiB,CAAK,GAAKA,GAE7BA,CACX,CAXa5D,EAAA2D,GAAA,OAajB,QADIE,EAAQC,GAAgBC,EAAOC,EAAQC,GAAGC,GAAGC,EAAQ,CAAC,EAAGC,EAAGC,EAAKC,GAAUC,IAClE,CAUT,GATAR,EAAQnB,EAAMA,EAAM,OAAS,CAAC,EAC1B,KAAK,eAAemB,CAAK,EACzBC,EAAS,KAAK,eAAeD,CAAK,IAE9BF,IAAW,MAAQ,OAAOA,EAAU,OACpCA,EAASF,GAAI,GAEjBK,EAAShB,EAAMe,CAAK,GAAKf,EAAMe,CAAK,EAAEF,CAAM,GAE5C,OAAOG,EAAW,KAAe,CAACA,EAAO,QAAU,CAACA,EAAO,CAAC,EAAG,CAC/D,IAAIQ,GAAS,GACbD,EAAW,CAAC,EACZ,IAAKH,KAAKpB,EAAMe,CAAK,EACb,KAAK,WAAWK,CAAC,GAAKA,EAAIlB,IAC1BqB,EAAS,KAAK,IAAO,KAAK,WAAWH,CAAC,EAAI,GAAI,EAGlDf,EAAM,aACNmB,GAAS,wBAA0BvC,EAAW,GAAK;AAAA,EAAQoB,EAAM,aAAa,EAAI;AAAA,YAAiBkB,EAAS,KAAK,IAAI,EAAI,WAAc,KAAK,WAAWV,CAAM,GAAKA,GAAU,IAE5KW,GAAS,wBAA0BvC,EAAW,GAAK,iBAAmB4B,GAAUV,GAAM,eAAiB,KAAQ,KAAK,WAAWU,CAAM,GAAKA,GAAU,KAExJ,KAAK,WAAWW,GAAQ,CACpB,KAAMnB,EAAM,MACZ,MAAO,KAAK,WAAWQ,CAAM,GAAKA,EAClC,KAAMR,EAAM,SACZ,IAAKE,GACL,SAAUgB,CACd,CAAC,CACL,CACA,GAAIP,EAAO,CAAC,YAAa,OAASA,EAAO,OAAS,EAC9C,MAAM,IAAI,MAAM,oDAAsDD,EAAQ,YAAcF,CAAM,EAEtG,OAAQG,EAAO,CAAC,EAAG,CACnB,IAAK,GACDpB,EAAM,KAAKiB,CAAM,EACjBf,EAAO,KAAKO,EAAM,MAAM,EACxBN,EAAO,KAAKM,EAAM,MAAM,EACxBT,EAAM,KAAKoB,EAAO,CAAC,CAAC,EACpBH,EAAS,KACJC,IASDD,EAASC,GACTA,GAAiB,OATjB9B,GAASqB,EAAM,OACftB,EAASsB,EAAM,OACfpB,EAAWoB,EAAM,SACjBE,GAAQF,EAAM,OACVJ,GAAa,GACbA,MAMR,MACJ,IAAK,GAwBD,GAvBAoB,EAAM,KAAK,aAAaL,EAAO,CAAC,CAAC,EAAE,CAAC,EACpCG,EAAM,EAAIrB,EAAOA,EAAO,OAASuB,CAAG,EACpCF,EAAM,GAAK,CACP,WAAYpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,WAC/C,UAAWtB,EAAOA,EAAO,OAAS,CAAC,EAAE,UACrC,aAAcA,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,aACjD,YAAatB,EAAOA,EAAO,OAAS,CAAC,EAAE,WAC3C,EACIS,KACAW,EAAM,GAAG,MAAQ,CACbpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,MAAM,CAAC,EAC1CtB,EAAOA,EAAO,OAAS,CAAC,EAAE,MAAM,CAAC,CACrC,GAEJmB,GAAI,KAAK,cAAc,MAAMC,EAAO,CAChCpC,EACAC,GACAC,EACAqB,EAAY,GACZU,EAAO,CAAC,EACRlB,EACAC,CACJ,EAAE,OAAOK,EAAI,CAAC,EACV,OAAOc,GAAM,IACb,OAAOA,GAEPG,IACAzB,EAAQA,EAAM,MAAM,EAAG,GAAKyB,EAAM,CAAC,EACnCvB,EAASA,EAAO,MAAM,EAAG,GAAKuB,CAAG,EACjCtB,EAASA,EAAO,MAAM,EAAG,GAAKsB,CAAG,GAErCzB,EAAM,KAAK,KAAK,aAAaoB,EAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAC1ClB,EAAO,KAAKqB,EAAM,CAAC,EACnBpB,EAAO,KAAKoB,EAAM,EAAE,EACpBG,GAAWtB,EAAMJ,EAAMA,EAAM,OAAS,CAAC,CAAC,EAAEA,EAAMA,EAAM,OAAS,CAAC,CAAC,EACjEA,EAAM,KAAK0B,EAAQ,EACnB,MACJ,IAAK,GACD,MAAO,EACX,CACJ,CACA,MAAO,EACX,EA3IO,QA2IN,EAGGjB,GAAS,UAAU,CACvB,IAAIA,EAAS,CAEb,IAAI,EAEJ,WAAWrD,EAAA,SAAoBuC,EAAKC,EAAM,CAClC,GAAI,KAAK,GAAG,OACR,KAAK,GAAG,OAAO,WAAWD,EAAKC,CAAI,MAEnC,OAAM,IAAI,MAAMD,CAAG,CAE3B,EANO,cASX,SAASvC,EAAA,SAAU0C,EAAOR,EAAI,CACtB,YAAK,GAAKA,GAAM,KAAK,IAAM,CAAC,EAC5B,KAAK,OAASQ,EACd,KAAK,MAAQ,KAAK,WAAa,KAAK,KAAO,GAC3C,KAAK,SAAW,KAAK,OAAS,EAC9B,KAAK,OAAS,KAAK,QAAU,KAAK,MAAQ,GAC1C,KAAK,eAAiB,CAAC,SAAS,EAChC,KAAK,OAAS,CACV,WAAY,EACZ,aAAc,EACd,UAAW,EACX,YAAa,CACjB,EACI,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAAC,EAAE,CAAC,GAE5B,KAAK,OAAS,EACP,IACX,EAlBK,YAqBT,MAAM1C,EAAA,UAAY,CACV,IAAIyE,EAAK,KAAK,OAAO,CAAC,EACtB,KAAK,QAAUA,EACf,KAAK,SACL,KAAK,SACL,KAAK,OAASA,EACd,KAAK,SAAWA,EAChB,IAAIC,EAAQD,EAAG,MAAM,iBAAiB,EACtC,OAAIC,GACA,KAAK,WACL,KAAK,OAAO,aAEZ,KAAK,OAAO,cAEZ,KAAK,QAAQ,QACb,KAAK,OAAO,MAAM,CAAC,IAGvB,KAAK,OAAS,KAAK,OAAO,MAAM,CAAC,EAC1BD,CACX,EApBE,SAuBN,MAAMzE,EAAA,SAAUyE,EAAI,CACZ,IAAIJ,EAAMI,EAAG,OACTC,EAAQD,EAAG,MAAM,eAAe,EAEpC,KAAK,OAASA,EAAK,KAAK,OACxB,KAAK,OAAS,KAAK,OAAO,OAAO,EAAG,KAAK,OAAO,OAASJ,CAAG,EAE5D,KAAK,QAAUA,EACf,IAAIM,EAAW,KAAK,MAAM,MAAM,eAAe,EAC/C,KAAK,MAAQ,KAAK,MAAM,OAAO,EAAG,KAAK,MAAM,OAAS,CAAC,EACvD,KAAK,QAAU,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,CAAC,EAEzDD,EAAM,OAAS,IACf,KAAK,UAAYA,EAAM,OAAS,GAEpC,IAAIR,EAAI,KAAK,OAAO,MAEpB,YAAK,OAAS,CACV,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,aAC1B,YAAaQ,GACRA,EAAM,SAAWC,EAAS,OAAS,KAAK,OAAO,aAAe,GAC5DA,EAASA,EAAS,OAASD,EAAM,MAAM,EAAE,OAASA,EAAM,CAAC,EAAE,OAChE,KAAK,OAAO,aAAeL,CACjC,EAEI,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAACH,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,KAAK,OAASG,CAAG,GAEvD,KAAK,OAAS,KAAK,OAAO,OACnB,IACX,EAhCE,SAmCN,KAAKrE,EAAA,UAAY,CACT,YAAK,MAAQ,GACN,IACX,EAHC,QAML,OAAOA,EAAA,UAAY,CACX,GAAI,KAAK,QAAQ,gBACb,KAAK,WAAa,OAElB,QAAO,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAAqI,KAAK,aAAa,EAAG,CAC9N,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACf,CAAC,EAGL,OAAO,IACX,EAZG,UAeP,KAAKA,EAAA,SAAU0D,EAAG,CACV,KAAK,MAAM,KAAK,MAAM,MAAMA,CAAC,CAAC,CAClC,EAFC,QAKL,UAAU1D,EAAA,UAAY,CACd,IAAI4E,EAAO,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,KAAK,MAAM,MAAM,EACzE,OAAQA,EAAK,OAAS,GAAK,MAAM,IAAMA,EAAK,OAAO,GAAG,EAAE,QAAQ,MAAO,EAAE,CAC7E,EAHM,aAMV,cAAc5E,EAAA,UAAY,CAClB,IAAI6E,EAAO,KAAK,MAChB,OAAIA,EAAK,OAAS,KACdA,GAAQ,KAAK,OAAO,OAAO,EAAG,GAAGA,EAAK,MAAM,IAExCA,EAAK,OAAO,EAAE,EAAE,GAAKA,EAAK,OAAS,GAAK,MAAQ,KAAK,QAAQ,MAAO,EAAE,CAClF,EANU,iBASd,aAAa7E,EAAA,UAAY,CACjB,IAAI8E,EAAM,KAAK,UAAU,EACrB,EAAI,IAAI,MAAMA,EAAI,OAAS,CAAC,EAAE,KAAK,GAAG,EAC1C,OAAOA,EAAM,KAAK,cAAc,EAAI;AAAA,EAAO,EAAI,GACnD,EAJS,gBAOb,WAAW9E,EAAA,SAAS+E,EAAOC,EAAc,CACjC,IAAIpB,EACAc,EACAO,EAwDJ,GAtDI,KAAK,QAAQ,kBAEbA,EAAS,CACL,SAAU,KAAK,SACf,OAAQ,CACJ,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,UAChB,aAAc,KAAK,OAAO,aAC1B,YAAa,KAAK,OAAO,WAC7B,EACA,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,QAAS,KAAK,QACd,QAAS,KAAK,QACd,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,OAAQ,KAAK,OACb,GAAI,KAAK,GACT,eAAgB,KAAK,eAAe,MAAM,CAAC,EAC3C,KAAM,KAAK,IACf,EACI,KAAK,QAAQ,SACbA,EAAO,OAAO,MAAQ,KAAK,OAAO,MAAM,MAAM,CAAC,IAIvDP,EAAQK,EAAM,CAAC,EAAE,MAAM,iBAAiB,EACpCL,IACA,KAAK,UAAYA,EAAM,QAE3B,KAAK,OAAS,CACV,WAAY,KAAK,OAAO,UACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,YAC1B,YAAaA,EACAA,EAAMA,EAAM,OAAS,CAAC,EAAE,OAASA,EAAMA,EAAM,OAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,OAC5E,KAAK,OAAO,YAAcK,EAAM,CAAC,EAAE,MACpD,EACA,KAAK,QAAUA,EAAM,CAAC,EACtB,KAAK,OAASA,EAAM,CAAC,EACrB,KAAK,QAAUA,EACf,KAAK,OAAS,KAAK,OAAO,OACtB,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAAC,KAAK,OAAQ,KAAK,QAAU,KAAK,MAAM,GAEhE,KAAK,MAAQ,GACb,KAAK,WAAa,GAClB,KAAK,OAAS,KAAK,OAAO,MAAMA,EAAM,CAAC,EAAE,MAAM,EAC/C,KAAK,SAAWA,EAAM,CAAC,EACvBnB,EAAQ,KAAK,cAAc,KAAK,KAAM,KAAK,GAAI,KAAMoB,EAAc,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAClH,KAAK,MAAQ,KAAK,SAClB,KAAK,KAAO,IAEZpB,EACA,OAAOA,EACJ,GAAI,KAAK,WAAY,CAExB,QAAS3D,KAAKgF,EACV,KAAKhF,CAAC,EAAIgF,EAAOhF,CAAC,EAEtB,MAAO,EACX,CACA,MAAO,EACX,EArEO,cAwEX,KAAKD,EAAA,UAAY,CACT,GAAI,KAAK,KACL,OAAO,KAAK,IAEX,KAAK,SACN,KAAK,KAAO,IAGhB,IAAI4D,EACAmB,EACAG,EACAC,EACC,KAAK,QACN,KAAK,OAAS,GACd,KAAK,MAAQ,IAGjB,QADIC,EAAQ,KAAK,cAAc,EACtBC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAE9B,GADAH,EAAY,KAAK,OAAO,MAAM,KAAK,MAAME,EAAMC,CAAC,CAAC,CAAC,EAC9CH,IAAc,CAACH,GAASG,EAAU,CAAC,EAAE,OAASH,EAAM,CAAC,EAAE,SAGvD,GAFAA,EAAQG,EACRC,EAAQE,EACJ,KAAK,QAAQ,gBAAiB,CAE9B,GADAzB,EAAQ,KAAK,WAAWsB,EAAWE,EAAMC,CAAC,CAAC,EACvCzB,IAAU,GACV,OAAOA,EACJ,GAAI,KAAK,WAAY,CACxBmB,EAAQ,GACR,QACJ,KAEI,OAAO,EAEf,SAAW,CAAC,KAAK,QAAQ,KACrB,MAIZ,OAAIA,GACAnB,EAAQ,KAAK,WAAWmB,EAAOK,EAAMD,CAAK,CAAC,EACvCvB,IAAU,GACHA,EAGJ,IAEP,KAAK,SAAW,GACT,KAAK,IAEL,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAA2B,KAAK,aAAa,EAAG,CACpH,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACf,CAAC,CAET,EAvDC,QA0DL,IAAI5D,EAAA,UAAgB,CACZ,IAAIkE,EAAI,KAAK,KAAK,EAClB,OAAIA,GAGO,KAAK,IAAI,CAExB,EAPA,OAUJ,MAAMlE,EAAA,SAAgBsF,EAAW,CACzB,KAAK,eAAe,KAAKA,CAAS,CACtC,EAFE,SAKN,SAAStF,EAAA,UAAqB,CACtB,IAAI0D,EAAI,KAAK,eAAe,OAAS,EACrC,OAAIA,EAAI,EACG,KAAK,eAAe,IAAI,EAExB,KAAK,eAAe,CAAC,CAEpC,EAPK,YAUT,cAAc1D,EAAA,UAA0B,CAChC,OAAI,KAAK,eAAe,QAAU,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,EACzE,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAAE,MAErE,KAAK,WAAW,QAAW,KAE1C,EANU,iBASd,SAASA,EAAA,SAAmB0D,EAAG,CAEvB,OADAA,EAAI,KAAK,eAAe,OAAS,EAAI,KAAK,IAAIA,GAAK,CAAC,EAChDA,GAAK,EACE,KAAK,eAAeA,CAAC,EAErB,SAEf,EAPK,YAUT,UAAU1D,EAAA,SAAoBsF,EAAW,CACjC,KAAK,MAAMA,CAAS,CACxB,EAFM,aAKV,eAAetF,EAAA,UAA0B,CACjC,OAAO,KAAK,eAAe,MAC/B,EAFW,kBAGf,QAAS,CAAC,mBAAmB,EAAI,EACjC,cAAeA,EAAA,SAAmBkC,EAAGqD,EAAIC,EAA0BC,EAAU,CAC7E,IAAIC,EAAQD,EACZ,OAAOD,EAA2B,CAClC,IAAK,GACL,MACA,IAAK,GACL,MACA,IAAK,GAAG,YAAK,SAAS,EAAU,GAChC,MACA,IAAK,GAAG,YAAK,SAAS,EAAU,GAChC,MACA,IAAK,GAAE,MAAO,IAEd,IAAK,GACL,MACA,IAAK,GAAG,MAAO,IAEf,IAAK,GAAG,YAAK,UAAU,WAAW,EAAS,GAC3C,MACA,IAAK,GAAG,YAAK,SAAS,EAAU,kBAChC,MACA,IAAK,GAAG,YAAK,UAAU,WAAW,EAAS,GAC3C,MACA,IAAK,IAAI,YAAK,SAAS,EAAU,kBACjC,MACA,IAAK,IAAI,KAAK,UAAU,qBAAqB,EAC7C,MACA,IAAK,IAAI,KAAK,SAAS,EACvB,MACA,IAAK,IAAI,MAAO,4BAEhB,IAAK,IAAG,MAAO,GAEf,IAAK,IAAG,MAAO,GAEf,IAAK,IAAI,YAAK,UAAU,WAAW,EAAU,SAC7C,MACA,IAAK,IAAI,YAAK,UAAU,WAAW,EAAU,SAC7C,MACA,IAAK,IAAI,YAAK,UAAU,gBAAgB,EAAU,GAClD,MACA,IAAK,IAAI,MAAO,IAEhB,IAAK,IAAI,YAAK,UAAU,MAAM,EAAU,GACxC,MACA,IAAK,IAAI,YAAK,UAAU,MAAM,EAAU,GACxC,MACA,IAAK,IAAI,YAAK,UAAU,YAAY,EAAU,GAC9C,MACA,IAAK,IAAI,MAAO,IAEhB,IAAK,IAAI,YAAK,SAAS,EAAU,GACjC,MACA,IAAK,IAAI,KAAK,SAAS,EACvB,MACA,IAAK,IAAG,KAAK,UAAU,QAAQ,EAC/B,MACA,IAAK,IAAG,KAAK,SAAS,EACtB,MACA,IAAK,IAAG,MAAO,MAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,QAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IACL,MACA,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,GAEf,CACA,EAhGe,aAiGf,MAAO,CAAC,uBAAuB,sBAAsB,gBAAgB,gBAAgB,gBAAgB,iBAAiB,gBAAgB,wBAAwB,wBAAwB,wBAAwB,wBAAwB,yBAAyB,WAAW,eAAe,uBAAuB,gCAAgC,iBAAiB,iBAAiB,WAAW,YAAY,eAAe,cAAc,WAAW,qCAAqC,WAAW,iLAAiL,YAAY,YAAY,cAAc,WAAW,WAAW,kBAAkB,UAAU,WAAW,UAAU,UAAU,WAAW,UAAU,aAAa,WAAW,UAAU,UAAU,eAAe,YAAY,UAAU,SAAS,EACx3B,WAAY,CAAC,WAAa,CAAC,MAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAI,EAAE,KAAO,CAAC,MAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAI,EAAE,eAAiB,CAAC,MAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAI,EAAE,UAAY,CAAC,MAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAI,EAAE,oBAAsB,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,CAAC,EAAE,UAAY,EAAK,EAAE,MAAQ,CAAC,MAAQ,CAAC,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,EAAE,UAAY,EAAK,EAAE,OAAS,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,QAAU,CAAC,MAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAI,CAAC,CAC/8B,EACA,OAAOnC,CACP,EAAG,EACHvD,GAAO,MAAQuD,GACf,SAASsC,IAAU,CACjB,KAAK,GAAK,CAAC,CACb,CAFS,OAAA3F,EAAA2F,GAAA,UAGTA,GAAO,UAAY7F,GAAOA,GAAO,OAAS6F,GACnC,IAAIA,EACX,EAAG,EACF7F,GAAO,OAASA,GAEhB,IAAO8F,GAAQC,GC3sBT,SAASC,GAAUC,EAAqC,CAC7D,OAAOA,EAAK,OAAS,KACvB,CAFgBC,EAAAF,GAAA,aAmBT,SAASG,GAAeF,EAA8C,CAC3E,OAAOA,EAAK,OAAS,MACvB,CAFgBC,EAAAC,GAAA,kBAIT,SAASC,EAAiBH,EAAgD,CAC/E,OAAOA,EAAK,OAAS,QACvB,CAFgBC,EAAAE,EAAA,oBC3DT,IAAMC,EAAN,KAAyE,CAC9E,YAAoBC,EAAuB,CAAvB,iBAAAA,CAAwB,CAT9C,MAQgF,CAAAC,EAAA,wCAE9E,gBAAgBC,EAAiBC,EAA6B,CAC5D,GAAI,CAAC,KAAK,YACR,MAAO,CACL,MAAOD,EAAM,OAAO,CAACE,EAAKC,IAAQ,KAAK,IAAIA,EAAI,OAAQD,CAAG,EAAG,CAAC,EAAID,EAClE,OAAQA,CACV,EAGF,IAAMG,EAAuB,CAC3B,MAAO,EACP,OAAQ,CACV,EAEMC,EAAO,KAAK,YACf,OAAO,GAAG,EACV,KAAK,aAAc,QAAQ,EAC3B,KAAK,YAAaJ,CAAQ,EAE7B,QAAWK,KAAKN,EAAO,CACrB,IAAMO,EAAOC,GAAuBH,EAAM,EAAGC,CAAC,EACxCG,EAAQF,EAAOA,EAAK,MAAQD,EAAE,OAASL,EACvCS,EAASH,EAAOA,EAAK,OAASN,EACpCG,EAAU,MAAQ,KAAK,IAAIA,EAAU,MAAOK,CAAK,EACjDL,EAAU,OAAS,KAAK,IAAIA,EAAU,OAAQM,CAAM,CACtD,CACA,OAAAL,EAAK,OAAO,EACLD,CACT,CACF,ECxBO,IAAeO,EAAf,KAAwC,CAY7C,YACYC,EACAC,EACAC,EACAC,EACV,CAJU,gBAAAH,EACA,WAAAC,EACA,6BAAAC,EACA,qBAAAC,EAfZ,KAAU,aAA6B,CAAE,EAAG,EAAG,EAAG,EAAG,MAAO,EAAG,OAAQ,CAAE,EACzE,KAAU,aAA6B,OAEvC,KAAU,UAAY,GACtB,KAAU,UAAY,GACtB,KAAU,SAAW,GACrB,KAAU,aAAe,GACzB,KAAU,aAAe,EACzB,KAAU,gBAAkB,EAC5B,KAAU,gBAAkB,EAQ1B,KAAK,MAAQ,CAAC,EAAG,EAAE,EACnB,KAAK,aAAe,CAAE,EAAG,EAAG,EAAG,EAAG,MAAO,EAAG,OAAQ,CAAE,EACtD,KAAK,aAAe,MACtB,CAnCF,MAc+C,CAAAC,EAAA,iBAuB7C,SAASC,EAA+B,CACtC,KAAK,MAAQA,EACT,KAAK,eAAiB,QAAU,KAAK,eAAiB,QACxD,KAAK,aAAa,OAASA,EAAM,CAAC,EAAIA,EAAM,CAAC,EAE7C,KAAK,aAAa,MAAQA,EAAM,CAAC,EAAIA,EAAM,CAAC,EAE9C,KAAK,iBAAiB,CACxB,CAEA,UAA6B,CAC3B,MAAO,CAAC,KAAK,MAAM,CAAC,EAAI,KAAK,aAAc,KAAK,MAAM,CAAC,EAAI,KAAK,YAAY,CAC9E,CAEA,gBAAgBC,EAAkC,CAChD,KAAK,aAAeA,EACpB,KAAK,SAAS,KAAK,KAAK,CAC1B,CAQA,iBAA0B,CACxB,IAAMD,EAAQ,KAAK,SAAS,EAC5B,OAAO,KAAK,IAAIA,EAAM,CAAC,EAAIA,EAAM,CAAC,CAAC,EAAI,KAAK,cAAc,EAAE,MAC9D,CAEA,qBAA8B,CAC5B,OAAO,KAAK,YACd,CAEQ,mBAA+B,CACrC,OAAO,KAAK,wBAAwB,gBAClC,KAAK,cAAc,EAAE,IAAKE,GAASA,EAAK,SAAS,CAAC,EAClD,KAAK,WAAW,aAClB,CACF,CAEA,kCAAyC,CACnC,GAAgC,KAAK,gBAAgB,EAAI,KAAK,aAAe,IAC/E,KAAK,aAAe,KAAK,MAAO,GAAgC,KAAK,gBAAgB,EAAK,CAAC,GAE7F,KAAK,iBAAiB,CACxB,CAEQ,kCAAkCC,EAA2B,CACnE,IAAIC,EAAkBD,EAAe,OAKrC,GAJI,KAAK,WAAW,cAAgBC,EAAkB,KAAK,WAAW,gBACpEA,GAAmB,KAAK,WAAW,cACnC,KAAK,aAAe,IAElB,KAAK,WAAW,UAAW,CAC7B,IAAMC,EAAgB,KAAK,kBAAkB,EACvCC,EAAa,GAA0CH,EAAe,MAC5E,KAAK,aAAe,KAAK,IAAIE,EAAc,MAAQ,EAAGC,CAAU,EAEhE,IAAMC,EAAiBF,EAAc,OAAS,KAAK,WAAW,aAAe,EAC7E,KAAK,gBAAkBA,EAAc,OACjCE,GAAkBH,IACpBA,GAAmBG,EACnB,KAAK,UAAY,GAErB,CAKA,GAJI,KAAK,WAAW,UAAYH,GAAmB,KAAK,WAAW,aACjE,KAAK,SAAW,GAChBA,GAAmB,KAAK,WAAW,YAEjC,KAAK,WAAW,WAAa,KAAK,MAAO,CAC3C,IAAMC,EAAgB,KAAK,wBAAwB,gBACjD,CAAC,KAAK,KAAK,EACX,KAAK,WAAW,aAClB,EACME,EAAiBF,EAAc,OAAS,KAAK,WAAW,aAAe,EAC7E,KAAK,gBAAkBA,EAAc,OACjCE,GAAkBH,IACpBA,GAAmBG,EACnB,KAAK,UAAY,GAErB,CACA,KAAK,aAAa,MAAQJ,EAAe,MACzC,KAAK,aAAa,OAASA,EAAe,OAASC,CACrD,CAEQ,8BAA8BD,EAA2B,CAC/D,IAAIK,EAAiBL,EAAe,MAKpC,GAJI,KAAK,WAAW,cAAgBK,EAAiB,KAAK,WAAW,gBACnEA,GAAkB,KAAK,WAAW,cAClC,KAAK,aAAe,IAElB,KAAK,WAAW,UAAW,CAC7B,IAAMH,EAAgB,KAAK,kBAAkB,EACvCC,EAAa,GAA0CH,EAAe,OAC5E,KAAK,aAAe,KAAK,IAAIE,EAAc,OAAS,EAAGC,CAAU,EACjE,IAAMG,EAAgBJ,EAAc,MAAQ,KAAK,WAAW,aAAe,EACvEI,GAAiBD,IACnBA,GAAkBC,EAClB,KAAK,UAAY,GAErB,CAKA,GAJI,KAAK,WAAW,UAAYD,GAAkB,KAAK,WAAW,aAChE,KAAK,SAAW,GAChBA,GAAkB,KAAK,WAAW,YAEhC,KAAK,WAAW,WAAa,KAAK,MAAO,CAC3C,IAAMH,EAAgB,KAAK,wBAAwB,gBACjD,CAAC,KAAK,KAAK,EACX,KAAK,WAAW,aAClB,EACMI,EAAgBJ,EAAc,OAAS,KAAK,WAAW,aAAe,EAC5E,KAAK,gBAAkBA,EAAc,OACjCI,GAAiBD,IACnBA,GAAkBC,EAClB,KAAK,UAAY,GAErB,CACA,KAAK,aAAa,MAAQN,EAAe,MAAQK,EACjD,KAAK,aAAa,OAASL,EAAe,MAC5C,CAEA,eAAeA,EAAsC,CACnD,OAAI,KAAK,eAAiB,QAAU,KAAK,eAAiB,QACxD,KAAK,8BAA8BA,CAAc,EAEjD,KAAK,kCAAkCA,CAAc,EAEvD,KAAK,iBAAiB,EACf,CACL,MAAO,KAAK,aAAa,MACzB,OAAQ,KAAK,aAAa,MAC5B,CACF,CAEA,iBAAiBO,EAAoB,CACnC,KAAK,aAAa,EAAIA,EAAM,EAC5B,KAAK,aAAa,EAAIA,EAAM,CAC9B,CAEQ,gCAAiD,CACvD,IAAMC,EAAkC,CAAC,EACzC,GAAI,KAAK,aAAc,CACrB,IAAMC,EAAI,KAAK,aAAa,EAAI,KAAK,aAAa,MAAQ,KAAK,WAAW,cAAgB,EAC1FD,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,YAAa,YAAY,EACtC,KAAM,CACJ,CACE,KAAM,KAAKC,CAAC,IAAI,KAAK,aAAa,CAAC,MAAMA,CAAC,IACxC,KAAK,aAAa,EAAI,KAAK,aAAa,MAC1C,IACA,WAAY,KAAK,gBAAgB,cACjC,YAAa,KAAK,WAAW,aAC/B,CACF,CACF,CAAC,CACH,CAsBA,GArBI,KAAK,WACPD,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,YAAa,OAAO,EACjC,KAAM,KAAK,cAAc,EAAE,IAAKT,IAAU,CACxC,KAAMA,EAAK,SAAS,EACpB,EACE,KAAK,aAAa,EAClB,KAAK,aAAa,OACjB,KAAK,UAAY,KAAK,WAAW,aAAe,IAChD,KAAK,SAAW,KAAK,WAAW,WAAa,IAC7C,KAAK,aAAe,KAAK,WAAW,cAAgB,GACvD,EAAG,KAAK,cAAcA,CAAI,EAC1B,KAAM,KAAK,gBAAgB,WAC3B,SAAU,KAAK,WAAW,cAC1B,SAAU,EACV,YAAa,SACb,cAAe,OACjB,EAAE,CACJ,CAAC,EAEC,KAAK,SAAU,CACjB,IAAMU,EACJ,KAAK,aAAa,EAClB,KAAK,aAAa,OACjB,KAAK,aAAe,KAAK,WAAW,cAAgB,GACvDD,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,YAAa,OAAO,EACjC,KAAM,KAAK,cAAc,EAAE,IAAKT,IAAU,CACxC,KAAM,KAAKU,CAAC,IAAI,KAAK,cAAcV,CAAI,CAAC,MACtCU,EAAI,KAAK,WAAW,UACtB,IAAI,KAAK,cAAcV,CAAI,CAAC,GAC5B,WAAY,KAAK,gBAAgB,UACjC,YAAa,KAAK,WAAW,SAC/B,EAAE,CACJ,CAAC,CACH,CACA,OAAI,KAAK,WACPS,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,YAAa,OAAO,EACjC,KAAM,CACJ,CACE,KAAM,KAAK,MACX,EAAG,KAAK,aAAa,EAAI,KAAK,WAAW,aACzC,EAAG,KAAK,aAAa,EAAI,KAAK,aAAa,OAAS,EACpD,KAAM,KAAK,gBAAgB,WAC3B,SAAU,KAAK,WAAW,cAC1B,SAAU,IACV,YAAa,MACb,cAAe,QACjB,CACF,CACF,CAAC,EAEIA,CACT,CACQ,kCAAmD,CACzD,IAAMA,EAAkC,CAAC,EACzC,GAAI,KAAK,aAAc,CACrB,IAAME,EAAI,KAAK,aAAa,EAAI,KAAK,WAAW,cAAgB,EAChEF,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,cAAe,WAAW,EACvC,KAAM,CACJ,CACE,KAAM,KAAK,KAAK,aAAa,CAAC,IAAIE,CAAC,MACjC,KAAK,aAAa,EAAI,KAAK,aAAa,KAC1C,IAAIA,CAAC,GACL,WAAY,KAAK,gBAAgB,cACjC,YAAa,KAAK,WAAW,aAC/B,CACF,CACF,CAAC,CACH,CAqBA,GApBI,KAAK,WACPF,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,cAAe,OAAO,EACnC,KAAM,KAAK,cAAc,EAAE,IAAKT,IAAU,CACxC,KAAMA,EAAK,SAAS,EACpB,EAAG,KAAK,cAAcA,CAAI,EAC1B,EACE,KAAK,aAAa,EAClB,KAAK,WAAW,cACf,KAAK,SAAW,KAAK,WAAW,WAAa,IAC7C,KAAK,aAAe,KAAK,WAAW,cAAgB,GACvD,KAAM,KAAK,gBAAgB,WAC3B,SAAU,KAAK,WAAW,cAC1B,SAAU,EACV,YAAa,MACb,cAAe,QACjB,EAAE,CACJ,CAAC,EAEC,KAAK,SAAU,CACjB,IAAMW,EAAI,KAAK,aAAa,GAAK,KAAK,aAAe,KAAK,WAAW,cAAgB,GACrFF,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,cAAe,OAAO,EACnC,KAAM,KAAK,cAAc,EAAE,IAAKT,IAAU,CACxC,KAAM,KAAK,KAAK,cAAcA,CAAI,CAAC,IAAIW,CAAC,MAAM,KAAK,cAAcX,CAAI,CAAC,IACpEW,EAAI,KAAK,WAAW,UACtB,GACA,WAAY,KAAK,gBAAgB,UACjC,YAAa,KAAK,WAAW,SAC/B,EAAE,CACJ,CAAC,CACH,CACA,OAAI,KAAK,WACPF,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,cAAe,OAAO,EACnC,KAAM,CACJ,CACE,KAAM,KAAK,MACX,EAAG,KAAK,MAAM,CAAC,GAAK,KAAK,MAAM,CAAC,EAAI,KAAK,MAAM,CAAC,GAAK,EACrD,EACE,KAAK,aAAa,EAClB,KAAK,aAAa,OAClB,KAAK,WAAW,aAChB,KAAK,gBACP,KAAM,KAAK,gBAAgB,WAC3B,SAAU,KAAK,WAAW,cAC1B,SAAU,EACV,YAAa,MACb,cAAe,QACjB,CACF,CACF,CAAC,EAEIA,CACT,CACQ,+BAAgD,CACtD,IAAMA,EAAkC,CAAC,EACzC,GAAI,KAAK,aAAc,CACrB,IAAME,EAAI,KAAK,aAAa,EAAI,KAAK,aAAa,OAAS,KAAK,WAAW,cAAgB,EAC3FF,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,WAAY,WAAW,EACpC,KAAM,CACJ,CACE,KAAM,KAAK,KAAK,aAAa,CAAC,IAAIE,CAAC,MACjC,KAAK,aAAa,EAAI,KAAK,aAAa,KAC1C,IAAIA,CAAC,GACL,WAAY,KAAK,gBAAgB,cACjC,YAAa,KAAK,WAAW,aAC/B,CACF,CACF,CAAC,CACH,CAoBA,GAnBI,KAAK,WACPF,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,WAAY,OAAO,EAChC,KAAM,KAAK,cAAc,EAAE,IAAKT,IAAU,CACxC,KAAMA,EAAK,SAAS,EACpB,EAAG,KAAK,cAAcA,CAAI,EAC1B,EACE,KAAK,aAAa,GACjB,KAAK,UAAY,KAAK,gBAAkB,KAAK,WAAW,aAAe,EAAI,GAC5E,KAAK,WAAW,aAClB,KAAM,KAAK,gBAAgB,WAC3B,SAAU,KAAK,WAAW,cAC1B,SAAU,EACV,YAAa,MACb,cAAe,QACjB,EAAE,CACJ,CAAC,EAEC,KAAK,SAAU,CACjB,IAAMW,EAAI,KAAK,aAAa,EAC5BF,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,WAAY,OAAO,EAChC,KAAM,KAAK,cAAc,EAAE,IAAKT,IAAU,CACxC,KAAM,KAAK,KAAK,cAAcA,CAAI,CAAC,IACjCW,EAAI,KAAK,aAAa,QAAU,KAAK,aAAe,KAAK,WAAW,cAAgB,EACtF,MAAM,KAAK,cAAcX,CAAI,CAAC,IAC5BW,EACA,KAAK,aAAa,OAClB,KAAK,WAAW,YACf,KAAK,aAAe,KAAK,WAAW,cAAgB,EACvD,GACA,WAAY,KAAK,gBAAgB,UACjC,YAAa,KAAK,WAAW,SAC/B,EAAE,CACJ,CAAC,CACH,CACA,OAAI,KAAK,WACPF,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,WAAY,OAAO,EAChC,KAAM,CACJ,CACE,KAAM,KAAK,MACX,EAAG,KAAK,aAAa,EAAI,KAAK,aAAa,MAAQ,EACnD,EAAG,KAAK,aAAa,EAAI,KAAK,WAAW,aACzC,KAAM,KAAK,gBAAgB,WAC3B,SAAU,KAAK,WAAW,cAC1B,SAAU,EACV,YAAa,MACb,cAAe,QACjB,CACF,CACF,CAAC,EAEIA,CACT,CAEA,qBAAsC,CACpC,GAAI,KAAK,eAAiB,OACxB,OAAO,KAAK,+BAA+B,EAE7C,GAAI,KAAK,eAAiB,QACxB,MAAM,MAAM,0CAA0C,EAExD,OAAI,KAAK,eAAiB,SACjB,KAAK,iCAAiC,EAE3C,KAAK,eAAiB,MACjB,KAAK,8BAA8B,EAErC,CAAC,CACV,CACF,EC9ZO,IAAMG,GAAN,cAAuBC,CAAS,CAPvC,MAOuC,CAAAC,EAAA,iBAIrC,YACEC,EACAC,EACAC,EACAC,EACAC,EACA,CACA,MAAMJ,EAAYG,EAAOC,EAAyBH,CAAe,EACjE,KAAK,WAAaC,EAClB,KAAK,MAAQG,GAAU,EAAE,OAAO,KAAK,UAAU,EAAE,MAAM,KAAK,SAAS,CAAC,CACxE,CAEA,SAASC,EAA+B,CACtC,MAAM,SAASA,CAAK,CACtB,CAEA,kBAAyB,CACvB,KAAK,MAAQD,GAAU,EACpB,OAAO,KAAK,UAAU,EACtB,MAAM,KAAK,SAAS,CAAC,EACrB,aAAa,CAAC,EACd,aAAa,CAAC,EACd,MAAM,EAAG,EACZE,EAAI,MAAM,0CAA2C,KAAK,WAAY,KAAK,SAAS,CAAC,CACvF,CAEA,eAAqC,CACnC,OAAO,KAAK,UACd,CAEA,cAAcC,EAAuB,CACnC,OAAO,KAAK,MAAMA,CAAK,GAAK,KAAK,SAAS,EAAE,CAAC,CAC/C,CACF,ECtCO,IAAMC,GAAN,cAAyBC,CAAS,CANzC,MAMyC,CAAAC,EAAA,mBAIvC,YACEC,EACAC,EACAC,EACAC,EACAC,EACA,CACA,MAAMJ,EAAYG,EAAOC,EAAyBH,CAAe,EACjE,KAAK,OAASC,EACd,KAAK,MAAQG,GAAY,EAAE,OAAO,KAAK,MAAM,EAAE,MAAM,KAAK,SAAS,CAAC,CACtE,CAEA,eAAqC,CACnC,OAAO,KAAK,MAAM,MAAM,CAC1B,CAEA,kBAAyB,CACvB,IAAMH,EAAS,CAAC,GAAG,KAAK,MAAM,EAC1B,KAAK,eAAiB,QACxBA,EAAO,QAAQ,EAEjB,KAAK,MAAQG,GAAY,EAAE,OAAOH,CAAM,EAAE,MAAM,KAAK,SAAS,CAAC,CACjE,CAEA,cAAcI,EAAuB,CACnC,OAAO,KAAK,MAAMA,CAAK,CACzB,CACF,ECdO,SAASC,GACdC,EACAC,EACAC,EACAC,EACM,CACN,IAAMC,EAA0B,IAAIC,EAAgCF,CAAW,EAC/E,OAAIG,GAAeN,CAAI,EACd,IAAIO,GACTN,EACAC,EACAF,EAAK,WACLA,EAAK,MACLI,CACF,EAEK,IAAII,GACTP,EACAC,EACA,CAACF,EAAK,IAAKA,EAAK,GAAG,EACnBA,EAAK,MACLI,CACF,CACF,CAvBgBK,EAAAV,GAAA,WCTT,IAAMW,GAAN,KAA2C,CAGhD,YACUC,EACAC,EACAC,EACAC,EACR,CAJQ,6BAAAH,EACA,iBAAAC,EACA,eAAAC,EACA,sBAAAC,EAER,KAAK,aAAe,CAClB,EAAG,EACH,EAAG,EACH,MAAO,EACP,OAAQ,CACV,EACA,KAAK,eAAiB,EACxB,CA9BF,MAckD,CAAAC,EAAA,mBAiBhD,iBAAiBC,EAAoB,CACnC,KAAK,aAAa,EAAIA,EAAM,EAC5B,KAAK,aAAa,EAAIA,EAAM,CAC9B,CACA,eAAeC,EAAsC,CACnD,IAAMC,EAAiB,KAAK,wBAAwB,gBAClD,CAAC,KAAK,UAAU,KAAK,EACrB,KAAK,YAAY,aACnB,EACMC,EAAgB,KAAK,IAAID,EAAe,MAAOD,EAAe,KAAK,EACnEG,EAAiBF,EAAe,OAAS,EAAI,KAAK,YAAY,aACpE,OACEA,EAAe,OAASC,GACxBD,EAAe,QAAUE,GACzB,KAAK,YAAY,WACjB,KAAK,UAAU,QAEf,KAAK,aAAa,MAAQD,EAC1B,KAAK,aAAa,OAASC,EAC3B,KAAK,eAAiB,IAGjB,CACL,MAAO,KAAK,aAAa,MACzB,OAAQ,KAAK,aAAa,MAC5B,CACF,CACA,qBAAsC,CACpC,IAAMC,EAA+B,CAAC,EACtC,OAAI,KAAK,gBACPA,EAAa,KAAK,CAChB,WAAY,CAAC,aAAa,EAC1B,KAAM,OACN,KAAM,CACJ,CACE,SAAU,KAAK,YAAY,cAC3B,KAAM,KAAK,UAAU,MACrB,YAAa,SACb,cAAe,SACf,EAAG,KAAK,aAAa,EAAI,KAAK,aAAa,MAAQ,EACnD,EAAG,KAAK,aAAa,EAAI,KAAK,aAAa,OAAS,EACpD,KAAM,KAAK,iBAAiB,WAC5B,SAAU,CACZ,CACF,CACF,CAAC,EAEIA,CACT,CACF,EAEO,SAASC,GACdV,EACAC,EACAC,EACAS,EACgB,CAChB,IAAMZ,EAA0B,IAAIa,EAAgCD,CAAW,EAC/E,OAAO,IAAIb,GAAWC,EAAyBC,EAAaC,EAAWC,CAAgB,CACzF,CARgBC,EAAAO,GAAA,0BC9ET,IAAMG,GAAN,KAAe,CACpB,YACUC,EACAC,EACAC,EACAC,EACAC,EACR,CALQ,cAAAJ,EACA,WAAAC,EACA,WAAAC,EACA,iBAAAC,EACA,eAAAC,CACP,CAXL,MAIsB,CAAAC,EAAA,iBASpB,oBAAqC,CACnC,IAAMC,EAAgC,KAAK,SAAS,KAAK,IAAKC,GAAM,CAClE,KAAK,MAAM,cAAcA,EAAE,CAAC,CAAC,EAC7B,KAAK,MAAM,cAAcA,EAAE,CAAC,CAAC,CAC/B,CAAC,EAEGC,EAUJ,OATI,KAAK,cAAgB,aACvBA,EAAOC,GAAK,EACT,EAAGF,GAAMA,EAAE,CAAC,CAAC,EACb,EAAGA,GAAMA,EAAE,CAAC,CAAC,EAAED,CAAS,EAE3BE,EAAOC,GAAK,EACT,EAAGF,GAAMA,EAAE,CAAC,CAAC,EACb,EAAGA,GAAMA,EAAE,CAAC,CAAC,EAAED,CAAS,EAExBE,EAGE,CACL,CACE,WAAY,CAAC,OAAQ,aAAa,KAAK,SAAS,EAAE,EAClD,KAAM,OACN,KAAM,CACJ,CACE,KAAAA,EACA,WAAY,KAAK,SAAS,WAC1B,YAAa,KAAK,SAAS,WAC7B,CACF,CACF,CACF,EAdS,CAAC,CAeZ,CACF,EC3CO,IAAME,GAAN,KAAc,CACnB,YACUC,EACAC,EACAC,EACAC,EACAC,EACAC,EACR,CANQ,aAAAL,EACA,kBAAAC,EACA,WAAAC,EACA,WAAAC,EACA,iBAAAC,EACA,eAAAC,CACP,CAXL,MAGqB,CAAAC,EAAA,gBAUnB,oBAAqC,CACnC,IAAMC,EAAgC,KAAK,QAAQ,KAAK,IAAKC,GAAM,CACjE,KAAK,MAAM,cAAcA,EAAE,CAAC,CAAC,EAC7B,KAAK,MAAM,cAAcA,EAAE,CAAC,CAAC,CAC/B,CAAC,EAIKC,EACJ,KAAK,IAAI,KAAK,MAAM,oBAAoB,EAAI,EAAG,KAAK,MAAM,gBAAgB,CAAC,GAC1E,EAJuB,KAKpBC,EAAeD,EAAW,EAEhC,OAAI,KAAK,cAAgB,aAChB,CACL,CACE,WAAY,CAAC,OAAQ,YAAY,KAAK,SAAS,EAAE,EACjD,KAAM,OACN,KAAMF,EAAU,IAAKI,IAAU,CAC7B,EAAG,KAAK,aAAa,EACrB,EAAGA,EAAK,CAAC,EAAID,EACb,OAAQD,EACR,MAAOE,EAAK,CAAC,EAAI,KAAK,aAAa,EACnC,KAAM,KAAK,QAAQ,KACnB,YAAa,EACb,WAAY,KAAK,QAAQ,IAC3B,EAAE,CACJ,CACF,EAEK,CACL,CACE,WAAY,CAAC,OAAQ,YAAY,KAAK,SAAS,EAAE,EACjD,KAAM,OACN,KAAMJ,EAAU,IAAKI,IAAU,CAC7B,EAAGA,EAAK,CAAC,EAAID,EACb,EAAGC,EAAK,CAAC,EACT,MAAOF,EACP,OAAQ,KAAK,aAAa,EAAI,KAAK,aAAa,OAASE,EAAK,CAAC,EAC/D,KAAM,KAAK,QAAQ,KACnB,YAAa,EACb,WAAY,KAAK,QAAQ,IAC3B,EAAE,CACJ,CACF,CACF,CACF,ECzCO,IAAMC,GAAN,KAA+B,CAKpC,YACUC,EACAC,EACAC,EACR,CAHQ,iBAAAF,EACA,eAAAC,EACA,sBAAAC,EAER,KAAK,aAAe,CAClB,EAAG,EACH,EAAG,EACH,MAAO,EACP,OAAQ,CACV,CACF,CAlCF,MAkBsC,CAAAC,EAAA,iBAiBpC,QAAQC,EAAaC,EAAa,CAChC,KAAK,MAAQD,EACb,KAAK,MAAQC,CACf,CACA,iBAAiBC,EAAoB,CACnC,KAAK,aAAa,EAAIA,EAAM,EAC5B,KAAK,aAAa,EAAIA,EAAM,CAC9B,CACA,eAAeC,EAAsC,CACnD,YAAK,aAAa,MAAQA,EAAe,MACzC,KAAK,aAAa,OAASA,EAAe,OAEnC,CACL,MAAO,KAAK,aAAa,MACzB,OAAQ,KAAK,aAAa,MAC5B,CACF,CACA,qBAAsC,CACpC,GAAI,EAAE,KAAK,OAAS,KAAK,OACvB,MAAM,MAAM,qCAAqC,EAEnD,IAAMC,EAA+B,CAAC,EACtC,OAAW,CAACC,EAAGC,CAAI,IAAK,KAAK,UAAU,MAAM,QAAQ,EACnD,OAAQA,EAAK,KAAM,CACjB,IAAK,OACH,CACE,IAAMC,EAAW,IAAIC,GACnBF,EACA,KAAK,MACL,KAAK,MACL,KAAK,YAAY,iBACjBD,CACF,EACAD,EAAa,KAAK,GAAGG,EAAS,mBAAmB,CAAC,CACpD,CACA,MACF,IAAK,MACH,CACE,IAAME,EAAU,IAAIC,GAClBJ,EACA,KAAK,aACL,KAAK,MACL,KAAK,MACL,KAAK,YAAY,iBACjBD,CACF,EACAD,EAAa,KAAK,GAAGK,EAAQ,mBAAmB,CAAC,CACnD,CACA,KACJ,CAEF,OAAOL,CACT,CACF,EAEO,SAASO,GACdf,EACAC,EACAC,EACM,CACN,OAAO,IAAIH,GAASC,EAAaC,EAAWC,CAAgB,CAC9D,CANgBC,EAAAY,GAAA,oBC3ET,IAAMC,GAAN,KAAmB,CAOxB,YACUC,EACAC,EACRC,EACAC,EACA,CAJQ,iBAAAH,EACA,eAAAC,EAIR,KAAK,eAAiB,CACpB,MAAOG,GAAuBJ,EAAaC,EAAWC,EAAkBC,CAAW,EACnF,KAAME,GAAiBL,EAAaC,EAAWC,CAAgB,EAC/D,MAAOI,GACLL,EAAU,MACVD,EAAY,MACZ,CACE,WAAYE,EAAiB,gBAC7B,WAAYA,EAAiB,gBAC7B,UAAWA,EAAiB,eAC5B,cAAeA,EAAiB,cAClC,EACAC,CACF,EACA,MAAOG,GACLL,EAAU,MACVD,EAAY,MACZ,CACE,WAAYE,EAAiB,gBAC7B,WAAYA,EAAiB,gBAC7B,UAAWA,EAAiB,eAC5B,cAAeA,EAAiB,cAClC,EACAC,CACF,CACF,CACF,CAtDF,MAe0B,CAAAI,EAAA,qBAyChB,wBAAyB,CAC/B,IAAIC,EAAiB,KAAK,YAAY,MAClCC,EAAkB,KAAK,YAAY,OACnCC,EAAQ,EACRC,EAAQ,EACRC,EAAa,KAAK,MAAOJ,EAAiB,KAAK,YAAY,yBAA4B,GAAG,EAC1FK,EAAc,KAAK,MACpBJ,EAAkB,KAAK,YAAY,yBAA4B,GAClE,EACIK,EAAY,KAAK,eAAe,KAAK,eAAe,CACtD,MAAOF,EACP,OAAQC,CACV,CAAC,EACDL,GAAkBM,EAAU,MAC5BL,GAAmBK,EAAU,OAE7BA,EAAY,KAAK,eAAe,MAAM,eAAe,CACnD,MAAO,KAAK,YAAY,MACxB,OAAQL,CACV,CAAC,EACDE,EAAQG,EAAU,OAClBL,GAAmBK,EAAU,OAC7B,KAAK,eAAe,MAAM,gBAAgB,QAAQ,EAClDA,EAAY,KAAK,eAAe,MAAM,eAAe,CACnD,MAAON,EACP,OAAQC,CACV,CAAC,EACDA,GAAmBK,EAAU,OAC7B,KAAK,eAAe,MAAM,gBAAgB,MAAM,EAChDA,EAAY,KAAK,eAAe,MAAM,eAAe,CACnD,MAAON,EACP,OAAQC,CACV,CAAC,EACDC,EAAQI,EAAU,MAClBN,GAAkBM,EAAU,MACxBN,EAAiB,IACnBI,GAAcJ,EACdA,EAAiB,GAEfC,EAAkB,IACpBI,GAAeJ,EACfA,EAAkB,GAEpB,KAAK,eAAe,KAAK,eAAe,CACtC,MAAOG,EACP,OAAQC,CACV,CAAC,EAED,KAAK,eAAe,KAAK,iBAAiB,CAAE,EAAGH,EAAO,EAAGC,CAAM,CAAC,EAChE,KAAK,eAAe,MAAM,SAAS,CAACD,EAAOA,EAAQE,CAAU,CAAC,EAC9D,KAAK,eAAe,MAAM,iBAAiB,CAAE,EAAGF,EAAO,EAAGC,EAAQE,CAAY,CAAC,EAC/E,KAAK,eAAe,MAAM,SAAS,CAACF,EAAOA,EAAQE,CAAW,CAAC,EAC/D,KAAK,eAAe,MAAM,iBAAiB,CAAE,EAAG,EAAG,EAAGF,CAAM,CAAC,EACzD,KAAK,UAAU,MAAM,KAAMI,GAAMC,GAAUD,CAAC,CAAC,GAC/C,KAAK,eAAe,MAAM,iCAAiC,CAE/D,CAEQ,0BAA2B,CACjC,IAAIP,EAAiB,KAAK,YAAY,MAClCC,EAAkB,KAAK,YAAY,OACnCQ,EAAY,EACZP,EAAQ,EACRC,EAAQ,EACRC,EAAa,KAAK,MAAOJ,EAAiB,KAAK,YAAY,yBAA4B,GAAG,EAC1FK,EAAc,KAAK,MACpBJ,EAAkB,KAAK,YAAY,yBAA4B,GAClE,EACIK,EAAY,KAAK,eAAe,KAAK,eAAe,CACtD,MAAOF,EACP,OAAQC,CACV,CAAC,EACDL,GAAkBM,EAAU,MAC5BL,GAAmBK,EAAU,OAE7BA,EAAY,KAAK,eAAe,MAAM,eAAe,CACnD,MAAO,KAAK,YAAY,MACxB,OAAQL,CACV,CAAC,EACDQ,EAAYH,EAAU,OACtBL,GAAmBK,EAAU,OAC7B,KAAK,eAAe,MAAM,gBAAgB,MAAM,EAChDA,EAAY,KAAK,eAAe,MAAM,eAAe,CACnD,MAAON,EACP,OAAQC,CACV,CAAC,EACDD,GAAkBM,EAAU,MAC5BJ,EAAQI,EAAU,MAClB,KAAK,eAAe,MAAM,gBAAgB,KAAK,EAC/CA,EAAY,KAAK,eAAe,MAAM,eAAe,CACnD,MAAON,EACP,OAAQC,CACV,CAAC,EACDA,GAAmBK,EAAU,OAC7BH,EAAQM,EAAYH,EAAU,OAC1BN,EAAiB,IACnBI,GAAcJ,EACdA,EAAiB,GAEfC,EAAkB,IACpBI,GAAeJ,EACfA,EAAkB,GAEpB,KAAK,eAAe,KAAK,eAAe,CACtC,MAAOG,EACP,OAAQC,CACV,CAAC,EAED,KAAK,eAAe,KAAK,iBAAiB,CAAE,EAAGH,EAAO,EAAGC,CAAM,CAAC,EAChE,KAAK,eAAe,MAAM,SAAS,CAACD,EAAOA,EAAQE,CAAU,CAAC,EAC9D,KAAK,eAAe,MAAM,iBAAiB,CAAE,EAAGF,EAAO,EAAGO,CAAU,CAAC,EACrE,KAAK,eAAe,MAAM,SAAS,CAACN,EAAOA,EAAQE,CAAW,CAAC,EAC/D,KAAK,eAAe,MAAM,iBAAiB,CAAE,EAAG,EAAG,EAAGF,CAAM,CAAC,EACzD,KAAK,UAAU,MAAM,KAAMI,GAAMC,GAAUD,CAAC,CAAC,GAC/C,KAAK,eAAe,MAAM,iCAAiC,CAE/D,CAEQ,gBAAiB,CACnB,KAAK,YAAY,mBAAqB,aACxC,KAAK,yBAAyB,EAE9B,KAAK,uBAAuB,CAEhC,CAEA,oBAAqB,CACnB,KAAK,eAAe,EACpB,IAAMG,EAA+B,CAAC,EACtC,KAAK,eAAe,KAAK,QAAQ,KAAK,eAAe,MAAO,KAAK,eAAe,KAAK,EACrF,QAAWC,KAAa,OAAO,OAAO,KAAK,cAAc,EACvDD,EAAa,KAAK,GAAGC,EAAU,oBAAoB,CAAC,EAEtD,OAAOD,CACT,CACF,EC3LO,IAAME,GAAN,KAAqB,CAJ5B,MAI4B,CAAAC,EAAA,uBAC1B,OAAO,MACLC,EACAC,EACAC,EACAC,EACgB,CAEhB,OADqB,IAAIC,GAAaJ,EAAQC,EAAWC,EAAkBC,CAAW,EAClE,mBAAmB,CACzC,CACF,ECWA,IAAIE,EAAY,EAEZC,GAEAC,EAA+BC,GAAsB,EACrDC,EAAyCC,GAA2B,EACpEC,EAA2BC,GAAoB,EAC/CC,GAAmBJ,EAAmB,iBAAiB,MAAM,GAAG,EAAE,IAAKK,GAAUA,EAAM,KAAK,CAAC,EAC7FC,GAAc,GACdC,GAAc,GAOlB,SAASN,IAAiD,CACxD,IAAMO,EAAwBC,GAAkB,EAC1CC,EAAmBC,EAAU,EACnC,OAAOC,GAAcJ,EAAsB,QAASE,EAAO,eAAe,OAAO,CACnF,CAJSG,EAAAZ,GAAA,8BAKT,SAASF,IAAuC,CAC9C,IAAMW,EAAmBC,EAAU,EACnC,OAAOC,GACLE,GAAc,QACdJ,EAAO,OACT,CACF,CANSG,EAAAd,GAAA,yBAQT,SAASI,IAAmC,CAC1C,MAAO,CACL,MAAO,CACL,KAAM,SACN,MAAO,GACP,IAAK,IACL,IAAK,IACP,EACA,MAAO,CACL,KAAM,OACN,MAAO,GACP,WAAY,CAAC,CACf,EACA,MAAO,GACP,MAAO,CAAC,CACV,CACF,CAhBSU,EAAAV,GAAA,uBAkBT,SAASY,GAAcC,EAAc,CACnC,IAAMN,EAAmBC,EAAU,EACnC,OAAOM,GAAaD,EAAK,KAAK,EAAGN,CAAM,CACzC,CAHSG,EAAAE,GAAA,iBAKT,SAASG,GAAWC,EAAgB,CAClCtB,GAAcsB,CAChB,CAFSN,EAAAK,GAAA,cAGT,SAASE,GAAeC,EAAqB,CACvCA,IAAgB,aAClBvB,EAAc,iBAAmB,aAEjCA,EAAc,iBAAmB,UAErC,CANSe,EAAAO,GAAA,kBAOT,SAASE,GAAcC,EAAuB,CAC5CrB,EAAY,MAAM,MAAQa,GAAcQ,EAAM,IAAI,CACpD,CAFSV,EAAAS,GAAA,iBAGT,SAASE,GAAkBC,EAAaC,EAAa,CACnDxB,EAAY,MAAQ,CAAE,KAAM,SAAU,MAAOA,EAAY,MAAM,MAAO,IAAAuB,EAAK,IAAAC,CAAI,EAC/EpB,GAAc,EAChB,CAHSO,EAAAW,GAAA,qBAIT,SAASG,GAAaC,EAA8B,CAClD1B,EAAY,MAAQ,CAClB,KAAM,OACN,MAAOA,EAAY,MAAM,MACzB,WAAY0B,EAAW,IAAKC,GAAMd,GAAcc,EAAE,IAAI,CAAC,CACzD,EACAvB,GAAc,EAChB,CAPSO,EAAAc,GAAA,gBAQT,SAASG,GAAcP,EAAuB,CAC5CrB,EAAY,MAAM,MAAQa,GAAcQ,EAAM,IAAI,CACpD,CAFSV,EAAAiB,GAAA,iBAGT,SAASC,GAAkBN,EAAaC,EAAa,CACnDxB,EAAY,MAAQ,CAAE,KAAM,SAAU,MAAOA,EAAY,MAAM,MAAO,IAAAuB,EAAK,IAAAC,CAAI,EAC/EnB,GAAc,EAChB,CAHSM,EAAAkB,GAAA,qBAMT,SAASC,GAA0BC,EAAgB,CACjD,IAAMC,EAAW,KAAK,IAAI,GAAGD,CAAI,EAC3BE,EAAW,KAAK,IAAI,GAAGF,CAAI,EAC3BG,EAAeC,EAAiBnC,EAAY,KAAK,EAAIA,EAAY,MAAM,IAAM,IAC7EoC,EAAeD,EAAiBnC,EAAY,KAAK,EAAIA,EAAY,MAAM,IAAM,KACnFA,EAAY,MAAQ,CAClB,KAAM,SACN,MAAOA,EAAY,MAAM,MACzB,IAAK,KAAK,IAAIkC,EAAcF,CAAQ,EACpC,IAAK,KAAK,IAAII,EAAcH,CAAQ,CACtC,CACF,CAXStB,EAAAmB,GAAA,6BAaT,SAASO,GAA6BN,EAAoC,CACxE,IAAIO,EAA8B,CAAC,EACnC,GAAIP,EAAK,SAAW,EAClB,OAAOO,EAET,GAAI,CAAClC,GAAa,CAChB,IAAM8B,EAAeC,EAAiBnC,EAAY,KAAK,EAAIA,EAAY,MAAM,IAAM,IAC7EoC,EAAeD,EAAiBnC,EAAY,KAAK,EAAIA,EAAY,MAAM,IAAM,KACnFsB,GAAkB,KAAK,IAAIY,EAAc,CAAC,EAAG,KAAK,IAAIE,EAAcL,EAAK,MAAM,CAAC,CAClF,CASA,GARK1B,IACHyB,GAA0BC,CAAI,EAG5BQ,GAAevC,EAAY,KAAK,IAClCsC,EAAUtC,EAAY,MAAM,WAAW,IAAI,CAAC2B,EAAGa,IAAM,CAACb,EAAGI,EAAKS,CAAC,CAAC,CAAC,GAG/DL,EAAiBnC,EAAY,KAAK,EAAG,CACvC,IAAMuB,EAAMvB,EAAY,MAAM,IACxBwB,EAAMxB,EAAY,MAAM,IACxByC,GAAQjB,EAAMD,IAAQQ,EAAK,OAAS,GACpCL,EAAuB,CAAC,EAC9B,QAASc,EAAIjB,EAAKiB,GAAKhB,EAAKgB,GAAKC,EAC/Bf,EAAW,KAAK,GAAGc,CAAC,EAAE,EAExBF,EAAUZ,EAAW,IAAI,CAACC,EAAGa,IAAM,CAACb,EAAGI,EAAKS,CAAC,CAAC,CAAC,CACjD,CAEA,OAAOF,CACT,CA9BS3B,EAAA0B,GAAA,gCAgCT,SAASK,GAAwBhD,EAA2B,CAC1D,OAAOQ,GAAiBR,IAAc,EAAI,EAAIA,EAAYQ,GAAiB,MAAM,CACnF,CAFSS,EAAA+B,GAAA,2BAIT,SAASC,GAAYtB,EAAuBU,EAAgB,CAC1D,IAAMa,EAAWP,GAA6BN,CAAI,EAClD/B,EAAY,MAAM,KAAK,CACrB,KAAM,OACN,WAAY0C,GAAwBhD,CAAS,EAC7C,YAAa,EACb,KAAMkD,CACR,CAAC,EACDlD,GACF,CATSiB,EAAAgC,GAAA,eAWT,SAASE,GAAWxB,EAAuBU,EAAgB,CACzD,IAAMa,EAAWP,GAA6BN,CAAI,EAClD/B,EAAY,MAAM,KAAK,CACrB,KAAM,MACN,KAAM0C,GAAwBhD,CAAS,EACvC,KAAMkD,CACR,CAAC,EACDlD,GACF,CARSiB,EAAAkC,GAAA,cAUT,SAASC,IAAkC,CACzC,GAAI9C,EAAY,MAAM,SAAW,EAC/B,MAAM,MAAM,yDAAyD,EAEvE,OAAAA,EAAY,MAAQ+C,GAAgB,EAC7BC,GAAe,MAAMpD,EAAeI,EAAaF,EAAoBH,EAAW,CACzF,CANSgB,EAAAmC,GAAA,mBAQT,SAASG,IAAsB,CAC7B,OAAOnD,CACT,CAFSa,EAAAsC,GAAA,uBAIT,SAASC,IAAiB,CACxB,OAAOtD,CACT,CAFSe,EAAAuC,GAAA,kBAIT,IAAMC,GAAQxC,EAAA,UAAY,CACxBwC,GAAY,EACZzD,EAAY,EACZE,EAAgBC,GAAsB,EACtCG,EAAcC,GAAoB,EAClCH,EAAqBC,GAA2B,EAChDG,GAAmBJ,EAAmB,iBAAiB,MAAM,GAAG,EAAE,IAAKK,GAAUA,EAAM,KAAK,CAAC,EAC7FC,GAAc,GACdC,GAAc,EAChB,EATc,SAWP+C,GAAQ,CACb,gBAAAN,GACA,MAAAK,GACA,YAAAE,GACA,YAAAC,GACA,gBAAAC,GACA,gBAAAR,GACA,kBAAAS,GACA,kBAAAC,GACA,eAAAvC,GACA,cAAAE,GACA,kBAAAE,GACA,aAAAG,GACA,cAAAG,GACA,kBAAAC,GACA,YAAAc,GACA,WAAAE,GACA,WAAA7B,GACA,oBAAAiC,GACA,eAAAC,EACF,ECxNO,IAAMQ,GAAOC,EAAA,CAACC,EAAaC,EAAYC,EAAkBC,IAAqB,CACnF,IAAMC,EAAKD,EAAQ,GACbE,EAAcD,EAAG,oBAAoB,EACrCE,EAAcF,EAAG,eAAe,EACtC,SAASG,EAAoBC,EAAgC,CAC3D,OAAOA,IAAkB,MAAQ,mBAAqB,QACxD,CAFST,EAAAQ,EAAA,uBAIT,SAASE,EAAcC,EAAgC,CACrD,OAAOA,IAAgB,OAAS,QAAUA,IAAgB,QAAU,MAAQ,QAC9E,CAFSX,EAAAU,EAAA,iBAIT,SAASE,EAAsBC,EAAgB,CAC7C,MAAO,aAAaA,EAAK,CAAC,KAAKA,EAAK,CAAC,YAAYA,EAAK,UAAY,CAAC,GACrE,CAFSb,EAAAY,EAAA,yBAITE,EAAI,MAAM;AAAA,EAA8Bb,CAAG,EAE3C,IAAMc,EAAMC,GAAiBd,CAAE,EACzBe,EAAQF,EAAI,OAAO,GAAG,EAAE,KAAK,QAAS,MAAM,EAC5CG,EAAaD,EAChB,OAAO,MAAM,EACb,KAAK,QAASV,EAAY,KAAK,EAC/B,KAAK,SAAUA,EAAY,MAAM,EACjC,KAAK,QAAS,YAAY,EAG7BY,GAAiBJ,EAAKR,EAAY,OAAQA,EAAY,MAAO,EAAI,EAEjEQ,EAAI,KAAK,UAAW,OAAOR,EAAY,KAAK,IAAIA,EAAY,MAAM,EAAE,EAEpEW,EAAW,KAAK,OAAQZ,EAAY,eAAe,EAEnDD,EAAG,WAAWU,EAAI,OAAO,GAAG,EAAE,KAAK,QAAS,mBAAmB,CAAC,EAEhE,IAAMK,EAAyBf,EAAG,gBAAgB,EAE5CgB,EAA8B,CAAC,EAErC,SAASC,EAASC,EAAiB,CACjC,IAAIC,EAAOP,EACPQ,EAAS,GACb,OAAW,CAACC,CAAC,IAAKH,EAAM,QAAQ,EAAG,CACjC,IAAII,EAASV,EACTS,EAAI,GAAKL,EAAOI,CAAM,IACxBE,EAASN,EAAOI,CAAM,GAExBA,GAAUF,EAAMG,CAAC,EACjBF,EAAOH,EAAOI,CAAM,EACfD,IACHA,EAAOH,EAAOI,CAAM,EAAIE,EAAO,OAAO,GAAG,EAAE,KAAK,QAASJ,EAAMG,CAAC,CAAC,EAErE,CACA,OAAOF,CACT,CAfSxB,EAAAsB,EAAA,YAiBT,QAAWM,KAASR,EAAQ,CAC1B,GAAIQ,EAAM,KAAK,SAAW,EACxB,SAGF,IAAMC,EAAaP,EAASM,EAAM,UAAU,EAE5C,OAAQA,EAAM,KAAM,CAClB,IAAK,OACHC,EACG,UAAU,MAAM,EAChB,KAAKD,EAAM,IAAI,EACf,MAAM,EACN,OAAO,MAAM,EACb,KAAK,IAAMf,GAASA,EAAK,CAAC,EAC1B,KAAK,IAAMA,GAASA,EAAK,CAAC,EAC1B,KAAK,QAAUA,GAASA,EAAK,KAAK,EAClC,KAAK,SAAWA,GAASA,EAAK,MAAM,EACpC,KAAK,OAASA,GAASA,EAAK,IAAI,EAChC,KAAK,SAAWA,GAASA,EAAK,UAAU,EACxC,KAAK,eAAiBA,GAASA,EAAK,WAAW,EAClD,MACF,IAAK,OACHgB,EACG,UAAU,MAAM,EAChB,KAAKD,EAAM,IAAI,EACf,MAAM,EACN,OAAO,MAAM,EACb,KAAK,IAAK,CAAC,EACX,KAAK,IAAK,CAAC,EACX,KAAK,OAASf,GAASA,EAAK,IAAI,EAChC,KAAK,YAAcA,GAASA,EAAK,QAAQ,EACzC,KAAK,oBAAsBA,GAASL,EAAoBK,EAAK,WAAW,CAAC,EACzE,KAAK,cAAgBA,GAASH,EAAcG,EAAK,aAAa,CAAC,EAC/D,KAAK,YAAcA,GAASD,EAAsBC,CAAI,CAAC,EACvD,KAAMA,GAASA,EAAK,IAAI,EAC3B,MACF,IAAK,OACHgB,EACG,UAAU,MAAM,EAChB,KAAKD,EAAM,IAAI,EACf,MAAM,EACN,OAAO,MAAM,EACb,KAAK,IAAMf,GAASA,EAAK,IAAI,EAC7B,KAAK,OAASA,GAAUA,EAAK,KAAOA,EAAK,KAAO,MAAO,EACvD,KAAK,SAAWA,GAASA,EAAK,UAAU,EACxC,KAAK,eAAiBA,GAASA,EAAK,WAAW,EAClD,KACJ,CACF,CACF,EA1GoB,QA4GbiB,GAAQ,CACb,KAAA/B,EACF,ECpHO,IAAMgC,GAA6B,CACxC,OAAAC,GACA,GAAAC,GACA,SAAAC,EACF", "names": ["parser", "o", "__name", "k", "v", "l", "$V0", "$V1", "$V2", "$V3", "$V4", "$V5", "$V6", "$V7", "$V8", "$V9", "$Va", "$Vb", "$Vc", "$Vd", "$Ve", "$Vf", "$Vg", "$Vh", "$Vi", "$Vj", "$Vk", "$Vl", "$Vm", "$Vn", "$Vo", "$Vp", "$Vq", "yytext", "yyleng", "y<PERSON><PERSON>o", "yy", "yystate", "$$", "_$", "$0", "str", "hash", "error", "input", "self", "stack", "tstack", "vstack", "lstack", "table", "recovering", "TERROR", "EOF", "args", "lexer", "sharedState", "yyloc", "ranges", "popStack", "n", "lex", "token", "symbol", "preErrorSymbol", "state", "action", "a", "r", "yyval", "p", "len", "newState", "expected", "errStr", "ch", "lines", "oldLines", "past", "next", "pre", "match", "indexed_rule", "backup", "tempMatch", "index", "rules", "i", "condition", "yy_", "$avoiding_name_collisions", "YY_START", "YYSTATE", "<PERSON><PERSON><PERSON>", "xychart_default", "parser", "isBarPlot", "data", "__name", "isBandAxisData", "isLinearAxisData", "TextDimensionCalculatorWithFont", "parentGroup", "__name", "texts", "fontSize", "acc", "cur", "dimension", "elem", "t", "bbox", "computeDimensionOfText", "width", "height", "BaseAxis", "axisConfig", "title", "textDimensionCalculator", "axisThemeConfig", "__name", "range", "axisPosition", "tick", "availableSpace", "availableHeight", "spaceRequired", "maxPadding", "heightRequired", "availableWidth", "widthRequired", "point", "drawableElement", "x", "y", "BandAxis", "BaseAxis", "__name", "axisConfig", "axisThemeConfig", "categories", "title", "textDimensionCalculator", "band", "range", "log", "value", "LinearAxis", "BaseAxis", "__name", "axisConfig", "axisThemeConfig", "domain", "title", "textDimensionCalculator", "linear", "value", "getAxis", "data", "axisConfig", "axisThemeConfig", "tmpSVGGroup", "textDimensionCalculator", "TextDimensionCalculatorWithFont", "isBandAxisData", "BandAxis", "LinearAxis", "__name", "ChartTitle", "textDimensionCalculator", "chartConfig", "chartData", "chartThemeConfig", "__name", "point", "availableSpace", "titleDimension", "widthRequired", "heightRequired", "drawableElem", "getChartTitleComponent", "tmpSVGGroup", "TextDimensionCalculatorWithFont", "LinePlot", "plotData", "xAxis", "yAxis", "orientation", "plotIndex", "__name", "finalData", "d", "path", "line_default", "BarPlot", "barData", "boundingRect", "xAxis", "yAxis", "orientation", "plotIndex", "__name", "finalData", "d", "<PERSON><PERSON><PERSON><PERSON>", "bar<PERSON><PERSON>thHalf", "data", "BasePlot", "chartConfig", "chartData", "chartThemeConfig", "__name", "xAxis", "yAxis", "point", "availableSpace", "drawableElem", "i", "plot", "linePlot", "LinePlot", "barPlot", "BarPlot", "getPlotComponent", "Orchestrator", "chartConfig", "chartData", "chartThemeConfig", "tmpSVGGroup", "getChartTitleComponent", "getPlotComponent", "getAxis", "__name", "availableWidth", "availableHeight", "plotX", "plotY", "chartWidth", "chartHeight", "spaceUsed", "p", "isBarPlot", "titleYEnd", "drawableElem", "component", "XYChartBuilder", "__name", "config", "chartData", "chartThemeConfig", "tmpSVGGroup", "Orchestrator", "plotIndex", "tmpSVGGroup", "xyChartConfig", "getChartDefaultConfig", "xyChartThemeConfig", "getChartDefaultThemeConfig", "xyChartData", "getChartDefaultData", "plotColorPalette", "color", "hasSetXAxis", "hasSetYAxis", "defaultThemeVariables", "getThemeVariables", "config", "getConfig", "cleanAndMerge", "__name", "defaultConfig_default", "textSanitizer", "text", "sanitizeText", "setTmpSVGG", "SVGG", "setOrientation", "orientation", "setXAxisTitle", "title", "setXAxisRangeData", "min", "max", "setXAxisBand", "categories", "c", "setYAxisTitle", "setYAxisRangeData", "setYAxisRangeFromPlotData", "data", "minValue", "maxValue", "prevMinValue", "isLinearAxisData", "prevMaxValue", "transformDataWithoutCategory", "retData", "isBandAxisData", "i", "step", "getPlotColorFromPalette", "setLineData", "plotData", "setBarData", "getDrawableElem", "getDiagramTitle", "XYChartBuilder", "getChartThemeConfig", "getChartConfig", "clear", "xychartDb_default", "setAccTitle", "getAccTitle", "setDiagramTitle", "getAccDescription", "setAccDescription", "draw", "__name", "txt", "id", "_version", "diagObj", "db", "themeConfig", "chartConfig", "getDominantBaseLine", "horizontalPos", "getTextAnchor", "verticalPos", "getTextTransformation", "data", "log", "svg", "selectSvgElement", "group", "background", "configureSvgSize", "shapes", "groups", "getGroup", "gList", "elem", "prefix", "i", "parent", "shape", "shapeGroup", "xychartRenderer_default", "diagram", "xychart_default", "xychartDb_default", "xychartRenderer_default"]}