{"name": "pkg-types", "version": "2.1.0", "description": "Node.js utilities and TypeScript definitions for `package.json` and `tsconfig.json`", "license": "MIT", "sideEffects": false, "exports": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "types": "./dist/index.d.mts", "repository": "unjs/pkg-types", "files": ["dist"], "scripts": {"build": "unbuild", "prepack": "pnpm build", "dev": "vitest --typecheck", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "lint": "eslint && prettier -c src test", "lint:fix": "automd && eslint --fix . && prettier -w src test", "test": "vitest run --typecheck --coverage"}, "dependencies": {"confbox": "^0.2.1", "exsolve": "^1.0.1", "pathe": "^2.0.3"}, "devDependencies": {"@types/node": "^22.13.8", "@vitest/coverage-v8": "^3.0.7", "automd": "^0.4.0", "changelogen": "^0.6.0", "eslint": "^9.21.0", "eslint-config-unjs": "^0.4.2", "expect-type": "^1.2.0", "jiti": "^2.4.2", "prettier": "^3.5.2", "typescript": "^5.8.2", "unbuild": "^3.5.0", "vitest": "^3.0.7"}, "packageManager": "pnpm@10.5.2"}