#relation_service.py

import os
import json
import logging
from relation_processor import RelationProcessor, convert_model_results

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("relation_service.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Création de l'instance du processeur de relations
# Augmentation du seuil de distance pour mieux détecter les relations entre flèches et classes
relation_processor = RelationProcessor(distance_threshold=100)

async def process_relations(results1, results2, extracted_texts):
    """
    Traite les relations entre les classes détectées dans l'image.
    
    Args:
        results1: Résultats du modèle 1 (classes et flèches)
        results2: Résultats du modèle 2 (types de relations)
        extracted_texts: Textes extraits des classes par Groq
        
    Returns:
        Liste des phrases décrivant les relations
    """
    logger.info("Début du traitement des relations")
    
    # Conversion des résultats bruts du modèle au format attendu par le processeur
    model1_formatted = convert_model_results(results1, 'model1')
    model2_formatted = convert_model_results(results2, 'model2')
    
    # Préparer un dictionnaire des textes extraits
    extracted_dict = {}
    
    # Logging pour le debug
    logger.debug(f"Textes extraits: {extracted_texts}")
    
    for text_item in extracted_texts:
        try:
            # Format attendu: "class X:\nTexte extrait"
            parts = text_item.split(':', 1)
            if len(parts) == 2:
                class_id = parts[0].strip()
                content = parts[1].strip()
                extracted_dict[class_id] = content
                # Ajouter une entrée pour le label complet et l'ID numérique seul
                # pour augmenter les chances de correspondance
                if "class" in class_id.lower():
                    num = ''.join(filter(str.isdigit, class_id))
                    if num:
                        extracted_dict[f"class_{num}"] = content
                        extracted_dict[f"class{num}"] = content
                
                logger.debug(f"Texte extrait pour {class_id}: {content[:30]}...")
        except Exception as e:
            logger.error(f"Erreur lors du traitement du texte extrait: {e}")
    
    logger.info(f"Traitement de {len(model1_formatted)} objets du modèle 1 et {len(model2_formatted)} objets du modèle 2")
    
    # Pour le debugging, afficher les classes et leurs boîtes
    for item in model1_formatted:
        if item['class_name'] == 'class':
            logger.debug(f"Classe détectée: {item['label']} à la position {item['box']}")
    
    # Pour le debugging, afficher les flèches
    for item in model1_formatted:
        if item['class_name'] == 'arrow':
            logger.debug(f"Flèche détectée: {item['label']} à la position {item['box']}")
    
    # Pour le debugging, afficher les relations
    for item in model2_formatted:
        logger.debug(f"Relation détectée: {item['class_name']} à la position {item['box']}")
    
    # Traiter les détections pour générer les phrases de relations
    relations = relation_processor.process_detections(
        model1_formatted,
        model2_formatted,
        extracted_dict
    )
    
    # Formater la sortie pour l'affichage
    relations_text = relation_processor.format_relations_output(relations)
    
    logger.info(f"Traitement terminé: {len(relations)} relations trouvées")
    return relations_text

def save_relations_to_file(relations_text):
    """
    Sauvegarde les relations détectées dans un fichier texte.
    
    Args:
        relations_text: Texte des relations formaté
        
    Returns:
        Chemin du fichier sauvegardé
    """
    output_path = "relations_detectees.txt"
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(relations_text)
    logger.info(f"Relations sauvegardées dans {output_path}")
    return output_path

async def append_relations_to_results(extracted_texts, results1, results2):
    """
    Ajoute les relations détectées aux résultats existants.
    Cette fonction est destinée à être appelée depuis le serveur principal.
    
    Args:
        extracted_texts: Liste des textes extraits des classes par Groq
        results1: Résultats du modèle 1 (classes et flèches)
        results2: Résultats du modèle 2 (types de relations)
        
    Returns:
        Texte combiné incluant les textes extraits et les relations détectées
    """
    # Activer le debug pour diagnostiquer les problèmes
    logger.setLevel(logging.DEBUG)
    
    # Traiter les relations
    relations_text = await process_relations(results1, results2, extracted_texts)
    
    # Sauvegarder les relations dans un fichier séparé pour le débogage
    save_relations_to_file(relations_text)
    
    # Combiner les textes extraits et les relations
    combined_text = "\n\n".join(extracted_texts)
    
    # Ajouter les relations à la fin
    combined_text += f"\n\n{relations_text}"
    
    return combined_text