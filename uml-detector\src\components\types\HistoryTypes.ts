export interface HistoryItem {
  id: string;
  userId: string;
  title: string;
  originalImageUrl: string;
  annotatedImageUrl?: string;
  extractedText: string;
  mermaidCode?: string;
  createdAt: Date;
  updatedAt: Date;
  thumbnail: string;
}

export interface HistoryContextType {
  historyItems: HistoryItem[];
  addHistoryItem: (item: Omit<HistoryItem, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  deleteHistoryItem: (id: string) => Promise<void>;
  deleteAllUserHistory: () => Promise<void>; // Ajouter cette ligne
  loadHistoryItem: (id: string) => Promise<void>;
  searchHistory: (query: string) => HistoryItem[];
  loading: boolean;
}