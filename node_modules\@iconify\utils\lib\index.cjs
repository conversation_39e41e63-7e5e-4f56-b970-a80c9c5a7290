'use strict';

const customisations_defaults = require('./customisations/defaults.cjs');
const customisations_merge = require('./customisations/merge.cjs');
const customisations_bool = require('./customisations/bool.cjs');
const customisations_flip = require('./customisations/flip.cjs');
const customisations_rotate = require('./customisations/rotate.cjs');
const icon_name = require('./icon/name.cjs');
const icon_merge = require('./icon/merge.cjs');
const icon_transformations = require('./icon/transformations.cjs');
const icon_defaults = require('./icon/defaults.cjs');
const icon_square = require('./icon/square.cjs');
const iconSet_tree = require('./icon-set/tree.cjs');
const iconSet_parse = require('./icon-set/parse.cjs');
const iconSet_validate = require('./icon-set/validate.cjs');
const iconSet_validateBasic = require('./icon-set/validate-basic.cjs');
const iconSet_expand = require('./icon-set/expand.cjs');
const iconSet_minify = require('./icon-set/minify.cjs');
const iconSet_getIcons = require('./icon-set/get-icons.cjs');
const iconSet_getIcon = require('./icon-set/get-icon.cjs');
const iconSet_convertInfo = require('./icon-set/convert-info.cjs');
const svg_build = require('./svg/build.cjs');
const svg_defs = require('./svg/defs.cjs');
const svg_id = require('./svg/id.cjs');
const svg_size = require('./svg/size.cjs');
const svg_encodeSvgForCss = require('./svg/encode-svg-for-css.cjs');
const svg_trim = require('./svg/trim.cjs');
const svg_pretty = require('./svg/pretty.cjs');
const svg_html = require('./svg/html.cjs');
const svg_url = require('./svg/url.cjs');
const svg_innerHtml = require('./svg/inner-html.cjs');
const svg_viewbox = require('./svg/viewbox.cjs');
const svg_parse = require('./svg/parse.cjs');
const colors_keywords = require('./colors/keywords.cjs');
const colors_index = require('./colors/index.cjs');
const css_icon = require('./css/icon.cjs');
const css_icons = require('./css/icons.cjs');
const loader_utils = require('./loader/utils.cjs');
const loader_custom = require('./loader/custom.cjs');
const loader_modern = require('./loader/modern.cjs');
const loader_loader = require('./loader/loader.cjs');
const emoji_cleanup = require('./emoji/cleanup.cjs');
const emoji_convert = require('./emoji/convert.cjs');
const emoji_format = require('./emoji/format.cjs');
const emoji_test_parse = require('./emoji/test/parse.cjs');
const emoji_test_variations = require('./emoji/test/variations.cjs');
const emoji_test_missing = require('./emoji/test/missing.cjs');
const emoji_regex_create = require('./emoji/regex/create.cjs');
const emoji_parse = require('./emoji/parse.cjs');
const emoji_replace_replace = require('./emoji/replace/replace.cjs');
const misc_strings = require('./misc/strings.cjs');
const misc_objects = require('./misc/objects.cjs');
const misc_title = require('./misc/title.cjs');
require('./css/common.cjs');
require('./css/format.cjs');
require('debug');
require('./emoji/data.cjs');
require('./emoji/test/components.cjs');
require('./emoji/regex/tree.cjs');
require('./emoji/regex/base.cjs');
require('./emoji/regex/numbers.cjs');
require('./emoji/regex/similar.cjs');
require('./emoji/test/similar.cjs');
require('./emoji/test/name.cjs');
require('./emoji/test/tree.cjs');
require('./emoji/replace/find.cjs');



exports.defaultIconCustomisations = customisations_defaults.defaultIconCustomisations;
exports.defaultIconSizeCustomisations = customisations_defaults.defaultIconSizeCustomisations;
exports.mergeCustomisations = customisations_merge.mergeCustomisations;
exports.toBoolean = customisations_bool.toBoolean;
exports.flipFromString = customisations_flip.flipFromString;
exports.rotateFromString = customisations_rotate.rotateFromString;
exports.matchIconName = icon_name.matchIconName;
exports.stringToIcon = icon_name.stringToIcon;
exports.validateIconName = icon_name.validateIconName;
exports.mergeIconData = icon_merge.mergeIconData;
exports.mergeIconTransformations = icon_transformations.mergeIconTransformations;
exports.defaultExtendedIconProps = icon_defaults.defaultExtendedIconProps;
exports.defaultIconDimensions = icon_defaults.defaultIconDimensions;
exports.defaultIconProps = icon_defaults.defaultIconProps;
exports.defaultIconTransformations = icon_defaults.defaultIconTransformations;
exports.makeIconSquare = icon_square.makeIconSquare;
exports.getIconsTree = iconSet_tree.getIconsTree;
exports.parseIconSet = iconSet_parse.parseIconSet;
exports.parseIconSetAsync = iconSet_parse.parseIconSetAsync;
exports.validateIconSet = iconSet_validate.validateIconSet;
exports.quicklyValidateIconSet = iconSet_validateBasic.quicklyValidateIconSet;
exports.expandIconSet = iconSet_expand.expandIconSet;
exports.minifyIconSet = iconSet_minify.minifyIconSet;
exports.getIcons = iconSet_getIcons.getIcons;
exports.getIconData = iconSet_getIcon.getIconData;
exports.convertIconSetInfo = iconSet_convertInfo.convertIconSetInfo;
exports.iconToSVG = svg_build.iconToSVG;
exports.mergeDefsAndContent = svg_defs.mergeDefsAndContent;
exports.splitSVGDefs = svg_defs.splitSVGDefs;
exports.wrapSVGContent = svg_defs.wrapSVGContent;
exports.replaceIDs = svg_id.replaceIDs;
exports.calculateSize = svg_size.calculateSize;
exports.encodeSvgForCss = svg_encodeSvgForCss.encodeSvgForCss;
exports.trimSVG = svg_trim.trimSVG;
exports.prettifySVG = svg_pretty.prettifySVG;
exports.iconToHTML = svg_html.iconToHTML;
exports.svgToData = svg_url.svgToData;
exports.svgToURL = svg_url.svgToURL;
exports.cleanUpInnerHTML = svg_innerHtml.cleanUpInnerHTML;
exports.getSVGViewBox = svg_viewbox.getSVGViewBox;
exports.buildParsedSVG = svg_parse.buildParsedSVG;
exports.convertParsedSVG = svg_parse.convertParsedSVG;
exports.parseSVGContent = svg_parse.parseSVGContent;
exports.colorKeywords = colors_keywords.colorKeywords;
exports.colorToString = colors_index.colorToString;
exports.compareColors = colors_index.compareColors;
exports.stringToColor = colors_index.stringToColor;
exports.getIconCSS = css_icon.getIconCSS;
exports.getIconContentCSS = css_icon.getIconContentCSS;
exports.getIconsCSS = css_icons.getIconsCSS;
exports.getIconsContentCSS = css_icons.getIconsContentCSS;
exports.mergeIconProps = loader_utils.mergeIconProps;
exports.getCustomIcon = loader_custom.getCustomIcon;
exports.searchForIcon = loader_modern.searchForIcon;
exports.loadIcon = loader_loader.loadIcon;
exports.getEmojiSequenceFromString = emoji_cleanup.getEmojiSequenceFromString;
exports.getUnqualifiedEmojiSequence = emoji_cleanup.getUnqualifiedEmojiSequence;
exports.convertEmojiSequenceToUTF16 = emoji_convert.convertEmojiSequenceToUTF16;
exports.convertEmojiSequenceToUTF32 = emoji_convert.convertEmojiSequenceToUTF32;
exports.getEmojiCodePoint = emoji_convert.getEmojiCodePoint;
exports.getEmojiUnicode = emoji_convert.getEmojiUnicode;
exports.isUTF32SplitNumber = emoji_convert.isUTF32SplitNumber;
exports.mergeUTF32Numbers = emoji_convert.mergeUTF32Numbers;
exports.splitUTF32Number = emoji_convert.splitUTF32Number;
exports.getEmojiSequenceKeyword = emoji_format.getEmojiSequenceKeyword;
exports.getEmojiSequenceString = emoji_format.getEmojiSequenceString;
exports.getEmojiUnicodeString = emoji_format.getEmojiUnicodeString;
exports.parseEmojiTestFile = emoji_test_parse.parseEmojiTestFile;
exports.getQualifiedEmojiVariations = emoji_test_variations.getQualifiedEmojiVariations;
exports.findMissingEmojis = emoji_test_missing.findMissingEmojis;
exports.createOptimisedRegex = emoji_regex_create.createOptimisedRegex;
exports.createOptimisedRegexForEmojiSequences = emoji_regex_create.createOptimisedRegexForEmojiSequences;
exports.prepareEmojiForIconSet = emoji_parse.prepareEmojiForIconSet;
exports.prepareEmojiForIconsList = emoji_parse.prepareEmojiForIconsList;
exports.findAndReplaceEmojisInText = emoji_replace_replace.findAndReplaceEmojisInText;
exports.camelToKebab = misc_strings.camelToKebab;
exports.camelize = misc_strings.camelize;
exports.pascalize = misc_strings.pascalize;
exports.snakelize = misc_strings.snakelize;
exports.commonObjectProps = misc_objects.commonObjectProps;
exports.compareObjects = misc_objects.compareObjects;
exports.unmergeObjects = misc_objects.unmergeObjects;
exports.sanitiseTitleAttribute = misc_title.sanitiseTitleAttribute;
