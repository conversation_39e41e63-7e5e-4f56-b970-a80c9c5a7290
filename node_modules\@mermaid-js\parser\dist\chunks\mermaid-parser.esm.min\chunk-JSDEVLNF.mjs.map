{"version": 3, "sources": ["../../../src/language/radar/tokenBuilder.ts", "../../../src/language/radar/module.ts"], "sourcesContent": ["import { AbstractMermaidTokenBuilder } from '../common/index.js';\n\nexport class RadarTokenBuilder extends AbstractMermaidTokenBuilder {\n  public constructor() {\n    super(['radar-beta']);\n  }\n}\n", "import type {\n  DefaultSharedCoreModuleContext,\n  LangiumCoreServices,\n  LangiumSharedCoreServices,\n  Module,\n  PartialLangiumCoreServices,\n} from 'langium';\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject,\n} from 'langium';\nimport { CommonValueConverter } from '../common/valueConverter.js';\nimport { MermaidGeneratedSharedModule, RadarGeneratedModule } from '../generated/module.js';\nimport { RadarTokenBuilder } from './tokenBuilder.js';\n\n/**\n * Declaration of `Radar` services.\n */\ninterface RadarAddedServices {\n  parser: {\n    TokenBuilder: RadarTokenBuilder;\n    ValueConverter: CommonValueConverter;\n  };\n}\n\n/**\n * Union of Langium default services and `Radar` services.\n */\nexport type RadarServices = LangiumCoreServices & RadarAddedServices;\n\n/**\n * Dependency injection module that overrides Langium default services and\n * contributes the declared `Radar` services.\n */\nexport const RadarModule: Module<RadarServices, PartialLangiumCoreServices & RadarAddedServices> = {\n  parser: {\n    TokenBuilder: () => new RadarTokenBuilder(),\n    ValueConverter: () => new CommonValueConverter(),\n  },\n};\n\n/**\n * Create the full set of services required by Langium.\n *\n * First inject the shared services by merging two modules:\n *  - Langium default shared services\n *  - Services generated by langium-cli\n *\n * Then inject the language-specific services by merging three modules:\n *  - Langium default language-specific services\n *  - Services generated by langium-cli\n *  - Services specified in this file\n * @param context - Optional module context with the LSP connection\n * @returns An object wrapping the shared services and the language-specific services\n */\nexport function createRadarServices(context: DefaultSharedCoreModuleContext = EmptyFileSystem): {\n  shared: LangiumSharedCoreServices;\n  Radar: RadarServices;\n} {\n  const shared: LangiumSharedCoreServices = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Radar: RadarServices = inject(\n    createDefaultCoreModule({ shared }),\n    RadarGeneratedModule,\n    RadarModule\n  );\n  shared.ServiceRegistry.register(Radar);\n  return { shared, Radar };\n}\n"], "mappings": "wGAEO,IAAMA,EAAN,cAAgCC,CAA4B,CAFnE,MAEmE,CAAAC,EAAA,0BAC1D,aAAc,CACnB,MAAM,CAAC,YAAY,CAAC,CACtB,CACF,EC8BO,IAAMC,EAAsF,CACjG,OAAQ,CACN,aAAcC,EAAA,IAAM,IAAIC,EAAV,gBACd,eAAgBD,EAAA,IAAM,IAAIE,EAAV,iBAClB,CACF,EAgBO,SAASC,EAAoBC,EAA0CC,EAG5E,CACA,IAAMC,EAAoCC,EACxCC,EAA8BJ,CAAO,EACrCK,CACF,EACMC,EAAuBH,EAC3BI,EAAwB,CAAE,OAAAL,CAAO,CAAC,EAClCM,EACAb,CACF,EACA,OAAAO,EAAO,gBAAgB,SAASI,CAAK,EAC9B,CAAE,OAAAJ,EAAQ,MAAAI,CAAM,CACzB,CAfgBV,EAAAG,EAAA", "names": ["RadarTokenBuilder", "AbstractMermaidTokenBuilder", "__name", "RadarModule", "__name", "RadarTokenBuilder", "CommonValueConverter", "createRadarServices", "context", "EmptyFileSystem", "shared", "inject", "createDefaultSharedCoreModule", "MermaidGeneratedSharedModule", "Radar", "createDefaultCoreModule", "RadarGeneratedModule"]}