{"version": 3, "file": "scope-computation.js", "sourceRoot": "", "sources": ["../../src/references/scope-computation.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAOhF,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAC7D,OAAO,EAAE,iBAAiB,EAAE,cAAc,EAAE,MAAM,uBAAuB,CAAC;AAC1E,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAwC9D;;;;;;;;;;GAUG;AACH,MAAM,OAAO,uBAAuB;IAKhC,YAAY,QAA6B;QACrC,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC;QACrD,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,SAAS,CAAC,0BAA0B,CAAC;IACtE,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAAyB,EAAE,WAAW,GAAG,iBAAiB,CAAC,IAAI;QAChF,OAAO,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;IACpG,CAAC;IAED;;;;;;;;;;;OAWG;IACH,KAAK,CAAC,qBAAqB,CAAC,UAAmB,EAAE,QAAkC,EAAE,WAAiD,cAAc,EAAE,cAAiC,iBAAiB,CAAC,IAAI;QACzM,MAAM,OAAO,GAAyB,EAAE,CAAC;QAEzC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC/C,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACtC,MAAM,iBAAiB,CAAC,WAAW,CAAC,CAAC;YACrC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC7C,CAAC;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;OAGG;IACO,UAAU,CAAC,IAAa,EAAE,OAA6B,EAAE,QAAyB;QACxF,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,IAAI,EAAE,CAAC;YACP,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC5E,CAAC;IACL,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,QAAyB,EAAE,WAAW,GAAG,iBAAiB,CAAC,IAAI;QACpF,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC;QAC5C,MAAM,MAAM,GAAG,IAAI,QAAQ,EAA+B,CAAC;QAC3D,wFAAwF;QACxF,KAAK,MAAM,IAAI,IAAI,iBAAiB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7C,MAAM,iBAAiB,CAAC,WAAW,CAAC,CAAC;YACrC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACO,WAAW,CAAC,IAAa,EAAE,QAAyB,EAAE,MAAyB;QACrF,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,IAAI,SAAS,EAAE,CAAC;YACZ,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC7C,IAAI,IAAI,EAAE,CAAC;gBACP,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;YACrF,CAAC;QACL,CAAC;IACL,CAAC;CAEJ"}