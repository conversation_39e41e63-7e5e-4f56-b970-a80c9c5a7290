// https://d3js.org/d3-random/ v3.0.1 Copyright 2010-2021 <PERSON>
!function(n,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r((n="undefined"!=typeof globalThis?globalThis:n||self).d3=n.d3||{})}(this,(function(n){"use strict";var r=Math.random,t=function n(r){function t(n,t){return n=null==n?0:+n,t=null==t?1:+t,1===arguments.length?(t=n,n=0):t-=n,function(){return r()*t+n}}return t.source=n,t}(r),o=function n(r){function t(n,t){return arguments.length<2&&(t=n,n=0),n=Math.floor(n),t=Math.floor(t)-n,function(){return Math.floor(r()*t+n)}}return t.source=n,t}(r),u=function n(r){function t(n,t){var o,u;return n=null==n?0:+n,t=null==t?1:+t,function(){var e;if(null!=o)e=o,o=null;else do{o=2*r()-1,e=2*r()-1,u=o*o+e*e}while(!u||u>1);return n+t*e*Math.sqrt(-2*Math.log(u)/u)}}return t.source=n,t}(r),e=function n(r){var t=u.source(r);function o(){var n=t.apply(this,arguments);return function(){return Math.exp(n())}}return o.source=n,o}(r),a=function n(r){function t(n){return(n=+n)<=0?()=>0:function(){for(var t=0,o=n;o>1;--o)t+=r();return t+o*r()}}return t.source=n,t}(r),i=function n(r){var t=a.source(r);function o(n){if(0==(n=+n))return r;var o=t(n);return function(){return o()/n}}return o.source=n,o}(r),c=function n(r){function t(n){return function(){return-Math.log1p(-r())/n}}return t.source=n,t}(r),f=function n(r){function t(n){if((n=+n)<0)throw new RangeError("invalid alpha");return n=1/-n,function(){return Math.pow(1-r(),n)}}return t.source=n,t}(r),l=function n(r){function t(n){if((n=+n)<0||n>1)throw new RangeError("invalid p");return function(){return Math.floor(r()+n)}}return t.source=n,t}(r),h=function n(r){function t(n){if((n=+n)<0||n>1)throw new RangeError("invalid p");return 0===n?()=>1/0:1===n?()=>1:(n=Math.log1p(-n),function(){return 1+Math.floor(Math.log1p(-r())/n)})}return t.source=n,t}(r),s=function n(r){var t=u.source(r)();function o(n,o){if((n=+n)<0)throw new RangeError("invalid k");if(0===n)return()=>0;if(o=null==o?1:+o,1===n)return()=>-Math.log1p(-r())*o;var u=(n<1?n+1:n)-1/3,e=1/(3*Math.sqrt(u)),a=n<1?()=>Math.pow(r(),1/n):()=>1;return function(){do{do{var n=t(),i=1+e*n}while(i<=0);i*=i*i;var c=1-r()}while(c>=1-.0331*n*n*n*n&&Math.log(c)>=.5*n*n+u*(1-i+Math.log(i)));return u*i*a()*o}}return o.source=n,o}(r),d=function n(r){var t=s.source(r);function o(n,r){var o=t(n),u=t(r);return function(){var n=o();return 0===n?0:n/(n+u())}}return o.source=n,o}(r),M=function n(r){var t=h.source(r),o=d.source(r);function u(n,r){return n=+n,(r=+r)>=1?()=>n:r<=0?()=>0:function(){for(var u=0,e=n,a=r;e*a>16&&e*(1-a)>16;){var i=Math.floor((e+1)*a),c=o(i,e-i+1)();c<=a?(u+=i,e-=i,a=(a-c)/(1-c)):(e=i-1,a/=c)}for(var f=a<.5,l=t(f?a:1-a),h=l(),s=0;h<=e;++s)h+=l();return u+(f?s:e-s)}}return u.source=n,u}(r),v=function n(r){function t(n,t,o){var u;return 0==(n=+n)?u=n=>-Math.log(n):(n=1/n,u=r=>Math.pow(r,n)),t=null==t?0:+t,o=null==o?1:+o,function(){return t+o*u(-Math.log1p(-r()))}}return t.source=n,t}(r),m=function n(r){function t(n,t){return n=null==n?0:+n,t=null==t?1:+t,function(){return n+t*Math.tan(Math.PI*r())}}return t.source=n,t}(r),p=function n(r){function t(n,t){return n=null==n?0:+n,t=null==t?1:+t,function(){var o=r();return n+t*Math.log(o/(1-o))}}return t.source=n,t}(r),g=function n(r){var t=s.source(r),o=M.source(r);function u(n){return function(){for(var u=0,e=n;e>16;){var a=Math.floor(.875*e),i=t(a)();if(i>e)return u+o(a-1,e/i)();u+=a,e-=i}for(var c=-Math.log1p(-r()),f=0;c<=e;++f)c-=Math.log1p(-r());return u+f}}return u.source=n,u}(r);const w=1/4294967296;n.randomBates=i,n.randomBernoulli=l,n.randomBeta=d,n.randomBinomial=M,n.randomCauchy=m,n.randomExponential=c,n.randomGamma=s,n.randomGeometric=h,n.randomInt=o,n.randomIrwinHall=a,n.randomLcg=function(n=Math.random()){let r=0|(0<=n&&n<1?n/w:Math.abs(n));return()=>(r=1664525*r+1013904223|0,w*(r>>>0))},n.randomLogNormal=e,n.randomLogistic=p,n.randomNormal=u,n.randomPareto=f,n.randomPoisson=g,n.randomUniform=t,n.randomWeibull=v,Object.defineProperty(n,"__esModule",{value:!0})}));
