[{"Cas n°": 1, "Description": "Flèche entre 2 classes, avec relation détectée", "Condition(s)": "2 extrémités proches de classes + relation détectée", "Relation déduite": "Relation détectée (ex : généralisation)", "Phrase générée / Action": "A → B (généralisation)"}, {"Cas n°": 2, "Description": "Flèche entre 2 classes, sans relation détectée", "Condition(s)": "2 extrémités proches de classes + aucune relation détectée", "Relation déduite": "Association par défaut", "Phrase générée / Action": "A → B (association)"}, {"Cas n°": 3, "Description": "Une seule extrémité détectée avec classe + relation", "Condition(s)": "1 extrémité avec classe et relation (ex : endpoint)", "Relation déduite": "Association", "Phrase générée / Action": "A → B (association)"}, {"Cas n°": 4, "Description": "Deux extrémités = endpoints", "Condition(s)": "Deux `endpoint` aux extrémités", "Relation déduite": "Association par défaut", "Phrase générée / Action": "A ↔ B (association)"}, {"Cas n°": 5, "Description": "Flèche détectée mais une seule classe détectée", "Condition(s)": "Une seule classe détectée à une extrémité", "Relation déduite": "Classe inconnue de l’autre côté", "Phrase générée / Action": "A → [Classe inconnue] (association)"}, {"Cas n°": 6, "Description": "Deux flèches relient à une même classe centrale", "Condition(s)": "Classe centrale pointée par 2 flèches", "Relation déduite": "Généralisation multiple (héritage)", "Phrase générée / Action": "A hérite de C, B hérite de C"}, {"Cas n°": 7, "Description": "Deux relations détectées sur la même flèche", "Condition(s)": "Superposition de relations (ex : association + généralisation)", "Relation déduite": "Garder la relation la plus spécifique", "Phrase générée / Action": "A → B (relation la plus forte)"}, {"Cas n°": 8, "Description": "Une relation détectée au centre de la flèche", "Condition(s)": "Relation spatialement au centre entre les 2 classes", "Relation déduite": "Utiliser cette relation", "Phrase générée / Action": "A → B (ex : agrégation)"}, {"Cas n°": 9, "Description": "Une seule extrémité a une relation détectée", "Condition(s)": "1 extrémité = endpoint ou relation", "Relation déduite": "Association supposée", "Phrase générée / Action": "A → B (association)"}, {"Cas n°": 10, "Description": "Deux flèches depuis une même classe vers 2 autres", "Condition(s)": "Plusieurs flèches partent d’une classe vers d’autres", "Relation déduite": "Association multiple", "Phrase générée / Action": "A est associée à B et C"}, {"Cas n°": 11, "Description": "Flèche connectée à un rectangle non reconnu", "Condition(s)": "Rectangle non identifié mais contient du texte", "Relation déduite": "OCR détecte une classe", "Phrase générée / Action": "Traiter comme une classe"}, {"Cas n°": 12, "Description": "Relation détectée mais pas de flèche visible", "Condition(s)": "Relation (généralisation...) seule", "Relation déduite": "Inférer 2 classes proches et relier", "Phrase générée / Action": "A → B (relation détectée)"}, {"Cas n°": 13, "Description": "Deux relations entre les mêmes classes", "Condition(s)": "Deux flèches entre A et B avec des types différents", "Relation déduite": "Afficher les deux", "Phrase générée / Action": "A hérite de B, A est associé à B"}, {"Cas n°": 14, "Description": "Flèche cassée détectée en 2 parties", "Condition(s)": "Deux flèches proches alignées", "Relation déduite": "<PERSON>ner en une flèche", "Phrase générée / Action": "Traiter comme cas 1/2/3"}, {"Cas n°": 15, "Description": "Flèche vers une classe abstraite", "Condition(s)": "Classe contient `<<abstract>>` ou similaire", "Relation déduite": "Généralisation vers classe abstraite", "Phrase générée / Action": "A hérite de la classe abstraite B"}, {"Cas n°": 16, "Description": "Modèle détecte one-way mais flèche est bidirectionnelle", "Condition(s)": "one-way-association + 2 `endpoints`", "Relation déduite": "Corriger en association", "Phrase générée / Action": "A ↔ B (association)"}, {"Cas n°": 17, "Description": "Plusieurs relations proches spatialement", "Condition(s)": "Relations de même type proches (< seuil distance)", "Relation déduite": "Fusionner les relations", "Phrase générée / Action": "Ne garder qu’une relation"}, {"Cas n°": 18, "Description": "Aucune classe détectée à une extrémité", "Condition(s)": "Flèche connectée à rien", "Relation déduite": "Classe inconnue", "Phrase générée / Action": "[Classe inconnue] → B (association)"}]