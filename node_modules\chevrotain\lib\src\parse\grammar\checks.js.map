{"version": 3, "file": "checks.js", "sourceRoot": "", "sources": ["../../../../src/parse/grammar/checks.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,KAAK,EACL,OAAO,EACP,UAAU,EACV,IAAI,EACJ,SAAS,EACT,MAAM,EACN,KAAK,EACL,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,QAAQ,EACR,OAAO,EACP,GAAG,EACH,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,GACP,MAAM,WAAW,CAAC;AACnB,OAAO,EAIL,yBAAyB,GAC1B,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EACL,WAAW,EACX,WAAW,IAAI,eAAe,EAC9B,WAAW,EACX,oBAAoB,EACpB,cAAc,EACd,WAAW,EACX,MAAM,EACN,UAAU,EACV,mBAAmB,EACnB,gCAAgC,EAChC,uBAAuB,EACvB,QAAQ,GACT,MAAM,kBAAkB,CAAC;AAC1B,OAAO,EAEL,YAAY,EACZ,gCAAgC,EAChC,sBAAsB,EACtB,WAAW,EACX,oBAAoB,GACrB,MAAM,gBAAgB,CAAC;AACxB,OAAO,EAAE,uBAAuB,EAAE,MAAM,kBAAkB,CAAC;AAY3D,OAAO,EAAE,sBAAsB,EAAE,MAAM,sBAAsB,CAAC;AAE9D,MAAM,UAAU,iBAAiB,CAAC,OAKjC;IACC,MAAM,gCAAgC,GAAG,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC;QAC1E,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,UAAU,EAAE,OAAO,CAAC,UAAU;QAC9B,WAAW,EAAE,OAAO,CAAC,WAAW;KACjC,CAAC,CAAC;IACH,OAAO,GAAG,CAAC,gCAAgC,EAAE,CAAC,YAAY,EAAE,EAAE,CAAC,iBAC7D,IAAI,EAAE,yBAAyB,CAAC,2BAA2B,IACxD,YAAY,EACf,CAAC,CAAC;AACN,CAAC;AAED,MAAM,UAAU,eAAe,CAC7B,SAAiB,EACjB,UAAuB,EACvB,cAAqD,EACrD,WAAmB;IAEnB,MAAM,eAAe,GAA6B,OAAO,CACvD,SAAS,EACT,CAAC,YAAY,EAAE,EAAE,CACf,4BAA4B,CAAC,YAAY,EAAE,cAAc,CAAC,CAC7D,CAAC;IAEF,MAAM,4BAA4B,GAAG,sCAAsC,CACzE,SAAS,EACT,UAAU,EACV,cAAc,CACf,CAAC;IAEF,MAAM,iBAAiB,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,EAAE,CACvD,mBAAmB,CAAC,OAAO,EAAE,cAAc,CAAC,CAC7C,CAAC;IAEF,MAAM,mBAAmB,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,EAAE,CACzD,+BAA+B,CAC7B,OAAO,EACP,SAAS,EACT,WAAW,EACX,cAAc,CACf,CACF,CAAC;IAEF,OAAO,eAAe,CAAC,MAAM,CAC3B,4BAA4B,EAC5B,iBAAiB,EACjB,mBAAmB,CACpB,CAAC;AACJ,CAAC;AAED,SAAS,4BAA4B,CACnC,YAAkB,EAClB,cAAqD;IAErD,MAAM,gBAAgB,GAAG,IAAI,6BAA6B,EAAE,CAAC;IAC7D,YAAY,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IACtC,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,cAAc,CAAC;IAE3D,MAAM,gBAAgB,GAAG,OAAO,CAC9B,kBAAkB,EAClB,+BAA+B,CAChC,CAAC;IAEF,MAAM,UAAU,GAAQ,MAAM,CAAC,gBAAgB,EAAE,CAAC,SAAS,EAAE,EAAE;QAC7D,OAAO,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,cAAmB,EAAE,EAAE;QAC7D,MAAM,SAAS,GAAQ,KAAK,CAAC,cAAc,CAAC,CAAC;QAC7C,MAAM,GAAG,GAAG,cAAc,CAAC,wBAAwB,CACjD,YAAY,EACZ,cAAc,CACf,CAAC;QACF,MAAM,OAAO,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC;QAChD,MAAM,QAAQ,GAAqC;YACjD,OAAO,EAAE,GAAG;YACZ,IAAI,EAAE,yBAAyB,CAAC,qBAAqB;YACrD,QAAQ,EAAE,YAAY,CAAC,IAAI;YAC3B,OAAO,EAAE,OAAO;YAChB,UAAU,EAAE,SAAS,CAAC,GAAG;SAC1B,CAAC;QAEF,MAAM,KAAK,GAAG,0BAA0B,CAAC,SAAS,CAAC,CAAC;QACpD,IAAI,KAAK,EAAE;YACT,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;SAC5B;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,UAAU,+BAA+B,CAC7C,IAA+B;IAE/B,OAAO,GAAG,oBAAoB,CAAC,IAAI,CAAC,MAClC,IAAI,CAAC,GACP,MAAM,0BAA0B,CAAC,IAAI,CAAC,EAAE,CAAC;AAC3C,CAAC;AAED,SAAS,0BAA0B,CAAC,IAA+B;IACjE,IAAI,IAAI,YAAY,QAAQ,EAAE;QAC5B,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;KAC/B;SAAM,IAAI,IAAI,YAAY,WAAW,EAAE;QACtC,OAAO,IAAI,CAAC,eAAe,CAAC;KAC7B;SAAM;QACL,OAAO,EAAE,CAAC;KACX;AACH,CAAC;AAED,MAAM,OAAO,6BAA8B,SAAQ,WAAW;IAA9D;;QACS,mBAAc,GAAgC,EAAE,CAAC;IAmC1D,CAAC;IAjCQ,gBAAgB,CAAC,OAAoB;QAC1C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;IAEM,WAAW,CAAC,MAAc;QAC/B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAEM,4BAA4B,CAAC,OAAgC;QAClE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;IAEM,wBAAwB,CAAC,UAA+B;QAC7D,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACvC,CAAC;IAEM,qCAAqC,CAC1C,aAA+C;QAE/C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC1C,CAAC;IAEM,eAAe,CAAC,IAAgB;QACrC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAEM,gBAAgB,CAAC,EAAe;QACrC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC;IAEM,aAAa,CAAC,QAAkB;QACrC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;CACF;AAED,MAAM,UAAU,+BAA+B,CAC7C,IAAU,EACV,QAAgB,EAChB,SAAiB,EACjB,cAAqD;IAErD,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,MAAM,WAAW,GAAG,MAAM,CACxB,QAAQ,EACR,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;QAClB,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;YAC9B,OAAO,MAAM,GAAG,CAAC,CAAC;SACnB;QACD,OAAO,MAAM,CAAC;IAChB,CAAC,EACD,CAAC,CACF,CAAC;IACF,IAAI,WAAW,GAAG,CAAC,EAAE;QACnB,MAAM,MAAM,GAAG,cAAc,CAAC,2BAA2B,CAAC;YACxD,YAAY,EAAE,IAAI;YAClB,WAAW,EAAE,SAAS;SACvB,CAAC,CAAC;QACH,MAAM,CAAC,IAAI,CAAC;YACV,OAAO,EAAE,MAAM;YACf,IAAI,EAAE,yBAAyB,CAAC,mBAAmB;YACnD,QAAQ,EAAE,IAAI,CAAC,IAAI;SACpB,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,+FAA+F;AAC/F,8FAA8F;AAC9F,0CAA0C;AAC1C,MAAM,UAAU,wBAAwB,CACtC,QAAgB,EAChB,iBAA2B,EAC3B,SAAiB;IAEjB,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,IAAI,MAAM,CAAC;IAEX,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,QAAQ,CAAC,EAAE;QAC1C,MAAM;YACJ,kCAAkC,QAAQ,6CAA6C,SAAS,IAAI;gBACpG,oDAAoD,CAAC;QACvD,MAAM,CAAC,IAAI,CAAC;YACV,OAAO,EAAE,MAAM;YACf,IAAI,EAAE,yBAAyB,CAAC,qBAAqB;YACrD,QAAQ,EAAE,QAAQ;SACnB,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,UAAU,uBAAuB,CACrC,OAAa,EACb,QAAc,EACd,cAAqD,EACrD,OAAe,EAAE;IAEjB,MAAM,MAAM,GAA6B,EAAE,CAAC;IAC5C,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACnE,IAAI,OAAO,CAAC,gBAAgB,CAAC,EAAE;QAC7B,OAAO,EAAE,CAAC;KACX;SAAM;QACL,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;QAC9B,MAAM,kBAAkB,GAAG,QAAQ,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;QAC/D,IAAI,kBAAkB,EAAE;YACtB,MAAM,CAAC,IAAI,CAAC;gBACV,OAAO,EAAE,cAAc,CAAC,uBAAuB,CAAC;oBAC9C,YAAY,EAAE,OAAO;oBACrB,iBAAiB,EAAE,IAAI;iBACxB,CAAC;gBACF,IAAI,EAAE,yBAAyB,CAAC,cAAc;gBAC9C,QAAQ,EAAE,QAAQ;aACnB,CAAC,CAAC;SACJ;QAED,4EAA4E;QAC5E,2FAA2F;QAC3F,MAAM,cAAc,GAAG,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC5E,MAAM,mBAAmB,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC,WAAW,EAAE,EAAE;YAClE,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;YAC5B,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC1B,OAAO,uBAAuB,CAC5B,OAAO,EACP,WAAW,EACX,cAAc,EACd,OAAO,CACR,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;KAC3C;AACH,CAAC;AAED,MAAM,UAAU,oBAAoB,CAAC,UAAyB;IAC5D,IAAI,MAAM,GAAW,EAAE,CAAC;IACxB,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE;QACvB,OAAO,MAAM,CAAC;KACf;IACD,MAAM,SAAS,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;IAEpC,0BAA0B;IAC1B,IAAI,SAAS,YAAY,WAAW,EAAE;QACpC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;KACvC;SAAM,IACL,SAAS,YAAY,eAAe;QACpC,SAAS,YAAY,MAAM;QAC3B,SAAS,YAAY,mBAAmB;QACxC,SAAS,YAAY,gCAAgC;QACrD,SAAS,YAAY,uBAAuB;QAC5C,SAAS,YAAY,UAAU,EAC/B;QACA,MAAM,GAAG,MAAM,CAAC,MAAM,CACpB,oBAAoB,CAAgB,SAAS,CAAC,UAAU,CAAC,CAC1D,CAAC;KACH;SAAM,IAAI,SAAS,YAAY,WAAW,EAAE;QAC3C,+CAA+C;QAC/C,MAAM,GAAG,OAAO,CACd,GAAG,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,EAAE,CACvC,oBAAoB,CAAmB,UAAW,CAAC,UAAU,CAAC,CAC/D,CACF,CAAC;KACH;SAAM,IAAI,SAAS,YAAY,QAAQ,EAAE;QACxC,6BAA6B;KAC9B;SAAM;QACL,MAAM,KAAK,CAAC,sBAAsB,CAAC,CAAC;KACrC;IAED,MAAM,eAAe,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC;IAClD,MAAM,OAAO,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IACtC,IAAI,eAAe,IAAI,OAAO,EAAE;QAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9B,OAAO,MAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;KAClD;SAAM;QACL,OAAO,MAAM,CAAC;KACf;AACH,CAAC;AAED,MAAM,WAAY,SAAQ,WAAW;IAArC;;QACS,iBAAY,GAAkB,EAAE,CAAC;IAK1C,CAAC;IAHQ,gBAAgB,CAAC,IAAiB;QACvC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;CACF;AAED,MAAM,UAAU,0BAA0B,CACxC,YAAkB,EAClB,cAAqD;IAErD,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;IACtC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IACjC,MAAM,GAAG,GAAG,WAAW,CAAC,YAAY,CAAC;IAErC,MAAM,MAAM,GAAG,OAAO,CACpB,GAAG,EACH,CAAC,MAAM,EAAE,EAAE;QACT,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAChD,OAAO,OAAO,CAAC,UAAU,EAAE,CAAC,eAAe,EAAE,UAAU,EAAE,EAAE;YACzD,MAAM,kBAAkB,GAAG,uBAAuB,CAChD,CAAC,eAAe,CAAC,EACjB,EAAE,EACF,sBAAsB,EACtB,CAAC,CACF,CAAC;YACF,IAAI,OAAO,CAAC,kBAAkB,CAAC,EAAE;gBAC/B,OAAO;oBACL;wBACE,OAAO,EAAE,cAAc,CAAC,0BAA0B,CAAC;4BACjD,YAAY,EAAE,YAAY;4BAC1B,WAAW,EAAE,MAAM;4BACnB,cAAc,EAAE,UAAU;yBAC3B,CAAC;wBACF,IAAI,EAAE,yBAAyB,CAAC,mBAAmB;wBACnD,QAAQ,EAAE,YAAY,CAAC,IAAI;wBAC3B,UAAU,EAAE,MAAM,CAAC,GAAG;wBACtB,WAAW,EAAE,UAAU,GAAG,CAAC;qBAC5B;iBACF,CAAC;aACH;iBAAM;gBACL,OAAO,EAAE,CAAC;aACX;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CACF,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,UAAU,wCAAwC,CACtD,YAAkB,EAClB,kBAA0B,EAC1B,cAAqD;IAErD,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;IACtC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IACjC,IAAI,GAAG,GAAG,WAAW,CAAC,YAAY,CAAC;IAEnC,uCAAuC;IACvC,wDAAwD;IACxD,GAAG,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,iBAAiB,KAAK,IAAI,CAAC,CAAC;IAEjE,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,MAAmB,EAAE,EAAE;QAClD,MAAM,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC;QAClC,MAAM,kBAAkB,GAAG,MAAM,CAAC,YAAY,IAAI,kBAAkB,CAAC;QACrE,MAAM,YAAY,GAAG,sBAAsB,CACzC,cAAc,EACd,YAAY,EACZ,kBAAkB,EAClB,MAAM,CACP,CAAC;QACF,MAAM,mBAAmB,GAAG,4BAA4B,CACtD,YAAY,EACZ,MAAM,EACN,YAAY,EACZ,cAAc,CACf,CAAC;QACF,MAAM,yBAAyB,GAAG,kCAAkC,CAClE,YAAY,EACZ,MAAM,EACN,YAAY,EACZ,cAAc,CACf,CAAC;QAEF,OAAO,mBAAmB,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;IAC/D,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,OAAO,mBAAoB,SAAQ,WAAW;IAApD;;QACS,mBAAc,GAEd,EAAE,CAAC;IAmBZ,CAAC;IAjBQ,4BAA4B,CAAC,OAAgC;QAClE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;IAEM,wBAAwB,CAAC,UAA+B;QAC7D,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACvC,CAAC;IAEM,qCAAqC,CAC1C,aAA+C;QAE/C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC1C,CAAC;IAEM,eAAe,CAAC,IAAgB;QACrC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;CACF;AAED,MAAM,UAAU,mBAAmB,CACjC,YAAkB,EAClB,cAAqD;IAErD,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;IACtC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IACjC,MAAM,GAAG,GAAG,WAAW,CAAC,YAAY,CAAC;IAErC,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,EAAE;QACrC,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG,EAAE;YAClC,OAAO;gBACL;oBACE,OAAO,EAAE,cAAc,CAAC,6BAA6B,CAAC;wBACpD,YAAY,EAAE,YAAY;wBAC1B,WAAW,EAAE,MAAM;qBACpB,CAAC;oBACF,IAAI,EAAE,yBAAyB,CAAC,aAAa;oBAC7C,QAAQ,EAAE,YAAY,CAAC,IAAI;oBAC3B,UAAU,EAAE,MAAM,CAAC,GAAG;iBACvB;aACF,CAAC;SACH;aAAM;YACL,OAAO,EAAE,CAAC;SACX;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,UAAU,iCAAiC,CAC/C,aAAqB,EACrB,YAAoB,EACpB,cAAqD;IAErD,MAAM,MAAM,GAA6B,EAAE,CAAC;IAC5C,OAAO,CAAC,aAAa,EAAE,CAAC,WAAW,EAAE,EAAE;QACrC,MAAM,gBAAgB,GAAG,IAAI,mBAAmB,EAAE,CAAC;QACnD,WAAW,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QACrC,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,cAAc,CAAC;QAC3D,OAAO,CAAC,kBAAkB,EAAE,CAAC,QAAQ,EAAE,EAAE;YACvC,MAAM,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;YACvC,MAAM,kBAAkB,GAAG,QAAQ,CAAC,YAAY,IAAI,YAAY,CAAC;YACjE,MAAM,cAAc,GAAG,QAAQ,CAAC,GAAG,CAAC;YACpC,MAAM,KAAK,GAAG,gCAAgC,CAC5C,cAAc,EACd,WAAW,EACX,QAAQ,EACR,kBAAkB,CACnB,CAAC;YACF,MAAM,qBAAqB,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACvC,IAAI,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,EAAE;gBAC3C,MAAM,MAAM,GAAG,cAAc,CAAC,yBAAyB,CAAC;oBACtD,YAAY,EAAE,WAAW;oBACzB,UAAU,EAAE,QAAQ;iBACrB,CAAC,CAAC;gBACH,MAAM,CAAC,IAAI,CAAC;oBACV,OAAO,EAAE,MAAM;oBACf,IAAI,EAAE,yBAAyB,CAAC,sBAAsB;oBACtD,QAAQ,EAAE,WAAW,CAAC,IAAI;iBAC3B,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AAOD,SAAS,4BAA4B,CACnC,YAA2B,EAC3B,WAAwB,EACxB,IAAU,EACV,cAAqD;IAErD,MAAM,mBAAmB,GAAgB,EAAE,CAAC;IAC5C,MAAM,oBAAoB,GAAG,MAAM,CACjC,YAAY,EACZ,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,EAAE;QAC9B,kDAAkD;QAClD,IAAI,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,iBAAiB,KAAK,IAAI,EAAE;YACjE,OAAO,MAAM,CAAC;SACf;QAED,OAAO,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,EAAE;YAC5B,MAAM,qBAAqB,GAAG,CAAC,UAAU,CAAC,CAAC;YAC3C,OAAO,CAAC,YAAY,EAAE,CAAC,YAAY,EAAE,eAAe,EAAE,EAAE;gBACtD,IACE,UAAU,KAAK,eAAe;oBAC9B,YAAY,CAAC,YAAY,EAAE,QAAQ,CAAC;oBACpC,0DAA0D;oBAC1D,WAAW,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,iBAAiB,KAAK,IAAI,EAClE;oBACA,qBAAqB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;iBAC7C;YACH,CAAC,CAAC,CAAC;YAEH,IACE,qBAAqB,CAAC,MAAM,GAAG,CAAC;gBAChC,CAAC,YAAY,CAAC,mBAAmB,EAAE,QAAQ,CAAC,EAC5C;gBACA,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACnC,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,qBAAqB;oBAC3B,IAAI,EAAE,QAAQ;iBACf,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,CAAC;QACH,OAAO,MAAM,CAAC;IAChB,CAAC,EACD,EAA6C,CAC9C,CAAC;IAEF,MAAM,UAAU,GAAG,GAAG,CAAC,oBAAoB,EAAE,CAAC,iBAAiB,EAAE,EAAE;QACjE,MAAM,WAAW,GAAG,GAAG,CACrB,iBAAiB,CAAC,IAAI,EACtB,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,GAAG,CAAC,CAC/B,CAAC;QAEF,MAAM,WAAW,GAAG,cAAc,CAAC,8BAA8B,CAAC;YAChE,YAAY,EAAE,IAAI;YAClB,WAAW,EAAE,WAAW;YACxB,gBAAgB,EAAE,WAAW;YAC7B,UAAU,EAAE,iBAAiB,CAAC,IAAI;SACnC,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,WAAW;YACpB,IAAI,EAAE,yBAAyB,CAAC,cAAc;YAC9C,QAAQ,EAAE,IAAI,CAAC,IAAI;YACnB,UAAU,EAAE,WAAW,CAAC,GAAG;YAC3B,YAAY,EAAE,iBAAiB,CAAC,IAAI;SACrC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,MAAM,UAAU,kCAAkC,CAChD,YAA2B,EAC3B,WAAwB,EACxB,IAAU,EACV,cAAqD;IAErD,UAAU;IACV,MAAM,eAAe,GAAG,MAAM,CAC5B,YAAY,EACZ,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE;QACvB,MAAM,eAAe,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,EAAE;YAChD,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC;QACH,OAAO,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;IACxC,CAAC,EACD,EAA0C,CAC3C,CAAC;IAEF,MAAM,MAAM,GAAG,OAAO,CACpB,OAAO,CAAC,eAAe,EAAE,CAAC,cAAc,EAAE,EAAE;QAC1C,MAAM,eAAe,GAAG,WAAW,CAAC,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QACnE,kDAAkD;QAClD,IAAI,eAAe,CAAC,iBAAiB,KAAK,IAAI,EAAE;YAC9C,OAAO,EAAE,CAAC;SACX;QACD,MAAM,SAAS,GAAG,cAAc,CAAC,GAAG,CAAC;QACrC,MAAM,UAAU,GAAG,cAAc,CAAC,IAAI,CAAC;QAEvC,MAAM,gCAAgC,GAAG,MAAM,CAC7C,eAAe,EACf,CAAC,gBAAgB,EAAE,EAAE;YACnB,6EAA6E;YAC7E,OAAO;YACL,0DAA0D;YAC1D,WAAW,CAAC,UAAU,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,iBAAiB;gBAC5D,IAAI;gBACN,gBAAgB,CAAC,GAAG,GAAG,SAAS;gBAChC,0DAA0D;gBAC1D,oDAAoD;gBACpD,oBAAoB,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAU,CAAC,CACxD,CAAC;QACJ,CAAC,CACF,CAAC;QAEF,MAAM,oBAAoB,GAAG,GAAG,CAC9B,gCAAgC,EAChC,CAAC,iBAAiB,EAA+C,EAAE;YACjE,MAAM,WAAW,GAAG,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC;YAC/D,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC;YAEhE,MAAM,OAAO,GAAG,cAAc,CAAC,oCAAoC,CAAC;gBAClE,YAAY,EAAE,IAAI;gBAClB,WAAW,EAAE,WAAW;gBACxB,gBAAgB,EAAE,WAAW;gBAC7B,UAAU,EAAE,iBAAiB,CAAC,IAAI;aACnC,CAAC,CAAC;YACH,OAAO;gBACL,OAAO,EAAE,OAAO;gBAChB,IAAI,EAAE,yBAAyB,CAAC,qBAAqB;gBACrD,QAAQ,EAAE,IAAI,CAAC,IAAI;gBACnB,UAAU,EAAE,UAAU;gBACtB,YAAY,EAAE,WAAW;aAC1B,CAAC;QACJ,CAAC,CACF,CAAC;QAEF,OAAO,oBAAoB,CAAC;IAC9B,CAAC,CAAC,CACH,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,sCAAsC,CAC7C,SAAiB,EACjB,UAAuB,EACvB,cAAqD;IAErD,MAAM,MAAM,GAA6B,EAAE,CAAC;IAE5C,MAAM,UAAU,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAElE,OAAO,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,EAAE;QAC9B,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC;QACnC,IAAI,QAAQ,CAAC,UAAU,EAAE,YAAY,CAAC,EAAE;YACtC,MAAM,MAAM,GAAG,cAAc,CAAC,2BAA2B,CAAC,QAAQ,CAAC,CAAC;YAEpE,MAAM,CAAC,IAAI,CAAC;gBACV,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE,yBAAyB,CAAC,+BAA+B;gBAC/D,QAAQ,EAAE,YAAY;aACvB,CAAC,CAAC;SACJ;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC"}