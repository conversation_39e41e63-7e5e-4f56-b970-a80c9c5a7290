{"version": 3, "sources": ["../../../package.json"], "sourcesContent": ["{\n  \"name\": \"mermaid\",\n  \"version\": \"11.6.0\",\n  \"description\": \"Markdown-ish syntax for generating flowcharts, mindmaps, sequence diagrams, class diagrams, gantt charts, git graphs and more.\",\n  \"type\": \"module\",\n  \"module\": \"./dist/mermaid.core.mjs\",\n  \"types\": \"./dist/mermaid.d.ts\",\n  \"exports\": {\n    \".\": {\n      \"types\": \"./dist/mermaid.d.ts\",\n      \"import\": \"./dist/mermaid.core.mjs\",\n      \"default\": \"./dist/mermaid.core.mjs\"\n    },\n    \"./*\": \"./*\"\n  },\n  \"keywords\": [\n    \"diagram\",\n    \"markdown\",\n    \"flowchart\",\n    \"sequence diagram\",\n    \"gantt\",\n    \"class diagram\",\n    \"git graph\",\n    \"mindmap\",\n    \"packet diagram\",\n    \"c4 diagram\",\n    \"er diagram\",\n    \"pie chart\",\n    \"pie diagram\",\n    \"quadrant chart\",\n    \"requirement diagram\",\n    \"graph\"\n  ],\n  \"scripts\": {\n    \"clean\": \"rimraf dist\",\n    \"dev\": \"pnpm -w dev\",\n    \"docs:code\": \"typedoc src/defaultConfig.ts src/config.ts src/mermaid.ts && prettier --write ./src/docs/config/setup\",\n    \"docs:build\": \"rimraf ../../docs && pnpm docs:code && pnpm docs:spellcheck && tsx scripts/docs.cli.mts\",\n    \"docs:verify\": \"pnpm docs:code && pnpm docs:spellcheck && tsx scripts/docs.cli.mts --verify\",\n    \"docs:pre:vitepress\": \"pnpm --filter ./src/docs prefetch && rimraf src/vitepress && pnpm docs:code && tsx scripts/docs.cli.mts --vitepress && pnpm --filter ./src/vitepress install --no-frozen-lockfile --ignore-scripts\",\n    \"docs:build:vitepress\": \"pnpm docs:pre:vitepress && (cd src/vitepress && pnpm run build) && cpy --flat src/docs/landing/ ./src/vitepress/.vitepress/dist/landing\",\n    \"docs:dev\": \"pnpm docs:pre:vitepress && concurrently \\\"pnpm --filter ./src/vitepress dev\\\" \\\"tsx scripts/docs.cli.mts --watch --vitepress\\\"\",\n    \"docs:dev:docker\": \"pnpm docs:pre:vitepress && concurrently \\\"pnpm --filter ./src/vitepress dev:docker\\\" \\\"tsx scripts/docs.cli.mts --watch --vitepress\\\"\",\n    \"docs:serve\": \"pnpm docs:build:vitepress && vitepress serve src/vitepress\",\n    \"docs:spellcheck\": \"cspell \\\"src/docs/**/*.md\\\"\",\n    \"docs:release-version\": \"tsx scripts/update-release-version.mts\",\n    \"docs:verify-version\": \"tsx scripts/update-release-version.mts --verify\",\n    \"types:build-config\": \"tsx scripts/create-types-from-json-schema.mts\",\n    \"types:verify-config\": \"tsx scripts/create-types-from-json-schema.mts --verify\",\n    \"checkCircle\": \"npx madge --circular ./src\",\n    \"prepublishOnly\": \"pnpm docs:verify-version\"\n  },\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"https://github.com/mermaid-js/mermaid\"\n  },\n  \"author\": \"Knut Sveidqvist\",\n  \"license\": \"MIT\",\n  \"standard\": {\n    \"ignore\": [\n      \"**/parser/*.js\",\n      \"dist/**/*.js\",\n      \"cypress/**/*.js\"\n    ],\n    \"globals\": [\n      \"page\"\n    ]\n  },\n  \"dependencies\": {\n    \"@braintree/sanitize-url\": \"^7.0.4\",\n    \"@iconify/utils\": \"^2.1.33\",\n    \"@mermaid-js/parser\": \"workspace:^\",\n    \"@types/d3\": \"^7.4.3\",\n    \"cytoscape\": \"^3.29.3\",\n    \"cytoscape-cose-bilkent\": \"^4.1.0\",\n    \"cytoscape-fcose\": \"^2.2.0\",\n    \"d3\": \"^7.9.0\",\n    \"d3-sankey\": \"^0.12.3\",\n    \"dagre-d3-es\": \"7.0.11\",\n    \"dayjs\": \"^1.11.13\",\n    \"dompurify\": \"^3.2.4\",\n    \"katex\": \"^0.16.9\",\n    \"khroma\": \"^2.1.0\",\n    \"lodash-es\": \"^4.17.21\",\n    \"marked\": \"^15.0.7\",\n    \"roughjs\": \"^4.6.6\",\n    \"stylis\": \"^4.3.6\",\n    \"ts-dedent\": \"^2.2.0\",\n    \"uuid\": \"^11.1.0\"\n  },\n  \"devDependencies\": {\n    \"@adobe/jsonschema2md\": \"^8.0.2\",\n    \"@iconify/types\": \"^2.0.0\",\n    \"@types/cytoscape\": \"^3.21.9\",\n    \"@types/cytoscape-fcose\": \"^2.2.4\",\n    \"@types/d3-sankey\": \"^0.12.4\",\n    \"@types/d3-scale\": \"^4.0.9\",\n    \"@types/d3-scale-chromatic\": \"^3.1.0\",\n    \"@types/d3-selection\": \"^3.0.11\",\n    \"@types/d3-shape\": \"^3.1.7\",\n    \"@types/jsdom\": \"^21.1.7\",\n    \"@types/katex\": \"^0.16.7\",\n    \"@types/lodash-es\": \"^4.17.12\",\n    \"@types/micromatch\": \"^4.0.9\",\n    \"@types/stylis\": \"^4.2.7\",\n    \"@types/uuid\": \"^10.0.0\",\n    \"ajv\": \"^8.17.1\",\n    \"chokidar\": \"^4.0.3\",\n    \"concurrently\": \"^9.1.2\",\n    \"csstree-validator\": \"^4.0.1\",\n    \"globby\": \"^14.0.2\",\n    \"jison\": \"^0.4.18\",\n    \"js-base64\": \"^3.7.7\",\n    \"jsdom\": \"^26.0.0\",\n    \"json-schema-to-typescript\": \"^15.0.4\",\n    \"micromatch\": \"^4.0.8\",\n    \"path-browserify\": \"^1.0.1\",\n    \"prettier\": \"^3.5.2\",\n    \"remark\": \"^15.0.1\",\n    \"remark-frontmatter\": \"^5.0.0\",\n    \"remark-gfm\": \"^4.0.1\",\n    \"rimraf\": \"^6.0.1\",\n    \"start-server-and-test\": \"^2.0.10\",\n    \"type-fest\": \"^4.35.0\",\n    \"typedoc\": \"^0.27.8\",\n    \"typedoc-plugin-markdown\": \"^4.4.2\",\n    \"typescript\": \"~5.7.3\",\n    \"unist-util-flatmap\": \"^1.0.0\",\n    \"unist-util-visit\": \"^5.0.0\",\n    \"vitepress\": \"^1.0.2\",\n    \"vitepress-plugin-search\": \"1.0.4-alpha.22\"\n  },\n  \"files\": [\n    \"dist/\",\n    \"README.md\"\n  ],\n  \"publishConfig\": {\n    \"access\": \"public\"\n  }\n}\n"], "mappings": ";AAAA;AAAA,EACE,MAAQ;AAAA,EACR,SAAW;AAAA,EACX,aAAe;AAAA,EACf,MAAQ;AAAA,EACR,QAAU;AAAA,EACV,OAAS;AAAA,EACT,SAAW;AAAA,IACT,KAAK;AAAA,MACH,OAAS;AAAA,MACT,QAAU;AAAA,MACV,SAAW;AAAA,IACb;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,UAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAW;AAAA,IACT,OAAS;AAAA,IACT,KAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,eAAe;AAAA,IACf,sBAAsB;AAAA,IACtB,wBAAwB;AAAA,IACxB,YAAY;AAAA,IACZ,mBAAmB;AAAA,IACnB,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,sBAAsB;AAAA,IACtB,uBAAuB;AAAA,IACvB,aAAe;AAAA,IACf,gBAAkB;AAAA,EACpB;AAAA,EACA,YAAc;AAAA,IACZ,MAAQ;AAAA,IACR,KAAO;AAAA,EACT;AAAA,EACA,QAAU;AAAA,EACV,SAAW;AAAA,EACX,UAAY;AAAA,IACV,QAAU;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,SAAW;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAgB;AAAA,IACd,2BAA2B;AAAA,IAC3B,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,aAAa;AAAA,IACb,WAAa;AAAA,IACb,0BAA0B;AAAA,IAC1B,mBAAmB;AAAA,IACnB,IAAM;AAAA,IACN,aAAa;AAAA,IACb,eAAe;AAAA,IACf,OAAS;AAAA,IACT,WAAa;AAAA,IACb,OAAS;AAAA,IACT,QAAU;AAAA,IACV,aAAa;AAAA,IACb,QAAU;AAAA,IACV,SAAW;AAAA,IACX,QAAU;AAAA,IACV,aAAa;AAAA,IACb,MAAQ;AAAA,EACV;AAAA,EACA,iBAAmB;AAAA,IACjB,wBAAwB;AAAA,IACxB,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB,0BAA0B;AAAA,IAC1B,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,6BAA6B;AAAA,IAC7B,uBAAuB;AAAA,IACvB,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,KAAO;AAAA,IACP,UAAY;AAAA,IACZ,cAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,QAAU;AAAA,IACV,OAAS;AAAA,IACT,aAAa;AAAA,IACb,OAAS;AAAA,IACT,6BAA6B;AAAA,IAC7B,YAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,UAAY;AAAA,IACZ,QAAU;AAAA,IACV,sBAAsB;AAAA,IACtB,cAAc;AAAA,IACd,QAAU;AAAA,IACV,yBAAyB;AAAA,IACzB,aAAa;AAAA,IACb,SAAW;AAAA,IACX,2BAA2B;AAAA,IAC3B,YAAc;AAAA,IACd,sBAAsB;AAAA,IACtB,oBAAoB;AAAA,IACpB,WAAa;AAAA,IACb,2BAA2B;AAAA,EAC7B;AAAA,EACA,OAAS;AAAA,IACP;AAAA,IACA;AAAA,EACF;AAAA,EACA,eAAiB;AAAA,IACf,QAAU;AAAA,EACZ;AACF;", "names": []}