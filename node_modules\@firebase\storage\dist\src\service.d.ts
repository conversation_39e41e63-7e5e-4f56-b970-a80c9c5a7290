/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { Location } from './implementation/location';
import { Request } from './implementation/request';
import { RequestInfo } from './implementation/requestinfo';
import { Reference } from './reference';
import { Provider } from '@firebase/component';
import { FirebaseAuthInternalName } from '@firebase/auth-interop-types';
import { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';
import { FirebaseApp } from '@firebase/app';
import { FirebaseStorage } from './public-types';
import { EmulatorMockTokenOptions } from '@firebase/util';
import { Connection, ConnectionType } from './implementation/connection';
export declare function isUrl(path?: string): boolean;
/**
 * Returns a storage Reference for the given url.
 * @param storage - `Storage` instance.
 * @param url - URL. If empty, returns root reference.
 * @public
 */
export declare function ref(storage: FirebaseStorageImpl, url?: string): Reference;
/**
 * Returns a storage Reference for the given path in the
 * default bucket.
 * @param storageOrRef - `Storage` service or storage `Reference`.
 * @param pathOrUrlStorage - path. If empty, returns root reference (if Storage
 * instance provided) or returns same reference (if Reference provided).
 * @public
 */
export declare function ref(storageOrRef: FirebaseStorageImpl | Reference, path?: string): Reference;
export declare function connectStorageEmulator(storage: FirebaseStorageImpl, host: string, port: number, options?: {
    mockUserToken?: EmulatorMockTokenOptions | string;
}): void;
/**
 * A service that provides Firebase Storage Reference instances.
 * @param opt_url - gs:// url to a custom Storage Bucket
 *
 * @internal
 */
export declare class FirebaseStorageImpl implements FirebaseStorage {
    /**
     * FirebaseApp associated with this StorageService instance.
     */
    readonly app: FirebaseApp;
    readonly _authProvider: Provider<FirebaseAuthInternalName>;
    /**
     * @internal
     */
    readonly _appCheckProvider: Provider<AppCheckInternalComponentName>;
    /**
     * @internal
     */
    readonly _url?: string | undefined;
    readonly _firebaseVersion?: string | undefined;
    _bucket: Location | null;
    /**
     * This string can be in the formats:
     * - host
     * - host:port
     */
    private _host;
    _protocol: string;
    protected readonly _appId: string | null;
    private readonly _requests;
    private _deleted;
    private _maxOperationRetryTime;
    private _maxUploadRetryTime;
    _overrideAuthToken?: string;
    constructor(
    /**
     * FirebaseApp associated with this StorageService instance.
     */
    app: FirebaseApp, _authProvider: Provider<FirebaseAuthInternalName>, 
    /**
     * @internal
     */
    _appCheckProvider: Provider<AppCheckInternalComponentName>, 
    /**
     * @internal
     */
    _url?: string | undefined, _firebaseVersion?: string | undefined);
    /**
     * The host string for this service, in the form of `host` or
     * `host:port`.
     */
    get host(): string;
    set host(host: string);
    /**
     * The maximum time to retry uploads in milliseconds.
     */
    get maxUploadRetryTime(): number;
    set maxUploadRetryTime(time: number);
    /**
     * The maximum time to retry operations other than uploads or downloads in
     * milliseconds.
     */
    get maxOperationRetryTime(): number;
    set maxOperationRetryTime(time: number);
    _getAuthToken(): Promise<string | null>;
    _getAppCheckToken(): Promise<string | null>;
    /**
     * Stop running requests and prevent more from being created.
     */
    _delete(): Promise<void>;
    /**
     * Returns a new firebaseStorage.Reference object referencing this StorageService
     * at the given Location.
     */
    _makeStorageReference(loc: Location): Reference;
    /**
     * @param requestInfo - HTTP RequestInfo object
     * @param authToken - Firebase auth token
     */
    _makeRequest<I extends ConnectionType, O>(requestInfo: RequestInfo<I, O>, requestFactory: () => Connection<I>, authToken: string | null, appCheckToken: string | null, retry?: boolean): Request<O>;
    makeRequestWithTokens<I extends ConnectionType, O>(requestInfo: RequestInfo<I, O>, requestFactory: () => Connection<I>): Promise<O>;
}
