// src/firebase/config.ts
import { initializeApp } from "firebase/app";
import { getAuth, GoogleAuthProvider } from "firebase/auth";
import { getFirestore } from "firebase/firestore";

const firebaseConfig = {
  apiKey: "AIzaSyA6srvJjDHs8mKcp9ybTSvn3ZLhR4WMtuU",
  authDomain: "umldiagrame.firebaseapp.com",
  projectId: "umldiagrame",
  storageBucket: "umldiagrame.firebasestorage.app",
  messagingSenderId: "1033760429224",
  appId: "1:1033760429224:web:cd732f0451cf932ea9de9d",
  measurementId: "G-K12069CFEX"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export const googleProvider = new GoogleAuthProvider();
export const db = getFirestore(app);

// Default export
export default app;