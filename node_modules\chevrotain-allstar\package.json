{"name": "chevrotain-allstar", "version": "0.3.1", "description": "LL(*) lookahead strategy for the Chevrotain parser library", "keywords": ["parser", "<PERSON><PERSON><PERSON>", "chevrotain", "langium"], "license": "MIT", "type": "module", "types": "./lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}}, "scripts": {"test": "vitest", "build": "tsc", "watch": "tsc -w", "clean": "shx rm -rf lib tsconfig.tsbuildinfo"}, "files": ["src", "lib"], "dependencies": {"lodash-es": "^4.17.21"}, "peerDependencies": {"chevrotain": "^11.0.0"}, "devDependencies": {"@types/jest": "^29.2.1", "@types/lodash-es": "^4.17.8", "shx": "^0.3.4", "typescript": "^5.1.6", "vitest": "^0.33.0"}, "repository": {"type": "git", "url": "https://github.com/langium/chevrotain-allstar"}, "bugs": "https://github.com/langium/chevrotain-allstar/issues", "author": {"name": "TypeFox", "url": "https://www.typefox.io"}}