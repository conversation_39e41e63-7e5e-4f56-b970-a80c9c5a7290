## Create Issue for new release

- Create a file with name <i>YYYY-MM-DD-VERSION-release.md</i> in the [_posts](https://github.com/cytoscape/cytoscape.js-blog/tree/gh-pages/_posts) directory.

- Include these main points in the posts
  - layout
  - title
  - subtitle
  - tags
  - New important features and who contributed to them.
  - Full list of changes.
  
- Sample blog post:

```
---
layout: post
title: Cytoscape.js $VERSION released
subtitle: An overview of what's new in Cytoscape.js $VERSION
tags:
- news
---

Cytoscape.js $VERSION has been released.  This version brings with it a [new right-slanting rhomboid shape](https://github.com/cytoscape/cytoscape.js/issues/3123) by [@somaniv](https://github.com/somaniv) and [trusted-types support](https://github.com/cytoscape/cytoscape.js/pull/3118) by [<PERSON><PERSON><PERSON>](https://github.com/vrana).

The full list of changes can be found in the [3.25.0 milestone on GitHub](https://github.com/cytoscape/cytoscape.js/milestone/236?closed=1).
```
