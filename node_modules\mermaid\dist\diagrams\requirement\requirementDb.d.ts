import type { DiagramDB } from '../../diagram-api/types.js';
import type { Node, Edge } from '../../rendering-util/types.js';
import type { Element, Relation, RelationshipType, Requirement, RequirementClass, RequirementType, RiskLevel, VerifyType } from './types.js';
export declare class RequirementDB implements DiagramDB {
    private relations;
    private latestRequirement;
    private requirements;
    private latestElement;
    private elements;
    private classes;
    private direction;
    private RequirementType;
    private RiskLevel;
    private VerifyType;
    private Relationships;
    constructor();
    getDirection(): string;
    setDirection(dir: string): void;
    private resetLatestRequirement;
    private resetLatestElement;
    private getInitialRequirement;
    private getInitialElement;
    addRequirement(name: string, type: RequirementType): Requirement | undefined;
    getRequirements(): Map<string, Requirement>;
    setNewReqId(id: string): void;
    setNewReqText(text: string): void;
    setNewReqRisk(risk: RiskLevel): void;
    setNewReqVerifyMethod(verifyMethod: VerifyType): void;
    addElement(name: string): Element | undefined;
    getElements(): Map<string, Element>;
    setNewElementType(type: string): void;
    setNewElementDocRef(docRef: string): void;
    addRelationship(type: RelationshipType, src: string, dst: string): void;
    getRelationships(): Relation[];
    clear(): void;
    setCssStyle(ids: string[], styles: string[]): void;
    setClass(ids: string[], classNames: string[]): void;
    defineClass(ids: string[], style: string[]): void;
    getClasses(): Map<string, RequirementClass>;
    getData(): {
        nodes: Node[];
        edges: Edge[];
        other: {};
        config: import("../../config.type.js").MermaidConfig;
        direction: string;
    };
    setAccTitle: (txt: string) => void;
    getAccTitle: () => string;
    setAccDescription: (txt: string) => void;
    getAccDescription: () => string;
    setDiagramTitle: (txt: string) => void;
    getDiagramTitle: () => string;
    getConfig: () => import("../../config.type.js").RequirementDiagramConfig | undefined;
}
