{"version": 3, "file": "firebase-app.js", "sources": ["../util/dist/postinstall.mjs", "../util/src/crypt.ts", "../util/src/global.ts", "../util/src/defaults.ts", "../util/src/deferred.ts", "../util/src/environment.ts", "../util/src/errors.ts", "../util/src/obj.ts", "../component/src/component.ts", "../component/src/constants.ts", "../component/src/provider.ts", "../component/src/component_container.ts", "../logger/src/logger.ts", "../../node_modules/idb/build/wrap-idb-value.js", "../../node_modules/idb/build/index.js", "../app/src/platformLoggerService.ts", "../app/src/logger.ts", "../app/src/constants.ts", "../app/src/internal.ts", "../app/src/errors.ts", "../app/src/firebaseApp.ts", "../app/src/firebaseServerApp.ts", "../app/src/api.ts", "../app/src/indexeddb.ts", "../app/src/heartbeatService.ts", "../app/src/registerCoreComponents.ts", "../app/src/index.ts", "app/index.cdn.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// This value is retrieved and hardcoded by the NPM postinstall script\nconst getDefaultsFromPostinstall = () => undefined;\n\nexport { getDefaultsFromPostinstall };\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst stringToByteArray = function (str: string): number[] {\n  // TODO(user): Use native implementations if/when available\n  const out: number[] = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = (c >> 6) | 192;\n      out[p++] = (c & 63) | 128;\n    } else if (\n      (c & 0xfc00) === 0xd800 &&\n      i + 1 < str.length &&\n      (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00\n    ) {\n      // Surrogate Pair\n      c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);\n      out[p++] = (c >> 18) | 240;\n      out[p++] = ((c >> 12) & 63) | 128;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    } else {\n      out[p++] = (c >> 12) | 224;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    }\n  }\n  return out;\n};\n\n/**\n * Turns an array of numbers into the string given by the concatenation of the\n * characters to which the numbers correspond.\n * @param bytes Array of numbers representing characters.\n * @return Stringification of the array.\n */\nconst byteArrayToString = function (bytes: number[]): string {\n  // TODO(user): Use native implementations if/when available\n  const out: string[] = [];\n  let pos = 0,\n    c = 0;\n  while (pos < bytes.length) {\n    const c1 = bytes[pos++];\n    if (c1 < 128) {\n      out[c++] = String.fromCharCode(c1);\n    } else if (c1 > 191 && c1 < 224) {\n      const c2 = bytes[pos++];\n      out[c++] = String.fromCharCode(((c1 & 31) << 6) | (c2 & 63));\n    } else if (c1 > 239 && c1 < 365) {\n      // Surrogate Pair\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      const c4 = bytes[pos++];\n      const u =\n        (((c1 & 7) << 18) | ((c2 & 63) << 12) | ((c3 & 63) << 6) | (c4 & 63)) -\n        0x10000;\n      out[c++] = String.fromCharCode(0xd800 + (u >> 10));\n      out[c++] = String.fromCharCode(0xdc00 + (u & 1023));\n    } else {\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      out[c++] = String.fromCharCode(\n        ((c1 & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)\n      );\n    }\n  }\n  return out.join('');\n};\n\ninterface Base64 {\n  byteToCharMap_: { [key: number]: string } | null;\n  charToByteMap_: { [key: string]: number } | null;\n  byteToCharMapWebSafe_: { [key: number]: string } | null;\n  charToByteMapWebSafe_: { [key: string]: number } | null;\n  ENCODED_VALS_BASE: string;\n  readonly ENCODED_VALS: string;\n  readonly ENCODED_VALS_WEBSAFE: string;\n  HAS_NATIVE_SUPPORT: boolean;\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string;\n  encodeString(input: string, webSafe?: boolean): string;\n  decodeString(input: string, webSafe: boolean): string;\n  decodeStringToByteArray(input: string, webSafe: boolean): number[];\n  init_(): void;\n}\n\n// We define it as an object literal instead of a class because a class compiled down to es5 can't\n// be treeshaked. https://github.com/rollup/rollup/issues/1691\n// Static lookup maps, lazily populated by init_()\n// TODO(dlarocque): Define this as a class, since we no longer target ES5.\nexport const base64: Base64 = {\n  /**\n   * Maps bytes to characters.\n   */\n  byteToCharMap_: null,\n\n  /**\n   * Maps characters to bytes.\n   */\n  charToByteMap_: null,\n\n  /**\n   * Maps bytes to websafe characters.\n   * @private\n   */\n  byteToCharMapWebSafe_: null,\n\n  /**\n   * Maps websafe characters to bytes.\n   * @private\n   */\n  charToByteMapWebSafe_: null,\n\n  /**\n   * Our default alphabet, shared between\n   * ENCODED_VALS and ENCODED_VALS_WEBSAFE\n   */\n  ENCODED_VALS_BASE:\n    'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz' + '0123456789',\n\n  /**\n   * Our default alphabet. Value 64 (=) is special; it means \"nothing.\"\n   */\n  get ENCODED_VALS() {\n    return this.ENCODED_VALS_BASE + '+/=';\n  },\n\n  /**\n   * Our websafe alphabet.\n   */\n  get ENCODED_VALS_WEBSAFE() {\n    return this.ENCODED_VALS_BASE + '-_.';\n  },\n\n  /**\n   * Whether this browser supports the atob and btoa functions. This extension\n   * started at Mozilla but is now implemented by many browsers. We use the\n   * ASSUME_* variables to avoid pulling in the full useragent detection library\n   * but still allowing the standard per-browser compilations.\n   *\n   */\n  HAS_NATIVE_SUPPORT: typeof atob === 'function',\n\n  /**\n   * Base64-encode an array of bytes.\n   *\n   * @param input An array of bytes (numbers with\n   *     value in [0, 255]) to encode.\n   * @param webSafe Boolean indicating we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string {\n    if (!Array.isArray(input)) {\n      throw Error('encodeByteArray takes an array as a parameter');\n    }\n\n    this.init_();\n\n    const byteToCharMap = webSafe\n      ? this.byteToCharMapWebSafe_!\n      : this.byteToCharMap_!;\n\n    const output = [];\n\n    for (let i = 0; i < input.length; i += 3) {\n      const byte1 = input[i];\n      const haveByte2 = i + 1 < input.length;\n      const byte2 = haveByte2 ? input[i + 1] : 0;\n      const haveByte3 = i + 2 < input.length;\n      const byte3 = haveByte3 ? input[i + 2] : 0;\n\n      const outByte1 = byte1 >> 2;\n      const outByte2 = ((byte1 & 0x03) << 4) | (byte2 >> 4);\n      let outByte3 = ((byte2 & 0x0f) << 2) | (byte3 >> 6);\n      let outByte4 = byte3 & 0x3f;\n\n      if (!haveByte3) {\n        outByte4 = 64;\n\n        if (!haveByte2) {\n          outByte3 = 64;\n        }\n      }\n\n      output.push(\n        byteToCharMap[outByte1],\n        byteToCharMap[outByte2],\n        byteToCharMap[outByte3],\n        byteToCharMap[outByte4]\n      );\n    }\n\n    return output.join('');\n  },\n\n  /**\n   * Base64-encode a string.\n   *\n   * @param input A string to encode.\n   * @param webSafe If true, we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeString(input: string, webSafe?: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return btoa(input);\n    }\n    return this.encodeByteArray(stringToByteArray(input), webSafe);\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * @param input to decode.\n   * @param webSafe True if we should use the\n   *     alternative alphabet.\n   * @return string representing the decoded value.\n   */\n  decodeString(input: string, webSafe: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return atob(input);\n    }\n    return byteArrayToString(this.decodeStringToByteArray(input, webSafe));\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * In base-64 decoding, groups of four characters are converted into three\n   * bytes.  If the encoder did not apply padding, the input length may not\n   * be a multiple of 4.\n   *\n   * In this case, the last group will have fewer than 4 characters, and\n   * padding will be inferred.  If the group has one or two characters, it decodes\n   * to one byte.  If the group has three characters, it decodes to two bytes.\n   *\n   * @param input Input to decode.\n   * @param webSafe True if we should use the web-safe alphabet.\n   * @return bytes representing the decoded value.\n   */\n  decodeStringToByteArray(input: string, webSafe: boolean): number[] {\n    this.init_();\n\n    const charToByteMap = webSafe\n      ? this.charToByteMapWebSafe_!\n      : this.charToByteMap_!;\n\n    const output: number[] = [];\n\n    for (let i = 0; i < input.length; ) {\n      const byte1 = charToByteMap[input.charAt(i++)];\n\n      const haveByte2 = i < input.length;\n      const byte2 = haveByte2 ? charToByteMap[input.charAt(i)] : 0;\n      ++i;\n\n      const haveByte3 = i < input.length;\n      const byte3 = haveByte3 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      const haveByte4 = i < input.length;\n      const byte4 = haveByte4 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      if (byte1 == null || byte2 == null || byte3 == null || byte4 == null) {\n        throw new DecodeBase64StringError();\n      }\n\n      const outByte1 = (byte1 << 2) | (byte2 >> 4);\n      output.push(outByte1);\n\n      if (byte3 !== 64) {\n        const outByte2 = ((byte2 << 4) & 0xf0) | (byte3 >> 2);\n        output.push(outByte2);\n\n        if (byte4 !== 64) {\n          const outByte3 = ((byte3 << 6) & 0xc0) | byte4;\n          output.push(outByte3);\n        }\n      }\n    }\n\n    return output;\n  },\n\n  /**\n   * Lazy static initialization function. Called before\n   * accessing any of the static map variables.\n   * @private\n   */\n  init_() {\n    if (!this.byteToCharMap_) {\n      this.byteToCharMap_ = {};\n      this.charToByteMap_ = {};\n      this.byteToCharMapWebSafe_ = {};\n      this.charToByteMapWebSafe_ = {};\n\n      // We want quick mappings back and forth, so we precompute two maps.\n      for (let i = 0; i < this.ENCODED_VALS.length; i++) {\n        this.byteToCharMap_[i] = this.ENCODED_VALS.charAt(i);\n        this.charToByteMap_[this.byteToCharMap_[i]] = i;\n        this.byteToCharMapWebSafe_[i] = this.ENCODED_VALS_WEBSAFE.charAt(i);\n        this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[i]] = i;\n\n        // Be forgiving when decoding and correctly decode both encodings.\n        if (i >= this.ENCODED_VALS_BASE.length) {\n          this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(i)] = i;\n          this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(i)] = i;\n        }\n      }\n    }\n  }\n};\n\n/**\n * An error encountered while decoding base64 string.\n */\nexport class DecodeBase64StringError extends Error {\n  readonly name = 'DecodeBase64StringError';\n}\n\n/**\n * URL-safe base64 encoding\n */\nexport const base64Encode = function (str: string): string {\n  const utf8Bytes = stringToByteArray(str);\n  return base64.encodeByteArray(utf8Bytes, true);\n};\n\n/**\n * URL-safe base64 encoding (without \".\" padding in the end).\n * e.g. Used in JSON Web Token (JWT) parts.\n */\nexport const base64urlEncodeWithoutPadding = function (str: string): string {\n  // Use base64url encoding and remove padding in the end (dot characters).\n  return base64Encode(str).replace(/\\./g, '');\n};\n\n/**\n * URL-safe base64 decoding\n *\n * NOTE: DO NOT use the global atob() function - it does NOT support the\n * base64Url variant encoding.\n *\n * @param str To be decoded\n * @return Decoded result, if possible\n */\nexport const base64Decode = function (str: string): string | null {\n  try {\n    return base64.decodeString(str, true);\n  } catch (e) {\n    console.error('base64Decode failed: ', e);\n  }\n  return null;\n};\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Polyfill for `globalThis` object.\n * @returns the `globalThis` object for the given environment.\n * @public\n */\nexport function getGlobal(): typeof globalThis {\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n  throw new Error('Unable to locate global object.');\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64Decode } from './crypt';\nimport { getGlobal } from './global';\nimport { getDefaultsFromPostinstall } from './postinstall';\n\n/**\n * Keys for experimental properties on the `FirebaseDefaults` object.\n * @public\n */\nexport type ExperimentalKey = 'authTokenSyncURL' | 'authIdTokenMaxAge';\n\n/**\n * An object that can be injected into the environment as __FIREBASE_DEFAULTS__,\n * either as a property of globalThis, a shell environment variable, or a\n * cookie.\n *\n * This object can be used to automatically configure and initialize\n * a Firebase app as well as any emulators.\n *\n * @public\n */\nexport interface FirebaseDefaults {\n  config?: Record<string, string>;\n  emulatorHosts?: Record<string, string>;\n  _authTokenSyncURL?: string;\n  _authIdTokenMaxAge?: number;\n  /**\n   * Override Firebase's runtime environment detection and\n   * force the SDK to act as if it were in the specified environment.\n   */\n  forceEnvironment?: 'browser' | 'node';\n  [key: string]: unknown;\n}\n\ndeclare global {\n  // Need `var` for this to work.\n  // eslint-disable-next-line no-var\n  var __FIREBASE_DEFAULTS__: FirebaseDefaults | undefined;\n}\n\nconst getDefaultsFromGlobal = (): FirebaseDefaults | undefined =>\n  getGlobal().__FIREBASE_DEFAULTS__;\n\n/**\n * Attempt to read defaults from a JSON string provided to\n * process(.)env(.)__FIREBASE_DEFAULTS__ or a JSON file whose path is in\n * process(.)env(.)__FIREBASE_DEFAULTS_PATH__\n * The dots are in parens because certain compilers (Vite?) cannot\n * handle seeing that variable in comments.\n * See https://github.com/firebase/firebase-js-sdk/issues/6838\n */\nconst getDefaultsFromEnvVariable = (): FirebaseDefaults | undefined => {\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return;\n  }\n  const defaultsJsonString = process.env.__FIREBASE_DEFAULTS__;\n  if (defaultsJsonString) {\n    return JSON.parse(defaultsJsonString);\n  }\n};\n\nconst getDefaultsFromCookie = (): FirebaseDefaults | undefined => {\n  if (typeof document === 'undefined') {\n    return;\n  }\n  let match;\n  try {\n    match = document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/);\n  } catch (e) {\n    // Some environments such as Angular Universal SSR have a\n    // `document` object but error on accessing `document.cookie`.\n    return;\n  }\n  const decoded = match && base64Decode(match[1]);\n  return decoded && JSON.parse(decoded);\n};\n\n/**\n * Get the __FIREBASE_DEFAULTS__ object. It checks in order:\n * (1) if such an object exists as a property of `globalThis`\n * (2) if such an object was provided on a shell environment variable\n * (3) if such an object exists in a cookie\n * @public\n */\nexport const getDefaults = (): FirebaseDefaults | undefined => {\n  try {\n    return (\n      getDefaultsFromPostinstall() ||\n      getDefaultsFromGlobal() ||\n      getDefaultsFromEnvVariable() ||\n      getDefaultsFromCookie()\n    );\n  } catch (e) {\n    /**\n     * Catch-all for being unable to get __FIREBASE_DEFAULTS__ due\n     * to any environment case we have not accounted for. Log to\n     * info instead of swallowing so we can find these unknown cases\n     * and add paths for them if needed.\n     */\n    console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);\n    return;\n  }\n};\n\n/**\n * Returns emulator host stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a URL host formatted like `127.0.0.1:9999` or `[::1]:4000` if available\n * @public\n */\nexport const getDefaultEmulatorHost = (\n  productName: string\n): string | undefined => getDefaults()?.emulatorHosts?.[productName];\n\n/**\n * Returns emulator hostname and port stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a pair of hostname and port like `[\"::1\", 4000]` if available\n * @public\n */\nexport const getDefaultEmulatorHostnameAndPort = (\n  productName: string\n): [hostname: string, port: number] | undefined => {\n  const host = getDefaultEmulatorHost(productName);\n  if (!host) {\n    return undefined;\n  }\n  const separatorIndex = host.lastIndexOf(':'); // Finding the last since IPv6 addr also has colons.\n  if (separatorIndex <= 0 || separatorIndex + 1 === host.length) {\n    throw new Error(`Invalid host ${host} with no separate hostname and port!`);\n  }\n  // eslint-disable-next-line no-restricted-globals\n  const port = parseInt(host.substring(separatorIndex + 1), 10);\n  if (host[0] === '[') {\n    // Bracket-quoted `[ipv6addr]:port` => return \"ipv6addr\" (without brackets).\n    return [host.substring(1, separatorIndex - 1), port];\n  } else {\n    return [host.substring(0, separatorIndex), port];\n  }\n};\n\n/**\n * Returns Firebase app config stored in the __FIREBASE_DEFAULTS__ object.\n * @public\n */\nexport const getDefaultAppConfig = (): Record<string, string> | undefined =>\n  getDefaults()?.config;\n\n/**\n * Returns an experimental setting on the __FIREBASE_DEFAULTS__ object (properties\n * prefixed by \"_\")\n * @public\n */\nexport const getExperimentalSetting = <T extends ExperimentalKey>(\n  name: T\n): FirebaseDefaults[`_${T}`] =>\n  getDefaults()?.[`_${name}`] as FirebaseDefaults[`_${T}`];\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport class Deferred<R> {\n  promise: Promise<R>;\n  reject: (value?: unknown) => void = () => {};\n  resolve: (value?: unknown) => void = () => {};\n  constructor() {\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = resolve as (value?: unknown) => void;\n      this.reject = reject as (value?: unknown) => void;\n    });\n  }\n\n  /**\n   * Our API internals are not promisified and cannot because our callback APIs have subtle expectations around\n   * invoking promises inline, which Promises are forbidden to do. This method accepts an optional node-style callback\n   * and returns a node-style callback which will resolve or reject the Deferred's promise.\n   */\n  wrapCallback(\n    callback?: (error?: unknown, value?: unknown) => void\n  ): (error: unknown, value?: unknown) => void {\n    return (error, value?) => {\n      if (error) {\n        this.reject(error);\n      } else {\n        this.resolve(value);\n      }\n      if (typeof callback === 'function') {\n        // Attaching noop handler just in case developer wasn't expecting\n        // promises\n        this.promise.catch(() => {});\n\n        // Some of our callbacks don't expect a value and our own tests\n        // assert that the parameter length is 1\n        if (callback.length === 1) {\n          callback(error);\n        } else {\n          callback(error, value);\n        }\n      }\n    };\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSTANTS } from './constants';\nimport { getDefaults } from './defaults';\n\n/**\n * Type placeholder for `WorkerGlobalScope` from `webworker`\n */\ndeclare class WorkerGlobalScope {}\n\n/**\n * Returns navigator.userAgent string or '' if it's not defined.\n * @return user agent string\n */\nexport function getUA(): string {\n  if (\n    typeof navigator !== 'undefined' &&\n    typeof navigator['userAgent'] === 'string'\n  ) {\n    return navigator['userAgent'];\n  } else {\n    return '';\n  }\n}\n\n/**\n * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.\n *\n * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap\n * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally\n * wait for a callback.\n */\nexport function isMobileCordova(): boolean {\n  return (\n    typeof window !== 'undefined' &&\n    // @ts-ignore Setting up an broadly applicable index signature for Window\n    // just to deal with this case would probably be a bad idea.\n    !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) &&\n    /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA())\n  );\n}\n\n/**\n * Detect Node.js.\n *\n * @return true if Node.js environment is detected or specified.\n */\n// Node detection logic from: https://github.com/iliakan/detect-node/\nexport function isNode(): boolean {\n  const forceEnvironment = getDefaults()?.forceEnvironment;\n  if (forceEnvironment === 'node') {\n    return true;\n  } else if (forceEnvironment === 'browser') {\n    return false;\n  }\n\n  try {\n    return (\n      Object.prototype.toString.call(global.process) === '[object process]'\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Detect Browser Environment.\n * Note: This will return true for certain test frameworks that are incompletely\n * mimicking a browser, and should not lead to assuming all browser APIs are\n * available.\n */\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined' || isWebWorker();\n}\n\n/**\n * Detect Web Worker context.\n */\nexport function isWebWorker(): boolean {\n  return (\n    typeof WorkerGlobalScope !== 'undefined' &&\n    typeof self !== 'undefined' &&\n    self instanceof WorkerGlobalScope\n  );\n}\n\n/**\n * Detect Cloudflare Worker context.\n */\nexport function isCloudflareWorker(): boolean {\n  return (\n    typeof navigator !== 'undefined' &&\n    navigator.userAgent === 'Cloudflare-Workers'\n  );\n}\n\n/**\n * Detect browser extensions (Chrome and Firefox at least).\n */\ninterface BrowserRuntime {\n  id?: unknown;\n}\ndeclare const chrome: { runtime?: BrowserRuntime };\ndeclare const browser: { runtime?: BrowserRuntime };\nexport function isBrowserExtension(): boolean {\n  const runtime =\n    typeof chrome === 'object'\n      ? chrome.runtime\n      : typeof browser === 'object'\n      ? browser.runtime\n      : undefined;\n  return typeof runtime === 'object' && runtime.id !== undefined;\n}\n\n/**\n * Detect React Native.\n *\n * @return true if ReactNative environment is detected.\n */\nexport function isReactNative(): boolean {\n  return (\n    typeof navigator === 'object' && navigator['product'] === 'ReactNative'\n  );\n}\n\n/** Detects Electron apps. */\nexport function isElectron(): boolean {\n  return getUA().indexOf('Electron/') >= 0;\n}\n\n/** Detects Internet Explorer. */\nexport function isIE(): boolean {\n  const ua = getUA();\n  return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\n}\n\n/** Detects Universal Windows Platform apps. */\nexport function isUWP(): boolean {\n  return getUA().indexOf('MSAppHost/') >= 0;\n}\n\n/**\n * Detect whether the current SDK build is the Node version.\n *\n * @return true if it's the Node SDK build.\n */\nexport function isNodeSdk(): boolean {\n  return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;\n}\n\n/** Returns true if we are running in Safari. */\nexport function isSafari(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    navigator.userAgent.includes('Safari') &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/**\n * This method checks if indexedDB is supported by current browser/service worker context\n * @return true if indexedDB is supported by current browser/service worker context\n */\nexport function isIndexedDBAvailable(): boolean {\n  try {\n    return typeof indexedDB === 'object';\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject\n * if errors occur during the database open operation.\n *\n * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox\n * private browsing)\n */\nexport function validateIndexedDBOpenable(): Promise<boolean> {\n  return new Promise((resolve, reject) => {\n    try {\n      let preExist: boolean = true;\n      const DB_CHECK_NAME =\n        'validate-browser-context-for-indexeddb-analytics-module';\n      const request = self.indexedDB.open(DB_CHECK_NAME);\n      request.onsuccess = () => {\n        request.result.close();\n        // delete database only when it doesn't pre-exist\n        if (!preExist) {\n          self.indexedDB.deleteDatabase(DB_CHECK_NAME);\n        }\n        resolve(true);\n      };\n      request.onupgradeneeded = () => {\n        preExist = false;\n      };\n\n      request.onerror = () => {\n        reject(request.error?.message || '');\n      };\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n\n/**\n *\n * This method checks whether cookie is enabled within current browser\n * @return true if cookie is enabled within current browser\n */\nexport function areCookiesEnabled(): boolean {\n  if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {\n    return false;\n  }\n  return true;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function contains<T extends object>(obj: T, key: string): boolean {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\nexport function safeGet<T extends object, K extends keyof T>(\n  obj: T,\n  key: K\n): T[K] | undefined {\n  if (Object.prototype.hasOwnProperty.call(obj, key)) {\n    return obj[key];\n  } else {\n    return undefined;\n  }\n}\n\nexport function isEmpty(obj: object): obj is {} {\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport function map<K extends string, V, U>(\n  obj: { [key in K]: V },\n  fn: (value: V, key: K, obj: { [key in K]: V }) => U,\n  contextObj?: unknown\n): { [key in K]: U } {\n  const res: Partial<{ [key in K]: U }> = {};\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      res[key] = fn.call(contextObj, obj[key], key, obj);\n    }\n  }\n  return res as { [key in K]: U };\n}\n\n/**\n * Deep equal two objects. Support Arrays and Objects.\n */\nexport function deepEqual(a: object, b: object): boolean {\n  if (a === b) {\n    return true;\n  }\n\n  const aKeys = Object.keys(a);\n  const bKeys = Object.keys(b);\n  for (const k of aKeys) {\n    if (!bKeys.includes(k)) {\n      return false;\n    }\n\n    const aProp = (a as Record<string, unknown>)[k];\n    const bProp = (b as Record<string, unknown>)[k];\n    if (isObject(aProp) && isObject(bProp)) {\n      if (!deepEqual(aProp, bProp)) {\n        return false;\n      }\n    } else if (aProp !== bProp) {\n      return false;\n    }\n  }\n\n  for (const k of bKeys) {\n    if (!aKeys.includes(k)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction isObject(thing: unknown): thing is object {\n  return thing !== null && typeof thing === 'object';\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const DEFAULT_ENTRY_NAME = '[DEFAULT]';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Deferred } from '@firebase/util';\nimport { ComponentContainer } from './component_container';\nimport { DEFAULT_ENTRY_NAME } from './constants';\nimport {\n  InitializeOptions,\n  InstantiationMode,\n  Name,\n  NameServiceMapping,\n  OnInitCallBack\n} from './types';\nimport { Component } from './component';\n\n/**\n * Provider for instance for service name T, e.g. 'auth', 'auth-internal'\n * NameServiceMapping[T] is an alias for the type of the instance\n */\nexport class Provider<T extends Name> {\n  private component: Component<T> | null = null;\n  private readonly instances: Map<string, NameServiceMapping[T]> = new Map();\n  private readonly instancesDeferred: Map<\n    string,\n    Deferred<NameServiceMapping[T]>\n  > = new Map();\n  private readonly instancesOptions: Map<string, Record<string, unknown>> =\n    new Map();\n  private onInitCallbacks: Map<string, Set<OnInitCallBack<T>>> = new Map();\n\n  constructor(\n    private readonly name: T,\n    private readonly container: ComponentContainer\n  ) {}\n\n  /**\n   * @param identifier A provider can provide multiple instances of a service\n   * if this.component.multipleInstances is true.\n   */\n  get(identifier?: string): Promise<NameServiceMapping[T]> {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n\n    if (!this.instancesDeferred.has(normalizedIdentifier)) {\n      const deferred = new Deferred<NameServiceMapping[T]>();\n      this.instancesDeferred.set(normalizedIdentifier, deferred);\n\n      if (\n        this.isInitialized(normalizedIdentifier) ||\n        this.shouldAutoInitialize()\n      ) {\n        // initialize the service if it can be auto-initialized\n        try {\n          const instance = this.getOrInitializeService({\n            instanceIdentifier: normalizedIdentifier\n          });\n          if (instance) {\n            deferred.resolve(instance);\n          }\n        } catch (e) {\n          // when the instance factory throws an exception during get(), it should not cause\n          // a fatal error. We just return the unresolved promise in this case.\n        }\n      }\n    }\n\n    return this.instancesDeferred.get(normalizedIdentifier)!.promise;\n  }\n\n  /**\n   *\n   * @param options.identifier A provider can provide multiple instances of a service\n   * if this.component.multipleInstances is true.\n   * @param options.optional If optional is false or not provided, the method throws an error when\n   * the service is not immediately available.\n   * If optional is true, the method returns null if the service is not immediately available.\n   */\n  getImmediate(options: {\n    identifier?: string;\n    optional: true;\n  }): NameServiceMapping[T] | null;\n  getImmediate(options?: {\n    identifier?: string;\n    optional?: false;\n  }): NameServiceMapping[T];\n  getImmediate(options?: {\n    identifier?: string;\n    optional?: boolean;\n  }): NameServiceMapping[T] | null {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(\n      options?.identifier\n    );\n    const optional = options?.optional ?? false;\n\n    if (\n      this.isInitialized(normalizedIdentifier) ||\n      this.shouldAutoInitialize()\n    ) {\n      try {\n        return this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        });\n      } catch (e) {\n        if (optional) {\n          return null;\n        } else {\n          throw e;\n        }\n      }\n    } else {\n      // In case a component is not initialized and should/cannot be auto-initialized at the moment, return null if the optional flag is set, or throw\n      if (optional) {\n        return null;\n      } else {\n        throw Error(`Service ${this.name} is not available`);\n      }\n    }\n  }\n\n  getComponent(): Component<T> | null {\n    return this.component;\n  }\n\n  setComponent(component: Component<T>): void {\n    if (component.name !== this.name) {\n      throw Error(\n        `Mismatching Component ${component.name} for Provider ${this.name}.`\n      );\n    }\n\n    if (this.component) {\n      throw Error(`Component for ${this.name} has already been provided`);\n    }\n\n    this.component = component;\n\n    // return early without attempting to initialize the component if the component requires explicit initialization (calling `Provider.initialize()`)\n    if (!this.shouldAutoInitialize()) {\n      return;\n    }\n\n    // if the service is eager, initialize the default instance\n    if (isComponentEager(component)) {\n      try {\n        this.getOrInitializeService({ instanceIdentifier: DEFAULT_ENTRY_NAME });\n      } catch (e) {\n        // when the instance factory for an eager Component throws an exception during the eager\n        // initialization, it should not cause a fatal error.\n        // TODO: Investigate if we need to make it configurable, because some component may want to cause\n        // a fatal error in this case?\n      }\n    }\n\n    // Create service instances for the pending promises and resolve them\n    // NOTE: if this.multipleInstances is false, only the default instance will be created\n    // and all promises with resolve with it regardless of the identifier.\n    for (const [\n      instanceIdentifier,\n      instanceDeferred\n    ] of this.instancesDeferred.entries()) {\n      const normalizedIdentifier =\n        this.normalizeInstanceIdentifier(instanceIdentifier);\n\n      try {\n        // `getOrInitializeService()` should always return a valid instance since a component is guaranteed. use ! to make typescript happy.\n        const instance = this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        })!;\n        instanceDeferred.resolve(instance);\n      } catch (e) {\n        // when the instance factory throws an exception, it should not cause\n        // a fatal error. We just leave the promise unresolved.\n      }\n    }\n  }\n\n  clearInstance(identifier: string = DEFAULT_ENTRY_NAME): void {\n    this.instancesDeferred.delete(identifier);\n    this.instancesOptions.delete(identifier);\n    this.instances.delete(identifier);\n  }\n\n  // app.delete() will call this method on every provider to delete the services\n  // TODO: should we mark the provider as deleted?\n  async delete(): Promise<void> {\n    const services = Array.from(this.instances.values());\n\n    await Promise.all([\n      ...services\n        .filter(service => 'INTERNAL' in service) // legacy services\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        .map(service => (service as any).INTERNAL!.delete()),\n      ...services\n        .filter(service => '_delete' in service) // modularized services\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        .map(service => (service as any)._delete())\n    ]);\n  }\n\n  isComponentSet(): boolean {\n    return this.component != null;\n  }\n\n  isInitialized(identifier: string = DEFAULT_ENTRY_NAME): boolean {\n    return this.instances.has(identifier);\n  }\n\n  getOptions(identifier: string = DEFAULT_ENTRY_NAME): Record<string, unknown> {\n    return this.instancesOptions.get(identifier) || {};\n  }\n\n  initialize(opts: InitializeOptions = {}): NameServiceMapping[T] {\n    const { options = {} } = opts;\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(\n      opts.instanceIdentifier\n    );\n    if (this.isInitialized(normalizedIdentifier)) {\n      throw Error(\n        `${this.name}(${normalizedIdentifier}) has already been initialized`\n      );\n    }\n\n    if (!this.isComponentSet()) {\n      throw Error(`Component ${this.name} has not been registered yet`);\n    }\n\n    const instance = this.getOrInitializeService({\n      instanceIdentifier: normalizedIdentifier,\n      options\n    })!;\n\n    // resolve any pending promise waiting for the service instance\n    for (const [\n      instanceIdentifier,\n      instanceDeferred\n    ] of this.instancesDeferred.entries()) {\n      const normalizedDeferredIdentifier =\n        this.normalizeInstanceIdentifier(instanceIdentifier);\n      if (normalizedIdentifier === normalizedDeferredIdentifier) {\n        instanceDeferred.resolve(instance);\n      }\n    }\n\n    return instance;\n  }\n\n  /**\n   *\n   * @param callback - a function that will be invoked  after the provider has been initialized by calling provider.initialize().\n   * The function is invoked SYNCHRONOUSLY, so it should not execute any longrunning tasks in order to not block the program.\n   *\n   * @param identifier An optional instance identifier\n   * @returns a function to unregister the callback\n   */\n  onInit(callback: OnInitCallBack<T>, identifier?: string): () => void {\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n    const existingCallbacks =\n      this.onInitCallbacks.get(normalizedIdentifier) ??\n      new Set<OnInitCallBack<T>>();\n    existingCallbacks.add(callback);\n    this.onInitCallbacks.set(normalizedIdentifier, existingCallbacks);\n\n    const existingInstance = this.instances.get(normalizedIdentifier);\n    if (existingInstance) {\n      callback(existingInstance, normalizedIdentifier);\n    }\n\n    return () => {\n      existingCallbacks.delete(callback);\n    };\n  }\n\n  /**\n   * Invoke onInit callbacks synchronously\n   * @param instance the service instance`\n   */\n  private invokeOnInitCallbacks(\n    instance: NameServiceMapping[T],\n    identifier: string\n  ): void {\n    const callbacks = this.onInitCallbacks.get(identifier);\n    if (!callbacks) {\n      return;\n    }\n    for (const callback of callbacks) {\n      try {\n        callback(instance, identifier);\n      } catch {\n        // ignore errors in the onInit callback\n      }\n    }\n  }\n\n  private getOrInitializeService({\n    instanceIdentifier,\n    options = {}\n  }: {\n    instanceIdentifier: string;\n    options?: Record<string, unknown>;\n  }): NameServiceMapping[T] | null {\n    let instance = this.instances.get(instanceIdentifier);\n    if (!instance && this.component) {\n      instance = this.component.instanceFactory(this.container, {\n        instanceIdentifier: normalizeIdentifierForFactory(instanceIdentifier),\n        options\n      });\n      this.instances.set(instanceIdentifier, instance!);\n      this.instancesOptions.set(instanceIdentifier, options);\n\n      /**\n       * Invoke onInit listeners.\n       * Note this.component.onInstanceCreated is different, which is used by the component creator,\n       * while onInit listeners are registered by consumers of the provider.\n       */\n      this.invokeOnInitCallbacks(instance!, instanceIdentifier);\n\n      /**\n       * Order is important\n       * onInstanceCreated() should be called after this.instances.set(instanceIdentifier, instance); which\n       * makes `isInitialized()` return true.\n       */\n      if (this.component.onInstanceCreated) {\n        try {\n          this.component.onInstanceCreated(\n            this.container,\n            instanceIdentifier,\n            instance!\n          );\n        } catch {\n          // ignore errors in the onInstanceCreatedCallback\n        }\n      }\n    }\n\n    return instance || null;\n  }\n\n  private normalizeInstanceIdentifier(\n    identifier: string = DEFAULT_ENTRY_NAME\n  ): string {\n    if (this.component) {\n      return this.component.multipleInstances ? identifier : DEFAULT_ENTRY_NAME;\n    } else {\n      return identifier; // assume multiple instances are supported before the component is provided.\n    }\n  }\n\n  private shouldAutoInitialize(): boolean {\n    return (\n      !!this.component &&\n      this.component.instantiationMode !== InstantiationMode.EXPLICIT\n    );\n  }\n}\n\n// undefined should be passed to the service factory for the default instance\nfunction normalizeIdentifierForFactory(identifier: string): string | undefined {\n  return identifier === DEFAULT_ENTRY_NAME ? undefined : identifier;\n}\n\nfunction isComponentEager<T extends Name>(component: Component<T>): boolean {\n  return component.instantiationMode === InstantiationMode.EAGER;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Provider } from './provider';\nimport { Component } from './component';\nimport { Name } from './types';\n\n/**\n * ComponentContainer that provides Providers for service name T, e.g. `auth`, `auth-internal`\n */\nexport class ComponentContainer {\n  private readonly providers = new Map<string, Provider<Name>>();\n\n  constructor(private readonly name: string) {}\n\n  /**\n   *\n   * @param component Component being added\n   * @param overwrite When a component with the same name has already been registered,\n   * if overwrite is true: overwrite the existing component with the new component and create a new\n   * provider with the new component. It can be useful in tests where you want to use different mocks\n   * for different tests.\n   * if overwrite is false: throw an exception\n   */\n  addComponent<T extends Name>(component: Component<T>): void {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      throw new Error(\n        `Component ${component.name} has already been registered with ${this.name}`\n      );\n    }\n\n    provider.setComponent(component);\n  }\n\n  addOrOverwriteComponent<T extends Name>(component: Component<T>): void {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      // delete the existing provider from the container, so we can register the new component\n      this.providers.delete(component.name);\n    }\n\n    this.addComponent(component);\n  }\n\n  /**\n   * getProvider provides a type safe interface where it can only be called with a field name\n   * present in NameServiceMapping interface.\n   *\n   * Firebase SDKs providing services should extend NameServiceMapping interface to register\n   * themselves.\n   */\n  getProvider<T extends Name>(name: T): Provider<T> {\n    if (this.providers.has(name)) {\n      return this.providers.get(name) as unknown as Provider<T>;\n    }\n\n    // create a Provider for a service that hasn't registered with Firebase\n    const provider = new Provider<T>(name, this);\n    this.providers.set(name, provider as unknown as Provider<Name>);\n\n    return provider as Provider<T>;\n  }\n\n  getProviders(): Array<Provider<Name>> {\n    return Array.from(this.providers.values());\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type LogLevelString =\n  | 'debug'\n  | 'verbose'\n  | 'info'\n  | 'warn'\n  | 'error'\n  | 'silent';\n\nexport interface LogOptions {\n  level: LogLevelString;\n}\n\nexport type LogCallback = (callbackParams: LogCallbackParams) => void;\n\nexport interface LogCallbackParams {\n  level: LogLevelString;\n  message: string;\n  args: unknown[];\n  type: string;\n}\n\n/**\n * A container for all of the Logger instances\n */\nexport const instances: Logger[] = [];\n\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nexport enum LogLevel {\n  DEBUG,\n  VERBOSE,\n  INFO,\n  WARN,\n  ERROR,\n  SILENT\n}\n\nconst levelStringToEnum: { [key in LogLevelString]: LogLevel } = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n\n/**\n * The default log level\n */\nconst defaultLogLevel: LogLevel = LogLevel.INFO;\n\n/**\n * We allow users the ability to pass their own log handler. We will pass the\n * type of log, the current log level, and any other arguments passed (i.e. the\n * messages that the user wants to log) to this function.\n */\nexport type LogHandler = (\n  loggerInstance: Logger,\n  logType: LogLevel,\n  ...args: unknown[]\n) => void;\n\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler: LogHandler = (instance, logType, ...args): void => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType as keyof typeof ConsoleMethod];\n  if (method) {\n    console[method as 'log' | 'info' | 'warn' | 'error'](\n      `[${now}]  ${instance.name}:`,\n      ...args\n    );\n  } else {\n    throw new Error(\n      `Attempted to log a message with an invalid logType (value: ${logType})`\n    );\n  }\n};\n\nexport class Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(public name: string) {\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n\n  /**\n   * The log level of the given Logger instance.\n   */\n  private _logLevel = defaultLogLevel;\n\n  get logLevel(): LogLevel {\n    return this._logLevel;\n  }\n\n  set logLevel(val: LogLevel) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val: LogLevel | LogLevelString): void {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n\n  /**\n   * The main (internal) log handler for the Logger instance.\n   * Can be set to a new function in internal package code but not by user.\n   */\n  private _logHandler: LogHandler = defaultLogHandler;\n  get logHandler(): LogHandler {\n    return this._logHandler;\n  }\n  set logHandler(val: LogHandler) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n\n  /**\n   * The optional, additional, user-defined log handler for the Logger instance.\n   */\n  private _userLogHandler: LogHandler | null = null;\n  get userLogHandler(): LogHandler | null {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val: LogHandler | null) {\n    this._userLogHandler = val;\n  }\n\n  /**\n   * The functions below are all based on the `console` interface\n   */\n\n  debug(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args: unknown[]): void {\n    this._userLogHandler &&\n      this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\n\nexport function setLogLevel(level: LogLevelString | LogLevel): void {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\n\nexport function setUserLogHandler(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  for (const instance of instances) {\n    let customLogLevel: LogLevel | null = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (\n        instance: Logger,\n        level: LogLevel,\n        ...args: unknown[]\n      ) => {\n        const message = args\n          .map(arg => {\n            if (arg == null) {\n              return null;\n            } else if (typeof arg === 'string') {\n              return arg;\n            } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n              return arg.toString();\n            } else if (arg instanceof Error) {\n              return arg.message;\n            } else {\n              try {\n                return JSON.stringify(arg);\n              } catch (ignored) {\n                return null;\n              }\n            }\n          })\n          .filter(arg => arg)\n          .join(' ');\n        if (level >= (customLogLevel ?? instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase() as LogLevelString,\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\n", "const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n", "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n        });\n    }\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event.newVersion, event));\n    }\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking) {\n            db.addEventListener('versionchange', (event) => blocking(event.oldVersion, event.newVersion, event));\n        }\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event));\n    }\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ComponentContainer,\n  ComponentType,\n  Provider,\n  Name\n} from '@firebase/component';\nimport { PlatformLoggerService, VersionService } from './types';\n\nexport class PlatformLoggerServiceImpl implements PlatformLoggerService {\n  constructor(private readonly container: ComponentContainer) {}\n  // In initial implementation, this will be called by installations on\n  // auth token refresh, and installations will send this string.\n  getPlatformInfoString(): string {\n    const providers = this.container.getProviders();\n    // Loop through providers and get library/version pairs from any that are\n    // version components.\n    return providers\n      .map(provider => {\n        if (isVersionServiceProvider(provider)) {\n          const service = provider.getImmediate() as VersionService;\n          return `${service.library}/${service.version}`;\n        } else {\n          return null;\n        }\n      })\n      .filter(logString => logString)\n      .join(' ');\n  }\n}\n/**\n *\n * @param provider check if this provider provides a VersionService\n *\n * NOTE: Using Provider<'app-version'> is a hack to indicate that the provider\n * provides VersionService. The provider is not necessarily a 'app-version'\n * provider.\n */\nfunction isVersionServiceProvider(provider: Provider<Name>): boolean {\n  const component = provider.getComponent();\n  return component?.type === ComponentType.VERSION;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nexport const logger = new Logger('@firebase/app');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { name as appName } from '../package.json';\nimport { name as appCompatName } from '../../app-compat/package.json';\nimport { name as analyticsCompatName } from '../../../packages/analytics-compat/package.json';\nimport { name as analyticsName } from '../../../packages/analytics/package.json';\nimport { name as appCheckCompatName } from '../../../packages/app-check-compat/package.json';\nimport { name as appCheckName } from '../../../packages/app-check/package.json';\nimport { name as authName } from '../../../packages/auth/package.json';\nimport { name as authCompatName } from '../../../packages/auth-compat/package.json';\nimport { name as databaseName } from '../../../packages/database/package.json';\nimport { name as dataconnectName } from '../../../packages/data-connect/package.json';\nimport { name as databaseCompatName } from '../../../packages/database-compat/package.json';\nimport { name as functionsName } from '../../../packages/functions/package.json';\nimport { name as functionsCompatName } from '../../../packages/functions-compat/package.json';\nimport { name as installationsName } from '../../../packages/installations/package.json';\nimport { name as installationsCompatName } from '../../../packages/installations-compat/package.json';\nimport { name as messagingName } from '../../../packages/messaging/package.json';\nimport { name as messagingCompatName } from '../../../packages/messaging-compat/package.json';\nimport { name as performanceName } from '../../../packages/performance/package.json';\nimport { name as performanceCompatName } from '../../../packages/performance-compat/package.json';\nimport { name as remoteConfigName } from '../../../packages/remote-config/package.json';\nimport { name as remoteConfigCompatName } from '../../../packages/remote-config-compat/package.json';\nimport { name as storageName } from '../../../packages/storage/package.json';\nimport { name as storageCompatName } from '../../../packages/storage-compat/package.json';\nimport { name as firestoreName } from '../../../packages/firestore/package.json';\nimport { name as vertexName } from '../../../packages/vertexai/package.json';\nimport { name as firestoreCompatName } from '../../../packages/firestore-compat/package.json';\nimport { name as packageName } from '../../../packages/firebase/package.json';\n\n/**\n * The default app name\n *\n * @internal\n */\nexport const DEFAULT_ENTRY_NAME = '[DEFAULT]';\n\nexport const PLATFORM_LOG_STRING = {\n  [appName]: 'fire-core',\n  [appCompatName]: 'fire-core-compat',\n  [analyticsName]: 'fire-analytics',\n  [analyticsCompatName]: 'fire-analytics-compat',\n  [appCheckName]: 'fire-app-check',\n  [appCheckCompatName]: 'fire-app-check-compat',\n  [authName]: 'fire-auth',\n  [authCompatName]: 'fire-auth-compat',\n  [databaseName]: 'fire-rtdb',\n  [dataconnectName]: 'fire-data-connect',\n  [databaseCompatName]: 'fire-rtdb-compat',\n  [functionsName]: 'fire-fn',\n  [functionsCompatName]: 'fire-fn-compat',\n  [installationsName]: 'fire-iid',\n  [installationsCompatName]: 'fire-iid-compat',\n  [messagingName]: 'fire-fcm',\n  [messagingCompatName]: 'fire-fcm-compat',\n  [performanceName]: 'fire-perf',\n  [performanceCompatName]: 'fire-perf-compat',\n  [remoteConfigName]: 'fire-rc',\n  [remoteConfigCompatName]: 'fire-rc-compat',\n  [storageName]: 'fire-gcs',\n  [storageCompatName]: 'fire-gcs-compat',\n  [firestoreName]: 'fire-fst',\n  [firestoreCompatName]: 'fire-fst-compat',\n  [vertexName]: 'fire-vertex',\n  'fire-js': 'fire-js', // Platform identifier for JS SDK.\n  [packageName]: 'fire-js-all'\n} as const;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseOptions,\n  FirebaseServerApp\n} from './public-types';\nimport { Component, Provider, Name } from '@firebase/component';\nimport { logger } from './logger';\nimport { DEFAULT_ENTRY_NAME } from './constants';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { FirebaseServerAppImpl } from './firebaseServerApp';\n\n/**\n * @internal\n */\nexport const _apps = new Map<string, FirebaseApp>();\n\n/**\n * @internal\n */\nexport const _serverApps = new Map<string, FirebaseServerApp>();\n\n/**\n * Registered components.\n *\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport const _components = new Map<string, Component<any>>();\n\n/**\n * @param component - the component being added to this app's container\n *\n * @internal\n */\nexport function _addComponent<T extends Name>(\n  app: FirebaseApp,\n  component: Component<T>\n): void {\n  try {\n    (app as FirebaseAppImpl).container.addComponent(component);\n  } catch (e) {\n    logger.debug(\n      `Component ${component.name} failed to register with FirebaseApp ${app.name}`,\n      e\n    );\n  }\n}\n\n/**\n *\n * @internal\n */\nexport function _addOrOverwriteComponent(\n  app: FirebaseApp,\n  component: Component\n): void {\n  (app as FirebaseAppImpl).container.addOrOverwriteComponent(component);\n}\n\n/**\n *\n * @param component - the component to register\n * @returns whether or not the component is registered successfully\n *\n * @internal\n */\nexport function _registerComponent<T extends Name>(\n  component: Component<T>\n): boolean {\n  const componentName = component.name;\n  if (_components.has(componentName)) {\n    logger.debug(\n      `There were multiple attempts to register component ${componentName}.`\n    );\n\n    return false;\n  }\n\n  _components.set(componentName, component);\n\n  // add the component to existing app instances\n  for (const app of _apps.values()) {\n    _addComponent(app as FirebaseAppImpl, component);\n  }\n\n  for (const serverApp of _serverApps.values()) {\n    _addComponent(serverApp as FirebaseServerAppImpl, component);\n  }\n\n  return true;\n}\n\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n *\n * @returns the provider for the service with the matching name\n *\n * @internal\n */\nexport function _getProvider<T extends Name>(\n  app: FirebaseApp,\n  name: T\n): Provider<T> {\n  const heartbeatController = (app as FirebaseAppImpl).container\n    .getProvider('heartbeat')\n    .getImmediate({ optional: true });\n  if (heartbeatController) {\n    void heartbeatController.triggerHeartbeat();\n  }\n  return (app as FirebaseAppImpl).container.getProvider(name);\n}\n\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n * @param instanceIdentifier - service instance identifier in case the service supports multiple instances\n *\n * @internal\n */\nexport function _removeServiceInstance<T extends Name>(\n  app: FirebaseApp,\n  name: T,\n  instanceIdentifier: string = DEFAULT_ENTRY_NAME\n): void {\n  _getProvider(app, name).clearInstance(instanceIdentifier);\n}\n\n/**\n *\n * @param obj - an object of type FirebaseApp or FirebaseOptions.\n *\n * @returns true if the provide object is of type FirebaseApp.\n *\n * @internal\n */\nexport function _isFirebaseApp(\n  obj: FirebaseApp | FirebaseOptions\n): obj is FirebaseApp {\n  return (obj as FirebaseApp).options !== undefined;\n}\n\n/**\n *\n * @param obj - an object of type FirebaseApp.\n *\n * @returns true if the provided object is of type FirebaseServerAppImpl.\n *\n * @internal\n */\nexport function _isFirebaseServerApp(\n  obj: FirebaseApp | FirebaseServerApp | null | undefined\n): obj is FirebaseServerApp {\n  if (obj === null || obj === undefined) {\n    return false;\n  }\n  return (obj as FirebaseServerApp).settings !== undefined;\n}\n\n/**\n * Test only\n *\n * @internal\n */\nexport function _clearComponents(): void {\n  _components.clear();\n}\n\n/**\n * Exported in order to be used in app-compat package\n */\nexport { DEFAULT_ENTRY_NAME as _DEFAULT_ENTRY_NAME };\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum AppError {\n  NO_APP = 'no-app',\n  BAD_APP_NAME = 'bad-app-name',\n  DUPLICATE_APP = 'duplicate-app',\n  APP_DELETED = 'app-deleted',\n  SERVER_APP_DELETED = 'server-app-deleted',\n  NO_OPTIONS = 'no-options',\n  INVALID_APP_ARGUMENT = 'invalid-app-argument',\n  INVALID_LOG_ARGUMENT = 'invalid-log-argument',\n  IDB_OPEN = 'idb-open',\n  IDB_GET = 'idb-get',\n  IDB_WRITE = 'idb-set',\n  IDB_DELETE = 'idb-delete',\n  FINALIZATION_REGISTRY_NOT_SUPPORTED = 'finalization-registry-not-supported',\n  INVALID_SERVER_APP_ENVIRONMENT = 'invalid-server-app-environment'\n}\n\nconst ERRORS: ErrorMap<AppError> = {\n  [AppError.NO_APP]:\n    \"No Firebase App '{$appName}' has been created - \" +\n    'call initializeApp() first',\n  [AppError.BAD_APP_NAME]: \"Illegal App name: '{$appName}'\",\n  [AppError.DUPLICATE_APP]:\n    \"Firebase App named '{$appName}' already exists with different options or config\",\n  [AppError.APP_DELETED]: \"Firebase App named '{$appName}' already deleted\",\n  [AppError.SERVER_APP_DELETED]: 'Firebase Server App has been deleted',\n  [AppError.NO_OPTIONS]:\n    'Need to provide options, when not being deployed to hosting via source.',\n  [AppError.INVALID_APP_ARGUMENT]:\n    'firebase.{$appName}() takes either no argument or a ' +\n    'Firebase App instance.',\n  [AppError.INVALID_LOG_ARGUMENT]:\n    'First argument to `onLog` must be null or a function.',\n  [AppError.IDB_OPEN]:\n    'Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_GET]:\n    'Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_WRITE]:\n    'Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_DELETE]:\n    'Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED]:\n    'FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.',\n  [AppError.INVALID_SERVER_APP_ENVIRONMENT]:\n    'FirebaseServerApp is not for use in browser environments.'\n};\n\ninterface ErrorParams {\n  [AppError.NO_APP]: { appName: string };\n  [AppError.BAD_APP_NAME]: { appName: string };\n  [AppError.DUPLICATE_APP]: { appName: string };\n  [AppError.APP_DELETED]: { appName: string };\n  [AppError.INVALID_APP_ARGUMENT]: { appName: string };\n  [AppError.IDB_OPEN]: { originalErrorMessage?: string };\n  [AppError.IDB_GET]: { originalErrorMessage?: string };\n  [AppError.IDB_WRITE]: { originalErrorMessage?: string };\n  [AppError.IDB_DELETE]: { originalErrorMessage?: string };\n  [AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED]: { appName?: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<AppError, ErrorParams>(\n  'app',\n  'Firebase',\n  ERRORS\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseOptions,\n  FirebaseAppSettings\n} from './public-types';\nimport {\n  ComponentContainer,\n  Component,\n  ComponentType\n} from '@firebase/component';\nimport { ERROR_FACTORY, AppError } from './errors';\n\nexport class FirebaseAppImpl implements FirebaseApp {\n  protected readonly _options: FirebaseOptions;\n  protected readonly _name: string;\n  /**\n   * Original config values passed in as a constructor parameter.\n   * It is only used to compare with another config object to support idempotent initializeApp().\n   *\n   * Updating automaticDataCollectionEnabled on the App instance will not change its value in _config.\n   */\n  private readonly _config: Required<FirebaseAppSettings>;\n  private _automaticDataCollectionEnabled: boolean;\n  protected _isDeleted = false;\n  private readonly _container: ComponentContainer;\n\n  constructor(\n    options: FirebaseOptions,\n    config: Required<FirebaseAppSettings>,\n    container: ComponentContainer\n  ) {\n    this._options = { ...options };\n    this._config = { ...config };\n    this._name = config.name;\n    this._automaticDataCollectionEnabled =\n      config.automaticDataCollectionEnabled;\n    this._container = container;\n    this.container.addComponent(\n      new Component('app', () => this, ComponentType.PUBLIC)\n    );\n  }\n\n  get automaticDataCollectionEnabled(): boolean {\n    this.checkDestroyed();\n    return this._automaticDataCollectionEnabled;\n  }\n\n  set automaticDataCollectionEnabled(val: boolean) {\n    this.checkDestroyed();\n    this._automaticDataCollectionEnabled = val;\n  }\n\n  get name(): string {\n    this.checkDestroyed();\n    return this._name;\n  }\n\n  get options(): FirebaseOptions {\n    this.checkDestroyed();\n    return this._options;\n  }\n\n  get config(): Required<FirebaseAppSettings> {\n    this.checkDestroyed();\n    return this._config;\n  }\n\n  get container(): ComponentContainer {\n    return this._container;\n  }\n\n  get isDeleted(): boolean {\n    return this._isDeleted;\n  }\n\n  set isDeleted(val: boolean) {\n    this._isDeleted = val;\n  }\n\n  /**\n   * This function will throw an Error if the App has already been deleted -\n   * use before performing API actions on the App.\n   */\n  protected checkDestroyed(): void {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(AppError.APP_DELETED, { appName: this._name });\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseAppSettings,\n  FirebaseServerApp,\n  FirebaseServerAppSettings,\n  FirebaseOptions\n} from './public-types';\nimport { deleteApp, registerVersion } from './api';\nimport { ComponentContainer } from '@firebase/component';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { ERROR_FACTORY, AppError } from './errors';\nimport { name as packageName, version } from '../package.json';\nimport { base64Decode } from '@firebase/util';\n\n// Parse the token and check to see if the `exp` claim is in the future.\n// Reports an error to the console if the token or claim could not be parsed, or if `exp` is in\n// the past.\nfunction validateTokenTTL(base64Token: string, tokenName: string): void {\n  const secondPart = base64Decode(base64Token.split('.')[1]);\n  if (secondPart === null) {\n    console.error(\n      `FirebaseServerApp ${tokenName} is invalid: second part could not be parsed.`\n    );\n    return;\n  }\n  const expClaim = JSON.parse(secondPart).exp;\n  if (expClaim === undefined) {\n    console.error(\n      `FirebaseServerApp ${tokenName} is invalid: expiration claim could not be parsed`\n    );\n    return;\n  }\n  const exp = JSON.parse(secondPart).exp * 1000;\n  const now = new Date().getTime();\n  const diff = exp - now;\n  if (diff <= 0) {\n    console.error(\n      `FirebaseServerApp ${tokenName} is invalid: the token has expired.`\n    );\n  }\n}\n\nexport class FirebaseServerAppImpl\n  extends FirebaseAppImpl\n  implements FirebaseServerApp\n{\n  private readonly _serverConfig: FirebaseServerAppSettings;\n  private _finalizationRegistry: FinalizationRegistry<object> | null;\n  private _refCount: number;\n\n  constructor(\n    options: FirebaseOptions | FirebaseAppImpl,\n    serverConfig: FirebaseServerAppSettings,\n    name: string,\n    container: ComponentContainer\n  ) {\n    // Build configuration parameters for the FirebaseAppImpl base class.\n    const automaticDataCollectionEnabled =\n      serverConfig.automaticDataCollectionEnabled !== undefined\n        ? serverConfig.automaticDataCollectionEnabled\n        : false;\n\n    // Create the FirebaseAppSettings object for the FirebaseAppImp constructor.\n    const config: Required<FirebaseAppSettings> = {\n      name,\n      automaticDataCollectionEnabled\n    };\n\n    if ((options as FirebaseOptions).apiKey !== undefined) {\n      // Construct the parent FirebaseAppImp object.\n      super(options as FirebaseOptions, config, container);\n    } else {\n      const appImpl: FirebaseAppImpl = options as FirebaseAppImpl;\n      super(appImpl.options, config, container);\n    }\n\n    // Now construct the data for the FirebaseServerAppImpl.\n    this._serverConfig = {\n      automaticDataCollectionEnabled,\n      ...serverConfig\n    };\n\n    // Ensure that the current time is within the `authIdtoken` window of validity.\n    if (this._serverConfig.authIdToken) {\n      validateTokenTTL(this._serverConfig.authIdToken, 'authIdToken');\n    }\n\n    // Ensure that the current time is within the `appCheckToken` window of validity.\n    if (this._serverConfig.appCheckToken) {\n      validateTokenTTL(this._serverConfig.appCheckToken, 'appCheckToken');\n    }\n\n    this._finalizationRegistry = null;\n    if (typeof FinalizationRegistry !== 'undefined') {\n      this._finalizationRegistry = new FinalizationRegistry(() => {\n        this.automaticCleanup();\n      });\n    }\n\n    this._refCount = 0;\n    this.incRefCount(this._serverConfig.releaseOnDeref);\n\n    // Do not retain a hard reference to the dref object, otherwise the FinalizationRegistry\n    // will never trigger.\n    this._serverConfig.releaseOnDeref = undefined;\n    serverConfig.releaseOnDeref = undefined;\n\n    registerVersion(packageName, version, 'serverapp');\n  }\n\n  toJSON(): undefined {\n    return undefined;\n  }\n\n  get refCount(): number {\n    return this._refCount;\n  }\n\n  // Increment the reference count of this server app. If an object is provided, register it\n  // with the finalization registry.\n  incRefCount(obj: object | undefined): void {\n    if (this.isDeleted) {\n      return;\n    }\n    this._refCount++;\n    if (obj !== undefined && this._finalizationRegistry !== null) {\n      this._finalizationRegistry.register(obj, this);\n    }\n  }\n\n  // Decrement the reference count.\n  decRefCount(): number {\n    if (this.isDeleted) {\n      return 0;\n    }\n    return --this._refCount;\n  }\n\n  // Invoked by the FinalizationRegistry callback to note that this app should go through its\n  // reference counts and delete itself if no reference count remain. The coordinating logic that\n  // handles this is in deleteApp(...).\n  private automaticCleanup(): void {\n    void deleteApp(this);\n  }\n\n  get settings(): FirebaseServerAppSettings {\n    this.checkDestroyed();\n    return this._serverConfig;\n  }\n\n  /**\n   * This function will throw an Error if the App has already been deleted -\n   * use before performing API actions on the App.\n   */\n  protected checkDestroyed(): void {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(AppError.SERVER_APP_DELETED);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseServerApp,\n  FirebaseOptions,\n  FirebaseAppSettings,\n  FirebaseServerAppSettings\n} from './public-types';\nimport { DEFAULT_ENTRY_NAME, PLATFORM_LOG_STRING } from './constants';\nimport { ERROR_FACTORY, AppError } from './errors';\nimport {\n  ComponentContainer,\n  Component,\n  Name,\n  ComponentType\n} from '@firebase/component';\nimport { version } from '../../firebase/package.json';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { FirebaseServerAppImpl } from './firebaseServerApp';\nimport {\n  _apps,\n  _components,\n  _isFirebaseApp,\n  _registerComponent,\n  _serverApps\n} from './internal';\nimport { logger } from './logger';\nimport {\n  LogLevelString,\n  setLogLevel as setLogLevelImpl,\n  LogCallback,\n  LogOptions,\n  setUserLogHandler\n} from '@firebase/logger';\nimport {\n  deepEqual,\n  getDefaultAppConfig,\n  isBrowser,\n  isWebWorker\n} from '@firebase/util';\n\nexport { FirebaseError } from '@firebase/util';\n\n/**\n * The current SDK version.\n *\n * @public\n */\nexport const SDK_VERSION = version;\n\n/**\n * Creates and initializes a {@link @firebase/app#FirebaseApp} instance.\n *\n * See\n * {@link\n *   https://firebase.google.com/docs/web/setup#add_firebase_to_your_app\n *   | Add Firebase to your app} and\n * {@link\n *   https://firebase.google.com/docs/web/setup#multiple-projects\n *   | Initialize multiple projects} for detailed documentation.\n *\n * @example\n * ```javascript\n *\n * // Initialize default app\n * // Retrieve your own options values by adding a web app on\n * // https://console.firebase.google.com\n * initializeApp({\n *   apiKey: \"AIza....\",                             // Auth / General Use\n *   authDomain: \"YOUR_APP.firebaseapp.com\",         // Auth with popup/redirect\n *   databaseURL: \"https://YOUR_APP.firebaseio.com\", // Realtime Database\n *   storageBucket: \"YOUR_APP.appspot.com\",          // Storage\n *   messagingSenderId: \"123456789\"                  // Cloud Messaging\n * });\n * ```\n *\n * @example\n * ```javascript\n *\n * // Initialize another app\n * const otherApp = initializeApp({\n *   databaseURL: \"https://<OTHER_DATABASE_NAME>.firebaseio.com\",\n *   storageBucket: \"<OTHER_STORAGE_BUCKET>.appspot.com\"\n * }, \"otherApp\");\n * ```\n *\n * @param options - Options to configure the app's services.\n * @param name - Optional name of the app to initialize. If no name\n *   is provided, the default is `\"[DEFAULT]\"`.\n *\n * @returns The initialized app.\n *\n * @public\n */\nexport function initializeApp(\n  options: FirebaseOptions,\n  name?: string\n): FirebaseApp;\n/**\n * Creates and initializes a FirebaseApp instance.\n *\n * @param options - Options to configure the app's services.\n * @param config - FirebaseApp Configuration\n *\n * @public\n */\nexport function initializeApp(\n  options: FirebaseOptions,\n  config?: FirebaseAppSettings\n): FirebaseApp;\n/**\n * Creates and initializes a FirebaseApp instance.\n *\n * @public\n */\nexport function initializeApp(): FirebaseApp;\nexport function initializeApp(\n  _options?: FirebaseOptions,\n  rawConfig = {}\n): FirebaseApp {\n  let options = _options;\n\n  if (typeof rawConfig !== 'object') {\n    const name = rawConfig;\n    rawConfig = { name };\n  }\n\n  const config: Required<FirebaseAppSettings> = {\n    name: DEFAULT_ENTRY_NAME,\n    automaticDataCollectionEnabled: false,\n    ...rawConfig\n  };\n  const name = config.name;\n\n  if (typeof name !== 'string' || !name) {\n    throw ERROR_FACTORY.create(AppError.BAD_APP_NAME, {\n      appName: String(name)\n    });\n  }\n\n  options ||= getDefaultAppConfig();\n\n  if (!options) {\n    throw ERROR_FACTORY.create(AppError.NO_OPTIONS);\n  }\n\n  const existingApp = _apps.get(name) as FirebaseAppImpl;\n  if (existingApp) {\n    // return the existing app if options and config deep equal the ones in the existing app.\n    if (\n      deepEqual(options, existingApp.options) &&\n      deepEqual(config, existingApp.config)\n    ) {\n      return existingApp;\n    } else {\n      throw ERROR_FACTORY.create(AppError.DUPLICATE_APP, { appName: name });\n    }\n  }\n\n  const container = new ComponentContainer(name);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n\n  const newApp = new FirebaseAppImpl(options, config, container);\n\n  _apps.set(name, newApp);\n\n  return newApp;\n}\n\n/**\n * Creates and initializes a {@link @firebase/app#FirebaseServerApp} instance.\n *\n * The `FirebaseServerApp` is similar to `FirebaseApp`, but is intended for execution in\n * server side rendering environments only. Initialization will fail if invoked from a\n * browser environment.\n *\n * See\n * {@link\n *   https://firebase.google.com/docs/web/setup#add_firebase_to_your_app\n *   | Add Firebase to your app} and\n * {@link\n *   https://firebase.google.com/docs/web/setup#multiple-projects\n *   | Initialize multiple projects} for detailed documentation.\n *\n * @example\n * ```javascript\n *\n * // Initialize an instance of `FirebaseServerApp`.\n * // Retrieve your own options values by adding a web app on\n * // https://console.firebase.google.com\n * initializeServerApp({\n *     apiKey: \"AIza....\",                             // Auth / General Use\n *     authDomain: \"YOUR_APP.firebaseapp.com\",         // Auth with popup/redirect\n *     databaseURL: \"https://YOUR_APP.firebaseio.com\", // Realtime Database\n *     storageBucket: \"YOUR_APP.appspot.com\",          // Storage\n *     messagingSenderId: \"123456789\"                  // Cloud Messaging\n *   },\n *   {\n *    authIdToken: \"Your Auth ID Token\"\n *   });\n * ```\n *\n * @param options - `Firebase.AppOptions` to configure the app's services, or a\n *   a `FirebaseApp` instance which contains the `AppOptions` within.\n * @param config - `FirebaseServerApp` configuration.\n *\n * @returns The initialized `FirebaseServerApp`.\n *\n * @public\n */\nexport function initializeServerApp(\n  options: FirebaseOptions | FirebaseApp,\n  config: FirebaseServerAppSettings\n): FirebaseServerApp;\n\nexport function initializeServerApp(\n  _options: FirebaseOptions | FirebaseApp,\n  _serverAppConfig: FirebaseServerAppSettings\n): FirebaseServerApp {\n  if (isBrowser() && !isWebWorker()) {\n    // FirebaseServerApp isn't designed to be run in browsers.\n    throw ERROR_FACTORY.create(AppError.INVALID_SERVER_APP_ENVIRONMENT);\n  }\n\n  if (_serverAppConfig.automaticDataCollectionEnabled === undefined) {\n    _serverAppConfig.automaticDataCollectionEnabled = false;\n  }\n\n  let appOptions: FirebaseOptions;\n  if (_isFirebaseApp(_options)) {\n    appOptions = _options.options;\n  } else {\n    appOptions = _options;\n  }\n\n  // Build an app name based on a hash of the configuration options.\n  const nameObj = {\n    ..._serverAppConfig,\n    ...appOptions\n  };\n\n  // However, Do not mangle the name based on releaseOnDeref, since it will vary between the\n  // construction of FirebaseServerApp instances. For example, if the object is the request headers.\n  if (nameObj.releaseOnDeref !== undefined) {\n    delete nameObj.releaseOnDeref;\n  }\n\n  const hashCode = (s: string): number => {\n    return [...s].reduce(\n      (hash, c) => (Math.imul(31, hash) + c.charCodeAt(0)) | 0,\n      0\n    );\n  };\n\n  if (_serverAppConfig.releaseOnDeref !== undefined) {\n    if (typeof FinalizationRegistry === 'undefined') {\n      throw ERROR_FACTORY.create(\n        AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED,\n        {}\n      );\n    }\n  }\n\n  const nameString = '' + hashCode(JSON.stringify(nameObj));\n  const existingApp = _serverApps.get(nameString) as FirebaseServerApp;\n  if (existingApp) {\n    (existingApp as FirebaseServerAppImpl).incRefCount(\n      _serverAppConfig.releaseOnDeref\n    );\n    return existingApp;\n  }\n\n  const container = new ComponentContainer(nameString);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n\n  const newApp = new FirebaseServerAppImpl(\n    appOptions,\n    _serverAppConfig,\n    nameString,\n    container\n  );\n\n  _serverApps.set(nameString, newApp);\n\n  return newApp;\n}\n\n/**\n * Retrieves a {@link @firebase/app#FirebaseApp} instance.\n *\n * When called with no arguments, the default app is returned. When an app name\n * is provided, the app corresponding to that name is returned.\n *\n * An exception is thrown if the app being retrieved has not yet been\n * initialized.\n *\n * @example\n * ```javascript\n * // Return the default app\n * const app = getApp();\n * ```\n *\n * @example\n * ```javascript\n * // Return a named app\n * const otherApp = getApp(\"otherApp\");\n * ```\n *\n * @param name - Optional name of the app to return. If no name is\n *   provided, the default is `\"[DEFAULT]\"`.\n *\n * @returns The app corresponding to the provided app name.\n *   If no app name is provided, the default app is returned.\n *\n * @public\n */\nexport function getApp(name: string = DEFAULT_ENTRY_NAME): FirebaseApp {\n  const app = _apps.get(name);\n  if (!app && name === DEFAULT_ENTRY_NAME && getDefaultAppConfig()) {\n    return initializeApp();\n  }\n  if (!app) {\n    throw ERROR_FACTORY.create(AppError.NO_APP, { appName: name });\n  }\n\n  return app;\n}\n\n/**\n * A (read-only) array of all initialized apps.\n * @public\n */\nexport function getApps(): FirebaseApp[] {\n  return Array.from(_apps.values());\n}\n\n/**\n * Renders this app unusable and frees the resources of all associated\n * services.\n *\n * @example\n * ```javascript\n * deleteApp(app)\n *   .then(function() {\n *     console.log(\"App deleted successfully\");\n *   })\n *   .catch(function(error) {\n *     console.log(\"Error deleting app:\", error);\n *   });\n * ```\n *\n * @public\n */\nexport async function deleteApp(app: FirebaseApp): Promise<void> {\n  let cleanupProviders = false;\n  const name = app.name;\n  if (_apps.has(name)) {\n    cleanupProviders = true;\n    _apps.delete(name);\n  } else if (_serverApps.has(name)) {\n    const firebaseServerApp = app as FirebaseServerAppImpl;\n    if (firebaseServerApp.decRefCount() <= 0) {\n      _serverApps.delete(name);\n      cleanupProviders = true;\n    }\n  }\n\n  if (cleanupProviders) {\n    await Promise.all(\n      (app as FirebaseAppImpl).container\n        .getProviders()\n        .map(provider => provider.delete())\n    );\n    (app as FirebaseAppImpl).isDeleted = true;\n  }\n}\n\n/**\n * Registers a library's name and version for platform logging purposes.\n * @param library - Name of 1p or 3p library (e.g. firestore, angularfire)\n * @param version - Current version of that library.\n * @param variant - Bundle variant, e.g., node, rn, etc.\n *\n * @public\n */\nexport function registerVersion(\n  libraryKeyOrName: string,\n  version: string,\n  variant?: string\n): void {\n  // TODO: We can use this check to whitelist strings when/if we set up\n  // a good whitelist system.\n  let library = PLATFORM_LOG_STRING[libraryKeyOrName] ?? libraryKeyOrName;\n  if (variant) {\n    library += `-${variant}`;\n  }\n  const libraryMismatch = library.match(/\\s|\\//);\n  const versionMismatch = version.match(/\\s|\\//);\n  if (libraryMismatch || versionMismatch) {\n    const warning = [\n      `Unable to register library \"${library}\" with version \"${version}\":`\n    ];\n    if (libraryMismatch) {\n      warning.push(\n        `library name \"${library}\" contains illegal characters (whitespace or \"/\")`\n      );\n    }\n    if (libraryMismatch && versionMismatch) {\n      warning.push('and');\n    }\n    if (versionMismatch) {\n      warning.push(\n        `version name \"${version}\" contains illegal characters (whitespace or \"/\")`\n      );\n    }\n    logger.warn(warning.join(' '));\n    return;\n  }\n  _registerComponent(\n    new Component(\n      `${library}-version` as Name,\n      () => ({ library, version }),\n      ComponentType.VERSION\n    )\n  );\n}\n\n/**\n * Sets log handler for all Firebase SDKs.\n * @param logCallback - An optional custom log handler that executes user code whenever\n * the Firebase SDK makes a logging call.\n *\n * @public\n */\nexport function onLog(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  if (logCallback !== null && typeof logCallback !== 'function') {\n    throw ERROR_FACTORY.create(AppError.INVALID_LOG_ARGUMENT);\n  }\n  setUserLogHandler(logCallback, options);\n}\n\n/**\n * Sets log level for all Firebase SDKs.\n *\n * All of the log types above the current log level are captured (i.e. if\n * you set the log level to `info`, errors are logged, but `debug` and\n * `verbose` logs are not).\n *\n * @public\n */\nexport function setLogLevel(logLevel: LogLevelString): void {\n  setLogLevelImpl(logLevel);\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { DBSchema, openDB, IDBPDatabase } from 'idb';\nimport { AppError, ERROR_FACTORY } from './errors';\nimport { FirebaseApp } from './public-types';\nimport { HeartbeatsInIndexedDB } from './types';\nimport { logger } from './logger';\n\nconst DB_NAME = 'firebase-heartbeat-database';\nconst DB_VERSION = 1;\nconst STORE_NAME = 'firebase-heartbeat-store';\n\ninterface AppDB extends DBSchema {\n  'firebase-heartbeat-store': {\n    key: string;\n    value: HeartbeatsInIndexedDB;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<AppDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<AppDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB<AppDB>(DB_NAME, DB_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            try {\n              db.createObjectStore(STORE_NAME);\n            } catch (e) {\n              // Safari/iOS browsers throw occasional exceptions on\n              // db.createObjectStore() that may be a bug. Avoid blocking\n              // the rest of the app functionality.\n              console.warn(e);\n            }\n        }\n      }\n    }).catch(e => {\n      throw ERROR_FACTORY.create(AppError.IDB_OPEN, {\n        originalErrorMessage: e.message\n      });\n    });\n  }\n  return dbPromise;\n}\n\nexport async function readHeartbeatsFromIndexedDB(\n  app: FirebaseApp\n): Promise<HeartbeatsInIndexedDB | undefined> {\n  try {\n    const db = await getDbPromise();\n    const tx = db.transaction(STORE_NAME);\n    const result = await tx.objectStore(STORE_NAME).get(computeKey(app));\n    // We already have the value but tx.done can throw,\n    // so we need to await it here to catch errors\n    await tx.done;\n    return result;\n  } catch (e) {\n    if (e instanceof FirebaseError) {\n      logger.warn(e.message);\n    } else {\n      const idbGetError = ERROR_FACTORY.create(AppError.IDB_GET, {\n        originalErrorMessage: (e as Error)?.message\n      });\n      logger.warn(idbGetError.message);\n    }\n  }\n}\n\nexport async function writeHeartbeatsToIndexedDB(\n  app: FirebaseApp,\n  heartbeatObject: HeartbeatsInIndexedDB\n): Promise<void> {\n  try {\n    const db = await getDbPromise();\n    const tx = db.transaction(STORE_NAME, 'readwrite');\n    const objectStore = tx.objectStore(STORE_NAME);\n    await objectStore.put(heartbeatObject, computeKey(app));\n    await tx.done;\n  } catch (e) {\n    if (e instanceof FirebaseError) {\n      logger.warn(e.message);\n    } else {\n      const idbGetError = ERROR_FACTORY.create(AppError.IDB_WRITE, {\n        originalErrorMessage: (e as Error)?.message\n      });\n      logger.warn(idbGetError.message);\n    }\n  }\n}\n\nfunction computeKey(app: FirebaseApp): string {\n  return `${app.name}!${app.options.appId}`;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ComponentContainer } from '@firebase/component';\nimport {\n  base64urlEncodeWithoutPadding,\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable\n} from '@firebase/util';\nimport {\n  readHeartbeatsFromIndexedDB,\n  writeHeartbeatsToIndexedDB\n} from './indexeddb';\nimport { FirebaseApp } from './public-types';\nimport {\n  HeartbeatsByUserAgent,\n  HeartbeatService,\n  HeartbeatsInIndexedDB,\n  HeartbeatStorage,\n  SingleDateHeartbeat\n} from './types';\nimport { logger } from './logger';\n\nconst MAX_HEADER_BYTES = 1024;\nexport const MAX_NUM_STORED_HEARTBEATS = 30;\n\nexport class HeartbeatServiceImpl implements HeartbeatService {\n  /**\n   * The persistence layer for heartbeats\n   * Leave public for easier testing.\n   */\n  _storage: HeartbeatStorageImpl;\n\n  /**\n   * In-memory cache for heartbeats, used by getHeartbeatsHeader() to generate\n   * the header string.\n   * Stores one record per date. This will be consolidated into the standard\n   * format of one record per user agent string before being sent as a header.\n   * Populated from indexedDB when the controller is instantiated and should\n   * be kept in sync with indexedDB.\n   * Leave public for easier testing.\n   */\n  _heartbeatsCache: HeartbeatsInIndexedDB | null = null;\n\n  /**\n   * the initialization promise for populating heartbeatCache.\n   * If getHeartbeatsHeader() is called before the promise resolves\n   * (heartbeatsCache == null), it should wait for this promise\n   * Leave public for easier testing.\n   */\n  _heartbeatsCachePromise: Promise<HeartbeatsInIndexedDB>;\n  constructor(private readonly container: ComponentContainer) {\n    const app = this.container.getProvider('app').getImmediate();\n    this._storage = new HeartbeatStorageImpl(app);\n    this._heartbeatsCachePromise = this._storage.read().then(result => {\n      this._heartbeatsCache = result;\n      return result;\n    });\n  }\n\n  /**\n   * Called to report a heartbeat. The function will generate\n   * a HeartbeatsByUserAgent object, update heartbeatsCache, and persist it\n   * to IndexedDB.\n   * Note that we only store one heartbeat per day. So if a heartbeat for today is\n   * already logged, subsequent calls to this function in the same day will be ignored.\n   */\n  async triggerHeartbeat(): Promise<void> {\n    try {\n      const platformLogger = this.container\n        .getProvider('platform-logger')\n        .getImmediate();\n\n      // This is the \"Firebase user agent\" string from the platform logger\n      // service, not the browser user agent.\n      const agent = platformLogger.getPlatformInfoString();\n      const date = getUTCDateString();\n      if (this._heartbeatsCache?.heartbeats == null) {\n        this._heartbeatsCache = await this._heartbeatsCachePromise;\n        // If we failed to construct a heartbeats cache, then return immediately.\n        if (this._heartbeatsCache?.heartbeats == null) {\n          return;\n        }\n      }\n      // Do not store a heartbeat if one is already stored for this day\n      // or if a header has already been sent today.\n      if (\n        this._heartbeatsCache.lastSentHeartbeatDate === date ||\n        this._heartbeatsCache.heartbeats.some(\n          singleDateHeartbeat => singleDateHeartbeat.date === date\n        )\n      ) {\n        return;\n      } else {\n        // There is no entry for this date. Create one.\n        this._heartbeatsCache.heartbeats.push({ date, agent });\n\n        // If the number of stored heartbeats exceeds the maximum number of stored heartbeats, remove the heartbeat with the earliest date.\n        // Since this is executed each time a heartbeat is pushed, the limit can only be exceeded by one, so only one needs to be removed.\n        if (\n          this._heartbeatsCache.heartbeats.length > MAX_NUM_STORED_HEARTBEATS\n        ) {\n          const earliestHeartbeatIdx = getEarliestHeartbeatIdx(\n            this._heartbeatsCache.heartbeats\n          );\n          this._heartbeatsCache.heartbeats.splice(earliestHeartbeatIdx, 1);\n        }\n      }\n\n      return this._storage.overwrite(this._heartbeatsCache);\n    } catch (e) {\n      logger.warn(e);\n    }\n  }\n\n  /**\n   * Returns a base64 encoded string which can be attached to the heartbeat-specific header directly.\n   * It also clears all heartbeats from memory as well as in IndexedDB.\n   *\n   * NOTE: Consuming product SDKs should not send the header if this method\n   * returns an empty string.\n   */\n  async getHeartbeatsHeader(): Promise<string> {\n    try {\n      if (this._heartbeatsCache === null) {\n        await this._heartbeatsCachePromise;\n      }\n      // If it's still null or the array is empty, there is no data to send.\n      if (\n        this._heartbeatsCache?.heartbeats == null ||\n        this._heartbeatsCache.heartbeats.length === 0\n      ) {\n        return '';\n      }\n      const date = getUTCDateString();\n      // Extract as many heartbeats from the cache as will fit under the size limit.\n      const { heartbeatsToSend, unsentEntries } = extractHeartbeatsForHeader(\n        this._heartbeatsCache.heartbeats\n      );\n      const headerString = base64urlEncodeWithoutPadding(\n        JSON.stringify({ version: 2, heartbeats: heartbeatsToSend })\n      );\n      // Store last sent date to prevent another being logged/sent for the same day.\n      this._heartbeatsCache.lastSentHeartbeatDate = date;\n      if (unsentEntries.length > 0) {\n        // Store any unsent entries if they exist.\n        this._heartbeatsCache.heartbeats = unsentEntries;\n        // This seems more likely than emptying the array (below) to lead to some odd state\n        // since the cache isn't empty and this will be called again on the next request,\n        // and is probably safest if we await it.\n        await this._storage.overwrite(this._heartbeatsCache);\n      } else {\n        this._heartbeatsCache.heartbeats = [];\n        // Do not wait for this, to reduce latency.\n        void this._storage.overwrite(this._heartbeatsCache);\n      }\n      return headerString;\n    } catch (e) {\n      logger.warn(e);\n      return '';\n    }\n  }\n}\n\nfunction getUTCDateString(): string {\n  const today = new Date();\n  // Returns date format 'YYYY-MM-DD'\n  return today.toISOString().substring(0, 10);\n}\n\nexport function extractHeartbeatsForHeader(\n  heartbeatsCache: SingleDateHeartbeat[],\n  maxSize = MAX_HEADER_BYTES\n): {\n  heartbeatsToSend: HeartbeatsByUserAgent[];\n  unsentEntries: SingleDateHeartbeat[];\n} {\n  // Heartbeats grouped by user agent in the standard format to be sent in\n  // the header.\n  const heartbeatsToSend: HeartbeatsByUserAgent[] = [];\n  // Single date format heartbeats that are not sent.\n  let unsentEntries = heartbeatsCache.slice();\n  for (const singleDateHeartbeat of heartbeatsCache) {\n    // Look for an existing entry with the same user agent.\n    const heartbeatEntry = heartbeatsToSend.find(\n      hb => hb.agent === singleDateHeartbeat.agent\n    );\n    if (!heartbeatEntry) {\n      // If no entry for this user agent exists, create one.\n      heartbeatsToSend.push({\n        agent: singleDateHeartbeat.agent,\n        dates: [singleDateHeartbeat.date]\n      });\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        // If the header would exceed max size, remove the added heartbeat\n        // entry and stop adding to the header.\n        heartbeatsToSend.pop();\n        break;\n      }\n    } else {\n      heartbeatEntry.dates.push(singleDateHeartbeat.date);\n      // If the header would exceed max size, remove the added date\n      // and stop adding to the header.\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        heartbeatEntry.dates.pop();\n        break;\n      }\n    }\n    // Pop unsent entry from queue. (Skipped if adding the entry exceeded\n    // quota and the loop breaks early.)\n    unsentEntries = unsentEntries.slice(1);\n  }\n  return {\n    heartbeatsToSend,\n    unsentEntries\n  };\n}\n\nexport class HeartbeatStorageImpl implements HeartbeatStorage {\n  private _canUseIndexedDBPromise: Promise<boolean>;\n  constructor(public app: FirebaseApp) {\n    this._canUseIndexedDBPromise = this.runIndexedDBEnvironmentCheck();\n  }\n  async runIndexedDBEnvironmentCheck(): Promise<boolean> {\n    if (!isIndexedDBAvailable()) {\n      return false;\n    } else {\n      return validateIndexedDBOpenable()\n        .then(() => true)\n        .catch(() => false);\n    }\n  }\n  /**\n   * Read all heartbeats.\n   */\n  async read(): Promise<HeartbeatsInIndexedDB> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return { heartbeats: [] };\n    } else {\n      const idbHeartbeatObject = await readHeartbeatsFromIndexedDB(this.app);\n      if (idbHeartbeatObject?.heartbeats) {\n        return idbHeartbeatObject;\n      } else {\n        return { heartbeats: [] };\n      }\n    }\n  }\n  // overwrite the storage with the provided heartbeats\n  async overwrite(heartbeatsObject: HeartbeatsInIndexedDB): Promise<void> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return;\n    } else {\n      const existingHeartbeatsObject = await this.read();\n      return writeHeartbeatsToIndexedDB(this.app, {\n        lastSentHeartbeatDate:\n          heartbeatsObject.lastSentHeartbeatDate ??\n          existingHeartbeatsObject.lastSentHeartbeatDate,\n        heartbeats: heartbeatsObject.heartbeats\n      });\n    }\n  }\n  // add heartbeats\n  async add(heartbeatsObject: HeartbeatsInIndexedDB): Promise<void> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return;\n    } else {\n      const existingHeartbeatsObject = await this.read();\n      return writeHeartbeatsToIndexedDB(this.app, {\n        lastSentHeartbeatDate:\n          heartbeatsObject.lastSentHeartbeatDate ??\n          existingHeartbeatsObject.lastSentHeartbeatDate,\n        heartbeats: [\n          ...existingHeartbeatsObject.heartbeats,\n          ...heartbeatsObject.heartbeats\n        ]\n      });\n    }\n  }\n}\n\n/**\n * Calculate bytes of a HeartbeatsByUserAgent array after being wrapped\n * in a platform logging header JSON object, stringified, and converted\n * to base 64.\n */\nexport function countBytes(heartbeatsCache: HeartbeatsByUserAgent[]): number {\n  // base64 has a restricted set of characters, all of which should be 1 byte.\n  return base64urlEncodeWithoutPadding(\n    // heartbeatsCache wrapper properties\n    JSON.stringify({ version: 2, heartbeats: heartbeatsCache })\n  ).length;\n}\n\n/**\n * Returns the index of the heartbeat with the earliest date.\n * If the heartbeats array is empty, -1 is returned.\n */\nexport function getEarliestHeartbeatIdx(\n  heartbeats: SingleDateHeartbeat[]\n): number {\n  if (heartbeats.length === 0) {\n    return -1;\n  }\n\n  let earliestHeartbeatIdx = 0;\n  let earliestHeartbeatDate = heartbeats[0].date;\n\n  for (let i = 1; i < heartbeats.length; i++) {\n    if (heartbeats[i].date < earliestHeartbeatDate) {\n      earliestHeartbeatDate = heartbeats[i].date;\n      earliestHeartbeatIdx = i;\n    }\n  }\n\n  return earliestHeartbeatIdx;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Component, ComponentType } from '@firebase/component';\nimport { PlatformLoggerServiceImpl } from './platformLoggerService';\nimport { name, version } from '../package.json';\nimport { _registerComponent } from './internal';\nimport { registerVersion } from './api';\nimport { HeartbeatServiceImpl } from './heartbeatService';\n\nexport function registerCoreComponents(variant?: string): void {\n  _registerComponent(\n    new Component(\n      'platform-logger',\n      container => new PlatformLoggerServiceImpl(container),\n      ComponentType.PRIVATE\n    )\n  );\n  _registerComponent(\n    new Component(\n      'heartbeat',\n      container => new HeartbeatServiceImpl(container),\n      ComponentType.PRIVATE\n    )\n  );\n\n  // Register `app` package.\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n  // Register platform SDK identifier (no version).\n  registerVersion('fire-js', '');\n}\n", "/**\n * Firebase App\n *\n * @remarks This package coordinates the communication between the different Firebase components\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerCoreComponents } from './registerCoreComponents';\n\nexport * from './api';\nexport * from './internal';\nexport * from './public-types';\n\nregisterCoreComponents('__RUNTIME_ENV__');\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerVersion } from '@firebase/app';\nimport { name, version } from '../package.json';\n\nregisterVersion(name, version, 'cdn');\nexport * from '@firebase/app';\n"], "names": ["stringToByteArray", "DEFAULT_ENTRY_NAME", "setLogLevel", "appName", "appCompatName", "analyticsName", "analyticsCompatName", "appCheckName", "appCheckCompatName", "authName", "authCompatName", "databaseName", "dataconnectName", "databaseCompatName", "functionsName", "functionsCompatName", "installationsName", "installationsCompatName", "messagingName", "messagingCompatName", "performanceName", "performanceCompatName", "remoteConfigName", "remoteConfigCompatName", "storageName", "storageCompatName", "firestoreName", "firestoreCompatName", "vertexName", "packageName", "version", "setLogLevelImpl", "name"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,0BAA0B,GAAG,MAAM,SAAS;;ACjBlD;;;;;;;;;;;;;;;AAeG;AAEH,MAAMA,mBAAiB,GAAG,UAAU,GAAW,EAAA;;IAE7C,MAAM,GAAG,GAAa,EAAE,CAAA;IACxB,IAAI,CAAC,GAAG,CAAC,CAAA;AACT,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,IAAI,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;AACzB,QAAA,IAAI,CAAC,GAAG,GAAG,EAAE;AACX,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;SACb;AAAM,aAAA,IAAI,CAAC,GAAG,IAAI,EAAE;AACnB,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,CAAA;AACzB,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAA;SAC1B;AAAM,aAAA,IACL,CAAC,CAAC,GAAG,MAAM,MAAM,MAAM;AACvB,YAAA,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM;AAClB,YAAA,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,MAAM,MAAM,EAC3C;;YAEA,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC,GAAG,MAAM,KAAK,EAAE,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,CAAA;AACnE,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,GAAG,CAAA;AAC1B,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,GAAG,CAAA;AACjC,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,CAAA;AAChC,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAA;SAC1B;aAAM;AACL,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,GAAG,CAAA;AAC1B,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,CAAA;AAChC,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAA;SAC1B;KACF;AACD,IAAA,OAAO,GAAG,CAAA;AACZ,CAAC,CAAA;AAED;;;;;AAKG;AACH,MAAM,iBAAiB,GAAG,UAAU,KAAe,EAAA;;IAEjD,MAAM,GAAG,GAAa,EAAE,CAAA;AACxB,IAAA,IAAI,GAAG,GAAG,CAAC,EACT,CAAC,GAAG,CAAC,CAAA;AACP,IAAA,OAAO,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE;AACzB,QAAA,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAA;AACvB,QAAA,IAAI,EAAE,GAAG,GAAG,EAAE;YACZ,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,CAAA;SACnC;aAAM,IAAI,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,EAAE;AAC/B,YAAA,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAA;YACvB,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAA;SAC7D;aAAM,IAAI,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,EAAE;;AAE/B,YAAA,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAA;AACvB,YAAA,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAA;AACvB,YAAA,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAA;AACvB,YAAA,MAAM,CAAC,GACL,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AACpE,gBAAA,OAAO,CAAA;AACT,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;AAClD,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAA;SACpD;aAAM;AACL,YAAA,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAA;AACvB,YAAA,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAA;AACvB,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,YAAY,CAC5B,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CACjD,CAAA;SACF;KACF;AACD,IAAA,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;AACrB,CAAC,CAAA;AAkBD;AACA;AACA;AACA;AACa,MAAA,MAAM,GAAW;AAC5B;;AAEG;AACH,IAAA,cAAc,EAAE,IAAI;AAEpB;;AAEG;AACH,IAAA,cAAc,EAAE,IAAI;AAEpB;;;AAGG;AACH,IAAA,qBAAqB,EAAE,IAAI;AAE3B;;;AAGG;AACH,IAAA,qBAAqB,EAAE,IAAI;AAE3B;;;AAGG;AACH,IAAA,iBAAiB,EACf,4BAA4B,GAAG,4BAA4B,GAAG,YAAY;AAE5E;;AAEG;AACH,IAAA,IAAI,YAAY,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAA;KACtC;AAED;;AAEG;AACH,IAAA,IAAI,oBAAoB,GAAA;AACtB,QAAA,OAAO,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAA;KACtC;AAED;;;;;;AAMG;AACH,IAAA,kBAAkB,EAAE,OAAO,IAAI,KAAK,UAAU;AAE9C;;;;;;;;AAQG;IACH,eAAe,CAAC,KAA4B,EAAE,OAAiB,EAAA;QAC7D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACzB,YAAA,MAAM,KAAK,CAAC,+CAA+C,CAAC,CAAA;SAC7D;QAED,IAAI,CAAC,KAAK,EAAE,CAAA;QAEZ,MAAM,aAAa,GAAG,OAAO;cACzB,IAAI,CAAC,qBAAsB;AAC3B,cAAA,IAAI,CAAC,cAAe,CAAA;QAExB,MAAM,MAAM,GAAG,EAAE,CAAA;AAEjB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;AACxC,YAAA,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;YACtB,MAAM,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAA;AACtC,YAAA,MAAM,KAAK,GAAG,SAAS,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;YAC1C,MAAM,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAA;AACtC,YAAA,MAAM,KAAK,GAAG,SAAS,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;AAE1C,YAAA,MAAM,QAAQ,GAAG,KAAK,IAAI,CAAC,CAAA;AAC3B,YAAA,MAAM,QAAQ,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAA;AACrD,YAAA,IAAI,QAAQ,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAA;AACnD,YAAA,IAAI,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAA;YAE3B,IAAI,CAAC,SAAS,EAAE;gBACd,QAAQ,GAAG,EAAE,CAAA;gBAEb,IAAI,CAAC,SAAS,EAAE;oBACd,QAAQ,GAAG,EAAE,CAAA;iBACd;aACF;YAED,MAAM,CAAC,IAAI,CACT,aAAa,CAAC,QAAQ,CAAC,EACvB,aAAa,CAAC,QAAQ,CAAC,EACvB,aAAa,CAAC,QAAQ,CAAC,EACvB,aAAa,CAAC,QAAQ,CAAC,CACxB,CAAA;SACF;AAED,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;KACvB;AAED;;;;;;;AAOG;IACH,YAAY,CAAC,KAAa,EAAE,OAAiB,EAAA;;;AAG3C,QAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,CAAC,OAAO,EAAE;AACvC,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,CAAA;SACnB;QACD,OAAO,IAAI,CAAC,eAAe,CAACA,mBAAiB,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAA;KAC/D;AAED;;;;;;;AAOG;IACH,YAAY,CAAC,KAAa,EAAE,OAAgB,EAAA;;;AAG1C,QAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,CAAC,OAAO,EAAE;AACvC,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,CAAA;SACnB;QACD,OAAO,iBAAiB,CAAC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAA;KACvE;AAED;;;;;;;;;;;;;;AAcG;IACH,uBAAuB,CAAC,KAAa,EAAE,OAAgB,EAAA;QACrD,IAAI,CAAC,KAAK,EAAE,CAAA;QAEZ,MAAM,aAAa,GAAG,OAAO;cACzB,IAAI,CAAC,qBAAsB;AAC3B,cAAA,IAAI,CAAC,cAAe,CAAA;QAExB,MAAM,MAAM,GAAa,EAAE,CAAA;QAE3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAI;AAClC,YAAA,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AAE9C,YAAA,MAAM,SAAS,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAA;AAClC,YAAA,MAAM,KAAK,GAAG,SAAS,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;AAC5D,YAAA,EAAE,CAAC,CAAA;AAEH,YAAA,MAAM,SAAS,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAA;AAClC,YAAA,MAAM,KAAK,GAAG,SAAS,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAA;AAC7D,YAAA,EAAE,CAAC,CAAA;AAEH,YAAA,MAAM,SAAS,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAA;AAClC,YAAA,MAAM,KAAK,GAAG,SAAS,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAA;AAC7D,YAAA,EAAE,CAAC,CAAA;AAEH,YAAA,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;gBACpE,MAAM,IAAI,uBAAuB,EAAE,CAAA;aACpC;AAED,YAAA,MAAM,QAAQ,GAAG,CAAC,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAA;AAC5C,YAAA,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;AAErB,YAAA,IAAI,KAAK,KAAK,EAAE,EAAE;AAChB,gBAAA,MAAM,QAAQ,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,CAAC,CAAA;AACrD,gBAAA,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;AAErB,gBAAA,IAAI,KAAK,KAAK,EAAE,EAAE;AAChB,oBAAA,MAAM,QAAQ,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,CAAA;AAC9C,oBAAA,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;iBACtB;aACF;SACF;AAED,QAAA,OAAO,MAAM,CAAA;KACd;AAED;;;;AAIG;IACH,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AACxB,YAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAA;AACxB,YAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAA;AACxB,YAAA,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAA;AAC/B,YAAA,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAA;;AAG/B,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,gBAAA,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;AACpD,gBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;AAC/C,gBAAA,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;AACnE,gBAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;;gBAG7D,IAAI,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;AACtC,oBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;AAC5D,oBAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;iBAC5D;aACF;SACF;KACF;CACD,CAAA;AAEF;;AAEG;AACG,MAAO,uBAAgC,SAAA,KAAK,CAAA;AAAlD,IAAA,WAAA,GAAA;;AACW,QAAA,IAAI,CAAA,IAAA,GAAG,yBAAyB,CAAA;KAC1C;AAAA,CAAA;AAED;;AAEG;AACI,MAAM,YAAY,GAAG,UAAU,GAAW,EAAA;AAC/C,IAAA,MAAM,SAAS,GAAGA,mBAAiB,CAAC,GAAG,CAAC,CAAA;IACxC,OAAO,MAAM,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;AAChD,CAAE,CAAA;AAEF;;;AAGG;AACI,MAAM,6BAA6B,GAAG,UAAU,GAAW,EAAA;;IAEhE,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;AAC7C,CAAE,CAAA;AAEF;;;;;;;;AAQG;AACI,MAAM,YAAY,GAAG,UAAU,GAAW,EAAA;AAC/C,IAAA,IAAI;QACF,OAAO,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;KACtC;IAAC,OAAO,CAAC,EAAE;AACV,QAAA,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,CAAC,CAAC,CAAA;KAC1C;AACD,IAAA,OAAO,IAAI,CAAA;AACb,CAAA,CAAA;ACxXA;;;;;;;;;;;;;;;AAeG;AAEH;;;;AAIG;SACa,SAAS,GAAA;AACvB,IAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC/B,QAAA,OAAO,IAAI,CAAA;KACZ;AACD,IAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AACjC,QAAA,OAAO,MAAM,CAAA;KACd;AACD,IAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AACjC,QAAA,OAAO,MAAM,CAAA;KACd;AACD,IAAA,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAA;AACpD,CAAA;ACjCA;;;;;;;;;;;;;;;AAeG;AAyCH,MAAM,qBAAqB,GAAG,MAC5B,SAAS,EAAE,CAAC,qBAAqB,CAAA;AAEnC;;;;;;;AAOG;AACH,MAAM,0BAA0B,GAAG,MAAmC;AACpE,IAAA,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,OAAO,CAAC,GAAG,KAAK,WAAW,EAAE;QACxE,OAAO;KACR;AACD,IAAA,MAAM,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAA;IAC5D,IAAI,kBAAkB,EAAE;AACtB,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAA;KACtC;AACH,CAAC,CAAA;AAED,MAAM,qBAAqB,GAAG,MAAmC;AAC/D,IAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;QACnC,OAAO;KACR;AACD,IAAA,IAAI,KAAK,CAAA;AACT,IAAA,IAAI;QACF,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAA;KAC/D;IAAC,OAAO,CAAC,EAAE;;;QAGV,OAAO;KACR;IACD,MAAM,OAAO,GAAG,KAAK,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IAC/C,OAAO,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;AACvC,CAAC,CAAA;AAED;;;;;;AAMG;AACI,MAAM,WAAW,GAAG,MAAmC;AAC5D,IAAA,IAAI;QACF,QACE,0BAA0B,EAAE;AAC5B,YAAA,qBAAqB,EAAE;AACvB,YAAA,0BAA0B,EAAE;YAC5B,qBAAqB,EAAE,EACvB;KACH;IAAC,OAAO,CAAC,EAAE;AACV;;;;;AAKG;AACH,QAAA,OAAO,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAA,CAAE,CAAC,CAAA;QAChE,OAAO;KACR;AACH,CAAE,CAAA;AAuCF;;;AAGG;AACI,MAAM,mBAAmB,GAAG,MAAyC,EAAA,IAAA,EAAA,CAAA,CAC1E,OAAA,CAAA,EAAA,GAAA,WAAW,EAAE,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,CAAA,EAAC,CAAA;AClKxB;;;;;;;;;;;;;;;AAeG;AAEU,MAAA,QAAQ,CAAA;AAInB,IAAA,WAAA,GAAA;AAFA,QAAA,IAAA,CAAA,MAAM,GAA8B,MAAA,GAAQ,CAAA;AAC5C,QAAA,IAAA,CAAA,OAAO,GAA8B,MAAA,GAAQ,CAAA;QAE3C,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;AAC7C,YAAA,IAAI,CAAC,OAAO,GAAG,OAAoC,CAAA;AACnD,YAAA,IAAI,CAAC,MAAM,GAAG,MAAmC,CAAA;AACnD,SAAC,CAAC,CAAA;KACH;AAED;;;;AAIG;AACH,IAAA,YAAY,CACV,QAAqD,EAAA;AAErD,QAAA,OAAO,CAAC,KAAK,EAAE,KAAM,KAAI;YACvB,IAAI,KAAK,EAAE;AACT,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;aACnB;iBAAM;AACL,gBAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;aACpB;AACD,YAAA,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;;;gBAGlC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAA,GAAQ,CAAC,CAAA;;;AAI5B,gBAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;oBACzB,QAAQ,CAAC,KAAK,CAAC,CAAA;iBAChB;qBAAM;AACL,oBAAA,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;iBACvB;aACF;AACH,SAAC,CAAA;KACF;AACF,CAAA;ACuBD;;;;;AAKG;SACa,SAAS,GAAA;AACvB,IAAA,OAAO,OAAO,MAAM,KAAK,WAAW,IAAI,WAAW,EAAE,CAAA;AACvD,CAAA;AAEA;;AAEG;SACa,WAAW,GAAA;AACzB,IAAA,QACE,OAAO,iBAAiB,KAAK,WAAW;QACxC,OAAO,IAAI,KAAK,WAAW;QAC3B,IAAI,YAAY,iBAAiB,EACjC;AACJ,CAAA;AA4EA;;;AAGG;SACa,oBAAoB,GAAA;AAClC,IAAA,IAAI;AACF,QAAA,OAAO,OAAO,SAAS,KAAK,QAAQ,CAAA;KACrC;IAAC,OAAO,CAAC,EAAE;AACV,QAAA,OAAO,KAAK,CAAA;KACb;AACH,CAAA;AAEA;;;;;;AAMG;SACa,yBAAyB,GAAA;IACvC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;AACrC,QAAA,IAAI;YACF,IAAI,QAAQ,GAAY,IAAI,CAAA;YAC5B,MAAM,aAAa,GACjB,yDAAyD,CAAA;YAC3D,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;AAClD,YAAA,OAAO,CAAC,SAAS,GAAG,MAAK;AACvB,gBAAA,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAA;;gBAEtB,IAAI,CAAC,QAAQ,EAAE;AACb,oBAAA,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,aAAa,CAAC,CAAA;iBAC7C;gBACD,OAAO,CAAC,IAAI,CAAC,CAAA;AACf,aAAC,CAAA;AACD,YAAA,OAAO,CAAC,eAAe,GAAG,MAAK;gBAC7B,QAAQ,GAAG,KAAK,CAAA;AAClB,aAAC,CAAA;AAED,YAAA,OAAO,CAAC,OAAO,GAAG,MAAK;;AACrB,gBAAA,MAAM,CAAC,CAAA,CAAA,EAAA,GAAA,OAAO,CAAC,KAAK,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,KAAI,EAAE,CAAC,CAAA;AACtC,aAAC,CAAA;SACF;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAA;SACd;AACH,KAAC,CAAC,CAAA;AACJ,CAAA;AC5NA;;;;;;;;;;;;;;;AAeG;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCG;AAMH,MAAM,UAAU,GAAG,eAAe,CAAA;AAUlC;AACA;AACM,MAAO,aAAsB,SAAA,KAAK,CAAA;AAItC,IAAA,WAAA;;AAEW,IAAA,IAAY,EACrB,OAAe;;IAER,UAAoC,EAAA;QAE3C,KAAK,CAAC,OAAO,CAAC,CAAA;AALL,QAAA,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAA;AAGN,QAAA,IAAU,CAAA,UAAA,GAAV,UAAU,CAAA;;AAPV,QAAA,IAAI,CAAA,IAAA,GAAW,UAAU,CAAA;;;;;QAehC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,aAAa,CAAC,SAAS,CAAC,CAAA;;;AAIpD,QAAA,IAAI,KAAK,CAAC,iBAAiB,EAAE;YAC3B,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;SAC7D;KACF;AACF,CAAA;AAEY,MAAA,YAAY,CAAA;AAIvB,IAAA,WAAA,CACmB,OAAe,EACf,WAAmB,EACnB,MAA2B,EAAA;AAF3B,QAAA,IAAO,CAAA,OAAA,GAAP,OAAO,CAAA;AACP,QAAA,IAAW,CAAA,WAAA,GAAX,WAAW,CAAA;AACX,QAAA,IAAM,CAAA,MAAA,GAAN,MAAM,CAAA;KACrB;AAEJ,IAAA,MAAM,CACJ,IAAO,EACP,GAAG,IAAyD,EAAA;QAE5D,MAAM,UAAU,GAAI,IAAI,CAAC,CAAC,CAAe,IAAI,EAAE,CAAA;QAC/C,MAAM,QAAQ,GAAG,CAAG,EAAA,IAAI,CAAC,OAAO,CAAA,CAAA,EAAI,IAAI,CAAA,CAAE,CAAA;QAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;AAElC,QAAA,MAAM,OAAO,GAAG,QAAQ,GAAG,eAAe,CAAC,QAAQ,EAAE,UAAU,CAAC,GAAG,OAAO,CAAA;;QAE1E,MAAM,WAAW,GAAG,CAAA,EAAG,IAAI,CAAC,WAAW,CAAA,EAAA,EAAK,OAAO,CAAA,EAAA,EAAK,QAAQ,CAAA,EAAA,CAAI,CAAA;QAEpE,MAAM,KAAK,GAAG,IAAI,aAAa,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC,CAAA;AAElE,QAAA,OAAO,KAAK,CAAA;KACb;AACF,CAAA;AAED,SAAS,eAAe,CAAC,QAAgB,EAAE,IAAe,EAAA;IACxD,OAAO,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,KAAI;AAC1C,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;AACvB,QAAA,OAAO,KAAK,IAAI,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAI,CAAA,EAAA,GAAG,IAAI,CAAA;AACpD,KAAC,CAAC,CAAA;AACJ,CAAA;AAEA,MAAM,OAAO,GAAG,eAAe,CAAA;AChF/B;;AAEG;AACa,SAAA,SAAS,CAAC,CAAS,EAAE,CAAS,EAAA;AAC5C,IAAA,IAAI,CAAC,KAAK,CAAC,EAAE;AACX,QAAA,OAAO,IAAI,CAAA;KACZ;IAED,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IAC5B,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AAC5B,IAAA,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE;QACrB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;AACtB,YAAA,OAAO,KAAK,CAAA;SACb;AAED,QAAA,MAAM,KAAK,GAAI,CAA6B,CAAC,CAAC,CAAC,CAAA;AAC/C,QAAA,MAAM,KAAK,GAAI,CAA6B,CAAC,CAAC,CAAC,CAAA;QAC/C,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;YACtC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;AAC5B,gBAAA,OAAO,KAAK,CAAA;aACb;SACF;AAAM,aAAA,IAAI,KAAK,KAAK,KAAK,EAAE;AAC1B,YAAA,OAAO,KAAK,CAAA;SACb;KACF;AAED,IAAA,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE;QACrB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;AACtB,YAAA,OAAO,KAAK,CAAA;SACb;KACF;AACD,IAAA,OAAO,IAAI,CAAA;AACb,CAAA;AAEA,SAAS,QAAQ,CAAC,KAAc,EAAA;IAC9B,OAAO,KAAK,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAA;AACpD;;AClEA;;AAEG;AACU,MAAA,SAAS,CAAA;AAWpB;;;;;AAKG;AACH,IAAA,WAAA,CACW,IAAO,EACP,eAAmC,EACnC,IAAmB,EAAA;AAFnB,QAAA,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAG;AACP,QAAA,IAAe,CAAA,eAAA,GAAf,eAAe,CAAoB;AACnC,QAAA,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAe;AAnB9B,QAAA,IAAiB,CAAA,iBAAA,GAAG,KAAK,CAAC;AAC1B;;AAEG;AACH,QAAA,IAAY,CAAA,YAAA,GAAe,EAAE,CAAC;AAE9B,QAAA,IAAA,CAAA,iBAAiB,GAA0B,MAAA,8BAAA;AAE3C,QAAA,IAAiB,CAAA,iBAAA,GAAwC,IAAI,CAAC;KAY1D;AAEJ,IAAA,oBAAoB,CAAC,IAAuB,EAAA;AAC1C,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAC9B,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,oBAAoB,CAAC,iBAA0B,EAAA;AAC7C,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC3C,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,eAAe,CAAC,KAAiB,EAAA;AAC/B,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC1B,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,0BAA0B,CAAC,QAAsC,EAAA;AAC/D,QAAA,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC;AAClC,QAAA,OAAO,IAAI,CAAC;KACb;AACF,CAAA;ACtED;;;;;;;;;;;;;;;AAeG;AAEI,MAAMC,oBAAkB,GAAG,WAAW,CAAA;ACjB7C;;;;;;;;;;;;;;;AAeG;AAcH;;;AAGG;AACU,MAAA,QAAQ,CAAA;IAWnB,WACmB,CAAA,IAAO,EACP,SAA6B,EAAA;AAD7B,QAAA,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAG;AACP,QAAA,IAAS,CAAA,SAAA,GAAT,SAAS,CAAoB;AAZxC,QAAA,IAAS,CAAA,SAAA,GAAwB,IAAI,CAAC;AAC7B,QAAA,IAAA,CAAA,SAAS,GAAuC,IAAI,GAAG,EAAE,CAAC;AAC1D,QAAA,IAAA,CAAA,iBAAiB,GAG9B,IAAI,GAAG,EAAE,CAAC;AACG,QAAA,IAAA,CAAA,gBAAgB,GAC/B,IAAI,GAAG,EAAE,CAAC;AACJ,QAAA,IAAA,CAAA,eAAe,GAAwC,IAAI,GAAG,EAAE,CAAC;KAKrE;AAEJ;;;AAGG;AACH,IAAA,GAAG,CAAC,UAAmB,EAAA;;QAErB,MAAM,oBAAoB,GAAG,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,CAAC;QAE1E,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE;AACrD,YAAA,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAyB,CAAC;YACvD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;AAE3D,YAAA,IACE,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC;AACxC,gBAAA,IAAI,CAAC,oBAAoB,EAAE,EAC3B;;AAEA,gBAAA,IAAI;AACF,oBAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAC3C,wBAAA,kBAAkB,EAAE,oBAAoB;AACzC,qBAAA,CAAC,CAAC;oBACH,IAAI,QAAQ,EAAE;AACZ,wBAAA,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;qBAC5B;iBACF;gBAAC,OAAO,CAAC,EAAE;;;iBAGX;aACF;SACF;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,oBAAoB,CAAE,CAAC,OAAO,CAAC;KAClE;AAkBD,IAAA,YAAY,CAAC,OAGZ,EAAA;;;QAEC,MAAM,oBAAoB,GAAG,IAAI,CAAC,2BAA2B,CAC3D,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,UAAU,CACpB,CAAC;AACF,QAAA,MAAM,QAAQ,GAAG,CAAA,EAAA,GAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,QAAQ,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,KAAK,CAAC;AAE5C,QAAA,IACE,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC;AACxC,YAAA,IAAI,CAAC,oBAAoB,EAAE,EAC3B;AACA,YAAA,IAAI;gBACF,OAAO,IAAI,CAAC,sBAAsB,CAAC;AACjC,oBAAA,kBAAkB,EAAE,oBAAoB;AACzC,iBAAA,CAAC,CAAC;aACJ;YAAC,OAAO,CAAC,EAAE;gBACV,IAAI,QAAQ,EAAE;AACZ,oBAAA,OAAO,IAAI,CAAC;iBACb;qBAAM;AACL,oBAAA,MAAM,CAAC,CAAC;iBACT;aACF;SACF;aAAM;;YAEL,IAAI,QAAQ,EAAE;AACZ,gBAAA,OAAO,IAAI,CAAC;aACb;iBAAM;gBACL,MAAM,KAAK,CAAC,CAAW,QAAA,EAAA,IAAI,CAAC,IAAI,CAAA,iBAAA,CAAmB,CAAC,CAAC;aACtD;SACF;KACF;IAED,YAAY,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;AAED,IAAA,YAAY,CAAC,SAAuB,EAAA;QAClC,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;AAChC,YAAA,MAAM,KAAK,CACT,CAAyB,sBAAA,EAAA,SAAS,CAAC,IAAI,CAAiB,cAAA,EAAA,IAAI,CAAC,IAAI,CAAG,CAAA,CAAA,CACrE,CAAC;SACH;AAED,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,KAAK,CAAC,CAAiB,cAAA,EAAA,IAAI,CAAC,IAAI,CAAA,0BAAA,CAA4B,CAAC,CAAC;SACrE;AAED,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;;AAG3B,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE;YAChC,OAAO;SACR;;AAGD,QAAA,IAAI,gBAAgB,CAAC,SAAS,CAAC,EAAE;AAC/B,YAAA,IAAI;gBACF,IAAI,CAAC,sBAAsB,CAAC,EAAE,kBAAkB,EAAEA,oBAAkB,EAAE,CAAC,CAAC;aACzE;YAAC,OAAO,CAAC,EAAE;;;;;aAKX;SACF;;;;AAKD,QAAA,KAAK,MAAM,CACT,kBAAkB,EAClB,gBAAgB,CACjB,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE;YACrC,MAAM,oBAAoB,GACxB,IAAI,CAAC,2BAA2B,CAAC,kBAAkB,CAAC,CAAC;AAEvD,YAAA,IAAI;;AAEF,gBAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAC3C,oBAAA,kBAAkB,EAAE,oBAAoB;AACzC,iBAAA,CAAE,CAAC;AACJ,gBAAA,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;aACpC;YAAC,OAAO,CAAC,EAAE;;;aAGX;SACF;KACF;IAED,aAAa,CAAC,UAAqB,GAAAA,oBAAkB,EAAA;AACnD,QAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AAC1C,QAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AACzC,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;KACnC;;;AAID,IAAA,MAAM,MAAM,GAAA;AACV,QAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;QAErD,MAAM,OAAO,CAAC,GAAG,CAAC;AAChB,YAAA,GAAG,QAAQ;iBACR,MAAM,CAAC,OAAO,IAAI,UAAU,IAAI,OAAO,CAAC;;iBAExC,GAAG,CAAC,OAAO,IAAK,OAAe,CAAC,QAAS,CAAC,MAAM,EAAE,CAAC;AACtD,YAAA,GAAG,QAAQ;iBACR,MAAM,CAAC,OAAO,IAAI,SAAS,IAAI,OAAO,CAAC;;iBAEvC,GAAG,CAAC,OAAO,IAAK,OAAe,CAAC,OAAO,EAAE,CAAC;AAC9C,SAAA,CAAC,CAAC;KACJ;IAED,cAAc,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC;KAC/B;IAED,aAAa,CAAC,UAAqB,GAAAA,oBAAkB,EAAA;QACnD,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;KACvC;IAED,UAAU,CAAC,UAAqB,GAAAA,oBAAkB,EAAA;QAChD,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;KACpD;IAED,UAAU,CAAC,IAA0B,GAAA,EAAE,EAAA;AACrC,QAAA,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC;QAC9B,MAAM,oBAAoB,GAAG,IAAI,CAAC,2BAA2B,CAC3D,IAAI,CAAC,kBAAkB,CACxB,CAAC;AACF,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,EAAE;YAC5C,MAAM,KAAK,CACT,CAAA,EAAG,IAAI,CAAC,IAAI,CAAI,CAAA,EAAA,oBAAoB,CAAgC,8BAAA,CAAA,CACrE,CAAC;SACH;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE;YAC1B,MAAM,KAAK,CAAC,CAAa,UAAA,EAAA,IAAI,CAAC,IAAI,CAAA,4BAAA,CAA8B,CAAC,CAAC;SACnE;AAED,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAC3C,YAAA,kBAAkB,EAAE,oBAAoB;YACxC,OAAO;AACR,SAAA,CAAE,CAAC;;AAGJ,QAAA,KAAK,MAAM,CACT,kBAAkB,EAClB,gBAAgB,CACjB,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE;YACrC,MAAM,4BAA4B,GAChC,IAAI,CAAC,2BAA2B,CAAC,kBAAkB,CAAC,CAAC;AACvD,YAAA,IAAI,oBAAoB,KAAK,4BAA4B,EAAE;AACzD,gBAAA,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;aACpC;SACF;AAED,QAAA,OAAO,QAAQ,CAAC;KACjB;AAED;;;;;;;AAOG;IACH,MAAM,CAAC,QAA2B,EAAE,UAAmB,EAAA;;QACrD,MAAM,oBAAoB,GAAG,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,CAAC;AAC1E,QAAA,MAAM,iBAAiB,GACrB,CAAA,EAAA,GAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,oBAAoB,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAC9C,IAAI,GAAG,EAAqB,CAAC;AAC/B,QAAA,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAChC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,oBAAoB,EAAE,iBAAiB,CAAC,CAAC;QAElE,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;QAClE,IAAI,gBAAgB,EAAE;AACpB,YAAA,QAAQ,CAAC,gBAAgB,EAAE,oBAAoB,CAAC,CAAC;SAClD;AAED,QAAA,OAAO,MAAK;AACV,YAAA,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACrC,SAAC,CAAC;KACH;AAED;;;AAGG;IACK,qBAAqB,CAC3B,QAA+B,EAC/B,UAAkB,EAAA;QAElB,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,EAAE;YACd,OAAO;SACR;AACD,QAAA,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;AAChC,YAAA,IAAI;AACF,gBAAA,QAAQ,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;aAChC;YAAC,OAAA,EAAA,EAAM;;aAEP;SACF;KACF;AAEO,IAAA,sBAAsB,CAAC,EAC7B,kBAAkB,EAClB,OAAO,GAAG,EAAE,EAIb,EAAA;QACC,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AACtD,QAAA,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YAC/B,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE;AACxD,gBAAA,kBAAkB,EAAE,6BAA6B,CAAC,kBAAkB,CAAC;gBACrE,OAAO;AACR,aAAA,CAAC,CAAC;YACH,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,EAAE,QAAS,CAAC,CAAC;YAClD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;AAEvD;;;;AAIG;AACH,YAAA,IAAI,CAAC,qBAAqB,CAAC,QAAS,EAAE,kBAAkB,CAAC,CAAC;AAE1D;;;;AAIG;AACH,YAAA,IAAI,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE;AACpC,gBAAA,IAAI;AACF,oBAAA,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC9B,IAAI,CAAC,SAAS,EACd,kBAAkB,EAClB,QAAS,CACV,CAAC;iBACH;gBAAC,OAAA,EAAA,EAAM;;iBAEP;aACF;SACF;QAED,OAAO,QAAQ,IAAI,IAAI,CAAC;KACzB;IAEO,2BAA2B,CACjC,UAAqB,GAAAA,oBAAkB,EAAA;AAEvC,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,GAAG,UAAU,GAAGA,oBAAkB,CAAC;SAC3E;aAAM;YACL,OAAO,UAAU,CAAC;SACnB;KACF;IAEO,oBAAoB,GAAA;AAC1B,QAAA,QACE,CAAC,CAAC,IAAI,CAAC,SAAS;YAChB,IAAI,CAAC,SAAS,CAAC,iBAAiB,KAAA,UAAA,mCAChC;KACH;AACF,CAAA;AAED;AACA,SAAS,6BAA6B,CAAC,UAAkB,EAAA;IACvD,OAAO,UAAU,KAAKA,oBAAkB,GAAG,SAAS,GAAG,UAAU,CAAC;AACpE,CAAC;AAED,SAAS,gBAAgB,CAAiB,SAAuB,EAAA;AAC/D,IAAA,OAAO,SAAS,CAAC,iBAAiB,KAAA,OAAA,+BAA6B;AACjE,CAAA;ACzXA;;;;;;;;;;;;;;;AAeG;AAMH;;AAEG;AACU,MAAA,kBAAkB,CAAA;AAG7B,IAAA,WAAA,CAA6B,IAAY,EAAA;AAAZ,QAAA,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;AAFxB,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,GAAG,EAA0B,CAAC;KAElB;AAE7C;;;;;;;;AAQG;AACH,IAAA,YAAY,CAAiB,SAAuB,EAAA;QAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAClD,QAAA,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE;AAC7B,YAAA,MAAM,IAAI,KAAK,CACb,CAAA,UAAA,EAAa,SAAS,CAAC,IAAI,CAAA,kCAAA,EAAqC,IAAI,CAAC,IAAI,CAAA,CAAE,CAC5E,CAAC;SACH;AAED,QAAA,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;KAClC;AAED,IAAA,uBAAuB,CAAiB,SAAuB,EAAA;QAC7D,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAClD,QAAA,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE;;YAE7B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;SACvC;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;KAC9B;AAED;;;;;;AAMG;AACH,IAAA,WAAW,CAAiB,IAAO,EAAA;QACjC,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAA2B,CAAC;SAC3D;;QAGD,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAI,IAAI,EAAE,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,QAAqC,CAAC,CAAC;AAEhE,QAAA,OAAO,QAAuB,CAAC;KAChC;IAED,YAAY,GAAA;QACV,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;KAC5C;AACF;;ACjFD;;;;;;;;;;;;;;;AAeG;AAuBH;;AAEG;AACI,MAAM,SAAS,GAAa,EAAE,CAAC;AAEtC;;;;;;;;;;AAUG;IACS,QAOX,CAAA;AAPD,CAAA,UAAY,QAAQ,EAAA;IAClB,QAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;IACL,QAAA,CAAA,QAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;IACP,QAAA,CAAA,QAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;IACJ,QAAA,CAAA,QAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;IACJ,QAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;IACL,QAAA,CAAA,QAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;AACR,CAAC,EAPW,QAAQ,KAAR,QAAQ,GAOnB,EAAA,CAAA,CAAA,CAAA;AAED,MAAM,iBAAiB,GAA0C;IAC/D,OAAO,EAAE,QAAQ,CAAC,KAAK;IACvB,SAAS,EAAE,QAAQ,CAAC,OAAO;IAC3B,MAAM,EAAE,QAAQ,CAAC,IAAI;IACrB,MAAM,EAAE,QAAQ,CAAC,IAAI;IACrB,OAAO,EAAE,QAAQ,CAAC,KAAK;IACvB,QAAQ,EAAE,QAAQ,CAAC,MAAM;CAC1B,CAAC;AAEF;;AAEG;AACH,MAAM,eAAe,GAAa,QAAQ,CAAC,IAAI,CAAC;AAahD;;;;;AAKG;AACH,MAAM,aAAa,GAAG;AACpB,IAAA,CAAC,QAAQ,CAAC,KAAK,GAAG,KAAK;AACvB,IAAA,CAAC,QAAQ,CAAC,OAAO,GAAG,KAAK;AACzB,IAAA,CAAC,QAAQ,CAAC,IAAI,GAAG,MAAM;AACvB,IAAA,CAAC,QAAQ,CAAC,IAAI,GAAG,MAAM;AACvB,IAAA,CAAC,QAAQ,CAAC,KAAK,GAAG,OAAO;CAC1B,CAAC;AAEF;;;;AAIG;AACH,MAAM,iBAAiB,GAAe,CAAC,QAAQ,EAAE,OAAO,EAAE,GAAG,IAAI,KAAU;AACzE,IAAA,IAAI,OAAO,GAAG,QAAQ,CAAC,QAAQ,EAAE;QAC/B,OAAO;KACR;IACD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;AACrC,IAAA,MAAM,MAAM,GAAG,aAAa,CAAC,OAAqC,CAAC,CAAC;IACpE,IAAI,MAAM,EAAE;AACV,QAAA,OAAO,CAAC,MAA2C,CAAC,CAClD,IAAI,GAAG,CAAA,GAAA,EAAM,QAAQ,CAAC,IAAI,CAAG,CAAA,CAAA,EAC7B,GAAG,IAAI,CACR,CAAC;KACH;SAAM;AACL,QAAA,MAAM,IAAI,KAAK,CACb,8DAA8D,OAAO,CAAA,CAAA,CAAG,CACzE,CAAC;KACH;AACH,CAAC,CAAC;AAEW,MAAA,MAAM,CAAA;AACjB;;;;;AAKG;AACH,IAAA,WAAA,CAAmB,IAAY,EAAA;AAAZ,QAAA,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;AAO/B;;AAEG;AACK,QAAA,IAAS,CAAA,SAAA,GAAG,eAAe,CAAC;AAkBpC;;;AAGG;AACK,QAAA,IAAW,CAAA,WAAA,GAAe,iBAAiB,CAAC;AAWpD;;AAEG;AACK,QAAA,IAAe,CAAA,eAAA,GAAsB,IAAI,CAAC;AA7ChD;;AAEG;AACH,QAAA,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACtB;AAOD,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IAED,IAAI,QAAQ,CAAC,GAAa,EAAA;AACxB,QAAA,IAAI,EAAE,GAAG,IAAI,QAAQ,CAAC,EAAE;AACtB,YAAA,MAAM,IAAI,SAAS,CAAC,kBAAkB,GAAG,CAAA,0BAAA,CAA4B,CAAC,CAAC;SACxE;AACD,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;KACtB;;AAGD,IAAA,WAAW,CAAC,GAA8B,EAAA;AACxC,QAAA,IAAI,CAAC,SAAS,GAAG,OAAO,GAAG,KAAK,QAAQ,GAAG,iBAAiB,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;KACzE;AAOD,IAAA,IAAI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;IACD,IAAI,UAAU,CAAC,GAAe,EAAA;AAC5B,QAAA,IAAI,OAAO,GAAG,KAAK,UAAU,EAAE;AAC7B,YAAA,MAAM,IAAI,SAAS,CAAC,mDAAmD,CAAC,CAAC;SAC1E;AACD,QAAA,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;KACxB;AAMD,IAAA,IAAI,cAAc,GAAA;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC;KAC7B;IACD,IAAI,cAAc,CAAC,GAAsB,EAAA;AACvC,QAAA,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC;KAC5B;AAED;;AAEG;IAEH,KAAK,CAAC,GAAG,IAAe,EAAA;AACtB,QAAA,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;AAC5E,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;KACjD;IACD,GAAG,CAAC,GAAG,IAAe,EAAA;AACpB,QAAA,IAAI,CAAC,eAAe;AAClB,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;AACxD,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;KACnD;IACD,IAAI,CAAC,GAAG,IAAe,EAAA;AACrB,QAAA,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;AAC3E,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;KAChD;IACD,IAAI,CAAC,GAAG,IAAe,EAAA;AACrB,QAAA,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;AAC3E,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;KAChD;IACD,KAAK,CAAC,GAAG,IAAe,EAAA;AACtB,QAAA,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;AAC5E,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;KACjD;AACF,CAAA;AAEK,SAAUC,aAAW,CAAC,KAAgC,EAAA;AAC1D,IAAA,SAAS,CAAC,OAAO,CAAC,IAAI,IAAG;AACvB,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC1B,KAAC,CAAC,CAAC;AACL,CAAC;AAEe,SAAA,iBAAiB,CAC/B,WAA+B,EAC/B,OAAoB,EAAA;AAEpB,IAAA,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;QAChC,IAAI,cAAc,GAAoB,IAAI,CAAC;AAC3C,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE;AAC5B,YAAA,cAAc,GAAG,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACnD;AACD,QAAA,IAAI,WAAW,KAAK,IAAI,EAAE;AACxB,YAAA,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC;SAChC;aAAM;YACL,QAAQ,CAAC,cAAc,GAAG,CACxB,QAAgB,EAChB,KAAe,EACf,GAAG,IAAe,KAChB;gBACF,MAAM,OAAO,GAAG,IAAI;qBACjB,GAAG,CAAC,GAAG,IAAG;AACT,oBAAA,IAAI,GAAG,IAAI,IAAI,EAAE;AACf,wBAAA,OAAO,IAAI,CAAC;qBACb;AAAM,yBAAA,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AAClC,wBAAA,OAAO,GAAG,CAAC;qBACZ;yBAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,GAAG,KAAK,SAAS,EAAE;AAC9D,wBAAA,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;qBACvB;AAAM,yBAAA,IAAI,GAAG,YAAY,KAAK,EAAE;wBAC/B,OAAO,GAAG,CAAC,OAAO,CAAC;qBACpB;yBAAM;AACL,wBAAA,IAAI;AACF,4BAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;yBAC5B;wBAAC,OAAO,OAAO,EAAE;AAChB,4BAAA,OAAO,IAAI,CAAC;yBACb;qBACF;AACH,iBAAC,CAAC;AACD,qBAAA,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC;qBAClB,IAAI,CAAC,GAAG,CAAC,CAAC;gBACb,IAAI,KAAK,KAAK,cAAc,KAAA,IAAA,IAAd,cAAc,KAAA,KAAA,CAAA,GAAd,cAAc,GAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE;AAClD,oBAAA,WAAW,CAAC;AACV,wBAAA,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,WAAW,EAAoB;wBACtD,OAAO;wBACP,IAAI;wBACJ,IAAI,EAAE,QAAQ,CAAC,IAAI;AACpB,qBAAA,CAAC,CAAC;iBACJ;AACH,aAAC,CAAC;SACH;KACF;AACH;;AC3QA,MAAM,aAAa,GAAG,CAAC,MAAM,EAAE,YAAY,KAAK,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,MAAM,YAAY,CAAC,CAAC,CAAC;AAE9F,IAAI,iBAAiB,CAAC;AACtB,IAAI,oBAAoB,CAAC;AACzB;AACA,SAAS,oBAAoB,GAAA;AACzB,IAAA,QAAQ,iBAAiB;AACrB,SAAC,iBAAiB,GAAG;YACjB,WAAW;YACX,cAAc;YACd,QAAQ;YACR,SAAS;YACT,cAAc;AACjB,SAAA,CAAC,EAAE;AACZ,CAAC;AACD;AACA,SAAS,uBAAuB,GAAA;AAC5B,IAAA,QAAQ,oBAAoB;AACxB,SAAC,oBAAoB,GAAG;YACpB,SAAS,CAAC,SAAS,CAAC,OAAO;YAC3B,SAAS,CAAC,SAAS,CAAC,QAAQ;YAC5B,SAAS,CAAC,SAAS,CAAC,kBAAkB;AACzC,SAAA,CAAC,EAAE;AACZ,CAAC;AACD,MAAM,gBAAgB,GAAG,IAAI,OAAO,EAAE,CAAC;AACvC,MAAM,kBAAkB,GAAG,IAAI,OAAO,EAAE,CAAC;AACzC,MAAM,wBAAwB,GAAG,IAAI,OAAO,EAAE,CAAC;AAC/C,MAAM,cAAc,GAAG,IAAI,OAAO,EAAE,CAAC;AACrC,MAAM,qBAAqB,GAAG,IAAI,OAAO,EAAE,CAAC;AAC5C,SAAS,gBAAgB,CAAC,OAAO,EAAA;IAC7B,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;QAC5C,MAAM,QAAQ,GAAG,MAAK;AAClB,YAAA,OAAO,CAAC,mBAAmB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AAChD,YAAA,OAAO,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAChD,SAAC,CAAC;QACF,MAAM,OAAO,GAAG,MAAK;YACjB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AAC9B,YAAA,QAAQ,EAAE,CAAC;AACf,SAAC,CAAC;QACF,MAAM,KAAK,GAAG,MAAK;AACf,YAAA,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACtB,YAAA,QAAQ,EAAE,CAAC;AACf,SAAC,CAAC;AACF,QAAA,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AAC7C,QAAA,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC7C,KAAC,CAAC,CAAC;IACH,OAAO;AACF,SAAA,IAAI,CAAC,CAAC,KAAK,KAAI;;;AAGhB,QAAA,IAAI,KAAK,YAAY,SAAS,EAAE;AAC5B,YAAA,gBAAgB,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;SACxC;;AAEL,KAAC,CAAC;AACG,SAAA,KAAK,CAAC,MAAQ,GAAC,CAAC,CAAC;;;AAGtB,IAAA,qBAAqB,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC5C,IAAA,OAAO,OAAO,CAAC;AACnB,CAAC;AACD,SAAS,8BAA8B,CAAC,EAAE,EAAA;;AAEtC,IAAA,IAAI,kBAAkB,CAAC,GAAG,CAAC,EAAE,CAAC;QAC1B,OAAO;IACX,MAAM,IAAI,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;QACzC,MAAM,QAAQ,GAAG,MAAK;AAClB,YAAA,EAAE,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;AAC7C,YAAA,EAAE,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACvC,YAAA,EAAE,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC3C,SAAC,CAAC;QACF,MAAM,QAAQ,GAAG,MAAK;AAClB,YAAA,OAAO,EAAE,CAAC;AACV,YAAA,QAAQ,EAAE,CAAC;AACf,SAAC,CAAC;QACF,MAAM,KAAK,GAAG,MAAK;AACf,YAAA,MAAM,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC,CAAC;AACjE,YAAA,QAAQ,EAAE,CAAC;AACf,SAAC,CAAC;AACF,QAAA,EAAE,CAAC,gBAAgB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;AAC1C,QAAA,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACpC,QAAA,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACxC,KAAC,CAAC,CAAC;;AAEH,IAAA,kBAAkB,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AACrC,CAAC;AACD,IAAI,aAAa,GAAG;AAChB,IAAA,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAA;AACtB,QAAA,IAAI,MAAM,YAAY,cAAc,EAAE;;YAElC,IAAI,IAAI,KAAK,MAAM;AACf,gBAAA,OAAO,kBAAkB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;;AAE1C,YAAA,IAAI,IAAI,KAAK,kBAAkB,EAAE;gBAC7B,OAAO,MAAM,CAAC,gBAAgB,IAAI,wBAAwB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;aAC1E;;AAED,YAAA,IAAI,IAAI,KAAK,OAAO,EAAE;AAClB,gBAAA,OAAO,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC/B,sBAAE,SAAS;AACX,sBAAE,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;aAC5D;SACJ;;AAED,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;KAC7B;AACD,IAAA,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAA;AACnB,QAAA,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;AACrB,QAAA,OAAO,IAAI,CAAC;KACf;IACD,GAAG,CAAC,MAAM,EAAE,IAAI,EAAA;QACZ,IAAI,MAAM,YAAY,cAAc;aAC/B,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,OAAO,CAAC,EAAE;AACvC,YAAA,OAAO,IAAI,CAAC;SACf;QACD,OAAO,IAAI,IAAI,MAAM,CAAC;KACzB;CACJ,CAAC;AACF,SAAS,YAAY,CAAC,QAAQ,EAAA;AAC1B,IAAA,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC;AAC5C,CAAC;AACD,SAAS,YAAY,CAAC,IAAI,EAAA;;;;AAItB,IAAA,IAAI,IAAI,KAAK,WAAW,CAAC,SAAS,CAAC,WAAW;QAC1C,EAAE,kBAAkB,IAAI,cAAc,CAAC,SAAS,CAAC,EAAE;AACnD,QAAA,OAAO,UAAU,UAAU,EAAE,GAAG,IAAI,EAAA;AAChC,YAAA,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,CAAC;YACxD,wBAAwB,CAAC,GAAG,CAAC,EAAE,EAAE,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;AACrF,YAAA,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;AACpB,SAAC,CAAC;KACL;;;;;;IAMD,IAAI,uBAAuB,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC1C,OAAO,UAAU,GAAG,IAAI,EAAA;;;YAGpB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;YAC/B,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5C,SAAC,CAAC;KACL;IACD,OAAO,UAAU,GAAG,IAAI,EAAA;;;AAGpB,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AAChD,KAAC,CAAC;AACN,CAAC;AACD,SAAS,sBAAsB,CAAC,KAAK,EAAA;IACjC,IAAI,OAAO,KAAK,KAAK,UAAU;AAC3B,QAAA,OAAO,YAAY,CAAC,KAAK,CAAC,CAAC;;;IAG/B,IAAI,KAAK,YAAY,cAAc;QAC/B,8BAA8B,CAAC,KAAK,CAAC,CAAC;AAC1C,IAAA,IAAI,aAAa,CAAC,KAAK,EAAE,oBAAoB,EAAE,CAAC;AAC5C,QAAA,OAAO,IAAI,KAAK,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;;AAE3C,IAAA,OAAO,KAAK,CAAC;AACjB,CAAC;AACD,SAAS,IAAI,CAAC,KAAK,EAAA;;;IAGf,IAAI,KAAK,YAAY,UAAU;AAC3B,QAAA,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC;;;AAGnC,IAAA,IAAI,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC;AACzB,QAAA,OAAO,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACrC,IAAA,MAAM,QAAQ,GAAG,sBAAsB,CAAC,KAAK,CAAC,CAAC;;;AAG/C,IAAA,IAAI,QAAQ,KAAK,KAAK,EAAE;AACpB,QAAA,cAAc,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AACpC,QAAA,qBAAqB,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;KAC9C;AACD,IAAA,OAAO,QAAQ,CAAC;AACpB,CAAC;AACD,MAAM,MAAM,GAAG,CAAC,KAAK,KAAK,qBAAqB,CAAC,GAAG,CAAC,KAAK,CAAC;;ACnL1D;;;;;;AAMG;AACH,SAAS,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,EAAE,EAAA;IAC1E,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC9C,IAAA,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;IAClC,IAAI,OAAO,EAAE;QACT,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,CAAC,KAAK,KAAI;YAChD,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,KAAK,CAAC,CAAC;AACxG,SAAC,CAAC,CAAC;KACN;IACD,IAAI,OAAO,EAAE;QACT,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,KAAK,KAAK,OAAO;;QAEtD,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;KAC/C;IACD,WAAW;AACN,SAAA,IAAI,CAAC,CAAC,EAAE,KAAI;AACb,QAAA,IAAI,UAAU;YACV,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM,UAAU,EAAE,CAAC,CAAC;QACrD,IAAI,QAAQ,EAAE;YACV,EAAE,CAAC,gBAAgB,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;SACxG;AACL,KAAC,CAAC;AACG,SAAA,KAAK,CAAC,MAAQ,GAAC,CAAC,CAAC;AACtB,IAAA,OAAO,WAAW,CAAC;AACvB,CAAC;AAgBD,MAAM,WAAW,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;AACvE,MAAM,YAAY,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AACvD,MAAM,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;AAChC,SAAS,SAAS,CAAC,MAAM,EAAE,IAAI,EAAA;AAC3B,IAAA,IAAI,EAAE,MAAM,YAAY,WAAW;AAC/B,QAAA,EAAE,IAAI,IAAI,MAAM,CAAC;AACjB,QAAA,OAAO,IAAI,KAAK,QAAQ,CAAC,EAAE;QAC3B,OAAO;KACV;AACD,IAAA,IAAI,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC;AACvB,QAAA,OAAO,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACnC,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;AACtD,IAAA,MAAM,QAAQ,GAAG,IAAI,KAAK,cAAc,CAAC;IACzC,MAAM,OAAO,GAAG,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;AACtD,IAAA;;AAEA,IAAA,EAAE,cAAc,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAG,cAAc,EAAE,SAAS,CAAC;QACjE,EAAE,OAAO,IAAI,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,EAAE;QACpD,OAAO;KACV;AACD,IAAA,MAAM,MAAM,GAAG,gBAAgB,SAAS,EAAE,GAAG,IAAI,EAAA;;AAE7C,QAAA,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,GAAG,WAAW,GAAG,UAAU,CAAC,CAAC;AAC3E,QAAA,IAAI,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC;AACtB,QAAA,IAAI,QAAQ;YACR,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;;;;;;AAMxC,QAAA,OAAO,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC;AACtB,YAAA,MAAM,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC;YAC/B,OAAO,IAAI,EAAE,CAAC,IAAI;AACrB,SAAA,CAAC,EAAE,CAAC,CAAC,CAAC;AACX,KAAC,CAAC;AACF,IAAA,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAChC,IAAA,OAAO,MAAM,CAAC;AAClB,CAAC;AACD,YAAY,CAAC,CAAC,QAAQ,MACf,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,QAAQ,CACX,EAAA,EAAA,GAAG,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,KAAK,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,EAChG,GAAG,EAAE,CAAC,MAAM,EAAE,IAAI,KAAK,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,EAAA,CAAA,CAChF,CAAC;;AC5FH;;;;;;;;;;;;;;;AAeG;AAUU,MAAA,yBAAyB,CAAA;AACpC,IAAA,WAAA,CAA6B,SAA6B,EAAA;AAA7B,QAAA,IAAS,CAAA,SAAA,GAAT,SAAS,CAAoB;KAAI;;;IAG9D,qBAAqB,GAAA;QACnB,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;;;AAGhD,QAAA,OAAO,SAAS;aACb,GAAG,CAAC,QAAQ,IAAG;AACd,YAAA,IAAI,wBAAwB,CAAC,QAAQ,CAAC,EAAE;AACtC,gBAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,YAAY,EAAoB,CAAC;gBAC1D,OAAO,CAAA,EAAG,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAA,CAAE,CAAC;aAChD;iBAAM;AACL,gBAAA,OAAO,IAAI,CAAC;aACb;AACH,SAAC,CAAC;AACD,aAAA,MAAM,CAAC,SAAS,IAAI,SAAS,CAAC;aAC9B,IAAI,CAAC,GAAG,CAAC,CAAC;KACd;AACF,CAAA;AACD;;;;;;;AAOG;AACH,SAAS,wBAAwB,CAAC,QAAwB,EAAA;AACxD,IAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;IAC1C,OAAO,CAAA,SAAS,KAAT,IAAA,IAAA,SAAS,KAAT,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,SAAS,CAAE,IAAI,MAAA,SAAA,6BAA2B;AACnD,CAAA;;;ACzDA;;;;;;;;;;;;;;;AAeG;AAII,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,eAAe,CAAC,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnBjD;;;;;;;;;;;;;;;AAeG;AA8BH;;;;AAIG;AACU,MAAA,kBAAkB,GAAG,YAAY;AAEvC,MAAM,mBAAmB,GAAG;IACjC,CAACC,MAAO,GAAG,WAAW;IACtB,CAACC,MAAa,GAAG,kBAAkB;IACnC,CAACC,MAAa,GAAG,gBAAgB;IACjC,CAACC,MAAmB,GAAG,uBAAuB;IAC9C,CAACC,MAAY,GAAG,gBAAgB;IAChC,CAACC,MAAkB,GAAG,uBAAuB;IAC7C,CAACC,MAAQ,GAAG,WAAW;IACvB,CAACC,MAAc,GAAG,kBAAkB;IACpC,CAACC,MAAY,GAAG,WAAW;IAC3B,CAACC,MAAe,GAAG,mBAAmB;IACtC,CAACC,MAAkB,GAAG,kBAAkB;IACxC,CAACC,MAAa,GAAG,SAAS;IAC1B,CAACC,MAAmB,GAAG,gBAAgB;IACvC,CAACC,MAAiB,GAAG,UAAU;IAC/B,CAACC,MAAuB,GAAG,iBAAiB;IAC5C,CAACC,MAAa,GAAG,UAAU;IAC3B,CAACC,MAAmB,GAAG,iBAAiB;IACxC,CAACC,MAAe,GAAG,WAAW;IAC9B,CAACC,MAAqB,GAAG,kBAAkB;IAC3C,CAACC,MAAgB,GAAG,SAAS;IAC7B,CAACC,MAAsB,GAAG,gBAAgB;IAC1C,CAACC,MAAW,GAAG,UAAU;IACzB,CAACC,MAAiB,GAAG,iBAAiB;IACtC,CAACC,MAAa,GAAG,UAAU;IAC3B,CAACC,MAAmB,GAAG,iBAAiB;IACxC,CAACC,MAAU,GAAG,aAAa;IAC3B,SAAS,EAAE,SAAS;IACpB,CAACC,MAAW,GAAG,aAAa;CACpB,CAAA;ACjFV;;;;;;;;;;;;;;;AAeG;AAaH;;AAEG;AACU,MAAA,KAAK,GAAG,IAAI,GAAG,GAAwB;AAEpD;;AAEG;AACU,MAAA,WAAW,GAAG,IAAI,GAAG,GAA8B;AAEhE;;;;AAIG;AACH;AACa,MAAA,WAAW,GAAG,IAAI,GAAG,GAA2B;AAE7D;;;;AAIG;AACa,SAAA,aAAa,CAC3B,GAAgB,EAChB,SAAuB,EAAA;AAEvB,IAAA,IAAI;AACD,QAAA,GAAuB,CAAC,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;KAC5D;IAAC,OAAO,CAAC,EAAE;AACV,QAAA,MAAM,CAAC,KAAK,CACV,CAAA,UAAA,EAAa,SAAS,CAAC,IAAI,CAAwC,qCAAA,EAAA,GAAG,CAAC,IAAI,CAAA,CAAE,EAC7E,CAAC,CACF,CAAC;KACH;AACH,CAAC;AAED;;;AAGG;AACa,SAAA,wBAAwB,CACtC,GAAgB,EAChB,SAAoB,EAAA;AAEnB,IAAA,GAAuB,CAAC,SAAS,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;AACxE,CAAC;AAED;;;;;;AAMG;AACG,SAAU,kBAAkB,CAChC,SAAuB,EAAA;AAEvB,IAAA,MAAM,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC;AACrC,IAAA,IAAI,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;AAClC,QAAA,MAAM,CAAC,KAAK,CACV,sDAAsD,aAAa,CAAA,CAAA,CAAG,CACvE,CAAC;AAEF,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,WAAW,CAAC,GAAG,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;;IAG1C,KAAK,MAAM,GAAG,IAAI,KAAK,CAAC,MAAM,EAAE,EAAE;AAChC,QAAA,aAAa,CAAC,GAAsB,EAAE,SAAS,CAAC,CAAC;KAClD;IAED,KAAK,MAAM,SAAS,IAAI,WAAW,CAAC,MAAM,EAAE,EAAE;AAC5C,QAAA,aAAa,CAAC,SAAkC,EAAE,SAAS,CAAC,CAAC;KAC9D;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;;AAQG;AACa,SAAA,YAAY,CAC1B,GAAgB,EAChB,IAAO,EAAA;AAEP,IAAA,MAAM,mBAAmB,GAAI,GAAuB,CAAC,SAAS;SAC3D,WAAW,CAAC,WAAW,CAAC;AACxB,SAAA,YAAY,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;IACpC,IAAI,mBAAmB,EAAE;AACvB,QAAA,KAAK,mBAAmB,CAAC,gBAAgB,EAAE,CAAC;KAC7C;IACD,OAAQ,GAAuB,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC9D,CAAC;AAED;;;;;;;AAOG;AACG,SAAU,sBAAsB,CACpC,GAAgB,EAChB,IAAO,EACP,kBAAA,GAA6B,kBAAkB,EAAA;IAE/C,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;AAC5D,CAAC;AAED;;;;;;;AAOG;AACG,SAAU,cAAc,CAC5B,GAAkC,EAAA;AAElC,IAAA,OAAQ,GAAmB,CAAC,OAAO,KAAK,SAAS,CAAC;AACpD,CAAC;AAED;;;;;;;AAOG;AACG,SAAU,oBAAoB,CAClC,GAAuD,EAAA;IAEvD,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;AACrC,QAAA,OAAO,KAAK,CAAC;KACd;AACD,IAAA,OAAQ,GAAyB,CAAC,QAAQ,KAAK,SAAS,CAAC;AAC3D,CAAC;AAED;;;;AAIG;AACa,SAAA,gBAAgB,GAAA;IAC9B,WAAW,CAAC,KAAK,EAAE,CAAC;AACtB,CAAA;ACzLA;;;;;;;;;;;;;;;AAeG;AAqBH,MAAM,MAAM,GAAuB;AACjC,IAAA,CAAA,QAAA,yBACE,kDAAkD;QAClD,4BAA4B;AAC9B,IAAA,CAAA,cAAA,+BAAyB,gCAAgC;AACzD,IAAA,CAAA,eAAA,gCACE,iFAAiF;AACnF,IAAA,CAAA,aAAA,8BAAwB,iDAAiD;AACzE,IAAA,CAAA,oBAAA,qCAA+B,sCAAsC;AACrE,IAAA,CAAA,YAAA,6BACE,yEAAyE;AAC3E,IAAA,CAAA,sBAAA,uCACE,sDAAsD;QACtD,wBAAwB;AAC1B,IAAA,CAAA,sBAAA,uCACE,uDAAuD;AACzD,IAAA,CAAA,UAAA,2BACE,+EAA+E;AACjF,IAAA,CAAA,SAAA,0BACE,oFAAoF;AACtF,IAAA,CAAA,SAAA,4BACE,kFAAkF;AACpF,IAAA,CAAA,YAAA,6BACE,qFAAqF;AACvF,IAAA,CAAA,qCAAA,sDACE,yGAAyG;AAC3G,IAAA,CAAA,gCAAA,iDACE,2DAA2D;CAC9D,CAAC;AAeK,MAAM,aAAa,GAAG,IAAI,YAAY,CAC3C,KAAK,EACL,UAAU,EACV,MAAM,CACP,CAAA;ACnFD;;;;;;;;;;;;;;;AAeG;AAcU,MAAA,eAAe,CAAA;AAc1B,IAAA,WAAA,CACE,OAAwB,EACxB,MAAqC,EACrC,SAA6B,EAAA;AANrB,QAAA,IAAU,CAAA,UAAA,GAAG,KAAK,CAAC;QAQ3B,IAAI,CAAC,QAAQ,GAAQ,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,OAAO,CAAE,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAQ,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,MAAM,CAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC;AACzB,QAAA,IAAI,CAAC,+BAA+B;YAClC,MAAM,CAAC,8BAA8B,CAAC;AACxC,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,SAAS,CAAC,YAAY,CACzB,IAAI,SAAS,CAAC,KAAK,EAAE,MAAM,IAAI,EAAA,QAAA,4BAAuB,CACvD,CAAC;KACH;AAED,IAAA,IAAI,8BAA8B,GAAA;QAChC,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC,+BAA+B,CAAC;KAC7C;IAED,IAAI,8BAA8B,CAAC,GAAY,EAAA;QAC7C,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,+BAA+B,GAAG,GAAG,CAAC;KAC5C;AAED,IAAA,IAAI,IAAI,GAAA;QACN,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;AAED,IAAA,IAAI,OAAO,GAAA;QACT,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;AAED,IAAA,IAAI,MAAM,GAAA;QACR,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;AAED,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;AAED,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;IAED,IAAI,SAAS,CAAC,GAAY,EAAA;AACxB,QAAA,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;KACvB;AAED;;;AAGG;IACO,cAAc,GAAA;AACtB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,aAAA,6BAAuB,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;SAC3E;KACF;AACF,CAAA;ACzGD;;;;;;;;;;;;;;;AAeG;AAeH;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,WAAmB,EAAE,SAAiB,EAAA;AAC9D,IAAA,MAAM,UAAU,GAAG,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,IAAA,IAAI,UAAU,KAAK,IAAI,EAAE;AACvB,QAAA,OAAO,CAAC,KAAK,CACX,qBAAqB,SAAS,CAAA,6CAAA,CAA+C,CAC9E,CAAC;QACF,OAAO;KACR;IACD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC;AAC5C,IAAA,IAAI,QAAQ,KAAK,SAAS,EAAE;AAC1B,QAAA,OAAO,CAAC,KAAK,CACX,qBAAqB,SAAS,CAAA,iDAAA,CAAmD,CAClF,CAAC;QACF,OAAO;KACR;AACD,IAAA,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;IAC9C,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;AACjC,IAAA,MAAM,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AACvB,IAAA,IAAI,IAAI,IAAI,CAAC,EAAE;AACb,QAAA,OAAO,CAAC,KAAK,CACX,qBAAqB,SAAS,CAAA,mCAAA,CAAqC,CACpE,CAAC;KACH;AACH,CAAC;AAEK,MAAO,qBACH,SAAA,eAAe,CAAA;AAOvB,IAAA,WAAA,CACE,OAA0C,EAC1C,YAAuC,EACvC,IAAY,EACZ,SAA6B,EAAA;;AAG7B,QAAA,MAAM,8BAA8B,GAClC,YAAY,CAAC,8BAA8B,KAAK,SAAS;cACrD,YAAY,CAAC,8BAA8B;cAC3C,KAAK,CAAC;;AAGZ,QAAA,MAAM,MAAM,GAAkC;YAC5C,IAAI;YACJ,8BAA8B;SAC/B,CAAC;AAEF,QAAA,IAAK,OAA2B,CAAC,MAAM,KAAK,SAAS,EAAE;;AAErD,YAAA,KAAK,CAAC,OAA0B,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;SACtD;aAAM;YACL,MAAM,OAAO,GAAoB,OAA0B,CAAC;YAC5D,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;SAC3C;;AAGD,QAAA,IAAI,CAAC,aAAa,GAAA,MAAA,CAAA,MAAA,CAAA,EAChB,8BAA8B,EAC3B,EAAA,YAAY,CAChB,CAAC;;AAGF,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE;YAClC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;SACjE;;AAGD,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE;YACpC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;SACrE;AAED,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AAClC,QAAA,IAAI,OAAO,oBAAoB,KAAK,WAAW,EAAE;AAC/C,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,oBAAoB,CAAC,MAAK;gBACzD,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC1B,aAAC,CAAC,CAAC;SACJ;AAED,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;;;AAIpD,QAAA,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG,SAAS,CAAC;AAC9C,QAAA,YAAY,CAAC,cAAc,GAAG,SAAS,CAAC;AAExC,QAAA,eAAe,CAACA,MAAW,EAAEC,SAAO,EAAE,WAAW,CAAC,CAAC;KACpD;IAED,MAAM,GAAA;AACJ,QAAA,OAAO,SAAS,CAAC;KAClB;AAED,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;;;AAID,IAAA,WAAW,CAAC,GAAuB,EAAA;AACjC,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO;SACR;QACD,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,GAAG,KAAK,SAAS,IAAI,IAAI,CAAC,qBAAqB,KAAK,IAAI,EAAE;YAC5D,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;SAChD;KACF;;IAGD,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,OAAO,CAAC,CAAC;SACV;AACD,QAAA,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;KACzB;;;;IAKO,gBAAgB,GAAA;AACtB,QAAA,KAAK,SAAS,CAAC,IAAI,CAAC,CAAC;KACtB;AAED,IAAA,IAAI,QAAQ,GAAA;QACV,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC,aAAa,CAAC;KAC3B;AAED;;;AAGG;IACO,cAAc,GAAA;AACtB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,aAAa,CAAC,MAAM,CAAA,oBAAA,mCAA6B,CAAC;SACzD;KACF;AACF,CAAA;AC/KD;;;;;;;;;;;;;;;AAeG;AA4CH;;;;AAIG;AACU,MAAA,WAAW,GAAGA,UAAQ;AAoEnB,SAAA,aAAa,CAC3B,QAA0B,EAC1B,SAAS,GAAG,EAAE,EAAA;IAEd,IAAI,OAAO,GAAG,QAAQ,CAAC;AAEvB,IAAA,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;QACjC,MAAM,IAAI,GAAG,SAAS,CAAC;AACvB,QAAA,SAAS,GAAG,EAAE,IAAI,EAAE,CAAC;KACtB;AAED,IAAA,MAAM,MAAM,GAAA,MAAA,CAAA,MAAA,CAAA,EACV,IAAI,EAAE,kBAAkB,EACxB,8BAA8B,EAAE,KAAK,EAAA,EAClC,SAAS,CACb,CAAC;AACF,IAAA,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;IAEzB,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,IAAI,EAAE;AACrC,QAAA,MAAM,aAAa,CAAC,MAAM,CAAwB,cAAA,8BAAA;AAChD,YAAA,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC;AACtB,SAAA,CAAC,CAAC;KACJ;AAED,IAAA,OAAO,KAAP,OAAO,GAAK,mBAAmB,EAAE,CAAC,CAAA;IAElC,IAAI,CAAC,OAAO,EAAE;QACZ,MAAM,aAAa,CAAC,MAAM,CAAA,YAAA,2BAAqB,CAAC;KACjD;IAED,MAAM,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAoB,CAAC;IACvD,IAAI,WAAW,EAAE;;AAEf,QAAA,IACE,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC;YACvC,SAAS,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,EACrC;AACA,YAAA,OAAO,WAAW,CAAC;SACpB;aAAM;AACL,YAAA,MAAM,aAAa,CAAC,MAAM,CAAyB,eAAA,+BAAA,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;SACvE;KACF;AAED,IAAA,MAAM,SAAS,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAC/C,KAAK,MAAM,SAAS,IAAI,WAAW,CAAC,MAAM,EAAE,EAAE;AAC5C,QAAA,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;KACnC;IAED,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;AAE/D,IAAA,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAExB,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAgDe,SAAA,mBAAmB,CACjC,QAAuC,EACvC,gBAA2C,EAAA;AAE3C,IAAA,IAAI,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE;;QAEjC,MAAM,aAAa,CAAC,MAAM,CAAA,gCAAA,+CAAyC,CAAC;KACrE;AAED,IAAA,IAAI,gBAAgB,CAAC,8BAA8B,KAAK,SAAS,EAAE;AACjE,QAAA,gBAAgB,CAAC,8BAA8B,GAAG,KAAK,CAAC;KACzD;AAED,IAAA,IAAI,UAA2B,CAAC;AAChC,IAAA,IAAI,cAAc,CAAC,QAAQ,CAAC,EAAE;AAC5B,QAAA,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC;KAC/B;SAAM;QACL,UAAU,GAAG,QAAQ,CAAC;KACvB;;AAGD,IAAA,MAAM,OAAO,GACR,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,gBAAgB,CAChB,EAAA,UAAU,CACd,CAAC;;;AAIF,IAAA,IAAI,OAAO,CAAC,cAAc,KAAK,SAAS,EAAE;QACxC,OAAO,OAAO,CAAC,cAAc,CAAC;KAC/B;AAED,IAAA,MAAM,QAAQ,GAAG,CAAC,CAAS,KAAY;AACrC,QAAA,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAClB,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EACxD,CAAC,CACF,CAAC;AACJ,KAAC,CAAC;AAEF,IAAA,IAAI,gBAAgB,CAAC,cAAc,KAAK,SAAS,EAAE;AACjD,QAAA,IAAI,OAAO,oBAAoB,KAAK,WAAW,EAAE;YAC/C,MAAM,aAAa,CAAC,MAAM,CAExB,qCAAA,qDAAA,EAAE,CACH,CAAC;SACH;KACF;AAED,IAAA,MAAM,UAAU,GAAG,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;IAC1D,MAAM,WAAW,GAAG,WAAW,CAAC,GAAG,CAAC,UAAU,CAAsB,CAAC;IACrE,IAAI,WAAW,EAAE;AACd,QAAA,WAAqC,CAAC,WAAW,CAChD,gBAAgB,CAAC,cAAc,CAChC,CAAC;AACF,QAAA,OAAO,WAAW,CAAC;KACpB;AAED,IAAA,MAAM,SAAS,GAAG,IAAI,kBAAkB,CAAC,UAAU,CAAC,CAAC;IACrD,KAAK,MAAM,SAAS,IAAI,WAAW,CAAC,MAAM,EAAE,EAAE;AAC5C,QAAA,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;KACnC;AAED,IAAA,MAAM,MAAM,GAAG,IAAI,qBAAqB,CACtC,UAAU,EACV,gBAAgB,EAChB,UAAU,EACV,SAAS,CACV,CAAC;AAEF,IAAA,WAAW,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AAEpC,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BG;AACa,SAAA,MAAM,CAAC,IAAA,GAAe,kBAAkB,EAAA;IACtD,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC5B,IAAI,CAAC,GAAG,IAAI,IAAI,KAAK,kBAAkB,IAAI,mBAAmB,EAAE,EAAE;QAChE,OAAO,aAAa,EAAE,CAAC;KACxB;IACD,IAAI,CAAC,GAAG,EAAE;AACR,QAAA,MAAM,aAAa,CAAC,MAAM,CAAkB,QAAA,wBAAA,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;KAChE;AAED,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;AAGG;AACa,SAAA,OAAO,GAAA;IACrB,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;AACpC,CAAC;AAED;;;;;;;;;;;;;;;;AAgBG;AACI,eAAe,SAAS,CAAC,GAAgB,EAAA;IAC9C,IAAI,gBAAgB,GAAG,KAAK,CAAC;AAC7B,IAAA,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;AACtB,IAAA,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QACnB,gBAAgB,GAAG,IAAI,CAAC;AACxB,QAAA,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;KACpB;AAAM,SAAA,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QAChC,MAAM,iBAAiB,GAAG,GAA4B,CAAC;AACvD,QAAA,IAAI,iBAAiB,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE;AACxC,YAAA,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACzB,gBAAgB,GAAG,IAAI,CAAC;SACzB;KACF;IAED,IAAI,gBAAgB,EAAE;AACpB,QAAA,MAAM,OAAO,CAAC,GAAG,CACd,GAAuB,CAAC,SAAS;AAC/B,aAAA,YAAY,EAAE;aACd,GAAG,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC,CACtC,CAAC;AACD,QAAA,GAAuB,CAAC,SAAS,GAAG,IAAI,CAAC;KAC3C;AACH,CAAC;AAED;;;;;;;AAOG;AACa,SAAA,eAAe,CAC7B,gBAAwB,EACxB,OAAe,EACf,OAAgB,EAAA;;;;IAIhB,IAAI,OAAO,GAAG,CAAA,EAAA,GAAA,mBAAmB,CAAC,gBAAgB,CAAC,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,gBAAgB,CAAC;IACxE,IAAI,OAAO,EAAE;AACX,QAAA,OAAO,IAAI,CAAA,CAAA,EAAI,OAAO,CAAA,CAAE,CAAC;KAC1B;IACD,MAAM,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC/C,MAAM,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAC/C,IAAA,IAAI,eAAe,IAAI,eAAe,EAAE;AACtC,QAAA,MAAM,OAAO,GAAG;YACd,CAA+B,4BAAA,EAAA,OAAO,CAAmB,gBAAA,EAAA,OAAO,CAAI,EAAA,CAAA;SACrE,CAAC;QACF,IAAI,eAAe,EAAE;AACnB,YAAA,OAAO,CAAC,IAAI,CACV,iBAAiB,OAAO,CAAA,iDAAA,CAAmD,CAC5E,CAAC;SACH;AACD,QAAA,IAAI,eAAe,IAAI,eAAe,EAAE;AACtC,YAAA,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACrB;QACD,IAAI,eAAe,EAAE;AACnB,YAAA,OAAO,CAAC,IAAI,CACV,iBAAiB,OAAO,CAAA,iDAAA,CAAmD,CAC5E,CAAC;SACH;QACD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/B,OAAO;KACR;IACD,kBAAkB,CAChB,IAAI,SAAS,CACX,CAAA,EAAG,OAAO,CAAkB,QAAA,CAAA,EAC5B,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,EAAA,SAAA,6BAE7B,CACF,CAAC;AACJ,CAAC;AAED;;;;;;AAMG;AACa,SAAA,KAAK,CACnB,WAA+B,EAC/B,OAAoB,EAAA;IAEpB,IAAI,WAAW,KAAK,IAAI,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE;QAC7D,MAAM,aAAa,CAAC,MAAM,CAAA,sBAAA,qCAA+B,CAAC;KAC3D;AACD,IAAA,iBAAiB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AAC1C,CAAC;AAED;;;;;;;;AAQG;AACG,SAAU,WAAW,CAAC,QAAwB,EAAA;IAClDC,aAAe,CAAC,QAAQ,CAAC,CAAC;AAC5B,CAAA;AC3dA;;;;;;;;;;;;;;;AAeG;AASH,MAAM,OAAO,GAAG,6BAA6B,CAAC;AAC9C,MAAM,UAAU,GAAG,CAAC,CAAC;AACrB,MAAM,UAAU,GAAG,0BAA0B,CAAC;AAS9C,IAAI,SAAS,GAAwC,IAAI,CAAC;AAC1D,SAAS,YAAY,GAAA;IACnB,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,SAAS,GAAG,MAAM,CAAQ,OAAO,EAAE,UAAU,EAAE;AAC7C,YAAA,OAAO,EAAE,CAAC,EAAE,EAAE,UAAU,KAAI;;;;;;gBAM1B,QAAQ,UAAU;AAChB,oBAAA,KAAK,CAAC;AACJ,wBAAA,IAAI;AACF,4BAAA,EAAE,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;yBAClC;wBAAC,OAAO,CAAC,EAAE;;;;AAIV,4BAAA,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;yBACjB;iBACJ;aACF;AACF,SAAA,CAAC,CAAC,KAAK,CAAC,CAAC,IAAG;AACX,YAAA,MAAM,aAAa,CAAC,MAAM,CAAoB,UAAA,0BAAA;gBAC5C,oBAAoB,EAAE,CAAC,CAAC,OAAO;AAChC,aAAA,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAEM,eAAe,2BAA2B,CAC/C,GAAgB,EAAA;AAEhB,IAAA,IAAI;AACF,QAAA,MAAM,EAAE,GAAG,MAAM,YAAY,EAAE,CAAC;QAChC,MAAM,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AACtC,QAAA,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;;;QAGrE,MAAM,EAAE,CAAC,IAAI,CAAC;AACd,QAAA,OAAO,MAAM,CAAC;KACf;IAAC,OAAO,CAAC,EAAE;AACV,QAAA,IAAI,CAAC,YAAY,aAAa,EAAE;AAC9B,YAAA,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;SACxB;aAAM;YACL,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CAAmB,SAAA,yBAAA;AACzD,gBAAA,oBAAoB,EAAG,CAAW,KAAA,IAAA,IAAX,CAAC,KAAD,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAC,CAAY,OAAO;AAC5C,aAAA,CAAC,CAAC;AACH,YAAA,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SAClC;KACF;AACH,CAAC;AAEM,eAAe,0BAA0B,CAC9C,GAAgB,EAChB,eAAsC,EAAA;AAEtC,IAAA,IAAI;AACF,QAAA,MAAM,EAAE,GAAG,MAAM,YAAY,EAAE,CAAC;QAChC,MAAM,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QACnD,MAAM,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC/C,MAAM,WAAW,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;QACxD,MAAM,EAAE,CAAC,IAAI,CAAC;KACf;IAAC,OAAO,CAAC,EAAE;AACV,QAAA,IAAI,CAAC,YAAY,aAAa,EAAE;AAC9B,YAAA,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;SACxB;aAAM;YACL,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CAAqB,SAAA,2BAAA;AAC3D,gBAAA,oBAAoB,EAAG,CAAW,KAAA,IAAA,IAAX,CAAC,KAAD,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAC,CAAY,OAAO;AAC5C,aAAA,CAAC,CAAC;AACH,YAAA,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SAClC;KACF;AACH,CAAC;AAED,SAAS,UAAU,CAAC,GAAgB,EAAA;IAClC,OAAO,CAAA,EAAG,GAAG,CAAC,IAAI,CAAA,CAAA,EAAI,GAAG,CAAC,OAAO,CAAC,KAAK,CAAA,CAAE,CAAC;AAC5C,CAAA;ACjHA;;;;;;;;;;;;;;;AAeG;AAsBH,MAAM,gBAAgB,GAAG,IAAI,CAAC;AACvB,MAAM,yBAAyB,GAAG,EAAE,CAAC;AAE/B,MAAA,oBAAoB,CAAA;AAyB/B,IAAA,WAAA,CAA6B,SAA6B,EAAA;AAA7B,QAAA,IAAS,CAAA,SAAA,GAAT,SAAS,CAAoB;AAlB1D;;;;;;;;AAQG;AACH,QAAA,IAAgB,CAAA,gBAAA,GAAiC,IAAI,CAAC;AAUpD,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,CAAC;QAC7D,IAAI,CAAC,QAAQ,GAAG,IAAI,oBAAoB,CAAC,GAAG,CAAC,CAAC;AAC9C,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,IAAG;AAChE,YAAA,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;AAC/B,YAAA,OAAO,MAAM,CAAC;AAChB,SAAC,CAAC,CAAC;KACJ;AAED;;;;;;AAMG;AACH,IAAA,MAAM,gBAAgB,GAAA;;AACpB,QAAA,IAAI;AACF,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS;iBAClC,WAAW,CAAC,iBAAiB,CAAC;AAC9B,iBAAA,YAAY,EAAE,CAAC;;;AAIlB,YAAA,MAAM,KAAK,GAAG,cAAc,CAAC,qBAAqB,EAAE,CAAC;AACrD,YAAA,MAAM,IAAI,GAAG,gBAAgB,EAAE,CAAC;AAChC,YAAA,IAAI,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,gBAAgB,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAU,KAAI,IAAI,EAAE;AAC7C,gBAAA,IAAI,CAAC,gBAAgB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC;;AAE3D,gBAAA,IAAI,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,gBAAgB,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAU,KAAI,IAAI,EAAE;oBAC7C,OAAO;iBACR;aACF;;;AAGD,YAAA,IACE,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,KAAK,IAAI;AACpD,gBAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CACnC,mBAAmB,IAAI,mBAAmB,CAAC,IAAI,KAAK,IAAI,CACzD,EACD;gBACA,OAAO;aACR;iBAAM;;AAEL,gBAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;;;gBAIvD,IACE,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,GAAG,yBAAyB,EACnE;oBACA,MAAM,oBAAoB,GAAG,uBAAuB,CAClD,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACjC,CAAC;oBACF,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;iBAClE;aACF;YAED,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACvD;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAChB;KACF;AAED;;;;;;AAMG;AACH,IAAA,MAAM,mBAAmB,GAAA;;AACvB,QAAA,IAAI;AACF,YAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,EAAE;gBAClC,MAAM,IAAI,CAAC,uBAAuB,CAAC;aACpC;;AAED,YAAA,IACE,CAAA,MAAA,IAAI,CAAC,gBAAgB,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAU,KAAI,IAAI;gBACzC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAC7C;AACA,gBAAA,OAAO,EAAE,CAAC;aACX;AACD,YAAA,MAAM,IAAI,GAAG,gBAAgB,EAAE,CAAC;;AAEhC,YAAA,MAAM,EAAE,gBAAgB,EAAE,aAAa,EAAE,GAAG,0BAA0B,CACpE,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACjC,CAAC;AACF,YAAA,MAAM,YAAY,GAAG,6BAA6B,CAChD,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,gBAAgB,EAAE,CAAC,CAC7D,CAAC;;AAEF,YAAA,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACnD,YAAA,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;;AAE5B,gBAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,GAAG,aAAa,CAAC;;;;gBAIjD,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;aACtD;iBAAM;AACL,gBAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,GAAG,EAAE,CAAC;;gBAEtC,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;aACrD;AACD,YAAA,OAAO,YAAY,CAAC;SACrB;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACf,YAAA,OAAO,EAAE,CAAC;SACX;KACF;AACF,CAAA;AAED,SAAS,gBAAgB,GAAA;AACvB,IAAA,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;;IAEzB,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC9C,CAAC;AAEe,SAAA,0BAA0B,CACxC,eAAsC,EACtC,OAAO,GAAG,gBAAgB,EAAA;;;IAO1B,MAAM,gBAAgB,GAA4B,EAAE,CAAC;;AAErD,IAAA,IAAI,aAAa,GAAG,eAAe,CAAC,KAAK,EAAE,CAAC;AAC5C,IAAA,KAAK,MAAM,mBAAmB,IAAI,eAAe,EAAE;;AAEjD,QAAA,MAAM,cAAc,GAAG,gBAAgB,CAAC,IAAI,CAC1C,EAAE,IAAI,EAAE,CAAC,KAAK,KAAK,mBAAmB,CAAC,KAAK,CAC7C,CAAC;QACF,IAAI,CAAC,cAAc,EAAE;;YAEnB,gBAAgB,CAAC,IAAI,CAAC;gBACpB,KAAK,EAAE,mBAAmB,CAAC,KAAK;AAChC,gBAAA,KAAK,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC;AAClC,aAAA,CAAC,CAAC;AACH,YAAA,IAAI,UAAU,CAAC,gBAAgB,CAAC,GAAG,OAAO,EAAE;;;gBAG1C,gBAAgB,CAAC,GAAG,EAAE,CAAC;gBACvB,MAAM;aACP;SACF;aAAM;YACL,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;;;AAGpD,YAAA,IAAI,UAAU,CAAC,gBAAgB,CAAC,GAAG,OAAO,EAAE;AAC1C,gBAAA,cAAc,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;gBAC3B,MAAM;aACP;SACF;;;AAGD,QAAA,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KACxC;IACD,OAAO;QACL,gBAAgB;QAChB,aAAa;KACd,CAAC;AACJ,CAAC;AAEY,MAAA,oBAAoB,CAAA;AAE/B,IAAA,WAAA,CAAmB,GAAgB,EAAA;AAAhB,QAAA,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;AACjC,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;KACpE;AACD,IAAA,MAAM,4BAA4B,GAAA;AAChC,QAAA,IAAI,CAAC,oBAAoB,EAAE,EAAE;AAC3B,YAAA,OAAO,KAAK,CAAC;SACd;aAAM;AACL,YAAA,OAAO,yBAAyB,EAAE;AAC/B,iBAAA,IAAI,CAAC,MAAM,IAAI,CAAC;AAChB,iBAAA,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;SACvB;KACF;AACD;;AAEG;AACH,IAAA,MAAM,IAAI,GAAA;AACR,QAAA,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC;QAC3D,IAAI,CAAC,eAAe,EAAE;AACpB,YAAA,OAAO,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC;SAC3B;aAAM;YACL,MAAM,kBAAkB,GAAG,MAAM,2BAA2B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACvE,IAAI,kBAAkB,KAAA,IAAA,IAAlB,kBAAkB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAlB,kBAAkB,CAAE,UAAU,EAAE;AAClC,gBAAA,OAAO,kBAAkB,CAAC;aAC3B;iBAAM;AACL,gBAAA,OAAO,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC;aAC3B;SACF;KACF;;IAED,MAAM,SAAS,CAAC,gBAAuC,EAAA;;AACrD,QAAA,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC;QAC3D,IAAI,CAAC,eAAe,EAAE;YACpB,OAAO;SACR;aAAM;AACL,YAAA,MAAM,wBAAwB,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;AACnD,YAAA,OAAO,0BAA0B,CAAC,IAAI,CAAC,GAAG,EAAE;gBAC1C,qBAAqB,EACnB,CAAA,EAAA,GAAA,gBAAgB,CAAC,qBAAqB,MACtC,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,wBAAwB,CAAC,qBAAqB;gBAChD,UAAU,EAAE,gBAAgB,CAAC,UAAU;AACxC,aAAA,CAAC,CAAC;SACJ;KACF;;IAED,MAAM,GAAG,CAAC,gBAAuC,EAAA;;AAC/C,QAAA,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC;QAC3D,IAAI,CAAC,eAAe,EAAE;YACpB,OAAO;SACR;aAAM;AACL,YAAA,MAAM,wBAAwB,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;AACnD,YAAA,OAAO,0BAA0B,CAAC,IAAI,CAAC,GAAG,EAAE;gBAC1C,qBAAqB,EACnB,CAAA,EAAA,GAAA,gBAAgB,CAAC,qBAAqB,MACtC,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,wBAAwB,CAAC,qBAAqB;AAChD,gBAAA,UAAU,EAAE;oBACV,GAAG,wBAAwB,CAAC,UAAU;oBACtC,GAAG,gBAAgB,CAAC,UAAU;AAC/B,iBAAA;AACF,aAAA,CAAC,CAAC;SACJ;KACF;AACF,CAAA;AAED;;;;AAIG;AACG,SAAU,UAAU,CAAC,eAAwC,EAAA;;AAEjE,IAAA,OAAO,6BAA6B;;AAElC,IAAA,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,eAAe,EAAE,CAAC,CAC5D,CAAC,MAAM,CAAC;AACX,CAAC;AAED;;;AAGG;AACG,SAAU,uBAAuB,CACrC,UAAiC,EAAA;AAEjC,IAAA,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;QAC3B,OAAO,CAAC,CAAC,CAAC;KACX;IAED,IAAI,oBAAoB,GAAG,CAAC,CAAC;IAC7B,IAAI,qBAAqB,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAE/C,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC1C,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,qBAAqB,EAAE;AAC9C,YAAA,qBAAqB,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAC3C,oBAAoB,GAAG,CAAC,CAAC;SAC1B;KACF;AAED,IAAA,OAAO,oBAAoB,CAAC;AAC9B,CAAA;AC5UA;;;;;;;;;;;;;;;AAeG;AASG,SAAU,sBAAsB,CAAC,OAAgB,EAAA;IACrD,kBAAkB,CAChB,IAAI,SAAS,CACX,iBAAiB,EACjB,SAAS,IAAI,IAAI,yBAAyB,CAAC,SAAS,CAAC,EAAA,SAAA,6BAEtD,CACF,CAAC;IACF,kBAAkB,CAChB,IAAI,SAAS,CACX,WAAW,EACX,SAAS,IAAI,IAAI,oBAAoB,CAAC,SAAS,CAAC,EAAA,SAAA,6BAEjD,CACF,CAAC;;AAGF,IAAA,eAAe,CAACC,MAAI,EAAEF,SAAO,EAAE,OAAO,CAAC,CAAC;;AAExC,IAAA,eAAe,CAACE,MAAI,EAAEF,SAAO,EAAE,SAAkB,CAAC,CAAC;;AAEnD,IAAA,eAAe,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;AACjC,CAAA;AC9CA;;;;;AAKG;AAyBH,sBAAsB,CAAC,EAAiB,CAAC;;;;;AC9BzC;;;;;;;;;;;;;;;AAeG;AAKH,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC;;;;", "preExistingComment": "firebase-app.js.map"}