/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "m15 15 6 6", key: "1s409w" }],
  ["path", { d: "m15 9 6-6", key: "ko1vev" }],
  ["path", { d: "M21 16.2V21h-4.8", key: "1hrera" }],
  ["path", { d: "M21 7.8V3h-4.8", key: "ul1q53" }],
  ["path", { d: "M3 16.2V21h4.8", key: "1x04uo" }],
  ["path", { d: "m3 21 6-6", key: "wwnumi" }],
  ["path", { d: "M3 7.8V3h4.8", key: "1ijppm" }],
  ["path", { d: "M9 9 3 3", key: "v551iv" }]
];
const Expand = createLucideIcon("expand", __iconNode);

export { __iconNode, Expand as default };
//# sourceMappingURL=expand.js.map
