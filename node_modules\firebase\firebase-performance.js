import{registerVersion as e,_registerComponent as t,_getProvider,getApp as c}from"https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";class FirebaseError extends Error{constructor(e,t,c){super(t),this.code=e,this.customData=c,this.name="FirebaseError",Object.setPrototypeOf(this,FirebaseError.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,ErrorFactory.prototype.create)}}class ErrorFactory{constructor(e,t,c){this.service=e,this.serviceName=t,this.errors=c}create(e,...t){const c=t[0]||{},w=`${this.service}/${e}`,I=this.errors[e],_=I?function replaceTemplate(e,t){return e.replace(g,((e,c)=>{const g=t[c];return null!=g?String(g):`<${c}?>`}))}(I,c):"Error",k=`${this.serviceName}: ${_} (${w}).`;return new FirebaseError(w,k,c)}}const g=/\{\$([^}]+)}/g;function deepEqual(e,t){if(e===t)return!0;const c=Object.keys(e),g=Object.keys(t);for(const w of c){if(!g.includes(w))return!1;const c=e[w],I=t[w];if(isObject(c)&&isObject(I)){if(!deepEqual(c,I))return!1}else if(c!==I)return!1}for(const e of g)if(!c.includes(e))return!1;return!0}function isObject(e){return null!==e&&"object"==typeof e}function getModularInstance(e){return e&&e._delegate?e._delegate:e}var w;!function(e){e[e.DEBUG=0]="DEBUG",e[e.VERBOSE=1]="VERBOSE",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.SILENT=5]="SILENT"}(w||(w={}));const I={debug:w.DEBUG,verbose:w.VERBOSE,info:w.INFO,warn:w.WARN,error:w.ERROR,silent:w.SILENT},_=w.INFO,k={[w.DEBUG]:"log",[w.VERBOSE]:"log",[w.INFO]:"info",[w.WARN]:"warn",[w.ERROR]:"error"},defaultLogHandler=(e,t,...c)=>{if(t<e.logLevel)return;const g=(new Date).toISOString(),w=k[t];if(!w)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[w](`[${g}]  ${e.name}:`,...c)};var C,R,n=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},r=function(e){if("loading"===document.readyState)return"loading";var t=n();if(t){if(e<t.domInteractive)return"loading";if(0===t.domContentLoadedEventStart||e<t.domContentLoadedEventStart)return"dom-interactive";if(0===t.domComplete||e<t.domComplete)return"dom-content-loaded"}return"complete"},i=function(e){var t=e.nodeName;return 1===e.nodeType?t.toLowerCase():t.toUpperCase().replace(/^#/,"")},a=function(e,t){var c="";try{for(;e&&9!==e.nodeType;){var g=e,w=g.id?"#"+g.id:i(g)+(g.classList&&g.classList.value&&g.classList.value.trim()&&g.classList.value.trim().length?"."+g.classList.value.trim().replace(/\s+/g,"."):"");if(c.length+w.length>(t||100)-1)return c||w;if(c=c?w+">"+c:w,g.id)break;e=g.parentNode}}catch(e){}return c},M=-1,u=function(e){addEventListener("pageshow",(function(t){t.persisted&&(M=t.timeStamp,e(t))}),!0)},s=function(){var e=n();return e&&e.activationStart||0},f=function(e,t){var c=n(),g="navigate";return M>=0?g="back-forward-cache":c&&(document.prerendering||s()>0?g="prerender":document.wasDiscarded?g="restore":c.type&&(g=c.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:g}},d=function(e,t,c){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var g=new PerformanceObserver((function(e){Promise.resolve().then((function(){t(e.getEntries())}))}));return g.observe(Object.assign({type:e,buffered:!0},c||{})),g}}catch(e){}},l=function(e,t,c,g){var w,I;return function(_){t.value>=0&&(_||g)&&((I=t.value-(w||0))||void 0===w)&&(w=t.value,t.delta=I,t.rating=function(e,t){return e>t[1]?"poor":e>t[0]?"needs-improvement":"good"}(t.value,c),e(t))}},m=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},p=function(e){document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&e()}))},v=function(e){var t=!1;return function(){t||(e(),t=!0)}},L=-1,h=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},T=function(e){"hidden"===document.visibilityState&&L>-1&&(L="visibilitychange"===e.type?e.timeStamp:0,E())},y=function(){addEventListener("visibilitychange",T,!0),addEventListener("prerenderingchange",T,!0)},E=function(){removeEventListener("visibilitychange",T,!0),removeEventListener("prerenderingchange",T,!0)},S=function(){return L<0&&(L=h(),y(),u((function(){setTimeout((function(){L=h(),y()}),0)}))),{get firstHiddenTime(){return L}}},b=function(e){document.prerendering?addEventListener("prerenderingchange",(function(){return e()}),!0):e()},O=[1800,3e3],B=[.1,.25],D=function(e,t){!function(e,t){t=t||{},function(e,t){t=t||{},b((function(){var c,g=S(),w=f("FCP"),I=d("paint",(function(e){e.forEach((function(e){"first-contentful-paint"===e.name&&(I.disconnect(),e.startTime<g.firstHiddenTime&&(w.value=Math.max(e.startTime-s(),0),w.entries.push(e),c(!0)))}))}));I&&(c=l(e,w,O,t.reportAllChanges),u((function(g){w=f("FCP"),c=l(e,w,O,t.reportAllChanges),m((function(){w.value=performance.now()-g.timeStamp,c(!0)}))})))}))}(v((function(){var c,g=f("CLS",0),w=0,I=[],o=function(e){e.forEach((function(e){if(!e.hadRecentInput){var t=I[0],c=I[I.length-1];w&&e.startTime-c.startTime<1e3&&e.startTime-t.startTime<5e3?(w+=e.value,I.push(e)):(w=e.value,I=[e])}})),w>g.value&&(g.value=w,g.entries=I,c())},_=d("layout-shift",o);_&&(c=l(e,g,B,t.reportAllChanges),p((function(){o(_.takeRecords()),c(!0)})),u((function(){w=0,g=f("CLS",0),c=l(e,g,B,t.reportAllChanges),m((function(){return c()}))})),setTimeout(c,0))})))}((function(t){var c=function(e){var t,c={};if(e.entries.length){var g=e.entries.reduce((function(e,t){return e&&e.value>t.value?e:t}));if(g&&g.sources&&g.sources.length){var w=(t=g.sources).find((function(e){return e.node&&1===e.node.nodeType}))||t[0];w&&(c={largestShiftTarget:a(w.node),largestShiftTime:g.startTime,largestShiftValue:g.value,largestShiftSource:w,largestShiftEntry:g,loadState:r(g.startTime)})}}return Object.assign(e,{attribution:c})}(t);e(c)}),t)},q=0,j=1/0,U=0,A=function(e){e.forEach((function(e){e.interactionId&&(j=Math.min(j,e.interactionId),U=Math.max(U,e.interactionId),q=U?(U-j)/7+1:0)}))},F=function(){return C?q:performance.interactionCount||0},P=function(){"interactionCount"in performance||C||(C=d("event",A,{type:"event",buffered:!0,durationThreshold:0}))},$=[],x=new Map,V=0,W=[],H=function(e){if(W.forEach((function(t){return t(e)})),e.interactionId||"first-input"===e.entryType){var t=$[$.length-1],c=x.get(e.interactionId);if(c||$.length<10||e.duration>t.latency){if(c)e.duration>c.latency?(c.entries=[e],c.latency=e.duration):e.duration===c.latency&&e.startTime===c.entries[0].startTime&&c.entries.push(e);else{var g={id:e.interactionId,latency:e.duration,entries:[e]};x.set(g.id,g),$.push(g)}$.sort((function(e,t){return t.latency-e.latency})),$.length>10&&$.splice(10).forEach((function(e){return x.delete(e.id)}))}}},N=function(e){var t=self.requestIdleCallback||self.setTimeout,c=-1;return e=v(e),"hidden"===document.visibilityState?e():(c=t(e),p(e)),c},K=[200,500],z=function(e,t){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(t=t||{},b((function(){var c;P();var g,w=f("INP"),a=function(e){N((function(){e.forEach(H);var t=function(){var e=Math.min($.length-1,Math.floor((F()-V)/50));return $[e]}();t&&t.latency!==w.value&&(w.value=t.latency,w.entries=t.entries,g())}))},I=d("event",a,{durationThreshold:null!==(c=t.durationThreshold)&&void 0!==c?c:40});g=l(e,w,K,t.reportAllChanges),I&&(I.observe({type:"first-input",buffered:!0}),p((function(){a(I.takeRecords()),g(!0)})),u((function(){V=F(),$.length=0,x.clear(),w=f("INP"),g=l(e,w,K,t.reportAllChanges)})))})))},G=[],J=[],Z=0,ee=new WeakMap,te=new Map,ne=-1,Q=function(e){G=G.concat(e),X()},X=function(){ne<0&&(ne=N(Y))},Y=function(){te.size>10&&te.forEach((function(e,t){x.has(t)||te.delete(t)}));var e=$.map((function(e){return ee.get(e.entries[0])})),t=J.length-50;J=J.filter((function(c,g){return g>=t||e.includes(c)}));for(var c=new Set,g=0;g<J.length;g++){var w=J[g];nt(w.startTime,w.processingEnd).forEach((function(e){c.add(e)}))}var I=G.length-1-50;G=G.filter((function(e,t){return e.startTime>Z&&t>I||c.has(e)})),ne=-1};W.push((function(e){e.interactionId&&e.target&&!te.has(e.interactionId)&&te.set(e.interactionId,e.target)}),(function(e){var t,c=e.startTime+e.duration;Z=Math.max(Z,e.processingEnd);for(var g=J.length-1;g>=0;g--){var w=J[g];if(Math.abs(c-w.renderTime)<=8){(t=w).startTime=Math.min(e.startTime,t.startTime),t.processingStart=Math.min(e.processingStart,t.processingStart),t.processingEnd=Math.max(e.processingEnd,t.processingEnd),t.entries.push(e);break}}t||(t={startTime:e.startTime,processingStart:e.processingStart,processingEnd:e.processingEnd,renderTime:c,entries:[e]},J.push(t)),(e.interactionId||"first-input"===e.entryType)&&ee.set(e,t),X()}));var nt=function(e,t){for(var c,g=[],w=0;c=G[w];w++)if(!(c.startTime+c.duration<e)){if(c.startTime>t)break;g.push(c)}return g},rt=function(e,t){R||(R=d("long-animation-frame",Q)),z((function(t){var c=function(e){var t=e.entries[0],c=ee.get(t),g=t.processingStart,w=c.processingEnd,I=c.entries.sort((function(e,t){return e.processingStart-t.processingStart})),_=nt(t.startTime,w),k=e.entries.find((function(e){return e.target})),C=k&&k.target||te.get(t.interactionId),R=[t.startTime+t.duration,w].concat(_.map((function(e){return e.startTime+e.duration}))),M=Math.max.apply(Math,R),L={interactionTarget:a(C),interactionTargetElement:C,interactionType:t.name.startsWith("key")?"keyboard":"pointer",interactionTime:t.startTime,nextPaintTime:M,processedEventEntries:I,longAnimationFrameEntries:_,inputDelay:g-t.startTime,processingDuration:w-g,presentationDelay:Math.max(M-w,0),loadState:r(t.startTime)};return Object.assign(e,{attribution:L})}(t);e(c)}),t)},re=[2500,4e3],ie={},ot=function(e,t){!function(e,t){t=t||{},b((function(){var c,g=S(),w=f("LCP"),a=function(e){t.reportAllChanges||(e=e.slice(-1)),e.forEach((function(e){e.startTime<g.firstHiddenTime&&(w.value=Math.max(e.startTime-s(),0),w.entries=[e],c())}))},I=d("largest-contentful-paint",a);if(I){c=l(e,w,re,t.reportAllChanges);var _=v((function(){ie[w.id]||(a(I.takeRecords()),I.disconnect(),ie[w.id]=!0,c(!0))}));["keydown","click"].forEach((function(e){addEventListener(e,(function(){return N(_)}),{once:!0,capture:!0})})),p(_),u((function(g){w=f("LCP"),c=l(e,w,re,t.reportAllChanges),m((function(){w.value=performance.now()-g.timeStamp,ie[w.id]=!0,c(!0)}))}))}}))}((function(t){var c=function(e){var t={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:e.value};if(e.entries.length){var c=n();if(c){var g=c.activationStart||0,w=e.entries[e.entries.length-1],I=w.url&&performance.getEntriesByType("resource").filter((function(e){return e.name===w.url}))[0],_=Math.max(0,c.responseStart-g),k=Math.max(_,I?(I.requestStart||I.startTime)-g:0),C=Math.max(k,I?I.responseEnd-g:0),R=Math.max(C,w.startTime-g);t={element:a(w.element),timeToFirstByte:_,resourceLoadDelay:k-_,resourceLoadDuration:C-k,elementRenderDelay:R-C,navigationEntry:c,lcpEntry:w},w.url&&(t.url=w.url),I&&(t.lcpResourceEntry=I)}}return Object.assign(e,{attribution:t})}(t);e(c)}),t)};class Component{constructor(e,t,c){this.name=e,this.instanceFactory=t,this.type=c,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}let ae,oe;const se=new WeakMap,ce=new WeakMap,ue=new WeakMap,le=new WeakMap,de=new WeakMap;let fe={get(e,t,c){if(e instanceof IDBTransaction){if("done"===t)return ce.get(e);if("objectStoreNames"===t)return e.objectStoreNames||ue.get(e);if("store"===t)return c.objectStoreNames[1]?void 0:c.objectStore(c.objectStoreNames[0])}return wrap(e[t])},set:(e,t,c)=>(e[t]=c,!0),has:(e,t)=>e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e};function wrapFunction(e){return e!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?function getCursorAdvanceMethods(){return oe||(oe=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])}().includes(e)?function(...t){return e.apply(unwrap(this),t),wrap(se.get(this))}:function(...t){return wrap(e.apply(unwrap(this),t))}:function(t,...c){const g=e.call(unwrap(this),t,...c);return ue.set(g,t.sort?t.sort():[t]),wrap(g)}}function transformCachableValue(e){return"function"==typeof e?wrapFunction(e):(e instanceof IDBTransaction&&function cacheDonePromiseForTransaction(e){if(ce.has(e))return;const t=new Promise(((t,c)=>{const unlisten=()=>{e.removeEventListener("complete",complete),e.removeEventListener("error",error),e.removeEventListener("abort",error)},complete=()=>{t(),unlisten()},error=()=>{c(e.error||new DOMException("AbortError","AbortError")),unlisten()};e.addEventListener("complete",complete),e.addEventListener("error",error),e.addEventListener("abort",error)}));ce.set(e,t)}(e),t=e,function getIdbProxyableTypes(){return ae||(ae=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])}().some((e=>t instanceof e))?new Proxy(e,fe):e);var t}function wrap(e){if(e instanceof IDBRequest)return function promisifyRequest(e){const t=new Promise(((t,c)=>{const unlisten=()=>{e.removeEventListener("success",success),e.removeEventListener("error",error)},success=()=>{t(wrap(e.result)),unlisten()},error=()=>{c(e.error),unlisten()};e.addEventListener("success",success),e.addEventListener("error",error)}));return t.then((t=>{t instanceof IDBCursor&&se.set(t,e)})).catch((()=>{})),de.set(t,e),t}(e);if(le.has(e))return le.get(e);const t=transformCachableValue(e);return t!==e&&(le.set(e,t),de.set(t,e)),t}const unwrap=e=>de.get(e);const pe=["get","getKey","getAll","getAllKeys","count"],ge=["put","add","delete","clear"],me=new Map;function getMethod(e,t){if(!(e instanceof IDBDatabase)||t in e||"string"!=typeof t)return;if(me.get(t))return me.get(t);const c=t.replace(/FromIndex$/,""),g=t!==c,w=ge.includes(c);if(!(c in(g?IDBIndex:IDBObjectStore).prototype)||!w&&!pe.includes(c))return;const method=async function(e,...t){const I=this.transaction(e,w?"readwrite":"readonly");let _=I.store;return g&&(_=_.index(t.shift())),(await Promise.all([_[c](...t),w&&I.done]))[0]};return me.set(t,method),method}!function replaceTraps(e){fe=e(fe)}((e=>Object.assign(Object.assign({},e),{get:(t,c,g)=>getMethod(t,c)||e.get(t,c,g),has:(t,c)=>!!getMethod(t,c)||e.has(t,c)})));const he="@firebase/installations",ve="0.6.13",Te=1e4,be=`w:${ve}`,ye="FIS_v2",Ee=36e5,we=new ErrorFactory("installations","Installations",{"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"not-registered":"Firebase Installation is not registered.","installation-not-found":"Firebase Installation not found.","request-failed":'{$requestName} request failed with error "{$serverCode} {$serverStatus}: {$serverMessage}"',"app-offline":"Could not process request. Application offline.","delete-pending-registration":"Can't delete installation while there is a pending registration request."});function isServerError(e){return e instanceof FirebaseError&&e.code.includes("request-failed")}function getInstallationsEndpoint({projectId:e}){return`https://firebaseinstallations.googleapis.com/v1/projects/${e}/installations`}function extractAuthTokenInfoFromResponse(e){return{token:e.token,requestStatus:2,expiresIn:(t=e.expiresIn,Number(t.replace("s","000"))),creationTime:Date.now()};var t}async function getErrorFromResponse(e,t){const c=(await t.json()).error;return we.create("request-failed",{requestName:e,serverCode:c.code,serverMessage:c.message,serverStatus:c.status})}function getHeaders({apiKey:e}){return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":e})}function getHeadersWithAuth(e,{refreshToken:t}){const c=getHeaders(e);return c.append("Authorization",function getAuthorizationHeader(e){return`${ye} ${e}`}(t)),c}async function retryIfServerError(e){const t=await e();return t.status>=500&&t.status<600?e():t}function sleep(e){return new Promise((t=>{setTimeout(t,e)}))}const Se=/^[cdef][\w-]{21}$/;function generateFid(){try{const e=new Uint8Array(17);(self.crypto||self.msCrypto).getRandomValues(e),e[0]=112+e[0]%16;const t=function encode(e){const t=function bufferToBase64UrlSafe(e){return btoa(String.fromCharCode(...e)).replace(/\+/g,"-").replace(/\//g,"_")}(e);return t.substr(0,22)}(e);return Se.test(t)?t:""}catch(e){return""}}function getKey(e){return`${e.appName}!${e.appId}`}const Ie=new Map;function fidChanged(e,t){const c=getKey(e);callFidChangeCallbacks(c,t),function broadcastFidChange(e,t){const c=function getBroadcastChannel(){!_e&&"BroadcastChannel"in self&&(_e=new BroadcastChannel("[Firebase] FID Change"),_e.onmessage=e=>{callFidChangeCallbacks(e.data.key,e.data.fid)});return _e}();c&&c.postMessage({key:e,fid:t});!function closeBroadcastChannel(){0===Ie.size&&_e&&(_e.close(),_e=null)}()}(c,t)}function callFidChangeCallbacks(e,t){const c=Ie.get(e);if(c)for(const e of c)e(t)}let _e=null;const Ae="firebase-installations-store";let ke=null;function getDbPromise(){return ke||(ke=function openDB(e,t,{blocked:c,upgrade:g,blocking:w,terminated:I}={}){const _=indexedDB.open(e,t),k=wrap(_);return g&&_.addEventListener("upgradeneeded",(e=>{g(wrap(_.result),e.oldVersion,e.newVersion,wrap(_.transaction),e)})),c&&_.addEventListener("blocked",(e=>c(e.oldVersion,e.newVersion,e))),k.then((e=>{I&&e.addEventListener("close",(()=>I())),w&&e.addEventListener("versionchange",(e=>w(e.oldVersion,e.newVersion,e)))})).catch((()=>{})),k}("firebase-installations-database",1,{upgrade:(e,t)=>{if(0===t)e.createObjectStore(Ae)}})),ke}async function set(e,t){const c=getKey(e),g=(await getDbPromise()).transaction(Ae,"readwrite"),w=g.objectStore(Ae),I=await w.get(c);return await w.put(t,c),await g.done,I&&I.fid===t.fid||fidChanged(e,t.fid),t}async function remove(e){const t=getKey(e),c=(await getDbPromise()).transaction(Ae,"readwrite");await c.objectStore(Ae).delete(t),await c.done}async function update(e,t){const c=getKey(e),g=(await getDbPromise()).transaction(Ae,"readwrite"),w=g.objectStore(Ae),I=await w.get(c),_=t(I);return void 0===_?await w.delete(c):await w.put(_,c),await g.done,!_||I&&I.fid===_.fid||fidChanged(e,_.fid),_}async function getInstallationEntry(e){let t;const c=await update(e.appConfig,(c=>{const g=function updateOrCreateInstallationEntry(e){const t=e||{fid:generateFid(),registrationStatus:0};return clearTimedOutRequest(t)}(c),w=function triggerRegistrationIfNecessary(e,t){if(0===t.registrationStatus){if(!navigator.onLine){return{installationEntry:t,registrationPromise:Promise.reject(we.create("app-offline"))}}const c={fid:t.fid,registrationStatus:1,registrationTime:Date.now()},g=async function registerInstallation(e,t){try{const c=await async function createInstallationRequest({appConfig:e,heartbeatServiceProvider:t},{fid:c}){const g=getInstallationsEndpoint(e),w=getHeaders(e),I=t.getImmediate({optional:!0});if(I){const e=await I.getHeartbeatsHeader();e&&w.append("x-firebase-client",e)}const _={fid:c,authVersion:ye,appId:e.appId,sdkVersion:be},k={method:"POST",headers:w,body:JSON.stringify(_)},C=await retryIfServerError((()=>fetch(g,k)));if(C.ok){const e=await C.json();return{fid:e.fid||c,registrationStatus:2,refreshToken:e.refreshToken,authToken:extractAuthTokenInfoFromResponse(e.authToken)}}throw await getErrorFromResponse("Create Installation",C)}(e,t);return set(e.appConfig,c)}catch(c){throw isServerError(c)&&409===c.customData.serverCode?await remove(e.appConfig):await set(e.appConfig,{fid:t.fid,registrationStatus:0}),c}}(e,c);return{installationEntry:c,registrationPromise:g}}return 1===t.registrationStatus?{installationEntry:t,registrationPromise:waitUntilFidRegistration(e)}:{installationEntry:t}}(e,g);return t=w.registrationPromise,w.installationEntry}));return""===c.fid?{installationEntry:await t}:{installationEntry:c,registrationPromise:t}}async function waitUntilFidRegistration(e){let t=await updateInstallationRequest(e.appConfig);for(;1===t.registrationStatus;)await sleep(100),t=await updateInstallationRequest(e.appConfig);if(0===t.registrationStatus){const{installationEntry:t,registrationPromise:c}=await getInstallationEntry(e);return c||t}return t}function updateInstallationRequest(e){return update(e,(e=>{if(!e)throw we.create("installation-not-found");return clearTimedOutRequest(e)}))}function clearTimedOutRequest(e){return function hasInstallationRequestTimedOut(e){return 1===e.registrationStatus&&e.registrationTime+Te<Date.now()}(e)?{fid:e.fid,registrationStatus:0}:e}async function generateAuthTokenRequest({appConfig:e,heartbeatServiceProvider:t},c){const g=function getGenerateAuthTokenEndpoint(e,{fid:t}){return`${getInstallationsEndpoint(e)}/${t}/authTokens:generate`}(e,c),w=getHeadersWithAuth(e,c),I=t.getImmediate({optional:!0});if(I){const e=await I.getHeartbeatsHeader();e&&w.append("x-firebase-client",e)}const _={installation:{sdkVersion:be,appId:e.appId}},k={method:"POST",headers:w,body:JSON.stringify(_)},C=await retryIfServerError((()=>fetch(g,k)));if(C.ok){return extractAuthTokenInfoFromResponse(await C.json())}throw await getErrorFromResponse("Generate Auth Token",C)}async function refreshAuthToken(e,t=!1){let c;const g=await update(e.appConfig,(g=>{if(!isEntryRegistered(g))throw we.create("not-registered");const w=g.authToken;if(!t&&function isAuthTokenValid(e){return 2===e.requestStatus&&!function isAuthTokenExpired(e){const t=Date.now();return t<e.creationTime||e.creationTime+e.expiresIn<t+Ee}(e)}(w))return g;if(1===w.requestStatus)return c=async function waitUntilAuthTokenRequest(e,t){let c=await updateAuthTokenRequest(e.appConfig);for(;1===c.authToken.requestStatus;)await sleep(100),c=await updateAuthTokenRequest(e.appConfig);const g=c.authToken;return 0===g.requestStatus?refreshAuthToken(e,t):g}(e,t),g;{if(!navigator.onLine)throw we.create("app-offline");const t=function makeAuthTokenRequestInProgressEntry(e){const t={requestStatus:1,requestTime:Date.now()};return Object.assign(Object.assign({},e),{authToken:t})}(g);return c=async function fetchAuthTokenFromServer(e,t){try{const c=await generateAuthTokenRequest(e,t),g=Object.assign(Object.assign({},t),{authToken:c});return await set(e.appConfig,g),c}catch(c){if(!isServerError(c)||401!==c.customData.serverCode&&404!==c.customData.serverCode){const c=Object.assign(Object.assign({},t),{authToken:{requestStatus:0}});await set(e.appConfig,c)}else await remove(e.appConfig);throw c}}(e,t),t}}));return c?await c:g.authToken}function updateAuthTokenRequest(e){return update(e,(e=>{if(!isEntryRegistered(e))throw we.create("not-registered");return function hasAuthTokenRequestTimedOut(e){return 1===e.requestStatus&&e.requestTime+Te<Date.now()}(e.authToken)?Object.assign(Object.assign({},e),{authToken:{requestStatus:0}}):e}))}function isEntryRegistered(e){return void 0!==e&&2===e.registrationStatus}async function getToken(e,t=!1){const c=e;await async function completeInstallationRegistration(e){const{registrationPromise:t}=await getInstallationEntry(e);t&&await t}(c);return(await refreshAuthToken(c,t)).token}function getMissingValueError(e){return we.create("missing-app-config-values",{valueName:e})}const Ce="installations",publicFactory=e=>{const t=e.getProvider("app").getImmediate(),c=function extractAppConfig(e){if(!e||!e.options)throw getMissingValueError("App Configuration");if(!e.name)throw getMissingValueError("App Name");const t=["projectId","apiKey","appId"];for(const c of t)if(!e.options[c])throw getMissingValueError(c);return{appName:e.name,projectId:e.options.projectId,apiKey:e.options.apiKey,appId:e.options.appId}}(t);return{app:t,appConfig:c,heartbeatServiceProvider:_getProvider(t,"heartbeat"),_delete:()=>Promise.resolve()}},internalFactory=e=>{const t=e.getProvider("app").getImmediate(),c=_getProvider(t,Ce).getImmediate();return{getId:()=>async function getId(e){const t=e,{installationEntry:c,registrationPromise:g}=await getInstallationEntry(t);return g?g.catch(console.error):refreshAuthToken(t).catch(console.error),c.fid}(c),getToken:e=>getToken(c,e)}};!function registerInstallations(){t(new Component(Ce,publicFactory,"PUBLIC")),t(new Component("installations-internal",internalFactory,"PRIVATE"))}(),e(he,ve),e(he,ve,"esm2017");const Re="@firebase/performance",Me="0.7.2",Le=Me,Pe="FB-PERF-TRACE-MEASURE",Oe="_wt_",Ne="_fcp",De="_fid",Be="_lcp",Fe="_inp",qe="_cls",je="@firebase/performance/config",Ue="@firebase/performance/configexpire",$e="Performance",xe=new ErrorFactory("performance",$e,{"trace started":"Trace {$traceName} was started before.","trace stopped":"Trace {$traceName} is not running.","nonpositive trace startTime":"Trace {$traceName} startTime should be positive.","nonpositive trace duration":"Trace {$traceName} duration should be positive.","no window":"Window is not available.","no app id":"App id is not available.","no project id":"Project id is not available.","no api key":"Api key is not available.","invalid cc log":"Attempted to queue invalid cc event","FB not default":"Performance can only start when Firebase app instance is the default one.","RC response not ok":"RC response is not ok","invalid attribute name":"Attribute name {$attributeName} is invalid.","invalid attribute value":"Attribute value {$attributeValue} is invalid.","invalid custom metric name":"Custom metric name {$customMetricName} is invalid","invalid String merger input":"Input for String merger is invalid, contact support team to resolve.","already initialized":"initializePerformance() has already been called with different options. To avoid this error, call initializePerformance() with the same options as when it was originally called, or call getPerformance() to return the already initialized instance."}),He=new class Logger{constructor(e){this.name=e,this._logLevel=_,this._logHandler=defaultLogHandler,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in w))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?I[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,w.DEBUG,...e),this._logHandler(this,w.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,w.VERBOSE,...e),this._logHandler(this,w.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,w.INFO,...e),this._logHandler(this,w.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,w.WARN,...e),this._logHandler(this,w.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,w.ERROR,...e),this._logHandler(this,w.ERROR,...e)}}($e);let Ve,ze,We,Ke;He.logLevel=w.INFO;class Api{constructor(e){if(this.window=e,!e)throw xe.create("no window");this.performance=e.performance,this.PerformanceObserver=e.PerformanceObserver,this.windowLocation=e.location,this.navigator=e.navigator,this.document=e.document,this.navigator&&this.navigator.cookieEnabled&&(this.localStorage=e.localStorage),e.perfMetrics&&e.perfMetrics.onFirstInputDelay&&(this.onFirstInputDelay=e.perfMetrics.onFirstInputDelay),this.onLCP=ot,this.onINP=rt,this.onCLS=D}getUrl(){return this.windowLocation.href.split("?")[0]}mark(e){this.performance&&this.performance.mark&&this.performance.mark(e)}measure(e,t,c){this.performance&&this.performance.measure&&this.performance.measure(e,t,c)}getEntriesByType(e){return this.performance&&this.performance.getEntriesByType?this.performance.getEntriesByType(e):[]}getEntriesByName(e){return this.performance&&this.performance.getEntriesByName?this.performance.getEntriesByName(e):[]}getTimeOrigin(){return this.performance&&(this.performance.timeOrigin||this.performance.timing.navigationStart)}requiredApisAvailable(){return fetch&&Promise&&function areCookiesEnabled(){return!("undefined"==typeof navigator||!navigator.cookieEnabled)}()?!!function isIndexedDBAvailable(){try{return"object"==typeof indexedDB}catch(e){return!1}}()||(He.info("IndexedDB is not supported by current browser"),!1):(He.info("Firebase Performance cannot start if browser does not support fetch and Promise or cookie is disabled."),!1)}setupObserver(e,t){if(!this.PerformanceObserver)return;new this.PerformanceObserver((e=>{for(const c of e.getEntries())t(c)})).observe({entryTypes:[e]})}static getInstance(){return void 0===Ve&&(Ve=new Api(ze)),Ve}}function getIid(){return We}function mergeStrings(e,t){const c=e.length-t.length;if(c<0||c>1)throw xe.create("invalid String merger input");const g=[];for(let c=0;c<e.length;c++)g.push(e.charAt(c)),t.length>c&&g.push(t.charAt(c));return g.join("")}class SettingsService{constructor(){this.instrumentationEnabled=!0,this.dataCollectionEnabled=!0,this.loggingEnabled=!1,this.tracesSamplingRate=1,this.networkRequestsSamplingRate=1,this.logEndPointUrl="https://firebaselogging.googleapis.com/v0cc/log?format=json_proto",this.flTransportEndpointUrl=mergeStrings("hts/frbslgigp.ogepscmv/ieo/eaylg","tp:/ieaeogn-agolai.o/1frlglgc/o"),this.transportKey=mergeStrings("AzSC8r6ReiGqFMyfvgow","Iayx0u-XT3vksVM-pIV"),this.logSource=462,this.logTraceAfterSampling=!1,this.logNetworkAfterSampling=!1,this.configTimeToLive=12}getFlTransportFullUrl(){return this.flTransportEndpointUrl.concat("?key=",this.transportKey)}static getInstance(){return void 0===Ke&&(Ke=new SettingsService),Ke}}var Qe;!function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.VISIBLE=1]="VISIBLE",e[e.HIDDEN=2]="HIDDEN"}(Qe||(Qe={}));const Ge=["firebase_","google_","ga_"],Je=new RegExp("^[a-zA-Z]\\w*$");function getServiceWorkerStatus(){const e=Api.getInstance().navigator;return(null==e?void 0:e.serviceWorker)?e.serviceWorker.controller?2:3:1}function getVisibilityState(){switch(Api.getInstance().document.visibilityState){case"visible":return Qe.VISIBLE;case"hidden":return Qe.HIDDEN;default:return Qe.UNKNOWN}}function getEffectiveConnectionType(){const e=Api.getInstance().navigator.connection;switch(e&&e.effectiveType){case"slow-2g":return 1;case"2g":return 2;case"3g":return 3;case"4g":return 4;default:return 0}}function getAppId(e){var t;const c=null===(t=e.options)||void 0===t?void 0:t.appId;if(!c)throw xe.create("no app id");return c}const Ze="0.0.1",Xe={loggingEnabled:!0},Ye="FIREBASE_INSTALLATIONS_AUTH";function getConfig(e,t){const c=function getStoredConfig(){const e=Api.getInstance().localStorage;if(!e)return;const t=e.getItem(Ue);if(!t||!function configValid(e){return Number(e)>Date.now()}(t))return;const c=e.getItem(je);if(!c)return;try{return JSON.parse(c)}catch(e){return}}();return c?(processConfig(c),Promise.resolve()):function getRemoteConfig(e,t){return function getAuthTokenPromise(e){const t=e.getToken();return t.then((e=>{})),t}(e.installations).then((c=>{const g=function getProjectId(e){var t;const c=null===(t=e.options)||void 0===t?void 0:t.projectId;if(!c)throw xe.create("no project id");return c}(e.app),w=function getApiKey(e){var t;const c=null===(t=e.options)||void 0===t?void 0:t.apiKey;if(!c)throw xe.create("no api key");return c}(e.app),I=new Request(`https://firebaseremoteconfig.googleapis.com/v1/projects/${g}/namespaces/fireperf:fetch?key=${w}`,{method:"POST",headers:{Authorization:`${Ye} ${c}`},body:JSON.stringify({app_instance_id:t,app_instance_id_token:c,app_id:getAppId(e.app),app_version:Le,sdk_version:Ze})});return fetch(I).then((e=>{if(e.ok)return e.json();throw xe.create("RC response not ok")}))})).catch((()=>{He.info(et)}))}(e,t).then(processConfig).then((e=>function storeConfig(e){const t=Api.getInstance().localStorage;if(!e||!t)return;t.setItem(je,JSON.stringify(e)),t.setItem(Ue,String(Date.now()+60*SettingsService.getInstance().configTimeToLive*60*1e3))}(e)),(()=>{}))}const et="Could not fetch config, will use default configs";function processConfig(e){if(!e)return e;const t=SettingsService.getInstance(),c=e.entries||{};return void 0!==c.fpr_enabled?t.loggingEnabled="true"===String(c.fpr_enabled):t.loggingEnabled=Xe.loggingEnabled,c.fpr_log_source?t.logSource=Number(c.fpr_log_source):Xe.logSource&&(t.logSource=Xe.logSource),c.fpr_log_endpoint_url?t.logEndPointUrl=c.fpr_log_endpoint_url:Xe.logEndPointUrl&&(t.logEndPointUrl=Xe.logEndPointUrl),c.fpr_log_transport_key?t.transportKey=c.fpr_log_transport_key:Xe.transportKey&&(t.transportKey=Xe.transportKey),void 0!==c.fpr_vc_network_request_sampling_rate?t.networkRequestsSamplingRate=Number(c.fpr_vc_network_request_sampling_rate):void 0!==Xe.networkRequestsSamplingRate&&(t.networkRequestsSamplingRate=Xe.networkRequestsSamplingRate),void 0!==c.fpr_vc_trace_sampling_rate?t.tracesSamplingRate=Number(c.fpr_vc_trace_sampling_rate):void 0!==Xe.tracesSamplingRate&&(t.tracesSamplingRate=Xe.tracesSamplingRate),t.logTraceAfterSampling=shouldLogAfterSampling(t.tracesSamplingRate),t.logNetworkAfterSampling=shouldLogAfterSampling(t.networkRequestsSamplingRate),e}function shouldLogAfterSampling(e){return Math.random()<=e}let tt,it=1;function getInitializationPromise(e){return it=2,tt=tt||function initializePerf(e){return function getDocumentReadyComplete(){const e=Api.getInstance().document;return new Promise((t=>{if(e&&"complete"!==e.readyState){const handler=()=>{"complete"===e.readyState&&(e.removeEventListener("readystatechange",handler),t())};e.addEventListener("readystatechange",handler)}else t()}))}().then((()=>function getIidPromise(e){const t=e.getId();return t.then((e=>{We=e})),t}(e.installations))).then((t=>getConfig(e,t))).then((()=>changeInitializationStatus()),(()=>changeInitializationStatus()))}(e),tt}function changeInitializationStatus(){it=3}let at,st=3,ct=[],ut=!1;function processQueue(e){setTimeout((()=>{st<=0||(ct.length>0&&dispatchQueueEvents(),processQueue(1e4))}),e)}function dispatchQueueEvents(){const e=ct.splice(0,1e3),t=e.map((e=>({source_extension_json_proto3:e.message,event_time_ms:String(e.eventTime)})));(function postToFlEndpoint(e){const t=SettingsService.getInstance().getFlTransportFullUrl(),c=JSON.stringify(e);return navigator.sendBeacon&&navigator.sendBeacon(t,c)?Promise.resolve():fetch(t,{method:"POST",body:c,keepalive:!0}).then()})({request_time_ms:String(Date.now()),client_info:{client_type:1,js_client_info:{}},log_source:SettingsService.getInstance().logSource,log_event:t}).then((()=>{st=3})).catch((()=>{ct=[...e,...ct],st--,He.info(`Tries left: ${st}.`),processQueue(1e4)}))}function transportHandler(e){return(...t)=>{!function addToQueue(e){if(!e.eventTime||!e.message)throw xe.create("invalid cc log");ct=[...ct,e]}({message:e(...t),eventTime:Date.now()})}}function flushQueuedEvents(){for(;ct.length>0;)dispatchQueueEvents()}function sendLog(e,t){at||(at={send:transportHandler(serializer),flush:flushQueuedEvents}),at.send(e,t)}function logTrace(e){const t=SettingsService.getInstance();!t.instrumentationEnabled&&e.isAuto||(t.dataCollectionEnabled||e.isAuto)&&Api.getInstance().requiredApisAvailable()&&(!function isPerfInitialized(){return 3===it}()?getInitializationPromise(e.performanceController).then((()=>sendTraceLog(e)),(()=>sendTraceLog(e))):sendTraceLog(e))}function sendTraceLog(e){if(!getIid())return;const t=SettingsService.getInstance();t.loggingEnabled&&t.logTraceAfterSampling&&sendLog(e,1)}function serializer(e,t){return 0===t?function serializeNetworkRequest(e){const t={url:e.url,http_method:e.httpMethod||0,http_response_code:200,response_payload_bytes:e.responsePayloadBytes,client_start_time_us:e.startTimeUs,time_to_response_initiated_us:e.timeToResponseInitiatedUs,time_to_response_completed_us:e.timeToResponseCompletedUs},c={application_info:getApplicationInfo(e.performanceController.app),network_request_metric:t};return JSON.stringify(c)}(e):function serializeTrace(e){const t={name:e.name,is_auto:e.isAuto,client_start_time_us:e.startTimeUs,duration_us:e.durationUs};0!==Object.keys(e.counters).length&&(t.counters=e.counters);const c=e.getAttributes();0!==Object.keys(c).length&&(t.custom_attributes=c);const g={application_info:getApplicationInfo(e.performanceController.app),trace_metric:t};return JSON.stringify(g)}(e)}function getApplicationInfo(e){return{google_app_id:getAppId(e),app_instance_id:getIid(),web_app_info:{sdk_version:Le,page_url:Api.getInstance().getUrl(),service_worker_status:getServiceWorkerStatus(),visibility_state:getVisibilityState(),effective_connection_type:getEffectiveConnectionType()},application_process_state:0}}function createNetworkRequestEntry(e,t){const c=t;if(!c||void 0===c.responseStart)return;const g=Api.getInstance().getTimeOrigin(),w=Math.floor(1e3*(c.startTime+g)),I=c.responseStart?Math.floor(1e3*(c.responseStart-c.startTime)):void 0,_=Math.floor(1e3*(c.responseEnd-c.startTime));!function logNetworkRequest(e){const t=SettingsService.getInstance();if(!t.instrumentationEnabled)return;const c=e.url,g=t.logEndPointUrl.split("?")[0],w=t.flTransportEndpointUrl.split("?")[0];c!==g&&c!==w&&t.loggingEnabled&&t.logNetworkAfterSampling&&sendLog(e,0)}({performanceController:e,url:c.name&&c.name.split("?")[0],responsePayloadBytes:c.transferSize,startTimeUs:w,timeToResponseInitiatedUs:I,timeToResponseCompletedUs:_})}const lt=["_fp",Ne,De,Be,qe,Fe];class Trace{constructor(e,t,c=!1,g){this.performanceController=e,this.name=t,this.isAuto=c,this.state=1,this.customAttributes={},this.counters={},this.api=Api.getInstance(),this.randomId=Math.floor(1e6*Math.random()),this.isAuto||(this.traceStartMark=`FB-PERF-TRACE-START-${this.randomId}-${this.name}`,this.traceStopMark=`FB-PERF-TRACE-STOP-${this.randomId}-${this.name}`,this.traceMeasure=g||`${Pe}-${this.randomId}-${this.name}`,g&&this.calculateTraceMetrics())}start(){if(1!==this.state)throw xe.create("trace started",{traceName:this.name});this.api.mark(this.traceStartMark),this.state=2}stop(){if(2!==this.state)throw xe.create("trace stopped",{traceName:this.name});this.state=3,this.api.mark(this.traceStopMark),this.api.measure(this.traceMeasure,this.traceStartMark,this.traceStopMark),this.calculateTraceMetrics(),logTrace(this)}record(e,t,c){if(e<=0)throw xe.create("nonpositive trace startTime",{traceName:this.name});if(t<=0)throw xe.create("nonpositive trace duration",{traceName:this.name});if(this.durationUs=Math.floor(1e3*t),this.startTimeUs=Math.floor(1e3*e),c&&c.attributes&&(this.customAttributes=Object.assign({},c.attributes)),c&&c.metrics)for(const e of Object.keys(c.metrics))isNaN(Number(c.metrics[e]))||(this.counters[e]=Math.floor(Number(c.metrics[e])));logTrace(this)}incrementMetric(e,t=1){void 0===this.counters[e]?this.putMetric(e,t):this.putMetric(e,this.counters[e]+t)}putMetric(e,t){if(!function isValidMetricName(e,t){return!(0===e.length||e.length>100)&&(t&&t.startsWith(Oe)&&lt.indexOf(e)>-1||!e.startsWith("_"))}(e,this.name))throw xe.create("invalid custom metric name",{customMetricName:e});this.counters[e]=function convertMetricValueToInteger(e){const t=Math.floor(e);return t<e&&He.info(`Metric value should be an Integer, setting the value as : ${t}.`),t}(null!=t?t:0)}getMetric(e){return this.counters[e]||0}putAttribute(e,t){const c=function isValidCustomAttributeName(e){return!(0===e.length||e.length>40)&&(!Ge.some((t=>e.startsWith(t)))&&!!e.match(Je))}(e),g=function isValidCustomAttributeValue(e){return 0!==e.length&&e.length<=100}(t);if(c&&g)this.customAttributes[e]=t;else{if(!c)throw xe.create("invalid attribute name",{attributeName:e});if(!g)throw xe.create("invalid attribute value",{attributeValue:t})}}getAttribute(e){return this.customAttributes[e]}removeAttribute(e){void 0!==this.customAttributes[e]&&delete this.customAttributes[e]}getAttributes(){return Object.assign({},this.customAttributes)}setStartTime(e){this.startTimeUs=e}setDuration(e){this.durationUs=e}calculateTraceMetrics(){const e=this.api.getEntriesByName(this.traceMeasure),t=e&&e[0];t&&(this.durationUs=Math.floor(1e3*t.duration),this.startTimeUs=Math.floor(1e3*(t.startTime+this.api.getTimeOrigin())))}static createOobTrace(e,t,c,g,w){const I=Api.getInstance().getUrl();if(!I)return;const _=new Trace(e,Oe+I,!0),k=Math.floor(1e3*Api.getInstance().getTimeOrigin());_.setStartTime(k),t&&t[0]&&(_.setDuration(Math.floor(1e3*t[0].duration)),_.putMetric("domInteractive",Math.floor(1e3*t[0].domInteractive)),_.putMetric("domContentLoadedEventEnd",Math.floor(1e3*t[0].domContentLoadedEventEnd)),_.putMetric("loadEventEnd",Math.floor(1e3*t[0].loadEventEnd)));if(c){const e=c.find((e=>"first-paint"===e.name));e&&e.startTime&&_.putMetric("_fp",Math.floor(1e3*e.startTime));const t=c.find((e=>"first-contentful-paint"===e.name));t&&t.startTime&&_.putMetric(Ne,Math.floor(1e3*t.startTime)),w&&_.putMetric(De,Math.floor(1e3*w))}this.addWebVitalMetric(_,Be,"lcp_element",g.lcp),this.addWebVitalMetric(_,qe,"cls_largestShiftTarget",g.cls),this.addWebVitalMetric(_,Fe,"inp_interactionTarget",g.inp),logTrace(_),function flushLogs(){at&&at.flush()}()}static addWebVitalMetric(e,t,c,g){g&&(e.putMetric(t,Math.floor(1e3*g.value)),g.elementAttribution&&e.putAttribute(c,g.elementAttribution))}static createUserTimingTrace(e,t){logTrace(new Trace(e,t,!1,t))}}let dt,ft={},pt=!1;function setupOobResources(e){getIid()&&(setTimeout((()=>function setupOobTraces(e){const t=Api.getInstance();"onpagehide"in window?t.document.addEventListener("pagehide",(()=>sendOobTrace(e))):t.document.addEventListener("unload",(()=>sendOobTrace(e)));t.document.addEventListener("visibilitychange",(()=>{"hidden"===t.document.visibilityState&&sendOobTrace(e)})),t.onFirstInputDelay&&t.onFirstInputDelay((e=>{dt=e}));t.onLCP((e=>{var t;ft.lcp={value:e.value,elementAttribution:null===(t=e.attribution)||void 0===t?void 0:t.element}})),t.onCLS((e=>{var t;ft.cls={value:e.value,elementAttribution:null===(t=e.attribution)||void 0===t?void 0:t.largestShiftTarget}})),t.onINP((e=>{var t;ft.inp={value:e.value,elementAttribution:null===(t=e.attribution)||void 0===t?void 0:t.interactionTarget}}))}(e)),0),setTimeout((()=>function setupNetworkRequests(e){const t=Api.getInstance(),c=t.getEntriesByType("resource");for(const t of c)createNetworkRequestEntry(e,t);t.setupObserver("resource",(t=>createNetworkRequestEntry(e,t)))}(e)),0),setTimeout((()=>function setupUserTimingTraces(e){const t=Api.getInstance(),c=t.getEntriesByType("measure");for(const t of c)createUserTimingTrace(e,t);t.setupObserver("measure",(t=>createUserTimingTrace(e,t)))}(e)),0))}function createUserTimingTrace(e,t){const c=t.name;c.substring(0,21)!==Pe&&Trace.createUserTimingTrace(e,c)}function sendOobTrace(e){if(!pt){pt=!0;const t=Api.getInstance(),c=t.getEntriesByType("navigation"),g=t.getEntriesByType("paint");setTimeout((()=>{Trace.createOobTrace(e,c,g,ft,dt)}),0)}}class PerformanceController{constructor(e,t){this.app=e,this.installations=t,this.initialized=!1}_init(e){this.initialized||(void 0!==(null==e?void 0:e.dataCollectionEnabled)&&(this.dataCollectionEnabled=e.dataCollectionEnabled),void 0!==(null==e?void 0:e.instrumentationEnabled)&&(this.instrumentationEnabled=e.instrumentationEnabled),Api.getInstance().requiredApisAvailable()?function validateIndexedDBOpenable(){return new Promise(((e,t)=>{try{let c=!0;const g="validate-browser-context-for-indexeddb-analytics-module",w=self.indexedDB.open(g);w.onsuccess=()=>{w.result.close(),c||self.indexedDB.deleteDatabase(g),e(!0)},w.onupgradeneeded=()=>{c=!1},w.onerror=()=>{var e;t((null===(e=w.error)||void 0===e?void 0:e.message)||"")}}catch(e){t(e)}}))}().then((e=>{e&&(!function setupTransportService(){ut||(processQueue(5500),ut=!0)}(),getInitializationPromise(this).then((()=>setupOobResources(this)),(()=>setupOobResources(this))),this.initialized=!0)})).catch((e=>{He.info(`Environment doesn't support IndexedDB: ${e}`)})):He.info('Firebase Performance cannot start if the browser does not support "Fetch" and "Promise", or cookies are disabled.'))}set instrumentationEnabled(e){SettingsService.getInstance().instrumentationEnabled=e}get instrumentationEnabled(){return SettingsService.getInstance().instrumentationEnabled}set dataCollectionEnabled(e){SettingsService.getInstance().dataCollectionEnabled=e}get dataCollectionEnabled(){return SettingsService.getInstance().dataCollectionEnabled}}function getPerformance(e=c()){e=getModularInstance(e);return _getProvider(e,"performance").getImmediate()}function initializePerformance(e,t){e=getModularInstance(e);const c=_getProvider(e,"performance");if(c.isInitialized()){const e=c.getImmediate();if(deepEqual(c.getOptions(),null!=t?t:{}))return e;throw xe.create("already initialized")}return c.initialize({options:t})}function trace(e,t){return e=getModularInstance(e),new Trace(e,t)}const factory=(e,{options:t})=>{const c=e.getProvider("app").getImmediate(),g=e.getProvider("installations-internal").getImmediate();if("[DEFAULT]"!==c.name)throw xe.create("FB not default");if("undefined"==typeof window)throw xe.create("no window");!function setupApi(e){ze=e}(window);const w=new PerformanceController(c,g);return w._init(t),w};!function registerPerformance(){t(new Component("performance",factory,"PUBLIC")),e(Re,Me),e(Re,Me,"esm2017")}();export{getPerformance,initializePerformance,trace};

//# sourceMappingURL=firebase-performance.js.map
