{"version": 3, "file": "firebase-app-compat.js", "sources": ["../util/dist/postinstall.mjs", "../util/src/crypt.ts", "../util/src/deepCopy.ts", "../util/src/global.ts", "../util/src/defaults.ts", "../util/src/deferred.ts", "../util/src/environment.ts", "../util/src/errors.ts", "../util/src/obj.ts", "../util/src/subscribe.ts", "../component/src/component.ts", "../component/src/constants.ts", "../component/src/provider.ts", "../component/src/component_container.ts", "../logger/src/logger.ts", "../../node_modules/idb/build/index.js", "../../node_modules/idb/build/wrap-idb-value.js", "../app/src/platformLoggerService.ts", "../app/src/logger.ts", "../app/src/registerCoreComponents.ts", "../app/src/constants.ts", "../app/src/internal.ts", "../app/src/errors.ts", "../app/src/firebaseApp.ts", "../app/src/firebaseServerApp.ts", "../app/src/api.ts", "../app/src/indexeddb.ts", "../app/src/heartbeatService.ts", "../app/src/index.ts", "../app-compat/src/firebaseApp.ts", "../app-compat/src/errors.ts", "../app-compat/src/firebaseNamespaceCore.ts", "../app-compat/src/firebaseNamespace.ts", "../app-compat/src/logger.ts", "../app-compat/src/index.ts", "../app-compat/src/registerCoreComponents.ts", "compat/app/index.cdn.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// This value is retrieved and hardcoded by the NPM postinstall script\nconst getDefaultsFromPostinstall = () => undefined;\n\nexport { getDefaultsFromPostinstall };\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst stringToByteArray = function (str: string): number[] {\n  // TODO(user): Use native implementations if/when available\n  const out: number[] = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = (c >> 6) | 192;\n      out[p++] = (c & 63) | 128;\n    } else if (\n      (c & 0xfc00) === 0xd800 &&\n      i + 1 < str.length &&\n      (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00\n    ) {\n      // Surrogate Pair\n      c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);\n      out[p++] = (c >> 18) | 240;\n      out[p++] = ((c >> 12) & 63) | 128;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    } else {\n      out[p++] = (c >> 12) | 224;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    }\n  }\n  return out;\n};\n\n/**\n * Turns an array of numbers into the string given by the concatenation of the\n * characters to which the numbers correspond.\n * @param bytes Array of numbers representing characters.\n * @return Stringification of the array.\n */\nconst byteArrayToString = function (bytes: number[]): string {\n  // TODO(user): Use native implementations if/when available\n  const out: string[] = [];\n  let pos = 0,\n    c = 0;\n  while (pos < bytes.length) {\n    const c1 = bytes[pos++];\n    if (c1 < 128) {\n      out[c++] = String.fromCharCode(c1);\n    } else if (c1 > 191 && c1 < 224) {\n      const c2 = bytes[pos++];\n      out[c++] = String.fromCharCode(((c1 & 31) << 6) | (c2 & 63));\n    } else if (c1 > 239 && c1 < 365) {\n      // Surrogate Pair\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      const c4 = bytes[pos++];\n      const u =\n        (((c1 & 7) << 18) | ((c2 & 63) << 12) | ((c3 & 63) << 6) | (c4 & 63)) -\n        0x10000;\n      out[c++] = String.fromCharCode(0xd800 + (u >> 10));\n      out[c++] = String.fromCharCode(0xdc00 + (u & 1023));\n    } else {\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      out[c++] = String.fromCharCode(\n        ((c1 & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)\n      );\n    }\n  }\n  return out.join('');\n};\n\ninterface Base64 {\n  byteToCharMap_: { [key: number]: string } | null;\n  charToByteMap_: { [key: string]: number } | null;\n  byteToCharMapWebSafe_: { [key: number]: string } | null;\n  charToByteMapWebSafe_: { [key: string]: number } | null;\n  ENCODED_VALS_BASE: string;\n  readonly ENCODED_VALS: string;\n  readonly ENCODED_VALS_WEBSAFE: string;\n  HAS_NATIVE_SUPPORT: boolean;\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string;\n  encodeString(input: string, webSafe?: boolean): string;\n  decodeString(input: string, webSafe: boolean): string;\n  decodeStringToByteArray(input: string, webSafe: boolean): number[];\n  init_(): void;\n}\n\n// We define it as an object literal instead of a class because a class compiled down to es5 can't\n// be treeshaked. https://github.com/rollup/rollup/issues/1691\n// Static lookup maps, lazily populated by init_()\n// TODO(dlarocque): Define this as a class, since we no longer target ES5.\nexport const base64: Base64 = {\n  /**\n   * Maps bytes to characters.\n   */\n  byteToCharMap_: null,\n\n  /**\n   * Maps characters to bytes.\n   */\n  charToByteMap_: null,\n\n  /**\n   * Maps bytes to websafe characters.\n   * @private\n   */\n  byteToCharMapWebSafe_: null,\n\n  /**\n   * Maps websafe characters to bytes.\n   * @private\n   */\n  charToByteMapWebSafe_: null,\n\n  /**\n   * Our default alphabet, shared between\n   * ENCODED_VALS and ENCODED_VALS_WEBSAFE\n   */\n  ENCODED_VALS_BASE:\n    'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz' + '0123456789',\n\n  /**\n   * Our default alphabet. Value 64 (=) is special; it means \"nothing.\"\n   */\n  get ENCODED_VALS() {\n    return this.ENCODED_VALS_BASE + '+/=';\n  },\n\n  /**\n   * Our websafe alphabet.\n   */\n  get ENCODED_VALS_WEBSAFE() {\n    return this.ENCODED_VALS_BASE + '-_.';\n  },\n\n  /**\n   * Whether this browser supports the atob and btoa functions. This extension\n   * started at Mozilla but is now implemented by many browsers. We use the\n   * ASSUME_* variables to avoid pulling in the full useragent detection library\n   * but still allowing the standard per-browser compilations.\n   *\n   */\n  HAS_NATIVE_SUPPORT: typeof atob === 'function',\n\n  /**\n   * Base64-encode an array of bytes.\n   *\n   * @param input An array of bytes (numbers with\n   *     value in [0, 255]) to encode.\n   * @param webSafe Boolean indicating we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string {\n    if (!Array.isArray(input)) {\n      throw Error('encodeByteArray takes an array as a parameter');\n    }\n\n    this.init_();\n\n    const byteToCharMap = webSafe\n      ? this.byteToCharMapWebSafe_!\n      : this.byteToCharMap_!;\n\n    const output = [];\n\n    for (let i = 0; i < input.length; i += 3) {\n      const byte1 = input[i];\n      const haveByte2 = i + 1 < input.length;\n      const byte2 = haveByte2 ? input[i + 1] : 0;\n      const haveByte3 = i + 2 < input.length;\n      const byte3 = haveByte3 ? input[i + 2] : 0;\n\n      const outByte1 = byte1 >> 2;\n      const outByte2 = ((byte1 & 0x03) << 4) | (byte2 >> 4);\n      let outByte3 = ((byte2 & 0x0f) << 2) | (byte3 >> 6);\n      let outByte4 = byte3 & 0x3f;\n\n      if (!haveByte3) {\n        outByte4 = 64;\n\n        if (!haveByte2) {\n          outByte3 = 64;\n        }\n      }\n\n      output.push(\n        byteToCharMap[outByte1],\n        byteToCharMap[outByte2],\n        byteToCharMap[outByte3],\n        byteToCharMap[outByte4]\n      );\n    }\n\n    return output.join('');\n  },\n\n  /**\n   * Base64-encode a string.\n   *\n   * @param input A string to encode.\n   * @param webSafe If true, we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeString(input: string, webSafe?: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return btoa(input);\n    }\n    return this.encodeByteArray(stringToByteArray(input), webSafe);\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * @param input to decode.\n   * @param webSafe True if we should use the\n   *     alternative alphabet.\n   * @return string representing the decoded value.\n   */\n  decodeString(input: string, webSafe: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return atob(input);\n    }\n    return byteArrayToString(this.decodeStringToByteArray(input, webSafe));\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * In base-64 decoding, groups of four characters are converted into three\n   * bytes.  If the encoder did not apply padding, the input length may not\n   * be a multiple of 4.\n   *\n   * In this case, the last group will have fewer than 4 characters, and\n   * padding will be inferred.  If the group has one or two characters, it decodes\n   * to one byte.  If the group has three characters, it decodes to two bytes.\n   *\n   * @param input Input to decode.\n   * @param webSafe True if we should use the web-safe alphabet.\n   * @return bytes representing the decoded value.\n   */\n  decodeStringToByteArray(input: string, webSafe: boolean): number[] {\n    this.init_();\n\n    const charToByteMap = webSafe\n      ? this.charToByteMapWebSafe_!\n      : this.charToByteMap_!;\n\n    const output: number[] = [];\n\n    for (let i = 0; i < input.length; ) {\n      const byte1 = charToByteMap[input.charAt(i++)];\n\n      const haveByte2 = i < input.length;\n      const byte2 = haveByte2 ? charToByteMap[input.charAt(i)] : 0;\n      ++i;\n\n      const haveByte3 = i < input.length;\n      const byte3 = haveByte3 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      const haveByte4 = i < input.length;\n      const byte4 = haveByte4 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      if (byte1 == null || byte2 == null || byte3 == null || byte4 == null) {\n        throw new DecodeBase64StringError();\n      }\n\n      const outByte1 = (byte1 << 2) | (byte2 >> 4);\n      output.push(outByte1);\n\n      if (byte3 !== 64) {\n        const outByte2 = ((byte2 << 4) & 0xf0) | (byte3 >> 2);\n        output.push(outByte2);\n\n        if (byte4 !== 64) {\n          const outByte3 = ((byte3 << 6) & 0xc0) | byte4;\n          output.push(outByte3);\n        }\n      }\n    }\n\n    return output;\n  },\n\n  /**\n   * Lazy static initialization function. Called before\n   * accessing any of the static map variables.\n   * @private\n   */\n  init_() {\n    if (!this.byteToCharMap_) {\n      this.byteToCharMap_ = {};\n      this.charToByteMap_ = {};\n      this.byteToCharMapWebSafe_ = {};\n      this.charToByteMapWebSafe_ = {};\n\n      // We want quick mappings back and forth, so we precompute two maps.\n      for (let i = 0; i < this.ENCODED_VALS.length; i++) {\n        this.byteToCharMap_[i] = this.ENCODED_VALS.charAt(i);\n        this.charToByteMap_[this.byteToCharMap_[i]] = i;\n        this.byteToCharMapWebSafe_[i] = this.ENCODED_VALS_WEBSAFE.charAt(i);\n        this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[i]] = i;\n\n        // Be forgiving when decoding and correctly decode both encodings.\n        if (i >= this.ENCODED_VALS_BASE.length) {\n          this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(i)] = i;\n          this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(i)] = i;\n        }\n      }\n    }\n  }\n};\n\n/**\n * An error encountered while decoding base64 string.\n */\nexport class DecodeBase64StringError extends Error {\n  readonly name = 'DecodeBase64StringError';\n}\n\n/**\n * URL-safe base64 encoding\n */\nexport const base64Encode = function (str: string): string {\n  const utf8Bytes = stringToByteArray(str);\n  return base64.encodeByteArray(utf8Bytes, true);\n};\n\n/**\n * URL-safe base64 encoding (without \".\" padding in the end).\n * e.g. Used in JSON Web Token (JWT) parts.\n */\nexport const base64urlEncodeWithoutPadding = function (str: string): string {\n  // Use base64url encoding and remove padding in the end (dot characters).\n  return base64Encode(str).replace(/\\./g, '');\n};\n\n/**\n * URL-safe base64 decoding\n *\n * NOTE: DO NOT use the global atob() function - it does NOT support the\n * base64Url variant encoding.\n *\n * @param str To be decoded\n * @return Decoded result, if possible\n */\nexport const base64Decode = function (str: string): string | null {\n  try {\n    return base64.decodeString(str, true);\n  } catch (e) {\n    console.error('base64Decode failed: ', e);\n  }\n  return null;\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Do a deep-copy of basic JavaScript Objects or Arrays.\n */\nexport function deepCopy<T>(value: T): T {\n  return deepExtend(undefined, value) as T;\n}\n\n/**\n * Copy properties from source to target (recursively allows extension\n * of Objects and Arrays).  Scalar values in the target are over-written.\n * If target is undefined, an object of the appropriate type will be created\n * (and returned).\n *\n * We recursively copy all child properties of plain Objects in the source- so\n * that namespace- like dictionaries are merged.\n *\n * Note that the target can be a function, in which case the properties in\n * the source Object are copied onto it as static properties of the Function.\n *\n * Note: we don't merge __proto__ to prevent prototype pollution\n */\nexport function deepExtend(target: unknown, source: unknown): unknown {\n  if (!(source instanceof Object)) {\n    return source;\n  }\n\n  switch (source.constructor) {\n    case Date:\n      // Treat Dates like scalars; if the target date object had any child\n      // properties - they will be lost!\n      const dateValue = source as Date;\n      return new Date(dateValue.getTime());\n\n    case Object:\n      if (target === undefined) {\n        target = {};\n      }\n      break;\n    case Array:\n      // Always copy the array source and overwrite the target.\n      target = [];\n      break;\n\n    default:\n      // Not a plain Object - treat it as a scalar.\n      return source;\n  }\n\n  for (const prop in source) {\n    // use isValidKey to guard against prototype pollution. See https://snyk.io/vuln/SNYK-JS-LODASH-450202\n    if (!source.hasOwnProperty(prop) || !isValidKey(prop)) {\n      continue;\n    }\n    (target as Record<string, unknown>)[prop] = deepExtend(\n      (target as Record<string, unknown>)[prop],\n      (source as Record<string, unknown>)[prop]\n    );\n  }\n\n  return target;\n}\n\nfunction isValidKey(key: string): boolean {\n  return key !== '__proto__';\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Polyfill for `globalThis` object.\n * @returns the `globalThis` object for the given environment.\n * @public\n */\nexport function getGlobal(): typeof globalThis {\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n  throw new Error('Unable to locate global object.');\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64Decode } from './crypt';\nimport { getGlobal } from './global';\nimport { getDefaultsFromPostinstall } from './postinstall';\n\n/**\n * Keys for experimental properties on the `FirebaseDefaults` object.\n * @public\n */\nexport type ExperimentalKey = 'authTokenSyncURL' | 'authIdTokenMaxAge';\n\n/**\n * An object that can be injected into the environment as __FIREBASE_DEFAULTS__,\n * either as a property of globalThis, a shell environment variable, or a\n * cookie.\n *\n * This object can be used to automatically configure and initialize\n * a Firebase app as well as any emulators.\n *\n * @public\n */\nexport interface FirebaseDefaults {\n  config?: Record<string, string>;\n  emulatorHosts?: Record<string, string>;\n  _authTokenSyncURL?: string;\n  _authIdTokenMaxAge?: number;\n  /**\n   * Override Firebase's runtime environment detection and\n   * force the SDK to act as if it were in the specified environment.\n   */\n  forceEnvironment?: 'browser' | 'node';\n  [key: string]: unknown;\n}\n\ndeclare global {\n  // Need `var` for this to work.\n  // eslint-disable-next-line no-var\n  var __FIREBASE_DEFAULTS__: FirebaseDefaults | undefined;\n}\n\nconst getDefaultsFromGlobal = (): FirebaseDefaults | undefined =>\n  getGlobal().__FIREBASE_DEFAULTS__;\n\n/**\n * Attempt to read defaults from a JSON string provided to\n * process(.)env(.)__FIREBASE_DEFAULTS__ or a JSON file whose path is in\n * process(.)env(.)__FIREBASE_DEFAULTS_PATH__\n * The dots are in parens because certain compilers (Vite?) cannot\n * handle seeing that variable in comments.\n * See https://github.com/firebase/firebase-js-sdk/issues/6838\n */\nconst getDefaultsFromEnvVariable = (): FirebaseDefaults | undefined => {\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return;\n  }\n  const defaultsJsonString = process.env.__FIREBASE_DEFAULTS__;\n  if (defaultsJsonString) {\n    return JSON.parse(defaultsJsonString);\n  }\n};\n\nconst getDefaultsFromCookie = (): FirebaseDefaults | undefined => {\n  if (typeof document === 'undefined') {\n    return;\n  }\n  let match;\n  try {\n    match = document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/);\n  } catch (e) {\n    // Some environments such as Angular Universal SSR have a\n    // `document` object but error on accessing `document.cookie`.\n    return;\n  }\n  const decoded = match && base64Decode(match[1]);\n  return decoded && JSON.parse(decoded);\n};\n\n/**\n * Get the __FIREBASE_DEFAULTS__ object. It checks in order:\n * (1) if such an object exists as a property of `globalThis`\n * (2) if such an object was provided on a shell environment variable\n * (3) if such an object exists in a cookie\n * @public\n */\nexport const getDefaults = (): FirebaseDefaults | undefined => {\n  try {\n    return (\n      getDefaultsFromPostinstall() ||\n      getDefaultsFromGlobal() ||\n      getDefaultsFromEnvVariable() ||\n      getDefaultsFromCookie()\n    );\n  } catch (e) {\n    /**\n     * Catch-all for being unable to get __FIREBASE_DEFAULTS__ due\n     * to any environment case we have not accounted for. Log to\n     * info instead of swallowing so we can find these unknown cases\n     * and add paths for them if needed.\n     */\n    console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);\n    return;\n  }\n};\n\n/**\n * Returns emulator host stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a URL host formatted like `127.0.0.1:9999` or `[::1]:4000` if available\n * @public\n */\nexport const getDefaultEmulatorHost = (\n  productName: string\n): string | undefined => getDefaults()?.emulatorHosts?.[productName];\n\n/**\n * Returns emulator hostname and port stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a pair of hostname and port like `[\"::1\", 4000]` if available\n * @public\n */\nexport const getDefaultEmulatorHostnameAndPort = (\n  productName: string\n): [hostname: string, port: number] | undefined => {\n  const host = getDefaultEmulatorHost(productName);\n  if (!host) {\n    return undefined;\n  }\n  const separatorIndex = host.lastIndexOf(':'); // Finding the last since IPv6 addr also has colons.\n  if (separatorIndex <= 0 || separatorIndex + 1 === host.length) {\n    throw new Error(`Invalid host ${host} with no separate hostname and port!`);\n  }\n  // eslint-disable-next-line no-restricted-globals\n  const port = parseInt(host.substring(separatorIndex + 1), 10);\n  if (host[0] === '[') {\n    // Bracket-quoted `[ipv6addr]:port` => return \"ipv6addr\" (without brackets).\n    return [host.substring(1, separatorIndex - 1), port];\n  } else {\n    return [host.substring(0, separatorIndex), port];\n  }\n};\n\n/**\n * Returns Firebase app config stored in the __FIREBASE_DEFAULTS__ object.\n * @public\n */\nexport const getDefaultAppConfig = (): Record<string, string> | undefined =>\n  getDefaults()?.config;\n\n/**\n * Returns an experimental setting on the __FIREBASE_DEFAULTS__ object (properties\n * prefixed by \"_\")\n * @public\n */\nexport const getExperimentalSetting = <T extends ExperimentalKey>(\n  name: T\n): FirebaseDefaults[`_${T}`] =>\n  getDefaults()?.[`_${name}`] as FirebaseDefaults[`_${T}`];\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport class Deferred<R> {\n  promise: Promise<R>;\n  reject: (value?: unknown) => void = () => {};\n  resolve: (value?: unknown) => void = () => {};\n  constructor() {\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = resolve as (value?: unknown) => void;\n      this.reject = reject as (value?: unknown) => void;\n    });\n  }\n\n  /**\n   * Our API internals are not promisified and cannot because our callback APIs have subtle expectations around\n   * invoking promises inline, which Promises are forbidden to do. This method accepts an optional node-style callback\n   * and returns a node-style callback which will resolve or reject the Deferred's promise.\n   */\n  wrapCallback(\n    callback?: (error?: unknown, value?: unknown) => void\n  ): (error: unknown, value?: unknown) => void {\n    return (error, value?) => {\n      if (error) {\n        this.reject(error);\n      } else {\n        this.resolve(value);\n      }\n      if (typeof callback === 'function') {\n        // Attaching noop handler just in case developer wasn't expecting\n        // promises\n        this.promise.catch(() => {});\n\n        // Some of our callbacks don't expect a value and our own tests\n        // assert that the parameter length is 1\n        if (callback.length === 1) {\n          callback(error);\n        } else {\n          callback(error, value);\n        }\n      }\n    };\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSTANTS } from './constants';\nimport { getDefaults } from './defaults';\n\n/**\n * Type placeholder for `WorkerGlobalScope` from `webworker`\n */\ndeclare class WorkerGlobalScope {}\n\n/**\n * Returns navigator.userAgent string or '' if it's not defined.\n * @return user agent string\n */\nexport function getUA(): string {\n  if (\n    typeof navigator !== 'undefined' &&\n    typeof navigator['userAgent'] === 'string'\n  ) {\n    return navigator['userAgent'];\n  } else {\n    return '';\n  }\n}\n\n/**\n * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.\n *\n * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap\n * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally\n * wait for a callback.\n */\nexport function isMobileCordova(): boolean {\n  return (\n    typeof window !== 'undefined' &&\n    // @ts-ignore Setting up an broadly applicable index signature for Window\n    // just to deal with this case would probably be a bad idea.\n    !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) &&\n    /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA())\n  );\n}\n\n/**\n * Detect Node.js.\n *\n * @return true if Node.js environment is detected or specified.\n */\n// Node detection logic from: https://github.com/iliakan/detect-node/\nexport function isNode(): boolean {\n  const forceEnvironment = getDefaults()?.forceEnvironment;\n  if (forceEnvironment === 'node') {\n    return true;\n  } else if (forceEnvironment === 'browser') {\n    return false;\n  }\n\n  try {\n    return (\n      Object.prototype.toString.call(global.process) === '[object process]'\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Detect Browser Environment.\n * Note: This will return true for certain test frameworks that are incompletely\n * mimicking a browser, and should not lead to assuming all browser APIs are\n * available.\n */\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined' || isWebWorker();\n}\n\n/**\n * Detect Web Worker context.\n */\nexport function isWebWorker(): boolean {\n  return (\n    typeof WorkerGlobalScope !== 'undefined' &&\n    typeof self !== 'undefined' &&\n    self instanceof WorkerGlobalScope\n  );\n}\n\n/**\n * Detect Cloudflare Worker context.\n */\nexport function isCloudflareWorker(): boolean {\n  return (\n    typeof navigator !== 'undefined' &&\n    navigator.userAgent === 'Cloudflare-Workers'\n  );\n}\n\n/**\n * Detect browser extensions (Chrome and Firefox at least).\n */\ninterface BrowserRuntime {\n  id?: unknown;\n}\ndeclare const chrome: { runtime?: BrowserRuntime };\ndeclare const browser: { runtime?: BrowserRuntime };\nexport function isBrowserExtension(): boolean {\n  const runtime =\n    typeof chrome === 'object'\n      ? chrome.runtime\n      : typeof browser === 'object'\n      ? browser.runtime\n      : undefined;\n  return typeof runtime === 'object' && runtime.id !== undefined;\n}\n\n/**\n * Detect React Native.\n *\n * @return true if ReactNative environment is detected.\n */\nexport function isReactNative(): boolean {\n  return (\n    typeof navigator === 'object' && navigator['product'] === 'ReactNative'\n  );\n}\n\n/** Detects Electron apps. */\nexport function isElectron(): boolean {\n  return getUA().indexOf('Electron/') >= 0;\n}\n\n/** Detects Internet Explorer. */\nexport function isIE(): boolean {\n  const ua = getUA();\n  return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\n}\n\n/** Detects Universal Windows Platform apps. */\nexport function isUWP(): boolean {\n  return getUA().indexOf('MSAppHost/') >= 0;\n}\n\n/**\n * Detect whether the current SDK build is the Node version.\n *\n * @return true if it's the Node SDK build.\n */\nexport function isNodeSdk(): boolean {\n  return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;\n}\n\n/** Returns true if we are running in Safari. */\nexport function isSafari(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    navigator.userAgent.includes('Safari') &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/**\n * This method checks if indexedDB is supported by current browser/service worker context\n * @return true if indexedDB is supported by current browser/service worker context\n */\nexport function isIndexedDBAvailable(): boolean {\n  try {\n    return typeof indexedDB === 'object';\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject\n * if errors occur during the database open operation.\n *\n * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox\n * private browsing)\n */\nexport function validateIndexedDBOpenable(): Promise<boolean> {\n  return new Promise((resolve, reject) => {\n    try {\n      let preExist: boolean = true;\n      const DB_CHECK_NAME =\n        'validate-browser-context-for-indexeddb-analytics-module';\n      const request = self.indexedDB.open(DB_CHECK_NAME);\n      request.onsuccess = () => {\n        request.result.close();\n        // delete database only when it doesn't pre-exist\n        if (!preExist) {\n          self.indexedDB.deleteDatabase(DB_CHECK_NAME);\n        }\n        resolve(true);\n      };\n      request.onupgradeneeded = () => {\n        preExist = false;\n      };\n\n      request.onerror = () => {\n        reject(request.error?.message || '');\n      };\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n\n/**\n *\n * This method checks whether cookie is enabled within current browser\n * @return true if cookie is enabled within current browser\n */\nexport function areCookiesEnabled(): boolean {\n  if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {\n    return false;\n  }\n  return true;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function contains<T extends object>(obj: T, key: string): boolean {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\nexport function safeGet<T extends object, K extends keyof T>(\n  obj: T,\n  key: K\n): T[K] | undefined {\n  if (Object.prototype.hasOwnProperty.call(obj, key)) {\n    return obj[key];\n  } else {\n    return undefined;\n  }\n}\n\nexport function isEmpty(obj: object): obj is {} {\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport function map<K extends string, V, U>(\n  obj: { [key in K]: V },\n  fn: (value: V, key: K, obj: { [key in K]: V }) => U,\n  contextObj?: unknown\n): { [key in K]: U } {\n  const res: Partial<{ [key in K]: U }> = {};\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      res[key] = fn.call(contextObj, obj[key], key, obj);\n    }\n  }\n  return res as { [key in K]: U };\n}\n\n/**\n * Deep equal two objects. Support Arrays and Objects.\n */\nexport function deepEqual(a: object, b: object): boolean {\n  if (a === b) {\n    return true;\n  }\n\n  const aKeys = Object.keys(a);\n  const bKeys = Object.keys(b);\n  for (const k of aKeys) {\n    if (!bKeys.includes(k)) {\n      return false;\n    }\n\n    const aProp = (a as Record<string, unknown>)[k];\n    const bProp = (b as Record<string, unknown>)[k];\n    if (isObject(aProp) && isObject(bProp)) {\n      if (!deepEqual(aProp, bProp)) {\n        return false;\n      }\n    } else if (aProp !== bProp) {\n      return false;\n    }\n  }\n\n  for (const k of bKeys) {\n    if (!aKeys.includes(k)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction isObject(thing: unknown): thing is object {\n  return thing !== null && typeof thing === 'object';\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport type NextFn<T> = (value: T) => void;\nexport type ErrorFn = (error: Error) => void;\nexport type CompleteFn = () => void;\n\nexport interface Observer<T> {\n  // Called once for each value in a stream of values.\n  next: NextFn<T>;\n\n  // A stream terminates by a single call to EITHER error() or complete().\n  error: ErrorFn;\n\n  // No events will be sent to next() once complete() is called.\n  complete: CompleteFn;\n}\n\nexport type PartialObserver<T> = Partial<Observer<T>>;\n\n// TODO: Support also Unsubscribe.unsubscribe?\nexport type Unsubscribe = () => void;\n\n/**\n * The Subscribe interface has two forms - passing the inline function\n * callbacks, or a object interface with callback properties.\n */\nexport interface Subscribe<T> {\n  (next?: NextFn<T>, error?: ErrorFn, complete?: CompleteFn): Unsubscribe;\n  (observer: PartialObserver<T>): Unsubscribe;\n}\n\nexport interface Observable<T> {\n  // Subscribe method\n  subscribe: Subscribe<T>;\n}\n\nexport type Executor<T> = (observer: Observer<T>) => void;\n\n/**\n * Helper to make a Subscribe function (just like Promise helps make a\n * Thenable).\n *\n * @param executor Function which can make calls to a single Observer\n *     as a proxy.\n * @param onNoObservers Callback when count of Observers goes to zero.\n */\nexport function createSubscribe<T>(\n  executor: Executor<T>,\n  onNoObservers?: Executor<T>\n): Subscribe<T> {\n  const proxy = new ObserverProxy<T>(executor, onNoObservers);\n  return proxy.subscribe.bind(proxy);\n}\n\n/**\n * Implement fan-out for any number of Observers attached via a subscribe\n * function.\n */\nclass ObserverProxy<T> implements Observer<T> {\n  private observers: Array<Observer<T>> | undefined = [];\n  private unsubscribes: Unsubscribe[] = [];\n  private onNoObservers: Executor<T> | undefined;\n  private observerCount = 0;\n  // Micro-task scheduling by calling task.then().\n  private task = Promise.resolve();\n  private finalized = false;\n  private finalError?: Error;\n\n  /**\n   * @param executor Function which can make calls to a single Observer\n   *     as a proxy.\n   * @param onNoObservers Callback when count of Observers goes to zero.\n   */\n  constructor(executor: Executor<T>, onNoObservers?: Executor<T>) {\n    this.onNoObservers = onNoObservers;\n    // Call the executor asynchronously so subscribers that are called\n    // synchronously after the creation of the subscribe function\n    // can still receive the very first value generated in the executor.\n    this.task\n      .then(() => {\n        executor(this);\n      })\n      .catch(e => {\n        this.error(e);\n      });\n  }\n\n  next(value: T): void {\n    this.forEachObserver((observer: Observer<T>) => {\n      observer.next(value);\n    });\n  }\n\n  error(error: Error): void {\n    this.forEachObserver((observer: Observer<T>) => {\n      observer.error(error);\n    });\n    this.close(error);\n  }\n\n  complete(): void {\n    this.forEachObserver((observer: Observer<T>) => {\n      observer.complete();\n    });\n    this.close();\n  }\n\n  /**\n   * Subscribe function that can be used to add an Observer to the fan-out list.\n   *\n   * - We require that no event is sent to a subscriber synchronously to their\n   *   call to subscribe().\n   */\n  subscribe(\n    nextOrObserver?: NextFn<T> | PartialObserver<T>,\n    error?: ErrorFn,\n    complete?: CompleteFn\n  ): Unsubscribe {\n    let observer: Observer<T>;\n\n    if (\n      nextOrObserver === undefined &&\n      error === undefined &&\n      complete === undefined\n    ) {\n      throw new Error('Missing Observer.');\n    }\n\n    // Assemble an Observer object when passed as callback functions.\n    if (\n      implementsAnyMethods(nextOrObserver as { [key: string]: unknown }, [\n        'next',\n        'error',\n        'complete'\n      ])\n    ) {\n      observer = nextOrObserver as Observer<T>;\n    } else {\n      observer = {\n        next: nextOrObserver as NextFn<T>,\n        error,\n        complete\n      } as Observer<T>;\n    }\n\n    if (observer.next === undefined) {\n      observer.next = noop as NextFn<T>;\n    }\n    if (observer.error === undefined) {\n      observer.error = noop as ErrorFn;\n    }\n    if (observer.complete === undefined) {\n      observer.complete = noop as CompleteFn;\n    }\n\n    const unsub = this.unsubscribeOne.bind(this, this.observers!.length);\n\n    // Attempt to subscribe to a terminated Observable - we\n    // just respond to the Observer with the final error or complete\n    // event.\n    if (this.finalized) {\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      this.task.then(() => {\n        try {\n          if (this.finalError) {\n            observer.error(this.finalError);\n          } else {\n            observer.complete();\n          }\n        } catch (e) {\n          // nothing\n        }\n        return;\n      });\n    }\n\n    this.observers!.push(observer as Observer<T>);\n\n    return unsub;\n  }\n\n  // Unsubscribe is synchronous - we guarantee that no events are sent to\n  // any unsubscribed Observer.\n  private unsubscribeOne(i: number): void {\n    if (this.observers === undefined || this.observers[i] === undefined) {\n      return;\n    }\n\n    delete this.observers[i];\n\n    this.observerCount -= 1;\n    if (this.observerCount === 0 && this.onNoObservers !== undefined) {\n      this.onNoObservers(this);\n    }\n  }\n\n  private forEachObserver(fn: (observer: Observer<T>) => void): void {\n    if (this.finalized) {\n      // Already closed by previous event....just eat the additional values.\n      return;\n    }\n\n    // Since sendOne calls asynchronously - there is no chance that\n    // this.observers will become undefined.\n    for (let i = 0; i < this.observers!.length; i++) {\n      this.sendOne(i, fn);\n    }\n  }\n\n  // Call the Observer via one of it's callback function. We are careful to\n  // confirm that the observe has not been unsubscribed since this asynchronous\n  // function had been queued.\n  private sendOne(i: number, fn: (observer: Observer<T>) => void): void {\n    // Execute the callback asynchronously\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.task.then(() => {\n      if (this.observers !== undefined && this.observers[i] !== undefined) {\n        try {\n          fn(this.observers[i]);\n        } catch (e) {\n          // Ignore exceptions raised in Observers or missing methods of an\n          // Observer.\n          // Log error to console. b/31404806\n          if (typeof console !== 'undefined' && console.error) {\n            console.error(e);\n          }\n        }\n      }\n    });\n  }\n\n  private close(err?: Error): void {\n    if (this.finalized) {\n      return;\n    }\n    this.finalized = true;\n    if (err !== undefined) {\n      this.finalError = err;\n    }\n    // Proxy is no longer needed - garbage collect references\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.task.then(() => {\n      this.observers = undefined;\n      this.onNoObservers = undefined;\n    });\n  }\n}\n\n/** Turn synchronous function into one called asynchronously. */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function async(fn: Function, onError?: ErrorFn): Function {\n  return (...args: unknown[]) => {\n    Promise.resolve(true)\n      .then(() => {\n        fn(...args);\n      })\n      .catch((error: Error) => {\n        if (onError) {\n          onError(error);\n        }\n      });\n  };\n}\n\n/**\n * Return true if the object passed in implements any of the named methods.\n */\nfunction implementsAnyMethods(\n  obj: { [key: string]: unknown },\n  methods: string[]\n): boolean {\n  if (typeof obj !== 'object' || obj === null) {\n    return false;\n  }\n\n  for (const method of methods) {\n    if (method in obj && typeof obj[method] === 'function') {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction noop(): void {\n  // do nothing\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const DEFAULT_ENTRY_NAME = '[DEFAULT]';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Deferred } from '@firebase/util';\nimport { ComponentContainer } from './component_container';\nimport { DEFAULT_ENTRY_NAME } from './constants';\nimport {\n  InitializeOptions,\n  InstantiationMode,\n  Name,\n  NameServiceMapping,\n  OnInitCallBack\n} from './types';\nimport { Component } from './component';\n\n/**\n * Provider for instance for service name T, e.g. 'auth', 'auth-internal'\n * NameServiceMapping[T] is an alias for the type of the instance\n */\nexport class Provider<T extends Name> {\n  private component: Component<T> | null = null;\n  private readonly instances: Map<string, NameServiceMapping[T]> = new Map();\n  private readonly instancesDeferred: Map<\n    string,\n    Deferred<NameServiceMapping[T]>\n  > = new Map();\n  private readonly instancesOptions: Map<string, Record<string, unknown>> =\n    new Map();\n  private onInitCallbacks: Map<string, Set<OnInitCallBack<T>>> = new Map();\n\n  constructor(\n    private readonly name: T,\n    private readonly container: ComponentContainer\n  ) {}\n\n  /**\n   * @param identifier A provider can provide multiple instances of a service\n   * if this.component.multipleInstances is true.\n   */\n  get(identifier?: string): Promise<NameServiceMapping[T]> {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n\n    if (!this.instancesDeferred.has(normalizedIdentifier)) {\n      const deferred = new Deferred<NameServiceMapping[T]>();\n      this.instancesDeferred.set(normalizedIdentifier, deferred);\n\n      if (\n        this.isInitialized(normalizedIdentifier) ||\n        this.shouldAutoInitialize()\n      ) {\n        // initialize the service if it can be auto-initialized\n        try {\n          const instance = this.getOrInitializeService({\n            instanceIdentifier: normalizedIdentifier\n          });\n          if (instance) {\n            deferred.resolve(instance);\n          }\n        } catch (e) {\n          // when the instance factory throws an exception during get(), it should not cause\n          // a fatal error. We just return the unresolved promise in this case.\n        }\n      }\n    }\n\n    return this.instancesDeferred.get(normalizedIdentifier)!.promise;\n  }\n\n  /**\n   *\n   * @param options.identifier A provider can provide multiple instances of a service\n   * if this.component.multipleInstances is true.\n   * @param options.optional If optional is false or not provided, the method throws an error when\n   * the service is not immediately available.\n   * If optional is true, the method returns null if the service is not immediately available.\n   */\n  getImmediate(options: {\n    identifier?: string;\n    optional: true;\n  }): NameServiceMapping[T] | null;\n  getImmediate(options?: {\n    identifier?: string;\n    optional?: false;\n  }): NameServiceMapping[T];\n  getImmediate(options?: {\n    identifier?: string;\n    optional?: boolean;\n  }): NameServiceMapping[T] | null {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(\n      options?.identifier\n    );\n    const optional = options?.optional ?? false;\n\n    if (\n      this.isInitialized(normalizedIdentifier) ||\n      this.shouldAutoInitialize()\n    ) {\n      try {\n        return this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        });\n      } catch (e) {\n        if (optional) {\n          return null;\n        } else {\n          throw e;\n        }\n      }\n    } else {\n      // In case a component is not initialized and should/cannot be auto-initialized at the moment, return null if the optional flag is set, or throw\n      if (optional) {\n        return null;\n      } else {\n        throw Error(`Service ${this.name} is not available`);\n      }\n    }\n  }\n\n  getComponent(): Component<T> | null {\n    return this.component;\n  }\n\n  setComponent(component: Component<T>): void {\n    if (component.name !== this.name) {\n      throw Error(\n        `Mismatching Component ${component.name} for Provider ${this.name}.`\n      );\n    }\n\n    if (this.component) {\n      throw Error(`Component for ${this.name} has already been provided`);\n    }\n\n    this.component = component;\n\n    // return early without attempting to initialize the component if the component requires explicit initialization (calling `Provider.initialize()`)\n    if (!this.shouldAutoInitialize()) {\n      return;\n    }\n\n    // if the service is eager, initialize the default instance\n    if (isComponentEager(component)) {\n      try {\n        this.getOrInitializeService({ instanceIdentifier: DEFAULT_ENTRY_NAME });\n      } catch (e) {\n        // when the instance factory for an eager Component throws an exception during the eager\n        // initialization, it should not cause a fatal error.\n        // TODO: Investigate if we need to make it configurable, because some component may want to cause\n        // a fatal error in this case?\n      }\n    }\n\n    // Create service instances for the pending promises and resolve them\n    // NOTE: if this.multipleInstances is false, only the default instance will be created\n    // and all promises with resolve with it regardless of the identifier.\n    for (const [\n      instanceIdentifier,\n      instanceDeferred\n    ] of this.instancesDeferred.entries()) {\n      const normalizedIdentifier =\n        this.normalizeInstanceIdentifier(instanceIdentifier);\n\n      try {\n        // `getOrInitializeService()` should always return a valid instance since a component is guaranteed. use ! to make typescript happy.\n        const instance = this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        })!;\n        instanceDeferred.resolve(instance);\n      } catch (e) {\n        // when the instance factory throws an exception, it should not cause\n        // a fatal error. We just leave the promise unresolved.\n      }\n    }\n  }\n\n  clearInstance(identifier: string = DEFAULT_ENTRY_NAME): void {\n    this.instancesDeferred.delete(identifier);\n    this.instancesOptions.delete(identifier);\n    this.instances.delete(identifier);\n  }\n\n  // app.delete() will call this method on every provider to delete the services\n  // TODO: should we mark the provider as deleted?\n  async delete(): Promise<void> {\n    const services = Array.from(this.instances.values());\n\n    await Promise.all([\n      ...services\n        .filter(service => 'INTERNAL' in service) // legacy services\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        .map(service => (service as any).INTERNAL!.delete()),\n      ...services\n        .filter(service => '_delete' in service) // modularized services\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        .map(service => (service as any)._delete())\n    ]);\n  }\n\n  isComponentSet(): boolean {\n    return this.component != null;\n  }\n\n  isInitialized(identifier: string = DEFAULT_ENTRY_NAME): boolean {\n    return this.instances.has(identifier);\n  }\n\n  getOptions(identifier: string = DEFAULT_ENTRY_NAME): Record<string, unknown> {\n    return this.instancesOptions.get(identifier) || {};\n  }\n\n  initialize(opts: InitializeOptions = {}): NameServiceMapping[T] {\n    const { options = {} } = opts;\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(\n      opts.instanceIdentifier\n    );\n    if (this.isInitialized(normalizedIdentifier)) {\n      throw Error(\n        `${this.name}(${normalizedIdentifier}) has already been initialized`\n      );\n    }\n\n    if (!this.isComponentSet()) {\n      throw Error(`Component ${this.name} has not been registered yet`);\n    }\n\n    const instance = this.getOrInitializeService({\n      instanceIdentifier: normalizedIdentifier,\n      options\n    })!;\n\n    // resolve any pending promise waiting for the service instance\n    for (const [\n      instanceIdentifier,\n      instanceDeferred\n    ] of this.instancesDeferred.entries()) {\n      const normalizedDeferredIdentifier =\n        this.normalizeInstanceIdentifier(instanceIdentifier);\n      if (normalizedIdentifier === normalizedDeferredIdentifier) {\n        instanceDeferred.resolve(instance);\n      }\n    }\n\n    return instance;\n  }\n\n  /**\n   *\n   * @param callback - a function that will be invoked  after the provider has been initialized by calling provider.initialize().\n   * The function is invoked SYNCHRONOUSLY, so it should not execute any longrunning tasks in order to not block the program.\n   *\n   * @param identifier An optional instance identifier\n   * @returns a function to unregister the callback\n   */\n  onInit(callback: OnInitCallBack<T>, identifier?: string): () => void {\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n    const existingCallbacks =\n      this.onInitCallbacks.get(normalizedIdentifier) ??\n      new Set<OnInitCallBack<T>>();\n    existingCallbacks.add(callback);\n    this.onInitCallbacks.set(normalizedIdentifier, existingCallbacks);\n\n    const existingInstance = this.instances.get(normalizedIdentifier);\n    if (existingInstance) {\n      callback(existingInstance, normalizedIdentifier);\n    }\n\n    return () => {\n      existingCallbacks.delete(callback);\n    };\n  }\n\n  /**\n   * Invoke onInit callbacks synchronously\n   * @param instance the service instance`\n   */\n  private invokeOnInitCallbacks(\n    instance: NameServiceMapping[T],\n    identifier: string\n  ): void {\n    const callbacks = this.onInitCallbacks.get(identifier);\n    if (!callbacks) {\n      return;\n    }\n    for (const callback of callbacks) {\n      try {\n        callback(instance, identifier);\n      } catch {\n        // ignore errors in the onInit callback\n      }\n    }\n  }\n\n  private getOrInitializeService({\n    instanceIdentifier,\n    options = {}\n  }: {\n    instanceIdentifier: string;\n    options?: Record<string, unknown>;\n  }): NameServiceMapping[T] | null {\n    let instance = this.instances.get(instanceIdentifier);\n    if (!instance && this.component) {\n      instance = this.component.instanceFactory(this.container, {\n        instanceIdentifier: normalizeIdentifierForFactory(instanceIdentifier),\n        options\n      });\n      this.instances.set(instanceIdentifier, instance!);\n      this.instancesOptions.set(instanceIdentifier, options);\n\n      /**\n       * Invoke onInit listeners.\n       * Note this.component.onInstanceCreated is different, which is used by the component creator,\n       * while onInit listeners are registered by consumers of the provider.\n       */\n      this.invokeOnInitCallbacks(instance!, instanceIdentifier);\n\n      /**\n       * Order is important\n       * onInstanceCreated() should be called after this.instances.set(instanceIdentifier, instance); which\n       * makes `isInitialized()` return true.\n       */\n      if (this.component.onInstanceCreated) {\n        try {\n          this.component.onInstanceCreated(\n            this.container,\n            instanceIdentifier,\n            instance!\n          );\n        } catch {\n          // ignore errors in the onInstanceCreatedCallback\n        }\n      }\n    }\n\n    return instance || null;\n  }\n\n  private normalizeInstanceIdentifier(\n    identifier: string = DEFAULT_ENTRY_NAME\n  ): string {\n    if (this.component) {\n      return this.component.multipleInstances ? identifier : DEFAULT_ENTRY_NAME;\n    } else {\n      return identifier; // assume multiple instances are supported before the component is provided.\n    }\n  }\n\n  private shouldAutoInitialize(): boolean {\n    return (\n      !!this.component &&\n      this.component.instantiationMode !== InstantiationMode.EXPLICIT\n    );\n  }\n}\n\n// undefined should be passed to the service factory for the default instance\nfunction normalizeIdentifierForFactory(identifier: string): string | undefined {\n  return identifier === DEFAULT_ENTRY_NAME ? undefined : identifier;\n}\n\nfunction isComponentEager<T extends Name>(component: Component<T>): boolean {\n  return component.instantiationMode === InstantiationMode.EAGER;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Provider } from './provider';\nimport { Component } from './component';\nimport { Name } from './types';\n\n/**\n * ComponentContainer that provides Providers for service name T, e.g. `auth`, `auth-internal`\n */\nexport class ComponentContainer {\n  private readonly providers = new Map<string, Provider<Name>>();\n\n  constructor(private readonly name: string) {}\n\n  /**\n   *\n   * @param component Component being added\n   * @param overwrite When a component with the same name has already been registered,\n   * if overwrite is true: overwrite the existing component with the new component and create a new\n   * provider with the new component. It can be useful in tests where you want to use different mocks\n   * for different tests.\n   * if overwrite is false: throw an exception\n   */\n  addComponent<T extends Name>(component: Component<T>): void {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      throw new Error(\n        `Component ${component.name} has already been registered with ${this.name}`\n      );\n    }\n\n    provider.setComponent(component);\n  }\n\n  addOrOverwriteComponent<T extends Name>(component: Component<T>): void {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      // delete the existing provider from the container, so we can register the new component\n      this.providers.delete(component.name);\n    }\n\n    this.addComponent(component);\n  }\n\n  /**\n   * getProvider provides a type safe interface where it can only be called with a field name\n   * present in NameServiceMapping interface.\n   *\n   * Firebase SDKs providing services should extend NameServiceMapping interface to register\n   * themselves.\n   */\n  getProvider<T extends Name>(name: T): Provider<T> {\n    if (this.providers.has(name)) {\n      return this.providers.get(name) as unknown as Provider<T>;\n    }\n\n    // create a Provider for a service that hasn't registered with Firebase\n    const provider = new Provider<T>(name, this);\n    this.providers.set(name, provider as unknown as Provider<Name>);\n\n    return provider as Provider<T>;\n  }\n\n  getProviders(): Array<Provider<Name>> {\n    return Array.from(this.providers.values());\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type LogLevelString =\n  | 'debug'\n  | 'verbose'\n  | 'info'\n  | 'warn'\n  | 'error'\n  | 'silent';\n\nexport interface LogOptions {\n  level: LogLevelString;\n}\n\nexport type LogCallback = (callbackParams: LogCallbackParams) => void;\n\nexport interface LogCallbackParams {\n  level: LogLevelString;\n  message: string;\n  args: unknown[];\n  type: string;\n}\n\n/**\n * A container for all of the Logger instances\n */\nexport const instances: Logger[] = [];\n\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nexport enum LogLevel {\n  DEBUG,\n  VERBOSE,\n  INFO,\n  WARN,\n  ERROR,\n  SILENT\n}\n\nconst levelStringToEnum: { [key in LogLevelString]: LogLevel } = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n\n/**\n * The default log level\n */\nconst defaultLogLevel: LogLevel = LogLevel.INFO;\n\n/**\n * We allow users the ability to pass their own log handler. We will pass the\n * type of log, the current log level, and any other arguments passed (i.e. the\n * messages that the user wants to log) to this function.\n */\nexport type LogHandler = (\n  loggerInstance: Logger,\n  logType: LogLevel,\n  ...args: unknown[]\n) => void;\n\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler: LogHandler = (instance, logType, ...args): void => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType as keyof typeof ConsoleMethod];\n  if (method) {\n    console[method as 'log' | 'info' | 'warn' | 'error'](\n      `[${now}]  ${instance.name}:`,\n      ...args\n    );\n  } else {\n    throw new Error(\n      `Attempted to log a message with an invalid logType (value: ${logType})`\n    );\n  }\n};\n\nexport class Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(public name: string) {\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n\n  /**\n   * The log level of the given Logger instance.\n   */\n  private _logLevel = defaultLogLevel;\n\n  get logLevel(): LogLevel {\n    return this._logLevel;\n  }\n\n  set logLevel(val: LogLevel) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val: LogLevel | LogLevelString): void {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n\n  /**\n   * The main (internal) log handler for the Logger instance.\n   * Can be set to a new function in internal package code but not by user.\n   */\n  private _logHandler: LogHandler = defaultLogHandler;\n  get logHandler(): LogHandler {\n    return this._logHandler;\n  }\n  set logHandler(val: LogHandler) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n\n  /**\n   * The optional, additional, user-defined log handler for the Logger instance.\n   */\n  private _userLogHandler: LogHandler | null = null;\n  get userLogHandler(): LogHandler | null {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val: LogHandler | null) {\n    this._userLogHandler = val;\n  }\n\n  /**\n   * The functions below are all based on the `console` interface\n   */\n\n  debug(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args: unknown[]): void {\n    this._userLogHandler &&\n      this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\n\nexport function setLogLevel(level: LogLevelString | LogLevel): void {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\n\nexport function setUserLogHandler(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  for (const instance of instances) {\n    let customLogLevel: LogLevel | null = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (\n        instance: Logger,\n        level: LogLevel,\n        ...args: unknown[]\n      ) => {\n        const message = args\n          .map(arg => {\n            if (arg == null) {\n              return null;\n            } else if (typeof arg === 'string') {\n              return arg;\n            } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n              return arg.toString();\n            } else if (arg instanceof Error) {\n              return arg.message;\n            } else {\n              try {\n                return JSON.stringify(arg);\n              } catch (ignored) {\n                return null;\n              }\n            }\n          })\n          .filter(arg => arg)\n          .join(' ');\n        if (level >= (customLogLevel ?? instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase() as LogLevelString,\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\n", "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n        });\n    }\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event.newVersion, event));\n    }\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking) {\n            db.addEventListener('versionchange', (event) => blocking(event.oldVersion, event.newVersion, event));\n        }\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event));\n    }\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n", "const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ComponentContainer,\n  ComponentType,\n  Provider,\n  Name\n} from '@firebase/component';\nimport { PlatformLoggerService, VersionService } from './types';\n\nexport class PlatformLoggerServiceImpl implements PlatformLoggerService {\n  constructor(private readonly container: ComponentContainer) {}\n  // In initial implementation, this will be called by installations on\n  // auth token refresh, and installations will send this string.\n  getPlatformInfoString(): string {\n    const providers = this.container.getProviders();\n    // Loop through providers and get library/version pairs from any that are\n    // version components.\n    return providers\n      .map(provider => {\n        if (isVersionServiceProvider(provider)) {\n          const service = provider.getImmediate() as VersionService;\n          return `${service.library}/${service.version}`;\n        } else {\n          return null;\n        }\n      })\n      .filter(logString => logString)\n      .join(' ');\n  }\n}\n/**\n *\n * @param provider check if this provider provides a VersionService\n *\n * NOTE: Using Provider<'app-version'> is a hack to indicate that the provider\n * provides VersionService. The provider is not necessarily a 'app-version'\n * provider.\n */\nfunction isVersionServiceProvider(provider: Provider<Name>): boolean {\n  const component = provider.getComponent();\n  return component?.type === ComponentType.VERSION;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nexport const logger = new Logger('@firebase/app');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Component, ComponentType } from '@firebase/component';\nimport { PlatformLoggerServiceImpl } from './platformLoggerService';\nimport { name, version } from '../package.json';\nimport { _registerComponent } from './internal';\nimport { registerVersion } from './api';\nimport { HeartbeatServiceImpl } from './heartbeatService';\n\nexport function registerCoreComponents(variant?: string): void {\n  _registerComponent(\n    new Component(\n      'platform-logger',\n      container => new PlatformLoggerServiceImpl(container),\n      ComponentType.PRIVATE\n    )\n  );\n  _registerComponent(\n    new Component(\n      'heartbeat',\n      container => new HeartbeatServiceImpl(container),\n      ComponentType.PRIVATE\n    )\n  );\n\n  // Register `app` package.\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n  // Register platform SDK identifier (no version).\n  registerVersion('fire-js', '');\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { name as appName } from '../package.json';\nimport { name as appCompatName } from '../../app-compat/package.json';\nimport { name as analyticsCompatName } from '../../../packages/analytics-compat/package.json';\nimport { name as analyticsName } from '../../../packages/analytics/package.json';\nimport { name as appCheckCompatName } from '../../../packages/app-check-compat/package.json';\nimport { name as appCheckName } from '../../../packages/app-check/package.json';\nimport { name as authName } from '../../../packages/auth/package.json';\nimport { name as authCompatName } from '../../../packages/auth-compat/package.json';\nimport { name as databaseName } from '../../../packages/database/package.json';\nimport { name as dataconnectName } from '../../../packages/data-connect/package.json';\nimport { name as databaseCompatName } from '../../../packages/database-compat/package.json';\nimport { name as functionsName } from '../../../packages/functions/package.json';\nimport { name as functionsCompatName } from '../../../packages/functions-compat/package.json';\nimport { name as installationsName } from '../../../packages/installations/package.json';\nimport { name as installationsCompatName } from '../../../packages/installations-compat/package.json';\nimport { name as messagingName } from '../../../packages/messaging/package.json';\nimport { name as messagingCompatName } from '../../../packages/messaging-compat/package.json';\nimport { name as performanceName } from '../../../packages/performance/package.json';\nimport { name as performanceCompatName } from '../../../packages/performance-compat/package.json';\nimport { name as remoteConfigName } from '../../../packages/remote-config/package.json';\nimport { name as remoteConfigCompatName } from '../../../packages/remote-config-compat/package.json';\nimport { name as storageName } from '../../../packages/storage/package.json';\nimport { name as storageCompatName } from '../../../packages/storage-compat/package.json';\nimport { name as firestoreName } from '../../../packages/firestore/package.json';\nimport { name as vertexName } from '../../../packages/vertexai/package.json';\nimport { name as firestoreCompatName } from '../../../packages/firestore-compat/package.json';\nimport { name as packageName } from '../../../packages/firebase/package.json';\n\n/**\n * The default app name\n *\n * @internal\n */\nexport const DEFAULT_ENTRY_NAME = '[DEFAULT]';\n\nexport const PLATFORM_LOG_STRING = {\n  [appName]: 'fire-core',\n  [appCompatName]: 'fire-core-compat',\n  [analyticsName]: 'fire-analytics',\n  [analyticsCompatName]: 'fire-analytics-compat',\n  [appCheckName]: 'fire-app-check',\n  [appCheckCompatName]: 'fire-app-check-compat',\n  [authName]: 'fire-auth',\n  [authCompatName]: 'fire-auth-compat',\n  [databaseName]: 'fire-rtdb',\n  [dataconnectName]: 'fire-data-connect',\n  [databaseCompatName]: 'fire-rtdb-compat',\n  [functionsName]: 'fire-fn',\n  [functionsCompatName]: 'fire-fn-compat',\n  [installationsName]: 'fire-iid',\n  [installationsCompatName]: 'fire-iid-compat',\n  [messagingName]: 'fire-fcm',\n  [messagingCompatName]: 'fire-fcm-compat',\n  [performanceName]: 'fire-perf',\n  [performanceCompatName]: 'fire-perf-compat',\n  [remoteConfigName]: 'fire-rc',\n  [remoteConfigCompatName]: 'fire-rc-compat',\n  [storageName]: 'fire-gcs',\n  [storageCompatName]: 'fire-gcs-compat',\n  [firestoreName]: 'fire-fst',\n  [firestoreCompatName]: 'fire-fst-compat',\n  [vertexName]: 'fire-vertex',\n  'fire-js': 'fire-js', // Platform identifier for JS SDK.\n  [packageName]: 'fire-js-all'\n} as const;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseOptions,\n  FirebaseServerApp\n} from './public-types';\nimport { Component, Provider, Name } from '@firebase/component';\nimport { logger } from './logger';\nimport { DEFAULT_ENTRY_NAME } from './constants';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { FirebaseServerAppImpl } from './firebaseServerApp';\n\n/**\n * @internal\n */\nexport const _apps = new Map<string, FirebaseApp>();\n\n/**\n * @internal\n */\nexport const _serverApps = new Map<string, FirebaseServerApp>();\n\n/**\n * Registered components.\n *\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport const _components = new Map<string, Component<any>>();\n\n/**\n * @param component - the component being added to this app's container\n *\n * @internal\n */\nexport function _addComponent<T extends Name>(\n  app: FirebaseApp,\n  component: Component<T>\n): void {\n  try {\n    (app as FirebaseAppImpl).container.addComponent(component);\n  } catch (e) {\n    logger.debug(\n      `Component ${component.name} failed to register with FirebaseApp ${app.name}`,\n      e\n    );\n  }\n}\n\n/**\n *\n * @internal\n */\nexport function _addOrOverwriteComponent(\n  app: FirebaseApp,\n  component: Component\n): void {\n  (app as FirebaseAppImpl).container.addOrOverwriteComponent(component);\n}\n\n/**\n *\n * @param component - the component to register\n * @returns whether or not the component is registered successfully\n *\n * @internal\n */\nexport function _registerComponent<T extends Name>(\n  component: Component<T>\n): boolean {\n  const componentName = component.name;\n  if (_components.has(componentName)) {\n    logger.debug(\n      `There were multiple attempts to register component ${componentName}.`\n    );\n\n    return false;\n  }\n\n  _components.set(componentName, component);\n\n  // add the component to existing app instances\n  for (const app of _apps.values()) {\n    _addComponent(app as FirebaseAppImpl, component);\n  }\n\n  for (const serverApp of _serverApps.values()) {\n    _addComponent(serverApp as FirebaseServerAppImpl, component);\n  }\n\n  return true;\n}\n\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n *\n * @returns the provider for the service with the matching name\n *\n * @internal\n */\nexport function _getProvider<T extends Name>(\n  app: FirebaseApp,\n  name: T\n): Provider<T> {\n  const heartbeatController = (app as FirebaseAppImpl).container\n    .getProvider('heartbeat')\n    .getImmediate({ optional: true });\n  if (heartbeatController) {\n    void heartbeatController.triggerHeartbeat();\n  }\n  return (app as FirebaseAppImpl).container.getProvider(name);\n}\n\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n * @param instanceIdentifier - service instance identifier in case the service supports multiple instances\n *\n * @internal\n */\nexport function _removeServiceInstance<T extends Name>(\n  app: FirebaseApp,\n  name: T,\n  instanceIdentifier: string = DEFAULT_ENTRY_NAME\n): void {\n  _getProvider(app, name).clearInstance(instanceIdentifier);\n}\n\n/**\n *\n * @param obj - an object of type FirebaseApp or FirebaseOptions.\n *\n * @returns true if the provide object is of type FirebaseApp.\n *\n * @internal\n */\nexport function _isFirebaseApp(\n  obj: FirebaseApp | FirebaseOptions\n): obj is FirebaseApp {\n  return (obj as FirebaseApp).options !== undefined;\n}\n\n/**\n *\n * @param obj - an object of type FirebaseApp.\n *\n * @returns true if the provided object is of type FirebaseServerAppImpl.\n *\n * @internal\n */\nexport function _isFirebaseServerApp(\n  obj: FirebaseApp | FirebaseServerApp | null | undefined\n): obj is FirebaseServerApp {\n  if (obj === null || obj === undefined) {\n    return false;\n  }\n  return (obj as FirebaseServerApp).settings !== undefined;\n}\n\n/**\n * Test only\n *\n * @internal\n */\nexport function _clearComponents(): void {\n  _components.clear();\n}\n\n/**\n * Exported in order to be used in app-compat package\n */\nexport { DEFAULT_ENTRY_NAME as _DEFAULT_ENTRY_NAME };\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum AppError {\n  NO_APP = 'no-app',\n  BAD_APP_NAME = 'bad-app-name',\n  DUPLICATE_APP = 'duplicate-app',\n  APP_DELETED = 'app-deleted',\n  SERVER_APP_DELETED = 'server-app-deleted',\n  NO_OPTIONS = 'no-options',\n  INVALID_APP_ARGUMENT = 'invalid-app-argument',\n  INVALID_LOG_ARGUMENT = 'invalid-log-argument',\n  IDB_OPEN = 'idb-open',\n  IDB_GET = 'idb-get',\n  IDB_WRITE = 'idb-set',\n  IDB_DELETE = 'idb-delete',\n  FINALIZATION_REGISTRY_NOT_SUPPORTED = 'finalization-registry-not-supported',\n  INVALID_SERVER_APP_ENVIRONMENT = 'invalid-server-app-environment'\n}\n\nconst ERRORS: ErrorMap<AppError> = {\n  [AppError.NO_APP]:\n    \"No Firebase App '{$appName}' has been created - \" +\n    'call initializeApp() first',\n  [AppError.BAD_APP_NAME]: \"Illegal App name: '{$appName}'\",\n  [AppError.DUPLICATE_APP]:\n    \"Firebase App named '{$appName}' already exists with different options or config\",\n  [AppError.APP_DELETED]: \"Firebase App named '{$appName}' already deleted\",\n  [AppError.SERVER_APP_DELETED]: 'Firebase Server App has been deleted',\n  [AppError.NO_OPTIONS]:\n    'Need to provide options, when not being deployed to hosting via source.',\n  [AppError.INVALID_APP_ARGUMENT]:\n    'firebase.{$appName}() takes either no argument or a ' +\n    'Firebase App instance.',\n  [AppError.INVALID_LOG_ARGUMENT]:\n    'First argument to `onLog` must be null or a function.',\n  [AppError.IDB_OPEN]:\n    'Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_GET]:\n    'Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_WRITE]:\n    'Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_DELETE]:\n    'Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED]:\n    'FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.',\n  [AppError.INVALID_SERVER_APP_ENVIRONMENT]:\n    'FirebaseServerApp is not for use in browser environments.'\n};\n\ninterface ErrorParams {\n  [AppError.NO_APP]: { appName: string };\n  [AppError.BAD_APP_NAME]: { appName: string };\n  [AppError.DUPLICATE_APP]: { appName: string };\n  [AppError.APP_DELETED]: { appName: string };\n  [AppError.INVALID_APP_ARGUMENT]: { appName: string };\n  [AppError.IDB_OPEN]: { originalErrorMessage?: string };\n  [AppError.IDB_GET]: { originalErrorMessage?: string };\n  [AppError.IDB_WRITE]: { originalErrorMessage?: string };\n  [AppError.IDB_DELETE]: { originalErrorMessage?: string };\n  [AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED]: { appName?: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<AppError, ErrorParams>(\n  'app',\n  'Firebase',\n  ERRORS\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseOptions,\n  FirebaseAppSettings\n} from './public-types';\nimport {\n  ComponentContainer,\n  Component,\n  ComponentType\n} from '@firebase/component';\nimport { ERROR_FACTORY, AppError } from './errors';\n\nexport class FirebaseAppImpl implements FirebaseApp {\n  protected readonly _options: FirebaseOptions;\n  protected readonly _name: string;\n  /**\n   * Original config values passed in as a constructor parameter.\n   * It is only used to compare with another config object to support idempotent initializeApp().\n   *\n   * Updating automaticDataCollectionEnabled on the App instance will not change its value in _config.\n   */\n  private readonly _config: Required<FirebaseAppSettings>;\n  private _automaticDataCollectionEnabled: boolean;\n  protected _isDeleted = false;\n  private readonly _container: ComponentContainer;\n\n  constructor(\n    options: FirebaseOptions,\n    config: Required<FirebaseAppSettings>,\n    container: ComponentContainer\n  ) {\n    this._options = { ...options };\n    this._config = { ...config };\n    this._name = config.name;\n    this._automaticDataCollectionEnabled =\n      config.automaticDataCollectionEnabled;\n    this._container = container;\n    this.container.addComponent(\n      new Component('app', () => this, ComponentType.PUBLIC)\n    );\n  }\n\n  get automaticDataCollectionEnabled(): boolean {\n    this.checkDestroyed();\n    return this._automaticDataCollectionEnabled;\n  }\n\n  set automaticDataCollectionEnabled(val: boolean) {\n    this.checkDestroyed();\n    this._automaticDataCollectionEnabled = val;\n  }\n\n  get name(): string {\n    this.checkDestroyed();\n    return this._name;\n  }\n\n  get options(): FirebaseOptions {\n    this.checkDestroyed();\n    return this._options;\n  }\n\n  get config(): Required<FirebaseAppSettings> {\n    this.checkDestroyed();\n    return this._config;\n  }\n\n  get container(): ComponentContainer {\n    return this._container;\n  }\n\n  get isDeleted(): boolean {\n    return this._isDeleted;\n  }\n\n  set isDeleted(val: boolean) {\n    this._isDeleted = val;\n  }\n\n  /**\n   * This function will throw an Error if the App has already been deleted -\n   * use before performing API actions on the App.\n   */\n  protected checkDestroyed(): void {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(AppError.APP_DELETED, { appName: this._name });\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseAppSettings,\n  FirebaseServerApp,\n  FirebaseServerAppSettings,\n  FirebaseOptions\n} from './public-types';\nimport { deleteApp, registerVersion } from './api';\nimport { ComponentContainer } from '@firebase/component';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { ERROR_FACTORY, AppError } from './errors';\nimport { name as packageName, version } from '../package.json';\nimport { base64Decode } from '@firebase/util';\n\n// Parse the token and check to see if the `exp` claim is in the future.\n// Reports an error to the console if the token or claim could not be parsed, or if `exp` is in\n// the past.\nfunction validateTokenTTL(base64Token: string, tokenName: string): void {\n  const secondPart = base64Decode(base64Token.split('.')[1]);\n  if (secondPart === null) {\n    console.error(\n      `FirebaseServerApp ${tokenName} is invalid: second part could not be parsed.`\n    );\n    return;\n  }\n  const expClaim = JSON.parse(secondPart).exp;\n  if (expClaim === undefined) {\n    console.error(\n      `FirebaseServerApp ${tokenName} is invalid: expiration claim could not be parsed`\n    );\n    return;\n  }\n  const exp = JSON.parse(secondPart).exp * 1000;\n  const now = new Date().getTime();\n  const diff = exp - now;\n  if (diff <= 0) {\n    console.error(\n      `FirebaseServerApp ${tokenName} is invalid: the token has expired.`\n    );\n  }\n}\n\nexport class FirebaseServerAppImpl\n  extends FirebaseAppImpl\n  implements FirebaseServerApp\n{\n  private readonly _serverConfig: FirebaseServerAppSettings;\n  private _finalizationRegistry: FinalizationRegistry<object> | null;\n  private _refCount: number;\n\n  constructor(\n    options: FirebaseOptions | FirebaseAppImpl,\n    serverConfig: FirebaseServerAppSettings,\n    name: string,\n    container: ComponentContainer\n  ) {\n    // Build configuration parameters for the FirebaseAppImpl base class.\n    const automaticDataCollectionEnabled =\n      serverConfig.automaticDataCollectionEnabled !== undefined\n        ? serverConfig.automaticDataCollectionEnabled\n        : false;\n\n    // Create the FirebaseAppSettings object for the FirebaseAppImp constructor.\n    const config: Required<FirebaseAppSettings> = {\n      name,\n      automaticDataCollectionEnabled\n    };\n\n    if ((options as FirebaseOptions).apiKey !== undefined) {\n      // Construct the parent FirebaseAppImp object.\n      super(options as FirebaseOptions, config, container);\n    } else {\n      const appImpl: FirebaseAppImpl = options as FirebaseAppImpl;\n      super(appImpl.options, config, container);\n    }\n\n    // Now construct the data for the FirebaseServerAppImpl.\n    this._serverConfig = {\n      automaticDataCollectionEnabled,\n      ...serverConfig\n    };\n\n    // Ensure that the current time is within the `authIdtoken` window of validity.\n    if (this._serverConfig.authIdToken) {\n      validateTokenTTL(this._serverConfig.authIdToken, 'authIdToken');\n    }\n\n    // Ensure that the current time is within the `appCheckToken` window of validity.\n    if (this._serverConfig.appCheckToken) {\n      validateTokenTTL(this._serverConfig.appCheckToken, 'appCheckToken');\n    }\n\n    this._finalizationRegistry = null;\n    if (typeof FinalizationRegistry !== 'undefined') {\n      this._finalizationRegistry = new FinalizationRegistry(() => {\n        this.automaticCleanup();\n      });\n    }\n\n    this._refCount = 0;\n    this.incRefCount(this._serverConfig.releaseOnDeref);\n\n    // Do not retain a hard reference to the dref object, otherwise the FinalizationRegistry\n    // will never trigger.\n    this._serverConfig.releaseOnDeref = undefined;\n    serverConfig.releaseOnDeref = undefined;\n\n    registerVersion(packageName, version, 'serverapp');\n  }\n\n  toJSON(): undefined {\n    return undefined;\n  }\n\n  get refCount(): number {\n    return this._refCount;\n  }\n\n  // Increment the reference count of this server app. If an object is provided, register it\n  // with the finalization registry.\n  incRefCount(obj: object | undefined): void {\n    if (this.isDeleted) {\n      return;\n    }\n    this._refCount++;\n    if (obj !== undefined && this._finalizationRegistry !== null) {\n      this._finalizationRegistry.register(obj, this);\n    }\n  }\n\n  // Decrement the reference count.\n  decRefCount(): number {\n    if (this.isDeleted) {\n      return 0;\n    }\n    return --this._refCount;\n  }\n\n  // Invoked by the FinalizationRegistry callback to note that this app should go through its\n  // reference counts and delete itself if no reference count remain. The coordinating logic that\n  // handles this is in deleteApp(...).\n  private automaticCleanup(): void {\n    void deleteApp(this);\n  }\n\n  get settings(): FirebaseServerAppSettings {\n    this.checkDestroyed();\n    return this._serverConfig;\n  }\n\n  /**\n   * This function will throw an Error if the App has already been deleted -\n   * use before performing API actions on the App.\n   */\n  protected checkDestroyed(): void {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(AppError.SERVER_APP_DELETED);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseServerApp,\n  FirebaseOptions,\n  FirebaseAppSettings,\n  FirebaseServerAppSettings\n} from './public-types';\nimport { DEFAULT_ENTRY_NAME, PLATFORM_LOG_STRING } from './constants';\nimport { ERROR_FACTORY, AppError } from './errors';\nimport {\n  ComponentContainer,\n  Component,\n  Name,\n  ComponentType\n} from '@firebase/component';\nimport { version } from '../../firebase/package.json';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { FirebaseServerAppImpl } from './firebaseServerApp';\nimport {\n  _apps,\n  _components,\n  _isFirebaseApp,\n  _registerComponent,\n  _serverApps\n} from './internal';\nimport { logger } from './logger';\nimport {\n  LogLevelString,\n  setLogLevel as setLogLevelImpl,\n  LogCallback,\n  LogOptions,\n  setUserLogHandler\n} from '@firebase/logger';\nimport {\n  deepEqual,\n  getDefaultAppConfig,\n  isBrowser,\n  isWebWorker\n} from '@firebase/util';\n\nexport { FirebaseError } from '@firebase/util';\n\n/**\n * The current SDK version.\n *\n * @public\n */\nexport const SDK_VERSION = version;\n\n/**\n * Creates and initializes a {@link @firebase/app#FirebaseApp} instance.\n *\n * See\n * {@link\n *   https://firebase.google.com/docs/web/setup#add_firebase_to_your_app\n *   | Add Firebase to your app} and\n * {@link\n *   https://firebase.google.com/docs/web/setup#multiple-projects\n *   | Initialize multiple projects} for detailed documentation.\n *\n * @example\n * ```javascript\n *\n * // Initialize default app\n * // Retrieve your own options values by adding a web app on\n * // https://console.firebase.google.com\n * initializeApp({\n *   apiKey: \"AIza....\",                             // Auth / General Use\n *   authDomain: \"YOUR_APP.firebaseapp.com\",         // Auth with popup/redirect\n *   databaseURL: \"https://YOUR_APP.firebaseio.com\", // Realtime Database\n *   storageBucket: \"YOUR_APP.appspot.com\",          // Storage\n *   messagingSenderId: \"123456789\"                  // Cloud Messaging\n * });\n * ```\n *\n * @example\n * ```javascript\n *\n * // Initialize another app\n * const otherApp = initializeApp({\n *   databaseURL: \"https://<OTHER_DATABASE_NAME>.firebaseio.com\",\n *   storageBucket: \"<OTHER_STORAGE_BUCKET>.appspot.com\"\n * }, \"otherApp\");\n * ```\n *\n * @param options - Options to configure the app's services.\n * @param name - Optional name of the app to initialize. If no name\n *   is provided, the default is `\"[DEFAULT]\"`.\n *\n * @returns The initialized app.\n *\n * @public\n */\nexport function initializeApp(\n  options: FirebaseOptions,\n  name?: string\n): FirebaseApp;\n/**\n * Creates and initializes a FirebaseApp instance.\n *\n * @param options - Options to configure the app's services.\n * @param config - FirebaseApp Configuration\n *\n * @public\n */\nexport function initializeApp(\n  options: FirebaseOptions,\n  config?: FirebaseAppSettings\n): FirebaseApp;\n/**\n * Creates and initializes a FirebaseApp instance.\n *\n * @public\n */\nexport function initializeApp(): FirebaseApp;\nexport function initializeApp(\n  _options?: FirebaseOptions,\n  rawConfig = {}\n): FirebaseApp {\n  let options = _options;\n\n  if (typeof rawConfig !== 'object') {\n    const name = rawConfig;\n    rawConfig = { name };\n  }\n\n  const config: Required<FirebaseAppSettings> = {\n    name: DEFAULT_ENTRY_NAME,\n    automaticDataCollectionEnabled: false,\n    ...rawConfig\n  };\n  const name = config.name;\n\n  if (typeof name !== 'string' || !name) {\n    throw ERROR_FACTORY.create(AppError.BAD_APP_NAME, {\n      appName: String(name)\n    });\n  }\n\n  options ||= getDefaultAppConfig();\n\n  if (!options) {\n    throw ERROR_FACTORY.create(AppError.NO_OPTIONS);\n  }\n\n  const existingApp = _apps.get(name) as FirebaseAppImpl;\n  if (existingApp) {\n    // return the existing app if options and config deep equal the ones in the existing app.\n    if (\n      deepEqual(options, existingApp.options) &&\n      deepEqual(config, existingApp.config)\n    ) {\n      return existingApp;\n    } else {\n      throw ERROR_FACTORY.create(AppError.DUPLICATE_APP, { appName: name });\n    }\n  }\n\n  const container = new ComponentContainer(name);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n\n  const newApp = new FirebaseAppImpl(options, config, container);\n\n  _apps.set(name, newApp);\n\n  return newApp;\n}\n\n/**\n * Creates and initializes a {@link @firebase/app#FirebaseServerApp} instance.\n *\n * The `FirebaseServerApp` is similar to `FirebaseApp`, but is intended for execution in\n * server side rendering environments only. Initialization will fail if invoked from a\n * browser environment.\n *\n * See\n * {@link\n *   https://firebase.google.com/docs/web/setup#add_firebase_to_your_app\n *   | Add Firebase to your app} and\n * {@link\n *   https://firebase.google.com/docs/web/setup#multiple-projects\n *   | Initialize multiple projects} for detailed documentation.\n *\n * @example\n * ```javascript\n *\n * // Initialize an instance of `FirebaseServerApp`.\n * // Retrieve your own options values by adding a web app on\n * // https://console.firebase.google.com\n * initializeServerApp({\n *     apiKey: \"AIza....\",                             // Auth / General Use\n *     authDomain: \"YOUR_APP.firebaseapp.com\",         // Auth with popup/redirect\n *     databaseURL: \"https://YOUR_APP.firebaseio.com\", // Realtime Database\n *     storageBucket: \"YOUR_APP.appspot.com\",          // Storage\n *     messagingSenderId: \"123456789\"                  // Cloud Messaging\n *   },\n *   {\n *    authIdToken: \"Your Auth ID Token\"\n *   });\n * ```\n *\n * @param options - `Firebase.AppOptions` to configure the app's services, or a\n *   a `FirebaseApp` instance which contains the `AppOptions` within.\n * @param config - `FirebaseServerApp` configuration.\n *\n * @returns The initialized `FirebaseServerApp`.\n *\n * @public\n */\nexport function initializeServerApp(\n  options: FirebaseOptions | FirebaseApp,\n  config: FirebaseServerAppSettings\n): FirebaseServerApp;\n\nexport function initializeServerApp(\n  _options: FirebaseOptions | FirebaseApp,\n  _serverAppConfig: FirebaseServerAppSettings\n): FirebaseServerApp {\n  if (isBrowser() && !isWebWorker()) {\n    // FirebaseServerApp isn't designed to be run in browsers.\n    throw ERROR_FACTORY.create(AppError.INVALID_SERVER_APP_ENVIRONMENT);\n  }\n\n  if (_serverAppConfig.automaticDataCollectionEnabled === undefined) {\n    _serverAppConfig.automaticDataCollectionEnabled = false;\n  }\n\n  let appOptions: FirebaseOptions;\n  if (_isFirebaseApp(_options)) {\n    appOptions = _options.options;\n  } else {\n    appOptions = _options;\n  }\n\n  // Build an app name based on a hash of the configuration options.\n  const nameObj = {\n    ..._serverAppConfig,\n    ...appOptions\n  };\n\n  // However, Do not mangle the name based on releaseOnDeref, since it will vary between the\n  // construction of FirebaseServerApp instances. For example, if the object is the request headers.\n  if (nameObj.releaseOnDeref !== undefined) {\n    delete nameObj.releaseOnDeref;\n  }\n\n  const hashCode = (s: string): number => {\n    return [...s].reduce(\n      (hash, c) => (Math.imul(31, hash) + c.charCodeAt(0)) | 0,\n      0\n    );\n  };\n\n  if (_serverAppConfig.releaseOnDeref !== undefined) {\n    if (typeof FinalizationRegistry === 'undefined') {\n      throw ERROR_FACTORY.create(\n        AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED,\n        {}\n      );\n    }\n  }\n\n  const nameString = '' + hashCode(JSON.stringify(nameObj));\n  const existingApp = _serverApps.get(nameString) as FirebaseServerApp;\n  if (existingApp) {\n    (existingApp as FirebaseServerAppImpl).incRefCount(\n      _serverAppConfig.releaseOnDeref\n    );\n    return existingApp;\n  }\n\n  const container = new ComponentContainer(nameString);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n\n  const newApp = new FirebaseServerAppImpl(\n    appOptions,\n    _serverAppConfig,\n    nameString,\n    container\n  );\n\n  _serverApps.set(nameString, newApp);\n\n  return newApp;\n}\n\n/**\n * Retrieves a {@link @firebase/app#FirebaseApp} instance.\n *\n * When called with no arguments, the default app is returned. When an app name\n * is provided, the app corresponding to that name is returned.\n *\n * An exception is thrown if the app being retrieved has not yet been\n * initialized.\n *\n * @example\n * ```javascript\n * // Return the default app\n * const app = getApp();\n * ```\n *\n * @example\n * ```javascript\n * // Return a named app\n * const otherApp = getApp(\"otherApp\");\n * ```\n *\n * @param name - Optional name of the app to return. If no name is\n *   provided, the default is `\"[DEFAULT]\"`.\n *\n * @returns The app corresponding to the provided app name.\n *   If no app name is provided, the default app is returned.\n *\n * @public\n */\nexport function getApp(name: string = DEFAULT_ENTRY_NAME): FirebaseApp {\n  const app = _apps.get(name);\n  if (!app && name === DEFAULT_ENTRY_NAME && getDefaultAppConfig()) {\n    return initializeApp();\n  }\n  if (!app) {\n    throw ERROR_FACTORY.create(AppError.NO_APP, { appName: name });\n  }\n\n  return app;\n}\n\n/**\n * A (read-only) array of all initialized apps.\n * @public\n */\nexport function getApps(): FirebaseApp[] {\n  return Array.from(_apps.values());\n}\n\n/**\n * Renders this app unusable and frees the resources of all associated\n * services.\n *\n * @example\n * ```javascript\n * deleteApp(app)\n *   .then(function() {\n *     console.log(\"App deleted successfully\");\n *   })\n *   .catch(function(error) {\n *     console.log(\"Error deleting app:\", error);\n *   });\n * ```\n *\n * @public\n */\nexport async function deleteApp(app: FirebaseApp): Promise<void> {\n  let cleanupProviders = false;\n  const name = app.name;\n  if (_apps.has(name)) {\n    cleanupProviders = true;\n    _apps.delete(name);\n  } else if (_serverApps.has(name)) {\n    const firebaseServerApp = app as FirebaseServerAppImpl;\n    if (firebaseServerApp.decRefCount() <= 0) {\n      _serverApps.delete(name);\n      cleanupProviders = true;\n    }\n  }\n\n  if (cleanupProviders) {\n    await Promise.all(\n      (app as FirebaseAppImpl).container\n        .getProviders()\n        .map(provider => provider.delete())\n    );\n    (app as FirebaseAppImpl).isDeleted = true;\n  }\n}\n\n/**\n * Registers a library's name and version for platform logging purposes.\n * @param library - Name of 1p or 3p library (e.g. firestore, angularfire)\n * @param version - Current version of that library.\n * @param variant - Bundle variant, e.g., node, rn, etc.\n *\n * @public\n */\nexport function registerVersion(\n  libraryKeyOrName: string,\n  version: string,\n  variant?: string\n): void {\n  // TODO: We can use this check to whitelist strings when/if we set up\n  // a good whitelist system.\n  let library = PLATFORM_LOG_STRING[libraryKeyOrName] ?? libraryKeyOrName;\n  if (variant) {\n    library += `-${variant}`;\n  }\n  const libraryMismatch = library.match(/\\s|\\//);\n  const versionMismatch = version.match(/\\s|\\//);\n  if (libraryMismatch || versionMismatch) {\n    const warning = [\n      `Unable to register library \"${library}\" with version \"${version}\":`\n    ];\n    if (libraryMismatch) {\n      warning.push(\n        `library name \"${library}\" contains illegal characters (whitespace or \"/\")`\n      );\n    }\n    if (libraryMismatch && versionMismatch) {\n      warning.push('and');\n    }\n    if (versionMismatch) {\n      warning.push(\n        `version name \"${version}\" contains illegal characters (whitespace or \"/\")`\n      );\n    }\n    logger.warn(warning.join(' '));\n    return;\n  }\n  _registerComponent(\n    new Component(\n      `${library}-version` as Name,\n      () => ({ library, version }),\n      ComponentType.VERSION\n    )\n  );\n}\n\n/**\n * Sets log handler for all Firebase SDKs.\n * @param logCallback - An optional custom log handler that executes user code whenever\n * the Firebase SDK makes a logging call.\n *\n * @public\n */\nexport function onLog(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  if (logCallback !== null && typeof logCallback !== 'function') {\n    throw ERROR_FACTORY.create(AppError.INVALID_LOG_ARGUMENT);\n  }\n  setUserLogHandler(logCallback, options);\n}\n\n/**\n * Sets log level for all Firebase SDKs.\n *\n * All of the log types above the current log level are captured (i.e. if\n * you set the log level to `info`, errors are logged, but `debug` and\n * `verbose` logs are not).\n *\n * @public\n */\nexport function setLogLevel(logLevel: LogLevelString): void {\n  setLogLevelImpl(logLevel);\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { DBSchema, openDB, IDBPDatabase } from 'idb';\nimport { AppError, ERROR_FACTORY } from './errors';\nimport { FirebaseApp } from './public-types';\nimport { HeartbeatsInIndexedDB } from './types';\nimport { logger } from './logger';\n\nconst DB_NAME = 'firebase-heartbeat-database';\nconst DB_VERSION = 1;\nconst STORE_NAME = 'firebase-heartbeat-store';\n\ninterface AppDB extends DBSchema {\n  'firebase-heartbeat-store': {\n    key: string;\n    value: HeartbeatsInIndexedDB;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<AppDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<AppDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB<AppDB>(DB_NAME, DB_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            try {\n              db.createObjectStore(STORE_NAME);\n            } catch (e) {\n              // Safari/iOS browsers throw occasional exceptions on\n              // db.createObjectStore() that may be a bug. Avoid blocking\n              // the rest of the app functionality.\n              console.warn(e);\n            }\n        }\n      }\n    }).catch(e => {\n      throw ERROR_FACTORY.create(AppError.IDB_OPEN, {\n        originalErrorMessage: e.message\n      });\n    });\n  }\n  return dbPromise;\n}\n\nexport async function readHeartbeatsFromIndexedDB(\n  app: FirebaseApp\n): Promise<HeartbeatsInIndexedDB | undefined> {\n  try {\n    const db = await getDbPromise();\n    const tx = db.transaction(STORE_NAME);\n    const result = await tx.objectStore(STORE_NAME).get(computeKey(app));\n    // We already have the value but tx.done can throw,\n    // so we need to await it here to catch errors\n    await tx.done;\n    return result;\n  } catch (e) {\n    if (e instanceof FirebaseError) {\n      logger.warn(e.message);\n    } else {\n      const idbGetError = ERROR_FACTORY.create(AppError.IDB_GET, {\n        originalErrorMessage: (e as Error)?.message\n      });\n      logger.warn(idbGetError.message);\n    }\n  }\n}\n\nexport async function writeHeartbeatsToIndexedDB(\n  app: FirebaseApp,\n  heartbeatObject: HeartbeatsInIndexedDB\n): Promise<void> {\n  try {\n    const db = await getDbPromise();\n    const tx = db.transaction(STORE_NAME, 'readwrite');\n    const objectStore = tx.objectStore(STORE_NAME);\n    await objectStore.put(heartbeatObject, computeKey(app));\n    await tx.done;\n  } catch (e) {\n    if (e instanceof FirebaseError) {\n      logger.warn(e.message);\n    } else {\n      const idbGetError = ERROR_FACTORY.create(AppError.IDB_WRITE, {\n        originalErrorMessage: (e as Error)?.message\n      });\n      logger.warn(idbGetError.message);\n    }\n  }\n}\n\nfunction computeKey(app: FirebaseApp): string {\n  return `${app.name}!${app.options.appId}`;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ComponentContainer } from '@firebase/component';\nimport {\n  base64urlEncodeWithoutPadding,\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable\n} from '@firebase/util';\nimport {\n  readHeartbeatsFromIndexedDB,\n  writeHeartbeatsToIndexedDB\n} from './indexeddb';\nimport { FirebaseApp } from './public-types';\nimport {\n  HeartbeatsByUserAgent,\n  HeartbeatService,\n  HeartbeatsInIndexedDB,\n  HeartbeatStorage,\n  SingleDateHeartbeat\n} from './types';\nimport { logger } from './logger';\n\nconst MAX_HEADER_BYTES = 1024;\nexport const MAX_NUM_STORED_HEARTBEATS = 30;\n\nexport class HeartbeatServiceImpl implements HeartbeatService {\n  /**\n   * The persistence layer for heartbeats\n   * Leave public for easier testing.\n   */\n  _storage: HeartbeatStorageImpl;\n\n  /**\n   * In-memory cache for heartbeats, used by getHeartbeatsHeader() to generate\n   * the header string.\n   * Stores one record per date. This will be consolidated into the standard\n   * format of one record per user agent string before being sent as a header.\n   * Populated from indexedDB when the controller is instantiated and should\n   * be kept in sync with indexedDB.\n   * Leave public for easier testing.\n   */\n  _heartbeatsCache: HeartbeatsInIndexedDB | null = null;\n\n  /**\n   * the initialization promise for populating heartbeatCache.\n   * If getHeartbeatsHeader() is called before the promise resolves\n   * (heartbeatsCache == null), it should wait for this promise\n   * Leave public for easier testing.\n   */\n  _heartbeatsCachePromise: Promise<HeartbeatsInIndexedDB>;\n  constructor(private readonly container: ComponentContainer) {\n    const app = this.container.getProvider('app').getImmediate();\n    this._storage = new HeartbeatStorageImpl(app);\n    this._heartbeatsCachePromise = this._storage.read().then(result => {\n      this._heartbeatsCache = result;\n      return result;\n    });\n  }\n\n  /**\n   * Called to report a heartbeat. The function will generate\n   * a HeartbeatsByUserAgent object, update heartbeatsCache, and persist it\n   * to IndexedDB.\n   * Note that we only store one heartbeat per day. So if a heartbeat for today is\n   * already logged, subsequent calls to this function in the same day will be ignored.\n   */\n  async triggerHeartbeat(): Promise<void> {\n    try {\n      const platformLogger = this.container\n        .getProvider('platform-logger')\n        .getImmediate();\n\n      // This is the \"Firebase user agent\" string from the platform logger\n      // service, not the browser user agent.\n      const agent = platformLogger.getPlatformInfoString();\n      const date = getUTCDateString();\n      if (this._heartbeatsCache?.heartbeats == null) {\n        this._heartbeatsCache = await this._heartbeatsCachePromise;\n        // If we failed to construct a heartbeats cache, then return immediately.\n        if (this._heartbeatsCache?.heartbeats == null) {\n          return;\n        }\n      }\n      // Do not store a heartbeat if one is already stored for this day\n      // or if a header has already been sent today.\n      if (\n        this._heartbeatsCache.lastSentHeartbeatDate === date ||\n        this._heartbeatsCache.heartbeats.some(\n          singleDateHeartbeat => singleDateHeartbeat.date === date\n        )\n      ) {\n        return;\n      } else {\n        // There is no entry for this date. Create one.\n        this._heartbeatsCache.heartbeats.push({ date, agent });\n\n        // If the number of stored heartbeats exceeds the maximum number of stored heartbeats, remove the heartbeat with the earliest date.\n        // Since this is executed each time a heartbeat is pushed, the limit can only be exceeded by one, so only one needs to be removed.\n        if (\n          this._heartbeatsCache.heartbeats.length > MAX_NUM_STORED_HEARTBEATS\n        ) {\n          const earliestHeartbeatIdx = getEarliestHeartbeatIdx(\n            this._heartbeatsCache.heartbeats\n          );\n          this._heartbeatsCache.heartbeats.splice(earliestHeartbeatIdx, 1);\n        }\n      }\n\n      return this._storage.overwrite(this._heartbeatsCache);\n    } catch (e) {\n      logger.warn(e);\n    }\n  }\n\n  /**\n   * Returns a base64 encoded string which can be attached to the heartbeat-specific header directly.\n   * It also clears all heartbeats from memory as well as in IndexedDB.\n   *\n   * NOTE: Consuming product SDKs should not send the header if this method\n   * returns an empty string.\n   */\n  async getHeartbeatsHeader(): Promise<string> {\n    try {\n      if (this._heartbeatsCache === null) {\n        await this._heartbeatsCachePromise;\n      }\n      // If it's still null or the array is empty, there is no data to send.\n      if (\n        this._heartbeatsCache?.heartbeats == null ||\n        this._heartbeatsCache.heartbeats.length === 0\n      ) {\n        return '';\n      }\n      const date = getUTCDateString();\n      // Extract as many heartbeats from the cache as will fit under the size limit.\n      const { heartbeatsToSend, unsentEntries } = extractHeartbeatsForHeader(\n        this._heartbeatsCache.heartbeats\n      );\n      const headerString = base64urlEncodeWithoutPadding(\n        JSON.stringify({ version: 2, heartbeats: heartbeatsToSend })\n      );\n      // Store last sent date to prevent another being logged/sent for the same day.\n      this._heartbeatsCache.lastSentHeartbeatDate = date;\n      if (unsentEntries.length > 0) {\n        // Store any unsent entries if they exist.\n        this._heartbeatsCache.heartbeats = unsentEntries;\n        // This seems more likely than emptying the array (below) to lead to some odd state\n        // since the cache isn't empty and this will be called again on the next request,\n        // and is probably safest if we await it.\n        await this._storage.overwrite(this._heartbeatsCache);\n      } else {\n        this._heartbeatsCache.heartbeats = [];\n        // Do not wait for this, to reduce latency.\n        void this._storage.overwrite(this._heartbeatsCache);\n      }\n      return headerString;\n    } catch (e) {\n      logger.warn(e);\n      return '';\n    }\n  }\n}\n\nfunction getUTCDateString(): string {\n  const today = new Date();\n  // Returns date format 'YYYY-MM-DD'\n  return today.toISOString().substring(0, 10);\n}\n\nexport function extractHeartbeatsForHeader(\n  heartbeatsCache: SingleDateHeartbeat[],\n  maxSize = MAX_HEADER_BYTES\n): {\n  heartbeatsToSend: HeartbeatsByUserAgent[];\n  unsentEntries: SingleDateHeartbeat[];\n} {\n  // Heartbeats grouped by user agent in the standard format to be sent in\n  // the header.\n  const heartbeatsToSend: HeartbeatsByUserAgent[] = [];\n  // Single date format heartbeats that are not sent.\n  let unsentEntries = heartbeatsCache.slice();\n  for (const singleDateHeartbeat of heartbeatsCache) {\n    // Look for an existing entry with the same user agent.\n    const heartbeatEntry = heartbeatsToSend.find(\n      hb => hb.agent === singleDateHeartbeat.agent\n    );\n    if (!heartbeatEntry) {\n      // If no entry for this user agent exists, create one.\n      heartbeatsToSend.push({\n        agent: singleDateHeartbeat.agent,\n        dates: [singleDateHeartbeat.date]\n      });\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        // If the header would exceed max size, remove the added heartbeat\n        // entry and stop adding to the header.\n        heartbeatsToSend.pop();\n        break;\n      }\n    } else {\n      heartbeatEntry.dates.push(singleDateHeartbeat.date);\n      // If the header would exceed max size, remove the added date\n      // and stop adding to the header.\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        heartbeatEntry.dates.pop();\n        break;\n      }\n    }\n    // Pop unsent entry from queue. (Skipped if adding the entry exceeded\n    // quota and the loop breaks early.)\n    unsentEntries = unsentEntries.slice(1);\n  }\n  return {\n    heartbeatsToSend,\n    unsentEntries\n  };\n}\n\nexport class HeartbeatStorageImpl implements HeartbeatStorage {\n  private _canUseIndexedDBPromise: Promise<boolean>;\n  constructor(public app: FirebaseApp) {\n    this._canUseIndexedDBPromise = this.runIndexedDBEnvironmentCheck();\n  }\n  async runIndexedDBEnvironmentCheck(): Promise<boolean> {\n    if (!isIndexedDBAvailable()) {\n      return false;\n    } else {\n      return validateIndexedDBOpenable()\n        .then(() => true)\n        .catch(() => false);\n    }\n  }\n  /**\n   * Read all heartbeats.\n   */\n  async read(): Promise<HeartbeatsInIndexedDB> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return { heartbeats: [] };\n    } else {\n      const idbHeartbeatObject = await readHeartbeatsFromIndexedDB(this.app);\n      if (idbHeartbeatObject?.heartbeats) {\n        return idbHeartbeatObject;\n      } else {\n        return { heartbeats: [] };\n      }\n    }\n  }\n  // overwrite the storage with the provided heartbeats\n  async overwrite(heartbeatsObject: HeartbeatsInIndexedDB): Promise<void> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return;\n    } else {\n      const existingHeartbeatsObject = await this.read();\n      return writeHeartbeatsToIndexedDB(this.app, {\n        lastSentHeartbeatDate:\n          heartbeatsObject.lastSentHeartbeatDate ??\n          existingHeartbeatsObject.lastSentHeartbeatDate,\n        heartbeats: heartbeatsObject.heartbeats\n      });\n    }\n  }\n  // add heartbeats\n  async add(heartbeatsObject: HeartbeatsInIndexedDB): Promise<void> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return;\n    } else {\n      const existingHeartbeatsObject = await this.read();\n      return writeHeartbeatsToIndexedDB(this.app, {\n        lastSentHeartbeatDate:\n          heartbeatsObject.lastSentHeartbeatDate ??\n          existingHeartbeatsObject.lastSentHeartbeatDate,\n        heartbeats: [\n          ...existingHeartbeatsObject.heartbeats,\n          ...heartbeatsObject.heartbeats\n        ]\n      });\n    }\n  }\n}\n\n/**\n * Calculate bytes of a HeartbeatsByUserAgent array after being wrapped\n * in a platform logging header JSON object, stringified, and converted\n * to base 64.\n */\nexport function countBytes(heartbeatsCache: HeartbeatsByUserAgent[]): number {\n  // base64 has a restricted set of characters, all of which should be 1 byte.\n  return base64urlEncodeWithoutPadding(\n    // heartbeatsCache wrapper properties\n    JSON.stringify({ version: 2, heartbeats: heartbeatsCache })\n  ).length;\n}\n\n/**\n * Returns the index of the heartbeat with the earliest date.\n * If the heartbeats array is empty, -1 is returned.\n */\nexport function getEarliestHeartbeatIdx(\n  heartbeats: SingleDateHeartbeat[]\n): number {\n  if (heartbeats.length === 0) {\n    return -1;\n  }\n\n  let earliestHeartbeatIdx = 0;\n  let earliestHeartbeatDate = heartbeats[0].date;\n\n  for (let i = 1; i < heartbeats.length; i++) {\n    if (heartbeats[i].date < earliestHeartbeatDate) {\n      earliestHeartbeatDate = heartbeats[i].date;\n      earliestHeartbeatIdx = i;\n    }\n  }\n\n  return earliestHeartbeatIdx;\n}\n", "/**\n * Firebase App\n *\n * @remarks This package coordinates the communication between the different Firebase components\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerCoreComponents } from './registerCoreComponents';\n\nexport * from './api';\nexport * from './internal';\nexport * from './public-types';\n\nregisterCoreComponents('__RUNTIME_ENV__');\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseOptions } from './public-types';\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType,\n  InstantiationMode,\n  Name\n} from '@firebase/component';\nimport {\n  deleteApp,\n  _addComponent,\n  _addOrOverwriteComponent,\n  _DEFAULT_ENTRY_NAME,\n  _FirebaseAppInternal as _FirebaseAppExp\n} from '@firebase/app';\nimport { _FirebaseService, _FirebaseNamespace } from './types';\nimport { Compat } from '@firebase/util';\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport interface _FirebaseApp {\n  /**\n   * The (read-only) name (identifier) for this App. '[DEFAULT]' is the default\n   * App.\n   */\n  name: string;\n\n  /**\n   * The (read-only) configuration options from the app initialization.\n   */\n  options: FirebaseOptions;\n\n  /**\n   * The settable config flag for GDPR opt-in/opt-out\n   */\n  automaticDataCollectionEnabled: boolean;\n\n  /**\n   * Make the given App unusable and free resources.\n   */\n  delete(): Promise<void>;\n}\n/**\n * Global context object for a collection of services using\n * a shared authentication state.\n *\n * marked as internal because it references internal types exported from @firebase/app\n * @internal\n */\nexport class FirebaseAppImpl implements Compat<_FirebaseAppExp>, _FirebaseApp {\n  private container: ComponentContainer;\n\n  constructor(\n    readonly _delegate: _FirebaseAppExp,\n    private readonly firebase: _FirebaseNamespace\n  ) {\n    // add itself to container\n    _addComponent(\n      _delegate,\n      new Component('app-compat', () => this, ComponentType.PUBLIC)\n    );\n\n    this.container = _delegate.container;\n  }\n\n  get automaticDataCollectionEnabled(): boolean {\n    return this._delegate.automaticDataCollectionEnabled;\n  }\n\n  set automaticDataCollectionEnabled(val) {\n    this._delegate.automaticDataCollectionEnabled = val;\n  }\n\n  get name(): string {\n    return this._delegate.name;\n  }\n\n  get options(): FirebaseOptions {\n    return this._delegate.options;\n  }\n\n  delete(): Promise<void> {\n    return new Promise<void>(resolve => {\n      this._delegate.checkDestroyed();\n      resolve();\n    }).then(() => {\n      this.firebase.INTERNAL.removeApp(this.name);\n      return deleteApp(this._delegate);\n    });\n  }\n\n  /**\n   * Return a service instance associated with this app (creating it\n   * on demand), identified by the passed instanceIdentifier.\n   *\n   * NOTE: Currently storage and functions are the only ones that are leveraging this\n   * functionality. They invoke it by calling:\n   *\n   * ```javascript\n   * firebase.app().storage('STORAGE BUCKET ID')\n   * ```\n   *\n   * The service name is passed to this already\n   * @internal\n   */\n  _getService(\n    name: string,\n    instanceIdentifier: string = _DEFAULT_ENTRY_NAME\n  ): _FirebaseService {\n    this._delegate.checkDestroyed();\n\n    // Initialize instance if InstantiationMode is `EXPLICIT`.\n    const provider = this._delegate.container.getProvider(name as Name);\n    if (\n      !provider.isInitialized() &&\n      provider.getComponent()?.instantiationMode === InstantiationMode.EXPLICIT\n    ) {\n      provider.initialize();\n    }\n\n    // getImmediate will always succeed because _getService is only called for registered components.\n    return provider.getImmediate({\n      identifier: instanceIdentifier\n    }) as unknown as _FirebaseService;\n  }\n\n  /**\n   * Remove a service instance from the cache, so we will create a new instance for this service\n   * when people try to get it again.\n   *\n   * NOTE: currently only firestore uses this functionality to support firestore shutdown.\n   *\n   * @param name The service name\n   * @param instanceIdentifier instance identifier in case multiple instances are allowed\n   * @internal\n   */\n  _removeServiceInstance(\n    name: string,\n    instanceIdentifier: string = _DEFAULT_ENTRY_NAME\n  ): void {\n    this._delegate.container\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      .getProvider(name as any)\n      .clearInstance(instanceIdentifier);\n  }\n\n  /**\n   * @param component the component being added to this app's container\n   * @internal\n   */\n  _addComponent(component: Component): void {\n    _addComponent(this._delegate, component);\n  }\n\n  _addOrOverwriteComponent(component: Component): void {\n    _addOrOverwriteComponent(this._delegate, component);\n  }\n\n  toJSON(): object {\n    return {\n      name: this.name,\n      automaticDataCollectionEnabled: this.automaticDataCollectionEnabled,\n      options: this.options\n    };\n  }\n}\n\n// TODO: investigate why the following needs to be commented out\n// Prevent dead-code elimination of these methods w/o invalid property\n// copying.\n// (FirebaseAppImpl.prototype.name && FirebaseAppImpl.prototype.options) ||\n//   FirebaseAppImpl.prototype.delete ||\n//   console.log('dc');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum AppError {\n  NO_APP = 'no-app',\n  INVALID_APP_ARGUMENT = 'invalid-app-argument'\n}\n\nconst ERRORS: ErrorMap<AppError> = {\n  [AppError.NO_APP]:\n    \"No Firebase App '{$appName}' has been created - \" +\n    'call Firebase App.initializeApp()',\n  [AppError.INVALID_APP_ARGUMENT]:\n    'firebase.{$appName}() takes either no argument or a ' +\n    'Firebase App instance.'\n};\n\ntype ErrorParams = { [key in AppError]: { appName: string } };\n\nexport const ERROR_FACTORY = new ErrorFactory<AppError, ErrorParams>(\n  'app-compat',\n  'Firebase',\n  ERRORS\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, FirebaseOptions } from './public-types';\nimport {\n  _FirebaseNamespace,\n  _FirebaseService,\n  FirebaseServiceNamespace\n} from './types';\nimport * as modularAPIs from '@firebase/app';\nimport { _FirebaseAppInternal as _FirebaseAppExp } from '@firebase/app';\nimport { Component, ComponentType, Name } from '@firebase/component';\n\nimport { deepExtend, contains } from '@firebase/util';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { ERROR_FACTORY, AppError } from './errors';\nimport { FirebaseAppLiteImpl } from './lite/firebaseAppLite';\n\n/**\n * Because auth can't share code with other components, we attach the utility functions\n * in an internal namespace to share code.\n * This function return a firebase namespace object without\n * any utility functions, so it can be shared between the regular firebaseNamespace and\n * the lite version.\n */\nexport function createFirebaseNamespaceCore(\n  firebaseAppImpl: typeof FirebaseAppImpl | typeof FirebaseAppLiteImpl\n): _FirebaseNamespace {\n  const apps: { [name: string]: FirebaseApp } = {};\n  // // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  // const components = new Map<string, Component<any>>();\n\n  // A namespace is a plain JavaScript Object.\n  const namespace: _FirebaseNamespace = {\n    // Hack to prevent Babel from modifying the object returned\n    // as the firebase namespace.\n    // @ts-ignore\n    __esModule: true,\n    initializeApp: initializeAppCompat,\n    // @ts-ignore\n    app,\n    registerVersion: modularAPIs.registerVersion,\n    setLogLevel: modularAPIs.setLogLevel,\n    onLog: modularAPIs.onLog,\n    // @ts-ignore\n    apps: null,\n    SDK_VERSION: modularAPIs.SDK_VERSION,\n    INTERNAL: {\n      registerComponent: registerComponentCompat,\n      removeApp,\n      useAsService,\n      modularAPIs\n    }\n  };\n\n  // Inject a circular default export to allow Babel users who were previously\n  // using:\n  //\n  //   import firebase from 'firebase';\n  //   which becomes: var firebase = require('firebase').default;\n  //\n  // instead of\n  //\n  //   import * as firebase from 'firebase';\n  //   which becomes: var firebase = require('firebase');\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  (namespace as any)['default'] = namespace;\n\n  // firebase.apps is a read-only getter.\n  Object.defineProperty(namespace, 'apps', {\n    get: getApps\n  });\n\n  /**\n   * Called by App.delete() - but before any services associated with the App\n   * are deleted.\n   */\n  function removeApp(name: string): void {\n    delete apps[name];\n  }\n\n  /**\n   * Get the App object for a given name (or DEFAULT).\n   */\n  function app(name?: string): FirebaseApp {\n    name = name || modularAPIs._DEFAULT_ENTRY_NAME;\n    if (!contains(apps, name)) {\n      throw ERROR_FACTORY.create(AppError.NO_APP, { appName: name });\n    }\n    return apps[name];\n  }\n\n  // @ts-ignore\n  app['App'] = firebaseAppImpl;\n\n  /**\n   * Create a new App instance (name must be unique).\n   *\n   * This function is idempotent. It can be called more than once and return the same instance using the same options and config.\n   */\n  function initializeAppCompat(\n    options: FirebaseOptions,\n    rawConfig = {}\n  ): FirebaseApp {\n    const app = modularAPIs.initializeApp(\n      options,\n      rawConfig\n    ) as _FirebaseAppExp;\n\n    if (contains(apps, app.name)) {\n      return apps[app.name];\n    }\n\n    const appCompat = new firebaseAppImpl(app, namespace);\n    apps[app.name] = appCompat;\n    return appCompat;\n  }\n\n  /*\n   * Return an array of all the non-deleted FirebaseApps.\n   */\n  function getApps(): FirebaseApp[] {\n    // Make a copy so caller cannot mutate the apps list.\n    return Object.keys(apps).map(name => apps[name]);\n  }\n\n  function registerComponentCompat<T extends Name>(\n    component: Component<T>\n  ): FirebaseServiceNamespace<_FirebaseService> | null {\n    const componentName = component.name;\n    const componentNameWithoutCompat = componentName.replace('-compat', '');\n    if (\n      modularAPIs._registerComponent(component) &&\n      component.type === ComponentType.PUBLIC\n    ) {\n      // create service namespace for public components\n      // The Service namespace is an accessor function ...\n      const serviceNamespace = (\n        appArg: FirebaseApp = app()\n      ): _FirebaseService => {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        if (typeof (appArg as any)[componentNameWithoutCompat] !== 'function') {\n          // Invalid argument.\n          // This happens in the following case: firebase.storage('gs:/')\n          throw ERROR_FACTORY.create(AppError.INVALID_APP_ARGUMENT, {\n            appName: componentName\n          });\n        }\n\n        // Forward service instance lookup to the FirebaseApp.\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        return (appArg as any)[componentNameWithoutCompat]();\n      };\n\n      // ... and a container for service-level properties.\n      if (component.serviceProps !== undefined) {\n        deepExtend(serviceNamespace, component.serviceProps);\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      (namespace as any)[componentNameWithoutCompat] = serviceNamespace;\n\n      // Patch the FirebaseAppImpl prototype\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      (firebaseAppImpl.prototype as any)[componentNameWithoutCompat] =\n        // TODO: The eslint disable can be removed and the 'ignoreRestArgs'\n        // option added to the no-explicit-any rule when ESlint releases it.\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        function (...args: any) {\n          const serviceFxn = this._getService.bind(this, componentName);\n          return serviceFxn.apply(\n            this,\n            component.multipleInstances ? args : []\n          );\n        };\n    }\n\n    return component.type === ComponentType.PUBLIC\n      ? // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        (namespace as any)[componentNameWithoutCompat]\n      : null;\n  }\n\n  // Map the requested service to a registered service name\n  // (used to map auth to serverAuth service when needed).\n  function useAsService(app: FirebaseApp, name: string): string | null {\n    if (name === 'serverAuth') {\n      return null;\n    }\n\n    const useService = name;\n\n    return useService;\n  }\n\n  return namespace;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseNamespace } from './public-types';\nimport { createSubscribe, deepExtend, ErrorFactory } from '@firebase/util';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { createFirebaseNamespaceCore } from './firebaseNamespaceCore';\n\n/**\n * Return a firebase namespace object.\n *\n * In production, this will be called exactly once and the result\n * assigned to the 'firebase' global.  It may be called multiple times\n * in unit tests.\n */\nexport function createFirebaseNamespace(): FirebaseNamespace {\n  const namespace = createFirebaseNamespaceCore(FirebaseAppImpl);\n  namespace.INTERNAL = {\n    ...namespace.INTERNAL,\n    createFirebaseNamespace,\n    extendNamespace,\n    createSubscribe,\n    ErrorFactory,\n    deepExtend\n  };\n\n  /**\n   * Patch the top-level firebase namespace with additional properties.\n   *\n   * firebase.INTERNAL.extendNamespace()\n   */\n  function extendNamespace(props: { [prop: string]: unknown }): void {\n    deepExtend(namespace, props);\n  }\n\n  return namespace;\n}\n\nexport const firebase = createFirebaseNamespace();\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nexport const logger = new Logger('@firebase/app-compat');\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseNamespace } from './public-types';\nimport { getGlobal } from '@firebase/util';\nimport { firebase as firebaseNamespace } from './firebaseNamespace';\nimport { logger } from './logger';\nimport { registerCoreComponents } from './registerCoreComponents';\n\ndeclare global {\n  interface Window {\n    firebase: FirebaseNamespace;\n  }\n}\n\ntry {\n  const globals = getGlobal();\n  // Firebase Lite detection\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  if ((globals as any).firebase !== undefined) {\n    logger.warn(`\n      Warning: Firebase is already defined in the global scope. Please make sure\n      Firebase library is only loaded once.\n    `);\n\n    // eslint-disable-next-line\n    const sdkVersion = ((globals as any).firebase as FirebaseNamespace)\n      .SDK_VERSION;\n    if (sdkVersion && sdkVersion.indexOf('LITE') >= 0) {\n      logger.warn(`\n        Warning: You are trying to load Firebase while using Firebase Performance standalone script.\n        You should load Firebase Performance with this instance of Firebase to avoid loading duplicate code.\n        `);\n    }\n  }\n} catch {\n  // ignore errors thrown by getGlobal\n}\n\nconst firebase = firebaseNamespace;\n\nregisterCoreComponents();\n\n// eslint-disable-next-line import/no-default-export\nexport default firebase;\n\nexport { _FirebaseNamespace, _FirebaseService } from './types';\nexport { FirebaseApp, FirebaseNamespace } from './public-types';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerVersion } from '@firebase/app';\n\nimport { name, version } from '../package.json';\n\nexport function registerCoreComponents(variant?: string): void {\n  // Register `app` package.\n  registerVersion(name, version, variant);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase from '@firebase/app-compat';\nimport { name, version } from '../../package.json';\n\nfirebase.registerVersion(name, version, 'app-compat-cdn');\n\nexport default firebase;\n"], "names": ["getDefaultsFromPostinstall", "stringToByteArray", "str", "out", "let", "p", "i", "length", "c", "charCodeAt", "base64", "byteToCharMap_", "charToByteMap_", "byteToCharMapWebSafe_", "charToByteMapWebSafe_", "ENCODED_VALS_BASE", "ENCODED_VALS", "this", "ENCODED_VALS_WEBSAFE", "HAS_NATIVE_SUPPORT", "atob", "encodeByteArray", "input", "webSafe", "Array", "isArray", "Error", "init_", "byteToCharMap", "output", "byte1", "haveByte2", "byte2", "haveByte3", "byte3", "outByte3", "outByte4", "push", "join", "encodeString", "btoa", "decodeString", "byteArrayToString", "bytes", "decodeStringToByteArray", "pos", "u", "c2", "c3", "c1", "String", "fromCharCode", "charToByteMap", "char<PERSON>t", "byte4", "DecodeBase64StringError", "constructor", "name", "base64Encode", "utf8Bytes", "base64urlEncodeWithoutPadding", "replace", "base64Decode", "e", "console", "error", "deepExtend", "target", "source", "Object", "Date", "getTime", "undefined", "prop", "hasOwnProperty", "getGlobal", "self", "window", "global", "getDefaultsFromGlobal", "__FIREBASE_DEFAULTS__", "getDefaultsFromEnvVariable", "defaultsJsonString", "process", "env", "JSON", "parse", "getDefaultsFromCookie", "document", "match", "cookie", "decoded", "getDefaults", "info", "getDefaultAppConfig", "_a", "config", "Deferred", "reject", "resolve", "promise", "Promise", "wrapCallback", "callback", "value", "catch", "isWebWorker", "WorkerGlobalScope", "FirebaseError", "code", "message", "customData", "super", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "PATTERN", "_", "key", "fullMessage", "contains", "obj", "call", "deepEqual", "a", "b", "k", "a<PERSON><PERSON><PERSON>", "keys", "b<PERSON><PERSON><PERSON>", "includes", "aProp", "bProp", "isObject", "thing", "createSubscribe", "executor", "onNoObservers", "proxy", "ObserverProxy", "subscribe", "bind", "observers", "unsubscribes", "observerCount", "task", "finalized", "then", "next", "forEachObserver", "observer", "close", "complete", "nextOrObserver", "methods", "method", "noop", "unsub", "unsubscribeOne", "finalError", "fn", "sendOne", "err", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "DEFAULT_ENTRY_NAME", "Provider", "container", "component", "instances", "Map", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "instancesOptions", "onInitCallbacks", "get", "identifier", "normalizedIdentifier", "normalizeInstanceIdentifier", "has", "deferred", "set", "isInitialized", "shouldAutoInitialize", "instance", "getOrInitializeService", "instanceIdentifier", "getImmediate", "options", "optional", "getComponent", "setComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entries", "clearInstance", "delete", "services", "from", "values", "await", "all", "filter", "map", "INTERNAL", "_delete", "isComponentSet", "getOptions", "initialize", "opts", "onInit", "existingCallbacks", "Set", "add", "existingInstance", "invokeOnInitCallbacks", "callbacks", "ComponentContainer", "providers", "addComponent", "provider", "get<PERSON><PERSON><PERSON>", "addOrOverwriteComponent", "getProviders", "LogLevel", "levelStringToEnum", "debug", "DEBUG", "verbose", "VERBOSE", "INFO", "warn", "WARN", "ERROR", "silent", "SILENT", "defaultLogLevel", "ConsoleMethod", "defaultLogHandler", "logType", "args", "logLevel", "now", "toISOString", "<PERSON><PERSON>", "_logLevel", "_log<PERSON><PERSON><PERSON>", "_userLogHandler", "val", "TypeError", "setLogLevel", "log<PERSON><PERSON><PERSON>", "userLogHandler", "log", "instanceOfAny", "object", "constructors", "some", "idbProxyableTypes", "cursorAdvanceMethods", "cursorRequestMap", "WeakMap", "transactionDoneMap", "transactionStoreNamesMap", "transformCache", "reverseTransformCache", "idbProxyTraps", "receiver", "IDBTransaction", "objectStoreNames", "objectStore", "wrap", "wrapFunction", "func", "IDBDatabase", "transaction", "IDBCursor", "advance", "continue", "continuePrimaryKey", "apply", "unwrap", "storeNames", "tx", "sort", "transformCachableValue", "done", "unlisten", "removeEventListener", "DOMException", "addEventListener", "IDBObjectStore", "IDBIndex", "Proxy", "request", "newValue", "IDBRequest", "success", "result", "readMethods", "writeMethods", "cachedMethods", "getMethod", "targetFuncName", "useIndex", "isWrite", "async", "storeName", "store", "index", "shift", "oldTraps", "PlatformLoggerServiceImpl", "getPlatformInfoString", "library", "version", "logString", "logger", "variant", "PLATFORM_LOG_STRING", "@firebase/app", "@firebase/app-compat", "@firebase/analytics", "@firebase/analytics-compat", "@firebase/app-check", "@firebase/app-check-compat", "@firebase/auth", "@firebase/auth-compat", "@firebase/database", "@firebase/data-connect", "@firebase/database-compat", "@firebase/functions", "@firebase/functions-compat", "@firebase/installations", "@firebase/installations-compat", "@firebase/messaging", "@firebase/messaging-compat", "@firebase/performance", "@firebase/performance-compat", "@firebase/remote-config", "@firebase/remote-config-compat", "@firebase/storage", "@firebase/storage-compat", "@firebase/firestore", "@firebase/firestore-compat", "@firebase/vertexai", "fire-js", "firebase", "_apps", "_serverApps", "_components", "_addComponent", "app", "_addOrOverwriteComponent", "_registerComponent", "serverApp", "componentName", "_get<PERSON><PERSON><PERSON>", "heartbeatController", "triggerHeartbeat", "_isFirebaseApp", "ERROR_FACTORY", "no-app", "bad-app-name", "duplicate-app", "app-deleted", "server-app-deleted", "no-options", "invalid-app-argument", "invalid-log-argument", "idb-open", "idb-get", "idb-set", "idb-delete", "finalization-registry-not-supported", "invalid-server-app-environment", "FirebaseAppImpl", "_isDeleted", "_options", "assign", "_config", "_name", "_automaticDataCollectionEnabled", "automaticDataCollectionEnabled", "_container", "checkDestroyed", "isDeleted", "appName", "validateTokenTTL", "base64Token", "tokenName", "second<PERSON><PERSON>", "split", "exp", "FirebaseServerAppImpl", "serverConfig", "<PERSON><PERSON><PERSON><PERSON>", "_serverConfig", "authIdToken", "appCheckToken", "_finalizationRegistry", "FinalizationRegistry", "automaticCleanup", "_refCount", "incRefCount", "releaseOnDeref", "registerVersion", "packageName", "toJSON", "refCount", "register", "decRefCount", "deleteApp", "settings", "SDK_VERSION", "initializeApp", "rawConfig", "existingApp", "newApp", "cleanupProviders", "libraryKeyOrName", "warning", "libraryMismatch", "versionMismatch", "onLog", "logCallback", "setUserLogHandler", "customLogLevel", "level", "arg", "toString", "stringify", "ignored", "toLowerCase", "for<PERSON>ach", "inst", "DB_NAME", "DB_VERSION", "STORE_NAME", "db<PERSON><PERSON><PERSON>", "getDbPromise", "blocked", "upgrade", "blocking", "terminated", "indexedDB", "open", "openPromise", "event", "oldVersion", "newVersion", "db", "createObjectStore", "originalErrorMessage", "writeHeartbeatsToIndexedDB", "heartbeatObject", "put", "computeKey", "idbGetError", "appId", "HeartbeatServiceImpl", "_heartbeatsCache", "_storage", "HeartbeatStorageImpl", "_heartbeatsCachePromise", "read", "earliestHeartbeatIdx", "agent", "date", "getUTCDateString", "heartbeats", "_b", "lastSentHeartbeatDate", "singleDateHeartbeat", "earliestHeartbeatDate", "splice", "overwrite", "getHeartbeatsHeader", "heartbeatsToSend", "unsentEntries", "headerString", "heartbeatsCache", "maxSize", "slice", "heartbeatEntry", "find", "hb", "dates", "countBytes", "pop", "substring", "_canUseIndexedDBPromise", "runIndexedDBEnvironmentCheck", "isIndexedDBAvailable", "preExist", "DB_CHECK_NAME", "onsuccess", "deleteDatabase", "onupgradeneeded", "onerror", "idbHeartbeatObject", "heartbeatsObject", "existingHeartbeatsObject", "clear", "_serverAppConfig", "appOptions", "nameObj", "nameString", "reduce", "hash", "Math", "imul", "_delegate", "removeApp", "_getService", "_DEFAULT_ENTRY_NAME", "_removeServiceInstance", "createFirebaseNamespaceCore", "firebaseAppImpl", "apps", "namespace", "__esModule", "modularAPIs.initializeApp", "appCompat", "modularAPIs.registerVersion", "modularAPIs.setLogLevel", "modularAPIs.onLog", "modularAPIs.SDK_VERSION", "registerComponent", "componentNameWithoutCompat", "serviceNamespace", "modularAPIs._registerComponent", "appArg", "useAsService", "useService", "modularAPIs", "modularAPIs._DEFAULT_ENTRY_NAME", "defineProperty", "createFirebaseNamespace", "extendNamespace", "sdkVersion", "globals", "indexOf", "firebaseNamespace", "registerCoreComponents"], "mappings": "sNAiBA,IAAMA,EAA6B,OCA7BC,EAAoB,SAAUC,GAElC,IAAMC,EAAgB,GACtBC,IAAIC,EAAI,EACR,IAAKD,IAAIE,EAAI,EAAGA,EAAIJ,EAAIK,OAAQD,CAAC,GAAI,CACnCF,IAAII,EAAIN,EAAIO,WAAWH,CAAC,EACpBE,EAAI,IACNL,EAAIE,CAAC,IAAMG,GACFA,EAAI,KACbL,EAAIE,CAAC,IAAOG,GAAK,EAAK,KAGL,QAAZ,MAAJA,IACDF,EAAI,EAAIJ,EAAIK,QACyB,QAAZ,MAAxBL,EAAIO,WAAWH,EAAI,CAAC,IAGrBE,EAAI,QAAgB,KAAJA,IAAe,KAA6B,KAAtBN,EAAIO,WAAW,EAAEH,CAAC,GACxDH,EAAIE,CAAC,IAAOG,GAAK,GAAM,IACvBL,EAAIE,CAAC,IAAQG,GAAK,GAAM,GAAM,KAI9BL,EAAIE,CAAC,IAAOG,GAAK,GAAM,IACvBL,EAAIE,CAAC,IAAQG,GAAK,EAAK,GAAM,KAC7BL,EAAIE,CAAC,IAAW,GAAJG,EAAU,IAEzB,CACD,OAAOL,CACT,EA6DaO,EAAiB,CAI5BC,eAAgB,KAKhBC,eAAgB,KAMhBC,sBAAuB,KAMvBC,sBAAuB,KAMvBC,kBACE,iEAKFC,mBACE,OAAOC,KAAKF,kBAAoB,KACjC,EAKDG,2BACE,OAAOD,KAAKF,kBAAoB,KACjC,EASDI,mBAAoC,YAAhB,OAAOC,KAW3BC,gBAAgBC,EAA8BC,GAC5C,GAAI,CAACC,MAAMC,QAAQH,CAAK,EACtB,MAAMI,MAAM,+CAA+C,EAG7DT,KAAKU,MAAK,EAEV,IAAMC,EAAgBL,EAClBN,KAAKJ,sBACLI,KAAKN,eAEHkB,EAAS,GAEf,IAAKzB,IAAIE,EAAI,EAAGA,EAAIgB,EAAMf,OAAQD,GAAK,EAAG,CACxC,IAAMwB,EAAQR,EAAMhB,GACdyB,EAAYzB,EAAI,EAAIgB,EAAMf,OAC1ByB,EAAQD,EAAYT,EAAMhB,EAAI,GAAK,EACnC2B,EAAY3B,EAAI,EAAIgB,EAAMf,OAC1B2B,EAAQD,EAAYX,EAAMhB,EAAI,GAAK,EAIzCF,IAAI+B,GAAqB,GAARH,IAAiB,EAAME,GAAS,EAC7CE,EAAmB,GAARF,EAEVD,IACHG,EAAW,GAENL,KACHI,EAAW,IAIfN,EAAOQ,KACLT,EAdeE,GAAS,GAexBF,GAdyB,EAARE,IAAiB,EAAME,GAAS,GAejDJ,EAAcO,GACdP,EAAcQ,EAAS,CAE1B,CAED,OAAOP,EAAOS,KAAK,EAAE,CACtB,EAUDC,aAAajB,EAAeC,GAG1B,OAAIN,KAAKE,oBAAsB,CAACI,EACvBiB,KAAKlB,CAAK,EAEZL,KAAKI,gBAAgBpB,EAAkBqB,CAAK,EAAGC,CAAO,CAC9D,EAUDkB,aAAanB,EAAeC,GAG1B,GAAIN,KAAKE,oBAAsB,CAACI,EAC9B,OAAOH,KAAKE,CAAK,EAEZoB,CAAAA,IA9LyBC,EA8LP1B,KAAK2B,wBAAwBtB,EAAOC,CAAO,EA5LtE,IAAMpB,EAAgB,GACtBC,IAAIyC,EAAM,EACRrC,EAAI,EACN,KAAOqC,EAAMF,EAAMpC,QAAQ,CACzB,IAWQuC,EAMAC,EACAC,EAlBFC,EAAKN,EAAME,CAAG,IAChBI,EAAK,IACP9C,EAAIK,CAAC,IAAM0C,OAAOC,aAAaF,CAAE,EACnB,IAALA,GAAYA,EAAK,KACpBF,EAAKJ,EAAME,CAAG,IACpB1C,EAAIK,CAAC,IAAM0C,OAAOC,cAAoB,GAALF,IAAY,EAAW,GAALF,CAAQ,GAC7C,IAALE,GAAYA,EAAK,KAKpBH,IACI,EAALG,IAAW,IAAa,GAJlBN,EAAME,CAAG,MAIgB,IAAa,GAHtCF,EAAME,CAAG,MAGoC,EAAW,GAFxDF,EAAME,CAAG,KAGlB,MACF1C,EAAIK,CAAC,IAAM0C,OAAOC,aAAa,OAAUL,GAAK,GAAG,EACjD3C,EAAIK,CAAC,IAAM0C,OAAOC,aAAa,OAAc,KAAJL,EAAS,IAE5CC,EAAKJ,EAAME,CAAG,IACdG,EAAKL,EAAME,CAAG,IACpB1C,EAAIK,CAAC,IAAM0C,OAAOC,cACT,GAALF,IAAY,IAAa,GAALF,IAAY,EAAW,GAALC,CAAQ,EAGrD,CACD,OAAO7C,EAAImC,KAAK,EAAE,EAgKTI,MAA8D,CACtE,EAiBDE,wBAAwBtB,EAAeC,GACrCN,KAAKU,MAAK,EAEV,IAAMyB,EAAgB7B,EAClBN,KAAKH,sBACLG,KAAKL,eAEHiB,EAAmB,GAEzB,IAAKzB,IAAIE,EAAI,EAAGA,EAAIgB,EAAMf,QAAU,CAClC,IAAMuB,EAAQsB,EAAc9B,EAAM+B,OAAO/C,CAAC,EAAE,GAGtC0B,EADY1B,EAAIgB,EAAMf,OACF6C,EAAc9B,EAAM+B,OAAO/C,CAAC,GAAK,EAIrD4B,EAHN,EAAE5B,EAEoBgB,EAAMf,OACF6C,EAAc9B,EAAM+B,OAAO/C,CAAC,GAAK,GAIrDgD,EAHN,EAAEhD,EAEoBgB,EAAMf,OACF6C,EAAc9B,EAAM+B,OAAO/C,CAAC,GAAK,GAG3D,GAFA,EAAEA,EAEW,MAATwB,GAA0B,MAATE,GAA0B,MAATE,GAA0B,MAAToB,EACrD,MAAM,IAAIC,EAIZ1B,EAAOQ,KADWP,GAAS,EAAME,GAAS,CACtB,EAEN,KAAVE,IAEFL,EAAOQ,KADYL,GAAS,EAAK,IAASE,GAAS,CAC/B,EAEN,KAAVoB,IAEFzB,EAAOQ,KADYH,GAAS,EAAK,IAAQoB,CACrB,CAGzB,CAED,OAAOzB,CACR,EAODF,QACE,GAAI,CAACV,KAAKN,eAAgB,CACxBM,KAAKN,eAAiB,GACtBM,KAAKL,eAAiB,GACtBK,KAAKJ,sBAAwB,GAC7BI,KAAKH,sBAAwB,GAG7B,IAAKV,IAAIE,EAAI,EAAGA,EAAIW,KAAKD,aAAaT,OAAQD,CAAC,GAC7CW,KAAKN,eAAeL,GAAKW,KAAKD,aAAaqC,OAAO/C,CAAC,EACnDW,KAAKL,eAAeK,KAAKN,eAAeL,IAAMA,EAC9CW,KAAKJ,sBAAsBP,GAAKW,KAAKC,qBAAqBmC,OAAO/C,CAAC,GAClEW,KAAKH,sBAAsBG,KAAKJ,sBAAsBP,IAAMA,IAGnDW,KAAKF,kBAAkBR,SAC9BU,KAAKL,eAAeK,KAAKC,qBAAqBmC,OAAO/C,CAAC,GAAKA,EAC3DW,KAAKH,sBAAsBG,KAAKD,aAAaqC,OAAO/C,CAAC,GAAKA,EAG/D,CACF,CACD,QAKWiD,UAAgC7B,MAA7C8B,kCACWvC,KAAIwC,KAAG,yBACjB,CAAA,CAK2B,SAAfC,EAAyBxD,GACpC,IAAMyD,EAAY1D,EAAkBC,CAAG,EACvC,OAAOQ,EAAOW,gBAAgBsC,EAAW,CAAA,CAAI,CAC/C,CAHO,IASMC,EAAgC,SAAU1D,GAErD,OAAOwD,EAAaxD,CAAG,EAAE2D,QAAQ,MAAO,EAAE,CAC5C,EAWaC,EAAe,SAAU5D,GACpC,IACE,OAAOQ,EAAO+B,aAAavC,EAAK,CAAA,CAAI,CAGrC,CAFC,MAAO6D,GACPC,QAAQC,MAAM,wBAAyBF,CAAC,CACzC,CACD,OAAO,IACT,EClVgB,SAAAG,EAAWC,EAAiBC,GAC1C,GAAI,EAAEA,aAAkBC,QACtB,OAAOD,EAGT,OAAQA,EAAOZ,aACb,KAAKc,KAIH,OAAO,IAAIA,KADOF,EACQG,QAAS,CAAA,EAErC,KAAKF,OACYG,KAAAA,IAAXL,IACFA,EAAS,IAEX,MACF,KAAK3C,MAEH2C,EAAS,GACT,MAEF,QAEE,OAAOC,CACV,CAED,IAAK,IAAMK,KAAQL,EAEZA,EAAOM,eAAeD,CAAI,GAalB,cAbmCA,IAG/CN,EAAmCM,GAAQP,EACzCC,EAAmCM,GACnCL,EAAmCK,EAAK,GAI7C,OAAON,CACT,UCvDgBQ,IACd,GAAoB,aAAhB,OAAOC,KACT,OAAOA,KAET,GAAsB,aAAlB,OAAOC,OACT,OAAOA,OAET,GAAsB,aAAlB,OAAOC,OACT,OAAOA,OAET,MAAM,IAAIpD,MAAM,iCAAiC,CACnD,CCuBA,IAAMqD,EAAwB,IAC5BJ,EAAW,EAACK,sBAURC,EAA6B,KACjC,IAGMC,EAHN,MAAuB,aAAnB,OAAOC,SAAkD,KAAA,IAAhBA,QAAQC,MAG/CF,EAAqBC,QAAQC,IAAIJ,uBAE9BK,KAAKC,MAAMJ,CAAkB,EALtC,KAAA,CAOF,EAEMK,EAAwB,KAC5B,GAAwB,aAApB,OAAOC,SAAX,CAGApF,IAAIqF,EACJ,IACEA,EAAQD,SAASE,OAAOD,MAAM,+BAA+B,CAK9D,CAJC,MAAO1B,GAGP,MACD,CACD,IAAM4B,EAAUF,GAAS3B,EAAa2B,EAAM,EAAE,EAC9C,OAAOE,GAAWN,KAAKC,MAAMK,CAAO,CAVnC,CAWH,EASaC,EAAc,KACzB,IACE,OACE5F,EAA4B,GAC5B+E,EAAuB,GACvBE,EAA4B,GAC5BM,GAWH,CATC,MAAOxB,GAOPC,QAAQ6B,KAAK,+CAA+C9B,CAAG,CAEhE,CACH,EA2Ca+B,EAAsB,KAAyC,IAAAC,EAC1E,OAAA,OAAAA,EAAAH,EAAa,GAAA,KAAA,EAAAG,EAAEC,cCjJJC,EAIXzC,cAFAvC,KAAAiF,OAAoC,OACpCjF,KAAAkF,QAAqC,OAEnClF,KAAKmF,QAAU,IAAIC,QAAQ,CAACF,EAASD,KACnCjF,KAAKkF,QAAUA,EACflF,KAAKiF,OAASA,CAChB,CAAC,CACF,CAODI,aACEC,GAEA,MAAO,CAACtC,EAAOuC,KACTvC,EACFhD,KAAKiF,OAAOjC,CAAK,EAEjBhD,KAAKkF,QAAQK,CAAK,EAEI,YAApB,OAAOD,IAGTtF,KAAKmF,QAAQK,MAAM,MAAQ,EAIH,IAApBF,EAAShG,OACXgG,EAAStC,CAAK,EAEdsC,EAAStC,EAAOuC,CAAK,EAG3B,CACD,CACF,UCoCeE,IACd,MAC+B,aAA7B,OAAOC,mBACS,aAAhB,OAAO/B,MACPA,gBAAgB+B,iBAEpB,OC1BaC,UAAsBlF,MAIjC8B,YAEWqD,EACTC,EAEOC,GAEPC,MAAMF,CAAO,EALJ7F,KAAI4F,KAAJA,EAGF5F,KAAU8F,WAAVA,EAPA9F,KAAIwC,KAdI,gBA6BfY,OAAO4C,eAAehG,KAAM2F,EAAcM,SAAS,EAI/CxF,MAAMyF,mBACRzF,MAAMyF,kBAAkBlG,KAAMmG,EAAaF,UAAUG,MAAM,CAE9D,CACF,OAEYD,EAIX5D,YACmB8D,EACAC,EACAC,GAFAvG,KAAOqG,QAAPA,EACArG,KAAWsG,YAAXA,EACAtG,KAAMuG,OAANA,CACf,CAEJH,OACER,KACGY,GAEH,IAcuCA,EAdjCV,EAAcU,EAAK,IAAoB,GACvCC,EAAczG,KAAKqG,QAAR,IAAmBT,EAC9Bc,EAAW1G,KAAKuG,OAAOX,GAEvBC,EAAUa,GAUuBF,EAVcV,EAAVY,EAW7B9D,QAAQ+D,EAAS,CAACC,EAAGC,KACnC,IAAMtB,EAAQiB,EAAKK,GACnB,OAAgB,MAATtB,EAAgBtD,OAAOsD,CAAK,MAAQsB,KAC7C,CAAC,GAdoE,QAE7DC,EAAiB9G,KAAKsG,iBAAgBT,MAAYY,MAIxD,OAFc,IAAId,EAAcc,EAAUK,EAAahB,CAAU,CAGlE,CACF,CASD,IAAMa,EAAU,gBCtHA,SAAAI,EAA2BC,EAAQH,GACjD,OAAOzD,OAAO6C,UAAUxC,eAAewD,KAAKD,EAAKH,CAAG,CACtD,CAuCgB,SAAAK,EAAUC,EAAWC,GACnC,GAAID,IAAMC,EAAV,CAIA,IAEWC,EAgBAA,EAlBLC,EAAQlE,OAAOmE,KAAKJ,CAAC,EACrBK,EAAQpE,OAAOmE,KAAKH,CAAC,EAC3B,IAAWC,KAAKC,EAAO,CACrB,GAAI,CAACE,EAAMC,SAASJ,CAAC,EACnB,OAGF,IAAMK,EAASP,EAA8BE,GACvCM,EAASP,EAA8BC,GAC7C,GAAIO,EAASF,CAAK,GAAKE,EAASD,CAAK,GACnC,GAAI,CAACT,EAAUQ,EAAOC,CAAK,EACzB,MACD,MACI,GAAID,IAAUC,EACnB,MAEH,CAED,IAAWN,KAAKG,EACd,GAAI,CAACF,EAAMG,SAASJ,CAAC,EACnB,MAtBH,CAyBD,OAAO,CACT,CAEA,SAASO,EAASC,GAChB,OAAiB,OAAVA,GAAmC,UAAjB,OAAOA,CAClC,CC/BgB,SAAAC,EACdC,EACAC,GAEA,IAAMC,EAAQ,IAAIC,EAAiBH,EAAUC,CAAa,EAC1D,OAAOC,EAAME,UAAUC,KAAKH,CAAK,CACnC,OAMMC,EAeJ3F,YAAYwF,EAAuBC,GAd3BhI,KAASqI,UAAmC,GAC5CrI,KAAYsI,aAAkB,GAE9BtI,KAAauI,cAAG,EAEhBvI,KAAAwI,KAAOpD,QAAQF,UACflF,KAASyI,UAAG,CAAA,EASlBzI,KAAKgI,cAAgBA,EAIrBhI,KAAKwI,KACFE,KAAK,KACJX,EAAS/H,IAAI,CACf,CAAC,EACAwF,MAAM1C,IACL9C,KAAKgD,MAAMF,CAAC,CACd,CAAC,CACJ,CAED6F,KAAKpD,GACHvF,KAAK4I,gBAAgB,IACnBC,EAASF,KAAKpD,CAAK,CACrB,CAAC,CACF,CAEDvC,MAAMA,GACJhD,KAAK4I,gBAAgB,IACnBC,EAAS7F,MAAMA,CAAK,CACtB,CAAC,EACDhD,KAAK8I,MAAM9F,CAAK,CACjB,CAED+F,WACE/I,KAAK4I,gBAAgB,IACnBC,EAASE,SAAQ,CACnB,CAAC,EACD/I,KAAK8I,MAAK,CACX,CAQDX,UACEa,EACAhG,EACA+F,GAEA5J,IAAI0J,EAEJ,GACqBtF,KAAAA,IAAnByF,GACUzF,KAAAA,IAAVP,GACaO,KAAAA,IAAbwF,EAEA,MAAM,IAAItI,MAAM,mBAAmB,EAoBf8C,KAAAA,KATpBsF,GAmIN,CACE7B,EACAiC,KAEA,GAAmB,UAAf,OAAOjC,GAA4B,OAARA,EAI/B,IAAK,IAAMkC,KAAUD,EACnB,GAAIC,KAAUlC,GAA8B,YAAvB,OAAOA,EAAIkC,GAC9B,OAAO,CAKb,GAxJ2BF,EAA8C,CACjE,OACA,QACA,WACD,EAEUA,EAEA,CACTL,KAAMK,EACNhG,MAAAA,EACA+F,SAAAA,CACc,GAGLJ,OACXE,EAASF,KAAOQ,GAEK5F,KAAAA,IAAnBsF,EAAS7F,QACX6F,EAAS7F,MAAQmG,GAEO5F,KAAAA,IAAtBsF,EAASE,WACXF,EAASE,SAAWI,GAGtB,IAAMC,EAAQpJ,KAAKqJ,eAAejB,KAAKpI,KAAMA,KAAKqI,UAAW/I,MAAM,EAuBnE,OAlBIU,KAAKyI,WAEPzI,KAAKwI,KAAKE,KAAK,KACb,IACM1I,KAAKsJ,WACPT,EAAS7F,MAAMhD,KAAKsJ,UAAU,EAE9BT,EAASE,SAAQ,CAIpB,CAFC,MAAOjG,IAIX,CAAC,EAGH9C,KAAKqI,UAAWjH,KAAKyH,CAAuB,EAErCO,CACR,CAIOC,eAAehK,GACEkE,KAAAA,IAAnBvD,KAAKqI,WAAiD9E,KAAAA,IAAtBvD,KAAKqI,UAAUhJ,KAInD,OAAOW,KAAKqI,UAAUhJ,GAEtBW,EAAAA,KAAKuI,cACsB,IAAvBvI,KAAKuI,gBAA8ChF,KAAAA,IAAvBvD,KAAKgI,eACnChI,KAAKgI,cAAchI,IAAI,CAE1B,CAEO4I,gBAAgBW,GACtB,GAAIvJ,CAAAA,KAAKyI,UAOT,IAAKtJ,IAAIE,EAAI,EAAGA,EAAIW,KAAKqI,UAAW/I,OAAQD,CAAC,GAC3CW,KAAKwJ,QAAQnK,EAAGkK,CAAE,CAErB,CAKOC,QAAQnK,EAAWkK,GAGzBvJ,KAAKwI,KAAKE,KAAK,KACb,GAAuBnF,KAAAA,IAAnBvD,KAAKqI,WAAiD9E,KAAAA,IAAtBvD,KAAKqI,UAAUhJ,GACjD,IACEkK,EAAGvJ,KAAKqI,UAAUhJ,EAAE,CAQrB,CAPC,MAAOyD,GAIgB,aAAnB,OAAOC,SAA2BA,QAAQC,OAC5CD,QAAQC,MAAMF,CAAC,CAElB,CAEL,CAAC,CACF,CAEOgG,MAAMW,GACRzJ,KAAKyI,YAGTzI,KAAKyI,UAAY,CAAA,EACLlF,KAAAA,IAARkG,IACFzJ,KAAKsJ,WAAaG,GAIpBzJ,KAAKwI,KAAKE,KAAK,KACb1I,KAAKqI,UAAY9E,KAAAA,EACjBvD,KAAKgI,cAAgBzE,KAAAA,CACvB,CAAC,EACF,CACF,CAsCD,SAAS4F,WC9QIO,EAiBXnH,YACWC,EACAmH,EACAC,GAFA5J,KAAIwC,KAAJA,EACAxC,KAAe2J,gBAAfA,EACA3J,KAAI4J,KAAJA,EAnBX5J,KAAiB6J,kBAAG,CAAA,EAIpB7J,KAAY8J,aAAe,GAE3B9J,KAAA+J,kBAA2C,OAE3C/J,KAAiBgK,kBAAwC,IAYrD,CAEJC,qBAAqBC,GAEnB,OADAlK,KAAK+J,kBAAoBG,EAClBlK,IACR,CAEDmK,qBAAqBN,GAEnB,OADA7J,KAAK6J,kBAAoBA,EAClB7J,IACR,CAEDoK,gBAAgBC,GAEd,OADArK,KAAK8J,aAAeO,EACbrK,IACR,CAEDsK,2BAA2BhF,GAEzB,OADAtF,KAAKgK,kBAAoB1E,EAClBtF,IACR,CACF,CCrDM,IAAMuK,EAAqB,kBCgBrBC,EAWXjI,YACmBC,EACAiI,GADAzK,KAAIwC,KAAJA,EACAxC,KAASyK,UAATA,EAZXzK,KAAS0K,UAAwB,KACxB1K,KAAA2K,UAAgD,IAAIC,IACpD5K,KAAA6K,kBAGb,IAAID,IACS5K,KAAA8K,iBACf,IAAIF,IACE5K,KAAA+K,gBAAuD,IAAIH,GAK/D,CAMJI,IAAIC,GAEF,IAAMC,EAAuBlL,KAAKmL,4BAA4BF,CAAU,EAExE,GAAI,CAACjL,KAAK6K,kBAAkBO,IAAIF,CAAoB,EAAG,CACrD,IAAMG,EAAW,IAAIrG,EAGrB,GAFAhF,KAAK6K,kBAAkBS,IAAIJ,EAAsBG,CAAQ,EAGvDrL,KAAKuL,cAAcL,CAAoB,GACvClL,KAAKwL,qBAAoB,EAGzB,IACE,IAAMC,EAAWzL,KAAK0L,uBAAuB,CAC3CC,mBAAoBT,CACrB,CAAA,EACGO,GACFJ,EAASnG,QAAQuG,CAAQ,CAK5B,CAHC,MAAO3I,IAKZ,CAED,OAAO9C,KAAK6K,kBAAkBG,IAAIE,CAAoB,EAAG/F,OAC1D,CAkBDyG,aAAaC,OAKLX,EAAuBlL,KAAKmL,4BAChCU,MAAAA,EAAA,KAAA,EAAAA,EAASZ,UAAU,EAEfa,EAAW,OAAAhH,EAAA+G,MAAAA,EAAA,KAAA,EAAAA,EAASC,WAAYhH,EAEtC,GACE9E,CAAAA,KAAKuL,cAAcL,CAAoB,GACvClL,CAAAA,KAAKwL,qBAAoB,EAapB,CAEL,GAAIM,EACF,OAAO,KAEP,MAAMrL,iBAAiBT,KAAKwC,uBAAuB,CAEtD,CAlBC,IACE,OAAOxC,KAAK0L,uBAAuB,CACjCC,mBAAoBT,CACrB,CAAA,CAOF,CANC,MAAOpI,GACP,GAAIgJ,EACF,OAAO,KAEP,MAAMhJ,CAET,CASJ,CAEDiJ,eACE,OAAO/L,KAAK0K,SACb,CAEDsB,aAAatB,GACX,GAAIA,EAAUlI,OAASxC,KAAKwC,KAC1B,MAAM/B,+BACqBiK,EAAUlI,qBAAqBxC,KAAKwC,OAAO,EAIxE,GAAIxC,KAAK0K,UACP,MAAMjK,uBAAuBT,KAAKwC,gCAAgC,EAMpE,GAHAxC,KAAK0K,UAAYA,EAGZ1K,KAAKwL,uBAAV,CAKA,GA2NgC,UA3NXd,EA2NNX,kBA1Nb,IACE/J,KAAK0L,uBAAuB,CAAEC,mBAAoBpB,CAAoB,CAAA,CAMvE,CALC,MAAOzH,IAWX,IAAK,GAAM,CACT6I,EACAM,KACGjM,KAAK6K,kBAAkBqB,UAAW,CAC/BhB,EACJlL,KAAKmL,4BAA4BQ,CAAkB,EAErD,IAEE,IAAMF,EAAWzL,KAAK0L,uBAAuB,CAC3CC,mBAAoBT,CACrB,CAAA,EACDe,EAAiB/G,QAAQuG,CAAQ,CAIlC,CAHC,MAAO3I,IAIV,CAlCA,CAmCF,CAEDqJ,cAAclB,EAAqBV,GACjCvK,KAAK6K,kBAAkBuB,OAAOnB,CAAU,EACxCjL,KAAK8K,iBAAiBsB,OAAOnB,CAAU,EACvCjL,KAAK2K,UAAUyB,OAAOnB,CAAU,CACjC,CAIDmB,eACE,IAAMC,EAAW9L,MAAM+L,KAAKtM,KAAK2K,UAAU4B,OAAM,CAAE,EAEnDC,MAAMpH,QAAQqH,IAAI,CAChB,GAAGJ,EACAK,OAAOrG,GAAW,aAAcA,CAAO,EAEvCsG,IAAItG,GAAYA,EAAgBuG,SAAUR,OAAM,CAAE,EACrD,GAAGC,EACAK,OAAOrG,GAAW,YAAaA,CAAO,EAEtCsG,IAAItG,GAAYA,EAAgBwG,SAAS,EAC7C,CACF,CAEDC,iBACE,OAAyB,MAAlB9M,KAAK0K,SACb,CAEDa,cAAcN,EAAqBV,GACjC,OAAOvK,KAAK2K,UAAUS,IAAIH,CAAU,CACrC,CAED8B,WAAW9B,EAAqBV,GAC9B,OAAOvK,KAAK8K,iBAAiBE,IAAIC,CAAU,GAAK,EACjD,CAED+B,WAAWC,EAA0B,IACnC,GAAM,CAAEpB,QAAAA,EAAU,EAAI,EAAGoB,EACnB/B,EAAuBlL,KAAKmL,4BAChC8B,EAAKtB,kBAAkB,EAEzB,GAAI3L,KAAKuL,cAAcL,CAAoB,EACzC,MAAMzK,MACDT,KAAKwC,SAAQ0I,iCAAoD,EAIxE,GAAI,CAAClL,KAAK8M,iBACR,MAAMrM,mBAAmBT,KAAKwC,kCAAkC,EAGlE,IAOEmJ,EACAM,EARIR,EAAWzL,KAAK0L,uBAAuB,CAC3CC,mBAAoBT,EACpBW,QAAAA,CACD,CAAA,EAGD,IAAW,CACTF,EACAM,KACGjM,KAAK6K,kBAAkBqB,UAGtBhB,IADFlL,KAAKmL,4BAA4BQ,CAAkB,GAEnDM,EAAiB/G,QAAQuG,CAAQ,EAIrC,OAAOA,CACR,CAUDyB,OAAO5H,EAA6B2F,OAC5BC,EAAuBlL,KAAKmL,4BAA4BF,CAAU,EACxE,IAAMkC,EACJ,OAAArI,EAAA9E,KAAK+K,gBAAgBC,IAAIE,CAAoB,GAACpG,EAC9C,IAAIsI,IACND,EAAkBE,IAAI/H,CAAQ,EAC9BtF,KAAK+K,gBAAgBO,IAAIJ,EAAsBiC,CAAiB,EAEhE,IAAMG,EAAmBtN,KAAK2K,UAAUK,IAAIE,CAAoB,EAKhE,OAJIoC,GACFhI,EAASgI,EAAkBpC,CAAoB,EAG1C,KACLiC,EAAkBf,OAAO9G,CAAQ,CACnC,CACD,CAMOiI,sBACN9B,EACAR,GAEA,IAAMuC,EAAYxN,KAAK+K,gBAAgBC,IAAIC,CAAU,EACrD,GAAKuC,EAGL,IAAK,IAAMlI,KAAYkI,EACrB,IACElI,EAASmG,EAAUR,CAAU,CAG9B,CAFC,MAAAnG,IAIL,CAEO4G,uBAAuB,CAC7BC,mBAAAA,EACAE,QAAAA,EAAU,EAAE,GAKZ1M,IAAIsM,EAAWzL,KAAK2K,UAAUK,IAAIW,CAAkB,EACpD,GAAI,CAACF,GAAYzL,KAAK0K,YACpBe,EAAWzL,KAAK0K,UAAUf,gBAAgB3J,KAAKyK,UAAW,CACxDkB,oBAqD+BV,EArDmBU,KAsDlCpB,EAAqBhH,KAAAA,EAAY0H,EArDjDY,QAAAA,CACD,CAAA,EACD7L,KAAK2K,UAAUW,IAAIK,EAAoBF,CAAS,EAChDzL,KAAK8K,iBAAiBQ,IAAIK,EAAoBE,CAAO,EAOrD7L,KAAKuN,sBAAsB9B,EAAWE,CAAkB,EAOpD3L,KAAK0K,UAAUV,mBACjB,IACEhK,KAAK0K,UAAUV,kBACbhK,KAAKyK,UACLkB,EACAF,CAAS,CAIZ,CAFC,MAAA3G,IA4BV,IAAuCmG,EAtBnC,OAAOQ,GAAY,IACpB,CAEON,4BACNF,EAAqBV,GAErB,MAAIvK,CAAAA,KAAK0K,WACA1K,KAAK0K,UAAUb,kBAEfoB,EAFgDV,CAI1D,CAEOiB,uBACN,MACE,CAAC,CAACxL,KAAK0K,WACyB,aAAhC1K,KAAK0K,UAAUX,iBAElB,CACF,OCxVY0D,GAGXlL,YAA6BC,GAAAxC,KAAIwC,KAAJA,EAFZxC,KAAA0N,UAAY,IAAI9C,GAEY,CAW7C+C,aAA6BjD,GAC3B,IAAMkD,EAAW5N,KAAK6N,YAAYnD,EAAUlI,IAAI,EAChD,GAAIoL,EAASd,iBACX,MAAM,IAAIrM,mBACKiK,EAAUlI,yCAAyCxC,KAAKwC,IAAM,EAI/EoL,EAAS5B,aAAatB,CAAS,CAChC,CAEDoD,wBAAwCpD,GACrB1K,KAAK6N,YAAYnD,EAAUlI,IAAI,EACnCsK,kBAEX9M,KAAK0N,UAAUtB,OAAO1B,EAAUlI,IAAI,EAGtCxC,KAAK2N,aAAajD,CAAS,CAC5B,CASDmD,YAA4BrL,GAC1B,IAKMoL,EALN,OAAI5N,KAAK0N,UAAUtC,IAAI5I,CAAI,EAClBxC,KAAK0N,UAAU1C,IAAIxI,CAAI,GAI1BoL,EAAW,IAAIpD,EAAYhI,EAAMxC,IAAI,EAC3CA,KAAK0N,UAAUpC,IAAI9I,EAAMoL,CAAqC,EAEvDA,EACR,CAEDG,eACE,OAAOxN,MAAM+L,KAAKtM,KAAK0N,UAAUnB,OAAQ,CAAA,CAC1C,CACF,CCxCM,IAAM5B,EAAsB,OAavBqD,ECkCC,GDlCDA,EAAAA,EAAAA,GAOX,IANCA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,OAAA,GAAA,SAGF,IAAMC,GAA2D,CAC/DC,MAASF,EAASG,MAClBC,QAAWJ,EAASK,QACpBzJ,KAAQoJ,EAASM,KACjBC,KAAQP,EAASQ,KACjBxL,MAASgL,EAASS,MAClBC,OAAUV,EAASW,QAMfC,GAA4BZ,EAASM,KAmBrCO,GAAgB,EACnBb,EAASG,OAAQ,OACjBH,EAASK,SAAU,OACnBL,EAASM,MAAO,QAChBN,EAASQ,MAAO,QAChBR,EAASS,OAAQ,SAQdK,GAAgC,CAACrD,EAAUsD,KAAYC,KAC3D,GAAID,EAAAA,EAAUtD,EAASwD,UAAvB,CAGA,IAAMC,GAAM,IAAI7L,MAAO8L,YAAW,EAC5BjG,EAAS2F,GAAcE,GAC7B,GAAI7F,CAAAA,EAMF,MAAM,IAAIzI,oEACsDsO,IAAU,EAN1EhM,QAAQmG,OACFgG,OAASzD,EAASjJ,QACtB,GAAGwM,CAAI,CANV,CAaH,QAEaI,GAOX7M,YAAmBC,GAAAxC,KAAIwC,KAAJA,EAUXxC,KAASqP,UAAGT,GAsBZ5O,KAAWsP,YAAeR,GAc1B9O,KAAeuP,gBAAsB,KA1C3C5E,EAAUvJ,KAAKpB,IAAI,CACpB,CAODiP,eACE,OAAOjP,KAAKqP,SACb,CAEDJ,aAAaO,GACX,GAAI,EAAEA,KAAOxB,GACX,MAAM,IAAIyB,4BAA4BD,6BAA+B,EAEvExP,KAAKqP,UAAYG,CAClB,CAGDE,YAAYF,GACVxP,KAAKqP,UAA2B,UAAf,OAAOG,EAAmBvB,GAAkBuB,GAAOA,CACrE,CAODG,iBACE,OAAO3P,KAAKsP,WACb,CACDK,eAAeH,GACb,GAAmB,YAAf,OAAOA,EACT,MAAM,IAAIC,UAAU,mDAAmD,EAEzEzP,KAAKsP,YAAcE,CACpB,CAMDI,qBACE,OAAO5P,KAAKuP,eACb,CACDK,mBAAmBJ,GACjBxP,KAAKuP,gBAAkBC,CACxB,CAMDtB,SAASc,GACPhP,KAAKuP,iBAAmBvP,KAAKuP,gBAAgBvP,KAAMgO,EAASG,MAAO,GAAGa,CAAI,EAC1EhP,KAAKsP,YAAYtP,KAAMgO,EAASG,MAAO,GAAGa,CAAI,CAC/C,CACDa,OAAOb,GACLhP,KAAKuP,iBACHvP,KAAKuP,gBAAgBvP,KAAMgO,EAASK,QAAS,GAAGW,CAAI,EACtDhP,KAAKsP,YAAYtP,KAAMgO,EAASK,QAAS,GAAGW,CAAI,CACjD,CACDpK,QAAQoK,GACNhP,KAAKuP,iBAAmBvP,KAAKuP,gBAAgBvP,KAAMgO,EAASM,KAAM,GAAGU,CAAI,EACzEhP,KAAKsP,YAAYtP,KAAMgO,EAASM,KAAM,GAAGU,CAAI,CAC9C,CACDT,QAAQS,GACNhP,KAAKuP,iBAAmBvP,KAAKuP,gBAAgBvP,KAAMgO,EAASQ,KAAM,GAAGQ,CAAI,EACzEhP,KAAKsP,YAAYtP,KAAMgO,EAASQ,KAAM,GAAGQ,CAAI,CAC9C,CACDhM,SAASgM,GACPhP,KAAKuP,iBAAmBvP,KAAKuP,gBAAgBvP,KAAMgO,EAASS,MAAO,GAAGO,CAAI,EAC1EhP,KAAKsP,YAAYtP,KAAMgO,EAASS,MAAO,GAAGO,CAAI,CAC/C,CACF,CEnND,IAAMc,GAAgB,CAACC,EAAQC,IAAiBA,EAAaC,KAAK,GAAOF,aAAkBxQ,CAAC,EAExF2Q,GACAC,GAqBJ,IAAMC,GAAmB,IAAIC,QACvBC,EAAqB,IAAID,QACzBE,GAA2B,IAAIF,QAC/BG,EAAiB,IAAIH,QACrBI,EAAwB,IAAIJ,QA0DlClR,IAAIuR,EAAgB,CAChB1F,IAAI9H,EAAQM,EAAMmN,GACd,GAAIzN,aAAkB0N,eAAgB,CAElC,GAAa,SAATpN,EACA,OAAO8M,EAAmBtF,IAAI9H,CAAM,EAExC,GAAa,qBAATM,EACA,OAAON,EAAO2N,kBAAoBN,GAAyBvF,IAAI9H,CAAM,EAGzE,GAAa,UAATM,EACA,OAAOmN,EAASE,iBAAiB,GAC3BtN,KAAAA,EACAoN,EAASG,YAAYH,EAASE,iBAAiB,EAAE,CAE9D,CAED,OAAOE,EAAK7N,EAAOM,EAAK,CAC3B,EACD8H,IAAIpI,EAAQM,EAAM+B,GAEd,OADArC,EAAOM,GAAQ+B,EACR,CAAA,CACV,EACD6F,IAAIlI,EAAQM,GACR,OAAIN,aAAkB0N,iBACR,SAATpN,GAA4B,UAATA,IAGjBA,KAAQN,CAClB,CACL,EAIA,SAAS8N,GAAaC,GAIlB,OAAIA,IAASC,YAAYjL,UAAUkL,aAC7B,qBAAsBP,eAAe3K,WA7GnCkK,GAAAA,IACoB,CACpBiB,UAAUnL,UAAUoL,QACpBD,UAAUnL,UAAUqL,SACpBF,UAAUnL,UAAUsL,qBAqHE9J,SAASwJ,CAAI,EAChC,YAAajC,GAIhB,OADAiC,EAAKO,MAAMC,EAAOzR,IAAI,EAAGgP,CAAI,EACtB+B,EAAKX,GAAiBpF,IAAIhL,IAAI,CAAC,CAClD,EAEW,YAAagP,GAGhB,OAAO+B,EAAKE,EAAKO,MAAMC,EAAOzR,IAAI,EAAGgP,CAAI,CAAC,CAClD,EAvBe,SAAU0C,KAAe1C,GAC5B,IAAM2C,EAAKV,EAAKhK,KAAKwK,EAAOzR,IAAI,EAAG0R,EAAY,GAAG1C,CAAI,EAEtD,OADAuB,GAAyBjF,IAAIqG,EAAID,EAAWE,KAAOF,EAAWE,KAAM,EAAG,CAACF,EAAW,EAC5EX,EAAKY,CAAE,CAC1B,CAoBA,CACA,SAASE,GAAuBtM,GAC5B,IA5FoCoM,EAI9BG,EAwFN,MAAqB,YAAjB,OAAOvM,EACAyL,GAAazL,CAAK,GAGzBA,aAAiBqL,iBAhGee,EAiGDpM,EA/F/B+K,EAAmBlF,IAAIuG,CAAE,IAEvBG,EAAO,IAAI1M,QAAQ,CAACF,EAASD,KAC/B,IAAM8M,EAAW,KACbJ,EAAGK,oBAAoB,WAAYjJ,CAAQ,EAC3C4I,EAAGK,oBAAoB,QAAShP,CAAK,EACrC2O,EAAGK,oBAAoB,QAAShP,CAAK,CACjD,EACc+F,EAAW,KACb7D,IACA6M,GACZ,EACc/O,EAAQ,KACViC,EAAO0M,EAAG3O,OAAS,IAAIiP,aAAa,aAAc,YAAY,CAAC,EAC/DF,GACZ,EACQJ,EAAGO,iBAAiB,WAAYnJ,CAAQ,EACxC4I,EAAGO,iBAAiB,QAASlP,CAAK,EAClC2O,EAAGO,iBAAiB,QAASlP,CAAK,CAC1C,CAAK,EAEDsN,EAAmBhF,IAAIqG,EAAIG,CAAI,IA2E3BhC,GAAcvK,EAzJV2K,GAAAA,IACiB,CACjBgB,YACAiB,eACAC,SACAhB,UACAR,eAmJuC,EACpC,IAAIyB,MAAM9M,EAAOmL,CAAa,EAElCnL,EACX,CACA,SAASwL,EAAKxL,GAGV,IA1IsB+M,EAgJhBC,EANN,OAAIhN,aAAiBiN,YA1ICF,EA2IM/M,GA1ItBJ,EAAU,IAAIC,QAAQ,CAACF,EAASD,KAClC,IAAM8M,EAAW,KACbO,EAAQN,oBAAoB,UAAWS,CAAO,EAC9CH,EAAQN,oBAAoB,QAAShP,CAAK,CACtD,EACcyP,EAAU,KACZvN,EAAQ6L,EAAKuB,EAAQI,MAAM,CAAC,EAC5BX,GACZ,EACc/O,EAAQ,KACViC,EAAOqN,EAAQtP,KAAK,EACpB+O,GACZ,EACQO,EAAQJ,iBAAiB,UAAWO,CAAO,EAC3CH,EAAQJ,iBAAiB,QAASlP,CAAK,CAC/C,CAAK,GAEI0F,KAAK,IAGFnD,aAAiB6L,WACjBhB,GAAiB9E,IAAI/F,EAAO+M,CAAO,CAG/C,CAAK,EACI9M,MAAM,MAAS,EAGpBiL,EAAsBnF,IAAInG,EAASmN,CAAO,EACnCnN,GAgHHqL,EAAepF,IAAI7F,CAAK,EACjBiL,EAAexF,IAAIzF,CAAK,IAC7BgN,EAAWV,GAAuBtM,CAAK,KAG5BA,IACbiL,EAAelF,IAAI/F,EAAOgN,CAAQ,EAClC9B,EAAsBnF,IAAIiH,EAAUhN,CAAK,GAEtCgN,EACX,CACA,IAAMd,EAAS,GAAWhB,EAAsBzF,IAAIzF,CAAK,EDrIzD,IAAMoN,GAAc,CAAC,MAAO,SAAU,SAAU,aAAc,SACxDC,GAAe,CAAC,MAAO,MAAO,SAAU,SACxCC,EAAgB,IAAIjI,IAC1B,SAASkI,GAAU5P,EAAQM,GACvB,GAAMN,aAAkBgO,aAClB,EAAA1N,KAAQN,IACM,UAAhB,OAAOM,EAFX,CAKA,GAAIqP,EAAc7H,IAAIxH,CAAI,EACtB,OAAOqP,EAAc7H,IAAIxH,CAAI,EACjC,IAAMuP,EAAiBvP,EAAKZ,QAAQ,aAAc,EAAE,EAC9CoQ,EAAWxP,IAASuP,EACpBE,EAAUL,GAAanL,SAASsL,CAAc,EACpD,IAMM7J,EANN,OAEE6J,KAAmBC,EAAWZ,SAAWD,gBAAgBlM,YACrDgN,GAAWN,GAAYlL,SAASsL,CAAc,IAG9C7J,EAASgK,eAAgBC,KAAcnE,GAEzC,IAAM2C,EAAK3R,KAAKmR,YAAYgC,EAAWF,EAAU,YAAc,UAAU,EACzE9T,IAAI+D,EAASyO,EAAGyB,MAQhB,OAPIJ,IACA9P,EAASA,EAAOmQ,MAAMrE,EAAKsE,MAAO,CAAA,IAM/B,MAAOlO,QAAQqH,IAAI,CACtBvJ,EAAO6P,GAAgB,GAAG/D,CAAI,EAC9BiE,GAAWtB,EAAGG,KACjB,GAAG,EACZ,EACIe,EAAcvH,IAAI9H,EAAM0F,CAAM,EACvBA,GAvBP,KAAA,CANC,CA8BL,CCgCIwH,ED/BwB,CACxB,GADS,EC+BgBA,ED7BzB1F,IAAK,CAAC9H,EAAQM,EAAMmN,IAAamC,GAAU5P,EAAQM,CAAI,GAAK+P,EAASvI,IAAI9H,EAAQM,EAAMmN,CAAQ,EAC/FvF,IAAK,CAAClI,EAAQM,IAAS,CAAC,CAACsP,GAAU5P,EAAQM,CAAI,GAAK+P,EAASnI,IAAIlI,EAAQM,CAAI,CAChF,QEnEYgQ,GACXjR,YAA6BkI,GAAAzK,KAASyK,UAATA,CAAiC,CAG9DgJ,wBAIE,OAHkBzT,KAAKyK,UAAUsD,aAAY,EAI1CpB,IAAIiB,IACH,IAoBAlD,EApBA,MAqBgB,aAAfA,OADDA,EApB6BkD,EAoBR7B,gBACpB,KAAA,EAAArB,EAAWd,OApBJvD,EAAUuH,EAAShC,gBACP8H,QAAX,IAAsBrN,EAAQsN,QAE9B,IAEX,CAAC,EACAjH,OAAOkH,GAAaA,CAAS,EAC7BvS,KAAK,GAAG,CACZ,CACF,kCC1BYwS,EAAS,IAAIzE,GAAO,eAAe,MCKT0E,EC0BhC,IAAMvJ,EAAqB,YAErBwJ,GAAsB,CACjCC,gBAAW,YACXC,uBAAiB,mBACjBC,sBAAiB,iBACjBC,6BAAuB,wBACvBC,sBAAgB,iBAChBC,6BAAsB,wBACtBC,iBAAY,YACZC,wBAAkB,mBAClBC,qBAAgB,YAChBC,yBAAmB,oBACnBC,4BAAsB,mBACtBC,sBAAiB,UACjBC,6BAAuB,iBACvBC,0BAAqB,WACrBC,iCAA2B,kBAC3BC,sBAAiB,WACjBC,6BAAuB,kBACvBC,wBAAmB,YACnBC,+BAAyB,mBACzBC,0BAAoB,UACpBC,iCAA0B,iBAC1BC,oBAAe,WACfC,2BAAqB,kBACrBC,sBAAiB,WACjBC,6BAAuB,kBACvBC,qBAAc,cACdC,UAAW,UACXC,SAAe,aACP,EClDGC,EAAQ,IAAIhL,IAKZiL,EAAc,IAAIjL,IAQlBkL,EAAc,IAAIlL,IAOf,SAAAmL,EACdC,EACAtL,GAEA,IACGsL,EAAwBvL,UAAUkD,aAAajD,CAAS,CAM1D,CALC,MAAO5H,GACP+Q,EAAO3F,mBACQxD,EAAUlI,4CAA4CwT,EAAIxT,KACvEM,CAAC,CAEJ,CACH,CAMgB,SAAAmT,GACdD,EACAtL,GAECsL,EAAwBvL,UAAUqD,wBAAwBpD,CAAS,CACtE,CASM,SAAUwL,EACdxL,GAEA,IAYWsL,EAIAG,EAhBLC,EAAgB1L,EAAUlI,KAChC,GAAIsT,EAAY1K,IAAIgL,CAAa,EAK/B,OAJAvC,EAAO3F,4DACiDkI,IAAgB,EAGjE,CAAA,EAGTN,EAAYxK,IAAI8K,EAAe1L,CAAS,EAGxC,IAAWsL,KAAOJ,EAAMrJ,SACtBwJ,EAAcC,EAAwBtL,CAAS,EAGjD,IAAWyL,KAAaN,EAAYtJ,SAClCwJ,EAAcI,EAAoCzL,CAAS,EAG7D,MAAO,CAAA,CACT,CAWgB,SAAA2L,GACdL,EACAxT,GAEA,IAAM8T,EAAuBN,EAAwBvL,UAClDoD,YAAY,WAAW,EACvBjC,aAAa,CAAEE,SAAU,CAAA,CAAI,CAAE,EAIlC,OAHIwK,GACGA,EAAoBC,mBAEnBP,EAAwBvL,UAAUoD,YAAYrL,CAAI,CAC5D,CA0BM,SAAUgU,GACdxP,GAEA,OAAwCzD,KAAAA,IAAhCyD,EAAoB6E,OAC9B,CChFO,IAAM4K,EAAgB,IAAItQ,EAC/B,MACA,WA7CiC,CACjCuQ,SACE,6EAEFC,eAAyB,iCACzBC,gBACE,kFACFC,cAAwB,kDACxBC,qBAA+B,uCAC/BC,aACE,0EACFC,uBACE,6EAEFC,uBACE,wDACFC,WACE,gFACFC,UACE,qFACFC,UACE,mFACFC,aACE,sFACFC,sCACE,0GACFC,iCACE,4DAmBI,QCrDKC,GAcXjV,YACEsJ,EACA9G,EACA0F,GANQzK,KAAUyX,WAAG,CAAA,EAQrBzX,KAAK0X,SAAgBtU,OAAAuU,OAAA,GAAA9L,CAAO,EAC5B7L,KAAK4X,QAAexU,OAAAuU,OAAA,GAAA5S,CAAM,EAC1B/E,KAAK6X,MAAQ9S,EAAOvC,KACpBxC,KAAK8X,gCACH/S,EAAOgT,+BACT/X,KAAKgY,WAAavN,EAClBzK,KAAKyK,UAAUkD,aACb,IAAIjE,EAAU,MAAO,IAAM1J,KAAI,QAAA,CAAuB,CAEzD,CAED+X,qCAEE,OADA/X,KAAKiY,eAAc,EACZjY,KAAK8X,+BACb,CAEDC,mCAAmCvI,GACjCxP,KAAKiY,eAAc,EACnBjY,KAAK8X,gCAAkCtI,CACxC,CAEDhN,WAEE,OADAxC,KAAKiY,eAAc,EACZjY,KAAK6X,KACb,CAEDhM,cAEE,OADA7L,KAAKiY,eAAc,EACZjY,KAAK0X,QACb,CAED3S,aAEE,OADA/E,KAAKiY,eAAc,EACZjY,KAAK4X,OACb,CAEDnN,gBACE,OAAOzK,KAAKgY,UACb,CAEDE,gBACE,OAAOlY,KAAKyX,UACb,CAEDS,cAAc1I,GACZxP,KAAKyX,WAAajI,CACnB,CAMSyI,iBACR,GAAIjY,KAAKkY,UACP,MAAMzB,EAAcrQ,OAAM,cAAuB,CAAE+R,QAASnY,KAAK6X,KAAK,CAAE,CAE3E,CACF,CCxED,SAASO,GAAiBC,EAAqBC,GAC7C,IAAMC,EAAa1V,EAAawV,EAAYG,MAAM,GAAG,EAAE,EAAE,EACtC,OAAfD,EACFxV,QAAQC,2BACesV,gDAAwD,EAKhE/U,KAAAA,IADAa,KAAKC,MAAMkU,CAAU,EAAEE,IAEtC1V,QAAQC,2BACesV,oDAA4D,EAI5C,IAA7BlU,KAAKC,MAAMkU,CAAU,EAAEE,KACvB,IAAIpV,MAAOC,QAAO,GAElB,GACVP,QAAQC,2BACesV,sCAA8C,CAGzE,OAEaI,WACHlB,GAORjV,YACEsJ,EACA8M,EACAnW,EACAiI,GAGA,IAAMsN,EAC4CxU,KAAAA,IAAhDoV,EAAaZ,gCACTY,EAAaZ,+BAIbhT,EAAwC,CAC5CvC,KAAAA,EACAuV,+BAAAA,GAG0CxU,KAAAA,IAAvCsI,EAA4B+M,OAE/B7S,MAAM8F,EAA4B9G,EAAQ0F,CAAS,EAGnD1E,MADiC8F,EACnBA,QAAS9G,EAAQ0F,CAAS,EAI1CzK,KAAK6Y,cAAazV,OAAAuU,OAAA,CAChBI,+BAAAA,CAA8B,EAC3BY,CAAY,EAIb3Y,KAAK6Y,cAAcC,aACrBV,GAAiBpY,KAAK6Y,cAAcC,YAAa,aAAa,EAI5D9Y,KAAK6Y,cAAcE,eACrBX,GAAiBpY,KAAK6Y,cAAcE,cAAe,eAAe,EAGpE/Y,KAAKgZ,sBAAwB,KACO,aAAhC,OAAOC,uBACTjZ,KAAKgZ,sBAAwB,IAAIC,qBAAqB,KACpDjZ,KAAKkZ,iBAAgB,CACvB,CAAC,GAGHlZ,KAAKmZ,UAAY,EACjBnZ,KAAKoZ,YAAYpZ,KAAK6Y,cAAcQ,cAAc,EAIlDrZ,KAAK6Y,cAAcQ,eAAiB9V,KAAAA,EACpCoV,EAAaU,eAAiB9V,KAAAA,EAE9B+V,EAAgBC,EAAa5F,EAAS,WAAW,CAClD,CAED6F,UAIAC,eACE,OAAOzZ,KAAKmZ,SACb,CAIDC,YAAYpS,GACNhH,KAAKkY,YAGTlY,KAAKmZ,SAAS,GACF5V,KAAAA,IAARyD,GAAoD,OAA/BhH,KAAKgZ,uBAC5BhZ,KAAKgZ,sBAAsBU,SAAS1S,EAAKhH,IAAI,EAEhD,CAGD2Z,cACE,OAAI3Z,KAAKkY,UACA,EAEF,EAAElY,KAAKmZ,SACf,CAKOD,mBACDU,EAAU5Z,IAAI,CACpB,CAED6Z,eAEE,OADA7Z,KAAKiY,eAAc,EACZjY,KAAK6Y,aACb,CAMSZ,iBACR,GAAIjY,KAAKkY,UACP,MAAMzB,EAAcrQ,OAAM,qBAE7B,CACF,CC/GM,IAAM0T,YAoEG,SAAAC,EACdrC,EACAsC,EAAY,IAEZ7a,IAAI0M,EAAU6L,EAEd,GAAyB,UAArB,OAAOsC,EAAwB,CACjC,IAAMxX,EAAOwX,EACbA,EAAY,CAAExX,KAAAA,EACf,CAED,IAAMuC,EAAM3B,OAAAuU,OAAA,CACVnV,KAAM+H,EACNwN,+BAAgC,CAAA,GAC7BiC,CAAS,EAEd,IAAMxX,EAAOuC,EAAOvC,KAEpB,GAAoB,UAAhB,OAAOA,GAAqB,CAACA,EAC/B,MAAMiU,EAAcrQ,OAA8B,eAAA,CAChD+R,QAASlW,OAAOO,CAAI,CACrB,CAAA,EAKH,GAAI,EAFJqJ,EAAAA,GAAYhH,EAAmB,GAG7B,MAAM4R,EAAcrQ,OAAM,cAG5B,IAAM6T,EAAcrE,EAAM5K,IAAIxI,CAAI,EAClC,GAAIyX,EAAa,CAEf,GACE/S,EAAU2E,EAASoO,EAAYpO,OAAO,GACtC3E,EAAUnC,EAAQkV,EAAYlV,MAAM,EAEpC,OAAOkV,EAEP,MAAMxD,EAAcrQ,OAA+B,gBAAA,CAAE+R,QAAS3V,CAAI,CAAE,CAEvE,CAED,IACWkI,EADLD,EAAY,IAAIgD,GAAmBjL,CAAI,EAC7C,IAAWkI,KAAaoL,EAAYvJ,SAClC9B,EAAUkD,aAAajD,CAAS,EAG5BwP,EAAS,IAAI1C,GAAgB3L,EAAS9G,EAAQ0F,CAAS,EAI7D,OAFAmL,EAAMtK,IAAI9I,EAAM0X,CAAM,EAEfA,CACT,CA4LOhH,eAAe0G,EAAU5D,GAC9B7W,IAAIgb,EAAmB,CAAA,EACvB,IAAM3X,EAAOwT,EAAIxT,KACboT,EAAMxK,IAAI5I,CAAI,GAChB2X,EAAmB,CAAA,EACnBvE,EAAMxJ,OAAO5J,CAAI,GACRqT,EAAYzK,IAAI5I,CAAI,GACHwT,EACJ2D,YAAa,GAAI,IACrC9D,EAAYzJ,OAAO5J,CAAI,EACvB2X,EAAmB,CAAA,GAInBA,IACF3N,MAAMpH,QAAQqH,IACXuJ,EAAwBvL,UACtBsD,aAAc,EACdpB,IAAIiB,GAAYA,EAASxB,OAAM,CAAE,CAAC,EAEtC4J,EAAwBkC,UAAY,CAAA,EAEzC,CAUgB,SAAAoB,EACdc,EACAzG,EACAG,SAIIJ,EAAU,OAAA5O,EAAAiP,GAAoBqG,IAAqBtV,EAAAsV,EACnDtG,IACFJ,GAAW,IAAII,GAEjB,IAGQuG,EAHFC,EAAkB5G,EAAQlP,MAAM,OAAO,EACvC+V,EAAkB5G,EAAQnP,MAAM,OAAO,EACzC8V,GAAmBC,GACfF,EAAU,gCACiB3G,oBAA0BC,OAEvD2G,GACFD,EAAQjZ,sBACWsS,oDAA0D,EAG3E4G,GAAmBC,GACrBF,EAAQjZ,KAAK,KAAK,EAEhBmZ,GACFF,EAAQjZ,sBACWuS,oDAA0D,EAG/EE,EAAOtF,KAAK8L,EAAQhZ,KAAK,GAAG,CAAC,GAG/B6U,EACE,IAAIxM,EACCgK,EAAH,WACA,KAAO,CAAEA,QAAAA,EAASC,QAAAA,CAAS,GAAC,SAE7B,CAAA,CAEL,CASgB,SAAA6G,GACdC,EACA5O,GAEA,GAAoB,OAAhB4O,GAA+C,YAAvB,OAAOA,EACjC,MAAMhE,EAAcrQ,OAAM,wBAE5BsU,IX9OWjP,EAHXgP,EWiPkBA,EXhPlB5O,EWgP+BA,EX9O/B,IAAWJ,KAAYd,EAAW,CAChCxL,IAAIwb,EAAkC,KAClC9O,GAAWA,EAAQ+O,QACrBD,EAAiB1M,GAAkBpC,EAAQ+O,QAG3CnP,EAASmE,eADS,OAAhB6K,EACwB,KAEA,CACxBhP,EACAmP,KACG5L,KAEH,IAAMnJ,EAAUmJ,EACbrC,IAAIkO,IACH,GAAW,MAAPA,EACF,OAAO,KACF,GAAmB,UAAf,OAAOA,EAChB,OAAOA,EACF,GAAmB,UAAf,OAAOA,GAAmC,WAAf,OAAOA,EAC3C,OAAOA,EAAIC,WACN,GAAID,aAAepa,MACxB,OAAOoa,EAAIhV,QAEX,IACE,OAAOzB,KAAK2W,UAAUF,CAAG,CAG1B,CAFC,MAAOG,GACP,OAAO,IACR,CAEL,CAAC,EACAtO,OAAOmO,GAAOA,CAAG,EACjBxZ,KAAK,GAAG,EACPuZ,IAAUD,MAAAA,EAAAA,EAAkBlP,EAASwD,WACvCwL,EAAY,CACVG,MAAO5M,EAAS4M,GAAOK,YAA+B,EACtDpV,QAAAA,EACAmJ,KAAAA,EACApF,KAAM6B,EAASjJ,IAChB,CAAA,CAEL,CAEH,CWoMH,CAWM,SAAUkN,GAAYT,GXpQtB,IAAsB2L,EAAAA,EWqQV3L,EXpQhBtE,EAAUuQ,QAAQC,IAChBA,EAAKzL,YAAYkL,CAAK,CACxB,CAAC,CWmQH,CCncA,IAAMQ,GAAU,8BACVC,GAAa,EACbC,EAAa,2BASfC,GAAiD,KACrD,SAASC,KA2BP,OA1BKD,GAAAA,KX3BP,CAAgB/Y,EAAMmR,EAAS,CAAE8H,QAAAA,EAASC,QAAAA,EAASC,SAAAA,EAAUC,WAAAA,CAAY,KACrE,IAAMtJ,EAAUuJ,UAAUC,KAAKtZ,EAAMmR,CAAO,EAC5C,IAAMoI,EAAchL,EAAKuB,CAAO,EAoBhC,OAnBIoJ,GACApJ,EAAQJ,iBAAiB,gBAAiB,IACtCwJ,EAAQ3K,EAAKuB,EAAQI,MAAM,EAAGsJ,EAAMC,WAAYD,EAAME,WAAYnL,EAAKuB,EAAQnB,WAAW,EAAG6K,CAAK,CAC9G,CAAS,EAEDP,GACAnJ,EAAQJ,iBAAiB,UAAW,GAAWuJ,EAE/CO,EAAMC,WAAYD,EAAME,WAAYF,CAAK,CAAC,EAE9CD,EACKrT,KAAK,IACFkT,GACAO,EAAGjK,iBAAiB,QAAS,IAAM0J,EAAY,CAAA,EAC/CD,GACAQ,EAAGjK,iBAAiB,gBAAiB,GAAWyJ,EAASK,EAAMC,WAAYD,EAAME,WAAYF,CAAK,CAAC,CAE/G,CAAK,EACIxW,MAAM,MAAS,EACbuW,CACX,GWK8BX,GAASC,GAAY,CAC7CK,QAAS,CAACS,EAAIF,KAMZ,GACO,IADCA,EAEJ,IACEE,EAAGC,kBAAkBd,CAAU,CAMhC,CALC,MAAOxY,GAIPC,QAAQwL,KAAKzL,CAAC,CACf,CAEN,CACF,CAAA,EAAE0C,MAAM1C,IACP,MAAM2T,EAAcrQ,OAA0B,WAAA,CAC5CiW,qBAAsBvZ,EAAE+C,OACzB,CAAA,CACH,CAAC,CAGL,CAyBOqN,eAAeoJ,GACpBtG,EACAuG,GAEA,IACE,IACM5K,GADKnF,MAAMgP,MACHrK,YAAYmK,EAAY,WAAW,EAEjD9O,MADoBmF,EAAGb,YAAYwK,CAAU,EAC3BkB,IAAID,EAAiBE,GAAWzG,CAAG,CAAC,EACtDxJ,MAAMmF,EAAGG,IAUV,CATC,MAAOhP,GACHA,aAAa6C,EACfkO,EAAOtF,KAAKzL,EAAE+C,OAAO,GAEf6W,EAAcjG,EAAcrQ,OAA2B,UAAA,CAC3DiW,qBAAuBvZ,MAAAA,EAAA,KAAA,EAAAA,EAAa+C,OACrC,CAAA,EACDgO,EAAOtF,KAAKmO,EAAY7W,OAAO,EAElC,CACH,CAEA,SAAS4W,GAAWzG,GAClB,OAAUA,EAAIxT,KAAP,IAAewT,EAAInK,QAAQ8Q,KACpC,OCzEaC,GAyBXra,YAA6BkI,GAAAzK,KAASyK,UAATA,EAT7BzK,KAAgB6c,iBAAiC,KAU/C,IAAM7G,EAAMhW,KAAKyK,UAAUoD,YAAY,KAAK,EAAEjC,eAC9C5L,KAAK8c,SAAW,IAAIC,GAAqB/G,CAAG,EAC5ChW,KAAKgd,wBAA0Bhd,KAAK8c,SAASG,KAAM,EAACvU,KAAKgK,GACvD1S,KAAK6c,iBAAmBnK,CAEzB,CACF,CASD6D,iCACE,IACE,IAiCU2G,EA3BJC,EANiBnd,KAAKyK,UACzBoD,YAAY,iBAAiB,EAC7BjC,eAI0B6H,wBAC7B,IAAM2J,EAAOC,KACb,GAAyC,OAArC,OAAAvY,EAAA9E,KAAK6c,kBAAkB,KAAA,EAAA/X,EAAAwY,cACzBtd,KAAK6c,iBAAmBrQ,MAAMxM,KAAKgd,wBAEM,OAArC,OAAAO,EAAAvd,KAAK6c,kBAAkB,KAAA,EAAAU,EAAAD,aAM7B,GACEtd,KAAK6c,iBAAiBW,wBAA0BJ,GAChDpd,CAAAA,KAAK6c,iBAAiBS,WAAWrN,KAC/BwN,GAAuBA,EAAoBL,OAASA,CAAI,EAoB5D,OAdEpd,KAAK6c,iBAAiBS,WAAWlc,KAAK,CAAEgc,KAAAA,EAAMD,MAAAA,CAAK,CAAE,EAvEpB,GA4E/Bnd,KAAK6c,iBAAiBS,WAAWhe,SAE3B4d,GAuMdI,IAEA,GAA0B,IAAtBA,EAAWhe,OACb,MAAO,CAAC,EAGVH,IAAI+d,EAAuB,EACvBQ,EAAwBJ,EAAW,GAAGF,KAE1C,IAAKje,IAAIE,EAAI,EAAGA,EAAIie,EAAWhe,OAAQD,CAAC,GAClCie,EAAWje,GAAG+d,KAAOM,IACvBA,EAAwBJ,EAAWje,GAAG+d,KACtCF,EAAuB7d,GAI3B,OAAO6d,CACT,GAvNYld,KAAK6c,iBAAiBS,UAAU,EAElCtd,KAAK6c,iBAAiBS,WAAWK,OAAOT,EAAsB,CAAC,GAI5Dld,KAAK8c,SAASc,UAAU5d,KAAK6c,gBAAgB,CAGrD,CAFC,MAAO/Z,GACP+Q,EAAOtF,KAAKzL,CAAC,CACd,CACF,CASD+a,kCAYUT,EAEEU,EAAkBC,EAGpBC,EAhBR,IAKE,OAJ8B,OAA1Bhe,KAAK6c,kBACPrQ,MAAMxM,KAAKgd,wBAI0B,OAArC,SAAAhd,KAAK6c,kBAAkB,KAAA,EAAA/X,EAAAwY,aACqB,IAA5Ctd,KAAK6c,iBAAiBS,WAAWhe,QAE1B,IAEH8d,EAAOC,KAEP,CAAES,iBAAAA,EAAkBC,cAAAA,CAAe,GAkC/B,CACdE,EACAC,EArJuB,QA4JvB,IAAMJ,EAA4C,GAE9CC,EAAgBE,EAAgBE,QACpC,IAAK,IAAMV,KAAuBQ,EAAiB,CAEjD,IAAMG,EAAiBN,EAAiBO,KACtCC,GAAMA,EAAGnB,QAAUM,EAAoBN,KAAK,EAE9C,GAAKiB,GAgBH,GAHAA,EAAeG,MAAMnd,KAAKqc,EAAoBL,IAAI,EAG9CoB,GAAWV,CAAgB,EAAII,EAAS,CAC1CE,EAAeG,MAAME,MACrB,KACD,CAAA,MAbD,GAJAX,EAAiB1c,KAAK,CACpB+b,MAAOM,EAAoBN,MAC3BoB,MAAO,CAACd,EAAoBL,KAC7B,CAAA,EACGoB,GAAWV,CAAgB,EAAII,EAAS,CAG1CJ,EAAiBW,IAAG,EACpB,KACD,CAYHV,EAAgBA,EAAcI,MAAM,CAAC,CACtC,CACD,MAAO,CACLL,iBAAAA,EACAC,cAAAA,EAEJ,GA/EQ/d,KAAK6c,iBAAiBS,UAAU,EAE5BU,EAAerb,EACnByB,KAAK2W,UAAU,CAAEpH,QAAS,EAAG2J,WAAYQ,CAAkB,CAAA,CAAC,EAG9D9d,KAAK6c,iBAAiBW,sBAAwBJ,EACnB,EAAvBW,EAAcze,QAEhBU,KAAK6c,iBAAiBS,WAAaS,EAInCvR,MAAMxM,KAAK8c,SAASc,UAAU5d,KAAK6c,gBAAgB,IAEnD7c,KAAK6c,iBAAiBS,WAAa,GAE9Btd,KAAK8c,SAASc,UAAU5d,KAAK6c,gBAAgB,GAE7CmB,EAIR,CAHC,MAAOlb,GAEP,OADA+Q,EAAOtF,KAAKzL,CAAC,EACN,EACR,CACF,CACF,CAED,SAASua,KAGP,OAFc,IAAIha,MAEL8L,YAAa,EAACuP,UAAU,EAAG,EAAE,CAC5C,OAkDa3B,GAEXxa,YAAmByT,GAAAhW,KAAGgW,IAAHA,EACjBhW,KAAK2e,wBAA0B3e,KAAK4e,8BACrC,CACDA,qCACE,MAAKC,CAAAA,OrB1DP,IACE,MAA4B,UAArB,OAAOhD,SAGf,CAFC,MAAO/Y,IAGX,GqBqD6B,GrB3CpB,IAAIsC,QAAQ,CAACF,EAASD,KAC3B,IACE9F,IAAI2f,EAAoB,CAAA,EAClBC,EACJ,0DACIzM,EAAU3O,KAAKkY,UAAUC,KAAKiD,CAAa,EACjDzM,EAAQ0M,UAAY,KAClB1M,EAAQI,OAAO5J,QAEVgW,GACHnb,KAAKkY,UAAUoD,eAAeF,CAAa,EAE7C7Z,EAAQ,CAAA,CAAI,CACd,EACAoN,EAAQ4M,gBAAkB,KACxBJ,EAAW,CAAA,CACb,EAEAxM,EAAQ6M,QAAU,WAChBla,GAAO,OAAAH,EAAAwN,EAAQtP,OAAK,KAAA,EAAA8B,EAAEe,UAAW,EAAE,CACrC,CAGD,CAFC,MAAO7C,GACPiC,EAAOjC,CAAK,CACb,CACH,CAAC,EqBuBM0F,KAAK,IAAM,CAAA,CAAI,EACflD,MAAM,IAAM,CAAA,CAAK,CAEvB,CAIDyX,aACE,IAIQmC,EAHR,OADwB5S,MAAMxM,KAAK2e,yBAK7BS,OADEA,EAAqB5S,MD5L1B0G,MACL8C,IAEA,IACE,IACMrE,GADKnF,MAAMgP,MACHrK,YAAYmK,CAAU,EAC9B5I,EAASlG,MAAMmF,EAAGb,YAAYwK,CAAU,EAAEtQ,IAAIyR,GAAWzG,CAAG,CAAC,EAInE,OADAxJ,MAAMmF,EAAGG,KACFY,CAUR,CATC,MAAO5P,GACHA,aAAa6C,EACfkO,EAAOtF,KAAKzL,EAAE+C,OAAO,GAEf6W,EAAcjG,EAAcrQ,OAAyB,UAAA,CACzDiW,qBAAuBvZ,MAAAA,EAAA,KAAA,EAAAA,EAAa+C,OACrC,CAAA,EACDgO,EAAOtF,KAAKmO,EAAY7W,OAAO,EAElC,CACH,GCuKmE7F,KAAKgW,GAAG,IACjEoJ,EAAoB9B,WACf8B,EAJF,CAAE9B,WAAY,GASxB,CAEDM,gBAAgByB,SAKNC,EAHR,GADwB9S,MAAMxM,KAAK2e,wBAKjC,OADMW,EAA2B9S,MAAMxM,KAAKid,OACrCX,GAA2Btc,KAAKgW,IAAK,CAC1CwH,sBACE,OAAA1Y,EAAAua,EAAiB7B,uBACjB1Y,EAAAwa,EAAyB9B,sBAC3BF,WAAY+B,EAAiB/B,UAC9B,CAAA,CAEJ,CAEDjQ,UAAUgS,SAKAC,EAHR,GADwB9S,MAAMxM,KAAK2e,wBAKjC,OADMW,EAA2B9S,MAAMxM,KAAKid,OACrCX,GAA2Btc,KAAKgW,IAAK,CAC1CwH,sBACE,OAAA1Y,EAAAua,EAAiB7B,uBACjB1Y,EAAAwa,EAAyB9B,sBAC3BF,WAAY,CACV,GAAGgC,EAAyBhC,WAC5B,GAAG+B,EAAiB/B,WAEvB,CAAA,CAEJ,CACF,CAOK,SAAUkB,GAAWP,GAEzB,OAAOtb,EAELyB,KAAK2W,UAAU,CAAEpH,QAAS,EAAG2J,WAAYW,CAAe,CAAE,CAAC,EAC3D3e,MACJ,CR5RuCwU,ESMhB,GTLrBoC,EACE,IAAIxM,EACF,kBACAe,GAAa,IAAI+I,GAA0B/I,CAAS,EAAC,SAAA,CAEtD,EAEHyL,EACE,IAAIxM,EACF,YACAe,GAAa,IAAImS,GAAqBnS,CAAS,EAAC,SAAA,CAEjD,EAIH6O,EAAgB9W,EAAMmR,EAASG,CAAO,EAEtCwF,EAAgB9W,EAAMmR,EAAS,SAAkB,EAEjD2F,EAAgB,UAAW,EAAE,iJE0If,WACdxD,EAAYyJ,MAAK,CACnB,uEAhBM,SACJvY,GAEA,OAAIA,MAAAA,GAG2CzD,KAAAA,IAAvCyD,EAA0B6S,QACpC,8CArCM,SACJ7D,EACAxT,EACAmJ,EAA6BpB,GAE7B8L,GAAaL,EAAKxT,CAAI,EAAE2J,cAAcR,CAAkB,CAC1D,mCI+LgB,SAAOnJ,EAAe+H,GACpC,IAAMyL,EAAMJ,EAAM5K,IAAIxI,CAAI,EAC1B,GAAI,CAACwT,GAAOxT,IAAS+H,GAAsB1F,EAAmB,EAC5D,OAAOkV,EAAa,EAEtB,GAAK/D,EAIL,OAAOA,EAHL,MAAMS,EAAcrQ,OAAwB,SAAA,CAAE+R,QAAS3V,CAAI,CAAE,CAIjE,UAMgB,WACd,OAAOjC,MAAM+L,KAAKsJ,EAAMrJ,OAAQ,CAAA,CAClC,sCAzHgB,SACdmL,EACA8H,GAEA,InBtJyB,aAAlB,OAAO5b,QAA0B6B,MmBsJrB,CAACA,IAElB,MAAMgR,EAAcrQ,OAAM,kCAG4B7C,KAAAA,IAApDic,EAAiBzH,iCACnByH,EAAiBzH,+BAAiC,CAAA,GAGpD5Y,IAAIsgB,EAEFA,EADEjJ,GAAekB,CAAQ,EACZA,EAAS7L,QAET6L,EAIf,IAAMgI,EACDtc,OAAAuU,OAAAvU,OAAAuU,OAAA,GAAA6H,CAAgB,EAChBC,CAAU,EAgBf,GAX+Blc,KAAAA,IAA3Bmc,EAAQrG,gBACV,OAAOqG,EAAQrG,eAUuB9V,KAAAA,IAApCic,EAAiBnG,gBACiB,aAAhC,OAAOJ,qBACT,MAAMxC,EAAcrQ,OAElB,sCAAA,EAAE,EAKR,IAAMuZ,EAAa,GAfV,CAAC,GAeuBvb,KAAK2W,UAAU2E,CAAO,GAfvCE,OACZ,CAACC,EAAMtgB,IAAOugB,KAAKC,KAAK,GAAIF,CAAI,EAAItgB,EAAEC,WAAW,CAAC,EAAK,EACvD,CAAC,EAcCya,EAAcpE,EAAY7K,IAAI2U,CAAU,EAC9C,GAAI1F,EACDA,EAAsCb,YACrCoG,EAAiBnG,cAAc,MAFnC,CAOA,IACW3O,EADLD,EAAY,IAAIgD,GAAmBkS,CAAU,EACnD,IAAWjV,KAAaoL,EAAYvJ,SAClC9B,EAAUkD,aAAajD,CAAS,EAG5BwP,EAAS,IAAIxB,GACjB+G,EACAD,EACAG,EACAlV,CAAS,EAGXoL,EAAYvK,IAAIqU,EAAYzF,CAAM,CAdjC,CAgBD,OAAOA,CACT,oEIhPa1C,GAGXjV,YACWyd,EACQrK,GADR3V,KAASggB,UAATA,EACQhgB,KAAQ2V,SAARA,EAGjBI,EACEiK,EACA,IAAItW,EAAU,aAAc,IAAM1J,KAAI,QAAA,CAAuB,EAG/DA,KAAKyK,UAAYuV,EAAUvV,SAC5B,CAEDsN,qCACE,OAAO/X,KAAKggB,UAAUjI,8BACvB,CAEDA,mCAAmCvI,GACjCxP,KAAKggB,UAAUjI,+BAAiCvI,CACjD,CAEDhN,WACE,OAAOxC,KAAKggB,UAAUxd,IACvB,CAEDqJ,cACE,OAAO7L,KAAKggB,UAAUnU,OACvB,CAEDO,SACE,OAAO,IAAIhH,QAAcF,IACvBlF,KAAKggB,UAAU/H,iBACf/S,GACF,CAAC,EAAEwD,KAAK,KACN1I,KAAK2V,SAAS/I,SAASqT,UAAUjgB,KAAKwC,IAAI,EACnCoX,EAAU5Z,KAAKggB,SAAS,EAChC,CACF,CAgBDE,YACE1d,EACAmJ,EAA6BwU,GAE7BngB,KAAKggB,UAAU/H,uBAGTrK,EAAW5N,KAAKggB,UAAUvV,UAAUoD,YAAYrL,CAAY,EASlE,OAPGoL,EAASrC,cAAe,GACgD,cAAzE,OAAAzG,EAAA8I,EAAS7B,aAAY,GAAI,KAAA,EAAAjH,EAAAiF,oBAEzB6D,EAASZ,WAAU,EAIdY,EAAShC,aAAa,CAC3BX,WAAYU,CACb,CAAA,CACF,CAYDyU,uBACE5d,EACAmJ,EAA6BwU,GAE7BngB,KAAKggB,UAAUvV,UAEZoD,YAAYrL,CAAW,EACvB2J,cAAcR,CAAkB,CACpC,CAMDoK,cAAcrL,GACZqL,EAAc/V,KAAKggB,UAAWtV,CAAS,CACxC,CAEDuL,yBAAyBvL,GACvBuL,GAAyBjW,KAAKggB,UAAWtV,CAAS,CACnD,CAED8O,SACE,MAAO,CACLhX,KAAMxC,KAAKwC,KACXuV,+BAAgC/X,KAAK+X,+BACrClM,QAAS7L,KAAK6L,QAEjB,CACF,CClJM,IAAM4K,GAAgB,IAAItQ,EAC/B,aACA,WAbiC,CACjCuQ,SACE,oFAEFM,uBACE,6EASI,ECCF,SAAUqJ,GACdC,GAEA,IAAMC,EAAwC,GAKxCC,EAAgC,CAIpCC,WAAY,CAAA,EACZ1G,cA8DF,SACElO,EACAmO,EAAY,IAEZ,IAAMhE,EAAM0K,EACV7U,EACAmO,CAAS,EAGX,GAAIjT,EAASwZ,EAAMvK,EAAIxT,IAAI,EACzB,OAAO+d,EAAKvK,EAAIxT,MAGlB,IAAMme,EAAY,IAAIL,EAAgBtK,EAAKwK,CAAS,EAEpD,OADAD,EAAKvK,EAAIxT,MAAQme,CAElB,EA5EC3K,IAAAA,EACAsD,gBAAiBsH,EACjBlR,YAAamR,GACbrG,MAAOsG,GAEPP,KAAM,KACNzG,YAAaiH,GACbnU,SAAU,CACRoU,kBA8EJ,SACEtW,GAEA,IAAM0L,EAAgB1L,EAAUlI,KAC1Bye,EAA6B7K,EAAcxT,QAAQ,UAAW,EAAE,EACtE,CAAA,IAMQse,EALNC,EAA+BzW,CAAS,GACD,WAAvCA,EAAUd,OAIJsX,EAAmB,CACvBE,EAAsBpL,OAGtB,GAA2D,YAAvD,OAAQoL,EAAeH,GAGzB,MAAMxK,GAAcrQ,OAAsC,uBAAA,CACxD+R,QAAS/B,CACV,CAAA,EAKH,OAAQgL,EAAeH,IACzB,EAG+B1d,KAAAA,IAA3BmH,EAAUZ,cACZ7G,EAAWie,EAAkBxW,EAAUZ,YAAY,EAIpD0W,EAAkBS,GAA8BC,EAIhDZ,EAAgBra,UAAkBgb,GAIjC,YAAajS,GAEX,OADmBhP,KAAKkgB,YAAY9X,KAAKpI,KAAMoW,CAAa,EAC1C5E,MAChBxR,KACA0K,EAAUb,kBAAoBmF,EAAO,EAAE,CAE3C,EACH,CAED,MAA8C,WAAvCtE,EAAUd,KAEZ4W,EAAkBS,GACnB,IACL,EApIGhB,UA4BJ,SAAmBzd,GACjB,OAAO+d,EAAK/d,EACb,EA7BG6e,aAuIJ,SAAsBrL,EAAkBxT,GACtC,GAAa,eAATA,EACF,OAAO,KAGT,IAAM8e,EAAa9e,EAEnB,OAAO8e,CACR,EA9IGC,YAAAA,EACD,GAgCH,SAASvL,EAAIxT,GAEX,GADAA,EAAOA,GAAQgf,EACVza,EAASwZ,EAAM/d,CAAI,EAGxB,OAAO+d,EAAK/d,GAFV,MAAMiU,GAAcrQ,OAAwB,SAAA,CAAE+R,QAAS3V,CAAI,CAAE,CAGhE,CAyGD,OAjICge,EAA2B,QAAIA,EAGhCpd,OAAOqe,eAAejB,EAAW,OAAQ,CACvCxV,IAmDF,WAEE,OAAO5H,OAAOmE,KAAKgZ,CAAI,EAAE5T,IAAInK,GAAQ+d,EAAK/d,EAAK,CAChD,CArDA,CAAA,EAsBDwT,EAAS,IAAIsK,EAsGNE,CACT,CC9JO,IAAM7K,GAvBG,SAAA+L,IACd,IAAMlB,EAAYH,GAA4B7I,EAAe,EAmB7D,OAlBAgJ,EAAU5T,SAAQxJ,OAAAuU,OAAAvU,OAAAuU,OAAA,GACb6I,EAAU5T,QAAQ,GACrB8U,wBAAAA,EACAC,gBAWF,SAAyBtX,GACvBpH,EAAWud,EAAWnW,CAAK,CAC5B,EAZCvC,gBAAAA,EACA3B,aAAAA,EACAlD,WAAAA,IAYKud,CACT,EAE+C,ECjClC3M,GAAS,IAAIzE,GAAO,sBAAsB,ECUvD,IACE,IAUQwS,GAVFC,EAAUne,IAGkBH,KAAAA,IAA7Bse,EAAgBlM,WACnB9B,GAAOtF;;;KAGN,EAGKqT,GAAeC,EAAgBlM,SAClCmE,cAC6C,GAA9B8H,GAAWE,QAAQ,MAAM,GACzCjO,GAAOtF;;;SAGJ,CAKT,CAFE,MAAAzJ,IAII6Q,EAAWoM,GC9BfzI,kCDgCF0I,KAAAA,CChCwC,SCHxCrM,EAAS2D,oCAA+B,gBAAgB"}