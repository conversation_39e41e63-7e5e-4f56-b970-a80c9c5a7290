var dx=Object.create;var Pa=Object.defineProperty;var px=Object.getOwnPropertyDescriptor;var mx=Object.getOwnPropertyNames;var hx=Object.getPrototypeOf,gx=Object.prototype.hasOwnProperty;var s=(t,e)=>Pa(t,"name",{value:e,configurable:!0});var Ma=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),Or=(t,e)=>{for(var r in e)Pa(t,r,{get:e[r],enumerable:!0})},Xl=(t,e,r,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of mx(e))!gx.call(t,i)&&i!==r&&Pa(t,i,{get:()=>e[i],enumerable:!(n=px(e,i))||n.enumerable});return t},B=(t,e,r)=>(Xl(t,e,"default"),r&&Xl(r,e,"default")),Lf=(t,e,r)=>(r=t!=null?dx(hx(t)):{},Xl(e||!t||!t.__esModule?Pa(r,"default",{value:t,enumerable:!0}):r,t));var Xc=Ma(qc=>{"use strict";Object.defineProperty(qc,"__esModule",{value:!0});var Vc;function zc(){if(Vc===void 0)throw new Error("No runtime abstraction layer installed");return Vc}s(zc,"RAL");(function(t){function e(r){if(r===void 0)throw new Error("No runtime abstraction layer provided");Vc=r}s(e,"install"),t.install=e})(zc||(zc={}));qc.default=zc});var vy=Ma(Be=>{"use strict";Object.defineProperty(Be,"__esModule",{value:!0});Be.stringArray=Be.array=Be.func=Be.error=Be.number=Be.string=Be.boolean=void 0;function ON(t){return t===!0||t===!1}s(ON,"boolean");Be.boolean=ON;function Ry(t){return typeof t=="string"||t instanceof String}s(Ry,"string");Be.string=Ry;function $N(t){return typeof t=="number"||t instanceof Number}s($N,"number");Be.number=$N;function LN(t){return t instanceof Error}s(LN,"error");Be.error=LN;function PN(t){return typeof t=="function"}s(PN,"func");Be.func=PN;function Ey(t){return Array.isArray(t)}s(Ey,"array");Be.array=Ey;function MN(t){return Ey(t)&&t.every(e=>Ry(e))}s(MN,"stringArray");Be.stringArray=MN});var Jc=Ma(Yi=>{"use strict";Object.defineProperty(Yi,"__esModule",{value:!0});Yi.Emitter=Yi.Event=void 0;var DN=Xc(),Iy;(function(t){let e={dispose(){}};t.None=function(){return e}})(Iy||(Yi.Event=Iy={}));var Yc=class{static{s(this,"CallbackList")}add(e,r=null,n){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(e),this._contexts.push(r),Array.isArray(n)&&n.push({dispose:s(()=>this.remove(e,r),"dispose")})}remove(e,r=null){if(!this._callbacks)return;let n=!1;for(let i=0,a=this._callbacks.length;i<a;i++)if(this._callbacks[i]===e)if(this._contexts[i]===r){this._callbacks.splice(i,1),this._contexts.splice(i,1);return}else n=!0;if(n)throw new Error("When adding a listener with a context, you should remove it with the same context")}invoke(...e){if(!this._callbacks)return[];let r=[],n=this._callbacks.slice(0),i=this._contexts.slice(0);for(let a=0,o=n.length;a<o;a++)try{r.push(n[a].apply(i[a],e))}catch(l){(0,DN.default)().console.error(l)}return r}isEmpty(){return!this._callbacks||this._callbacks.length===0}dispose(){this._callbacks=void 0,this._contexts=void 0}},Rl=class t{static{s(this,"Emitter")}constructor(e){this._options=e}get event(){return this._event||(this._event=(e,r,n)=>{this._callbacks||(this._callbacks=new Yc),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(e,r);let i={dispose:s(()=>{this._callbacks&&(this._callbacks.remove(e,r),i.dispose=t._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))},"dispose")};return Array.isArray(n)&&n.push(i),i}),this._event}fire(e){this._callbacks&&this._callbacks.invoke.call(this._callbacks,e)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}};Yi.Emitter=Rl;Rl._noop=function(){}});var Sy=Ma(Ji=>{"use strict";Object.defineProperty(Ji,"__esModule",{value:!0});Ji.CancellationTokenSource=Ji.CancellationToken=void 0;var FN=Xc(),GN=vy(),Zc=Jc(),El;(function(t){t.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:Zc.Event.None}),t.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:Zc.Event.None});function e(r){let n=r;return n&&(n===t.None||n===t.Cancelled||GN.boolean(n.isCancellationRequested)&&!!n.onCancellationRequested)}s(e,"is"),t.is=e})(El||(Ji.CancellationToken=El={}));var UN=Object.freeze(function(t,e){let r=(0,FN.default)().timer.setTimeout(t.bind(e),0);return{dispose(){r.dispose()}}}),vl=class{static{s(this,"MutableToken")}constructor(){this._isCancelled=!1}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?UN:(this._emitter||(this._emitter=new Zc.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}},Qc=class{static{s(this,"CancellationTokenSource")}get token(){return this._token||(this._token=new vl),this._token}cancel(){this._token?this._token.cancel():this._token=El.Cancelled}dispose(){this._token?this._token instanceof vl&&this._token.dispose():this._token=El.None}};Ji.CancellationTokenSource=Qc});var Ae={};Or(Ae,{AbstractAstReflection:()=>$r,AbstractCstNode:()=>Js,AbstractLangiumParser:()=>Zs,AbstractParserErrorMessageProvider:()=>Tl,AbstractThreadedAsyncParser:()=>df,AstUtils:()=>qa,BiMap:()=>nn,Cancellation:()=>w,CompositeCstNodeImpl:()=>en,ContextCache:()=>sn,CstNodeBuilder:()=>Ys,CstUtils:()=>Ga,DEFAULT_TOKENIZE_OPTIONS:()=>_l,DONE_RESULT:()=>Me,DatatypeSymbol:()=>xl,DefaultAstNodeDescriptionProvider:()=>ha,DefaultAstNodeLocator:()=>ya,DefaultAsyncParser:()=>Ca,DefaultCommentProvider:()=>ka,DefaultConfigurationProvider:()=>xa,DefaultDocumentBuilder:()=>Ta,DefaultDocumentValidator:()=>ma,DefaultHydrator:()=>_a,DefaultIndexManager:()=>Aa,DefaultJsonSerializer:()=>fa,DefaultLangiumDocumentFactory:()=>ra,DefaultLangiumDocuments:()=>na,DefaultLexer:()=>on,DefaultLexerErrorMessageProvider:()=>Ea,DefaultLinker:()=>ia,DefaultNameProvider:()=>sa,DefaultReferenceDescriptionProvider:()=>ga,DefaultReferences:()=>aa,DefaultScopeComputation:()=>oa,DefaultScopeProvider:()=>ca,DefaultServiceRegistry:()=>da,DefaultTokenBuilder:()=>sr,DefaultValueConverter:()=>rn,DefaultWorkspaceLock:()=>Na,DefaultWorkspaceManager:()=>Ra,Deferred:()=>Xe,Disposable:()=>_r,DisposableCache:()=>ts,DocumentCache:()=>Cl,DocumentState:()=>J,DocumentValidator:()=>pt,EMPTY_SCOPE:()=>jN,EMPTY_STREAM:()=>ss,EmptyFileSystem:()=>Tf,EmptyFileSystemProvider:()=>Ml,ErrorWithLocation:()=>Dr,GrammarAST:()=>ms,GrammarUtils:()=>Qa,IndentationAwareLexer:()=>xf,IndentationAwareTokenBuilder:()=>Pl,JSDocDocumentationProvider:()=>Sa,LangiumCompletionParser:()=>ea,LangiumParser:()=>Qs,LangiumParserErrorMessageProvider:()=>Xi,LeafCstNodeImpl:()=>Qr,LexingMode:()=>ln,MapScope:()=>la,Module:()=>gf,MultiMap:()=>At,OperationCancelled:()=>Ut,ParserWorker:()=>pf,Reduction:()=>fn,RegExpUtils:()=>Ja,RootCstNodeImpl:()=>qi,SimpleCache:()=>ua,StreamImpl:()=>it,StreamScope:()=>es,TextDocument:()=>Zi,TreeStreamImpl:()=>Et,URI:()=>Ye,UriUtils:()=>Je,ValidationCategory:()=>ns,ValidationRegistry:()=>pa,ValueConverter:()=>Gt,WorkspaceCache:()=>rs,assertUnreachable:()=>It,createCompletionParser:()=>Kc,createDefaultCoreModule:()=>mf,createDefaultSharedCoreModule:()=>hf,createGrammarConfig:()=>Uu,createLangiumParser:()=>Hc,createParser:()=>ta,delayNextTick:()=>ef,diagnosticData:()=>an,eagerLoad:()=>Hy,getDiagnosticRange:()=>Ly,indentationBuilderDefaultOptions:()=>yf,inject:()=>Ll,interruptAndCheck:()=>Te,isAstNode:()=>fe,isAstNodeDescription:()=>Yl,isAstNodeWithComment:()=>nf,isCompositeCstNode:()=>ht,isIMultiModeLexerDefinition:()=>af,isJSDoc:()=>cf,isLeafCstNode:()=>ur,isLinkingError:()=>Lr,isNamed:()=>Oy,isOperationCancelled:()=>Bt,isReference:()=>be,isRootCstNode:()=>is,isTokenTypeArray:()=>wl,isTokenTypeDictionary:()=>sf,loadGrammarFromJson:()=>ar,parseJSDoc:()=>uf,prepareLangiumParser:()=>Ay,setInterruptionPeriod:()=>Cy,startCancelableOperation:()=>Sl,stream:()=>H,toDiagnosticData:()=>Py,toDiagnosticSeverity:()=>Nl});var Ga={};Or(Ga,{DefaultNameRegexp:()=>Fa,RangeComparison:()=>vt,compareRange:()=>Mf,findCommentNode:()=>eu,findDeclarationNodeAtOffset:()=>Tx,findLeafNodeAtOffset:()=>tu,findLeafNodeBeforeOffset:()=>Df,flattenCst:()=>xx,getInteriorNodes:()=>Ex,getNextNode:()=>Ax,getPreviousNode:()=>Gf,getStartlineNode:()=>Rx,inRange:()=>Ql,isChildNode:()=>Zl,isCommentNode:()=>Jl,streamCst:()=>Pr,toDocumentSegment:()=>Mr,tokenToRange:()=>dn});function fe(t){return typeof t=="object"&&t!==null&&typeof t.$type=="string"}s(fe,"isAstNode");function be(t){return typeof t=="object"&&t!==null&&typeof t.$refText=="string"}s(be,"isReference");function Yl(t){return typeof t=="object"&&t!==null&&typeof t.name=="string"&&typeof t.type=="string"&&typeof t.path=="string"}s(Yl,"isAstNodeDescription");function Lr(t){return typeof t=="object"&&t!==null&&fe(t.container)&&be(t.reference)&&typeof t.message=="string"}s(Lr,"isLinkingError");var $r=class{static{s(this,"AbstractAstReflection")}constructor(){this.subtypes={},this.allSubtypes={}}isInstance(e,r){return fe(e)&&this.isSubtype(e.$type,r)}isSubtype(e,r){if(e===r)return!0;let n=this.subtypes[e];n||(n=this.subtypes[e]={});let i=n[r];if(i!==void 0)return i;{let a=this.computeIsSubtype(e,r);return n[r]=a,a}}getAllSubTypes(e){let r=this.allSubtypes[e];if(r)return r;{let n=this.getAllTypes(),i=[];for(let a of n)this.isSubtype(a,e)&&i.push(a);return this.allSubtypes[e]=i,i}}};function ht(t){return typeof t=="object"&&t!==null&&Array.isArray(t.content)}s(ht,"isCompositeCstNode");function ur(t){return typeof t=="object"&&t!==null&&typeof t.tokenType=="object"}s(ur,"isLeafCstNode");function is(t){return ht(t)&&typeof t.fullText=="string"}s(is,"isRootCstNode");var it=class t{static{s(this,"StreamImpl")}constructor(e,r){this.startFn=e,this.nextFn=r}iterator(){let e={state:this.startFn(),next:s(()=>this.nextFn(e.state),"next"),[Symbol.iterator]:()=>e};return e}[Symbol.iterator](){return this.iterator()}isEmpty(){return!!this.iterator().next().done}count(){let e=this.iterator(),r=0,n=e.next();for(;!n.done;)r++,n=e.next();return r}toArray(){let e=[],r=this.iterator(),n;do n=r.next(),n.value!==void 0&&e.push(n.value);while(!n.done);return e}toSet(){return new Set(this)}toMap(e,r){let n=this.map(i=>[e?e(i):i,r?r(i):i]);return new Map(n)}toString(){return this.join()}concat(e){return new t(()=>({first:this.startFn(),firstDone:!1,iterator:e[Symbol.iterator]()}),r=>{let n;if(!r.firstDone){do if(n=this.nextFn(r.first),!n.done)return n;while(!n.done);r.firstDone=!0}do if(n=r.iterator.next(),!n.done)return n;while(!n.done);return Me})}join(e=","){let r=this.iterator(),n="",i,a=!1;do i=r.next(),i.done||(a&&(n+=e),n+=yx(i.value)),a=!0;while(!i.done);return n}indexOf(e,r=0){let n=this.iterator(),i=0,a=n.next();for(;!a.done;){if(i>=r&&a.value===e)return i;a=n.next(),i++}return-1}every(e){let r=this.iterator(),n=r.next();for(;!n.done;){if(!e(n.value))return!1;n=r.next()}return!0}some(e){let r=this.iterator(),n=r.next();for(;!n.done;){if(e(n.value))return!0;n=r.next()}return!1}forEach(e){let r=this.iterator(),n=0,i=r.next();for(;!i.done;)e(i.value,n),i=r.next(),n++}map(e){return new t(this.startFn,r=>{let{done:n,value:i}=this.nextFn(r);return n?Me:{done:!1,value:e(i)}})}filter(e){return new t(this.startFn,r=>{let n;do if(n=this.nextFn(r),!n.done&&e(n.value))return n;while(!n.done);return Me})}nonNullable(){return this.filter(e=>e!=null)}reduce(e,r){let n=this.iterator(),i=r,a=n.next();for(;!a.done;)i===void 0?i=a.value:i=e(i,a.value),a=n.next();return i}reduceRight(e,r){return this.recursiveReduce(this.iterator(),e,r)}recursiveReduce(e,r,n){let i=e.next();if(i.done)return n;let a=this.recursiveReduce(e,r,n);return a===void 0?i.value:r(a,i.value)}find(e){let r=this.iterator(),n=r.next();for(;!n.done;){if(e(n.value))return n.value;n=r.next()}}findIndex(e){let r=this.iterator(),n=0,i=r.next();for(;!i.done;){if(e(i.value))return n;i=r.next(),n++}return-1}includes(e){let r=this.iterator(),n=r.next();for(;!n.done;){if(n.value===e)return!0;n=r.next()}return!1}flatMap(e){return new t(()=>({this:this.startFn()}),r=>{do{if(r.iterator){let a=r.iterator.next();if(a.done)r.iterator=void 0;else return a}let{done:n,value:i}=this.nextFn(r.this);if(!n){let a=e(i);if(Da(a))r.iterator=a[Symbol.iterator]();else return{done:!1,value:a}}}while(r.iterator);return Me})}flat(e){if(e===void 0&&(e=1),e<=0)return this;let r=e>1?this.flat(e-1):this;return new t(()=>({this:r.startFn()}),n=>{do{if(n.iterator){let o=n.iterator.next();if(o.done)n.iterator=void 0;else return o}let{done:i,value:a}=r.nextFn(n.this);if(!i)if(Da(a))n.iterator=a[Symbol.iterator]();else return{done:!1,value:a}}while(n.iterator);return Me})}head(){let r=this.iterator().next();if(!r.done)return r.value}tail(e=1){return new t(()=>{let r=this.startFn();for(let n=0;n<e;n++)if(this.nextFn(r).done)return r;return r},this.nextFn)}limit(e){return new t(()=>({size:0,state:this.startFn()}),r=>(r.size++,r.size>e?Me:this.nextFn(r.state)))}distinct(e){return new t(()=>({set:new Set,internalState:this.startFn()}),r=>{let n;do if(n=this.nextFn(r.internalState),!n.done){let i=e?e(n.value):n.value;if(!r.set.has(i))return r.set.add(i),n}while(!n.done);return Me})}exclude(e,r){let n=new Set;for(let i of e){let a=r?r(i):i;n.add(a)}return this.filter(i=>{let a=r?r(i):i;return!n.has(a)})}};function yx(t){return typeof t=="string"?t:typeof t>"u"?"undefined":typeof t.toString=="function"?t.toString():Object.prototype.toString.call(t)}s(yx,"toString");function Da(t){return!!t&&typeof t[Symbol.iterator]=="function"}s(Da,"isIterable");var ss=new it(()=>{},()=>Me),Me=Object.freeze({done:!0,value:void 0});function H(...t){if(t.length===1){let e=t[0];if(e instanceof it)return e;if(Da(e))return new it(()=>e[Symbol.iterator](),r=>r.next());if(typeof e.length=="number")return new it(()=>({index:0}),r=>r.index<e.length?{done:!1,value:e[r.index++]}:Me)}return t.length>1?new it(()=>({collIndex:0,arrIndex:0}),e=>{do{if(e.iterator){let r=e.iterator.next();if(!r.done)return r;e.iterator=void 0}if(e.array){if(e.arrIndex<e.array.length)return{done:!1,value:e.array[e.arrIndex++]};e.array=void 0,e.arrIndex=0}if(e.collIndex<t.length){let r=t[e.collIndex++];Da(r)?e.iterator=r[Symbol.iterator]():r&&typeof r.length=="number"&&(e.array=r)}}while(e.iterator||e.array||e.collIndex<t.length);return Me}):ss}s(H,"stream");var Et=class extends it{static{s(this,"TreeStreamImpl")}constructor(e,r,n){super(()=>({iterators:n?.includeRoot?[[e][Symbol.iterator]()]:[r(e)[Symbol.iterator]()],pruned:!1}),i=>{for(i.pruned&&(i.iterators.pop(),i.pruned=!1);i.iterators.length>0;){let o=i.iterators[i.iterators.length-1].next();if(o.done)i.iterators.pop();else return i.iterators.push(r(o.value)[Symbol.iterator]()),o}return Me})}iterator(){let e={state:this.startFn(),next:s(()=>this.nextFn(e.state),"next"),prune:s(()=>{e.state.pruned=!0},"prune"),[Symbol.iterator]:()=>e};return e}},fn;(function(t){function e(a){return a.reduce((o,l)=>o+l,0)}s(e,"sum"),t.sum=e;function r(a){return a.reduce((o,l)=>o*l,0)}s(r,"product"),t.product=r;function n(a){return a.reduce((o,l)=>Math.min(o,l))}s(n,"min"),t.min=n;function i(a){return a.reduce((o,l)=>Math.max(o,l))}s(i,"max"),t.max=i})(fn||(fn={}));function Pr(t){return new Et(t,e=>ht(e)?e.content:[],{includeRoot:!0})}s(Pr,"streamCst");function xx(t){return Pr(t).filter(ur)}s(xx,"flattenCst");function Zl(t,e){for(;t.container;)if(t=t.container,t===e)return!0;return!1}s(Zl,"isChildNode");function dn(t){return{start:{character:t.startColumn-1,line:t.startLine-1},end:{character:t.endColumn,line:t.endLine-1}}}s(dn,"tokenToRange");function Mr(t){if(!t)return;let{offset:e,end:r,range:n}=t;return{range:n,offset:e,end:r,length:r-e}}s(Mr,"toDocumentSegment");var vt;(function(t){t[t.Before=0]="Before",t[t.After=1]="After",t[t.OverlapFront=2]="OverlapFront",t[t.OverlapBack=3]="OverlapBack",t[t.Inside=4]="Inside",t[t.Outside=5]="Outside"})(vt||(vt={}));function Mf(t,e){if(t.end.line<e.start.line||t.end.line===e.start.line&&t.end.character<=e.start.character)return vt.Before;if(t.start.line>e.end.line||t.start.line===e.end.line&&t.start.character>=e.end.character)return vt.After;let r=t.start.line>e.start.line||t.start.line===e.start.line&&t.start.character>=e.start.character,n=t.end.line<e.end.line||t.end.line===e.end.line&&t.end.character<=e.end.character;return r&&n?vt.Inside:r?vt.OverlapBack:n?vt.OverlapFront:vt.Outside}s(Mf,"compareRange");function Ql(t,e){return Mf(t,e)>vt.After}s(Ql,"inRange");var Fa=/^[\w\p{L}]$/u;function Tx(t,e,r=Fa){if(t){if(e>0){let n=e-t.offset,i=t.text.charAt(n);r.test(i)||e--}return tu(t,e)}}s(Tx,"findDeclarationNodeAtOffset");function eu(t,e){if(t){let r=Gf(t,!0);if(r&&Jl(r,e))return r;if(is(t)){let n=t.content.findIndex(i=>!i.hidden);for(let i=n-1;i>=0;i--){let a=t.content[i];if(Jl(a,e))return a}}}}s(eu,"findCommentNode");function Jl(t,e){return ur(t)&&e.includes(t.tokenType.name)}s(Jl,"isCommentNode");function tu(t,e){if(ur(t))return t;if(ht(t)){let r=Ff(t,e,!1);if(r)return tu(r,e)}}s(tu,"findLeafNodeAtOffset");function Df(t,e){if(ur(t))return t;if(ht(t)){let r=Ff(t,e,!0);if(r)return Df(r,e)}}s(Df,"findLeafNodeBeforeOffset");function Ff(t,e,r){let n=0,i=t.content.length-1,a;for(;n<=i;){let o=Math.floor((n+i)/2),l=t.content[o];if(l.offset<=e&&l.end>e)return l;l.end<=e?(a=r?l:void 0,n=o+1):i=o-1}return a}s(Ff,"binarySearch");function Gf(t,e=!0){for(;t.container;){let r=t.container,n=r.content.indexOf(t);for(;n>0;){n--;let i=r.content[n];if(e||!i.hidden)return i}t=r}}s(Gf,"getPreviousNode");function Ax(t,e=!0){for(;t.container;){let r=t.container,n=r.content.indexOf(t),i=r.content.length-1;for(;n<i;){n++;let a=r.content[n];if(e||!a.hidden)return a}t=r}}s(Ax,"getNextNode");function Rx(t){if(t.range.start.character===0)return t;let e=t.range.start.line,r=t,n;for(;t.container;){let i=t.container,a=n??i.content.indexOf(t);if(a===0?(t=i,n=void 0):(n=a-1,t=i.content[n]),t.range.start.line!==e)break;r=t}return r}s(Rx,"getStartlineNode");function Ex(t,e){let r=vx(t,e);return r?r.parent.content.slice(r.a+1,r.b):[]}s(Ex,"getInteriorNodes");function vx(t,e){let r=Pf(t),n=Pf(e),i;for(let a=0;a<r.length&&a<n.length;a++){let o=r[a],l=n[a];if(o.parent===l.parent)i={parent:o.parent,a:o.index,b:l.index};else break}return i}s(vx,"getCommonParent");function Pf(t){let e=[];for(;t.container;){let r=t.container,n=r.content.indexOf(t);e.push({parent:r,index:n}),t=r}return e.reverse()}s(Pf,"getParentChain");var Qa={};Or(Qa,{findAssignment:()=>Du,findNameAssignment:()=>Za,findNodeForKeyword:()=>Pu,findNodeForProperty:()=>Rs,findNodesForKeyword:()=>Kx,findNodesForKeywordInternal:()=>Mu,findNodesForProperty:()=>$u,getActionAtElement:()=>Xf,getActionType:()=>Jf,getAllReachableRules:()=>As,getCrossReferenceTerminal:()=>bu,getEntryRule:()=>Hf,getExplicitRuleType:()=>ti,getHiddenRules:()=>Vf,getRuleType:()=>Fu,getRuleTypeName:()=>Xx,getTypeName:()=>vs,isArrayCardinality:()=>Vx,isArrayOperator:()=>zx,isCommentTerminal:()=>Ou,isDataType:()=>qx,isDataTypeRule:()=>Es,isOptionalCardinality:()=>Hx,terminalRegex:()=>ri});var Dr=class extends Error{static{s(this,"ErrorWithLocation")}constructor(e,r){super(e?`${r} at ${e.range.start.line}:${e.range.start.character}`:r)}};function It(t){throw new Error("Error! The input value was not handled.")}s(It,"assertUnreachable");var ms={};Or(ms,{AbstractElement:()=>hn,AbstractRule:()=>pn,AbstractType:()=>mn,Action:()=>Ln,Alternatives:()=>Pn,ArrayLiteral:()=>gn,ArrayType:()=>yn,Assignment:()=>Mn,BooleanLiteral:()=>xn,CharacterRange:()=>Dn,Condition:()=>as,Conjunction:()=>Tn,CrossReference:()=>Fn,Disjunction:()=>An,EndOfFile:()=>Gn,Grammar:()=>Rn,GrammarImport:()=>ls,Group:()=>Un,InferredType:()=>En,Interface:()=>vn,Keyword:()=>Bn,LangiumGrammarAstReflection:()=>Jn,LangiumGrammarTerminals:()=>Ix,NamedArgument:()=>us,NegatedToken:()=>jn,Negation:()=>In,NumberLiteral:()=>Sn,Parameter:()=>kn,ParameterReference:()=>Cn,ParserRule:()=>Nn,ReferenceType:()=>_n,RegexToken:()=>Wn,ReturnType:()=>cs,RuleCall:()=>Kn,SimpleType:()=>wn,StringLiteral:()=>bn,TerminalAlternatives:()=>Hn,TerminalGroup:()=>Vn,TerminalRule:()=>Fr,TerminalRuleCall:()=>zn,Type:()=>On,TypeAttribute:()=>fs,TypeDefinition:()=>Ua,UnionType:()=>$n,UnorderedGroup:()=>qn,UntilToken:()=>Xn,ValueLiteral:()=>os,Wildcard:()=>Yn,isAbstractElement:()=>ds,isAbstractRule:()=>Sx,isAbstractType:()=>kx,isAction:()=>Ht,isAlternatives:()=>Ka,isArrayLiteral:()=>bx,isArrayType:()=>ru,isAssignment:()=>gt,isBooleanLiteral:()=>nu,isCharacterRange:()=>fu,isCondition:()=>Cx,isConjunction:()=>iu,isCrossReference:()=>Gr,isDisjunction:()=>su,isEndOfFile:()=>du,isFeatureName:()=>Nx,isGrammar:()=>Ox,isGrammarImport:()=>$x,isGroup:()=>cr,isInferredType:()=>Ba,isInterface:()=>ja,isKeyword:()=>ut,isNamedArgument:()=>Lx,isNegatedToken:()=>pu,isNegation:()=>au,isNumberLiteral:()=>Px,isParameter:()=>Mx,isParameterReference:()=>ou,isParserRule:()=>De,isPrimitiveType:()=>Uf,isReferenceType:()=>lu,isRegexToken:()=>mu,isReturnType:()=>uu,isRuleCall:()=>yt,isSimpleType:()=>Wa,isStringLiteral:()=>Dx,isTerminalAlternatives:()=>hu,isTerminalGroup:()=>gu,isTerminalRule:()=>st,isTerminalRuleCall:()=>Ha,isType:()=>ps,isTypeAttribute:()=>Fx,isTypeDefinition:()=>_x,isUnionType:()=>cu,isUnorderedGroup:()=>Va,isUntilToken:()=>yu,isValueLiteral:()=>wx,isWildcard:()=>xu,reflection:()=>O});var Ix={ID:/\^?[_a-zA-Z][\w_]*/,STRING:/"(\\.|[^"\\])*"|'(\\.|[^'\\])*'/,NUMBER:/NaN|-?((\d*\.\d+|\d+)([Ee][+-]?\d+)?|Infinity)/,RegexLiteral:/\/(?![*+?])(?:[^\r\n\[/\\]|\\.|\[(?:[^\r\n\]\\]|\\.)*\])+\/[a-z]*/,WS:/\s+/,ML_COMMENT:/\/\*[\s\S]*?\*\//,SL_COMMENT:/\/\/[^\n\r]*/},pn="AbstractRule";function Sx(t){return O.isInstance(t,pn)}s(Sx,"isAbstractRule");var mn="AbstractType";function kx(t){return O.isInstance(t,mn)}s(kx,"isAbstractType");var as="Condition";function Cx(t){return O.isInstance(t,as)}s(Cx,"isCondition");function Nx(t){return Uf(t)||t==="current"||t==="entry"||t==="extends"||t==="false"||t==="fragment"||t==="grammar"||t==="hidden"||t==="import"||t==="interface"||t==="returns"||t==="terminal"||t==="true"||t==="type"||t==="infer"||t==="infers"||t==="with"||typeof t=="string"&&/\^?[_a-zA-Z][\w_]*/.test(t)}s(Nx,"isFeatureName");function Uf(t){return t==="string"||t==="number"||t==="boolean"||t==="Date"||t==="bigint"}s(Uf,"isPrimitiveType");var Ua="TypeDefinition";function _x(t){return O.isInstance(t,Ua)}s(_x,"isTypeDefinition");var os="ValueLiteral";function wx(t){return O.isInstance(t,os)}s(wx,"isValueLiteral");var hn="AbstractElement";function ds(t){return O.isInstance(t,hn)}s(ds,"isAbstractElement");var gn="ArrayLiteral";function bx(t){return O.isInstance(t,gn)}s(bx,"isArrayLiteral");var yn="ArrayType";function ru(t){return O.isInstance(t,yn)}s(ru,"isArrayType");var xn="BooleanLiteral";function nu(t){return O.isInstance(t,xn)}s(nu,"isBooleanLiteral");var Tn="Conjunction";function iu(t){return O.isInstance(t,Tn)}s(iu,"isConjunction");var An="Disjunction";function su(t){return O.isInstance(t,An)}s(su,"isDisjunction");var Rn="Grammar";function Ox(t){return O.isInstance(t,Rn)}s(Ox,"isGrammar");var ls="GrammarImport";function $x(t){return O.isInstance(t,ls)}s($x,"isGrammarImport");var En="InferredType";function Ba(t){return O.isInstance(t,En)}s(Ba,"isInferredType");var vn="Interface";function ja(t){return O.isInstance(t,vn)}s(ja,"isInterface");var us="NamedArgument";function Lx(t){return O.isInstance(t,us)}s(Lx,"isNamedArgument");var In="Negation";function au(t){return O.isInstance(t,In)}s(au,"isNegation");var Sn="NumberLiteral";function Px(t){return O.isInstance(t,Sn)}s(Px,"isNumberLiteral");var kn="Parameter";function Mx(t){return O.isInstance(t,kn)}s(Mx,"isParameter");var Cn="ParameterReference";function ou(t){return O.isInstance(t,Cn)}s(ou,"isParameterReference");var Nn="ParserRule";function De(t){return O.isInstance(t,Nn)}s(De,"isParserRule");var _n="ReferenceType";function lu(t){return O.isInstance(t,_n)}s(lu,"isReferenceType");var cs="ReturnType";function uu(t){return O.isInstance(t,cs)}s(uu,"isReturnType");var wn="SimpleType";function Wa(t){return O.isInstance(t,wn)}s(Wa,"isSimpleType");var bn="StringLiteral";function Dx(t){return O.isInstance(t,bn)}s(Dx,"isStringLiteral");var Fr="TerminalRule";function st(t){return O.isInstance(t,Fr)}s(st,"isTerminalRule");var On="Type";function ps(t){return O.isInstance(t,On)}s(ps,"isType");var fs="TypeAttribute";function Fx(t){return O.isInstance(t,fs)}s(Fx,"isTypeAttribute");var $n="UnionType";function cu(t){return O.isInstance(t,$n)}s(cu,"isUnionType");var Ln="Action";function Ht(t){return O.isInstance(t,Ln)}s(Ht,"isAction");var Pn="Alternatives";function Ka(t){return O.isInstance(t,Pn)}s(Ka,"isAlternatives");var Mn="Assignment";function gt(t){return O.isInstance(t,Mn)}s(gt,"isAssignment");var Dn="CharacterRange";function fu(t){return O.isInstance(t,Dn)}s(fu,"isCharacterRange");var Fn="CrossReference";function Gr(t){return O.isInstance(t,Fn)}s(Gr,"isCrossReference");var Gn="EndOfFile";function du(t){return O.isInstance(t,Gn)}s(du,"isEndOfFile");var Un="Group";function cr(t){return O.isInstance(t,Un)}s(cr,"isGroup");var Bn="Keyword";function ut(t){return O.isInstance(t,Bn)}s(ut,"isKeyword");var jn="NegatedToken";function pu(t){return O.isInstance(t,jn)}s(pu,"isNegatedToken");var Wn="RegexToken";function mu(t){return O.isInstance(t,Wn)}s(mu,"isRegexToken");var Kn="RuleCall";function yt(t){return O.isInstance(t,Kn)}s(yt,"isRuleCall");var Hn="TerminalAlternatives";function hu(t){return O.isInstance(t,Hn)}s(hu,"isTerminalAlternatives");var Vn="TerminalGroup";function gu(t){return O.isInstance(t,Vn)}s(gu,"isTerminalGroup");var zn="TerminalRuleCall";function Ha(t){return O.isInstance(t,zn)}s(Ha,"isTerminalRuleCall");var qn="UnorderedGroup";function Va(t){return O.isInstance(t,qn)}s(Va,"isUnorderedGroup");var Xn="UntilToken";function yu(t){return O.isInstance(t,Xn)}s(yu,"isUntilToken");var Yn="Wildcard";function xu(t){return O.isInstance(t,Yn)}s(xu,"isWildcard");var Jn=class extends $r{static{s(this,"LangiumGrammarAstReflection")}getAllTypes(){return[hn,pn,mn,Ln,Pn,gn,yn,Mn,xn,Dn,as,Tn,Fn,An,Gn,Rn,ls,Un,En,vn,Bn,us,jn,In,Sn,kn,Cn,Nn,_n,Wn,cs,Kn,wn,bn,Hn,Vn,Fr,zn,On,fs,Ua,$n,qn,Xn,os,Yn]}computeIsSubtype(e,r){switch(e){case Ln:case Pn:case Mn:case Dn:case Fn:case Gn:case Un:case Bn:case jn:case Wn:case Kn:case Hn:case Vn:case zn:case qn:case Xn:case Yn:return this.isSubtype(hn,r);case gn:case Sn:case bn:return this.isSubtype(os,r);case yn:case _n:case wn:case $n:return this.isSubtype(Ua,r);case xn:return this.isSubtype(as,r)||this.isSubtype(os,r);case Tn:case An:case In:case Cn:return this.isSubtype(as,r);case En:case vn:case On:return this.isSubtype(mn,r);case Nn:return this.isSubtype(pn,r)||this.isSubtype(mn,r);case Fr:return this.isSubtype(pn,r);default:return!1}}getReferenceType(e){let r=`${e.container.$type}:${e.property}`;switch(r){case"Action:type":case"CrossReference:type":case"Interface:superTypes":case"ParserRule:returnType":case"SimpleType:typeRef":return mn;case"Grammar:hiddenTokens":case"ParserRule:hiddenTokens":case"RuleCall:rule":return pn;case"Grammar:usedGrammars":return Rn;case"NamedArgument:parameter":case"ParameterReference:parameter":return kn;case"TerminalRuleCall:rule":return Fr;default:throw new Error(`${r} is not a valid reference id.`)}}getTypeMetaData(e){switch(e){case hn:return{name:hn,properties:[{name:"cardinality"},{name:"lookahead"}]};case gn:return{name:gn,properties:[{name:"elements",defaultValue:[]}]};case yn:return{name:yn,properties:[{name:"elementType"}]};case xn:return{name:xn,properties:[{name:"true",defaultValue:!1}]};case Tn:return{name:Tn,properties:[{name:"left"},{name:"right"}]};case An:return{name:An,properties:[{name:"left"},{name:"right"}]};case Rn:return{name:Rn,properties:[{name:"definesHiddenTokens",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"imports",defaultValue:[]},{name:"interfaces",defaultValue:[]},{name:"isDeclared",defaultValue:!1},{name:"name"},{name:"rules",defaultValue:[]},{name:"types",defaultValue:[]},{name:"usedGrammars",defaultValue:[]}]};case ls:return{name:ls,properties:[{name:"path"}]};case En:return{name:En,properties:[{name:"name"}]};case vn:return{name:vn,properties:[{name:"attributes",defaultValue:[]},{name:"name"},{name:"superTypes",defaultValue:[]}]};case us:return{name:us,properties:[{name:"calledByName",defaultValue:!1},{name:"parameter"},{name:"value"}]};case In:return{name:In,properties:[{name:"value"}]};case Sn:return{name:Sn,properties:[{name:"value"}]};case kn:return{name:kn,properties:[{name:"name"}]};case Cn:return{name:Cn,properties:[{name:"parameter"}]};case Nn:return{name:Nn,properties:[{name:"dataType"},{name:"definesHiddenTokens",defaultValue:!1},{name:"definition"},{name:"entry",defaultValue:!1},{name:"fragment",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"inferredType"},{name:"name"},{name:"parameters",defaultValue:[]},{name:"returnType"},{name:"wildcard",defaultValue:!1}]};case _n:return{name:_n,properties:[{name:"referenceType"}]};case cs:return{name:cs,properties:[{name:"name"}]};case wn:return{name:wn,properties:[{name:"primitiveType"},{name:"stringType"},{name:"typeRef"}]};case bn:return{name:bn,properties:[{name:"value"}]};case Fr:return{name:Fr,properties:[{name:"definition"},{name:"fragment",defaultValue:!1},{name:"hidden",defaultValue:!1},{name:"name"},{name:"type"}]};case On:return{name:On,properties:[{name:"name"},{name:"type"}]};case fs:return{name:fs,properties:[{name:"defaultValue"},{name:"isOptional",defaultValue:!1},{name:"name"},{name:"type"}]};case $n:return{name:$n,properties:[{name:"types",defaultValue:[]}]};case Ln:return{name:Ln,properties:[{name:"cardinality"},{name:"feature"},{name:"inferredType"},{name:"lookahead"},{name:"operator"},{name:"type"}]};case Pn:return{name:Pn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Mn:return{name:Mn,properties:[{name:"cardinality"},{name:"feature"},{name:"lookahead"},{name:"operator"},{name:"terminal"}]};case Dn:return{name:Dn,properties:[{name:"cardinality"},{name:"left"},{name:"lookahead"},{name:"right"}]};case Fn:return{name:Fn,properties:[{name:"cardinality"},{name:"deprecatedSyntax",defaultValue:!1},{name:"lookahead"},{name:"terminal"},{name:"type"}]};case Gn:return{name:Gn,properties:[{name:"cardinality"},{name:"lookahead"}]};case Un:return{name:Un,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"guardCondition"},{name:"lookahead"}]};case Bn:return{name:Bn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"value"}]};case jn:return{name:jn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case Wn:return{name:Wn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"regex"}]};case Kn:return{name:Kn,properties:[{name:"arguments",defaultValue:[]},{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case Hn:return{name:Hn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Vn:return{name:Vn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case zn:return{name:zn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case qn:return{name:qn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Xn:return{name:Xn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case Yn:return{name:Yn,properties:[{name:"cardinality"},{name:"lookahead"}]};default:return{name:e,properties:[]}}}},O=new Jn;var qa={};Or(qa,{assignMandatoryProperties:()=>Ru,copyAstNode:()=>Au,findLocalReferences:()=>Ux,findRootNode:()=>hs,getContainerOfType:()=>Ur,getDocument:()=>Fe,hasContainerOfType:()=>Gx,linkContentToContainer:()=>za,streamAllContents:()=>St,streamAst:()=>ct,streamContents:()=>gs,streamReferences:()=>Zn});function za(t){for(let[e,r]of Object.entries(t))e.startsWith("$")||(Array.isArray(r)?r.forEach((n,i)=>{fe(n)&&(n.$container=t,n.$containerProperty=e,n.$containerIndex=i)}):fe(r)&&(r.$container=t,r.$containerProperty=e))}s(za,"linkContentToContainer");function Ur(t,e){let r=t;for(;r;){if(e(r))return r;r=r.$container}}s(Ur,"getContainerOfType");function Gx(t,e){let r=t;for(;r;){if(e(r))return!0;r=r.$container}return!1}s(Gx,"hasContainerOfType");function Fe(t){let r=hs(t).$document;if(!r)throw new Error("AST node has no document.");return r}s(Fe,"getDocument");function hs(t){for(;t.$container;)t=t.$container;return t}s(hs,"findRootNode");function gs(t,e){if(!t)throw new Error("Node must be an AstNode.");let r=e?.range;return new it(()=>({keys:Object.keys(t),keyIndex:0,arrayIndex:0}),n=>{for(;n.keyIndex<n.keys.length;){let i=n.keys[n.keyIndex];if(!i.startsWith("$")){let a=t[i];if(fe(a)){if(n.keyIndex++,Tu(a,r))return{done:!1,value:a}}else if(Array.isArray(a)){for(;n.arrayIndex<a.length;){let o=n.arrayIndex++,l=a[o];if(fe(l)&&Tu(l,r))return{done:!1,value:l}}n.arrayIndex=0}}n.keyIndex++}return Me})}s(gs,"streamContents");function St(t,e){if(!t)throw new Error("Root node must be an AstNode.");return new Et(t,r=>gs(r,e))}s(St,"streamAllContents");function ct(t,e){if(t){if(e?.range&&!Tu(t,e.range))return new Et(t,()=>[])}else throw new Error("Root node must be an AstNode.");return new Et(t,r=>gs(r,e),{includeRoot:!0})}s(ct,"streamAst");function Tu(t,e){var r;if(!e)return!0;let n=(r=t.$cstNode)===null||r===void 0?void 0:r.range;return n?Ql(n,e):!1}s(Tu,"isAstNodeInRange");function Zn(t){return new it(()=>({keys:Object.keys(t),keyIndex:0,arrayIndex:0}),e=>{for(;e.keyIndex<e.keys.length;){let r=e.keys[e.keyIndex];if(!r.startsWith("$")){let n=t[r];if(be(n))return e.keyIndex++,{done:!1,value:{reference:n,container:t,property:r}};if(Array.isArray(n)){for(;e.arrayIndex<n.length;){let i=e.arrayIndex++,a=n[i];if(be(a))return{done:!1,value:{reference:a,container:t,property:r,index:i}}}e.arrayIndex=0}}e.keyIndex++}return Me})}s(Zn,"streamReferences");function Ux(t,e=Fe(t).parseResult.value){let r=[];return ct(e).forEach(n=>{Zn(n).forEach(i=>{i.reference.ref===t&&r.push(i.reference)})}),H(r)}s(Ux,"findLocalReferences");function Ru(t,e){let r=t.getTypeMetaData(e.$type),n=e;for(let i of r.properties)i.defaultValue!==void 0&&n[i.name]===void 0&&(n[i.name]=Bf(i.defaultValue))}s(Ru,"assignMandatoryProperties");function Bf(t){return Array.isArray(t)?[...t.map(Bf)]:t}s(Bf,"copyDefaultValue");function Au(t,e){let r={$type:t.$type};for(let[n,i]of Object.entries(t))if(!n.startsWith("$"))if(fe(i))r[n]=Au(i,e);else if(be(i))r[n]=e(r,n,i.$refNode,i.$refText);else if(Array.isArray(i)){let a=[];for(let o of i)fe(o)?a.push(Au(o,e)):be(o)?a.push(e(r,n,o.$refNode,o.$refText)):a.push(o);r[n]=a}else r[n]=i;return za(r),r}s(Au,"copyAstNode");var Ja={};Or(Ja,{NEWLINE_REGEXP:()=>Su,escapeRegExp:()=>Kr,getCaseInsensitivePattern:()=>Cu,getTerminalParts:()=>Wx,isMultilineComment:()=>ku,isWhitespace:()=>ei,partialMatches:()=>Nu,partialRegExp:()=>Kf,whitespaceCharacters:()=>Wf});function b(t){return t.charCodeAt(0)}s(b,"cc");function Xa(t,e){Array.isArray(t)?t.forEach(function(r){e.push(r)}):e.push(t)}s(Xa,"insertToSet");function Qn(t,e){if(t[e]===!0)throw"duplicate flag "+e;let r=t[e];t[e]=!0}s(Qn,"addFlag");function Br(t){if(t===void 0)throw Error("Internal Error - Should never get here!");return!0}s(Br,"ASSERT_EXISTS");function ys(){throw Error("Internal Error - Should never get here!")}s(ys,"ASSERT_NEVER_REACH_HERE");function Eu(t){return t.type==="Character"}s(Eu,"isCharacter");var xs=[];for(let t=b("0");t<=b("9");t++)xs.push(t);var Ts=[b("_")].concat(xs);for(let t=b("a");t<=b("z");t++)Ts.push(t);for(let t=b("A");t<=b("Z");t++)Ts.push(t);var vu=[b(" "),b("\f"),b(`
`),b("\r"),b("	"),b("\v"),b("	"),b("\xA0"),b("\u1680"),b("\u2000"),b("\u2001"),b("\u2002"),b("\u2003"),b("\u2004"),b("\u2005"),b("\u2006"),b("\u2007"),b("\u2008"),b("\u2009"),b("\u200A"),b("\u2028"),b("\u2029"),b("\u202F"),b("\u205F"),b("\u3000"),b("\uFEFF")];var Bx=/[0-9a-fA-F]/,Ya=/[0-9]/,jx=/[1-9]/,jr=class{static{s(this,"RegExpParser")}constructor(){this.idx=0,this.input="",this.groupIdx=0}saveState(){return{idx:this.idx,input:this.input,groupIdx:this.groupIdx}}restoreState(e){this.idx=e.idx,this.input=e.input,this.groupIdx=e.groupIdx}pattern(e){this.idx=0,this.input=e,this.groupIdx=0,this.consumeChar("/");let r=this.disjunction();this.consumeChar("/");let n={type:"Flags",loc:{begin:this.idx,end:e.length},global:!1,ignoreCase:!1,multiLine:!1,unicode:!1,sticky:!1};for(;this.isRegExpFlag();)switch(this.popChar()){case"g":Qn(n,"global");break;case"i":Qn(n,"ignoreCase");break;case"m":Qn(n,"multiLine");break;case"u":Qn(n,"unicode");break;case"y":Qn(n,"sticky");break}if(this.idx!==this.input.length)throw Error("Redundant input: "+this.input.substring(this.idx));return{type:"Pattern",flags:n,value:r,loc:this.loc(0)}}disjunction(){let e=[],r=this.idx;for(e.push(this.alternative());this.peekChar()==="|";)this.consumeChar("|"),e.push(this.alternative());return{type:"Disjunction",value:e,loc:this.loc(r)}}alternative(){let e=[],r=this.idx;for(;this.isTerm();)e.push(this.term());return{type:"Alternative",value:e,loc:this.loc(r)}}term(){return this.isAssertion()?this.assertion():this.atom()}assertion(){let e=this.idx;switch(this.popChar()){case"^":return{type:"StartAnchor",loc:this.loc(e)};case"$":return{type:"EndAnchor",loc:this.loc(e)};case"\\":switch(this.popChar()){case"b":return{type:"WordBoundary",loc:this.loc(e)};case"B":return{type:"NonWordBoundary",loc:this.loc(e)}}throw Error("Invalid Assertion Escape");case"(":this.consumeChar("?");let r;switch(this.popChar()){case"=":r="Lookahead";break;case"!":r="NegativeLookahead";break}Br(r);let n=this.disjunction();return this.consumeChar(")"),{type:r,value:n,loc:this.loc(e)}}return ys()}quantifier(e=!1){let r,n=this.idx;switch(this.popChar()){case"*":r={atLeast:0,atMost:1/0};break;case"+":r={atLeast:1,atMost:1/0};break;case"?":r={atLeast:0,atMost:1};break;case"{":let i=this.integerIncludingZero();switch(this.popChar()){case"}":r={atLeast:i,atMost:i};break;case",":let a;this.isDigit()?(a=this.integerIncludingZero(),r={atLeast:i,atMost:a}):r={atLeast:i,atMost:1/0},this.consumeChar("}");break}if(e===!0&&r===void 0)return;Br(r);break}if(!(e===!0&&r===void 0)&&Br(r))return this.peekChar(0)==="?"?(this.consumeChar("?"),r.greedy=!1):r.greedy=!0,r.type="Quantifier",r.loc=this.loc(n),r}atom(){let e,r=this.idx;switch(this.peekChar()){case".":e=this.dotAll();break;case"\\":e=this.atomEscape();break;case"[":e=this.characterClass();break;case"(":e=this.group();break}return e===void 0&&this.isPatternCharacter()&&(e=this.patternCharacter()),Br(e)?(e.loc=this.loc(r),this.isQuantifier()&&(e.quantifier=this.quantifier()),e):ys()}dotAll(){return this.consumeChar("."),{type:"Set",complement:!0,value:[b(`
`),b("\r"),b("\u2028"),b("\u2029")]}}atomEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":return this.decimalEscapeAtom();case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}decimalEscapeAtom(){return{type:"GroupBackReference",value:this.positiveInteger()}}characterClassEscape(){let e,r=!1;switch(this.popChar()){case"d":e=xs;break;case"D":e=xs,r=!0;break;case"s":e=vu;break;case"S":e=vu,r=!0;break;case"w":e=Ts;break;case"W":e=Ts,r=!0;break}return Br(e)?{type:"Set",value:e,complement:r}:ys()}controlEscapeAtom(){let e;switch(this.popChar()){case"f":e=b("\f");break;case"n":e=b(`
`);break;case"r":e=b("\r");break;case"t":e=b("	");break;case"v":e=b("\v");break}return Br(e)?{type:"Character",value:e}:ys()}controlLetterEscapeAtom(){this.consumeChar("c");let e=this.popChar();if(/[a-zA-Z]/.test(e)===!1)throw Error("Invalid ");return{type:"Character",value:e.toUpperCase().charCodeAt(0)-64}}nulCharacterAtom(){return this.consumeChar("0"),{type:"Character",value:b("\0")}}hexEscapeSequenceAtom(){return this.consumeChar("x"),this.parseHexDigits(2)}regExpUnicodeEscapeSequenceAtom(){return this.consumeChar("u"),this.parseHexDigits(4)}identityEscapeAtom(){let e=this.popChar();return{type:"Character",value:b(e)}}classPatternCharacterAtom(){switch(this.peekChar()){case`
`:case"\r":case"\u2028":case"\u2029":case"\\":case"]":throw Error("TBD");default:let e=this.popChar();return{type:"Character",value:b(e)}}}characterClass(){let e=[],r=!1;for(this.consumeChar("["),this.peekChar(0)==="^"&&(this.consumeChar("^"),r=!0);this.isClassAtom();){let n=this.classAtom(),i=n.type==="Character";if(Eu(n)&&this.isRangeDash()){this.consumeChar("-");let a=this.classAtom(),o=a.type==="Character";if(Eu(a)){if(a.value<n.value)throw Error("Range out of order in character class");e.push({from:n.value,to:a.value})}else Xa(n.value,e),e.push(b("-")),Xa(a.value,e)}else Xa(n.value,e)}return this.consumeChar("]"),{type:"Set",complement:r,value:e}}classAtom(){switch(this.peekChar()){case"]":case`
`:case"\r":case"\u2028":case"\u2029":throw Error("TBD");case"\\":return this.classEscape();default:return this.classPatternCharacterAtom()}}classEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"b":return this.consumeChar("b"),{type:"Character",value:b("\b")};case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}group(){let e=!0;switch(this.consumeChar("("),this.peekChar(0)){case"?":this.consumeChar("?"),this.consumeChar(":"),e=!1;break;default:this.groupIdx++;break}let r=this.disjunction();this.consumeChar(")");let n={type:"Group",capturing:e,value:r};return e&&(n.idx=this.groupIdx),n}positiveInteger(){let e=this.popChar();if(jx.test(e)===!1)throw Error("Expecting a positive integer");for(;Ya.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}integerIncludingZero(){let e=this.popChar();if(Ya.test(e)===!1)throw Error("Expecting an integer");for(;Ya.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}patternCharacter(){let e=this.popChar();switch(e){case`
`:case"\r":case"\u2028":case"\u2029":case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":throw Error("TBD");default:return{type:"Character",value:b(e)}}}isRegExpFlag(){switch(this.peekChar(0)){case"g":case"i":case"m":case"u":case"y":return!0;default:return!1}}isRangeDash(){return this.peekChar()==="-"&&this.isClassAtom(1)}isDigit(){return Ya.test(this.peekChar(0))}isClassAtom(e=0){switch(this.peekChar(e)){case"]":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}isTerm(){return this.isAtom()||this.isAssertion()}isAtom(){if(this.isPatternCharacter())return!0;switch(this.peekChar(0)){case".":case"\\":case"[":case"(":return!0;default:return!1}}isAssertion(){switch(this.peekChar(0)){case"^":case"$":return!0;case"\\":switch(this.peekChar(1)){case"b":case"B":return!0;default:return!1}case"(":return this.peekChar(1)==="?"&&(this.peekChar(2)==="="||this.peekChar(2)==="!");default:return!1}}isQuantifier(){let e=this.saveState();try{return this.quantifier(!0)!==void 0}catch{return!1}finally{this.restoreState(e)}}isPatternCharacter(){switch(this.peekChar()){case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":case"/":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}parseHexDigits(e){let r="";for(let i=0;i<e;i++){let a=this.popChar();if(Bx.test(a)===!1)throw Error("Expecting a HexDecimal digits");r+=a}return{type:"Character",value:parseInt(r,16)}}peekChar(e=0){return this.input[this.idx+e]}popChar(){let e=this.peekChar(0);return this.consumeChar(void 0),e}consumeChar(e){if(e!==void 0&&this.input[this.idx]!==e)throw Error("Expected: '"+e+"' but found: '"+this.input[this.idx]+"' at offset: "+this.idx);if(this.idx>=this.input.length)throw Error("Unexpected end of input");this.idx++}loc(e){return{begin:e,end:this.idx}}};var kt=class{static{s(this,"BaseRegExpVisitor")}visitChildren(e){for(let r in e){let n=e[r];e.hasOwnProperty(r)&&(n.type!==void 0?this.visit(n):Array.isArray(n)&&n.forEach(i=>{this.visit(i)},this))}}visit(e){switch(e.type){case"Pattern":this.visitPattern(e);break;case"Flags":this.visitFlags(e);break;case"Disjunction":this.visitDisjunction(e);break;case"Alternative":this.visitAlternative(e);break;case"StartAnchor":this.visitStartAnchor(e);break;case"EndAnchor":this.visitEndAnchor(e);break;case"WordBoundary":this.visitWordBoundary(e);break;case"NonWordBoundary":this.visitNonWordBoundary(e);break;case"Lookahead":this.visitLookahead(e);break;case"NegativeLookahead":this.visitNegativeLookahead(e);break;case"Character":this.visitCharacter(e);break;case"Set":this.visitSet(e);break;case"Group":this.visitGroup(e);break;case"GroupBackReference":this.visitGroupBackReference(e);break;case"Quantifier":this.visitQuantifier(e);break}this.visitChildren(e)}visitPattern(e){}visitFlags(e){}visitDisjunction(e){}visitAlternative(e){}visitStartAnchor(e){}visitEndAnchor(e){}visitWordBoundary(e){}visitNonWordBoundary(e){}visitLookahead(e){}visitNegativeLookahead(e){}visitCharacter(e){}visitSet(e){}visitGroup(e){}visitGroupBackReference(e){}visitQuantifier(e){}};var Su=/\r?\n/gm,jf=new jr,Iu=class extends kt{static{s(this,"TerminalRegExpVisitor")}constructor(){super(...arguments),this.isStarting=!0,this.endRegexpStack=[],this.multiline=!1}get endRegex(){return this.endRegexpStack.join("")}reset(e){this.multiline=!1,this.regex=e,this.startRegexp="",this.isStarting=!0,this.endRegexpStack=[]}visitGroup(e){e.quantifier&&(this.isStarting=!1,this.endRegexpStack=[])}visitCharacter(e){let r=String.fromCharCode(e.value);if(!this.multiline&&r===`
`&&(this.multiline=!0),e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{let n=Kr(r);this.endRegexpStack.push(n),this.isStarting&&(this.startRegexp+=n)}}visitSet(e){if(!this.multiline){let r=this.regex.substring(e.loc.begin,e.loc.end),n=new RegExp(r);this.multiline=!!`
`.match(n)}if(e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{let r=this.regex.substring(e.loc.begin,e.loc.end);this.endRegexpStack.push(r),this.isStarting&&(this.startRegexp+=r)}}visitChildren(e){e.type==="Group"&&e.quantifier||super.visitChildren(e)}},Wr=new Iu;function Wx(t){try{typeof t!="string"&&(t=t.source),t=`/${t}/`;let e=jf.pattern(t),r=[];for(let n of e.value.value)Wr.reset(t),Wr.visit(n),r.push({start:Wr.startRegexp,end:Wr.endRegex});return r}catch{return[]}}s(Wx,"getTerminalParts");function ku(t){try{return typeof t=="string"&&(t=new RegExp(t)),t=t.toString(),Wr.reset(t),Wr.visit(jf.pattern(t)),Wr.multiline}catch{return!1}}s(ku,"isMultilineComment");var Wf=`\f
\r	\v \xA0\u1680\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF`.split("");function ei(t){let e=typeof t=="string"?new RegExp(t):t;return Wf.some(r=>e.test(r))}s(ei,"isWhitespace");function Kr(t){return t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}s(Kr,"escapeRegExp");function Cu(t){return Array.prototype.map.call(t,e=>/\w/.test(e)?`[${e.toLowerCase()}${e.toUpperCase()}]`:Kr(e)).join("")}s(Cu,"getCaseInsensitivePattern");function Nu(t,e){let r=Kf(t),n=e.match(r);return!!n&&n[0].length>0}s(Nu,"partialMatches");function Kf(t){typeof t=="string"&&(t=new RegExp(t));let e=t,r=t.source,n=0;function i(){let a="",o;function l(c){a+=r.substr(n,c),n+=c}s(l,"appendRaw");function u(c){a+="(?:"+r.substr(n,c)+"|$)",n+=c}for(s(u,"appendOptional");n<r.length;)switch(r[n]){case"\\":switch(r[n+1]){case"c":u(3);break;case"x":u(4);break;case"u":e.unicode?r[n+2]==="{"?u(r.indexOf("}",n)-n+1):u(6):u(2);break;case"p":case"P":e.unicode?u(r.indexOf("}",n)-n+1):u(2);break;case"k":u(r.indexOf(">",n)-n+1);break;default:u(2);break}break;case"[":o=/\[(?:\\.|.)*?\]/g,o.lastIndex=n,o=o.exec(r)||[],u(o[0].length);break;case"|":case"^":case"$":case"*":case"+":case"?":l(1);break;case"{":o=/\{\d+,?\d*\}/g,o.lastIndex=n,o=o.exec(r),o?l(o[0].length):u(1);break;case"(":if(r[n+1]==="?")switch(r[n+2]){case":":a+="(?:",n+=3,a+=i()+"|$)";break;case"=":a+="(?=",n+=3,a+=i()+")";break;case"!":o=n,n+=3,i(),a+=r.substr(o,n-o);break;case"<":switch(r[n+3]){case"=":case"!":o=n,n+=4,i(),a+=r.substr(o,n-o);break;default:l(r.indexOf(">",n)-n+1),a+=i()+"|$)";break}break}else l(1),a+=i()+"|$)";break;case")":return++n,a;default:u(1);break}return a}return s(i,"process"),new RegExp(i(),t.flags)}s(Kf,"partialRegExp");function Hf(t){return t.rules.find(e=>De(e)&&e.entry)}s(Hf,"getEntryRule");function Vf(t){return t.rules.filter(e=>st(e)&&e.hidden)}s(Vf,"getHiddenRules");function As(t,e){let r=new Set,n=Hf(t);if(!n)return new Set(t.rules);let i=[n].concat(Vf(t));for(let o of i)zf(o,r,e);let a=new Set;for(let o of t.rules)(r.has(o.name)||st(o)&&o.hidden)&&a.add(o);return a}s(As,"getAllReachableRules");function zf(t,e,r){e.add(t.name),St(t).forEach(n=>{if(yt(n)||r&&Ha(n)){let i=n.rule.ref;i&&!e.has(i.name)&&zf(i,e,r)}})}s(zf,"ruleDfs");function bu(t){if(t.terminal)return t.terminal;if(t.type.ref){let e=Za(t.type.ref);return e?.terminal}}s(bu,"getCrossReferenceTerminal");function Ou(t){return t.hidden&&!ei(ri(t))}s(Ou,"isCommentTerminal");function $u(t,e){return!t||!e?[]:Lu(t,e,t.astNode,!0)}s($u,"findNodesForProperty");function Rs(t,e,r){if(!t||!e)return;let n=Lu(t,e,t.astNode,!0);if(n.length!==0)return r!==void 0?r=Math.max(0,Math.min(r,n.length-1)):r=0,n[r]}s(Rs,"findNodeForProperty");function Lu(t,e,r,n){if(!n){let i=Ur(t.grammarSource,gt);if(i&&i.feature===e)return[t]}return ht(t)&&t.astNode===r?t.content.flatMap(i=>Lu(i,e,r,!1)):[]}s(Lu,"findNodesForPropertyInternal");function Kx(t,e){return t?Mu(t,e,t?.astNode):[]}s(Kx,"findNodesForKeyword");function Pu(t,e,r){if(!t)return;let n=Mu(t,e,t?.astNode);if(n.length!==0)return r!==void 0?r=Math.max(0,Math.min(r,n.length-1)):r=0,n[r]}s(Pu,"findNodeForKeyword");function Mu(t,e,r){if(t.astNode!==r)return[];if(ut(t.grammarSource)&&t.grammarSource.value===e)return[t];let n=Pr(t).iterator(),i,a=[];do if(i=n.next(),!i.done){let o=i.value;o.astNode===r?ut(o.grammarSource)&&o.grammarSource.value===e&&a.push(o):n.prune()}while(!i.done);return a}s(Mu,"findNodesForKeywordInternal");function Du(t){var e;let r=t.astNode;for(;r===((e=t.container)===null||e===void 0?void 0:e.astNode);){let n=Ur(t.grammarSource,gt);if(n)return n;t=t.container}}s(Du,"findAssignment");function Za(t){let e=t;return Ba(e)&&(Ht(e.$container)?e=e.$container.$container:De(e.$container)?e=e.$container:It(e.$container)),qf(t,e,new Map)}s(Za,"findNameAssignment");function qf(t,e,r){var n;function i(a,o){let l;return Ur(a,gt)||(l=qf(o,o,r)),r.set(t,l),l}if(s(i,"go"),r.has(t))return r.get(t);r.set(t,void 0);for(let a of St(e)){if(gt(a)&&a.feature.toLowerCase()==="name")return r.set(t,a),a;if(yt(a)&&De(a.rule.ref))return i(a,a.rule.ref);if(Wa(a)&&(!((n=a.typeRef)===null||n===void 0)&&n.ref))return i(a,a.typeRef.ref)}}s(qf,"findNameAssignmentInternal");function Xf(t){let e=t.$container;if(cr(e)){let r=e.elements,n=r.indexOf(t);for(let i=n-1;i>=0;i--){let a=r[i];if(Ht(a))return a;{let o=St(r[i]).find(Ht);if(o)return o}}}if(ds(e))return Xf(e)}s(Xf,"getActionAtElement");function Hx(t,e){return t==="?"||t==="*"||cr(e)&&!!e.guardCondition}s(Hx,"isOptionalCardinality");function Vx(t){return t==="*"||t==="+"}s(Vx,"isArrayCardinality");function zx(t){return t==="+="}s(zx,"isArrayOperator");function Es(t){return Yf(t,new Set)}s(Es,"isDataTypeRule");function Yf(t,e){if(e.has(t))return!0;e.add(t);for(let r of St(t))if(yt(r)){if(!r.rule.ref||De(r.rule.ref)&&!Yf(r.rule.ref,e))return!1}else{if(gt(r))return!1;if(Ht(r))return!1}return!!t.definition}s(Yf,"isDataTypeRuleInternal");function qx(t){return wu(t.type,new Set)}s(qx,"isDataType");function wu(t,e){if(e.has(t))return!0;if(e.add(t),ru(t))return!1;if(lu(t))return!1;if(cu(t))return t.types.every(r=>wu(r,e));if(Wa(t)){if(t.primitiveType!==void 0)return!0;if(t.stringType!==void 0)return!0;if(t.typeRef!==void 0){let r=t.typeRef.ref;return ps(r)?wu(r.type,e):!1}else return!1}else return!1}s(wu,"isDataTypeInternal");function ti(t){if(t.inferredType)return t.inferredType.name;if(t.dataType)return t.dataType;if(t.returnType){let e=t.returnType.ref;if(e){if(De(e))return e.name;if(ja(e)||ps(e))return e.name}}}s(ti,"getExplicitRuleType");function vs(t){var e;if(De(t))return Es(t)?t.name:(e=ti(t))!==null&&e!==void 0?e:t.name;if(ja(t)||ps(t)||uu(t))return t.name;if(Ht(t)){let r=Jf(t);if(r)return r}else if(Ba(t))return t.name;throw new Error("Cannot get name of Unknown Type")}s(vs,"getTypeName");function Jf(t){var e;if(t.inferredType)return t.inferredType.name;if(!((e=t.type)===null||e===void 0)&&e.ref)return vs(t.type.ref)}s(Jf,"getActionType");function Xx(t){var e,r,n;return st(t)?(r=(e=t.type)===null||e===void 0?void 0:e.name)!==null&&r!==void 0?r:"string":Es(t)?t.name:(n=ti(t))!==null&&n!==void 0?n:t.name}s(Xx,"getRuleTypeName");function Fu(t){var e,r,n;return st(t)?(r=(e=t.type)===null||e===void 0?void 0:e.name)!==null&&r!==void 0?r:"string":(n=ti(t))!==null&&n!==void 0?n:t.name}s(Fu,"getRuleType");function ri(t){let e={s:!1,i:!1,u:!1},r=ni(t.definition,e),n=Object.entries(e).filter(([,i])=>i).map(([i])=>i).join("");return new RegExp(r,n)}s(ri,"terminalRegex");var Gu=/[\s\S]/.source;function ni(t,e){if(hu(t))return Yx(t);if(gu(t))return Jx(t);if(fu(t))return eT(t);if(Ha(t)){let r=t.rule.ref;if(!r)throw new Error("Missing rule reference.");return Vt(ni(r.definition),{cardinality:t.cardinality,lookahead:t.lookahead})}else{if(pu(t))return Qx(t);if(yu(t))return Zx(t);if(mu(t)){let r=t.regex.lastIndexOf("/"),n=t.regex.substring(1,r),i=t.regex.substring(r+1);return e&&(e.i=i.includes("i"),e.s=i.includes("s"),e.u=i.includes("u")),Vt(n,{cardinality:t.cardinality,lookahead:t.lookahead,wrap:!1})}else{if(xu(t))return Vt(Gu,{cardinality:t.cardinality,lookahead:t.lookahead});throw new Error(`Invalid terminal element: ${t?.$type}`)}}}s(ni,"abstractElementToRegex");function Yx(t){return Vt(t.elements.map(e=>ni(e)).join("|"),{cardinality:t.cardinality,lookahead:t.lookahead})}s(Yx,"terminalAlternativesToRegex");function Jx(t){return Vt(t.elements.map(e=>ni(e)).join(""),{cardinality:t.cardinality,lookahead:t.lookahead})}s(Jx,"terminalGroupToRegex");function Zx(t){return Vt(`${Gu}*?${ni(t.terminal)}`,{cardinality:t.cardinality,lookahead:t.lookahead})}s(Zx,"untilTokenToRegex");function Qx(t){return Vt(`(?!${ni(t.terminal)})${Gu}*?`,{cardinality:t.cardinality,lookahead:t.lookahead})}s(Qx,"negateTokenToRegex");function eT(t){return t.right?Vt(`[${_u(t.left)}-${_u(t.right)}]`,{cardinality:t.cardinality,lookahead:t.lookahead,wrap:!1}):Vt(_u(t.left),{cardinality:t.cardinality,lookahead:t.lookahead,wrap:!1})}s(eT,"characterRangeToRegex");function _u(t){return Kr(t.value)}s(_u,"keywordToRegex");function Vt(t,e){var r;return(e.wrap!==!1||e.lookahead)&&(t=`(${(r=e.lookahead)!==null&&r!==void 0?r:""}${t})`),e.cardinality?`${t}${e.cardinality}`:t}s(Vt,"withCardinality");function Uu(t){let e=[],r=t.Grammar;for(let n of r.rules)st(n)&&Ou(n)&&ku(ri(n))&&e.push(n.name);return{multilineCommentRules:e,nameRegexp:Fa}}s(Uu,"createGrammarConfig");var tT=typeof global=="object"&&global&&global.Object===Object&&global,eo=tT;var rT=typeof self=="object"&&self&&self.Object===Object&&self,nT=eo||rT||Function("return this")(),Re=nT;var iT=Re.Symbol,Ce=iT;var Zf=Object.prototype,sT=Zf.hasOwnProperty,aT=Zf.toString,Is=Ce?Ce.toStringTag:void 0;function oT(t){var e=sT.call(t,Is),r=t[Is];try{t[Is]=void 0;var n=!0}catch{}var i=aT.call(t);return n&&(e?t[Is]=r:delete t[Is]),i}s(oT,"getRawTag");var Qf=oT;var lT=Object.prototype,uT=lT.toString;function cT(t){return uT.call(t)}s(cT,"objectToString");var ed=cT;var fT="[object Null]",dT="[object Undefined]",td=Ce?Ce.toStringTag:void 0;function pT(t){return t==null?t===void 0?dT:fT:td&&td in Object(t)?Qf(t):ed(t)}s(pT,"baseGetTag");var Ke=pT;function mT(t){return t!=null&&typeof t=="object"}s(mT,"isObjectLike");var ge=mT;var hT="[object Symbol]";function gT(t){return typeof t=="symbol"||ge(t)&&Ke(t)==hT}s(gT,"isSymbol");var Ct=gT;function yT(t,e){for(var r=-1,n=t==null?0:t.length,i=Array(n);++r<n;)i[r]=e(t[r],r,t);return i}s(yT,"arrayMap");var Nt=yT;var xT=Array.isArray,N=xT;var TT=1/0,rd=Ce?Ce.prototype:void 0,nd=rd?rd.toString:void 0;function id(t){if(typeof t=="string")return t;if(N(t))return Nt(t,id)+"";if(Ct(t))return nd?nd.call(t):"";var e=t+"";return e=="0"&&1/t==-TT?"-0":e}s(id,"baseToString");var sd=id;var AT=/\s/;function RT(t){for(var e=t.length;e--&&AT.test(t.charAt(e)););return e}s(RT,"trimmedEndIndex");var ad=RT;var ET=/^\s+/;function vT(t){return t&&t.slice(0,ad(t)+1).replace(ET,"")}s(vT,"baseTrim");var od=vT;function IT(t){var e=typeof t;return t!=null&&(e=="object"||e=="function")}s(IT,"isObject");var de=IT;var ld=NaN,ST=/^[-+]0x[0-9a-f]+$/i,kT=/^0b[01]+$/i,CT=/^0o[0-7]+$/i,NT=parseInt;function _T(t){if(typeof t=="number")return t;if(Ct(t))return ld;if(de(t)){var e=typeof t.valueOf=="function"?t.valueOf():t;t=de(e)?e+"":e}if(typeof t!="string")return t===0?t:+t;t=od(t);var r=kT.test(t);return r||CT.test(t)?NT(t.slice(2),r?2:8):ST.test(t)?ld:+t}s(_T,"toNumber");var ud=_T;var cd=1/0,wT=17976931348623157e292;function bT(t){if(!t)return t===0?t:0;if(t=ud(t),t===cd||t===-cd){var e=t<0?-1:1;return e*wT}return t===t?t:0}s(bT,"toFinite");var fd=bT;function OT(t){var e=fd(t),r=e%1;return e===e?r?e-r:e:0}s(OT,"toInteger");var _t=OT;function $T(t){return t}s($T,"identity");var Ze=$T;var LT="[object AsyncFunction]",PT="[object Function]",MT="[object GeneratorFunction]",DT="[object Proxy]";function FT(t){if(!de(t))return!1;var e=Ke(t);return e==PT||e==MT||e==LT||e==DT}s(FT,"isFunction");var He=FT;var GT=Re["__core-js_shared__"],to=GT;var dd=function(){var t=/[^.]+$/.exec(to&&to.keys&&to.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();function UT(t){return!!dd&&dd in t}s(UT,"isMasked");var pd=UT;var BT=Function.prototype,jT=BT.toString;function WT(t){if(t!=null){try{return jT.call(t)}catch{}try{return t+""}catch{}}return""}s(WT,"toSource");var zt=WT;var KT=/[\\^$.*+?()[\]{}|]/g,HT=/^\[object .+?Constructor\]$/,VT=Function.prototype,zT=Object.prototype,qT=VT.toString,XT=zT.hasOwnProperty,YT=RegExp("^"+qT.call(XT).replace(KT,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function JT(t){if(!de(t)||pd(t))return!1;var e=He(t)?YT:HT;return e.test(zt(t))}s(JT,"baseIsNative");var md=JT;function ZT(t,e){return t?.[e]}s(ZT,"getValue");var hd=ZT;function QT(t,e){var r=hd(t,e);return md(r)?r:void 0}s(QT,"getNative");var Qe=QT;var eA=Qe(Re,"WeakMap"),ro=eA;var gd=Object.create,tA=function(){function t(){}return s(t,"object"),function(e){if(!de(e))return{};if(gd)return gd(e);t.prototype=e;var r=new t;return t.prototype=void 0,r}}(),yd=tA;function rA(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}s(rA,"apply");var xd=rA;function nA(){}s(nA,"noop");var pe=nA;function iA(t,e){var r=-1,n=t.length;for(e||(e=Array(n));++r<n;)e[r]=t[r];return e}s(iA,"copyArray");var Td=iA;var sA=800,aA=16,oA=Date.now;function lA(t){var e=0,r=0;return function(){var n=oA(),i=aA-(n-r);if(r=n,i>0){if(++e>=sA)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}s(lA,"shortOut");var Ad=lA;function uA(t){return function(){return t}}s(uA,"constant");var Rd=uA;var cA=function(){try{var t=Qe(Object,"defineProperty");return t({},"",{}),t}catch{}}(),ii=cA;var fA=ii?function(t,e){return ii(t,"toString",{configurable:!0,enumerable:!1,value:Rd(e),writable:!0})}:Ze,Ed=fA;var dA=Ad(Ed),vd=dA;function pA(t,e){for(var r=-1,n=t==null?0:t.length;++r<n&&e(t[r],r,t)!==!1;);return t}s(pA,"arrayEach");var no=pA;function mA(t,e,r,n){for(var i=t.length,a=r+(n?1:-1);n?a--:++a<i;)if(e(t[a],a,t))return a;return-1}s(mA,"baseFindIndex");var io=mA;function hA(t){return t!==t}s(hA,"baseIsNaN");var Id=hA;function gA(t,e,r){for(var n=r-1,i=t.length;++n<i;)if(t[n]===e)return n;return-1}s(gA,"strictIndexOf");var Sd=gA;function yA(t,e,r){return e===e?Sd(t,e,r):io(t,Id,r)}s(yA,"baseIndexOf");var si=yA;function xA(t,e){var r=t==null?0:t.length;return!!r&&si(t,e,0)>-1}s(xA,"arrayIncludes");var so=xA;var TA=9007199254740991,AA=/^(?:0|[1-9]\d*)$/;function RA(t,e){var r=typeof t;return e=e??TA,!!e&&(r=="number"||r!="symbol"&&AA.test(t))&&t>-1&&t%1==0&&t<e}s(RA,"isIndex");var fr=RA;function EA(t,e,r){e=="__proto__"&&ii?ii(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}s(EA,"baseAssignValue");var ai=EA;function vA(t,e){return t===e||t!==t&&e!==e}s(vA,"eq");var wt=vA;var IA=Object.prototype,SA=IA.hasOwnProperty;function kA(t,e,r){var n=t[e];(!(SA.call(t,e)&&wt(n,r))||r===void 0&&!(e in t))&&ai(t,e,r)}s(kA,"assignValue");var dr=kA;function CA(t,e,r,n){var i=!r;r||(r={});for(var a=-1,o=e.length;++a<o;){var l=e[a],u=n?n(r[l],t[l],l,r,t):void 0;u===void 0&&(u=t[l]),i?ai(r,l,u):dr(r,l,u)}return r}s(CA,"copyObject");var bt=CA;var kd=Math.max;function NA(t,e,r){return e=kd(e===void 0?t.length-1:e,0),function(){for(var n=arguments,i=-1,a=kd(n.length-e,0),o=Array(a);++i<a;)o[i]=n[e+i];i=-1;for(var l=Array(e+1);++i<e;)l[i]=n[i];return l[e]=r(o),xd(t,this,l)}}s(NA,"overRest");var Cd=NA;function _A(t,e){return vd(Cd(t,e,Ze),t+"")}s(_A,"baseRest");var oi=_A;var wA=9007199254740991;function bA(t){return typeof t=="number"&&t>-1&&t%1==0&&t<=wA}s(bA,"isLength");var li=bA;function OA(t){return t!=null&&li(t.length)&&!He(t)}s(OA,"isArrayLike");var Ee=OA;function $A(t,e,r){if(!de(r))return!1;var n=typeof e;return(n=="number"?Ee(r)&&fr(e,r.length):n=="string"&&e in r)?wt(r[e],t):!1}s($A,"isIterateeCall");var pr=$A;function LA(t){return oi(function(e,r){var n=-1,i=r.length,a=i>1?r[i-1]:void 0,o=i>2?r[2]:void 0;for(a=t.length>3&&typeof a=="function"?(i--,a):void 0,o&&pr(r[0],r[1],o)&&(a=i<3?void 0:a,i=1),e=Object(e);++n<i;){var l=r[n];l&&t(e,l,n,a)}return e})}s(LA,"createAssigner");var Nd=LA;var PA=Object.prototype;function MA(t){var e=t&&t.constructor,r=typeof e=="function"&&e.prototype||PA;return t===r}s(MA,"isPrototype");var Ot=MA;function DA(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}s(DA,"baseTimes");var _d=DA;var FA="[object Arguments]";function GA(t){return ge(t)&&Ke(t)==FA}s(GA,"baseIsArguments");var Bu=GA;var wd=Object.prototype,UA=wd.hasOwnProperty,BA=wd.propertyIsEnumerable,jA=Bu(function(){return arguments}())?Bu:function(t){return ge(t)&&UA.call(t,"callee")&&!BA.call(t,"callee")},mr=jA;function WA(){return!1}s(WA,"stubFalse");var bd=WA;var Ld=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Od=Ld&&typeof module=="object"&&module&&!module.nodeType&&module,KA=Od&&Od.exports===Ld,$d=KA?Re.Buffer:void 0,HA=$d?$d.isBuffer:void 0,VA=HA||bd,qt=VA;var zA="[object Arguments]",qA="[object Array]",XA="[object Boolean]",YA="[object Date]",JA="[object Error]",ZA="[object Function]",QA="[object Map]",eR="[object Number]",tR="[object Object]",rR="[object RegExp]",nR="[object Set]",iR="[object String]",sR="[object WeakMap]",aR="[object ArrayBuffer]",oR="[object DataView]",lR="[object Float32Array]",uR="[object Float64Array]",cR="[object Int8Array]",fR="[object Int16Array]",dR="[object Int32Array]",pR="[object Uint8Array]",mR="[object Uint8ClampedArray]",hR="[object Uint16Array]",gR="[object Uint32Array]",se={};se[lR]=se[uR]=se[cR]=se[fR]=se[dR]=se[pR]=se[mR]=se[hR]=se[gR]=!0;se[zA]=se[qA]=se[aR]=se[XA]=se[oR]=se[YA]=se[JA]=se[ZA]=se[QA]=se[eR]=se[tR]=se[rR]=se[nR]=se[iR]=se[sR]=!1;function yR(t){return ge(t)&&li(t.length)&&!!se[Ke(t)]}s(yR,"baseIsTypedArray");var Pd=yR;function xR(t){return function(e){return t(e)}}s(xR,"baseUnary");var $t=xR;var Md=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Ss=Md&&typeof module=="object"&&module&&!module.nodeType&&module,TR=Ss&&Ss.exports===Md,ju=TR&&eo.process,AR=function(){try{var t=Ss&&Ss.require&&Ss.require("util").types;return t||ju&&ju.binding&&ju.binding("util")}catch{}}(),ft=AR;var Dd=ft&&ft.isTypedArray,RR=Dd?$t(Dd):Pd,ui=RR;var ER=Object.prototype,vR=ER.hasOwnProperty;function IR(t,e){var r=N(t),n=!r&&mr(t),i=!r&&!n&&qt(t),a=!r&&!n&&!i&&ui(t),o=r||n||i||a,l=o?_d(t.length,String):[],u=l.length;for(var c in t)(e||vR.call(t,c))&&!(o&&(c=="length"||i&&(c=="offset"||c=="parent")||a&&(c=="buffer"||c=="byteLength"||c=="byteOffset")||fr(c,u)))&&l.push(c);return l}s(IR,"arrayLikeKeys");var ao=IR;function SR(t,e){return function(r){return t(e(r))}}s(SR,"overArg");var oo=SR;var kR=oo(Object.keys,Object),Fd=kR;var CR=Object.prototype,NR=CR.hasOwnProperty;function _R(t){if(!Ot(t))return Fd(t);var e=[];for(var r in Object(t))NR.call(t,r)&&r!="constructor"&&e.push(r);return e}s(_R,"baseKeys");var lo=_R;function wR(t){return Ee(t)?ao(t):lo(t)}s(wR,"keys");var Z=wR;var bR=Object.prototype,OR=bR.hasOwnProperty,$R=Nd(function(t,e){if(Ot(e)||Ee(e)){bt(e,Z(e),t);return}for(var r in e)OR.call(e,r)&&dr(t,r,e[r])}),Oe=$R;function LR(t){var e=[];if(t!=null)for(var r in Object(t))e.push(r);return e}s(LR,"nativeKeysIn");var Gd=LR;var PR=Object.prototype,MR=PR.hasOwnProperty;function DR(t){if(!de(t))return Gd(t);var e=Ot(t),r=[];for(var n in t)n=="constructor"&&(e||!MR.call(t,n))||r.push(n);return r}s(DR,"baseKeysIn");var Ud=DR;function FR(t){return Ee(t)?ao(t,!0):Ud(t)}s(FR,"keysIn");var hr=FR;var GR=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,UR=/^\w*$/;function BR(t,e){if(N(t))return!1;var r=typeof t;return r=="number"||r=="symbol"||r=="boolean"||t==null||Ct(t)?!0:UR.test(t)||!GR.test(t)||e!=null&&t in Object(e)}s(BR,"isKey");var ci=BR;var jR=Qe(Object,"create"),Xt=jR;function WR(){this.__data__=Xt?Xt(null):{},this.size=0}s(WR,"hashClear");var Bd=WR;function KR(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}s(KR,"hashDelete");var jd=KR;var HR="__lodash_hash_undefined__",VR=Object.prototype,zR=VR.hasOwnProperty;function qR(t){var e=this.__data__;if(Xt){var r=e[t];return r===HR?void 0:r}return zR.call(e,t)?e[t]:void 0}s(qR,"hashGet");var Wd=qR;var XR=Object.prototype,YR=XR.hasOwnProperty;function JR(t){var e=this.__data__;return Xt?e[t]!==void 0:YR.call(e,t)}s(JR,"hashHas");var Kd=JR;var ZR="__lodash_hash_undefined__";function QR(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=Xt&&e===void 0?ZR:e,this}s(QR,"hashSet");var Hd=QR;function fi(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}s(fi,"Hash");fi.prototype.clear=Bd;fi.prototype.delete=jd;fi.prototype.get=Wd;fi.prototype.has=Kd;fi.prototype.set=Hd;var Wu=fi;function eE(){this.__data__=[],this.size=0}s(eE,"listCacheClear");var Vd=eE;function tE(t,e){for(var r=t.length;r--;)if(wt(t[r][0],e))return r;return-1}s(tE,"assocIndexOf");var gr=tE;var rE=Array.prototype,nE=rE.splice;function iE(t){var e=this.__data__,r=gr(e,t);if(r<0)return!1;var n=e.length-1;return r==n?e.pop():nE.call(e,r,1),--this.size,!0}s(iE,"listCacheDelete");var zd=iE;function sE(t){var e=this.__data__,r=gr(e,t);return r<0?void 0:e[r][1]}s(sE,"listCacheGet");var qd=sE;function aE(t){return gr(this.__data__,t)>-1}s(aE,"listCacheHas");var Xd=aE;function oE(t,e){var r=this.__data__,n=gr(r,t);return n<0?(++this.size,r.push([t,e])):r[n][1]=e,this}s(oE,"listCacheSet");var Yd=oE;function di(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}s(di,"ListCache");di.prototype.clear=Vd;di.prototype.delete=zd;di.prototype.get=qd;di.prototype.has=Xd;di.prototype.set=Yd;var yr=di;var lE=Qe(Re,"Map"),xr=lE;function uE(){this.size=0,this.__data__={hash:new Wu,map:new(xr||yr),string:new Wu}}s(uE,"mapCacheClear");var Jd=uE;function cE(t){var e=typeof t;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?t!=="__proto__":t===null}s(cE,"isKeyable");var Zd=cE;function fE(t,e){var r=t.__data__;return Zd(e)?r[typeof e=="string"?"string":"hash"]:r.map}s(fE,"getMapData");var Tr=fE;function dE(t){var e=Tr(this,t).delete(t);return this.size-=e?1:0,e}s(dE,"mapCacheDelete");var Qd=dE;function pE(t){return Tr(this,t).get(t)}s(pE,"mapCacheGet");var ep=pE;function mE(t){return Tr(this,t).has(t)}s(mE,"mapCacheHas");var tp=mE;function hE(t,e){var r=Tr(this,t),n=r.size;return r.set(t,e),this.size+=r.size==n?0:1,this}s(hE,"mapCacheSet");var rp=hE;function pi(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}s(pi,"MapCache");pi.prototype.clear=Jd;pi.prototype.delete=Qd;pi.prototype.get=ep;pi.prototype.has=tp;pi.prototype.set=rp;var Hr=pi;var gE="Expected a function";function Ku(t,e){if(typeof t!="function"||e!=null&&typeof e!="function")throw new TypeError(gE);var r=s(function(){var n=arguments,i=e?e.apply(this,n):n[0],a=r.cache;if(a.has(i))return a.get(i);var o=t.apply(this,n);return r.cache=a.set(i,o)||a,o},"memoized");return r.cache=new(Ku.Cache||Hr),r}s(Ku,"memoize");Ku.Cache=Hr;var np=Ku;var yE=500;function xE(t){var e=np(t,function(n){return r.size===yE&&r.clear(),n}),r=e.cache;return e}s(xE,"memoizeCapped");var ip=xE;var TE=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,AE=/\\(\\)?/g,RE=ip(function(t){var e=[];return t.charCodeAt(0)===46&&e.push(""),t.replace(TE,function(r,n,i,a){e.push(i?a.replace(AE,"$1"):n||r)}),e}),sp=RE;function EE(t){return t==null?"":sd(t)}s(EE,"toString");var ap=EE;function vE(t,e){return N(t)?t:ci(t,e)?[t]:sp(ap(t))}s(vE,"castPath");var Ar=vE;var IE=1/0;function SE(t){if(typeof t=="string"||Ct(t))return t;var e=t+"";return e=="0"&&1/t==-IE?"-0":e}s(SE,"toKey");var Lt=SE;function kE(t,e){e=Ar(e,t);for(var r=0,n=e.length;t!=null&&r<n;)t=t[Lt(e[r++])];return r&&r==n?t:void 0}s(kE,"baseGet");var mi=kE;function CE(t,e,r){var n=t==null?void 0:mi(t,e);return n===void 0?r:n}s(CE,"get");var op=CE;function NE(t,e){for(var r=-1,n=e.length,i=t.length;++r<n;)t[i+r]=e[r];return t}s(NE,"arrayPush");var hi=NE;var lp=Ce?Ce.isConcatSpreadable:void 0;function _E(t){return N(t)||mr(t)||!!(lp&&t&&t[lp])}s(_E,"isFlattenable");var up=_E;function cp(t,e,r,n,i){var a=-1,o=t.length;for(r||(r=up),i||(i=[]);++a<o;){var l=t[a];e>0&&r(l)?e>1?cp(l,e-1,r,n,i):hi(i,l):n||(i[i.length]=l)}return i}s(cp,"baseFlatten");var gi=cp;function wE(t){var e=t==null?0:t.length;return e?gi(t,1):[]}s(wE,"flatten");var ye=wE;var bE=oo(Object.getPrototypeOf,Object),uo=bE;function OE(t,e,r){var n=-1,i=t.length;e<0&&(e=-e>i?0:i+e),r=r>i?i:r,r<0&&(r+=i),i=e>r?0:r-e>>>0,e>>>=0;for(var a=Array(i);++n<i;)a[n]=t[n+e];return a}s(OE,"baseSlice");var co=OE;function $E(t,e,r,n){var i=-1,a=t==null?0:t.length;for(n&&a&&(r=t[++i]);++i<a;)r=e(r,t[i],i,t);return r}s($E,"arrayReduce");var fp=$E;function LE(){this.__data__=new yr,this.size=0}s(LE,"stackClear");var dp=LE;function PE(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}s(PE,"stackDelete");var pp=PE;function ME(t){return this.__data__.get(t)}s(ME,"stackGet");var mp=ME;function DE(t){return this.__data__.has(t)}s(DE,"stackHas");var hp=DE;var FE=200;function GE(t,e){var r=this.__data__;if(r instanceof yr){var n=r.__data__;if(!xr||n.length<FE-1)return n.push([t,e]),this.size=++r.size,this;r=this.__data__=new Hr(n)}return r.set(t,e),this.size=r.size,this}s(GE,"stackSet");var gp=GE;function yi(t){var e=this.__data__=new yr(t);this.size=e.size}s(yi,"Stack");yi.prototype.clear=dp;yi.prototype.delete=pp;yi.prototype.get=mp;yi.prototype.has=hp;yi.prototype.set=gp;var Rr=yi;function UE(t,e){return t&&bt(e,Z(e),t)}s(UE,"baseAssign");var yp=UE;function BE(t,e){return t&&bt(e,hr(e),t)}s(BE,"baseAssignIn");var xp=BE;var Ep=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Tp=Ep&&typeof module=="object"&&module&&!module.nodeType&&module,jE=Tp&&Tp.exports===Ep,Ap=jE?Re.Buffer:void 0,Rp=Ap?Ap.allocUnsafe:void 0;function WE(t,e){if(e)return t.slice();var r=t.length,n=Rp?Rp(r):new t.constructor(r);return t.copy(n),n}s(WE,"cloneBuffer");var vp=WE;function KE(t,e){for(var r=-1,n=t==null?0:t.length,i=0,a=[];++r<n;){var o=t[r];e(o,r,t)&&(a[i++]=o)}return a}s(KE,"arrayFilter");var xi=KE;function HE(){return[]}s(HE,"stubArray");var fo=HE;var VE=Object.prototype,zE=VE.propertyIsEnumerable,Ip=Object.getOwnPropertySymbols,qE=Ip?function(t){return t==null?[]:(t=Object(t),xi(Ip(t),function(e){return zE.call(t,e)}))}:fo,Ti=qE;function XE(t,e){return bt(t,Ti(t),e)}s(XE,"copySymbols");var Sp=XE;var YE=Object.getOwnPropertySymbols,JE=YE?function(t){for(var e=[];t;)hi(e,Ti(t)),t=uo(t);return e}:fo,po=JE;function ZE(t,e){return bt(t,po(t),e)}s(ZE,"copySymbolsIn");var kp=ZE;function QE(t,e,r){var n=e(t);return N(t)?n:hi(n,r(t))}s(QE,"baseGetAllKeys");var mo=QE;function ev(t){return mo(t,Z,Ti)}s(ev,"getAllKeys");var ks=ev;function tv(t){return mo(t,hr,po)}s(tv,"getAllKeysIn");var ho=tv;var rv=Qe(Re,"DataView"),go=rv;var nv=Qe(Re,"Promise"),yo=nv;var iv=Qe(Re,"Set"),Er=iv;var Cp="[object Map]",sv="[object Object]",Np="[object Promise]",_p="[object Set]",wp="[object WeakMap]",bp="[object DataView]",av=zt(go),ov=zt(xr),lv=zt(yo),uv=zt(Er),cv=zt(ro),Vr=Ke;(go&&Vr(new go(new ArrayBuffer(1)))!=bp||xr&&Vr(new xr)!=Cp||yo&&Vr(yo.resolve())!=Np||Er&&Vr(new Er)!=_p||ro&&Vr(new ro)!=wp)&&(Vr=s(function(t){var e=Ke(t),r=e==sv?t.constructor:void 0,n=r?zt(r):"";if(n)switch(n){case av:return bp;case ov:return Cp;case lv:return Np;case uv:return _p;case cv:return wp}return e},"getTag"));var xt=Vr;var fv=Object.prototype,dv=fv.hasOwnProperty;function pv(t){var e=t.length,r=new t.constructor(e);return e&&typeof t[0]=="string"&&dv.call(t,"index")&&(r.index=t.index,r.input=t.input),r}s(pv,"initCloneArray");var Op=pv;var mv=Re.Uint8Array,Ai=mv;function hv(t){var e=new t.constructor(t.byteLength);return new Ai(e).set(new Ai(t)),e}s(hv,"cloneArrayBuffer");var Ri=hv;function gv(t,e){var r=e?Ri(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.byteLength)}s(gv,"cloneDataView");var $p=gv;var yv=/\w*$/;function xv(t){var e=new t.constructor(t.source,yv.exec(t));return e.lastIndex=t.lastIndex,e}s(xv,"cloneRegExp");var Lp=xv;var Pp=Ce?Ce.prototype:void 0,Mp=Pp?Pp.valueOf:void 0;function Tv(t){return Mp?Object(Mp.call(t)):{}}s(Tv,"cloneSymbol");var Dp=Tv;function Av(t,e){var r=e?Ri(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.length)}s(Av,"cloneTypedArray");var Fp=Av;var Rv="[object Boolean]",Ev="[object Date]",vv="[object Map]",Iv="[object Number]",Sv="[object RegExp]",kv="[object Set]",Cv="[object String]",Nv="[object Symbol]",_v="[object ArrayBuffer]",wv="[object DataView]",bv="[object Float32Array]",Ov="[object Float64Array]",$v="[object Int8Array]",Lv="[object Int16Array]",Pv="[object Int32Array]",Mv="[object Uint8Array]",Dv="[object Uint8ClampedArray]",Fv="[object Uint16Array]",Gv="[object Uint32Array]";function Uv(t,e,r){var n=t.constructor;switch(e){case _v:return Ri(t);case Rv:case Ev:return new n(+t);case wv:return $p(t,r);case bv:case Ov:case $v:case Lv:case Pv:case Mv:case Dv:case Fv:case Gv:return Fp(t,r);case vv:return new n;case Iv:case Cv:return new n(t);case Sv:return Lp(t);case kv:return new n;case Nv:return Dp(t)}}s(Uv,"initCloneByTag");var Gp=Uv;function Bv(t){return typeof t.constructor=="function"&&!Ot(t)?yd(uo(t)):{}}s(Bv,"initCloneObject");var Up=Bv;var jv="[object Map]";function Wv(t){return ge(t)&&xt(t)==jv}s(Wv,"baseIsMap");var Bp=Wv;var jp=ft&&ft.isMap,Kv=jp?$t(jp):Bp,Wp=Kv;var Hv="[object Set]";function Vv(t){return ge(t)&&xt(t)==Hv}s(Vv,"baseIsSet");var Kp=Vv;var Hp=ft&&ft.isSet,zv=Hp?$t(Hp):Kp,Vp=zv;var qv=1,Xv=2,Yv=4,zp="[object Arguments]",Jv="[object Array]",Zv="[object Boolean]",Qv="[object Date]",eI="[object Error]",qp="[object Function]",tI="[object GeneratorFunction]",rI="[object Map]",nI="[object Number]",Xp="[object Object]",iI="[object RegExp]",sI="[object Set]",aI="[object String]",oI="[object Symbol]",lI="[object WeakMap]",uI="[object ArrayBuffer]",cI="[object DataView]",fI="[object Float32Array]",dI="[object Float64Array]",pI="[object Int8Array]",mI="[object Int16Array]",hI="[object Int32Array]",gI="[object Uint8Array]",yI="[object Uint8ClampedArray]",xI="[object Uint16Array]",TI="[object Uint32Array]",Q={};Q[zp]=Q[Jv]=Q[uI]=Q[cI]=Q[Zv]=Q[Qv]=Q[fI]=Q[dI]=Q[pI]=Q[mI]=Q[hI]=Q[rI]=Q[nI]=Q[Xp]=Q[iI]=Q[sI]=Q[aI]=Q[oI]=Q[gI]=Q[yI]=Q[xI]=Q[TI]=!0;Q[eI]=Q[qp]=Q[lI]=!1;function xo(t,e,r,n,i,a){var o,l=e&qv,u=e&Xv,c=e&Yv;if(r&&(o=i?r(t,n,i,a):r(t)),o!==void 0)return o;if(!de(t))return t;var f=N(t);if(f){if(o=Op(t),!l)return Td(t,o)}else{var d=xt(t),p=d==qp||d==tI;if(qt(t))return vp(t,l);if(d==Xp||d==zp||p&&!i){if(o=u||p?{}:Up(t),!l)return u?kp(t,xp(o,t)):Sp(t,yp(o,t))}else{if(!Q[d])return i?t:{};o=Gp(t,d,l)}}a||(a=new Rr);var m=a.get(t);if(m)return m;a.set(t,o),Vp(t)?t.forEach(function(R){o.add(xo(R,e,r,R,t,a))}):Wp(t)&&t.forEach(function(R,T){o.set(T,xo(R,e,r,T,t,a))});var g=c?u?ho:ks:u?hr:Z,y=f?void 0:g(t);return no(y||t,function(R,T){y&&(T=R,R=t[T]),dr(o,T,xo(R,e,r,T,t,a))}),o}s(xo,"baseClone");var Yp=xo;var AI=4;function RI(t){return Yp(t,AI)}s(RI,"clone");var ee=RI;function EI(t){for(var e=-1,r=t==null?0:t.length,n=0,i=[];++e<r;){var a=t[e];a&&(i[n++]=a)}return i}s(EI,"compact");var Pt=EI;var vI="__lodash_hash_undefined__";function II(t){return this.__data__.set(t,vI),this}s(II,"setCacheAdd");var Jp=II;function SI(t){return this.__data__.has(t)}s(SI,"setCacheHas");var Zp=SI;function To(t){var e=-1,r=t==null?0:t.length;for(this.__data__=new Hr;++e<r;)this.add(t[e])}s(To,"SetCache");To.prototype.add=To.prototype.push=Jp;To.prototype.has=Zp;var Ei=To;function kI(t,e){for(var r=-1,n=t==null?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}s(kI,"arraySome");var Ao=kI;function CI(t,e){return t.has(e)}s(CI,"cacheHas");var vi=CI;var NI=1,_I=2;function wI(t,e,r,n,i,a){var o=r&NI,l=t.length,u=e.length;if(l!=u&&!(o&&u>l))return!1;var c=a.get(t),f=a.get(e);if(c&&f)return c==e&&f==t;var d=-1,p=!0,m=r&_I?new Ei:void 0;for(a.set(t,e),a.set(e,t);++d<l;){var g=t[d],y=e[d];if(n)var R=o?n(y,g,d,e,t,a):n(g,y,d,t,e,a);if(R!==void 0){if(R)continue;p=!1;break}if(m){if(!Ao(e,function(T,E){if(!vi(m,E)&&(g===T||i(g,T,r,n,a)))return m.push(E)})){p=!1;break}}else if(!(g===y||i(g,y,r,n,a))){p=!1;break}}return a.delete(t),a.delete(e),p}s(wI,"equalArrays");var Ro=wI;function bI(t){var e=-1,r=Array(t.size);return t.forEach(function(n,i){r[++e]=[i,n]}),r}s(bI,"mapToArray");var Qp=bI;function OI(t){var e=-1,r=Array(t.size);return t.forEach(function(n){r[++e]=n}),r}s(OI,"setToArray");var Ii=OI;var $I=1,LI=2,PI="[object Boolean]",MI="[object Date]",DI="[object Error]",FI="[object Map]",GI="[object Number]",UI="[object RegExp]",BI="[object Set]",jI="[object String]",WI="[object Symbol]",KI="[object ArrayBuffer]",HI="[object DataView]",em=Ce?Ce.prototype:void 0,Hu=em?em.valueOf:void 0;function VI(t,e,r,n,i,a,o){switch(r){case HI:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case KI:return!(t.byteLength!=e.byteLength||!a(new Ai(t),new Ai(e)));case PI:case MI:case GI:return wt(+t,+e);case DI:return t.name==e.name&&t.message==e.message;case UI:case jI:return t==e+"";case FI:var l=Qp;case BI:var u=n&$I;if(l||(l=Ii),t.size!=e.size&&!u)return!1;var c=o.get(t);if(c)return c==e;n|=LI,o.set(t,e);var f=Ro(l(t),l(e),n,i,a,o);return o.delete(t),f;case WI:if(Hu)return Hu.call(t)==Hu.call(e)}return!1}s(VI,"equalByTag");var tm=VI;var zI=1,qI=Object.prototype,XI=qI.hasOwnProperty;function YI(t,e,r,n,i,a){var o=r&zI,l=ks(t),u=l.length,c=ks(e),f=c.length;if(u!=f&&!o)return!1;for(var d=u;d--;){var p=l[d];if(!(o?p in e:XI.call(e,p)))return!1}var m=a.get(t),g=a.get(e);if(m&&g)return m==e&&g==t;var y=!0;a.set(t,e),a.set(e,t);for(var R=o;++d<u;){p=l[d];var T=t[p],E=e[p];if(n)var A=o?n(E,T,p,e,t,a):n(T,E,p,t,e,a);if(!(A===void 0?T===E||i(T,E,r,n,a):A)){y=!1;break}R||(R=p=="constructor")}if(y&&!R){var $=t.constructor,L=e.constructor;$!=L&&"constructor"in t&&"constructor"in e&&!(typeof $=="function"&&$ instanceof $&&typeof L=="function"&&L instanceof L)&&(y=!1)}return a.delete(t),a.delete(e),y}s(YI,"equalObjects");var rm=YI;var JI=1,nm="[object Arguments]",im="[object Array]",Eo="[object Object]",ZI=Object.prototype,sm=ZI.hasOwnProperty;function QI(t,e,r,n,i,a){var o=N(t),l=N(e),u=o?im:xt(t),c=l?im:xt(e);u=u==nm?Eo:u,c=c==nm?Eo:c;var f=u==Eo,d=c==Eo,p=u==c;if(p&&qt(t)){if(!qt(e))return!1;o=!0,f=!1}if(p&&!f)return a||(a=new Rr),o||ui(t)?Ro(t,e,r,n,i,a):tm(t,e,u,r,n,i,a);if(!(r&JI)){var m=f&&sm.call(t,"__wrapped__"),g=d&&sm.call(e,"__wrapped__");if(m||g){var y=m?t.value():t,R=g?e.value():e;return a||(a=new Rr),i(y,R,r,n,a)}}return p?(a||(a=new Rr),rm(t,e,r,n,i,a)):!1}s(QI,"baseIsEqualDeep");var am=QI;function om(t,e,r,n,i){return t===e?!0:t==null||e==null||!ge(t)&&!ge(e)?t!==t&&e!==e:am(t,e,r,n,om,i)}s(om,"baseIsEqual");var vo=om;var eS=1,tS=2;function rS(t,e,r,n){var i=r.length,a=i,o=!n;if(t==null)return!a;for(t=Object(t);i--;){var l=r[i];if(o&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++i<a;){l=r[i];var u=l[0],c=t[u],f=l[1];if(o&&l[2]){if(c===void 0&&!(u in t))return!1}else{var d=new Rr;if(n)var p=n(c,f,u,t,e,d);if(!(p===void 0?vo(f,c,eS|tS,n,d):p))return!1}}return!0}s(rS,"baseIsMatch");var lm=rS;function nS(t){return t===t&&!de(t)}s(nS,"isStrictComparable");var Io=nS;function iS(t){for(var e=Z(t),r=e.length;r--;){var n=e[r],i=t[n];e[r]=[n,i,Io(i)]}return e}s(iS,"getMatchData");var um=iS;function sS(t,e){return function(r){return r==null?!1:r[t]===e&&(e!==void 0||t in Object(r))}}s(sS,"matchesStrictComparable");var So=sS;function aS(t){var e=um(t);return e.length==1&&e[0][2]?So(e[0][0],e[0][1]):function(r){return r===t||lm(r,t,e)}}s(aS,"baseMatches");var cm=aS;function oS(t,e){return t!=null&&e in Object(t)}s(oS,"baseHasIn");var fm=oS;function lS(t,e,r){e=Ar(e,t);for(var n=-1,i=e.length,a=!1;++n<i;){var o=Lt(e[n]);if(!(a=t!=null&&r(t,o)))break;t=t[o]}return a||++n!=i?a:(i=t==null?0:t.length,!!i&&li(i)&&fr(o,i)&&(N(t)||mr(t)))}s(lS,"hasPath");var ko=lS;function uS(t,e){return t!=null&&ko(t,e,fm)}s(uS,"hasIn");var dm=uS;var cS=1,fS=2;function dS(t,e){return ci(t)&&Io(e)?So(Lt(t),e):function(r){var n=op(r,t);return n===void 0&&n===e?dm(r,t):vo(e,n,cS|fS)}}s(dS,"baseMatchesProperty");var pm=dS;function pS(t){return function(e){return e?.[t]}}s(pS,"baseProperty");var mm=pS;function mS(t){return function(e){return mi(e,t)}}s(mS,"basePropertyDeep");var hm=mS;function hS(t){return ci(t)?mm(Lt(t)):hm(t)}s(hS,"property");var gm=hS;function gS(t){return typeof t=="function"?t:t==null?Ze:typeof t=="object"?N(t)?pm(t[0],t[1]):cm(t):gm(t)}s(gS,"baseIteratee");var he=gS;function yS(t,e,r,n){for(var i=-1,a=t==null?0:t.length;++i<a;){var o=t[i];e(n,o,r(o),t)}return n}s(yS,"arrayAggregator");var ym=yS;function xS(t){return function(e,r,n){for(var i=-1,a=Object(e),o=n(e),l=o.length;l--;){var u=o[t?l:++i];if(r(a[u],u,a)===!1)break}return e}}s(xS,"createBaseFor");var xm=xS;var TS=xm(),Tm=TS;function AS(t,e){return t&&Tm(t,e,Z)}s(AS,"baseForOwn");var Am=AS;function RS(t,e){return function(r,n){if(r==null)return r;if(!Ee(r))return t(r,n);for(var i=r.length,a=e?i:-1,o=Object(r);(e?a--:++a<i)&&n(o[a],a,o)!==!1;);return r}}s(RS,"createBaseEach");var Rm=RS;var ES=Rm(Am),et=ES;function vS(t,e,r,n){return et(t,function(i,a,o){e(n,i,r(i),o)}),n}s(vS,"baseAggregator");var Em=vS;function IS(t,e){return function(r,n){var i=N(r)?ym:Em,a=e?e():{};return i(r,t,he(n,2),a)}}s(IS,"createAggregator");var vm=IS;var Im=Object.prototype,SS=Im.hasOwnProperty,kS=oi(function(t,e){t=Object(t);var r=-1,n=e.length,i=n>2?e[2]:void 0;for(i&&pr(e[0],e[1],i)&&(n=1);++r<n;)for(var a=e[r],o=hr(a),l=-1,u=o.length;++l<u;){var c=o[l],f=t[c];(f===void 0||wt(f,Im[c])&&!SS.call(t,c))&&(t[c]=a[c])}return t}),Si=kS;function CS(t){return ge(t)&&Ee(t)}s(CS,"isArrayLikeObject");var Vu=CS;function NS(t,e,r){for(var n=-1,i=t==null?0:t.length;++n<i;)if(r(e,t[n]))return!0;return!1}s(NS,"arrayIncludesWith");var Co=NS;var _S=200;function wS(t,e,r,n){var i=-1,a=so,o=!0,l=t.length,u=[],c=e.length;if(!l)return u;r&&(e=Nt(e,$t(r))),n?(a=Co,o=!1):e.length>=_S&&(a=vi,o=!1,e=new Ei(e));e:for(;++i<l;){var f=t[i],d=r==null?f:r(f);if(f=n||f!==0?f:0,o&&d===d){for(var p=c;p--;)if(e[p]===d)continue e;u.push(f)}else a(e,d,n)||u.push(f)}return u}s(wS,"baseDifference");var Sm=wS;var bS=oi(function(t,e){return Vu(t)?Sm(t,gi(e,1,Vu,!0)):[]}),vr=bS;function OS(t){var e=t==null?0:t.length;return e?t[e-1]:void 0}s(OS,"last");var Mt=OS;function $S(t,e,r){var n=t==null?0:t.length;return n?(e=r||e===void 0?1:_t(e),co(t,e<0?0:e,n)):[]}s($S,"drop");var xe=$S;function LS(t,e,r){var n=t==null?0:t.length;return n?(e=r||e===void 0?1:_t(e),e=n-e,co(t,0,e<0?0:e)):[]}s(LS,"dropRight");var Yt=LS;function PS(t){return typeof t=="function"?t:Ze}s(PS,"castFunction");var km=PS;function MS(t,e){var r=N(t)?no:et;return r(t,km(e))}s(MS,"forEach");var S=MS;function DS(t,e){for(var r=-1,n=t==null?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}s(DS,"arrayEvery");var Cm=DS;function FS(t,e){var r=!0;return et(t,function(n,i,a){return r=!!e(n,i,a),r}),r}s(FS,"baseEvery");var Nm=FS;function GS(t,e,r){var n=N(t)?Cm:Nm;return r&&pr(t,e,r)&&(e=void 0),n(t,he(e,3))}s(GS,"every");var Ge=GS;function US(t,e){var r=[];return et(t,function(n,i,a){e(n,i,a)&&r.push(n)}),r}s(US,"baseFilter");var No=US;function BS(t,e){var r=N(t)?xi:No;return r(t,he(e,3))}s(BS,"filter");var Ne=BS;function jS(t){return function(e,r,n){var i=Object(e);if(!Ee(e)){var a=he(r,3);e=Z(e),r=s(function(l){return a(i[l],l,i)},"predicate")}var o=t(e,r,n);return o>-1?i[a?e[o]:o]:void 0}}s(jS,"createFind");var _m=jS;var WS=Math.max;function KS(t,e,r){var n=t==null?0:t.length;if(!n)return-1;var i=r==null?0:_t(r);return i<0&&(i=WS(n+i,0)),io(t,he(e,3),i)}s(KS,"findIndex");var wm=KS;var HS=_m(wm),Dt=HS;function VS(t){return t&&t.length?t[0]:void 0}s(VS,"head");var _e=VS;function zS(t,e){var r=-1,n=Ee(t)?Array(t.length):[];return et(t,function(i,a,o){n[++r]=e(i,a,o)}),n}s(zS,"baseMap");var bm=zS;function qS(t,e){var r=N(t)?Nt:bm;return r(t,he(e,3))}s(qS,"map");var v=qS;function XS(t,e){return gi(v(t,e),1)}s(XS,"flatMap");var $e=XS;var YS=Object.prototype,JS=YS.hasOwnProperty,ZS=vm(function(t,e,r){JS.call(t,r)?t[r].push(e):ai(t,r,[e])}),zu=ZS;var QS=Object.prototype,ek=QS.hasOwnProperty;function tk(t,e){return t!=null&&ek.call(t,e)}s(tk,"baseHas");var Om=tk;function rk(t,e){return t!=null&&ko(t,e,Om)}s(rk,"has");var k=rk;var nk="[object String]";function ik(t){return typeof t=="string"||!N(t)&&ge(t)&&Ke(t)==nk}s(ik,"isString");var Ie=ik;function sk(t,e){return Nt(e,function(r){return t[r]})}s(sk,"baseValues");var $m=sk;function ak(t){return t==null?[]:$m(t,Z(t))}s(ak,"values");var q=ak;var ok=Math.max;function lk(t,e,r,n){t=Ee(t)?t:q(t),r=r&&!n?_t(r):0;var i=t.length;return r<0&&(r=ok(i+r,0)),Ie(t)?r<=i&&t.indexOf(e,r)>-1:!!i&&si(t,e,r)>-1}s(lk,"includes");var oe=lk;var uk=Math.max;function ck(t,e,r){var n=t==null?0:t.length;if(!n)return-1;var i=r==null?0:_t(r);return i<0&&(i=uk(n+i,0)),si(t,e,i)}s(ck,"indexOf");var _o=ck;var fk="[object Map]",dk="[object Set]",pk=Object.prototype,mk=pk.hasOwnProperty;function hk(t){if(t==null)return!0;if(Ee(t)&&(N(t)||typeof t=="string"||typeof t.splice=="function"||qt(t)||ui(t)||mr(t)))return!t.length;var e=xt(t);if(e==fk||e==dk)return!t.size;if(Ot(t))return!lo(t).length;for(var r in t)if(mk.call(t,r))return!1;return!0}s(hk,"isEmpty");var D=hk;var gk="[object RegExp]";function yk(t){return ge(t)&&Ke(t)==gk}s(yk,"baseIsRegExp");var Lm=yk;var Pm=ft&&ft.isRegExp,xk=Pm?$t(Pm):Lm,dt=xk;function Tk(t){return t===void 0}s(Tk,"isUndefined");var Ue=Tk;function Ak(t,e){return t<e}s(Ak,"baseLt");var Mm=Ak;function Rk(t,e,r){for(var n=-1,i=t.length;++n<i;){var a=t[n],o=e(a);if(o!=null&&(l===void 0?o===o&&!Ct(o):r(o,l)))var l=o,u=a}return u}s(Rk,"baseExtremum");var Dm=Rk;function Ek(t){return t&&t.length?Dm(t,Ze,Mm):void 0}s(Ek,"min");var Fm=Ek;var vk="Expected a function";function Ik(t){if(typeof t!="function")throw new TypeError(vk);return function(){var e=arguments;switch(e.length){case 0:return!t.call(this);case 1:return!t.call(this,e[0]);case 2:return!t.call(this,e[0],e[1]);case 3:return!t.call(this,e[0],e[1],e[2])}return!t.apply(this,e)}}s(Ik,"negate");var Gm=Ik;function Sk(t,e,r,n){if(!de(t))return t;e=Ar(e,t);for(var i=-1,a=e.length,o=a-1,l=t;l!=null&&++i<a;){var u=Lt(e[i]),c=r;if(u==="__proto__"||u==="constructor"||u==="prototype")return t;if(i!=o){var f=l[u];c=n?n(f,u,l):void 0,c===void 0&&(c=de(f)?f:fr(e[i+1])?[]:{})}dr(l,u,c),l=l[u]}return t}s(Sk,"baseSet");var Um=Sk;function kk(t,e,r){for(var n=-1,i=e.length,a={};++n<i;){var o=e[n],l=mi(t,o);r(l,o)&&Um(a,Ar(o,t),l)}return a}s(kk,"basePickBy");var Bm=kk;function Ck(t,e){if(t==null)return{};var r=Nt(ho(t),function(n){return[n]});return e=he(e),Bm(t,r,function(n,i){return e(n,i[0])})}s(Ck,"pickBy");var tt=Ck;function Nk(t,e,r,n,i){return i(t,function(a,o,l){r=n?(n=!1,a):e(r,a,o,l)}),r}s(Nk,"baseReduce");var jm=Nk;function _k(t,e,r){var n=N(t)?fp:jm,i=arguments.length<3;return n(t,he(e,4),r,i,et)}s(_k,"reduce");var me=_k;function wk(t,e){var r=N(t)?xi:No;return r(t,Gm(he(e,3)))}s(wk,"reject");var Ir=wk;function bk(t,e){var r;return et(t,function(n,i,a){return r=e(n,i,a),!r}),!!r}s(bk,"baseSome");var Wm=bk;function Ok(t,e,r){var n=N(t)?Ao:Wm;return r&&pr(t,e,r)&&(e=void 0),n(t,he(e,3))}s(Ok,"some");var Cs=Ok;var $k=1/0,Lk=Er&&1/Ii(new Er([,-0]))[1]==$k?function(t){return new Er(t)}:pe,Km=Lk;var Pk=200;function Mk(t,e,r){var n=-1,i=so,a=t.length,o=!0,l=[],u=l;if(r)o=!1,i=Co;else if(a>=Pk){var c=e?null:Km(t);if(c)return Ii(c);o=!1,i=vi,u=new Ei}else u=e?[]:l;e:for(;++n<a;){var f=t[n],d=e?e(f):f;if(f=r||f!==0?f:0,o&&d===d){for(var p=u.length;p--;)if(u[p]===d)continue e;e&&u.push(d),l.push(f)}else i(u,d,r)||(u!==l&&u.push(d),l.push(f))}return l}s(Mk,"baseUniq");var wo=Mk;function Dk(t){return t&&t.length?wo(t):[]}s(Dk,"uniq");var ki=Dk;function Fk(t,e){return t&&t.length?wo(t,he(e,2)):[]}s(Fk,"uniqBy");var Hm=Fk;function Ci(t){console&&console.error&&console.error(`Error: ${t}`)}s(Ci,"PRINT_ERROR");function Ns(t){console&&console.warn&&console.warn(`Warning: ${t}`)}s(Ns,"PRINT_WARNING");function _s(t){let e=new Date().getTime(),r=t();return{time:new Date().getTime()-e,value:r}}s(_s,"timer");function ws(t){function e(){}s(e,"FakeConstructor"),e.prototype=t;let r=new e;function n(){return typeof r.bar}return s(n,"fakeAccess"),n(),n(),t;(0,eval)(t)}s(ws,"toFastProperties");function Gk(t){return Uk(t)?t.LABEL:t.name}s(Gk,"tokenLabel");function Uk(t){return Ie(t.LABEL)&&t.LABEL!==""}s(Uk,"hasTokenLabel");var at=class{static{s(this,"AbstractProduction")}get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){this._definition=e}accept(e){e.visit(this),S(this.definition,r=>{r.accept(e)})}},V=class extends at{static{s(this,"NonTerminal")}constructor(e){super([]),this.idx=1,Oe(this,tt(e,r=>r!==void 0))}set definition(e){}get definition(){return this.referencedRule!==void 0?this.referencedRule.definition:[]}accept(e){e.visit(this)}},Ve=class extends at{static{s(this,"Rule")}constructor(e){super(e.definition),this.orgText="",Oe(this,tt(e,r=>r!==void 0))}},te=class extends at{static{s(this,"Alternative")}constructor(e){super(e.definition),this.ignoreAmbiguities=!1,Oe(this,tt(e,r=>r!==void 0))}},z=class extends at{static{s(this,"Option")}constructor(e){super(e.definition),this.idx=1,Oe(this,tt(e,r=>r!==void 0))}},re=class extends at{static{s(this,"RepetitionMandatory")}constructor(e){super(e.definition),this.idx=1,Oe(this,tt(e,r=>r!==void 0))}},ne=class extends at{static{s(this,"RepetitionMandatoryWithSeparator")}constructor(e){super(e.definition),this.idx=1,Oe(this,tt(e,r=>r!==void 0))}},G=class extends at{static{s(this,"Repetition")}constructor(e){super(e.definition),this.idx=1,Oe(this,tt(e,r=>r!==void 0))}},X=class extends at{static{s(this,"RepetitionWithSeparator")}constructor(e){super(e.definition),this.idx=1,Oe(this,tt(e,r=>r!==void 0))}},Y=class extends at{static{s(this,"Alternation")}get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){super(e.definition),this.idx=1,this.ignoreAmbiguities=!1,this.hasPredicates=!1,Oe(this,tt(e,r=>r!==void 0))}},F=class{static{s(this,"Terminal")}constructor(e){this.idx=1,Oe(this,tt(e,r=>r!==void 0))}accept(e){e.visit(this)}};function bo(t){return v(t,Ni)}s(bo,"serializeGrammar");function Ni(t){function e(r){return v(r,Ni)}if(s(e,"convertDefinition"),t instanceof V){let r={type:"NonTerminal",name:t.nonTerminalName,idx:t.idx};return Ie(t.label)&&(r.label=t.label),r}else{if(t instanceof te)return{type:"Alternative",definition:e(t.definition)};if(t instanceof z)return{type:"Option",idx:t.idx,definition:e(t.definition)};if(t instanceof re)return{type:"RepetitionMandatory",idx:t.idx,definition:e(t.definition)};if(t instanceof ne)return{type:"RepetitionMandatoryWithSeparator",idx:t.idx,separator:Ni(new F({terminalType:t.separator})),definition:e(t.definition)};if(t instanceof X)return{type:"RepetitionWithSeparator",idx:t.idx,separator:Ni(new F({terminalType:t.separator})),definition:e(t.definition)};if(t instanceof G)return{type:"Repetition",idx:t.idx,definition:e(t.definition)};if(t instanceof Y)return{type:"Alternation",idx:t.idx,definition:e(t.definition)};if(t instanceof F){let r={type:"Terminal",name:t.terminalType.name,label:Gk(t.terminalType),idx:t.idx};Ie(t.label)&&(r.terminalLabel=t.label);let n=t.terminalType.PATTERN;return t.terminalType.PATTERN&&(r.pattern=dt(n)?n.source:n),r}else{if(t instanceof Ve)return{type:"Rule",name:t.name,orgText:t.orgText,definition:e(t.definition)};throw Error("non exhaustive match")}}}s(Ni,"serializeProduction");var ze=class{static{s(this,"GAstVisitor")}visit(e){let r=e;switch(r.constructor){case V:return this.visitNonTerminal(r);case te:return this.visitAlternative(r);case z:return this.visitOption(r);case re:return this.visitRepetitionMandatory(r);case ne:return this.visitRepetitionMandatoryWithSeparator(r);case X:return this.visitRepetitionWithSeparator(r);case G:return this.visitRepetition(r);case Y:return this.visitAlternation(r);case F:return this.visitTerminal(r);case Ve:return this.visitRule(r);default:throw Error("non exhaustive match")}}visitNonTerminal(e){}visitAlternative(e){}visitOption(e){}visitRepetition(e){}visitRepetitionMandatory(e){}visitRepetitionMandatoryWithSeparator(e){}visitRepetitionWithSeparator(e){}visitAlternation(e){}visitTerminal(e){}visitRule(e){}};function qu(t){return t instanceof te||t instanceof z||t instanceof G||t instanceof re||t instanceof ne||t instanceof X||t instanceof F||t instanceof Ve}s(qu,"isSequenceProd");function zr(t,e=[]){return t instanceof z||t instanceof G||t instanceof X?!0:t instanceof Y?Cs(t.definition,n=>zr(n,e)):t instanceof V&&oe(e,t)?!1:t instanceof at?(t instanceof V&&e.push(t),Ge(t.definition,n=>zr(n,e))):!1}s(zr,"isOptionalProd");function Xu(t){return t instanceof Y}s(Xu,"isBranchingProd");function rt(t){if(t instanceof V)return"SUBRULE";if(t instanceof z)return"OPTION";if(t instanceof Y)return"OR";if(t instanceof re)return"AT_LEAST_ONE";if(t instanceof ne)return"AT_LEAST_ONE_SEP";if(t instanceof X)return"MANY_SEP";if(t instanceof G)return"MANY";if(t instanceof F)return"CONSUME";throw Error("non exhaustive match")}s(rt,"getProductionDslName");var Jt=class{static{s(this,"RestWalker")}walk(e,r=[]){S(e.definition,(n,i)=>{let a=xe(e.definition,i+1);if(n instanceof V)this.walkProdRef(n,a,r);else if(n instanceof F)this.walkTerminal(n,a,r);else if(n instanceof te)this.walkFlat(n,a,r);else if(n instanceof z)this.walkOption(n,a,r);else if(n instanceof re)this.walkAtLeastOne(n,a,r);else if(n instanceof ne)this.walkAtLeastOneSep(n,a,r);else if(n instanceof X)this.walkManySep(n,a,r);else if(n instanceof G)this.walkMany(n,a,r);else if(n instanceof Y)this.walkOr(n,a,r);else throw Error("non exhaustive match")})}walkTerminal(e,r,n){}walkProdRef(e,r,n){}walkFlat(e,r,n){let i=r.concat(n);this.walk(e,i)}walkOption(e,r,n){let i=r.concat(n);this.walk(e,i)}walkAtLeastOne(e,r,n){let i=[new z({definition:e.definition})].concat(r,n);this.walk(e,i)}walkAtLeastOneSep(e,r,n){let i=Vm(e,r,n);this.walk(e,i)}walkMany(e,r,n){let i=[new z({definition:e.definition})].concat(r,n);this.walk(e,i)}walkManySep(e,r,n){let i=Vm(e,r,n);this.walk(e,i)}walkOr(e,r,n){let i=r.concat(n);S(e.definition,a=>{let o=new te({definition:[a]});this.walk(o,i)})}};function Vm(t,e,r){return[new z({definition:[new F({terminalType:t.separator})].concat(t.definition)})].concat(e,r)}s(Vm,"restForRepetitionWithSeparator");function qr(t){if(t instanceof V)return qr(t.referencedRule);if(t instanceof F)return Wk(t);if(qu(t))return Bk(t);if(Xu(t))return jk(t);throw Error("non exhaustive match")}s(qr,"first");function Bk(t){let e=[],r=t.definition,n=0,i=r.length>n,a,o=!0;for(;i&&o;)a=r[n],o=zr(a),e=e.concat(qr(a)),n=n+1,i=r.length>n;return ki(e)}s(Bk,"firstForSequence");function jk(t){let e=v(t.definition,r=>qr(r));return ki(ye(e))}s(jk,"firstForBranching");function Wk(t){return[t.terminalType]}s(Wk,"firstForTerminal");var Oo="_~IN~_";var Yu=class extends Jt{static{s(this,"ResyncFollowsWalker")}constructor(e){super(),this.topProd=e,this.follows={}}startWalking(){return this.walk(this.topProd),this.follows}walkTerminal(e,r,n){}walkProdRef(e,r,n){let i=Kk(e.referencedRule,e.idx)+this.topProd.name,a=r.concat(n),o=new te({definition:a}),l=qr(o);this.follows[i]=l}};function zm(t){let e={};return S(t,r=>{let n=new Yu(r).startWalking();Oe(e,n)}),e}s(zm,"computeAllProdsFollows");function Kk(t,e){return t.name+e+Oo}s(Kk,"buildBetweenProdsFollowPrefix");var $o={},Hk=new jr;function _i(t){let e=t.toString();if($o.hasOwnProperty(e))return $o[e];{let r=Hk.pattern(e);return $o[e]=r,r}}s(_i,"getRegExpAst");function qm(){$o={}}s(qm,"clearRegExpParserCache");var Ym="Complement Sets are not supported for first char optimization",bs=`Unable to use "first char" lexer optimizations:
`;function Jm(t,e=!1){try{let r=_i(t);return Ju(r.value,{},r.flags.ignoreCase)}catch(r){if(r.message===Ym)e&&Ns(`${bs}	Unable to optimize: < ${t.toString()} >
	Complement Sets cannot be automatically optimized.
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#COMPLEMENT for details.`);else{let n="";e&&(n=`
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#REGEXP_PARSING for details.`),Ci(`${bs}
	Failed parsing: < ${t.toString()} >
	Using the @chevrotain/regexp-to-ast library
	Please open an issue at: https://github.com/chevrotain/chevrotain/issues`+n)}}return[]}s(Jm,"getOptimizedStartCodesIndices");function Ju(t,e,r){switch(t.type){case"Disjunction":for(let i=0;i<t.value.length;i++)Ju(t.value[i],e,r);break;case"Alternative":let n=t.value;for(let i=0;i<n.length;i++){let a=n[i];switch(a.type){case"EndAnchor":case"GroupBackReference":case"Lookahead":case"NegativeLookahead":case"StartAnchor":case"WordBoundary":case"NonWordBoundary":continue}let o=a;switch(o.type){case"Character":Lo(o.value,e,r);break;case"Set":if(o.complement===!0)throw Error(Ym);S(o.value,u=>{if(typeof u=="number")Lo(u,e,r);else{let c=u;if(r===!0)for(let f=c.from;f<=c.to;f++)Lo(f,e,r);else{for(let f=c.from;f<=c.to&&f<wi;f++)Lo(f,e,r);if(c.to>=wi){let f=c.from>=wi?c.from:wi,d=c.to,p=Ft(f),m=Ft(d);for(let g=p;g<=m;g++)e[g]=g}}}});break;case"Group":Ju(o.value,e,r);break;default:throw Error("Non Exhaustive Match")}let l=o.quantifier!==void 0&&o.quantifier.atLeast===0;if(o.type==="Group"&&Zu(o)===!1||o.type!=="Group"&&l===!1)break}break;default:throw Error("non exhaustive match!")}return q(e)}s(Ju,"firstCharOptimizedIndices");function Lo(t,e,r){let n=Ft(t);e[n]=n,r===!0&&Vk(t,e)}s(Lo,"addOptimizedIdxToResult");function Vk(t,e){let r=String.fromCharCode(t),n=r.toUpperCase();if(n!==r){let i=Ft(n.charCodeAt(0));e[i]=i}else{let i=r.toLowerCase();if(i!==r){let a=Ft(i.charCodeAt(0));e[a]=a}}}s(Vk,"handleIgnoreCase");function Xm(t,e){return Dt(t.value,r=>{if(typeof r=="number")return oe(e,r);{let n=r;return Dt(e,i=>n.from<=i&&i<=n.to)!==void 0}})}s(Xm,"findCode");function Zu(t){let e=t.quantifier;return e&&e.atLeast===0?!0:t.value?N(t.value)?Ge(t.value,Zu):Zu(t.value):!1}s(Zu,"isWholeOptional");var Qu=class extends kt{static{s(this,"CharCodeFinder")}constructor(e){super(),this.targetCharCodes=e,this.found=!1}visitChildren(e){if(this.found!==!0){switch(e.type){case"Lookahead":this.visitLookahead(e);return;case"NegativeLookahead":this.visitNegativeLookahead(e);return}super.visitChildren(e)}}visitCharacter(e){oe(this.targetCharCodes,e.value)&&(this.found=!0)}visitSet(e){e.complement?Xm(e,this.targetCharCodes)===void 0&&(this.found=!0):Xm(e,this.targetCharCodes)!==void 0&&(this.found=!0)}};function Po(t,e){if(e instanceof RegExp){let r=_i(e),n=new Qu(t);return n.visit(r),n.found}else return Dt(e,r=>oe(t,r.charCodeAt(0)))!==void 0}s(Po,"canMatchCharCode");var Xr="PATTERN",bi="defaultMode",Mo="modes",tc=typeof new RegExp("(?:)").sticky=="boolean";function eh(t,e){e=Si(e,{useSticky:tc,debug:!1,safeMode:!1,positionTracking:"full",lineTerminatorCharacters:["\r",`
`],tracer:s((E,A)=>A(),"tracer")});let r=e.tracer;r("initCharCodeToOptimizedIndexMap",()=>{cC()});let n;r("Reject Lexer.NA",()=>{n=Ir(t,E=>E[Xr]===ue.NA)});let i=!1,a;r("Transform Patterns",()=>{i=!1,a=v(n,E=>{let A=E[Xr];if(dt(A)){let $=A.source;return $.length===1&&$!=="^"&&$!=="$"&&$!=="."&&!A.ignoreCase?$:$.length===2&&$[0]==="\\"&&!oe(["d","D","s","S","t","r","n","t","0","c","b","B","f","v","w","W"],$[1])?$[1]:e.useSticky?Qm(A):Zm(A)}else{if(He(A))return i=!0,{exec:A};if(typeof A=="object")return i=!0,A;if(typeof A=="string"){if(A.length===1)return A;{let $=A.replace(/[\\^$.*+?()[\]{}|]/g,"\\$&"),L=new RegExp($);return e.useSticky?Qm(L):Zm(L)}}else throw Error("non exhaustive match")}})});let o,l,u,c,f;r("misc mapping",()=>{o=v(n,E=>E.tokenTypeIdx),l=v(n,E=>{let A=E.GROUP;if(A!==ue.SKIPPED){if(Ie(A))return A;if(Ue(A))return!1;throw Error("non exhaustive match")}}),u=v(n,E=>{let A=E.LONGER_ALT;if(A)return N(A)?v(A,L=>_o(n,L)):[_o(n,A)]}),c=v(n,E=>E.PUSH_MODE),f=v(n,E=>k(E,"POP_MODE"))});let d;r("Line Terminator Handling",()=>{let E=lh(e.lineTerminatorCharacters);d=v(n,A=>!1),e.positionTracking!=="onlyOffset"&&(d=v(n,A=>k(A,"LINE_BREAKS")?!!A.LINE_BREAKS:oh(A,E)===!1&&Po(E,A.PATTERN)))});let p,m,g,y;r("Misc Mapping #2",()=>{p=v(n,sh),m=v(a,lC),g=me(n,(E,A)=>{let $=A.GROUP;return Ie($)&&$!==ue.SKIPPED&&(E[$]=[]),E},{}),y=v(a,(E,A)=>({pattern:a[A],longerAlt:u[A],canLineTerminator:d[A],isCustom:p[A],short:m[A],group:l[A],push:c[A],pop:f[A],tokenTypeIdx:o[A],tokenType:n[A]}))});let R=!0,T=[];return e.safeMode||r("First Char Optimization",()=>{T=me(n,(E,A,$)=>{if(typeof A.PATTERN=="string"){let L=A.PATTERN.charCodeAt(0),ke=Ft(L);ec(E,ke,y[$])}else if(N(A.START_CHARS_HINT)){let L;S(A.START_CHARS_HINT,ke=>{let un=typeof ke=="string"?ke.charCodeAt(0):ke,Le=Ft(un);L!==Le&&(L=Le,ec(E,Le,y[$]))})}else if(dt(A.PATTERN))if(A.PATTERN.unicode)R=!1,e.ensureOptimizations&&Ci(`${bs}	Unable to analyze < ${A.PATTERN.toString()} > pattern.
	The regexp unicode flag is not currently supported by the regexp-to-ast library.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNICODE_OPTIMIZE`);else{let L=Jm(A.PATTERN,e.ensureOptimizations);D(L)&&(R=!1),S(L,ke=>{ec(E,ke,y[$])})}else e.ensureOptimizations&&Ci(`${bs}	TokenType: <${A.name}> is using a custom token pattern without providing <start_chars_hint> parameter.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_OPTIMIZE`),R=!1;return E},[])}),{emptyGroups:g,patternIdxToConfig:y,charCodeToPatternIdxToConfig:T,hasCustom:i,canBeOptimized:R}}s(eh,"analyzeTokenTypes");function th(t,e){let r=[],n=qk(t);r=r.concat(n.errors);let i=Xk(n.valid),a=i.valid;return r=r.concat(i.errors),r=r.concat(zk(a)),r=r.concat(nC(a)),r=r.concat(iC(a,e)),r=r.concat(sC(a)),r}s(th,"validatePatterns");function zk(t){let e=[],r=Ne(t,n=>dt(n[Xr]));return e=e.concat(Jk(r)),e=e.concat(eC(r)),e=e.concat(tC(r)),e=e.concat(rC(r)),e=e.concat(Zk(r)),e}s(zk,"validateRegExpPattern");function qk(t){let e=Ne(t,i=>!k(i,Xr)),r=v(e,i=>({message:"Token Type: ->"+i.name+"<- missing static 'PATTERN' property",type:le.MISSING_PATTERN,tokenTypes:[i]})),n=vr(t,e);return{errors:r,valid:n}}s(qk,"findMissingPatterns");function Xk(t){let e=Ne(t,i=>{let a=i[Xr];return!dt(a)&&!He(a)&&!k(a,"exec")&&!Ie(a)}),r=v(e,i=>({message:"Token Type: ->"+i.name+"<- static 'PATTERN' can only be a RegExp, a Function matching the {CustomPatternMatcherFunc} type or an Object matching the {ICustomPattern} interface.",type:le.INVALID_PATTERN,tokenTypes:[i]})),n=vr(t,e);return{errors:r,valid:n}}s(Xk,"findInvalidPatterns");var Yk=/[^\\][$]/;function Jk(t){class e extends kt{static{s(this,"EndAnchorFinder")}constructor(){super(...arguments),this.found=!1}visitEndAnchor(a){this.found=!0}}let r=Ne(t,i=>{let a=i.PATTERN;try{let o=_i(a),l=new e;return l.visit(o),l.found}catch{return Yk.test(a.source)}});return v(r,i=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+i.name+`<- static 'PATTERN' cannot contain end of input anchor '$'
	See chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:le.EOI_ANCHOR_FOUND,tokenTypes:[i]}))}s(Jk,"findEndOfInputAnchor");function Zk(t){let e=Ne(t,n=>n.PATTERN.test(""));return v(e,n=>({message:"Token Type: ->"+n.name+"<- static 'PATTERN' must not match an empty string",type:le.EMPTY_MATCH_PATTERN,tokenTypes:[n]}))}s(Zk,"findEmptyMatchRegExps");var Qk=/[^\\[][\^]|^\^/;function eC(t){class e extends kt{static{s(this,"StartAnchorFinder")}constructor(){super(...arguments),this.found=!1}visitStartAnchor(a){this.found=!0}}let r=Ne(t,i=>{let a=i.PATTERN;try{let o=_i(a),l=new e;return l.visit(o),l.found}catch{return Qk.test(a.source)}});return v(r,i=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+i.name+`<- static 'PATTERN' cannot contain start of input anchor '^'
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:le.SOI_ANCHOR_FOUND,tokenTypes:[i]}))}s(eC,"findStartOfInputAnchor");function tC(t){let e=Ne(t,n=>{let i=n[Xr];return i instanceof RegExp&&(i.multiline||i.global)});return v(e,n=>({message:"Token Type: ->"+n.name+"<- static 'PATTERN' may NOT contain global('g') or multiline('m')",type:le.UNSUPPORTED_FLAGS_FOUND,tokenTypes:[n]}))}s(tC,"findUnsupportedFlags");function rC(t){let e=[],r=v(t,a=>me(t,(o,l)=>(a.PATTERN.source===l.PATTERN.source&&!oe(e,l)&&l.PATTERN!==ue.NA&&(e.push(l),o.push(l)),o),[]));r=Pt(r);let n=Ne(r,a=>a.length>1);return v(n,a=>{let o=v(a,u=>u.name);return{message:`The same RegExp pattern ->${_e(a).PATTERN}<-has been used in all of the following Token Types: ${o.join(", ")} <-`,type:le.DUPLICATE_PATTERNS_FOUND,tokenTypes:a}})}s(rC,"findDuplicatePatterns");function nC(t){let e=Ne(t,n=>{if(!k(n,"GROUP"))return!1;let i=n.GROUP;return i!==ue.SKIPPED&&i!==ue.NA&&!Ie(i)});return v(e,n=>({message:"Token Type: ->"+n.name+"<- static 'GROUP' can only be Lexer.SKIPPED/Lexer.NA/A String",type:le.INVALID_GROUP_TYPE_FOUND,tokenTypes:[n]}))}s(nC,"findInvalidGroupType");function iC(t,e){let r=Ne(t,i=>i.PUSH_MODE!==void 0&&!oe(e,i.PUSH_MODE));return v(r,i=>({message:`Token Type: ->${i.name}<- static 'PUSH_MODE' value cannot refer to a Lexer Mode ->${i.PUSH_MODE}<-which does not exist`,type:le.PUSH_MODE_DOES_NOT_EXIST,tokenTypes:[i]}))}s(iC,"findModesThatDoNotExist");function sC(t){let e=[],r=me(t,(n,i,a)=>{let o=i.PATTERN;return o===ue.NA||(Ie(o)?n.push({str:o,idx:a,tokenType:i}):dt(o)&&oC(o)&&n.push({str:o.source,idx:a,tokenType:i})),n},[]);return S(t,(n,i)=>{S(r,({str:a,idx:o,tokenType:l})=>{if(i<o&&aC(a,n.PATTERN)){let u=`Token: ->${l.name}<- can never be matched.
Because it appears AFTER the Token Type ->${n.name}<-in the lexer's definition.
See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNREACHABLE`;e.push({message:u,type:le.UNREACHABLE_PATTERN,tokenTypes:[n,l]})}})}),e}s(sC,"findUnreachablePatterns");function aC(t,e){if(dt(e)){let r=e.exec(t);return r!==null&&r.index===0}else{if(He(e))return e(t,0,[],{});if(k(e,"exec"))return e.exec(t,0,[],{});if(typeof e=="string")return e===t;throw Error("non exhaustive match")}}s(aC,"testTokenType");function oC(t){return Dt([".","\\","[","]","|","^","$","(",")","?","*","+","{"],r=>t.source.indexOf(r)!==-1)===void 0}s(oC,"noMetaChar");function Zm(t){let e=t.ignoreCase?"i":"";return new RegExp(`^(?:${t.source})`,e)}s(Zm,"addStartOfInput");function Qm(t){let e=t.ignoreCase?"iy":"y";return new RegExp(`${t.source}`,e)}s(Qm,"addStickyFlag");function rh(t,e,r){let n=[];return k(t,bi)||n.push({message:"A MultiMode Lexer cannot be initialized without a <"+bi+`> property in its definition
`,type:le.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE}),k(t,Mo)||n.push({message:"A MultiMode Lexer cannot be initialized without a <"+Mo+`> property in its definition
`,type:le.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY}),k(t,Mo)&&k(t,bi)&&!k(t.modes,t.defaultMode)&&n.push({message:`A MultiMode Lexer cannot be initialized with a ${bi}: <${t.defaultMode}>which does not exist
`,type:le.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST}),k(t,Mo)&&S(t.modes,(i,a)=>{S(i,(o,l)=>{if(Ue(o))n.push({message:`A Lexer cannot be initialized using an undefined Token Type. Mode:<${a}> at index: <${l}>
`,type:le.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED});else if(k(o,"LONGER_ALT")){let u=N(o.LONGER_ALT)?o.LONGER_ALT:[o.LONGER_ALT];S(u,c=>{!Ue(c)&&!oe(i,c)&&n.push({message:`A MultiMode Lexer cannot be initialized with a longer_alt <${c.name}> on token <${o.name}> outside of mode <${a}>
`,type:le.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE})})}})}),n}s(rh,"performRuntimeChecks");function nh(t,e,r){let n=[],i=!1,a=Pt(ye(q(t.modes))),o=Ir(a,u=>u[Xr]===ue.NA),l=lh(r);return e&&S(o,u=>{let c=oh(u,l);if(c!==!1){let d={message:uC(u,c),type:c.issue,tokenType:u};n.push(d)}else k(u,"LINE_BREAKS")?u.LINE_BREAKS===!0&&(i=!0):Po(l,u.PATTERN)&&(i=!0)}),e&&!i&&n.push({message:`Warning: No LINE_BREAKS Found.
	This Lexer has been defined to track line and column information,
	But none of the Token Types can be identified as matching a line terminator.
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#LINE_BREAKS 
	for details.`,type:le.NO_LINE_BREAKS_FLAGS}),n}s(nh,"performWarningRuntimeChecks");function ih(t){let e={},r=Z(t);return S(r,n=>{let i=t[n];if(N(i))e[n]=[];else throw Error("non exhaustive match")}),e}s(ih,"cloneEmptyGroups");function sh(t){let e=t.PATTERN;if(dt(e))return!1;if(He(e))return!0;if(k(e,"exec"))return!0;if(Ie(e))return!1;throw Error("non exhaustive match")}s(sh,"isCustomPattern");function lC(t){return Ie(t)&&t.length===1?t.charCodeAt(0):!1}s(lC,"isShortPattern");var ah={test:s(function(t){let e=t.length;for(let r=this.lastIndex;r<e;r++){let n=t.charCodeAt(r);if(n===10)return this.lastIndex=r+1,!0;if(n===13)return t.charCodeAt(r+1)===10?this.lastIndex=r+2:this.lastIndex=r+1,!0}return!1},"test"),lastIndex:0};function oh(t,e){if(k(t,"LINE_BREAKS"))return!1;if(dt(t.PATTERN)){try{Po(e,t.PATTERN)}catch(r){return{issue:le.IDENTIFY_TERMINATOR,errMsg:r.message}}return!1}else{if(Ie(t.PATTERN))return!1;if(sh(t))return{issue:le.CUSTOM_LINE_BREAK};throw Error("non exhaustive match")}}s(oh,"checkLineBreaksIssues");function uC(t,e){if(e.issue===le.IDENTIFY_TERMINATOR)return`Warning: unable to identify line terminator usage in pattern.
	The problem is in the <${t.name}> Token Type
	 Root cause: ${e.errMsg}.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#IDENTIFY_TERMINATOR`;if(e.issue===le.CUSTOM_LINE_BREAK)return`Warning: A Custom Token Pattern should specify the <line_breaks> option.
	The problem is in the <${t.name}> Token Type
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_LINE_BREAK`;throw Error("non exhaustive match")}s(uC,"buildLineBreakIssueMessage");function lh(t){return v(t,r=>Ie(r)?r.charCodeAt(0):r)}s(lh,"getCharCodes");function ec(t,e,r){t[e]===void 0?t[e]=[r]:t[e].push(r)}s(ec,"addToMapOfArrays");var wi=256,Do=[];function Ft(t){return t<wi?t:Do[t]}s(Ft,"charCodeToOptimizedIndex");function cC(){if(D(Do)){Do=new Array(65536);for(let t=0;t<65536;t++)Do[t]=t>255?255+~~(t/255):t}}s(cC,"initCharCodeToOptimizedIndexMap");function Zt(t,e){let r=t.tokenTypeIdx;return r===e.tokenTypeIdx?!0:e.isParent===!0&&e.categoryMatchesMap[r]===!0}s(Zt,"tokenStructuredMatcher");function Oi(t,e){return t.tokenTypeIdx===e.tokenTypeIdx}s(Oi,"tokenStructuredMatcherNoCategories");var uh=1,fh={};function Qt(t){let e=fC(t);dC(e),mC(e),pC(e),S(e,r=>{r.isParent=r.categoryMatches.length>0})}s(Qt,"augmentTokenTypes");function fC(t){let e=ee(t),r=t,n=!0;for(;n;){r=Pt(ye(v(r,a=>a.CATEGORIES)));let i=vr(r,e);e=e.concat(i),D(i)?n=!1:r=i}return e}s(fC,"expandCategories");function dC(t){S(t,e=>{rc(e)||(fh[uh]=e,e.tokenTypeIdx=uh++),ch(e)&&!N(e.CATEGORIES)&&(e.CATEGORIES=[e.CATEGORIES]),ch(e)||(e.CATEGORIES=[]),hC(e)||(e.categoryMatches=[]),gC(e)||(e.categoryMatchesMap={})})}s(dC,"assignTokenDefaultProps");function pC(t){S(t,e=>{e.categoryMatches=[],S(e.categoryMatchesMap,(r,n)=>{e.categoryMatches.push(fh[n].tokenTypeIdx)})})}s(pC,"assignCategoriesTokensProp");function mC(t){S(t,e=>{dh([],e)})}s(mC,"assignCategoriesMapProp");function dh(t,e){S(t,r=>{e.categoryMatchesMap[r.tokenTypeIdx]=!0}),S(e.CATEGORIES,r=>{let n=t.concat(e);oe(n,r)||dh(n,r)})}s(dh,"singleAssignCategoriesToksMap");function rc(t){return k(t,"tokenTypeIdx")}s(rc,"hasShortKeyProperty");function ch(t){return k(t,"CATEGORIES")}s(ch,"hasCategoriesProperty");function hC(t){return k(t,"categoryMatches")}s(hC,"hasExtendingTokensTypesProperty");function gC(t){return k(t,"categoryMatchesMap")}s(gC,"hasExtendingTokensTypesMapProperty");function ph(t){return k(t,"tokenTypeIdx")}s(ph,"isTokenType");var $i={buildUnableToPopLexerModeMessage(t){return`Unable to pop Lexer Mode after encountering Token ->${t.image}<- The Mode Stack is empty`},buildUnexpectedCharactersMessage(t,e,r,n,i){return`unexpected character: ->${t.charAt(e)}<- at offset: ${e}, skipped ${r} characters.`}};var le;(function(t){t[t.MISSING_PATTERN=0]="MISSING_PATTERN",t[t.INVALID_PATTERN=1]="INVALID_PATTERN",t[t.EOI_ANCHOR_FOUND=2]="EOI_ANCHOR_FOUND",t[t.UNSUPPORTED_FLAGS_FOUND=3]="UNSUPPORTED_FLAGS_FOUND",t[t.DUPLICATE_PATTERNS_FOUND=4]="DUPLICATE_PATTERNS_FOUND",t[t.INVALID_GROUP_TYPE_FOUND=5]="INVALID_GROUP_TYPE_FOUND",t[t.PUSH_MODE_DOES_NOT_EXIST=6]="PUSH_MODE_DOES_NOT_EXIST",t[t.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE=7]="MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE",t[t.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY=8]="MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY",t[t.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST=9]="MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST",t[t.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED=10]="LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED",t[t.SOI_ANCHOR_FOUND=11]="SOI_ANCHOR_FOUND",t[t.EMPTY_MATCH_PATTERN=12]="EMPTY_MATCH_PATTERN",t[t.NO_LINE_BREAKS_FLAGS=13]="NO_LINE_BREAKS_FLAGS",t[t.UNREACHABLE_PATTERN=14]="UNREACHABLE_PATTERN",t[t.IDENTIFY_TERMINATOR=15]="IDENTIFY_TERMINATOR",t[t.CUSTOM_LINE_BREAK=16]="CUSTOM_LINE_BREAK",t[t.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE=17]="MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE"})(le||(le={}));var Os={deferDefinitionErrorsHandling:!1,positionTracking:"full",lineTerminatorsPattern:/\n|\r\n?/g,lineTerminatorCharacters:[`
`,"\r"],ensureOptimizations:!1,safeMode:!1,errorMessageProvider:$i,traceInitPerf:!1,skipValidations:!1,recoveryEnabled:!0};Object.freeze(Os);var ue=class{static{s(this,"Lexer")}constructor(e,r=Os){if(this.lexerDefinition=e,this.lexerDefinitionErrors=[],this.lexerDefinitionWarning=[],this.patternIdxToConfig={},this.charCodeToPatternIdxToConfig={},this.modes=[],this.emptyGroups={},this.trackStartLines=!0,this.trackEndLines=!0,this.hasCustom=!1,this.canModeBeOptimized={},this.TRACE_INIT=(i,a)=>{if(this.traceInitPerf===!0){this.traceInitIndent++;let o=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${o}--> <${i}>`);let{time:l,value:u}=_s(a),c=l>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&c(`${o}<-- <${i}> time: ${l}ms`),this.traceInitIndent--,u}else return a()},typeof r=="boolean")throw Error(`The second argument to the Lexer constructor is now an ILexerConfig Object.
a boolean 2nd argument is no longer supported`);this.config=Oe({},Os,r);let n=this.config.traceInitPerf;n===!0?(this.traceInitMaxIdent=1/0,this.traceInitPerf=!0):typeof n=="number"&&(this.traceInitMaxIdent=n,this.traceInitPerf=!0),this.traceInitIndent=-1,this.TRACE_INIT("Lexer Constructor",()=>{let i,a=!0;this.TRACE_INIT("Lexer Config handling",()=>{if(this.config.lineTerminatorsPattern===Os.lineTerminatorsPattern)this.config.lineTerminatorsPattern=ah;else if(this.config.lineTerminatorCharacters===Os.lineTerminatorCharacters)throw Error(`Error: Missing <lineTerminatorCharacters> property on the Lexer config.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#MISSING_LINE_TERM_CHARS`);if(r.safeMode&&r.ensureOptimizations)throw Error('"safeMode" and "ensureOptimizations" flags are mutually exclusive.');this.trackStartLines=/full|onlyStart/i.test(this.config.positionTracking),this.trackEndLines=/full/i.test(this.config.positionTracking),N(e)?i={modes:{defaultMode:ee(e)},defaultMode:bi}:(a=!1,i=ee(e))}),this.config.skipValidations===!1&&(this.TRACE_INIT("performRuntimeChecks",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(rh(i,this.trackStartLines,this.config.lineTerminatorCharacters))}),this.TRACE_INIT("performWarningRuntimeChecks",()=>{this.lexerDefinitionWarning=this.lexerDefinitionWarning.concat(nh(i,this.trackStartLines,this.config.lineTerminatorCharacters))})),i.modes=i.modes?i.modes:{},S(i.modes,(l,u)=>{i.modes[u]=Ir(l,c=>Ue(c))});let o=Z(i.modes);if(S(i.modes,(l,u)=>{this.TRACE_INIT(`Mode: <${u}> processing`,()=>{if(this.modes.push(u),this.config.skipValidations===!1&&this.TRACE_INIT("validatePatterns",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(th(l,o))}),D(this.lexerDefinitionErrors)){Qt(l);let c;this.TRACE_INIT("analyzeTokenTypes",()=>{c=eh(l,{lineTerminatorCharacters:this.config.lineTerminatorCharacters,positionTracking:r.positionTracking,ensureOptimizations:r.ensureOptimizations,safeMode:r.safeMode,tracer:this.TRACE_INIT})}),this.patternIdxToConfig[u]=c.patternIdxToConfig,this.charCodeToPatternIdxToConfig[u]=c.charCodeToPatternIdxToConfig,this.emptyGroups=Oe({},this.emptyGroups,c.emptyGroups),this.hasCustom=c.hasCustom||this.hasCustom,this.canModeBeOptimized[u]=c.canBeOptimized}})}),this.defaultMode=i.defaultMode,!D(this.lexerDefinitionErrors)&&!this.config.deferDefinitionErrorsHandling){let u=v(this.lexerDefinitionErrors,c=>c.message).join(`-----------------------
`);throw new Error(`Errors detected in definition of Lexer:
`+u)}S(this.lexerDefinitionWarning,l=>{Ns(l.message)}),this.TRACE_INIT("Choosing sub-methods implementations",()=>{if(tc?(this.chopInput=Ze,this.match=this.matchWithTest):(this.updateLastIndex=pe,this.match=this.matchWithExec),a&&(this.handleModes=pe),this.trackStartLines===!1&&(this.computeNewColumn=Ze),this.trackEndLines===!1&&(this.updateTokenEndLineColumnLocation=pe),/full/i.test(this.config.positionTracking))this.createTokenInstance=this.createFullToken;else if(/onlyStart/i.test(this.config.positionTracking))this.createTokenInstance=this.createStartOnlyToken;else if(/onlyOffset/i.test(this.config.positionTracking))this.createTokenInstance=this.createOffsetOnlyToken;else throw Error(`Invalid <positionTracking> config option: "${this.config.positionTracking}"`);this.hasCustom?(this.addToken=this.addTokenUsingPush,this.handlePayload=this.handlePayloadWithCustom):(this.addToken=this.addTokenUsingMemberAccess,this.handlePayload=this.handlePayloadNoCustom)}),this.TRACE_INIT("Failed Optimization Warnings",()=>{let l=me(this.canModeBeOptimized,(u,c,f)=>(c===!1&&u.push(f),u),[]);if(r.ensureOptimizations&&!D(l))throw Error(`Lexer Modes: < ${l.join(", ")} > cannot be optimized.
	 Disable the "ensureOptimizations" lexer config flag to silently ignore this and run the lexer in an un-optimized mode.
	 Or inspect the console log for details on how to resolve these issues.`)}),this.TRACE_INIT("clearRegExpParserCache",()=>{qm()}),this.TRACE_INIT("toFastProperties",()=>{ws(this)})})}tokenize(e,r=this.defaultMode){if(!D(this.lexerDefinitionErrors)){let i=v(this.lexerDefinitionErrors,a=>a.message).join(`-----------------------
`);throw new Error(`Unable to Tokenize because Errors detected in definition of Lexer:
`+i)}return this.tokenizeInternal(e,r)}tokenizeInternal(e,r){let n,i,a,o,l,u,c,f,d,p,m,g,y,R,T,E,A=e,$=A.length,L=0,ke=0,un=this.hasCustom?0:Math.floor(e.length/10),Le=new Array(un),or=[],jt=this.trackStartLines?1:void 0,I=this.trackStartLines?1:void 0,x=ih(this.emptyGroups),_=this.trackStartLines,C=this.config.lineTerminatorsPattern,K=0,M=[],P=[],je=[],We=[];Object.freeze(We);let ie;function wr(){return M}s(wr,"getPossiblePatternsSlow");function wf(Pe){let lt=Ft(Pe),cn=P[lt];return cn===void 0?We:cn}s(wf,"getPossiblePatternsOptimized");let fx=s(Pe=>{if(je.length===1&&Pe.tokenType.PUSH_MODE===void 0){let lt=this.config.errorMessageProvider.buildUnableToPopLexerModeMessage(Pe);or.push({offset:Pe.startOffset,line:Pe.startLine,column:Pe.startColumn,length:Pe.image.length,message:lt})}else{je.pop();let lt=Mt(je);M=this.patternIdxToConfig[lt],P=this.charCodeToPatternIdxToConfig[lt],K=M.length;let cn=this.canModeBeOptimized[lt]&&this.config.safeMode===!1;P&&cn?ie=wf:ie=wr}},"pop_mode");function bf(Pe){je.push(Pe),P=this.charCodeToPatternIdxToConfig[Pe],M=this.patternIdxToConfig[Pe],K=M.length,K=M.length;let lt=this.canModeBeOptimized[Pe]&&this.config.safeMode===!1;P&&lt?ie=wf:ie=wr}s(bf,"push_mode"),bf.call(this,r);let mt,Of=this.config.recoveryEnabled;for(;L<$;){u=null;let Pe=A.charCodeAt(L),lt=ie(Pe),cn=lt.length;for(n=0;n<cn;n++){mt=lt[n];let nt=mt.pattern;c=null;let Wt=mt.short;if(Wt!==!1?Pe===Wt&&(u=nt):mt.isCustom===!0?(E=nt.exec(A,L,Le,x),E!==null?(u=E[0],E.payload!==void 0&&(c=E.payload)):u=null):(this.updateLastIndex(nt,L),u=this.match(nt,e,L)),u!==null){if(l=mt.longerAlt,l!==void 0){let lr=l.length;for(a=0;a<lr;a++){let Kt=M[l[a]],br=Kt.pattern;if(f=null,Kt.isCustom===!0?(E=br.exec(A,L,Le,x),E!==null?(o=E[0],E.payload!==void 0&&(f=E.payload)):o=null):(this.updateLastIndex(br,L),o=this.match(br,e,L)),o&&o.length>u.length){u=o,c=f,mt=Kt;break}}}break}}if(u!==null){if(d=u.length,p=mt.group,p!==void 0&&(m=mt.tokenTypeIdx,g=this.createTokenInstance(u,L,m,mt.tokenType,jt,I,d),this.handlePayload(g,c),p===!1?ke=this.addToken(Le,ke,g):x[p].push(g)),e=this.chopInput(e,d),L=L+d,I=this.computeNewColumn(I,d),_===!0&&mt.canLineTerminator===!0){let nt=0,Wt,lr;C.lastIndex=0;do Wt=C.test(u),Wt===!0&&(lr=C.lastIndex-1,nt++);while(Wt===!0);nt!==0&&(jt=jt+nt,I=d-lr,this.updateTokenEndLineColumnLocation(g,p,lr,nt,jt,I,d))}this.handleModes(mt,fx,bf,g)}else{let nt=L,Wt=jt,lr=I,Kt=Of===!1;for(;Kt===!1&&L<$;)for(e=this.chopInput(e,1),L++,i=0;i<K;i++){let br=M[i],ql=br.pattern,$f=br.short;if($f!==!1?A.charCodeAt(L)===$f&&(Kt=!0):br.isCustom===!0?Kt=ql.exec(A,L,Le,x)!==null:(this.updateLastIndex(ql,L),Kt=ql.exec(e)!==null),Kt===!0)break}if(y=L-nt,I=this.computeNewColumn(I,y),T=this.config.errorMessageProvider.buildUnexpectedCharactersMessage(A,nt,y,Wt,lr),or.push({offset:nt,line:Wt,column:lr,length:y,message:T}),Of===!1)break}}return this.hasCustom||(Le.length=ke),{tokens:Le,groups:x,errors:or}}handleModes(e,r,n,i){if(e.pop===!0){let a=e.push;r(i),a!==void 0&&n.call(this,a)}else e.push!==void 0&&n.call(this,e.push)}chopInput(e,r){return e.substring(r)}updateLastIndex(e,r){e.lastIndex=r}updateTokenEndLineColumnLocation(e,r,n,i,a,o,l){let u,c;r!==void 0&&(u=n===l-1,c=u?-1:0,i===1&&u===!0||(e.endLine=a+c,e.endColumn=o-1+-c))}computeNewColumn(e,r){return e+r}createOffsetOnlyToken(e,r,n,i){return{image:e,startOffset:r,tokenTypeIdx:n,tokenType:i}}createStartOnlyToken(e,r,n,i,a,o){return{image:e,startOffset:r,startLine:a,startColumn:o,tokenTypeIdx:n,tokenType:i}}createFullToken(e,r,n,i,a,o,l){return{image:e,startOffset:r,endOffset:r+l-1,startLine:a,endLine:a,startColumn:o,endColumn:o+l-1,tokenTypeIdx:n,tokenType:i}}addTokenUsingPush(e,r,n){return e.push(n),r}addTokenUsingMemberAccess(e,r,n){return e[r]=n,r++,r}handlePayloadNoCustom(e,r){}handlePayloadWithCustom(e,r){r!==null&&(e.payload=r)}matchWithTest(e,r,n){return e.test(r)===!0?r.substring(n,e.lastIndex):null}matchWithExec(e,r){let n=e.exec(r);return n!==null?n[0]:null}};ue.SKIPPED="This marks a skipped Token pattern, this means each token identified by it willbe consumed and then thrown into oblivion, this can be used to for example to completely ignore whitespace.";ue.NA=/NOT_APPLICABLE/;function er(t){return nc(t)?t.LABEL:t.name}s(er,"tokenLabel");function nc(t){return Ie(t.LABEL)&&t.LABEL!==""}s(nc,"hasTokenLabel");var yC="parent",mh="categories",hh="label",gh="group",yh="push_mode",xh="pop_mode",Th="longer_alt",Ah="line_breaks",Rh="start_chars_hint";function Sr(t){return xC(t)}s(Sr,"createToken");function xC(t){let e=t.pattern,r={};if(r.name=t.name,Ue(e)||(r.PATTERN=e),k(t,yC))throw`The parent property is no longer supported.
See: https://github.com/chevrotain/chevrotain/issues/564#issuecomment-349062346 for details.`;return k(t,mh)&&(r.CATEGORIES=t[mh]),Qt([r]),k(t,hh)&&(r.LABEL=t[hh]),k(t,gh)&&(r.GROUP=t[gh]),k(t,xh)&&(r.POP_MODE=t[xh]),k(t,yh)&&(r.PUSH_MODE=t[yh]),k(t,Th)&&(r.LONGER_ALT=t[Th]),k(t,Ah)&&(r.LINE_BREAKS=t[Ah]),k(t,Rh)&&(r.START_CHARS_HINT=t[Rh]),r}s(xC,"createTokenInternal");var ot=Sr({name:"EOF",pattern:ue.NA});Qt([ot]);function tr(t,e,r,n,i,a,o,l){return{image:e,startOffset:r,endOffset:n,startLine:i,endLine:a,startColumn:o,endColumn:l,tokenTypeIdx:t.tokenTypeIdx,tokenType:t}}s(tr,"createTokenInstance");function $s(t,e){return Zt(t,e)}s($s,"tokenMatcher");var rr={buildMismatchTokenMessage({expected:t,actual:e,previous:r,ruleName:n}){return`Expecting ${nc(t)?`--> ${er(t)} <--`:`token of type --> ${t.name} <--`} but found --> '${e.image}' <--`},buildNotAllInputParsedMessage({firstRedundant:t,ruleName:e}){return"Redundant input, expecting EOF but found: "+t.image},buildNoViableAltMessage({expectedPathsPerAlt:t,actual:e,previous:r,customUserDescription:n,ruleName:i}){let a="Expecting: ",l=`
but found: '`+_e(e).image+"'";if(n)return a+n+l;{let u=me(t,(p,m)=>p.concat(m),[]),c=v(u,p=>`[${v(p,m=>er(m)).join(", ")}]`),d=`one of these possible Token sequences:
${v(c,(p,m)=>`  ${m+1}. ${p}`).join(`
`)}`;return a+d+l}},buildEarlyExitMessage({expectedIterationPaths:t,actual:e,customUserDescription:r,ruleName:n}){let i="Expecting: ",o=`
but found: '`+_e(e).image+"'";if(r)return i+r+o;{let u=`expecting at least one iteration which starts with one of these possible Token sequences::
  <${v(t,c=>`[${v(c,f=>er(f)).join(",")}]`).join(" ,")}>`;return i+u+o}}};Object.freeze(rr);var Eh={buildRuleNotFoundError(t,e){return"Invalid grammar, reference to a rule which is not defined: ->"+e.nonTerminalName+`<-
inside top level rule: ->`+t.name+"<-"}},Tt={buildDuplicateFoundError(t,e){function r(f){return f instanceof F?f.terminalType.name:f instanceof V?f.nonTerminalName:""}s(r,"getExtraProductionArgument");let n=t.name,i=_e(e),a=i.idx,o=rt(i),l=r(i),u=a>0,c=`->${o}${u?a:""}<- ${l?`with argument: ->${l}<-`:""}
                  appears more than once (${e.length} times) in the top level rule: ->${n}<-.                  
                  For further details see: https://chevrotain.io/docs/FAQ.html#NUMERICAL_SUFFIXES 
                  `;return c=c.replace(/[ \t]+/g," "),c=c.replace(/\s\s+/g,`
`),c},buildNamespaceConflictError(t){return`Namespace conflict found in grammar.
The grammar has both a Terminal(Token) and a Non-Terminal(Rule) named: <${t.name}>.
To resolve this make sure each Terminal and Non-Terminal names are unique
This is easy to accomplish by using the convention that Terminal names start with an uppercase letter
and Non-Terminal names start with a lower case letter.`},buildAlternationPrefixAmbiguityError(t){let e=v(t.prefixPath,i=>er(i)).join(", "),r=t.alternation.idx===0?"":t.alternation.idx;return`Ambiguous alternatives: <${t.ambiguityIndices.join(" ,")}> due to common lookahead prefix
in <OR${r}> inside <${t.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#COMMON_PREFIX
For Further details.`},buildAlternationAmbiguityError(t){let e=v(t.prefixPath,i=>er(i)).join(", "),r=t.alternation.idx===0?"":t.alternation.idx,n=`Ambiguous Alternatives Detected: <${t.ambiguityIndices.join(" ,")}> in <OR${r}> inside <${t.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
`;return n=n+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,n},buildEmptyRepetitionError(t){let e=rt(t.repetition);return t.repetition.idx!==0&&(e+=t.repetition.idx),`The repetition <${e}> within Rule <${t.topLevelRule.name}> can never consume any tokens.
This could lead to an infinite loop.`},buildTokenNameError(t){return"deprecated"},buildEmptyAlternationError(t){return`Ambiguous empty alternative: <${t.emptyChoiceIdx+1}> in <OR${t.alternation.idx}> inside <${t.topLevelRule.name}> Rule.
Only the last alternative may be an empty alternative.`},buildTooManyAlternativesError(t){return`An Alternation cannot have more than 256 alternatives:
<OR${t.alternation.idx}> inside <${t.topLevelRule.name}> Rule.
 has ${t.alternation.definition.length+1} alternatives.`},buildLeftRecursionError(t){let e=t.topLevelRule.name,r=v(t.leftRecursionPath,a=>a.name),n=`${e} --> ${r.concat([e]).join(" --> ")}`;return`Left Recursion found in grammar.
rule: <${e}> can be invoked from itself (directly or indirectly)
without consuming any Tokens. The grammar path that causes this is: 
 ${n}
 To fix this refactor your grammar to remove the left recursion.
see: https://en.wikipedia.org/wiki/LL_parser#Left_factoring.`},buildInvalidRuleNameError(t){return"deprecated"},buildDuplicateRuleNameError(t){let e;return t.topLevelRule instanceof Ve?e=t.topLevelRule.name:e=t.topLevelRule,`Duplicate definition, rule: ->${e}<- is already defined in the grammar: ->${t.grammarName}<-`}};function vh(t,e){let r=new ic(t,e);return r.resolveRefs(),r.errors}s(vh,"resolveGrammar");var ic=class extends ze{static{s(this,"GastRefResolverVisitor")}constructor(e,r){super(),this.nameToTopRule=e,this.errMsgProvider=r,this.errors=[]}resolveRefs(){S(q(this.nameToTopRule),e=>{this.currTopLevel=e,e.accept(this)})}visitNonTerminal(e){let r=this.nameToTopRule[e.nonTerminalName];if(r)e.referencedRule=r;else{let n=this.errMsgProvider.buildRuleNotFoundError(this.currTopLevel,e);this.errors.push({message:n,type:Se.UNRESOLVED_SUBRULE_REF,ruleName:this.currTopLevel.name,unresolvedRefName:e.nonTerminalName})}}};var sc=class extends Jt{static{s(this,"AbstractNextPossibleTokensWalker")}constructor(e,r){super(),this.topProd=e,this.path=r,this.possibleTokTypes=[],this.nextProductionName="",this.nextProductionOccurrence=0,this.found=!1,this.isAtEndOfPath=!1}startWalking(){if(this.found=!1,this.path.ruleStack[0]!==this.topProd.name)throw Error("The path does not start with the walker's top Rule!");return this.ruleStack=ee(this.path.ruleStack).reverse(),this.occurrenceStack=ee(this.path.occurrenceStack).reverse(),this.ruleStack.pop(),this.occurrenceStack.pop(),this.updateExpectedNext(),this.walk(this.topProd),this.possibleTokTypes}walk(e,r=[]){this.found||super.walk(e,r)}walkProdRef(e,r,n){if(e.referencedRule.name===this.nextProductionName&&e.idx===this.nextProductionOccurrence){let i=r.concat(n);this.updateExpectedNext(),this.walk(e.referencedRule,i)}}updateExpectedNext(){D(this.ruleStack)?(this.nextProductionName="",this.nextProductionOccurrence=0,this.isAtEndOfPath=!0):(this.nextProductionName=this.ruleStack.pop(),this.nextProductionOccurrence=this.occurrenceStack.pop())}},Fo=class extends sc{static{s(this,"NextAfterTokenWalker")}constructor(e,r){super(e,r),this.path=r,this.nextTerminalName="",this.nextTerminalOccurrence=0,this.nextTerminalName=this.path.lastTok.name,this.nextTerminalOccurrence=this.path.lastTokOccurrence}walkTerminal(e,r,n){if(this.isAtEndOfPath&&e.terminalType.name===this.nextTerminalName&&e.idx===this.nextTerminalOccurrence&&!this.found){let i=r.concat(n),a=new te({definition:i});this.possibleTokTypes=qr(a),this.found=!0}}},Li=class extends Jt{static{s(this,"AbstractNextTerminalAfterProductionWalker")}constructor(e,r){super(),this.topRule=e,this.occurrence=r,this.result={token:void 0,occurrence:void 0,isEndOfRule:void 0}}startWalking(){return this.walk(this.topRule),this.result}},Go=class extends Li{static{s(this,"NextTerminalAfterManyWalker")}walkMany(e,r,n){if(e.idx===this.occurrence){let i=_e(r.concat(n));this.result.isEndOfRule=i===void 0,i instanceof F&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkMany(e,r,n)}},Ls=class extends Li{static{s(this,"NextTerminalAfterManySepWalker")}walkManySep(e,r,n){if(e.idx===this.occurrence){let i=_e(r.concat(n));this.result.isEndOfRule=i===void 0,i instanceof F&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkManySep(e,r,n)}},Uo=class extends Li{static{s(this,"NextTerminalAfterAtLeastOneWalker")}walkAtLeastOne(e,r,n){if(e.idx===this.occurrence){let i=_e(r.concat(n));this.result.isEndOfRule=i===void 0,i instanceof F&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkAtLeastOne(e,r,n)}},Ps=class extends Li{static{s(this,"NextTerminalAfterAtLeastOneSepWalker")}walkAtLeastOneSep(e,r,n){if(e.idx===this.occurrence){let i=_e(r.concat(n));this.result.isEndOfRule=i===void 0,i instanceof F&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkAtLeastOneSep(e,r,n)}};function Bo(t,e,r=[]){r=ee(r);let n=[],i=0;function a(l){return l.concat(xe(t,i+1))}s(a,"remainingPathWith");function o(l){let u=Bo(a(l),e,r);return n.concat(u)}for(s(o,"getAlternativesForProd");r.length<e&&i<t.length;){let l=t[i];if(l instanceof te)return o(l.definition);if(l instanceof V)return o(l.definition);if(l instanceof z)n=o(l.definition);else if(l instanceof re){let u=l.definition.concat([new G({definition:l.definition})]);return o(u)}else if(l instanceof ne){let u=[new te({definition:l.definition}),new G({definition:[new F({terminalType:l.separator})].concat(l.definition)})];return o(u)}else if(l instanceof X){let u=l.definition.concat([new G({definition:[new F({terminalType:l.separator})].concat(l.definition)})]);n=o(u)}else if(l instanceof G){let u=l.definition.concat([new G({definition:l.definition})]);n=o(u)}else{if(l instanceof Y)return S(l.definition,u=>{D(u.definition)===!1&&(n=o(u.definition))}),n;if(l instanceof F)r.push(l.terminalType);else throw Error("non exhaustive match")}i++}return n.push({partialPath:r,suffixDef:xe(t,i)}),n}s(Bo,"possiblePathsFrom");function jo(t,e,r,n){let i="EXIT_NONE_TERMINAL",a=[i],o="EXIT_ALTERNATIVE",l=!1,u=e.length,c=u-n-1,f=[],d=[];for(d.push({idx:-1,def:t,ruleStack:[],occurrenceStack:[]});!D(d);){let p=d.pop();if(p===o){l&&Mt(d).idx<=c&&d.pop();continue}let m=p.def,g=p.idx,y=p.ruleStack,R=p.occurrenceStack;if(D(m))continue;let T=m[0];if(T===i){let E={idx:g,def:xe(m),ruleStack:Yt(y),occurrenceStack:Yt(R)};d.push(E)}else if(T instanceof F)if(g<u-1){let E=g+1,A=e[E];if(r(A,T.terminalType)){let $={idx:E,def:xe(m),ruleStack:y,occurrenceStack:R};d.push($)}}else if(g===u-1)f.push({nextTokenType:T.terminalType,nextTokenOccurrence:T.idx,ruleStack:y,occurrenceStack:R}),l=!0;else throw Error("non exhaustive match");else if(T instanceof V){let E=ee(y);E.push(T.nonTerminalName);let A=ee(R);A.push(T.idx);let $={idx:g,def:T.definition.concat(a,xe(m)),ruleStack:E,occurrenceStack:A};d.push($)}else if(T instanceof z){let E={idx:g,def:xe(m),ruleStack:y,occurrenceStack:R};d.push(E),d.push(o);let A={idx:g,def:T.definition.concat(xe(m)),ruleStack:y,occurrenceStack:R};d.push(A)}else if(T instanceof re){let E=new G({definition:T.definition,idx:T.idx}),A=T.definition.concat([E],xe(m)),$={idx:g,def:A,ruleStack:y,occurrenceStack:R};d.push($)}else if(T instanceof ne){let E=new F({terminalType:T.separator}),A=new G({definition:[E].concat(T.definition),idx:T.idx}),$=T.definition.concat([A],xe(m)),L={idx:g,def:$,ruleStack:y,occurrenceStack:R};d.push(L)}else if(T instanceof X){let E={idx:g,def:xe(m),ruleStack:y,occurrenceStack:R};d.push(E),d.push(o);let A=new F({terminalType:T.separator}),$=new G({definition:[A].concat(T.definition),idx:T.idx}),L=T.definition.concat([$],xe(m)),ke={idx:g,def:L,ruleStack:y,occurrenceStack:R};d.push(ke)}else if(T instanceof G){let E={idx:g,def:xe(m),ruleStack:y,occurrenceStack:R};d.push(E),d.push(o);let A=new G({definition:T.definition,idx:T.idx}),$=T.definition.concat([A],xe(m)),L={idx:g,def:$,ruleStack:y,occurrenceStack:R};d.push(L)}else if(T instanceof Y)for(let E=T.definition.length-1;E>=0;E--){let A=T.definition[E],$={idx:g,def:A.definition.concat(xe(m)),ruleStack:y,occurrenceStack:R};d.push($),d.push(o)}else if(T instanceof te)d.push({idx:g,def:T.definition.concat(xe(m)),ruleStack:y,occurrenceStack:R});else if(T instanceof Ve)d.push(TC(T,g,y,R));else throw Error("non exhaustive match")}return f}s(jo,"nextPossibleTokensAfter");function TC(t,e,r,n){let i=ee(r);i.push(t.name);let a=ee(n);return a.push(1),{idx:e,def:t.definition,ruleStack:i,occurrenceStack:a}}s(TC,"expandTopLevelRule");var ce;(function(t){t[t.OPTION=0]="OPTION",t[t.REPETITION=1]="REPETITION",t[t.REPETITION_MANDATORY=2]="REPETITION_MANDATORY",t[t.REPETITION_MANDATORY_WITH_SEPARATOR=3]="REPETITION_MANDATORY_WITH_SEPARATOR",t[t.REPETITION_WITH_SEPARATOR=4]="REPETITION_WITH_SEPARATOR",t[t.ALTERNATION=5]="ALTERNATION"})(ce||(ce={}));function Ms(t){if(t instanceof z||t==="Option")return ce.OPTION;if(t instanceof G||t==="Repetition")return ce.REPETITION;if(t instanceof re||t==="RepetitionMandatory")return ce.REPETITION_MANDATORY;if(t instanceof ne||t==="RepetitionMandatoryWithSeparator")return ce.REPETITION_MANDATORY_WITH_SEPARATOR;if(t instanceof X||t==="RepetitionWithSeparator")return ce.REPETITION_WITH_SEPARATOR;if(t instanceof Y||t==="Alternation")return ce.ALTERNATION;throw Error("non exhaustive match")}s(Ms,"getProdType");function Ko(t){let{occurrence:e,rule:r,prodType:n,maxLookahead:i}=t,a=Ms(n);return a===ce.ALTERNATION?Pi(e,r,i):Mi(e,r,a,i)}s(Ko,"getLookaheadPaths");function Sh(t,e,r,n,i,a){let o=Pi(t,e,r),l=bh(o)?Oi:Zt;return a(o,n,l,i)}s(Sh,"buildLookaheadFuncForOr");function kh(t,e,r,n,i,a){let o=Mi(t,e,i,r),l=bh(o)?Oi:Zt;return a(o[0],l,n)}s(kh,"buildLookaheadFuncForOptionalProd");function Ch(t,e,r,n){let i=t.length,a=Ge(t,o=>Ge(o,l=>l.length===1));if(e)return function(o){let l=v(o,u=>u.GATE);for(let u=0;u<i;u++){let c=t[u],f=c.length,d=l[u];if(!(d!==void 0&&d.call(this)===!1))e:for(let p=0;p<f;p++){let m=c[p],g=m.length;for(let y=0;y<g;y++){let R=this.LA(y+1);if(r(R,m[y])===!1)continue e}return u}}};if(a&&!n){let o=v(t,u=>ye(u)),l=me(o,(u,c,f)=>(S(c,d=>{k(u,d.tokenTypeIdx)||(u[d.tokenTypeIdx]=f),S(d.categoryMatches,p=>{k(u,p)||(u[p]=f)})}),u),{});return function(){let u=this.LA(1);return l[u.tokenTypeIdx]}}else return function(){for(let o=0;o<i;o++){let l=t[o],u=l.length;e:for(let c=0;c<u;c++){let f=l[c],d=f.length;for(let p=0;p<d;p++){let m=this.LA(p+1);if(r(m,f[p])===!1)continue e}return o}}}}s(Ch,"buildAlternativesLookAheadFunc");function Nh(t,e,r){let n=Ge(t,a=>a.length===1),i=t.length;if(n&&!r){let a=ye(t);if(a.length===1&&D(a[0].categoryMatches)){let l=a[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===l}}else{let o=me(a,(l,u,c)=>(l[u.tokenTypeIdx]=!0,S(u.categoryMatches,f=>{l[f]=!0}),l),[]);return function(){let l=this.LA(1);return o[l.tokenTypeIdx]===!0}}}else return function(){e:for(let a=0;a<i;a++){let o=t[a],l=o.length;for(let u=0;u<l;u++){let c=this.LA(u+1);if(e(c,o[u])===!1)continue e}return!0}return!1}}s(Nh,"buildSingleAlternativeLookaheadFunction");var oc=class extends Jt{static{s(this,"RestDefinitionFinderWalker")}constructor(e,r,n){super(),this.topProd=e,this.targetOccurrence=r,this.targetProdType=n}startWalking(){return this.walk(this.topProd),this.restDef}checkIsTarget(e,r,n,i){return e.idx===this.targetOccurrence&&this.targetProdType===r?(this.restDef=n.concat(i),!0):!1}walkOption(e,r,n){this.checkIsTarget(e,ce.OPTION,r,n)||super.walkOption(e,r,n)}walkAtLeastOne(e,r,n){this.checkIsTarget(e,ce.REPETITION_MANDATORY,r,n)||super.walkOption(e,r,n)}walkAtLeastOneSep(e,r,n){this.checkIsTarget(e,ce.REPETITION_MANDATORY_WITH_SEPARATOR,r,n)||super.walkOption(e,r,n)}walkMany(e,r,n){this.checkIsTarget(e,ce.REPETITION,r,n)||super.walkOption(e,r,n)}walkManySep(e,r,n){this.checkIsTarget(e,ce.REPETITION_WITH_SEPARATOR,r,n)||super.walkOption(e,r,n)}},Wo=class extends ze{static{s(this,"InsideDefinitionFinderVisitor")}constructor(e,r,n){super(),this.targetOccurrence=e,this.targetProdType=r,this.targetRef=n,this.result=[]}checkIsTarget(e,r){e.idx===this.targetOccurrence&&this.targetProdType===r&&(this.targetRef===void 0||e===this.targetRef)&&(this.result=e.definition)}visitOption(e){this.checkIsTarget(e,ce.OPTION)}visitRepetition(e){this.checkIsTarget(e,ce.REPETITION)}visitRepetitionMandatory(e){this.checkIsTarget(e,ce.REPETITION_MANDATORY)}visitRepetitionMandatoryWithSeparator(e){this.checkIsTarget(e,ce.REPETITION_MANDATORY_WITH_SEPARATOR)}visitRepetitionWithSeparator(e){this.checkIsTarget(e,ce.REPETITION_WITH_SEPARATOR)}visitAlternation(e){this.checkIsTarget(e,ce.ALTERNATION)}};function Ih(t){let e=new Array(t);for(let r=0;r<t;r++)e[r]=[];return e}s(Ih,"initializeArrayOfArrays");function ac(t){let e=[""];for(let r=0;r<t.length;r++){let n=t[r],i=[];for(let a=0;a<e.length;a++){let o=e[a];i.push(o+"_"+n.tokenTypeIdx);for(let l=0;l<n.categoryMatches.length;l++){let u="_"+n.categoryMatches[l];i.push(o+u)}}e=i}return e}s(ac,"pathToHashKeys");function AC(t,e,r){for(let n=0;n<t.length;n++){if(n===r)continue;let i=t[n];for(let a=0;a<e.length;a++){let o=e[a];if(i[o]===!0)return!1}}return!0}s(AC,"isUniquePrefixHash");function _h(t,e){let r=v(t,o=>Bo([o],1)),n=Ih(r.length),i=v(r,o=>{let l={};return S(o,u=>{let c=ac(u.partialPath);S(c,f=>{l[f]=!0})}),l}),a=r;for(let o=1;o<=e;o++){let l=a;a=Ih(l.length);for(let u=0;u<l.length;u++){let c=l[u];for(let f=0;f<c.length;f++){let d=c[f].partialPath,p=c[f].suffixDef,m=ac(d);if(AC(i,m,u)||D(p)||d.length===e){let y=n[u];if(Ho(y,d)===!1){y.push(d);for(let R=0;R<m.length;R++){let T=m[R];i[u][T]=!0}}}else{let y=Bo(p,o+1,d);a[u]=a[u].concat(y),S(y,R=>{let T=ac(R.partialPath);S(T,E=>{i[u][E]=!0})})}}}}return n}s(_h,"lookAheadSequenceFromAlternatives");function Pi(t,e,r,n){let i=new Wo(t,ce.ALTERNATION,n);return e.accept(i),_h(i.result,r)}s(Pi,"getLookaheadPathsForOr");function Mi(t,e,r,n){let i=new Wo(t,r);e.accept(i);let a=i.result,l=new oc(e,t,r).startWalking(),u=new te({definition:a}),c=new te({definition:l});return _h([u,c],n)}s(Mi,"getLookaheadPathsForOptionalProd");function Ho(t,e){e:for(let r=0;r<t.length;r++){let n=t[r];if(n.length===e.length){for(let i=0;i<n.length;i++){let a=e[i],o=n[i];if((a===o||o.categoryMatchesMap[a.tokenTypeIdx]!==void 0)===!1)continue e}return!0}}return!1}s(Ho,"containsPath");function wh(t,e){return t.length<e.length&&Ge(t,(r,n)=>{let i=e[n];return r===i||i.categoryMatchesMap[r.tokenTypeIdx]})}s(wh,"isStrictPrefixOfPath");function bh(t){return Ge(t,e=>Ge(e,r=>Ge(r,n=>D(n.categoryMatches))))}s(bh,"areTokenCategoriesNotUsed");function Oh(t){let e=t.lookaheadStrategy.validate({rules:t.rules,tokenTypes:t.tokenTypes,grammarName:t.grammarName});return v(e,r=>Object.assign({type:Se.CUSTOM_LOOKAHEAD_VALIDATION},r))}s(Oh,"validateLookahead");function $h(t,e,r,n){let i=$e(t,u=>RC(u,r)),a=CC(t,e,r),o=$e(t,u=>IC(u,r)),l=$e(t,u=>vC(u,t,n,r));return i.concat(a,o,l)}s($h,"validateGrammar");function RC(t,e){let r=new lc;t.accept(r);let n=r.allProductions,i=zu(n,EC),a=tt(i,l=>l.length>1);return v(q(a),l=>{let u=_e(l),c=e.buildDuplicateFoundError(t,l),f=rt(u),d={message:c,type:Se.DUPLICATE_PRODUCTIONS,ruleName:t.name,dslName:f,occurrence:u.idx},p=Lh(u);return p&&(d.parameter=p),d})}s(RC,"validateDuplicateProductions");function EC(t){return`${rt(t)}_#_${t.idx}_#_${Lh(t)}`}s(EC,"identifyProductionForDuplicates");function Lh(t){return t instanceof F?t.terminalType.name:t instanceof V?t.nonTerminalName:""}s(Lh,"getExtraProductionArgument");var lc=class extends ze{static{s(this,"OccurrenceValidationCollector")}constructor(){super(...arguments),this.allProductions=[]}visitNonTerminal(e){this.allProductions.push(e)}visitOption(e){this.allProductions.push(e)}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}visitAlternation(e){this.allProductions.push(e)}visitTerminal(e){this.allProductions.push(e)}};function vC(t,e,r,n){let i=[];if(me(e,(o,l)=>l.name===t.name?o+1:o,0)>1){let o=n.buildDuplicateRuleNameError({topLevelRule:t,grammarName:r});i.push({message:o,type:Se.DUPLICATE_RULE_NAME,ruleName:t.name})}return i}s(vC,"validateRuleDoesNotAlreadyExist");function Ph(t,e,r){let n=[],i;return oe(e,t)||(i=`Invalid rule override, rule: ->${t}<- cannot be overridden in the grammar: ->${r}<-as it is not defined in any of the super grammars `,n.push({message:i,type:Se.INVALID_RULE_OVERRIDE,ruleName:t})),n}s(Ph,"validateRuleIsOverridden");function cc(t,e,r,n=[]){let i=[],a=Vo(e.definition);if(D(a))return[];{let o=t.name;oe(a,t)&&i.push({message:r.buildLeftRecursionError({topLevelRule:t,leftRecursionPath:n}),type:Se.LEFT_RECURSION,ruleName:o});let u=vr(a,n.concat([t])),c=$e(u,f=>{let d=ee(n);return d.push(f),cc(t,f,r,d)});return i.concat(c)}}s(cc,"validateNoLeftRecursion");function Vo(t){let e=[];if(D(t))return e;let r=_e(t);if(r instanceof V)e.push(r.referencedRule);else if(r instanceof te||r instanceof z||r instanceof re||r instanceof ne||r instanceof X||r instanceof G)e=e.concat(Vo(r.definition));else if(r instanceof Y)e=ye(v(r.definition,a=>Vo(a.definition)));else if(!(r instanceof F))throw Error("non exhaustive match");let n=zr(r),i=t.length>1;if(n&&i){let a=xe(t);return e.concat(Vo(a))}else return e}s(Vo,"getFirstNoneTerminal");var Ds=class extends ze{static{s(this,"OrCollector")}constructor(){super(...arguments),this.alternations=[]}visitAlternation(e){this.alternations.push(e)}};function Mh(t,e){let r=new Ds;t.accept(r);let n=r.alternations;return $e(n,a=>{let o=Yt(a.definition);return $e(o,(l,u)=>{let c=jo([l],[],Zt,1);return D(c)?[{message:e.buildEmptyAlternationError({topLevelRule:t,alternation:a,emptyChoiceIdx:u}),type:Se.NONE_LAST_EMPTY_ALT,ruleName:t.name,occurrence:a.idx,alternative:u+1}]:[]})})}s(Mh,"validateEmptyOrAlternative");function Dh(t,e,r){let n=new Ds;t.accept(n);let i=n.alternations;return i=Ir(i,o=>o.ignoreAmbiguities===!0),$e(i,o=>{let l=o.idx,u=o.maxLookahead||e,c=Pi(l,t,u,o),f=SC(c,o,t,r),d=kC(c,o,t,r);return f.concat(d)})}s(Dh,"validateAmbiguousAlternationAlternatives");var uc=class extends ze{static{s(this,"RepetitionCollector")}constructor(){super(...arguments),this.allProductions=[]}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}};function IC(t,e){let r=new Ds;t.accept(r);let n=r.alternations;return $e(n,a=>a.definition.length>255?[{message:e.buildTooManyAlternativesError({topLevelRule:t,alternation:a}),type:Se.TOO_MANY_ALTS,ruleName:t.name,occurrence:a.idx}]:[])}s(IC,"validateTooManyAlts");function Fh(t,e,r){let n=[];return S(t,i=>{let a=new uc;i.accept(a);let o=a.allProductions;S(o,l=>{let u=Ms(l),c=l.maxLookahead||e,f=l.idx,p=Mi(f,i,u,c)[0];if(D(ye(p))){let m=r.buildEmptyRepetitionError({topLevelRule:i,repetition:l});n.push({message:m,type:Se.NO_NON_EMPTY_LOOKAHEAD,ruleName:i.name})}})}),n}s(Fh,"validateSomeNonEmptyLookaheadPath");function SC(t,e,r,n){let i=[],a=me(t,(l,u,c)=>(e.definition[c].ignoreAmbiguities===!0||S(u,f=>{let d=[c];S(t,(p,m)=>{c!==m&&Ho(p,f)&&e.definition[m].ignoreAmbiguities!==!0&&d.push(m)}),d.length>1&&!Ho(i,f)&&(i.push(f),l.push({alts:d,path:f}))}),l),[]);return v(a,l=>{let u=v(l.alts,f=>f+1);return{message:n.buildAlternationAmbiguityError({topLevelRule:r,alternation:e,ambiguityIndices:u,prefixPath:l.path}),type:Se.AMBIGUOUS_ALTS,ruleName:r.name,occurrence:e.idx,alternatives:l.alts}})}s(SC,"checkAlternativesAmbiguities");function kC(t,e,r,n){let i=me(t,(o,l,u)=>{let c=v(l,f=>({idx:u,path:f}));return o.concat(c)},[]);return Pt($e(i,o=>{if(e.definition[o.idx].ignoreAmbiguities===!0)return[];let u=o.idx,c=o.path,f=Ne(i,p=>e.definition[p.idx].ignoreAmbiguities!==!0&&p.idx<u&&wh(p.path,c));return v(f,p=>{let m=[p.idx+1,u+1],g=e.idx===0?"":e.idx;return{message:n.buildAlternationPrefixAmbiguityError({topLevelRule:r,alternation:e,ambiguityIndices:m,prefixPath:p.path}),type:Se.AMBIGUOUS_PREFIX_ALTS,ruleName:r.name,occurrence:g,alternatives:m}})}))}s(kC,"checkPrefixAlternativesAmbiguities");function CC(t,e,r){let n=[],i=v(e,a=>a.name);return S(t,a=>{let o=a.name;if(oe(i,o)){let l=r.buildNamespaceConflictError(a);n.push({message:l,type:Se.CONFLICT_TOKENS_RULES_NAMESPACE,ruleName:o})}}),n}s(CC,"checkTerminalAndNoneTerminalsNameSpace");function Gh(t){let e=Si(t,{errMsgProvider:Eh}),r={};return S(t.rules,n=>{r[n.name]=n}),vh(r,e.errMsgProvider)}s(Gh,"resolveGrammar");function Uh(t){return t=Si(t,{errMsgProvider:Tt}),$h(t.rules,t.tokenTypes,t.errMsgProvider,t.grammarName)}s(Uh,"validateGrammar");var Bh="MismatchedTokenException",jh="NoViableAltException",Wh="EarlyExitException",Kh="NotAllInputParsedException",Hh=[Bh,jh,Wh,Kh];Object.freeze(Hh);function kr(t){return oe(Hh,t.name)}s(kr,"isRecognitionException");var Di=class extends Error{static{s(this,"RecognitionException")}constructor(e,r){super(e),this.token=r,this.resyncedTokens=[],Object.setPrototypeOf(this,new.target.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}},Yr=class extends Di{static{s(this,"MismatchedTokenException")}constructor(e,r,n){super(e,r),this.previousToken=n,this.name=Bh}},Fs=class extends Di{static{s(this,"NoViableAltException")}constructor(e,r,n){super(e,r),this.previousToken=n,this.name=jh}},Gs=class extends Di{static{s(this,"NotAllInputParsedException")}constructor(e,r){super(e,r),this.name=Kh}},Us=class extends Di{static{s(this,"EarlyExitException")}constructor(e,r,n){super(e,r),this.previousToken=n,this.name=Wh}};var fc={},pc="InRuleRecoveryException",dc=class extends Error{static{s(this,"InRuleRecoveryException")}constructor(e){super(e),this.name=pc}},zo=class{static{s(this,"Recoverable")}initRecoverable(e){this.firstAfterRepMap={},this.resyncFollows={},this.recoveryEnabled=k(e,"recoveryEnabled")?e.recoveryEnabled:qe.recoveryEnabled,this.recoveryEnabled&&(this.attemptInRepetitionRecovery=NC)}getTokenToInsert(e){let r=tr(e,"",NaN,NaN,NaN,NaN,NaN,NaN);return r.isInsertedInRecovery=!0,r}canTokenTypeBeInsertedInRecovery(e){return!0}canTokenTypeBeDeletedInRecovery(e){return!0}tryInRepetitionRecovery(e,r,n,i){let a=this.findReSyncTokenType(),o=this.exportLexerState(),l=[],u=!1,c=this.LA(1),f=this.LA(1),d=s(()=>{let p=this.LA(0),m=this.errorMessageProvider.buildMismatchTokenMessage({expected:i,actual:c,previous:p,ruleName:this.getCurrRuleFullName()}),g=new Yr(m,c,this.LA(0));g.resyncedTokens=Yt(l),this.SAVE_ERROR(g)},"generateErrorMessage");for(;!u;)if(this.tokenMatcher(f,i)){d();return}else if(n.call(this)){d(),e.apply(this,r);return}else this.tokenMatcher(f,a)?u=!0:(f=this.SKIP_TOKEN(),this.addToResyncTokens(f,l));this.importLexerState(o)}shouldInRepetitionRecoveryBeTried(e,r,n){return!(n===!1||this.tokenMatcher(this.LA(1),e)||this.isBackTracking()||this.canPerformInRuleRecovery(e,this.getFollowsForInRuleRecovery(e,r)))}getFollowsForInRuleRecovery(e,r){let n=this.getCurrentGrammarPath(e,r);return this.getNextPossibleTokenTypes(n)}tryInRuleRecovery(e,r){if(this.canRecoverWithSingleTokenInsertion(e,r))return this.getTokenToInsert(e);if(this.canRecoverWithSingleTokenDeletion(e)){let n=this.SKIP_TOKEN();return this.consumeToken(),n}throw new dc("sad sad panda")}canPerformInRuleRecovery(e,r){return this.canRecoverWithSingleTokenInsertion(e,r)||this.canRecoverWithSingleTokenDeletion(e)}canRecoverWithSingleTokenInsertion(e,r){if(!this.canTokenTypeBeInsertedInRecovery(e)||D(r))return!1;let n=this.LA(1);return Dt(r,a=>this.tokenMatcher(n,a))!==void 0}canRecoverWithSingleTokenDeletion(e){return this.canTokenTypeBeDeletedInRecovery(e)?this.tokenMatcher(this.LA(2),e):!1}isInCurrentRuleReSyncSet(e){let r=this.getCurrFollowKey(),n=this.getFollowSetFromFollowKey(r);return oe(n,e)}findReSyncTokenType(){let e=this.flattenFollowSet(),r=this.LA(1),n=2;for(;;){let i=Dt(e,a=>$s(r,a));if(i!==void 0)return i;r=this.LA(n),n++}}getCurrFollowKey(){if(this.RULE_STACK.length===1)return fc;let e=this.getLastExplicitRuleShortName(),r=this.getLastExplicitRuleOccurrenceIndex(),n=this.getPreviousExplicitRuleShortName();return{ruleName:this.shortRuleNameToFullName(e),idxInCallingRule:r,inRule:this.shortRuleNameToFullName(n)}}buildFullFollowKeyStack(){let e=this.RULE_STACK,r=this.RULE_OCCURRENCE_STACK;return v(e,(n,i)=>i===0?fc:{ruleName:this.shortRuleNameToFullName(n),idxInCallingRule:r[i],inRule:this.shortRuleNameToFullName(e[i-1])})}flattenFollowSet(){let e=v(this.buildFullFollowKeyStack(),r=>this.getFollowSetFromFollowKey(r));return ye(e)}getFollowSetFromFollowKey(e){if(e===fc)return[ot];let r=e.ruleName+e.idxInCallingRule+Oo+e.inRule;return this.resyncFollows[r]}addToResyncTokens(e,r){return this.tokenMatcher(e,ot)||r.push(e),r}reSyncTo(e){let r=[],n=this.LA(1);for(;this.tokenMatcher(n,e)===!1;)n=this.SKIP_TOKEN(),this.addToResyncTokens(n,r);return Yt(r)}attemptInRepetitionRecovery(e,r,n,i,a,o,l){}getCurrentGrammarPath(e,r){let n=this.getHumanReadableRuleStack(),i=ee(this.RULE_OCCURRENCE_STACK);return{ruleStack:n,occurrenceStack:i,lastTok:e,lastTokOccurrence:r}}getHumanReadableRuleStack(){return v(this.RULE_STACK,e=>this.shortRuleNameToFullName(e))}};function NC(t,e,r,n,i,a,o){let l=this.getKeyForAutomaticLookahead(n,i),u=this.firstAfterRepMap[l];if(u===void 0){let p=this.getCurrRuleFullName(),m=this.getGAstProductions()[p];u=new a(m,i).startWalking(),this.firstAfterRepMap[l]=u}let c=u.token,f=u.occurrence,d=u.isEndOfRule;this.RULE_STACK.length===1&&d&&c===void 0&&(c=ot,f=1),!(c===void 0||f===void 0)&&this.shouldInRepetitionRecoveryBeTried(c,f,o)&&this.tryInRepetitionRecovery(t,e,r,c)}s(NC,"attemptInRepetitionRecovery");function qo(t,e,r){return r|e|t}s(qo,"getKeyForAutomaticLookahead");var nr=class{static{s(this,"LLkLookaheadStrategy")}constructor(e){var r;this.maxLookahead=(r=e?.maxLookahead)!==null&&r!==void 0?r:qe.maxLookahead}validate(e){let r=this.validateNoLeftRecursion(e.rules);if(D(r)){let n=this.validateEmptyOrAlternatives(e.rules),i=this.validateAmbiguousAlternationAlternatives(e.rules,this.maxLookahead),a=this.validateSomeNonEmptyLookaheadPath(e.rules,this.maxLookahead);return[...r,...n,...i,...a]}return r}validateNoLeftRecursion(e){return $e(e,r=>cc(r,r,Tt))}validateEmptyOrAlternatives(e){return $e(e,r=>Mh(r,Tt))}validateAmbiguousAlternationAlternatives(e,r){return $e(e,n=>Dh(n,r,Tt))}validateSomeNonEmptyLookaheadPath(e,r){return Fh(e,r,Tt)}buildLookaheadForAlternation(e){return Sh(e.prodOccurrence,e.rule,e.maxLookahead,e.hasPredicates,e.dynamicTokensEnabled,Ch)}buildLookaheadForOptional(e){return kh(e.prodOccurrence,e.rule,e.maxLookahead,e.dynamicTokensEnabled,Ms(e.prodType),Nh)}};var Yo=class{static{s(this,"LooksAhead")}initLooksAhead(e){this.dynamicTokensEnabled=k(e,"dynamicTokensEnabled")?e.dynamicTokensEnabled:qe.dynamicTokensEnabled,this.maxLookahead=k(e,"maxLookahead")?e.maxLookahead:qe.maxLookahead,this.lookaheadStrategy=k(e,"lookaheadStrategy")?e.lookaheadStrategy:new nr({maxLookahead:this.maxLookahead}),this.lookAheadFuncsCache=new Map}preComputeLookaheadFunctions(e){S(e,r=>{this.TRACE_INIT(`${r.name} Rule Lookahead`,()=>{let{alternation:n,repetition:i,option:a,repetitionMandatory:o,repetitionMandatoryWithSeparator:l,repetitionWithSeparator:u}=_C(r);S(n,c=>{let f=c.idx===0?"":c.idx;this.TRACE_INIT(`${rt(c)}${f}`,()=>{let d=this.lookaheadStrategy.buildLookaheadForAlternation({prodOccurrence:c.idx,rule:r,maxLookahead:c.maxLookahead||this.maxLookahead,hasPredicates:c.hasPredicates,dynamicTokensEnabled:this.dynamicTokensEnabled}),p=qo(this.fullRuleNameToShort[r.name],256,c.idx);this.setLaFuncCache(p,d)})}),S(i,c=>{this.computeLookaheadFunc(r,c.idx,768,"Repetition",c.maxLookahead,rt(c))}),S(a,c=>{this.computeLookaheadFunc(r,c.idx,512,"Option",c.maxLookahead,rt(c))}),S(o,c=>{this.computeLookaheadFunc(r,c.idx,1024,"RepetitionMandatory",c.maxLookahead,rt(c))}),S(l,c=>{this.computeLookaheadFunc(r,c.idx,1536,"RepetitionMandatoryWithSeparator",c.maxLookahead,rt(c))}),S(u,c=>{this.computeLookaheadFunc(r,c.idx,1280,"RepetitionWithSeparator",c.maxLookahead,rt(c))})})})}computeLookaheadFunc(e,r,n,i,a,o){this.TRACE_INIT(`${o}${r===0?"":r}`,()=>{let l=this.lookaheadStrategy.buildLookaheadForOptional({prodOccurrence:r,rule:e,maxLookahead:a||this.maxLookahead,dynamicTokensEnabled:this.dynamicTokensEnabled,prodType:i}),u=qo(this.fullRuleNameToShort[e.name],n,r);this.setLaFuncCache(u,l)})}getKeyForAutomaticLookahead(e,r){let n=this.getLastExplicitRuleShortName();return qo(n,e,r)}getLaFuncFromCache(e){return this.lookAheadFuncsCache.get(e)}setLaFuncCache(e,r){this.lookAheadFuncsCache.set(e,r)}},mc=class extends ze{static{s(this,"DslMethodsCollectorVisitor")}constructor(){super(...arguments),this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}reset(){this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}visitOption(e){this.dslMethods.option.push(e)}visitRepetitionWithSeparator(e){this.dslMethods.repetitionWithSeparator.push(e)}visitRepetitionMandatory(e){this.dslMethods.repetitionMandatory.push(e)}visitRepetitionMandatoryWithSeparator(e){this.dslMethods.repetitionMandatoryWithSeparator.push(e)}visitRepetition(e){this.dslMethods.repetition.push(e)}visitAlternation(e){this.dslMethods.alternation.push(e)}},Xo=new mc;function _C(t){Xo.reset(),t.accept(Xo);let e=Xo.dslMethods;return Xo.reset(),e}s(_C,"collectMethods");function yc(t,e){isNaN(t.startOffset)===!0?(t.startOffset=e.startOffset,t.endOffset=e.endOffset):t.endOffset<e.endOffset&&(t.endOffset=e.endOffset)}s(yc,"setNodeLocationOnlyOffset");function xc(t,e){isNaN(t.startOffset)===!0?(t.startOffset=e.startOffset,t.startColumn=e.startColumn,t.startLine=e.startLine,t.endOffset=e.endOffset,t.endColumn=e.endColumn,t.endLine=e.endLine):t.endOffset<e.endOffset&&(t.endOffset=e.endOffset,t.endColumn=e.endColumn,t.endLine=e.endLine)}s(xc,"setNodeLocationFull");function Vh(t,e,r){t.children[r]===void 0?t.children[r]=[e]:t.children[r].push(e)}s(Vh,"addTerminalToCst");function zh(t,e,r){t.children[e]===void 0?t.children[e]=[r]:t.children[e].push(r)}s(zh,"addNoneTerminalToCst");var wC="name";function Tc(t,e){Object.defineProperty(t,wC,{enumerable:!1,configurable:!0,writable:!1,value:e})}s(Tc,"defineNameProp");function bC(t,e){let r=Z(t),n=r.length;for(let i=0;i<n;i++){let a=r[i],o=t[a],l=o.length;for(let u=0;u<l;u++){let c=o[u];c.tokenTypeIdx===void 0&&this[c.name](c.children,e)}}}s(bC,"defaultVisit");function qh(t,e){let r=s(function(){},"derivedConstructor");Tc(r,t+"BaseSemantics");let n={visit:s(function(i,a){if(N(i)&&(i=i[0]),!Ue(i))return this[i.name](i.children,a)},"visit"),validateVisitor:s(function(){let i=OC(this,e);if(!D(i)){let a=v(i,o=>o.msg);throw Error(`Errors Detected in CST Visitor <${this.constructor.name}>:
	${a.join(`

`).replace(/\n/g,`
	`)}`)}},"validateVisitor")};return r.prototype=n,r.prototype.constructor=r,r._RULE_NAMES=e,r}s(qh,"createBaseSemanticVisitorConstructor");function Xh(t,e,r){let n=s(function(){},"derivedConstructor");Tc(n,t+"BaseSemanticsWithDefaults");let i=Object.create(r.prototype);return S(e,a=>{i[a]=bC}),n.prototype=i,n.prototype.constructor=n,n}s(Xh,"createBaseVisitorConstructorWithDefaults");var Ac;(function(t){t[t.REDUNDANT_METHOD=0]="REDUNDANT_METHOD",t[t.MISSING_METHOD=1]="MISSING_METHOD"})(Ac||(Ac={}));function OC(t,e){return $C(t,e)}s(OC,"validateVisitor");function $C(t,e){let r=Ne(e,i=>He(t[i])===!1),n=v(r,i=>({msg:`Missing visitor method: <${i}> on ${t.constructor.name} CST Visitor.`,type:Ac.MISSING_METHOD,methodName:i}));return Pt(n)}s($C,"validateMissingCstMethods");var el=class{static{s(this,"TreeBuilder")}initTreeBuilder(e){if(this.CST_STACK=[],this.outputCst=e.outputCst,this.nodeLocationTracking=k(e,"nodeLocationTracking")?e.nodeLocationTracking:qe.nodeLocationTracking,!this.outputCst)this.cstInvocationStateUpdate=pe,this.cstFinallyStateUpdate=pe,this.cstPostTerminal=pe,this.cstPostNonTerminal=pe,this.cstPostRule=pe;else if(/full/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=xc,this.setNodeLocationFromNode=xc,this.cstPostRule=pe,this.setInitialNodeLocation=this.setInitialNodeLocationFullRecovery):(this.setNodeLocationFromToken=pe,this.setNodeLocationFromNode=pe,this.cstPostRule=this.cstPostRuleFull,this.setInitialNodeLocation=this.setInitialNodeLocationFullRegular);else if(/onlyOffset/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=yc,this.setNodeLocationFromNode=yc,this.cstPostRule=pe,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRecovery):(this.setNodeLocationFromToken=pe,this.setNodeLocationFromNode=pe,this.cstPostRule=this.cstPostRuleOnlyOffset,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRegular);else if(/none/i.test(this.nodeLocationTracking))this.setNodeLocationFromToken=pe,this.setNodeLocationFromNode=pe,this.cstPostRule=pe,this.setInitialNodeLocation=pe;else throw Error(`Invalid <nodeLocationTracking> config option: "${e.nodeLocationTracking}"`)}setInitialNodeLocationOnlyOffsetRecovery(e){e.location={startOffset:NaN,endOffset:NaN}}setInitialNodeLocationOnlyOffsetRegular(e){e.location={startOffset:this.LA(1).startOffset,endOffset:NaN}}setInitialNodeLocationFullRecovery(e){e.location={startOffset:NaN,startLine:NaN,startColumn:NaN,endOffset:NaN,endLine:NaN,endColumn:NaN}}setInitialNodeLocationFullRegular(e){let r=this.LA(1);e.location={startOffset:r.startOffset,startLine:r.startLine,startColumn:r.startColumn,endOffset:NaN,endLine:NaN,endColumn:NaN}}cstInvocationStateUpdate(e){let r={name:e,children:Object.create(null)};this.setInitialNodeLocation(r),this.CST_STACK.push(r)}cstFinallyStateUpdate(){this.CST_STACK.pop()}cstPostRuleFull(e){let r=this.LA(0),n=e.location;n.startOffset<=r.startOffset?(n.endOffset=r.endOffset,n.endLine=r.endLine,n.endColumn=r.endColumn):(n.startOffset=NaN,n.startLine=NaN,n.startColumn=NaN)}cstPostRuleOnlyOffset(e){let r=this.LA(0),n=e.location;n.startOffset<=r.startOffset?n.endOffset=r.endOffset:n.startOffset=NaN}cstPostTerminal(e,r){let n=this.CST_STACK[this.CST_STACK.length-1];Vh(n,r,e),this.setNodeLocationFromToken(n.location,r)}cstPostNonTerminal(e,r){let n=this.CST_STACK[this.CST_STACK.length-1];zh(n,r,e),this.setNodeLocationFromNode(n.location,e.location)}getBaseCstVisitorConstructor(){if(Ue(this.baseCstVisitorConstructor)){let e=qh(this.className,Z(this.gastProductionsCache));return this.baseCstVisitorConstructor=e,e}return this.baseCstVisitorConstructor}getBaseCstVisitorConstructorWithDefaults(){if(Ue(this.baseCstVisitorWithDefaultsConstructor)){let e=Xh(this.className,Z(this.gastProductionsCache),this.getBaseCstVisitorConstructor());return this.baseCstVisitorWithDefaultsConstructor=e,e}return this.baseCstVisitorWithDefaultsConstructor}getLastExplicitRuleShortName(){let e=this.RULE_STACK;return e[e.length-1]}getPreviousExplicitRuleShortName(){let e=this.RULE_STACK;return e[e.length-2]}getLastExplicitRuleOccurrenceIndex(){let e=this.RULE_OCCURRENCE_STACK;return e[e.length-1]}};var tl=class{static{s(this,"LexerAdapter")}initLexerAdapter(){this.tokVector=[],this.tokVectorLength=0,this.currIdx=-1}set input(e){if(this.selfAnalysisDone!==!0)throw Error("Missing <performSelfAnalysis> invocation at the end of the Parser's constructor.");this.reset(),this.tokVector=e,this.tokVectorLength=e.length}get input(){return this.tokVector}SKIP_TOKEN(){return this.currIdx<=this.tokVector.length-2?(this.consumeToken(),this.LA(1)):Fi}LA(e){let r=this.currIdx+e;return r<0||this.tokVectorLength<=r?Fi:this.tokVector[r]}consumeToken(){this.currIdx++}exportLexerState(){return this.currIdx}importLexerState(e){this.currIdx=e}resetLexerState(){this.currIdx=-1}moveToTerminatedState(){this.currIdx=this.tokVector.length-1}getLexerPosition(){return this.exportLexerState()}};var rl=class{static{s(this,"RecognizerApi")}ACTION(e){return e.call(this)}consume(e,r,n){return this.consumeInternal(r,e,n)}subrule(e,r,n){return this.subruleInternal(r,e,n)}option(e,r){return this.optionInternal(r,e)}or(e,r){return this.orInternal(r,e)}many(e,r){return this.manyInternal(e,r)}atLeastOne(e,r){return this.atLeastOneInternal(e,r)}CONSUME(e,r){return this.consumeInternal(e,0,r)}CONSUME1(e,r){return this.consumeInternal(e,1,r)}CONSUME2(e,r){return this.consumeInternal(e,2,r)}CONSUME3(e,r){return this.consumeInternal(e,3,r)}CONSUME4(e,r){return this.consumeInternal(e,4,r)}CONSUME5(e,r){return this.consumeInternal(e,5,r)}CONSUME6(e,r){return this.consumeInternal(e,6,r)}CONSUME7(e,r){return this.consumeInternal(e,7,r)}CONSUME8(e,r){return this.consumeInternal(e,8,r)}CONSUME9(e,r){return this.consumeInternal(e,9,r)}SUBRULE(e,r){return this.subruleInternal(e,0,r)}SUBRULE1(e,r){return this.subruleInternal(e,1,r)}SUBRULE2(e,r){return this.subruleInternal(e,2,r)}SUBRULE3(e,r){return this.subruleInternal(e,3,r)}SUBRULE4(e,r){return this.subruleInternal(e,4,r)}SUBRULE5(e,r){return this.subruleInternal(e,5,r)}SUBRULE6(e,r){return this.subruleInternal(e,6,r)}SUBRULE7(e,r){return this.subruleInternal(e,7,r)}SUBRULE8(e,r){return this.subruleInternal(e,8,r)}SUBRULE9(e,r){return this.subruleInternal(e,9,r)}OPTION(e){return this.optionInternal(e,0)}OPTION1(e){return this.optionInternal(e,1)}OPTION2(e){return this.optionInternal(e,2)}OPTION3(e){return this.optionInternal(e,3)}OPTION4(e){return this.optionInternal(e,4)}OPTION5(e){return this.optionInternal(e,5)}OPTION6(e){return this.optionInternal(e,6)}OPTION7(e){return this.optionInternal(e,7)}OPTION8(e){return this.optionInternal(e,8)}OPTION9(e){return this.optionInternal(e,9)}OR(e){return this.orInternal(e,0)}OR1(e){return this.orInternal(e,1)}OR2(e){return this.orInternal(e,2)}OR3(e){return this.orInternal(e,3)}OR4(e){return this.orInternal(e,4)}OR5(e){return this.orInternal(e,5)}OR6(e){return this.orInternal(e,6)}OR7(e){return this.orInternal(e,7)}OR8(e){return this.orInternal(e,8)}OR9(e){return this.orInternal(e,9)}MANY(e){this.manyInternal(0,e)}MANY1(e){this.manyInternal(1,e)}MANY2(e){this.manyInternal(2,e)}MANY3(e){this.manyInternal(3,e)}MANY4(e){this.manyInternal(4,e)}MANY5(e){this.manyInternal(5,e)}MANY6(e){this.manyInternal(6,e)}MANY7(e){this.manyInternal(7,e)}MANY8(e){this.manyInternal(8,e)}MANY9(e){this.manyInternal(9,e)}MANY_SEP(e){this.manySepFirstInternal(0,e)}MANY_SEP1(e){this.manySepFirstInternal(1,e)}MANY_SEP2(e){this.manySepFirstInternal(2,e)}MANY_SEP3(e){this.manySepFirstInternal(3,e)}MANY_SEP4(e){this.manySepFirstInternal(4,e)}MANY_SEP5(e){this.manySepFirstInternal(5,e)}MANY_SEP6(e){this.manySepFirstInternal(6,e)}MANY_SEP7(e){this.manySepFirstInternal(7,e)}MANY_SEP8(e){this.manySepFirstInternal(8,e)}MANY_SEP9(e){this.manySepFirstInternal(9,e)}AT_LEAST_ONE(e){this.atLeastOneInternal(0,e)}AT_LEAST_ONE1(e){return this.atLeastOneInternal(1,e)}AT_LEAST_ONE2(e){this.atLeastOneInternal(2,e)}AT_LEAST_ONE3(e){this.atLeastOneInternal(3,e)}AT_LEAST_ONE4(e){this.atLeastOneInternal(4,e)}AT_LEAST_ONE5(e){this.atLeastOneInternal(5,e)}AT_LEAST_ONE6(e){this.atLeastOneInternal(6,e)}AT_LEAST_ONE7(e){this.atLeastOneInternal(7,e)}AT_LEAST_ONE8(e){this.atLeastOneInternal(8,e)}AT_LEAST_ONE9(e){this.atLeastOneInternal(9,e)}AT_LEAST_ONE_SEP(e){this.atLeastOneSepFirstInternal(0,e)}AT_LEAST_ONE_SEP1(e){this.atLeastOneSepFirstInternal(1,e)}AT_LEAST_ONE_SEP2(e){this.atLeastOneSepFirstInternal(2,e)}AT_LEAST_ONE_SEP3(e){this.atLeastOneSepFirstInternal(3,e)}AT_LEAST_ONE_SEP4(e){this.atLeastOneSepFirstInternal(4,e)}AT_LEAST_ONE_SEP5(e){this.atLeastOneSepFirstInternal(5,e)}AT_LEAST_ONE_SEP6(e){this.atLeastOneSepFirstInternal(6,e)}AT_LEAST_ONE_SEP7(e){this.atLeastOneSepFirstInternal(7,e)}AT_LEAST_ONE_SEP8(e){this.atLeastOneSepFirstInternal(8,e)}AT_LEAST_ONE_SEP9(e){this.atLeastOneSepFirstInternal(9,e)}RULE(e,r,n=Gi){if(oe(this.definedRulesNames,e)){let o={message:Tt.buildDuplicateRuleNameError({topLevelRule:e,grammarName:this.className}),type:Se.DUPLICATE_RULE_NAME,ruleName:e};this.definitionErrors.push(o)}this.definedRulesNames.push(e);let i=this.defineRule(e,r,n);return this[e]=i,i}OVERRIDE_RULE(e,r,n=Gi){let i=Ph(e,this.definedRulesNames,this.className);this.definitionErrors=this.definitionErrors.concat(i);let a=this.defineRule(e,r,n);return this[e]=a,a}BACKTRACK(e,r){return function(){this.isBackTrackingStack.push(1);let n=this.saveRecogState();try{return e.apply(this,r),!0}catch(i){if(kr(i))return!1;throw i}finally{this.reloadRecogState(n),this.isBackTrackingStack.pop()}}}getGAstProductions(){return this.gastProductionsCache}getSerializedGastProductions(){return bo(q(this.gastProductionsCache))}};var nl=class{static{s(this,"RecognizerEngine")}initRecognizerEngine(e,r){if(this.className=this.constructor.name,this.shortRuleNameToFull={},this.fullRuleNameToShort={},this.ruleShortNameIdx=256,this.tokenMatcher=Oi,this.subruleIdx=0,this.definedRulesNames=[],this.tokensMap={},this.isBackTrackingStack=[],this.RULE_STACK=[],this.RULE_OCCURRENCE_STACK=[],this.gastProductionsCache={},k(r,"serializedGrammar"))throw Error(`The Parser's configuration can no longer contain a <serializedGrammar> property.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_6-0-0
	For Further details.`);if(N(e)){if(D(e))throw Error(`A Token Vocabulary cannot be empty.
	Note that the first argument for the parser constructor
	is no longer a Token vector (since v4.0).`);if(typeof e[0].startOffset=="number")throw Error(`The Parser constructor no longer accepts a token vector as the first argument.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_4-0-0
	For Further details.`)}if(N(e))this.tokensMap=me(e,(a,o)=>(a[o.name]=o,a),{});else if(k(e,"modes")&&Ge(ye(q(e.modes)),ph)){let a=ye(q(e.modes)),o=ki(a);this.tokensMap=me(o,(l,u)=>(l[u.name]=u,l),{})}else if(de(e))this.tokensMap=ee(e);else throw new Error("<tokensDictionary> argument must be An Array of Token constructors, A dictionary of Token constructors or an IMultiModeLexerDefinition");this.tokensMap.EOF=ot;let n=k(e,"modes")?ye(q(e.modes)):q(e),i=Ge(n,a=>D(a.categoryMatches));this.tokenMatcher=i?Oi:Zt,Qt(q(this.tokensMap))}defineRule(e,r,n){if(this.selfAnalysisDone)throw Error(`Grammar rule <${e}> may not be defined after the 'performSelfAnalysis' method has been called'
Make sure that all grammar rule definitions are done before 'performSelfAnalysis' is called.`);let i=k(n,"resyncEnabled")?n.resyncEnabled:Gi.resyncEnabled,a=k(n,"recoveryValueFunc")?n.recoveryValueFunc:Gi.recoveryValueFunc,o=this.ruleShortNameIdx<<12;this.ruleShortNameIdx++,this.shortRuleNameToFull[o]=e,this.fullRuleNameToShort[e]=o;let l;return this.outputCst===!0?l=s(function(...f){try{this.ruleInvocationStateUpdate(o,e,this.subruleIdx),r.apply(this,f);let d=this.CST_STACK[this.CST_STACK.length-1];return this.cstPostRule(d),d}catch(d){return this.invokeRuleCatch(d,i,a)}finally{this.ruleFinallyStateUpdate()}},"invokeRuleWithTry"):l=s(function(...f){try{return this.ruleInvocationStateUpdate(o,e,this.subruleIdx),r.apply(this,f)}catch(d){return this.invokeRuleCatch(d,i,a)}finally{this.ruleFinallyStateUpdate()}},"invokeRuleWithTryCst"),Object.assign(l,{ruleName:e,originalGrammarAction:r})}invokeRuleCatch(e,r,n){let i=this.RULE_STACK.length===1,a=r&&!this.isBackTracking()&&this.recoveryEnabled;if(kr(e)){let o=e;if(a){let l=this.findReSyncTokenType();if(this.isInCurrentRuleReSyncSet(l))if(o.resyncedTokens=this.reSyncTo(l),this.outputCst){let u=this.CST_STACK[this.CST_STACK.length-1];return u.recoveredNode=!0,u}else return n(e);else{if(this.outputCst){let u=this.CST_STACK[this.CST_STACK.length-1];u.recoveredNode=!0,o.partialCstResult=u}throw o}}else{if(i)return this.moveToTerminatedState(),n(e);throw o}}else throw e}optionInternal(e,r){let n=this.getKeyForAutomaticLookahead(512,r);return this.optionInternalLogic(e,r,n)}optionInternalLogic(e,r,n){let i=this.getLaFuncFromCache(n),a;if(typeof e!="function"){a=e.DEF;let o=e.GATE;if(o!==void 0){let l=i;i=s(()=>o.call(this)&&l.call(this),"lookAheadFunc")}}else a=e;if(i.call(this)===!0)return a.call(this)}atLeastOneInternal(e,r){let n=this.getKeyForAutomaticLookahead(1024,e);return this.atLeastOneInternalLogic(e,r,n)}atLeastOneInternalLogic(e,r,n){let i=this.getLaFuncFromCache(n),a;if(typeof r!="function"){a=r.DEF;let o=r.GATE;if(o!==void 0){let l=i;i=s(()=>o.call(this)&&l.call(this),"lookAheadFunc")}}else a=r;if(i.call(this)===!0){let o=this.doSingleRepetition(a);for(;i.call(this)===!0&&o===!0;)o=this.doSingleRepetition(a)}else throw this.raiseEarlyExitException(e,ce.REPETITION_MANDATORY,r.ERR_MSG);this.attemptInRepetitionRecovery(this.atLeastOneInternal,[e,r],i,1024,e,Uo)}atLeastOneSepFirstInternal(e,r){let n=this.getKeyForAutomaticLookahead(1536,e);this.atLeastOneSepFirstInternalLogic(e,r,n)}atLeastOneSepFirstInternalLogic(e,r,n){let i=r.DEF,a=r.SEP;if(this.getLaFuncFromCache(n).call(this)===!0){i.call(this);let l=s(()=>this.tokenMatcher(this.LA(1),a),"separatorLookAheadFunc");for(;this.tokenMatcher(this.LA(1),a)===!0;)this.CONSUME(a),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,a,l,i,Ps],l,1536,e,Ps)}else throw this.raiseEarlyExitException(e,ce.REPETITION_MANDATORY_WITH_SEPARATOR,r.ERR_MSG)}manyInternal(e,r){let n=this.getKeyForAutomaticLookahead(768,e);return this.manyInternalLogic(e,r,n)}manyInternalLogic(e,r,n){let i=this.getLaFuncFromCache(n),a;if(typeof r!="function"){a=r.DEF;let l=r.GATE;if(l!==void 0){let u=i;i=s(()=>l.call(this)&&u.call(this),"lookaheadFunction")}}else a=r;let o=!0;for(;i.call(this)===!0&&o===!0;)o=this.doSingleRepetition(a);this.attemptInRepetitionRecovery(this.manyInternal,[e,r],i,768,e,Go,o)}manySepFirstInternal(e,r){let n=this.getKeyForAutomaticLookahead(1280,e);this.manySepFirstInternalLogic(e,r,n)}manySepFirstInternalLogic(e,r,n){let i=r.DEF,a=r.SEP;if(this.getLaFuncFromCache(n).call(this)===!0){i.call(this);let l=s(()=>this.tokenMatcher(this.LA(1),a),"separatorLookAheadFunc");for(;this.tokenMatcher(this.LA(1),a)===!0;)this.CONSUME(a),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,a,l,i,Ls],l,1280,e,Ls)}}repetitionSepSecondInternal(e,r,n,i,a){for(;n();)this.CONSUME(r),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,r,n,i,a],n,1536,e,a)}doSingleRepetition(e){let r=this.getLexerPosition();return e.call(this),this.getLexerPosition()>r}orInternal(e,r){let n=this.getKeyForAutomaticLookahead(256,r),i=N(e)?e:e.DEF,o=this.getLaFuncFromCache(n).call(this,i);if(o!==void 0)return i[o].ALT.call(this);this.raiseNoAltException(r,e.ERR_MSG)}ruleFinallyStateUpdate(){if(this.RULE_STACK.pop(),this.RULE_OCCURRENCE_STACK.pop(),this.cstFinallyStateUpdate(),this.RULE_STACK.length===0&&this.isAtEndOfInput()===!1){let e=this.LA(1),r=this.errorMessageProvider.buildNotAllInputParsedMessage({firstRedundant:e,ruleName:this.getCurrRuleFullName()});this.SAVE_ERROR(new Gs(r,e))}}subruleInternal(e,r,n){let i;try{let a=n!==void 0?n.ARGS:void 0;return this.subruleIdx=r,i=e.apply(this,a),this.cstPostNonTerminal(i,n!==void 0&&n.LABEL!==void 0?n.LABEL:e.ruleName),i}catch(a){throw this.subruleInternalError(a,n,e.ruleName)}}subruleInternalError(e,r,n){throw kr(e)&&e.partialCstResult!==void 0&&(this.cstPostNonTerminal(e.partialCstResult,r!==void 0&&r.LABEL!==void 0?r.LABEL:n),delete e.partialCstResult),e}consumeInternal(e,r,n){let i;try{let a=this.LA(1);this.tokenMatcher(a,e)===!0?(this.consumeToken(),i=a):this.consumeInternalError(e,a,n)}catch(a){i=this.consumeInternalRecovery(e,r,a)}return this.cstPostTerminal(n!==void 0&&n.LABEL!==void 0?n.LABEL:e.name,i),i}consumeInternalError(e,r,n){let i,a=this.LA(0);throw n!==void 0&&n.ERR_MSG?i=n.ERR_MSG:i=this.errorMessageProvider.buildMismatchTokenMessage({expected:e,actual:r,previous:a,ruleName:this.getCurrRuleFullName()}),this.SAVE_ERROR(new Yr(i,r,a))}consumeInternalRecovery(e,r,n){if(this.recoveryEnabled&&n.name==="MismatchedTokenException"&&!this.isBackTracking()){let i=this.getFollowsForInRuleRecovery(e,r);try{return this.tryInRuleRecovery(e,i)}catch(a){throw a.name===pc?n:a}}else throw n}saveRecogState(){let e=this.errors,r=ee(this.RULE_STACK);return{errors:e,lexerState:this.exportLexerState(),RULE_STACK:r,CST_STACK:this.CST_STACK}}reloadRecogState(e){this.errors=e.errors,this.importLexerState(e.lexerState),this.RULE_STACK=e.RULE_STACK}ruleInvocationStateUpdate(e,r,n){this.RULE_OCCURRENCE_STACK.push(n),this.RULE_STACK.push(e),this.cstInvocationStateUpdate(r)}isBackTracking(){return this.isBackTrackingStack.length!==0}getCurrRuleFullName(){let e=this.getLastExplicitRuleShortName();return this.shortRuleNameToFull[e]}shortRuleNameToFullName(e){return this.shortRuleNameToFull[e]}isAtEndOfInput(){return this.tokenMatcher(this.LA(1),ot)}reset(){this.resetLexerState(),this.subruleIdx=0,this.isBackTrackingStack=[],this.errors=[],this.RULE_STACK=[],this.CST_STACK=[],this.RULE_OCCURRENCE_STACK=[]}};var il=class{static{s(this,"ErrorHandler")}initErrorHandler(e){this._errors=[],this.errorMessageProvider=k(e,"errorMessageProvider")?e.errorMessageProvider:qe.errorMessageProvider}SAVE_ERROR(e){if(kr(e))return e.context={ruleStack:this.getHumanReadableRuleStack(),ruleOccurrenceStack:ee(this.RULE_OCCURRENCE_STACK)},this._errors.push(e),e;throw Error("Trying to save an Error which is not a RecognitionException")}get errors(){return ee(this._errors)}set errors(e){this._errors=e}raiseEarlyExitException(e,r,n){let i=this.getCurrRuleFullName(),a=this.getGAstProductions()[i],l=Mi(e,a,r,this.maxLookahead)[0],u=[];for(let f=1;f<=this.maxLookahead;f++)u.push(this.LA(f));let c=this.errorMessageProvider.buildEarlyExitMessage({expectedIterationPaths:l,actual:u,previous:this.LA(0),customUserDescription:n,ruleName:i});throw this.SAVE_ERROR(new Us(c,this.LA(1),this.LA(0)))}raiseNoAltException(e,r){let n=this.getCurrRuleFullName(),i=this.getGAstProductions()[n],a=Pi(e,i,this.maxLookahead),o=[];for(let c=1;c<=this.maxLookahead;c++)o.push(this.LA(c));let l=this.LA(0),u=this.errorMessageProvider.buildNoViableAltMessage({expectedPathsPerAlt:a,actual:o,previous:l,customUserDescription:r,ruleName:this.getCurrRuleFullName()});throw this.SAVE_ERROR(new Fs(u,this.LA(1),l))}};var sl=class{static{s(this,"ContentAssist")}initContentAssist(){}computeContentAssist(e,r){let n=this.gastProductionsCache[e];if(Ue(n))throw Error(`Rule ->${e}<- does not exist in this grammar.`);return jo([n],r,this.tokenMatcher,this.maxLookahead)}getNextPossibleTokenTypes(e){let r=_e(e.ruleStack),i=this.getGAstProductions()[r];return new Fo(i,e).startWalking()}};var ll={description:"This Object indicates the Parser is during Recording Phase"};Object.freeze(ll);var Yh=!0,Jh=Math.pow(2,8)-1,Qh=Sr({name:"RECORDING_PHASE_TOKEN",pattern:ue.NA});Qt([Qh]);var eg=tr(Qh,`This IToken indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,-1,-1,-1,-1,-1,-1);Object.freeze(eg);var PC={name:`This CSTNode indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,children:{}},al=class{static{s(this,"GastRecorder")}initGastRecorder(e){this.recordingProdStack=[],this.RECORDING_PHASE=!1}enableRecording(){this.RECORDING_PHASE=!0,this.TRACE_INIT("Enable Recording",()=>{for(let e=0;e<10;e++){let r=e>0?e:"";this[`CONSUME${r}`]=function(n,i){return this.consumeInternalRecord(n,e,i)},this[`SUBRULE${r}`]=function(n,i){return this.subruleInternalRecord(n,e,i)},this[`OPTION${r}`]=function(n){return this.optionInternalRecord(n,e)},this[`OR${r}`]=function(n){return this.orInternalRecord(n,e)},this[`MANY${r}`]=function(n){this.manyInternalRecord(e,n)},this[`MANY_SEP${r}`]=function(n){this.manySepFirstInternalRecord(e,n)},this[`AT_LEAST_ONE${r}`]=function(n){this.atLeastOneInternalRecord(e,n)},this[`AT_LEAST_ONE_SEP${r}`]=function(n){this.atLeastOneSepFirstInternalRecord(e,n)}}this.consume=function(e,r,n){return this.consumeInternalRecord(r,e,n)},this.subrule=function(e,r,n){return this.subruleInternalRecord(r,e,n)},this.option=function(e,r){return this.optionInternalRecord(r,e)},this.or=function(e,r){return this.orInternalRecord(r,e)},this.many=function(e,r){this.manyInternalRecord(e,r)},this.atLeastOne=function(e,r){this.atLeastOneInternalRecord(e,r)},this.ACTION=this.ACTION_RECORD,this.BACKTRACK=this.BACKTRACK_RECORD,this.LA=this.LA_RECORD})}disableRecording(){this.RECORDING_PHASE=!1,this.TRACE_INIT("Deleting Recording methods",()=>{let e=this;for(let r=0;r<10;r++){let n=r>0?r:"";delete e[`CONSUME${n}`],delete e[`SUBRULE${n}`],delete e[`OPTION${n}`],delete e[`OR${n}`],delete e[`MANY${n}`],delete e[`MANY_SEP${n}`],delete e[`AT_LEAST_ONE${n}`],delete e[`AT_LEAST_ONE_SEP${n}`]}delete e.consume,delete e.subrule,delete e.option,delete e.or,delete e.many,delete e.atLeastOne,delete e.ACTION,delete e.BACKTRACK,delete e.LA})}ACTION_RECORD(e){}BACKTRACK_RECORD(e,r){return()=>!0}LA_RECORD(e){return Fi}topLevelRuleRecord(e,r){try{let n=new Ve({definition:[],name:e});return n.name=e,this.recordingProdStack.push(n),r.call(this),this.recordingProdStack.pop(),n}catch(n){if(n.KNOWN_RECORDER_ERROR!==!0)try{n.message=n.message+`
	 This error was thrown during the "grammar recording phase" For more info see:
	https://chevrotain.io/docs/guide/internals.html#grammar-recording`}catch{throw n}throw n}}optionInternalRecord(e,r){return js.call(this,z,e,r)}atLeastOneInternalRecord(e,r){js.call(this,re,r,e)}atLeastOneSepFirstInternalRecord(e,r){js.call(this,ne,r,e,Yh)}manyInternalRecord(e,r){js.call(this,G,r,e)}manySepFirstInternalRecord(e,r){js.call(this,X,r,e,Yh)}orInternalRecord(e,r){return MC.call(this,e,r)}subruleInternalRecord(e,r,n){if(ol(r),!e||k(e,"ruleName")===!1){let l=new Error(`<SUBRULE${Zh(r)}> argument is invalid expecting a Parser method reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw l.KNOWN_RECORDER_ERROR=!0,l}let i=Mt(this.recordingProdStack),a=e.ruleName,o=new V({idx:r,nonTerminalName:a,label:n?.LABEL,referencedRule:void 0});return i.definition.push(o),this.outputCst?PC:ll}consumeInternalRecord(e,r,n){if(ol(r),!rc(e)){let o=new Error(`<CONSUME${Zh(r)}> argument is invalid expecting a TokenType reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw o.KNOWN_RECORDER_ERROR=!0,o}let i=Mt(this.recordingProdStack),a=new F({idx:r,terminalType:e,label:n?.LABEL});return i.definition.push(a),eg}};function js(t,e,r,n=!1){ol(r);let i=Mt(this.recordingProdStack),a=He(e)?e:e.DEF,o=new t({definition:[],idx:r});return n&&(o.separator=e.SEP),k(e,"MAX_LOOKAHEAD")&&(o.maxLookahead=e.MAX_LOOKAHEAD),this.recordingProdStack.push(o),a.call(this),i.definition.push(o),this.recordingProdStack.pop(),ll}s(js,"recordProd");function MC(t,e){ol(e);let r=Mt(this.recordingProdStack),n=N(t)===!1,i=n===!1?t:t.DEF,a=new Y({definition:[],idx:e,ignoreAmbiguities:n&&t.IGNORE_AMBIGUITIES===!0});k(t,"MAX_LOOKAHEAD")&&(a.maxLookahead=t.MAX_LOOKAHEAD);let o=Cs(i,l=>He(l.GATE));return a.hasPredicates=o,r.definition.push(a),S(i,l=>{let u=new te({definition:[]});a.definition.push(u),k(l,"IGNORE_AMBIGUITIES")?u.ignoreAmbiguities=l.IGNORE_AMBIGUITIES:k(l,"GATE")&&(u.ignoreAmbiguities=!0),this.recordingProdStack.push(u),l.ALT.call(this),this.recordingProdStack.pop()}),ll}s(MC,"recordOrProd");function Zh(t){return t===0?"":`${t}`}s(Zh,"getIdxSuffix");function ol(t){if(t<0||t>Jh){let e=new Error(`Invalid DSL Method idx value: <${t}>
	Idx value must be a none negative value smaller than ${Jh+1}`);throw e.KNOWN_RECORDER_ERROR=!0,e}}s(ol,"assertMethodIdxIsValid");var ul=class{static{s(this,"PerformanceTracer")}initPerformanceTracer(e){if(k(e,"traceInitPerf")){let r=e.traceInitPerf,n=typeof r=="number";this.traceInitMaxIdent=n?r:1/0,this.traceInitPerf=n?r>0:r}else this.traceInitMaxIdent=0,this.traceInitPerf=qe.traceInitPerf;this.traceInitIndent=-1}TRACE_INIT(e,r){if(this.traceInitPerf===!0){this.traceInitIndent++;let n=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${n}--> <${e}>`);let{time:i,value:a}=_s(r),o=i>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&o(`${n}<-- <${e}> time: ${i}ms`),this.traceInitIndent--,a}else return r()}};function tg(t,e){e.forEach(r=>{let n=r.prototype;Object.getOwnPropertyNames(n).forEach(i=>{if(i==="constructor")return;let a=Object.getOwnPropertyDescriptor(n,i);a&&(a.get||a.set)?Object.defineProperty(t.prototype,i,a):t.prototype[i]=r.prototype[i]})})}s(tg,"applyMixins");var Fi=tr(ot,"",NaN,NaN,NaN,NaN,NaN,NaN);Object.freeze(Fi);var qe=Object.freeze({recoveryEnabled:!1,maxLookahead:3,dynamicTokensEnabled:!1,outputCst:!0,errorMessageProvider:rr,nodeLocationTracking:"none",traceInitPerf:!1,skipValidations:!1}),Gi=Object.freeze({recoveryValueFunc:s(()=>{},"recoveryValueFunc"),resyncEnabled:!0}),Se;(function(t){t[t.INVALID_RULE_NAME=0]="INVALID_RULE_NAME",t[t.DUPLICATE_RULE_NAME=1]="DUPLICATE_RULE_NAME",t[t.INVALID_RULE_OVERRIDE=2]="INVALID_RULE_OVERRIDE",t[t.DUPLICATE_PRODUCTIONS=3]="DUPLICATE_PRODUCTIONS",t[t.UNRESOLVED_SUBRULE_REF=4]="UNRESOLVED_SUBRULE_REF",t[t.LEFT_RECURSION=5]="LEFT_RECURSION",t[t.NONE_LAST_EMPTY_ALT=6]="NONE_LAST_EMPTY_ALT",t[t.AMBIGUOUS_ALTS=7]="AMBIGUOUS_ALTS",t[t.CONFLICT_TOKENS_RULES_NAMESPACE=8]="CONFLICT_TOKENS_RULES_NAMESPACE",t[t.INVALID_TOKEN_NAME=9]="INVALID_TOKEN_NAME",t[t.NO_NON_EMPTY_LOOKAHEAD=10]="NO_NON_EMPTY_LOOKAHEAD",t[t.AMBIGUOUS_PREFIX_ALTS=11]="AMBIGUOUS_PREFIX_ALTS",t[t.TOO_MANY_ALTS=12]="TOO_MANY_ALTS",t[t.CUSTOM_LOOKAHEAD_VALIDATION=13]="CUSTOM_LOOKAHEAD_VALIDATION"})(Se||(Se={}));function cl(t=void 0){return function(){return t}}s(cl,"EMPTY_ALT");var Ws=class t{static{s(this,"Parser")}static performSelfAnalysis(e){throw Error("The **static** `performSelfAnalysis` method has been deprecated.	\nUse the **instance** method with the same name instead.")}performSelfAnalysis(){this.TRACE_INIT("performSelfAnalysis",()=>{let e;this.selfAnalysisDone=!0;let r=this.className;this.TRACE_INIT("toFastProps",()=>{ws(this)}),this.TRACE_INIT("Grammar Recording",()=>{try{this.enableRecording(),S(this.definedRulesNames,i=>{let o=this[i].originalGrammarAction,l;this.TRACE_INIT(`${i} Rule`,()=>{l=this.topLevelRuleRecord(i,o)}),this.gastProductionsCache[i]=l})}finally{this.disableRecording()}});let n=[];if(this.TRACE_INIT("Grammar Resolving",()=>{n=Gh({rules:q(this.gastProductionsCache)}),this.definitionErrors=this.definitionErrors.concat(n)}),this.TRACE_INIT("Grammar Validations",()=>{if(D(n)&&this.skipValidations===!1){let i=Uh({rules:q(this.gastProductionsCache),tokenTypes:q(this.tokensMap),errMsgProvider:Tt,grammarName:r}),a=Oh({lookaheadStrategy:this.lookaheadStrategy,rules:q(this.gastProductionsCache),tokenTypes:q(this.tokensMap),grammarName:r});this.definitionErrors=this.definitionErrors.concat(i,a)}}),D(this.definitionErrors)&&(this.recoveryEnabled&&this.TRACE_INIT("computeAllProdsFollows",()=>{let i=zm(q(this.gastProductionsCache));this.resyncFollows=i}),this.TRACE_INIT("ComputeLookaheadFunctions",()=>{var i,a;(a=(i=this.lookaheadStrategy).initialize)===null||a===void 0||a.call(i,{rules:q(this.gastProductionsCache)}),this.preComputeLookaheadFunctions(q(this.gastProductionsCache))})),!t.DEFER_DEFINITION_ERRORS_HANDLING&&!D(this.definitionErrors))throw e=v(this.definitionErrors,i=>i.message),new Error(`Parser Definition Errors detected:
 ${e.join(`
-------------------------------
`)}`)})}constructor(e,r){this.definitionErrors=[],this.selfAnalysisDone=!1;let n=this;if(n.initErrorHandler(r),n.initLexerAdapter(),n.initLooksAhead(r),n.initRecognizerEngine(e,r),n.initRecoverable(r),n.initTreeBuilder(r),n.initContentAssist(),n.initGastRecorder(r),n.initPerformanceTracer(r),k(r,"ignoredIssues"))throw new Error(`The <ignoredIssues> IParserConfig property has been deprecated.
	Please use the <IGNORE_AMBIGUITIES> flag on the relevant DSL method instead.
	See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#IGNORING_AMBIGUITIES
	For further details.`);this.skipValidations=k(r,"skipValidations")?r.skipValidations:qe.skipValidations}};Ws.DEFER_DEFINITION_ERRORS_HANDLING=!1;tg(Ws,[zo,Yo,el,tl,nl,rl,il,sl,al,ul]);var Ks=class extends Ws{static{s(this,"EmbeddedActionsParser")}constructor(e,r=qe){let n=ee(r);n.outputCst=!1,super(e,n)}};function Jr(t,e,r){return`${t.name}_${e}_${r}`}s(Jr,"buildATNKey");var Cr=1,FC=2,rg=4,ng=5;var ji=7,GC=8,UC=9,BC=10,jC=11,ig=12,Hs=class{static{s(this,"AbstractTransition")}constructor(e){this.target=e}isEpsilon(){return!1}},Ui=class extends Hs{static{s(this,"AtomTransition")}constructor(e,r){super(e),this.tokenType=r}},Vs=class extends Hs{static{s(this,"EpsilonTransition")}constructor(e){super(e)}isEpsilon(){return!0}},Bi=class extends Hs{static{s(this,"RuleTransition")}constructor(e,r,n){super(e),this.rule=r,this.followState=n}isEpsilon(){return!0}};function sg(t){let e={decisionMap:{},decisionStates:[],ruleToStartState:new Map,ruleToStopState:new Map,states:[]};WC(e,t);let r=t.length;for(let n=0;n<r;n++){let i=t[n],a=Zr(e,i,i);a!==void 0&&eN(e,i,a)}return e}s(sg,"createATN");function WC(t,e){let r=e.length;for(let n=0;n<r;n++){let i=e[n],a=we(t,i,void 0,{type:FC}),o=we(t,i,void 0,{type:ji});a.stop=o,t.ruleToStartState.set(i,a),t.ruleToStopState.set(i,o)}}s(WC,"createRuleStartAndStopATNStates");function ag(t,e,r){return r instanceof F?Ec(t,e,r.terminalType,r):r instanceof V?QC(t,e,r):r instanceof Y?qC(t,e,r):r instanceof z?XC(t,e,r):r instanceof G?KC(t,e,r):r instanceof X?HC(t,e,r):r instanceof re?VC(t,e,r):r instanceof ne?zC(t,e,r):Zr(t,e,r)}s(ag,"atom");function KC(t,e,r){let n=we(t,e,r,{type:ng});Nr(t,n);let i=Wi(t,e,n,r,Zr(t,e,r));return lg(t,e,r,i)}s(KC,"repetition");function HC(t,e,r){let n=we(t,e,r,{type:ng});Nr(t,n);let i=Wi(t,e,n,r,Zr(t,e,r)),a=Ec(t,e,r.separator,r);return lg(t,e,r,i,a)}s(HC,"repetitionSep");function VC(t,e,r){let n=we(t,e,r,{type:rg});Nr(t,n);let i=Wi(t,e,n,r,Zr(t,e,r));return og(t,e,r,i)}s(VC,"repetitionMandatory");function zC(t,e,r){let n=we(t,e,r,{type:rg});Nr(t,n);let i=Wi(t,e,n,r,Zr(t,e,r)),a=Ec(t,e,r.separator,r);return og(t,e,r,i,a)}s(zC,"repetitionMandatorySep");function qC(t,e,r){let n=we(t,e,r,{type:Cr});Nr(t,n);let i=v(r.definition,o=>ag(t,e,o));return Wi(t,e,n,r,...i)}s(qC,"alternation");function XC(t,e,r){let n=we(t,e,r,{type:Cr});Nr(t,n);let i=Wi(t,e,n,r,Zr(t,e,r));return YC(t,e,r,i)}s(XC,"option");function Zr(t,e,r){let n=Ne(v(r.definition,i=>ag(t,e,i)),i=>i!==void 0);return n.length===1?n[0]:n.length===0?void 0:ZC(t,n)}s(Zr,"block");function og(t,e,r,n,i){let a=n.left,o=n.right,l=we(t,e,r,{type:jC});Nr(t,l);let u=we(t,e,r,{type:ig});return a.loopback=l,u.loopback=l,t.decisionMap[Jr(e,i?"RepetitionMandatoryWithSeparator":"RepetitionMandatory",r.idx)]=l,ve(o,l),i===void 0?(ve(l,a),ve(l,u)):(ve(l,u),ve(l,i.left),ve(i.right,a)),{left:a,right:u}}s(og,"plus");function lg(t,e,r,n,i){let a=n.left,o=n.right,l=we(t,e,r,{type:BC});Nr(t,l);let u=we(t,e,r,{type:ig}),c=we(t,e,r,{type:UC});return l.loopback=c,u.loopback=c,ve(l,a),ve(l,u),ve(o,c),i!==void 0?(ve(c,u),ve(c,i.left),ve(i.right,a)):ve(c,l),t.decisionMap[Jr(e,i?"RepetitionWithSeparator":"Repetition",r.idx)]=l,{left:l,right:u}}s(lg,"star");function YC(t,e,r,n){let i=n.left,a=n.right;return ve(i,a),t.decisionMap[Jr(e,"Option",r.idx)]=i,n}s(YC,"optional");function Nr(t,e){return t.decisionStates.push(e),e.decision=t.decisionStates.length-1,e.decision}s(Nr,"defineDecisionState");function Wi(t,e,r,n,...i){let a=we(t,e,n,{type:GC,start:r});r.end=a;for(let l of i)l!==void 0?(ve(r,l.left),ve(l.right,a)):ve(r,a);let o={left:r,right:a};return t.decisionMap[Jr(e,JC(n),n.idx)]=r,o}s(Wi,"makeAlts");function JC(t){if(t instanceof Y)return"Alternation";if(t instanceof z)return"Option";if(t instanceof G)return"Repetition";if(t instanceof X)return"RepetitionWithSeparator";if(t instanceof re)return"RepetitionMandatory";if(t instanceof ne)return"RepetitionMandatoryWithSeparator";throw new Error("Invalid production type encountered")}s(JC,"getProdType");function ZC(t,e){let r=e.length;for(let a=0;a<r-1;a++){let o=e[a],l;o.left.transitions.length===1&&(l=o.left.transitions[0]);let u=l instanceof Bi,c=l,f=e[a+1].left;o.left.type===Cr&&o.right.type===Cr&&l!==void 0&&(u&&c.followState===o.right||l.target===o.right)?(u?c.followState=f:l.target=f,tN(t,o.right)):ve(o.right,f)}let n=e[0],i=e[r-1];return{left:n.left,right:i.right}}s(ZC,"makeBlock");function Ec(t,e,r,n){let i=we(t,e,n,{type:Cr}),a=we(t,e,n,{type:Cr});return vc(i,new Ui(a,r)),{left:i,right:a}}s(Ec,"tokenRef");function QC(t,e,r){let n=r.referencedRule,i=t.ruleToStartState.get(n),a=we(t,e,r,{type:Cr}),o=we(t,e,r,{type:Cr}),l=new Bi(i,n,o);return vc(a,l),{left:a,right:o}}s(QC,"ruleRef");function eN(t,e,r){let n=t.ruleToStartState.get(e);ve(n,r.left);let i=t.ruleToStopState.get(e);return ve(r.right,i),{left:n,right:i}}s(eN,"buildRuleHandle");function ve(t,e){let r=new Vs(e);vc(t,r)}s(ve,"epsilon");function we(t,e,r,n){let i=Object.assign({atn:t,production:r,epsilonOnlyTransitions:!1,rule:e,transitions:[],nextTokenWithinRule:[],stateNumber:t.states.length},n);return t.states.push(i),i}s(we,"newState");function vc(t,e){t.transitions.length===0&&(t.epsilonOnlyTransitions=e.isEpsilon()),t.transitions.push(e)}s(vc,"addTransition");function tN(t,e){t.states.splice(t.states.indexOf(e),1)}s(tN,"removeState");var zs={},Ki=class{static{s(this,"ATNConfigSet")}constructor(){this.map={},this.configs=[]}get size(){return this.configs.length}finalize(){this.map={}}add(e){let r=Ic(e);r in this.map||(this.map[r]=this.configs.length,this.configs.push(e))}get elements(){return this.configs}get alts(){return v(this.configs,e=>e.alt)}get key(){let e="";for(let r in this.map)e+=r+":";return e}};function Ic(t,e=!0){return`${e?`a${t.alt}`:""}s${t.state.stateNumber}:${t.stack.map(r=>r.stateNumber.toString()).join("_")}`}s(Ic,"getATNConfigKey");function rN(t,e){let r={};return n=>{let i=n.toString(),a=r[i];return a!==void 0||(a={atnStartState:t,decision:e,states:{}},r[i]=a),a}}s(rN,"createDFACache");var fl=class{static{s(this,"PredicateSet")}constructor(){this.predicates=[]}is(e){return e>=this.predicates.length||this.predicates[e]}set(e,r){this.predicates[e]=r}toString(){let e="",r=this.predicates.length;for(let n=0;n<r;n++)e+=this.predicates[n]===!0?"1":"0";return e}},ug=new fl,qs=class extends nr{static{s(this,"LLStarLookaheadStrategy")}constructor(e){var r;super(),this.logging=(r=e?.logging)!==null&&r!==void 0?r:n=>console.log(n)}initialize(e){this.atn=sg(e.rules),this.dfas=nN(this.atn)}validateAmbiguousAlternationAlternatives(){return[]}validateEmptyOrAlternatives(){return[]}buildLookaheadForAlternation(e){let{prodOccurrence:r,rule:n,hasPredicates:i,dynamicTokensEnabled:a}=e,o=this.dfas,l=this.logging,u=Jr(n,"Alternation",r),f=this.atn.decisionMap[u].decision,d=v(Ko({maxLookahead:1,occurrence:r,prodType:"Alternation",rule:n}),p=>v(p,m=>m[0]));if(cg(d,!1)&&!a){let p=me(d,(m,g,y)=>(S(g,R=>{R&&(m[R.tokenTypeIdx]=y,S(R.categoryMatches,T=>{m[T]=y}))}),m),{});return i?function(m){var g;let y=this.LA(1),R=p[y.tokenTypeIdx];if(m!==void 0&&R!==void 0){let T=(g=m[R])===null||g===void 0?void 0:g.GATE;if(T!==void 0&&T.call(this)===!1)return}return R}:function(){let m=this.LA(1);return p[m.tokenTypeIdx]}}else return i?function(p){let m=new fl,g=p===void 0?0:p.length;for(let R=0;R<g;R++){let T=p?.[R].GATE;m.set(R,T===void 0||T.call(this))}let y=Sc.call(this,o,f,m,l);return typeof y=="number"?y:void 0}:function(){let p=Sc.call(this,o,f,ug,l);return typeof p=="number"?p:void 0}}buildLookaheadForOptional(e){let{prodOccurrence:r,rule:n,prodType:i,dynamicTokensEnabled:a}=e,o=this.dfas,l=this.logging,u=Jr(n,i,r),f=this.atn.decisionMap[u].decision,d=v(Ko({maxLookahead:1,occurrence:r,prodType:i,rule:n}),p=>v(p,m=>m[0]));if(cg(d)&&d[0][0]&&!a){let p=d[0],m=ye(p);if(m.length===1&&D(m[0].categoryMatches)){let y=m[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===y}}else{let g=me(m,(y,R)=>(R!==void 0&&(y[R.tokenTypeIdx]=!0,S(R.categoryMatches,T=>{y[T]=!0})),y),{});return function(){let y=this.LA(1);return g[y.tokenTypeIdx]===!0}}}return function(){let p=Sc.call(this,o,f,ug,l);return typeof p=="object"?!1:p===0}}};function cg(t,e=!0){let r=new Set;for(let n of t){let i=new Set;for(let a of n){if(a===void 0){if(e)break;return!1}let o=[a.tokenTypeIdx].concat(a.categoryMatches);for(let l of o)if(r.has(l)){if(!i.has(l))return!1}else r.add(l),i.add(l)}}return!0}s(cg,"isLL1Sequence");function nN(t){let e=t.decisionStates.length,r=Array(e);for(let n=0;n<e;n++)r[n]=rN(t.decisionStates[n],n);return r}s(nN,"initATNSimulator");function Sc(t,e,r,n){let i=t[e](r),a=i.start;if(a===void 0){let l=mN(i.atnStartState);a=pg(i,dg(l)),i.start=a}return iN.apply(this,[i,a,r,n])}s(Sc,"adaptivePredict");function iN(t,e,r,n){let i=e,a=1,o=[],l=this.LA(a++);for(;;){let u=cN(i,l);if(u===void 0&&(u=sN.apply(this,[t,i,l,a,r,n])),u===zs)return uN(o,i,l);if(u.isAcceptState===!0)return u.prediction;i=u,o.push(l),l=this.LA(a++)}}s(iN,"performLookahead");function sN(t,e,r,n,i,a){let o=fN(e.configs,r,i);if(o.size===0)return fg(t,e,r,zs),zs;let l=dg(o),u=pN(o,i);if(u!==void 0)l.isAcceptState=!0,l.prediction=u,l.configs.uniqueAlt=u;else if(xN(o)){let c=Fm(o.alts);l.isAcceptState=!0,l.prediction=c,l.configs.uniqueAlt=c,aN.apply(this,[t,n,o.alts,a])}return l=fg(t,e,r,l),l}s(sN,"computeLookaheadTarget");function aN(t,e,r,n){let i=[];for(let c=1;c<=e;c++)i.push(this.LA(c).tokenType);let a=t.atnStartState,o=a.rule,l=a.production,u=oN({topLevelRule:o,ambiguityIndices:r,production:l,prefixPath:i});n(u)}s(aN,"reportLookaheadAmbiguity");function oN(t){let e=v(t.prefixPath,i=>er(i)).join(", "),r=t.production.idx===0?"":t.production.idx,n=`Ambiguous Alternatives Detected: <${t.ambiguityIndices.join(", ")}> in <${lN(t.production)}${r}> inside <${t.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
`;return n=n+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,n}s(oN,"buildAmbiguityError");function lN(t){if(t instanceof V)return"SUBRULE";if(t instanceof z)return"OPTION";if(t instanceof Y)return"OR";if(t instanceof re)return"AT_LEAST_ONE";if(t instanceof ne)return"AT_LEAST_ONE_SEP";if(t instanceof X)return"MANY_SEP";if(t instanceof G)return"MANY";if(t instanceof F)return"CONSUME";throw Error("non exhaustive match")}s(lN,"getProductionDslName");function uN(t,e,r){let n=$e(e.configs.elements,a=>a.state.transitions),i=Hm(n.filter(a=>a instanceof Ui).map(a=>a.tokenType),a=>a.tokenTypeIdx);return{actualToken:r,possibleTokenTypes:i,tokenPath:t}}s(uN,"buildAdaptivePredictError");function cN(t,e){return t.edges[e.tokenTypeIdx]}s(cN,"getExistingTargetState");function fN(t,e,r){let n=new Ki,i=[];for(let o of t.elements){if(r.is(o.alt)===!1)continue;if(o.state.type===ji){i.push(o);continue}let l=o.state.transitions.length;for(let u=0;u<l;u++){let c=o.state.transitions[u],f=dN(c,e);f!==void 0&&n.add({state:f,alt:o.alt,stack:o.stack})}}let a;if(i.length===0&&n.size===1&&(a=n),a===void 0){a=new Ki;for(let o of n.elements)dl(o,a)}if(i.length>0&&!gN(a))for(let o of i)a.add(o);return a}s(fN,"computeReachSet");function dN(t,e){if(t instanceof Ui&&$s(e,t.tokenType))return t.target}s(dN,"getReachableTarget");function pN(t,e){let r;for(let n of t.elements)if(e.is(n.alt)===!0){if(r===void 0)r=n.alt;else if(r!==n.alt)return}return r}s(pN,"getUniqueAlt");function dg(t){return{configs:t,edges:{},isAcceptState:!1,prediction:-1}}s(dg,"newDFAState");function fg(t,e,r,n){return n=pg(t,n),e.edges[r.tokenTypeIdx]=n,n}s(fg,"addDFAEdge");function pg(t,e){if(e===zs)return e;let r=e.configs.key,n=t.states[r];return n!==void 0?n:(e.configs.finalize(),t.states[r]=e,e)}s(pg,"addDFAState");function mN(t){let e=new Ki,r=t.transitions.length;for(let n=0;n<r;n++){let a={state:t.transitions[n].target,alt:n,stack:[]};dl(a,e)}return e}s(mN,"computeStartState");function dl(t,e){let r=t.state;if(r.type===ji){if(t.stack.length>0){let i=[...t.stack],o={state:i.pop(),alt:t.alt,stack:i};dl(o,e)}else e.add(t);return}r.epsilonOnlyTransitions||e.add(t);let n=r.transitions.length;for(let i=0;i<n;i++){let a=r.transitions[i],o=hN(t,a);o!==void 0&&dl(o,e)}}s(dl,"closure");function hN(t,e){if(e instanceof Vs)return{state:e.target,alt:t.alt,stack:t.stack};if(e instanceof Bi){let r=[...t.stack,e.followState];return{state:e.target,alt:t.alt,stack:r}}}s(hN,"getEpsilonTarget");function gN(t){for(let e of t.elements)if(e.state.type===ji)return!0;return!1}s(gN,"hasConfigInRuleStopState");function yN(t){for(let e of t.elements)if(e.state.type!==ji)return!1;return!0}s(yN,"allConfigsInRuleStopStates");function xN(t){if(yN(t))return!0;let e=TN(t.elements);return AN(e)&&!RN(e)}s(xN,"hasConflictTerminatingPrediction");function TN(t){let e=new Map;for(let r of t){let n=Ic(r,!1),i=e.get(n);i===void 0&&(i={},e.set(n,i)),i[r.alt]=!0}return e}s(TN,"getConflictingAltSets");function AN(t){for(let e of Array.from(t.values()))if(Object.keys(e).length>1)return!0;return!1}s(AN,"hasConflictingAltSet");function RN(t){for(let e of Array.from(t.values()))if(Object.keys(e).length===1)return!0;return!1}s(RN,"hasStateAssociatedWithOneAlt");var mg;(function(t){function e(r){return typeof r=="string"}s(e,"is"),t.is=e})(mg||(mg={}));var kc;(function(t){function e(r){return typeof r=="string"}s(e,"is"),t.is=e})(kc||(kc={}));var hg;(function(t){t.MIN_VALUE=-2147483648,t.MAX_VALUE=2147483647;function e(r){return typeof r=="number"&&t.MIN_VALUE<=r&&r<=t.MAX_VALUE}s(e,"is"),t.is=e})(hg||(hg={}));var pl;(function(t){t.MIN_VALUE=0,t.MAX_VALUE=2147483647;function e(r){return typeof r=="number"&&t.MIN_VALUE<=r&&r<=t.MAX_VALUE}s(e,"is"),t.is=e})(pl||(pl={}));var j;(function(t){function e(n,i){return n===Number.MAX_VALUE&&(n=pl.MAX_VALUE),i===Number.MAX_VALUE&&(i=pl.MAX_VALUE),{line:n,character:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&h.uinteger(i.line)&&h.uinteger(i.character)}s(r,"is"),t.is=r})(j||(j={}));var U;(function(t){function e(n,i,a,o){if(h.uinteger(n)&&h.uinteger(i)&&h.uinteger(a)&&h.uinteger(o))return{start:j.create(n,i),end:j.create(a,o)};if(j.is(n)&&j.is(i))return{start:n,end:i};throw new Error(`Range#create called with invalid arguments[${n}, ${i}, ${a}, ${o}]`)}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&j.is(i.start)&&j.is(i.end)}s(r,"is"),t.is=r})(U||(U={}));var ml;(function(t){function e(n,i){return{uri:n,range:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&U.is(i.range)&&(h.string(i.uri)||h.undefined(i.uri))}s(r,"is"),t.is=r})(ml||(ml={}));var gg;(function(t){function e(n,i,a,o){return{targetUri:n,targetRange:i,targetSelectionRange:a,originSelectionRange:o}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&U.is(i.targetRange)&&h.string(i.targetUri)&&U.is(i.targetSelectionRange)&&(U.is(i.originSelectionRange)||h.undefined(i.originSelectionRange))}s(r,"is"),t.is=r})(gg||(gg={}));var Cc;(function(t){function e(n,i,a,o){return{red:n,green:i,blue:a,alpha:o}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&h.numberRange(i.red,0,1)&&h.numberRange(i.green,0,1)&&h.numberRange(i.blue,0,1)&&h.numberRange(i.alpha,0,1)}s(r,"is"),t.is=r})(Cc||(Cc={}));var yg;(function(t){function e(n,i){return{range:n,color:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&U.is(i.range)&&Cc.is(i.color)}s(r,"is"),t.is=r})(yg||(yg={}));var xg;(function(t){function e(n,i,a){return{label:n,textEdit:i,additionalTextEdits:a}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&h.string(i.label)&&(h.undefined(i.textEdit)||Vi.is(i))&&(h.undefined(i.additionalTextEdits)||h.typedArray(i.additionalTextEdits,Vi.is))}s(r,"is"),t.is=r})(xg||(xg={}));var Tg;(function(t){t.Comment="comment",t.Imports="imports",t.Region="region"})(Tg||(Tg={}));var Ag;(function(t){function e(n,i,a,o,l,u){let c={startLine:n,endLine:i};return h.defined(a)&&(c.startCharacter=a),h.defined(o)&&(c.endCharacter=o),h.defined(l)&&(c.kind=l),h.defined(u)&&(c.collapsedText=u),c}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&h.uinteger(i.startLine)&&h.uinteger(i.startLine)&&(h.undefined(i.startCharacter)||h.uinteger(i.startCharacter))&&(h.undefined(i.endCharacter)||h.uinteger(i.endCharacter))&&(h.undefined(i.kind)||h.string(i.kind))}s(r,"is"),t.is=r})(Ag||(Ag={}));var Nc;(function(t){function e(n,i){return{location:n,message:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&ml.is(i.location)&&h.string(i.message)}s(r,"is"),t.is=r})(Nc||(Nc={}));var Rg;(function(t){t.Error=1,t.Warning=2,t.Information=3,t.Hint=4})(Rg||(Rg={}));var Eg;(function(t){t.Unnecessary=1,t.Deprecated=2})(Eg||(Eg={}));var vg;(function(t){function e(r){let n=r;return h.objectLiteral(n)&&h.string(n.href)}s(e,"is"),t.is=e})(vg||(vg={}));var hl;(function(t){function e(n,i,a,o,l,u){let c={range:n,message:i};return h.defined(a)&&(c.severity=a),h.defined(o)&&(c.code=o),h.defined(l)&&(c.source=l),h.defined(u)&&(c.relatedInformation=u),c}s(e,"create"),t.create=e;function r(n){var i;let a=n;return h.defined(a)&&U.is(a.range)&&h.string(a.message)&&(h.number(a.severity)||h.undefined(a.severity))&&(h.integer(a.code)||h.string(a.code)||h.undefined(a.code))&&(h.undefined(a.codeDescription)||h.string((i=a.codeDescription)===null||i===void 0?void 0:i.href))&&(h.string(a.source)||h.undefined(a.source))&&(h.undefined(a.relatedInformation)||h.typedArray(a.relatedInformation,Nc.is))}s(r,"is"),t.is=r})(hl||(hl={}));var Hi;(function(t){function e(n,i,...a){let o={title:n,command:i};return h.defined(a)&&a.length>0&&(o.arguments=a),o}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&h.string(i.title)&&h.string(i.command)}s(r,"is"),t.is=r})(Hi||(Hi={}));var Vi;(function(t){function e(a,o){return{range:a,newText:o}}s(e,"replace"),t.replace=e;function r(a,o){return{range:{start:a,end:a},newText:o}}s(r,"insert"),t.insert=r;function n(a){return{range:a,newText:""}}s(n,"del"),t.del=n;function i(a){let o=a;return h.objectLiteral(o)&&h.string(o.newText)&&U.is(o.range)}s(i,"is"),t.is=i})(Vi||(Vi={}));var _c;(function(t){function e(n,i,a){let o={label:n};return i!==void 0&&(o.needsConfirmation=i),a!==void 0&&(o.description=a),o}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&h.string(i.label)&&(h.boolean(i.needsConfirmation)||i.needsConfirmation===void 0)&&(h.string(i.description)||i.description===void 0)}s(r,"is"),t.is=r})(_c||(_c={}));var zi;(function(t){function e(r){let n=r;return h.string(n)}s(e,"is"),t.is=e})(zi||(zi={}));var Ig;(function(t){function e(a,o,l){return{range:a,newText:o,annotationId:l}}s(e,"replace"),t.replace=e;function r(a,o,l){return{range:{start:a,end:a},newText:o,annotationId:l}}s(r,"insert"),t.insert=r;function n(a,o){return{range:a,newText:"",annotationId:o}}s(n,"del"),t.del=n;function i(a){let o=a;return Vi.is(o)&&(_c.is(o.annotationId)||zi.is(o.annotationId))}s(i,"is"),t.is=i})(Ig||(Ig={}));var wc;(function(t){function e(n,i){return{textDocument:n,edits:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&Pc.is(i.textDocument)&&Array.isArray(i.edits)}s(r,"is"),t.is=r})(wc||(wc={}));var bc;(function(t){function e(n,i,a){let o={kind:"create",uri:n};return i!==void 0&&(i.overwrite!==void 0||i.ignoreIfExists!==void 0)&&(o.options=i),a!==void 0&&(o.annotationId=a),o}s(e,"create"),t.create=e;function r(n){let i=n;return i&&i.kind==="create"&&h.string(i.uri)&&(i.options===void 0||(i.options.overwrite===void 0||h.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||h.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||zi.is(i.annotationId))}s(r,"is"),t.is=r})(bc||(bc={}));var Oc;(function(t){function e(n,i,a,o){let l={kind:"rename",oldUri:n,newUri:i};return a!==void 0&&(a.overwrite!==void 0||a.ignoreIfExists!==void 0)&&(l.options=a),o!==void 0&&(l.annotationId=o),l}s(e,"create"),t.create=e;function r(n){let i=n;return i&&i.kind==="rename"&&h.string(i.oldUri)&&h.string(i.newUri)&&(i.options===void 0||(i.options.overwrite===void 0||h.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||h.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||zi.is(i.annotationId))}s(r,"is"),t.is=r})(Oc||(Oc={}));var $c;(function(t){function e(n,i,a){let o={kind:"delete",uri:n};return i!==void 0&&(i.recursive!==void 0||i.ignoreIfNotExists!==void 0)&&(o.options=i),a!==void 0&&(o.annotationId=a),o}s(e,"create"),t.create=e;function r(n){let i=n;return i&&i.kind==="delete"&&h.string(i.uri)&&(i.options===void 0||(i.options.recursive===void 0||h.boolean(i.options.recursive))&&(i.options.ignoreIfNotExists===void 0||h.boolean(i.options.ignoreIfNotExists)))&&(i.annotationId===void 0||zi.is(i.annotationId))}s(r,"is"),t.is=r})($c||($c={}));var Lc;(function(t){function e(r){let n=r;return n&&(n.changes!==void 0||n.documentChanges!==void 0)&&(n.documentChanges===void 0||n.documentChanges.every(i=>h.string(i.kind)?bc.is(i)||Oc.is(i)||$c.is(i):wc.is(i)))}s(e,"is"),t.is=e})(Lc||(Lc={}));var Sg;(function(t){function e(n){return{uri:n}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&h.string(i.uri)}s(r,"is"),t.is=r})(Sg||(Sg={}));var kg;(function(t){function e(n,i){return{uri:n,version:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&h.string(i.uri)&&h.integer(i.version)}s(r,"is"),t.is=r})(kg||(kg={}));var Pc;(function(t){function e(n,i){return{uri:n,version:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&h.string(i.uri)&&(i.version===null||h.integer(i.version))}s(r,"is"),t.is=r})(Pc||(Pc={}));var Cg;(function(t){function e(n,i,a,o){return{uri:n,languageId:i,version:a,text:o}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&h.string(i.uri)&&h.string(i.languageId)&&h.integer(i.version)&&h.string(i.text)}s(r,"is"),t.is=r})(Cg||(Cg={}));var Mc;(function(t){t.PlainText="plaintext",t.Markdown="markdown";function e(r){let n=r;return n===t.PlainText||n===t.Markdown}s(e,"is"),t.is=e})(Mc||(Mc={}));var Xs;(function(t){function e(r){let n=r;return h.objectLiteral(r)&&Mc.is(n.kind)&&h.string(n.value)}s(e,"is"),t.is=e})(Xs||(Xs={}));var Ng;(function(t){t.Text=1,t.Method=2,t.Function=3,t.Constructor=4,t.Field=5,t.Variable=6,t.Class=7,t.Interface=8,t.Module=9,t.Property=10,t.Unit=11,t.Value=12,t.Enum=13,t.Keyword=14,t.Snippet=15,t.Color=16,t.File=17,t.Reference=18,t.Folder=19,t.EnumMember=20,t.Constant=21,t.Struct=22,t.Event=23,t.Operator=24,t.TypeParameter=25})(Ng||(Ng={}));var _g;(function(t){t.PlainText=1,t.Snippet=2})(_g||(_g={}));var wg;(function(t){t.Deprecated=1})(wg||(wg={}));var bg;(function(t){function e(n,i,a){return{newText:n,insert:i,replace:a}}s(e,"create"),t.create=e;function r(n){let i=n;return i&&h.string(i.newText)&&U.is(i.insert)&&U.is(i.replace)}s(r,"is"),t.is=r})(bg||(bg={}));var Og;(function(t){t.asIs=1,t.adjustIndentation=2})(Og||(Og={}));var $g;(function(t){function e(r){let n=r;return n&&(h.string(n.detail)||n.detail===void 0)&&(h.string(n.description)||n.description===void 0)}s(e,"is"),t.is=e})($g||($g={}));var Lg;(function(t){function e(r){return{label:r}}s(e,"create"),t.create=e})(Lg||(Lg={}));var Pg;(function(t){function e(r,n){return{items:r||[],isIncomplete:!!n}}s(e,"create"),t.create=e})(Pg||(Pg={}));var gl;(function(t){function e(n){return n.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}s(e,"fromPlainText"),t.fromPlainText=e;function r(n){let i=n;return h.string(i)||h.objectLiteral(i)&&h.string(i.language)&&h.string(i.value)}s(r,"is"),t.is=r})(gl||(gl={}));var Mg;(function(t){function e(r){let n=r;return!!n&&h.objectLiteral(n)&&(Xs.is(n.contents)||gl.is(n.contents)||h.typedArray(n.contents,gl.is))&&(r.range===void 0||U.is(r.range))}s(e,"is"),t.is=e})(Mg||(Mg={}));var Dg;(function(t){function e(r,n){return n?{label:r,documentation:n}:{label:r}}s(e,"create"),t.create=e})(Dg||(Dg={}));var Fg;(function(t){function e(r,n,...i){let a={label:r};return h.defined(n)&&(a.documentation=n),h.defined(i)?a.parameters=i:a.parameters=[],a}s(e,"create"),t.create=e})(Fg||(Fg={}));var Gg;(function(t){t.Text=1,t.Read=2,t.Write=3})(Gg||(Gg={}));var Ug;(function(t){function e(r,n){let i={range:r};return h.number(n)&&(i.kind=n),i}s(e,"create"),t.create=e})(Ug||(Ug={}));var Bg;(function(t){t.File=1,t.Module=2,t.Namespace=3,t.Package=4,t.Class=5,t.Method=6,t.Property=7,t.Field=8,t.Constructor=9,t.Enum=10,t.Interface=11,t.Function=12,t.Variable=13,t.Constant=14,t.String=15,t.Number=16,t.Boolean=17,t.Array=18,t.Object=19,t.Key=20,t.Null=21,t.EnumMember=22,t.Struct=23,t.Event=24,t.Operator=25,t.TypeParameter=26})(Bg||(Bg={}));var jg;(function(t){t.Deprecated=1})(jg||(jg={}));var Wg;(function(t){function e(r,n,i,a,o){let l={name:r,kind:n,location:{uri:a,range:i}};return o&&(l.containerName=o),l}s(e,"create"),t.create=e})(Wg||(Wg={}));var Kg;(function(t){function e(r,n,i,a){return a!==void 0?{name:r,kind:n,location:{uri:i,range:a}}:{name:r,kind:n,location:{uri:i}}}s(e,"create"),t.create=e})(Kg||(Kg={}));var Hg;(function(t){function e(n,i,a,o,l,u){let c={name:n,detail:i,kind:a,range:o,selectionRange:l};return u!==void 0&&(c.children=u),c}s(e,"create"),t.create=e;function r(n){let i=n;return i&&h.string(i.name)&&h.number(i.kind)&&U.is(i.range)&&U.is(i.selectionRange)&&(i.detail===void 0||h.string(i.detail))&&(i.deprecated===void 0||h.boolean(i.deprecated))&&(i.children===void 0||Array.isArray(i.children))&&(i.tags===void 0||Array.isArray(i.tags))}s(r,"is"),t.is=r})(Hg||(Hg={}));var Vg;(function(t){t.Empty="",t.QuickFix="quickfix",t.Refactor="refactor",t.RefactorExtract="refactor.extract",t.RefactorInline="refactor.inline",t.RefactorRewrite="refactor.rewrite",t.Source="source",t.SourceOrganizeImports="source.organizeImports",t.SourceFixAll="source.fixAll"})(Vg||(Vg={}));var yl;(function(t){t.Invoked=1,t.Automatic=2})(yl||(yl={}));var zg;(function(t){function e(n,i,a){let o={diagnostics:n};return i!=null&&(o.only=i),a!=null&&(o.triggerKind=a),o}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&h.typedArray(i.diagnostics,hl.is)&&(i.only===void 0||h.typedArray(i.only,h.string))&&(i.triggerKind===void 0||i.triggerKind===yl.Invoked||i.triggerKind===yl.Automatic)}s(r,"is"),t.is=r})(zg||(zg={}));var qg;(function(t){function e(n,i,a){let o={title:n},l=!0;return typeof i=="string"?(l=!1,o.kind=i):Hi.is(i)?o.command=i:o.edit=i,l&&a!==void 0&&(o.kind=a),o}s(e,"create"),t.create=e;function r(n){let i=n;return i&&h.string(i.title)&&(i.diagnostics===void 0||h.typedArray(i.diagnostics,hl.is))&&(i.kind===void 0||h.string(i.kind))&&(i.edit!==void 0||i.command!==void 0)&&(i.command===void 0||Hi.is(i.command))&&(i.isPreferred===void 0||h.boolean(i.isPreferred))&&(i.edit===void 0||Lc.is(i.edit))}s(r,"is"),t.is=r})(qg||(qg={}));var Xg;(function(t){function e(n,i){let a={range:n};return h.defined(i)&&(a.data=i),a}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&U.is(i.range)&&(h.undefined(i.command)||Hi.is(i.command))}s(r,"is"),t.is=r})(Xg||(Xg={}));var Yg;(function(t){function e(n,i){return{tabSize:n,insertSpaces:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&h.uinteger(i.tabSize)&&h.boolean(i.insertSpaces)}s(r,"is"),t.is=r})(Yg||(Yg={}));var Jg;(function(t){function e(n,i,a){return{range:n,target:i,data:a}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&U.is(i.range)&&(h.undefined(i.target)||h.string(i.target))}s(r,"is"),t.is=r})(Jg||(Jg={}));var Zg;(function(t){function e(n,i){return{range:n,parent:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&U.is(i.range)&&(i.parent===void 0||t.is(i.parent))}s(r,"is"),t.is=r})(Zg||(Zg={}));var Qg;(function(t){t.namespace="namespace",t.type="type",t.class="class",t.enum="enum",t.interface="interface",t.struct="struct",t.typeParameter="typeParameter",t.parameter="parameter",t.variable="variable",t.property="property",t.enumMember="enumMember",t.event="event",t.function="function",t.method="method",t.macro="macro",t.keyword="keyword",t.modifier="modifier",t.comment="comment",t.string="string",t.number="number",t.regexp="regexp",t.operator="operator",t.decorator="decorator"})(Qg||(Qg={}));var ey;(function(t){t.declaration="declaration",t.definition="definition",t.readonly="readonly",t.static="static",t.deprecated="deprecated",t.abstract="abstract",t.async="async",t.modification="modification",t.documentation="documentation",t.defaultLibrary="defaultLibrary"})(ey||(ey={}));var ty;(function(t){function e(r){let n=r;return h.objectLiteral(n)&&(n.resultId===void 0||typeof n.resultId=="string")&&Array.isArray(n.data)&&(n.data.length===0||typeof n.data[0]=="number")}s(e,"is"),t.is=e})(ty||(ty={}));var ry;(function(t){function e(n,i){return{range:n,text:i}}s(e,"create"),t.create=e;function r(n){let i=n;return i!=null&&U.is(i.range)&&h.string(i.text)}s(r,"is"),t.is=r})(ry||(ry={}));var ny;(function(t){function e(n,i,a){return{range:n,variableName:i,caseSensitiveLookup:a}}s(e,"create"),t.create=e;function r(n){let i=n;return i!=null&&U.is(i.range)&&h.boolean(i.caseSensitiveLookup)&&(h.string(i.variableName)||i.variableName===void 0)}s(r,"is"),t.is=r})(ny||(ny={}));var iy;(function(t){function e(n,i){return{range:n,expression:i}}s(e,"create"),t.create=e;function r(n){let i=n;return i!=null&&U.is(i.range)&&(h.string(i.expression)||i.expression===void 0)}s(r,"is"),t.is=r})(iy||(iy={}));var sy;(function(t){function e(n,i){return{frameId:n,stoppedLocation:i}}s(e,"create"),t.create=e;function r(n){let i=n;return h.defined(i)&&U.is(n.stoppedLocation)}s(r,"is"),t.is=r})(sy||(sy={}));var Dc;(function(t){t.Type=1,t.Parameter=2;function e(r){return r===1||r===2}s(e,"is"),t.is=e})(Dc||(Dc={}));var Fc;(function(t){function e(n){return{value:n}}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&(i.tooltip===void 0||h.string(i.tooltip)||Xs.is(i.tooltip))&&(i.location===void 0||ml.is(i.location))&&(i.command===void 0||Hi.is(i.command))}s(r,"is"),t.is=r})(Fc||(Fc={}));var ay;(function(t){function e(n,i,a){let o={position:n,label:i};return a!==void 0&&(o.kind=a),o}s(e,"create"),t.create=e;function r(n){let i=n;return h.objectLiteral(i)&&j.is(i.position)&&(h.string(i.label)||h.typedArray(i.label,Fc.is))&&(i.kind===void 0||Dc.is(i.kind))&&i.textEdits===void 0||h.typedArray(i.textEdits,Vi.is)&&(i.tooltip===void 0||h.string(i.tooltip)||Xs.is(i.tooltip))&&(i.paddingLeft===void 0||h.boolean(i.paddingLeft))&&(i.paddingRight===void 0||h.boolean(i.paddingRight))}s(r,"is"),t.is=r})(ay||(ay={}));var oy;(function(t){function e(r){return{kind:"snippet",value:r}}s(e,"createSnippet"),t.createSnippet=e})(oy||(oy={}));var ly;(function(t){function e(r,n,i,a){return{insertText:r,filterText:n,range:i,command:a}}s(e,"create"),t.create=e})(ly||(ly={}));var uy;(function(t){function e(r){return{items:r}}s(e,"create"),t.create=e})(uy||(uy={}));var cy;(function(t){t.Invoked=0,t.Automatic=1})(cy||(cy={}));var fy;(function(t){function e(r,n){return{range:r,text:n}}s(e,"create"),t.create=e})(fy||(fy={}));var dy;(function(t){function e(r,n){return{triggerKind:r,selectedCompletionInfo:n}}s(e,"create"),t.create=e})(dy||(dy={}));var py;(function(t){function e(r){let n=r;return h.objectLiteral(n)&&kc.is(n.uri)&&h.string(n.name)}s(e,"is"),t.is=e})(py||(py={}));var my;(function(t){function e(a,o,l,u){return new Gc(a,o,l,u)}s(e,"create"),t.create=e;function r(a){let o=a;return!!(h.defined(o)&&h.string(o.uri)&&(h.undefined(o.languageId)||h.string(o.languageId))&&h.uinteger(o.lineCount)&&h.func(o.getText)&&h.func(o.positionAt)&&h.func(o.offsetAt))}s(r,"is"),t.is=r;function n(a,o){let l=a.getText(),u=i(o,(f,d)=>{let p=f.range.start.line-d.range.start.line;return p===0?f.range.start.character-d.range.start.character:p}),c=l.length;for(let f=u.length-1;f>=0;f--){let d=u[f],p=a.offsetAt(d.range.start),m=a.offsetAt(d.range.end);if(m<=c)l=l.substring(0,p)+d.newText+l.substring(m,l.length);else throw new Error("Overlapping edit");c=p}return l}s(n,"applyEdits"),t.applyEdits=n;function i(a,o){if(a.length<=1)return a;let l=a.length/2|0,u=a.slice(0,l),c=a.slice(l);i(u,o),i(c,o);let f=0,d=0,p=0;for(;f<u.length&&d<c.length;)o(u[f],c[d])<=0?a[p++]=u[f++]:a[p++]=c[d++];for(;f<u.length;)a[p++]=u[f++];for(;d<c.length;)a[p++]=c[d++];return a}s(i,"mergeSort")})(my||(my={}));var Gc=class{static{s(this,"FullTextDocument")}constructor(e,r,n,i){this._uri=e,this._languageId=r,this._version=n,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let r=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(r,n)}return this._content}update(e,r){this._content=e.text,this._version=r,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let e=[],r=this._content,n=!0;for(let i=0;i<r.length;i++){n&&(e.push(i),n=!1);let a=r.charAt(i);n=a==="\r"||a===`
`,a==="\r"&&i+1<r.length&&r.charAt(i+1)===`
`&&i++}n&&r.length>0&&e.push(r.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let r=this.getLineOffsets(),n=0,i=r.length;if(i===0)return j.create(0,e);for(;n<i;){let o=Math.floor((n+i)/2);r[o]>e?i=o:n=o+1}let a=n-1;return j.create(a,e-r[a])}offsetAt(e){let r=this.getLineOffsets();if(e.line>=r.length)return this._content.length;if(e.line<0)return 0;let n=r[e.line],i=e.line+1<r.length?r[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,i),n)}get lineCount(){return this.getLineOffsets().length}},h;(function(t){let e=Object.prototype.toString;function r(m){return typeof m<"u"}s(r,"defined"),t.defined=r;function n(m){return typeof m>"u"}s(n,"undefined"),t.undefined=n;function i(m){return m===!0||m===!1}s(i,"boolean"),t.boolean=i;function a(m){return e.call(m)==="[object String]"}s(a,"string"),t.string=a;function o(m){return e.call(m)==="[object Number]"}s(o,"number"),t.number=o;function l(m,g,y){return e.call(m)==="[object Number]"&&g<=m&&m<=y}s(l,"numberRange"),t.numberRange=l;function u(m){return e.call(m)==="[object Number]"&&-2147483648<=m&&m<=2147483647}s(u,"integer"),t.integer=u;function c(m){return e.call(m)==="[object Number]"&&0<=m&&m<=2147483647}s(c,"uinteger"),t.uinteger=c;function f(m){return e.call(m)==="[object Function]"}s(f,"func"),t.func=f;function d(m){return m!==null&&typeof m=="object"}s(d,"objectLiteral"),t.objectLiteral=d;function p(m,g){return Array.isArray(m)&&m.every(g)}s(p,"typedArray"),t.typedArray=p})(h||(h={}));var Ys=class{static{s(this,"CstNodeBuilder")}constructor(){this.nodeStack=[]}get current(){var e;return(e=this.nodeStack[this.nodeStack.length-1])!==null&&e!==void 0?e:this.rootNode}buildRootNode(e){return this.rootNode=new qi(e),this.rootNode.root=this.rootNode,this.nodeStack=[this.rootNode],this.rootNode}buildCompositeNode(e){let r=new en;return r.grammarSource=e,r.root=this.rootNode,this.current.content.push(r),this.nodeStack.push(r),r}buildLeafNode(e,r){let n=new Qr(e.startOffset,e.image.length,dn(e),e.tokenType,!r);return n.grammarSource=r,n.root=this.rootNode,this.current.content.push(n),n}removeNode(e){let r=e.container;if(r){let n=r.content.indexOf(e);n>=0&&r.content.splice(n,1)}}addHiddenNodes(e){let r=[];for(let a of e){let o=new Qr(a.startOffset,a.image.length,dn(a),a.tokenType,!0);o.root=this.rootNode,r.push(o)}let n=this.current,i=!1;if(n.content.length>0){n.content.push(...r);return}for(;n.container;){let a=n.container.content.indexOf(n);if(a>0){n.container.content.splice(a,0,...r),i=!0;break}n=n.container}i||this.rootNode.content.unshift(...r)}construct(e){let r=this.current;typeof e.$type=="string"&&(this.current.astNode=e),e.$cstNode=r;let n=this.nodeStack.pop();n?.content.length===0&&this.removeNode(n)}},Js=class{static{s(this,"AbstractCstNode")}get parent(){return this.container}get feature(){return this.grammarSource}get hidden(){return!1}get astNode(){var e,r;let n=typeof((e=this._astNode)===null||e===void 0?void 0:e.$type)=="string"?this._astNode:(r=this.container)===null||r===void 0?void 0:r.astNode;if(!n)throw new Error("This node has no associated AST element");return n}set astNode(e){this._astNode=e}get element(){return this.astNode}get text(){return this.root.fullText.substring(this.offset,this.end)}},Qr=class extends Js{static{s(this,"LeafCstNodeImpl")}get offset(){return this._offset}get length(){return this._length}get end(){return this._offset+this._length}get hidden(){return this._hidden}get tokenType(){return this._tokenType}get range(){return this._range}constructor(e,r,n,i,a=!1){super(),this._hidden=a,this._offset=e,this._tokenType=i,this._length=r,this._range=n}},en=class extends Js{static{s(this,"CompositeCstNodeImpl")}constructor(){super(...arguments),this.content=new Uc(this)}get children(){return this.content}get offset(){var e,r;return(r=(e=this.firstNonHiddenNode)===null||e===void 0?void 0:e.offset)!==null&&r!==void 0?r:0}get length(){return this.end-this.offset}get end(){var e,r;return(r=(e=this.lastNonHiddenNode)===null||e===void 0?void 0:e.end)!==null&&r!==void 0?r:0}get range(){let e=this.firstNonHiddenNode,r=this.lastNonHiddenNode;if(e&&r){if(this._rangeCache===void 0){let{range:n}=e,{range:i}=r;this._rangeCache={start:n.start,end:i.end.line<n.start.line?n.start:i.end}}return this._rangeCache}else return{start:j.create(0,0),end:j.create(0,0)}}get firstNonHiddenNode(){for(let e of this.content)if(!e.hidden)return e;return this.content[0]}get lastNonHiddenNode(){for(let e=this.content.length-1;e>=0;e--){let r=this.content[e];if(!r.hidden)return r}return this.content[this.content.length-1]}},Uc=class t extends Array{static{s(this,"CstNodeContainer")}constructor(e){super(),this.parent=e,Object.setPrototypeOf(this,t.prototype)}push(...e){return this.addParents(e),super.push(...e)}unshift(...e){return this.addParents(e),super.unshift(...e)}splice(e,r,...n){return this.addParents(n),super.splice(e,r,...n)}addParents(e){for(let r of e)r.container=this.parent}},qi=class extends en{static{s(this,"RootCstNodeImpl")}get text(){return this._text.substring(this.offset,this.end)}get fullText(){return this._text}constructor(e){super(),this._text="",this._text=e??""}};var xl=Symbol("Datatype");function Bc(t){return t.$type===xl}s(Bc,"isDataTypeNode");var hy="\u200B",gy=s(t=>t.endsWith(hy)?t:t+hy,"withRuleSuffix"),Zs=class{static{s(this,"AbstractLangiumParser")}constructor(e){this._unorderedGroups=new Map,this.allRules=new Map,this.lexer=e.parser.Lexer;let r=this.lexer.definition,n=e.LanguageMetaData.mode==="production";this.wrapper=new jc(r,Object.assign(Object.assign({},e.parser.ParserConfig),{skipValidations:n,errorMessageProvider:e.parser.ParserErrorMessageProvider}))}alternatives(e,r){this.wrapper.wrapOr(e,r)}optional(e,r){this.wrapper.wrapOption(e,r)}many(e,r){this.wrapper.wrapMany(e,r)}atLeastOne(e,r){this.wrapper.wrapAtLeastOne(e,r)}getRule(e){return this.allRules.get(e)}isRecording(){return this.wrapper.IS_RECORDING}get unorderedGroups(){return this._unorderedGroups}getRuleStack(){return this.wrapper.RULE_STACK}finalize(){this.wrapper.wrapSelfAnalysis()}},Qs=class extends Zs{static{s(this,"LangiumParser")}get current(){return this.stack[this.stack.length-1]}constructor(e){super(e),this.nodeBuilder=new Ys,this.stack=[],this.assignmentMap=new Map,this.linker=e.references.Linker,this.converter=e.parser.ValueConverter,this.astReflection=e.shared.AstReflection}rule(e,r){let n=this.computeRuleType(e),i=this.wrapper.DEFINE_RULE(gy(e.name),this.startImplementation(n,r).bind(this));return this.allRules.set(e.name,i),e.entry&&(this.mainRule=i),i}computeRuleType(e){if(!e.fragment){if(Es(e))return xl;{let r=ti(e);return r??e.name}}}parse(e,r={}){this.nodeBuilder.buildRootNode(e);let n=this.lexerResult=this.lexer.tokenize(e);this.wrapper.input=n.tokens;let i=r.rule?this.allRules.get(r.rule):this.mainRule;if(!i)throw new Error(r.rule?`No rule found with name '${r.rule}'`:"No main rule available.");let a=i.call(this.wrapper,{});return this.nodeBuilder.addHiddenNodes(n.hidden),this.unorderedGroups.clear(),this.lexerResult=void 0,{value:a,lexerErrors:n.errors,lexerReport:n.report,parserErrors:this.wrapper.errors}}startImplementation(e,r){return n=>{let i=!this.isRecording()&&e!==void 0;if(i){let o={$type:e};this.stack.push(o),e===xl&&(o.value="")}let a;try{a=r(n)}catch{a=void 0}return a===void 0&&i&&(a=this.construct()),a}}extractHiddenTokens(e){let r=this.lexerResult.hidden;if(!r.length)return[];let n=e.startOffset;for(let i=0;i<r.length;i++)if(r[i].startOffset>n)return r.splice(0,i);return r.splice(0,r.length)}consume(e,r,n){let i=this.wrapper.wrapConsume(e,r);if(!this.isRecording()&&this.isValidToken(i)){let a=this.extractHiddenTokens(i);this.nodeBuilder.addHiddenNodes(a);let o=this.nodeBuilder.buildLeafNode(i,n),{assignment:l,isCrossRef:u}=this.getAssignment(n),c=this.current;if(l){let f=ut(n)?i.image:this.converter.convert(i.image,o);this.assign(l.operator,l.feature,f,o,u)}else if(Bc(c)){let f=i.image;ut(n)||(f=this.converter.convert(f,o).toString()),c.value+=f}}}isValidToken(e){return!e.isInsertedInRecovery&&!isNaN(e.startOffset)&&typeof e.endOffset=="number"&&!isNaN(e.endOffset)}subrule(e,r,n,i,a){let o;!this.isRecording()&&!n&&(o=this.nodeBuilder.buildCompositeNode(i));let l=this.wrapper.wrapSubrule(e,r,a);!this.isRecording()&&o&&o.length>0&&this.performSubruleAssignment(l,i,o)}performSubruleAssignment(e,r,n){let{assignment:i,isCrossRef:a}=this.getAssignment(r);if(i)this.assign(i.operator,i.feature,e,n,a);else if(!i){let o=this.current;if(Bc(o))o.value+=e.toString();else if(typeof e=="object"&&e){let u=this.assignWithoutOverride(e,o);this.stack.pop(),this.stack.push(u)}}}action(e,r){if(!this.isRecording()){let n=this.current;if(r.feature&&r.operator){n=this.construct(),this.nodeBuilder.removeNode(n.$cstNode),this.nodeBuilder.buildCompositeNode(r).content.push(n.$cstNode);let a={$type:e};this.stack.push(a),this.assign(r.operator,r.feature,n,n.$cstNode,!1)}else n.$type=e}}construct(){if(this.isRecording())return;let e=this.current;return za(e),this.nodeBuilder.construct(e),this.stack.pop(),Bc(e)?this.converter.convert(e.value,e.$cstNode):(Ru(this.astReflection,e),e)}getAssignment(e){if(!this.assignmentMap.has(e)){let r=Ur(e,gt);this.assignmentMap.set(e,{assignment:r,isCrossRef:r?Gr(r.terminal):!1})}return this.assignmentMap.get(e)}assign(e,r,n,i,a){let o=this.current,l;switch(a&&typeof n=="string"?l=this.linker.buildReference(o,r,i,n):l=n,e){case"=":{o[r]=l;break}case"?=":{o[r]=!0;break}case"+=":Array.isArray(o[r])||(o[r]=[]),o[r].push(l)}}assignWithoutOverride(e,r){for(let[i,a]of Object.entries(r)){let o=e[i];o===void 0?e[i]=a:Array.isArray(o)&&Array.isArray(a)&&(a.push(...o),e[i]=a)}let n=e.$cstNode;return n&&(n.astNode=void 0,e.$cstNode=void 0),e}get definitionErrors(){return this.wrapper.definitionErrors}},Tl=class{static{s(this,"AbstractParserErrorMessageProvider")}buildMismatchTokenMessage(e){return rr.buildMismatchTokenMessage(e)}buildNotAllInputParsedMessage(e){return rr.buildNotAllInputParsedMessage(e)}buildNoViableAltMessage(e){return rr.buildNoViableAltMessage(e)}buildEarlyExitMessage(e){return rr.buildEarlyExitMessage(e)}},Xi=class extends Tl{static{s(this,"LangiumParserErrorMessageProvider")}buildMismatchTokenMessage({expected:e,actual:r}){return`Expecting ${e.LABEL?"`"+e.LABEL+"`":e.name.endsWith(":KW")?`keyword '${e.name.substring(0,e.name.length-3)}'`:`token of type '${e.name}'`} but found \`${r.image}\`.`}buildNotAllInputParsedMessage({firstRedundant:e}){return`Expecting end of file but found \`${e.image}\`.`}},ea=class extends Zs{static{s(this,"LangiumCompletionParser")}constructor(){super(...arguments),this.tokens=[],this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}action(){}construct(){}parse(e){this.resetState();let r=this.lexer.tokenize(e,{mode:"partial"});return this.tokens=r.tokens,this.wrapper.input=[...this.tokens],this.mainRule.call(this.wrapper,{}),this.unorderedGroups.clear(),{tokens:this.tokens,elementStack:[...this.lastElementStack],tokenIndex:this.nextTokenIndex}}rule(e,r){let n=this.wrapper.DEFINE_RULE(gy(e.name),this.startImplementation(r).bind(this));return this.allRules.set(e.name,n),e.entry&&(this.mainRule=n),n}resetState(){this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}startImplementation(e){return r=>{let n=this.keepStackSize();try{e(r)}finally{this.resetStackSize(n)}}}removeUnexpectedElements(){this.elementStack.splice(this.stackSize)}keepStackSize(){let e=this.elementStack.length;return this.stackSize=e,e}resetStackSize(e){this.removeUnexpectedElements(),this.stackSize=e}consume(e,r,n){this.wrapper.wrapConsume(e,r),this.isRecording()||(this.lastElementStack=[...this.elementStack,n],this.nextTokenIndex=this.currIdx+1)}subrule(e,r,n,i,a){this.before(i),this.wrapper.wrapSubrule(e,r,a),this.after(i)}before(e){this.isRecording()||this.elementStack.push(e)}after(e){if(!this.isRecording()){let r=this.elementStack.lastIndexOf(e);r>=0&&this.elementStack.splice(r)}}get currIdx(){return this.wrapper.currIdx}},EN={recoveryEnabled:!0,nodeLocationTracking:"full",skipValidations:!0,errorMessageProvider:new Xi},jc=class extends Ks{static{s(this,"ChevrotainWrapper")}constructor(e,r){let n=r&&"maxLookahead"in r;super(e,Object.assign(Object.assign(Object.assign({},EN),{lookaheadStrategy:n?new nr({maxLookahead:r.maxLookahead}):new qs({logging:r.skipValidations?()=>{}:void 0})}),r))}get IS_RECORDING(){return this.RECORDING_PHASE}DEFINE_RULE(e,r){return this.RULE(e,r)}wrapSelfAnalysis(){this.performSelfAnalysis()}wrapConsume(e,r){return this.consume(e,r)}wrapSubrule(e,r,n){return this.subrule(e,r,{ARGS:[n]})}wrapOr(e,r){this.or(e,r)}wrapOption(e,r){this.option(e,r)}wrapMany(e,r){this.many(e,r)}wrapAtLeastOne(e,r){this.atLeastOne(e,r)}};function ta(t,e,r){return vN({parser:e,tokens:r,ruleNames:new Map},t),e}s(ta,"createParser");function vN(t,e){let r=As(e,!1),n=H(e.rules).filter(De).filter(i=>r.has(i));for(let i of n){let a=Object.assign(Object.assign({},t),{consume:1,optional:1,subrule:1,many:1,or:1});t.parser.rule(i,tn(a,i.definition))}}s(vN,"buildRules");function tn(t,e,r=!1){let n;if(ut(e))n=wN(t,e);else if(Ht(e))n=IN(t,e);else if(gt(e))n=tn(t,e.terminal);else if(Gr(e))n=yy(t,e);else if(yt(e))n=SN(t,e);else if(Ka(e))n=CN(t,e);else if(Va(e))n=NN(t,e);else if(cr(e))n=_N(t,e);else if(du(e)){let i=t.consume++;n=s(()=>t.parser.consume(i,ot,e),"method")}else throw new Dr(e.$cstNode,`Unexpected element type: ${e.$type}`);return xy(t,r?void 0:Al(e),n,e.cardinality)}s(tn,"buildElement");function IN(t,e){let r=vs(e);return()=>t.parser.action(r,e)}s(IN,"buildAction");function SN(t,e){let r=e.rule.ref;if(De(r)){let n=t.subrule++,i=r.fragment,a=e.arguments.length>0?kN(r,e.arguments):()=>({});return o=>t.parser.subrule(n,Ty(t,r),i,e,a(o))}else if(st(r)){let n=t.consume++,i=Wc(t,r.name);return()=>t.parser.consume(n,i,e)}else if(r)It(r);else throw new Dr(e.$cstNode,`Undefined rule: ${e.rule.$refText}`)}s(SN,"buildRuleCall");function kN(t,e){let r=e.map(n=>ir(n.value));return n=>{let i={};for(let a=0;a<r.length;a++){let o=t.parameters[a],l=r[a];i[o.name]=l(n)}return i}}s(kN,"buildRuleCallPredicate");function ir(t){if(su(t)){let e=ir(t.left),r=ir(t.right);return n=>e(n)||r(n)}else if(iu(t)){let e=ir(t.left),r=ir(t.right);return n=>e(n)&&r(n)}else if(au(t)){let e=ir(t.value);return r=>!e(r)}else if(ou(t)){let e=t.parameter.ref.name;return r=>r!==void 0&&r[e]===!0}else if(nu(t)){let e=!!t.true;return()=>e}It(t)}s(ir,"buildPredicate");function CN(t,e){if(e.elements.length===1)return tn(t,e.elements[0]);{let r=[];for(let i of e.elements){let a={ALT:tn(t,i,!0)},o=Al(i);o&&(a.GATE=ir(o)),r.push(a)}let n=t.or++;return i=>t.parser.alternatives(n,r.map(a=>{let o={ALT:s(()=>a.ALT(i),"ALT")},l=a.GATE;return l&&(o.GATE=()=>l(i)),o}))}}s(CN,"buildAlternatives");function NN(t,e){if(e.elements.length===1)return tn(t,e.elements[0]);let r=[];for(let l of e.elements){let u={ALT:tn(t,l,!0)},c=Al(l);c&&(u.GATE=ir(c)),r.push(u)}let n=t.or++,i=s((l,u)=>{let c=u.getRuleStack().join("-");return`uGroup_${l}_${c}`},"idFunc"),a=s(l=>t.parser.alternatives(n,r.map((u,c)=>{let f={ALT:s(()=>!0,"ALT")},d=t.parser;f.ALT=()=>{if(u.ALT(l),!d.isRecording()){let m=i(n,d);d.unorderedGroups.get(m)||d.unorderedGroups.set(m,[]);let g=d.unorderedGroups.get(m);typeof g?.[c]>"u"&&(g[c]=!0)}};let p=u.GATE;return p?f.GATE=()=>p(l):f.GATE=()=>{let m=d.unorderedGroups.get(i(n,d));return!m?.[c]},f})),"alternatives"),o=xy(t,Al(e),a,"*");return l=>{o(l),t.parser.isRecording()||t.parser.unorderedGroups.delete(i(n,t.parser))}}s(NN,"buildUnorderedGroup");function _N(t,e){let r=e.elements.map(n=>tn(t,n));return n=>r.forEach(i=>i(n))}s(_N,"buildGroup");function Al(t){if(cr(t))return t.guardCondition}s(Al,"getGuardCondition");function yy(t,e,r=e.terminal){if(r)if(yt(r)&&De(r.rule.ref)){let n=r.rule.ref,i=t.subrule++;return a=>t.parser.subrule(i,Ty(t,n),!1,e,a)}else if(yt(r)&&st(r.rule.ref)){let n=t.consume++,i=Wc(t,r.rule.ref.name);return()=>t.parser.consume(n,i,e)}else if(ut(r)){let n=t.consume++,i=Wc(t,r.value);return()=>t.parser.consume(n,i,e)}else throw new Error("Could not build cross reference parser");else{if(!e.type.ref)throw new Error("Could not resolve reference to type: "+e.type.$refText);let n=Za(e.type.ref),i=n?.terminal;if(!i)throw new Error("Could not find name assignment for type: "+vs(e.type.ref));return yy(t,e,i)}}s(yy,"buildCrossReference");function wN(t,e){let r=t.consume++,n=t.tokens[e.value];if(!n)throw new Error("Could not find token for keyword: "+e.value);return()=>t.parser.consume(r,n,e)}s(wN,"buildKeyword");function xy(t,e,r,n){let i=e&&ir(e);if(!n)if(i){let a=t.or++;return o=>t.parser.alternatives(a,[{ALT:s(()=>r(o),"ALT"),GATE:s(()=>i(o),"GATE")},{ALT:cl(),GATE:s(()=>!i(o),"GATE")}])}else return r;if(n==="*"){let a=t.many++;return o=>t.parser.many(a,{DEF:s(()=>r(o),"DEF"),GATE:i?()=>i(o):void 0})}else if(n==="+"){let a=t.many++;if(i){let o=t.or++;return l=>t.parser.alternatives(o,[{ALT:s(()=>t.parser.atLeastOne(a,{DEF:s(()=>r(l),"DEF")}),"ALT"),GATE:s(()=>i(l),"GATE")},{ALT:cl(),GATE:s(()=>!i(l),"GATE")}])}else return o=>t.parser.atLeastOne(a,{DEF:s(()=>r(o),"DEF")})}else if(n==="?"){let a=t.optional++;return o=>t.parser.optional(a,{DEF:s(()=>r(o),"DEF"),GATE:i?()=>i(o):void 0})}else It(n)}s(xy,"wrap");function Ty(t,e){let r=bN(t,e),n=t.parser.getRule(r);if(!n)throw new Error(`Rule "${r}" not found."`);return n}s(Ty,"getRule");function bN(t,e){if(De(e))return e.name;if(t.ruleNames.has(e))return t.ruleNames.get(e);{let r=e,n=r.$container,i=e.$type;for(;!De(n);)(cr(n)||Ka(n)||Va(n))&&(i=n.elements.indexOf(r).toString()+":"+i),r=n,n=n.$container;return i=n.name+":"+i,t.ruleNames.set(e,i),i}}s(bN,"getRuleName");function Wc(t,e){let r=t.tokens[e];if(!r)throw new Error(`Token "${e}" not found."`);return r}s(Wc,"getToken");function Kc(t){let e=t.Grammar,r=t.parser.Lexer,n=new ea(t);return ta(e,n,r.definition),n.finalize(),n}s(Kc,"createCompletionParser");function Hc(t){let e=Ay(t);return e.finalize(),e}s(Hc,"createLangiumParser");function Ay(t){let e=t.Grammar,r=t.parser.Lexer,n=new Qs(t);return ta(e,n,r.definition)}s(Ay,"prepareLangiumParser");var sr=class{static{s(this,"DefaultTokenBuilder")}constructor(){this.diagnostics=[]}buildTokens(e,r){let n=H(As(e,!1)),i=this.buildTerminalTokens(n),a=this.buildKeywordTokens(n,i,r);return i.forEach(o=>{let l=o.PATTERN;typeof l=="object"&&l&&"test"in l&&ei(l)?a.unshift(o):a.push(o)}),a}flushLexingReport(e){return{diagnostics:this.popDiagnostics()}}popDiagnostics(){let e=[...this.diagnostics];return this.diagnostics=[],e}buildTerminalTokens(e){return e.filter(st).filter(r=>!r.fragment).map(r=>this.buildTerminalToken(r)).toArray()}buildTerminalToken(e){let r=ri(e),n=this.requiresCustomPattern(r)?this.regexPatternFunction(r):r,i={name:e.name,PATTERN:n};return typeof n=="function"&&(i.LINE_BREAKS=!0),e.hidden&&(i.GROUP=ei(r)?ue.SKIPPED:"hidden"),i}requiresCustomPattern(e){return e.flags.includes("u")||e.flags.includes("s")?!0:!!(e.source.includes("?<=")||e.source.includes("?<!"))}regexPatternFunction(e){let r=new RegExp(e,e.flags+"y");return(n,i)=>(r.lastIndex=i,r.exec(n))}buildKeywordTokens(e,r,n){return e.filter(De).flatMap(i=>St(i).filter(ut)).distinct(i=>i.value).toArray().sort((i,a)=>a.value.length-i.value.length).map(i=>this.buildKeywordToken(i,r,!!n?.caseInsensitive))}buildKeywordToken(e,r,n){let i=this.buildKeywordPattern(e,n),a={name:e.value,PATTERN:i,LONGER_ALT:this.findLongerAlt(e,r)};return typeof i=="function"&&(a.LINE_BREAKS=!0),a}buildKeywordPattern(e,r){return r?new RegExp(Cu(e.value)):e.value}findLongerAlt(e,r){return r.reduce((n,i)=>{let a=i?.PATTERN;return a?.source&&Nu("^"+a.source+"$",e.value)&&n.push(i),n},[])}};var rn=class{static{s(this,"DefaultValueConverter")}convert(e,r){let n=r.grammarSource;if(Gr(n)&&(n=bu(n)),yt(n)){let i=n.rule.ref;if(!i)throw new Error("This cst node was not parsed by a rule.");return this.runConverter(i,e,r)}return e}runConverter(e,r,n){var i;switch(e.name.toUpperCase()){case"INT":return Gt.convertInt(r);case"STRING":return Gt.convertString(r);case"ID":return Gt.convertID(r)}switch((i=Fu(e))===null||i===void 0?void 0:i.toLowerCase()){case"number":return Gt.convertNumber(r);case"boolean":return Gt.convertBoolean(r);case"bigint":return Gt.convertBigint(r);case"date":return Gt.convertDate(r);default:return r}}},Gt;(function(t){function e(c){let f="";for(let d=1;d<c.length-1;d++){let p=c.charAt(d);if(p==="\\"){let m=c.charAt(++d);f+=r(m)}else f+=p}return f}s(e,"convertString"),t.convertString=e;function r(c){switch(c){case"b":return"\b";case"f":return"\f";case"n":return`
`;case"r":return"\r";case"t":return"	";case"v":return"\v";case"0":return"\0";default:return c}}s(r,"convertEscapeCharacter");function n(c){return c.charAt(0)==="^"?c.substring(1):c}s(n,"convertID"),t.convertID=n;function i(c){return parseInt(c)}s(i,"convertInt"),t.convertInt=i;function a(c){return BigInt(c)}s(a,"convertBigint"),t.convertBigint=a;function o(c){return new Date(c)}s(o,"convertDate"),t.convertDate=o;function l(c){return Number(c)}s(l,"convertNumber"),t.convertNumber=l;function u(c){return c.toLowerCase()==="true"}s(u,"convertBoolean"),t.convertBoolean=u})(Gt||(Gt={}));var w={};B(w,Lf(Sy(),1));function ef(){return new Promise(t=>{typeof setImmediate>"u"?setTimeout(t,0):setImmediate(t)})}s(ef,"delayNextTick");var Il=0,ky=10;function Sl(){return Il=performance.now(),new w.CancellationTokenSource}s(Sl,"startCancelableOperation");function Cy(t){ky=t}s(Cy,"setInterruptionPeriod");var Ut=Symbol("OperationCancelled");function Bt(t){return t===Ut}s(Bt,"isOperationCancelled");async function Te(t){if(t===w.CancellationToken.None)return;let e=performance.now();if(e-Il>=ky&&(Il=e,await ef(),Il=performance.now()),t.isCancellationRequested)throw Ut}s(Te,"interruptAndCheck");var Xe=class{static{s(this,"Deferred")}constructor(){this.promise=new Promise((e,r)=>{this.resolve=n=>(e(n),this),this.reject=n=>(r(n),this)})}};var kl=class t{static{s(this,"FullTextDocument")}constructor(e,r,n,i){this._uri=e,this._languageId=r,this._version=n,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let r=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(r,n)}return this._content}update(e,r){for(let n of e)if(t.isIncremental(n)){let i=wy(n.range),a=this.offsetAt(i.start),o=this.offsetAt(i.end);this._content=this._content.substring(0,a)+n.text+this._content.substring(o,this._content.length);let l=Math.max(i.start.line,0),u=Math.max(i.end.line,0),c=this._lineOffsets,f=Ny(n.text,!1,a);if(u-l===f.length)for(let p=0,m=f.length;p<m;p++)c[p+l+1]=f[p];else f.length<1e4?c.splice(l+1,u-l,...f):this._lineOffsets=c=c.slice(0,l+1).concat(f,c.slice(u+1));let d=n.text.length-(o-a);if(d!==0)for(let p=l+1+f.length,m=c.length;p<m;p++)c[p]=c[p]+d}else if(t.isFull(n))this._content=n.text,this._lineOffsets=void 0;else throw new Error("Unknown change event received");this._version=r}getLineOffsets(){return this._lineOffsets===void 0&&(this._lineOffsets=Ny(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let r=this.getLineOffsets(),n=0,i=r.length;if(i===0)return{line:0,character:e};for(;n<i;){let o=Math.floor((n+i)/2);r[o]>e?i=o:n=o+1}let a=n-1;return e=this.ensureBeforeEOL(e,r[a]),{line:a,character:e-r[a]}}offsetAt(e){let r=this.getLineOffsets();if(e.line>=r.length)return this._content.length;if(e.line<0)return 0;let n=r[e.line];if(e.character<=0)return n;let i=e.line+1<r.length?r[e.line+1]:this._content.length,a=Math.min(n+e.character,i);return this.ensureBeforeEOL(a,n)}ensureBeforeEOL(e,r){for(;e>r&&_y(this._content.charCodeAt(e-1));)e--;return e}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){let r=e;return r!=null&&typeof r.text=="string"&&r.range!==void 0&&(r.rangeLength===void 0||typeof r.rangeLength=="number")}static isFull(e){let r=e;return r!=null&&typeof r.text=="string"&&r.range===void 0&&r.rangeLength===void 0}},Zi;(function(t){function e(i,a,o,l){return new kl(i,a,o,l)}s(e,"create"),t.create=e;function r(i,a,o){if(i instanceof kl)return i.update(a,o),i;throw new Error("TextDocument.update: document must be created by TextDocument.create")}s(r,"update"),t.update=r;function n(i,a){let o=i.getText(),l=tf(a.map(BN),(f,d)=>{let p=f.range.start.line-d.range.start.line;return p===0?f.range.start.character-d.range.start.character:p}),u=0,c=[];for(let f of l){let d=i.offsetAt(f.range.start);if(d<u)throw new Error("Overlapping edit");d>u&&c.push(o.substring(u,d)),f.newText.length&&c.push(f.newText),u=i.offsetAt(f.range.end)}return c.push(o.substr(u)),c.join("")}s(n,"applyEdits"),t.applyEdits=n})(Zi||(Zi={}));function tf(t,e){if(t.length<=1)return t;let r=t.length/2|0,n=t.slice(0,r),i=t.slice(r);tf(n,e),tf(i,e);let a=0,o=0,l=0;for(;a<n.length&&o<i.length;)e(n[a],i[o])<=0?t[l++]=n[a++]:t[l++]=i[o++];for(;a<n.length;)t[l++]=n[a++];for(;o<i.length;)t[l++]=i[o++];return t}s(tf,"mergeSort");function Ny(t,e,r=0){let n=e?[r]:[];for(let i=0;i<t.length;i++){let a=t.charCodeAt(i);_y(a)&&(a===13&&i+1<t.length&&t.charCodeAt(i+1)===10&&i++,n.push(r+i+1))}return n}s(Ny,"computeLineOffsets");function _y(t){return t===13||t===10}s(_y,"isEOL");function wy(t){let e=t.start,r=t.end;return e.line>r.line||e.line===r.line&&e.character>r.character?{start:r,end:e}:t}s(wy,"getWellformedRange");function BN(t){let e=wy(t.range);return e!==t.range?{newText:t.newText,range:e}:t}s(BN,"getWellformedEdit");var by;(()=>{"use strict";var t={470:i=>{function a(u){if(typeof u!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(u))}s(a,"e");function o(u,c){for(var f,d="",p=0,m=-1,g=0,y=0;y<=u.length;++y){if(y<u.length)f=u.charCodeAt(y);else{if(f===47)break;f=47}if(f===47){if(!(m===y-1||g===1))if(m!==y-1&&g===2){if(d.length<2||p!==2||d.charCodeAt(d.length-1)!==46||d.charCodeAt(d.length-2)!==46){if(d.length>2){var R=d.lastIndexOf("/");if(R!==d.length-1){R===-1?(d="",p=0):p=(d=d.slice(0,R)).length-1-d.lastIndexOf("/"),m=y,g=0;continue}}else if(d.length===2||d.length===1){d="",p=0,m=y,g=0;continue}}c&&(d.length>0?d+="/..":d="..",p=2)}else d.length>0?d+="/"+u.slice(m+1,y):d=u.slice(m+1,y),p=y-m-1;m=y,g=0}else f===46&&g!==-1?++g:g=-1}return d}s(o,"r");var l={resolve:s(function(){for(var u,c="",f=!1,d=arguments.length-1;d>=-1&&!f;d--){var p;d>=0?p=arguments[d]:(u===void 0&&(u=process.cwd()),p=u),a(p),p.length!==0&&(c=p+"/"+c,f=p.charCodeAt(0)===47)}return c=o(c,!f),f?c.length>0?"/"+c:"/":c.length>0?c:"."},"resolve"),normalize:s(function(u){if(a(u),u.length===0)return".";var c=u.charCodeAt(0)===47,f=u.charCodeAt(u.length-1)===47;return(u=o(u,!c)).length!==0||c||(u="."),u.length>0&&f&&(u+="/"),c?"/"+u:u},"normalize"),isAbsolute:s(function(u){return a(u),u.length>0&&u.charCodeAt(0)===47},"isAbsolute"),join:s(function(){if(arguments.length===0)return".";for(var u,c=0;c<arguments.length;++c){var f=arguments[c];a(f),f.length>0&&(u===void 0?u=f:u+="/"+f)}return u===void 0?".":l.normalize(u)},"join"),relative:s(function(u,c){if(a(u),a(c),u===c||(u=l.resolve(u))===(c=l.resolve(c)))return"";for(var f=1;f<u.length&&u.charCodeAt(f)===47;++f);for(var d=u.length,p=d-f,m=1;m<c.length&&c.charCodeAt(m)===47;++m);for(var g=c.length-m,y=p<g?p:g,R=-1,T=0;T<=y;++T){if(T===y){if(g>y){if(c.charCodeAt(m+T)===47)return c.slice(m+T+1);if(T===0)return c.slice(m+T)}else p>y&&(u.charCodeAt(f+T)===47?R=T:T===0&&(R=0));break}var E=u.charCodeAt(f+T);if(E!==c.charCodeAt(m+T))break;E===47&&(R=T)}var A="";for(T=f+R+1;T<=d;++T)T!==d&&u.charCodeAt(T)!==47||(A.length===0?A+="..":A+="/..");return A.length>0?A+c.slice(m+R):(m+=R,c.charCodeAt(m)===47&&++m,c.slice(m))},"relative"),_makeLong:s(function(u){return u},"_makeLong"),dirname:s(function(u){if(a(u),u.length===0)return".";for(var c=u.charCodeAt(0),f=c===47,d=-1,p=!0,m=u.length-1;m>=1;--m)if((c=u.charCodeAt(m))===47){if(!p){d=m;break}}else p=!1;return d===-1?f?"/":".":f&&d===1?"//":u.slice(0,d)},"dirname"),basename:s(function(u,c){if(c!==void 0&&typeof c!="string")throw new TypeError('"ext" argument must be a string');a(u);var f,d=0,p=-1,m=!0;if(c!==void 0&&c.length>0&&c.length<=u.length){if(c.length===u.length&&c===u)return"";var g=c.length-1,y=-1;for(f=u.length-1;f>=0;--f){var R=u.charCodeAt(f);if(R===47){if(!m){d=f+1;break}}else y===-1&&(m=!1,y=f+1),g>=0&&(R===c.charCodeAt(g)?--g==-1&&(p=f):(g=-1,p=y))}return d===p?p=y:p===-1&&(p=u.length),u.slice(d,p)}for(f=u.length-1;f>=0;--f)if(u.charCodeAt(f)===47){if(!m){d=f+1;break}}else p===-1&&(m=!1,p=f+1);return p===-1?"":u.slice(d,p)},"basename"),extname:s(function(u){a(u);for(var c=-1,f=0,d=-1,p=!0,m=0,g=u.length-1;g>=0;--g){var y=u.charCodeAt(g);if(y!==47)d===-1&&(p=!1,d=g+1),y===46?c===-1?c=g:m!==1&&(m=1):c!==-1&&(m=-1);else if(!p){f=g+1;break}}return c===-1||d===-1||m===0||m===1&&c===d-1&&c===f+1?"":u.slice(c,d)},"extname"),format:s(function(u){if(u===null||typeof u!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof u);return function(c,f){var d=f.dir||f.root,p=f.base||(f.name||"")+(f.ext||"");return d?d===f.root?d+p:d+"/"+p:p}(0,u)},"format"),parse:s(function(u){a(u);var c={root:"",dir:"",base:"",ext:"",name:""};if(u.length===0)return c;var f,d=u.charCodeAt(0),p=d===47;p?(c.root="/",f=1):f=0;for(var m=-1,g=0,y=-1,R=!0,T=u.length-1,E=0;T>=f;--T)if((d=u.charCodeAt(T))!==47)y===-1&&(R=!1,y=T+1),d===46?m===-1?m=T:E!==1&&(E=1):m!==-1&&(E=-1);else if(!R){g=T+1;break}return m===-1||y===-1||E===0||E===1&&m===y-1&&m===g+1?y!==-1&&(c.base=c.name=g===0&&p?u.slice(1,y):u.slice(g,y)):(g===0&&p?(c.name=u.slice(1,m),c.base=u.slice(1,y)):(c.name=u.slice(g,m),c.base=u.slice(g,y)),c.ext=u.slice(m,y)),g>0?c.dir=u.slice(0,g-1):p&&(c.dir="/"),c},"parse"),sep:"/",delimiter:":",win32:null,posix:null};l.posix=l,i.exports=l}},e={};function r(i){var a=e[i];if(a!==void 0)return a.exports;var o=e[i]={exports:{}};return t[i](o,o.exports,r),o.exports}s(r,"r"),r.d=(i,a)=>{for(var o in a)r.o(a,o)&&!r.o(i,o)&&Object.defineProperty(i,o,{enumerable:!0,get:a[o]})},r.o=(i,a)=>Object.prototype.hasOwnProperty.call(i,a),r.r=i=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(i,"__esModule",{value:!0})};var n={};(()=>{let i;r.r(n),r.d(n,{URI:s(()=>p,"URI"),Utils:s(()=>jt,"Utils")}),typeof process=="object"?i=process.platform==="win32":typeof navigator=="object"&&(i=navigator.userAgent.indexOf("Windows")>=0);let a=/^\w[\w\d+.-]*$/,o=/^\//,l=/^\/\//;function u(I,x){if(!I.scheme&&x)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${I.authority}", path: "${I.path}", query: "${I.query}", fragment: "${I.fragment}"}`);if(I.scheme&&!a.test(I.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(I.path){if(I.authority){if(!o.test(I.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(l.test(I.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}s(u,"s");let c="",f="/",d=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class p{static{s(this,"f")}static isUri(x){return x instanceof p||!!x&&typeof x.authority=="string"&&typeof x.fragment=="string"&&typeof x.path=="string"&&typeof x.query=="string"&&typeof x.scheme=="string"&&typeof x.fsPath=="string"&&typeof x.with=="function"&&typeof x.toString=="function"}scheme;authority;path;query;fragment;constructor(x,_,C,K,M,P=!1){typeof x=="object"?(this.scheme=x.scheme||c,this.authority=x.authority||c,this.path=x.path||c,this.query=x.query||c,this.fragment=x.fragment||c):(this.scheme=function(je,We){return je||We?je:"file"}(x,P),this.authority=_||c,this.path=function(je,We){switch(je){case"https":case"http":case"file":We?We[0]!==f&&(We=f+We):We=f}return We}(this.scheme,C||c),this.query=K||c,this.fragment=M||c,u(this,P))}get fsPath(){return E(this,!1)}with(x){if(!x)return this;let{scheme:_,authority:C,path:K,query:M,fragment:P}=x;return _===void 0?_=this.scheme:_===null&&(_=c),C===void 0?C=this.authority:C===null&&(C=c),K===void 0?K=this.path:K===null&&(K=c),M===void 0?M=this.query:M===null&&(M=c),P===void 0?P=this.fragment:P===null&&(P=c),_===this.scheme&&C===this.authority&&K===this.path&&M===this.query&&P===this.fragment?this:new g(_,C,K,M,P)}static parse(x,_=!1){let C=d.exec(x);return C?new g(C[2]||c,ke(C[4]||c),ke(C[5]||c),ke(C[7]||c),ke(C[9]||c),_):new g(c,c,c,c,c)}static file(x){let _=c;if(i&&(x=x.replace(/\\/g,f)),x[0]===f&&x[1]===f){let C=x.indexOf(f,2);C===-1?(_=x.substring(2),x=f):(_=x.substring(2,C),x=x.substring(C)||f)}return new g("file",_,x,c,c)}static from(x){let _=new g(x.scheme,x.authority,x.path,x.query,x.fragment);return u(_,!0),_}toString(x=!1){return A(this,x)}toJSON(){return this}static revive(x){if(x){if(x instanceof p)return x;{let _=new g(x);return _._formatted=x.external,_._fsPath=x._sep===m?x.fsPath:null,_}}return x}}let m=i?1:void 0;class g extends p{static{s(this,"l")}_formatted=null;_fsPath=null;get fsPath(){return this._fsPath||(this._fsPath=E(this,!1)),this._fsPath}toString(x=!1){return x?A(this,!0):(this._formatted||(this._formatted=A(this,!1)),this._formatted)}toJSON(){let x={$mid:1};return this._fsPath&&(x.fsPath=this._fsPath,x._sep=m),this._formatted&&(x.external=this._formatted),this.path&&(x.path=this.path),this.scheme&&(x.scheme=this.scheme),this.authority&&(x.authority=this.authority),this.query&&(x.query=this.query),this.fragment&&(x.fragment=this.fragment),x}}let y={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function R(I,x,_){let C,K=-1;for(let M=0;M<I.length;M++){let P=I.charCodeAt(M);if(P>=97&&P<=122||P>=65&&P<=90||P>=48&&P<=57||P===45||P===46||P===95||P===126||x&&P===47||_&&P===91||_&&P===93||_&&P===58)K!==-1&&(C+=encodeURIComponent(I.substring(K,M)),K=-1),C!==void 0&&(C+=I.charAt(M));else{C===void 0&&(C=I.substr(0,M));let je=y[P];je!==void 0?(K!==-1&&(C+=encodeURIComponent(I.substring(K,M)),K=-1),C+=je):K===-1&&(K=M)}}return K!==-1&&(C+=encodeURIComponent(I.substring(K))),C!==void 0?C:I}s(R,"d");function T(I){let x;for(let _=0;_<I.length;_++){let C=I.charCodeAt(_);C===35||C===63?(x===void 0&&(x=I.substr(0,_)),x+=y[C]):x!==void 0&&(x+=I[_])}return x!==void 0?x:I}s(T,"p");function E(I,x){let _;return _=I.authority&&I.path.length>1&&I.scheme==="file"?`//${I.authority}${I.path}`:I.path.charCodeAt(0)===47&&(I.path.charCodeAt(1)>=65&&I.path.charCodeAt(1)<=90||I.path.charCodeAt(1)>=97&&I.path.charCodeAt(1)<=122)&&I.path.charCodeAt(2)===58?x?I.path.substr(1):I.path[1].toLowerCase()+I.path.substr(2):I.path,i&&(_=_.replace(/\//g,"\\")),_}s(E,"m");function A(I,x){let _=x?T:R,C="",{scheme:K,authority:M,path:P,query:je,fragment:We}=I;if(K&&(C+=K,C+=":"),(M||K==="file")&&(C+=f,C+=f),M){let ie=M.indexOf("@");if(ie!==-1){let wr=M.substr(0,ie);M=M.substr(ie+1),ie=wr.lastIndexOf(":"),ie===-1?C+=_(wr,!1,!1):(C+=_(wr.substr(0,ie),!1,!1),C+=":",C+=_(wr.substr(ie+1),!1,!0)),C+="@"}M=M.toLowerCase(),ie=M.lastIndexOf(":"),ie===-1?C+=_(M,!1,!0):(C+=_(M.substr(0,ie),!1,!0),C+=M.substr(ie))}if(P){if(P.length>=3&&P.charCodeAt(0)===47&&P.charCodeAt(2)===58){let ie=P.charCodeAt(1);ie>=65&&ie<=90&&(P=`/${String.fromCharCode(ie+32)}:${P.substr(3)}`)}else if(P.length>=2&&P.charCodeAt(1)===58){let ie=P.charCodeAt(0);ie>=65&&ie<=90&&(P=`${String.fromCharCode(ie+32)}:${P.substr(2)}`)}C+=_(P,!0,!1)}return je&&(C+="?",C+=_(je,!1,!1)),We&&(C+="#",C+=x?We:R(We,!1,!1)),C}s(A,"y");function $(I){try{return decodeURIComponent(I)}catch{return I.length>3?I.substr(0,3)+$(I.substr(3)):I}}s($,"v");let L=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function ke(I){return I.match(L)?I.replace(L,x=>$(x)):I}s(ke,"C");var un=r(470);let Le=un.posix||un,or="/";var jt;(function(I){I.joinPath=function(x,..._){return x.with({path:Le.join(x.path,..._)})},I.resolvePath=function(x,..._){let C=x.path,K=!1;C[0]!==or&&(C=or+C,K=!0);let M=Le.resolve(C,..._);return K&&M[0]===or&&!x.authority&&(M=M.substring(1)),x.with({path:M})},I.dirname=function(x){if(x.path.length===0||x.path===or)return x;let _=Le.dirname(x.path);return _.length===1&&_.charCodeAt(0)===46&&(_=""),x.with({path:_})},I.basename=function(x){return Le.basename(x.path)},I.extname=function(x){return Le.extname(x.path)}})(jt||(jt={}))})(),by=n})();var{URI:Ye,Utils:Qi}=by;var Je;(function(t){t.basename=Qi.basename,t.dirname=Qi.dirname,t.extname=Qi.extname,t.joinPath=Qi.joinPath,t.resolvePath=Qi.resolvePath;function e(i,a){return i?.toString()===a?.toString()}s(e,"equals"),t.equals=e;function r(i,a){let o=typeof i=="string"?i:i.path,l=typeof a=="string"?a:a.path,u=o.split("/").filter(m=>m.length>0),c=l.split("/").filter(m=>m.length>0),f=0;for(;f<u.length&&u[f]===c[f];f++);let d="../".repeat(u.length-f),p=c.slice(f).join("/");return d+p}s(r,"relative"),t.relative=r;function n(i){return Ye.parse(i.toString()).toString()}s(n,"normalize"),t.normalize=n})(Je||(Je={}));var J;(function(t){t[t.Changed=0]="Changed",t[t.Parsed=1]="Parsed",t[t.IndexedContent=2]="IndexedContent",t[t.ComputedScopes=3]="ComputedScopes",t[t.Linked=4]="Linked",t[t.IndexedReferences=5]="IndexedReferences",t[t.Validated=6]="Validated"})(J||(J={}));var ra=class{static{s(this,"DefaultLangiumDocumentFactory")}constructor(e){this.serviceRegistry=e.ServiceRegistry,this.textDocuments=e.workspace.TextDocuments,this.fileSystemProvider=e.workspace.FileSystemProvider}async fromUri(e,r=w.CancellationToken.None){let n=await this.fileSystemProvider.readFile(e);return this.createAsync(e,n,r)}fromTextDocument(e,r,n){return r=r??Ye.parse(e.uri),w.CancellationToken.is(n)?this.createAsync(r,e,n):this.create(r,e,n)}fromString(e,r,n){return w.CancellationToken.is(n)?this.createAsync(r,e,n):this.create(r,e,n)}fromModel(e,r){return this.create(r,{$model:e})}create(e,r,n){if(typeof r=="string"){let i=this.parse(e,r,n);return this.createLangiumDocument(i,e,void 0,r)}else if("$model"in r){let i={value:r.$model,parserErrors:[],lexerErrors:[]};return this.createLangiumDocument(i,e)}else{let i=this.parse(e,r.getText(),n);return this.createLangiumDocument(i,e,r)}}async createAsync(e,r,n){if(typeof r=="string"){let i=await this.parseAsync(e,r,n);return this.createLangiumDocument(i,e,void 0,r)}else{let i=await this.parseAsync(e,r.getText(),n);return this.createLangiumDocument(i,e,r)}}createLangiumDocument(e,r,n,i){let a;if(n)a={parseResult:e,uri:r,state:J.Parsed,references:[],textDocument:n};else{let o=this.createTextDocumentGetter(r,i);a={parseResult:e,uri:r,state:J.Parsed,references:[],get textDocument(){return o()}}}return e.value.$document=a,a}async update(e,r){var n,i;let a=(n=e.parseResult.value.$cstNode)===null||n===void 0?void 0:n.root.fullText,o=(i=this.textDocuments)===null||i===void 0?void 0:i.get(e.uri.toString()),l=o?o.getText():await this.fileSystemProvider.readFile(e.uri);if(o)Object.defineProperty(e,"textDocument",{value:o});else{let u=this.createTextDocumentGetter(e.uri,l);Object.defineProperty(e,"textDocument",{get:u})}return a!==l&&(e.parseResult=await this.parseAsync(e.uri,l,r),e.parseResult.value.$document=e),e.state=J.Parsed,e}parse(e,r,n){return this.serviceRegistry.getServices(e).parser.LangiumParser.parse(r,n)}parseAsync(e,r,n){return this.serviceRegistry.getServices(e).parser.AsyncParser.parse(r,n)}createTextDocumentGetter(e,r){let n=this.serviceRegistry,i;return()=>i??(i=Zi.create(e.toString(),n.getServices(e).LanguageMetaData.languageId,0,r??""))}},na=class{static{s(this,"DefaultLangiumDocuments")}constructor(e){this.documentMap=new Map,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.serviceRegistry=e.ServiceRegistry}get all(){return H(this.documentMap.values())}addDocument(e){let r=e.uri.toString();if(this.documentMap.has(r))throw new Error(`A document with the URI '${r}' is already present.`);this.documentMap.set(r,e)}getDocument(e){let r=e.toString();return this.documentMap.get(r)}async getOrCreateDocument(e,r){let n=this.getDocument(e);return n||(n=await this.langiumDocumentFactory.fromUri(e,r),this.addDocument(n),n)}createDocument(e,r,n){if(n)return this.langiumDocumentFactory.fromString(r,e,n).then(i=>(this.addDocument(i),i));{let i=this.langiumDocumentFactory.fromString(r,e);return this.addDocument(i),i}}hasDocument(e){return this.documentMap.has(e.toString())}invalidateDocument(e){let r=e.toString(),n=this.documentMap.get(r);return n&&(this.serviceRegistry.getServices(e).references.Linker.unlink(n),n.state=J.Changed,n.precomputedScopes=void 0,n.diagnostics=void 0),n}deleteDocument(e){let r=e.toString(),n=this.documentMap.get(r);return n&&(n.state=J.Changed,this.documentMap.delete(r)),n}};var rf=Symbol("ref_resolving"),ia=class{static{s(this,"DefaultLinker")}constructor(e){this.reflection=e.shared.AstReflection,this.langiumDocuments=()=>e.shared.workspace.LangiumDocuments,this.scopeProvider=e.references.ScopeProvider,this.astNodeLocator=e.workspace.AstNodeLocator}async link(e,r=w.CancellationToken.None){for(let n of ct(e.parseResult.value))await Te(r),Zn(n).forEach(i=>this.doLink(i,e))}doLink(e,r){var n;let i=e.reference;if(i._ref===void 0){i._ref=rf;try{let a=this.getCandidate(e);if(Lr(a))i._ref=a;else if(i._nodeDescription=a,this.langiumDocuments().hasDocument(a.documentUri)){let o=this.loadAstNode(a);i._ref=o??this.createLinkingError(e,a)}else i._ref=void 0}catch(a){console.error(`An error occurred while resolving reference to '${i.$refText}':`,a);let o=(n=a.message)!==null&&n!==void 0?n:String(a);i._ref=Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${i.$refText}': ${o}`})}r.references.push(i)}}unlink(e){for(let r of e.references)delete r._ref,delete r._nodeDescription;e.references=[]}getCandidate(e){let n=this.scopeProvider.getScope(e).getElement(e.reference.$refText);return n??this.createLinkingError(e)}buildReference(e,r,n,i){let a=this,o={$refNode:n,$refText:i,get ref(){var l;if(fe(this._ref))return this._ref;if(Yl(this._nodeDescription)){let u=a.loadAstNode(this._nodeDescription);this._ref=u??a.createLinkingError({reference:o,container:e,property:r},this._nodeDescription)}else if(this._ref===void 0){this._ref=rf;let u=hs(e).$document,c=a.getLinkedNode({reference:o,container:e,property:r});if(c.error&&u&&u.state<J.ComputedScopes)return this._ref=void 0;this._ref=(l=c.node)!==null&&l!==void 0?l:c.error,this._nodeDescription=c.descr,u?.references.push(this)}else if(this._ref===rf)throw new Error(`Cyclic reference resolution detected: ${a.astNodeLocator.getAstNodePath(e)}/${r} (symbol '${i}')`);return fe(this._ref)?this._ref:void 0},get $nodeDescription(){return this._nodeDescription},get error(){return Lr(this._ref)?this._ref:void 0}};return o}getLinkedNode(e){var r;try{let n=this.getCandidate(e);if(Lr(n))return{error:n};let i=this.loadAstNode(n);return i?{node:i,descr:n}:{descr:n,error:this.createLinkingError(e,n)}}catch(n){console.error(`An error occurred while resolving reference to '${e.reference.$refText}':`,n);let i=(r=n.message)!==null&&r!==void 0?r:String(n);return{error:Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${e.reference.$refText}': ${i}`})}}}loadAstNode(e){if(e.node)return e.node;let r=this.langiumDocuments().getDocument(e.documentUri);if(r)return this.astNodeLocator.getAstNode(r.parseResult.value,e.path)}createLinkingError(e,r){let n=hs(e.container).$document;n&&n.state<J.ComputedScopes&&console.warn(`Attempted reference resolution before document reached ComputedScopes state (${n.uri}).`);let i=this.reflection.getReferenceType(e);return Object.assign(Object.assign({},e),{message:`Could not resolve reference to ${i} named '${e.reference.$refText}'.`,targetDescription:r})}};function Oy(t){return typeof t.name=="string"}s(Oy,"isNamed");var sa=class{static{s(this,"DefaultNameProvider")}getName(e){if(Oy(e))return e.name}getNameNode(e){return Rs(e.$cstNode,"name")}};var aa=class{static{s(this,"DefaultReferences")}constructor(e){this.nameProvider=e.references.NameProvider,this.index=e.shared.workspace.IndexManager,this.nodeLocator=e.workspace.AstNodeLocator}findDeclaration(e){if(e){let r=Du(e),n=e.astNode;if(r&&n){let i=n[r.feature];if(be(i))return i.ref;if(Array.isArray(i)){for(let a of i)if(be(a)&&a.$refNode&&a.$refNode.offset<=e.offset&&a.$refNode.end>=e.end)return a.ref}}if(n){let i=this.nameProvider.getNameNode(n);if(i&&(i===e||Zl(e,i)))return n}}}findDeclarationNode(e){let r=this.findDeclaration(e);if(r?.$cstNode){let n=this.nameProvider.getNameNode(r);return n??r.$cstNode}}findReferences(e,r){let n=[];if(r.includeDeclaration){let a=this.getReferenceToSelf(e);a&&n.push(a)}let i=this.index.findAllReferences(e,this.nodeLocator.getAstNodePath(e));return r.documentUri&&(i=i.filter(a=>Je.equals(a.sourceUri,r.documentUri))),n.push(...i),H(n)}getReferenceToSelf(e){let r=this.nameProvider.getNameNode(e);if(r){let n=Fe(e),i=this.nodeLocator.getAstNodePath(e);return{sourceUri:n.uri,sourcePath:i,targetUri:n.uri,targetPath:i,segment:Mr(r),local:!0}}}};var At=class{static{s(this,"MultiMap")}constructor(e){if(this.map=new Map,e)for(let[r,n]of e)this.add(r,n)}get size(){return fn.sum(H(this.map.values()).map(e=>e.length))}clear(){this.map.clear()}delete(e,r){if(r===void 0)return this.map.delete(e);{let n=this.map.get(e);if(n){let i=n.indexOf(r);if(i>=0)return n.length===1?this.map.delete(e):n.splice(i,1),!0}return!1}}get(e){var r;return(r=this.map.get(e))!==null&&r!==void 0?r:[]}has(e,r){if(r===void 0)return this.map.has(e);{let n=this.map.get(e);return n?n.indexOf(r)>=0:!1}}add(e,r){return this.map.has(e)?this.map.get(e).push(r):this.map.set(e,[r]),this}addAll(e,r){return this.map.has(e)?this.map.get(e).push(...r):this.map.set(e,Array.from(r)),this}forEach(e){this.map.forEach((r,n)=>r.forEach(i=>e(i,n,this)))}[Symbol.iterator](){return this.entries().iterator()}entries(){return H(this.map.entries()).flatMap(([e,r])=>r.map(n=>[e,n]))}keys(){return H(this.map.keys())}values(){return H(this.map.values()).flat()}entriesGroupedByKey(){return H(this.map.entries())}},nn=class{static{s(this,"BiMap")}get size(){return this.map.size}constructor(e){if(this.map=new Map,this.inverse=new Map,e)for(let[r,n]of e)this.set(r,n)}clear(){this.map.clear(),this.inverse.clear()}set(e,r){return this.map.set(e,r),this.inverse.set(r,e),this}get(e){return this.map.get(e)}getKey(e){return this.inverse.get(e)}delete(e){let r=this.map.get(e);return r!==void 0?(this.map.delete(e),this.inverse.delete(r),!0):!1}};var oa=class{static{s(this,"DefaultScopeComputation")}constructor(e){this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider}async computeExports(e,r=w.CancellationToken.None){return this.computeExportsForNode(e.parseResult.value,e,void 0,r)}async computeExportsForNode(e,r,n=gs,i=w.CancellationToken.None){let a=[];this.exportNode(e,a,r);for(let o of n(e))await Te(i),this.exportNode(o,a,r);return a}exportNode(e,r,n){let i=this.nameProvider.getName(e);i&&r.push(this.descriptions.createDescription(e,i,n))}async computeLocalScopes(e,r=w.CancellationToken.None){let n=e.parseResult.value,i=new At;for(let a of St(n))await Te(r),this.processNode(a,e,i);return i}processNode(e,r,n){let i=e.$container;if(i){let a=this.nameProvider.getName(e);a&&n.add(i,this.descriptions.createDescription(e,a,r))}}};var es=class{static{s(this,"StreamScope")}constructor(e,r,n){var i;this.elements=e,this.outerScope=r,this.caseInsensitive=(i=n?.caseInsensitive)!==null&&i!==void 0?i:!1}getAllElements(){return this.outerScope?this.elements.concat(this.outerScope.getAllElements()):this.elements}getElement(e){let r=this.caseInsensitive?this.elements.find(n=>n.name.toLowerCase()===e.toLowerCase()):this.elements.find(n=>n.name===e);if(r)return r;if(this.outerScope)return this.outerScope.getElement(e)}},la=class{static{s(this,"MapScope")}constructor(e,r,n){var i;this.elements=new Map,this.caseInsensitive=(i=n?.caseInsensitive)!==null&&i!==void 0?i:!1;for(let a of e){let o=this.caseInsensitive?a.name.toLowerCase():a.name;this.elements.set(o,a)}this.outerScope=r}getElement(e){let r=this.caseInsensitive?e.toLowerCase():e,n=this.elements.get(r);if(n)return n;if(this.outerScope)return this.outerScope.getElement(e)}getAllElements(){let e=H(this.elements.values());return this.outerScope&&(e=e.concat(this.outerScope.getAllElements())),e}},jN={getElement(){},getAllElements(){return ss}};var ts=class{static{s(this,"DisposableCache")}constructor(){this.toDispose=[],this.isDisposed=!1}onDispose(e){this.toDispose.push(e)}dispose(){this.throwIfDisposed(),this.clear(),this.isDisposed=!0,this.toDispose.forEach(e=>e.dispose())}throwIfDisposed(){if(this.isDisposed)throw new Error("This cache has already been disposed")}},ua=class extends ts{static{s(this,"SimpleCache")}constructor(){super(...arguments),this.cache=new Map}has(e){return this.throwIfDisposed(),this.cache.has(e)}set(e,r){this.throwIfDisposed(),this.cache.set(e,r)}get(e,r){if(this.throwIfDisposed(),this.cache.has(e))return this.cache.get(e);if(r){let n=r();return this.cache.set(e,n),n}else return}delete(e){return this.throwIfDisposed(),this.cache.delete(e)}clear(){this.throwIfDisposed(),this.cache.clear()}},sn=class extends ts{static{s(this,"ContextCache")}constructor(e){super(),this.cache=new Map,this.converter=e??(r=>r)}has(e,r){return this.throwIfDisposed(),this.cacheForContext(e).has(r)}set(e,r,n){this.throwIfDisposed(),this.cacheForContext(e).set(r,n)}get(e,r,n){this.throwIfDisposed();let i=this.cacheForContext(e);if(i.has(r))return i.get(r);if(n){let a=n();return i.set(r,a),a}else return}delete(e,r){return this.throwIfDisposed(),this.cacheForContext(e).delete(r)}clear(e){if(this.throwIfDisposed(),e){let r=this.converter(e);this.cache.delete(r)}else this.cache.clear()}cacheForContext(e){let r=this.converter(e),n=this.cache.get(r);return n||(n=new Map,this.cache.set(r,n)),n}},Cl=class extends sn{static{s(this,"DocumentCache")}constructor(e,r){super(n=>n.toString()),r?(this.toDispose.push(e.workspace.DocumentBuilder.onDocumentPhase(r,n=>{this.clear(n.uri.toString())})),this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((n,i)=>{for(let a of i)this.clear(a)}))):this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((n,i)=>{let a=n.concat(i);for(let o of a)this.clear(o)}))}},rs=class extends ua{static{s(this,"WorkspaceCache")}constructor(e,r){super(),r?(this.toDispose.push(e.workspace.DocumentBuilder.onBuildPhase(r,()=>{this.clear()})),this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((n,i)=>{i.length>0&&this.clear()}))):this.toDispose.push(e.workspace.DocumentBuilder.onUpdate(()=>{this.clear()}))}};var ca=class{static{s(this,"DefaultScopeProvider")}constructor(e){this.reflection=e.shared.AstReflection,this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider,this.indexManager=e.shared.workspace.IndexManager,this.globalScopeCache=new rs(e.shared)}getScope(e){let r=[],n=this.reflection.getReferenceType(e),i=Fe(e.container).precomputedScopes;if(i){let o=e.container;do{let l=i.get(o);l.length>0&&r.push(H(l).filter(u=>this.reflection.isSubtype(u.type,n))),o=o.$container}while(o)}let a=this.getGlobalScope(n,e);for(let o=r.length-1;o>=0;o--)a=this.createScope(r[o],a);return a}createScope(e,r,n){return new es(H(e),r,n)}createScopeForNodes(e,r,n){let i=H(e).map(a=>{let o=this.nameProvider.getName(a);if(o)return this.descriptions.createDescription(a,o)}).nonNullable();return new es(i,r,n)}getGlobalScope(e,r){return this.globalScopeCache.get(e,()=>new la(this.indexManager.allElements(e)))}};function nf(t){return typeof t.$comment=="string"}s(nf,"isAstNodeWithComment");function $y(t){return typeof t=="object"&&!!t&&("$ref"in t||"$error"in t)}s($y,"isIntermediateReference");var fa=class{static{s(this,"DefaultJsonSerializer")}constructor(e){this.ignoreProperties=new Set(["$container","$containerProperty","$containerIndex","$document","$cstNode"]),this.langiumDocuments=e.shared.workspace.LangiumDocuments,this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider,this.commentProvider=e.documentation.CommentProvider}serialize(e,r){let n=r??{},i=r?.replacer,a=s((l,u)=>this.replacer(l,u,n),"defaultReplacer"),o=i?(l,u)=>i(l,u,a):a;try{return this.currentDocument=Fe(e),JSON.stringify(e,o,r?.space)}finally{this.currentDocument=void 0}}deserialize(e,r){let n=r??{},i=JSON.parse(e);return this.linkNode(i,i,n),i}replacer(e,r,{refText:n,sourceText:i,textRegions:a,comments:o,uriConverter:l}){var u,c,f,d;if(!this.ignoreProperties.has(e))if(be(r)){let p=r.ref,m=n?r.$refText:void 0;if(p){let g=Fe(p),y="";this.currentDocument&&this.currentDocument!==g&&(l?y=l(g.uri,r):y=g.uri.toString());let R=this.astNodeLocator.getAstNodePath(p);return{$ref:`${y}#${R}`,$refText:m}}else return{$error:(c=(u=r.error)===null||u===void 0?void 0:u.message)!==null&&c!==void 0?c:"Could not resolve reference",$refText:m}}else if(fe(r)){let p;if(a&&(p=this.addAstNodeRegionWithAssignmentsTo(Object.assign({},r)),(!e||r.$document)&&p?.$textRegion&&(p.$textRegion.documentURI=(f=this.currentDocument)===null||f===void 0?void 0:f.uri.toString())),i&&!e&&(p??(p=Object.assign({},r)),p.$sourceText=(d=r.$cstNode)===null||d===void 0?void 0:d.text),o){p??(p=Object.assign({},r));let m=this.commentProvider.getComment(r);m&&(p.$comment=m.replace(/\r/g,""))}return p??r}else return r}addAstNodeRegionWithAssignmentsTo(e){let r=s(n=>({offset:n.offset,end:n.end,length:n.length,range:n.range}),"createDocumentSegment");if(e.$cstNode){let n=e.$textRegion=r(e.$cstNode),i=n.assignments={};return Object.keys(e).filter(a=>!a.startsWith("$")).forEach(a=>{let o=$u(e.$cstNode,a).map(r);o.length!==0&&(i[a]=o)}),e}}linkNode(e,r,n,i,a,o){for(let[u,c]of Object.entries(e))if(Array.isArray(c))for(let f=0;f<c.length;f++){let d=c[f];$y(d)?c[f]=this.reviveReference(e,u,r,d,n):fe(d)&&this.linkNode(d,r,n,e,u,f)}else $y(c)?e[u]=this.reviveReference(e,u,r,c,n):fe(c)&&this.linkNode(c,r,n,e,u);let l=e;l.$container=i,l.$containerProperty=a,l.$containerIndex=o}reviveReference(e,r,n,i,a){let o=i.$refText,l=i.$error;if(i.$ref){let u=this.getRefNode(n,i.$ref,a.uriConverter);if(fe(u))return o||(o=this.nameProvider.getName(u)),{$refText:o??"",ref:u};l=u}if(l){let u={$refText:o??""};return u.error={container:e,property:r,message:l,reference:u},u}else return}getRefNode(e,r,n){try{let i=r.indexOf("#");if(i===0){let u=this.astNodeLocator.getAstNode(e,r.substring(1));return u||"Could not resolve path: "+r}if(i<0){let u=n?n(r):Ye.parse(r),c=this.langiumDocuments.getDocument(u);return c?c.parseResult.value:"Could not find document for URI: "+r}let a=n?n(r.substring(0,i)):Ye.parse(r.substring(0,i)),o=this.langiumDocuments.getDocument(a);if(!o)return"Could not find document for URI: "+r;if(i===r.length-1)return o.parseResult.value;let l=this.astNodeLocator.getAstNode(o.parseResult.value,r.substring(i+1));return l||"Could not resolve URI: "+r}catch(i){return String(i)}}};var da=class{static{s(this,"DefaultServiceRegistry")}get map(){return this.fileExtensionMap}constructor(e){this.languageIdMap=new Map,this.fileExtensionMap=new Map,this.textDocuments=e?.workspace.TextDocuments}register(e){let r=e.LanguageMetaData;for(let n of r.fileExtensions)this.fileExtensionMap.has(n)&&console.warn(`The file extension ${n} is used by multiple languages. It is now assigned to '${r.languageId}'.`),this.fileExtensionMap.set(n,e);this.languageIdMap.set(r.languageId,e),this.languageIdMap.size===1?this.singleton=e:this.singleton=void 0}getServices(e){var r,n;if(this.singleton!==void 0)return this.singleton;if(this.languageIdMap.size===0)throw new Error("The service registry is empty. Use `register` to register the services of a language.");let i=(n=(r=this.textDocuments)===null||r===void 0?void 0:r.get(e))===null||n===void 0?void 0:n.languageId;if(i!==void 0){let l=this.languageIdMap.get(i);if(l)return l}let a=Je.extname(e),o=this.fileExtensionMap.get(a);if(!o)throw i?new Error(`The service registry contains no services for the extension '${a}' for language '${i}'.`):new Error(`The service registry contains no services for the extension '${a}'.`);return o}hasServices(e){try{return this.getServices(e),!0}catch{return!1}}get all(){return Array.from(this.languageIdMap.values())}};function an(t){return{code:t}}s(an,"diagnosticData");var ns;(function(t){t.all=["fast","slow","built-in"]})(ns||(ns={}));var pa=class{static{s(this,"ValidationRegistry")}constructor(e){this.entries=new At,this.entriesBefore=[],this.entriesAfter=[],this.reflection=e.shared.AstReflection}register(e,r=this,n="fast"){if(n==="built-in")throw new Error("The 'built-in' category is reserved for lexer, parser, and linker errors.");for(let[i,a]of Object.entries(e)){let o=a;if(Array.isArray(o))for(let l of o){let u={check:this.wrapValidationException(l,r),category:n};this.addEntry(i,u)}else if(typeof o=="function"){let l={check:this.wrapValidationException(o,r),category:n};this.addEntry(i,l)}else It(o)}}wrapValidationException(e,r){return async(n,i,a)=>{await this.handleException(()=>e.call(r,n,i,a),"An error occurred during validation",i,n)}}async handleException(e,r,n,i){try{await e()}catch(a){if(Bt(a))throw a;console.error(`${r}:`,a),a instanceof Error&&a.stack&&console.error(a.stack);let o=a instanceof Error?a.message:String(a);n("error",`${r}: ${o}`,{node:i})}}addEntry(e,r){if(e==="AstNode"){this.entries.add("AstNode",r);return}for(let n of this.reflection.getAllSubTypes(e))this.entries.add(n,r)}getChecks(e,r){let n=H(this.entries.get(e)).concat(this.entries.get("AstNode"));return r&&(n=n.filter(i=>r.includes(i.category))),n.map(i=>i.check)}registerBeforeDocument(e,r=this){this.entriesBefore.push(this.wrapPreparationException(e,"An error occurred during set-up of the validation",r))}registerAfterDocument(e,r=this){this.entriesAfter.push(this.wrapPreparationException(e,"An error occurred during tear-down of the validation",r))}wrapPreparationException(e,r,n){return async(i,a,o,l)=>{await this.handleException(()=>e.call(n,i,a,o,l),r,a,i)}}get checksBefore(){return this.entriesBefore}get checksAfter(){return this.entriesAfter}};var ma=class{static{s(this,"DefaultDocumentValidator")}constructor(e){this.validationRegistry=e.validation.ValidationRegistry,this.metadata=e.LanguageMetaData}async validateDocument(e,r={},n=w.CancellationToken.None){let i=e.parseResult,a=[];if(await Te(n),(!r.categories||r.categories.includes("built-in"))&&(this.processLexingErrors(i,a,r),r.stopAfterLexingErrors&&a.some(o=>{var l;return((l=o.data)===null||l===void 0?void 0:l.code)===pt.LexingError})||(this.processParsingErrors(i,a,r),r.stopAfterParsingErrors&&a.some(o=>{var l;return((l=o.data)===null||l===void 0?void 0:l.code)===pt.ParsingError}))||(this.processLinkingErrors(e,a,r),r.stopAfterLinkingErrors&&a.some(o=>{var l;return((l=o.data)===null||l===void 0?void 0:l.code)===pt.LinkingError}))))return a;try{a.push(...await this.validateAst(i.value,r,n))}catch(o){if(Bt(o))throw o;console.error("An error occurred during validation:",o)}return await Te(n),a}processLexingErrors(e,r,n){var i,a,o;let l=[...e.lexerErrors,...(a=(i=e.lexerReport)===null||i===void 0?void 0:i.diagnostics)!==null&&a!==void 0?a:[]];for(let u of l){let c=(o=u.severity)!==null&&o!==void 0?o:"error",f={severity:Nl(c),range:{start:{line:u.line-1,character:u.column-1},end:{line:u.line-1,character:u.column+u.length-1}},message:u.message,data:Py(c),source:this.getSource()};r.push(f)}}processParsingErrors(e,r,n){for(let i of e.parserErrors){let a;if(isNaN(i.token.startOffset)){if("previousToken"in i){let o=i.previousToken;if(isNaN(o.startOffset)){let l={line:0,character:0};a={start:l,end:l}}else{let l={line:o.endLine-1,character:o.endColumn};a={start:l,end:l}}}}else a=dn(i.token);if(a){let o={severity:Nl("error"),range:a,message:i.message,data:an(pt.ParsingError),source:this.getSource()};r.push(o)}}}processLinkingErrors(e,r,n){for(let i of e.references){let a=i.error;if(a){let o={node:a.container,property:a.property,index:a.index,data:{code:pt.LinkingError,containerType:a.container.$type,property:a.property,refText:a.reference.$refText}};r.push(this.toDiagnostic("error",a.message,o))}}}async validateAst(e,r,n=w.CancellationToken.None){let i=[],a=s((o,l,u)=>{i.push(this.toDiagnostic(o,l,u))},"acceptor");return await this.validateAstBefore(e,r,a,n),await this.validateAstNodes(e,r,a,n),await this.validateAstAfter(e,r,a,n),i}async validateAstBefore(e,r,n,i=w.CancellationToken.None){var a;let o=this.validationRegistry.checksBefore;for(let l of o)await Te(i),await l(e,n,(a=r.categories)!==null&&a!==void 0?a:[],i)}async validateAstNodes(e,r,n,i=w.CancellationToken.None){await Promise.all(ct(e).map(async a=>{await Te(i);let o=this.validationRegistry.getChecks(a.$type,r.categories);for(let l of o)await l(a,n,i)}))}async validateAstAfter(e,r,n,i=w.CancellationToken.None){var a;let o=this.validationRegistry.checksAfter;for(let l of o)await Te(i),await l(e,n,(a=r.categories)!==null&&a!==void 0?a:[],i)}toDiagnostic(e,r,n){return{message:r,range:Ly(n),severity:Nl(e),code:n.code,codeDescription:n.codeDescription,tags:n.tags,relatedInformation:n.relatedInformation,data:n.data,source:this.getSource()}}getSource(){return this.metadata.languageId}};function Ly(t){if(t.range)return t.range;let e;return typeof t.property=="string"?e=Rs(t.node.$cstNode,t.property,t.index):typeof t.keyword=="string"&&(e=Pu(t.node.$cstNode,t.keyword,t.index)),e??(e=t.node.$cstNode),e?e.range:{start:{line:0,character:0},end:{line:0,character:0}}}s(Ly,"getDiagnosticRange");function Nl(t){switch(t){case"error":return 1;case"warning":return 2;case"info":return 3;case"hint":return 4;default:throw new Error("Invalid diagnostic severity: "+t)}}s(Nl,"toDiagnosticSeverity");function Py(t){switch(t){case"error":return an(pt.LexingError);case"warning":return an(pt.LexingWarning);case"info":return an(pt.LexingInfo);case"hint":return an(pt.LexingHint);default:throw new Error("Invalid diagnostic severity: "+t)}}s(Py,"toDiagnosticData");var pt;(function(t){t.LexingError="lexing-error",t.LexingWarning="lexing-warning",t.LexingInfo="lexing-info",t.LexingHint="lexing-hint",t.ParsingError="parsing-error",t.LinkingError="linking-error"})(pt||(pt={}));var ha=class{static{s(this,"DefaultAstNodeDescriptionProvider")}constructor(e){this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider}createDescription(e,r,n){let i=n??Fe(e);r??(r=this.nameProvider.getName(e));let a=this.astNodeLocator.getAstNodePath(e);if(!r)throw new Error(`Node at path ${a} has no name.`);let o,l=s(()=>{var u;return o??(o=Mr((u=this.nameProvider.getNameNode(e))!==null&&u!==void 0?u:e.$cstNode))},"nameSegmentGetter");return{node:e,name:r,get nameSegment(){return l()},selectionSegment:Mr(e.$cstNode),type:e.$type,documentUri:i.uri,path:a}}},ga=class{static{s(this,"DefaultReferenceDescriptionProvider")}constructor(e){this.nodeLocator=e.workspace.AstNodeLocator}async createDescriptions(e,r=w.CancellationToken.None){let n=[],i=e.parseResult.value;for(let a of ct(i))await Te(r),Zn(a).filter(o=>!Lr(o)).forEach(o=>{let l=this.createDescription(o);l&&n.push(l)});return n}createDescription(e){let r=e.reference.$nodeDescription,n=e.reference.$refNode;if(!r||!n)return;let i=Fe(e.container).uri;return{sourceUri:i,sourcePath:this.nodeLocator.getAstNodePath(e.container),targetUri:r.documentUri,targetPath:r.path,segment:Mr(n),local:Je.equals(r.documentUri,i)}}};var ya=class{static{s(this,"DefaultAstNodeLocator")}constructor(){this.segmentSeparator="/",this.indexSeparator="@"}getAstNodePath(e){if(e.$container){let r=this.getAstNodePath(e.$container),n=this.getPathSegment(e);return r+this.segmentSeparator+n}return""}getPathSegment({$containerProperty:e,$containerIndex:r}){if(!e)throw new Error("Missing '$containerProperty' in AST node.");return r!==void 0?e+this.indexSeparator+r:e}getAstNode(e,r){return r.split(this.segmentSeparator).reduce((i,a)=>{if(!i||a.length===0)return i;let o=a.indexOf(this.indexSeparator);if(o>0){let l=a.substring(0,o),u=parseInt(a.substring(o+1)),c=i[l];return c?.[u]}return i[a]},e)}};var ae={};B(ae,Lf(Jc(),1));var xa=class{static{s(this,"DefaultConfigurationProvider")}constructor(e){this._ready=new Xe,this.settings={},this.workspaceConfig=!1,this.onConfigurationSectionUpdateEmitter=new ae.Emitter,this.serviceRegistry=e.ServiceRegistry}get ready(){return this._ready.promise}initialize(e){var r,n;this.workspaceConfig=(n=(r=e.capabilities.workspace)===null||r===void 0?void 0:r.configuration)!==null&&n!==void 0?n:!1}async initialized(e){if(this.workspaceConfig){if(e.register){let r=this.serviceRegistry.all;e.register({section:r.map(n=>this.toSectionName(n.LanguageMetaData.languageId))})}if(e.fetchConfiguration){let r=this.serviceRegistry.all.map(i=>({section:this.toSectionName(i.LanguageMetaData.languageId)})),n=await e.fetchConfiguration(r);r.forEach((i,a)=>{this.updateSectionConfiguration(i.section,n[a])})}}this._ready.resolve()}updateConfiguration(e){e.settings&&Object.keys(e.settings).forEach(r=>{let n=e.settings[r];this.updateSectionConfiguration(r,n),this.onConfigurationSectionUpdateEmitter.fire({section:r,configuration:n})})}updateSectionConfiguration(e,r){this.settings[e]=r}async getConfiguration(e,r){await this.ready;let n=this.toSectionName(e);if(this.settings[n])return this.settings[n][r]}toSectionName(e){return`${e}`}get onConfigurationSectionUpdate(){return this.onConfigurationSectionUpdateEmitter.event}};var _r;(function(t){function e(r){return{dispose:s(async()=>await r(),"dispose")}}s(e,"create"),t.create=e})(_r||(_r={}));var Ta=class{static{s(this,"DefaultDocumentBuilder")}constructor(e){this.updateBuildOptions={validation:{categories:["built-in","fast"]}},this.updateListeners=[],this.buildPhaseListeners=new At,this.documentPhaseListeners=new At,this.buildState=new Map,this.documentBuildWaiters=new Map,this.currentState=J.Changed,this.langiumDocuments=e.workspace.LangiumDocuments,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.textDocuments=e.workspace.TextDocuments,this.indexManager=e.workspace.IndexManager,this.serviceRegistry=e.ServiceRegistry}async build(e,r={},n=w.CancellationToken.None){var i,a;for(let o of e){let l=o.uri.toString();if(o.state===J.Validated){if(typeof r.validation=="boolean"&&r.validation)o.state=J.IndexedReferences,o.diagnostics=void 0,this.buildState.delete(l);else if(typeof r.validation=="object"){let u=this.buildState.get(l),c=(i=u?.result)===null||i===void 0?void 0:i.validationChecks;if(c){let d=((a=r.validation.categories)!==null&&a!==void 0?a:ns.all).filter(p=>!c.includes(p));d.length>0&&(this.buildState.set(l,{completed:!1,options:{validation:Object.assign(Object.assign({},r.validation),{categories:d})},result:u.result}),o.state=J.IndexedReferences)}}}else this.buildState.delete(l)}this.currentState=J.Changed,await this.emitUpdate(e.map(o=>o.uri),[]),await this.buildDocuments(e,r,n)}async update(e,r,n=w.CancellationToken.None){this.currentState=J.Changed;for(let o of r)this.langiumDocuments.deleteDocument(o),this.buildState.delete(o.toString()),this.indexManager.remove(o);for(let o of e){if(!this.langiumDocuments.invalidateDocument(o)){let u=this.langiumDocumentFactory.fromModel({$type:"INVALID"},o);u.state=J.Changed,this.langiumDocuments.addDocument(u)}this.buildState.delete(o.toString())}let i=H(e).concat(r).map(o=>o.toString()).toSet();this.langiumDocuments.all.filter(o=>!i.has(o.uri.toString())&&this.shouldRelink(o,i)).forEach(o=>{this.serviceRegistry.getServices(o.uri).references.Linker.unlink(o),o.state=Math.min(o.state,J.ComputedScopes),o.diagnostics=void 0}),await this.emitUpdate(e,r),await Te(n);let a=this.sortDocuments(this.langiumDocuments.all.filter(o=>{var l;return o.state<J.Linked||!(!((l=this.buildState.get(o.uri.toString()))===null||l===void 0)&&l.completed)}).toArray());await this.buildDocuments(a,this.updateBuildOptions,n)}async emitUpdate(e,r){await Promise.all(this.updateListeners.map(n=>n(e,r)))}sortDocuments(e){let r=0,n=e.length-1;for(;r<n;){for(;r<e.length&&this.hasTextDocument(e[r]);)r++;for(;n>=0&&!this.hasTextDocument(e[n]);)n--;r<n&&([e[r],e[n]]=[e[n],e[r]])}return e}hasTextDocument(e){var r;return!!(!((r=this.textDocuments)===null||r===void 0)&&r.get(e.uri))}shouldRelink(e,r){return e.references.some(n=>n.error!==void 0)?!0:this.indexManager.isAffected(e,r)}onUpdate(e){return this.updateListeners.push(e),_r.create(()=>{let r=this.updateListeners.indexOf(e);r>=0&&this.updateListeners.splice(r,1)})}async buildDocuments(e,r,n){this.prepareBuild(e,r),await this.runCancelable(e,J.Parsed,n,a=>this.langiumDocumentFactory.update(a,n)),await this.runCancelable(e,J.IndexedContent,n,a=>this.indexManager.updateContent(a,n)),await this.runCancelable(e,J.ComputedScopes,n,async a=>{let o=this.serviceRegistry.getServices(a.uri).references.ScopeComputation;a.precomputedScopes=await o.computeLocalScopes(a,n)}),await this.runCancelable(e,J.Linked,n,a=>this.serviceRegistry.getServices(a.uri).references.Linker.link(a,n)),await this.runCancelable(e,J.IndexedReferences,n,a=>this.indexManager.updateReferences(a,n));let i=e.filter(a=>this.shouldValidate(a));await this.runCancelable(i,J.Validated,n,a=>this.validate(a,n));for(let a of e){let o=this.buildState.get(a.uri.toString());o&&(o.completed=!0)}}prepareBuild(e,r){for(let n of e){let i=n.uri.toString(),a=this.buildState.get(i);(!a||a.completed)&&this.buildState.set(i,{completed:!1,options:r,result:a?.result})}}async runCancelable(e,r,n,i){let a=e.filter(l=>l.state<r);for(let l of a)await Te(n),await i(l),l.state=r,await this.notifyDocumentPhase(l,r,n);let o=e.filter(l=>l.state===r);await this.notifyBuildPhase(o,r,n),this.currentState=r}onBuildPhase(e,r){return this.buildPhaseListeners.add(e,r),_r.create(()=>{this.buildPhaseListeners.delete(e,r)})}onDocumentPhase(e,r){return this.documentPhaseListeners.add(e,r),_r.create(()=>{this.documentPhaseListeners.delete(e,r)})}waitUntil(e,r,n){let i;if(r&&"path"in r?i=r:n=r,n??(n=w.CancellationToken.None),i){let a=this.langiumDocuments.getDocument(i);if(a&&a.state>e)return Promise.resolve(i)}return this.currentState>=e?Promise.resolve(void 0):n.isCancellationRequested?Promise.reject(Ut):new Promise((a,o)=>{let l=this.onBuildPhase(e,()=>{if(l.dispose(),u.dispose(),i){let c=this.langiumDocuments.getDocument(i);a(c?.uri)}else a(void 0)}),u=n.onCancellationRequested(()=>{l.dispose(),u.dispose(),o(Ut)})})}async notifyDocumentPhase(e,r,n){let a=this.documentPhaseListeners.get(r).slice();for(let o of a)try{await o(e,n)}catch(l){if(!Bt(l))throw l}}async notifyBuildPhase(e,r,n){if(e.length===0)return;let a=this.buildPhaseListeners.get(r).slice();for(let o of a)await Te(n),await o(e,n)}shouldValidate(e){return!!this.getBuildOptions(e).validation}async validate(e,r){var n,i;let a=this.serviceRegistry.getServices(e.uri).validation.DocumentValidator,o=this.getBuildOptions(e).validation,l=typeof o=="object"?o:void 0,u=await a.validateDocument(e,l,r);e.diagnostics?e.diagnostics.push(...u):e.diagnostics=u;let c=this.buildState.get(e.uri.toString());if(c){(n=c.result)!==null&&n!==void 0||(c.result={});let f=(i=l?.categories)!==null&&i!==void 0?i:ns.all;c.result.validationChecks?c.result.validationChecks.push(...f):c.result.validationChecks=[...f]}}getBuildOptions(e){var r,n;return(n=(r=this.buildState.get(e.uri.toString()))===null||r===void 0?void 0:r.options)!==null&&n!==void 0?n:{}}};var Aa=class{static{s(this,"DefaultIndexManager")}constructor(e){this.symbolIndex=new Map,this.symbolByTypeIndex=new sn,this.referenceIndex=new Map,this.documents=e.workspace.LangiumDocuments,this.serviceRegistry=e.ServiceRegistry,this.astReflection=e.AstReflection}findAllReferences(e,r){let n=Fe(e).uri,i=[];return this.referenceIndex.forEach(a=>{a.forEach(o=>{Je.equals(o.targetUri,n)&&o.targetPath===r&&i.push(o)})}),H(i)}allElements(e,r){let n=H(this.symbolIndex.keys());return r&&(n=n.filter(i=>!r||r.has(i))),n.map(i=>this.getFileDescriptions(i,e)).flat()}getFileDescriptions(e,r){var n;return r?this.symbolByTypeIndex.get(e,r,()=>{var a;return((a=this.symbolIndex.get(e))!==null&&a!==void 0?a:[]).filter(l=>this.astReflection.isSubtype(l.type,r))}):(n=this.symbolIndex.get(e))!==null&&n!==void 0?n:[]}remove(e){let r=e.toString();this.symbolIndex.delete(r),this.symbolByTypeIndex.clear(r),this.referenceIndex.delete(r)}async updateContent(e,r=w.CancellationToken.None){let i=await this.serviceRegistry.getServices(e.uri).references.ScopeComputation.computeExports(e,r),a=e.uri.toString();this.symbolIndex.set(a,i),this.symbolByTypeIndex.clear(a)}async updateReferences(e,r=w.CancellationToken.None){let i=await this.serviceRegistry.getServices(e.uri).workspace.ReferenceDescriptionProvider.createDescriptions(e,r);this.referenceIndex.set(e.uri.toString(),i)}isAffected(e,r){let n=this.referenceIndex.get(e.uri.toString());return n?n.some(i=>!i.local&&r.has(i.targetUri.toString())):!1}};var Ra=class{static{s(this,"DefaultWorkspaceManager")}constructor(e){this.initialBuildOptions={},this._ready=new Xe,this.serviceRegistry=e.ServiceRegistry,this.langiumDocuments=e.workspace.LangiumDocuments,this.documentBuilder=e.workspace.DocumentBuilder,this.fileSystemProvider=e.workspace.FileSystemProvider,this.mutex=e.workspace.WorkspaceLock}get ready(){return this._ready.promise}get workspaceFolders(){return this.folders}initialize(e){var r;this.folders=(r=e.workspaceFolders)!==null&&r!==void 0?r:void 0}initialized(e){return this.mutex.write(r=>{var n;return this.initializeWorkspace((n=this.folders)!==null&&n!==void 0?n:[],r)})}async initializeWorkspace(e,r=w.CancellationToken.None){let n=await this.performStartup(e);await Te(r),await this.documentBuilder.build(n,this.initialBuildOptions,r)}async performStartup(e){let r=this.serviceRegistry.all.flatMap(a=>a.LanguageMetaData.fileExtensions),n=[],i=s(a=>{n.push(a),this.langiumDocuments.hasDocument(a.uri)||this.langiumDocuments.addDocument(a)},"collector");return await this.loadAdditionalDocuments(e,i),await Promise.all(e.map(a=>[a,this.getRootFolder(a)]).map(async a=>this.traverseFolder(...a,r,i))),this._ready.resolve(),n}loadAdditionalDocuments(e,r){return Promise.resolve()}getRootFolder(e){return Ye.parse(e.uri)}async traverseFolder(e,r,n,i){let a=await this.fileSystemProvider.readDirectory(r);await Promise.all(a.map(async o=>{if(this.includeEntry(e,o,n)){if(o.isDirectory)await this.traverseFolder(e,o.uri,n,i);else if(o.isFile){let l=await this.langiumDocuments.getOrCreateDocument(o.uri);i(l)}}}))}includeEntry(e,r,n){let i=Je.basename(r.uri);if(i.startsWith("."))return!1;if(r.isDirectory)return i!=="node_modules"&&i!=="out";if(r.isFile){let a=Je.extname(r.uri);return n.includes(a)}return!1}};var Ea=class{static{s(this,"DefaultLexerErrorMessageProvider")}buildUnexpectedCharactersMessage(e,r,n,i,a){return $i.buildUnexpectedCharactersMessage(e,r,n,i,a)}buildUnableToPopLexerModeMessage(e){return $i.buildUnableToPopLexerModeMessage(e)}},_l={mode:"full"},on=class{static{s(this,"DefaultLexer")}constructor(e){this.errorMessageProvider=e.parser.LexerErrorMessageProvider,this.tokenBuilder=e.parser.TokenBuilder;let r=this.tokenBuilder.buildTokens(e.Grammar,{caseInsensitive:e.LanguageMetaData.caseInsensitive});this.tokenTypes=this.toTokenTypeDictionary(r);let n=sf(r)?Object.values(r):r,i=e.LanguageMetaData.mode==="production";this.chevrotainLexer=new ue(n,{positionTracking:"full",skipValidations:i,errorMessageProvider:this.errorMessageProvider})}get definition(){return this.tokenTypes}tokenize(e,r=_l){var n,i,a;let o=this.chevrotainLexer.tokenize(e);return{tokens:o.tokens,errors:o.errors,hidden:(n=o.groups.hidden)!==null&&n!==void 0?n:[],report:(a=(i=this.tokenBuilder).flushLexingReport)===null||a===void 0?void 0:a.call(i,e)}}toTokenTypeDictionary(e){if(sf(e))return e;let r=af(e)?Object.values(e.modes).flat():e,n={};return r.forEach(i=>n[i.name]=i),n}};function wl(t){return Array.isArray(t)&&(t.length===0||"name"in t[0])}s(wl,"isTokenTypeArray");function af(t){return t&&"modes"in t&&"defaultMode"in t}s(af,"isIMultiModeLexerDefinition");function sf(t){return!wl(t)&&!af(t)}s(sf,"isTokenTypeDictionary");function uf(t,e,r){let n,i;typeof t=="string"?(i=e,n=r):(i=t.range.start,n=e),i||(i=j.create(0,0));let a=Fy(t),o=ff(n),l=KN({lines:a,position:i,options:o});return XN({index:0,tokens:l,position:i})}s(uf,"parseJSDoc");function cf(t,e){let r=ff(e),n=Fy(t);if(n.length===0)return!1;let i=n[0],a=n[n.length-1],o=r.start,l=r.end;return!!o?.exec(i)&&!!l?.exec(a)}s(cf,"isJSDoc");function Fy(t){let e="";return typeof t=="string"?e=t:e=t.text,e.split(Su)}s(Fy,"getLines");var My=/\s*(@([\p{L}][\p{L}\p{N}]*)?)/uy,WN=/\{(@[\p{L}][\p{L}\p{N}]*)(\s*)([^\r\n}]+)?\}/gu;function KN(t){var e,r,n;let i=[],a=t.position.line,o=t.position.character;for(let l=0;l<t.lines.length;l++){let u=l===0,c=l===t.lines.length-1,f=t.lines[l],d=0;if(u&&t.options.start){let m=(e=t.options.start)===null||e===void 0?void 0:e.exec(f);m&&(d=m.index+m[0].length)}else{let m=(r=t.options.line)===null||r===void 0?void 0:r.exec(f);m&&(d=m.index+m[0].length)}if(c){let m=(n=t.options.end)===null||n===void 0?void 0:n.exec(f);m&&(f=f.substring(0,m.index))}if(f=f.substring(0,qN(f)),lf(f,d)>=f.length){if(i.length>0){let m=j.create(a,o);i.push({type:"break",content:"",range:U.create(m,m)})}}else{My.lastIndex=d;let m=My.exec(f);if(m){let g=m[0],y=m[1],R=j.create(a,o+d),T=j.create(a,o+d+g.length);i.push({type:"tag",content:y,range:U.create(R,T)}),d+=g.length,d=lf(f,d)}if(d<f.length){let g=f.substring(d),y=Array.from(g.matchAll(WN));i.push(...HN(y,g,a,o+d))}}a++,o=0}return i.length>0&&i[i.length-1].type==="break"?i.slice(0,-1):i}s(KN,"tokenize");function HN(t,e,r,n){let i=[];if(t.length===0){let a=j.create(r,n),o=j.create(r,n+e.length);i.push({type:"text",content:e,range:U.create(a,o)})}else{let a=0;for(let l of t){let u=l.index,c=e.substring(a,u);c.length>0&&i.push({type:"text",content:e.substring(a,u),range:U.create(j.create(r,a+n),j.create(r,u+n))});let f=c.length+1,d=l[1];if(i.push({type:"inline-tag",content:d,range:U.create(j.create(r,a+f+n),j.create(r,a+f+d.length+n))}),f+=d.length,l.length===4){f+=l[2].length;let p=l[3];i.push({type:"text",content:p,range:U.create(j.create(r,a+f+n),j.create(r,a+f+p.length+n))})}else i.push({type:"text",content:"",range:U.create(j.create(r,a+f+n),j.create(r,a+f+n))});a=u+l[0].length}let o=e.substring(a);o.length>0&&i.push({type:"text",content:o,range:U.create(j.create(r,a+n),j.create(r,a+n+o.length))})}return i}s(HN,"buildInlineTokens");var VN=/\S/,zN=/\s*$/;function lf(t,e){let r=t.substring(e).match(VN);return r?e+r.index:t.length}s(lf,"skipWhitespace");function qN(t){let e=t.match(zN);if(e&&typeof e.index=="number")return e.index}s(qN,"lastCharacter");function XN(t){var e,r,n,i;let a=j.create(t.position.line,t.position.character);if(t.tokens.length===0)return new bl([],U.create(a,a));let o=[];for(;t.index<t.tokens.length;){let c=YN(t,o[o.length-1]);c&&o.push(c)}let l=(r=(e=o[0])===null||e===void 0?void 0:e.range.start)!==null&&r!==void 0?r:a,u=(i=(n=o[o.length-1])===null||n===void 0?void 0:n.range.end)!==null&&i!==void 0?i:a;return new bl(o,U.create(l,u))}s(XN,"parseJSDocComment");function YN(t,e){let r=t.tokens[t.index];if(r.type==="tag")return Uy(t,!1);if(r.type==="text"||r.type==="inline-tag")return Gy(t);JN(r,e),t.index++}s(YN,"parseJSDocElement");function JN(t,e){if(e){let r=new Ol("",t.range);"inlines"in e?e.inlines.push(r):e.content.inlines.push(r)}}s(JN,"appendEmptyLine");function Gy(t){let e=t.tokens[t.index],r=e,n=e,i=[];for(;e&&e.type!=="break"&&e.type!=="tag";)i.push(ZN(t)),n=e,e=t.tokens[t.index];return new Ia(i,U.create(r.range.start,n.range.end))}s(Gy,"parseJSDocText");function ZN(t){return t.tokens[t.index].type==="inline-tag"?Uy(t,!0):By(t)}s(ZN,"parseJSDocInline");function Uy(t,e){let r=t.tokens[t.index++],n=r.content.substring(1),i=t.tokens[t.index];if(i?.type==="text")if(e){let a=By(t);return new va(n,new Ia([a],a.range),e,U.create(r.range.start,a.range.end))}else{let a=Gy(t);return new va(n,a,e,U.create(r.range.start,a.range.end))}else{let a=r.range;return new va(n,new Ia([],a),e,a)}}s(Uy,"parseJSDocTag");function By(t){let e=t.tokens[t.index++];return new Ol(e.content,e.range)}s(By,"parseJSDocLine");function ff(t){if(!t)return ff({start:"/**",end:"*/",line:"*"});let{start:e,end:r,line:n}=t;return{start:of(e,!0),end:of(r,!1),line:of(n,!0)}}s(ff,"normalizeOptions");function of(t,e){if(typeof t=="string"||typeof t=="object"){let r=typeof t=="string"?Kr(t):t.source;return e?new RegExp(`^\\s*${r}`):new RegExp(`\\s*${r}\\s*$`)}else return t}s(of,"normalizeOption");var bl=class{static{s(this,"JSDocCommentImpl")}constructor(e,r){this.elements=e,this.range=r}getTag(e){return this.getAllTags().find(r=>r.name===e)}getTags(e){return this.getAllTags().filter(r=>r.name===e)}getAllTags(){return this.elements.filter(e=>"name"in e)}toString(){let e="";for(let r of this.elements)if(e.length===0)e=r.toString();else{let n=r.toString();e+=Dy(e)+n}return e.trim()}toMarkdown(e){let r="";for(let n of this.elements)if(r.length===0)r=n.toMarkdown(e);else{let i=n.toMarkdown(e);r+=Dy(r)+i}return r.trim()}},va=class{static{s(this,"JSDocTagImpl")}constructor(e,r,n,i){this.name=e,this.content=r,this.inline=n,this.range=i}toString(){let e=`@${this.name}`,r=this.content.toString();return this.content.inlines.length===1?e=`${e} ${r}`:this.content.inlines.length>1&&(e=`${e}
${r}`),this.inline?`{${e}}`:e}toMarkdown(e){var r,n;return(n=(r=e?.renderTag)===null||r===void 0?void 0:r.call(e,this))!==null&&n!==void 0?n:this.toMarkdownDefault(e)}toMarkdownDefault(e){let r=this.content.toMarkdown(e);if(this.inline){let a=QN(this.name,r,e??{});if(typeof a=="string")return a}let n="";e?.tag==="italic"||e?.tag===void 0?n="*":e?.tag==="bold"?n="**":e?.tag==="bold-italic"&&(n="***");let i=`${n}@${this.name}${n}`;return this.content.inlines.length===1?i=`${i} \u2014 ${r}`:this.content.inlines.length>1&&(i=`${i}
${r}`),this.inline?`{${i}}`:i}};function QN(t,e,r){var n,i;if(t==="linkplain"||t==="linkcode"||t==="link"){let a=e.indexOf(" "),o=e;if(a>0){let u=lf(e,a);o=e.substring(u),e=e.substring(0,a)}return(t==="linkcode"||t==="link"&&r.link==="code")&&(o=`\`${o}\``),(i=(n=r.renderLink)===null||n===void 0?void 0:n.call(r,e,o))!==null&&i!==void 0?i:e_(e,o)}}s(QN,"renderInlineTag");function e_(t,e){try{return Ye.parse(t,!0),`[${e}](${t})`}catch{return t}}s(e_,"renderLinkDefault");var Ia=class{static{s(this,"JSDocTextImpl")}constructor(e,r){this.inlines=e,this.range=r}toString(){let e="";for(let r=0;r<this.inlines.length;r++){let n=this.inlines[r],i=this.inlines[r+1];e+=n.toString(),i&&i.range.start.line>n.range.start.line&&(e+=`
`)}return e}toMarkdown(e){let r="";for(let n=0;n<this.inlines.length;n++){let i=this.inlines[n],a=this.inlines[n+1];r+=i.toMarkdown(e),a&&a.range.start.line>i.range.start.line&&(r+=`
`)}return r}},Ol=class{static{s(this,"JSDocLineImpl")}constructor(e,r){this.text=e,this.range=r}toString(){return this.text}toMarkdown(){return this.text}};function Dy(t){return t.endsWith(`
`)?`
`:`

`}s(Dy,"fillNewlines");var Sa=class{static{s(this,"JSDocDocumentationProvider")}constructor(e){this.indexManager=e.shared.workspace.IndexManager,this.commentProvider=e.documentation.CommentProvider}getDocumentation(e){let r=this.commentProvider.getComment(e);if(r&&cf(r))return uf(r).toMarkdown({renderLink:s((i,a)=>this.documentationLinkRenderer(e,i,a),"renderLink"),renderTag:s(i=>this.documentationTagRenderer(e,i),"renderTag")})}documentationLinkRenderer(e,r,n){var i;let a=(i=this.findNameInPrecomputedScopes(e,r))!==null&&i!==void 0?i:this.findNameInGlobalScope(e,r);if(a&&a.nameSegment){let o=a.nameSegment.range.start.line+1,l=a.nameSegment.range.start.character+1,u=a.documentUri.with({fragment:`L${o},${l}`});return`[${n}](${u.toString()})`}else return}documentationTagRenderer(e,r){}findNameInPrecomputedScopes(e,r){let i=Fe(e).precomputedScopes;if(!i)return;let a=e;do{let l=i.get(a).find(u=>u.name===r);if(l)return l;a=a.$container}while(a)}findNameInGlobalScope(e,r){return this.indexManager.allElements().find(i=>i.name===r)}};var ka=class{static{s(this,"DefaultCommentProvider")}constructor(e){this.grammarConfig=()=>e.parser.GrammarConfig}getComment(e){var r;return nf(e)?e.$comment:(r=eu(e.$cstNode,this.grammarConfig().multilineCommentRules))===null||r===void 0?void 0:r.text}};var Ca=class{static{s(this,"DefaultAsyncParser")}constructor(e){this.syncParser=e.parser.LangiumParser}parse(e,r){return Promise.resolve(this.syncParser.parse(e))}},df=class{static{s(this,"AbstractThreadedAsyncParser")}constructor(e){this.threadCount=8,this.terminationDelay=200,this.workerPool=[],this.queue=[],this.hydrator=e.serializer.Hydrator}initializeWorkers(){for(;this.workerPool.length<this.threadCount;){let e=this.createWorker();e.onReady(()=>{if(this.queue.length>0){let r=this.queue.shift();r&&(e.lock(),r.resolve(e))}}),this.workerPool.push(e)}}async parse(e,r){let n=await this.acquireParserWorker(r),i=new Xe,a,o=r.onCancellationRequested(()=>{a=setTimeout(()=>{this.terminateWorker(n)},this.terminationDelay)});return n.parse(e).then(l=>{let u=this.hydrator.hydrate(l);i.resolve(u)}).catch(l=>{i.reject(l)}).finally(()=>{o.dispose(),clearTimeout(a)}),i.promise}terminateWorker(e){e.terminate();let r=this.workerPool.indexOf(e);r>=0&&this.workerPool.splice(r,1)}async acquireParserWorker(e){this.initializeWorkers();for(let n of this.workerPool)if(n.ready)return n.lock(),n;let r=new Xe;return e.onCancellationRequested(()=>{let n=this.queue.indexOf(r);n>=0&&this.queue.splice(n,1),r.reject(Ut)}),this.queue.push(r),r.promise}},pf=class{static{s(this,"ParserWorker")}get ready(){return this._ready}get onReady(){return this.onReadyEmitter.event}constructor(e,r,n,i){this.onReadyEmitter=new ae.Emitter,this.deferred=new Xe,this._ready=!0,this._parsing=!1,this.sendMessage=e,this._terminate=i,r(a=>{let o=a;this.deferred.resolve(o),this.unlock()}),n(a=>{this.deferred.reject(a),this.unlock()})}terminate(){this.deferred.reject(Ut),this._terminate()}lock(){this._ready=!1}unlock(){this._parsing=!1,this._ready=!0,this.onReadyEmitter.fire()}parse(e){if(this._parsing)throw new Error("Parser worker is busy");return this._parsing=!0,this.deferred=new Xe,this.sendMessage(e),this.deferred.promise}};var Na=class{static{s(this,"DefaultWorkspaceLock")}constructor(){this.previousTokenSource=new w.CancellationTokenSource,this.writeQueue=[],this.readQueue=[],this.done=!0}write(e){this.cancelWrite();let r=Sl();return this.previousTokenSource=r,this.enqueue(this.writeQueue,e,r.token)}read(e){return this.enqueue(this.readQueue,e)}enqueue(e,r,n=w.CancellationToken.None){let i=new Xe,a={action:r,deferred:i,cancellationToken:n};return e.push(a),this.performNextOperation(),i.promise}async performNextOperation(){if(!this.done)return;let e=[];if(this.writeQueue.length>0)e.push(this.writeQueue.shift());else if(this.readQueue.length>0)e.push(...this.readQueue.splice(0,this.readQueue.length));else return;this.done=!1,await Promise.all(e.map(async({action:r,deferred:n,cancellationToken:i})=>{try{let a=await Promise.resolve().then(()=>r(i));n.resolve(a)}catch(a){Bt(a)?n.resolve(void 0):n.reject(a)}})),this.done=!0,this.performNextOperation()}cancelWrite(){this.previousTokenSource.cancel()}};var _a=class{static{s(this,"DefaultHydrator")}constructor(e){this.grammarElementIdMap=new nn,this.tokenTypeIdMap=new nn,this.grammar=e.Grammar,this.lexer=e.parser.Lexer,this.linker=e.references.Linker}dehydrate(e){return{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport?this.dehydrateLexerReport(e.lexerReport):void 0,parserErrors:e.parserErrors.map(r=>Object.assign(Object.assign({},r),{message:r.message})),value:this.dehydrateAstNode(e.value,this.createDehyrationContext(e.value))}}dehydrateLexerReport(e){return e}createDehyrationContext(e){let r=new Map,n=new Map;for(let i of ct(e))r.set(i,{});if(e.$cstNode)for(let i of Pr(e.$cstNode))n.set(i,{});return{astNodes:r,cstNodes:n}}dehydrateAstNode(e,r){let n=r.astNodes.get(e);n.$type=e.$type,n.$containerIndex=e.$containerIndex,n.$containerProperty=e.$containerProperty,e.$cstNode!==void 0&&(n.$cstNode=this.dehydrateCstNode(e.$cstNode,r));for(let[i,a]of Object.entries(e))if(!i.startsWith("$"))if(Array.isArray(a)){let o=[];n[i]=o;for(let l of a)fe(l)?o.push(this.dehydrateAstNode(l,r)):be(l)?o.push(this.dehydrateReference(l,r)):o.push(l)}else fe(a)?n[i]=this.dehydrateAstNode(a,r):be(a)?n[i]=this.dehydrateReference(a,r):a!==void 0&&(n[i]=a);return n}dehydrateReference(e,r){let n={};return n.$refText=e.$refText,e.$refNode&&(n.$refNode=r.cstNodes.get(e.$refNode)),n}dehydrateCstNode(e,r){let n=r.cstNodes.get(e);return is(e)?n.fullText=e.fullText:n.grammarSource=this.getGrammarElementId(e.grammarSource),n.hidden=e.hidden,n.astNode=r.astNodes.get(e.astNode),ht(e)?n.content=e.content.map(i=>this.dehydrateCstNode(i,r)):ur(e)&&(n.tokenType=e.tokenType.name,n.offset=e.offset,n.length=e.length,n.startLine=e.range.start.line,n.startColumn=e.range.start.character,n.endLine=e.range.end.line,n.endColumn=e.range.end.character),n}hydrate(e){let r=e.value,n=this.createHydrationContext(r);return"$cstNode"in r&&this.hydrateCstNode(r.$cstNode,n),{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport,parserErrors:e.parserErrors,value:this.hydrateAstNode(r,n)}}createHydrationContext(e){let r=new Map,n=new Map;for(let a of ct(e))r.set(a,{});let i;if(e.$cstNode)for(let a of Pr(e.$cstNode)){let o;"fullText"in a?(o=new qi(a.fullText),i=o):"content"in a?o=new en:"tokenType"in a&&(o=this.hydrateCstLeafNode(a)),o&&(n.set(a,o),o.root=i)}return{astNodes:r,cstNodes:n}}hydrateAstNode(e,r){let n=r.astNodes.get(e);n.$type=e.$type,n.$containerIndex=e.$containerIndex,n.$containerProperty=e.$containerProperty,e.$cstNode&&(n.$cstNode=r.cstNodes.get(e.$cstNode));for(let[i,a]of Object.entries(e))if(!i.startsWith("$"))if(Array.isArray(a)){let o=[];n[i]=o;for(let l of a)fe(l)?o.push(this.setParent(this.hydrateAstNode(l,r),n)):be(l)?o.push(this.hydrateReference(l,n,i,r)):o.push(l)}else fe(a)?n[i]=this.setParent(this.hydrateAstNode(a,r),n):be(a)?n[i]=this.hydrateReference(a,n,i,r):a!==void 0&&(n[i]=a);return n}setParent(e,r){return e.$container=r,e}hydrateReference(e,r,n,i){return this.linker.buildReference(r,n,i.cstNodes.get(e.$refNode),e.$refText)}hydrateCstNode(e,r,n=0){let i=r.cstNodes.get(e);if(typeof e.grammarSource=="number"&&(i.grammarSource=this.getGrammarElement(e.grammarSource)),i.astNode=r.astNodes.get(e.astNode),ht(i))for(let a of e.content){let o=this.hydrateCstNode(a,r,n++);i.content.push(o)}return i}hydrateCstLeafNode(e){let r=this.getTokenType(e.tokenType),n=e.offset,i=e.length,a=e.startLine,o=e.startColumn,l=e.endLine,u=e.endColumn,c=e.hidden;return new Qr(n,i,{start:{line:a,character:o},end:{line:l,character:u}},r,c)}getTokenType(e){return this.lexer.definition[e]}getGrammarElementId(e){if(e)return this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap(),this.grammarElementIdMap.get(e)}getGrammarElement(e){return this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap(),this.grammarElementIdMap.getKey(e)}createGrammarElementIdMap(){let e=0;for(let r of ct(this.grammar))ds(r)&&this.grammarElementIdMap.set(r,e++)}};function mf(t){return{documentation:{CommentProvider:s(e=>new ka(e),"CommentProvider"),DocumentationProvider:s(e=>new Sa(e),"DocumentationProvider")},parser:{AsyncParser:s(e=>new Ca(e),"AsyncParser"),GrammarConfig:s(e=>Uu(e),"GrammarConfig"),LangiumParser:s(e=>Hc(e),"LangiumParser"),CompletionParser:s(e=>Kc(e),"CompletionParser"),ValueConverter:s(()=>new rn,"ValueConverter"),TokenBuilder:s(()=>new sr,"TokenBuilder"),Lexer:s(e=>new on(e),"Lexer"),ParserErrorMessageProvider:s(()=>new Xi,"ParserErrorMessageProvider"),LexerErrorMessageProvider:s(()=>new Ea,"LexerErrorMessageProvider")},workspace:{AstNodeLocator:s(()=>new ya,"AstNodeLocator"),AstNodeDescriptionProvider:s(e=>new ha(e),"AstNodeDescriptionProvider"),ReferenceDescriptionProvider:s(e=>new ga(e),"ReferenceDescriptionProvider")},references:{Linker:s(e=>new ia(e),"Linker"),NameProvider:s(()=>new sa,"NameProvider"),ScopeProvider:s(e=>new ca(e),"ScopeProvider"),ScopeComputation:s(e=>new oa(e),"ScopeComputation"),References:s(e=>new aa(e),"References")},serializer:{Hydrator:s(e=>new _a(e),"Hydrator"),JsonSerializer:s(e=>new fa(e),"JsonSerializer")},validation:{DocumentValidator:s(e=>new ma(e),"DocumentValidator"),ValidationRegistry:s(e=>new pa(e),"ValidationRegistry")},shared:s(()=>t.shared,"shared")}}s(mf,"createDefaultCoreModule");function hf(t){return{ServiceRegistry:s(e=>new da(e),"ServiceRegistry"),workspace:{LangiumDocuments:s(e=>new na(e),"LangiumDocuments"),LangiumDocumentFactory:s(e=>new ra(e),"LangiumDocumentFactory"),DocumentBuilder:s(e=>new Ta(e),"DocumentBuilder"),IndexManager:s(e=>new Aa(e),"IndexManager"),WorkspaceManager:s(e=>new Ra(e),"WorkspaceManager"),FileSystemProvider:s(e=>t.fileSystemProvider(e),"FileSystemProvider"),WorkspaceLock:s(()=>new Na,"WorkspaceLock"),ConfigurationProvider:s(e=>new xa(e),"ConfigurationProvider")}}}s(hf,"createDefaultSharedCoreModule");var gf;(function(t){t.merge=(e,r)=>$l($l({},e),r)})(gf||(gf={}));function Ll(t,e,r,n,i,a,o,l,u){let c=[t,e,r,n,i,a,o,l,u].reduce($l,{});return Vy(c)}s(Ll,"inject");var Ky=Symbol("isProxy");function Hy(t){if(t&&t[Ky])for(let e of Object.values(t))Hy(e);return t}s(Hy,"eagerLoad");function Vy(t,e){let r=new Proxy({},{deleteProperty:s(()=>!1,"deleteProperty"),set:s(()=>{throw new Error("Cannot set property on injected service container")},"set"),get:s((n,i)=>i===Ky?!0:Wy(n,i,t,e||r),"get"),getOwnPropertyDescriptor:s((n,i)=>(Wy(n,i,t,e||r),Object.getOwnPropertyDescriptor(n,i)),"getOwnPropertyDescriptor"),has:s((n,i)=>i in t,"has"),ownKeys:s(()=>[...Object.getOwnPropertyNames(t)],"ownKeys")});return r}s(Vy,"_inject");var jy=Symbol();function Wy(t,e,r,n){if(e in t){if(t[e]instanceof Error)throw new Error("Construction failure. Please make sure that your dependencies are constructable.",{cause:t[e]});if(t[e]===jy)throw new Error('Cycle detected. Please make "'+String(e)+'" lazy. Visit https://langium.org/docs/reference/configuration-services/#resolving-cyclic-dependencies');return t[e]}else if(e in r){let i=r[e];t[e]=jy;try{t[e]=typeof i=="function"?i(n):Vy(i,n)}catch(a){throw t[e]=a instanceof Error?a:void 0,a}return t[e]}else return}s(Wy,"_resolve");function $l(t,e){if(e){for(let[r,n]of Object.entries(e))if(n!==void 0){let i=t[r];i!==null&&n!==null&&typeof i=="object"&&typeof n=="object"?t[r]=$l(i,n):t[r]=n}}return t}s($l,"_merge");var yf={indentTokenName:"INDENT",dedentTokenName:"DEDENT",whitespaceTokenName:"WS",ignoreIndentationDelimiters:[]},ln;(function(t){t.REGULAR="indentation-sensitive",t.IGNORE_INDENTATION="ignore-indentation"})(ln||(ln={}));var Pl=class extends sr{static{s(this,"IndentationAwareTokenBuilder")}constructor(e=yf){super(),this.indentationStack=[0],this.whitespaceRegExp=/[ \t]+/y,this.options=Object.assign(Object.assign({},yf),e),this.indentTokenType=Sr({name:this.options.indentTokenName,pattern:this.indentMatcher.bind(this),line_breaks:!1}),this.dedentTokenType=Sr({name:this.options.dedentTokenName,pattern:this.dedentMatcher.bind(this),line_breaks:!1})}buildTokens(e,r){let n=super.buildTokens(e,r);if(!wl(n))throw new Error("Invalid tokens built by default builder");let{indentTokenName:i,dedentTokenName:a,whitespaceTokenName:o,ignoreIndentationDelimiters:l}=this.options,u,c,f,d=[];for(let p of n){for(let[m,g]of l)p.name===m?p.PUSH_MODE=ln.IGNORE_INDENTATION:p.name===g&&(p.POP_MODE=!0);p.name===a?u=p:p.name===i?c=p:p.name===o?f=p:d.push(p)}if(!u||!c||!f)throw new Error("Some indentation/whitespace tokens not found!");return l.length>0?{modes:{[ln.REGULAR]:[u,c,...d,f],[ln.IGNORE_INDENTATION]:[...d,f]},defaultMode:ln.REGULAR}:[u,c,f,...d]}flushLexingReport(e){let r=super.flushLexingReport(e);return Object.assign(Object.assign({},r),{remainingDedents:this.flushRemainingDedents(e)})}isStartOfLine(e,r){return r===0||`\r
`.includes(e[r-1])}matchWhitespace(e,r,n,i){var a;this.whitespaceRegExp.lastIndex=r;let o=this.whitespaceRegExp.exec(e);return{currIndentLevel:(a=o?.[0].length)!==null&&a!==void 0?a:0,prevIndentLevel:this.indentationStack.at(-1),match:o}}createIndentationTokenInstance(e,r,n,i){let a=this.getLineNumber(r,i);return tr(e,n,i,i+n.length,a,a,1,n.length)}getLineNumber(e,r){return e.substring(0,r).split(/\r\n|\r|\n/).length}indentMatcher(e,r,n,i){if(!this.isStartOfLine(e,r))return null;let{currIndentLevel:a,prevIndentLevel:o,match:l}=this.matchWhitespace(e,r,n,i);return a<=o?null:(this.indentationStack.push(a),l)}dedentMatcher(e,r,n,i){var a,o,l,u;if(!this.isStartOfLine(e,r))return null;let{currIndentLevel:c,prevIndentLevel:f,match:d}=this.matchWhitespace(e,r,n,i);if(c>=f)return null;let p=this.indentationStack.lastIndexOf(c);if(p===-1)return this.diagnostics.push({severity:"error",message:`Invalid dedent level ${c} at offset: ${r}. Current indentation stack: ${this.indentationStack}`,offset:r,length:(o=(a=d?.[0])===null||a===void 0?void 0:a.length)!==null&&o!==void 0?o:0,line:this.getLineNumber(e,r),column:1}),null;let m=this.indentationStack.length-p-1,g=(u=(l=e.substring(0,r).match(/[\r\n]+$/))===null||l===void 0?void 0:l[0].length)!==null&&u!==void 0?u:1;for(let y=0;y<m;y++){let R=this.createIndentationTokenInstance(this.dedentTokenType,e,"",r-(g-1));n.push(R),this.indentationStack.pop()}return null}buildTerminalToken(e){let r=super.buildTerminalToken(e),{indentTokenName:n,dedentTokenName:i,whitespaceTokenName:a}=this.options;return r.name===n?this.indentTokenType:r.name===i?this.dedentTokenType:r.name===a?Sr({name:a,pattern:this.whitespaceRegExp,group:ue.SKIPPED}):r}flushRemainingDedents(e){let r=[];for(;this.indentationStack.length>1;)r.push(this.createIndentationTokenInstance(this.dedentTokenType,e,"",e.length)),this.indentationStack.pop();return this.indentationStack=[0],r}},xf=class extends on{static{s(this,"IndentationAwareLexer")}constructor(e){if(super(e),e.parser.TokenBuilder instanceof Pl)this.indentationTokenBuilder=e.parser.TokenBuilder;else throw new Error("IndentationAwareLexer requires an accompanying IndentationAwareTokenBuilder")}tokenize(e,r=_l){let n=super.tokenize(e),i=n.report;r?.mode==="full"&&n.tokens.push(...i.remainingDedents),i.remainingDedents=[];let{indentTokenType:a,dedentTokenType:o}=this.indentationTokenBuilder,l=a.tokenTypeIdx,u=o.tokenTypeIdx,c=[],f=n.tokens.length-1;for(let d=0;d<f;d++){let p=n.tokens[d],m=n.tokens[d+1];if(p.tokenTypeIdx===l&&m.tokenTypeIdx===u){d++;continue}c.push(p)}return f>=0&&c.push(n.tokens[f]),n.tokens=c,n}};var W={};Or(W,{AstUtils:()=>qa,BiMap:()=>nn,Cancellation:()=>w,ContextCache:()=>sn,CstUtils:()=>Ga,DONE_RESULT:()=>Me,Deferred:()=>Xe,Disposable:()=>_r,DisposableCache:()=>ts,DocumentCache:()=>Cl,EMPTY_STREAM:()=>ss,ErrorWithLocation:()=>Dr,GrammarUtils:()=>Qa,MultiMap:()=>At,OperationCancelled:()=>Ut,Reduction:()=>fn,RegExpUtils:()=>Ja,SimpleCache:()=>ua,StreamImpl:()=>it,TreeStreamImpl:()=>Et,URI:()=>Ye,UriUtils:()=>Je,WorkspaceCache:()=>rs,assertUnreachable:()=>It,delayNextTick:()=>ef,interruptAndCheck:()=>Te,isOperationCancelled:()=>Bt,loadGrammarFromJson:()=>ar,setInterruptionPeriod:()=>Cy,startCancelableOperation:()=>Sl,stream:()=>H});B(W,ae);var Ml=class{static{s(this,"EmptyFileSystemProvider")}readFile(){throw new Error("No file system is available.")}async readDirectory(){return[]}},Tf={fileSystemProvider:s(()=>new Ml,"fileSystemProvider")};var t_={Grammar:s(()=>{},"Grammar"),LanguageMetaData:s(()=>({caseInsensitive:!1,fileExtensions:[".langium"],languageId:"langium"}),"LanguageMetaData")},r_={AstReflection:s(()=>new Jn,"AstReflection")};function n_(){let t=Ll(hf(Tf),r_),e=Ll(mf({shared:t}),t_);return t.ServiceRegistry.register(e),e}s(n_,"createMinimalGrammarServices");function ar(t){var e;let r=n_(),n=r.serializer.JsonSerializer.deserialize(t);return r.shared.workspace.LangiumDocumentFactory.fromModel(n,Ye.parse(`memory://${(e=n.name)!==null&&e!==void 0?e:"grammar"}.langium`)),n}s(ar,"loadGrammarFromJson");B(Ae,W);var zy="Statement";var Bl="Architecture";function B9(t){return Rt.isInstance(t,Bl)}s(B9,"isArchitecture");var Dl="Axis";var wa="Branch";function j9(t){return Rt.isInstance(t,wa)}s(j9,"isBranch");var Fl="Checkout";var Gl="CherryPicking";var ba="Commit";function W9(t){return Rt.isInstance(t,ba)}s(W9,"isCommit");var jl="Common";function K9(t){return Rt.isInstance(t,jl)}s(K9,"isCommon");var Af="Curve";var Rf="Edge";var Ef="Entry";var Oa="GitGraph";function H9(t){return Rt.isInstance(t,Oa)}s(H9,"isGitGraph");var vf="Group";var Wl="Info";function V9(t){return Rt.isInstance(t,Wl)}s(V9,"isInfo");var If="Junction";var $a="Merge";function z9(t){return Rt.isInstance(t,$a)}s(z9,"isMerge");var Sf="Option";var Kl="Packet";function q9(t){return Rt.isInstance(t,Kl)}s(q9,"isPacket");var Hl="PacketBlock";function X9(t){return Rt.isInstance(t,Hl)}s(X9,"isPacketBlock");var Vl="Pie";function Y9(t){return Rt.isInstance(t,Vl)}s(Y9,"isPie");var zl="PieSection";function J9(t){return Rt.isInstance(t,zl)}s(J9,"isPieSection");var kf="Radar";var Cf="Service";var Ul="Direction";var La=class extends $r{static{s(this,"MermaidAstReflection")}getAllTypes(){return[Bl,Dl,wa,Fl,Gl,ba,jl,Af,Ul,Rf,Ef,Oa,vf,Wl,If,$a,Sf,Kl,Hl,Vl,zl,kf,Cf,zy]}computeIsSubtype(e,r){switch(e){case wa:case Fl:case Gl:case ba:case $a:return this.isSubtype(zy,r);case Ul:return this.isSubtype(Oa,r);default:return!1}}getReferenceType(e){let r=`${e.container.$type}:${e.property}`;switch(r){case"Entry:axis":return Dl;default:throw new Error(`${r} is not a valid reference id.`)}}getTypeMetaData(e){switch(e){case Bl:return{name:Bl,properties:[{name:"accDescr"},{name:"accTitle"},{name:"edges",defaultValue:[]},{name:"groups",defaultValue:[]},{name:"junctions",defaultValue:[]},{name:"services",defaultValue:[]},{name:"title"}]};case Dl:return{name:Dl,properties:[{name:"label"},{name:"name"}]};case wa:return{name:wa,properties:[{name:"name"},{name:"order"}]};case Fl:return{name:Fl,properties:[{name:"branch"}]};case Gl:return{name:Gl,properties:[{name:"id"},{name:"parent"},{name:"tags",defaultValue:[]}]};case ba:return{name:ba,properties:[{name:"id"},{name:"message"},{name:"tags",defaultValue:[]},{name:"type"}]};case jl:return{name:jl,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case Af:return{name:Af,properties:[{name:"entries",defaultValue:[]},{name:"label"},{name:"name"}]};case Rf:return{name:Rf,properties:[{name:"lhsDir"},{name:"lhsGroup",defaultValue:!1},{name:"lhsId"},{name:"lhsInto",defaultValue:!1},{name:"rhsDir"},{name:"rhsGroup",defaultValue:!1},{name:"rhsId"},{name:"rhsInto",defaultValue:!1},{name:"title"}]};case Ef:return{name:Ef,properties:[{name:"axis"},{name:"value"}]};case Oa:return{name:Oa,properties:[{name:"accDescr"},{name:"accTitle"},{name:"statements",defaultValue:[]},{name:"title"}]};case vf:return{name:vf,properties:[{name:"icon"},{name:"id"},{name:"in"},{name:"title"}]};case Wl:return{name:Wl,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case If:return{name:If,properties:[{name:"id"},{name:"in"}]};case $a:return{name:$a,properties:[{name:"branch"},{name:"id"},{name:"tags",defaultValue:[]},{name:"type"}]};case Sf:return{name:Sf,properties:[{name:"name"},{name:"value",defaultValue:!1}]};case Kl:return{name:Kl,properties:[{name:"accDescr"},{name:"accTitle"},{name:"blocks",defaultValue:[]},{name:"title"}]};case Hl:return{name:Hl,properties:[{name:"end"},{name:"label"},{name:"start"}]};case Vl:return{name:Vl,properties:[{name:"accDescr"},{name:"accTitle"},{name:"sections",defaultValue:[]},{name:"showData",defaultValue:!1},{name:"title"}]};case zl:return{name:zl,properties:[{name:"label"},{name:"value"}]};case kf:return{name:kf,properties:[{name:"accDescr"},{name:"accTitle"},{name:"axes",defaultValue:[]},{name:"curves",defaultValue:[]},{name:"options",defaultValue:[]},{name:"title"}]};case Cf:return{name:Cf,properties:[{name:"icon"},{name:"iconText"},{name:"id"},{name:"in"},{name:"title"}]};case Ul:return{name:Ul,properties:[{name:"accDescr"},{name:"accTitle"},{name:"dir"},{name:"statements",defaultValue:[]},{name:"title"}]};default:return{name:e,properties:[]}}}},Rt=new La;var qy,ex=s(()=>qy??(qy=ar('{"$type":"Grammar","isDeclared":true,"name":"Info","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Info","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"info"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"Keyword","value":"showInfo"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}')),"InfoGrammar"),Xy,tx=s(()=>Xy??(Xy=ar(`{"$type":"Grammar","isDeclared":true,"name":"Packet","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Packet","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"packet-beta"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"+"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PacketBlock","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"start","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"end","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}}],"cardinality":"?"},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}`)),"PacketGrammar"),Yy,rx=s(()=>Yy??(Yy=ar('{"$type":"Grammar","isDeclared":true,"name":"Pie","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Pie","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"pie"},{"$type":"Assignment","feature":"showData","operator":"?=","terminal":{"$type":"Keyword","value":"showData"},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"+"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PieSection","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"PIE_SECTION_LABEL","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]+\\"/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"PIE_SECTION_VALUE","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/(0|[1-9][0-9]*)(\\\\.[0-9]+)?/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}')),"PieGrammar"),Jy,nx=s(()=>Jy??(Jy=ar('{"$type":"Grammar","isDeclared":true,"name":"Architecture","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Architecture","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"architecture-beta"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"*"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"groups","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"services","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"junctions","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"edges","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"LeftPort","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"lhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"RightPort","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"rhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Keyword","value":":"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Arrow","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]},{"$type":"Assignment","feature":"lhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"--"},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}},{"$type":"Keyword","value":"-"}]}]},{"$type":"Assignment","feature":"rhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Group","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"group"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]},"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Service","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"service"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"iconText","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}}],"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Junction","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"junction"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Edge","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"lhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"lhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"rhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"rhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"ARROW_DIRECTION","definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"L"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"R"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"T"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"B"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_ID","definition":{"$type":"RegexToken","regex":"/[\\\\w]+/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TEXT_ICON","definition":{"$type":"RegexToken","regex":"/\\\\(\\"[^\\"]+\\"\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_ICON","definition":{"$type":"RegexToken","regex":"/\\\\([\\\\w-:]+\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TITLE","definition":{"$type":"RegexToken","regex":"/\\\\[[\\\\w ]+\\\\]/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_GROUP","definition":{"$type":"RegexToken","regex":"/\\\\{group\\\\}/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_INTO","definition":{"$type":"RegexToken","regex":"/<|>/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}')),"ArchitectureGrammar"),Zy,ix=s(()=>Zy??(Zy=ar(`{"$type":"Grammar","isDeclared":true,"name":"GitGraph","interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"rules":[{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"ParserRule","entry":true,"name":"GitGraph","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Keyword","value":":"}]},{"$type":"Keyword","value":"gitGraph:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]},{"$type":"Keyword","value":":"}]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@0"},"arguments":[]},{"$type":"Assignment","feature":"statements","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Direction","definition":{"$type":"Assignment","feature":"dir","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"LR"},{"$type":"Keyword","value":"TB"},{"$type":"Keyword","value":"BT"}]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Commit","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"commit"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"msg:","cardinality":"?"},{"$type":"Assignment","feature":"message","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Branch","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"branch"},{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"order:"},{"$type":"Assignment","feature":"order","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Merge","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"merge"},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Checkout","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"checkout"},{"$type":"Keyword","value":"switch"}]},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"CherryPicking","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"cherry-pick"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"parent:"},{"$type":"Assignment","feature":"parent","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+(?=\\\\s)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\\\w([-\\\\./\\\\w]*[-\\\\w])?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"imports":[],"types":[],"usedGrammars":[]}`)),"GitGraphGrammar"),Qy,sx=s(()=>Qy??(Qy=ar(`{"$type":"Grammar","isDeclared":true,"name":"Radar","interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]},{"$type":"Interface","name":"Entry","attributes":[{"$type":"TypeAttribute","name":"axis","isOptional":true,"type":{"$type":"ReferenceType","referenceType":{"$type":"SimpleType","typeRef":{"$ref":"#/rules@12"}}}},{"$type":"TypeAttribute","name":"value","type":{"$type":"SimpleType","primitiveType":"number"},"isOptional":false}],"superTypes":[]}],"rules":[{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"ParserRule","entry":true,"name":"Radar","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":"radar-beta:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":":"}]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@0"},"arguments":[]},{"$type":"Group","elements":[{"$type":"Keyword","value":"axis"},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"curve"},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Label","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"["},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Keyword","value":"]"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Axis","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Curve","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[],"cardinality":"?"},{"$type":"Keyword","value":"{"},{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},{"$type":"Keyword","value":"}"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Entries","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"DetailedEntry","returnType":{"$ref":"#/interfaces@1"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"axis","operator":"=","terminal":{"$type":"CrossReference","type":{"$ref":"#/rules@12"},"terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]},"deprecatedSyntax":false}},{"$type":"Keyword","value":":","cardinality":"?"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"NumberEntry","returnType":{"$ref":"#/interfaces@1"},"definition":{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Option","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"showLegend"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"ticks"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"max"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"min"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"graticule"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/(0|[1-9][0-9]*)(\\\\.[0-9]+)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"GRATICULE","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"circle"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"polygon"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[a-zA-Z_][a-zA-Z0-9\\\\-_]*/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"imports":[],"types":[],"usedGrammars":[]}`)),"RadarGrammar");var i_={languageId:"info",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},s_={languageId:"packet",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},a_={languageId:"pie",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},o_={languageId:"architecture",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},l_={languageId:"gitGraph",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},u_={languageId:"radar",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},s8={AstReflection:s(()=>new La,"AstReflection")},a8={Grammar:s(()=>ex(),"Grammar"),LanguageMetaData:s(()=>i_,"LanguageMetaData"),parser:{}},o8={Grammar:s(()=>tx(),"Grammar"),LanguageMetaData:s(()=>s_,"LanguageMetaData"),parser:{}},l8={Grammar:s(()=>rx(),"Grammar"),LanguageMetaData:s(()=>a_,"LanguageMetaData"),parser:{}},u8={Grammar:s(()=>nx(),"Grammar"),LanguageMetaData:s(()=>o_,"LanguageMetaData"),parser:{}},c8={Grammar:s(()=>ix(),"Grammar"),LanguageMetaData:s(()=>l_,"LanguageMetaData"),parser:{}},f8={Grammar:s(()=>sx(),"Grammar"),LanguageMetaData:s(()=>u_,"LanguageMetaData"),parser:{}};var ax=/accDescr(?:[\t ]*:([^\n\r]*)|\s*{([^}]*)})/,ox=/accTitle[\t ]*:([^\n\r]*)/,lx=/title([\t ][^\n\r]*|)/;var c_={ACC_DESCR:ax,ACC_TITLE:ox,TITLE:lx},Nf=class extends rn{static{s(this,"AbstractMermaidValueConverter")}runConverter(e,r,n){let i=this.runCommonConverter(e,r,n);return i===void 0&&(i=this.runCustomConverter(e,r,n)),i===void 0?super.runConverter(e,r,n):i}runCommonConverter(e,r,n){let i=c_[e.name];if(i===void 0)return;let a=i.exec(r);if(a!==null){if(a[1]!==void 0)return a[1].trim().replace(/[\t ]{2,}/gm," ");if(a[2]!==void 0)return a[2].replace(/^\s*/gm,"").replace(/\s+$/gm,"").replace(/[\t ]{2,}/gm," ").replace(/[\n\r]{2,}/gm,`
`)}}},ux=class extends Nf{static{s(this,"CommonValueConverter")}runCustomConverter(e,r,n){}};var _f=class extends sr{static{s(this,"AbstractMermaidTokenBuilder")}constructor(e){super(),this.keywords=new Set(e)}buildKeywordTokens(e,r,n){let i=super.buildKeywordTokens(e,r,n);return i.forEach(a=>{this.keywords.has(a.name)&&a.PATTERN!==void 0&&(a.PATTERN=new RegExp(a.PATTERN.toString()+"(?:(?=%%)|(?!\\S))"))}),i}},cx=class extends _f{static{s(this,"CommonTokenBuilder")}};export{s as a,mf as b,hf as c,Ll as d,Tf as e,Ae as f,zy as g,Bl as h,B9 as i,wa as j,j9 as k,ba as l,W9 as m,K9 as n,Oa as o,H9 as p,Wl as q,V9 as r,$a as s,z9 as t,Kl as u,q9 as v,Hl as w,X9 as x,Vl as y,Y9 as z,zl as A,J9 as B,kf as C,s8 as D,a8 as E,o8 as F,l8 as G,u8 as H,c8 as I,f8 as J,Nf as K,ux as L,_f as M,cx as N};
/*! Bundled license information:

lodash-es/lodash.js:
  (**
   * @license
   * Lodash (Custom Build) <https://lodash.com/>
   * Build: `lodash modularize exports="es" -o ./`
   * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
   * Released under MIT license <https://lodash.com/license>
   * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
   * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
   *)
*/
