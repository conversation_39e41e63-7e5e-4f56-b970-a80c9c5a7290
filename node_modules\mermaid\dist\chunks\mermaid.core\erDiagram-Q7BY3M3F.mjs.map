{"version": 3, "sources": ["../../../src/diagrams/er/parser/erDiagram.jison", "../../../src/diagrams/er/erDb.ts", "../../../src/diagrams/er/erRenderer-unified.ts", "../../../src/diagrams/er/styles.ts", "../../../src/diagrams/er/erDiagram.ts"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[6,8,10,22,24,26,28,33,34,35,36,37,40,43,44,50],$V1=[1,10],$V2=[1,11],$V3=[1,12],$V4=[1,13],$V5=[1,20],$V6=[1,21],$V7=[1,22],$V8=[1,23],$V9=[1,24],$Va=[1,19],$Vb=[1,25],$Vc=[1,26],$Vd=[1,18],$Ve=[1,33],$Vf=[1,34],$Vg=[1,35],$Vh=[1,36],$Vi=[1,37],$Vj=[6,8,10,13,15,17,20,21,22,24,26,28,33,34,35,36,37,40,43,44,50,63,64,65,66,67],$Vk=[1,42],$Vl=[1,43],$Vm=[1,52],$Vn=[40,50,68,69],$Vo=[1,63],$Vp=[1,61],$Vq=[1,58],$Vr=[1,62],$Vs=[1,64],$Vt=[6,8,10,13,17,22,24,26,28,33,34,35,36,37,40,41,42,43,44,48,49,50,63,64,65,66,67],$Vu=[63,64,65,66,67],$Vv=[1,81],$Vw=[1,80],$Vx=[1,78],$Vy=[1,79],$Vz=[6,10,42,47],$VA=[6,10,13,41,42,47,48,49],$VB=[1,89],$VC=[1,88],$VD=[1,87],$VE=[19,56],$VF=[1,98],$VG=[1,97],$VH=[19,56,58,60];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"start\":3,\"ER_DIAGRAM\":4,\"document\":5,\"EOF\":6,\"line\":7,\"SPACE\":8,\"statement\":9,\"NEWLINE\":10,\"entityName\":11,\"relSpec\":12,\"COLON\":13,\"role\":14,\"STYLE_SEPARATOR\":15,\"idList\":16,\"BLOCK_START\":17,\"attributes\":18,\"BLOCK_STOP\":19,\"SQS\":20,\"SQE\":21,\"title\":22,\"title_value\":23,\"acc_title\":24,\"acc_title_value\":25,\"acc_descr\":26,\"acc_descr_value\":27,\"acc_descr_multiline_value\":28,\"direction\":29,\"classDefStatement\":30,\"classStatement\":31,\"styleStatement\":32,\"direction_tb\":33,\"direction_bt\":34,\"direction_rl\":35,\"direction_lr\":36,\"CLASSDEF\":37,\"stylesOpt\":38,\"separator\":39,\"UNICODE_TEXT\":40,\"STYLE_TEXT\":41,\"COMMA\":42,\"CLASS\":43,\"STYLE\":44,\"style\":45,\"styleComponent\":46,\"SEMI\":47,\"NUM\":48,\"BRKT\":49,\"ENTITY_NAME\":50,\"attribute\":51,\"attributeType\":52,\"attributeName\":53,\"attributeKeyTypeList\":54,\"attributeComment\":55,\"ATTRIBUTE_WORD\":56,\"attributeKeyType\":57,\",\":58,\"ATTRIBUTE_KEY\":59,\"COMMENT\":60,\"cardinality\":61,\"relType\":62,\"ZERO_OR_ONE\":63,\"ZERO_OR_MORE\":64,\"ONE_OR_MORE\":65,\"ONLY_ONE\":66,\"MD_PARENT\":67,\"NON_IDENTIFYING\":68,\"IDENTIFYING\":69,\"WORD\":70,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",4:\"ER_DIAGRAM\",6:\"EOF\",8:\"SPACE\",10:\"NEWLINE\",13:\"COLON\",15:\"STYLE_SEPARATOR\",17:\"BLOCK_START\",19:\"BLOCK_STOP\",20:\"SQS\",21:\"SQE\",22:\"title\",23:\"title_value\",24:\"acc_title\",25:\"acc_title_value\",26:\"acc_descr\",27:\"acc_descr_value\",28:\"acc_descr_multiline_value\",33:\"direction_tb\",34:\"direction_bt\",35:\"direction_rl\",36:\"direction_lr\",37:\"CLASSDEF\",40:\"UNICODE_TEXT\",41:\"STYLE_TEXT\",42:\"COMMA\",43:\"CLASS\",44:\"STYLE\",47:\"SEMI\",48:\"NUM\",49:\"BRKT\",50:\"ENTITY_NAME\",56:\"ATTRIBUTE_WORD\",58:\",\",59:\"ATTRIBUTE_KEY\",60:\"COMMENT\",63:\"ZERO_OR_ONE\",64:\"ZERO_OR_MORE\",65:\"ONE_OR_MORE\",66:\"ONLY_ONE\",67:\"MD_PARENT\",68:\"NON_IDENTIFYING\",69:\"IDENTIFYING\",70:\"WORD\"},\nproductions_: [0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[9,5],[9,9],[9,7],[9,7],[9,4],[9,6],[9,3],[9,5],[9,1],[9,3],[9,7],[9,9],[9,6],[9,8],[9,4],[9,6],[9,2],[9,2],[9,2],[9,1],[9,1],[9,1],[9,1],[9,1],[29,1],[29,1],[29,1],[29,1],[30,4],[16,1],[16,1],[16,3],[16,3],[31,3],[32,4],[38,1],[38,3],[45,1],[45,2],[39,1],[39,1],[39,1],[46,1],[46,1],[46,1],[46,1],[11,1],[11,1],[18,1],[18,2],[51,2],[51,3],[51,3],[51,4],[52,1],[53,1],[54,1],[54,3],[57,1],[55,1],[12,3],[61,1],[61,1],[61,1],[61,1],[61,1],[62,1],[62,1],[14,1],[14,1],[14,1]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 1:\n /*console.log('finished parsing');*/ \nbreak;\ncase 2:\n this.$ = [] \nbreak;\ncase 3:\n$$[$0-1].push($$[$0]);this.$ = $$[$0-1]\nbreak;\ncase 4: case 5:\n this.$ = $$[$0] \nbreak;\ncase 6: case 7:\n this.$=[];\nbreak;\ncase 8:\n\n          yy.addEntity($$[$0-4]);\n          yy.addEntity($$[$0-2]);\n          yy.addRelationship($$[$0-4], $$[$0], $$[$0-2], $$[$0-3]);\n      \nbreak;\ncase 9:\n\n          yy.addEntity($$[$0-8]);\n          yy.addEntity($$[$0-4]);\n          yy.addRelationship($$[$0-8], $$[$0], $$[$0-4], $$[$0-5]);\n          yy.setClass([$$[$0-8]], $$[$0-6]);\n          yy.setClass([$$[$0-4]], $$[$0-2]);\n      \nbreak;\ncase 10:\n\n          yy.addEntity($$[$0-6]);\n          yy.addEntity($$[$0-2]);\n          yy.addRelationship($$[$0-6], $$[$0], $$[$0-2], $$[$0-3]);\n          yy.setClass([$$[$0-6]], $$[$0-4]);\n      \nbreak;\ncase 11:\n\n          yy.addEntity($$[$0-6]);\n          yy.addEntity($$[$0-4]);\n          yy.addRelationship($$[$0-6], $$[$0], $$[$0-4], $$[$0-5]);\n          yy.setClass([$$[$0-4]], $$[$0-2]);\n      \nbreak;\ncase 12:\n\n          yy.addEntity($$[$0-3]);\n          yy.addAttributes($$[$0-3], $$[$0-1]);\n      \nbreak;\ncase 13:\n\n          yy.addEntity($$[$0-5]);\n          yy.addAttributes($$[$0-5], $$[$0-1]);\n          yy.setClass([$$[$0-5]], $$[$0-3]);\n      \nbreak;\ncase 14:\n yy.addEntity($$[$0-2]); \nbreak;\ncase 15:\n yy.addEntity($$[$0-4]); yy.setClass([$$[$0-4]], $$[$0-2]); \nbreak;\ncase 16:\n yy.addEntity($$[$0]); \nbreak;\ncase 17:\n yy.addEntity($$[$0-2]); yy.setClass([$$[$0-2]], $$[$0]); \nbreak;\ncase 18:\n\n          yy.addEntity($$[$0-6], $$[$0-4]);\n          yy.addAttributes($$[$0-6], $$[$0-1]);\n      \nbreak;\ncase 19:\n\n          yy.addEntity($$[$0-8], $$[$0-6]);\n          yy.addAttributes($$[$0-8], $$[$0-1]);\n          yy.setClass([$$[$0-8]], $$[$0-3]);\n\n      \nbreak;\ncase 20:\n yy.addEntity($$[$0-5], $$[$0-3]); \nbreak;\ncase 21:\n yy.addEntity($$[$0-7], $$[$0-5]); yy.setClass([$$[$0-7]], $$[$0-2]); \nbreak;\ncase 22:\n yy.addEntity($$[$0-3], $$[$0-1]); \nbreak;\ncase 23:\n yy.addEntity($$[$0-5], $$[$0-3]); yy.setClass([$$[$0-5]], $$[$0]); \nbreak;\ncase 24: case 25:\n this.$=$$[$0].trim();yy.setAccTitle(this.$); \nbreak;\ncase 26: case 27:\n this.$=$$[$0].trim();yy.setAccDescription(this.$); \nbreak;\ncase 32:\n yy.setDirection('TB');\nbreak;\ncase 33:\n yy.setDirection('BT');\nbreak;\ncase 34:\n yy.setDirection('RL');\nbreak;\ncase 35:\n yy.setDirection('LR');\nbreak;\ncase 36:\nthis.$ = $$[$0-3];yy.addClass($$[$0-2],$$[$0-1]);\nbreak;\ncase 37: case 38: case 56: case 64:\n this.$ = [$$[$0]]; \nbreak;\ncase 39: case 40:\n this.$ = $$[$0-2].concat([$$[$0]]); \nbreak;\ncase 41:\nthis.$ = $$[$0-2];yy.setClass($$[$0-1], $$[$0]);\nbreak;\ncase 42:\n;this.$ = $$[$0-3];yy.addCssStyles($$[$0-2],$$[$0-1]);\nbreak;\ncase 43:\n this.$ = [$$[$0]] \nbreak;\ncase 44:\n$$[$0-2].push($$[$0]);this.$ = $$[$0-2];\nbreak;\ncase 46:\n this.$ = $$[$0-1] + $$[$0]; \nbreak;\ncase 54: case 76: case 77:\n this.$ = $$[$0].replace(/\"/g, ''); \nbreak;\ncase 55: case 78:\n this.$ = $$[$0]; \nbreak;\ncase 57:\n $$[$0].push($$[$0-1]); this.$=$$[$0]; \nbreak;\ncase 58:\n this.$ = { type: $$[$0-1], name: $$[$0] }; \nbreak;\ncase 59:\n this.$ = { type: $$[$0-2], name: $$[$0-1], keys: $$[$0] }; \nbreak;\ncase 60:\n this.$ = { type: $$[$0-2], name: $$[$0-1], comment: $$[$0] }; \nbreak;\ncase 61:\n this.$ = { type: $$[$0-3], name: $$[$0-2], keys: $$[$0-1], comment: $$[$0] }; \nbreak;\ncase 62: case 63: case 66:\n this.$=$$[$0]; \nbreak;\ncase 65:\n $$[$0-2].push($$[$0]); this.$ = $$[$0-2]; \nbreak;\ncase 67:\n this.$=$$[$0].replace(/\"/g, ''); \nbreak;\ncase 68:\n\n        this.$ = { cardA: $$[$0], relType: $$[$0-1], cardB: $$[$0-2] };\n        /*console.log('relSpec: ' + $$[$0] + $$[$0-1] + $$[$0-2]);*/\n      \nbreak;\ncase 69:\n this.$ = yy.Cardinality.ZERO_OR_ONE; \nbreak;\ncase 70:\n this.$ = yy.Cardinality.ZERO_OR_MORE; \nbreak;\ncase 71:\n this.$ = yy.Cardinality.ONE_OR_MORE; \nbreak;\ncase 72:\n this.$ = yy.Cardinality.ONLY_ONE; \nbreak;\ncase 73:\n this.$ = yy.Cardinality.MD_PARENT; \nbreak;\ncase 74:\n this.$ = yy.Identification.NON_IDENTIFYING;  \nbreak;\ncase 75:\n this.$ = yy.Identification.IDENTIFYING; \nbreak;\n}\n},\ntable: [{3:1,4:[1,2]},{1:[3]},o($V0,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:9,22:$V1,24:$V2,26:$V3,28:$V4,29:14,30:15,31:16,32:17,33:$V5,34:$V6,35:$V7,36:$V8,37:$V9,40:$Va,43:$Vb,44:$Vc,50:$Vd},o($V0,[2,7],{1:[2,1]}),o($V0,[2,3]),{9:27,11:9,22:$V1,24:$V2,26:$V3,28:$V4,29:14,30:15,31:16,32:17,33:$V5,34:$V6,35:$V7,36:$V8,37:$V9,40:$Va,43:$Vb,44:$Vc,50:$Vd},o($V0,[2,5]),o($V0,[2,6]),o($V0,[2,16],{12:28,61:32,15:[1,29],17:[1,30],20:[1,31],63:$Ve,64:$Vf,65:$Vg,66:$Vh,67:$Vi}),{23:[1,38]},{25:[1,39]},{27:[1,40]},o($V0,[2,27]),o($V0,[2,28]),o($V0,[2,29]),o($V0,[2,30]),o($V0,[2,31]),o($Vj,[2,54]),o($Vj,[2,55]),o($V0,[2,32]),o($V0,[2,33]),o($V0,[2,34]),o($V0,[2,35]),{16:41,40:$Vk,41:$Vl},{16:44,40:$Vk,41:$Vl},{16:45,40:$Vk,41:$Vl},o($V0,[2,4]),{11:46,40:$Va,50:$Vd},{16:47,40:$Vk,41:$Vl},{18:48,19:[1,49],51:50,52:51,56:$Vm},{11:53,40:$Va,50:$Vd},{62:54,68:[1,55],69:[1,56]},o($Vn,[2,69]),o($Vn,[2,70]),o($Vn,[2,71]),o($Vn,[2,72]),o($Vn,[2,73]),o($V0,[2,24]),o($V0,[2,25]),o($V0,[2,26]),{13:$Vo,38:57,41:$Vp,42:$Vq,45:59,46:60,48:$Vr,49:$Vs},o($Vt,[2,37]),o($Vt,[2,38]),{16:65,40:$Vk,41:$Vl,42:$Vq},{13:$Vo,38:66,41:$Vp,42:$Vq,45:59,46:60,48:$Vr,49:$Vs},{13:[1,67],15:[1,68]},o($V0,[2,17],{61:32,12:69,17:[1,70],42:$Vq,63:$Ve,64:$Vf,65:$Vg,66:$Vh,67:$Vi}),{19:[1,71]},o($V0,[2,14]),{18:72,19:[2,56],51:50,52:51,56:$Vm},{53:73,56:[1,74]},{56:[2,62]},{21:[1,75]},{61:76,63:$Ve,64:$Vf,65:$Vg,66:$Vh,67:$Vi},o($Vu,[2,74]),o($Vu,[2,75]),{6:$Vv,10:$Vw,39:77,42:$Vx,47:$Vy},{40:[1,82],41:[1,83]},o($Vz,[2,43],{46:84,13:$Vo,41:$Vp,48:$Vr,49:$Vs}),o($VA,[2,45]),o($VA,[2,50]),o($VA,[2,51]),o($VA,[2,52]),o($VA,[2,53]),o($V0,[2,41],{42:$Vq}),{6:$Vv,10:$Vw,39:85,42:$Vx,47:$Vy},{14:86,40:$VB,50:$VC,70:$VD},{16:90,40:$Vk,41:$Vl},{11:91,40:$Va,50:$Vd},{18:92,19:[1,93],51:50,52:51,56:$Vm},o($V0,[2,12]),{19:[2,57]},o($VE,[2,58],{54:94,55:95,57:96,59:$VF,60:$VG}),o([19,56,59,60],[2,63]),o($V0,[2,22],{15:[1,100],17:[1,99]}),o([40,50],[2,68]),o($V0,[2,36]),{13:$Vo,41:$Vp,45:101,46:60,48:$Vr,49:$Vs},o($V0,[2,47]),o($V0,[2,48]),o($V0,[2,49]),o($Vt,[2,39]),o($Vt,[2,40]),o($VA,[2,46]),o($V0,[2,42]),o($V0,[2,8]),o($V0,[2,76]),o($V0,[2,77]),o($V0,[2,78]),{13:[1,102],42:$Vq},{13:[1,104],15:[1,103]},{19:[1,105]},o($V0,[2,15]),o($VE,[2,59],{55:106,58:[1,107],60:$VG}),o($VE,[2,60]),o($VH,[2,64]),o($VE,[2,67]),o($VH,[2,66]),{18:108,19:[1,109],51:50,52:51,56:$Vm},{16:110,40:$Vk,41:$Vl},o($Vz,[2,44],{46:84,13:$Vo,41:$Vp,48:$Vr,49:$Vs}),{14:111,40:$VB,50:$VC,70:$VD},{16:112,40:$Vk,41:$Vl},{14:113,40:$VB,50:$VC,70:$VD},o($V0,[2,13]),o($VE,[2,61]),{57:114,59:$VF},{19:[1,115]},o($V0,[2,20]),o($V0,[2,23],{17:[1,116],42:$Vq}),o($V0,[2,11]),{13:[1,117],42:$Vq},o($V0,[2,10]),o($VH,[2,65]),o($V0,[2,18]),{18:118,19:[1,119],51:50,52:51,56:$Vm},{14:120,40:$VB,50:$VC,70:$VD},{19:[1,121]},o($V0,[2,21]),o($V0,[2,9]),o($V0,[2,19])],\ndefaultActions: {52:[2,62],72:[2,57]},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {\"case-insensitive\":true},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0: this.begin(\"acc_title\");return 24; \nbreak;\ncase 1: this.popState(); return \"acc_title_value\"; \nbreak;\ncase 2: this.begin(\"acc_descr\");return 26; \nbreak;\ncase 3: this.popState(); return \"acc_descr_value\"; \nbreak;\ncase 4: this.begin(\"acc_descr_multiline\");\nbreak;\ncase 5: this.popState(); \nbreak;\ncase 6:return \"acc_descr_multiline_value\";\nbreak;\ncase 7:return 33;\nbreak;\ncase 8:return 34;\nbreak;\ncase 9:return 35;\nbreak;\ncase 10:return 36;\nbreak;\ncase 11:return 10;\nbreak;\ncase 12:/* skip whitespace */\nbreak;\ncase 13:return 8;\nbreak;\ncase 14:return 50;\nbreak;\ncase 15:return 70;\nbreak;\ncase 16:return 4;\nbreak;\ncase 17: this.begin(\"block\"); return 17; \nbreak;\ncase 18:return 49;\nbreak;\ncase 19:return 49;\nbreak;\ncase 20:return 42;\nbreak;\ncase 21:return 15;\nbreak;\ncase 22:return 13;\nbreak;\ncase 23:/* skip whitespace in block */\nbreak;\ncase 24:return 59\nbreak;\ncase 25:return 56;\nbreak;\ncase 26:return 56;\nbreak;\ncase 27:return 60;\nbreak;\ncase 28:/* nothing */\nbreak;\ncase 29: this.popState(); return 19; \nbreak;\ncase 30:return yy_.yytext[0];\nbreak;\ncase 31:return 20;\nbreak;\ncase 32:return 21;\nbreak;\ncase 33: this.begin(\"style\"); return 44; \nbreak;\ncase 34: this.popState(); return 10; \nbreak;\ncase 35:/* skip whitespace in block */\nbreak;\ncase 36:return 13;\nbreak;\ncase 37:return 42;\nbreak;\ncase 38:return 49;\nbreak;\ncase 39: this.begin(\"style\"); return 37; \nbreak;\ncase 40:return 43;\nbreak;\ncase 41:return 63;\nbreak;\ncase 42:return 65;\nbreak;\ncase 43:return 65;\nbreak;\ncase 44:return 65;\nbreak;\ncase 45:return 63;\nbreak;\ncase 46:return 63;\nbreak;\ncase 47:return 64;\nbreak;\ncase 48:return 64;\nbreak;\ncase 49:return 64;\nbreak;\ncase 50:return 64;\nbreak;\ncase 51:return 64;\nbreak;\ncase 52:return 65;\nbreak;\ncase 53:return 64;\nbreak;\ncase 54:return 65;\nbreak;\ncase 55:return 66;\nbreak;\ncase 56:return 66;\nbreak;\ncase 57:return 66;\nbreak;\ncase 58:return 66;\nbreak;\ncase 59:return 63;\nbreak;\ncase 60:return 64;\nbreak;\ncase 61:return 65;\nbreak;\ncase 62:return 67;\nbreak;\ncase 63:return 68;\nbreak;\ncase 64:return 69;\nbreak;\ncase 65:return 69;\nbreak;\ncase 66:return 68;\nbreak;\ncase 67:return 68;\nbreak;\ncase 68:return 68;\nbreak;\ncase 69:return 41;\nbreak;\ncase 70:return 47;\nbreak;\ncase 71:return 40;\nbreak;\ncase 72:return 48;\nbreak;\ncase 73:return yy_.yytext[0];\nbreak;\ncase 74:return 6;\nbreak;\n}\n},\nrules: [/^(?:accTitle\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*\\{\\s*)/i,/^(?:[\\}])/i,/^(?:[^\\}]*)/i,/^(?:.*direction\\s+TB[^\\n]*)/i,/^(?:.*direction\\s+BT[^\\n]*)/i,/^(?:.*direction\\s+RL[^\\n]*)/i,/^(?:.*direction\\s+LR[^\\n]*)/i,/^(?:[\\n]+)/i,/^(?:\\s+)/i,/^(?:[\\s]+)/i,/^(?:\"[^\"%\\r\\n\\v\\b\\\\]+\")/i,/^(?:\"[^\"]*\")/i,/^(?:erDiagram\\b)/i,/^(?:\\{)/i,/^(?:#)/i,/^(?:#)/i,/^(?:,)/i,/^(?::::)/i,/^(?::)/i,/^(?:\\s+)/i,/^(?:\\b((?:PK)|(?:FK)|(?:UK))\\b)/i,/^(?:([^\\s]*)[~].*[~]([^\\s]*))/i,/^(?:([\\*A-Za-z_\\u00C0-\\uFFFF][A-Za-z0-9\\-\\_\\[\\]\\(\\)\\u00C0-\\uFFFF\\*]*))/i,/^(?:\"[^\"]*\")/i,/^(?:[\\n]+)/i,/^(?:\\})/i,/^(?:.)/i,/^(?:\\[)/i,/^(?:\\])/i,/^(?:style\\b)/i,/^(?:[\\n]+)/i,/^(?:\\s+)/i,/^(?::)/i,/^(?:,)/i,/^(?:#)/i,/^(?:classDef\\b)/i,/^(?:class\\b)/i,/^(?:one or zero\\b)/i,/^(?:one or more\\b)/i,/^(?:one or many\\b)/i,/^(?:1\\+)/i,/^(?:\\|o\\b)/i,/^(?:zero or one\\b)/i,/^(?:zero or more\\b)/i,/^(?:zero or many\\b)/i,/^(?:0\\+)/i,/^(?:\\}o\\b)/i,/^(?:many\\(0\\))/i,/^(?:many\\(1\\))/i,/^(?:many\\b)/i,/^(?:\\}\\|)/i,/^(?:one\\b)/i,/^(?:only one\\b)/i,/^(?:1\\b)/i,/^(?:\\|\\|)/i,/^(?:o\\|)/i,/^(?:o\\{)/i,/^(?:\\|\\{)/i,/^(?:\\s*u\\b)/i,/^(?:\\.\\.)/i,/^(?:--)/i,/^(?:to\\b)/i,/^(?:optionally to\\b)/i,/^(?:\\.-)/i,/^(?:-\\.)/i,/^(?:([^\\x00-\\x7F]|\\w|-|\\*)+)/i,/^(?:;)/i,/^(?:([^\\x00-\\x7F]|\\w|-|\\*)+)/i,/^(?:[0-9])/i,/^(?:.)/i,/^(?:$)/i],\nconditions: {\"style\":{\"rules\":[34,35,36,37,38,69,70],\"inclusive\":false},\"acc_descr_multiline\":{\"rules\":[5,6],\"inclusive\":false},\"acc_descr\":{\"rules\":[3],\"inclusive\":false},\"acc_title\":{\"rules\":[1],\"inclusive\":false},\"block\":{\"rules\":[23,24,25,26,27,28,29,30],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,2,4,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,31,32,33,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,71,72,73,74],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "import { log } from '../../logger.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport type { Edge, Node } from '../../rendering-util/types.js';\nimport type { EntityNode, Attribute, Relationship, EntityClass, RelSpec } from './erTypes.js';\nimport {\n  setAccTitle,\n  getAccTitle,\n  getAccDescription,\n  setAccDescription,\n  clear as commonClear,\n  setDiagramTitle,\n  getDiagramTitle,\n} from '../common/commonDb.js';\nimport { getEdgeId } from '../../utils.js';\nimport type { DiagramDB } from '../../diagram-api/types.js';\n\nexport class ErDB implements DiagramDB {\n  private entities = new Map<string, EntityNode>();\n  private relationships: Relationship[] = [];\n  private classes = new Map<string, EntityClass>();\n  private direction = 'TB';\n\n  private Cardinality = {\n    ZERO_OR_ONE: 'ZERO_OR_ONE',\n    ZERO_OR_MORE: 'ZERO_OR_MORE',\n    ONE_OR_MORE: 'ONE_OR_MORE',\n    ONLY_ONE: 'ONLY_ONE',\n    MD_PARENT: 'MD_PARENT',\n  };\n\n  private Identification = {\n    NON_IDENTIFYING: 'NON_IDENTIFYING',\n    IDENTIFYING: 'IDENTIFYING',\n  };\n\n  constructor() {\n    this.clear();\n    this.addEntity = this.addEntity.bind(this);\n    this.addAttributes = this.addAttributes.bind(this);\n    this.addRelationship = this.addRelationship.bind(this);\n    this.setDirection = this.setDirection.bind(this);\n    this.addCssStyles = this.addCssStyles.bind(this);\n    this.addClass = this.addClass.bind(this);\n    this.setClass = this.setClass.bind(this);\n    this.setAccTitle = this.setAccTitle.bind(this);\n    this.setAccDescription = this.setAccDescription.bind(this);\n  }\n\n  /**\n   * Add entity\n   * @param name - The name of the entity\n   * @param alias - The alias of the entity\n   */\n  public addEntity(name: string, alias = ''): EntityNode {\n    if (!this.entities.has(name)) {\n      this.entities.set(name, {\n        id: `entity-${name}-${this.entities.size}`,\n        label: name,\n        attributes: [],\n        alias,\n        shape: 'erBox',\n        look: getConfig().look ?? 'default',\n        cssClasses: 'default',\n        cssStyles: [],\n      });\n      log.info('Added new entity :', name);\n    } else if (!this.entities.get(name)?.alias && alias) {\n      this.entities.get(name)!.alias = alias;\n      log.info(`Add alias '${alias}' to entity '${name}'`);\n    }\n\n    return this.entities.get(name)!;\n  }\n\n  public getEntity(name: string) {\n    return this.entities.get(name);\n  }\n\n  public getEntities() {\n    return this.entities;\n  }\n\n  public getClasses() {\n    return this.classes;\n  }\n\n  public addAttributes(entityName: string, attribs: Attribute[]) {\n    const entity = this.addEntity(entityName); // May do nothing (if entity has already been added)\n\n    // Process attribs in reverse order due to effect of recursive construction (last attribute is first)\n    let i;\n    for (i = attribs.length - 1; i >= 0; i--) {\n      if (!attribs[i].keys) {\n        attribs[i].keys = [];\n      }\n      if (!attribs[i].comment) {\n        attribs[i].comment = '';\n      }\n      entity.attributes.push(attribs[i]);\n      log.debug('Added attribute ', attribs[i].name);\n    }\n  }\n\n  /**\n   * Add a relationship\n   *\n   * @param entA - The first entity in the relationship\n   * @param rolA - The role played by the first entity in relation to the second\n   * @param entB - The second entity in the relationship\n   * @param rSpec - The details of the relationship between the two entities\n   */\n  public addRelationship(entA: string, rolA: string, entB: string, rSpec: RelSpec) {\n    const entityA = this.entities.get(entA);\n    const entityB = this.entities.get(entB);\n    if (!entityA || !entityB) {\n      return;\n    }\n\n    const rel = {\n      entityA: entityA.id,\n      roleA: rolA,\n      entityB: entityB.id,\n      relSpec: rSpec,\n    };\n\n    this.relationships.push(rel);\n    log.debug('Added new relationship :', rel);\n  }\n\n  public getRelationships() {\n    return this.relationships;\n  }\n\n  public getDirection() {\n    return this.direction;\n  }\n\n  public setDirection(dir: string) {\n    this.direction = dir;\n  }\n\n  private getCompiledStyles(classDefs: string[]) {\n    let compiledStyles: string[] = [];\n    for (const customClass of classDefs) {\n      const cssClass = this.classes.get(customClass);\n      if (cssClass?.styles) {\n        compiledStyles = [...compiledStyles, ...(cssClass.styles ?? [])].map((s) => s.trim());\n      }\n      if (cssClass?.textStyles) {\n        compiledStyles = [...compiledStyles, ...(cssClass.textStyles ?? [])].map((s) => s.trim());\n      }\n    }\n    return compiledStyles;\n  }\n\n  public addCssStyles(ids: string[], styles: string[]) {\n    for (const id of ids) {\n      const entity = this.entities.get(id);\n      if (!styles || !entity) {\n        return;\n      }\n      for (const style of styles) {\n        entity.cssStyles!.push(style);\n      }\n    }\n  }\n\n  public addClass(ids: string[], style: string[]) {\n    ids.forEach((id) => {\n      let classNode = this.classes.get(id);\n      if (classNode === undefined) {\n        classNode = { id, styles: [], textStyles: [] };\n        this.classes.set(id, classNode);\n      }\n\n      if (style) {\n        style.forEach(function (s) {\n          if (/color/.exec(s)) {\n            const newStyle = s.replace('fill', 'bgFill');\n            classNode.textStyles.push(newStyle);\n          }\n          classNode.styles.push(s);\n        });\n      }\n    });\n  }\n\n  public setClass(ids: string[], classNames: string[]) {\n    for (const id of ids) {\n      const entity = this.entities.get(id);\n      if (entity) {\n        for (const className of classNames) {\n          entity.cssClasses += ' ' + className;\n        }\n      }\n    }\n  }\n\n  public clear() {\n    this.entities = new Map();\n    this.classes = new Map();\n    this.relationships = [];\n    commonClear();\n  }\n\n  public getData() {\n    const nodes: Node[] = [];\n    const edges: Edge[] = [];\n    const config = getConfig();\n\n    for (const entityKey of this.entities.keys()) {\n      const entityNode = this.entities.get(entityKey);\n      if (entityNode) {\n        entityNode.cssCompiledStyles = this.getCompiledStyles(entityNode.cssClasses!.split(' '));\n        nodes.push(entityNode as unknown as Node);\n      }\n    }\n\n    let count = 0;\n    for (const relationship of this.relationships) {\n      const edge: Edge = {\n        id: getEdgeId(relationship.entityA, relationship.entityB, {\n          prefix: 'id',\n          counter: count++,\n        }),\n        type: 'normal',\n        curve: 'basis',\n        start: relationship.entityA,\n        end: relationship.entityB,\n        label: relationship.roleA,\n        labelpos: 'c',\n        thickness: 'normal',\n        classes: 'relationshipLine',\n        arrowTypeStart: relationship.relSpec.cardB.toLowerCase(),\n        arrowTypeEnd: relationship.relSpec.cardA.toLowerCase(),\n        pattern: relationship.relSpec.relType == 'IDENTIFYING' ? 'solid' : 'dashed',\n        look: config.look,\n      };\n      edges.push(edge);\n    }\n    return { nodes, edges, other: {}, config, direction: 'TB' };\n  }\n\n  public setAccTitle = setAccTitle;\n  public getAccTitle = getAccTitle;\n  public setAccDescription = setAccDescription;\n  public getAccDescription = getAccDescription;\n  public setDiagramTitle = setDiagramTitle;\n  public getDiagramTitle = getDiagramTitle;\n  public getConfig = () => getConfig().er;\n}\n", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { log } from '../../logger.js';\nimport { getDiagramElement } from '../../rendering-util/insertElementsForSize.js';\nimport { getRegisteredLayoutAlgorithm, render } from '../../rendering-util/render.js';\nimport { setupViewPortForSVG } from '../../rendering-util/setupViewPortForSVG.js';\nimport type { LayoutData } from '../../rendering-util/types.js';\nimport utils from '../../utils.js';\nimport { select } from 'd3';\n\nexport const draw = async function (text: string, id: string, _version: string, diag: any) {\n  log.info('REF0:');\n  log.info('Drawing er diagram (unified)', id);\n  const { securityLevel, er: conf, layout } = getConfig();\n\n  // The getData method provided in all supported diagrams is used to extract the data from the parsed structure\n  // into the Layout data format\n  const data4Layout = diag.db.getData() as LayoutData;\n\n  // Create the root SVG - the element is the div containing the SVG element\n  const svg = getDiagramElement(id, securityLevel);\n\n  data4Layout.type = diag.type;\n  data4Layout.layoutAlgorithm = getRegisteredLayoutAlgorithm(layout);\n\n  // Workaround as when rendering and setting up the graph it uses flowchart spacing before data4Layout spacing?\n  data4Layout.config.flowchart!.nodeSpacing = conf?.nodeSpacing || 140;\n  data4Layout.config.flowchart!.rankSpacing = conf?.rankSpacing || 80;\n  data4Layout.direction = diag.db.getDirection();\n\n  data4Layout.markers = ['only_one', 'zero_or_one', 'one_or_more', 'zero_or_more'];\n  data4Layout.diagramId = id;\n  await render(data4Layout, svg);\n  // Elk layout algorithm displays markers above nodes, so move edges to top so they are \"painted\" over by the nodes.\n  if (data4Layout.layoutAlgorithm === 'elk') {\n    svg.select('.edges').lower();\n  }\n\n  // Sets the background nodes to the same position as their original counterparts.\n  // Background nodes are created when the look is handDrawn so the ER diagram markers do not show underneath.\n  const backgroundNodes = svg.selectAll('[id*=\"-background\"]');\n  // eslint-disable-next-line unicorn/prefer-spread\n  if (Array.from(backgroundNodes).length > 0) {\n    backgroundNodes.each(function (this: SVGElement) {\n      const backgroundNode = select(this);\n      const backgroundId = backgroundNode.attr('id');\n\n      const nonBackgroundId = backgroundId.replace('-background', '');\n      const nonBackgroundNode = svg.select(`#${CSS.escape(nonBackgroundId)}`);\n\n      if (!nonBackgroundNode.empty()) {\n        const transform = nonBackgroundNode.attr('transform');\n        backgroundNode.attr('transform', transform);\n      }\n    });\n  }\n\n  const padding = 8;\n  utils.insertTitle(\n    svg,\n    'erDiagramTitleText',\n    conf?.titleTopMargin ?? 25,\n    diag.db.getDiagramTitle()\n  );\n\n  setupViewPortForSVG(svg, padding, 'erDiagram', conf?.useMaxWidth ?? true);\n};\n", "import * as khroma from 'khroma';\nimport type { FlowChartStyleOptions } from '../flowchart/styles.js';\n\nconst fade = (color: string, opacity: number) => {\n  // @ts-ignore TODO: incorrect types from khroma\n  const channel = khroma.channel;\n\n  const r = channel(color, 'r');\n  const g = channel(color, 'g');\n  const b = channel(color, 'b');\n\n  // @ts-ignore incorrect types from khroma\n  return khroma.rgba(r, g, b, opacity);\n};\n\nconst getStyles = (options: FlowChartStyleOptions) =>\n  `\n  .entityBox {\n    fill: ${options.mainBkg};\n    stroke: ${options.nodeBorder};\n  }\n\n  .relationshipLabelBox {\n    fill: ${options.tertiaryColor};\n    opacity: 0.7;\n    background-color: ${options.tertiaryColor};\n      rect {\n        opacity: 0.5;\n      }\n  }\n\n  .labelBkg {\n    background-color: ${fade(options.tertiaryColor, 0.5)};\n  }\n\n  .edgeLabel .label {\n    fill: ${options.nodeBorder};\n    font-size: 14px;\n  }\n\n  .label {\n    font-family: ${options.fontFamily};\n    color: ${options.nodeTextColor || options.textColor};\n  }\n\n  .edge-pattern-dashed {\n    stroke-dasharray: 8,8;\n  }\n\n  .node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon\n  {\n    fill: ${options.mainBkg};\n    stroke: ${options.nodeBorder};\n    stroke-width: 1px;\n  }\n\n  .relationshipLine {\n    stroke: ${options.lineColor};\n    stroke-width: 1;\n    fill: none;\n  }\n\n  .marker {\n    fill: none !important;\n    stroke: ${options.lineColor} !important;\n    stroke-width: 1;\n  }\n`;\n\nexport default getStyles;\n", "// @ts-ignore: TODO: Fix ts errors\nimport erParser from './parser/erDiagram.jison';\nimport { ErDB } from './erDb.js';\nimport * as renderer from './erRenderer-unified.js';\nimport erStyles from './styles.js';\n\nexport const diagram = {\n  parser: erParser,\n  get db() {\n    return new ErDB();\n  },\n  renderer,\n  styles: erStyles,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA,IAAI,SAAU,WAAU;AACxB,MAAI,IAAE,gCAAS,GAAE,GAAEA,IAAE,GAAE;AAAC,SAAIA,KAAEA,MAAG,CAAC,GAAE,IAAE,EAAE,QAAO,KAAIA,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE;AAAC,WAAOA;AAAA,EAAC,GAAhE,MAAkE,MAAI,CAAC,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,IAAG,IAAG,IAAG,EAAE;AACrxB,MAAIC,UAAS;AAAA,IAAC,OAAO,gCAAS,QAAS;AAAA,IAAE,GAApB;AAAA,IACrB,IAAI,CAAC;AAAA,IACL,UAAU,EAAC,SAAQ,GAAE,SAAQ,GAAE,cAAa,GAAE,YAAW,GAAE,OAAM,GAAE,QAAO,GAAE,SAAQ,GAAE,aAAY,GAAE,WAAU,IAAG,cAAa,IAAG,WAAU,IAAG,SAAQ,IAAG,QAAO,IAAG,mBAAkB,IAAG,UAAS,IAAG,eAAc,IAAG,cAAa,IAAG,cAAa,IAAG,OAAM,IAAG,OAAM,IAAG,SAAQ,IAAG,eAAc,IAAG,aAAY,IAAG,mBAAkB,IAAG,aAAY,IAAG,mBAAkB,IAAG,6BAA4B,IAAG,aAAY,IAAG,qBAAoB,IAAG,kBAAiB,IAAG,kBAAiB,IAAG,gBAAe,IAAG,gBAAe,IAAG,gBAAe,IAAG,gBAAe,IAAG,YAAW,IAAG,aAAY,IAAG,aAAY,IAAG,gBAAe,IAAG,cAAa,IAAG,SAAQ,IAAG,SAAQ,IAAG,SAAQ,IAAG,SAAQ,IAAG,kBAAiB,IAAG,QAAO,IAAG,OAAM,IAAG,QAAO,IAAG,eAAc,IAAG,aAAY,IAAG,iBAAgB,IAAG,iBAAgB,IAAG,wBAAuB,IAAG,oBAAmB,IAAG,kBAAiB,IAAG,oBAAmB,IAAG,KAAI,IAAG,iBAAgB,IAAG,WAAU,IAAG,eAAc,IAAG,WAAU,IAAG,eAAc,IAAG,gBAAe,IAAG,eAAc,IAAG,YAAW,IAAG,aAAY,IAAG,mBAAkB,IAAG,eAAc,IAAG,QAAO,IAAG,WAAU,GAAE,QAAO,EAAC;AAAA,IACrkC,YAAY,EAAC,GAAE,SAAQ,GAAE,cAAa,GAAE,OAAM,GAAE,SAAQ,IAAG,WAAU,IAAG,SAAQ,IAAG,mBAAkB,IAAG,eAAc,IAAG,cAAa,IAAG,OAAM,IAAG,OAAM,IAAG,SAAQ,IAAG,eAAc,IAAG,aAAY,IAAG,mBAAkB,IAAG,aAAY,IAAG,mBAAkB,IAAG,6BAA4B,IAAG,gBAAe,IAAG,gBAAe,IAAG,gBAAe,IAAG,gBAAe,IAAG,YAAW,IAAG,gBAAe,IAAG,cAAa,IAAG,SAAQ,IAAG,SAAQ,IAAG,SAAQ,IAAG,QAAO,IAAG,OAAM,IAAG,QAAO,IAAG,eAAc,IAAG,kBAAiB,IAAG,KAAI,IAAG,iBAAgB,IAAG,WAAU,IAAG,eAAc,IAAG,gBAAe,IAAG,eAAc,IAAG,YAAW,IAAG,aAAY,IAAG,mBAAkB,IAAG,eAAc,IAAG,OAAM;AAAA,IAC7pB,cAAc,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC;AAAA,IACnhB,eAAe,gCAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAyB,IAAiB,IAAiB;AAG3H,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAS;AAAA,QACjB,KAAK;AAEL;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,CAAC;AACX;AAAA,QACA,KAAK;AACL,aAAG,KAAG,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC;AAAE,eAAK,IAAI,GAAG,KAAG,CAAC;AACtC;AAAA,QACA,KAAK;AAAA,QAAG,KAAK;AACZ,eAAK,IAAI,GAAG,EAAE;AACf;AAAA,QACA,KAAK;AAAA,QAAG,KAAK;AACZ,eAAK,IAAE,CAAC;AACT;AAAA,QACA,KAAK;AAEK,aAAG,UAAU,GAAG,KAAG,CAAC,CAAC;AACrB,aAAG,UAAU,GAAG,KAAG,CAAC,CAAC;AACrB,aAAG,gBAAgB,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AAEjE;AAAA,QACA,KAAK;AAEK,aAAG,UAAU,GAAG,KAAG,CAAC,CAAC;AACrB,aAAG,UAAU,GAAG,KAAG,CAAC,CAAC;AACrB,aAAG,gBAAgB,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AACvD,aAAG,SAAS,CAAC,GAAG,KAAG,CAAC,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AAChC,aAAG,SAAS,CAAC,GAAG,KAAG,CAAC,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AAE1C;AAAA,QACA,KAAK;AAEK,aAAG,UAAU,GAAG,KAAG,CAAC,CAAC;AACrB,aAAG,UAAU,GAAG,KAAG,CAAC,CAAC;AACrB,aAAG,gBAAgB,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AACvD,aAAG,SAAS,CAAC,GAAG,KAAG,CAAC,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AAE1C;AAAA,QACA,KAAK;AAEK,aAAG,UAAU,GAAG,KAAG,CAAC,CAAC;AACrB,aAAG,UAAU,GAAG,KAAG,CAAC,CAAC;AACrB,aAAG,gBAAgB,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AACvD,aAAG,SAAS,CAAC,GAAG,KAAG,CAAC,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AAE1C;AAAA,QACA,KAAK;AAEK,aAAG,UAAU,GAAG,KAAG,CAAC,CAAC;AACrB,aAAG,cAAc,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AAE7C;AAAA,QACA,KAAK;AAEK,aAAG,UAAU,GAAG,KAAG,CAAC,CAAC;AACrB,aAAG,cAAc,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AACnC,aAAG,SAAS,CAAC,GAAG,KAAG,CAAC,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AAE1C;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,GAAG,KAAG,CAAC,CAAC;AACtB;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,GAAG,KAAG,CAAC,CAAC;AAAG,aAAG,SAAS,CAAC,GAAG,KAAG,CAAC,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AACzD;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,GAAG,EAAE,CAAC;AACpB;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,GAAG,KAAG,CAAC,CAAC;AAAG,aAAG,SAAS,CAAC,GAAG,KAAG,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;AACvD;AAAA,QACA,KAAK;AAEK,aAAG,UAAU,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AAC/B,aAAG,cAAc,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AAE7C;AAAA,QACA,KAAK;AAEK,aAAG,UAAU,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AAC/B,aAAG,cAAc,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AACnC,aAAG,SAAS,CAAC,GAAG,KAAG,CAAC,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AAG1C;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AAChC;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AAAG,aAAG,SAAS,CAAC,GAAG,KAAG,CAAC,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AACnE;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AAChC;AAAA,QACA,KAAK;AACJ,aAAG,UAAU,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AAAG,aAAG,SAAS,CAAC,GAAG,KAAG,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;AACjE;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AACb,eAAK,IAAE,GAAG,EAAE,EAAE,KAAK;AAAE,aAAG,YAAY,KAAK,CAAC;AAC3C;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AACb,eAAK,IAAE,GAAG,EAAE,EAAE,KAAK;AAAE,aAAG,kBAAkB,KAAK,CAAC;AACjD;AAAA,QACA,KAAK;AACJ,aAAG,aAAa,IAAI;AACrB;AAAA,QACA,KAAK;AACJ,aAAG,aAAa,IAAI;AACrB;AAAA,QACA,KAAK;AACJ,aAAG,aAAa,IAAI;AACrB;AAAA,QACA,KAAK;AACJ,aAAG,aAAa,IAAI;AACrB;AAAA,QACA,KAAK;AACL,eAAK,IAAI,GAAG,KAAG,CAAC;AAAE,aAAG,SAAS,GAAG,KAAG,CAAC,GAAE,GAAG,KAAG,CAAC,CAAC;AAC/C;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AAAA,QAAI,KAAK;AAAA,QAAI,KAAK;AAC/B,eAAK,IAAI,CAAC,GAAG,EAAE,CAAC;AACjB;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AACb,eAAK,IAAI,GAAG,KAAG,CAAC,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;AAClC;AAAA,QACA,KAAK;AACL,eAAK,IAAI,GAAG,KAAG,CAAC;AAAE,aAAG,SAAS,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC;AAC9C;AAAA,QACA,KAAK;AACL;AAAC,eAAK,IAAI,GAAG,KAAG,CAAC;AAAE,aAAG,aAAa,GAAG,KAAG,CAAC,GAAE,GAAG,KAAG,CAAC,CAAC;AACpD;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,CAAC,GAAG,EAAE,CAAC;AACjB;AAAA,QACA,KAAK;AACL,aAAG,KAAG,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC;AAAE,eAAK,IAAI,GAAG,KAAG,CAAC;AACtC;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,KAAG,CAAC,IAAI,GAAG,EAAE;AAC1B;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AAAA,QAAI,KAAK;AACtB,eAAK,IAAI,GAAG,EAAE,EAAE,QAAQ,MAAM,EAAE;AACjC;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AACb,eAAK,IAAI,GAAG,EAAE;AACf;AAAA,QACA,KAAK;AACJ,aAAG,EAAE,EAAE,KAAK,GAAG,KAAG,CAAC,CAAC;AAAG,eAAK,IAAE,GAAG,EAAE;AACpC;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,EAAE,MAAM,GAAG,KAAG,CAAC,GAAG,MAAM,GAAG,EAAE,EAAE;AACzC;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,EAAE,MAAM,GAAG,KAAG,CAAC,GAAG,MAAM,GAAG,KAAG,CAAC,GAAG,MAAM,GAAG,EAAE,EAAE;AACzD;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,EAAE,MAAM,GAAG,KAAG,CAAC,GAAG,MAAM,GAAG,KAAG,CAAC,GAAG,SAAS,GAAG,EAAE,EAAE;AAC5D;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,EAAE,MAAM,GAAG,KAAG,CAAC,GAAG,MAAM,GAAG,KAAG,CAAC,GAAG,MAAM,GAAG,KAAG,CAAC,GAAG,SAAS,GAAG,EAAE,EAAE;AAC5E;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AAAA,QAAI,KAAK;AACtB,eAAK,IAAE,GAAG,EAAE;AACb;AAAA,QACA,KAAK;AACJ,aAAG,KAAG,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC;AAAG,eAAK,IAAI,GAAG,KAAG,CAAC;AACxC;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,EAAE,EAAE,QAAQ,MAAM,EAAE;AAC/B;AAAA,QACA,KAAK;AAEG,eAAK,IAAI,EAAE,OAAO,GAAG,EAAE,GAAG,SAAS,GAAG,KAAG,CAAC,GAAG,OAAO,GAAG,KAAG,CAAC,EAAE;AAGrE;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,YAAY;AACzB;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,YAAY;AACzB;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,YAAY;AACzB;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,YAAY;AACzB;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,YAAY;AACzB;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,eAAe;AAC5B;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,GAAG,eAAe;AAC5B;AAAA,MACA;AAAA,IACA,GA3Me;AAAA,IA4Mf,OAAO,CAAC,EAAC,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,CAAC,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,EAAC,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,GAAE,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAC,GAAE,IAAG,IAAG,GAAE,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,GAAE,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,CAAC,GAAE,EAAC,GAAE,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAE,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,CAAC,GAAE,EAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,KAAI,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,KAAI,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,CAAC,GAAE,EAAC,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,IAAG,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,KAAI,IAAG,CAAC,GAAE,GAAG,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,GAAE,EAAC,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAC,IAAG,CAAC,GAAE,GAAG,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IACjwF,gBAAgB,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC;AAAA,IACpC,YAAY,gCAAS,WAAY,KAAK,MAAM;AACxC,UAAI,KAAK,aAAa;AAClB,aAAK,MAAM,GAAG;AAAA,MAClB,OAAO;AACH,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;AAAA,MACV;AAAA,IACJ,GARY;AAAA,IASZ,OAAO,gCAAS,MAAM,OAAO;AACzB,UAAI,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAIC,SAAQ,OAAO,OAAO,KAAK,KAAK;AACpC,UAAI,cAAc,EAAE,IAAI,CAAC,EAAE;AAC3B,eAAS,KAAK,KAAK,IAAI;AACnB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AAClD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;AAAA,QACjC;AAAA,MACJ;AACA,MAAAA,OAAM,SAAS,OAAO,YAAY,EAAE;AACpC,kBAAY,GAAG,QAAQA;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAOA,OAAM,UAAU,aAAa;AACpC,QAAAA,OAAM,SAAS,CAAC;AAAA,MACpB;AACA,UAAI,QAAQA,OAAM;AAClB,aAAO,KAAK,KAAK;AACjB,UAAI,SAASA,OAAM,WAAWA,OAAM,QAAQ;AAC5C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACjD,aAAK,aAAa,YAAY,GAAG;AAAA,MACrC,OAAO;AACH,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;AAAA,MAClD;AACA,eAAS,SAAS,GAAG;AACjB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;AAAA,MACpC;AAJS;AAKD,eAAS,MAAM;AACf,YAAI;AACJ,gBAAQ,OAAO,IAAI,KAAKA,OAAM,IAAI,KAAK;AACvC,YAAI,OAAO,UAAU,UAAU;AAC3B,cAAI,iBAAiB,OAAO;AACxB,qBAAS;AACT,oBAAQ,OAAO,IAAI;AAAA,UACvB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,QACpC;AACA,eAAO;AAAA,MACX;AAXa;AAYjB,UAAI,QAAQ,gBAAgB,OAAO,QAAQ,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACT,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC5B,mBAAS,KAAK,eAAe,KAAK;AAAA,QACtC,OAAO;AACH,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACjD,qBAAS,IAAI;AAAA,UACjB;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,QAChD;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AAC/D,cAAI,SAAS;AACb,qBAAW,CAAC;AACZ,eAAK,KAAK,MAAM,KAAK,GAAG;AACpB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AAClC,uBAAS,KAAK,MAAO,KAAK,WAAW,CAAC,IAAI,GAAI;AAAA,YAClD;AAAA,UACJ;AACA,cAAIA,OAAM,cAAc;AACpB,qBAAS,0BAA0B,WAAW,KAAK,QAAQA,OAAM,aAAa,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAc,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UAChL,OAAO;AACH,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAQ,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UACxJ;AACA,eAAK,WAAW,QAAQ;AAAA,YACpB,MAAMA,OAAM;AAAA,YACZ,OAAO,KAAK,WAAW,MAAM,KAAK;AAAA,YAClC,MAAMA,OAAM;AAAA,YACZ,KAAK;AAAA,YACL;AAAA,UACJ,CAAC;AAAA,QACL;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACjD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;AAAA,QACtG;AACA,gBAAQ,OAAO,CAAC,GAAG;AAAA,UACnB,KAAK;AACD,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAKA,OAAM,MAAM;AACxB,mBAAO,KAAKA,OAAM,MAAM;AACxB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACT,gBAAI,CAAC,gBAAgB;AACjB,uBAASA,OAAM;AACf,uBAASA,OAAM;AACf,yBAAWA,OAAM;AACjB,sBAAQA,OAAM;AACd,kBAAI,aAAa,GAAG;AAChB;AAAA,cACJ;AAAA,YACJ,OAAO;AACH,uBAAS;AACT,+BAAiB;AAAA,YACrB;AACA;AAAA,UACJ,KAAK;AACD,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;AAAA,cACP,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,YAC3C;AACA,gBAAI,QAAQ;AACR,oBAAM,GAAG,QAAQ;AAAA,gBACb,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;AAAA,gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,cACrC;AAAA,YACJ;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;AAAA,cAChC;AAAA,cACA;AAAA,cACA;AAAA,cACA,YAAY;AAAA,cACZ,OAAO,CAAC;AAAA,cACR;AAAA,cACA;AAAA,YACJ,EAAE,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC1B,qBAAO;AAAA,YACX;AACA,gBAAI,KAAK;AACL,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AAAA,YACrC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;AAAA,UACJ,KAAK;AACD,mBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX,GA3IO;AAAA,EA2IN;AAED,MAAI,QAAS,2BAAU;AACvB,QAAIA,SAAS;AAAA,MAEb,KAAI;AAAA,MAEJ,YAAW,gCAAS,WAAW,KAAK,MAAM;AAClC,YAAI,KAAK,GAAG,QAAQ;AAChB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;AAAA,QACvC,OAAO;AACH,gBAAM,IAAI,MAAM,GAAG;AAAA,QACvB;AAAA,MACJ,GANO;AAAA;AAAA,MASX,UAAS,gCAAU,OAAO,IAAI;AACtB,aAAK,KAAK,MAAM,KAAK,MAAM,CAAC;AAC5B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;AAAA,UACV,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,WAAW;AAAA,UACX,aAAa;AAAA,QACjB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,GAAE,CAAC;AAAA,QAC5B;AACA,aAAK,SAAS;AACd,eAAO;AAAA,MACX,GAlBK;AAAA;AAAA,MAqBT,OAAM,kCAAY;AACV,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACP,eAAK;AACL,eAAK,OAAO;AAAA,QAChB,OAAO;AACH,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,MAAM,CAAC;AAAA,QACvB;AAEA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;AAAA,MACX,GApBE;AAAA;AAAA,MAuBN,OAAM,gCAAU,IAAI;AACZ,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AAEpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAE5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAE7D,YAAI,MAAM,SAAS,GAAG;AAClB,eAAK,YAAY,MAAM,SAAS;AAAA,QACpC;AACA,YAAI,IAAI,KAAK,OAAO;AAEpB,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,SACR,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAC5D,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAChE,KAAK,OAAO,eAAe;AAAA,QACjC;AAEA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;AAAA,QACvD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;AAAA,MACX,GAhCE;AAAA;AAAA,MAmCN,MAAK,kCAAY;AACT,aAAK,QAAQ;AACb,eAAO;AAAA,MACX,GAHC;AAAA;AAAA,MAML,QAAO,kCAAY;AACX,YAAI,KAAK,QAAQ,iBAAiB;AAC9B,eAAK,aAAa;AAAA,QACtB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAa,GAAG;AAAA,YAC9N,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QAEL;AACA,eAAO;AAAA,MACX,GAZG;AAAA;AAAA,MAeP,MAAK,gCAAU,GAAG;AACV,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,MAClC,GAFC;AAAA;AAAA,MAKL,WAAU,kCAAY;AACd,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAM,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,MAC7E,GAHM;AAAA;AAAA,MAMV,eAAc,kCAAY;AAClB,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AAClB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAG,KAAK,MAAM;AAAA,QAChD;AACA,gBAAQ,KAAK,OAAO,GAAE,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;AAAA,MAClF,GANU;AAAA;AAAA,MASd,cAAa,kCAAY;AACjB,YAAI,MAAM,KAAK,UAAU;AACzB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAc,IAAI,OAAO,IAAI;AAAA,MACnD,GAJS;AAAA;AAAA,MAOb,YAAW,gCAAS,OAAO,cAAc;AACjC,YAAI,OACA,OACA;AAEJ,YAAI,KAAK,QAAQ,iBAAiB;AAE9B,mBAAS;AAAA,YACL,UAAU,KAAK;AAAA,YACf,QAAQ;AAAA,cACJ,YAAY,KAAK,OAAO;AAAA,cACxB,WAAW,KAAK;AAAA,cAChB,cAAc,KAAK,OAAO;AAAA,cAC1B,aAAa,KAAK,OAAO;AAAA,YAC7B;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,IAAI,KAAK;AAAA,YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;AAAA,YAC3C,MAAM,KAAK;AAAA,UACf;AACA,cAAI,KAAK,QAAQ,QAAQ;AACrB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;AAAA,UACnD;AAAA,QACJ;AAEA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACP,eAAK,YAAY,MAAM;AAAA,QAC3B;AACA,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,QACA,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAC5E,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;AAAA,QACpD;AACA,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;AAAA,QAChE;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC1B,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,OAAO;AACP,iBAAO;AAAA,QACX,WAAW,KAAK,YAAY;AAExB,mBAAS,KAAK,QAAQ;AAClB,iBAAK,CAAC,IAAI,OAAO,CAAC;AAAA,UACtB;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,GArEO;AAAA;AAAA,MAwEX,MAAK,kCAAY;AACT,YAAI,KAAK,MAAM;AACX,iBAAO,KAAK;AAAA,QAChB;AACA,YAAI,CAAC,KAAK,QAAQ;AACd,eAAK,OAAO;AAAA,QAChB;AAEA,YAAI,OACA,OACA,WACA;AACJ,YAAI,CAAC,KAAK,OAAO;AACb,eAAK,SAAS;AACd,eAAK,QAAQ;AAAA,QACjB;AACA,YAAI,QAAQ,KAAK,cAAc;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAChE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAC9B,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACjB,uBAAO;AAAA,cACX,WAAW,KAAK,YAAY;AACxB,wBAAQ;AACR;AAAA,cACJ,OAAO;AAEH,uBAAO;AAAA,cACX;AAAA,YACJ,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC3B;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,OAAO;AACP,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACjB,mBAAO;AAAA,UACX;AAEA,iBAAO;AAAA,QACX;AACA,YAAI,KAAK,WAAW,IAAI;AACpB,iBAAO,KAAK;AAAA,QAChB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAa,GAAG;AAAA,YACpH,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QACL;AAAA,MACJ,GAvDC;AAAA;AAAA,MA0DL,KAAI,gCAAS,MAAO;AACZ,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,GAAG;AACH,iBAAO;AAAA,QACX,OAAO;AACH,iBAAO,KAAK,IAAI;AAAA,QACpB;AAAA,MACJ,GAPA;AAAA;AAAA,MAUJ,OAAM,gCAAS,MAAO,WAAW;AACzB,aAAK,eAAe,KAAK,SAAS;AAAA,MACtC,GAFE;AAAA;AAAA,MAKN,UAAS,gCAAS,WAAY;AACtB,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACP,iBAAO,KAAK,eAAe,IAAI;AAAA,QACnC,OAAO;AACH,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,eAAc,gCAAS,gBAAiB;AAChC,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACnF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;AAAA,QAChF,OAAO;AACH,iBAAO,KAAK,WAAW,SAAS,EAAE;AAAA,QACtC;AAAA,MACJ,GANU;AAAA;AAAA,MASd,UAAS,gCAAS,SAAU,GAAG;AACvB,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACR,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC,OAAO;AACH,iBAAO;AAAA,QACX;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,WAAU,gCAAS,UAAW,WAAW;AACjC,aAAK,MAAM,SAAS;AAAA,MACxB,GAFM;AAAA;AAAA,MAKV,gBAAe,gCAAS,iBAAiB;AACjC,eAAO,KAAK,eAAe;AAAA,MAC/B,GAFW;AAAA,MAGf,SAAS,EAAC,oBAAmB,KAAI;AAAA,MACjC,eAAe,gCAAS,UAAU,IAAG,KAAI,2BAA0B,UAAU;AAC7E,YAAI,UAAQ;AACZ,gBAAO,2BAA2B;AAAA,UAClC,KAAK;AAAG,iBAAK,MAAM,WAAW;AAAE,mBAAO;AACvC;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAG,mBAAO;AAChC;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,WAAW;AAAE,mBAAO;AACvC;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAG,mBAAO;AAChC;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,qBAAqB;AACxC;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AACtB;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,OAAO;AAAG,mBAAO;AACrC;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,mBAAO;AACjC;AAAA,UACA,KAAK;AAAG,mBAAO,IAAI,OAAO,CAAC;AAC3B;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,OAAO;AAAG,mBAAO;AACrC;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,mBAAO;AACjC;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,OAAO;AAAG,mBAAO;AACrC;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO,IAAI,OAAO,CAAC;AAC3B;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,QACA;AAAA,MACA,GA1Je;AAAA,MA2Jf,OAAO,CAAC,yBAAwB,yBAAwB,yBAAwB,yBAAwB,0BAAyB,cAAa,gBAAe,gCAA+B,gCAA+B,gCAA+B,gCAA+B,eAAc,aAAY,eAAc,4BAA2B,iBAAgB,qBAAoB,YAAW,WAAU,WAAU,WAAU,aAAY,WAAU,aAAY,oCAAmC,kCAAiC,2EAA0E,iBAAgB,eAAc,YAAW,WAAU,YAAW,YAAW,iBAAgB,eAAc,aAAY,WAAU,WAAU,WAAU,oBAAmB,iBAAgB,uBAAsB,uBAAsB,uBAAsB,aAAY,eAAc,uBAAsB,wBAAuB,wBAAuB,aAAY,eAAc,mBAAkB,mBAAkB,gBAAe,cAAa,eAAc,oBAAmB,aAAY,cAAa,aAAY,aAAY,cAAa,gBAAe,cAAa,YAAW,cAAa,yBAAwB,aAAY,aAAY,iCAAgC,WAAU,iCAAgC,eAAc,WAAU,SAAS;AAAA,MACrzC,YAAY,EAAC,SAAQ,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,uBAAsB,EAAC,SAAQ,CAAC,GAAE,CAAC,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,CAAC,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,CAAC,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,WAAU,EAAC,SAAQ,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,KAAI,EAAC;AAAA,IAC9d;AACA,WAAOA;AAAA,EACP,EAAG;AACH,EAAAD,QAAO,QAAQ;AACf,WAAS,SAAU;AACjB,SAAK,KAAK,CAAC;AAAA,EACb;AAFS;AAGT,SAAO,YAAYA;AAAO,EAAAA,QAAO,SAAS;AAC1C,SAAO,IAAI;AACX,EAAG;AACF,OAAO,SAAS;AAEhB,IAAO,oBAAQ;;;ACj5BT,IAAM,OAAN,MAAgC;AAAA,EAmBrC,cAAc;AAlBd,SAAQ,WAAW,oBAAI,IAAwB;AAC/C,SAAQ,gBAAgC,CAAC;AACzC,SAAQ,UAAU,oBAAI,IAAyB;AAC/C,SAAQ,YAAY;AAEpB,SAAQ,cAAc;AAAA,MACpB,aAAa;AAAA,MACb,cAAc;AAAA,MACd,aAAa;AAAA,MACb,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAEA,SAAQ,iBAAiB;AAAA,MACvB,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAkNA,SAAO,cAAc;AACrB,SAAO,cAAc;AACrB,SAAO,oBAAoB;AAC3B,SAAO,oBAAoB;AAC3B,SAAO,kBAAkB;AACzB,SAAO,kBAAkB;AACzB,SAAO,YAAY,6BAAM,UAAU,EAAE,IAAlB;AArNjB,SAAK,MAAM;AACX,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AACzC,SAAK,gBAAgB,KAAK,cAAc,KAAK,IAAI;AACjD,SAAK,kBAAkB,KAAK,gBAAgB,KAAK,IAAI;AACrD,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,WAAW,KAAK,SAAS,KAAK,IAAI;AACvC,SAAK,WAAW,KAAK,SAAS,KAAK,IAAI;AACvC,SAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAC7C,SAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;AAAA,EAC3D;AAAA,EA9CF,OAgBuC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqC9B,UAAU,MAAc,QAAQ,IAAgB;AACrD,QAAI,CAAC,KAAK,SAAS,IAAI,IAAI,GAAG;AAC5B,WAAK,SAAS,IAAI,MAAM;AAAA,QACtB,IAAI,UAAU,IAAI,IAAI,KAAK,SAAS,IAAI;AAAA,QACxC,OAAO;AAAA,QACP,YAAY,CAAC;AAAA,QACb;AAAA,QACA,OAAO;AAAA,QACP,MAAM,UAAU,EAAE,QAAQ;AAAA,QAC1B,YAAY;AAAA,QACZ,WAAW,CAAC;AAAA,MACd,CAAC;AACD,UAAI,KAAK,sBAAsB,IAAI;AAAA,IACrC,WAAW,CAAC,KAAK,SAAS,IAAI,IAAI,GAAG,SAAS,OAAO;AACnD,WAAK,SAAS,IAAI,IAAI,EAAG,QAAQ;AACjC,UAAI,KAAK,cAAc,KAAK,gBAAgB,IAAI,GAAG;AAAA,IACrD;AAEA,WAAO,KAAK,SAAS,IAAI,IAAI;AAAA,EAC/B;AAAA,EAEO,UAAU,MAAc;AAC7B,WAAO,KAAK,SAAS,IAAI,IAAI;AAAA,EAC/B;AAAA,EAEO,cAAc;AACnB,WAAO,KAAK;AAAA,EACd;AAAA,EAEO,aAAa;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EAEO,cAAc,YAAoB,SAAsB;AAC7D,UAAM,SAAS,KAAK,UAAU,UAAU;AAGxC,QAAI;AACJ,SAAK,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AACxC,UAAI,CAAC,QAAQ,CAAC,EAAE,MAAM;AACpB,gBAAQ,CAAC,EAAE,OAAO,CAAC;AAAA,MACrB;AACA,UAAI,CAAC,QAAQ,CAAC,EAAE,SAAS;AACvB,gBAAQ,CAAC,EAAE,UAAU;AAAA,MACvB;AACA,aAAO,WAAW,KAAK,QAAQ,CAAC,CAAC;AACjC,UAAI,MAAM,oBAAoB,QAAQ,CAAC,EAAE,IAAI;AAAA,IAC/C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUO,gBAAgB,MAAc,MAAc,MAAc,OAAgB;AAC/E,UAAM,UAAU,KAAK,SAAS,IAAI,IAAI;AACtC,UAAM,UAAU,KAAK,SAAS,IAAI,IAAI;AACtC,QAAI,CAAC,WAAW,CAAC,SAAS;AACxB;AAAA,IACF;AAEA,UAAM,MAAM;AAAA,MACV,SAAS,QAAQ;AAAA,MACjB,OAAO;AAAA,MACP,SAAS,QAAQ;AAAA,MACjB,SAAS;AAAA,IACX;AAEA,SAAK,cAAc,KAAK,GAAG;AAC3B,QAAI,MAAM,4BAA4B,GAAG;AAAA,EAC3C;AAAA,EAEO,mBAAmB;AACxB,WAAO,KAAK;AAAA,EACd;AAAA,EAEO,eAAe;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EAEO,aAAa,KAAa;AAC/B,SAAK,YAAY;AAAA,EACnB;AAAA,EAEQ,kBAAkB,WAAqB;AAC7C,QAAI,iBAA2B,CAAC;AAChC,eAAW,eAAe,WAAW;AACnC,YAAM,WAAW,KAAK,QAAQ,IAAI,WAAW;AAC7C,UAAI,UAAU,QAAQ;AACpB,yBAAiB,CAAC,GAAG,gBAAgB,GAAI,SAAS,UAAU,CAAC,CAAE,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;AAAA,MACtF;AACA,UAAI,UAAU,YAAY;AACxB,yBAAiB,CAAC,GAAG,gBAAgB,GAAI,SAAS,cAAc,CAAC,CAAE,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;AAAA,MAC1F;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEO,aAAa,KAAe,QAAkB;AACnD,eAAW,MAAM,KAAK;AACpB,YAAM,SAAS,KAAK,SAAS,IAAI,EAAE;AACnC,UAAI,CAAC,UAAU,CAAC,QAAQ;AACtB;AAAA,MACF;AACA,iBAAW,SAAS,QAAQ;AAC1B,eAAO,UAAW,KAAK,KAAK;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAAA,EAEO,SAAS,KAAe,OAAiB;AAC9C,QAAI,QAAQ,CAAC,OAAO;AAClB,UAAI,YAAY,KAAK,QAAQ,IAAI,EAAE;AACnC,UAAI,cAAc,QAAW;AAC3B,oBAAY,EAAE,IAAI,QAAQ,CAAC,GAAG,YAAY,CAAC,EAAE;AAC7C,aAAK,QAAQ,IAAI,IAAI,SAAS;AAAA,MAChC;AAEA,UAAI,OAAO;AACT,cAAM,QAAQ,SAAU,GAAG;AACzB,cAAI,QAAQ,KAAK,CAAC,GAAG;AACnB,kBAAM,WAAW,EAAE,QAAQ,QAAQ,QAAQ;AAC3C,sBAAU,WAAW,KAAK,QAAQ;AAAA,UACpC;AACA,oBAAU,OAAO,KAAK,CAAC;AAAA,QACzB,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEO,SAAS,KAAe,YAAsB;AACnD,eAAW,MAAM,KAAK;AACpB,YAAM,SAAS,KAAK,SAAS,IAAI,EAAE;AACnC,UAAI,QAAQ;AACV,mBAAW,aAAa,YAAY;AAClC,iBAAO,cAAc,MAAM;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEO,QAAQ;AACb,SAAK,WAAW,oBAAI,IAAI;AACxB,SAAK,UAAU,oBAAI,IAAI;AACvB,SAAK,gBAAgB,CAAC;AACtB,UAAY;AAAA,EACd;AAAA,EAEO,UAAU;AACf,UAAM,QAAgB,CAAC;AACvB,UAAM,QAAgB,CAAC;AACvB,UAAM,SAAS,UAAU;AAEzB,eAAW,aAAa,KAAK,SAAS,KAAK,GAAG;AAC5C,YAAM,aAAa,KAAK,SAAS,IAAI,SAAS;AAC9C,UAAI,YAAY;AACd,mBAAW,oBAAoB,KAAK,kBAAkB,WAAW,WAAY,MAAM,GAAG,CAAC;AACvF,cAAM,KAAK,UAA6B;AAAA,MAC1C;AAAA,IACF;AAEA,QAAI,QAAQ;AACZ,eAAW,gBAAgB,KAAK,eAAe;AAC7C,YAAM,OAAa;AAAA,QACjB,IAAI,UAAU,aAAa,SAAS,aAAa,SAAS;AAAA,UACxD,QAAQ;AAAA,UACR,SAAS;AAAA,QACX,CAAC;AAAA,QACD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO,aAAa;AAAA,QACpB,KAAK,aAAa;AAAA,QAClB,OAAO,aAAa;AAAA,QACpB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,SAAS;AAAA,QACT,gBAAgB,aAAa,QAAQ,MAAM,YAAY;AAAA,QACvD,cAAc,aAAa,QAAQ,MAAM,YAAY;AAAA,QACrD,SAAS,aAAa,QAAQ,WAAW,gBAAgB,UAAU;AAAA,QACnE,MAAM,OAAO;AAAA,MACf;AACA,YAAM,KAAK,IAAI;AAAA,IACjB;AACA,WAAO,EAAE,OAAO,OAAO,OAAO,CAAC,GAAG,QAAQ,WAAW,KAAK;AAAA,EAC5D;AASF;;;AC1PA;AAAA;AAAA;AAAA;AAOA,SAAS,cAAc;AAEhB,IAAM,OAAO,sCAAgB,MAAc,IAAY,UAAkB,MAAW;AACzF,MAAI,KAAK,OAAO;AAChB,MAAI,KAAK,gCAAgC,EAAE;AAC3C,QAAM,EAAE,eAAe,IAAI,MAAM,OAAO,IAAI,UAAU;AAItD,QAAM,cAAc,KAAK,GAAG,QAAQ;AAGpC,QAAM,MAAM,kBAAkB,IAAI,aAAa;AAE/C,cAAY,OAAO,KAAK;AACxB,cAAY,kBAAkB,6BAA6B,MAAM;AAGjE,cAAY,OAAO,UAAW,cAAc,MAAM,eAAe;AACjE,cAAY,OAAO,UAAW,cAAc,MAAM,eAAe;AACjE,cAAY,YAAY,KAAK,GAAG,aAAa;AAE7C,cAAY,UAAU,CAAC,YAAY,eAAe,eAAe,cAAc;AAC/E,cAAY,YAAY;AACxB,QAAM,OAAO,aAAa,GAAG;AAE7B,MAAI,YAAY,oBAAoB,OAAO;AACzC,QAAI,OAAO,QAAQ,EAAE,MAAM;AAAA,EAC7B;AAIA,QAAM,kBAAkB,IAAI,UAAU,qBAAqB;AAE3D,MAAI,MAAM,KAAK,eAAe,EAAE,SAAS,GAAG;AAC1C,oBAAgB,KAAK,WAA4B;AAC/C,YAAM,iBAAiB,OAAO,IAAI;AAClC,YAAM,eAAe,eAAe,KAAK,IAAI;AAE7C,YAAM,kBAAkB,aAAa,QAAQ,eAAe,EAAE;AAC9D,YAAM,oBAAoB,IAAI,OAAO,IAAI,IAAI,OAAO,eAAe,CAAC,EAAE;AAEtE,UAAI,CAAC,kBAAkB,MAAM,GAAG;AAC9B,cAAM,YAAY,kBAAkB,KAAK,WAAW;AACpD,uBAAe,KAAK,aAAa,SAAS;AAAA,MAC5C;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,UAAU;AAChB,gBAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,MAAM,kBAAkB;AAAA,IACxB,KAAK,GAAG,gBAAgB;AAAA,EAC1B;AAEA,sBAAoB,KAAK,SAAS,aAAa,MAAM,eAAe,IAAI;AAC1E,GAxDoB;;;ACTpB,YAAY,YAAY;AAGxB,IAAM,OAAO,wBAAC,OAAe,YAAoB;AAE/C,QAAME,WAAiB;AAEvB,QAAM,IAAIA,SAAQ,OAAO,GAAG;AAC5B,QAAM,IAAIA,SAAQ,OAAO,GAAG;AAC5B,QAAM,IAAIA,SAAQ,OAAO,GAAG;AAG5B,SAAc,YAAK,GAAG,GAAG,GAAG,OAAO;AACrC,GAVa;AAYb,IAAM,YAAY,wBAAC,YACjB;AAAA;AAAA,YAEU,QAAQ,OAAO;AAAA,cACb,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,YAIpB,QAAQ,aAAa;AAAA;AAAA,wBAET,QAAQ,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAOrB,KAAK,QAAQ,eAAe,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA,YAI5C,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,mBAKX,QAAQ,UAAU;AAAA,aACxB,QAAQ,iBAAiB,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAY3C,QAAQ,OAAO;AAAA,cACb,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,cAKlB,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAOjB,QAAQ,SAAS;AAAA;AAAA;AAAA,GApDb;AAyDlB,IAAO,iBAAQ;;;AClER,IAAM,UAAU;AAAA,EACrB,QAAQ;AAAA,EACR,IAAI,KAAK;AACP,WAAO,IAAI,KAAK;AAAA,EAClB;AAAA,EACA;AAAA,EACA,QAAQ;AACV;", "names": ["o", "parser", "lexer", "channel"]}