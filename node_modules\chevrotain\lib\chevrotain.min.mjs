var jr="11.0.3";var Of=typeof global=="object"&&global&&global.Object===Object&&global,$r=Of;var _f=typeof self=="object"&&self&&self.Object===Object&&self,Nf=$r||_f||Function("return this")(),Z=Nf;var Cf=Z.Symbol,nt=Cf;var ai=Object.prototype,vf=ai.hasOwnProperty,Lf=ai.toString,Tr=nt?nt.toStringTag:void 0;function bf(e){var t=vf.call(e,Tr),r=e[Tr];try{e[Tr]=void 0;var n=!0}catch{}var o=Lf.call(e);return n&&(t?e[Tr]=r:delete e[Tr]),o}var si=bf;var Pf=Object.prototype,kf=Pf.toString;function Mf(e){return kf.call(e)}var li=Mf;var wf="[object Null]",Ff="[object Undefined]",fi=nt?nt.toStringTag:void 0;function Uf(e){return e==null?e===void 0?Ff:wf:fi&&fi in Object(e)?si(e):li(e)}var pt=Uf;function Df(e){return e!=null&&typeof e=="object"}var V=Df;var Bf="[object Symbol]";function Gf(e){return typeof e=="symbol"||V(e)&&pt(e)==Bf}var ee=Gf;function Wf(e,t){for(var r=-1,n=e==null?0:e.length,o=Array(n);++r<n;)o[r]=t(e[r],r,e);return o}var Pt=Wf;var Kf=Array.isArray,E=Kf;var jf=1/0,ci=nt?nt.prototype:void 0,ui=ci?ci.toString:void 0;function pi(e){if(typeof e=="string")return e;if(E(e))return Pt(e,pi)+"";if(ee(e))return ui?ui.call(e):"";var t=e+"";return t=="0"&&1/e==-jf?"-0":t}var hi=pi;var $f=/\s/;function Hf(e){for(var t=e.length;t--&&$f.test(e.charAt(t)););return t}var di=Hf;var zf=/^\s+/;function Vf(e){return e&&e.slice(0,di(e)+1).replace(zf,"")}var mi=Vf;function Xf(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var W=Xf;var xi=0/0,Yf=/^[-+]0x[0-9a-f]+$/i,qf=/^0b[01]+$/i,Zf=/^0o[0-7]+$/i,Jf=parseInt;function Qf(e){if(typeof e=="number")return e;if(ee(e))return xi;if(W(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=W(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=mi(e);var r=qf.test(e);return r||Zf.test(e)?Jf(e.slice(2),r?2:8):Yf.test(e)?xi:+e}var gi=Qf;var Ti=1/0,tc=17976931348623157e292;function ec(e){if(!e)return e===0?e:0;if(e=gi(e),e===Ti||e===-Ti){var t=e<0?-1:1;return t*tc}return e===e?e:0}var Ei=ec;function rc(e){var t=Ei(e),r=t%1;return t===t?r?t-r:t:0}var kt=rc;function nc(e){return e}var St=nc;var oc="[object AsyncFunction]",ic="[object Function]",ac="[object GeneratorFunction]",sc="[object Proxy]";function lc(e){if(!W(e))return!1;var t=pt(e);return t==ic||t==ac||t==oc||t==sc}var ht=lc;var fc=Z["__core-js_shared__"],Hr=fc;var Ai=function(){var e=/[^.]+$/.exec(Hr&&Hr.keys&&Hr.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function cc(e){return!!Ai&&Ai in e}var Ii=cc;var uc=Function.prototype,pc=uc.toString;function hc(e){if(e!=null){try{return pc.call(e)}catch{}try{return e+""}catch{}}return""}var Ht=hc;var dc=/[\\^$.*+?()[\]{}|]/g,mc=/^\[object .+?Constructor\]$/,xc=Function.prototype,gc=Object.prototype,Tc=xc.toString,Ec=gc.hasOwnProperty,Ac=RegExp("^"+Tc.call(Ec).replace(dc,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function Ic(e){if(!W(e)||Ii(e))return!1;var t=ht(e)?Ac:mc;return t.test(Ht(e))}var Ri=Ic;function Rc(e,t){return e?.[t]}var yi=Rc;function yc(e,t){var r=yi(e,t);return Ri(r)?r:void 0}var xt=yc;var Sc=xt(Z,"WeakMap"),zr=Sc;var Si=Object.create,Oc=function(){function e(){}return function(t){if(!W(t))return{};if(Si)return Si(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}(),Oi=Oc;function _c(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}var _i=_c;function Nc(){}var K=Nc;function Cc(e,t){var r=-1,n=e.length;for(t||(t=Array(n));++r<n;)t[r]=e[r];return t}var Ni=Cc;var vc=800,Lc=16,bc=Date.now;function Pc(e){var t=0,r=0;return function(){var n=bc(),o=Lc-(n-r);if(r=n,o>0){if(++t>=vc)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}var Ci=Pc;function kc(e){return function(){return e}}var vi=kc;var Mc=function(){try{var e=xt(Object,"defineProperty");return e({},"",{}),e}catch{}}(),be=Mc;var wc=be?function(e,t){return be(e,"toString",{configurable:!0,enumerable:!1,value:vi(t),writable:!0})}:St,Li=wc;var Fc=Ci(Li),bi=Fc;function Uc(e,t){for(var r=-1,n=e==null?0:e.length;++r<n&&t(e[r],r,e)!==!1;);return e}var Vr=Uc;function Dc(e,t,r,n){for(var o=e.length,i=r+(n?1:-1);n?i--:++i<o;)if(t(e[i],i,e))return i;return-1}var Xr=Dc;function Bc(e){return e!==e}var Pi=Bc;function Gc(e,t,r){for(var n=r-1,o=e.length;++n<o;)if(e[n]===t)return n;return-1}var ki=Gc;function Wc(e,t,r){return t===t?ki(e,t,r):Xr(e,Pi,r)}var Pe=Wc;function Kc(e,t){var r=e==null?0:e.length;return!!r&&Pe(e,t,0)>-1}var Yr=Kc;var jc=9007199254740991,$c=/^(?:0|[1-9]\d*)$/;function Hc(e,t){var r=typeof e;return t=t??jc,!!t&&(r=="number"||r!="symbol"&&$c.test(e))&&e>-1&&e%1==0&&e<t}var re=Hc;function zc(e,t,r){t=="__proto__"&&be?be(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}var ke=zc;function Vc(e,t){return e===t||e!==e&&t!==t}var Mt=Vc;var Xc=Object.prototype,Yc=Xc.hasOwnProperty;function qc(e,t,r){var n=e[t];(!(Yc.call(e,t)&&Mt(n,r))||r===void 0&&!(t in e))&&ke(e,t,r)}var ne=qc;function Zc(e,t,r,n){var o=!r;r||(r={});for(var i=-1,a=t.length;++i<a;){var s=t[i],l=n?n(r[s],e[s],s,r,e):void 0;l===void 0&&(l=e[s]),o?ke(r,s,l):ne(r,s,l)}return r}var wt=Zc;var Mi=Math.max;function Jc(e,t,r){return t=Mi(t===void 0?e.length-1:t,0),function(){for(var n=arguments,o=-1,i=Mi(n.length-t,0),a=Array(i);++o<i;)a[o]=n[t+o];o=-1;for(var s=Array(t+1);++o<t;)s[o]=n[o];return s[t]=r(a),_i(e,this,s)}}var wi=Jc;function Qc(e,t){return bi(wi(e,t,St),e+"")}var Me=Qc;var tu=9007199254740991;function eu(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=tu}var we=eu;function ru(e){return e!=null&&we(e.length)&&!ht(e)}var J=ru;function nu(e,t,r){if(!W(r))return!1;var n=typeof t;return(n=="number"?J(r)&&re(t,r.length):n=="string"&&t in r)?Mt(r[t],e):!1}var oe=nu;function ou(e){return Me(function(t,r){var n=-1,o=r.length,i=o>1?r[o-1]:void 0,a=o>2?r[2]:void 0;for(i=e.length>3&&typeof i=="function"?(o--,i):void 0,a&&oe(r[0],r[1],a)&&(i=o<3?void 0:i,o=1),t=Object(t);++n<o;){var s=r[n];s&&e(t,s,n,i)}return t})}var Fi=ou;var iu=Object.prototype;function au(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||iu;return e===r}var Ft=au;function su(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}var Ui=su;var lu="[object Arguments]";function fu(e){return V(e)&&pt(e)==lu}var fo=fu;var Di=Object.prototype,cu=Di.hasOwnProperty,uu=Di.propertyIsEnumerable,pu=fo(function(){return arguments}())?fo:function(e){return V(e)&&cu.call(e,"callee")&&!uu.call(e,"callee")},ie=pu;function hu(){return!1}var Bi=hu;var Ki=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Gi=Ki&&typeof module=="object"&&module&&!module.nodeType&&module,du=Gi&&Gi.exports===Ki,Wi=du?Z.Buffer:void 0,mu=Wi?Wi.isBuffer:void 0,xu=mu||Bi,zt=xu;var gu="[object Arguments]",Tu="[object Array]",Eu="[object Boolean]",Au="[object Date]",Iu="[object Error]",Ru="[object Function]",yu="[object Map]",Su="[object Number]",Ou="[object Object]",_u="[object RegExp]",Nu="[object Set]",Cu="[object String]",vu="[object WeakMap]",Lu="[object ArrayBuffer]",bu="[object DataView]",Pu="[object Float32Array]",ku="[object Float64Array]",Mu="[object Int8Array]",wu="[object Int16Array]",Fu="[object Int32Array]",Uu="[object Uint8Array]",Du="[object Uint8ClampedArray]",Bu="[object Uint16Array]",Gu="[object Uint32Array]",F={};F[Pu]=F[ku]=F[Mu]=F[wu]=F[Fu]=F[Uu]=F[Du]=F[Bu]=F[Gu]=!0;F[gu]=F[Tu]=F[Lu]=F[Eu]=F[bu]=F[Au]=F[Iu]=F[Ru]=F[yu]=F[Su]=F[Ou]=F[_u]=F[Nu]=F[Cu]=F[vu]=!1;function Wu(e){return V(e)&&we(e.length)&&!!F[pt(e)]}var ji=Wu;function Ku(e){return function(t){return e(t)}}var Ut=Ku;var $i=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Er=$i&&typeof module=="object"&&module&&!module.nodeType&&module,ju=Er&&Er.exports===$i,co=ju&&$r.process,$u=function(){try{var e=Er&&Er.require&&Er.require("util").types;return e||co&&co.binding&&co.binding("util")}catch{}}(),Ot=$u;var Hi=Ot&&Ot.isTypedArray,Hu=Hi?Ut(Hi):ji,Fe=Hu;var zu=Object.prototype,Vu=zu.hasOwnProperty;function Xu(e,t){var r=E(e),n=!r&&ie(e),o=!r&&!n&&zt(e),i=!r&&!n&&!o&&Fe(e),a=r||n||o||i,s=a?Ui(e.length,String):[],l=s.length;for(var f in e)(t||Vu.call(e,f))&&!(a&&(f=="length"||o&&(f=="offset"||f=="parent")||i&&(f=="buffer"||f=="byteLength"||f=="byteOffset")||re(f,l)))&&s.push(f);return s}var qr=Xu;function Yu(e,t){return function(r){return e(t(r))}}var Zr=Yu;var qu=Zr(Object.keys,Object),zi=qu;var Zu=Object.prototype,Ju=Zu.hasOwnProperty;function Qu(e){if(!Ft(e))return zi(e);var t=[];for(var r in Object(e))Ju.call(e,r)&&r!="constructor"&&t.push(r);return t}var Jr=Qu;function tp(e){return J(e)?qr(e):Jr(e)}var k=tp;var ep=Object.prototype,rp=ep.hasOwnProperty,np=Fi(function(e,t){if(Ft(t)||J(t)){wt(t,k(t),e);return}for(var r in t)rp.call(t,r)&&ne(e,r,t[r])}),tt=np;function op(e){var t=[];if(e!=null)for(var r in Object(e))t.push(r);return t}var Vi=op;var ip=Object.prototype,ap=ip.hasOwnProperty;function sp(e){if(!W(e))return Vi(e);var t=Ft(e),r=[];for(var n in e)n=="constructor"&&(t||!ap.call(e,n))||r.push(n);return r}var Xi=sp;function lp(e){return J(e)?qr(e,!0):Xi(e)}var ae=lp;var fp=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,cp=/^\w*$/;function up(e,t){if(E(e))return!1;var r=typeof e;return r=="number"||r=="symbol"||r=="boolean"||e==null||ee(e)?!0:cp.test(e)||!fp.test(e)||t!=null&&e in Object(t)}var Ue=up;var pp=xt(Object,"create"),Vt=pp;function hp(){this.__data__=Vt?Vt(null):{},this.size=0}var Yi=hp;function dp(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var qi=dp;var mp="__lodash_hash_undefined__",xp=Object.prototype,gp=xp.hasOwnProperty;function Tp(e){var t=this.__data__;if(Vt){var r=t[e];return r===mp?void 0:r}return gp.call(t,e)?t[e]:void 0}var Zi=Tp;var Ep=Object.prototype,Ap=Ep.hasOwnProperty;function Ip(e){var t=this.__data__;return Vt?t[e]!==void 0:Ap.call(t,e)}var Ji=Ip;var Rp="__lodash_hash_undefined__";function yp(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=Vt&&t===void 0?Rp:t,this}var Qi=yp;function De(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}De.prototype.clear=Yi;De.prototype.delete=qi;De.prototype.get=Zi;De.prototype.has=Ji;De.prototype.set=Qi;var uo=De;function Sp(){this.__data__=[],this.size=0}var ta=Sp;function Op(e,t){for(var r=e.length;r--;)if(Mt(e[r][0],t))return r;return-1}var se=Op;var _p=Array.prototype,Np=_p.splice;function Cp(e){var t=this.__data__,r=se(t,e);if(r<0)return!1;var n=t.length-1;return r==n?t.pop():Np.call(t,r,1),--this.size,!0}var ea=Cp;function vp(e){var t=this.__data__,r=se(t,e);return r<0?void 0:t[r][1]}var ra=vp;function Lp(e){return se(this.__data__,e)>-1}var na=Lp;function bp(e,t){var r=this.__data__,n=se(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this}var oa=bp;function Be(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Be.prototype.clear=ta;Be.prototype.delete=ea;Be.prototype.get=ra;Be.prototype.has=na;Be.prototype.set=oa;var le=Be;var Pp=xt(Z,"Map"),fe=Pp;function kp(){this.size=0,this.__data__={hash:new uo,map:new(fe||le),string:new uo}}var ia=kp;function Mp(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}var aa=Mp;function wp(e,t){var r=e.__data__;return aa(t)?r[typeof t=="string"?"string":"hash"]:r.map}var ce=wp;function Fp(e){var t=ce(this,e).delete(e);return this.size-=t?1:0,t}var sa=Fp;function Up(e){return ce(this,e).get(e)}var la=Up;function Dp(e){return ce(this,e).has(e)}var fa=Dp;function Bp(e,t){var r=ce(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this}var ca=Bp;function Ge(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Ge.prototype.clear=ia;Ge.prototype.delete=sa;Ge.prototype.get=la;Ge.prototype.has=fa;Ge.prototype.set=ca;var Ae=Ge;var Gp="Expected a function";function po(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(Gp);var r=function(){var n=arguments,o=t?t.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=e.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(po.Cache||Ae),r}po.Cache=Ae;var ua=po;var Wp=500;function Kp(e){var t=ua(e,function(n){return r.size===Wp&&r.clear(),n}),r=t.cache;return t}var pa=Kp;var jp=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,$p=/\\(\\)?/g,Hp=pa(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(jp,function(r,n,o,i){t.push(o?i.replace($p,"$1"):n||r)}),t}),ha=Hp;function zp(e){return e==null?"":hi(e)}var Qr=zp;function Vp(e,t){return E(e)?e:Ue(e,t)?[e]:ha(Qr(e))}var ue=Vp;var Xp=1/0;function Yp(e){if(typeof e=="string"||ee(e))return e;var t=e+"";return t=="0"&&1/e==-Xp?"-0":t}var Dt=Yp;function qp(e,t){t=ue(t,e);for(var r=0,n=t.length;e!=null&&r<n;)e=e[Dt(t[r++])];return r&&r==n?e:void 0}var We=qp;function Zp(e,t,r){var n=e==null?void 0:We(e,t);return n===void 0?r:n}var da=Zp;function Jp(e,t){for(var r=-1,n=t.length,o=e.length;++r<n;)e[o+r]=t[r];return e}var Ke=Jp;var ma=nt?nt.isConcatSpreadable:void 0;function Qp(e){return E(e)||ie(e)||!!(ma&&e&&e[ma])}var xa=Qp;function ga(e,t,r,n,o){var i=-1,a=e.length;for(r||(r=xa),o||(o=[]);++i<a;){var s=e[i];t>0&&r(s)?t>1?ga(s,t-1,r,n,o):Ke(o,s):n||(o[o.length]=s)}return o}var je=ga;function th(e){var t=e==null?0:e.length;return t?je(e,1):[]}var j=th;var eh=Zr(Object.getPrototypeOf,Object),tn=eh;function rh(e,t,r){var n=-1,o=e.length;t<0&&(t=-t>o?0:o+t),r=r>o?o:r,r<0&&(r+=o),o=t>r?0:r-t>>>0,t>>>=0;for(var i=Array(o);++n<o;)i[n]=e[n+t];return i}var $e=rh;function nh(e,t,r){var n=e.length;return r=r===void 0?n:r,!t&&r>=n?e:$e(e,t,r)}var Ta=nh;var oh="\\ud800-\\udfff",ih="\\u0300-\\u036f",ah="\\ufe20-\\ufe2f",sh="\\u20d0-\\u20ff",lh=ih+ah+sh,fh="\\ufe0e\\ufe0f",ch="\\u200d",uh=RegExp("["+ch+oh+lh+fh+"]");function ph(e){return uh.test(e)}var en=ph;function hh(e){return e.split("")}var Ea=hh;var Aa="\\ud800-\\udfff",dh="\\u0300-\\u036f",mh="\\ufe20-\\ufe2f",xh="\\u20d0-\\u20ff",gh=dh+mh+xh,Th="\\ufe0e\\ufe0f",Eh="["+Aa+"]",ho="["+gh+"]",mo="\\ud83c[\\udffb-\\udfff]",Ah="(?:"+ho+"|"+mo+")",Ia="[^"+Aa+"]",Ra="(?:\\ud83c[\\udde6-\\uddff]){2}",ya="[\\ud800-\\udbff][\\udc00-\\udfff]",Ih="\\u200d",Sa=Ah+"?",Oa="["+Th+"]?",Rh="(?:"+Ih+"(?:"+[Ia,Ra,ya].join("|")+")"+Oa+Sa+")*",yh=Oa+Sa+Rh,Sh="(?:"+[Ia+ho+"?",ho,Ra,ya,Eh].join("|")+")",Oh=RegExp(mo+"(?="+mo+")|"+Sh+yh,"g");function _h(e){return e.match(Oh)||[]}var _a=_h;function Nh(e){return en(e)?_a(e):Ea(e)}var Na=Nh;function Ch(e){return function(t){t=Qr(t);var r=en(t)?Na(t):void 0,n=r?r[0]:t.charAt(0),o=r?Ta(r,1).join(""):t.slice(1);return n[e]()+o}}var Ca=Ch;var vh=Ca("toUpperCase"),rn=vh;function Lh(e,t,r,n){var o=-1,i=e==null?0:e.length;for(n&&i&&(r=e[++o]);++o<i;)r=t(r,e[o],o,e);return r}var va=Lh;function bh(){this.__data__=new le,this.size=0}var La=bh;function Ph(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}var ba=Ph;function kh(e){return this.__data__.get(e)}var Pa=kh;function Mh(e){return this.__data__.has(e)}var ka=Mh;var wh=200;function Fh(e,t){var r=this.__data__;if(r instanceof le){var n=r.__data__;if(!fe||n.length<wh-1)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new Ae(n)}return r.set(e,t),this.size=r.size,this}var Ma=Fh;function He(e){var t=this.__data__=new le(e);this.size=t.size}He.prototype.clear=La;He.prototype.delete=ba;He.prototype.get=Pa;He.prototype.has=ka;He.prototype.set=Ma;var pe=He;function Uh(e,t){return e&&wt(t,k(t),e)}var wa=Uh;function Dh(e,t){return e&&wt(t,ae(t),e)}var Fa=Dh;var Ga=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Ua=Ga&&typeof module=="object"&&module&&!module.nodeType&&module,Bh=Ua&&Ua.exports===Ga,Da=Bh?Z.Buffer:void 0,Ba=Da?Da.allocUnsafe:void 0;function Gh(e,t){if(t)return e.slice();var r=e.length,n=Ba?Ba(r):new e.constructor(r);return e.copy(n),n}var Wa=Gh;function Wh(e,t){for(var r=-1,n=e==null?0:e.length,o=0,i=[];++r<n;){var a=e[r];t(a,r,e)&&(i[o++]=a)}return i}var ze=Wh;function Kh(){return[]}var nn=Kh;var jh=Object.prototype,$h=jh.propertyIsEnumerable,Ka=Object.getOwnPropertySymbols,Hh=Ka?function(e){return e==null?[]:(e=Object(e),ze(Ka(e),function(t){return $h.call(e,t)}))}:nn,Ve=Hh;function zh(e,t){return wt(e,Ve(e),t)}var ja=zh;var Vh=Object.getOwnPropertySymbols,Xh=Vh?function(e){for(var t=[];e;)Ke(t,Ve(e)),e=tn(e);return t}:nn,on=Xh;function Yh(e,t){return wt(e,on(e),t)}var $a=Yh;function qh(e,t,r){var n=t(e);return E(e)?n:Ke(n,r(e))}var an=qh;function Zh(e){return an(e,k,Ve)}var Ar=Zh;function Jh(e){return an(e,ae,on)}var sn=Jh;var Qh=xt(Z,"DataView"),ln=Qh;var td=xt(Z,"Promise"),fn=td;var ed=xt(Z,"Set"),he=ed;var Ha="[object Map]",rd="[object Object]",za="[object Promise]",Va="[object Set]",Xa="[object WeakMap]",Ya="[object DataView]",nd=Ht(ln),od=Ht(fe),id=Ht(fn),ad=Ht(he),sd=Ht(zr),Ie=pt;(ln&&Ie(new ln(new ArrayBuffer(1)))!=Ya||fe&&Ie(new fe)!=Ha||fn&&Ie(fn.resolve())!=za||he&&Ie(new he)!=Va||zr&&Ie(new zr)!=Xa)&&(Ie=function(e){var t=pt(e),r=t==rd?e.constructor:void 0,n=r?Ht(r):"";if(n)switch(n){case nd:return Ya;case od:return Ha;case id:return za;case ad:return Va;case sd:return Xa}return t});var Ct=Ie;var ld=Object.prototype,fd=ld.hasOwnProperty;function cd(e){var t=e.length,r=new e.constructor(t);return t&&typeof e[0]=="string"&&fd.call(e,"index")&&(r.index=e.index,r.input=e.input),r}var qa=cd;var ud=Z.Uint8Array,Xe=ud;function pd(e){var t=new e.constructor(e.byteLength);return new Xe(t).set(new Xe(e)),t}var Ye=pd;function hd(e,t){var r=t?Ye(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.byteLength)}var Za=hd;var dd=/\w*$/;function md(e){var t=new e.constructor(e.source,dd.exec(e));return t.lastIndex=e.lastIndex,t}var Ja=md;var Qa=nt?nt.prototype:void 0,ts=Qa?Qa.valueOf:void 0;function xd(e){return ts?Object(ts.call(e)):{}}var es=xd;function gd(e,t){var r=t?Ye(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}var rs=gd;var Td="[object Boolean]",Ed="[object Date]",Ad="[object Map]",Id="[object Number]",Rd="[object RegExp]",yd="[object Set]",Sd="[object String]",Od="[object Symbol]",_d="[object ArrayBuffer]",Nd="[object DataView]",Cd="[object Float32Array]",vd="[object Float64Array]",Ld="[object Int8Array]",bd="[object Int16Array]",Pd="[object Int32Array]",kd="[object Uint8Array]",Md="[object Uint8ClampedArray]",wd="[object Uint16Array]",Fd="[object Uint32Array]";function Ud(e,t,r){var n=e.constructor;switch(t){case _d:return Ye(e);case Td:case Ed:return new n(+e);case Nd:return Za(e,r);case Cd:case vd:case Ld:case bd:case Pd:case kd:case Md:case wd:case Fd:return rs(e,r);case Ad:return new n;case Id:case Sd:return new n(e);case Rd:return Ja(e);case yd:return new n;case Od:return es(e)}}var ns=Ud;function Dd(e){return typeof e.constructor=="function"&&!Ft(e)?Oi(tn(e)):{}}var os=Dd;var Bd="[object Map]";function Gd(e){return V(e)&&Ct(e)==Bd}var is=Gd;var as=Ot&&Ot.isMap,Wd=as?Ut(as):is,ss=Wd;var Kd="[object Set]";function jd(e){return V(e)&&Ct(e)==Kd}var ls=jd;var fs=Ot&&Ot.isSet,$d=fs?Ut(fs):ls,cs=$d;var Hd=1,zd=2,Vd=4,us="[object Arguments]",Xd="[object Array]",Yd="[object Boolean]",qd="[object Date]",Zd="[object Error]",ps="[object Function]",Jd="[object GeneratorFunction]",Qd="[object Map]",tm="[object Number]",hs="[object Object]",em="[object RegExp]",rm="[object Set]",nm="[object String]",om="[object Symbol]",im="[object WeakMap]",am="[object ArrayBuffer]",sm="[object DataView]",lm="[object Float32Array]",fm="[object Float64Array]",cm="[object Int8Array]",um="[object Int16Array]",pm="[object Int32Array]",hm="[object Uint8Array]",dm="[object Uint8ClampedArray]",mm="[object Uint16Array]",xm="[object Uint32Array]",M={};M[us]=M[Xd]=M[am]=M[sm]=M[Yd]=M[qd]=M[lm]=M[fm]=M[cm]=M[um]=M[pm]=M[Qd]=M[tm]=M[hs]=M[em]=M[rm]=M[nm]=M[om]=M[hm]=M[dm]=M[mm]=M[xm]=!0;M[Zd]=M[ps]=M[im]=!1;function cn(e,t,r,n,o,i){var a,s=t&Hd,l=t&zd,f=t&Vd;if(r&&(a=o?r(e,n,o,i):r(e)),a!==void 0)return a;if(!W(e))return e;var c=E(e);if(c){if(a=qa(e),!s)return Ni(e,a)}else{var u=Ct(e),p=u==ps||u==Jd;if(zt(e))return Wa(e,s);if(u==hs||u==us||p&&!o){if(a=l||p?{}:os(e),!s)return l?$a(e,Fa(a,e)):ja(e,wa(a,e))}else{if(!M[u])return o?e:{};a=ns(e,u,s)}}i||(i=new pe);var g=i.get(e);if(g)return g;i.set(e,a),cs(e)?e.forEach(function(S){a.add(cn(S,t,r,S,e,i))}):ss(e)&&e.forEach(function(S,A){a.set(A,cn(S,t,r,A,e,i))});var I=f?l?sn:Ar:l?ae:k,R=c?void 0:I(e);return Vr(R||e,function(S,A){R&&(A=S,S=e[A]),ne(a,A,cn(S,t,r,A,e,i))}),a}var ds=cn;var gm=4;function Tm(e){return ds(e,gm)}var b=Tm;function Em(e){for(var t=-1,r=e==null?0:e.length,n=0,o=[];++t<r;){var i=e[t];i&&(o[n++]=i)}return o}var Bt=Em;var Am="__lodash_hash_undefined__";function Im(e){return this.__data__.set(e,Am),this}var ms=Im;function Rm(e){return this.__data__.has(e)}var xs=Rm;function un(e){var t=-1,r=e==null?0:e.length;for(this.__data__=new Ae;++t<r;)this.add(e[t])}un.prototype.add=un.prototype.push=ms;un.prototype.has=xs;var qe=un;function ym(e,t){for(var r=-1,n=e==null?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}var pn=ym;function Sm(e,t){return e.has(t)}var Ze=Sm;var Om=1,_m=2;function Nm(e,t,r,n,o,i){var a=r&Om,s=e.length,l=t.length;if(s!=l&&!(a&&l>s))return!1;var f=i.get(e),c=i.get(t);if(f&&c)return f==t&&c==e;var u=-1,p=!0,g=r&_m?new qe:void 0;for(i.set(e,t),i.set(t,e);++u<s;){var I=e[u],R=t[u];if(n)var S=a?n(R,I,u,t,e,i):n(I,R,u,e,t,i);if(S!==void 0){if(S)continue;p=!1;break}if(g){if(!pn(t,function(A,m){if(!Ze(g,m)&&(I===A||o(I,A,r,n,i)))return g.push(m)})){p=!1;break}}else if(!(I===R||o(I,R,r,n,i))){p=!1;break}}return i.delete(e),i.delete(t),p}var hn=Nm;function Cm(e){var t=-1,r=Array(e.size);return e.forEach(function(n,o){r[++t]=[o,n]}),r}var gs=Cm;function vm(e){var t=-1,r=Array(e.size);return e.forEach(function(n){r[++t]=n}),r}var Je=vm;var Lm=1,bm=2,Pm="[object Boolean]",km="[object Date]",Mm="[object Error]",wm="[object Map]",Fm="[object Number]",Um="[object RegExp]",Dm="[object Set]",Bm="[object String]",Gm="[object Symbol]",Wm="[object ArrayBuffer]",Km="[object DataView]",Ts=nt?nt.prototype:void 0,xo=Ts?Ts.valueOf:void 0;function jm(e,t,r,n,o,i,a){switch(r){case Km:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case Wm:return!(e.byteLength!=t.byteLength||!i(new Xe(e),new Xe(t)));case Pm:case km:case Fm:return Mt(+e,+t);case Mm:return e.name==t.name&&e.message==t.message;case Um:case Bm:return e==t+"";case wm:var s=gs;case Dm:var l=n&Lm;if(s||(s=Je),e.size!=t.size&&!l)return!1;var f=a.get(e);if(f)return f==t;n|=bm,a.set(e,t);var c=hn(s(e),s(t),n,o,i,a);return a.delete(e),c;case Gm:if(xo)return xo.call(e)==xo.call(t)}return!1}var Es=jm;var $m=1,Hm=Object.prototype,zm=Hm.hasOwnProperty;function Vm(e,t,r,n,o,i){var a=r&$m,s=Ar(e),l=s.length,f=Ar(t),c=f.length;if(l!=c&&!a)return!1;for(var u=l;u--;){var p=s[u];if(!(a?p in t:zm.call(t,p)))return!1}var g=i.get(e),I=i.get(t);if(g&&I)return g==t&&I==e;var R=!0;i.set(e,t),i.set(t,e);for(var S=a;++u<l;){p=s[u];var A=e[p],m=t[p];if(n)var h=a?n(m,A,p,t,e,i):n(A,m,p,e,t,i);if(!(h===void 0?A===m||o(A,m,r,n,i):h)){R=!1;break}S||(S=p=="constructor")}if(R&&!S){var O=e.constructor,_=t.constructor;O!=_&&"constructor"in e&&"constructor"in t&&!(typeof O=="function"&&O instanceof O&&typeof _=="function"&&_ instanceof _)&&(R=!1)}return i.delete(e),i.delete(t),R}var As=Vm;var Xm=1,Is="[object Arguments]",Rs="[object Array]",dn="[object Object]",Ym=Object.prototype,ys=Ym.hasOwnProperty;function qm(e,t,r,n,o,i){var a=E(e),s=E(t),l=a?Rs:Ct(e),f=s?Rs:Ct(t);l=l==Is?dn:l,f=f==Is?dn:f;var c=l==dn,u=f==dn,p=l==f;if(p&&zt(e)){if(!zt(t))return!1;a=!0,c=!1}if(p&&!c)return i||(i=new pe),a||Fe(e)?hn(e,t,r,n,o,i):Es(e,t,l,r,n,o,i);if(!(r&Xm)){var g=c&&ys.call(e,"__wrapped__"),I=u&&ys.call(t,"__wrapped__");if(g||I){var R=g?e.value():e,S=I?t.value():t;return i||(i=new pe),o(R,S,r,n,i)}}return p?(i||(i=new pe),As(e,t,r,n,o,i)):!1}var Ss=qm;function Os(e,t,r,n,o){return e===t?!0:e==null||t==null||!V(e)&&!V(t)?e!==e&&t!==t:Ss(e,t,r,n,Os,o)}var mn=Os;var Zm=1,Jm=2;function Qm(e,t,r,n){var o=r.length,i=o,a=!n;if(e==null)return!i;for(e=Object(e);o--;){var s=r[o];if(a&&s[2]?s[1]!==e[s[0]]:!(s[0]in e))return!1}for(;++o<i;){s=r[o];var l=s[0],f=e[l],c=s[1];if(a&&s[2]){if(f===void 0&&!(l in e))return!1}else{var u=new pe;if(n)var p=n(f,c,l,e,t,u);if(!(p===void 0?mn(c,f,Zm|Jm,n,u):p))return!1}}return!0}var _s=Qm;function tx(e){return e===e&&!W(e)}var xn=tx;function ex(e){for(var t=k(e),r=t.length;r--;){var n=t[r],o=e[n];t[r]=[n,o,xn(o)]}return t}var Ns=ex;function rx(e,t){return function(r){return r==null?!1:r[e]===t&&(t!==void 0||e in Object(r))}}var gn=rx;function nx(e){var t=Ns(e);return t.length==1&&t[0][2]?gn(t[0][0],t[0][1]):function(r){return r===e||_s(r,e,t)}}var Cs=nx;function ox(e,t){return e!=null&&t in Object(e)}var vs=ox;function ix(e,t,r){t=ue(t,e);for(var n=-1,o=t.length,i=!1;++n<o;){var a=Dt(t[n]);if(!(i=e!=null&&r(e,a)))break;e=e[a]}return i||++n!=o?i:(o=e==null?0:e.length,!!o&&we(o)&&re(a,o)&&(E(e)||ie(e)))}var Tn=ix;function ax(e,t){return e!=null&&Tn(e,t,vs)}var Ls=ax;var sx=1,lx=2;function fx(e,t){return Ue(e)&&xn(t)?gn(Dt(e),t):function(r){var n=da(r,e);return n===void 0&&n===t?Ls(r,e):mn(t,n,sx|lx)}}var bs=fx;function cx(e){return function(t){return t?.[e]}}var Ps=cx;function ux(e){return function(t){return We(t,e)}}var ks=ux;function px(e){return Ue(e)?Ps(Dt(e)):ks(e)}var Ms=px;function hx(e){return typeof e=="function"?e:e==null?St:typeof e=="object"?E(e)?bs(e[0],e[1]):Cs(e):Ms(e)}var Q=hx;function dx(e,t,r,n){for(var o=-1,i=e==null?0:e.length;++o<i;){var a=e[o];t(n,a,r(a),e)}return n}var ws=dx;function mx(e){return function(t,r,n){for(var o=-1,i=Object(t),a=n(t),s=a.length;s--;){var l=a[e?s:++o];if(r(i[l],l,i)===!1)break}return t}}var Fs=mx;var xx=Fs(),Us=xx;function gx(e,t){return e&&Us(e,t,k)}var Ds=gx;function Tx(e,t){return function(r,n){if(r==null)return r;if(!J(r))return e(r,n);for(var o=r.length,i=t?o:-1,a=Object(r);(t?i--:++i<o)&&n(a[i],i,a)!==!1;);return r}}var Bs=Tx;var Ex=Bs(Ds),gt=Ex;function Ax(e,t,r,n){return gt(e,function(o,i,a){t(n,o,r(o),a)}),n}var Gs=Ax;function Ix(e,t){return function(r,n){var o=E(r)?ws:Gs,i=t?t():{};return o(r,e,Q(n,2),i)}}var Ws=Ix;var Ks=Object.prototype,Rx=Ks.hasOwnProperty,yx=Me(function(e,t){e=Object(e);var r=-1,n=t.length,o=n>2?t[2]:void 0;for(o&&oe(t[0],t[1],o)&&(n=1);++r<n;)for(var i=t[r],a=ae(i),s=-1,l=a.length;++s<l;){var f=a[s],c=e[f];(c===void 0||Mt(c,Ks[f])&&!Rx.call(e,f))&&(e[f]=i[f])}return e}),Qe=yx;function Sx(e){return V(e)&&J(e)}var go=Sx;function Ox(e,t,r){for(var n=-1,o=e==null?0:e.length;++n<o;)if(r(t,e[n]))return!0;return!1}var En=Ox;var _x=200;function Nx(e,t,r,n){var o=-1,i=Yr,a=!0,s=e.length,l=[],f=t.length;if(!s)return l;r&&(t=Pt(t,Ut(r))),n?(i=En,a=!1):t.length>=_x&&(i=Ze,a=!1,t=new qe(t));t:for(;++o<s;){var c=e[o],u=r==null?c:r(c);if(c=n||c!==0?c:0,a&&u===u){for(var p=f;p--;)if(t[p]===u)continue t;l.push(c)}else i(t,u,n)||l.push(c)}return l}var js=Nx;var Cx=Me(function(e,t){return go(e)?js(e,je(t,1,go,!0)):[]}),de=Cx;function vx(e){var t=e==null?0:e.length;return t?e[t-1]:void 0}var Gt=vx;function Lx(e,t,r){var n=e==null?0:e.length;return n?(t=r||t===void 0?1:kt(t),$e(e,t<0?0:t,n)):[]}var X=Lx;function bx(e,t,r){var n=e==null?0:e.length;return n?(t=r||t===void 0?1:kt(t),t=n-t,$e(e,0,t<0?0:t)):[]}var Xt=bx;function Px(e){return typeof e=="function"?e:St}var $s=Px;function kx(e,t){var r=E(e)?Vr:gt;return r(e,$s(t))}var x=kx;function Mx(e,t){for(var r=-1,n=e==null?0:e.length;++r<n;)if(!t(e[r],r,e))return!1;return!0}var Hs=Mx;function wx(e,t){var r=!0;return gt(e,function(n,o,i){return r=!!t(n,o,i),r}),r}var zs=wx;function Fx(e,t,r){var n=E(e)?Hs:zs;return r&&oe(e,t,r)&&(t=void 0),n(e,Q(t,3))}var st=Fx;function Ux(e,t){var r=[];return gt(e,function(n,o,i){t(n,o,i)&&r.push(n)}),r}var An=Ux;function Dx(e,t){var r=E(e)?ze:An;return r(e,Q(t,3))}var lt=Dx;function Bx(e){return function(t,r,n){var o=Object(t);if(!J(t)){var i=Q(r,3);t=k(t),r=function(s){return i(o[s],s,o)}}var a=e(t,r,n);return a>-1?o[i?t[a]:a]:void 0}}var Vs=Bx;var Gx=Math.max;function Wx(e,t,r){var n=e==null?0:e.length;if(!n)return-1;var o=r==null?0:kt(r);return o<0&&(o=Gx(n+o,0)),Xr(e,Q(t,3),o)}var Xs=Wx;var Kx=Vs(Xs),Wt=Kx;function jx(e){return e&&e.length?e[0]:void 0}var ot=jx;function $x(e,t){var r=-1,n=J(e)?Array(e.length):[];return gt(e,function(o,i,a){n[++r]=t(o,i,a)}),n}var Ys=$x;function Hx(e,t){var r=E(e)?Pt:Ys;return r(e,Q(t,3))}var d=Hx;function zx(e,t){return je(d(e,t),1)}var dt=zx;var Vx=Object.prototype,Xx=Vx.hasOwnProperty,Yx=Ws(function(e,t,r){Xx.call(e,r)?e[r].push(t):ke(e,r,[t])}),Ir=Yx;var qx=Object.prototype,Zx=qx.hasOwnProperty;function Jx(e,t){return e!=null&&Zx.call(e,t)}var qs=Jx;function Qx(e,t){return e!=null&&Tn(e,t,qs)}var T=Qx;var tg="[object String]";function eg(e){return typeof e=="string"||!E(e)&&V(e)&&pt(e)==tg}var et=eg;function rg(e,t){return Pt(t,function(r){return e[r]})}var Zs=rg;function ng(e){return e==null?[]:Zs(e,k(e))}var v=ng;var og=Math.max;function ig(e,t,r,n){e=J(e)?e:v(e),r=r&&!n?kt(r):0;var o=e.length;return r<0&&(r=og(o+r,0)),et(e)?r<=o&&e.indexOf(t,r)>-1:!!o&&Pe(e,t,r)>-1}var D=ig;var ag=Math.max;function sg(e,t,r){var n=e==null?0:e.length;if(!n)return-1;var o=r==null?0:kt(r);return o<0&&(o=ag(n+o,0)),Pe(e,t,o)}var In=sg;var lg="[object Map]",fg="[object Set]",cg=Object.prototype,ug=cg.hasOwnProperty;function pg(e){if(e==null)return!0;if(J(e)&&(E(e)||typeof e=="string"||typeof e.splice=="function"||zt(e)||Fe(e)||ie(e)))return!e.length;var t=Ct(e);if(t==lg||t==fg)return!e.size;if(Ft(e))return!Jr(e).length;for(var r in e)if(ug.call(e,r))return!1;return!0}var N=pg;var hg="[object RegExp]";function dg(e){return V(e)&&pt(e)==hg}var Js=dg;var Qs=Ot&&Ot.isRegExp,mg=Qs?Ut(Qs):Js,_t=mg;function xg(e){return e===void 0}var ft=xg;var gg="Expected a function";function Tg(e){if(typeof e!="function")throw new TypeError(gg);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}var tl=Tg;function Eg(e,t,r,n){if(!W(e))return e;t=ue(t,e);for(var o=-1,i=t.length,a=i-1,s=e;s!=null&&++o<i;){var l=Dt(t[o]),f=r;if(l==="__proto__"||l==="constructor"||l==="prototype")return e;if(o!=a){var c=s[l];f=n?n(c,l,s):void 0,f===void 0&&(f=W(c)?c:re(t[o+1])?[]:{})}ne(s,l,f),s=s[l]}return e}var el=Eg;function Ag(e,t,r){for(var n=-1,o=t.length,i={};++n<o;){var a=t[n],s=We(e,a);r(s,a)&&el(i,ue(a,e),s)}return i}var rl=Ag;function Ig(e,t){if(e==null)return{};var r=Pt(sn(e),function(n){return[n]});return t=Q(t),rl(e,r,function(n,o){return t(n,o[0])})}var Tt=Ig;function Rg(e,t,r,n,o){return o(e,function(i,a,s){r=n?(n=!1,i):t(r,i,a,s)}),r}var nl=Rg;function yg(e,t,r){var n=E(e)?va:nl,o=arguments.length<3;return n(e,Q(t,4),r,o,gt)}var $=yg;function Sg(e,t){var r=E(e)?ze:An;return r(e,tl(Q(t,3)))}var me=Sg;function Og(e,t){var r;return gt(e,function(n,o,i){return r=t(n,o,i),!r}),!!r}var ol=Og;function _g(e,t,r){var n=E(e)?pn:ol;return r&&oe(e,t,r)&&(t=void 0),n(e,Q(t,3))}var Re=_g;var Ng=1/0,Cg=he&&1/Je(new he([,-0]))[1]==Ng?function(e){return new he(e)}:K,il=Cg;var vg=200;function Lg(e,t,r){var n=-1,o=Yr,i=e.length,a=!0,s=[],l=s;if(r)a=!1,o=En;else if(i>=vg){var f=t?null:il(e);if(f)return Je(f);a=!1,o=Ze,l=new qe}else l=t?[]:s;t:for(;++n<i;){var c=e[n],u=t?t(c):c;if(c=r||c!==0?c:0,a&&u===u){for(var p=l.length;p--;)if(l[p]===u)continue t;t&&l.push(u),s.push(c)}else o(l,u,r)||(l!==s&&l.push(u),s.push(c))}return s}var al=Lg;function bg(e){return e&&e.length?al(e):[]}var xe=bg;function tr(e){console&&console.error&&console.error(`Error: ${e}`)}function Rr(e){console&&console.warn&&console.warn(`Warning: ${e}`)}function yr(e){let t=new Date().getTime(),r=e();return{time:new Date().getTime()-t,value:r}}function Sr(e){function t(){}t.prototype=e;let r=new t;function n(){return typeof r.bar}return n(),n(),e;(0,eval)(e)}function Pg(e){return kg(e)?e.LABEL:e.name}function kg(e){return et(e.LABEL)&&e.LABEL!==""}var Rt=class{get definition(){return this._definition}set definition(t){this._definition=t}constructor(t){this._definition=t}accept(t){t.visit(this),x(this.definition,r=>{r.accept(t)})}},P=class extends Rt{constructor(t){super([]),this.idx=1,tt(this,Tt(t,r=>r!==void 0))}set definition(t){}get definition(){return this.referencedRule!==void 0?this.referencedRule.definition:[]}accept(t){t.visit(this)}},mt=class extends Rt{constructor(t){super(t.definition),this.orgText="",tt(this,Tt(t,r=>r!==void 0))}},w=class extends Rt{constructor(t){super(t.definition),this.ignoreAmbiguities=!1,tt(this,Tt(t,r=>r!==void 0))}},U=class extends Rt{constructor(t){super(t.definition),this.idx=1,tt(this,Tt(t,r=>r!==void 0))}},Y=class extends Rt{constructor(t){super(t.definition),this.idx=1,tt(this,Tt(t,r=>r!==void 0))}},q=class extends Rt{constructor(t){super(t.definition),this.idx=1,tt(this,Tt(t,r=>r!==void 0))}},L=class extends Rt{constructor(t){super(t.definition),this.idx=1,tt(this,Tt(t,r=>r!==void 0))}},H=class extends Rt{constructor(t){super(t.definition),this.idx=1,tt(this,Tt(t,r=>r!==void 0))}},z=class extends Rt{get definition(){return this._definition}set definition(t){this._definition=t}constructor(t){super(t.definition),this.idx=1,this.ignoreAmbiguities=!1,this.hasPredicates=!1,tt(this,Tt(t,r=>r!==void 0))}},C=class{constructor(t){this.idx=1,tt(this,Tt(t,r=>r!==void 0))}accept(t){t.visit(this)}};function Rn(e){return d(e,er)}function er(e){function t(r){return d(r,er)}if(e instanceof P){let r={type:"NonTerminal",name:e.nonTerminalName,idx:e.idx};return et(e.label)&&(r.label=e.label),r}else{if(e instanceof w)return{type:"Alternative",definition:t(e.definition)};if(e instanceof U)return{type:"Option",idx:e.idx,definition:t(e.definition)};if(e instanceof Y)return{type:"RepetitionMandatory",idx:e.idx,definition:t(e.definition)};if(e instanceof q)return{type:"RepetitionMandatoryWithSeparator",idx:e.idx,separator:er(new C({terminalType:e.separator})),definition:t(e.definition)};if(e instanceof H)return{type:"RepetitionWithSeparator",idx:e.idx,separator:er(new C({terminalType:e.separator})),definition:t(e.definition)};if(e instanceof L)return{type:"Repetition",idx:e.idx,definition:t(e.definition)};if(e instanceof z)return{type:"Alternation",idx:e.idx,definition:t(e.definition)};if(e instanceof C){let r={type:"Terminal",name:e.terminalType.name,label:Pg(e.terminalType),idx:e.idx};et(e.label)&&(r.terminalLabel=e.label);let n=e.terminalType.PATTERN;return e.terminalType.PATTERN&&(r.pattern=_t(n)?n.source:n),r}else{if(e instanceof mt)return{type:"Rule",name:e.name,orgText:e.orgText,definition:t(e.definition)};throw Error("non exhaustive match")}}}var ct=class{visit(t){let r=t;switch(r.constructor){case P:return this.visitNonTerminal(r);case w:return this.visitAlternative(r);case U:return this.visitOption(r);case Y:return this.visitRepetitionMandatory(r);case q:return this.visitRepetitionMandatoryWithSeparator(r);case H:return this.visitRepetitionWithSeparator(r);case L:return this.visitRepetition(r);case z:return this.visitAlternation(r);case C:return this.visitTerminal(r);case mt:return this.visitRule(r);default:throw Error("non exhaustive match")}}visitNonTerminal(t){}visitAlternative(t){}visitOption(t){}visitRepetition(t){}visitRepetitionMandatory(t){}visitRepetitionMandatoryWithSeparator(t){}visitRepetitionWithSeparator(t){}visitAlternation(t){}visitTerminal(t){}visitRule(t){}};function To(e){return e instanceof w||e instanceof U||e instanceof L||e instanceof Y||e instanceof q||e instanceof H||e instanceof C||e instanceof mt}function ye(e,t=[]){return e instanceof U||e instanceof L||e instanceof H?!0:e instanceof z?Re(e.definition,n=>ye(n,t)):e instanceof P&&D(t,e)?!1:e instanceof Rt?(e instanceof P&&t.push(e),st(e.definition,n=>ye(n,t))):!1}function Eo(e){return e instanceof z}function Et(e){if(e instanceof P)return"SUBRULE";if(e instanceof U)return"OPTION";if(e instanceof z)return"OR";if(e instanceof Y)return"AT_LEAST_ONE";if(e instanceof q)return"AT_LEAST_ONE_SEP";if(e instanceof H)return"MANY_SEP";if(e instanceof L)return"MANY";if(e instanceof C)return"CONSUME";throw Error("non exhaustive match")}var Yt=class{walk(t,r=[]){x(t.definition,(n,o)=>{let i=X(t.definition,o+1);if(n instanceof P)this.walkProdRef(n,i,r);else if(n instanceof C)this.walkTerminal(n,i,r);else if(n instanceof w)this.walkFlat(n,i,r);else if(n instanceof U)this.walkOption(n,i,r);else if(n instanceof Y)this.walkAtLeastOne(n,i,r);else if(n instanceof q)this.walkAtLeastOneSep(n,i,r);else if(n instanceof H)this.walkManySep(n,i,r);else if(n instanceof L)this.walkMany(n,i,r);else if(n instanceof z)this.walkOr(n,i,r);else throw Error("non exhaustive match")})}walkTerminal(t,r,n){}walkProdRef(t,r,n){}walkFlat(t,r,n){let o=r.concat(n);this.walk(t,o)}walkOption(t,r,n){let o=r.concat(n);this.walk(t,o)}walkAtLeastOne(t,r,n){let o=[new U({definition:t.definition})].concat(r,n);this.walk(t,o)}walkAtLeastOneSep(t,r,n){let o=sl(t,r,n);this.walk(t,o)}walkMany(t,r,n){let o=[new U({definition:t.definition})].concat(r,n);this.walk(t,o)}walkManySep(t,r,n){let o=sl(t,r,n);this.walk(t,o)}walkOr(t,r,n){let o=r.concat(n);x(t.definition,i=>{let a=new w({definition:[i]});this.walk(a,o)})}};function sl(e,t,r){return[new U({definition:[new C({terminalType:e.separator})].concat(e.definition)})].concat(t,r)}function Se(e){if(e instanceof P)return Se(e.referencedRule);if(e instanceof C)return Fg(e);if(To(e))return Mg(e);if(Eo(e))return wg(e);throw Error("non exhaustive match")}function Mg(e){let t=[],r=e.definition,n=0,o=r.length>n,i,a=!0;for(;o&&a;)i=r[n],a=ye(i),t=t.concat(Se(i)),n=n+1,o=r.length>n;return xe(t)}function wg(e){let t=d(e.definition,r=>Se(r));return xe(j(t))}function Fg(e){return[e.terminalType]}var yn="_~IN~_";var Ao=class extends Yt{constructor(t){super(),this.topProd=t,this.follows={}}startWalking(){return this.walk(this.topProd),this.follows}walkTerminal(t,r,n){}walkProdRef(t,r,n){let o=Ug(t.referencedRule,t.idx)+this.topProd.name,i=r.concat(n),a=new w({definition:i}),s=Se(a);this.follows[o]=s}};function ll(e){let t={};return x(e,r=>{let n=new Ao(r).startWalking();tt(t,n)}),t}function Ug(e,t){return e.name+t+yn}function y(e){return e.charCodeAt(0)}function Sn(e,t){Array.isArray(e)?e.forEach(function(r){t.push(r)}):t.push(e)}function rr(e,t){if(e[t]===!0)throw"duplicate flag "+t;let r=e[t];e[t]=!0}function Oe(e){if(e===void 0)throw Error("Internal Error - Should never get here!");return!0}function Or(){throw Error("Internal Error - Should never get here!")}function Io(e){return e.type==="Character"}var _r=[];for(let e=y("0");e<=y("9");e++)_r.push(e);var Nr=[y("_")].concat(_r);for(let e=y("a");e<=y("z");e++)Nr.push(e);for(let e=y("A");e<=y("Z");e++)Nr.push(e);var Ro=[y(" "),y("\f"),y(`
`),y("\r"),y("	"),y("\v"),y("	"),y("\xA0"),y("\u1680"),y("\u2000"),y("\u2001"),y("\u2002"),y("\u2003"),y("\u2004"),y("\u2005"),y("\u2006"),y("\u2007"),y("\u2008"),y("\u2009"),y("\u200A"),y("\u2028"),y("\u2029"),y("\u202F"),y("\u205F"),y("\u3000"),y("\uFEFF")];var Dg=/[0-9a-fA-F]/,On=/[0-9]/,Bg=/[1-9]/,Cr=class{constructor(){this.idx=0,this.input="",this.groupIdx=0}saveState(){return{idx:this.idx,input:this.input,groupIdx:this.groupIdx}}restoreState(t){this.idx=t.idx,this.input=t.input,this.groupIdx=t.groupIdx}pattern(t){this.idx=0,this.input=t,this.groupIdx=0,this.consumeChar("/");let r=this.disjunction();this.consumeChar("/");let n={type:"Flags",loc:{begin:this.idx,end:t.length},global:!1,ignoreCase:!1,multiLine:!1,unicode:!1,sticky:!1};for(;this.isRegExpFlag();)switch(this.popChar()){case"g":rr(n,"global");break;case"i":rr(n,"ignoreCase");break;case"m":rr(n,"multiLine");break;case"u":rr(n,"unicode");break;case"y":rr(n,"sticky");break}if(this.idx!==this.input.length)throw Error("Redundant input: "+this.input.substring(this.idx));return{type:"Pattern",flags:n,value:r,loc:this.loc(0)}}disjunction(){let t=[],r=this.idx;for(t.push(this.alternative());this.peekChar()==="|";)this.consumeChar("|"),t.push(this.alternative());return{type:"Disjunction",value:t,loc:this.loc(r)}}alternative(){let t=[],r=this.idx;for(;this.isTerm();)t.push(this.term());return{type:"Alternative",value:t,loc:this.loc(r)}}term(){return this.isAssertion()?this.assertion():this.atom()}assertion(){let t=this.idx;switch(this.popChar()){case"^":return{type:"StartAnchor",loc:this.loc(t)};case"$":return{type:"EndAnchor",loc:this.loc(t)};case"\\":switch(this.popChar()){case"b":return{type:"WordBoundary",loc:this.loc(t)};case"B":return{type:"NonWordBoundary",loc:this.loc(t)}}throw Error("Invalid Assertion Escape");case"(":this.consumeChar("?");let r;switch(this.popChar()){case"=":r="Lookahead";break;case"!":r="NegativeLookahead";break}Oe(r);let n=this.disjunction();return this.consumeChar(")"),{type:r,value:n,loc:this.loc(t)}}return Or()}quantifier(t=!1){let r,n=this.idx;switch(this.popChar()){case"*":r={atLeast:0,atMost:1/0};break;case"+":r={atLeast:1,atMost:1/0};break;case"?":r={atLeast:0,atMost:1};break;case"{":let o=this.integerIncludingZero();switch(this.popChar()){case"}":r={atLeast:o,atMost:o};break;case",":let i;this.isDigit()?(i=this.integerIncludingZero(),r={atLeast:o,atMost:i}):r={atLeast:o,atMost:1/0},this.consumeChar("}");break}if(t===!0&&r===void 0)return;Oe(r);break}if(!(t===!0&&r===void 0)&&Oe(r))return this.peekChar(0)==="?"?(this.consumeChar("?"),r.greedy=!1):r.greedy=!0,r.type="Quantifier",r.loc=this.loc(n),r}atom(){let t,r=this.idx;switch(this.peekChar()){case".":t=this.dotAll();break;case"\\":t=this.atomEscape();break;case"[":t=this.characterClass();break;case"(":t=this.group();break}return t===void 0&&this.isPatternCharacter()&&(t=this.patternCharacter()),Oe(t)?(t.loc=this.loc(r),this.isQuantifier()&&(t.quantifier=this.quantifier()),t):Or()}dotAll(){return this.consumeChar("."),{type:"Set",complement:!0,value:[y(`
`),y("\r"),y("\u2028"),y("\u2029")]}}atomEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":return this.decimalEscapeAtom();case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}decimalEscapeAtom(){return{type:"GroupBackReference",value:this.positiveInteger()}}characterClassEscape(){let t,r=!1;switch(this.popChar()){case"d":t=_r;break;case"D":t=_r,r=!0;break;case"s":t=Ro;break;case"S":t=Ro,r=!0;break;case"w":t=Nr;break;case"W":t=Nr,r=!0;break}return Oe(t)?{type:"Set",value:t,complement:r}:Or()}controlEscapeAtom(){let t;switch(this.popChar()){case"f":t=y("\f");break;case"n":t=y(`
`);break;case"r":t=y("\r");break;case"t":t=y("	");break;case"v":t=y("\v");break}return Oe(t)?{type:"Character",value:t}:Or()}controlLetterEscapeAtom(){this.consumeChar("c");let t=this.popChar();if(/[a-zA-Z]/.test(t)===!1)throw Error("Invalid ");return{type:"Character",value:t.toUpperCase().charCodeAt(0)-64}}nulCharacterAtom(){return this.consumeChar("0"),{type:"Character",value:y("\0")}}hexEscapeSequenceAtom(){return this.consumeChar("x"),this.parseHexDigits(2)}regExpUnicodeEscapeSequenceAtom(){return this.consumeChar("u"),this.parseHexDigits(4)}identityEscapeAtom(){let t=this.popChar();return{type:"Character",value:y(t)}}classPatternCharacterAtom(){switch(this.peekChar()){case`
`:case"\r":case"\u2028":case"\u2029":case"\\":case"]":throw Error("TBD");default:let t=this.popChar();return{type:"Character",value:y(t)}}}characterClass(){let t=[],r=!1;for(this.consumeChar("["),this.peekChar(0)==="^"&&(this.consumeChar("^"),r=!0);this.isClassAtom();){let n=this.classAtom(),o=n.type==="Character";if(Io(n)&&this.isRangeDash()){this.consumeChar("-");let i=this.classAtom(),a=i.type==="Character";if(Io(i)){if(i.value<n.value)throw Error("Range out of order in character class");t.push({from:n.value,to:i.value})}else Sn(n.value,t),t.push(y("-")),Sn(i.value,t)}else Sn(n.value,t)}return this.consumeChar("]"),{type:"Set",complement:r,value:t}}classAtom(){switch(this.peekChar()){case"]":case`
`:case"\r":case"\u2028":case"\u2029":throw Error("TBD");case"\\":return this.classEscape();default:return this.classPatternCharacterAtom()}}classEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"b":return this.consumeChar("b"),{type:"Character",value:y("\b")};case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}group(){let t=!0;switch(this.consumeChar("("),this.peekChar(0)){case"?":this.consumeChar("?"),this.consumeChar(":"),t=!1;break;default:this.groupIdx++;break}let r=this.disjunction();this.consumeChar(")");let n={type:"Group",capturing:t,value:r};return t&&(n.idx=this.groupIdx),n}positiveInteger(){let t=this.popChar();if(Bg.test(t)===!1)throw Error("Expecting a positive integer");for(;On.test(this.peekChar(0));)t+=this.popChar();return parseInt(t,10)}integerIncludingZero(){let t=this.popChar();if(On.test(t)===!1)throw Error("Expecting an integer");for(;On.test(this.peekChar(0));)t+=this.popChar();return parseInt(t,10)}patternCharacter(){let t=this.popChar();switch(t){case`
`:case"\r":case"\u2028":case"\u2029":case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":throw Error("TBD");default:return{type:"Character",value:y(t)}}}isRegExpFlag(){switch(this.peekChar(0)){case"g":case"i":case"m":case"u":case"y":return!0;default:return!1}}isRangeDash(){return this.peekChar()==="-"&&this.isClassAtom(1)}isDigit(){return On.test(this.peekChar(0))}isClassAtom(t=0){switch(this.peekChar(t)){case"]":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}isTerm(){return this.isAtom()||this.isAssertion()}isAtom(){if(this.isPatternCharacter())return!0;switch(this.peekChar(0)){case".":case"\\":case"[":case"(":return!0;default:return!1}}isAssertion(){switch(this.peekChar(0)){case"^":case"$":return!0;case"\\":switch(this.peekChar(1)){case"b":case"B":return!0;default:return!1}case"(":return this.peekChar(1)==="?"&&(this.peekChar(2)==="="||this.peekChar(2)==="!");default:return!1}}isQuantifier(){let t=this.saveState();try{return this.quantifier(!0)!==void 0}catch{return!1}finally{this.restoreState(t)}}isPatternCharacter(){switch(this.peekChar()){case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":case"/":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}parseHexDigits(t){let r="";for(let o=0;o<t;o++){let i=this.popChar();if(Dg.test(i)===!1)throw Error("Expecting a HexDecimal digits");r+=i}return{type:"Character",value:parseInt(r,16)}}peekChar(t=0){return this.input[this.idx+t]}popChar(){let t=this.peekChar(0);return this.consumeChar(void 0),t}consumeChar(t){if(t!==void 0&&this.input[this.idx]!==t)throw Error("Expected: '"+t+"' but found: '"+this.input[this.idx]+"' at offset: "+this.idx);if(this.idx>=this.input.length)throw Error("Unexpected end of input");this.idx++}loc(t){return{begin:t,end:this.idx}}};var ge=class{visitChildren(t){for(let r in t){let n=t[r];t.hasOwnProperty(r)&&(n.type!==void 0?this.visit(n):Array.isArray(n)&&n.forEach(o=>{this.visit(o)},this))}}visit(t){switch(t.type){case"Pattern":this.visitPattern(t);break;case"Flags":this.visitFlags(t);break;case"Disjunction":this.visitDisjunction(t);break;case"Alternative":this.visitAlternative(t);break;case"StartAnchor":this.visitStartAnchor(t);break;case"EndAnchor":this.visitEndAnchor(t);break;case"WordBoundary":this.visitWordBoundary(t);break;case"NonWordBoundary":this.visitNonWordBoundary(t);break;case"Lookahead":this.visitLookahead(t);break;case"NegativeLookahead":this.visitNegativeLookahead(t);break;case"Character":this.visitCharacter(t);break;case"Set":this.visitSet(t);break;case"Group":this.visitGroup(t);break;case"GroupBackReference":this.visitGroupBackReference(t);break;case"Quantifier":this.visitQuantifier(t);break}this.visitChildren(t)}visitPattern(t){}visitFlags(t){}visitDisjunction(t){}visitAlternative(t){}visitStartAnchor(t){}visitEndAnchor(t){}visitWordBoundary(t){}visitNonWordBoundary(t){}visitLookahead(t){}visitNegativeLookahead(t){}visitCharacter(t){}visitSet(t){}visitGroup(t){}visitGroupBackReference(t){}visitQuantifier(t){}};var _n={},Gg=new Cr;function nr(e){let t=e.toString();if(_n.hasOwnProperty(t))return _n[t];{let r=Gg.pattern(t);return _n[t]=r,r}}function fl(){_n={}}var ul="Complement Sets are not supported for first char optimization",vr=`Unable to use "first char" lexer optimizations:
`;function pl(e,t=!1){try{let r=nr(e);return yo(r.value,{},r.flags.ignoreCase)}catch(r){if(r.message===ul)t&&Rr(`${vr}	Unable to optimize: < ${e.toString()} >
	Complement Sets cannot be automatically optimized.
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#COMPLEMENT for details.`);else{let n="";t&&(n=`
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#REGEXP_PARSING for details.`),tr(`${vr}
	Failed parsing: < ${e.toString()} >
	Using the @chevrotain/regexp-to-ast library
	Please open an issue at: https://github.com/chevrotain/chevrotain/issues`+n)}}return[]}function yo(e,t,r){switch(e.type){case"Disjunction":for(let o=0;o<e.value.length;o++)yo(e.value[o],t,r);break;case"Alternative":let n=e.value;for(let o=0;o<n.length;o++){let i=n[o];switch(i.type){case"EndAnchor":case"GroupBackReference":case"Lookahead":case"NegativeLookahead":case"StartAnchor":case"WordBoundary":case"NonWordBoundary":continue}let a=i;switch(a.type){case"Character":Nn(a.value,t,r);break;case"Set":if(a.complement===!0)throw Error(ul);x(a.value,l=>{if(typeof l=="number")Nn(l,t,r);else{let f=l;if(r===!0)for(let c=f.from;c<=f.to;c++)Nn(c,t,r);else{for(let c=f.from;c<=f.to&&c<or;c++)Nn(c,t,r);if(f.to>=or){let c=f.from>=or?f.from:or,u=f.to,p=Kt(c),g=Kt(u);for(let I=p;I<=g;I++)t[I]=I}}}});break;case"Group":yo(a.value,t,r);break;default:throw Error("Non Exhaustive Match")}let s=a.quantifier!==void 0&&a.quantifier.atLeast===0;if(a.type==="Group"&&So(a)===!1||a.type!=="Group"&&s===!1)break}break;default:throw Error("non exhaustive match!")}return v(t)}function Nn(e,t,r){let n=Kt(e);t[n]=n,r===!0&&Wg(e,t)}function Wg(e,t){let r=String.fromCharCode(e),n=r.toUpperCase();if(n!==r){let o=Kt(n.charCodeAt(0));t[o]=o}else{let o=r.toLowerCase();if(o!==r){let i=Kt(o.charCodeAt(0));t[i]=i}}}function cl(e,t){return Wt(e.value,r=>{if(typeof r=="number")return D(t,r);{let n=r;return Wt(t,o=>n.from<=o&&o<=n.to)!==void 0}})}function So(e){let t=e.quantifier;return t&&t.atLeast===0?!0:e.value?E(e.value)?st(e.value,So):So(e.value):!1}var Oo=class extends ge{constructor(t){super(),this.targetCharCodes=t,this.found=!1}visitChildren(t){if(this.found!==!0){switch(t.type){case"Lookahead":this.visitLookahead(t);return;case"NegativeLookahead":this.visitNegativeLookahead(t);return}super.visitChildren(t)}}visitCharacter(t){D(this.targetCharCodes,t.value)&&(this.found=!0)}visitSet(t){t.complement?cl(t,this.targetCharCodes)===void 0&&(this.found=!0):cl(t,this.targetCharCodes)!==void 0&&(this.found=!0)}};function Cn(e,t){if(t instanceof RegExp){let r=nr(t),n=new Oo(e);return n.visit(r),n.found}else return Wt(t,r=>D(e,r.charCodeAt(0)))!==void 0}var _e="PATTERN",ir="defaultMode",vn="modes",No=typeof new RegExp("(?:)").sticky=="boolean";function ml(e,t){t=Qe(t,{useSticky:No,debug:!1,safeMode:!1,positionTracking:"full",lineTerminatorCharacters:["\r",`
`],tracer:(m,h)=>h()});let r=t.tracer;r("initCharCodeToOptimizedIndexMap",()=>{iT()});let n;r("Reject Lexer.NA",()=>{n=me(e,m=>m[_e]===it.NA)});let o=!1,i;r("Transform Patterns",()=>{o=!1,i=d(n,m=>{let h=m[_e];if(_t(h)){let O=h.source;return O.length===1&&O!=="^"&&O!=="$"&&O!=="."&&!h.ignoreCase?O:O.length===2&&O[0]==="\\"&&!D(["d","D","s","S","t","r","n","t","0","c","b","B","f","v","w","W"],O[1])?O[1]:t.useSticky?dl(h):hl(h)}else{if(ht(h))return o=!0,{exec:h};if(typeof h=="object")return o=!0,h;if(typeof h=="string"){if(h.length===1)return h;{let O=h.replace(/[\\^$.*+?()[\]{}|]/g,"\\$&"),_=new RegExp(O);return t.useSticky?dl(_):hl(_)}}else throw Error("non exhaustive match")}})});let a,s,l,f,c;r("misc mapping",()=>{a=d(n,m=>m.tokenTypeIdx),s=d(n,m=>{let h=m.GROUP;if(h!==it.SKIPPED){if(et(h))return h;if(ft(h))return!1;throw Error("non exhaustive match")}}),l=d(n,m=>{let h=m.LONGER_ALT;if(h)return E(h)?d(h,_=>In(n,_)):[In(n,h)]}),f=d(n,m=>m.PUSH_MODE),c=d(n,m=>T(m,"POP_MODE"))});let u;r("Line Terminator Handling",()=>{let m=yl(t.lineTerminatorCharacters);u=d(n,h=>!1),t.positionTracking!=="onlyOffset"&&(u=d(n,h=>T(h,"LINE_BREAKS")?!!h.LINE_BREAKS:Rl(h,m)===!1&&Cn(m,h.PATTERN)))});let p,g,I,R;r("Misc Mapping #2",()=>{p=d(n,Al),g=d(i,nT),I=$(n,(m,h)=>{let O=h.GROUP;return et(O)&&O!==it.SKIPPED&&(m[O]=[]),m},{}),R=d(i,(m,h)=>({pattern:i[h],longerAlt:l[h],canLineTerminator:u[h],isCustom:p[h],short:g[h],group:s[h],push:f[h],pop:c[h],tokenTypeIdx:a[h],tokenType:n[h]}))});let S=!0,A=[];return t.safeMode||r("First Char Optimization",()=>{A=$(n,(m,h,O)=>{if(typeof h.PATTERN=="string"){let _=h.PATTERN.charCodeAt(0),At=Kt(_);_o(m,At,R[O])}else if(E(h.START_CHARS_HINT)){let _;x(h.START_CHARS_HINT,At=>{let io=typeof At=="string"?At.charCodeAt(0):At,bt=Kt(io);_!==bt&&(_=bt,_o(m,bt,R[O]))})}else if(_t(h.PATTERN))if(h.PATTERN.unicode)S=!1,t.ensureOptimizations&&tr(`${vr}	Unable to analyze < ${h.PATTERN.toString()} > pattern.
	The regexp unicode flag is not currently supported by the regexp-to-ast library.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNICODE_OPTIMIZE`);else{let _=pl(h.PATTERN,t.ensureOptimizations);N(_)&&(S=!1),x(_,At=>{_o(m,At,R[O])})}else t.ensureOptimizations&&tr(`${vr}	TokenType: <${h.name}> is using a custom token pattern without providing <start_chars_hint> parameter.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_OPTIMIZE`),S=!1;return m},[])}),{emptyGroups:I,patternIdxToConfig:R,charCodeToPatternIdxToConfig:A,hasCustom:o,canBeOptimized:S}}function xl(e,t){let r=[],n=jg(e);r=r.concat(n.errors);let o=$g(n.valid),i=o.valid;return r=r.concat(o.errors),r=r.concat(Kg(i)),r=r.concat(Jg(i)),r=r.concat(Qg(i,t)),r=r.concat(tT(i)),r}function Kg(e){let t=[],r=lt(e,n=>_t(n[_e]));return t=t.concat(zg(r)),t=t.concat(Yg(r)),t=t.concat(qg(r)),t=t.concat(Zg(r)),t=t.concat(Vg(r)),t}function jg(e){let t=lt(e,o=>!T(o,_e)),r=d(t,o=>({message:"Token Type: ->"+o.name+"<- missing static 'PATTERN' property",type:B.MISSING_PATTERN,tokenTypes:[o]})),n=de(e,t);return{errors:r,valid:n}}function $g(e){let t=lt(e,o=>{let i=o[_e];return!_t(i)&&!ht(i)&&!T(i,"exec")&&!et(i)}),r=d(t,o=>({message:"Token Type: ->"+o.name+"<- static 'PATTERN' can only be a RegExp, a Function matching the {CustomPatternMatcherFunc} type or an Object matching the {ICustomPattern} interface.",type:B.INVALID_PATTERN,tokenTypes:[o]})),n=de(e,t);return{errors:r,valid:n}}var Hg=/[^\\][$]/;function zg(e){class t extends ge{constructor(){super(...arguments),this.found=!1}visitEndAnchor(i){this.found=!0}}let r=lt(e,o=>{let i=o.PATTERN;try{let a=nr(i),s=new t;return s.visit(a),s.found}catch{return Hg.test(i.source)}});return d(r,o=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+o.name+`<- static 'PATTERN' cannot contain end of input anchor '$'
	See chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:B.EOI_ANCHOR_FOUND,tokenTypes:[o]}))}function Vg(e){let t=lt(e,n=>n.PATTERN.test(""));return d(t,n=>({message:"Token Type: ->"+n.name+"<- static 'PATTERN' must not match an empty string",type:B.EMPTY_MATCH_PATTERN,tokenTypes:[n]}))}var Xg=/[^\\[][\^]|^\^/;function Yg(e){class t extends ge{constructor(){super(...arguments),this.found=!1}visitStartAnchor(i){this.found=!0}}let r=lt(e,o=>{let i=o.PATTERN;try{let a=nr(i),s=new t;return s.visit(a),s.found}catch{return Xg.test(i.source)}});return d(r,o=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+o.name+`<- static 'PATTERN' cannot contain start of input anchor '^'
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:B.SOI_ANCHOR_FOUND,tokenTypes:[o]}))}function qg(e){let t=lt(e,n=>{let o=n[_e];return o instanceof RegExp&&(o.multiline||o.global)});return d(t,n=>({message:"Token Type: ->"+n.name+"<- static 'PATTERN' may NOT contain global('g') or multiline('m')",type:B.UNSUPPORTED_FLAGS_FOUND,tokenTypes:[n]}))}function Zg(e){let t=[],r=d(e,i=>$(e,(a,s)=>(i.PATTERN.source===s.PATTERN.source&&!D(t,s)&&s.PATTERN!==it.NA&&(t.push(s),a.push(s)),a),[]));r=Bt(r);let n=lt(r,i=>i.length>1);return d(n,i=>{let a=d(i,l=>l.name);return{message:`The same RegExp pattern ->${ot(i).PATTERN}<-has been used in all of the following Token Types: ${a.join(", ")} <-`,type:B.DUPLICATE_PATTERNS_FOUND,tokenTypes:i}})}function Jg(e){let t=lt(e,n=>{if(!T(n,"GROUP"))return!1;let o=n.GROUP;return o!==it.SKIPPED&&o!==it.NA&&!et(o)});return d(t,n=>({message:"Token Type: ->"+n.name+"<- static 'GROUP' can only be Lexer.SKIPPED/Lexer.NA/A String",type:B.INVALID_GROUP_TYPE_FOUND,tokenTypes:[n]}))}function Qg(e,t){let r=lt(e,o=>o.PUSH_MODE!==void 0&&!D(t,o.PUSH_MODE));return d(r,o=>({message:`Token Type: ->${o.name}<- static 'PUSH_MODE' value cannot refer to a Lexer Mode ->${o.PUSH_MODE}<-which does not exist`,type:B.PUSH_MODE_DOES_NOT_EXIST,tokenTypes:[o]}))}function tT(e){let t=[],r=$(e,(n,o,i)=>{let a=o.PATTERN;return a===it.NA||(et(a)?n.push({str:a,idx:i,tokenType:o}):_t(a)&&rT(a)&&n.push({str:a.source,idx:i,tokenType:o})),n},[]);return x(e,(n,o)=>{x(r,({str:i,idx:a,tokenType:s})=>{if(o<a&&eT(i,n.PATTERN)){let l=`Token: ->${s.name}<- can never be matched.
Because it appears AFTER the Token Type ->${n.name}<-in the lexer's definition.
See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNREACHABLE`;t.push({message:l,type:B.UNREACHABLE_PATTERN,tokenTypes:[n,s]})}})}),t}function eT(e,t){if(_t(t)){let r=t.exec(e);return r!==null&&r.index===0}else{if(ht(t))return t(e,0,[],{});if(T(t,"exec"))return t.exec(e,0,[],{});if(typeof t=="string")return t===e;throw Error("non exhaustive match")}}function rT(e){return Wt([".","\\","[","]","|","^","$","(",")","?","*","+","{"],r=>e.source.indexOf(r)!==-1)===void 0}function hl(e){let t=e.ignoreCase?"i":"";return new RegExp(`^(?:${e.source})`,t)}function dl(e){let t=e.ignoreCase?"iy":"y";return new RegExp(`${e.source}`,t)}function gl(e,t,r){let n=[];return T(e,ir)||n.push({message:"A MultiMode Lexer cannot be initialized without a <"+ir+`> property in its definition
`,type:B.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE}),T(e,vn)||n.push({message:"A MultiMode Lexer cannot be initialized without a <"+vn+`> property in its definition
`,type:B.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY}),T(e,vn)&&T(e,ir)&&!T(e.modes,e.defaultMode)&&n.push({message:`A MultiMode Lexer cannot be initialized with a ${ir}: <${e.defaultMode}>which does not exist
`,type:B.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST}),T(e,vn)&&x(e.modes,(o,i)=>{x(o,(a,s)=>{if(ft(a))n.push({message:`A Lexer cannot be initialized using an undefined Token Type. Mode:<${i}> at index: <${s}>
`,type:B.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED});else if(T(a,"LONGER_ALT")){let l=E(a.LONGER_ALT)?a.LONGER_ALT:[a.LONGER_ALT];x(l,f=>{!ft(f)&&!D(o,f)&&n.push({message:`A MultiMode Lexer cannot be initialized with a longer_alt <${f.name}> on token <${a.name}> outside of mode <${i}>
`,type:B.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE})})}})}),n}function Tl(e,t,r){let n=[],o=!1,i=Bt(j(v(e.modes))),a=me(i,l=>l[_e]===it.NA),s=yl(r);return t&&x(a,l=>{let f=Rl(l,s);if(f!==!1){let u={message:oT(l,f),type:f.issue,tokenType:l};n.push(u)}else T(l,"LINE_BREAKS")?l.LINE_BREAKS===!0&&(o=!0):Cn(s,l.PATTERN)&&(o=!0)}),t&&!o&&n.push({message:`Warning: No LINE_BREAKS Found.
	This Lexer has been defined to track line and column information,
	But none of the Token Types can be identified as matching a line terminator.
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#LINE_BREAKS 
	for details.`,type:B.NO_LINE_BREAKS_FLAGS}),n}function El(e){let t={},r=k(e);return x(r,n=>{let o=e[n];if(E(o))t[n]=[];else throw Error("non exhaustive match")}),t}function Al(e){let t=e.PATTERN;if(_t(t))return!1;if(ht(t))return!0;if(T(t,"exec"))return!0;if(et(t))return!1;throw Error("non exhaustive match")}function nT(e){return et(e)&&e.length===1?e.charCodeAt(0):!1}var Il={test:function(e){let t=e.length;for(let r=this.lastIndex;r<t;r++){let n=e.charCodeAt(r);if(n===10)return this.lastIndex=r+1,!0;if(n===13)return e.charCodeAt(r+1)===10?this.lastIndex=r+2:this.lastIndex=r+1,!0}return!1},lastIndex:0};function Rl(e,t){if(T(e,"LINE_BREAKS"))return!1;if(_t(e.PATTERN)){try{Cn(t,e.PATTERN)}catch(r){return{issue:B.IDENTIFY_TERMINATOR,errMsg:r.message}}return!1}else{if(et(e.PATTERN))return!1;if(Al(e))return{issue:B.CUSTOM_LINE_BREAK};throw Error("non exhaustive match")}}function oT(e,t){if(t.issue===B.IDENTIFY_TERMINATOR)return`Warning: unable to identify line terminator usage in pattern.
	The problem is in the <${e.name}> Token Type
	 Root cause: ${t.errMsg}.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#IDENTIFY_TERMINATOR`;if(t.issue===B.CUSTOM_LINE_BREAK)return`Warning: A Custom Token Pattern should specify the <line_breaks> option.
	The problem is in the <${e.name}> Token Type
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_LINE_BREAK`;throw Error("non exhaustive match")}function yl(e){return d(e,r=>et(r)?r.charCodeAt(0):r)}function _o(e,t,r){e[t]===void 0?e[t]=[r]:e[t].push(r)}var or=256,Ln=[];function Kt(e){return e<or?e:Ln[e]}function iT(){if(N(Ln)){Ln=new Array(65536);for(let e=0;e<65536;e++)Ln[e]=e>255?255+~~(e/255):e}}function qt(e,t){let r=e.tokenTypeIdx;return r===t.tokenTypeIdx?!0:t.isParent===!0&&t.categoryMatchesMap[r]===!0}function ar(e,t){return e.tokenTypeIdx===t.tokenTypeIdx}var Sl=1,_l={};function Zt(e){let t=aT(e);sT(t),fT(t),lT(t),x(t,r=>{r.isParent=r.categoryMatches.length>0})}function aT(e){let t=b(e),r=e,n=!0;for(;n;){r=Bt(j(d(r,i=>i.CATEGORIES)));let o=de(r,t);t=t.concat(o),N(o)?n=!1:r=o}return t}function sT(e){x(e,t=>{Co(t)||(_l[Sl]=t,t.tokenTypeIdx=Sl++),Ol(t)&&!E(t.CATEGORIES)&&(t.CATEGORIES=[t.CATEGORIES]),Ol(t)||(t.CATEGORIES=[]),cT(t)||(t.categoryMatches=[]),uT(t)||(t.categoryMatchesMap={})})}function lT(e){x(e,t=>{t.categoryMatches=[],x(t.categoryMatchesMap,(r,n)=>{t.categoryMatches.push(_l[n].tokenTypeIdx)})})}function fT(e){x(e,t=>{Nl([],t)})}function Nl(e,t){x(e,r=>{t.categoryMatchesMap[r.tokenTypeIdx]=!0}),x(t.CATEGORIES,r=>{let n=e.concat(t);D(n,r)||Nl(n,r)})}function Co(e){return T(e,"tokenTypeIdx")}function Ol(e){return T(e,"CATEGORIES")}function cT(e){return T(e,"categoryMatches")}function uT(e){return T(e,"categoryMatchesMap")}function Cl(e){return T(e,"tokenTypeIdx")}var vo={buildUnableToPopLexerModeMessage(e){return`Unable to pop Lexer Mode after encountering Token ->${e.image}<- The Mode Stack is empty`},buildUnexpectedCharactersMessage(e,t,r,n,o){return`unexpected character: ->${e.charAt(t)}<- at offset: ${t}, skipped ${r} characters.`}};var B;(function(e){e[e.MISSING_PATTERN=0]="MISSING_PATTERN",e[e.INVALID_PATTERN=1]="INVALID_PATTERN",e[e.EOI_ANCHOR_FOUND=2]="EOI_ANCHOR_FOUND",e[e.UNSUPPORTED_FLAGS_FOUND=3]="UNSUPPORTED_FLAGS_FOUND",e[e.DUPLICATE_PATTERNS_FOUND=4]="DUPLICATE_PATTERNS_FOUND",e[e.INVALID_GROUP_TYPE_FOUND=5]="INVALID_GROUP_TYPE_FOUND",e[e.PUSH_MODE_DOES_NOT_EXIST=6]="PUSH_MODE_DOES_NOT_EXIST",e[e.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE=7]="MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE",e[e.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY=8]="MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY",e[e.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST=9]="MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST",e[e.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED=10]="LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED",e[e.SOI_ANCHOR_FOUND=11]="SOI_ANCHOR_FOUND",e[e.EMPTY_MATCH_PATTERN=12]="EMPTY_MATCH_PATTERN",e[e.NO_LINE_BREAKS_FLAGS=13]="NO_LINE_BREAKS_FLAGS",e[e.UNREACHABLE_PATTERN=14]="UNREACHABLE_PATTERN",e[e.IDENTIFY_TERMINATOR=15]="IDENTIFY_TERMINATOR",e[e.CUSTOM_LINE_BREAK=16]="CUSTOM_LINE_BREAK",e[e.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE=17]="MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE"})(B||(B={}));var Lr={deferDefinitionErrorsHandling:!1,positionTracking:"full",lineTerminatorsPattern:/\n|\r\n?/g,lineTerminatorCharacters:[`
`,"\r"],ensureOptimizations:!1,safeMode:!1,errorMessageProvider:vo,traceInitPerf:!1,skipValidations:!1,recoveryEnabled:!0};Object.freeze(Lr);var it=class{constructor(t,r=Lr){if(this.lexerDefinition=t,this.lexerDefinitionErrors=[],this.lexerDefinitionWarning=[],this.patternIdxToConfig={},this.charCodeToPatternIdxToConfig={},this.modes=[],this.emptyGroups={},this.trackStartLines=!0,this.trackEndLines=!0,this.hasCustom=!1,this.canModeBeOptimized={},this.TRACE_INIT=(o,i)=>{if(this.traceInitPerf===!0){this.traceInitIndent++;let a=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${a}--> <${o}>`);let{time:s,value:l}=yr(i),f=s>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&f(`${a}<-- <${o}> time: ${s}ms`),this.traceInitIndent--,l}else return i()},typeof r=="boolean")throw Error(`The second argument to the Lexer constructor is now an ILexerConfig Object.
a boolean 2nd argument is no longer supported`);this.config=tt({},Lr,r);let n=this.config.traceInitPerf;n===!0?(this.traceInitMaxIdent=1/0,this.traceInitPerf=!0):typeof n=="number"&&(this.traceInitMaxIdent=n,this.traceInitPerf=!0),this.traceInitIndent=-1,this.TRACE_INIT("Lexer Constructor",()=>{let o,i=!0;this.TRACE_INIT("Lexer Config handling",()=>{if(this.config.lineTerminatorsPattern===Lr.lineTerminatorsPattern)this.config.lineTerminatorsPattern=Il;else if(this.config.lineTerminatorCharacters===Lr.lineTerminatorCharacters)throw Error(`Error: Missing <lineTerminatorCharacters> property on the Lexer config.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#MISSING_LINE_TERM_CHARS`);if(r.safeMode&&r.ensureOptimizations)throw Error('"safeMode" and "ensureOptimizations" flags are mutually exclusive.');this.trackStartLines=/full|onlyStart/i.test(this.config.positionTracking),this.trackEndLines=/full/i.test(this.config.positionTracking),E(t)?o={modes:{defaultMode:b(t)},defaultMode:ir}:(i=!1,o=b(t))}),this.config.skipValidations===!1&&(this.TRACE_INIT("performRuntimeChecks",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(gl(o,this.trackStartLines,this.config.lineTerminatorCharacters))}),this.TRACE_INIT("performWarningRuntimeChecks",()=>{this.lexerDefinitionWarning=this.lexerDefinitionWarning.concat(Tl(o,this.trackStartLines,this.config.lineTerminatorCharacters))})),o.modes=o.modes?o.modes:{},x(o.modes,(s,l)=>{o.modes[l]=me(s,f=>ft(f))});let a=k(o.modes);if(x(o.modes,(s,l)=>{this.TRACE_INIT(`Mode: <${l}> processing`,()=>{if(this.modes.push(l),this.config.skipValidations===!1&&this.TRACE_INIT("validatePatterns",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(xl(s,a))}),N(this.lexerDefinitionErrors)){Zt(s);let f;this.TRACE_INIT("analyzeTokenTypes",()=>{f=ml(s,{lineTerminatorCharacters:this.config.lineTerminatorCharacters,positionTracking:r.positionTracking,ensureOptimizations:r.ensureOptimizations,safeMode:r.safeMode,tracer:this.TRACE_INIT})}),this.patternIdxToConfig[l]=f.patternIdxToConfig,this.charCodeToPatternIdxToConfig[l]=f.charCodeToPatternIdxToConfig,this.emptyGroups=tt({},this.emptyGroups,f.emptyGroups),this.hasCustom=f.hasCustom||this.hasCustom,this.canModeBeOptimized[l]=f.canBeOptimized}})}),this.defaultMode=o.defaultMode,!N(this.lexerDefinitionErrors)&&!this.config.deferDefinitionErrorsHandling){let l=d(this.lexerDefinitionErrors,f=>f.message).join(`-----------------------
`);throw new Error(`Errors detected in definition of Lexer:
`+l)}x(this.lexerDefinitionWarning,s=>{Rr(s.message)}),this.TRACE_INIT("Choosing sub-methods implementations",()=>{if(No?(this.chopInput=St,this.match=this.matchWithTest):(this.updateLastIndex=K,this.match=this.matchWithExec),i&&(this.handleModes=K),this.trackStartLines===!1&&(this.computeNewColumn=St),this.trackEndLines===!1&&(this.updateTokenEndLineColumnLocation=K),/full/i.test(this.config.positionTracking))this.createTokenInstance=this.createFullToken;else if(/onlyStart/i.test(this.config.positionTracking))this.createTokenInstance=this.createStartOnlyToken;else if(/onlyOffset/i.test(this.config.positionTracking))this.createTokenInstance=this.createOffsetOnlyToken;else throw Error(`Invalid <positionTracking> config option: "${this.config.positionTracking}"`);this.hasCustom?(this.addToken=this.addTokenUsingPush,this.handlePayload=this.handlePayloadWithCustom):(this.addToken=this.addTokenUsingMemberAccess,this.handlePayload=this.handlePayloadNoCustom)}),this.TRACE_INIT("Failed Optimization Warnings",()=>{let s=$(this.canModeBeOptimized,(l,f,c)=>(f===!1&&l.push(c),l),[]);if(r.ensureOptimizations&&!N(s))throw Error(`Lexer Modes: < ${s.join(", ")} > cannot be optimized.
	 Disable the "ensureOptimizations" lexer config flag to silently ignore this and run the lexer in an un-optimized mode.
	 Or inspect the console log for details on how to resolve these issues.`)}),this.TRACE_INIT("clearRegExpParserCache",()=>{fl()}),this.TRACE_INIT("toFastProperties",()=>{Sr(this)})})}tokenize(t,r=this.defaultMode){if(!N(this.lexerDefinitionErrors)){let o=d(this.lexerDefinitionErrors,i=>i.message).join(`-----------------------
`);throw new Error(`Unable to Tokenize because Errors detected in definition of Lexer:
`+o)}return this.tokenizeInternal(t,r)}tokenizeInternal(t,r){let n,o,i,a,s,l,f,c,u,p,g,I,R,S,A,m,h=t,O=h.length,_=0,At=0,io=this.hasCustom?0:Math.floor(t.length/10),bt=new Array(io),ao=[],dr=this.trackStartLines?1:void 0,Jt=this.trackStartLines?1:void 0,mr=El(this.emptyGroups),yf=this.trackStartLines,so=this.config.lineTerminatorsPattern,Wr=0,Qt=[],xr=[],Kr=[],ti=[];Object.freeze(ti);let gr;function ei(){return Qt}function ri(at){let yt=Kt(at),Le=xr[yt];return Le===void 0?ti:Le}let Sf=at=>{if(Kr.length===1&&at.tokenType.PUSH_MODE===void 0){let yt=this.config.errorMessageProvider.buildUnableToPopLexerModeMessage(at);ao.push({offset:at.startOffset,line:at.startLine,column:at.startColumn,length:at.image.length,message:yt})}else{Kr.pop();let yt=Gt(Kr);Qt=this.patternIdxToConfig[yt],xr=this.charCodeToPatternIdxToConfig[yt],Wr=Qt.length;let Le=this.canModeBeOptimized[yt]&&this.config.safeMode===!1;xr&&Le?gr=ri:gr=ei}};function ni(at){Kr.push(at),xr=this.charCodeToPatternIdxToConfig[at],Qt=this.patternIdxToConfig[at],Wr=Qt.length,Wr=Qt.length;let yt=this.canModeBeOptimized[at]&&this.config.safeMode===!1;xr&&yt?gr=ri:gr=ei}ni.call(this,r);let Nt,oi=this.config.recoveryEnabled;for(;_<O;){l=null;let at=h.charCodeAt(_),yt=gr(at),Le=yt.length;for(n=0;n<Le;n++){Nt=yt[n];let It=Nt.pattern;f=null;let jt=Nt.short;if(jt!==!1?at===jt&&(l=It):Nt.isCustom===!0?(m=It.exec(h,_,bt,mr),m!==null?(l=m[0],m.payload!==void 0&&(f=m.payload)):l=null):(this.updateLastIndex(It,_),l=this.match(It,t,_)),l!==null){if(s=Nt.longerAlt,s!==void 0){let te=s.length;for(i=0;i<te;i++){let $t=Qt[s[i]],Ee=$t.pattern;if(c=null,$t.isCustom===!0?(m=Ee.exec(h,_,bt,mr),m!==null?(a=m[0],m.payload!==void 0&&(c=m.payload)):a=null):(this.updateLastIndex(Ee,_),a=this.match(Ee,t,_)),a&&a.length>l.length){l=a,f=c,Nt=$t;break}}}break}}if(l!==null){if(u=l.length,p=Nt.group,p!==void 0&&(g=Nt.tokenTypeIdx,I=this.createTokenInstance(l,_,g,Nt.tokenType,dr,Jt,u),this.handlePayload(I,f),p===!1?At=this.addToken(bt,At,I):mr[p].push(I)),t=this.chopInput(t,u),_=_+u,Jt=this.computeNewColumn(Jt,u),yf===!0&&Nt.canLineTerminator===!0){let It=0,jt,te;so.lastIndex=0;do jt=so.test(l),jt===!0&&(te=so.lastIndex-1,It++);while(jt===!0);It!==0&&(dr=dr+It,Jt=u-te,this.updateTokenEndLineColumnLocation(I,p,te,It,dr,Jt,u))}this.handleModes(Nt,Sf,ni,I)}else{let It=_,jt=dr,te=Jt,$t=oi===!1;for(;$t===!1&&_<O;)for(t=this.chopInput(t,1),_++,o=0;o<Wr;o++){let Ee=Qt[o],lo=Ee.pattern,ii=Ee.short;if(ii!==!1?h.charCodeAt(_)===ii&&($t=!0):Ee.isCustom===!0?$t=lo.exec(h,_,bt,mr)!==null:(this.updateLastIndex(lo,_),$t=lo.exec(t)!==null),$t===!0)break}if(R=_-It,Jt=this.computeNewColumn(Jt,R),A=this.config.errorMessageProvider.buildUnexpectedCharactersMessage(h,It,R,jt,te),ao.push({offset:It,line:jt,column:te,length:R,message:A}),oi===!1)break}}return this.hasCustom||(bt.length=At),{tokens:bt,groups:mr,errors:ao}}handleModes(t,r,n,o){if(t.pop===!0){let i=t.push;r(o),i!==void 0&&n.call(this,i)}else t.push!==void 0&&n.call(this,t.push)}chopInput(t,r){return t.substring(r)}updateLastIndex(t,r){t.lastIndex=r}updateTokenEndLineColumnLocation(t,r,n,o,i,a,s){let l,f;r!==void 0&&(l=n===s-1,f=l?-1:0,o===1&&l===!0||(t.endLine=i+f,t.endColumn=a-1+-f))}computeNewColumn(t,r){return t+r}createOffsetOnlyToken(t,r,n,o){return{image:t,startOffset:r,tokenTypeIdx:n,tokenType:o}}createStartOnlyToken(t,r,n,o,i,a){return{image:t,startOffset:r,startLine:i,startColumn:a,tokenTypeIdx:n,tokenType:o}}createFullToken(t,r,n,o,i,a,s){return{image:t,startOffset:r,endOffset:r+s-1,startLine:i,endLine:i,startColumn:a,endColumn:a+s-1,tokenTypeIdx:n,tokenType:o}}addTokenUsingPush(t,r,n){return t.push(n),r}addTokenUsingMemberAccess(t,r,n){return t[r]=n,r++,r}handlePayloadNoCustom(t,r){}handlePayloadWithCustom(t,r){r!==null&&(t.payload=r)}matchWithTest(t,r,n){return t.test(r)===!0?r.substring(n,t.lastIndex):null}matchWithExec(t,r){let n=t.exec(r);return n!==null?n[0]:null}};it.SKIPPED="This marks a skipped Token pattern, this means each token identified by it willbe consumed and then thrown into oblivion, this can be used to for example to completely ignore whitespace.";it.NA=/NOT_APPLICABLE/;function Ne(e){return Lo(e)?e.LABEL:e.name}function pT(e){return e.name}function Lo(e){return et(e.LABEL)&&e.LABEL!==""}var hT="parent",vl="categories",Ll="label",bl="group",Pl="push_mode",kl="pop_mode",Ml="longer_alt",wl="line_breaks",Fl="start_chars_hint";function bn(e){return dT(e)}function dT(e){let t=e.pattern,r={};if(r.name=e.name,ft(t)||(r.PATTERN=t),T(e,hT))throw`The parent property is no longer supported.
See: https://github.com/chevrotain/chevrotain/issues/564#issuecomment-349062346 for details.`;return T(e,vl)&&(r.CATEGORIES=e[vl]),Zt([r]),T(e,Ll)&&(r.LABEL=e[Ll]),T(e,bl)&&(r.GROUP=e[bl]),T(e,kl)&&(r.POP_MODE=e[kl]),T(e,Pl)&&(r.PUSH_MODE=e[Pl]),T(e,Ml)&&(r.LONGER_ALT=e[Ml]),T(e,wl)&&(r.LINE_BREAKS=e[wl]),T(e,Fl)&&(r.START_CHARS_HINT=e[Fl]),r}var vt=bn({name:"EOF",pattern:it.NA});Zt([vt]);function Ce(e,t,r,n,o,i,a,s){return{image:t,startOffset:r,endOffset:n,startLine:o,endLine:i,startColumn:a,endColumn:s,tokenTypeIdx:e.tokenTypeIdx,tokenType:e}}function bo(e,t){return qt(e,t)}var Pn={buildMismatchTokenMessage({expected:e,actual:t,previous:r,ruleName:n}){return`Expecting ${Lo(e)?`--> ${Ne(e)} <--`:`token of type --> ${e.name} <--`} but found --> '${t.image}' <--`},buildNotAllInputParsedMessage({firstRedundant:e,ruleName:t}){return"Redundant input, expecting EOF but found: "+e.image},buildNoViableAltMessage({expectedPathsPerAlt:e,actual:t,previous:r,customUserDescription:n,ruleName:o}){let i="Expecting: ",s=`
but found: '`+ot(t).image+"'";if(n)return i+n+s;{let l=$(e,(p,g)=>p.concat(g),[]),f=d(l,p=>`[${d(p,g=>Ne(g)).join(", ")}]`),u=`one of these possible Token sequences:
${d(f,(p,g)=>`  ${g+1}. ${p}`).join(`
`)}`;return i+u+s}},buildEarlyExitMessage({expectedIterationPaths:e,actual:t,customUserDescription:r,ruleName:n}){let o="Expecting: ",a=`
but found: '`+ot(t).image+"'";if(r)return o+r+a;{let l=`expecting at least one iteration which starts with one of these possible Token sequences::
  <${d(e,f=>`[${d(f,c=>Ne(c)).join(",")}]`).join(" ,")}>`;return o+l+a}}};Object.freeze(Pn);var Ul={buildRuleNotFoundError(e,t){return"Invalid grammar, reference to a rule which is not defined: ->"+t.nonTerminalName+`<-
inside top level rule: ->`+e.name+"<-"}},Lt={buildDuplicateFoundError(e,t){function r(c){return c instanceof C?c.terminalType.name:c instanceof P?c.nonTerminalName:""}let n=e.name,o=ot(t),i=o.idx,a=Et(o),s=r(o),l=i>0,f=`->${a}${l?i:""}<- ${s?`with argument: ->${s}<-`:""}
                  appears more than once (${t.length} times) in the top level rule: ->${n}<-.                  
                  For further details see: https://chevrotain.io/docs/FAQ.html#NUMERICAL_SUFFIXES 
                  `;return f=f.replace(/[ \t]+/g," "),f=f.replace(/\s\s+/g,`
`),f},buildNamespaceConflictError(e){return`Namespace conflict found in grammar.
The grammar has both a Terminal(Token) and a Non-Terminal(Rule) named: <${e.name}>.
To resolve this make sure each Terminal and Non-Terminal names are unique
This is easy to accomplish by using the convention that Terminal names start with an uppercase letter
and Non-Terminal names start with a lower case letter.`},buildAlternationPrefixAmbiguityError(e){let t=d(e.prefixPath,o=>Ne(o)).join(", "),r=e.alternation.idx===0?"":e.alternation.idx;return`Ambiguous alternatives: <${e.ambiguityIndices.join(" ,")}> due to common lookahead prefix
in <OR${r}> inside <${e.topLevelRule.name}> Rule,
<${t}> may appears as a prefix path in all these alternatives.
See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#COMMON_PREFIX
For Further details.`},buildAlternationAmbiguityError(e){let t=d(e.prefixPath,o=>Ne(o)).join(", "),r=e.alternation.idx===0?"":e.alternation.idx,n=`Ambiguous Alternatives Detected: <${e.ambiguityIndices.join(" ,")}> in <OR${r}> inside <${e.topLevelRule.name}> Rule,
<${t}> may appears as a prefix path in all these alternatives.
`;return n=n+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,n},buildEmptyRepetitionError(e){let t=Et(e.repetition);return e.repetition.idx!==0&&(t+=e.repetition.idx),`The repetition <${t}> within Rule <${e.topLevelRule.name}> can never consume any tokens.
This could lead to an infinite loop.`},buildTokenNameError(e){return"deprecated"},buildEmptyAlternationError(e){return`Ambiguous empty alternative: <${e.emptyChoiceIdx+1}> in <OR${e.alternation.idx}> inside <${e.topLevelRule.name}> Rule.
Only the last alternative may be an empty alternative.`},buildTooManyAlternativesError(e){return`An Alternation cannot have more than 256 alternatives:
<OR${e.alternation.idx}> inside <${e.topLevelRule.name}> Rule.
 has ${e.alternation.definition.length+1} alternatives.`},buildLeftRecursionError(e){let t=e.topLevelRule.name,r=d(e.leftRecursionPath,i=>i.name),n=`${t} --> ${r.concat([t]).join(" --> ")}`;return`Left Recursion found in grammar.
rule: <${t}> can be invoked from itself (directly or indirectly)
without consuming any Tokens. The grammar path that causes this is: 
 ${n}
 To fix this refactor your grammar to remove the left recursion.
see: https://en.wikipedia.org/wiki/LL_parser#Left_factoring.`},buildInvalidRuleNameError(e){return"deprecated"},buildDuplicateRuleNameError(e){let t;return e.topLevelRule instanceof mt?t=e.topLevelRule.name:t=e.topLevelRule,`Duplicate definition, rule: ->${t}<- is already defined in the grammar: ->${e.grammarName}<-`}};function Dl(e,t){let r=new Po(e,t);return r.resolveRefs(),r.errors}var Po=class extends ct{constructor(t,r){super(),this.nameToTopRule=t,this.errMsgProvider=r,this.errors=[]}resolveRefs(){x(v(this.nameToTopRule),t=>{this.currTopLevel=t,t.accept(this)})}visitNonTerminal(t){let r=this.nameToTopRule[t.nonTerminalName];if(r)t.referencedRule=r;else{let n=this.errMsgProvider.buildRuleNotFoundError(this.currTopLevel,t);this.errors.push({message:n,type:rt.UNRESOLVED_SUBRULE_REF,ruleName:this.currTopLevel.name,unresolvedRefName:t.nonTerminalName})}}};var ko=class extends Yt{constructor(t,r){super(),this.topProd=t,this.path=r,this.possibleTokTypes=[],this.nextProductionName="",this.nextProductionOccurrence=0,this.found=!1,this.isAtEndOfPath=!1}startWalking(){if(this.found=!1,this.path.ruleStack[0]!==this.topProd.name)throw Error("The path does not start with the walker's top Rule!");return this.ruleStack=b(this.path.ruleStack).reverse(),this.occurrenceStack=b(this.path.occurrenceStack).reverse(),this.ruleStack.pop(),this.occurrenceStack.pop(),this.updateExpectedNext(),this.walk(this.topProd),this.possibleTokTypes}walk(t,r=[]){this.found||super.walk(t,r)}walkProdRef(t,r,n){if(t.referencedRule.name===this.nextProductionName&&t.idx===this.nextProductionOccurrence){let o=r.concat(n);this.updateExpectedNext(),this.walk(t.referencedRule,o)}}updateExpectedNext(){N(this.ruleStack)?(this.nextProductionName="",this.nextProductionOccurrence=0,this.isAtEndOfPath=!0):(this.nextProductionName=this.ruleStack.pop(),this.nextProductionOccurrence=this.occurrenceStack.pop())}},kn=class extends ko{constructor(t,r){super(t,r),this.path=r,this.nextTerminalName="",this.nextTerminalOccurrence=0,this.nextTerminalName=this.path.lastTok.name,this.nextTerminalOccurrence=this.path.lastTokOccurrence}walkTerminal(t,r,n){if(this.isAtEndOfPath&&t.terminalType.name===this.nextTerminalName&&t.idx===this.nextTerminalOccurrence&&!this.found){let o=r.concat(n),i=new w({definition:o});this.possibleTokTypes=Se(i),this.found=!0}}},sr=class extends Yt{constructor(t,r){super(),this.topRule=t,this.occurrence=r,this.result={token:void 0,occurrence:void 0,isEndOfRule:void 0}}startWalking(){return this.walk(this.topRule),this.result}},Mn=class extends sr{walkMany(t,r,n){if(t.idx===this.occurrence){let o=ot(r.concat(n));this.result.isEndOfRule=o===void 0,o instanceof C&&(this.result.token=o.terminalType,this.result.occurrence=o.idx)}else super.walkMany(t,r,n)}},br=class extends sr{walkManySep(t,r,n){if(t.idx===this.occurrence){let o=ot(r.concat(n));this.result.isEndOfRule=o===void 0,o instanceof C&&(this.result.token=o.terminalType,this.result.occurrence=o.idx)}else super.walkManySep(t,r,n)}},wn=class extends sr{walkAtLeastOne(t,r,n){if(t.idx===this.occurrence){let o=ot(r.concat(n));this.result.isEndOfRule=o===void 0,o instanceof C&&(this.result.token=o.terminalType,this.result.occurrence=o.idx)}else super.walkAtLeastOne(t,r,n)}},Pr=class extends sr{walkAtLeastOneSep(t,r,n){if(t.idx===this.occurrence){let o=ot(r.concat(n));this.result.isEndOfRule=o===void 0,o instanceof C&&(this.result.token=o.terminalType,this.result.occurrence=o.idx)}else super.walkAtLeastOneSep(t,r,n)}};function Fn(e,t,r=[]){r=b(r);let n=[],o=0;function i(s){return s.concat(X(e,o+1))}function a(s){let l=Fn(i(s),t,r);return n.concat(l)}for(;r.length<t&&o<e.length;){let s=e[o];if(s instanceof w)return a(s.definition);if(s instanceof P)return a(s.definition);if(s instanceof U)n=a(s.definition);else if(s instanceof Y){let l=s.definition.concat([new L({definition:s.definition})]);return a(l)}else if(s instanceof q){let l=[new w({definition:s.definition}),new L({definition:[new C({terminalType:s.separator})].concat(s.definition)})];return a(l)}else if(s instanceof H){let l=s.definition.concat([new L({definition:[new C({terminalType:s.separator})].concat(s.definition)})]);n=a(l)}else if(s instanceof L){let l=s.definition.concat([new L({definition:s.definition})]);n=a(l)}else{if(s instanceof z)return x(s.definition,l=>{N(l.definition)===!1&&(n=a(l.definition))}),n;if(s instanceof C)r.push(s.terminalType);else throw Error("non exhaustive match")}o++}return n.push({partialPath:r,suffixDef:X(e,o)}),n}function Un(e,t,r,n){let o="EXIT_NONE_TERMINAL",i=[o],a="EXIT_ALTERNATIVE",s=!1,l=t.length,f=l-n-1,c=[],u=[];for(u.push({idx:-1,def:e,ruleStack:[],occurrenceStack:[]});!N(u);){let p=u.pop();if(p===a){s&&Gt(u).idx<=f&&u.pop();continue}let g=p.def,I=p.idx,R=p.ruleStack,S=p.occurrenceStack;if(N(g))continue;let A=g[0];if(A===o){let m={idx:I,def:X(g),ruleStack:Xt(R),occurrenceStack:Xt(S)};u.push(m)}else if(A instanceof C)if(I<l-1){let m=I+1,h=t[m];if(r(h,A.terminalType)){let O={idx:m,def:X(g),ruleStack:R,occurrenceStack:S};u.push(O)}}else if(I===l-1)c.push({nextTokenType:A.terminalType,nextTokenOccurrence:A.idx,ruleStack:R,occurrenceStack:S}),s=!0;else throw Error("non exhaustive match");else if(A instanceof P){let m=b(R);m.push(A.nonTerminalName);let h=b(S);h.push(A.idx);let O={idx:I,def:A.definition.concat(i,X(g)),ruleStack:m,occurrenceStack:h};u.push(O)}else if(A instanceof U){let m={idx:I,def:X(g),ruleStack:R,occurrenceStack:S};u.push(m),u.push(a);let h={idx:I,def:A.definition.concat(X(g)),ruleStack:R,occurrenceStack:S};u.push(h)}else if(A instanceof Y){let m=new L({definition:A.definition,idx:A.idx}),h=A.definition.concat([m],X(g)),O={idx:I,def:h,ruleStack:R,occurrenceStack:S};u.push(O)}else if(A instanceof q){let m=new C({terminalType:A.separator}),h=new L({definition:[m].concat(A.definition),idx:A.idx}),O=A.definition.concat([h],X(g)),_={idx:I,def:O,ruleStack:R,occurrenceStack:S};u.push(_)}else if(A instanceof H){let m={idx:I,def:X(g),ruleStack:R,occurrenceStack:S};u.push(m),u.push(a);let h=new C({terminalType:A.separator}),O=new L({definition:[h].concat(A.definition),idx:A.idx}),_=A.definition.concat([O],X(g)),At={idx:I,def:_,ruleStack:R,occurrenceStack:S};u.push(At)}else if(A instanceof L){let m={idx:I,def:X(g),ruleStack:R,occurrenceStack:S};u.push(m),u.push(a);let h=new L({definition:A.definition,idx:A.idx}),O=A.definition.concat([h],X(g)),_={idx:I,def:O,ruleStack:R,occurrenceStack:S};u.push(_)}else if(A instanceof z)for(let m=A.definition.length-1;m>=0;m--){let h=A.definition[m],O={idx:I,def:h.definition.concat(X(g)),ruleStack:R,occurrenceStack:S};u.push(O),u.push(a)}else if(A instanceof w)u.push({idx:I,def:A.definition.concat(X(g)),ruleStack:R,occurrenceStack:S});else if(A instanceof mt)u.push(mT(A,I,R,S));else throw Error("non exhaustive match")}return c}function mT(e,t,r,n){let o=b(r);o.push(e.name);let i=b(n);return i.push(1),{idx:t,def:e.definition,ruleStack:o,occurrenceStack:i}}var G;(function(e){e[e.OPTION=0]="OPTION",e[e.REPETITION=1]="REPETITION",e[e.REPETITION_MANDATORY=2]="REPETITION_MANDATORY",e[e.REPETITION_MANDATORY_WITH_SEPARATOR=3]="REPETITION_MANDATORY_WITH_SEPARATOR",e[e.REPETITION_WITH_SEPARATOR=4]="REPETITION_WITH_SEPARATOR",e[e.ALTERNATION=5]="ALTERNATION"})(G||(G={}));function kr(e){if(e instanceof U||e==="Option")return G.OPTION;if(e instanceof L||e==="Repetition")return G.REPETITION;if(e instanceof Y||e==="RepetitionMandatory")return G.REPETITION_MANDATORY;if(e instanceof q||e==="RepetitionMandatoryWithSeparator")return G.REPETITION_MANDATORY_WITH_SEPARATOR;if(e instanceof H||e==="RepetitionWithSeparator")return G.REPETITION_WITH_SEPARATOR;if(e instanceof z||e==="Alternation")return G.ALTERNATION;throw Error("non exhaustive match")}function xT(e){let{occurrence:t,rule:r,prodType:n,maxLookahead:o}=e,i=kr(n);return i===G.ALTERNATION?lr(t,r,o):fr(t,r,i,o)}function Gl(e,t,r,n,o,i){let a=lr(e,t,r),s=zl(a)?ar:qt;return i(a,n,s,o)}function Wl(e,t,r,n,o,i){let a=fr(e,t,o,r),s=zl(a)?ar:qt;return i(a[0],s,n)}function Kl(e,t,r,n){let o=e.length,i=st(e,a=>st(a,s=>s.length===1));if(t)return function(a){let s=d(a,l=>l.GATE);for(let l=0;l<o;l++){let f=e[l],c=f.length,u=s[l];if(!(u!==void 0&&u.call(this)===!1))t:for(let p=0;p<c;p++){let g=f[p],I=g.length;for(let R=0;R<I;R++){let S=this.LA(R+1);if(r(S,g[R])===!1)continue t}return l}}};if(i&&!n){let a=d(e,l=>j(l)),s=$(a,(l,f,c)=>(x(f,u=>{T(l,u.tokenTypeIdx)||(l[u.tokenTypeIdx]=c),x(u.categoryMatches,p=>{T(l,p)||(l[p]=c)})}),l),{});return function(){let l=this.LA(1);return s[l.tokenTypeIdx]}}else return function(){for(let a=0;a<o;a++){let s=e[a],l=s.length;t:for(let f=0;f<l;f++){let c=s[f],u=c.length;for(let p=0;p<u;p++){let g=this.LA(p+1);if(r(g,c[p])===!1)continue t}return a}}}}function jl(e,t,r){let n=st(e,i=>i.length===1),o=e.length;if(n&&!r){let i=j(e);if(i.length===1&&N(i[0].categoryMatches)){let s=i[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===s}}else{let a=$(i,(s,l,f)=>(s[l.tokenTypeIdx]=!0,x(l.categoryMatches,c=>{s[c]=!0}),s),[]);return function(){let s=this.LA(1);return a[s.tokenTypeIdx]===!0}}}else return function(){t:for(let i=0;i<o;i++){let a=e[i],s=a.length;for(let l=0;l<s;l++){let f=this.LA(l+1);if(t(f,a[l])===!1)continue t}return!0}return!1}}var wo=class extends Yt{constructor(t,r,n){super(),this.topProd=t,this.targetOccurrence=r,this.targetProdType=n}startWalking(){return this.walk(this.topProd),this.restDef}checkIsTarget(t,r,n,o){return t.idx===this.targetOccurrence&&this.targetProdType===r?(this.restDef=n.concat(o),!0):!1}walkOption(t,r,n){this.checkIsTarget(t,G.OPTION,r,n)||super.walkOption(t,r,n)}walkAtLeastOne(t,r,n){this.checkIsTarget(t,G.REPETITION_MANDATORY,r,n)||super.walkOption(t,r,n)}walkAtLeastOneSep(t,r,n){this.checkIsTarget(t,G.REPETITION_MANDATORY_WITH_SEPARATOR,r,n)||super.walkOption(t,r,n)}walkMany(t,r,n){this.checkIsTarget(t,G.REPETITION,r,n)||super.walkOption(t,r,n)}walkManySep(t,r,n){this.checkIsTarget(t,G.REPETITION_WITH_SEPARATOR,r,n)||super.walkOption(t,r,n)}},Dn=class extends ct{constructor(t,r,n){super(),this.targetOccurrence=t,this.targetProdType=r,this.targetRef=n,this.result=[]}checkIsTarget(t,r){t.idx===this.targetOccurrence&&this.targetProdType===r&&(this.targetRef===void 0||t===this.targetRef)&&(this.result=t.definition)}visitOption(t){this.checkIsTarget(t,G.OPTION)}visitRepetition(t){this.checkIsTarget(t,G.REPETITION)}visitRepetitionMandatory(t){this.checkIsTarget(t,G.REPETITION_MANDATORY)}visitRepetitionMandatoryWithSeparator(t){this.checkIsTarget(t,G.REPETITION_MANDATORY_WITH_SEPARATOR)}visitRepetitionWithSeparator(t){this.checkIsTarget(t,G.REPETITION_WITH_SEPARATOR)}visitAlternation(t){this.checkIsTarget(t,G.ALTERNATION)}};function Bl(e){let t=new Array(e);for(let r=0;r<e;r++)t[r]=[];return t}function Mo(e){let t=[""];for(let r=0;r<e.length;r++){let n=e[r],o=[];for(let i=0;i<t.length;i++){let a=t[i];o.push(a+"_"+n.tokenTypeIdx);for(let s=0;s<n.categoryMatches.length;s++){let l="_"+n.categoryMatches[s];o.push(a+l)}}t=o}return t}function gT(e,t,r){for(let n=0;n<e.length;n++){if(n===r)continue;let o=e[n];for(let i=0;i<t.length;i++){let a=t[i];if(o[a]===!0)return!1}}return!0}function $l(e,t){let r=d(e,a=>Fn([a],1)),n=Bl(r.length),o=d(r,a=>{let s={};return x(a,l=>{let f=Mo(l.partialPath);x(f,c=>{s[c]=!0})}),s}),i=r;for(let a=1;a<=t;a++){let s=i;i=Bl(s.length);for(let l=0;l<s.length;l++){let f=s[l];for(let c=0;c<f.length;c++){let u=f[c].partialPath,p=f[c].suffixDef,g=Mo(u);if(gT(o,g,l)||N(p)||u.length===t){let R=n[l];if(Bn(R,u)===!1){R.push(u);for(let S=0;S<g.length;S++){let A=g[S];o[l][A]=!0}}}else{let R=Fn(p,a+1,u);i[l]=i[l].concat(R),x(R,S=>{let A=Mo(S.partialPath);x(A,m=>{o[l][m]=!0})})}}}}return n}function lr(e,t,r,n){let o=new Dn(e,G.ALTERNATION,n);return t.accept(o),$l(o.result,r)}function fr(e,t,r,n){let o=new Dn(e,r);t.accept(o);let i=o.result,s=new wo(t,e,r).startWalking(),l=new w({definition:i}),f=new w({definition:s});return $l([l,f],n)}function Bn(e,t){t:for(let r=0;r<e.length;r++){let n=e[r];if(n.length===t.length){for(let o=0;o<n.length;o++){let i=t[o],a=n[o];if((i===a||a.categoryMatchesMap[i.tokenTypeIdx]!==void 0)===!1)continue t}return!0}}return!1}function Hl(e,t){return e.length<t.length&&st(e,(r,n)=>{let o=t[n];return r===o||o.categoryMatchesMap[r.tokenTypeIdx]})}function zl(e){return st(e,t=>st(t,r=>st(r,n=>N(n.categoryMatches))))}function Vl(e){let t=e.lookaheadStrategy.validate({rules:e.rules,tokenTypes:e.tokenTypes,grammarName:e.grammarName});return d(t,r=>Object.assign({type:rt.CUSTOM_LOOKAHEAD_VALIDATION},r))}function Xl(e,t,r,n){let o=dt(e,l=>TT(l,r)),i=ST(e,t,r),a=dt(e,l=>IT(l,r)),s=dt(e,l=>AT(l,e,n,r));return o.concat(i,a,s)}function TT(e,t){let r=new Fo;e.accept(r);let n=r.allProductions,o=Ir(n,ET),i=Tt(o,s=>s.length>1);return d(v(i),s=>{let l=ot(s),f=t.buildDuplicateFoundError(e,s),c=Et(l),u={message:f,type:rt.DUPLICATE_PRODUCTIONS,ruleName:e.name,dslName:c,occurrence:l.idx},p=Yl(l);return p&&(u.parameter=p),u})}function ET(e){return`${Et(e)}_#_${e.idx}_#_${Yl(e)}`}function Yl(e){return e instanceof C?e.terminalType.name:e instanceof P?e.nonTerminalName:""}var Fo=class extends ct{constructor(){super(...arguments),this.allProductions=[]}visitNonTerminal(t){this.allProductions.push(t)}visitOption(t){this.allProductions.push(t)}visitRepetitionWithSeparator(t){this.allProductions.push(t)}visitRepetitionMandatory(t){this.allProductions.push(t)}visitRepetitionMandatoryWithSeparator(t){this.allProductions.push(t)}visitRepetition(t){this.allProductions.push(t)}visitAlternation(t){this.allProductions.push(t)}visitTerminal(t){this.allProductions.push(t)}};function AT(e,t,r,n){let o=[];if($(t,(a,s)=>s.name===e.name?a+1:a,0)>1){let a=n.buildDuplicateRuleNameError({topLevelRule:e,grammarName:r});o.push({message:a,type:rt.DUPLICATE_RULE_NAME,ruleName:e.name})}return o}function ql(e,t,r){let n=[],o;return D(t,e)||(o=`Invalid rule override, rule: ->${e}<- cannot be overridden in the grammar: ->${r}<-as it is not defined in any of the super grammars `,n.push({message:o,type:rt.INVALID_RULE_OVERRIDE,ruleName:e})),n}function Do(e,t,r,n=[]){let o=[],i=Gn(t.definition);if(N(i))return[];{let a=e.name;D(i,e)&&o.push({message:r.buildLeftRecursionError({topLevelRule:e,leftRecursionPath:n}),type:rt.LEFT_RECURSION,ruleName:a});let l=de(i,n.concat([e])),f=dt(l,c=>{let u=b(n);return u.push(c),Do(e,c,r,u)});return o.concat(f)}}function Gn(e){let t=[];if(N(e))return t;let r=ot(e);if(r instanceof P)t.push(r.referencedRule);else if(r instanceof w||r instanceof U||r instanceof Y||r instanceof q||r instanceof H||r instanceof L)t=t.concat(Gn(r.definition));else if(r instanceof z)t=j(d(r.definition,i=>Gn(i.definition)));else if(!(r instanceof C))throw Error("non exhaustive match");let n=ye(r),o=e.length>1;if(n&&o){let i=X(e);return t.concat(Gn(i))}else return t}var Mr=class extends ct{constructor(){super(...arguments),this.alternations=[]}visitAlternation(t){this.alternations.push(t)}};function Zl(e,t){let r=new Mr;e.accept(r);let n=r.alternations;return dt(n,i=>{let a=Xt(i.definition);return dt(a,(s,l)=>{let f=Un([s],[],qt,1);return N(f)?[{message:t.buildEmptyAlternationError({topLevelRule:e,alternation:i,emptyChoiceIdx:l}),type:rt.NONE_LAST_EMPTY_ALT,ruleName:e.name,occurrence:i.idx,alternative:l+1}]:[]})})}function Jl(e,t,r){let n=new Mr;e.accept(n);let o=n.alternations;return o=me(o,a=>a.ignoreAmbiguities===!0),dt(o,a=>{let s=a.idx,l=a.maxLookahead||t,f=lr(s,e,l,a),c=RT(f,a,e,r),u=yT(f,a,e,r);return c.concat(u)})}var Uo=class extends ct{constructor(){super(...arguments),this.allProductions=[]}visitRepetitionWithSeparator(t){this.allProductions.push(t)}visitRepetitionMandatory(t){this.allProductions.push(t)}visitRepetitionMandatoryWithSeparator(t){this.allProductions.push(t)}visitRepetition(t){this.allProductions.push(t)}};function IT(e,t){let r=new Mr;e.accept(r);let n=r.alternations;return dt(n,i=>i.definition.length>255?[{message:t.buildTooManyAlternativesError({topLevelRule:e,alternation:i}),type:rt.TOO_MANY_ALTS,ruleName:e.name,occurrence:i.idx}]:[])}function Ql(e,t,r){let n=[];return x(e,o=>{let i=new Uo;o.accept(i);let a=i.allProductions;x(a,s=>{let l=kr(s),f=s.maxLookahead||t,c=s.idx,p=fr(c,o,l,f)[0];if(N(j(p))){let g=r.buildEmptyRepetitionError({topLevelRule:o,repetition:s});n.push({message:g,type:rt.NO_NON_EMPTY_LOOKAHEAD,ruleName:o.name})}})}),n}function RT(e,t,r,n){let o=[],i=$(e,(s,l,f)=>(t.definition[f].ignoreAmbiguities===!0||x(l,c=>{let u=[f];x(e,(p,g)=>{f!==g&&Bn(p,c)&&t.definition[g].ignoreAmbiguities!==!0&&u.push(g)}),u.length>1&&!Bn(o,c)&&(o.push(c),s.push({alts:u,path:c}))}),s),[]);return d(i,s=>{let l=d(s.alts,c=>c+1);return{message:n.buildAlternationAmbiguityError({topLevelRule:r,alternation:t,ambiguityIndices:l,prefixPath:s.path}),type:rt.AMBIGUOUS_ALTS,ruleName:r.name,occurrence:t.idx,alternatives:s.alts}})}function yT(e,t,r,n){let o=$(e,(a,s,l)=>{let f=d(s,c=>({idx:l,path:c}));return a.concat(f)},[]);return Bt(dt(o,a=>{if(t.definition[a.idx].ignoreAmbiguities===!0)return[];let l=a.idx,f=a.path,c=lt(o,p=>t.definition[p.idx].ignoreAmbiguities!==!0&&p.idx<l&&Hl(p.path,f));return d(c,p=>{let g=[p.idx+1,l+1],I=t.idx===0?"":t.idx;return{message:n.buildAlternationPrefixAmbiguityError({topLevelRule:r,alternation:t,ambiguityIndices:g,prefixPath:p.path}),type:rt.AMBIGUOUS_PREFIX_ALTS,ruleName:r.name,occurrence:I,alternatives:g}})}))}function ST(e,t,r){let n=[],o=d(t,i=>i.name);return x(e,i=>{let a=i.name;if(D(o,a)){let s=r.buildNamespaceConflictError(i);n.push({message:s,type:rt.CONFLICT_TOKENS_RULES_NAMESPACE,ruleName:a})}}),n}function tf(e){let t=Qe(e,{errMsgProvider:Ul}),r={};return x(e.rules,n=>{r[n.name]=n}),Dl(r,t.errMsgProvider)}function ef(e){return e=Qe(e,{errMsgProvider:Lt}),Xl(e.rules,e.tokenTypes,e.errMsgProvider,e.grammarName)}var rf="MismatchedTokenException",nf="NoViableAltException",of="EarlyExitException",af="NotAllInputParsedException",sf=[rf,nf,of,af];Object.freeze(sf);function Te(e){return D(sf,e.name)}var cr=class extends Error{constructor(t,r){super(t),this.token=r,this.resyncedTokens=[],Object.setPrototypeOf(this,new.target.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}},ve=class extends cr{constructor(t,r,n){super(t,r),this.previousToken=n,this.name=rf}},wr=class extends cr{constructor(t,r,n){super(t,r),this.previousToken=n,this.name=nf}},Fr=class extends cr{constructor(t,r){super(t,r),this.name=af}},Ur=class extends cr{constructor(t,r,n){super(t,r),this.previousToken=n,this.name=of}};var Bo={},Wo="InRuleRecoveryException",Go=class extends Error{constructor(t){super(t),this.name=Wo}},Wn=class{initRecoverable(t){this.firstAfterRepMap={},this.resyncFollows={},this.recoveryEnabled=T(t,"recoveryEnabled")?t.recoveryEnabled:ut.recoveryEnabled,this.recoveryEnabled&&(this.attemptInRepetitionRecovery=OT)}getTokenToInsert(t){let r=Ce(t,"",NaN,NaN,NaN,NaN,NaN,NaN);return r.isInsertedInRecovery=!0,r}canTokenTypeBeInsertedInRecovery(t){return!0}canTokenTypeBeDeletedInRecovery(t){return!0}tryInRepetitionRecovery(t,r,n,o){let i=this.findReSyncTokenType(),a=this.exportLexerState(),s=[],l=!1,f=this.LA(1),c=this.LA(1),u=()=>{let p=this.LA(0),g=this.errorMessageProvider.buildMismatchTokenMessage({expected:o,actual:f,previous:p,ruleName:this.getCurrRuleFullName()}),I=new ve(g,f,this.LA(0));I.resyncedTokens=Xt(s),this.SAVE_ERROR(I)};for(;!l;)if(this.tokenMatcher(c,o)){u();return}else if(n.call(this)){u(),t.apply(this,r);return}else this.tokenMatcher(c,i)?l=!0:(c=this.SKIP_TOKEN(),this.addToResyncTokens(c,s));this.importLexerState(a)}shouldInRepetitionRecoveryBeTried(t,r,n){return!(n===!1||this.tokenMatcher(this.LA(1),t)||this.isBackTracking()||this.canPerformInRuleRecovery(t,this.getFollowsForInRuleRecovery(t,r)))}getFollowsForInRuleRecovery(t,r){let n=this.getCurrentGrammarPath(t,r);return this.getNextPossibleTokenTypes(n)}tryInRuleRecovery(t,r){if(this.canRecoverWithSingleTokenInsertion(t,r))return this.getTokenToInsert(t);if(this.canRecoverWithSingleTokenDeletion(t)){let n=this.SKIP_TOKEN();return this.consumeToken(),n}throw new Go("sad sad panda")}canPerformInRuleRecovery(t,r){return this.canRecoverWithSingleTokenInsertion(t,r)||this.canRecoverWithSingleTokenDeletion(t)}canRecoverWithSingleTokenInsertion(t,r){if(!this.canTokenTypeBeInsertedInRecovery(t)||N(r))return!1;let n=this.LA(1);return Wt(r,i=>this.tokenMatcher(n,i))!==void 0}canRecoverWithSingleTokenDeletion(t){return this.canTokenTypeBeDeletedInRecovery(t)?this.tokenMatcher(this.LA(2),t):!1}isInCurrentRuleReSyncSet(t){let r=this.getCurrFollowKey(),n=this.getFollowSetFromFollowKey(r);return D(n,t)}findReSyncTokenType(){let t=this.flattenFollowSet(),r=this.LA(1),n=2;for(;;){let o=Wt(t,i=>bo(r,i));if(o!==void 0)return o;r=this.LA(n),n++}}getCurrFollowKey(){if(this.RULE_STACK.length===1)return Bo;let t=this.getLastExplicitRuleShortName(),r=this.getLastExplicitRuleOccurrenceIndex(),n=this.getPreviousExplicitRuleShortName();return{ruleName:this.shortRuleNameToFullName(t),idxInCallingRule:r,inRule:this.shortRuleNameToFullName(n)}}buildFullFollowKeyStack(){let t=this.RULE_STACK,r=this.RULE_OCCURRENCE_STACK;return d(t,(n,o)=>o===0?Bo:{ruleName:this.shortRuleNameToFullName(n),idxInCallingRule:r[o],inRule:this.shortRuleNameToFullName(t[o-1])})}flattenFollowSet(){let t=d(this.buildFullFollowKeyStack(),r=>this.getFollowSetFromFollowKey(r));return j(t)}getFollowSetFromFollowKey(t){if(t===Bo)return[vt];let r=t.ruleName+t.idxInCallingRule+yn+t.inRule;return this.resyncFollows[r]}addToResyncTokens(t,r){return this.tokenMatcher(t,vt)||r.push(t),r}reSyncTo(t){let r=[],n=this.LA(1);for(;this.tokenMatcher(n,t)===!1;)n=this.SKIP_TOKEN(),this.addToResyncTokens(n,r);return Xt(r)}attemptInRepetitionRecovery(t,r,n,o,i,a,s){}getCurrentGrammarPath(t,r){let n=this.getHumanReadableRuleStack(),o=b(this.RULE_OCCURRENCE_STACK);return{ruleStack:n,occurrenceStack:o,lastTok:t,lastTokOccurrence:r}}getHumanReadableRuleStack(){return d(this.RULE_STACK,t=>this.shortRuleNameToFullName(t))}};function OT(e,t,r,n,o,i,a){let s=this.getKeyForAutomaticLookahead(n,o),l=this.firstAfterRepMap[s];if(l===void 0){let p=this.getCurrRuleFullName(),g=this.getGAstProductions()[p];l=new i(g,o).startWalking(),this.firstAfterRepMap[s]=l}let f=l.token,c=l.occurrence,u=l.isEndOfRule;this.RULE_STACK.length===1&&u&&f===void 0&&(f=vt,c=1),!(f===void 0||c===void 0)&&this.shouldInRepetitionRecoveryBeTried(f,c,a)&&this.tryInRepetitionRecovery(e,t,r,f)}function Kn(e,t,r){return r|t|e}var oM=32-8;var Dr=class{constructor(t){var r;this.maxLookahead=(r=t?.maxLookahead)!==null&&r!==void 0?r:ut.maxLookahead}validate(t){let r=this.validateNoLeftRecursion(t.rules);if(N(r)){let n=this.validateEmptyOrAlternatives(t.rules),o=this.validateAmbiguousAlternationAlternatives(t.rules,this.maxLookahead),i=this.validateSomeNonEmptyLookaheadPath(t.rules,this.maxLookahead);return[...r,...n,...o,...i]}return r}validateNoLeftRecursion(t){return dt(t,r=>Do(r,r,Lt))}validateEmptyOrAlternatives(t){return dt(t,r=>Zl(r,Lt))}validateAmbiguousAlternationAlternatives(t,r){return dt(t,n=>Jl(n,r,Lt))}validateSomeNonEmptyLookaheadPath(t,r){return Ql(t,r,Lt)}buildLookaheadForAlternation(t){return Gl(t.prodOccurrence,t.rule,t.maxLookahead,t.hasPredicates,t.dynamicTokensEnabled,Kl)}buildLookaheadForOptional(t){return Wl(t.prodOccurrence,t.rule,t.maxLookahead,t.dynamicTokensEnabled,kr(t.prodType),jl)}};var $n=class{initLooksAhead(t){this.dynamicTokensEnabled=T(t,"dynamicTokensEnabled")?t.dynamicTokensEnabled:ut.dynamicTokensEnabled,this.maxLookahead=T(t,"maxLookahead")?t.maxLookahead:ut.maxLookahead,this.lookaheadStrategy=T(t,"lookaheadStrategy")?t.lookaheadStrategy:new Dr({maxLookahead:this.maxLookahead}),this.lookAheadFuncsCache=new Map}preComputeLookaheadFunctions(t){x(t,r=>{this.TRACE_INIT(`${r.name} Rule Lookahead`,()=>{let{alternation:n,repetition:o,option:i,repetitionMandatory:a,repetitionMandatoryWithSeparator:s,repetitionWithSeparator:l}=_T(r);x(n,f=>{let c=f.idx===0?"":f.idx;this.TRACE_INIT(`${Et(f)}${c}`,()=>{let u=this.lookaheadStrategy.buildLookaheadForAlternation({prodOccurrence:f.idx,rule:r,maxLookahead:f.maxLookahead||this.maxLookahead,hasPredicates:f.hasPredicates,dynamicTokensEnabled:this.dynamicTokensEnabled}),p=Kn(this.fullRuleNameToShort[r.name],256,f.idx);this.setLaFuncCache(p,u)})}),x(o,f=>{this.computeLookaheadFunc(r,f.idx,768,"Repetition",f.maxLookahead,Et(f))}),x(i,f=>{this.computeLookaheadFunc(r,f.idx,512,"Option",f.maxLookahead,Et(f))}),x(a,f=>{this.computeLookaheadFunc(r,f.idx,1024,"RepetitionMandatory",f.maxLookahead,Et(f))}),x(s,f=>{this.computeLookaheadFunc(r,f.idx,1536,"RepetitionMandatoryWithSeparator",f.maxLookahead,Et(f))}),x(l,f=>{this.computeLookaheadFunc(r,f.idx,1280,"RepetitionWithSeparator",f.maxLookahead,Et(f))})})})}computeLookaheadFunc(t,r,n,o,i,a){this.TRACE_INIT(`${a}${r===0?"":r}`,()=>{let s=this.lookaheadStrategy.buildLookaheadForOptional({prodOccurrence:r,rule:t,maxLookahead:i||this.maxLookahead,dynamicTokensEnabled:this.dynamicTokensEnabled,prodType:o}),l=Kn(this.fullRuleNameToShort[t.name],n,r);this.setLaFuncCache(l,s)})}getKeyForAutomaticLookahead(t,r){let n=this.getLastExplicitRuleShortName();return Kn(n,t,r)}getLaFuncFromCache(t){return this.lookAheadFuncsCache.get(t)}setLaFuncCache(t,r){this.lookAheadFuncsCache.set(t,r)}},Ko=class extends ct{constructor(){super(...arguments),this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}reset(){this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}visitOption(t){this.dslMethods.option.push(t)}visitRepetitionWithSeparator(t){this.dslMethods.repetitionWithSeparator.push(t)}visitRepetitionMandatory(t){this.dslMethods.repetitionMandatory.push(t)}visitRepetitionMandatoryWithSeparator(t){this.dslMethods.repetitionMandatoryWithSeparator.push(t)}visitRepetition(t){this.dslMethods.repetition.push(t)}visitAlternation(t){this.dslMethods.alternation.push(t)}},jn=new Ko;function _T(e){jn.reset(),e.accept(jn);let t=jn.dslMethods;return jn.reset(),t}function Ho(e,t){isNaN(e.startOffset)===!0?(e.startOffset=t.startOffset,e.endOffset=t.endOffset):e.endOffset<t.endOffset&&(e.endOffset=t.endOffset)}function zo(e,t){isNaN(e.startOffset)===!0?(e.startOffset=t.startOffset,e.startColumn=t.startColumn,e.startLine=t.startLine,e.endOffset=t.endOffset,e.endColumn=t.endColumn,e.endLine=t.endLine):e.endOffset<t.endOffset&&(e.endOffset=t.endOffset,e.endColumn=t.endColumn,e.endLine=t.endLine)}function lf(e,t,r){e.children[r]===void 0?e.children[r]=[t]:e.children[r].push(t)}function ff(e,t,r){e.children[t]===void 0?e.children[t]=[r]:e.children[t].push(r)}var NT="name";function Vo(e,t){Object.defineProperty(e,NT,{enumerable:!1,configurable:!0,writable:!1,value:t})}function CT(e,t){let r=k(e),n=r.length;for(let o=0;o<n;o++){let i=r[o],a=e[i],s=a.length;for(let l=0;l<s;l++){let f=a[l];f.tokenTypeIdx===void 0&&this[f.name](f.children,t)}}}function cf(e,t){let r=function(){};Vo(r,e+"BaseSemantics");let n={visit:function(o,i){if(E(o)&&(o=o[0]),!ft(o))return this[o.name](o.children,i)},validateVisitor:function(){let o=vT(this,t);if(!N(o)){let i=d(o,a=>a.msg);throw Error(`Errors Detected in CST Visitor <${this.constructor.name}>:
	${i.join(`

`).replace(/\n/g,`
	`)}`)}}};return r.prototype=n,r.prototype.constructor=r,r._RULE_NAMES=t,r}function uf(e,t,r){let n=function(){};Vo(n,e+"BaseSemanticsWithDefaults");let o=Object.create(r.prototype);return x(t,i=>{o[i]=CT}),n.prototype=o,n.prototype.constructor=n,n}var Xo;(function(e){e[e.REDUNDANT_METHOD=0]="REDUNDANT_METHOD",e[e.MISSING_METHOD=1]="MISSING_METHOD"})(Xo||(Xo={}));function vT(e,t){return LT(e,t)}function LT(e,t){let r=lt(t,o=>ht(e[o])===!1),n=d(r,o=>({msg:`Missing visitor method: <${o}> on ${e.constructor.name} CST Visitor.`,type:Xo.MISSING_METHOD,methodName:o}));return Bt(n)}var Xn=class{initTreeBuilder(t){if(this.CST_STACK=[],this.outputCst=t.outputCst,this.nodeLocationTracking=T(t,"nodeLocationTracking")?t.nodeLocationTracking:ut.nodeLocationTracking,!this.outputCst)this.cstInvocationStateUpdate=K,this.cstFinallyStateUpdate=K,this.cstPostTerminal=K,this.cstPostNonTerminal=K,this.cstPostRule=K;else if(/full/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=zo,this.setNodeLocationFromNode=zo,this.cstPostRule=K,this.setInitialNodeLocation=this.setInitialNodeLocationFullRecovery):(this.setNodeLocationFromToken=K,this.setNodeLocationFromNode=K,this.cstPostRule=this.cstPostRuleFull,this.setInitialNodeLocation=this.setInitialNodeLocationFullRegular);else if(/onlyOffset/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=Ho,this.setNodeLocationFromNode=Ho,this.cstPostRule=K,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRecovery):(this.setNodeLocationFromToken=K,this.setNodeLocationFromNode=K,this.cstPostRule=this.cstPostRuleOnlyOffset,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRegular);else if(/none/i.test(this.nodeLocationTracking))this.setNodeLocationFromToken=K,this.setNodeLocationFromNode=K,this.cstPostRule=K,this.setInitialNodeLocation=K;else throw Error(`Invalid <nodeLocationTracking> config option: "${t.nodeLocationTracking}"`)}setInitialNodeLocationOnlyOffsetRecovery(t){t.location={startOffset:NaN,endOffset:NaN}}setInitialNodeLocationOnlyOffsetRegular(t){t.location={startOffset:this.LA(1).startOffset,endOffset:NaN}}setInitialNodeLocationFullRecovery(t){t.location={startOffset:NaN,startLine:NaN,startColumn:NaN,endOffset:NaN,endLine:NaN,endColumn:NaN}}setInitialNodeLocationFullRegular(t){let r=this.LA(1);t.location={startOffset:r.startOffset,startLine:r.startLine,startColumn:r.startColumn,endOffset:NaN,endLine:NaN,endColumn:NaN}}cstInvocationStateUpdate(t){let r={name:t,children:Object.create(null)};this.setInitialNodeLocation(r),this.CST_STACK.push(r)}cstFinallyStateUpdate(){this.CST_STACK.pop()}cstPostRuleFull(t){let r=this.LA(0),n=t.location;n.startOffset<=r.startOffset?(n.endOffset=r.endOffset,n.endLine=r.endLine,n.endColumn=r.endColumn):(n.startOffset=NaN,n.startLine=NaN,n.startColumn=NaN)}cstPostRuleOnlyOffset(t){let r=this.LA(0),n=t.location;n.startOffset<=r.startOffset?n.endOffset=r.endOffset:n.startOffset=NaN}cstPostTerminal(t,r){let n=this.CST_STACK[this.CST_STACK.length-1];lf(n,r,t),this.setNodeLocationFromToken(n.location,r)}cstPostNonTerminal(t,r){let n=this.CST_STACK[this.CST_STACK.length-1];ff(n,r,t),this.setNodeLocationFromNode(n.location,t.location)}getBaseCstVisitorConstructor(){if(ft(this.baseCstVisitorConstructor)){let t=cf(this.className,k(this.gastProductionsCache));return this.baseCstVisitorConstructor=t,t}return this.baseCstVisitorConstructor}getBaseCstVisitorConstructorWithDefaults(){if(ft(this.baseCstVisitorWithDefaultsConstructor)){let t=uf(this.className,k(this.gastProductionsCache),this.getBaseCstVisitorConstructor());return this.baseCstVisitorWithDefaultsConstructor=t,t}return this.baseCstVisitorWithDefaultsConstructor}getLastExplicitRuleShortName(){let t=this.RULE_STACK;return t[t.length-1]}getPreviousExplicitRuleShortName(){let t=this.RULE_STACK;return t[t.length-2]}getLastExplicitRuleOccurrenceIndex(){let t=this.RULE_OCCURRENCE_STACK;return t[t.length-1]}};var Yn=class{initLexerAdapter(){this.tokVector=[],this.tokVectorLength=0,this.currIdx=-1}set input(t){if(this.selfAnalysisDone!==!0)throw Error("Missing <performSelfAnalysis> invocation at the end of the Parser's constructor.");this.reset(),this.tokVector=t,this.tokVectorLength=t.length}get input(){return this.tokVector}SKIP_TOKEN(){return this.currIdx<=this.tokVector.length-2?(this.consumeToken(),this.LA(1)):ur}LA(t){let r=this.currIdx+t;return r<0||this.tokVectorLength<=r?ur:this.tokVector[r]}consumeToken(){this.currIdx++}exportLexerState(){return this.currIdx}importLexerState(t){this.currIdx=t}resetLexerState(){this.currIdx=-1}moveToTerminatedState(){this.currIdx=this.tokVector.length-1}getLexerPosition(){return this.exportLexerState()}};var qn=class{ACTION(t){return t.call(this)}consume(t,r,n){return this.consumeInternal(r,t,n)}subrule(t,r,n){return this.subruleInternal(r,t,n)}option(t,r){return this.optionInternal(r,t)}or(t,r){return this.orInternal(r,t)}many(t,r){return this.manyInternal(t,r)}atLeastOne(t,r){return this.atLeastOneInternal(t,r)}CONSUME(t,r){return this.consumeInternal(t,0,r)}CONSUME1(t,r){return this.consumeInternal(t,1,r)}CONSUME2(t,r){return this.consumeInternal(t,2,r)}CONSUME3(t,r){return this.consumeInternal(t,3,r)}CONSUME4(t,r){return this.consumeInternal(t,4,r)}CONSUME5(t,r){return this.consumeInternal(t,5,r)}CONSUME6(t,r){return this.consumeInternal(t,6,r)}CONSUME7(t,r){return this.consumeInternal(t,7,r)}CONSUME8(t,r){return this.consumeInternal(t,8,r)}CONSUME9(t,r){return this.consumeInternal(t,9,r)}SUBRULE(t,r){return this.subruleInternal(t,0,r)}SUBRULE1(t,r){return this.subruleInternal(t,1,r)}SUBRULE2(t,r){return this.subruleInternal(t,2,r)}SUBRULE3(t,r){return this.subruleInternal(t,3,r)}SUBRULE4(t,r){return this.subruleInternal(t,4,r)}SUBRULE5(t,r){return this.subruleInternal(t,5,r)}SUBRULE6(t,r){return this.subruleInternal(t,6,r)}SUBRULE7(t,r){return this.subruleInternal(t,7,r)}SUBRULE8(t,r){return this.subruleInternal(t,8,r)}SUBRULE9(t,r){return this.subruleInternal(t,9,r)}OPTION(t){return this.optionInternal(t,0)}OPTION1(t){return this.optionInternal(t,1)}OPTION2(t){return this.optionInternal(t,2)}OPTION3(t){return this.optionInternal(t,3)}OPTION4(t){return this.optionInternal(t,4)}OPTION5(t){return this.optionInternal(t,5)}OPTION6(t){return this.optionInternal(t,6)}OPTION7(t){return this.optionInternal(t,7)}OPTION8(t){return this.optionInternal(t,8)}OPTION9(t){return this.optionInternal(t,9)}OR(t){return this.orInternal(t,0)}OR1(t){return this.orInternal(t,1)}OR2(t){return this.orInternal(t,2)}OR3(t){return this.orInternal(t,3)}OR4(t){return this.orInternal(t,4)}OR5(t){return this.orInternal(t,5)}OR6(t){return this.orInternal(t,6)}OR7(t){return this.orInternal(t,7)}OR8(t){return this.orInternal(t,8)}OR9(t){return this.orInternal(t,9)}MANY(t){this.manyInternal(0,t)}MANY1(t){this.manyInternal(1,t)}MANY2(t){this.manyInternal(2,t)}MANY3(t){this.manyInternal(3,t)}MANY4(t){this.manyInternal(4,t)}MANY5(t){this.manyInternal(5,t)}MANY6(t){this.manyInternal(6,t)}MANY7(t){this.manyInternal(7,t)}MANY8(t){this.manyInternal(8,t)}MANY9(t){this.manyInternal(9,t)}MANY_SEP(t){this.manySepFirstInternal(0,t)}MANY_SEP1(t){this.manySepFirstInternal(1,t)}MANY_SEP2(t){this.manySepFirstInternal(2,t)}MANY_SEP3(t){this.manySepFirstInternal(3,t)}MANY_SEP4(t){this.manySepFirstInternal(4,t)}MANY_SEP5(t){this.manySepFirstInternal(5,t)}MANY_SEP6(t){this.manySepFirstInternal(6,t)}MANY_SEP7(t){this.manySepFirstInternal(7,t)}MANY_SEP8(t){this.manySepFirstInternal(8,t)}MANY_SEP9(t){this.manySepFirstInternal(9,t)}AT_LEAST_ONE(t){this.atLeastOneInternal(0,t)}AT_LEAST_ONE1(t){return this.atLeastOneInternal(1,t)}AT_LEAST_ONE2(t){this.atLeastOneInternal(2,t)}AT_LEAST_ONE3(t){this.atLeastOneInternal(3,t)}AT_LEAST_ONE4(t){this.atLeastOneInternal(4,t)}AT_LEAST_ONE5(t){this.atLeastOneInternal(5,t)}AT_LEAST_ONE6(t){this.atLeastOneInternal(6,t)}AT_LEAST_ONE7(t){this.atLeastOneInternal(7,t)}AT_LEAST_ONE8(t){this.atLeastOneInternal(8,t)}AT_LEAST_ONE9(t){this.atLeastOneInternal(9,t)}AT_LEAST_ONE_SEP(t){this.atLeastOneSepFirstInternal(0,t)}AT_LEAST_ONE_SEP1(t){this.atLeastOneSepFirstInternal(1,t)}AT_LEAST_ONE_SEP2(t){this.atLeastOneSepFirstInternal(2,t)}AT_LEAST_ONE_SEP3(t){this.atLeastOneSepFirstInternal(3,t)}AT_LEAST_ONE_SEP4(t){this.atLeastOneSepFirstInternal(4,t)}AT_LEAST_ONE_SEP5(t){this.atLeastOneSepFirstInternal(5,t)}AT_LEAST_ONE_SEP6(t){this.atLeastOneSepFirstInternal(6,t)}AT_LEAST_ONE_SEP7(t){this.atLeastOneSepFirstInternal(7,t)}AT_LEAST_ONE_SEP8(t){this.atLeastOneSepFirstInternal(8,t)}AT_LEAST_ONE_SEP9(t){this.atLeastOneSepFirstInternal(9,t)}RULE(t,r,n=pr){if(D(this.definedRulesNames,t)){let a={message:Lt.buildDuplicateRuleNameError({topLevelRule:t,grammarName:this.className}),type:rt.DUPLICATE_RULE_NAME,ruleName:t};this.definitionErrors.push(a)}this.definedRulesNames.push(t);let o=this.defineRule(t,r,n);return this[t]=o,o}OVERRIDE_RULE(t,r,n=pr){let o=ql(t,this.definedRulesNames,this.className);this.definitionErrors=this.definitionErrors.concat(o);let i=this.defineRule(t,r,n);return this[t]=i,i}BACKTRACK(t,r){return function(){this.isBackTrackingStack.push(1);let n=this.saveRecogState();try{return t.apply(this,r),!0}catch(o){if(Te(o))return!1;throw o}finally{this.reloadRecogState(n),this.isBackTrackingStack.pop()}}}getGAstProductions(){return this.gastProductionsCache}getSerializedGastProductions(){return Rn(v(this.gastProductionsCache))}};var Zn=class{initRecognizerEngine(t,r){if(this.className=this.constructor.name,this.shortRuleNameToFull={},this.fullRuleNameToShort={},this.ruleShortNameIdx=256,this.tokenMatcher=ar,this.subruleIdx=0,this.definedRulesNames=[],this.tokensMap={},this.isBackTrackingStack=[],this.RULE_STACK=[],this.RULE_OCCURRENCE_STACK=[],this.gastProductionsCache={},T(r,"serializedGrammar"))throw Error(`The Parser's configuration can no longer contain a <serializedGrammar> property.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_6-0-0
	For Further details.`);if(E(t)){if(N(t))throw Error(`A Token Vocabulary cannot be empty.
	Note that the first argument for the parser constructor
	is no longer a Token vector (since v4.0).`);if(typeof t[0].startOffset=="number")throw Error(`The Parser constructor no longer accepts a token vector as the first argument.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_4-0-0
	For Further details.`)}if(E(t))this.tokensMap=$(t,(i,a)=>(i[a.name]=a,i),{});else if(T(t,"modes")&&st(j(v(t.modes)),Cl)){let i=j(v(t.modes)),a=xe(i);this.tokensMap=$(a,(s,l)=>(s[l.name]=l,s),{})}else if(W(t))this.tokensMap=b(t);else throw new Error("<tokensDictionary> argument must be An Array of Token constructors, A dictionary of Token constructors or an IMultiModeLexerDefinition");this.tokensMap.EOF=vt;let n=T(t,"modes")?j(v(t.modes)):v(t),o=st(n,i=>N(i.categoryMatches));this.tokenMatcher=o?ar:qt,Zt(v(this.tokensMap))}defineRule(t,r,n){if(this.selfAnalysisDone)throw Error(`Grammar rule <${t}> may not be defined after the 'performSelfAnalysis' method has been called'
Make sure that all grammar rule definitions are done before 'performSelfAnalysis' is called.`);let o=T(n,"resyncEnabled")?n.resyncEnabled:pr.resyncEnabled,i=T(n,"recoveryValueFunc")?n.recoveryValueFunc:pr.recoveryValueFunc,a=this.ruleShortNameIdx<<4+8;this.ruleShortNameIdx++,this.shortRuleNameToFull[a]=t,this.fullRuleNameToShort[t]=a;let s;return this.outputCst===!0?s=function(...c){try{this.ruleInvocationStateUpdate(a,t,this.subruleIdx),r.apply(this,c);let u=this.CST_STACK[this.CST_STACK.length-1];return this.cstPostRule(u),u}catch(u){return this.invokeRuleCatch(u,o,i)}finally{this.ruleFinallyStateUpdate()}}:s=function(...c){try{return this.ruleInvocationStateUpdate(a,t,this.subruleIdx),r.apply(this,c)}catch(u){return this.invokeRuleCatch(u,o,i)}finally{this.ruleFinallyStateUpdate()}},Object.assign(s,{ruleName:t,originalGrammarAction:r})}invokeRuleCatch(t,r,n){let o=this.RULE_STACK.length===1,i=r&&!this.isBackTracking()&&this.recoveryEnabled;if(Te(t)){let a=t;if(i){let s=this.findReSyncTokenType();if(this.isInCurrentRuleReSyncSet(s))if(a.resyncedTokens=this.reSyncTo(s),this.outputCst){let l=this.CST_STACK[this.CST_STACK.length-1];return l.recoveredNode=!0,l}else return n(t);else{if(this.outputCst){let l=this.CST_STACK[this.CST_STACK.length-1];l.recoveredNode=!0,a.partialCstResult=l}throw a}}else{if(o)return this.moveToTerminatedState(),n(t);throw a}}else throw t}optionInternal(t,r){let n=this.getKeyForAutomaticLookahead(512,r);return this.optionInternalLogic(t,r,n)}optionInternalLogic(t,r,n){let o=this.getLaFuncFromCache(n),i;if(typeof t!="function"){i=t.DEF;let a=t.GATE;if(a!==void 0){let s=o;o=()=>a.call(this)&&s.call(this)}}else i=t;if(o.call(this)===!0)return i.call(this)}atLeastOneInternal(t,r){let n=this.getKeyForAutomaticLookahead(1024,t);return this.atLeastOneInternalLogic(t,r,n)}atLeastOneInternalLogic(t,r,n){let o=this.getLaFuncFromCache(n),i;if(typeof r!="function"){i=r.DEF;let a=r.GATE;if(a!==void 0){let s=o;o=()=>a.call(this)&&s.call(this)}}else i=r;if(o.call(this)===!0){let a=this.doSingleRepetition(i);for(;o.call(this)===!0&&a===!0;)a=this.doSingleRepetition(i)}else throw this.raiseEarlyExitException(t,G.REPETITION_MANDATORY,r.ERR_MSG);this.attemptInRepetitionRecovery(this.atLeastOneInternal,[t,r],o,1024,t,wn)}atLeastOneSepFirstInternal(t,r){let n=this.getKeyForAutomaticLookahead(1536,t);this.atLeastOneSepFirstInternalLogic(t,r,n)}atLeastOneSepFirstInternalLogic(t,r,n){let o=r.DEF,i=r.SEP;if(this.getLaFuncFromCache(n).call(this)===!0){o.call(this);let s=()=>this.tokenMatcher(this.LA(1),i);for(;this.tokenMatcher(this.LA(1),i)===!0;)this.CONSUME(i),o.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[t,i,s,o,Pr],s,1536,t,Pr)}else throw this.raiseEarlyExitException(t,G.REPETITION_MANDATORY_WITH_SEPARATOR,r.ERR_MSG)}manyInternal(t,r){let n=this.getKeyForAutomaticLookahead(768,t);return this.manyInternalLogic(t,r,n)}manyInternalLogic(t,r,n){let o=this.getLaFuncFromCache(n),i;if(typeof r!="function"){i=r.DEF;let s=r.GATE;if(s!==void 0){let l=o;o=()=>s.call(this)&&l.call(this)}}else i=r;let a=!0;for(;o.call(this)===!0&&a===!0;)a=this.doSingleRepetition(i);this.attemptInRepetitionRecovery(this.manyInternal,[t,r],o,768,t,Mn,a)}manySepFirstInternal(t,r){let n=this.getKeyForAutomaticLookahead(1280,t);this.manySepFirstInternalLogic(t,r,n)}manySepFirstInternalLogic(t,r,n){let o=r.DEF,i=r.SEP;if(this.getLaFuncFromCache(n).call(this)===!0){o.call(this);let s=()=>this.tokenMatcher(this.LA(1),i);for(;this.tokenMatcher(this.LA(1),i)===!0;)this.CONSUME(i),o.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[t,i,s,o,br],s,1280,t,br)}}repetitionSepSecondInternal(t,r,n,o,i){for(;n();)this.CONSUME(r),o.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[t,r,n,o,i],n,1536,t,i)}doSingleRepetition(t){let r=this.getLexerPosition();return t.call(this),this.getLexerPosition()>r}orInternal(t,r){let n=this.getKeyForAutomaticLookahead(256,r),o=E(t)?t:t.DEF,a=this.getLaFuncFromCache(n).call(this,o);if(a!==void 0)return o[a].ALT.call(this);this.raiseNoAltException(r,t.ERR_MSG)}ruleFinallyStateUpdate(){if(this.RULE_STACK.pop(),this.RULE_OCCURRENCE_STACK.pop(),this.cstFinallyStateUpdate(),this.RULE_STACK.length===0&&this.isAtEndOfInput()===!1){let t=this.LA(1),r=this.errorMessageProvider.buildNotAllInputParsedMessage({firstRedundant:t,ruleName:this.getCurrRuleFullName()});this.SAVE_ERROR(new Fr(r,t))}}subruleInternal(t,r,n){let o;try{let i=n!==void 0?n.ARGS:void 0;return this.subruleIdx=r,o=t.apply(this,i),this.cstPostNonTerminal(o,n!==void 0&&n.LABEL!==void 0?n.LABEL:t.ruleName),o}catch(i){throw this.subruleInternalError(i,n,t.ruleName)}}subruleInternalError(t,r,n){throw Te(t)&&t.partialCstResult!==void 0&&(this.cstPostNonTerminal(t.partialCstResult,r!==void 0&&r.LABEL!==void 0?r.LABEL:n),delete t.partialCstResult),t}consumeInternal(t,r,n){let o;try{let i=this.LA(1);this.tokenMatcher(i,t)===!0?(this.consumeToken(),o=i):this.consumeInternalError(t,i,n)}catch(i){o=this.consumeInternalRecovery(t,r,i)}return this.cstPostTerminal(n!==void 0&&n.LABEL!==void 0?n.LABEL:t.name,o),o}consumeInternalError(t,r,n){let o,i=this.LA(0);throw n!==void 0&&n.ERR_MSG?o=n.ERR_MSG:o=this.errorMessageProvider.buildMismatchTokenMessage({expected:t,actual:r,previous:i,ruleName:this.getCurrRuleFullName()}),this.SAVE_ERROR(new ve(o,r,i))}consumeInternalRecovery(t,r,n){if(this.recoveryEnabled&&n.name==="MismatchedTokenException"&&!this.isBackTracking()){let o=this.getFollowsForInRuleRecovery(t,r);try{return this.tryInRuleRecovery(t,o)}catch(i){throw i.name===Wo?n:i}}else throw n}saveRecogState(){let t=this.errors,r=b(this.RULE_STACK);return{errors:t,lexerState:this.exportLexerState(),RULE_STACK:r,CST_STACK:this.CST_STACK}}reloadRecogState(t){this.errors=t.errors,this.importLexerState(t.lexerState),this.RULE_STACK=t.RULE_STACK}ruleInvocationStateUpdate(t,r,n){this.RULE_OCCURRENCE_STACK.push(n),this.RULE_STACK.push(t),this.cstInvocationStateUpdate(r)}isBackTracking(){return this.isBackTrackingStack.length!==0}getCurrRuleFullName(){let t=this.getLastExplicitRuleShortName();return this.shortRuleNameToFull[t]}shortRuleNameToFullName(t){return this.shortRuleNameToFull[t]}isAtEndOfInput(){return this.tokenMatcher(this.LA(1),vt)}reset(){this.resetLexerState(),this.subruleIdx=0,this.isBackTrackingStack=[],this.errors=[],this.RULE_STACK=[],this.CST_STACK=[],this.RULE_OCCURRENCE_STACK=[]}};var Jn=class{initErrorHandler(t){this._errors=[],this.errorMessageProvider=T(t,"errorMessageProvider")?t.errorMessageProvider:ut.errorMessageProvider}SAVE_ERROR(t){if(Te(t))return t.context={ruleStack:this.getHumanReadableRuleStack(),ruleOccurrenceStack:b(this.RULE_OCCURRENCE_STACK)},this._errors.push(t),t;throw Error("Trying to save an Error which is not a RecognitionException")}get errors(){return b(this._errors)}set errors(t){this._errors=t}raiseEarlyExitException(t,r,n){let o=this.getCurrRuleFullName(),i=this.getGAstProductions()[o],s=fr(t,i,r,this.maxLookahead)[0],l=[];for(let c=1;c<=this.maxLookahead;c++)l.push(this.LA(c));let f=this.errorMessageProvider.buildEarlyExitMessage({expectedIterationPaths:s,actual:l,previous:this.LA(0),customUserDescription:n,ruleName:o});throw this.SAVE_ERROR(new Ur(f,this.LA(1),this.LA(0)))}raiseNoAltException(t,r){let n=this.getCurrRuleFullName(),o=this.getGAstProductions()[n],i=lr(t,o,this.maxLookahead),a=[];for(let f=1;f<=this.maxLookahead;f++)a.push(this.LA(f));let s=this.LA(0),l=this.errorMessageProvider.buildNoViableAltMessage({expectedPathsPerAlt:i,actual:a,previous:s,customUserDescription:r,ruleName:this.getCurrRuleFullName()});throw this.SAVE_ERROR(new wr(l,this.LA(1),s))}};var Qn=class{initContentAssist(){}computeContentAssist(t,r){let n=this.gastProductionsCache[t];if(ft(n))throw Error(`Rule ->${t}<- does not exist in this grammar.`);return Un([n],r,this.tokenMatcher,this.maxLookahead)}getNextPossibleTokenTypes(t){let r=ot(t.ruleStack),o=this.getGAstProductions()[r];return new kn(o,t).startWalking()}};var ro={description:"This Object indicates the Parser is during Recording Phase"};Object.freeze(ro);var pf=!0,hf=Math.pow(2,8)-1,mf=bn({name:"RECORDING_PHASE_TOKEN",pattern:it.NA});Zt([mf]);var xf=Ce(mf,`This IToken indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,-1,-1,-1,-1,-1,-1);Object.freeze(xf);var PT={name:`This CSTNode indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,children:{}},to=class{initGastRecorder(t){this.recordingProdStack=[],this.RECORDING_PHASE=!1}enableRecording(){this.RECORDING_PHASE=!0,this.TRACE_INIT("Enable Recording",()=>{for(let t=0;t<10;t++){let r=t>0?t:"";this[`CONSUME${r}`]=function(n,o){return this.consumeInternalRecord(n,t,o)},this[`SUBRULE${r}`]=function(n,o){return this.subruleInternalRecord(n,t,o)},this[`OPTION${r}`]=function(n){return this.optionInternalRecord(n,t)},this[`OR${r}`]=function(n){return this.orInternalRecord(n,t)},this[`MANY${r}`]=function(n){this.manyInternalRecord(t,n)},this[`MANY_SEP${r}`]=function(n){this.manySepFirstInternalRecord(t,n)},this[`AT_LEAST_ONE${r}`]=function(n){this.atLeastOneInternalRecord(t,n)},this[`AT_LEAST_ONE_SEP${r}`]=function(n){this.atLeastOneSepFirstInternalRecord(t,n)}}this.consume=function(t,r,n){return this.consumeInternalRecord(r,t,n)},this.subrule=function(t,r,n){return this.subruleInternalRecord(r,t,n)},this.option=function(t,r){return this.optionInternalRecord(r,t)},this.or=function(t,r){return this.orInternalRecord(r,t)},this.many=function(t,r){this.manyInternalRecord(t,r)},this.atLeastOne=function(t,r){this.atLeastOneInternalRecord(t,r)},this.ACTION=this.ACTION_RECORD,this.BACKTRACK=this.BACKTRACK_RECORD,this.LA=this.LA_RECORD})}disableRecording(){this.RECORDING_PHASE=!1,this.TRACE_INIT("Deleting Recording methods",()=>{let t=this;for(let r=0;r<10;r++){let n=r>0?r:"";delete t[`CONSUME${n}`],delete t[`SUBRULE${n}`],delete t[`OPTION${n}`],delete t[`OR${n}`],delete t[`MANY${n}`],delete t[`MANY_SEP${n}`],delete t[`AT_LEAST_ONE${n}`],delete t[`AT_LEAST_ONE_SEP${n}`]}delete t.consume,delete t.subrule,delete t.option,delete t.or,delete t.many,delete t.atLeastOne,delete t.ACTION,delete t.BACKTRACK,delete t.LA})}ACTION_RECORD(t){}BACKTRACK_RECORD(t,r){return()=>!0}LA_RECORD(t){return ur}topLevelRuleRecord(t,r){try{let n=new mt({definition:[],name:t});return n.name=t,this.recordingProdStack.push(n),r.call(this),this.recordingProdStack.pop(),n}catch(n){if(n.KNOWN_RECORDER_ERROR!==!0)try{n.message=n.message+`
	 This error was thrown during the "grammar recording phase" For more info see:
	https://chevrotain.io/docs/guide/internals.html#grammar-recording`}catch{throw n}throw n}}optionInternalRecord(t,r){return Gr.call(this,U,t,r)}atLeastOneInternalRecord(t,r){Gr.call(this,Y,r,t)}atLeastOneSepFirstInternalRecord(t,r){Gr.call(this,q,r,t,pf)}manyInternalRecord(t,r){Gr.call(this,L,r,t)}manySepFirstInternalRecord(t,r){Gr.call(this,H,r,t,pf)}orInternalRecord(t,r){return kT.call(this,t,r)}subruleInternalRecord(t,r,n){if(eo(r),!t||T(t,"ruleName")===!1){let s=new Error(`<SUBRULE${df(r)}> argument is invalid expecting a Parser method reference but got: <${JSON.stringify(t)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw s.KNOWN_RECORDER_ERROR=!0,s}let o=Gt(this.recordingProdStack),i=t.ruleName,a=new P({idx:r,nonTerminalName:i,label:n?.LABEL,referencedRule:void 0});return o.definition.push(a),this.outputCst?PT:ro}consumeInternalRecord(t,r,n){if(eo(r),!Co(t)){let a=new Error(`<CONSUME${df(r)}> argument is invalid expecting a TokenType reference but got: <${JSON.stringify(t)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw a.KNOWN_RECORDER_ERROR=!0,a}let o=Gt(this.recordingProdStack),i=new C({idx:r,terminalType:t,label:n?.LABEL});return o.definition.push(i),xf}};function Gr(e,t,r,n=!1){eo(r);let o=Gt(this.recordingProdStack),i=ht(t)?t:t.DEF,a=new e({definition:[],idx:r});return n&&(a.separator=t.SEP),T(t,"MAX_LOOKAHEAD")&&(a.maxLookahead=t.MAX_LOOKAHEAD),this.recordingProdStack.push(a),i.call(this),o.definition.push(a),this.recordingProdStack.pop(),ro}function kT(e,t){eo(t);let r=Gt(this.recordingProdStack),n=E(e)===!1,o=n===!1?e:e.DEF,i=new z({definition:[],idx:t,ignoreAmbiguities:n&&e.IGNORE_AMBIGUITIES===!0});T(e,"MAX_LOOKAHEAD")&&(i.maxLookahead=e.MAX_LOOKAHEAD);let a=Re(o,s=>ht(s.GATE));return i.hasPredicates=a,r.definition.push(i),x(o,s=>{let l=new w({definition:[]});i.definition.push(l),T(s,"IGNORE_AMBIGUITIES")?l.ignoreAmbiguities=s.IGNORE_AMBIGUITIES:T(s,"GATE")&&(l.ignoreAmbiguities=!0),this.recordingProdStack.push(l),s.ALT.call(this),this.recordingProdStack.pop()}),ro}function df(e){return e===0?"":`${e}`}function eo(e){if(e<0||e>hf){let t=new Error(`Invalid DSL Method idx value: <${e}>
	Idx value must be a none negative value smaller than ${hf+1}`);throw t.KNOWN_RECORDER_ERROR=!0,t}}var no=class{initPerformanceTracer(t){if(T(t,"traceInitPerf")){let r=t.traceInitPerf,n=typeof r=="number";this.traceInitMaxIdent=n?r:1/0,this.traceInitPerf=n?r>0:r}else this.traceInitMaxIdent=0,this.traceInitPerf=ut.traceInitPerf;this.traceInitIndent=-1}TRACE_INIT(t,r){if(this.traceInitPerf===!0){this.traceInitIndent++;let n=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${n}--> <${t}>`);let{time:o,value:i}=yr(r),a=o>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&a(`${n}<-- <${t}> time: ${o}ms`),this.traceInitIndent--,i}else return r()}};function gf(e,t){t.forEach(r=>{let n=r.prototype;Object.getOwnPropertyNames(n).forEach(o=>{if(o==="constructor")return;let i=Object.getOwnPropertyDescriptor(n,o);i&&(i.get||i.set)?Object.defineProperty(e.prototype,o,i):e.prototype[o]=r.prototype[o]})})}var ur=Ce(vt,"",NaN,NaN,NaN,NaN,NaN,NaN);Object.freeze(ur);var ut=Object.freeze({recoveryEnabled:!1,maxLookahead:3,dynamicTokensEnabled:!1,outputCst:!0,errorMessageProvider:Pn,nodeLocationTracking:"none",traceInitPerf:!1,skipValidations:!1}),pr=Object.freeze({recoveryValueFunc:()=>{},resyncEnabled:!0}),rt;(function(e){e[e.INVALID_RULE_NAME=0]="INVALID_RULE_NAME",e[e.DUPLICATE_RULE_NAME=1]="DUPLICATE_RULE_NAME",e[e.INVALID_RULE_OVERRIDE=2]="INVALID_RULE_OVERRIDE",e[e.DUPLICATE_PRODUCTIONS=3]="DUPLICATE_PRODUCTIONS",e[e.UNRESOLVED_SUBRULE_REF=4]="UNRESOLVED_SUBRULE_REF",e[e.LEFT_RECURSION=5]="LEFT_RECURSION",e[e.NONE_LAST_EMPTY_ALT=6]="NONE_LAST_EMPTY_ALT",e[e.AMBIGUOUS_ALTS=7]="AMBIGUOUS_ALTS",e[e.CONFLICT_TOKENS_RULES_NAMESPACE=8]="CONFLICT_TOKENS_RULES_NAMESPACE",e[e.INVALID_TOKEN_NAME=9]="INVALID_TOKEN_NAME",e[e.NO_NON_EMPTY_LOOKAHEAD=10]="NO_NON_EMPTY_LOOKAHEAD",e[e.AMBIGUOUS_PREFIX_ALTS=11]="AMBIGUOUS_PREFIX_ALTS",e[e.TOO_MANY_ALTS=12]="TOO_MANY_ALTS",e[e.CUSTOM_LOOKAHEAD_VALIDATION=13]="CUSTOM_LOOKAHEAD_VALIDATION"})(rt||(rt={}));function MT(e=void 0){return function(){return e}}var hr=class e{static performSelfAnalysis(t){throw Error("The **static** `performSelfAnalysis` method has been deprecated.	\nUse the **instance** method with the same name instead.")}performSelfAnalysis(){this.TRACE_INIT("performSelfAnalysis",()=>{let t;this.selfAnalysisDone=!0;let r=this.className;this.TRACE_INIT("toFastProps",()=>{Sr(this)}),this.TRACE_INIT("Grammar Recording",()=>{try{this.enableRecording(),x(this.definedRulesNames,o=>{let a=this[o].originalGrammarAction,s;this.TRACE_INIT(`${o} Rule`,()=>{s=this.topLevelRuleRecord(o,a)}),this.gastProductionsCache[o]=s})}finally{this.disableRecording()}});let n=[];if(this.TRACE_INIT("Grammar Resolving",()=>{n=tf({rules:v(this.gastProductionsCache)}),this.definitionErrors=this.definitionErrors.concat(n)}),this.TRACE_INIT("Grammar Validations",()=>{if(N(n)&&this.skipValidations===!1){let o=ef({rules:v(this.gastProductionsCache),tokenTypes:v(this.tokensMap),errMsgProvider:Lt,grammarName:r}),i=Vl({lookaheadStrategy:this.lookaheadStrategy,rules:v(this.gastProductionsCache),tokenTypes:v(this.tokensMap),grammarName:r});this.definitionErrors=this.definitionErrors.concat(o,i)}}),N(this.definitionErrors)&&(this.recoveryEnabled&&this.TRACE_INIT("computeAllProdsFollows",()=>{let o=ll(v(this.gastProductionsCache));this.resyncFollows=o}),this.TRACE_INIT("ComputeLookaheadFunctions",()=>{var o,i;(i=(o=this.lookaheadStrategy).initialize)===null||i===void 0||i.call(o,{rules:v(this.gastProductionsCache)}),this.preComputeLookaheadFunctions(v(this.gastProductionsCache))})),!e.DEFER_DEFINITION_ERRORS_HANDLING&&!N(this.definitionErrors))throw t=d(this.definitionErrors,o=>o.message),new Error(`Parser Definition Errors detected:
 ${t.join(`
-------------------------------
`)}`)})}constructor(t,r){this.definitionErrors=[],this.selfAnalysisDone=!1;let n=this;if(n.initErrorHandler(r),n.initLexerAdapter(),n.initLooksAhead(r),n.initRecognizerEngine(t,r),n.initRecoverable(r),n.initTreeBuilder(r),n.initContentAssist(),n.initGastRecorder(r),n.initPerformanceTracer(r),T(r,"ignoredIssues"))throw new Error(`The <ignoredIssues> IParserConfig property has been deprecated.
	Please use the <IGNORE_AMBIGUITIES> flag on the relevant DSL method instead.
	See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#IGNORING_AMBIGUITIES
	For further details.`);this.skipValidations=T(r,"skipValidations")?r.skipValidations:ut.skipValidations}};hr.DEFER_DEFINITION_ERRORS_HANDLING=!1;gf(hr,[Wn,$n,Xn,Yn,Zn,qn,Jn,Qn,to,no]);var qo=class extends hr{constructor(t,r=ut){let n=b(r);n.outputCst=!0,super(t,n)}},Zo=class extends hr{constructor(t,r=ut){let n=b(r);n.outputCst=!1,super(t,n)}};function Tf(e){let t=new Jo,r=v(e);return d(r,n=>t.visitRule(n))}var Jo=class extends ct{visitRule(t){let r=this.visitEach(t.definition),n=Ir(r,i=>i.propertyName),o=d(n,(i,a)=>{let s=!Re(i,f=>!f.canBeNull),l=i[0].type;return i.length>1&&(l=d(i,f=>f.type)),{name:a,type:l,optional:s}});return{name:t.name,properties:o}}visitAlternative(t){return this.visitEachAndOverrideWith(t.definition,{canBeNull:!0})}visitOption(t){return this.visitEachAndOverrideWith(t.definition,{canBeNull:!0})}visitRepetition(t){return this.visitEachAndOverrideWith(t.definition,{canBeNull:!0})}visitRepetitionMandatory(t){return this.visitEach(t.definition)}visitRepetitionMandatoryWithSeparator(t){return this.visitEach(t.definition).concat({propertyName:t.separator.name,canBeNull:!0,type:oo(t.separator)})}visitRepetitionWithSeparator(t){return this.visitEachAndOverrideWith(t.definition,{canBeNull:!0}).concat({propertyName:t.separator.name,canBeNull:!0,type:oo(t.separator)})}visitAlternation(t){return this.visitEachAndOverrideWith(t.definition,{canBeNull:!0})}visitTerminal(t){return[{propertyName:t.label||t.terminalType.name,canBeNull:!1,type:oo(t)}]}visitNonTerminal(t){return[{propertyName:t.label||t.nonTerminalName,canBeNull:!1,type:oo(t)}]}visitEachAndOverrideWith(t,r){return d(this.visitEach(t),n=>tt({},n,r))}visitEach(t){return j(d(t,r=>this.visit(r)))}};function oo(e){return e instanceof P?{kind:"rule",name:e.referencedRule.name}:{kind:"token"}}function Af(e,t){let r=[];return r=r.concat('import type { CstNode, ICstVisitor, IToken } from "chevrotain";'),r=r.concat(j(d(e,n=>wT(n)))),t.includeVisitorInterface&&(r=r.concat(BT(t.visitorInterfaceName,e))),r.join(`

`)+`
`}function wT(e){let t=FT(e),r=UT(e);return[t,r]}function FT(e){let t=If(e.name),r=Qo(e.name);return`export interface ${t} extends CstNode {
  name: "${e.name}";
  children: ${r};
}`}function UT(e){return`export type ${Qo(e.name)} = {
  ${d(e.properties,r=>DT(r)).join(`
  `)}
};`}function DT(e){let t=WT(e.type);return`${e.name}${e.optional?"?":""}: ${t}[];`}function BT(e,t){return`export interface ${e}<IN, OUT> extends ICstVisitor<IN, OUT> {
  ${d(t,r=>GT(r)).join(`
  `)}
}`}function GT(e){let t=Qo(e.name);return`${e.name}(children: ${t}, param?: IN): OUT;`}function WT(e){if(E(e)){let t=xe(d(e,n=>Ef(n)));return"("+$(t,(n,o)=>n+" | "+o)+")"}else return Ef(e)}function Ef(e){return e.kind==="token"?"IToken":If(e.name)}function If(e){return rn(e)+"CstNode"}function Qo(e){return rn(e)+"CstChildren"}var KT={includeVisitorInterface:!0,visitorInterfaceName:"ICstNodeVisitor"};function jT(e,t){let r=Object.assign(Object.assign({},KT),t),n=Tf(e);return Af(n,r)}function $T(e,{resourceBase:t=`https://unpkg.com/chevrotain@${jr}/diagrams/`,css:r=`https://unpkg.com/chevrotain@${jr}/diagrams/diagrams.css`}={}){let n=`
<!-- This is a generated file -->
<!DOCTYPE html>
<meta charset="utf-8">
<style>
  body {
    background-color: hsl(30, 20%, 95%)
  }
</style>

`,o=`
<link rel='stylesheet' href='${r}'>
`,i=`
<script src='${t}vendor/railroad-diagrams.js'><\/script>
<script src='${t}src/diagrams_builder.js'><\/script>
<script src='${t}src/diagrams_behavior.js'><\/script>
<script src='${t}src/main.js'><\/script>
`,a=`
<div id="diagrams" align="center"></div>    
`,s=`
<script>
    window.serializedGrammar = ${JSON.stringify(e,null,"  ")};
<\/script>
`,l=`
<script>
    var diagramsDiv = document.getElementById("diagrams");
    main.drawDiagramsFromSerializedGrammar(serializedGrammar, diagramsDiv);
<\/script>
`;return n+o+i+a+s+l}function jw(){console.warn(`The clearCache function was 'soft' removed from the Chevrotain API.
	 It performs no action other than printing this message.
	 Please avoid using it as it will be completely removed in the future`)}var Rf=class{constructor(){throw new Error(`The Parser class has been deprecated, use CstParser or EmbeddedActionsParser instead.	
See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_7-0-0`)}};export{z as Alternation,w as Alternative,qo as CstParser,MT as EMPTY_ALT,vt as EOF,Ur as EarlyExitException,Zo as EmbeddedActionsParser,ct as GAstVisitor,Dr as LLkLookaheadStrategy,it as Lexer,B as LexerDefinitionErrorType,ve as MismatchedTokenException,wr as NoViableAltException,P as NonTerminal,Fr as NotAllInputParsedException,U as Option,Rf as Parser,rt as ParserDefinitionErrorType,L as Repetition,Y as RepetitionMandatory,q as RepetitionMandatoryWithSeparator,H as RepetitionWithSeparator,mt as Rule,C as Terminal,jr as VERSION,jw as clearCache,$T as createSyntaxDiagramsCode,bn as createToken,Ce as createTokenInstance,vo as defaultLexerErrorProvider,Pn as defaultParserErrorProvider,jT as generateCstDts,xT as getLookaheadPaths,Te as isRecognitionException,Rn as serializeGrammar,er as serializeProduction,Ne as tokenLabel,bo as tokenMatcher,pT as tokenName};
/*! Bundled license information:

lodash-es/lodash.js:
  (**
   * @license
   * Lodash (Custom Build) <https://lodash.com/>
   * Build: `lodash modularize exports="es" -o ./`
   * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
   * Released under MIT license <https://lodash.com/license>
   * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
   * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
   *)
*/
//# sourceMappingURL=chevrotain.min.mjs.map
