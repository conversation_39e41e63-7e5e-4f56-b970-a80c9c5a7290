{"version": 3, "file": "document-update-handler.js", "sourceRoot": "", "sources": ["../../src/lsp/document-update-handler.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAGhF,OAAO,EAAE,iCAAiC,EAAE,cAAc,EAAE,MAAM,uBAAuB,CAAC;AAC1F,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,GAAG,EAAE,MAAM,uBAAuB,CAAC;AA2D5C,MAAM,OAAO,4BAA4B;IAOrC,YAAY,QAA+B;QACvC,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC5D,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,SAAS,CAAC,eAAe,CAAC;QAC1D,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC;QACtD,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAC;QAEhD,IAAI,sBAAsB,GAAG,KAAK,CAAC;QACnC,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;;YAC9C,sBAAsB,GAAG,OAAO,CAAC,MAAA,MAAA,MAAM,CAAC,YAAY,CAAC,SAAS,0CAAE,qBAAqB,0CAAE,mBAAmB,CAAC,CAAC;QAChH,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE;YAChD,IAAI,sBAAsB,EAAE,CAAC;gBACzB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;YACvC,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAES,mBAAmB,CAAC,QAA+B;QACzD,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,CAAC;aACtD,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,gBAAgB,CAAC,cAAc,CAAC;aAC7D,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;aACxD,QAAQ,EAAE;aACV,OAAO,EAAE,CAAC;QACf,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC;YAC3C,MAAM,OAAO,GAA6C;gBACtD,QAAQ,EAAE,CAAC;wBACP,WAAW,EAAE,cAAc,CAAC,MAAM,KAAK,CAAC;4BACpC,CAAC,CAAC,QAAQ,cAAc,CAAC,CAAC,CAAC,EAAE;4BAC7B,CAAC,CAAC,SAAS,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG;qBAC7C,CAAC;aACL,CAAC;YACF,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,MAAM,CAAC,QAAQ,CAAC,iCAAiC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACjF,CAAC;IACL,CAAC;IAES,kBAAkB,CAAC,OAAc,EAAE,OAAc;QACvD,6DAA6D;QAC7D,+DAA+D;QAC/D,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QACvE,oEAAoE;QACpE,iEAAiE;QACjE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE;YAClC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QAC5F,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;YACX,qEAAqE;YACrE,OAAO,CAAC,KAAK,CAAC,qEAAqE,EAAE,GAAG,CAAC,CAAC;QAC9F,CAAC,CAAC,CAAC;IACP,CAAC;IAED,gBAAgB,CAAC,MAA6C;QAC1D,IAAI,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAClE,CAAC;IAED,qBAAqB,CAAC,MAAmC;QACrD,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;aACrC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,cAAc,CAAC,OAAO,CAAC;aAC9C,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;aACpB,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;aAC1B,OAAO,EAAE,CAAC;QACf,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;aACrC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,cAAc,CAAC,OAAO,CAAC;aAC9C,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;aACpB,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;aAC1B,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IACtD,CAAC;CACJ"}