import{D as p,I as s,L as c,M as m,a as e,b as d,c as n,d as i,e as u,f as G}from"./chunk-U22FQTB5.mjs";var r=class extends m{static{e(this,"GitGraphTokenBuilder")}constructor(){super(["gitGraph"])}};var l={parser:{TokenBuilder:e(()=>new r,"TokenBuilder"),ValueConverter:e(()=>new c,"ValueConverter")}};function x(o=u){let t=i(n(o),p),a=i(d({shared:t}),s,l);return t.ServiceRegistry.register(a),{shared:t,GitGraph:a}}e(x,"createGitGraphServices");export{l as a,x as b};
