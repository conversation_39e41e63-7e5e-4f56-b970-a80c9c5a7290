/******************************************************************************
 * Copyright 2023 TypeFox GmbH
 * This program and the accompanying materials are made available under the
 * terms of the MIT License, which is available in the project root.
 ******************************************************************************/
import type { AstNode } from '../../syntax-tree.js';
import type { TypeHierarchyItem } from 'vscode-languageserver';
import { AbstractTypeHierarchyProvider } from '../../lsp/type-hierarchy-provider.js';
export declare class LangiumGrammarTypeHierarchyProvider extends AbstractTypeHierarchyProvider {
    protected getSupertypes(node: AstNode): TypeHierarchyItem[] | undefined;
    protected getSubtypes(node: AstNode): TypeHierarchyItem[] | undefined;
}
//# sourceMappingURL=grammar-type-hierarchy.d.ts.map