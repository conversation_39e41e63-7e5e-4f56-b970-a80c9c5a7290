((e,t)=>{"object"==typeof exports&&"undefined"!=typeof module?t(require("@firebase/app-compat"),require("@firebase/app")):"function"==typeof define&&define.amd?define(["@firebase/app-compat","@firebase/app"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).firebase,e.firebase.INTERNAL.modularAPIs)})(this,function(Wn,qn){try{!(function(){function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var V=t(Wn);let r=()=>{},x={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(r,e){if(!Array.isArray(r))throw Error("encodeByteArray takes an array as a parameter");this.init_();var i=e?this.byteToCharMapWebSafe_:this.byteToCharMap_,n=[];for(let u=0;u<r.length;u+=3){var s=r[u],a=u+1<r.length,o=a?r[u+1]:0,l=u+2<r.length,c=l?r[u+2]:0;let e=(15&o)<<2|c>>6,t=63&c;l||(t=64,a)||(e=64),n.push(i[s>>2],i[(3&s)<<4|o>>4],i[e],i[t])}return n.join("")},encodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(e):this.encodeByteArray((t=>{var r=[];let i=0;for(let n=0;n<t.length;n++){let e=t.charCodeAt(n);e<128?r[i++]=e:(e<2048?r[i++]=e>>6|192:(55296==(64512&e)&&n+1<t.length&&56320==(64512&t.charCodeAt(n+1))?(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++n)),r[i++]=e>>18|240,r[i++]=e>>12&63|128):r[i++]=e>>12|224,r[i++]=e>>6&63|128),r[i++]=63&e|128)}return r})(e),t)},decodeString(r,i){if(this.HAS_NATIVE_SUPPORT&&!i)return atob(r);{var n=this.decodeStringToByteArray(r,i);var s=[];let e=0,t=0;for(;e<n.length;){var a,o,l,c=n[e++];c<128?s[t++]=String.fromCharCode(c):191<c&&c<224?(a=n[e++],s[t++]=String.fromCharCode((31&c)<<6|63&a)):239<c&&c<365?(a=((7&c)<<18|(63&n[e++])<<12|(63&n[e++])<<6|63&n[e++])-65536,s[t++]=String.fromCharCode(55296+(a>>10)),s[t++]=String.fromCharCode(56320+(1023&a))):(o=n[e++],l=n[e++],s[t++]=String.fromCharCode((15&c)<<12|(63&o)<<6|63&l))}return s.join("");return}},decodeStringToByteArray(e,t){this.init_();var r=t?this.charToByteMapWebSafe_:this.charToByteMap_,i=[];for(let l=0;l<e.length;){var n=r[e.charAt(l++)],s=l<e.length?r[e.charAt(l)]:0,a=++l<e.length?r[e.charAt(l)]:64,o=++l<e.length?r[e.charAt(l)]:64;if(++l,null==n||null==s||null==a||null==o)throw new j;i.push(n<<2|s>>4),64!==a&&(i.push(s<<4&240|a>>2),64!==o)&&i.push(a<<6&192|o)}return i},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),(this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e)>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};class j extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}let H=function(e){try{return x.decodeString(e,!0)}catch(e){console.error("base64Decode failed: ",e)}return null};let W=()=>(()=>{if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("Unable to locate global object.")})().__FIREBASE_DEFAULTS__,q=()=>{var e;return"undefined"!=typeof process&&void 0!==process.env&&(e=process.env.__FIREBASE_DEFAULTS__)?JSON.parse(e):void 0},B=()=>{if("undefined"!=typeof document){let e;try{e=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(e){return}var t=e&&H(e[1]);return t&&JSON.parse(t)}},z=()=>{try{return r()||W()||q()||B()}catch(e){console.info("Unable to get __FIREBASE_DEFAULTS__ due to: "+e)}};var i;function c(){return"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent?navigator.userAgent:""}function G(){var e=null==(e=z())?void 0:e.forceEnvironment;if("node"===e)return!0;if("browser"===e)return!1;try{return"[object process]"===Object.prototype.toString.call(global.process)}catch(e){return!1}}function K(){var e="object"==typeof chrome?chrome.runtime:"object"==typeof browser?browser.runtime:void 0;return"object"==typeof e&&void 0!==e.id}function J(){return"object"==typeof navigator&&"ReactNative"===navigator.product}function Y(){var e=c();return 0<=e.indexOf("MSIE ")||0<=e.indexOf("Trident/")}function $(){try{return"object"==typeof indexedDB}catch(e){return!1}}class u extends Error{constructor(e,t,r){super(t),this.code=e,this.customData=r,this.name="FirebaseError",Object.setPrototypeOf(this,u.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,X.prototype.create)}}class X{constructor(e,t,r){this.service=e,this.serviceName=t,this.errors=r}create(e,...t){var i,r=t[0]||{},n=this.service+"/"+e,s=this.errors[e],s=s?(i=r,s.replace(Q,(e,t)=>{var r=i[t];return null!=r?String(r):`<${t}?>`})):"Error",s=this.serviceName+`: ${s} (${n}).`;return new u(n,s,r)}}let Q=/\{\$([^}]+)}/g;function Z(e,t){if(e!==t){var r,i,n=Object.keys(e),s=Object.keys(t);for(r of n){if(!s.includes(r))return!1;var a=e[r],o=t[r];if(ee(a)&&ee(o)){if(!Z(a,o))return!1}else if(a!==o)return!1}for(i of s)if(!n.includes(i))return!1}return!0}function ee(e){return null!==e&&"object"==typeof e}function te(e){let t=[];for(let[r,i]of Object.entries(e))Array.isArray(i)?i.forEach(e=>{t.push(encodeURIComponent(r)+"="+encodeURIComponent(e))}):t.push(encodeURIComponent(r)+"="+encodeURIComponent(i));return t.length?"&"+t.join("&"):""}function re(e){let i={};return e.replace(/^\?/,"").split("&").forEach(e=>{var t,r;e&&([t,r]=e.split("="),i[decodeURIComponent(t)]=decodeURIComponent(r))}),i}function ie(e){var t,r=e.indexOf("?");return r?(t=e.indexOf("#",r),e.substring(r,0<t?t:void 0)):""}class ne{constructor(e,t){this.observers=[],this.unsubscribes=[],this.observerCount=0,this.task=Promise.resolve(),this.finalized=!1,this.onNoObservers=t,this.task.then(()=>{e(this)}).catch(e=>{this.error(e)})}next(t){this.forEachObserver(e=>{e.next(t)})}error(t){this.forEachObserver(e=>{e.error(t)}),this.close(t)}complete(){this.forEachObserver(e=>{e.complete()}),this.close()}subscribe(e,t,r){let i;if(void 0===e&&void 0===t&&void 0===r)throw new Error("Missing Observer.");void 0===(i=((e,t)=>{if("object"==typeof e&&null!==e)for(var r of t)if(r in e&&"function"==typeof e[r])return 1})(e,["next","error","complete"])?e:{next:e,error:t,complete:r}).next&&(i.next=se),void 0===i.error&&(i.error=se),void 0===i.complete&&(i.complete=se);var n=this.unsubscribeOne.bind(this,this.observers.length);return this.finalized&&this.task.then(()=>{try{this.finalError?i.error(this.finalError):i.complete()}catch(e){}}),this.observers.push(i),n}unsubscribeOne(e){void 0!==this.observers&&void 0!==this.observers[e]&&(delete this.observers[e],--this.observerCount,0===this.observerCount)&&void 0!==this.onNoObservers&&this.onNoObservers(this)}forEachObserver(t){if(!this.finalized)for(let e=0;e<this.observers.length;e++)this.sendOne(e,t)}sendOne(e,t){this.task.then(()=>{if(void 0!==this.observers&&void 0!==this.observers[e])try{t(this.observers[e])}catch(e){"undefined"!=typeof console&&console.error&&console.error(e)}})}close(e){this.finalized||(this.finalized=!0,void 0!==e&&(this.finalError=e),this.task.then(()=>{this.observers=void 0,this.onNoObservers=void 0}))}}function se(){}function o(e){return e&&e._delegate?e._delegate:e}(e=i=i||{})[e.DEBUG=0]="DEBUG",e[e.VERBOSE=1]="VERBOSE",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.SILENT=5]="SILENT";let ae={debug:i.DEBUG,verbose:i.VERBOSE,info:i.INFO,warn:i.WARN,error:i.ERROR,silent:i.SILENT},oe=i.INFO,le={[i.DEBUG]:"log",[i.VERBOSE]:"log",[i.INFO]:"info",[i.WARN]:"warn",[i.ERROR]:"error"},ce=(e,t,...r)=>{if(!(t<e.logLevel)){var i=(new Date).toISOString(),n=le[t];if(!n)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[n](`[${i}]  ${e.name}:`,...r)}};function ue(e,t){var r={};for(n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)t.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r}class de{constructor(e,t,r){this.name=e,this.instanceFactory=t,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}let he={FACEBOOK:"facebook.com",GITHUB:"github.com",GOOGLE:"google.com",PASSWORD:"password",PHONE:"phone",TWITTER:"twitter.com"},pe={EMAIL_SIGNIN:"EMAIL_SIGNIN",PASSWORD_RESET:"PASSWORD_RESET",RECOVER_EMAIL:"RECOVER_EMAIL",REVERT_SECOND_FACTOR_ADDITION:"REVERT_SECOND_FACTOR_ADDITION",VERIFY_AND_CHANGE_EMAIL:"VERIFY_AND_CHANGE_EMAIL",VERIFY_EMAIL:"VERIFY_EMAIL"};function me(){return{"dependent-sdk-initialized-before-auth":"Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK."}}function ge(){return{"admin-restricted-operation":"This operation is restricted to administrators only.","argument-error":"","app-not-authorized":"This app, identified by the domain where it's hosted, is not authorized to use Firebase Authentication with the provided API key. Review your key configuration in the Google API console.","app-not-installed":"The requested mobile application corresponding to the identifier (Android package name or iOS bundle ID) provided is not installed on this device.","captcha-check-failed":"The reCAPTCHA response token provided is either invalid, expired, already used or the domain associated with it does not match the list of whitelisted domains.","code-expired":"The SMS code has expired. Please re-send the verification code to try again.","cordova-not-ready":"Cordova framework is not ready.","cors-unsupported":"This browser is not supported.","credential-already-in-use":"This credential is already associated with a different user account.","custom-token-mismatch":"The custom token corresponds to a different audience.","requires-recent-login":"This operation is sensitive and requires recent authentication. Log in again before retrying this request.","dependent-sdk-initialized-before-auth":"Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK.","dynamic-link-not-activated":"Please activate Dynamic Links in the Firebase Console and agree to the terms and conditions.","email-change-needs-verification":"Multi-factor users must always have a verified email.","email-already-in-use":"The email address is already in use by another account.","emulator-config-failed":'Auth instance has already been used to make a network call. Auth can no longer be configured to use the emulator. Try calling "connectAuthEmulator()" sooner.',"expired-action-code":"The action code has expired.","cancelled-popup-request":"This operation has been cancelled due to another conflicting popup being opened.","internal-error":"An internal AuthError has occurred.","invalid-app-credential":"The phone verification request contains an invalid application verifier. The reCAPTCHA token response is either invalid or expired.","invalid-app-id":"The mobile app identifier is not registered for the current project.","invalid-user-token":"This user's credential isn't valid for this project. This can happen if the user's token has been tampered with, or if the user isn't for the project associated with this API key.","invalid-auth-event":"An internal AuthError has occurred.","invalid-verification-code":"The SMS verification code used to create the phone auth credential is invalid. Please resend the verification code sms and be sure to use the verification code provided by the user.","invalid-continue-uri":"The continue URL provided in the request is invalid.","invalid-cordova-configuration":"The following Cordova plugins must be installed to enable OAuth sign-in: cordova-plugin-buildinfo, cordova-universal-links-plugin, cordova-plugin-browsertab, cordova-plugin-inappbrowser and cordova-plugin-customurlscheme.","invalid-custom-token":"The custom token format is incorrect. Please check the documentation.","invalid-dynamic-link-domain":"The provided dynamic link domain is not configured or authorized for the current project.","invalid-email":"The email address is badly formatted.","invalid-emulator-scheme":"Emulator URL must start with a valid scheme (http:// or https://).","invalid-api-key":"Your API key is invalid, please check you have copied it correctly.","invalid-cert-hash":"The SHA-1 certificate hash provided is invalid.","invalid-credential":"The supplied auth credential is incorrect, malformed or has expired.","invalid-message-payload":"The email template corresponding to this action contains invalid characters in its message. Please fix by going to the Auth email templates section in the Firebase Console.","invalid-multi-factor-session":"The request does not contain a valid proof of first factor successful sign-in.","invalid-oauth-provider":"EmailAuthProvider is not supported for this operation. This operation only supports OAuth providers.","invalid-oauth-client-id":"The OAuth client ID provided is either invalid or does not match the specified API key.","unauthorized-domain":"This domain is not authorized for OAuth operations for your Firebase project. Edit the list of authorized domains from the Firebase console.","invalid-action-code":"The action code is invalid. This can happen if the code is malformed, expired, or has already been used.","wrong-password":"The password is invalid or the user does not have a password.","invalid-persistence-type":"The specified persistence type is invalid. It can only be local, session or none.","invalid-phone-number":"The format of the phone number provided is incorrect. Please enter the phone number in a format that can be parsed into E.164 format. E.164 phone numbers are written in the format [+][country code][subscriber number including area code].","invalid-provider-id":"The specified provider ID is invalid.","invalid-recipient-email":"The email corresponding to this action failed to send as the provided recipient email address is invalid.","invalid-sender":"The email template corresponding to this action contains an invalid sender email or name. Please fix by going to the Auth email templates section in the Firebase Console.","invalid-verification-id":"The verification ID used to create the phone auth credential is invalid.","invalid-tenant-id":"The Auth instance's tenant ID is invalid.","login-blocked":"Login blocked by user-provided method: {$originalMessage}","missing-android-pkg-name":"An Android Package Name must be provided if the Android App is required to be installed.","auth-domain-config-required":"Be sure to include authDomain when calling firebase.initializeApp(), by following the instructions in the Firebase console.","missing-app-credential":"The phone verification request is missing an application verifier assertion. A reCAPTCHA response token needs to be provided.","missing-verification-code":"The phone auth credential was created with an empty SMS verification code.","missing-continue-uri":"A continue URL must be provided in the request.","missing-iframe-start":"An internal AuthError has occurred.","missing-ios-bundle-id":"An iOS Bundle ID must be provided if an App Store ID is provided.","missing-or-invalid-nonce":"The request does not contain a valid nonce. This can occur if the SHA-256 hash of the provided raw nonce does not match the hashed nonce in the ID token payload.","missing-password":"A non-empty password must be provided","missing-multi-factor-info":"No second factor identifier is provided.","missing-multi-factor-session":"The request is missing proof of first factor successful sign-in.","missing-phone-number":"To send verification codes, provide a phone number for the recipient.","missing-verification-id":"The phone auth credential was created with an empty verification ID.","app-deleted":"This instance of FirebaseApp has been deleted.","multi-factor-info-not-found":"The user does not have a second factor matching the identifier provided.","multi-factor-auth-required":"Proof of ownership of a second factor is required to complete sign-in.","account-exists-with-different-credential":"An account already exists with the same email address but different sign-in credentials. Sign in using a provider associated with this email address.","network-request-failed":"A network AuthError (such as timeout, interrupted connection or unreachable host) has occurred.","no-auth-event":"An internal AuthError has occurred.","no-such-provider":"User was not linked to an account with the given provider.","null-user":"A null user object was provided as the argument for an operation which requires a non-null user object.","operation-not-allowed":"The given sign-in provider is disabled for this Firebase project. Enable it in the Firebase console, under the sign-in method tab of the Auth section.","operation-not-supported-in-this-environment":'This operation is not supported in the environment this application is running on. "location.protocol" must be http, https or chrome-extension and web storage must be enabled.',"popup-blocked":"Unable to establish a connection with the popup. It may have been blocked by the browser.","popup-closed-by-user":"The popup has been closed by the user before finalizing the operation.","provider-already-linked":"User can only be linked to one identity for the given provider.","quota-exceeded":"The project's quota for this operation has been exceeded.","redirect-cancelled-by-user":"The redirect operation has been cancelled by the user before finalizing.","redirect-operation-pending":"A redirect sign-in operation is already pending.","rejected-credential":"The request contains malformed or mismatching credentials.","second-factor-already-in-use":"The second factor is already enrolled on this account.","maximum-second-factor-count-exceeded":"The maximum allowed number of second factors on a user has been exceeded.","tenant-id-mismatch":"The provided tenant ID does not match the Auth instance's tenant ID",timeout:"The operation has timed out.","user-token-expired":"The user's credential is no longer valid. The user must sign in again.","too-many-requests":"We have blocked all requests from this device due to unusual activity. Try again later.","unauthorized-continue-uri":"The domain of the continue URL is not whitelisted.  Please whitelist the domain in the Firebase console.","unsupported-first-factor":"Enrolling a second factor or signing in with a multi-factor account requires sign-in with a supported first factor.","unsupported-persistence-type":"The current environment does not support the specified persistence type.","unsupported-tenant-operation":"This operation is not supported in a multi-tenant context.","unverified-email":"The operation requires a verified email.","user-cancelled":"The user did not grant your application the permissions it requested.","user-not-found":"There is no user record corresponding to this identifier. The user may have been deleted.","user-disabled":"The user account has been disabled by an administrator.","user-mismatch":"The supplied credentials do not correspond to the previously signed in user.","user-signed-out":"","weak-password":"The password must be 6 characters long or more.","web-storage-unsupported":"This browser is not supported or 3rd party cookies and data may be disabled.","already-initialized":"initializeAuth() has already been called with different options. To avoid this error, call initializeAuth() with the same options as when it was originally called, or call getAuth() to return the already initialized instance.","missing-recaptcha-token":"The reCAPTCHA token is missing when sending request to the backend.","invalid-recaptcha-token":"The reCAPTCHA token is invalid when sending request to the backend.","invalid-recaptcha-action":"The reCAPTCHA action is invalid when sending request to the backend.","recaptcha-not-enabled":"reCAPTCHA Enterprise integration is not enabled for this project.","missing-client-type":"The reCAPTCHA client type is missing when sending request to the backend.","missing-recaptcha-version":"The reCAPTCHA version is missing when sending request to the backend.","invalid-req-type":"Invalid request parameters.","invalid-recaptcha-version":"The reCAPTCHA version is invalid when sending request to the backend.","unsupported-password-policy-schema-version":"The password policy received from the backend uses a schema version that is not supported by this version of the Firebase SDK.","password-does-not-meet-requirements":"The password does not meet the requirements.","invalid-hosting-link-domain":"The provided Hosting link domain is not configured in Firebase Hosting or is not owned by the current project. This cannot be a default Hosting domain (`web.app` or `firebaseapp.com`)."}}let ve=me,fe=new X("auth","Firebase",me()),_e=new class{constructor(e){this.name=e,this._logLevel=oe,this._logHandler=ce,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in i))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?ae[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,i.DEBUG,...e),this._logHandler(this,i.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,i.VERBOSE,...e),this._logHandler(this,i.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,i.INFO,...e),this._logHandler(this,i.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,i.WARN,...e),this._logHandler(this,i.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,i.ERROR,...e),this._logHandler(this,i.ERROR,...e)}}("@firebase/auth");function ye(e,...t){_e.logLevel<=i.ERROR&&_e.error(`Auth (${qn.SDK_VERSION}): `+e,...t)}function d(e,...t){throw Te(e,...t)}function h(e,...t){return Te(e,...t)}function Ie(e,t,r){var i=Object.assign(Object.assign({},ve()),{[t]:r});return new X("auth","Firebase",i).create(t,{appName:e.name})}function l(e){return Ie(e,"operation-not-supported-in-this-environment","Operations that alter the current user are not supported in conjunction with FirebaseServerApp")}function we(e,t,r){var i=r;if(!(t instanceof i))throw i.name!==t.constructor.name&&d(e,"argument-error"),Ie(e,"argument-error",`Type of ${t.constructor.name} does not match expected instance.`+"Did you pass a reference from a different Auth SDK?")}function Te(e,...t){var r,i;return"string"!=typeof e?(r=t[0],(i=[...t.slice(1)])[0]&&(i[0].appName=e.name),e._errorFactory.create(r,...i)):fe.create(e,...t)}function g(e,t,...r){if(!e)throw Te(t,...r)}function n(e){var t="INTERNAL ASSERTION FAILED: "+e;throw ye(t),new Error(t)}function a(e,t){e||n(t)}function Ee(){var e;return"undefined"!=typeof self&&(null==(e=self.location)?void 0:e.href)||""}function be(){return"http:"===ke()||"https:"===ke()}function ke(){var e;return"undefined"!=typeof self&&(null==(e=self.location)?void 0:e.protocol)||null}class Se{constructor(e,t){a((this.shortDelay=e)<(this.longDelay=t),"Short delay should be less than long delay!"),this.isMobile="undefined"!=typeof window&&!!(window.cordova||window.phonegap||window.PhoneGap)&&/ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(c())||J()}get(){return"undefined"!=typeof navigator&&navigator&&"onLine"in navigator&&"boolean"==typeof navigator.onLine&&(be()||K()||"connection"in navigator)&&!navigator.onLine?Math.min(5e3,this.shortDelay):this.isMobile?this.longDelay:this.shortDelay}}function Re(e,t){a(e.emulator,"Emulator should always be set here");var r=e.emulator.url;return t?""+r+(t.startsWith("/")?t.slice(1):t):r}class Ae{static initialize(e,t,r){this.fetchImpl=e,t&&(this.headersImpl=t),r&&(this.responseImpl=r)}static fetch(){return this.fetchImpl||("undefined"!=typeof self&&"fetch"in self?self.fetch:"undefined"!=typeof globalThis&&globalThis.fetch?globalThis.fetch:"undefined"!=typeof fetch?fetch:void n("Could not find fetch implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill"))}static headers(){return this.headersImpl||("undefined"!=typeof self&&"Headers"in self?self.Headers:"undefined"!=typeof globalThis&&globalThis.Headers?globalThis.Headers:"undefined"!=typeof Headers?Headers:void n("Could not find Headers implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill"))}static response(){return this.responseImpl||("undefined"!=typeof self&&"Response"in self?self.Response:"undefined"!=typeof globalThis&&globalThis.Response?globalThis.Response:"undefined"!=typeof Response?Response:void n("Could not find Response implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill"))}}let Pe={CREDENTIAL_MISMATCH:"custom-token-mismatch",MISSING_CUSTOM_TOKEN:"internal-error",INVALID_IDENTIFIER:"invalid-email",MISSING_CONTINUE_URI:"internal-error",INVALID_PASSWORD:"wrong-password",MISSING_PASSWORD:"missing-password",INVALID_LOGIN_CREDENTIALS:"invalid-credential",EMAIL_EXISTS:"email-already-in-use",PASSWORD_LOGIN_DISABLED:"operation-not-allowed",INVALID_IDP_RESPONSE:"invalid-credential",INVALID_PENDING_TOKEN:"invalid-credential",FEDERATED_USER_ID_ALREADY_LINKED:"credential-already-in-use",MISSING_REQ_TYPE:"internal-error",EMAIL_NOT_FOUND:"user-not-found",RESET_PASSWORD_EXCEED_LIMIT:"too-many-requests",EXPIRED_OOB_CODE:"expired-action-code",INVALID_OOB_CODE:"invalid-action-code",MISSING_OOB_CODE:"internal-error",CREDENTIAL_TOO_OLD_LOGIN_AGAIN:"requires-recent-login",INVALID_ID_TOKEN:"invalid-user-token",TOKEN_EXPIRED:"user-token-expired",USER_NOT_FOUND:"user-token-expired",TOO_MANY_ATTEMPTS_TRY_LATER:"too-many-requests",PASSWORD_DOES_NOT_MEET_REQUIREMENTS:"password-does-not-meet-requirements",INVALID_CODE:"invalid-verification-code",INVALID_SESSION_INFO:"invalid-verification-id",INVALID_TEMPORARY_PROOF:"invalid-credential",MISSING_SESSION_INFO:"missing-verification-id",SESSION_EXPIRED:"code-expired",MISSING_ANDROID_PACKAGE_NAME:"missing-android-pkg-name",UNAUTHORIZED_DOMAIN:"unauthorized-continue-uri",INVALID_OAUTH_CLIENT_ID:"invalid-oauth-client-id",ADMIN_ONLY_OPERATION:"admin-restricted-operation",INVALID_MFA_PENDING_CREDENTIAL:"invalid-multi-factor-session",MFA_ENROLLMENT_NOT_FOUND:"multi-factor-info-not-found",MISSING_MFA_ENROLLMENT_ID:"missing-multi-factor-info",MISSING_MFA_PENDING_CREDENTIAL:"missing-multi-factor-session",SECOND_FACTOR_EXISTS:"second-factor-already-in-use",SECOND_FACTOR_LIMIT_EXCEEDED:"maximum-second-factor-count-exceeded",BLOCKING_FUNCTION_ERROR_RESPONSE:"internal-error",RECAPTCHA_NOT_ENABLED:"recaptcha-not-enabled",MISSING_RECAPTCHA_TOKEN:"missing-recaptcha-token",INVALID_RECAPTCHA_TOKEN:"invalid-recaptcha-token",INVALID_RECAPTCHA_ACTION:"invalid-recaptcha-action",MISSING_CLIENT_TYPE:"missing-client-type",MISSING_RECAPTCHA_VERSION:"missing-recaptcha-version",INVALID_RECAPTCHA_VERSION:"invalid-recaptcha-version",INVALID_REQ_TYPE:"invalid-req-type"},Ce=["/v1/accounts:signInWithCustomToken","/v1/accounts:signInWithEmailLink","/v1/accounts:signInWithIdp","/v1/accounts:signInWithPassword","/v1/accounts:signInWithPhoneNumber","/v1/token"],Oe=new Se(3e4,6e4);function p(e,t){return e.tenantId&&!t.tenantId?Object.assign(Object.assign({},t),{tenantId:e.tenantId}):t}async function m(n,s,a,o,e={}){return Ne(n,e,async()=>{let e={},t={};o&&("GET"===s?t=o:e={body:JSON.stringify(o)});var r=te(Object.assign({key:n.config.apiKey},t)).slice(1),i=await n._getAdditionalHeaders(),i=(i["Content-Type"]="application/json",n.languageCode&&(i["X-Firebase-Locale"]=n.languageCode),Object.assign({method:s,headers:i},e));return"undefined"!=typeof navigator&&"Cloudflare-Workers"===navigator.userAgent||(i.referrerPolicy="no-referrer"),Ae.fetch()(await Le(n,n.config.apiHost,a,r),i)})}async function Ne(t,e,r){t._canInitEmulator=!1;var i=Object.assign(Object.assign({},Pe),e);try{var n=new De(t),s=await Promise.race([r(),n.promise]),a=(n.clearNetworkTimeout(),await s.json());if("needConfirmation"in a)throw Me(t,"account-exists-with-different-credential",a);if(s.ok&&!("errorMessage"in a))return a;var[o,l]=(s.ok?a.errorMessage:a.error.message).split(" : ");if("FEDERATED_USER_ID_ALREADY_LINKED"===o)throw Me(t,"credential-already-in-use",a);if("EMAIL_EXISTS"===o)throw Me(t,"email-already-in-use",a);if("USER_DISABLED"===o)throw Me(t,"user-disabled",a);var c=i[o]||o.toLowerCase().replace(/[_\s]+/g,"-");if(l)throw Ie(t,c,l);d(t,c)}catch(e){if(e instanceof u)throw e;d(t,"network-request-failed",{message:String(e)})}}async function s(e,t,r,i,n={}){var s=await m(e,t,r,i,n);return"mfaPendingCredential"in s&&d(e,"multi-factor-auth-required",{_serverResponse:s}),s}async function Le(e,t,r,i){var n=""+t+r+"?"+i,s=e,n=s.config.emulator?Re(e.config,n):e.config.apiScheme+"://"+n;if(Ce.includes(r)&&(await s._persistenceManagerAvailable,"COOKIE"===s._getPersistenceType()))return s._getPersistence()._getFinalTarget(n).toString();return n}class De{clearNetworkTimeout(){clearTimeout(this.timer)}constructor(e){this.auth=e,this.timer=null,this.promise=new Promise((e,t)=>{this.timer=setTimeout(()=>t(h(this.auth,"network-request-failed")),Oe.get())})}}function Me(e,t,r){var i={appName:e.name},i=(r.email&&(i.email=r.email),r.phoneNumber&&(i.phoneNumber=r.phoneNumber),h(e,t,i));return i.customData._tokenResponse=r,i}function Ue(e){return void 0!==e&&void 0!==e.getResponse}function Fe(e){return void 0!==e&&void 0!==e.enterprise}class Ve{constructor(e){if(this.siteKey="",this.recaptchaEnforcementState=[],void 0===e.recaptchaKey)throw new Error("recaptchaKey undefined");this.siteKey=e.recaptchaKey.split("/")[3],this.recaptchaEnforcementState=e.recaptchaEnforcementState}getProviderEnforcementState(t){if(this.recaptchaEnforcementState&&0!==this.recaptchaEnforcementState.length)for(let e of this.recaptchaEnforcementState)if(e.provider&&e.provider===t){switch(e.enforcementState){case"ENFORCE":return"ENFORCE";case"AUDIT":return"AUDIT";case"OFF":return"OFF";default:return"ENFORCEMENT_STATE_UNSPECIFIED"}return}return null}isProviderEnabled(e){return"ENFORCE"===this.getProviderEnforcementState(e)||"AUDIT"===this.getProviderEnforcementState(e)}isAnyProviderEnabled(){return this.isProviderEnabled("EMAIL_PASSWORD_PROVIDER")||this.isProviderEnabled("PHONE_PROVIDER")}}async function xe(e,t){return m(e,"GET","/v2/recaptchaConfig",p(e,t))}async function je(e,t){return m(e,"POST","/v1/accounts:lookup",t)}function He(e){if(e)try{var t=new Date(Number(e));if(!isNaN(t.getTime()))return t.toUTCString()}catch(e){}}function We(e){return 1e3*Number(e)}function qe(e){var[t,r,i]=e.split(".");if(void 0===t||void 0===r||void 0===i)return ye("JWT malformed, contained fewer than 3 sections"),null;try{var n=H(r);return n?JSON.parse(n):(ye("Failed to decode base64 JWT payload"),null)}catch(e){return ye("Caught error parsing JWT payload as JSON",null==e?void 0:e.toString()),null}}function Be(e){var t=qe(e);return g(t,"internal-error"),g(void 0!==t.exp,"internal-error"),g(void 0!==t.iat,"internal-error"),Number(t.exp)-Number(t.iat)}async function v(t,e,r=!1){if(r)return e;try{return await e}catch(e){throw e instanceof u&&(r=[e.code][0],"auth/user-disabled"===r||"auth/user-token-expired"===r)&&(t.auth.currentUser===t&&await t.auth.signOut()),e}}class ze{constructor(e){this.user=e,this.isRunning=!1,this.timerId=null,this.errorBackoff=3e4}_start(){this.isRunning||(this.isRunning=!0,this.schedule())}_stop(){this.isRunning&&(this.isRunning=!1,null!==this.timerId)&&clearTimeout(this.timerId)}getInterval(e){var t;return e?(t=this.errorBackoff,this.errorBackoff=Math.min(2*this.errorBackoff,96e4),t):(this.errorBackoff=3e4,t=(null!=(t=this.user.stsTokenManager.expirationTime)?t:0)-Date.now()-3e5,Math.max(0,t))}schedule(e=!1){var t;this.isRunning&&(t=this.getInterval(e),this.timerId=setTimeout(async()=>{await this.iteration()},t))}async iteration(){try{await this.user.getIdToken(!0)}catch(e){return void("auth/network-request-failed"===(null==e?void 0:e.code)&&this.schedule(!0))}this.schedule()}}class Ge{constructor(e,t){this.createdAt=e,this.lastLoginAt=t,this._initializeTime()}_initializeTime(){this.lastSignInTime=He(this.lastLoginAt),this.creationTime=He(this.createdAt)}_copy(e){this.createdAt=e.createdAt,this.lastLoginAt=e.lastLoginAt,this._initializeTime()}toJSON(){return{createdAt:this.createdAt,lastLoginAt:this.lastLoginAt}}}async function Ke(e){var t,r,i=e.auth,n=await e.getIdToken(),n=await v(e,je(i,{idToken:n})),i=(g(null==n?void 0:n.users.length,i,"internal-error"),n.users[0]),n=(e._notifyReloadListener(i),null!=(n=i.providerUserInfo)&&n.length?Je(i.providerUserInfo):[]),n=(t=e.providerData,r=n,[...t.filter(t=>!r.some(e=>e.providerId===t.providerId)),...r]),s=!(e.email&&i.passwordHash||null!==n&&n.length),s=!!e.isAnonymous&&s,n={uid:i.localId,displayName:i.displayName||null,photoURL:i.photoUrl||null,email:i.email||null,emailVerified:i.emailVerified||!1,phoneNumber:i.phoneNumber||null,tenantId:i.tenantId||null,providerData:n,metadata:new Ge(i.createdAt,i.lastLoginAt),isAnonymous:s};Object.assign(e,n)}function Je(e){return e.map(e=>{var t=e.providerId,r=ue(e,["providerId"]);return{providerId:t,uid:r.rawId||"",displayName:r.displayName||null,email:r.email||null,phoneNumber:r.phoneNumber||null,photoURL:r.photoUrl||null}})}class Ye{constructor(){this.refreshToken=null,this.accessToken=null,this.expirationTime=null}get isExpired(){return!this.expirationTime||Date.now()>this.expirationTime-3e4}updateFromServerResponse(e){g(e.idToken,"internal-error"),g(void 0!==e.idToken,"internal-error"),g(void 0!==e.refreshToken,"internal-error");var t="expiresIn"in e&&void 0!==e.expiresIn?Number(e.expiresIn):Be(e.idToken);this.updateTokensAndExpiration(e.idToken,e.refreshToken,t)}updateFromIdToken(e){g(0!==e.length,"internal-error");var t=Be(e);this.updateTokensAndExpiration(e,null,t)}async getToken(e,t=!1){return t||!this.accessToken||this.isExpired?(g(this.refreshToken,e,"user-token-expired"),this.refreshToken?(await this.refresh(e,this.refreshToken),this.accessToken):null):this.accessToken}clearRefreshToken(){this.refreshToken=null}async refresh(e,t){n=t;var i,n,{accessToken:r,refreshToken:s,expiresIn:a}=await{accessToken:(r=await Ne(i=e,{},async()=>{var e=te({grant_type:"refresh_token",refresh_token:n}).slice(1),{tokenApiHost:t,apiKey:r}=i.config,t=await Le(i,t,"/v1/token","key="+r),r=await i._getAdditionalHeaders();return r["Content-Type"]="application/x-www-form-urlencoded",Ae.fetch()(t,{method:"POST",headers:r,body:e})})).access_token,expiresIn:r.expires_in,refreshToken:r.refresh_token};this.updateTokensAndExpiration(r,s,Number(a))}updateTokensAndExpiration(e,t,r){this.refreshToken=t||null,this.accessToken=e||null,this.expirationTime=Date.now()+1e3*r}static fromJSON(e,t){var{refreshToken:r,accessToken:i,expirationTime:n}=t,s=new Ye;return r&&(g("string"==typeof r,"internal-error",{appName:e}),s.refreshToken=r),i&&(g("string"==typeof i,"internal-error",{appName:e}),s.accessToken=i),n&&(g("number"==typeof n,"internal-error",{appName:e}),s.expirationTime=n),s}toJSON(){return{refreshToken:this.refreshToken,accessToken:this.accessToken,expirationTime:this.expirationTime}}_assign(e){this.accessToken=e.accessToken,this.refreshToken=e.refreshToken,this.expirationTime=e.expirationTime}_clone(){return Object.assign(new Ye,this.toJSON())}_performRefresh(){return n("not implemented")}}function f(e,t){g("string"==typeof e||void 0===e,"internal-error",{appName:t})}class _{constructor(e){var{uid:t,auth:r,stsTokenManager:i}=e,n=ue(e,["uid","auth","stsTokenManager"]);this.providerId="firebase",this.proactiveRefresh=new ze(this),this.reloadUserInfo=null,this.reloadListener=null,this.uid=t,this.auth=r,this.stsTokenManager=i,this.accessToken=i.accessToken,this.displayName=n.displayName||null,this.email=n.email||null,this.emailVerified=n.emailVerified||!1,this.phoneNumber=n.phoneNumber||null,this.photoURL=n.photoURL||null,this.isAnonymous=n.isAnonymous||!1,this.tenantId=n.tenantId||null,this.providerData=n.providerData?[...n.providerData]:[],this.metadata=new Ge(n.createdAt||void 0,n.lastLoginAt||void 0)}async getIdToken(e){var t=await v(this,this.stsTokenManager.getToken(this.auth,e));return g(t,this.auth,"internal-error"),this.accessToken!==t&&(this.accessToken=t,await this.auth._persistUserIfCurrent(this),this.auth._notifyListenersIfCurrent(this)),t}getIdTokenResult(e){return(async(e,t=!1)=>{var r=o(e),i=await r.getIdToken(t),n=qe(i),s=(g(n&&n.exp&&n.auth_time&&n.iat,r.auth,"internal-error"),null==(r="object"==typeof n.firebase?n.firebase:void 0)?void 0:r.sign_in_provider);return{claims:n,token:i,authTime:He(We(n.auth_time)),issuedAtTime:He(We(n.iat)),expirationTime:He(We(n.exp)),signInProvider:s||null,signInSecondFactor:(null==r?void 0:r.sign_in_second_factor)||null}})(this,e)}reload(){return(async e=>{var t=o(e);await Ke(t),await t.auth._persistUserIfCurrent(t),t.auth._notifyListenersIfCurrent(t)})(this)}_assign(e){this!==e&&(g(this.uid===e.uid,this.auth,"internal-error"),this.displayName=e.displayName,this.photoURL=e.photoURL,this.email=e.email,this.emailVerified=e.emailVerified,this.phoneNumber=e.phoneNumber,this.isAnonymous=e.isAnonymous,this.tenantId=e.tenantId,this.providerData=e.providerData.map(e=>Object.assign({},e)),this.metadata._copy(e.metadata),this.stsTokenManager._assign(e.stsTokenManager))}_clone(e){var t=new _(Object.assign(Object.assign({},this),{auth:e,stsTokenManager:this.stsTokenManager._clone()}));return t.metadata._copy(this.metadata),t}_onReload(e){g(!this.reloadListener,this.auth,"internal-error"),this.reloadListener=e,this.reloadUserInfo&&(this._notifyReloadListener(this.reloadUserInfo),this.reloadUserInfo=null)}_notifyReloadListener(e){this.reloadListener?this.reloadListener(e):this.reloadUserInfo=e}_startProactiveRefresh(){this.proactiveRefresh._start()}_stopProactiveRefresh(){this.proactiveRefresh._stop()}async _updateTokensIfNecessary(e,t=!1){let r=!1;e.idToken&&e.idToken!==this.stsTokenManager.accessToken&&(this.stsTokenManager.updateFromServerResponse(e),r=!0),t&&await Ke(this),await this.auth._persistUserIfCurrent(this),r&&this.auth._notifyListenersIfCurrent(this)}async delete(){var e;return qn._isFirebaseServerApp(this.auth.app)?Promise.reject(l(this.auth)):(e=await this.getIdToken(),await v(this,(async(e,t)=>m(e,"POST","/v1/accounts:delete",t))(this.auth,{idToken:e})),this.stsTokenManager.clearRefreshToken(),this.auth.signOut())}toJSON(){return Object.assign(Object.assign({uid:this.uid,email:this.email||void 0,emailVerified:this.emailVerified,displayName:this.displayName||void 0,isAnonymous:this.isAnonymous,photoURL:this.photoURL||void 0,phoneNumber:this.phoneNumber||void 0,tenantId:this.tenantId||void 0,providerData:this.providerData.map(e=>Object.assign({},e)),stsTokenManager:this.stsTokenManager.toJSON(),_redirectEventId:this._redirectEventId},this.metadata.toJSON()),{apiKey:this.auth.config.apiKey,appName:this.auth.name})}get refreshToken(){return this.stsTokenManager.refreshToken||""}static _fromJSON(e,t){var r=null!=(r=t.displayName)?r:void 0,i=null!=(i=t.email)?i:void 0,n=null!=(n=t.phoneNumber)?n:void 0,s=null!=(s=t.photoURL)?s:void 0,a=null!=(a=t.tenantId)?a:void 0,o=null!=(o=t._redirectEventId)?o:void 0,l=null!=(l=t.createdAt)?l:void 0,c=null!=(c=t.lastLoginAt)?c:void 0,{uid:u,emailVerified:d,isAnonymous:h,providerData:p,stsTokenManager:m}=t,m=(g(u&&m,e,"internal-error"),Ye.fromJSON(this.name,m)),u=(g("string"==typeof u,e,"internal-error"),f(r,e.name),f(i,e.name),g("boolean"==typeof d,e,"internal-error"),g("boolean"==typeof h,e,"internal-error"),f(n,e.name),f(s,e.name),f(a,e.name),f(o,e.name),f(l,e.name),f(c,e.name),new _({uid:u,auth:e,email:i,emailVerified:d,displayName:r,isAnonymous:h,photoURL:s,phoneNumber:n,tenantId:a,stsTokenManager:m,createdAt:l,lastLoginAt:c}));return p&&Array.isArray(p)&&(u.providerData=p.map(e=>Object.assign({},e))),o&&(u._redirectEventId=o),u}static async _fromIdTokenResponse(e,t,r=!1){var i=new Ye,i=(i.updateFromServerResponse(t),new _({uid:t.localId,auth:e,stsTokenManager:i,isAnonymous:r}));return await Ke(i),i}static async _fromGetAccountInfoResponse(e,t,r){var i=t.users[0],n=(g(void 0!==i.localId,"internal-error"),void 0!==i.providerUserInfo?Je(i.providerUserInfo):[]),s=!(i.email&&i.passwordHash||null!=n&&n.length),a=new Ye,a=(a.updateFromIdToken(r),new _({uid:i.localId,auth:e,stsTokenManager:a,isAnonymous:s})),s={uid:i.localId,displayName:i.displayName||null,photoURL:i.photoUrl||null,email:i.email||null,emailVerified:i.emailVerified||!1,phoneNumber:i.phoneNumber||null,tenantId:i.tenantId||null,providerData:n,metadata:new Ge(i.createdAt,i.lastLoginAt),isAnonymous:!(i.email&&i.passwordHash||null!=n&&n.length)};return Object.assign(a,s),a}}let $e=new Map;function y(e){a(e instanceof Function,"Expected a class definition");var t=$e.get(e);return t?a(t instanceof e,"Instance stored in cache mismatched with class"):(t=new e,$e.set(e,t)),t}class Xe{constructor(){this.type="NONE",this.storage={}}async _isAvailable(){return!0}async _set(e,t){this.storage[e]=t}async _get(e){var t=this.storage[e];return void 0===t?null:t}async _remove(e){delete this.storage[e]}_addListener(e,t){}_removeListener(e,t){}}Xe.type="NONE";let Qe=Xe;function I(e,t,r){return`firebase:${e}:${t}:`+r}class Ze{constructor(e,t,r){this.persistence=e,this.auth=t,this.userKey=r;var{config:i,name:n}=this.auth;this.fullUserKey=I(this.userKey,i.apiKey,n),this.fullPersistenceKey=I("persistence",i.apiKey,n),this.boundEventHandler=t._onStorageEvent.bind(t),this.persistence._addListener(this.fullUserKey,this.boundEventHandler)}setCurrentUser(e){return this.persistence._set(this.fullUserKey,e.toJSON())}async getCurrentUser(){var e,t=await this.persistence._get(this.fullUserKey);return t?"string"==typeof t?(e=await je(this.auth,{idToken:t}).catch(()=>{}))?_._fromGetAccountInfoResponse(this.auth,e,t):null:_._fromJSON(this.auth,t):null}removeCurrentUser(){return this.persistence._remove(this.fullUserKey)}savePersistenceForRedirect(){return this.persistence._set(this.fullPersistenceKey,this.persistence.type)}async setPersistence(e){var t;if(this.persistence!==e)return t=await this.getCurrentUser(),await this.removeCurrentUser(),this.persistence=e,t?this.setCurrentUser(t):void 0}delete(){this.persistence._removeListener(this.fullUserKey,this.boundEventHandler)}static async create(t,e,r="authUser"){if(!e.length)return new Ze(y(Qe),t,r);var i,n=(await Promise.all(e.map(async e=>{if(await e._isAvailable())return e}))).filter(e=>e);let s=n[0]||y(Qe),a=I(r,t.config.apiKey,t.name),o=null;for(i of e)try{var l=await i._get(a);if(l){let e;if("string"==typeof l){var c=await je(t,{idToken:l}).catch(()=>{});if(!c)break;e=await _._fromGetAccountInfoResponse(t,c,l)}else e=_._fromJSON(t,l);i!==s&&(o=e),s=i;break}}catch(e){}n=n.filter(e=>e._shouldAllowMigration);return s._shouldAllowMigration&&n.length&&(s=n[0],o&&await s._set(a,o.toJSON()),await Promise.all(e.map(async e=>{if(e!==s)try{await e._remove(a)}catch(e){}}))),new Ze(s,t,r)}}function et(e){var t=e.toLowerCase();return t.includes("opera/")||t.includes("opr/")||t.includes("opios/")?"Opera":nt(t)?"IEMobile":t.includes("msie")||t.includes("trident/")?"IE":t.includes("edge/")?"Edge":tt(t)?"Firefox":t.includes("silk/")?"Silk":at(t)?"Blackberry":ot(t)?"Webos":rt(t)?"Safari":!t.includes("chrome/")&&!it(t)||t.includes("edge/")?st(t)?"Android":2===(null==(t=e.match(/([a-zA-Z\d\.]+)\/[a-zA-Z\d\.]*$/))?void 0:t.length)?t[1]:"Other":"Chrome"}function tt(e=c()){return/firefox\//i.test(e)}function rt(e=c()){var t=e.toLowerCase();return t.includes("safari/")&&!t.includes("chrome/")&&!t.includes("crios/")&&!t.includes("android")}function it(e=c()){return/crios\//i.test(e)}function nt(e=c()){return/iemobile/i.test(e)}function st(e=c()){return/android/i.test(e)}function at(e=c()){return/blackberry/i.test(e)}function ot(e=c()){return/webos/i.test(e)}function lt(e=c()){return/iphone|ipad|ipod/i.test(e)||/macintosh/i.test(e)&&/mobile/i.test(e)}function ct(e=c()){return lt(e)||st(e)||ot(e)||at(e)||/windows phone/i.test(e)||nt(e)}function ut(e,t=[]){let r;switch(e){case"Browser":r=et(c());break;case"Worker":r=et(c())+"-"+e;break;default:r=e}var i=t.length?t.join(","):"FirebaseCore-web";return`${r}/JsCore/${qn.SDK_VERSION}/`+i}class dt{constructor(e){this.auth=e,this.queue=[]}pushCallback(i,e){var t=r=>new Promise((e,t)=>{try{e(i(r))}catch(e){t(e)}});t.onAbort=e,this.queue.push(t);let r=this.queue.length-1;return()=>{this.queue[r]=()=>Promise.resolve()}}async runMiddleware(e){if(this.auth.currentUser!==e){var t=[];try{for(var r of this.queue)await r(e),r.onAbort&&t.push(r.onAbort)}catch(e){t.reverse();for(var i of t)try{i()}catch(e){}throw this.auth._errorFactory.create("login-blocked",{originalMessage:null==e?void 0:e.message})}}}}class ht{constructor(e){var t,r=e.customStrengthOptions;this.customStrengthOptions={},this.customStrengthOptions.minPasswordLength=null!=(t=r.minPasswordLength)?t:6,r.maxPasswordLength&&(this.customStrengthOptions.maxPasswordLength=r.maxPasswordLength),void 0!==r.containsLowercaseCharacter&&(this.customStrengthOptions.containsLowercaseLetter=r.containsLowercaseCharacter),void 0!==r.containsUppercaseCharacter&&(this.customStrengthOptions.containsUppercaseLetter=r.containsUppercaseCharacter),void 0!==r.containsNumericCharacter&&(this.customStrengthOptions.containsNumericCharacter=r.containsNumericCharacter),void 0!==r.containsNonAlphanumericCharacter&&(this.customStrengthOptions.containsNonAlphanumericCharacter=r.containsNonAlphanumericCharacter),this.enforcementState=e.enforcementState,"ENFORCEMENT_STATE_UNSPECIFIED"===this.enforcementState&&(this.enforcementState="OFF"),this.allowedNonAlphanumericCharacters=null!=(r=null==(t=e.allowedNonAlphanumericCharacters)?void 0:t.join(""))?r:"",this.forceUpgradeOnSignin=null!=(t=e.forceUpgradeOnSignin)&&t,this.schemaVersion=e.schemaVersion}validatePassword(e){var t,r={isValid:!0,passwordPolicy:this};return this.validatePasswordLengthOptions(e,r),this.validatePasswordCharacterOptions(e,r),r.isValid&&(r.isValid=null==(t=r.meetsMinPasswordLength)||t),r.isValid&&(r.isValid=null==(t=r.meetsMaxPasswordLength)||t),r.isValid&&(r.isValid=null==(t=r.containsLowercaseLetter)||t),r.isValid&&(r.isValid=null==(t=r.containsUppercaseLetter)||t),r.isValid&&(r.isValid=null==(t=r.containsNumericCharacter)||t),r.isValid&&(r.isValid=null==(t=r.containsNonAlphanumericCharacter)||t),r}validatePasswordLengthOptions(e,t){var r=this.customStrengthOptions.minPasswordLength,i=this.customStrengthOptions.maxPasswordLength;r&&(t.meetsMinPasswordLength=e.length>=r),i&&(t.meetsMaxPasswordLength=e.length<=i)}validatePasswordCharacterOptions(e,t){var r;this.updatePasswordCharacterOptionsStatuses(t,!1,!1,!1,!1);for(let i=0;i<e.length;i++)r=e.charAt(i),this.updatePasswordCharacterOptionsStatuses(t,"a"<=r&&r<="z","A"<=r&&r<="Z","0"<=r&&r<="9",this.allowedNonAlphanumericCharacters.includes(r))}updatePasswordCharacterOptionsStatuses(e,t,r,i,n){this.customStrengthOptions.containsLowercaseLetter&&!e.containsLowercaseLetter&&(e.containsLowercaseLetter=t),this.customStrengthOptions.containsUppercaseLetter&&!e.containsUppercaseLetter&&(e.containsUppercaseLetter=r),this.customStrengthOptions.containsNumericCharacter&&!e.containsNumericCharacter&&(e.containsNumericCharacter=i),this.customStrengthOptions.containsNonAlphanumericCharacter&&!e.containsNonAlphanumericCharacter&&(e.containsNonAlphanumericCharacter=n)}}class pt{constructor(e,t,r,i){this.app=e,this.heartbeatServiceProvider=t,this.appCheckServiceProvider=r,this.config=i,this.currentUser=null,this.emulatorConfig=null,this.operations=Promise.resolve(),this.authStateSubscription=new mt(this),this.idTokenSubscription=new mt(this),this.beforeStateQueue=new dt(this),this.redirectUser=null,this.isProactiveRefreshEnabled=!1,this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION=1,this._canInitEmulator=!0,this._isInitialized=!1,this._deleted=!1,this._initializationPromise=null,this._popupRedirectResolver=null,this._errorFactory=fe,this._agentRecaptchaConfig=null,this._tenantRecaptchaConfigs={},this._projectPasswordPolicy=null,this._tenantPasswordPolicies={},this._resolvePersistenceManagerAvailable=void 0,this.lastNotifiedUid=void 0,this.languageCode=null,this.tenantId=null,this.settings={appVerificationDisabledForTesting:!1},this.frameworks=[],this.name=e.name,this.clientVersion=i.sdkClientVersion,this._persistenceManagerAvailable=new Promise(e=>this._resolvePersistenceManagerAvailable=e)}_initializeWithPersistence(t,r){return r&&(this._popupRedirectResolver=y(r)),this._initializationPromise=this.queue(async()=>{var e;if(!this._deleted&&(this.persistenceManager=await Ze.create(this,t),null!=(e=this._resolvePersistenceManagerAvailable)&&e.call(this),!this._deleted)){if(null!=(e=this._popupRedirectResolver)&&e._shouldInitProactively)try{await this._popupRedirectResolver._initialize(this)}catch(e){}await this.initializeCurrentUser(r),this.lastNotifiedUid=(null==(e=this.currentUser)?void 0:e.uid)||null,this._deleted||(this._isInitialized=!0)}}),this._initializationPromise}async _onStorageEvent(){var e;!this._deleted&&(e=await this.assertedPersistence.getCurrentUser(),this.currentUser||e)&&(this.currentUser&&e&&this.currentUser.uid===e.uid?(this._currentUser._assign(e),await this.currentUser.getIdToken()):await this._updateCurrentUser(e,!0))}async initializeCurrentUserFromIdToken(e){try{var t=await je(this,{idToken:e}),r=await _._fromGetAccountInfoResponse(this,t,e);await this.directlySetCurrentUser(r)}catch(e){console.warn("FirebaseServerApp could not login user with provided authIdToken: ",e),await this.directlySetCurrentUser(null)}}async initializeCurrentUser(e){if(qn._isFirebaseServerApp(this.app)){let t=this.app.settings.authIdToken;return t?new Promise(e=>{setTimeout(()=>this.initializeCurrentUserFromIdToken(t).then(e,e))}):this.directlySetCurrentUser(null)}var t,r,i,n=await this.assertedPersistence.getCurrentUser();let s=n,a=!1;if(e&&this.config.authDomain&&(await this.getOrInitRedirectPersistenceManager(),t=null==(t=this.redirectUser)?void 0:t._redirectEventId,r=null==s?void 0:s._redirectEventId,i=await this.tryRedirectSignIn(e),t&&t!==r||null==i||!i.user||(s=i.user,a=!0)),!s)return this.directlySetCurrentUser(null);if(s._redirectEventId)return g(this._popupRedirectResolver,this,"argument-error"),await this.getOrInitRedirectPersistenceManager(),this.redirectUser&&this.redirectUser._redirectEventId===s._redirectEventId?this.directlySetCurrentUser(s):this.reloadAndSetCurrentUserOrClear(s);if(a)try{await this.beforeStateQueue.runMiddleware(s)}catch(e){s=n,this._popupRedirectResolver._overrideRedirectResult(this,()=>Promise.reject(e))}return s?this.reloadAndSetCurrentUserOrClear(s):this.directlySetCurrentUser(null)}async tryRedirectSignIn(e){let t=null;try{t=await this._popupRedirectResolver._completeRedirectFn(this,e,!0)}catch(e){await this._setRedirectUser(null)}return t}async reloadAndSetCurrentUserOrClear(e){try{await Ke(e)}catch(e){if("auth/network-request-failed"!==(null==e?void 0:e.code))return this.directlySetCurrentUser(null)}return this.directlySetCurrentUser(e)}useDeviceLanguage(){var e;this.languageCode="undefined"!=typeof navigator&&((e=navigator).languages&&e.languages[0]||e.language)||null}async _delete(){this._deleted=!0}async updateCurrentUser(e){var t;return qn._isFirebaseServerApp(this.app)?Promise.reject(l(this)):((t=e?o(e):null)&&g(t.auth.config.apiKey===this.config.apiKey,this,"invalid-user-token"),this._updateCurrentUser(t&&t._clone(this)))}async _updateCurrentUser(e,t=!1){if(!this._deleted)return e&&g(this.tenantId===e.tenantId,this,"tenant-id-mismatch"),t||await this.beforeStateQueue.runMiddleware(e),this.queue(async()=>{await this.directlySetCurrentUser(e),this.notifyAuthListeners()})}async signOut(){return qn._isFirebaseServerApp(this.app)?Promise.reject(l(this)):(await this.beforeStateQueue.runMiddleware(null),(this.redirectPersistenceManager||this._popupRedirectResolver)&&await this._setRedirectUser(null),this._updateCurrentUser(null,!0))}setPersistence(e){return qn._isFirebaseServerApp(this.app)?Promise.reject(l(this)):this.queue(async()=>{await this.assertedPersistence.setPersistence(y(e))})}_getRecaptchaConfig(){return null==this.tenantId?this._agentRecaptchaConfig:this._tenantRecaptchaConfigs[this.tenantId]}async validatePassword(e){this._getPasswordPolicyInternal()||await this._updatePasswordPolicy();var t=this._getPasswordPolicyInternal();return t.schemaVersion!==this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION?Promise.reject(this._errorFactory.create("unsupported-password-policy-schema-version",{})):t.validatePassword(e)}_getPasswordPolicyInternal(){return null===this.tenantId?this._projectPasswordPolicy:this._tenantPasswordPolicies[this.tenantId]}async _updatePasswordPolicy(){var e,t=await m(e=this,"GET","/v2/passwordPolicy",p(e,{})),t=new ht(t);null===this.tenantId?this._projectPasswordPolicy=t:this._tenantPasswordPolicies[this.tenantId]=t}_getPersistenceType(){return this.assertedPersistence.persistence.type}_getPersistence(){return this.assertedPersistence.persistence}_updateErrorMap(e){this._errorFactory=new X("auth","Firebase",e())}onAuthStateChanged(e,t,r){return this.registerStateListener(this.authStateSubscription,e,t,r)}beforeAuthStateChanged(e,t){return this.beforeStateQueue.pushCallback(e,t)}onIdTokenChanged(e,t,r){return this.registerStateListener(this.idTokenSubscription,e,t,r)}authStateReady(){return new Promise((t,r)=>{if(this.currentUser)t();else{let e=this.onAuthStateChanged(()=>{e(),t()},r)}})}async revokeAccessToken(e){var t;this.currentUser&&(t={providerId:"apple.com",tokenType:"ACCESS_TOKEN",token:e,idToken:await this.currentUser.getIdToken()},null!=this.tenantId&&(t.tenantId=this.tenantId),await m(e=this,"POST","/v2/accounts:revokeToken",p(e,t)))}toJSON(){var e;return{apiKey:this.config.apiKey,authDomain:this.config.authDomain,appName:this.name,currentUser:null==(e=this._currentUser)?void 0:e.toJSON()}}async _setRedirectUser(e,t){var r=await this.getOrInitRedirectPersistenceManager(t);return null===e?r.removeCurrentUser():r.setCurrentUser(e)}async getOrInitRedirectPersistenceManager(e){var t;return this.redirectPersistenceManager||(g(t=e&&y(e)||this._popupRedirectResolver,this,"argument-error"),this.redirectPersistenceManager=await Ze.create(this,[y(t._redirectPersistence)],"redirectUser"),this.redirectUser=await this.redirectPersistenceManager.getCurrentUser()),this.redirectPersistenceManager}async _redirectUserForId(e){var t;return this._isInitialized&&await this.queue(async()=>{}),(null==(t=this._currentUser)?void 0:t._redirectEventId)===e?this._currentUser:(null==(t=this.redirectUser)?void 0:t._redirectEventId)===e?this.redirectUser:null}async _persistUserIfCurrent(e){if(e===this.currentUser)return this.queue(async()=>this.directlySetCurrentUser(e))}_notifyListenersIfCurrent(e){e===this.currentUser&&this.notifyAuthListeners()}_key(){return`${this.config.authDomain}:${this.config.apiKey}:`+this.name}_startProactiveRefresh(){this.isProactiveRefreshEnabled=!0,this.currentUser&&this._currentUser._startProactiveRefresh()}_stopProactiveRefresh(){this.isProactiveRefreshEnabled=!1,this.currentUser&&this._currentUser._stopProactiveRefresh()}get _currentUser(){return this.currentUser}notifyAuthListeners(){var e;this._isInitialized&&(this.idTokenSubscription.next(this.currentUser),e=null!=(e=null==(e=this.currentUser)?void 0:e.uid)?e:null,this.lastNotifiedUid!==e)&&(this.lastNotifiedUid=e,this.authStateSubscription.next(this.currentUser))}registerStateListener(t,r,i,n){if(this._deleted)return()=>{};let e="function"==typeof r?r:r.next.bind(r),s=!1;var a=this._isInitialized?Promise.resolve():this._initializationPromise;if(g(a,this,"internal-error"),a.then(()=>{s||e(this.currentUser)}),"function"==typeof r){let e=t.addObserver(r,i,n);return()=>{s=!0,e()}}{let e=t.addObserver(r);return()=>{s=!0,e()}}}async directlySetCurrentUser(e){this.currentUser&&this.currentUser!==e&&this._currentUser._stopProactiveRefresh(),e&&this.isProactiveRefreshEnabled&&e._startProactiveRefresh(),(this.currentUser=e)?await this.assertedPersistence.setCurrentUser(e):await this.assertedPersistence.removeCurrentUser()}queue(e){return this.operations=this.operations.then(e,e),this.operations}get assertedPersistence(){return g(this.persistenceManager,this,"internal-error"),this.persistenceManager}_logFramework(e){e&&!this.frameworks.includes(e)&&(this.frameworks.push(e),this.frameworks.sort(),this.clientVersion=ut(this.config.clientPlatform,this._getFrameworks()))}_getFrameworks(){return this.frameworks}async _getAdditionalHeaders(){var e={"X-Client-Version":this.clientVersion},t=(this.app.options.appId&&(e["X-Firebase-gmpid"]=this.app.options.appId),await(null==(t=this.heartbeatServiceProvider.getImmediate({optional:!0}))?void 0:t.getHeartbeatsHeader())),t=(t&&(e["X-Firebase-Client"]=t),await this._getAppCheckToken());return t&&(e["X-Firebase-AppCheck"]=t),e}async _getAppCheckToken(){var e,t,r;return qn._isFirebaseServerApp(this.app)&&this.app.settings.appCheckToken?this.app.settings.appCheckToken:(null!=(e=await(null==(e=this.appCheckServiceProvider.getImmediate({optional:!0}))?void 0:e.getToken()))&&e.error&&(t="Error while retrieving App Check token: "+e.error,r=[],_e.logLevel<=i.WARN)&&_e.warn(`Auth (${qn.SDK_VERSION}): `+t,...r),null==e?void 0:e.token)}}function w(e){return o(e)}class mt{constructor(e){var t,r;this.auth=e,this.observer=null,this.addObserver=(e=e=>this.observer=e,(r=new ne(e,t)).subscribe.bind(r))}get next(){return g(this.observer,this.auth,"internal-error"),this.observer.next.bind(this.observer)}}let gt={async loadJS(){throw new Error("Unable to load external scripts")},recaptchaV2Script:"",recaptchaEnterpriseScript:"",gapiScript:""};function vt(e){return gt.loadJS(e)}function ft(e){return"__"+e+Math.floor(1e6*Math.random())}class _t{constructor(e){this.auth=e,this.counter=1e12,this._widgets=new Map}render(e,t){var r=this.counter;return this._widgets.set(r,new wt(e,this.auth.name,t||{})),this.counter++,r}reset(e){var t,r=e||1e12;null!=(t=this._widgets.get(r))&&t.delete(),this._widgets.delete(r)}getResponse(e){var t;return(null==(t=this._widgets.get(e||1e12))?void 0:t.getResponse())||""}async execute(e){var t;return null!=(t=this._widgets.get(e||1e12))&&t.execute(),""}}class yt{constructor(){this.enterprise=new It}ready(e){e()}execute(e,t){return Promise.resolve("token")}render(e,t){return""}}class It{ready(e){e()}execute(e,t){return Promise.resolve("token")}render(e,t){return""}}class wt{constructor(e,t,r){this.params=r,this.timerId=null,this.deleted=!1,this.responseToken=null,this.clickHandler=()=>{this.execute()};var i="string"==typeof e?document.getElementById(e):e;g(i,"argument-error",{appName:t}),this.container=i,this.isVisible="invisible"!==this.params.size,this.isVisible?this.execute():this.container.addEventListener("click",this.clickHandler)}getResponse(){return this.checkIfDeleted(),this.responseToken}delete(){this.checkIfDeleted(),this.deleted=!0,this.timerId&&(clearTimeout(this.timerId),this.timerId=null),this.container.removeEventListener("click",this.clickHandler)}execute(){this.checkIfDeleted(),this.timerId||(this.timerId=window.setTimeout(()=>{this.responseToken=(e=>{var t=[],r="1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";for(let i=0;i<e;i++)t.push(r.charAt(Math.floor(Math.random()*r.length)));return t.join("")})(50);let{callback:e,"expired-callback":t}=this.params;if(e)try{e(this.responseToken)}catch(e){}this.timerId=window.setTimeout(()=>{if(this.timerId=null,this.responseToken=null,t)try{t()}catch(e){}this.isVisible&&this.execute()},6e4)},500))}checkIfDeleted(){if(this.deleted)throw new Error("reCAPTCHA mock was already deleted!")}}let Tt="NO_RECAPTCHA";class Et{constructor(e){this.type="recaptcha-enterprise",this.auth=w(e)}async verify(n="verify",e=!1){function s(e,t,r){let i=window.grecaptcha;Fe(i)?i.enterprise.ready(()=>{i.enterprise.execute(e,{action:n}).then(e=>{t(e)}).catch(()=>{t(Tt)})}):r(Error("No reCAPTCHA enterprise script loaded."))}return this.auth.settings.appVerificationDisabledForTesting?(new yt).execute("siteKey",{action:"verify"}):new Promise((r,i)=>{(async n=>{if(!e){if(null==n.tenantId&&null!=n._agentRecaptchaConfig)return n._agentRecaptchaConfig.siteKey;if(null!=n.tenantId&&void 0!==n._tenantRecaptchaConfigs[n.tenantId])return n._tenantRecaptchaConfigs[n.tenantId].siteKey}return new Promise(async(r,i)=>{xe(n,{clientType:"CLIENT_TYPE_WEB",version:"RECAPTCHA_ENTERPRISE"}).then(e=>{var t;if(void 0!==e.recaptchaKey)return t=new Ve(e),null==n.tenantId?n._agentRecaptchaConfig=t:n._tenantRecaptchaConfigs[n.tenantId]=t,r(t.siteKey);i(new Error("recaptcha Enterprise site key undefined"))}).catch(e=>{i(e)})})})(this.auth).then(t=>{if(!e&&Fe(window.grecaptcha))s(t,r,i);else if("undefined"==typeof window)i(new Error("RecaptchaVerifier is only supported in browser"));else{let e=gt.recaptchaEnterpriseScript;0!==e.length&&(e+=t),vt(e).then(()=>{s(t,r,i)}).catch(e=>{i(e)})}}).catch(e=>{i(e)})})}}async function bt(e,t,r,i=!1,n=!1){var s=new Et(e);let a;if(n)a=Tt;else try{a=await s.verify(r)}catch(e){a=await s.verify(r,!0)}var o,l,s=Object.assign({},t);return"mfaSmsEnrollment"===r||"mfaSmsSignIn"===r?"phoneEnrollmentInfo"in s?(l=s.phoneEnrollmentInfo.phoneNumber,o=s.phoneEnrollmentInfo.recaptchaToken,Object.assign(s,{phoneEnrollmentInfo:{phoneNumber:l,recaptchaToken:o,captchaResponse:a,clientType:"CLIENT_TYPE_WEB",recaptchaVersion:"RECAPTCHA_ENTERPRISE"}})):"phoneSignInInfo"in s&&(l=s.phoneSignInInfo.recaptchaToken,Object.assign(s,{phoneSignInInfo:{recaptchaToken:l,captchaResponse:a,clientType:"CLIENT_TYPE_WEB",recaptchaVersion:"RECAPTCHA_ENTERPRISE"}})):(i?Object.assign(s,{captchaResp:a}):Object.assign(s,{captchaResponse:a}),Object.assign(s,{clientType:"CLIENT_TYPE_WEB"}),Object.assign(s,{recaptchaVersion:"RECAPTCHA_ENTERPRISE"})),s}async function T(r,i,n,s,e){var t;return"EMAIL_PASSWORD_PROVIDER"===e?null!=(t=r._getRecaptchaConfig())&&t.isProviderEnabled("EMAIL_PASSWORD_PROVIDER")?(t=await bt(r,i,n,"getOobCode"===n),s(r,t)):s(r,i).catch(async e=>{var t;return"auth/missing-recaptcha-token"===e.code?(console.log(n+" is protected by reCAPTCHA Enterprise for this project. Automatically triggering the reCAPTCHA flow and restarting the flow."),t=await bt(r,i,n,"getOobCode"===n),s(r,t)):Promise.reject(e)}):"PHONE_PROVIDER"===e?null!=(t=r._getRecaptchaConfig())&&t.isProviderEnabled("PHONE_PROVIDER")?(t=await bt(r,i,n),s(r,t).catch(async e=>{var t;if("AUDIT"===(null==(t=r._getRecaptchaConfig())?void 0:t.getProviderEnforcementState("PHONE_PROVIDER"))&&("auth/missing-recaptcha-token"===e.code||"auth/invalid-app-credential"===e.code))return console.log(`Failed to verify with reCAPTCHA Enterprise. Automatically triggering the reCAPTCHA v2 flow to complete the ${n} flow.`),t=await bt(r,i,n,!1,!0),s(r,t);return Promise.reject(e)})):(t=await bt(r,i,n,!1,!0),s(r,t)):Promise.reject(e+" provider is not supported.")}function kt(e,t,r){var i=w(e),n=(g(/^https?:\/\//.test(t),i,"invalid-emulator-scheme"),!(null==r||!r.disableWarnings)),s=St(t),{host:a,port:o}=(e=>{var t,r=St(e);return(r=/(\/\/)?([^?#/]+)/.exec(e.substr(r.length)))?(r=r[2].split("@").pop()||"",(t=/^(\[[^\]]+\])(:|$)/.exec(r))?{host:t=t[1],port:Rt(r.substr(t.length+1))}:([t,r]=r.split(":"),{host:t,port:Rt(r)})):{host:"",port:null}})(t),l={url:s+`//${a}${null===o?"":":"+o}/`},a=Object.freeze({host:a,port:o,protocol:s.replace(":",""),options:Object.freeze({disableWarnings:n})});function c(){var e=document.createElement("p"),t=e.style;e.innerText="Running in emulator mode. Do not use with production credentials.",t.position="fixed",t.width="100%",t.backgroundColor="#ffffff",t.border=".1em solid #000000",t.color="#b50000",t.bottom="0px",t.left="0px",t.margin="0px",t.zIndex="10000",t.textAlign="center",e.classList.add("firebase-emulator-warning"),document.body.appendChild(e)}i._canInitEmulator?(i.config.emulator=l,i.emulatorConfig=a,i.settings.appVerificationDisabledForTesting=!0,n||("undefined"!=typeof console&&"function"==typeof console.info&&console.info("WARNING: You are using the Auth Emulator, which is intended for local testing only.  Do not use with production credentials."),"undefined"!=typeof window&&"undefined"!=typeof document&&("loading"===document.readyState?window.addEventListener("DOMContentLoaded",c):c()))):(g(i.config.emulator&&i.emulatorConfig,i,"emulator-config-failed"),g(Z(l,i.config.emulator)&&Z(a,i.emulatorConfig),i,"emulator-config-failed"))}function St(e){var t=e.indexOf(":");return t<0?"":e.substr(0,t+1)}function Rt(e){var t;return!e||(t=Number(e),isNaN(t))?null:t}class At{constructor(e,t){this.providerId=e,this.signInMethod=t}toJSON(){return n("not implemented")}_getIdTokenResponse(e){return n("not implemented")}_linkToIdToken(e,t){return n("not implemented")}_getReauthenticationResolver(e){return n("not implemented")}}async function Pt(e,t){return m(e,"POST","/v1/accounts:resetPassword",p(e,t))}async function Ct(e,t){return m(e,"POST","/v1/accounts:signUp",t)}async function Ot(e,t){return s(e,"POST","/v1/accounts:signInWithPassword",p(e,t))}async function Nt(e,t){return m(e,"POST","/v1/accounts:sendOobCode",p(e,t))}async function Lt(e,t){return Nt(e,t)}async function Dt(e,t){return Nt(e,t)}class Mt extends At{constructor(e,t,r,i=null){super("password",r),this._email=e,this._password=t,this._tenantId=i}static _fromEmailAndPassword(e,t){return new Mt(e,t,"password")}static _fromEmailAndCode(e,t,r=null){return new Mt(e,t,"emailLink",r)}toJSON(){return{email:this._email,password:this._password,signInMethod:this.signInMethod,tenantId:this._tenantId}}static fromJSON(e){var t="string"==typeof e?JSON.parse(e):e;if(null!=t&&t.email&&null!=t&&t.password){if("password"===t.signInMethod)return this._fromEmailAndPassword(t.email,t.password);if("emailLink"===t.signInMethod)return this._fromEmailAndCode(t.email,t.password,t.tenantId)}return null}async _getIdTokenResponse(e){switch(this.signInMethod){case"password":return T(e,{returnSecureToken:!0,email:this._email,password:this._password,clientType:"CLIENT_TYPE_WEB"},"signInWithPassword",Ot,"EMAIL_PASSWORD_PROVIDER");case"emailLink":return(async(e,t)=>s(e,"POST","/v1/accounts:signInWithEmailLink",p(e,t)))(e,{email:this._email,oobCode:this._password});default:d(e,"internal-error")}}async _linkToIdToken(e,t){switch(this.signInMethod){case"password":return T(e,{idToken:t,returnSecureToken:!0,email:this._email,password:this._password,clientType:"CLIENT_TYPE_WEB"},"signUpPassword",Ct,"EMAIL_PASSWORD_PROVIDER");case"emailLink":return(async(e,t)=>s(e,"POST","/v1/accounts:signInWithEmailLink",p(e,t)))(e,{idToken:t,email:this._email,oobCode:this._password});default:d(e,"internal-error")}}_getReauthenticationResolver(e){return this._getIdTokenResponse(e)}}async function E(e,t){return s(e,"POST","/v1/accounts:signInWithIdp",p(e,t))}class b extends At{constructor(){super(...arguments),this.pendingToken=null}static _fromParams(e){var t=new b(e.providerId,e.signInMethod);return e.idToken||e.accessToken?(e.idToken&&(t.idToken=e.idToken),e.accessToken&&(t.accessToken=e.accessToken),e.nonce&&!e.pendingToken&&(t.nonce=e.nonce),e.pendingToken&&(t.pendingToken=e.pendingToken)):e.oauthToken&&e.oauthTokenSecret?(t.accessToken=e.oauthToken,t.secret=e.oauthTokenSecret):d("argument-error"),t}toJSON(){return{idToken:this.idToken,accessToken:this.accessToken,secret:this.secret,nonce:this.nonce,pendingToken:this.pendingToken,providerId:this.providerId,signInMethod:this.signInMethod}}static fromJSON(e){var t="string"==typeof e?JSON.parse(e):e,{providerId:r,signInMethod:i}=t,t=ue(t,["providerId","signInMethod"]);return r&&i?((r=new b(r,i)).idToken=t.idToken||void 0,r.accessToken=t.accessToken||void 0,r.secret=t.secret,r.nonce=t.nonce,r.pendingToken=t.pendingToken||null,r):null}_getIdTokenResponse(e){return E(e,this.buildRequest())}_linkToIdToken(e,t){var r=this.buildRequest();return r.idToken=t,E(e,r)}_getReauthenticationResolver(e){var t=this.buildRequest();return t.autoCreate=!1,E(e,t)}buildRequest(){var e,t={requestUri:"http://localhost",returnSecureToken:!0};return this.pendingToken?t.pendingToken=this.pendingToken:(e={},this.idToken&&(e.id_token=this.idToken),this.accessToken&&(e.access_token=this.accessToken),this.secret&&(e.oauth_token_secret=this.secret),e.providerId=this.providerId,this.nonce&&!this.pendingToken&&(e.nonce=this.nonce),t.postBody=te(e)),t}}async function Ut(e,t){return m(e,"POST","/v1/accounts:sendVerificationCode",p(e,t))}let Ft={USER_NOT_FOUND:"user-not-found"};class Vt extends At{constructor(e){super("phone","phone"),this.params=e}static _fromVerification(e,t){return new Vt({verificationId:e,verificationCode:t})}static _fromTokenResponse(e,t){return new Vt({phoneNumber:e,temporaryProof:t})}_getIdTokenResponse(e){return(async(e,t)=>s(e,"POST","/v1/accounts:signInWithPhoneNumber",p(e,t)))(e,this._makeVerificationRequest())}_linkToIdToken(e,t){return(async(e,t)=>{var r=await s(e,"POST","/v1/accounts:signInWithPhoneNumber",p(e,t));if(r.temporaryProof)throw Me(e,"account-exists-with-different-credential",r);return r})(e,Object.assign({idToken:t},this._makeVerificationRequest()))}_getReauthenticationResolver(e){return(async(e,t)=>s(e,"POST","/v1/accounts:signInWithPhoneNumber",p(e,Object.assign(Object.assign({},t),{operation:"REAUTH"})),Ft))(e,this._makeVerificationRequest())}_makeVerificationRequest(){var{temporaryProof:e,phoneNumber:t,verificationId:r,verificationCode:i}=this.params;return e&&t?{temporaryProof:e,phoneNumber:t}:{sessionInfo:r,code:i}}toJSON(){var e={providerId:this.providerId};return this.params.phoneNumber&&(e.phoneNumber=this.params.phoneNumber),this.params.temporaryProof&&(e.temporaryProof=this.params.temporaryProof),this.params.verificationCode&&(e.verificationCode=this.params.verificationCode),this.params.verificationId&&(e.verificationId=this.params.verificationId),e}static fromJSON(e){var{verificationId:t,verificationCode:r,phoneNumber:i,temporaryProof:n}=e="string"==typeof e?JSON.parse(e):e;return r||t||i||n?new Vt({verificationId:t,verificationCode:r,phoneNumber:i,temporaryProof:n}):null}}class xt{constructor(e){var t=re(ie(e)),r=null!=(r=t.apiKey)?r:null,i=null!=(i=t.oobCode)?i:null,n=(e=>{switch(e){case"recoverEmail":return"RECOVER_EMAIL";case"resetPassword":return"PASSWORD_RESET";case"signIn":return"EMAIL_SIGNIN";case"verifyEmail":return"VERIFY_EMAIL";case"verifyAndChangeEmail":return"VERIFY_AND_CHANGE_EMAIL";case"revertSecondFactorAddition":return"REVERT_SECOND_FACTOR_ADDITION";default:return null}})(null!=(n=t.mode)?n:null);g(r&&i&&n,"argument-error"),this.apiKey=r,this.operation=n,this.code=i,this.continueUrl=null!=(r=t.continueUrl)?r:null,this.languageCode=null!=(n=t.lang)?n:null,this.tenantId=null!=(i=t.tenantId)?i:null}static parseLink(e){t=re(ie(e=e)).link,r=t?re(ie(t)).deep_link_id:null;var t,r,i=((i=re(ie(e)).deep_link_id)?re(ie(i)).link:null)||i||r||t||e;try{return new xt(i)}catch(e){return null}}}class jt{constructor(){this.providerId=jt.PROVIDER_ID}static credential(e,t){return Mt._fromEmailAndPassword(e,t)}static credentialWithLink(e,t){var r=xt.parseLink(t);return g(r,"argument-error"),Mt._fromEmailAndCode(e,r.code,r.tenantId)}}jt.PROVIDER_ID="password",jt.EMAIL_PASSWORD_SIGN_IN_METHOD="password",jt.EMAIL_LINK_SIGN_IN_METHOD="emailLink";class k{constructor(e){this.providerId=e,this.defaultLanguageCode=null,this.customParameters={}}setDefaultLanguage(e){this.defaultLanguageCode=e}setCustomParameters(e){return this.customParameters=e,this}getCustomParameters(){return this.customParameters}}class Ht extends k{constructor(){super(...arguments),this.scopes=[]}addScope(e){return this.scopes.includes(e)||this.scopes.push(e),this}getScopes(){return[...this.scopes]}}class Wt extends Ht{static credentialFromJSON(e){var t="string"==typeof e?JSON.parse(e):e;return g("providerId"in t&&"signInMethod"in t,"argument-error"),b._fromParams(t)}credential(e){return this._credential(Object.assign(Object.assign({},e),{nonce:e.rawNonce}))}_credential(e){return g(e.idToken||e.accessToken,"argument-error"),b._fromParams(Object.assign(Object.assign({},e),{providerId:this.providerId,signInMethod:this.providerId}))}static credentialFromResult(e){return Wt.oauthCredentialFromTaggedObject(e)}static credentialFromError(e){return Wt.oauthCredentialFromTaggedObject(e.customData||{})}static oauthCredentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{oauthIdToken:t,oauthAccessToken:r,oauthTokenSecret:i,pendingToken:n,nonce:s,providerId:a}=e;if(!(r||i||t||n))return null;if(!a)return null;try{return new Wt(a)._credential({idToken:t,accessToken:r,nonce:s,pendingToken:n})}catch(e){return null}}}class S extends Ht{constructor(){super("facebook.com")}static credential(e){return b._fromParams({providerId:S.PROVIDER_ID,signInMethod:S.FACEBOOK_SIGN_IN_METHOD,accessToken:e})}static credentialFromResult(e){return S.credentialFromTaggedObject(e)}static credentialFromError(e){return S.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!(e&&"oauthAccessToken"in e))return null;if(!e.oauthAccessToken)return null;try{return S.credential(e.oauthAccessToken)}catch(e){return null}}}S.FACEBOOK_SIGN_IN_METHOD="facebook.com",S.PROVIDER_ID="facebook.com";class R extends Ht{constructor(){super("google.com"),this.addScope("profile")}static credential(e,t){return b._fromParams({providerId:R.PROVIDER_ID,signInMethod:R.GOOGLE_SIGN_IN_METHOD,idToken:e,accessToken:t})}static credentialFromResult(e){return R.credentialFromTaggedObject(e)}static credentialFromError(e){return R.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{oauthIdToken:t,oauthAccessToken:r}=e;if(!t&&!r)return null;try{return R.credential(t,r)}catch(e){return null}}}R.GOOGLE_SIGN_IN_METHOD="google.com",R.PROVIDER_ID="google.com";class A extends Ht{constructor(){super("github.com")}static credential(e){return b._fromParams({providerId:A.PROVIDER_ID,signInMethod:A.GITHUB_SIGN_IN_METHOD,accessToken:e})}static credentialFromResult(e){return A.credentialFromTaggedObject(e)}static credentialFromError(e){return A.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!(e&&"oauthAccessToken"in e))return null;if(!e.oauthAccessToken)return null;try{return A.credential(e.oauthAccessToken)}catch(e){return null}}}A.GITHUB_SIGN_IN_METHOD="github.com",A.PROVIDER_ID="github.com";class qt extends At{constructor(e,t){super(e,e),this.pendingToken=t}_getIdTokenResponse(e){return E(e,this.buildRequest())}_linkToIdToken(e,t){var r=this.buildRequest();return r.idToken=t,E(e,r)}_getReauthenticationResolver(e){var t=this.buildRequest();return t.autoCreate=!1,E(e,t)}toJSON(){return{signInMethod:this.signInMethod,providerId:this.providerId,pendingToken:this.pendingToken}}static fromJSON(e){var{providerId:t,signInMethod:r,pendingToken:i}="string"==typeof e?JSON.parse(e):e;return t&&r&&i&&t===r?new qt(t,i):null}static _create(e,t){return new qt(e,t)}buildRequest(){return{requestUri:"http://localhost",returnSecureToken:!0,pendingToken:this.pendingToken}}}class Bt extends k{constructor(e){g(e.startsWith("saml."),"argument-error"),super(e)}static credentialFromResult(e){return Bt.samlCredentialFromTaggedObject(e)}static credentialFromError(e){return Bt.samlCredentialFromTaggedObject(e.customData||{})}static credentialFromJSON(e){var t=qt.fromJSON(e);return g(t,"argument-error"),t}static samlCredentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{pendingToken:t,providerId:r}=e;if(!t||!r)return null;try{return qt._create(r,t)}catch(e){return null}}}class P extends Ht{constructor(){super("twitter.com")}static credential(e,t){return b._fromParams({providerId:P.PROVIDER_ID,signInMethod:P.TWITTER_SIGN_IN_METHOD,oauthToken:e,oauthTokenSecret:t})}static credentialFromResult(e){return P.credentialFromTaggedObject(e)}static credentialFromError(e){return P.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){if(!e)return null;var{oauthAccessToken:t,oauthTokenSecret:r}=e;if(!t||!r)return null;try{return P.credential(t,r)}catch(e){return null}}}async function zt(e,t){return s(e,"POST","/v1/accounts:signUp",p(e,t))}P.TWITTER_SIGN_IN_METHOD="twitter.com",P.PROVIDER_ID="twitter.com";class C{constructor(e){this.user=e.user,this.providerId=e.providerId,this._tokenResponse=e._tokenResponse,this.operationType=e.operationType}static async _fromIdTokenResponse(e,t,r,i=!1){var n=await _._fromIdTokenResponse(e,r,i),s=Gt(r);return new C({user:n,providerId:s,_tokenResponse:r,operationType:t})}static async _forOperation(e,t,r){await e._updateTokensIfNecessary(r,!0);var i=Gt(r);return new C({user:e,providerId:i,_tokenResponse:r,operationType:t})}}function Gt(e){return e.providerId||("phoneNumber"in e?"phone":null)}class Kt extends u{constructor(e,t,r,i){var n;super(t.code,t.message),this.operationType=r,this.user=i,Object.setPrototypeOf(this,Kt.prototype),this.customData={appName:e.name,tenantId:null!=(n=e.tenantId)?n:void 0,_serverResponse:t.customData._serverResponse,operationType:r}}static _fromErrorAndOperation(e,t,r,i){return new Kt(e,t,r,i)}}function Jt(t,r,e,i){return("reauthenticate"===r?e._getReauthenticationResolver(t):e._getIdTokenResponse(t)).catch(e=>{if("auth/multi-factor-auth-required"===e.code)throw Kt._fromErrorAndOperation(t,e,r,i);throw e})}function Yt(e){return new Set(e.map(({providerId:e})=>e).filter(e=>!!e))}async function $t(e,t){var r=o(e),i=(await Qt(!0,r,t),e=r.auth,t={idToken:await r.getIdToken(),deleteProvider:[t]},await m(e,"POST","/v1/accounts:update",t)).providerUserInfo;let n=Yt(i||[]);return r.providerData=r.providerData.filter(e=>n.has(e.providerId)),n.has("phone")||(r.phoneNumber=null),await r.auth._persistUserIfCurrent(r),r}async function Xt(e,t,r=!1){var i=await v(e,t._linkToIdToken(e.auth,await e.getIdToken()),r);return C._forOperation(e,"link",i)}async function Qt(e,t,r){await Ke(t);var i=!1===e?"provider-already-linked":"no-such-provider";g(Yt(t.providerData).has(r)===e,t.auth,i)}async function Zt(e,t,r=!1){var i=e.auth;if(qn._isFirebaseServerApp(i.app))return Promise.reject(l(i));var n="reauthenticate";try{var s=await v(e,Jt(i,n,t,e),r),a=(g(s.idToken,i,"internal-error"),qe(s.idToken)),o=(g(a,i,"internal-error"),a).sub;return g(e.uid===o,i,"user-mismatch"),C._forOperation(e,n,s)}catch(e){throw"auth/user-not-found"===(null==e?void 0:e.code)&&d(i,"user-mismatch"),e}}async function er(e,t,r=!1){var i;return qn._isFirebaseServerApp(e.app)?Promise.reject(l(e)):(i=await Jt(e,"signIn",t),i=await C._fromIdTokenResponse(e,"signIn",i),r||await e._updateCurrentUser(i.user),i)}async function tr(e,t){return er(w(e),t)}async function rr(e,t){var r=o(e);return await Qt(!1,r,t.providerId),Xt(r,t)}async function ir(e,t){return Zt(o(e),t)}async function nr(e,t){var r,i;return qn._isFirebaseServerApp(e.app)?Promise.reject(l(e)):(i=await s(r=w(e),"POST","/v1/accounts:signInWithCustomToken",p(r,{token:t,returnSecureToken:!0})),i=await C._fromIdTokenResponse(r,"signIn",i),await r._updateCurrentUser(i.user),i)}class sr{constructor(e,t){this.factorId=e,this.uid=t.mfaEnrollmentId,this.enrollmentTime=new Date(t.enrolledAt).toUTCString(),this.displayName=t.displayName}static _fromServerResponse(e,t){return"phoneInfo"in t?ar._fromServerResponse(e,t):"totpInfo"in t?or._fromServerResponse(e,t):d(e,"internal-error")}}class ar extends sr{constructor(e){super("phone",e),this.phoneNumber=e.phoneInfo}static _fromServerResponse(e,t){return new ar(t)}}class or extends sr{constructor(e){super("totp",e)}static _fromServerResponse(e,t){return new or(t)}}function lr(e,t,r){var i;g(0<(null==(i=r.url)?void 0:i.length),e,"invalid-continue-uri"),g(void 0===r.dynamicLinkDomain||0<r.dynamicLinkDomain.length,e,"invalid-dynamic-link-domain"),g(void 0===r.linkDomain||0<r.linkDomain.length,e,"invalid-hosting-link-domain"),t.continueUrl=r.url,t.dynamicLinkDomain=r.dynamicLinkDomain,t.linkDomain=r.linkDomain,t.canHandleCodeInApp=r.handleCodeInApp,r.iOS&&(g(0<r.iOS.bundleId.length,e,"missing-ios-bundle-id"),t.iOSBundleId=r.iOS.bundleId),r.android&&(g(0<r.android.packageName.length,e,"missing-android-pkg-name"),t.androidInstallApp=r.android.installApp,t.androidMinimumVersionCode=r.android.minimumVersion,t.androidPackageName=r.android.packageName)}async function cr(e){var t=w(e);t._getPasswordPolicyInternal()&&await t._updatePasswordPolicy()}async function ur(e,t){await m(e=o(e),"POST","/v1/accounts:update",p(e,{oobCode:t}))}async function dr(e,t){var r=o(e),i=await Pt(r,{oobCode:t}),n=i.requestType;switch(g(n,r,"internal-error"),n){case"EMAIL_SIGNIN":break;case"VERIFY_AND_CHANGE_EMAIL":g(i.newEmail,r,"internal-error");break;case"REVERT_SECOND_FACTOR_ADDITION":g(i.mfaInfo,r,"internal-error");default:g(i.email,r,"internal-error")}let s=null;return i.mfaInfo&&(s=sr._fromServerResponse(w(r),i.mfaInfo)),{data:{email:("VERIFY_AND_CHANGE_EMAIL"===i.requestType?i.newEmail:i.email)||null,previousEmail:("VERIFY_AND_CHANGE_EMAIL"===i.requestType?i.email:i.newEmail)||null,multiFactorInfo:s},operation:n}}async function hr(e,t){var r=be()?Ee():"http://localhost",r=(await m(e=o(e),"POST","/v1/accounts:createAuthUri",p(e,{identifier:t,continueUri:r}))).signinMethods;return r||[]}async function pr(e,t){var r=o(e),i={requestType:"VERIFY_EMAIL",idToken:await e.getIdToken()},r=(t&&lr(r.auth,i,t),await Nt(r.auth,i)).email;r!==e.email&&await e.reload()}async function mr(e,t,r){var i=o(e),n={requestType:"VERIFY_AND_CHANGE_EMAIL",idToken:await e.getIdToken(),newEmail:t},i=(r&&lr(i.auth,n,r),await Nt(i.auth,n)).email;i!==e.email&&await e.reload()}async function gr(e,{displayName:t,photoURL:r}){var i,n,s;void 0===t&&void 0===r||(n=await(i=o(e)).getIdToken(),n=await v(i,(async(e,t)=>m(e,"POST","/v1/accounts:update",t))(i.auth,{idToken:n,displayName:t,photoUrl:r,returnSecureToken:!0})),i.displayName=n.displayName||null,i.photoURL=n.photoUrl||null,(s=i.providerData.find(({providerId:e})=>"password"===e))&&(s.displayName=i.displayName,s.photoURL=i.photoURL),await i._updateTokensIfNecessary(n))}async function vr(e,t,r){var i=e.auth,n={idToken:await e.getIdToken(),returnSecureToken:!0},i=(t&&(n.email=t),r&&(n.password=r),await v(e,(async(e,t)=>m(e,"POST","/v1/accounts:update",t))(i,n)));await e._updateTokensIfNecessary(i,!0)}class fr{constructor(e,t,r={}){this.isNewUser=e,this.providerId=t,this.profile=r}}class _r extends fr{constructor(e,t,r,i){super(e,t,r),this.username=i}}class yr extends fr{constructor(e,t){super(e,"facebook.com",t)}}class Ir extends _r{constructor(e,t){super(e,"github.com",t,"string"==typeof(null==t?void 0:t.login)?null==t?void 0:t.login:null)}}class wr extends fr{constructor(e,t){super(e,"google.com",t)}}class Tr extends _r{constructor(e,t,r){super(e,"twitter.com",t,r)}}function Er(e){var{user:t,_tokenResponse:r}=e;if(t.isAnonymous&&!r)return{providerId:null,isNewUser:!1,profile:null};var i=r;if(!i)return null;var n=i.providerId,s=i.rawUserInfo?JSON.parse(i.rawUserInfo):{},a=i.isNewUser||"identitytoolkit#SignupNewUserResponse"===i.kind;if(!n&&null!=i&&i.idToken){t=null==(t=null==(t=qe(i.idToken))?void 0:t.firebase)?void 0:t.sign_in_provider;if(t)return t="anonymous"!==t&&"custom"!==t?t:null,new fr(a,t)}if(!n)return null;switch(n){case"facebook.com":return new yr(a,s);case"github.com":return new Ir(a,s);case"google.com":return new wr(a,s);case"twitter.com":return new Tr(a,s,i.screenName||null);case"custom":case"anonymous":return new fr(a,null);default:return new fr(a,n,s)}}class br{constructor(e,t,r){this.type=e,this.credential=t,this.user=r}static _fromIdtoken(e,t){return new br("enroll",e,t)}static _fromMfaPendingCredential(e){return new br("signin",e)}toJSON(){return{multiFactorSession:{["enroll"===this.type?"idToken":"pendingCredential"]:this.credential}}}static fromJSON(e){var t;if(null!=e&&e.multiFactorSession){if(null!=(t=e.multiFactorSession)&&t.pendingCredential)return br._fromMfaPendingCredential(e.multiFactorSession.pendingCredential);if(null!=(t=e.multiFactorSession)&&t.idToken)return br._fromIdtoken(e.multiFactorSession.idToken)}return null}}class kr{constructor(e,t,r){this.session=e,this.hints=t,this.signInResolver=r}static _fromError(e,n){let s=w(e),a=n.customData._serverResponse;var t=(a.mfaInfo||[]).map(e=>sr._fromServerResponse(s,e));g(a.mfaPendingCredential,s,"internal-error");let o=br._fromMfaPendingCredential(a.mfaPendingCredential);return new kr(o,t,async e=>{var t=await e._process(s,o),r=(delete a.mfaInfo,delete a.mfaPendingCredential,Object.assign(Object.assign({},a),{idToken:t.idToken,refreshToken:t.refreshToken}));switch(n.operationType){case"signIn":var i=await C._fromIdTokenResponse(s,n.operationType,r);return await s._updateCurrentUser(i.user),i;case"reauthenticate":return g(n.user,s,"internal-error"),C._forOperation(n.user,n.operationType,r);default:d(s,"internal-error")}})}async resolveSignIn(e){return this.signInResolver(e)}}function Sr(e,t){return m(e,"POST","/v2/accounts/mfaEnrollment:start",p(e,t))}class Rr{constructor(t){this.user=t,this.enrolledFactors=[],t._onReload(e=>{e.mfaInfo&&(this.enrolledFactors=e.mfaInfo.map(e=>sr._fromServerResponse(t.auth,e)))})}static _fromUser(e){return new Rr(e)}async getSession(){return br._fromIdtoken(await this.user.getIdToken(),this.user)}async enroll(e,t){var r=e,i=await this.getSession(),r=await v(this.user,r._process(this.user.auth,i,t));return await this.user._updateTokensIfNecessary(r),this.user.reload()}async unenroll(e){let t="string"==typeof e?e:e.uid;var r,i,n=await this.user.getIdToken();try{var s=await v(this.user,(r=this.user.auth,i={idToken:n,mfaEnrollmentId:t},m(r,"POST","/v2/accounts/mfaEnrollment:withdraw",p(r,i))));this.enrolledFactors=this.enrolledFactors.filter(({uid:e})=>e!==t),await this.user._updateTokensIfNecessary(s),await this.user.reload()}catch(e){throw e}}}let Ar=new WeakMap;let Pr="__sak";class Cr{constructor(e,t){this.storageRetriever=e,this.type=t}_isAvailable(){try{return this.storage?(this.storage.setItem(Pr,"1"),this.storage.removeItem(Pr),Promise.resolve(!0)):Promise.resolve(!1)}catch(e){return Promise.resolve(!1)}}_set(e,t){return this.storage.setItem(e,JSON.stringify(t)),Promise.resolve()}_get(e){var t=this.storage.getItem(e);return Promise.resolve(t?JSON.parse(t):null)}_remove(e){return this.storage.removeItem(e),Promise.resolve()}get storage(){return this.storageRetriever()}}class Or extends Cr{constructor(){super(()=>window.localStorage,"LOCAL"),this.boundEventHandler=(e,t)=>this.onStorageEvent(e,t),this.listeners={},this.localCache={},this.pollTimer=null,this.fallbackToPolling=ct(),this._shouldAllowMigration=!0}forAllChangedKeys(e){for(var t of Object.keys(this.listeners)){var r=this.storage.getItem(t),i=this.localCache[t];r!==i&&e(t,i,r)}}onStorageEvent(e,r=!1){if(e.key){let t=e.key;r?this.detachListener():this.stopPolling();var i=()=>{var e=this.storage.getItem(t);!r&&this.localCache[t]===e||this.notifyListeners(t,e)},n=this.storage.getItem(t);Y()&&10===document.documentMode&&n!==e.newValue&&e.newValue!==e.oldValue?setTimeout(i,10):i()}else this.forAllChangedKeys((e,t,r)=>{this.notifyListeners(e,r)})}notifyListeners(e,t){this.localCache[e]=t;var r=this.listeners[e];if(r)for(var i of Array.from(r))i(t&&JSON.parse(t))}startPolling(){this.stopPolling(),this.pollTimer=setInterval(()=>{this.forAllChangedKeys((e,t,r)=>{this.onStorageEvent(new StorageEvent("storage",{key:e,oldValue:t,newValue:r}),!0)})},1e3)}stopPolling(){this.pollTimer&&(clearInterval(this.pollTimer),this.pollTimer=null)}attachListener(){window.addEventListener("storage",this.boundEventHandler)}detachListener(){window.removeEventListener("storage",this.boundEventHandler)}_addListener(e,t){0===Object.keys(this.listeners).length&&(this.fallbackToPolling?this.startPolling():this.attachListener()),this.listeners[e]||(this.listeners[e]=new Set,this.localCache[e]=this.storage.getItem(e)),this.listeners[e].add(t)}_removeListener(e,t){this.listeners[e]&&(this.listeners[e].delete(t),0===this.listeners[e].size)&&delete this.listeners[e],0===Object.keys(this.listeners).length&&(this.detachListener(),this.stopPolling())}async _set(e,t){await super._set(e,t),this.localCache[e]=JSON.stringify(t)}async _get(e){var t=await super._get(e);return this.localCache[e]=JSON.stringify(t),t}async _remove(e){await super._remove(e),delete this.localCache[e]}}Or.type="LOCAL";let Nr=Or;class Lr extends Cr{constructor(){super(()=>window.sessionStorage,"SESSION")}_addListener(e,t){}_removeListener(e,t){}}Lr.type="SESSION";let Dr=Lr;class Mr{constructor(e){this.eventTarget=e,this.handlersMap={},this.boundEventHandler=this.handleEvent.bind(this)}static _getInstance(t){var e=this.receivers.find(e=>e.isListeningto(t));return e||(e=new Mr(t),this.receivers.push(e),e)}isListeningto(e){return this.eventTarget===e}async handleEvent(e){let t=e,{eventId:r,eventType:i,data:n}=t.data;var s=this.handlersMap[i];null!=s&&s.size&&(t.ports[0].postMessage({status:"ack",eventId:r,eventType:i}),s=Array.from(s).map(async e=>e(t.origin,n)),s=await Promise.all(s.map(async e=>{try{return{fulfilled:!0,value:await e}}catch(e){return{fulfilled:!1,reason:e}}})),t.ports[0].postMessage({status:"done",eventId:r,eventType:i,response:s}))}_subscribe(e,t){0===Object.keys(this.handlersMap).length&&this.eventTarget.addEventListener("message",this.boundEventHandler),this.handlersMap[e]||(this.handlersMap[e]=new Set),this.handlersMap[e].add(t)}_unsubscribe(e,t){this.handlersMap[e]&&t&&this.handlersMap[e].delete(t),t&&0!==this.handlersMap[e].size||delete this.handlersMap[e],0===Object.keys(this.handlersMap).length&&this.eventTarget.removeEventListener("message",this.boundEventHandler)}}function Ur(e="",t=10){let r="";for(let i=0;i<t;i++)r+=Math.floor(10*Math.random());return e+r}Mr.receivers=[];class Fr{constructor(e){this.target=e,this.handlers=new Set}removeMessageHandler(e){e.messageChannel&&(e.messageChannel.port1.removeEventListener("message",e.onMessage),e.messageChannel.port1.close()),this.handlers.delete(e)}async _send(e,t,a=50){let o="undefined"!=typeof MessageChannel?new MessageChannel:null;if(!o)throw new Error("connection_unavailable");let l,c;return new Promise((r,i)=>{let n=Ur("",20),s=(o.port1.start(),setTimeout(()=>{i(new Error("unsupported_event"))},a));c={messageChannel:o,onMessage(e){var t=e;if(t.data.eventId===n)switch(t.data.status){case"ack":clearTimeout(s),l=setTimeout(()=>{i(new Error("timeout"))},3e3);break;case"done":clearTimeout(l),r(t.data.response);break;default:clearTimeout(s),clearTimeout(l),i(new Error("invalid_response"))}}},this.handlers.add(c),o.port1.addEventListener("message",c.onMessage),this.target.postMessage({eventType:e,eventId:n,data:t},[o.port2])}).finally(()=>{c&&this.removeMessageHandler(c)})}}function O(){return window}function Vr(){return void 0!==O().WorkerGlobalScope&&"function"==typeof O().importScripts}let xr="firebaseLocalStorageDb",jr="firebaseLocalStorage",Hr="fbase_key";class Wr{constructor(e){this.request=e}toPromise(){return new Promise((e,t)=>{this.request.addEventListener("success",()=>{e(this.request.result)}),this.request.addEventListener("error",()=>{t(this.request.error)})})}}function qr(e,t){return e.transaction([jr],t?"readwrite":"readonly").objectStore(jr)}function Br(){let i=indexedDB.open(xr,1);return new Promise((t,r)=>{i.addEventListener("error",()=>{r(i.error)}),i.addEventListener("upgradeneeded",()=>{var e=i.result;try{e.createObjectStore(jr,{keyPath:Hr})}catch(e){r(e)}}),i.addEventListener("success",async()=>{var e=i.result;e.objectStoreNames.contains(jr)?t(e):(e.close(),e=indexedDB.deleteDatabase(xr),await new Wr(e).toPromise(),t(await Br()))})})}async function zr(e,t,r){var i=qr(e,!0).put({fbase_key:t,value:r});return new Wr(i).toPromise()}function Gr(e,t){var r=qr(e,!0).delete(t);return new Wr(r).toPromise()}class Kr{constructor(){this.type="LOCAL",this._shouldAllowMigration=!0,this.listeners={},this.localCache={},this.pollTimer=null,this.pendingWrites=0,this.receiver=null,this.sender=null,this.serviceWorkerReceiverAvailable=!1,this.activeServiceWorker=null,this._workerInitializationPromise=this.initializeServiceWorkerMessaging().then(()=>{},()=>{})}async _openDb(){return this.db||(this.db=await Br()),this.db}async _withRetries(e){let t=0;for(;;)try{return await e(await this._openDb())}catch(e){if(3<t++)throw e;this.db&&(this.db.close(),this.db=void 0)}}async initializeServiceWorkerMessaging(){return Vr()?this.initializeReceiver():this.initializeSender()}async initializeReceiver(){this.receiver=Mr._getInstance(Vr()?self:null),this.receiver._subscribe("keyChanged",async(e,t)=>({keyProcessed:(await this._poll()).includes(t.key)})),this.receiver._subscribe("ping",async(e,t)=>["keyChanged"])}async initializeSender(){var e,t;this.activeServiceWorker=await(async()=>{if(null==navigator||!navigator.serviceWorker)return null;try{return(await navigator.serviceWorker.ready).active}catch(e){return null}})(),this.activeServiceWorker&&(this.sender=new Fr(this.activeServiceWorker),t=await this.sender._send("ping",{},800))&&null!=(e=t[0])&&e.fulfilled&&null!=(e=t[0])&&e.value.includes("keyChanged")&&(this.serviceWorkerReceiverAvailable=!0)}async notifyServiceWorker(e){var t;if(this.sender&&this.activeServiceWorker&&((null==(t=null==navigator?void 0:navigator.serviceWorker)?void 0:t.controller)||null)===this.activeServiceWorker)try{await this.sender._send("keyChanged",{key:e},this.serviceWorkerReceiverAvailable?800:50)}catch(e){}}async _isAvailable(){try{var e;return indexedDB?(await zr(e=await Br(),Pr,"1"),await Gr(e,Pr),!0):!1}catch(e){}return!1}async _withPendingWrite(e){this.pendingWrites++;try{await e()}finally{this.pendingWrites--}}async _set(t,r){return this._withPendingWrite(async()=>(await this._withRetries(e=>zr(e,t,r)),this.localCache[t]=r,this.notifyServiceWorker(t)))}async _get(t){var e=await this._withRetries(e=>(async(e,t)=>{var r=qr(e,!1).get(t);return void 0===(r=await new Wr(r).toPromise())?null:r.value})(e,t));return this.localCache[t]=e}async _remove(t){return this._withPendingWrite(async()=>(await this._withRetries(e=>Gr(e,t)),delete this.localCache[t],this.notifyServiceWorker(t)))}async _poll(){var e=await this._withRetries(e=>{var t=qr(e,!1).getAll();return new Wr(t).toPromise()});if(!e)return[];if(0!==this.pendingWrites)return[];var t,r=[],i=new Set;if(0!==e.length)for(var{fbase_key:n,value:s}of e)i.add(n),JSON.stringify(this.localCache[n])!==JSON.stringify(s)&&(this.notifyListeners(n,s),r.push(n));for(t of Object.keys(this.localCache))this.localCache[t]&&!i.has(t)&&(this.notifyListeners(t,null),r.push(t));return r}notifyListeners(e,t){this.localCache[e]=t;var r=this.listeners[e];if(r)for(var i of Array.from(r))i(t)}startPolling(){this.stopPolling(),this.pollTimer=setInterval(async()=>this._poll(),800)}stopPolling(){this.pollTimer&&(clearInterval(this.pollTimer),this.pollTimer=null)}_addListener(e,t){0===Object.keys(this.listeners).length&&this.startPolling(),this.listeners[e]||(this.listeners[e]=new Set,this._get(e)),this.listeners[e].add(t)}_removeListener(e,t){this.listeners[e]&&(this.listeners[e].delete(t),0===this.listeners[e].size)&&delete this.listeners[e],0===Object.keys(this.listeners).length&&this.stopPolling()}}Kr.type="LOCAL";let Jr=Kr;function Yr(e,t){return m(e,"POST","/v2/accounts/mfaSignIn:start",p(e,t))}let $r=ft("rcb"),Xr=new Se(3e4,6e4);class Qr{constructor(){var e;this.hostLanguage="",this.counter=0,this.librarySeparatelyLoaded=!(null==(e=O().grecaptcha)||!e.render)}load(n,s=""){var e;return g((e=s).length<=6&&/^\s*[a-zA-Z0-9\-]*\s*$/.test(e),n,"argument-error"),this.shouldResolveImmediately(s)&&Ue(O().grecaptcha)?Promise.resolve(O().grecaptcha):new Promise((t,r)=>{let i=O().setTimeout(()=>{r(h(n,"network-request-failed"))},Xr.get());O()[$r]=()=>{O().clearTimeout(i),delete O()[$r];var e=O().grecaptcha;if(e&&Ue(e)){let i=e.render;e.render=(e,t)=>{var r=i(e,t);return this.counter++,r},this.hostLanguage=s,t(e)}else r(h(n,"internal-error"))},vt(gt.recaptchaV2Script+"?"+te({onload:$r,render:"explicit",hl:s})).catch(()=>{clearTimeout(i),r(h(n,"internal-error"))})})}clearedOneInstance(){this.counter--}shouldResolveImmediately(e){var t;return!(null==(t=O().grecaptcha)||!t.render)&&(e===this.hostLanguage||0<this.counter||this.librarySeparatelyLoaded)}}class Zr{async load(e){return new _t(e)}clearedOneInstance(){}}let ei="recaptcha",ti={theme:"light",type:"image"};class ri{constructor(e,t,r=Object.assign({},ti)){this.parameters=r,this.type=ei,this.destroyed=!1,this.widgetId=null,this.tokenChangeListeners=new Set,this.renderPromise=null,this.recaptcha=null,this.auth=w(e),this.isInvisible="invisible"===this.parameters.size,g("undefined"!=typeof document,this.auth,"operation-not-supported-in-this-environment");var i="string"==typeof t?document.getElementById(t):t;g(i,this.auth,"argument-error"),this.container=i,this.parameters.callback=this.makeTokenCallback(this.parameters.callback),this._recaptchaLoader=new(this.auth.settings.appVerificationDisabledForTesting?Zr:Qr),this.validateStartingState()}async verify(){this.assertNotDestroyed();let e=await this.render(),i=this.getAssertedRecaptcha();var t=i.getResponse(e);return t||new Promise(t=>{let r=e=>{e&&(this.tokenChangeListeners.delete(r),t(e))};this.tokenChangeListeners.add(r),this.isInvisible&&i.execute(e)})}render(){try{this.assertNotDestroyed()}catch(e){return Promise.reject(e)}return this.renderPromise||(this.renderPromise=this.makeRenderPromise().catch(e=>{throw this.renderPromise=null,e})),this.renderPromise}_reset(){this.assertNotDestroyed(),null!==this.widgetId&&this.getAssertedRecaptcha().reset(this.widgetId)}clear(){this.assertNotDestroyed(),this.destroyed=!0,this._recaptchaLoader.clearedOneInstance(),this.isInvisible||this.container.childNodes.forEach(e=>{this.container.removeChild(e)})}validateStartingState(){g(!this.parameters.sitekey,this.auth,"argument-error"),g(this.isInvisible||!this.container.hasChildNodes(),this.auth,"argument-error"),g("undefined"!=typeof document,this.auth,"operation-not-supported-in-this-environment")}makeTokenCallback(r){return t=>{var e;this.tokenChangeListeners.forEach(e=>e(t)),"function"==typeof r?r(t):"string"==typeof r&&"function"==typeof(e=O()[r])&&e(t)}}assertNotDestroyed(){g(!this.destroyed,this.auth,"internal-error")}async makeRenderPromise(){if(await this.init(),!this.widgetId){let e=this.container;var t;this.isInvisible||(t=document.createElement("div"),e.appendChild(t),e=t),this.widgetId=this.getAssertedRecaptcha().render(e,this.parameters)}return this.widgetId}async init(){g(be()&&!Vr(),this.auth,"internal-error"),await(()=>{let t=null;return new Promise(e=>{"complete"===document.readyState?e():(t=()=>e(),window.addEventListener("load",t))}).catch(e=>{throw t&&window.removeEventListener("load",t),e})})(),this.recaptcha=await this._recaptchaLoader.load(this.auth,this.auth.languageCode||void 0);var e=await((await m(this.auth,"GET","/v1/recaptchaParams")).recaptchaSiteKey||"");g(e,this.auth,"internal-error"),this.parameters.sitekey=e}getAssertedRecaptcha(){return g(this.recaptcha,this.auth,"internal-error"),this.recaptcha}}class ii{constructor(e,t){this.verificationId=e,this.onConfirmation=t}confirm(e){var t=Vt._fromVerification(this.verificationId,e);return this.onConfirmation(t)}}async function ni(t,r,i){var n,e,s,a,o,l,c,u;if(!t._getRecaptchaConfig())try{e=w(t),s=await xe(e,{clientType:"CLIENT_TYPE_WEB",version:"RECAPTCHA_ENTERPRISE"}),s=new Ve(s),null==e.tenantId?e._agentRecaptchaConfig=s:e._tenantRecaptchaConfigs[e.tenantId]=s,await(!s.isAnyProviderEnabled()||!new Et(e).verify())}catch(e){console.log("Failed to initialize reCAPTCHA Enterprise config. Triggering the reCAPTCHA v2 verification.")}try{let e;return("session"in(e="string"==typeof r?{phoneNumber:r}:r)?(a=e.session,"phoneNumber"in e?(g("enroll"===a.type,t,"internal-error"),o={idToken:a.credential,phoneEnrollmentInfo:{phoneNumber:e.phoneNumber,clientType:"CLIENT_TYPE_WEB"}},(await T(t,o,"mfaSmsEnrollment",async(e,t)=>t.phoneEnrollmentInfo.captchaResponse===Tt?(g((null==i?void 0:i.type)===ei,e,"argument-error"),Sr(e,await si(e,t,i))):Sr(e,t),"PHONE_PROVIDER").catch(e=>Promise.reject(e))).phoneSessionInfo):(g("signin"===a.type,t,"internal-error"),g(l=(null==(n=e.multiFactorHint)?void 0:n.uid)||e.multiFactorUid,t,"missing-multi-factor-info"),c={mfaPendingCredential:a.credential,mfaEnrollmentId:l,phoneSignInInfo:{clientType:"CLIENT_TYPE_WEB"}},(await T(t,c,"mfaSmsSignIn",async(e,t)=>t.phoneSignInInfo.captchaResponse===Tt?(g((null==i?void 0:i.type)===ei,e,"argument-error"),Yr(e,await si(e,t,i))):Yr(e,t),"PHONE_PROVIDER").catch(e=>Promise.reject(e))).phoneResponseInfo)):(u={phoneNumber:e.phoneNumber,clientType:"CLIENT_TYPE_WEB"},await T(t,u,"sendVerificationCode",async(e,t)=>t.captchaResponse===Tt?(g((null==i?void 0:i.type)===ei,e,"argument-error"),Ut(e,await si(e,t,i))):Ut(e,t),"PHONE_PROVIDER").catch(e=>Promise.reject(e)))).sessionInfo}finally{null!=i&&i._reset()}}async function si(e,t,r){g(r.type===ei,e,"argument-error");var i,n,s,a,o=await r.verify(),l=(g("string"==typeof o,e,"argument-error"),Object.assign({},t));return"phoneEnrollmentInfo"in l?(n=l.phoneEnrollmentInfo.phoneNumber,s=l.phoneEnrollmentInfo.captchaResponse,a=l.phoneEnrollmentInfo.clientType,i=l.phoneEnrollmentInfo.recaptchaVersion,Object.assign(l,{phoneEnrollmentInfo:{phoneNumber:n,recaptchaToken:o,captchaResponse:s,clientType:a,recaptchaVersion:i}})):"phoneSignInInfo"in l?(n=l.phoneSignInInfo.captchaResponse,s=l.phoneSignInInfo.clientType,a=l.phoneSignInInfo.recaptchaVersion,Object.assign(l,{phoneSignInInfo:{recaptchaToken:o,captchaResponse:n,clientType:s,recaptchaVersion:a}})):Object.assign(l,{recaptchaToken:o}),l}class N{constructor(e){this.providerId=N.PROVIDER_ID,this.auth=w(e)}verifyPhoneNumber(e,t){return ni(this.auth,e,o(t))}static credential(e,t){return Vt._fromVerification(e,t)}static credentialFromResult(e){var t=e;return N.credentialFromTaggedObject(t)}static credentialFromError(e){return N.credentialFromTaggedObject(e.customData||{})}static credentialFromTaggedObject({_tokenResponse:e}){var t,r;return e&&({phoneNumber:t,temporaryProof:r}=e,t)&&r?Vt._fromTokenResponse(t,r):null}}function ai(e,t){return t?y(t):(g(e._popupRedirectResolver,e,"argument-error"),e._popupRedirectResolver)}N.PROVIDER_ID="phone",N.PHONE_SIGN_IN_METHOD="phone";class oi extends At{constructor(e){super("custom","custom"),this.params=e}_getIdTokenResponse(e){return E(e,this._buildIdpRequest())}_linkToIdToken(e,t){return E(e,this._buildIdpRequest(t))}_getReauthenticationResolver(e){return E(e,this._buildIdpRequest())}_buildIdpRequest(e){var t={requestUri:this.params.requestUri,sessionId:this.params.sessionId,postBody:this.params.postBody,tenantId:this.params.tenantId,pendingToken:this.params.pendingToken,returnSecureToken:!0,returnIdpCredential:!0};return e&&(t.idToken=e),t}}function li(e){return er(e.auth,new oi(e),e.bypassAuthState)}function ci(e){var{auth:t,user:r}=e;return g(r,t,"internal-error"),Zt(r,new oi(e),e.bypassAuthState)}async function ui(e){var{auth:t,user:r}=e;return g(r,t,"internal-error"),Xt(r,new oi(e),e.bypassAuthState)}class di{constructor(e,t,r,i,n=!1){this.auth=e,this.resolver=r,this.user=i,this.bypassAuthState=n,this.pendingPromise=null,this.eventManager=null,this.filter=Array.isArray(t)?t:[t]}execute(){return new Promise(async(e,t)=>{this.pendingPromise={resolve:e,reject:t};try{this.eventManager=await this.resolver._initialize(this.auth),await this.onExecution(),this.eventManager.registerConsumer(this)}catch(e){this.reject(e)}})}async onAuthEvent(e){var{urlResponse:t,sessionId:r,postBody:i,tenantId:n,error:s,type:a}=e;if(s)this.reject(s);else{s={auth:this.auth,requestUri:t,sessionId:r,tenantId:n||void 0,postBody:i||void 0,user:this.user,bypassAuthState:this.bypassAuthState};try{this.resolve(await this.getIdpTask(a)(s))}catch(e){this.reject(e)}}}onError(e){this.reject(e)}getIdpTask(e){switch(e){case"signInViaPopup":case"signInViaRedirect":return li;case"linkViaPopup":case"linkViaRedirect":return ui;case"reauthViaPopup":case"reauthViaRedirect":return ci;default:d(this.auth,"internal-error")}}resolve(e){a(this.pendingPromise,"Pending promise was never set"),this.pendingPromise.resolve(e),this.unregisterAndCleanUp()}reject(e){a(this.pendingPromise,"Pending promise was never set"),this.pendingPromise.reject(e),this.unregisterAndCleanUp()}unregisterAndCleanUp(){this.eventManager&&this.eventManager.unregisterConsumer(this),this.pendingPromise=null,this.cleanUp()}}let hi=new Se(2e3,1e4);class L extends di{constructor(e,t,r,i,n){super(e,t,i,n),this.provider=r,this.authWindow=null,this.pollId=null,L.currentPopupAction&&L.currentPopupAction.cancel(),L.currentPopupAction=this}async executeNotNull(){var e=await this.execute();return g(e,this.auth,"internal-error"),e}async onExecution(){a(1===this.filter.length,"Popup operations only handle one event");var e=Ur();this.authWindow=await this.resolver._openPopup(this.auth,this.provider,this.filter[0],e),this.authWindow.associatedEvent=e,this.resolver._originValidation(this.auth).catch(e=>{this.reject(e)}),this.resolver._isIframeWebStorageSupported(this.auth,e=>{e||this.reject(h(this.auth,"web-storage-unsupported"))}),this.pollUserCancellation()}get eventId(){var e;return(null==(e=this.authWindow)?void 0:e.associatedEvent)||null}cancel(){this.reject(h(this.auth,"cancelled-popup-request"))}cleanUp(){this.authWindow&&this.authWindow.close(),this.pollId&&window.clearTimeout(this.pollId),this.authWindow=null,this.pollId=null,L.currentPopupAction=null}pollUserCancellation(){let t=()=>{var e;null!=(e=null==(e=this.authWindow)?void 0:e.window)&&e.closed?this.pollId=window.setTimeout(()=>{this.pollId=null,this.reject(h(this.auth,"popup-closed-by-user"))},8e3):this.pollId=window.setTimeout(t,hi.get())};t()}}L.currentPopupAction=null;let pi="pendingRedirect",mi=new Map;class gi extends di{constructor(e,t,r=!1){super(e,["signInViaRedirect","linkViaRedirect","reauthViaRedirect","unknown"],t,void 0,r),this.eventId=null}async execute(){let t=mi.get(this.auth._key());if(!t){try{let e=await(async(e,t)=>{var r,i=yi(t),n=_i(e);return!!await n._isAvailable()&&(r="true"===await n._get(i),await n._remove(i),r)})(this.resolver,this.auth)?await super.execute():null;t=()=>Promise.resolve(e)}catch(e){t=()=>Promise.reject(e)}mi.set(this.auth._key(),t)}return this.bypassAuthState||mi.set(this.auth._key(),()=>Promise.resolve(null)),t()}async onAuthEvent(e){if("signInViaRedirect"===e.type)return super.onAuthEvent(e);if("unknown"===e.type)this.resolve(null);else if(e.eventId){var t=await this.auth._redirectUserForId(e.eventId);if(t)return this.user=t,super.onAuthEvent(e);this.resolve(null)}}async onExecution(){}cleanUp(){}}async function vi(e,t){return _i(e)._set(yi(t),"true")}function fi(e,t){mi.set(e._key(),t)}function _i(e){return y(e._redirectPersistence)}function yi(e){return I(pi,e.config.apiKey,e.name)}function Ii(e,t,r){return(async(e,t,r)=>{var i,n;return qn._isFirebaseServerApp(e.app)?Promise.reject(l(e)):(i=w(e),we(e,t,k),await i._initializationPromise,await vi(n=ai(i,r),i),n._openRedirect(i,t,"signInViaRedirect"))})(e,t,r)}function wi(e,t,r){return(async(e,t,r)=>{var i=o(e);if(we(i.auth,t,k),qn._isFirebaseServerApp(i.auth.app))return Promise.reject(l(i.auth));await i.auth._initializationPromise;var n=ai(i.auth,r),s=(await vi(n,i.auth),await bi(i));return n._openRedirect(i.auth,t,"reauthViaRedirect",s)})(e,t,r)}function Ti(e,t,r){return(async(e,t,r)=>{var i=o(e),n=(we(i.auth,t,k),await i.auth._initializationPromise,ai(i.auth,r)),s=(await Qt(!1,i,t.providerId),await vi(n,i.auth),await bi(i));return n._openRedirect(i.auth,t,"linkViaRedirect",s)})(e,t,r)}async function Ei(e,t,r=!1){var i,n;return qn._isFirebaseServerApp(e.app)?Promise.reject(l(e)):(n=ai(i=w(e),t),(n=await new gi(i,n,r).execute())&&!r&&(delete n.user._redirectEventId,await i._persistUserIfCurrent(n.user),await i._setRedirectUser(null,t)),n)}async function bi(e){var t=Ur(e.uid+":::");return e._redirectEventId=t,await e.auth._setRedirectUser(e),await e.auth._persistUserIfCurrent(e),t}class ki{constructor(e){this.auth=e,this.cachedEventUids=new Set,this.consumers=new Set,this.queuedRedirectEvent=null,this.hasHandledPotentialRedirect=!1,this.lastProcessedEventTime=Date.now()}registerConsumer(e){this.consumers.add(e),this.queuedRedirectEvent&&this.isEventForConsumer(this.queuedRedirectEvent,e)&&(this.sendToConsumer(this.queuedRedirectEvent,e),this.saveEventToCache(this.queuedRedirectEvent),this.queuedRedirectEvent=null)}unregisterConsumer(e){this.consumers.delete(e)}onEvent(t){if(this.hasEventBeenHandled(t))return!1;let r=!1;return this.consumers.forEach(e=>{this.isEventForConsumer(t,e)&&(r=!0,this.sendToConsumer(t,e),this.saveEventToCache(t))}),this.hasHandledPotentialRedirect||!(e=>{switch(e.type){case"signInViaRedirect":case"linkViaRedirect":case"reauthViaRedirect":return 1;case"unknown":return Ri(e);default:return}})(t)||(this.hasHandledPotentialRedirect=!0,r)||(this.queuedRedirectEvent=t,r=!0),r}sendToConsumer(e,t){var r;e.error&&!Ri(e)?(r=(null==(r=e.error.code)?void 0:r.split("auth/")[1])||"internal-error",t.onError(h(this.auth,r))):t.onAuthEvent(e)}isEventForConsumer(e,t){var r=null===t.eventId||!!e.eventId&&e.eventId===t.eventId;return t.filter.includes(e.type)&&r}hasEventBeenHandled(e){return 6e5<=Date.now()-this.lastProcessedEventTime&&this.cachedEventUids.clear(),this.cachedEventUids.has(Si(e))}saveEventToCache(e){this.cachedEventUids.add(Si(e)),this.lastProcessedEventTime=Date.now()}}function Si(e){return[e.type,e.eventId,e.sessionId,e.tenantId].filter(e=>e).join("-")}function Ri({type:e,error:t}){return"unknown"===e&&"auth/no-auth-event"===(null==t?void 0:t.code)}async function Ai(e,t={}){return m(e,"GET","/v1/projects",t)}let Pi=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,Ci=/^https?/;async function Oi(e){if(!e.config.emulator){var t,r=(await Ai(e)).authorizedDomains;for(t of r)try{if((e=>{var t,r=Ee(),{protocol:i,hostname:n}=new URL(r);return e.startsWith("chrome-extension://")?""===(t=new URL(e)).hostname&&""===n?"chrome-extension:"===i&&e.replace("chrome-extension://","")===r.replace("chrome-extension://",""):"chrome-extension:"===i&&t.hostname===n:Ci.test(i)&&(Pi.test(e)?n===e:(r=e.replace(/\./g,"\\."),(t=new RegExp("^(.+\\."+r+"|"+r+")$","i")).test(n)))})(t))return}catch(e){}d(e,"unauthorized-domain")}}let Ni=new Se(3e4,6e4);function Li(){var t=O().___jsl;if(null!=t&&t.H)for(var r of Object.keys(t.H))if(t.H[r].r=t.H[r].r||[],t.H[r].L=t.H[r].L||[],t.H[r].r=[...t.H[r].L],t.CP)for(let e=0;e<t.CP.length;e++)t.CP[e]=null}function Di(n){return new Promise((e,t)=>{var r;function i(){Li(),gapi.load("gapi.iframes",{callback:()=>{e(gapi.iframes.getContext())},ontimeout:()=>{Li(),t(h(n,"network-request-failed"))},timeout:Ni.get()})}if(null!=(r=null==(r=O().gapi)?void 0:r.iframes)&&r.Iframe)e(gapi.iframes.getContext());else{if(null==(r=O().gapi)||!r.load)return r=ft("iframefcb"),O()[r]=()=>{gapi.load?i():t(h(n,"network-request-failed"))},vt(gt.gapiScript+"?onload="+r).catch(e=>t(e));i()}}).catch(e=>{throw Mi=null,e})}let Mi=null;let Ui=new Se(5e3,15e3),Fi="__/auth/iframe",Vi="emulator/auth/iframe",xi={style:{position:"absolute",top:"-100px",width:"1px",height:"1px"},"aria-hidden":"true",tabindex:"-1"},ji=new Map([["identitytoolkit.googleapis.com","p"],["staging-identitytoolkit.sandbox.googleapis.com","s"],["test-identitytoolkit.sandbox.googleapis.com","t"]]);async function Hi(a){e=a;var e,t,r,i=await(Mi=Mi||Di(e)),n=O().gapi;return g(n,a,"internal-error"),i.open({where:document.body,url:(g((i=(e=a).config).authDomain,e,"auth-domain-config-required"),t=i.emulator?Re(i,Vi):`https://${e.config.authDomain}/`+Fi,i={apiKey:i.apiKey,appName:e.name,v:qn.SDK_VERSION},(r=ji.get(e.config.apiHost))&&(i.eid=r),(r=e._getFrameworks()).length&&(i.fw=r.join(",")),t+"?"+te(i).slice(1)),messageHandlersFilter:n.iframes.CROSS_ORIGIN_IFRAMES_FILTER,attributes:xi,dontclear:!0},s=>new Promise(async(e,t)=>{await s.restyle({setHideOnLeave:!1});let r=h(a,"network-request-failed"),i=O().setTimeout(()=>{t(r)},Ui.get());function n(){O().clearTimeout(i),e(s)}s.ping(n).then(n,()=>{t(r)})}))}let Wi={location:"yes",resizable:"yes",statusbar:"yes",toolbar:"no"};class qi{constructor(e){this.window=e,this.associatedEvent=null}close(){if(this.window)try{this.window.close()}catch(e){}}}function Bi(e,t,r,i=500,n=600){var s=Math.max((window.screen.availHeight-n)/2,0).toString(),a=Math.max((window.screen.availWidth-i)/2,0).toString();let o="";var l,s=Object.assign(Object.assign({},Wi),{width:i.toString(),height:n.toString(),top:s,left:a}),a=c().toLowerCase(),s=(r&&(o=it(a)?"_blank":r),tt(a)&&(t=t||"http://localhost",s.scrollbars="yes"),Object.entries(s).reduce((e,[t,r])=>""+e+t+`=${r},`,""));if([i=c()]=[a],lt(i)&&null!=(a=window.navigator)&&a.standalone&&"_self"!==o)return n=t||"",r=o,(a=document.createElement("a")).href=n,a.target=r,(l=document.createEvent("MouseEvent")).initMouseEvent("click",!0,!0,window,1,0,0,0,0,!1,!1,!1,!1,1,null),a.dispatchEvent(l),new qi(null);a=window.open(t||"",o,s);g(a,e,"popup-blocked");try{a.focus()}catch(e){}return new qi(a)}let zi="__/auth/handler",Gi="emulator/auth/handler",Ki=encodeURIComponent("fac");async function Ji(e,t,r,i,n,s){g(e.config.authDomain,e,"auth-domain-config-required"),g(e.config.apiKey,e,"invalid-api-key");var a={apiKey:e.config.apiKey,appName:e.name,authType:r,redirectUrl:i,v:qn.SDK_VERSION,eventId:n};if(t instanceof k){t.setDefaultLanguage(e.languageCode),a.providerId=t.providerId||"",(e=>{for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t))return;return 1})(t.getCustomParameters())||(a.customParameters=JSON.stringify(t.getCustomParameters()));for(var[o,l]of Object.entries(s||{}))a[o]=l}t instanceof Ht&&0<(d=t.getScopes().filter(e=>""!==e)).length&&(a.scopes=d.join(",")),e.tenantId&&(a.tid=e.tenantId);var c,u=a;for(c of Object.keys(u))void 0===u[c]&&delete u[c];var d=await e._getAppCheckToken(),d=d?`#${Ki}=`+encodeURIComponent(d):"";return`${r=[e.config][0],r.emulator?Re(r,Gi):`https://${r.authDomain}/`+zi}?`+te(u).slice(1)+d}let Yi="webStorageSupport";class $i{constructor(){this.eventManagers={},this.iframes={},this.originValidationPromises={},this._redirectPersistence=Dr,this._completeRedirectFn=Ei,this._overrideRedirectResult=fi}async _openPopup(e,t,r,i){a(null==(n=this.eventManagers[e._key()])?void 0:n.manager,"_initialize() not called before _openPopup()");var n=await Ji(e,t,r,Ee(),i);return Bi(e,n,Ur())}async _openRedirect(e,t,r,i){await this._originValidation(e);var n=await Ji(e,t,r,Ee(),i);return O().location.href=n,new Promise(()=>{})}_initialize(e){let r=e._key();if(this.eventManagers[r]){let{manager:e,promise:t}=this.eventManagers[r];return e?Promise.resolve(e):(a(t,"If manager is not set, promise should be"),t)}let t=this.initAndGetManager(e);return this.eventManagers[r]={promise:t},t.catch(()=>{delete this.eventManagers[r]}),t}async initAndGetManager(t){var e=await Hi(t);let r=new ki(t);return e.register("authEvent",e=>(g(null==e?void 0:e.authEvent,t,"invalid-auth-event"),{status:r.onEvent(e.authEvent)?"ACK":"ERROR"}),gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER),this.eventManagers[t._key()]={manager:r},this.iframes[t._key()]=e,r}_isIframeWebStorageSupported(r,i){this.iframes[r._key()].send(Yi,{type:Yi},e=>{var t=null==(t=null==e?void 0:e[0])?void 0:t[Yi];void 0!==t&&i(!!t),d(r,"internal-error")},gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER)}_originValidation(e){var t=e._key();return this.originValidationPromises[t]||(this.originValidationPromises[t]=Oi(e)),this.originValidationPromises[t]}get _shouldInitProactively(){return ct()||rt()||lt()}}let Xi=$i;class Qi extends class{constructor(e){this.factorId=e}_process(e,t,r){switch(t.type){case"enroll":return this._finalizeEnroll(e,t.credential,r);case"signin":return this._finalizeSignIn(e,t.credential);default:return n("unexpected MultiFactorSessionType")}}}{constructor(e){super("phone"),this.credential=e}static _fromCredential(e){return new Qi(e)}_finalizeEnroll(e,t,r){return e=e,t={idToken:t,displayName:r,phoneVerificationInfo:this.credential._makeVerificationRequest()},m(e,"POST","/v2/accounts/mfaEnrollment:finalize",p(e,t))}_finalizeSignIn(e,t){return e=e,t={mfaPendingCredential:t,phoneVerificationInfo:this.credential._makeVerificationRequest()},m(e,"POST","/v2/accounts/mfaSignIn:finalize",p(e,t))}}class Zi{constructor(){}static assertion(e){return Qi._fromCredential(e)}}Zi.FACTOR_ID="phone";var en="@firebase/auth";class tn{constructor(e){this.auth=e,this.internalListeners=new Map}getUid(){var e;return this.assertAuthConfigured(),(null==(e=this.auth.currentUser)?void 0:e.uid)||null}async getToken(e){return this.assertAuthConfigured(),await this.auth._initializationPromise,this.auth.currentUser?{accessToken:await this.auth.currentUser.getIdToken(e)}:null}addAuthTokenListener(t){var e;this.assertAuthConfigured(),this.internalListeners.has(t)||(e=this.auth.onIdTokenChanged(e=>{t((null==e?void 0:e.stsTokenManager.accessToken)||null)}),this.internalListeners.set(t,e),this.updateProactiveRefresh())}removeAuthTokenListener(e){this.assertAuthConfigured();var t=this.internalListeners.get(e);t&&(this.internalListeners.delete(e),t(),this.updateProactiveRefresh())}assertAuthConfigured(){g(this.auth._initializationPromise,"dependent-sdk-initialized-before-auth")}updateProactiveRefresh(){0<this.internalListeners.size?this.auth._startProactiveRefresh():this.auth._stopProactiveRefresh()}}var rn,nn;function sn(){return window}e="authIdTokenMaxAge",null!=(rn=z())&&rn["_"+e],gt={loadJS(n){return new Promise((e,r)=>{var t,i=document.createElement("script");i.setAttribute("src",n),i.onload=e,i.onerror=e=>{var t=h("internal-error");t.customData=e,r(t)},i.type="text/javascript",i.charset="UTF-8",(null!=(t=null==(t=document.getElementsByTagName("head"))?void 0:t[0])?t:document).appendChild(i)})},gapiScript:"https://apis.google.com/js/api.js",recaptchaV2Script:"https://www.google.com/recaptcha/api.js",recaptchaEnterpriseScript:"https://www.google.com/recaptcha/enterprise.js?render="},nn="Browser",qn._registerComponent(new de("auth",(e,{options:t})=>{var r=e.getProvider("app").getImmediate(),i=e.getProvider("heartbeat"),n=e.getProvider("app-check-internal"),{apiKey:s,authDomain:a}=r.options,s=(g(s&&!s.includes(":"),"invalid-api-key",{appName:r.name}),{apiKey:s,authDomain:a,clientPlatform:nn,apiHost:"identitytoolkit.googleapis.com",tokenApiHost:"securetoken.googleapis.com",apiScheme:"https",sdkClientVersion:ut(nn)}),a=new pt(r,i,n,s);return e=a,r=(null==(t=t)?void 0:t.persistence)||[],r=(Array.isArray(r)?r:[r]).map(y),null!=t&&t.errorMap&&e._updateErrorMap(t.errorMap),e._initializeWithPersistence(r,null==t?void 0:t.popupRedirectResolver),a},"PUBLIC").setInstantiationMode("EXPLICIT").setInstanceCreatedCallback((e,t,r)=>{e.getProvider("auth-internal").initialize()})),qn._registerComponent(new de("auth-internal",e=>{var t=w(e.getProvider("auth").getImmediate());return e=t,new tn(e)},"PRIVATE").setInstantiationMode("EXPLICIT")),qn.registerVersion(en,"1.10.1",(e=>{switch(e){case"Node":return"node";case"ReactNative":return"rn";case"Worker":return"webworker";case"Cordova":return"cordova";case"WebExtension":return"web-extension";default:return}})(nn)),qn.registerVersion(en,"1.10.1","esm2017");async function an(e,t,r){var i=sn().BuildInfo,n=(a(t.sessionId,"AuthEvent did not contain a session ID"),n=(e=>{if(a(/[0-9a-zA-Z]+/.test(e),"Can only convert alpha-numeric strings"),"undefined"!=typeof TextEncoder)return(new TextEncoder).encode(e);var t=new ArrayBuffer(e.length),r=new Uint8Array(t);for(let i=0;i<e.length;i++)r[i]=e.charCodeAt(i);return r})(t.sessionId),n=await crypto.subtle.digest("SHA-256",n),await(n=Array.from(new Uint8Array(n))).map(e=>e.toString(16).padStart(2,"0")).join("")),s={};return lt()?s.ibi=i.packageName:st()?s.apn=i.packageName:d(e,"operation-not-supported-in-this-environment"),i.displayName&&(s.appDisplayName=i.displayName),s.sessionId=n,Ji(e,r,t.type,void 0,null!=(i=t.eventId)?i:void 0,s)}function on(i){let n=sn().cordova;return new Promise(r=>{n.plugins.browsertab.isAvailable(e=>{let t=null;e?n.plugins.browsertab.openUrl(i):t=n.InAppBrowser.open(i,(e=c(),/(iPad|iPhone|iPod).*OS 7_\d/i.test(e)||/(iPad|iPhone|iPod).*OS 8_\d/i.test(e)?"_blank":"_system"),"location=yes"),r(t)})})}let ln=20;class cn extends ki{constructor(){super(...arguments),this.passiveListeners=new Set,this.initPromise=new Promise(e=>{this.resolveInitialized=e})}addPassiveListener(e){this.passiveListeners.add(e)}removePassiveListener(e){this.passiveListeners.delete(e)}resetRedirect(){this.queuedRedirectEvent=null,this.hasHandledPotentialRedirect=!1}onEvent(t){return this.resolveInitialized(),this.passiveListeners.forEach(e=>e(t)),super.onEvent(t)}async initialized(){await this.initPromise}}function un(e,t,r=null){return{type:t,eventId:r,urlResponse:null,sessionId:(()=>{var e=[],t="1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";for(let i=0;i<ln;i++){var r=Math.floor(Math.random()*t.length);e.push(t.charAt(r))}return e.join("")})(),postBody:null,tenantId:e.tenantId,error:h(e,"no-auth-event")}}async function dn(e){var t=await pn()._get(mn(e));return t&&await pn()._remove(mn(e)),t}function hn(e,t){n=gn(t=t),i=n.link?decodeURIComponent(n.link):void 0,r=gn(i).link,n=n.deep_link_id?decodeURIComponent(n.deep_link_id):void 0;var r,i,n=gn(n).link||n||r||i||t;return n.includes("/__/auth/callback")?(i=(r=null==(i=null==(r=null==(i=(r=gn(n)).firebaseError?(e=>{try{return JSON.parse(e)}catch(e){return null}})(decodeURIComponent(r.firebaseError)):null)?void 0:i.code)?void 0:r.split("auth/"))?void 0:i[1])?h(r):null)?{type:e.type,eventId:e.eventId,tenantId:e.tenantId,error:i,urlResponse:null,sessionId:null,postBody:null}:{type:e.type,eventId:e.eventId,tenantId:e.tenantId,sessionId:e.sessionId,urlResponse:n,postBody:null}:null}function pn(){return y(Nr)}function mn(e){return I("authEvent",e.config.apiKey,e.name)}function gn(e){var t,r;return null!=e&&e.includes("?")?([t,...r]=e.split("?"),re(r.join("?"))):{}}class vn{constructor(){this._redirectPersistence=Dr,this._shouldInitProactively=!0,this.eventManagers=new Map,this.originValidationPromises={},this._completeRedirectFn=Ei,this._overrideRedirectResult=fi}async _initialize(e){var t=e._key();let r=this.eventManagers.get(t);return r||(r=new cn(e),this.eventManagers.set(t,r),this.attachCallbackListeners(e,r)),r}_openPopup(e){d(e,"operation-not-supported-in-this-environment")}async _openRedirect(e,t,r,i){n=e,a=sn(),g("function"==typeof(null==(s=null==a?void 0:a.universalLinks)?void 0:s.subscribe),n,"invalid-cordova-configuration",{missingPlugin:"cordova-universal-links-plugin-fix"}),g(void 0!==(null==(s=null==a?void 0:a.BuildInfo)?void 0:s.packageName),n,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-buildInfo"}),g("function"==typeof(null==(s=null==(s=null==(s=null==a?void 0:a.cordova)?void 0:s.plugins)?void 0:s.browsertab)?void 0:s.openUrl),n,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-browsertab"}),g("function"==typeof(null==(s=null==(s=null==(s=null==a?void 0:a.cordova)?void 0:s.plugins)?void 0:s.browsertab)?void 0:s.isAvailable),n,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-browsertab"}),g("function"==typeof(null==(a=null==(s=null==a?void 0:a.cordova)?void 0:s.InAppBrowser)?void 0:a.open),n,"invalid-cordova-configuration",{missingPlugin:"cordova-plugin-inappbrowser"});var n,s=await this._initialize(e),a=(await s.initialized(),s.resetRedirect(),mi.clear(),await this._originValidation(e),un(e,r,i)),a=(n=e,r=a,await pn()._set(mn(n),r),await an(e,a,t));return(async(a,o,l)=>{let c=sn().cordova,u=()=>{};try{await new Promise((t,e)=>{let r=null;function i(){t();var e=null==(e=c.plugins.browsertab)?void 0:e.close;"function"==typeof e&&e(),"function"==typeof(null==l?void 0:l.close)&&l.close()}function n(){r=r||window.setTimeout(()=>{e(h(a,"redirect-cancelled-by-user"))},2e3)}function s(){"visible"===(null==document?void 0:document.visibilityState)&&n()}o.addPassiveListener(i),document.addEventListener("resume",n,!1),st()&&document.addEventListener("visibilitychange",s,!1),u=()=>{o.removePassiveListener(i),document.removeEventListener("resume",n,!1),document.removeEventListener("visibilitychange",s,!1),r&&window.clearTimeout(r)}})}finally{u()}})(e,s,await on(a))}_isIframeWebStorageSupported(e,t){throw new Error("Method not implemented.")}_originValidation(e){var t=e._key();return this.originValidationPromises[t]||(this.originValidationPromises[t]=(async e=>{var t=sn().BuildInfo,r={};lt()?r.iosBundleId=t.packageName:st()?r.androidPackageName=t.packageName:d(e,"operation-not-supported-in-this-environment"),await Ai(e,r)})(e)),this.originValidationPromises[t]}attachCallbackListeners(i,n){var{universalLinks:e,handleOpenURL:t,BuildInfo:r}=sn();let s=setTimeout(async()=>{await dn(i),n.onEvent(_n())},500),a=async e=>{clearTimeout(s);var t=await dn(i);let r=null;t&&null!=e&&e.url&&(r=hn(t,e.url)),n.onEvent(r||_n())},o=(void 0!==e&&"function"==typeof e.subscribe&&e.subscribe(null,a),t),l=r.packageName.toLowerCase()+"://";sn().handleOpenURL=async e=>{if(e.toLowerCase().startsWith(l)&&a({url:e}),"function"==typeof o)try{o(e)}catch(e){console.error(e)}}}}let fn=vn;function _n(){return{type:"unknown",eventId:null,sessionId:null,urlResponse:null,postBody:null,tenantId:null,error:h("no-auth-event")}}var e;function yn(){var e;return(null==(e=null==self?void 0:self.location)?void 0:e.protocol)||null}function In(e=c()){return!("file:"!==yn()&&"ionic:"!==yn()&&"capacitor:"!==yn()||!e.toLowerCase().match(/iphone|ipad|ipod|android/))}function wn(e=c()){return Y()&&11===(null==document?void 0:document.documentMode)||([e=c()]=[e],/Edge\/\d+/.test(e))}function Tn(){try{var e=self.localStorage,t=Ur();if(e)return e.setItem(t,"1"),e.removeItem(t),!wn()||$()}catch(e){return En()&&$()}return!1}function En(){return"undefined"!=typeof global&&"WorkerGlobalScope"in global&&"importScripts"in global}function bn(){return("http:"===yn()||"https:"===yn()||K()||In())&&!(J()||G())&&Tn()&&!En()}function kn(){return In()&&"undefined"!=typeof document}let D={LOCAL:"local",NONE:"none",SESSION:"session"},Sn=g,Rn="persistence";async function An(e){await e._initializationPromise;var t=Pn(),r=I(Rn,e.config.apiKey,e.name);t&&t.setItem(r,e._getPersistenceType())}function Pn(){var e;try{return(null===(e="undefined"!=typeof window?window:null)?void 0:e.sessionStorage)||null}catch(e){return null}}let Cn=g;class M{constructor(){this.browserResolver=y(Xi),this.cordovaResolver=y(fn),this.underlyingResolver=null,this._redirectPersistence=Dr,this._completeRedirectFn=Ei,this._overrideRedirectResult=fi}async _initialize(e){return await this.selectUnderlyingResolver(),this.assertedUnderlyingResolver._initialize(e)}async _openPopup(e,t,r,i){return await this.selectUnderlyingResolver(),this.assertedUnderlyingResolver._openPopup(e,t,r,i)}async _openRedirect(e,t,r,i){return await this.selectUnderlyingResolver(),this.assertedUnderlyingResolver._openRedirect(e,t,r,i)}_isIframeWebStorageSupported(e,t){this.assertedUnderlyingResolver._isIframeWebStorageSupported(e,t)}_originValidation(e){return this.assertedUnderlyingResolver._originValidation(e)}get _shouldInitProactively(){return kn()||this.browserResolver._shouldInitProactively}get assertedUnderlyingResolver(){return Cn(this.underlyingResolver,"internal-error"),this.underlyingResolver}async selectUnderlyingResolver(){var e;this.underlyingResolver||(e=await(!!kn()&&new Promise(e=>{let t=setTimeout(()=>{e(!1)},1e3);document.addEventListener("deviceready",()=>{clearTimeout(t),e(!0)})})),this.underlyingResolver=e?this.cordovaResolver:this.browserResolver)}}function On(e){return e.unwrap()}function Nn(e,t){var r,i,n,s,a=null==(a=t.customData)?void 0:a._tokenResponse;"auth/multi-factor-auth-required"===(null==t?void 0:t.code)?t.resolver=new Mn(e,(r=t,n=o(e),g((s=r).customData.operationType,n,"argument-error"),g(null==(i=s.customData._serverResponse)?void 0:i.mfaPendingCredential,n,"argument-error"),kr._fromError(n,s))):a&&(n=Ln(i=t))&&(i.credential=n,i.tenantId=a.tenantId||void 0,i.email=a.email||void 0,i.phoneNumber=a.phoneNumber||void 0)}function Ln(e){var t=(e instanceof u?e.customData:e)._tokenResponse;if(!t)return null;if(!(e instanceof u)&&"temporaryProof"in t&&"phoneNumber"in t)return N.credentialFromResult(e);var r=t.providerId;if(!r||r===he.PASSWORD)return null;let i;switch(r){case he.GOOGLE:i=R;break;case he.FACEBOOK:i=S;break;case he.GITHUB:i=A;break;case he.TWITTER:i=P;break;default:var{oauthIdToken:n,oauthAccessToken:s,oauthTokenSecret:a,pendingToken:o,nonce:l}=t;return s||a||n||o?o?r.startsWith("saml.")?qt._create(r,o):b._fromParams({providerId:r,signInMethod:r,pendingToken:o,idToken:n,accessToken:s}):new Wt(r).credential({idToken:n,accessToken:s,rawNonce:l}):null}return e instanceof u?i.credentialFromError(e):i.credentialFromResult(e)}function U(t,e){return e.catch(e=>{throw e instanceof u&&Nn(t,e),e}).then(e=>{var t=e.operationType,r=e.user;return{operationType:t,credential:Ln(e),additionalUserInfo:Er(e),user:F.getOrCreate(r)}})}async function Dn(t,e){let r=await e;return{verificationId:r.verificationId,confirm:e=>U(t,r.confirm(e))}}class Mn{constructor(e,t){this.resolver=t,this.auth=e.wrapped()}get session(){return this.resolver.session}get hints(){return this.resolver.hints}resolveSignIn(e){return U(On(this.auth),this.resolver.resolveSignIn(e))}}class F{constructor(e){var t;this._delegate=e,this.multiFactor=(t=o(e),Ar.has(t)||Ar.set(t,Rr._fromUser(t)),Ar.get(t))}static getOrCreate(e){return F.USER_MAP.has(e)||F.USER_MAP.set(e,new F(e)),F.USER_MAP.get(e)}delete(){return this._delegate.delete()}reload(){return this._delegate.reload()}toJSON(){return this._delegate.toJSON()}getIdTokenResult(e){return this._delegate.getIdTokenResult(e)}getIdToken(e){return this._delegate.getIdToken(e)}linkAndRetrieveDataWithCredential(e){return this.linkWithCredential(e)}async linkWithCredential(e){return U(this.auth,rr(this._delegate,e))}async linkWithPhoneNumber(e,t){return Dn(this.auth,(async(e,t,r)=>{let i=o(e);await Qt(!1,i,"phone");var n=await ni(i.auth,t,o(r));return new ii(n,e=>rr(i,e))})(this._delegate,e,t))}async linkWithPopup(e){return U(this.auth,(async(e,t,r)=>{var i=o(e),n=(we(i.auth,t,k),ai(i.auth,r));return new L(i.auth,"linkViaPopup",t,n,i).executeNotNull()})(this._delegate,e,M))}async linkWithRedirect(e){return await An(w(this.auth)),Ti(this._delegate,e,M)}reauthenticateAndRetrieveDataWithCredential(e){return this.reauthenticateWithCredential(e)}async reauthenticateWithCredential(e){return U(this.auth,ir(this._delegate,e))}reauthenticateWithPhoneNumber(e,t){return Dn(this.auth,(async(e,t,r)=>{let i=o(e);var n;return qn._isFirebaseServerApp(i.auth.app)?Promise.reject(l(i.auth)):(n=await ni(i.auth,t,o(r)),new ii(n,e=>ir(i,e)))})(this._delegate,e,t))}reauthenticateWithPopup(e){return U(this.auth,(async(e,t,r)=>{var i=o(e);if(qn._isFirebaseServerApp(i.auth.app))return Promise.reject(h(i.auth,"operation-not-supported-in-this-environment"));we(i.auth,t,k);var n=ai(i.auth,r);return new L(i.auth,"reauthViaPopup",t,n,i).executeNotNull()})(this._delegate,e,M))}async reauthenticateWithRedirect(e){return await An(w(this.auth)),wi(this._delegate,e,M)}sendEmailVerification(e){return pr(this._delegate,e)}async unlink(e){return await $t(this._delegate,e),this}updateEmail(e){return t=this._delegate,e=e,r=o(t),qn._isFirebaseServerApp(r.auth.app)?Promise.reject(l(r.auth)):vr(r,e,null);var t,r}updatePassword(e){return vr(o(this._delegate),null,e)}updatePhoneNumber(e){return(async(e,t)=>{var r=o(e);if(qn._isFirebaseServerApp(r.auth.app))return Promise.reject(l(r.auth));await Xt(r,t)})(this._delegate,e)}updateProfile(e){return gr(this._delegate,e)}verifyBeforeUpdateEmail(e,t){return mr(this._delegate,e,t)}get emailVerified(){return this._delegate.emailVerified}get isAnonymous(){return this._delegate.isAnonymous}get metadata(){return this._delegate.metadata}get phoneNumber(){return this._delegate.phoneNumber}get providerData(){return this._delegate.providerData}get refreshToken(){return this._delegate.refreshToken}get tenantId(){return this._delegate.tenantId}get displayName(){return this._delegate.displayName}get email(){return this._delegate.email}get photoURL(){return this._delegate.photoURL}get providerId(){return this._delegate.providerId}get uid(){return this._delegate.uid}get auth(){return this._delegate.auth}}F.USER_MAP=new WeakMap;let Un=g;class Fn{constructor(e,t){var r,i;this.app=e,t.isInitialized()?this._delegate=t.getImmediate():(r=e.options.apiKey,Un(r,"invalid-api-key",{appName:e.name}),Un(r,"invalid-api-key",{appName:e.name}),i="undefined"!=typeof window?M:void 0,this._delegate=t.initialize({options:{persistence:((e,t)=>{var r=((e,t)=>{var r=Pn();if(!r)return[];var i=I(Rn,e,t);switch(r.getItem(i)){case D.NONE:return[Qe];case D.LOCAL:return[Jr,Dr];case D.SESSION:return[Dr];default:return[]}})(e,t);if("undefined"==typeof self||r.includes(Jr)||r.push(Jr),"undefined"!=typeof window)for(var i of[Nr,Dr])r.includes(i)||r.push(i);return r.includes(Qe)||r.push(Qe),r})(r,e.name),popupRedirectResolver:i}}),this._delegate._updateErrorMap(ge)),this.linkUnderlyingAuth()}get emulatorConfig(){return this._delegate.emulatorConfig}get currentUser(){return this._delegate.currentUser?F.getOrCreate(this._delegate.currentUser):null}get languageCode(){return this._delegate.languageCode}set languageCode(e){this._delegate.languageCode=e}get settings(){return this._delegate.settings}get tenantId(){return this._delegate.tenantId}set tenantId(e){this._delegate.tenantId=e}useDeviceLanguage(){this._delegate.useDeviceLanguage()}signOut(){return this._delegate.signOut()}useEmulator(e,t){kt(this._delegate,e,t)}applyActionCode(e){return ur(this._delegate,e)}checkActionCode(e){return dr(this._delegate,e)}confirmPasswordReset(e,t){return(async(t,e,r)=>{await Pt(o(t),{oobCode:e,newPassword:r}).catch(async e=>{throw"auth/password-does-not-meet-requirements"===e.code&&cr(t),e})})(this._delegate,e,t)}async createUserWithEmailAndPassword(e,t){return U(this._delegate,(async(t,e,r)=>{var i,n;return qn._isFirebaseServerApp(t.app)?Promise.reject(l(t)):(n=await T(i=w(t),{returnSecureToken:!0,email:e,password:r,clientType:"CLIENT_TYPE_WEB"},"signUpPassword",zt,"EMAIL_PASSWORD_PROVIDER").catch(e=>{throw"auth/password-does-not-meet-requirements"===e.code&&cr(t),e}),n=await C._fromIdTokenResponse(i,"signIn",n),await i._updateCurrentUser(n.user),n)})(this._delegate,e,t))}fetchProvidersForEmail(e){return this.fetchSignInMethodsForEmail(e)}fetchSignInMethodsForEmail(e){return hr(this._delegate,e)}isSignInWithEmailLink(e){return this._delegate,e=e,"EMAIL_SIGNIN"===(null==(t=xt.parseLink(e))?void 0:t.operation);var t}async getRedirectResult(){Un(bn(),this._delegate,"operation-not-supported-in-this-environment");e=this._delegate,t=M,await w(e)._initializationPromise;var e,t,r=await Ei(e,t,!1);return r?U(this._delegate,Promise.resolve(r)):{credential:null,user:null}}addFrameworkForLogging(e){w(this._delegate)._logFramework(e)}onAuthStateChanged(e,t,r){var{next:i,error:n,complete:s}=Vn(e,t,r);return this._delegate.onAuthStateChanged(i,n,s)}onIdTokenChanged(e,t,r){var{next:i,error:n,complete:s}=Vn(e,t,r);return this._delegate.onIdTokenChanged(i,n,s)}sendSignInLinkToEmail(e,t){return(async(e,t,r)=>{let i=w(e);var n={requestType:"EMAIL_SIGNIN",email:t,clientType:"CLIENT_TYPE_WEB"};e=n,g((t=r).handleCodeInApp,i,"argument-error"),t&&lr(i,e,t),await T(i,n,"getOobCode",Dt,"EMAIL_PASSWORD_PROVIDER")})(this._delegate,e,t)}sendPasswordResetEmail(e,t){return(async(e,t,r)=>{var i=w(e),n={requestType:"PASSWORD_RESET",email:t,clientType:"CLIENT_TYPE_WEB"};r&&lr(i,n,r),await T(i,n,"getOobCode",Lt,"EMAIL_PASSWORD_PROVIDER")})(this._delegate,e,t||void 0)}async setPersistence(e){var t,r;t=this._delegate,r=e,Sn(Object.values(D).includes(r),t,"invalid-persistence-type"),J()?Sn(r!==D.SESSION,t,"unsupported-persistence-type"):G()?Sn(r===D.NONE,t,"unsupported-persistence-type"):En()?Sn(r===D.NONE||r===D.LOCAL&&$(),t,"unsupported-persistence-type"):Sn(r===D.NONE||Tn(),t,"unsupported-persistence-type");let i;switch(e){case D.SESSION:i=Dr;break;case D.LOCAL:var n=await y(Jr)._isAvailable();i=n?Jr:Nr;break;case D.NONE:i=Qe;break;default:return d("argument-error",{appName:this._delegate.name})}return this._delegate.setPersistence(i)}signInAndRetrieveDataWithCredential(e){return this.signInWithCredential(e)}signInAnonymously(){return U(this._delegate,(async e=>{var t,r;return qn._isFirebaseServerApp(e.app)?Promise.reject(l(e)):(await(t=w(e))._initializationPromise,null!=(r=t.currentUser)&&r.isAnonymous?new C({user:t.currentUser,providerId:null,operationType:"signIn"}):(r=await zt(t,{returnSecureToken:!0}),r=await C._fromIdTokenResponse(t,"signIn",r,!0),await t._updateCurrentUser(r.user),r))})(this._delegate))}signInWithCredential(e){return U(this._delegate,tr(this._delegate,e))}signInWithCustomToken(e){return U(this._delegate,nr(this._delegate,e))}signInWithEmailAndPassword(e,t){return U(this._delegate,(r=this._delegate,e=e,t=t,qn._isFirebaseServerApp(r.app)?Promise.reject(l(r)):tr(o(r),jt.credential(e,t)).catch(async e=>{throw"auth/password-does-not-meet-requirements"===e.code&&cr(r),e})));var r}signInWithEmailLink(e,t){return U(this._delegate,(async(e,t,r)=>{var i,n;return qn._isFirebaseServerApp(e.app)?Promise.reject(l(e)):(i=o(e),g((n=jt.credentialWithLink(t,r||Ee()))._tenantId===(i.tenantId||null),i,"tenant-id-mismatch"),tr(i,n))})(this._delegate,e,t))}signInWithPhoneNumber(e,t){return Dn(this._delegate,(async(e,t,r)=>{if(qn._isFirebaseServerApp(e.app))return Promise.reject(l(e));let i=w(e);var n=await ni(i,t,o(r));return new ii(n,e=>tr(i,e))})(this._delegate,e,t))}async signInWithPopup(e){return Un(bn(),this._delegate,"operation-not-supported-in-this-environment"),U(this._delegate,(async(e,t,r)=>{var i,n;return qn._isFirebaseServerApp(e.app)?Promise.reject(h(e,"operation-not-supported-in-this-environment")):(i=w(e),we(e,t,k),n=ai(i,r),new L(i,"signInViaPopup",t,n).executeNotNull())})(this._delegate,e,M))}async signInWithRedirect(e){return Un(bn(),this._delegate,"operation-not-supported-in-this-environment"),await An(this._delegate),Ii(this._delegate,e,M)}updateCurrentUser(e){return this._delegate.updateCurrentUser(e)}verifyPasswordResetCode(e){return(async(e,t)=>{var r=(await dr(o(e),t)).data;return r.email})(this._delegate,e)}unwrap(){return this._delegate}_delete(){return this._delegate._delete()}linkUnderlyingAuth(){this._delegate.wrapped=()=>this}}function Vn(e,t,r){let i=e,n=("function"!=typeof e&&({next:i,error:t,complete:r}=e),i);return{next:e=>n(e&&F.getOrCreate(e)),error:t,complete:r}}Fn.Persistence=D;class xn{static credential(e,t){return N.credential(e,t)}constructor(){this.providerId="phone",this._delegate=new N(On(V.default.auth()))}verifyPhoneNumber(e,t){return this._delegate.verifyPhoneNumber(e,t)}unwrap(){return this._delegate}}xn.PHONE_SIGN_IN_METHOD=N.PHONE_SIGN_IN_METHOD,xn.PROVIDER_ID=N.PROVIDER_ID;let jn=g;class Hn{constructor(e,t,r=V.default.app()){var i;jn(null==(i=r.options)?void 0:i.apiKey,"invalid-api-key",{appName:r.name}),this._delegate=new ri(r.auth(),e,t),this.type=this._delegate.type}clear(){this._delegate.clear()}render(){return this._delegate.render()}verify(){return this._delegate.verify()}}(e=V.default).INTERNAL.registerComponent(new de("auth-compat",e=>{var t=e.getProvider("app-compat").getImmediate(),r=e.getProvider("auth");return new Fn(t,r)},"PUBLIC").setServiceProps({ActionCodeInfo:{Operation:{EMAIL_SIGNIN:pe.EMAIL_SIGNIN,PASSWORD_RESET:pe.PASSWORD_RESET,RECOVER_EMAIL:pe.RECOVER_EMAIL,REVERT_SECOND_FACTOR_ADDITION:pe.REVERT_SECOND_FACTOR_ADDITION,VERIFY_AND_CHANGE_EMAIL:pe.VERIFY_AND_CHANGE_EMAIL,VERIFY_EMAIL:pe.VERIFY_EMAIL}},EmailAuthProvider:jt,FacebookAuthProvider:S,GithubAuthProvider:A,GoogleAuthProvider:R,OAuthProvider:Wt,SAMLAuthProvider:Bt,PhoneAuthProvider:xn,PhoneMultiFactorGenerator:Zi,RecaptchaVerifier:Hn,TwitterAuthProvider:P,Auth:Fn,AuthCredential:At,Error:u}).setInstantiationMode("LAZY").setMultipleInstances(!1)),e.registerVersion("@firebase/auth-compat","0.5.21")}).apply(this,arguments)}catch(e){throw console.error(e),new Error("Cannot instantiate firebase-auth-compat.js - be sure to load firebase-app.js first.")}});
//# sourceMappingURL=firebase-auth-compat.js.map
