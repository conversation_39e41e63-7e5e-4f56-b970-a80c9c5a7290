---
name: Feature request
about: Suggest an idea for something new
title: ''
labels: ''
assignees: ''

---

**Description of new feature**

_What should the new feature do?  For visual features, include an image/mockup of the expected output._



**Motivation for new feature**

_Describe your use case for this new feature._



**For reviewers**

_Reviewers should ensure that the following tasks are carried out for incorporated issues:_

- [ ] Ensure that the reporter has adequately described their idea.  If not, elicit more information about the use case.  You should iteratively build a spec together.
- [ ] Ensure that the issue is a good fit for the core library.  Some things are best done in extensions (e.g. UI-related features that aren't style-related).  Some things are best done by app authors themselves -- instead of in Cytoscape libraries.
- [ ] The issue has been associated with a corresponding [milestone](https://github.com/cytoscape/cytoscape.js/milestones).
- [ ] The commits have been incorporated into the `unstable` branch via pull request.  The corresponding pull request is cross-referenced.

