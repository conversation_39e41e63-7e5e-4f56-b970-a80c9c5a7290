{"version": 3, "sources": ["../../../src/diagrams/gantt/parser/gantt.jison", "../../../src/diagrams/gantt/ganttDb.js", "../../../src/diagrams/gantt/ganttRenderer.js", "../../../src/diagrams/gantt/styles.js", "../../../src/diagrams/gantt/ganttDiagram.ts"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[6,8,10,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28,29,30,31,33,35,36,38,40],$V1=[1,26],$V2=[1,27],$V3=[1,28],$V4=[1,29],$V5=[1,30],$V6=[1,31],$V7=[1,32],$V8=[1,33],$V9=[1,34],$Va=[1,9],$Vb=[1,10],$Vc=[1,11],$Vd=[1,12],$Ve=[1,13],$Vf=[1,14],$Vg=[1,15],$Vh=[1,16],$Vi=[1,19],$Vj=[1,20],$Vk=[1,21],$Vl=[1,22],$Vm=[1,23],$Vn=[1,25],$Vo=[1,35];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"start\":3,\"gantt\":4,\"document\":5,\"EOF\":6,\"line\":7,\"SPACE\":8,\"statement\":9,\"NL\":10,\"weekday\":11,\"weekday_monday\":12,\"weekday_tuesday\":13,\"weekday_wednesday\":14,\"weekday_thursday\":15,\"weekday_friday\":16,\"weekday_saturday\":17,\"weekday_sunday\":18,\"weekend\":19,\"weekend_friday\":20,\"weekend_saturday\":21,\"dateFormat\":22,\"inclusiveEndDates\":23,\"topAxis\":24,\"axisFormat\":25,\"tickInterval\":26,\"excludes\":27,\"includes\":28,\"todayMarker\":29,\"title\":30,\"acc_title\":31,\"acc_title_value\":32,\"acc_descr\":33,\"acc_descr_value\":34,\"acc_descr_multiline_value\":35,\"section\":36,\"clickStatement\":37,\"taskTxt\":38,\"taskData\":39,\"click\":40,\"callbackname\":41,\"callbackargs\":42,\"href\":43,\"clickStatementDebug\":44,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",4:\"gantt\",6:\"EOF\",8:\"SPACE\",10:\"NL\",12:\"weekday_monday\",13:\"weekday_tuesday\",14:\"weekday_wednesday\",15:\"weekday_thursday\",16:\"weekday_friday\",17:\"weekday_saturday\",18:\"weekday_sunday\",20:\"weekend_friday\",21:\"weekend_saturday\",22:\"dateFormat\",23:\"inclusiveEndDates\",24:\"topAxis\",25:\"axisFormat\",26:\"tickInterval\",27:\"excludes\",28:\"includes\",29:\"todayMarker\",30:\"title\",31:\"acc_title\",32:\"acc_title_value\",33:\"acc_descr\",34:\"acc_descr_value\",35:\"acc_descr_multiline_value\",36:\"section\",38:\"taskTxt\",39:\"taskData\",40:\"click\",41:\"callbackname\",42:\"callbackargs\",43:\"href\"},\nproductions_: [0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[11,1],[11,1],[11,1],[11,1],[11,1],[11,1],[11,1],[19,1],[19,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,2],[9,2],[9,1],[9,1],[9,1],[9,2],[37,2],[37,3],[37,3],[37,4],[37,3],[37,4],[37,2],[44,2],[44,3],[44,3],[44,4],[44,3],[44,4],[44,2]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 1:\n return $$[$0-1]; \nbreak;\ncase 2:\n this.$ = [] \nbreak;\ncase 3:\n$$[$0-1].push($$[$0]);this.$ = $$[$0-1]\nbreak;\ncase 4: case 5:\n this.$ = $$[$0] \nbreak;\ncase 6: case 7:\n this.$=[];\nbreak;\ncase 8:\n yy.setWeekday(\"monday\");\nbreak;\ncase 9:\n yy.setWeekday(\"tuesday\");\nbreak;\ncase 10:\n yy.setWeekday(\"wednesday\");\nbreak;\ncase 11:\n yy.setWeekday(\"thursday\");\nbreak;\ncase 12:\n yy.setWeekday(\"friday\");\nbreak;\ncase 13:\n yy.setWeekday(\"saturday\");\nbreak;\ncase 14:\n yy.setWeekday(\"sunday\");\nbreak;\ncase 15:\n yy.setWeekend(\"friday\");\nbreak;\ncase 16:\n yy.setWeekend(\"saturday\");\nbreak;\ncase 17:\nyy.setDateFormat($$[$0].substr(11));this.$=$$[$0].substr(11);\nbreak;\ncase 18:\nyy.enableInclusiveEndDates();this.$=$$[$0].substr(18);\nbreak;\ncase 19:\nyy.TopAxis();this.$=$$[$0].substr(8);\nbreak;\ncase 20:\nyy.setAxisFormat($$[$0].substr(11));this.$=$$[$0].substr(11);\nbreak;\ncase 21:\nyy.setTickInterval($$[$0].substr(13));this.$=$$[$0].substr(13);\nbreak;\ncase 22:\nyy.setExcludes($$[$0].substr(9));this.$=$$[$0].substr(9);\nbreak;\ncase 23:\nyy.setIncludes($$[$0].substr(9));this.$=$$[$0].substr(9);\nbreak;\ncase 24:\nyy.setTodayMarker($$[$0].substr(12));this.$=$$[$0].substr(12);\nbreak;\ncase 27:\nyy.setDiagramTitle($$[$0].substr(6));this.$=$$[$0].substr(6);\nbreak;\ncase 28:\n this.$=$$[$0].trim();yy.setAccTitle(this.$); \nbreak;\ncase 29: case 30:\n this.$=$$[$0].trim();yy.setAccDescription(this.$); \nbreak;\ncase 31:\n yy.addSection($$[$0].substr(8));this.$=$$[$0].substr(8); \nbreak;\ncase 33:\nyy.addTask($$[$0-1],$$[$0]);this.$='task';\nbreak;\ncase 34:\nthis.$ = $$[$0-1];yy.setClickEvent($$[$0-1], $$[$0], null);\nbreak;\ncase 35:\nthis.$ = $$[$0-2];yy.setClickEvent($$[$0-2], $$[$0-1], $$[$0]);\nbreak;\ncase 36:\nthis.$ = $$[$0-2];yy.setClickEvent($$[$0-2], $$[$0-1], null);yy.setLink($$[$0-2],$$[$0]);\nbreak;\ncase 37:\nthis.$ = $$[$0-3];yy.setClickEvent($$[$0-3], $$[$0-2], $$[$0-1]);yy.setLink($$[$0-3],$$[$0]);\nbreak;\ncase 38:\nthis.$ = $$[$0-2];yy.setClickEvent($$[$0-2], $$[$0], null);yy.setLink($$[$0-2],$$[$0-1]);\nbreak;\ncase 39:\nthis.$ = $$[$0-3];yy.setClickEvent($$[$0-3], $$[$0-1], $$[$0]);yy.setLink($$[$0-3],$$[$0-2]);\nbreak;\ncase 40:\nthis.$ = $$[$0-1];yy.setLink($$[$0-1], $$[$0]);\nbreak;\ncase 41: case 47:\nthis.$=$$[$0-1] + ' ' + $$[$0];\nbreak;\ncase 42: case 43: case 45:\nthis.$=$$[$0-2] + ' ' + $$[$0-1] + ' ' + $$[$0];\nbreak;\ncase 44: case 46:\nthis.$=$$[$0-3] + ' ' + $$[$0-2] + ' ' + $$[$0-1] + ' ' + $$[$0];\nbreak;\n}\n},\ntable: [{3:1,4:[1,2]},{1:[3]},o($V0,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:17,12:$V1,13:$V2,14:$V3,15:$V4,16:$V5,17:$V6,18:$V7,19:18,20:$V8,21:$V9,22:$Va,23:$Vb,24:$Vc,25:$Vd,26:$Ve,27:$Vf,28:$Vg,29:$Vh,30:$Vi,31:$Vj,33:$Vk,35:$Vl,36:$Vm,37:24,38:$Vn,40:$Vo},o($V0,[2,7],{1:[2,1]}),o($V0,[2,3]),{9:36,11:17,12:$V1,13:$V2,14:$V3,15:$V4,16:$V5,17:$V6,18:$V7,19:18,20:$V8,21:$V9,22:$Va,23:$Vb,24:$Vc,25:$Vd,26:$Ve,27:$Vf,28:$Vg,29:$Vh,30:$Vi,31:$Vj,33:$Vk,35:$Vl,36:$Vm,37:24,38:$Vn,40:$Vo},o($V0,[2,5]),o($V0,[2,6]),o($V0,[2,17]),o($V0,[2,18]),o($V0,[2,19]),o($V0,[2,20]),o($V0,[2,21]),o($V0,[2,22]),o($V0,[2,23]),o($V0,[2,24]),o($V0,[2,25]),o($V0,[2,26]),o($V0,[2,27]),{32:[1,37]},{34:[1,38]},o($V0,[2,30]),o($V0,[2,31]),o($V0,[2,32]),{39:[1,39]},o($V0,[2,8]),o($V0,[2,9]),o($V0,[2,10]),o($V0,[2,11]),o($V0,[2,12]),o($V0,[2,13]),o($V0,[2,14]),o($V0,[2,15]),o($V0,[2,16]),{41:[1,40],43:[1,41]},o($V0,[2,4]),o($V0,[2,28]),o($V0,[2,29]),o($V0,[2,33]),o($V0,[2,34],{42:[1,42],43:[1,43]}),o($V0,[2,40],{41:[1,44]}),o($V0,[2,35],{43:[1,45]}),o($V0,[2,36]),o($V0,[2,38],{42:[1,46]}),o($V0,[2,37]),o($V0,[2,39])],\ndefaultActions: {},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {\"case-insensitive\":true},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0: this.begin('open_directive'); return 'open_directive'; \nbreak;\ncase 1: this.begin(\"acc_title\");return 31; \nbreak;\ncase 2: this.popState(); return \"acc_title_value\"; \nbreak;\ncase 3: this.begin(\"acc_descr\");return 33; \nbreak;\ncase 4: this.popState(); return \"acc_descr_value\"; \nbreak;\ncase 5: this.begin(\"acc_descr_multiline\");\nbreak;\ncase 6: this.popState(); \nbreak;\ncase 7:return \"acc_descr_multiline_value\";\nbreak;\ncase 8:/* skip comments */\nbreak;\ncase 9:/* skip comments */\nbreak;\ncase 10:/* do nothing */\nbreak;\ncase 11:return 10;\nbreak;\ncase 12:/* skip whitespace */\nbreak;\ncase 13:/* skip comments */\nbreak;\ncase 14:this.begin(\"href\");\nbreak;\ncase 15:this.popState();\nbreak;\ncase 16:return 43;\nbreak;\ncase 17:this.begin(\"callbackname\");\nbreak;\ncase 18:this.popState();\nbreak;\ncase 19:this.popState(); this.begin(\"callbackargs\");\nbreak;\ncase 20:return 41;\nbreak;\ncase 21:this.popState();\nbreak;\ncase 22:return 42;\nbreak;\ncase 23:this.begin(\"click\");\nbreak;\ncase 24:this.popState();\nbreak;\ncase 25:return 40;\nbreak;\ncase 26:return 4;\nbreak;\ncase 27:return 22;\nbreak;\ncase 28:return 23;\nbreak;\ncase 29:return 24;\nbreak;\ncase 30:return 25;\nbreak;\ncase 31:return 26;\nbreak;\ncase 32:return 28;\nbreak;\ncase 33:return 27;\nbreak;\ncase 34:return 29;\nbreak;\ncase 35:return 12\nbreak;\ncase 36:return 13\nbreak;\ncase 37:return 14\nbreak;\ncase 38:return 15\nbreak;\ncase 39:return 16\nbreak;\ncase 40:return 17\nbreak;\ncase 41:return 18\nbreak;\ncase 42:return 20\nbreak;\ncase 43:return 21\nbreak;\ncase 44:return 'date';\nbreak;\ncase 45:return 30;\nbreak;\ncase 46:return 'accDescription'\nbreak;\ncase 47:return 36;\nbreak;\ncase 48:return 38;\nbreak;\ncase 49:return 39;\nbreak;\ncase 50:return ':';\nbreak;\ncase 51:return 6;\nbreak;\ncase 52:return 'INVALID';\nbreak;\n}\n},\nrules: [/^(?:%%\\{)/i,/^(?:accTitle\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*\\{\\s*)/i,/^(?:[\\}])/i,/^(?:[^\\}]*)/i,/^(?:%%(?!\\{)*[^\\n]*)/i,/^(?:[^\\}]%%*[^\\n]*)/i,/^(?:%%*[^\\n]*[\\n]*)/i,/^(?:[\\n]+)/i,/^(?:\\s+)/i,/^(?:%[^\\n]*)/i,/^(?:href[\\s]+[\"])/i,/^(?:[\"])/i,/^(?:[^\"]*)/i,/^(?:call[\\s]+)/i,/^(?:\\([\\s]*\\))/i,/^(?:\\()/i,/^(?:[^(]*)/i,/^(?:\\))/i,/^(?:[^)]*)/i,/^(?:click[\\s]+)/i,/^(?:[\\s\\n])/i,/^(?:[^\\s\\n]*)/i,/^(?:gantt\\b)/i,/^(?:dateFormat\\s[^#\\n;]+)/i,/^(?:inclusiveEndDates\\b)/i,/^(?:topAxis\\b)/i,/^(?:axisFormat\\s[^#\\n;]+)/i,/^(?:tickInterval\\s[^#\\n;]+)/i,/^(?:includes\\s[^#\\n;]+)/i,/^(?:excludes\\s[^#\\n;]+)/i,/^(?:todayMarker\\s[^\\n;]+)/i,/^(?:weekday\\s+monday\\b)/i,/^(?:weekday\\s+tuesday\\b)/i,/^(?:weekday\\s+wednesday\\b)/i,/^(?:weekday\\s+thursday\\b)/i,/^(?:weekday\\s+friday\\b)/i,/^(?:weekday\\s+saturday\\b)/i,/^(?:weekday\\s+sunday\\b)/i,/^(?:weekend\\s+friday\\b)/i,/^(?:weekend\\s+saturday\\b)/i,/^(?:\\d\\d\\d\\d-\\d\\d-\\d\\d\\b)/i,/^(?:title\\s[^\\n]+)/i,/^(?:accDescription\\s[^#\\n;]+)/i,/^(?:section\\s[^\\n]+)/i,/^(?:[^:\\n]+)/i,/^(?::[^#\\n;]+)/i,/^(?::)/i,/^(?:$)/i,/^(?:.)/i],\nconditions: {\"acc_descr_multiline\":{\"rules\":[6,7],\"inclusive\":false},\"acc_descr\":{\"rules\":[4],\"inclusive\":false},\"acc_title\":{\"rules\":[2],\"inclusive\":false},\"callbackargs\":{\"rules\":[21,22],\"inclusive\":false},\"callbackname\":{\"rules\":[18,19,20],\"inclusive\":false},\"href\":{\"rules\":[15,16],\"inclusive\":false},\"click\":{\"rules\":[24,25],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,3,5,8,9,10,11,12,13,14,17,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "import { sanitizeUrl } from '@braintree/sanitize-url';\nimport dayjs from 'dayjs';\nimport dayjsIsoWeek from 'dayjs/plugin/isoWeek.js';\nimport dayjsCustomParseFormat from 'dayjs/plugin/customParseFormat.js';\nimport dayjsAdvancedFormat from 'dayjs/plugin/advancedFormat.js';\nimport { log } from '../../logger.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport utils from '../../utils.js';\n\nimport {\n  setAccTitle,\n  getAccTitle,\n  getAccDescription,\n  setAccDescription,\n  clear as commonClear,\n  setDiagramTitle,\n  getDiagramTitle,\n} from '../common/commonDb.js';\n\ndayjs.extend(dayjsIsoWeek);\ndayjs.extend(dayjsCustomParseFormat);\ndayjs.extend(dayjsAdvancedFormat);\n\nconst WEEKEND_START_DAY = { friday: 5, saturday: 6 };\nlet dateFormat = '';\nlet axisFormat = '';\nlet tickInterval = undefined;\nlet todayMarker = '';\nlet includes = [];\nlet excludes = [];\nlet links = new Map();\nlet sections = [];\nlet tasks = [];\nlet currentSection = '';\nlet displayMode = '';\nconst tags = ['active', 'done', 'crit', 'milestone'];\nlet funs = [];\nlet inclusiveEndDates = false;\nlet topAxis = false;\nlet weekday = 'sunday';\nlet weekend = 'saturday';\n\n// The serial order of the task in the script\nlet lastOrder = 0;\n\nexport const clear = function () {\n  sections = [];\n  tasks = [];\n  currentSection = '';\n  funs = [];\n  taskCnt = 0;\n  lastTask = undefined;\n  lastTaskID = undefined;\n  rawTasks = [];\n  dateFormat = '';\n  axisFormat = '';\n  displayMode = '';\n  tickInterval = undefined;\n  todayMarker = '';\n  includes = [];\n  excludes = [];\n  inclusiveEndDates = false;\n  topAxis = false;\n  lastOrder = 0;\n  links = new Map();\n  commonClear();\n  weekday = 'sunday';\n  weekend = 'saturday';\n};\n\nexport const setAxisFormat = function (txt) {\n  axisFormat = txt;\n};\n\nexport const getAxisFormat = function () {\n  return axisFormat;\n};\n\nexport const setTickInterval = function (txt) {\n  tickInterval = txt;\n};\n\nexport const getTickInterval = function () {\n  return tickInterval;\n};\n\nexport const setTodayMarker = function (txt) {\n  todayMarker = txt;\n};\n\nexport const getTodayMarker = function () {\n  return todayMarker;\n};\n\nexport const setDateFormat = function (txt) {\n  dateFormat = txt;\n};\n\nexport const enableInclusiveEndDates = function () {\n  inclusiveEndDates = true;\n};\n\nexport const endDatesAreInclusive = function () {\n  return inclusiveEndDates;\n};\n\nexport const enableTopAxis = function () {\n  topAxis = true;\n};\n\nexport const topAxisEnabled = function () {\n  return topAxis;\n};\n\nexport const setDisplayMode = function (txt) {\n  displayMode = txt;\n};\n\nexport const getDisplayMode = function () {\n  return displayMode;\n};\n\nexport const getDateFormat = function () {\n  return dateFormat;\n};\n\nexport const setIncludes = function (txt) {\n  includes = txt.toLowerCase().split(/[\\s,]+/);\n};\n\nexport const getIncludes = function () {\n  return includes;\n};\nexport const setExcludes = function (txt) {\n  excludes = txt.toLowerCase().split(/[\\s,]+/);\n};\n\nexport const getExcludes = function () {\n  return excludes;\n};\n\nexport const getLinks = function () {\n  return links;\n};\n\nexport const addSection = function (txt) {\n  currentSection = txt;\n  sections.push(txt);\n};\n\nexport const getSections = function () {\n  return sections;\n};\n\nexport const getTasks = function () {\n  let allItemsProcessed = compileTasks();\n  const maxDepth = 10;\n  let iterationCount = 0;\n  while (!allItemsProcessed && iterationCount < maxDepth) {\n    allItemsProcessed = compileTasks();\n    iterationCount++;\n  }\n\n  tasks = rawTasks;\n\n  return tasks;\n};\n\nexport const isInvalidDate = function (date, dateFormat, excludes, includes) {\n  if (includes.includes(date.format(dateFormat.trim()))) {\n    return false;\n  }\n  if (\n    excludes.includes('weekends') &&\n    (date.isoWeekday() === WEEKEND_START_DAY[weekend] ||\n      date.isoWeekday() === WEEKEND_START_DAY[weekend] + 1)\n  ) {\n    return true;\n  }\n  if (excludes.includes(date.format('dddd').toLowerCase())) {\n    return true;\n  }\n  return excludes.includes(date.format(dateFormat.trim()));\n};\n\nexport const setWeekday = function (txt) {\n  weekday = txt;\n};\n\nexport const getWeekday = function () {\n  return weekday;\n};\n\nexport const setWeekend = function (startDay) {\n  weekend = startDay;\n};\n\n/**\n * TODO: fully document what this function does and what types it accepts\n *\n * @param {object} task - The task to check.\n * @param {string | Date} task.startTime - Might be a `Date` or a `string`.\n * TODO: is this always a Date?\n * @param {string | Date} task.endTime - Might be a `Date` or a `string`.\n * TODO: is this always a Date?\n * @param {string} dateFormat - Dayjs date format string.\n * @param {*} excludes\n * @param {*} includes\n */\nconst checkTaskDates = function (task, dateFormat, excludes, includes) {\n  if (!excludes.length || task.manualEndTime) {\n    return;\n  }\n  let startTime;\n  if (task.startTime instanceof Date) {\n    startTime = dayjs(task.startTime);\n  } else {\n    startTime = dayjs(task.startTime, dateFormat, true);\n  }\n  startTime = startTime.add(1, 'd');\n\n  let originalEndTime;\n  if (task.endTime instanceof Date) {\n    originalEndTime = dayjs(task.endTime);\n  } else {\n    originalEndTime = dayjs(task.endTime, dateFormat, true);\n  }\n  const [fixedEndTime, renderEndTime] = fixTaskDates(\n    startTime,\n    originalEndTime,\n    dateFormat,\n    excludes,\n    includes\n  );\n  task.endTime = fixedEndTime.toDate();\n  task.renderEndTime = renderEndTime;\n};\n\n/**\n * TODO: what does this function do?\n *\n * @param {dayjs.Dayjs} startTime - The start time.\n * @param {dayjs.Dayjs} endTime - The original end time (will return a different end time if it's invalid).\n * @param {string} dateFormat - Dayjs date format string.\n * @param {*} excludes\n * @param {*} includes\n * @returns {[endTime: dayjs.Dayjs, renderEndTime: Date | null]} The new `endTime`, and the end time to render.\n * `renderEndTime` may be `null` if `startTime` is newer than `endTime`.\n */\nconst fixTaskDates = function (startTime, endTime, dateFormat, excludes, includes) {\n  let invalid = false;\n  let renderEndTime = null;\n  while (startTime <= endTime) {\n    if (!invalid) {\n      renderEndTime = endTime.toDate();\n    }\n    invalid = isInvalidDate(startTime, dateFormat, excludes, includes);\n    if (invalid) {\n      endTime = endTime.add(1, 'd');\n    }\n    startTime = startTime.add(1, 'd');\n  }\n  return [endTime, renderEndTime];\n};\n\nconst getStartDate = function (prevTime, dateFormat, str) {\n  str = str.trim();\n\n  // Test for after\n  const afterRePattern = /^after\\s+(?<ids>[\\d\\w- ]+)/;\n  const afterStatement = afterRePattern.exec(str);\n\n  if (afterStatement !== null) {\n    // check all after ids and take the latest\n    let latestTask = null;\n    for (const id of afterStatement.groups.ids.split(' ')) {\n      let task = findTaskById(id);\n      if (task !== undefined && (!latestTask || task.endTime > latestTask.endTime)) {\n        latestTask = task;\n      }\n    }\n\n    if (latestTask) {\n      return latestTask.endTime;\n    }\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    return today;\n  }\n\n  // Check for actual date set\n  let mDate = dayjs(str, dateFormat.trim(), true);\n  if (mDate.isValid()) {\n    return mDate.toDate();\n  } else {\n    log.debug('Invalid date:' + str);\n    log.debug('With date format:' + dateFormat.trim());\n    const d = new Date(str);\n    if (\n      d === undefined ||\n      isNaN(d.getTime()) ||\n      // WebKit browsers can mis-parse invalid dates to be ridiculously\n      // huge numbers, e.g. new Date('202304') gets parsed as January 1, 202304.\n      // This can cause virtually infinite loops while rendering, so for the\n      // purposes of Gantt charts we'll just treat any date beyond 10,000 AD/BC as\n      // invalid.\n      d.getFullYear() < -10000 ||\n      d.getFullYear() > 10000\n    ) {\n      throw new Error('Invalid date:' + str);\n    }\n    return d;\n  }\n};\n\n/**\n * Parse a string into the args for `dayjs.add()`.\n *\n * The string have to be compound by a value and a shorthand duration unit. For example `5d`\n * represents 5 days.\n *\n * Please be aware that 1 day may be 23 or 25 hours, if the user lives in an area\n * that has daylight savings time (or even 23.5/24.5 hours in Lord Howe Island!)\n *\n * Shorthand unit supported are:\n *\n * - `y` for years\n * - `M` for months\n * - `w` for weeks\n * - `d` for days\n * - `h` for hours\n * - `s` for seconds\n * - `ms` for milliseconds\n *\n * @param {string} str - A string representing the duration.\n * @returns {[value: number, unit: dayjs.ManipulateType]} Arguments to pass to `dayjs.add()`\n */\nconst parseDuration = function (str) {\n  // cspell:disable-next-line\n  const statement = /^(\\d+(?:\\.\\d+)?)([Mdhmswy]|ms)$/.exec(str.trim());\n  if (statement !== null) {\n    return [Number.parseFloat(statement[1]), statement[2]];\n  }\n  // NaN means an invalid duration\n  return [NaN, 'ms'];\n};\n\nconst getEndDate = function (prevTime, dateFormat, str, inclusive = false) {\n  str = str.trim();\n\n  // test for until\n  const untilRePattern = /^until\\s+(?<ids>[\\d\\w- ]+)/;\n  const untilStatement = untilRePattern.exec(str);\n\n  if (untilStatement !== null) {\n    // check all until ids and take the earliest\n    let earliestTask = null;\n    for (const id of untilStatement.groups.ids.split(' ')) {\n      let task = findTaskById(id);\n      if (task !== undefined && (!earliestTask || task.startTime < earliestTask.startTime)) {\n        earliestTask = task;\n      }\n    }\n\n    if (earliestTask) {\n      return earliestTask.startTime;\n    }\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    return today;\n  }\n\n  // check for actual date\n  let parsedDate = dayjs(str, dateFormat.trim(), true);\n  if (parsedDate.isValid()) {\n    if (inclusive) {\n      parsedDate = parsedDate.add(1, 'd');\n    }\n    return parsedDate.toDate();\n  }\n\n  let endTime = dayjs(prevTime);\n  const [durationValue, durationUnit] = parseDuration(str);\n  if (!Number.isNaN(durationValue)) {\n    const newEndTime = endTime.add(durationValue, durationUnit);\n    if (newEndTime.isValid()) {\n      endTime = newEndTime;\n    }\n  }\n  return endTime.toDate();\n};\n\nlet taskCnt = 0;\nconst parseId = function (idStr) {\n  if (idStr === undefined) {\n    taskCnt = taskCnt + 1;\n    return 'task' + taskCnt;\n  }\n  return idStr;\n};\n// id, startDate, endDate\n// id, startDate, length\n// id, after x, endDate\n// id, after x, length\n// startDate, endDate\n// startDate, length\n// after x, endDate\n// after x, length\n// endDate\n// length\n\nconst compileData = function (prevTask, dataStr) {\n  let ds;\n\n  if (dataStr.substr(0, 1) === ':') {\n    ds = dataStr.substr(1, dataStr.length);\n  } else {\n    ds = dataStr;\n  }\n\n  const data = ds.split(',');\n\n  const task = {};\n\n  // Get tags like active, done, crit and milestone\n  getTaskTags(data, task, tags);\n\n  for (let i = 0; i < data.length; i++) {\n    data[i] = data[i].trim();\n  }\n\n  let endTimeData = '';\n  switch (data.length) {\n    case 1:\n      task.id = parseId();\n      task.startTime = prevTask.endTime;\n      endTimeData = data[0];\n      break;\n    case 2:\n      task.id = parseId();\n      task.startTime = getStartDate(undefined, dateFormat, data[0]);\n      endTimeData = data[1];\n      break;\n    case 3:\n      task.id = parseId(data[0]);\n      task.startTime = getStartDate(undefined, dateFormat, data[1]);\n      endTimeData = data[2];\n      break;\n    default:\n  }\n\n  if (endTimeData) {\n    task.endTime = getEndDate(task.startTime, dateFormat, endTimeData, inclusiveEndDates);\n    task.manualEndTime = dayjs(endTimeData, 'YYYY-MM-DD', true).isValid();\n    checkTaskDates(task, dateFormat, excludes, includes);\n  }\n\n  return task;\n};\n\nconst parseData = function (prevTaskId, dataStr) {\n  let ds;\n  if (dataStr.substr(0, 1) === ':') {\n    ds = dataStr.substr(1, dataStr.length);\n  } else {\n    ds = dataStr;\n  }\n\n  const data = ds.split(',');\n\n  const task = {};\n\n  // Get tags like active, done, crit and milestone\n  getTaskTags(data, task, tags);\n\n  for (let i = 0; i < data.length; i++) {\n    data[i] = data[i].trim();\n  }\n\n  switch (data.length) {\n    case 1:\n      task.id = parseId();\n      task.startTime = {\n        type: 'prevTaskEnd',\n        id: prevTaskId,\n      };\n      task.endTime = {\n        data: data[0],\n      };\n      break;\n    case 2:\n      task.id = parseId();\n      task.startTime = {\n        type: 'getStartDate',\n        startData: data[0],\n      };\n      task.endTime = {\n        data: data[1],\n      };\n      break;\n    case 3:\n      task.id = parseId(data[0]);\n      task.startTime = {\n        type: 'getStartDate',\n        startData: data[1],\n      };\n      task.endTime = {\n        data: data[2],\n      };\n      break;\n    default:\n  }\n\n  return task;\n};\n\nlet lastTask;\nlet lastTaskID;\nlet rawTasks = [];\nconst taskDb = {};\nexport const addTask = function (descr, data) {\n  const rawTask = {\n    section: currentSection,\n    type: currentSection,\n    processed: false,\n    manualEndTime: false,\n    renderEndTime: null,\n    raw: { data: data },\n    task: descr,\n    classes: [],\n  };\n  const taskInfo = parseData(lastTaskID, data);\n  rawTask.raw.startTime = taskInfo.startTime;\n  rawTask.raw.endTime = taskInfo.endTime;\n  rawTask.id = taskInfo.id;\n  rawTask.prevTaskId = lastTaskID;\n  rawTask.active = taskInfo.active;\n  rawTask.done = taskInfo.done;\n  rawTask.crit = taskInfo.crit;\n  rawTask.milestone = taskInfo.milestone;\n  rawTask.order = lastOrder;\n\n  lastOrder++;\n\n  const pos = rawTasks.push(rawTask);\n\n  lastTaskID = rawTask.id;\n  // Store cross ref\n  taskDb[rawTask.id] = pos - 1;\n};\n\nexport const findTaskById = function (id) {\n  const pos = taskDb[id];\n  return rawTasks[pos];\n};\n\nexport const addTaskOrg = function (descr, data) {\n  const newTask = {\n    section: currentSection,\n    type: currentSection,\n    description: descr,\n    task: descr,\n    classes: [],\n  };\n  const taskInfo = compileData(lastTask, data);\n  newTask.startTime = taskInfo.startTime;\n  newTask.endTime = taskInfo.endTime;\n  newTask.id = taskInfo.id;\n  newTask.active = taskInfo.active;\n  newTask.done = taskInfo.done;\n  newTask.crit = taskInfo.crit;\n  newTask.milestone = taskInfo.milestone;\n  lastTask = newTask;\n  tasks.push(newTask);\n};\n\nconst compileTasks = function () {\n  const compileTask = function (pos) {\n    const task = rawTasks[pos];\n    let startTime = '';\n    switch (rawTasks[pos].raw.startTime.type) {\n      case 'prevTaskEnd': {\n        const prevTask = findTaskById(task.prevTaskId);\n        task.startTime = prevTask.endTime;\n        break;\n      }\n      case 'getStartDate':\n        startTime = getStartDate(undefined, dateFormat, rawTasks[pos].raw.startTime.startData);\n        if (startTime) {\n          rawTasks[pos].startTime = startTime;\n        }\n        break;\n    }\n\n    if (rawTasks[pos].startTime) {\n      rawTasks[pos].endTime = getEndDate(\n        rawTasks[pos].startTime,\n        dateFormat,\n        rawTasks[pos].raw.endTime.data,\n        inclusiveEndDates\n      );\n      if (rawTasks[pos].endTime) {\n        rawTasks[pos].processed = true;\n        rawTasks[pos].manualEndTime = dayjs(\n          rawTasks[pos].raw.endTime.data,\n          'YYYY-MM-DD',\n          true\n        ).isValid();\n        checkTaskDates(rawTasks[pos], dateFormat, excludes, includes);\n      }\n    }\n\n    return rawTasks[pos].processed;\n  };\n\n  let allProcessed = true;\n  for (const [i, rawTask] of rawTasks.entries()) {\n    compileTask(i);\n\n    allProcessed = allProcessed && rawTask.processed;\n  }\n  return allProcessed;\n};\n\n/**\n * Called by parser when a link is found. Adds the URL to the vertex data.\n *\n * @param ids Comma separated list of ids\n * @param _linkStr URL to create a link for\n */\nexport const setLink = function (ids, _linkStr) {\n  let linkStr = _linkStr;\n  if (getConfig().securityLevel !== 'loose') {\n    linkStr = sanitizeUrl(_linkStr);\n  }\n  ids.split(',').forEach(function (id) {\n    let rawTask = findTaskById(id);\n    if (rawTask !== undefined) {\n      pushFun(id, () => {\n        window.open(linkStr, '_self');\n      });\n      links.set(id, linkStr);\n    }\n  });\n  setClass(ids, 'clickable');\n};\n\n/**\n * Called by parser when a special node is found, e.g. a clickable element.\n *\n * @param ids Comma separated list of ids\n * @param className Class to add\n */\nexport const setClass = function (ids, className) {\n  ids.split(',').forEach(function (id) {\n    let rawTask = findTaskById(id);\n    if (rawTask !== undefined) {\n      rawTask.classes.push(className);\n    }\n  });\n};\n\nconst setClickFun = function (id, functionName, functionArgs) {\n  if (getConfig().securityLevel !== 'loose') {\n    return;\n  }\n  if (functionName === undefined) {\n    return;\n  }\n\n  let argList = [];\n  if (typeof functionArgs === 'string') {\n    /* Splits functionArgs by ',', ignoring all ',' in double quoted strings */\n    argList = functionArgs.split(/,(?=(?:(?:[^\"]*\"){2})*[^\"]*$)/);\n    for (let i = 0; i < argList.length; i++) {\n      let item = argList[i].trim();\n      /* Removes all double quotes at the start and end of an argument */\n      /* This preserves all starting and ending whitespace inside */\n      if (item.startsWith('\"') && item.endsWith('\"')) {\n        item = item.substr(1, item.length - 2);\n      }\n      argList[i] = item;\n    }\n  }\n\n  /* if no arguments passed into callback, default to passing in id */\n  if (argList.length === 0) {\n    argList.push(id);\n  }\n\n  let rawTask = findTaskById(id);\n  if (rawTask !== undefined) {\n    pushFun(id, () => {\n      utils.runFunc(functionName, ...argList);\n    });\n  }\n};\n\n/**\n * The callbackFunction is executed in a click event bound to the task with the specified id or the\n * task's assigned text\n *\n * @param id The task's id\n * @param callbackFunction A function to be executed when clicked on the task or the task's text\n */\nconst pushFun = function (id, callbackFunction) {\n  funs.push(\n    function () {\n      // const elem = d3.select(element).select(`[id=\"${id}\"]`)\n      const elem = document.querySelector(`[id=\"${id}\"]`);\n      if (elem !== null) {\n        elem.addEventListener('click', function () {\n          callbackFunction();\n        });\n      }\n    },\n    function () {\n      // const elem = d3.select(element).select(`[id=\"${id}-text\"]`)\n      const elem = document.querySelector(`[id=\"${id}-text\"]`);\n      if (elem !== null) {\n        elem.addEventListener('click', function () {\n          callbackFunction();\n        });\n      }\n    }\n  );\n};\n\n/**\n * Called by parser when a click definition is found. Registers an event handler.\n *\n * @param ids Comma separated list of ids\n * @param functionName Function to be called on click\n * @param functionArgs Function args the function should be called with\n */\nexport const setClickEvent = function (ids, functionName, functionArgs) {\n  ids.split(',').forEach(function (id) {\n    setClickFun(id, functionName, functionArgs);\n  });\n  setClass(ids, 'clickable');\n};\n\n/**\n * Binds all functions previously added to fun (specified through click) to the element\n *\n * @param element\n */\nexport const bindFunctions = function (element) {\n  funs.forEach(function (fun) {\n    fun(element);\n  });\n};\n\nexport default {\n  getConfig: () => getConfig().gantt,\n  clear,\n  setDateFormat,\n  getDateFormat,\n  enableInclusiveEndDates,\n  endDatesAreInclusive,\n  enableTopAxis,\n  topAxisEnabled,\n  setAxisFormat,\n  getAxisFormat,\n  setTickInterval,\n  getTickInterval,\n  setTodayMarker,\n  getTodayMarker,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  setDisplayMode,\n  getDisplayMode,\n  setAccDescription,\n  getAccDescription,\n  addSection,\n  getSections,\n  getTasks,\n  addTask,\n  findTaskById,\n  addTaskOrg,\n  setIncludes,\n  getIncludes,\n  setExcludes,\n  getExcludes,\n  setClickEvent,\n  setLink,\n  getLinks,\n  bindFunctions,\n  parseDuration,\n  isInvalidDate,\n  setWeekday,\n  getWeekday,\n  setWeekend,\n};\n\n/**\n * @param data\n * @param task\n * @param tags\n */\nfunction getTaskTags(data, task, tags) {\n  let matchFound = true;\n  while (matchFound) {\n    matchFound = false;\n    tags.forEach(function (t) {\n      const pattern = '^\\\\s*' + t + '\\\\s*$';\n      const regex = new RegExp(pattern);\n      if (data[0].match(regex)) {\n        task[t] = true;\n        data.shift(1);\n        matchFound = true;\n      }\n    });\n  }\n}\n", "import dayjs from 'dayjs';\nimport { log } from '../../logger.js';\nimport {\n  select,\n  scaleTime,\n  min,\n  max,\n  scaleLinear,\n  interpolateHcl,\n  axisBottom,\n  axisTop,\n  timeFormat,\n  timeMillisecond,\n  timeSecond,\n  timeMinute,\n  timeHour,\n  timeDay,\n  timeMonday,\n  timeTuesday,\n  timeWednesday,\n  timeThursday,\n  timeFriday,\n  timeSaturday,\n  timeSunday,\n  timeMonth,\n} from 'd3';\nimport common from '../common/common.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\n\nexport const setConf = function () {\n  log.debug('Something is calling, setConf, remove the call');\n};\n\n/**\n * This will map any day of the week that can be set in the `weekday` option to\n * the corresponding d3-time function that is used to calculate the ticks.\n */\nconst mapWeekdayToTimeFunction = {\n  monday: timeMonday,\n  tuesday: timeTuesday,\n  wednesday: timeWednesday,\n  thursday: timeThursday,\n  friday: timeFriday,\n  saturday: timeSaturday,\n  sunday: timeSunday,\n};\n\n/**\n * For this issue:\n * https://github.com/mermaid-js/mermaid/issues/1618\n *\n * Finds the number of intersections between tasks that happen at any point in time.\n * Used to figure out how many rows are needed to display the tasks when the display\n * mode is set to 'compact'.\n *\n * @param tasks\n * @param orderOffset\n */\nconst getMaxIntersections = (tasks, orderOffset) => {\n  let timeline = [...tasks].map(() => -Infinity);\n  let sorted = [...tasks].sort((a, b) => a.startTime - b.startTime || a.order - b.order);\n  let maxIntersections = 0;\n  for (const element of sorted) {\n    for (let j = 0; j < timeline.length; j++) {\n      if (element.startTime >= timeline[j]) {\n        timeline[j] = element.endTime;\n        element.order = j + orderOffset;\n        if (j > maxIntersections) {\n          maxIntersections = j;\n        }\n        break;\n      }\n    }\n  }\n\n  return maxIntersections;\n};\n\nlet w;\nexport const draw = function (text, id, version, diagObj) {\n  const conf = getConfig().gantt;\n\n  const securityLevel = getConfig().securityLevel;\n  // Handle root and Document for when rendering in sandbox mode\n  let sandboxElement;\n  if (securityLevel === 'sandbox') {\n    sandboxElement = select('#i' + id);\n  }\n  const root =\n    securityLevel === 'sandbox'\n      ? select(sandboxElement.nodes()[0].contentDocument.body)\n      : select('body');\n  const doc = securityLevel === 'sandbox' ? sandboxElement.nodes()[0].contentDocument : document;\n\n  const elem = doc.getElementById(id);\n  w = elem.parentElement.offsetWidth;\n\n  if (w === undefined) {\n    w = 1200;\n  }\n\n  if (conf.useWidth !== undefined) {\n    w = conf.useWidth;\n  }\n\n  const taskArray = diagObj.db.getTasks();\n\n  // Set height based on number of tasks\n\n  let categories = [];\n\n  for (const element of taskArray) {\n    categories.push(element.type);\n  }\n\n  categories = checkUnique(categories);\n  const categoryHeights = {};\n\n  let h = 2 * conf.topPadding;\n  if (diagObj.db.getDisplayMode() === 'compact' || conf.displayMode === 'compact') {\n    const categoryElements = {};\n    for (const element of taskArray) {\n      if (categoryElements[element.section] === undefined) {\n        categoryElements[element.section] = [element];\n      } else {\n        categoryElements[element.section].push(element);\n      }\n    }\n\n    let intersections = 0;\n    for (const category of Object.keys(categoryElements)) {\n      const categoryHeight = getMaxIntersections(categoryElements[category], intersections) + 1;\n      intersections += categoryHeight;\n      h += categoryHeight * (conf.barHeight + conf.barGap);\n      categoryHeights[category] = categoryHeight;\n    }\n  } else {\n    h += taskArray.length * (conf.barHeight + conf.barGap);\n    for (const category of categories) {\n      categoryHeights[category] = taskArray.filter((task) => task.type === category).length;\n    }\n  }\n\n  // Set viewBox\n  elem.setAttribute('viewBox', '0 0 ' + w + ' ' + h);\n  const svg = root.select(`[id=\"${id}\"]`);\n\n  // Set timescale\n  const timeScale = scaleTime()\n    .domain([\n      min(taskArray, function (d) {\n        return d.startTime;\n      }),\n      max(taskArray, function (d) {\n        return d.endTime;\n      }),\n    ])\n    .rangeRound([0, w - conf.leftPadding - conf.rightPadding]);\n\n  /**\n   * @param a\n   * @param b\n   */\n  function taskCompare(a, b) {\n    const taskA = a.startTime;\n    const taskB = b.startTime;\n    let result = 0;\n    if (taskA > taskB) {\n      result = 1;\n    } else if (taskA < taskB) {\n      result = -1;\n    }\n    return result;\n  }\n\n  // Sort the task array using the above taskCompare() so that\n  // tasks are created based on their order of startTime\n  taskArray.sort(taskCompare);\n\n  makeGantt(taskArray, w, h);\n\n  configureSvgSize(svg, h, w, conf.useMaxWidth);\n\n  svg\n    .append('text')\n    .text(diagObj.db.getDiagramTitle())\n    .attr('x', w / 2)\n    .attr('y', conf.titleTopMargin)\n    .attr('class', 'titleText');\n\n  /**\n   * @param tasks\n   * @param pageWidth\n   * @param pageHeight\n   */\n  function makeGantt(tasks, pageWidth, pageHeight) {\n    const barHeight = conf.barHeight;\n    const gap = barHeight + conf.barGap;\n    const topPadding = conf.topPadding;\n    const leftPadding = conf.leftPadding;\n\n    const colorScale = scaleLinear()\n      .domain([0, categories.length])\n      .range(['#00B9FA', '#F95002'])\n      .interpolate(interpolateHcl);\n\n    drawExcludeDays(\n      gap,\n      topPadding,\n      leftPadding,\n      pageWidth,\n      pageHeight,\n      tasks,\n      diagObj.db.getExcludes(),\n      diagObj.db.getIncludes()\n    );\n    makeGrid(leftPadding, topPadding, pageWidth, pageHeight);\n    drawRects(tasks, gap, topPadding, leftPadding, barHeight, colorScale, pageWidth, pageHeight);\n    vertLabels(gap, topPadding, leftPadding, barHeight, colorScale);\n    drawToday(leftPadding, topPadding, pageWidth, pageHeight);\n  }\n\n  /**\n   * @param theArray\n   * @param theGap\n   * @param theTopPad\n   * @param theSidePad\n   * @param theBarHeight\n   * @param theColorScale\n   * @param w\n   */\n  function drawRects(theArray, theGap, theTopPad, theSidePad, theBarHeight, theColorScale, w) {\n    // Get unique task orders. Required to draw the background rects when display mode is compact.\n    const uniqueTaskOrderIds = [...new Set(theArray.map((item) => item.order))];\n    const uniqueTasks = uniqueTaskOrderIds.map((id) => theArray.find((item) => item.order === id));\n\n    // Draw background rects covering the entire width of the graph, these form the section rows.\n    svg\n      .append('g')\n      .selectAll('rect')\n      .data(uniqueTasks)\n      .enter()\n      .append('rect')\n      .attr('x', 0)\n      .attr('y', function (d, i) {\n        // Ignore the incoming i value and use our order instead\n        i = d.order;\n        return i * theGap + theTopPad - 2;\n      })\n      .attr('width', function () {\n        return w - conf.rightPadding / 2;\n      })\n      .attr('height', theGap)\n      .attr('class', function (d) {\n        for (const [i, category] of categories.entries()) {\n          if (d.type === category) {\n            return 'section section' + (i % conf.numberSectionStyles);\n          }\n        }\n        return 'section section0';\n      });\n\n    // Draw the rects representing the tasks\n    const rectangles = svg.append('g').selectAll('rect').data(theArray).enter();\n\n    const links = diagObj.db.getLinks();\n\n    // Render the tasks with links\n    // Render the other tasks\n    rectangles\n      .append('rect')\n      .attr('id', function (d) {\n        return d.id;\n      })\n      .attr('rx', 3)\n      .attr('ry', 3)\n      .attr('x', function (d) {\n        if (d.milestone) {\n          return (\n            timeScale(d.startTime) +\n            theSidePad +\n            0.5 * (timeScale(d.endTime) - timeScale(d.startTime)) -\n            0.5 * theBarHeight\n          );\n        }\n        return timeScale(d.startTime) + theSidePad;\n      })\n      .attr('y', function (d, i) {\n        // Ignore the incoming i value and use our order instead\n        i = d.order;\n        return i * theGap + theTopPad;\n      })\n      .attr('width', function (d) {\n        if (d.milestone) {\n          return theBarHeight;\n        }\n        return timeScale(d.renderEndTime || d.endTime) - timeScale(d.startTime);\n      })\n      .attr('height', theBarHeight)\n      .attr('transform-origin', function (d, i) {\n        // Ignore the incoming i value and use our order instead\n        i = d.order;\n\n        return (\n          (\n            timeScale(d.startTime) +\n            theSidePad +\n            0.5 * (timeScale(d.endTime) - timeScale(d.startTime))\n          ).toString() +\n          'px ' +\n          (i * theGap + theTopPad + 0.5 * theBarHeight).toString() +\n          'px'\n        );\n      })\n      .attr('class', function (d) {\n        const res = 'task';\n\n        let classStr = '';\n        if (d.classes.length > 0) {\n          classStr = d.classes.join(' ');\n        }\n\n        let secNum = 0;\n        for (const [i, category] of categories.entries()) {\n          if (d.type === category) {\n            secNum = i % conf.numberSectionStyles;\n          }\n        }\n\n        let taskClass = '';\n        if (d.active) {\n          if (d.crit) {\n            taskClass += ' activeCrit';\n          } else {\n            taskClass = ' active';\n          }\n        } else if (d.done) {\n          if (d.crit) {\n            taskClass = ' doneCrit';\n          } else {\n            taskClass = ' done';\n          }\n        } else {\n          if (d.crit) {\n            taskClass += ' crit';\n          }\n        }\n\n        if (taskClass.length === 0) {\n          taskClass = ' task';\n        }\n\n        if (d.milestone) {\n          taskClass = ' milestone ' + taskClass;\n        }\n\n        taskClass += secNum;\n\n        taskClass += ' ' + classStr;\n\n        return res + taskClass;\n      });\n\n    // Append task labels\n    rectangles\n      .append('text')\n      .attr('id', function (d) {\n        return d.id + '-text';\n      })\n      .text(function (d) {\n        return d.task;\n      })\n      .attr('font-size', conf.fontSize)\n      .attr('x', function (d) {\n        let startX = timeScale(d.startTime);\n        let endX = timeScale(d.renderEndTime || d.endTime);\n        if (d.milestone) {\n          startX += 0.5 * (timeScale(d.endTime) - timeScale(d.startTime)) - 0.5 * theBarHeight;\n        }\n        if (d.milestone) {\n          endX = startX + theBarHeight;\n        }\n        const textWidth = this.getBBox().width;\n\n        // Check id text width > width of rectangle\n        if (textWidth > endX - startX) {\n          if (endX + textWidth + 1.5 * conf.leftPadding > w) {\n            return startX + theSidePad - 5;\n          } else {\n            return endX + theSidePad + 5;\n          }\n        } else {\n          return (endX - startX) / 2 + startX + theSidePad;\n        }\n      })\n      .attr('y', function (d, i) {\n        // Ignore the incoming i value and use our order instead\n        i = d.order;\n        return i * theGap + conf.barHeight / 2 + (conf.fontSize / 2 - 2) + theTopPad;\n      })\n      .attr('text-height', theBarHeight)\n      .attr('class', function (d) {\n        const startX = timeScale(d.startTime);\n        let endX = timeScale(d.endTime);\n        if (d.milestone) {\n          endX = startX + theBarHeight;\n        }\n        const textWidth = this.getBBox().width;\n\n        let classStr = '';\n        if (d.classes.length > 0) {\n          classStr = d.classes.join(' ');\n        }\n\n        let secNum = 0;\n        for (const [i, category] of categories.entries()) {\n          if (d.type === category) {\n            secNum = i % conf.numberSectionStyles;\n          }\n        }\n\n        let taskType = '';\n        if (d.active) {\n          if (d.crit) {\n            taskType = 'activeCritText' + secNum;\n          } else {\n            taskType = 'activeText' + secNum;\n          }\n        }\n\n        if (d.done) {\n          if (d.crit) {\n            taskType = taskType + ' doneCritText' + secNum;\n          } else {\n            taskType = taskType + ' doneText' + secNum;\n          }\n        } else {\n          if (d.crit) {\n            taskType = taskType + ' critText' + secNum;\n          }\n        }\n\n        if (d.milestone) {\n          taskType += ' milestoneText';\n        }\n\n        // Check id text width > width of rectangle\n        if (textWidth > endX - startX) {\n          if (endX + textWidth + 1.5 * conf.leftPadding > w) {\n            return classStr + ' taskTextOutsideLeft taskTextOutside' + secNum + ' ' + taskType;\n          } else {\n            return (\n              classStr +\n              ' taskTextOutsideRight taskTextOutside' +\n              secNum +\n              ' ' +\n              taskType +\n              ' width-' +\n              textWidth\n            );\n          }\n        } else {\n          return classStr + ' taskText taskText' + secNum + ' ' + taskType + ' width-' + textWidth;\n        }\n      });\n\n    const securityLevel = getConfig().securityLevel;\n\n    // Wrap the tasks in an a tag for working links without javascript\n    if (securityLevel === 'sandbox') {\n      let sandboxElement;\n      sandboxElement = select('#i' + id);\n      const doc = sandboxElement.nodes()[0].contentDocument;\n\n      rectangles\n        .filter(function (d) {\n          return links.has(d.id);\n        })\n        .each(function (o) {\n          var taskRect = doc.querySelector('#' + o.id);\n          var taskText = doc.querySelector('#' + o.id + '-text');\n          const oldParent = taskRect.parentNode;\n          var Link = doc.createElement('a');\n          Link.setAttribute('xlink:href', links.get(o.id));\n          Link.setAttribute('target', '_top');\n          oldParent.appendChild(Link);\n          Link.appendChild(taskRect);\n          Link.appendChild(taskText);\n        });\n    }\n  }\n  /**\n   * @param theGap\n   * @param theTopPad\n   * @param theSidePad\n   * @param w\n   * @param h\n   * @param tasks\n   * @param {unknown[]} excludes\n   * @param {unknown[]} includes\n   */\n  function drawExcludeDays(theGap, theTopPad, theSidePad, w, h, tasks, excludes, includes) {\n    if (excludes.length === 0 && includes.length === 0) {\n      return;\n    }\n\n    let minTime;\n    let maxTime;\n    for (const { startTime, endTime } of tasks) {\n      if (minTime === undefined || startTime < minTime) {\n        minTime = startTime;\n      }\n      if (maxTime === undefined || endTime > maxTime) {\n        maxTime = endTime;\n      }\n    }\n\n    if (!minTime || !maxTime) {\n      return;\n    }\n\n    if (dayjs(maxTime).diff(dayjs(minTime), 'year') > 5) {\n      log.warn(\n        'The difference between the min and max time is more than 5 years. This will cause performance issues. Skipping drawing exclude days.'\n      );\n      return;\n    }\n\n    const dateFormat = diagObj.db.getDateFormat();\n    const excludeRanges = [];\n    let range = null;\n    let d = dayjs(minTime);\n    while (d.valueOf() <= maxTime) {\n      if (diagObj.db.isInvalidDate(d, dateFormat, excludes, includes)) {\n        if (!range) {\n          range = {\n            start: d,\n            end: d,\n          };\n        } else {\n          range.end = d;\n        }\n      } else {\n        if (range) {\n          excludeRanges.push(range);\n          range = null;\n        }\n      }\n      d = d.add(1, 'd');\n    }\n\n    const rectangles = svg.append('g').selectAll('rect').data(excludeRanges).enter();\n\n    rectangles\n      .append('rect')\n      .attr('id', function (d) {\n        return 'exclude-' + d.start.format('YYYY-MM-DD');\n      })\n      .attr('x', function (d) {\n        return timeScale(d.start) + theSidePad;\n      })\n      .attr('y', conf.gridLineStartPadding)\n      .attr('width', function (d) {\n        const renderEnd = d.end.add(1, 'day');\n        return timeScale(renderEnd) - timeScale(d.start);\n      })\n      .attr('height', h - theTopPad - conf.gridLineStartPadding)\n      .attr('transform-origin', function (d, i) {\n        return (\n          (\n            timeScale(d.start) +\n            theSidePad +\n            0.5 * (timeScale(d.end) - timeScale(d.start))\n          ).toString() +\n          'px ' +\n          (i * theGap + 0.5 * h).toString() +\n          'px'\n        );\n      })\n      .attr('class', 'exclude-range');\n  }\n\n  /**\n   * @param theSidePad\n   * @param theTopPad\n   * @param w\n   * @param h\n   */\n  function makeGrid(theSidePad, theTopPad, w, h) {\n    let bottomXAxis = axisBottom(timeScale)\n      .tickSize(-h + theTopPad + conf.gridLineStartPadding)\n      .tickFormat(timeFormat(diagObj.db.getAxisFormat() || conf.axisFormat || '%Y-%m-%d'));\n\n    const reTickInterval = /^([1-9]\\d*)(millisecond|second|minute|hour|day|week|month)$/;\n    const resultTickInterval = reTickInterval.exec(\n      diagObj.db.getTickInterval() || conf.tickInterval\n    );\n\n    if (resultTickInterval !== null) {\n      const every = resultTickInterval[1];\n      const interval = resultTickInterval[2];\n      const weekday = diagObj.db.getWeekday() || conf.weekday;\n\n      switch (interval) {\n        case 'millisecond':\n          bottomXAxis.ticks(timeMillisecond.every(every));\n          break;\n        case 'second':\n          bottomXAxis.ticks(timeSecond.every(every));\n          break;\n        case 'minute':\n          bottomXAxis.ticks(timeMinute.every(every));\n          break;\n        case 'hour':\n          bottomXAxis.ticks(timeHour.every(every));\n          break;\n        case 'day':\n          bottomXAxis.ticks(timeDay.every(every));\n          break;\n        case 'week':\n          bottomXAxis.ticks(mapWeekdayToTimeFunction[weekday].every(every));\n          break;\n        case 'month':\n          bottomXAxis.ticks(timeMonth.every(every));\n          break;\n      }\n    }\n\n    svg\n      .append('g')\n      .attr('class', 'grid')\n      .attr('transform', 'translate(' + theSidePad + ', ' + (h - 50) + ')')\n      .call(bottomXAxis)\n      .selectAll('text')\n      .style('text-anchor', 'middle')\n      .attr('fill', '#000')\n      .attr('stroke', 'none')\n      .attr('font-size', 10)\n      .attr('dy', '1em');\n\n    if (diagObj.db.topAxisEnabled() || conf.topAxis) {\n      let topXAxis = axisTop(timeScale)\n        .tickSize(-h + theTopPad + conf.gridLineStartPadding)\n        .tickFormat(timeFormat(diagObj.db.getAxisFormat() || conf.axisFormat || '%Y-%m-%d'));\n\n      if (resultTickInterval !== null) {\n        const every = resultTickInterval[1];\n        const interval = resultTickInterval[2];\n        const weekday = diagObj.db.getWeekday() || conf.weekday;\n\n        switch (interval) {\n          case 'millisecond':\n            topXAxis.ticks(timeMillisecond.every(every));\n            break;\n          case 'second':\n            topXAxis.ticks(timeSecond.every(every));\n            break;\n          case 'minute':\n            topXAxis.ticks(timeMinute.every(every));\n            break;\n          case 'hour':\n            topXAxis.ticks(timeHour.every(every));\n            break;\n          case 'day':\n            topXAxis.ticks(timeDay.every(every));\n            break;\n          case 'week':\n            topXAxis.ticks(mapWeekdayToTimeFunction[weekday].every(every));\n            break;\n          case 'month':\n            topXAxis.ticks(timeMonth.every(every));\n            break;\n        }\n      }\n\n      svg\n        .append('g')\n        .attr('class', 'grid')\n        .attr('transform', 'translate(' + theSidePad + ', ' + theTopPad + ')')\n        .call(topXAxis)\n        .selectAll('text')\n        .style('text-anchor', 'middle')\n        .attr('fill', '#000')\n        .attr('stroke', 'none')\n        .attr('font-size', 10);\n      // .attr('dy', '1em');\n    }\n  }\n\n  /**\n   * @param theGap\n   * @param theTopPad\n   */\n  function vertLabels(theGap, theTopPad) {\n    let prevGap = 0;\n\n    const numOccurrences = Object.keys(categoryHeights).map((d) => [d, categoryHeights[d]]);\n\n    svg\n      .append('g') // without doing this, impossible to put grid lines behind text\n      .selectAll('text')\n      .data(numOccurrences)\n      .enter()\n      .append(function (d) {\n        const rows = d[0].split(common.lineBreakRegex);\n        const dy = -(rows.length - 1) / 2;\n\n        const svgLabel = doc.createElementNS('http://www.w3.org/2000/svg', 'text');\n        svgLabel.setAttribute('dy', dy + 'em');\n\n        for (const [j, row] of rows.entries()) {\n          const tspan = doc.createElementNS('http://www.w3.org/2000/svg', 'tspan');\n          tspan.setAttribute('alignment-baseline', 'central');\n          tspan.setAttribute('x', '10');\n          if (j > 0) {\n            tspan.setAttribute('dy', '1em');\n          }\n          tspan.textContent = row;\n          svgLabel.appendChild(tspan);\n        }\n        return svgLabel;\n      })\n      .attr('x', 10)\n      .attr('y', function (d, i) {\n        if (i > 0) {\n          for (let j = 0; j < i; j++) {\n            prevGap += numOccurrences[i - 1][1];\n            return (d[1] * theGap) / 2 + prevGap * theGap + theTopPad;\n          }\n        } else {\n          return (d[1] * theGap) / 2 + theTopPad;\n        }\n      })\n      .attr('font-size', conf.sectionFontSize)\n      .attr('class', function (d) {\n        for (const [i, category] of categories.entries()) {\n          if (d[0] === category) {\n            return 'sectionTitle sectionTitle' + (i % conf.numberSectionStyles);\n          }\n        }\n        return 'sectionTitle';\n      });\n  }\n\n  /**\n   * @param theSidePad\n   * @param theTopPad\n   * @param w\n   * @param h\n   */\n  function drawToday(theSidePad, theTopPad, w, h) {\n    const todayMarker = diagObj.db.getTodayMarker();\n    if (todayMarker === 'off') {\n      return;\n    }\n\n    const todayG = svg.append('g').attr('class', 'today');\n    const today = new Date();\n    const todayLine = todayG.append('line');\n\n    todayLine\n      .attr('x1', timeScale(today) + theSidePad)\n      .attr('x2', timeScale(today) + theSidePad)\n      .attr('y1', conf.titleTopMargin)\n      .attr('y2', h - conf.titleTopMargin)\n      .attr('class', 'today');\n\n    if (todayMarker !== '') {\n      todayLine.attr('style', todayMarker.replace(/,/g, ';'));\n    }\n  }\n\n  /**\n   * From this stack exchange question:\n   * http://stackoverflow.com/questions/1890203/unique-for-arrays-in-javascript\n   *\n   * @param arr\n   */\n  function checkUnique(arr) {\n    const hash = {};\n    const result = [];\n    for (let i = 0, l = arr.length; i < l; ++i) {\n      if (!Object.prototype.hasOwnProperty.call(hash, arr[i])) {\n        // it works with objects! in FF, at least\n        hash[arr[i]] = true;\n        result.push(arr[i]);\n      }\n    }\n    return result;\n  }\n};\n\nexport default {\n  setConf,\n  draw,\n};\n", "const getStyles = (options) =>\n  `\n  .mermaid-main-font {\n        font-family: ${options.fontFamily};\n  }\n\n  .exclude-range {\n    fill: ${options.excludeBkgColor};\n  }\n\n  .section {\n    stroke: none;\n    opacity: 0.2;\n  }\n\n  .section0 {\n    fill: ${options.sectionBkgColor};\n  }\n\n  .section2 {\n    fill: ${options.sectionBkgColor2};\n  }\n\n  .section1,\n  .section3 {\n    fill: ${options.altSectionBkgColor};\n    opacity: 0.2;\n  }\n\n  .sectionTitle0 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle1 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle2 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle3 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle {\n    text-anchor: start;\n    font-family: ${options.fontFamily};\n  }\n\n\n  /* Grid and axis */\n\n  .grid .tick {\n    stroke: ${options.gridColor};\n    opacity: 0.8;\n    shape-rendering: crispEdges;\n  }\n\n  .grid .tick text {\n    font-family: ${options.fontFamily};\n    fill: ${options.textColor};\n  }\n\n  .grid path {\n    stroke-width: 0;\n  }\n\n\n  /* Today line */\n\n  .today {\n    fill: none;\n    stroke: ${options.todayLineColor};\n    stroke-width: 2px;\n  }\n\n\n  /* Task styling */\n\n  /* Default task */\n\n  .task {\n    stroke-width: 2;\n  }\n\n  .taskText {\n    text-anchor: middle;\n    font-family: ${options.fontFamily};\n  }\n\n  .taskTextOutsideRight {\n    fill: ${options.taskTextDarkColor};\n    text-anchor: start;\n    font-family: ${options.fontFamily};\n  }\n\n  .taskTextOutsideLeft {\n    fill: ${options.taskTextDarkColor};\n    text-anchor: end;\n  }\n\n\n  /* Special case clickable */\n\n  .task.clickable {\n    cursor: pointer;\n  }\n\n  .taskText.clickable {\n    cursor: pointer;\n    fill: ${options.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n  .taskTextOutsideLeft.clickable {\n    cursor: pointer;\n    fill: ${options.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n  .taskTextOutsideRight.clickable {\n    cursor: pointer;\n    fill: ${options.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n\n  /* Specific task settings for the sections*/\n\n  .taskText0,\n  .taskText1,\n  .taskText2,\n  .taskText3 {\n    fill: ${options.taskTextColor};\n  }\n\n  .task0,\n  .task1,\n  .task2,\n  .task3 {\n    fill: ${options.taskBkgColor};\n    stroke: ${options.taskBorderColor};\n  }\n\n  .taskTextOutside0,\n  .taskTextOutside2\n  {\n    fill: ${options.taskTextOutsideColor};\n  }\n\n  .taskTextOutside1,\n  .taskTextOutside3 {\n    fill: ${options.taskTextOutsideColor};\n  }\n\n\n  /* Active task */\n\n  .active0,\n  .active1,\n  .active2,\n  .active3 {\n    fill: ${options.activeTaskBkgColor};\n    stroke: ${options.activeTaskBorderColor};\n  }\n\n  .activeText0,\n  .activeText1,\n  .activeText2,\n  .activeText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n\n  /* Completed task */\n\n  .done0,\n  .done1,\n  .done2,\n  .done3 {\n    stroke: ${options.doneTaskBorderColor};\n    fill: ${options.doneTaskBkgColor};\n    stroke-width: 2;\n  }\n\n  .doneText0,\n  .doneText1,\n  .doneText2,\n  .doneText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n\n  /* Tasks on the critical line */\n\n  .crit0,\n  .crit1,\n  .crit2,\n  .crit3 {\n    stroke: ${options.critBorderColor};\n    fill: ${options.critBkgColor};\n    stroke-width: 2;\n  }\n\n  .activeCrit0,\n  .activeCrit1,\n  .activeCrit2,\n  .activeCrit3 {\n    stroke: ${options.critBorderColor};\n    fill: ${options.activeTaskBkgColor};\n    stroke-width: 2;\n  }\n\n  .doneCrit0,\n  .doneCrit1,\n  .doneCrit2,\n  .doneCrit3 {\n    stroke: ${options.critBorderColor};\n    fill: ${options.doneTaskBkgColor};\n    stroke-width: 2;\n    cursor: pointer;\n    shape-rendering: crispEdges;\n  }\n\n  .milestone {\n    transform: rotate(45deg) scale(0.8,0.8);\n  }\n\n  .milestoneText {\n    font-style: italic;\n  }\n  .doneCritText0,\n  .doneCritText1,\n  .doneCritText2,\n  .doneCritText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n  .activeCritText0,\n  .activeCritText1,\n  .activeCritText2,\n  .activeCritText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n  .titleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${options.titleColor || options.textColor};\n    font-family: ${options.fontFamily};\n  }\n`;\n\nexport default getStyles;\n", "// @ts-ignore: JISON doesn't support types\nimport ganttParser from './parser/gantt.jison';\nimport ganttDb from './ganttDb.js';\nimport ganttRenderer from './ganttRenderer.js';\nimport ganttStyles from './styles.js';\nimport type { DiagramDefinition } from '../../diagram-api/types.js';\n\nexport const diagram: DiagramDefinition = {\n  parser: ganttParser,\n  db: ganttDb,\n  renderer: ganttRenderer,\n  styles: ganttStyles,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAyEA,IAAI,SAAU,WAAU;AACxB,MAAI,IAAE,gCAAS,GAAE,GAAEA,IAAE,GAAE;AAAC,SAAIA,KAAEA,MAAG,CAAC,GAAE,IAAE,EAAE,QAAO,KAAIA,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE;AAAC,WAAOA;AAAA,EAAC,GAAhE,MAAkE,MAAI,CAAC,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE;AACla,MAAIC,UAAS;AAAA,IAAC,OAAO,gCAAS,QAAS;AAAA,IAAE,GAApB;AAAA,IACrB,IAAI,CAAC;AAAA,IACL,UAAU,EAAC,SAAQ,GAAE,SAAQ,GAAE,SAAQ,GAAE,YAAW,GAAE,OAAM,GAAE,QAAO,GAAE,SAAQ,GAAE,aAAY,GAAE,MAAK,IAAG,WAAU,IAAG,kBAAiB,IAAG,mBAAkB,IAAG,qBAAoB,IAAG,oBAAmB,IAAG,kBAAiB,IAAG,oBAAmB,IAAG,kBAAiB,IAAG,WAAU,IAAG,kBAAiB,IAAG,oBAAmB,IAAG,cAAa,IAAG,qBAAoB,IAAG,WAAU,IAAG,cAAa,IAAG,gBAAe,IAAG,YAAW,IAAG,YAAW,IAAG,eAAc,IAAG,SAAQ,IAAG,aAAY,IAAG,mBAAkB,IAAG,aAAY,IAAG,mBAAkB,IAAG,6BAA4B,IAAG,WAAU,IAAG,kBAAiB,IAAG,WAAU,IAAG,YAAW,IAAG,SAAQ,IAAG,gBAAe,IAAG,gBAAe,IAAG,QAAO,IAAG,uBAAsB,IAAG,WAAU,GAAE,QAAO,EAAC;AAAA,IACttB,YAAY,EAAC,GAAE,SAAQ,GAAE,SAAQ,GAAE,OAAM,GAAE,SAAQ,IAAG,MAAK,IAAG,kBAAiB,IAAG,mBAAkB,IAAG,qBAAoB,IAAG,oBAAmB,IAAG,kBAAiB,IAAG,oBAAmB,IAAG,kBAAiB,IAAG,kBAAiB,IAAG,oBAAmB,IAAG,cAAa,IAAG,qBAAoB,IAAG,WAAU,IAAG,cAAa,IAAG,gBAAe,IAAG,YAAW,IAAG,YAAW,IAAG,eAAc,IAAG,SAAQ,IAAG,aAAY,IAAG,mBAAkB,IAAG,aAAY,IAAG,mBAAkB,IAAG,6BAA4B,IAAG,WAAU,IAAG,WAAU,IAAG,YAAW,IAAG,SAAQ,IAAG,gBAAe,IAAG,gBAAe,IAAG,OAAM;AAAA,IAC9kB,cAAc,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC;AAAA,IACjU,eAAe,gCAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAyB,IAAiB,IAAiB;AAG3H,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAS;AAAA,QACjB,KAAK;AACJ,iBAAO,GAAG,KAAG,CAAC;AACf;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,CAAC;AACX;AAAA,QACA,KAAK;AACL,aAAG,KAAG,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC;AAAE,eAAK,IAAI,GAAG,KAAG,CAAC;AACtC;AAAA,QACA,KAAK;AAAA,QAAG,KAAK;AACZ,eAAK,IAAI,GAAG,EAAE;AACf;AAAA,QACA,KAAK;AAAA,QAAG,KAAK;AACZ,eAAK,IAAE,CAAC;AACT;AAAA,QACA,KAAK;AACJ,aAAG,WAAW,QAAQ;AACvB;AAAA,QACA,KAAK;AACJ,aAAG,WAAW,SAAS;AACxB;AAAA,QACA,KAAK;AACJ,aAAG,WAAW,WAAW;AAC1B;AAAA,QACA,KAAK;AACJ,aAAG,WAAW,UAAU;AACzB;AAAA,QACA,KAAK;AACJ,aAAG,WAAW,QAAQ;AACvB;AAAA,QACA,KAAK;AACJ,aAAG,WAAW,UAAU;AACzB;AAAA,QACA,KAAK;AACJ,aAAG,WAAW,QAAQ;AACvB;AAAA,QACA,KAAK;AACJ,aAAG,WAAW,QAAQ;AACvB;AAAA,QACA,KAAK;AACJ,aAAG,WAAW,UAAU;AACzB;AAAA,QACA,KAAK;AACL,aAAG,cAAc,GAAG,EAAE,EAAE,OAAO,EAAE,CAAC;AAAE,eAAK,IAAE,GAAG,EAAE,EAAE,OAAO,EAAE;AAC3D;AAAA,QACA,KAAK;AACL,aAAG,wBAAwB;AAAE,eAAK,IAAE,GAAG,EAAE,EAAE,OAAO,EAAE;AACpD;AAAA,QACA,KAAK;AACL,aAAG,QAAQ;AAAE,eAAK,IAAE,GAAG,EAAE,EAAE,OAAO,CAAC;AACnC;AAAA,QACA,KAAK;AACL,aAAG,cAAc,GAAG,EAAE,EAAE,OAAO,EAAE,CAAC;AAAE,eAAK,IAAE,GAAG,EAAE,EAAE,OAAO,EAAE;AAC3D;AAAA,QACA,KAAK;AACL,aAAG,gBAAgB,GAAG,EAAE,EAAE,OAAO,EAAE,CAAC;AAAE,eAAK,IAAE,GAAG,EAAE,EAAE,OAAO,EAAE;AAC7D;AAAA,QACA,KAAK;AACL,aAAG,YAAY,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC;AAAE,eAAK,IAAE,GAAG,EAAE,EAAE,OAAO,CAAC;AACvD;AAAA,QACA,KAAK;AACL,aAAG,YAAY,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC;AAAE,eAAK,IAAE,GAAG,EAAE,EAAE,OAAO,CAAC;AACvD;AAAA,QACA,KAAK;AACL,aAAG,eAAe,GAAG,EAAE,EAAE,OAAO,EAAE,CAAC;AAAE,eAAK,IAAE,GAAG,EAAE,EAAE,OAAO,EAAE;AAC5D;AAAA,QACA,KAAK;AACL,aAAG,gBAAgB,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC;AAAE,eAAK,IAAE,GAAG,EAAE,EAAE,OAAO,CAAC;AAC3D;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,EAAE,EAAE,KAAK;AAAE,aAAG,YAAY,KAAK,CAAC;AAC3C;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AACb,eAAK,IAAE,GAAG,EAAE,EAAE,KAAK;AAAE,aAAG,kBAAkB,KAAK,CAAC;AACjD;AAAA,QACA,KAAK;AACJ,aAAG,WAAW,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC;AAAE,eAAK,IAAE,GAAG,EAAE,EAAE,OAAO,CAAC;AACvD;AAAA,QACA,KAAK;AACL,aAAG,QAAQ,GAAG,KAAG,CAAC,GAAE,GAAG,EAAE,CAAC;AAAE,eAAK,IAAE;AACnC;AAAA,QACA,KAAK;AACL,eAAK,IAAI,GAAG,KAAG,CAAC;AAAE,aAAG,cAAc,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,GAAG,IAAI;AACzD;AAAA,QACA,KAAK;AACL,eAAK,IAAI,GAAG,KAAG,CAAC;AAAE,aAAG,cAAc,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC;AAC7D;AAAA,QACA,KAAK;AACL,eAAK,IAAI,GAAG,KAAG,CAAC;AAAE,aAAG,cAAc,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,GAAG,IAAI;AAAE,aAAG,QAAQ,GAAG,KAAG,CAAC,GAAE,GAAG,EAAE,CAAC;AACvF;AAAA,QACA,KAAK;AACL,eAAK,IAAI,GAAG,KAAG,CAAC;AAAE,aAAG,cAAc,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,CAAC;AAAE,aAAG,QAAQ,GAAG,KAAG,CAAC,GAAE,GAAG,EAAE,CAAC;AAC3F;AAAA,QACA,KAAK;AACL,eAAK,IAAI,GAAG,KAAG,CAAC;AAAE,aAAG,cAAc,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,GAAG,IAAI;AAAE,aAAG,QAAQ,GAAG,KAAG,CAAC,GAAE,GAAG,KAAG,CAAC,CAAC;AACvF;AAAA,QACA,KAAK;AACL,eAAK,IAAI,GAAG,KAAG,CAAC;AAAE,aAAG,cAAc,GAAG,KAAG,CAAC,GAAG,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC;AAAE,aAAG,QAAQ,GAAG,KAAG,CAAC,GAAE,GAAG,KAAG,CAAC,CAAC;AAC3F;AAAA,QACA,KAAK;AACL,eAAK,IAAI,GAAG,KAAG,CAAC;AAAE,aAAG,QAAQ,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC;AAC7C;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AACd,eAAK,IAAE,GAAG,KAAG,CAAC,IAAI,MAAM,GAAG,EAAE;AAC7B;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AAAA,QAAI,KAAK;AACvB,eAAK,IAAE,GAAG,KAAG,CAAC,IAAI,MAAM,GAAG,KAAG,CAAC,IAAI,MAAM,GAAG,EAAE;AAC9C;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AACd,eAAK,IAAE,GAAG,KAAG,CAAC,IAAI,MAAM,GAAG,KAAG,CAAC,IAAI,MAAM,GAAG,KAAG,CAAC,IAAI,MAAM,GAAG,EAAE;AAC/D;AAAA,MACA;AAAA,IACA,GArHe;AAAA,IAsHf,OAAO,CAAC,EAAC,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,CAAC,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,EAAC,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAC,GAAE,IAAG,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,EAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IACzlC,gBAAgB,CAAC;AAAA,IACjB,YAAY,gCAAS,WAAY,KAAK,MAAM;AACxC,UAAI,KAAK,aAAa;AAClB,aAAK,MAAM,GAAG;AAAA,MAClB,OAAO;AACH,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;AAAA,MACV;AAAA,IACJ,GARY;AAAA,IASZ,OAAO,gCAAS,MAAM,OAAO;AACzB,UAAI,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAIC,SAAQ,OAAO,OAAO,KAAK,KAAK;AACpC,UAAI,cAAc,EAAE,IAAI,CAAC,EAAE;AAC3B,eAAS,KAAK,KAAK,IAAI;AACnB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AAClD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;AAAA,QACjC;AAAA,MACJ;AACA,MAAAA,OAAM,SAAS,OAAO,YAAY,EAAE;AACpC,kBAAY,GAAG,QAAQA;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAOA,OAAM,UAAU,aAAa;AACpC,QAAAA,OAAM,SAAS,CAAC;AAAA,MACpB;AACA,UAAI,QAAQA,OAAM;AAClB,aAAO,KAAK,KAAK;AACjB,UAAI,SAASA,OAAM,WAAWA,OAAM,QAAQ;AAC5C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACjD,aAAK,aAAa,YAAY,GAAG;AAAA,MACrC,OAAO;AACH,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;AAAA,MAClD;AACA,eAAS,SAAS,GAAG;AACjB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;AAAA,MACpC;AAJS;AAKD,eAAS,MAAM;AACf,YAAI;AACJ,gBAAQ,OAAO,IAAI,KAAKA,OAAM,IAAI,KAAK;AACvC,YAAI,OAAO,UAAU,UAAU;AAC3B,cAAI,iBAAiB,OAAO;AACxB,qBAAS;AACT,oBAAQ,OAAO,IAAI;AAAA,UACvB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,QACpC;AACA,eAAO;AAAA,MACX;AAXa;AAYjB,UAAI,QAAQ,gBAAgB,OAAO,QAAQ,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACT,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC5B,mBAAS,KAAK,eAAe,KAAK;AAAA,QACtC,OAAO;AACH,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACjD,qBAAS,IAAI;AAAA,UACjB;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,QAChD;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AAC/D,cAAI,SAAS;AACb,qBAAW,CAAC;AACZ,eAAK,KAAK,MAAM,KAAK,GAAG;AACpB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AAClC,uBAAS,KAAK,MAAO,KAAK,WAAW,CAAC,IAAI,GAAI;AAAA,YAClD;AAAA,UACJ;AACA,cAAIA,OAAM,cAAc;AACpB,qBAAS,0BAA0B,WAAW,KAAK,QAAQA,OAAM,aAAa,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAc,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UAChL,OAAO;AACH,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAQ,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UACxJ;AACA,eAAK,WAAW,QAAQ;AAAA,YACpB,MAAMA,OAAM;AAAA,YACZ,OAAO,KAAK,WAAW,MAAM,KAAK;AAAA,YAClC,MAAMA,OAAM;AAAA,YACZ,KAAK;AAAA,YACL;AAAA,UACJ,CAAC;AAAA,QACL;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACjD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;AAAA,QACtG;AACA,gBAAQ,OAAO,CAAC,GAAG;AAAA,UACnB,KAAK;AACD,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAKA,OAAM,MAAM;AACxB,mBAAO,KAAKA,OAAM,MAAM;AACxB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACT,gBAAI,CAAC,gBAAgB;AACjB,uBAASA,OAAM;AACf,uBAASA,OAAM;AACf,yBAAWA,OAAM;AACjB,sBAAQA,OAAM;AACd,kBAAI,aAAa,GAAG;AAChB;AAAA,cACJ;AAAA,YACJ,OAAO;AACH,uBAAS;AACT,+BAAiB;AAAA,YACrB;AACA;AAAA,UACJ,KAAK;AACD,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;AAAA,cACP,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,YAC3C;AACA,gBAAI,QAAQ;AACR,oBAAM,GAAG,QAAQ;AAAA,gBACb,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;AAAA,gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,cACrC;AAAA,YACJ;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;AAAA,cAChC;AAAA,cACA;AAAA,cACA;AAAA,cACA,YAAY;AAAA,cACZ,OAAO,CAAC;AAAA,cACR;AAAA,cACA;AAAA,YACJ,EAAE,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC1B,qBAAO;AAAA,YACX;AACA,gBAAI,KAAK;AACL,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AAAA,YACrC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;AAAA,UACJ,KAAK;AACD,mBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX,GA3IO;AAAA,EA2IN;AAGD,MAAI,QAAS,2BAAU;AACvB,QAAIA,SAAS;AAAA,MAEb,KAAI;AAAA,MAEJ,YAAW,gCAAS,WAAW,KAAK,MAAM;AAClC,YAAI,KAAK,GAAG,QAAQ;AAChB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;AAAA,QACvC,OAAO;AACH,gBAAM,IAAI,MAAM,GAAG;AAAA,QACvB;AAAA,MACJ,GANO;AAAA;AAAA,MASX,UAAS,gCAAU,OAAO,IAAI;AACtB,aAAK,KAAK,MAAM,KAAK,MAAM,CAAC;AAC5B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;AAAA,UACV,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,WAAW;AAAA,UACX,aAAa;AAAA,QACjB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,GAAE,CAAC;AAAA,QAC5B;AACA,aAAK,SAAS;AACd,eAAO;AAAA,MACX,GAlBK;AAAA;AAAA,MAqBT,OAAM,kCAAY;AACV,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACP,eAAK;AACL,eAAK,OAAO;AAAA,QAChB,OAAO;AACH,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,MAAM,CAAC;AAAA,QACvB;AAEA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;AAAA,MACX,GApBE;AAAA;AAAA,MAuBN,OAAM,gCAAU,IAAI;AACZ,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AAEpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAE5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAE7D,YAAI,MAAM,SAAS,GAAG;AAClB,eAAK,YAAY,MAAM,SAAS;AAAA,QACpC;AACA,YAAI,IAAI,KAAK,OAAO;AAEpB,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,SACR,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAC5D,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAChE,KAAK,OAAO,eAAe;AAAA,QACjC;AAEA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;AAAA,QACvD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;AAAA,MACX,GAhCE;AAAA;AAAA,MAmCN,MAAK,kCAAY;AACT,aAAK,QAAQ;AACb,eAAO;AAAA,MACX,GAHC;AAAA;AAAA,MAML,QAAO,kCAAY;AACX,YAAI,KAAK,QAAQ,iBAAiB;AAC9B,eAAK,aAAa;AAAA,QACtB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAa,GAAG;AAAA,YAC9N,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QAEL;AACA,eAAO;AAAA,MACX,GAZG;AAAA;AAAA,MAeP,MAAK,gCAAU,GAAG;AACV,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,MAClC,GAFC;AAAA;AAAA,MAKL,WAAU,kCAAY;AACd,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAM,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,MAC7E,GAHM;AAAA;AAAA,MAMV,eAAc,kCAAY;AAClB,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AAClB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAG,KAAK,MAAM;AAAA,QAChD;AACA,gBAAQ,KAAK,OAAO,GAAE,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;AAAA,MAClF,GANU;AAAA;AAAA,MASd,cAAa,kCAAY;AACjB,YAAI,MAAM,KAAK,UAAU;AACzB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAc,IAAI,OAAO,IAAI;AAAA,MACnD,GAJS;AAAA;AAAA,MAOb,YAAW,gCAAS,OAAO,cAAc;AACjC,YAAI,OACA,OACA;AAEJ,YAAI,KAAK,QAAQ,iBAAiB;AAE9B,mBAAS;AAAA,YACL,UAAU,KAAK;AAAA,YACf,QAAQ;AAAA,cACJ,YAAY,KAAK,OAAO;AAAA,cACxB,WAAW,KAAK;AAAA,cAChB,cAAc,KAAK,OAAO;AAAA,cAC1B,aAAa,KAAK,OAAO;AAAA,YAC7B;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,IAAI,KAAK;AAAA,YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;AAAA,YAC3C,MAAM,KAAK;AAAA,UACf;AACA,cAAI,KAAK,QAAQ,QAAQ;AACrB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;AAAA,UACnD;AAAA,QACJ;AAEA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACP,eAAK,YAAY,MAAM;AAAA,QAC3B;AACA,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,QACA,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAC5E,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;AAAA,QACpD;AACA,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;AAAA,QAChE;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC1B,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,OAAO;AACP,iBAAO;AAAA,QACX,WAAW,KAAK,YAAY;AAExB,mBAAS,KAAK,QAAQ;AAClB,iBAAK,CAAC,IAAI,OAAO,CAAC;AAAA,UACtB;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,GArEO;AAAA;AAAA,MAwEX,MAAK,kCAAY;AACT,YAAI,KAAK,MAAM;AACX,iBAAO,KAAK;AAAA,QAChB;AACA,YAAI,CAAC,KAAK,QAAQ;AACd,eAAK,OAAO;AAAA,QAChB;AAEA,YAAI,OACA,OACA,WACA;AACJ,YAAI,CAAC,KAAK,OAAO;AACb,eAAK,SAAS;AACd,eAAK,QAAQ;AAAA,QACjB;AACA,YAAI,QAAQ,KAAK,cAAc;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAChE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAC9B,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACjB,uBAAO;AAAA,cACX,WAAW,KAAK,YAAY;AACxB,wBAAQ;AACR;AAAA,cACJ,OAAO;AAEH,uBAAO;AAAA,cACX;AAAA,YACJ,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC3B;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,OAAO;AACP,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACjB,mBAAO;AAAA,UACX;AAEA,iBAAO;AAAA,QACX;AACA,YAAI,KAAK,WAAW,IAAI;AACpB,iBAAO,KAAK;AAAA,QAChB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAa,GAAG;AAAA,YACpH,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QACL;AAAA,MACJ,GAvDC;AAAA;AAAA,MA0DL,KAAI,gCAAS,MAAO;AACZ,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,GAAG;AACH,iBAAO;AAAA,QACX,OAAO;AACH,iBAAO,KAAK,IAAI;AAAA,QACpB;AAAA,MACJ,GAPA;AAAA;AAAA,MAUJ,OAAM,gCAAS,MAAO,WAAW;AACzB,aAAK,eAAe,KAAK,SAAS;AAAA,MACtC,GAFE;AAAA;AAAA,MAKN,UAAS,gCAAS,WAAY;AACtB,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACP,iBAAO,KAAK,eAAe,IAAI;AAAA,QACnC,OAAO;AACH,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,eAAc,gCAAS,gBAAiB;AAChC,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACnF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;AAAA,QAChF,OAAO;AACH,iBAAO,KAAK,WAAW,SAAS,EAAE;AAAA,QACtC;AAAA,MACJ,GANU;AAAA;AAAA,MASd,UAAS,gCAAS,SAAU,GAAG;AACvB,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACR,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC,OAAO;AACH,iBAAO;AAAA,QACX;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,WAAU,gCAAS,UAAW,WAAW;AACjC,aAAK,MAAM,SAAS;AAAA,MACxB,GAFM;AAAA;AAAA,MAKV,gBAAe,gCAAS,iBAAiB;AACjC,eAAO,KAAK,eAAe;AAAA,MAC/B,GAFW;AAAA,MAGf,SAAS,EAAC,oBAAmB,KAAI;AAAA,MACjC,eAAe,gCAAS,UAAU,IAAG,KAAI,2BAA0B,UAAU;AAC7E,YAAI,UAAQ;AACZ,gBAAO,2BAA2B;AAAA,UAClC,KAAK;AAAG,iBAAK,MAAM,gBAAgB;AAAG,mBAAO;AAC7C;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,WAAW;AAAE,mBAAO;AACvC;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAG,mBAAO;AAChC;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,WAAW;AAAE,mBAAO;AACvC;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAG,mBAAO;AAChC;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,qBAAqB;AACxC;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AACtB;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,MAAM;AACzB;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AACtB;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,cAAc;AACjC;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AACtB;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAG,iBAAK,MAAM,cAAc;AAClD;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AACtB;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,OAAO;AAC1B;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AACtB;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,QACA;AAAA,MACA,GA9Ge;AAAA,MA+Gf,OAAO,CAAC,cAAa,yBAAwB,yBAAwB,yBAAwB,yBAAwB,0BAAyB,cAAa,gBAAe,yBAAwB,wBAAuB,wBAAuB,eAAc,aAAY,iBAAgB,sBAAqB,aAAY,eAAc,mBAAkB,mBAAkB,YAAW,eAAc,YAAW,eAAc,oBAAmB,gBAAe,kBAAiB,iBAAgB,8BAA6B,6BAA4B,mBAAkB,8BAA6B,gCAA+B,4BAA2B,4BAA2B,8BAA6B,4BAA2B,6BAA4B,+BAA8B,8BAA6B,4BAA2B,8BAA6B,4BAA2B,4BAA2B,8BAA6B,8BAA6B,uBAAsB,kCAAiC,yBAAwB,iBAAgB,mBAAkB,WAAU,WAAU,SAAS;AAAA,MACpmC,YAAY,EAAC,uBAAsB,EAAC,SAAQ,CAAC,GAAE,CAAC,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,CAAC,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,CAAC,GAAE,aAAY,MAAK,GAAE,gBAAe,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,gBAAe,EAAC,SAAQ,CAAC,IAAG,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,QAAO,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,SAAQ,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,WAAU,EAAC,SAAQ,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,KAAI,EAAC;AAAA,IACrf;AACA,WAAOA;AAAA,EACP,EAAG;AACH,EAAAD,QAAO,QAAQ;AACf,WAAS,SAAU;AACjB,SAAK,KAAK,CAAC;AAAA,EACb;AAFS;AAGT,SAAO,YAAYA;AAAO,EAAAA,QAAO,SAAS;AAC1C,SAAO,IAAI;AACX,EAAG;AACF,OAAO,SAAS;AAEhB,IAAO,gBAAQ;;;AChyBhB,SAAS,mBAAmB;AAC5B,OAAO,WAAW;AAClB,OAAO,kBAAkB;AACzB,OAAO,4BAA4B;AACnC,OAAO,yBAAyB;AAehC,MAAM,OAAO,YAAY;AACzB,MAAM,OAAO,sBAAsB;AACnC,MAAM,OAAO,mBAAmB;AAEhC,IAAM,oBAAoB,EAAE,QAAQ,GAAG,UAAU,EAAE;AACnD,IAAI,aAAa;AACjB,IAAI,aAAa;AACjB,IAAI,eAAe;AACnB,IAAI,cAAc;AAClB,IAAI,WAAW,CAAC;AAChB,IAAI,WAAW,CAAC;AAChB,IAAI,QAAQ,oBAAI,IAAI;AACpB,IAAI,WAAW,CAAC;AAChB,IAAI,QAAQ,CAAC;AACb,IAAI,iBAAiB;AACrB,IAAI,cAAc;AAClB,IAAM,OAAO,CAAC,UAAU,QAAQ,QAAQ,WAAW;AACnD,IAAI,OAAO,CAAC;AACZ,IAAI,oBAAoB;AACxB,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,UAAU;AAGd,IAAI,YAAY;AAET,IAAME,SAAQ,kCAAY;AAC/B,aAAW,CAAC;AACZ,UAAQ,CAAC;AACT,mBAAiB;AACjB,SAAO,CAAC;AACR,YAAU;AACV,aAAW;AACX,eAAa;AACb,aAAW,CAAC;AACZ,eAAa;AACb,eAAa;AACb,gBAAc;AACd,iBAAe;AACf,gBAAc;AACd,aAAW,CAAC;AACZ,aAAW,CAAC;AACZ,sBAAoB;AACpB,YAAU;AACV,cAAY;AACZ,UAAQ,oBAAI,IAAI;AAChB,QAAY;AACZ,YAAU;AACV,YAAU;AACZ,GAvBqB;AAyBd,IAAM,gBAAgB,gCAAU,KAAK;AAC1C,eAAa;AACf,GAF6B;AAItB,IAAM,gBAAgB,kCAAY;AACvC,SAAO;AACT,GAF6B;AAItB,IAAM,kBAAkB,gCAAU,KAAK;AAC5C,iBAAe;AACjB,GAF+B;AAIxB,IAAM,kBAAkB,kCAAY;AACzC,SAAO;AACT,GAF+B;AAIxB,IAAM,iBAAiB,gCAAU,KAAK;AAC3C,gBAAc;AAChB,GAF8B;AAIvB,IAAM,iBAAiB,kCAAY;AACxC,SAAO;AACT,GAF8B;AAIvB,IAAM,gBAAgB,gCAAU,KAAK;AAC1C,eAAa;AACf,GAF6B;AAItB,IAAM,0BAA0B,kCAAY;AACjD,sBAAoB;AACtB,GAFuC;AAIhC,IAAM,uBAAuB,kCAAY;AAC9C,SAAO;AACT,GAFoC;AAI7B,IAAM,gBAAgB,kCAAY;AACvC,YAAU;AACZ,GAF6B;AAItB,IAAM,iBAAiB,kCAAY;AACxC,SAAO;AACT,GAF8B;AAIvB,IAAM,iBAAiB,gCAAU,KAAK;AAC3C,gBAAc;AAChB,GAF8B;AAIvB,IAAM,iBAAiB,kCAAY;AACxC,SAAO;AACT,GAF8B;AAIvB,IAAM,gBAAgB,kCAAY;AACvC,SAAO;AACT,GAF6B;AAItB,IAAM,cAAc,gCAAU,KAAK;AACxC,aAAW,IAAI,YAAY,EAAE,MAAM,QAAQ;AAC7C,GAF2B;AAIpB,IAAM,cAAc,kCAAY;AACrC,SAAO;AACT,GAF2B;AAGpB,IAAM,cAAc,gCAAU,KAAK;AACxC,aAAW,IAAI,YAAY,EAAE,MAAM,QAAQ;AAC7C,GAF2B;AAIpB,IAAM,cAAc,kCAAY;AACrC,SAAO;AACT,GAF2B;AAIpB,IAAM,WAAW,kCAAY;AAClC,SAAO;AACT,GAFwB;AAIjB,IAAM,aAAa,gCAAU,KAAK;AACvC,mBAAiB;AACjB,WAAS,KAAK,GAAG;AACnB,GAH0B;AAKnB,IAAM,cAAc,kCAAY;AACrC,SAAO;AACT,GAF2B;AAIpB,IAAM,WAAW,kCAAY;AAClC,MAAI,oBAAoB,aAAa;AACrC,QAAM,WAAW;AACjB,MAAI,iBAAiB;AACrB,SAAO,CAAC,qBAAqB,iBAAiB,UAAU;AACtD,wBAAoB,aAAa;AACjC;AAAA,EACF;AAEA,UAAQ;AAER,SAAO;AACT,GAZwB;AAcjB,IAAM,gBAAgB,gCAAU,MAAMC,aAAYC,WAAUC,WAAU;AAC3E,MAAIA,UAAS,SAAS,KAAK,OAAOF,YAAW,KAAK,CAAC,CAAC,GAAG;AACrD,WAAO;AAAA,EACT;AACA,MACEC,UAAS,SAAS,UAAU,MAC3B,KAAK,WAAW,MAAM,kBAAkB,OAAO,KAC9C,KAAK,WAAW,MAAM,kBAAkB,OAAO,IAAI,IACrD;AACA,WAAO;AAAA,EACT;AACA,MAAIA,UAAS,SAAS,KAAK,OAAO,MAAM,EAAE,YAAY,CAAC,GAAG;AACxD,WAAO;AAAA,EACT;AACA,SAAOA,UAAS,SAAS,KAAK,OAAOD,YAAW,KAAK,CAAC,CAAC;AACzD,GAf6B;AAiBtB,IAAM,aAAa,gCAAU,KAAK;AACvC,YAAU;AACZ,GAF0B;AAInB,IAAM,aAAa,kCAAY;AACpC,SAAO;AACT,GAF0B;AAInB,IAAM,aAAa,gCAAU,UAAU;AAC5C,YAAU;AACZ,GAF0B;AAgB1B,IAAM,iBAAiB,gCAAU,MAAMA,aAAYC,WAAUC,WAAU;AACrE,MAAI,CAACD,UAAS,UAAU,KAAK,eAAe;AAC1C;AAAA,EACF;AACA,MAAI;AACJ,MAAI,KAAK,qBAAqB,MAAM;AAClC,gBAAY,MAAM,KAAK,SAAS;AAAA,EAClC,OAAO;AACL,gBAAY,MAAM,KAAK,WAAWD,aAAY,IAAI;AAAA,EACpD;AACA,cAAY,UAAU,IAAI,GAAG,GAAG;AAEhC,MAAI;AACJ,MAAI,KAAK,mBAAmB,MAAM;AAChC,sBAAkB,MAAM,KAAK,OAAO;AAAA,EACtC,OAAO;AACL,sBAAkB,MAAM,KAAK,SAASA,aAAY,IAAI;AAAA,EACxD;AACA,QAAM,CAAC,cAAc,aAAa,IAAI;AAAA,IACpC;AAAA,IACA;AAAA,IACAA;AAAA,IACAC;AAAA,IACAC;AAAA,EACF;AACA,OAAK,UAAU,aAAa,OAAO;AACnC,OAAK,gBAAgB;AACvB,GA3BuB;AAwCvB,IAAM,eAAe,gCAAU,WAAW,SAASF,aAAYC,WAAUC,WAAU;AACjF,MAAI,UAAU;AACd,MAAI,gBAAgB;AACpB,SAAO,aAAa,SAAS;AAC3B,QAAI,CAAC,SAAS;AACZ,sBAAgB,QAAQ,OAAO;AAAA,IACjC;AACA,cAAU,cAAc,WAAWF,aAAYC,WAAUC,SAAQ;AACjE,QAAI,SAAS;AACX,gBAAU,QAAQ,IAAI,GAAG,GAAG;AAAA,IAC9B;AACA,gBAAY,UAAU,IAAI,GAAG,GAAG;AAAA,EAClC;AACA,SAAO,CAAC,SAAS,aAAa;AAChC,GAdqB;AAgBrB,IAAM,eAAe,gCAAU,UAAUF,aAAY,KAAK;AACxD,QAAM,IAAI,KAAK;AAGf,QAAM,iBAAiB;AACvB,QAAM,iBAAiB,eAAe,KAAK,GAAG;AAE9C,MAAI,mBAAmB,MAAM;AAE3B,QAAI,aAAa;AACjB,eAAW,MAAM,eAAe,OAAO,IAAI,MAAM,GAAG,GAAG;AACrD,UAAI,OAAO,aAAa,EAAE;AAC1B,UAAI,SAAS,WAAc,CAAC,cAAc,KAAK,UAAU,WAAW,UAAU;AAC5E,qBAAa;AAAA,MACf;AAAA,IACF;AAEA,QAAI,YAAY;AACd,aAAO,WAAW;AAAA,IACpB;AACA,UAAM,QAAQ,oBAAI,KAAK;AACvB,UAAM,SAAS,GAAG,GAAG,GAAG,CAAC;AACzB,WAAO;AAAA,EACT;AAGA,MAAI,QAAQ,MAAM,KAAKA,YAAW,KAAK,GAAG,IAAI;AAC9C,MAAI,MAAM,QAAQ,GAAG;AACnB,WAAO,MAAM,OAAO;AAAA,EACtB,OAAO;AACL,QAAI,MAAM,kBAAkB,GAAG;AAC/B,QAAI,MAAM,sBAAsBA,YAAW,KAAK,CAAC;AACjD,UAAM,IAAI,IAAI,KAAK,GAAG;AACtB,QACE,MAAM,UACN,MAAM,EAAE,QAAQ,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,IAMjB,EAAE,YAAY,IAAI,QAClB,EAAE,YAAY,IAAI,KAClB;AACA,YAAM,IAAI,MAAM,kBAAkB,GAAG;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AACF,GAhDqB;AAwErB,IAAM,gBAAgB,gCAAU,KAAK;AAEnC,QAAM,YAAY,kCAAkC,KAAK,IAAI,KAAK,CAAC;AACnE,MAAI,cAAc,MAAM;AACtB,WAAO,CAAC,OAAO,WAAW,UAAU,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;AAAA,EACvD;AAEA,SAAO,CAAC,KAAK,IAAI;AACnB,GARsB;AAUtB,IAAM,aAAa,gCAAU,UAAUA,aAAY,KAAK,YAAY,OAAO;AACzE,QAAM,IAAI,KAAK;AAGf,QAAM,iBAAiB;AACvB,QAAM,iBAAiB,eAAe,KAAK,GAAG;AAE9C,MAAI,mBAAmB,MAAM;AAE3B,QAAI,eAAe;AACnB,eAAW,MAAM,eAAe,OAAO,IAAI,MAAM,GAAG,GAAG;AACrD,UAAI,OAAO,aAAa,EAAE;AAC1B,UAAI,SAAS,WAAc,CAAC,gBAAgB,KAAK,YAAY,aAAa,YAAY;AACpF,uBAAe;AAAA,MACjB;AAAA,IACF;AAEA,QAAI,cAAc;AAChB,aAAO,aAAa;AAAA,IACtB;AACA,UAAM,QAAQ,oBAAI,KAAK;AACvB,UAAM,SAAS,GAAG,GAAG,GAAG,CAAC;AACzB,WAAO;AAAA,EACT;AAGA,MAAI,aAAa,MAAM,KAAKA,YAAW,KAAK,GAAG,IAAI;AACnD,MAAI,WAAW,QAAQ,GAAG;AACxB,QAAI,WAAW;AACb,mBAAa,WAAW,IAAI,GAAG,GAAG;AAAA,IACpC;AACA,WAAO,WAAW,OAAO;AAAA,EAC3B;AAEA,MAAI,UAAU,MAAM,QAAQ;AAC5B,QAAM,CAAC,eAAe,YAAY,IAAI,cAAc,GAAG;AACvD,MAAI,CAAC,OAAO,MAAM,aAAa,GAAG;AAChC,UAAM,aAAa,QAAQ,IAAI,eAAe,YAAY;AAC1D,QAAI,WAAW,QAAQ,GAAG;AACxB,gBAAU;AAAA,IACZ;AAAA,EACF;AACA,SAAO,QAAQ,OAAO;AACxB,GA3CmB;AA6CnB,IAAI,UAAU;AACd,IAAM,UAAU,gCAAU,OAAO;AAC/B,MAAI,UAAU,QAAW;AACvB,cAAU,UAAU;AACpB,WAAO,SAAS;AAAA,EAClB;AACA,SAAO;AACT,GANgB;AAkBhB,IAAM,cAAc,gCAAU,UAAU,SAAS;AAC/C,MAAI;AAEJ,MAAI,QAAQ,OAAO,GAAG,CAAC,MAAM,KAAK;AAChC,SAAK,QAAQ,OAAO,GAAG,QAAQ,MAAM;AAAA,EACvC,OAAO;AACL,SAAK;AAAA,EACP;AAEA,QAAM,OAAO,GAAG,MAAM,GAAG;AAEzB,QAAM,OAAO,CAAC;AAGd,cAAY,MAAM,MAAM,IAAI;AAE5B,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,SAAK,CAAC,IAAI,KAAK,CAAC,EAAE,KAAK;AAAA,EACzB;AAEA,MAAI,cAAc;AAClB,UAAQ,KAAK,QAAQ;AAAA,IACnB,KAAK;AACH,WAAK,KAAK,QAAQ;AAClB,WAAK,YAAY,SAAS;AAC1B,oBAAc,KAAK,CAAC;AACpB;AAAA,IACF,KAAK;AACH,WAAK,KAAK,QAAQ;AAClB,WAAK,YAAY,aAAa,QAAW,YAAY,KAAK,CAAC,CAAC;AAC5D,oBAAc,KAAK,CAAC;AACpB;AAAA,IACF,KAAK;AACH,WAAK,KAAK,QAAQ,KAAK,CAAC,CAAC;AACzB,WAAK,YAAY,aAAa,QAAW,YAAY,KAAK,CAAC,CAAC;AAC5D,oBAAc,KAAK,CAAC;AACpB;AAAA,IACF;AAAA,EACF;AAEA,MAAI,aAAa;AACf,SAAK,UAAU,WAAW,KAAK,WAAW,YAAY,aAAa,iBAAiB;AACpF,SAAK,gBAAgB,MAAM,aAAa,cAAc,IAAI,EAAE,QAAQ;AACpE,mBAAe,MAAM,YAAY,UAAU,QAAQ;AAAA,EACrD;AAEA,SAAO;AACT,GA/CoB;AAiDpB,IAAM,YAAY,gCAAU,YAAY,SAAS;AAC/C,MAAI;AACJ,MAAI,QAAQ,OAAO,GAAG,CAAC,MAAM,KAAK;AAChC,SAAK,QAAQ,OAAO,GAAG,QAAQ,MAAM;AAAA,EACvC,OAAO;AACL,SAAK;AAAA,EACP;AAEA,QAAM,OAAO,GAAG,MAAM,GAAG;AAEzB,QAAM,OAAO,CAAC;AAGd,cAAY,MAAM,MAAM,IAAI;AAE5B,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,SAAK,CAAC,IAAI,KAAK,CAAC,EAAE,KAAK;AAAA,EACzB;AAEA,UAAQ,KAAK,QAAQ;AAAA,IACnB,KAAK;AACH,WAAK,KAAK,QAAQ;AAClB,WAAK,YAAY;AAAA,QACf,MAAM;AAAA,QACN,IAAI;AAAA,MACN;AACA,WAAK,UAAU;AAAA,QACb,MAAM,KAAK,CAAC;AAAA,MACd;AACA;AAAA,IACF,KAAK;AACH,WAAK,KAAK,QAAQ;AAClB,WAAK,YAAY;AAAA,QACf,MAAM;AAAA,QACN,WAAW,KAAK,CAAC;AAAA,MACnB;AACA,WAAK,UAAU;AAAA,QACb,MAAM,KAAK,CAAC;AAAA,MACd;AACA;AAAA,IACF,KAAK;AACH,WAAK,KAAK,QAAQ,KAAK,CAAC,CAAC;AACzB,WAAK,YAAY;AAAA,QACf,MAAM;AAAA,QACN,WAAW,KAAK,CAAC;AAAA,MACnB;AACA,WAAK,UAAU;AAAA,QACb,MAAM,KAAK,CAAC;AAAA,MACd;AACA;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT,GAtDkB;AAwDlB,IAAI;AACJ,IAAI;AACJ,IAAI,WAAW,CAAC;AAChB,IAAM,SAAS,CAAC;AACT,IAAM,UAAU,gCAAU,OAAO,MAAM;AAC5C,QAAM,UAAU;AAAA,IACd,SAAS;AAAA,IACT,MAAM;AAAA,IACN,WAAW;AAAA,IACX,eAAe;AAAA,IACf,eAAe;AAAA,IACf,KAAK,EAAE,KAAW;AAAA,IAClB,MAAM;AAAA,IACN,SAAS,CAAC;AAAA,EACZ;AACA,QAAM,WAAW,UAAU,YAAY,IAAI;AAC3C,UAAQ,IAAI,YAAY,SAAS;AACjC,UAAQ,IAAI,UAAU,SAAS;AAC/B,UAAQ,KAAK,SAAS;AACtB,UAAQ,aAAa;AACrB,UAAQ,SAAS,SAAS;AAC1B,UAAQ,OAAO,SAAS;AACxB,UAAQ,OAAO,SAAS;AACxB,UAAQ,YAAY,SAAS;AAC7B,UAAQ,QAAQ;AAEhB;AAEA,QAAM,MAAM,SAAS,KAAK,OAAO;AAEjC,eAAa,QAAQ;AAErB,SAAO,QAAQ,EAAE,IAAI,MAAM;AAC7B,GA7BuB;AA+BhB,IAAM,eAAe,gCAAU,IAAI;AACxC,QAAM,MAAM,OAAO,EAAE;AACrB,SAAO,SAAS,GAAG;AACrB,GAH4B;AAKrB,IAAM,aAAa,gCAAU,OAAO,MAAM;AAC/C,QAAM,UAAU;AAAA,IACd,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS,CAAC;AAAA,EACZ;AACA,QAAM,WAAW,YAAY,UAAU,IAAI;AAC3C,UAAQ,YAAY,SAAS;AAC7B,UAAQ,UAAU,SAAS;AAC3B,UAAQ,KAAK,SAAS;AACtB,UAAQ,SAAS,SAAS;AAC1B,UAAQ,OAAO,SAAS;AACxB,UAAQ,OAAO,SAAS;AACxB,UAAQ,YAAY,SAAS;AAC7B,aAAW;AACX,QAAM,KAAK,OAAO;AACpB,GAlB0B;AAoB1B,IAAM,eAAe,kCAAY;AAC/B,QAAM,cAAc,gCAAU,KAAK;AACjC,UAAM,OAAO,SAAS,GAAG;AACzB,QAAI,YAAY;AAChB,YAAQ,SAAS,GAAG,EAAE,IAAI,UAAU,MAAM;AAAA,MACxC,KAAK,eAAe;AAClB,cAAM,WAAW,aAAa,KAAK,UAAU;AAC7C,aAAK,YAAY,SAAS;AAC1B;AAAA,MACF;AAAA,MACA,KAAK;AACH,oBAAY,aAAa,QAAW,YAAY,SAAS,GAAG,EAAE,IAAI,UAAU,SAAS;AACrF,YAAI,WAAW;AACb,mBAAS,GAAG,EAAE,YAAY;AAAA,QAC5B;AACA;AAAA,IACJ;AAEA,QAAI,SAAS,GAAG,EAAE,WAAW;AAC3B,eAAS,GAAG,EAAE,UAAU;AAAA,QACtB,SAAS,GAAG,EAAE;AAAA,QACd;AAAA,QACA,SAAS,GAAG,EAAE,IAAI,QAAQ;AAAA,QAC1B;AAAA,MACF;AACA,UAAI,SAAS,GAAG,EAAE,SAAS;AACzB,iBAAS,GAAG,EAAE,YAAY;AAC1B,iBAAS,GAAG,EAAE,gBAAgB;AAAA,UAC5B,SAAS,GAAG,EAAE,IAAI,QAAQ;AAAA,UAC1B;AAAA,UACA;AAAA,QACF,EAAE,QAAQ;AACV,uBAAe,SAAS,GAAG,GAAG,YAAY,UAAU,QAAQ;AAAA,MAC9D;AAAA,IACF;AAEA,WAAO,SAAS,GAAG,EAAE;AAAA,EACvB,GApCoB;AAsCpB,MAAI,eAAe;AACnB,aAAW,CAAC,GAAG,OAAO,KAAK,SAAS,QAAQ,GAAG;AAC7C,gBAAY,CAAC;AAEb,mBAAe,gBAAgB,QAAQ;AAAA,EACzC;AACA,SAAO;AACT,GA9CqB;AAsDd,IAAM,UAAU,gCAAU,KAAK,UAAU;AAC9C,MAAI,UAAU;AACd,MAAI,UAAU,EAAE,kBAAkB,SAAS;AACzC,cAAU,YAAY,QAAQ;AAAA,EAChC;AACA,MAAI,MAAM,GAAG,EAAE,QAAQ,SAAU,IAAI;AACnC,QAAI,UAAU,aAAa,EAAE;AAC7B,QAAI,YAAY,QAAW;AACzB,cAAQ,IAAI,MAAM;AAChB,eAAO,KAAK,SAAS,OAAO;AAAA,MAC9B,CAAC;AACD,YAAM,IAAI,IAAI,OAAO;AAAA,IACvB;AAAA,EACF,CAAC;AACD,WAAS,KAAK,WAAW;AAC3B,GAfuB;AAuBhB,IAAM,WAAW,gCAAU,KAAK,WAAW;AAChD,MAAI,MAAM,GAAG,EAAE,QAAQ,SAAU,IAAI;AACnC,QAAI,UAAU,aAAa,EAAE;AAC7B,QAAI,YAAY,QAAW;AACzB,cAAQ,QAAQ,KAAK,SAAS;AAAA,IAChC;AAAA,EACF,CAAC;AACH,GAPwB;AASxB,IAAM,cAAc,gCAAU,IAAI,cAAc,cAAc;AAC5D,MAAI,UAAU,EAAE,kBAAkB,SAAS;AACzC;AAAA,EACF;AACA,MAAI,iBAAiB,QAAW;AAC9B;AAAA,EACF;AAEA,MAAI,UAAU,CAAC;AACf,MAAI,OAAO,iBAAiB,UAAU;AAEpC,cAAU,aAAa,MAAM,+BAA+B;AAC5D,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAI,OAAO,QAAQ,CAAC,EAAE,KAAK;AAG3B,UAAI,KAAK,WAAW,GAAG,KAAK,KAAK,SAAS,GAAG,GAAG;AAC9C,eAAO,KAAK,OAAO,GAAG,KAAK,SAAS,CAAC;AAAA,MACvC;AACA,cAAQ,CAAC,IAAI;AAAA,IACf;AAAA,EACF;AAGA,MAAI,QAAQ,WAAW,GAAG;AACxB,YAAQ,KAAK,EAAE;AAAA,EACjB;AAEA,MAAI,UAAU,aAAa,EAAE;AAC7B,MAAI,YAAY,QAAW;AACzB,YAAQ,IAAI,MAAM;AAChB,oBAAM,QAAQ,cAAc,GAAG,OAAO;AAAA,IACxC,CAAC;AAAA,EACH;AACF,GAlCoB;AA2CpB,IAAM,UAAU,gCAAU,IAAI,kBAAkB;AAC9C,OAAK;AAAA,IACH,WAAY;AAEV,YAAM,OAAO,SAAS,cAAc,QAAQ,EAAE,IAAI;AAClD,UAAI,SAAS,MAAM;AACjB,aAAK,iBAAiB,SAAS,WAAY;AACzC,2BAAiB;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,WAAY;AAEV,YAAM,OAAO,SAAS,cAAc,QAAQ,EAAE,SAAS;AACvD,UAAI,SAAS,MAAM;AACjB,aAAK,iBAAiB,SAAS,WAAY;AACzC,2BAAiB;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF,GArBgB;AA8BT,IAAM,gBAAgB,gCAAU,KAAK,cAAc,cAAc;AACtE,MAAI,MAAM,GAAG,EAAE,QAAQ,SAAU,IAAI;AACnC,gBAAY,IAAI,cAAc,YAAY;AAAA,EAC5C,CAAC;AACD,WAAS,KAAK,WAAW;AAC3B,GAL6B;AAYtB,IAAM,gBAAgB,gCAAU,SAAS;AAC9C,OAAK,QAAQ,SAAU,KAAK;AAC1B,QAAI,OAAO;AAAA,EACb,CAAC;AACH,GAJ6B;AAM7B,IAAO,kBAAQ;AAAA,EACb,WAAW,6BAAM,UAAU,EAAE,OAAlB;AAAA,EACX,OAAAD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAOA,SAAS,YAAY,MAAM,MAAMI,OAAM;AACrC,MAAI,aAAa;AACjB,SAAO,YAAY;AACjB,iBAAa;AACb,IAAAA,MAAK,QAAQ,SAAU,GAAG;AACxB,YAAM,UAAU,UAAU,IAAI;AAC9B,YAAM,QAAQ,IAAI,OAAO,OAAO;AAChC,UAAI,KAAK,CAAC,EAAE,MAAM,KAAK,GAAG;AACxB,aAAK,CAAC,IAAI;AACV,aAAK,MAAM,CAAC;AACZ,qBAAa;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAdS;;;AClyBT,OAAOC,YAAW;AAElB;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAKA,IAAM,UAAU,kCAAY;AACjC,MAAI,MAAM,gDAAgD;AAC5D,GAFuB;AAQvB,IAAM,2BAA2B;AAAA,EAC/B,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,QAAQ;AACV;AAaA,IAAM,sBAAsB,wBAACC,QAAO,gBAAgB;AAClD,MAAI,WAAW,CAAC,GAAGA,MAAK,EAAE,IAAI,MAAM,SAAS;AAC7C,MAAI,SAAS,CAAC,GAAGA,MAAK,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK;AACrF,MAAI,mBAAmB;AACvB,aAAW,WAAW,QAAQ;AAC5B,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,UAAI,QAAQ,aAAa,SAAS,CAAC,GAAG;AACpC,iBAAS,CAAC,IAAI,QAAQ;AACtB,gBAAQ,QAAQ,IAAI;AACpB,YAAI,IAAI,kBAAkB;AACxB,6BAAmB;AAAA,QACrB;AACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT,GAlB4B;AAoB5B,IAAI;AACG,IAAM,OAAO,gCAAU,MAAM,IAAI,SAAS,SAAS;AACxD,QAAM,OAAO,UAAU,EAAE;AAEzB,QAAM,gBAAgB,UAAU,EAAE;AAElC,MAAI;AACJ,MAAI,kBAAkB,WAAW;AAC/B,qBAAiB,OAAO,OAAO,EAAE;AAAA,EACnC;AACA,QAAM,OACJ,kBAAkB,YACd,OAAO,eAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,IACrD,OAAO,MAAM;AACnB,QAAM,MAAM,kBAAkB,YAAY,eAAe,MAAM,EAAE,CAAC,EAAE,kBAAkB;AAEtF,QAAM,OAAO,IAAI,eAAe,EAAE;AAClC,MAAI,KAAK,cAAc;AAEvB,MAAI,MAAM,QAAW;AACnB,QAAI;AAAA,EACN;AAEA,MAAI,KAAK,aAAa,QAAW;AAC/B,QAAI,KAAK;AAAA,EACX;AAEA,QAAM,YAAY,QAAQ,GAAG,SAAS;AAItC,MAAI,aAAa,CAAC;AAElB,aAAW,WAAW,WAAW;AAC/B,eAAW,KAAK,QAAQ,IAAI;AAAA,EAC9B;AAEA,eAAa,YAAY,UAAU;AACnC,QAAM,kBAAkB,CAAC;AAEzB,MAAI,IAAI,IAAI,KAAK;AACjB,MAAI,QAAQ,GAAG,eAAe,MAAM,aAAa,KAAK,gBAAgB,WAAW;AAC/E,UAAM,mBAAmB,CAAC;AAC1B,eAAW,WAAW,WAAW;AAC/B,UAAI,iBAAiB,QAAQ,OAAO,MAAM,QAAW;AACnD,yBAAiB,QAAQ,OAAO,IAAI,CAAC,OAAO;AAAA,MAC9C,OAAO;AACL,yBAAiB,QAAQ,OAAO,EAAE,KAAK,OAAO;AAAA,MAChD;AAAA,IACF;AAEA,QAAI,gBAAgB;AACpB,eAAW,YAAY,OAAO,KAAK,gBAAgB,GAAG;AACpD,YAAM,iBAAiB,oBAAoB,iBAAiB,QAAQ,GAAG,aAAa,IAAI;AACxF,uBAAiB;AACjB,WAAK,kBAAkB,KAAK,YAAY,KAAK;AAC7C,sBAAgB,QAAQ,IAAI;AAAA,IAC9B;AAAA,EACF,OAAO;AACL,SAAK,UAAU,UAAU,KAAK,YAAY,KAAK;AAC/C,eAAW,YAAY,YAAY;AACjC,sBAAgB,QAAQ,IAAI,UAAU,OAAO,CAAC,SAAS,KAAK,SAAS,QAAQ,EAAE;AAAA,IACjF;AAAA,EACF;AAGA,OAAK,aAAa,WAAW,SAAS,IAAI,MAAM,CAAC;AACjD,QAAM,MAAM,KAAK,OAAO,QAAQ,EAAE,IAAI;AAGtC,QAAM,YAAY,UAAU,EACzB,OAAO;AAAA,IACN,IAAI,WAAW,SAAU,GAAG;AAC1B,aAAO,EAAE;AAAA,IACX,CAAC;AAAA,IACD,IAAI,WAAW,SAAU,GAAG;AAC1B,aAAO,EAAE;AAAA,IACX,CAAC;AAAA,EACH,CAAC,EACA,WAAW,CAAC,GAAG,IAAI,KAAK,cAAc,KAAK,YAAY,CAAC;AAM3D,WAAS,YAAY,GAAG,GAAG;AACzB,UAAM,QAAQ,EAAE;AAChB,UAAM,QAAQ,EAAE;AAChB,QAAI,SAAS;AACb,QAAI,QAAQ,OAAO;AACjB,eAAS;AAAA,IACX,WAAW,QAAQ,OAAO;AACxB,eAAS;AAAA,IACX;AACA,WAAO;AAAA,EACT;AAVS;AAcT,YAAU,KAAK,WAAW;AAE1B,YAAU,WAAW,GAAG,CAAC;AAEzB,mBAAiB,KAAK,GAAG,GAAG,KAAK,WAAW;AAE5C,MACG,OAAO,MAAM,EACb,KAAK,QAAQ,GAAG,gBAAgB,CAAC,EACjC,KAAK,KAAK,IAAI,CAAC,EACf,KAAK,KAAK,KAAK,cAAc,EAC7B,KAAK,SAAS,WAAW;AAO5B,WAAS,UAAUA,QAAO,WAAW,YAAY;AAC/C,UAAM,YAAY,KAAK;AACvB,UAAM,MAAM,YAAY,KAAK;AAC7B,UAAM,aAAa,KAAK;AACxB,UAAM,cAAc,KAAK;AAEzB,UAAM,aAAa,YAAY,EAC5B,OAAO,CAAC,GAAG,WAAW,MAAM,CAAC,EAC7B,MAAM,CAAC,WAAW,SAAS,CAAC,EAC5B,YAAY,cAAc;AAE7B;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACAA;AAAA,MACA,QAAQ,GAAG,YAAY;AAAA,MACvB,QAAQ,GAAG,YAAY;AAAA,IACzB;AACA,aAAS,aAAa,YAAY,WAAW,UAAU;AACvD,cAAUA,QAAO,KAAK,YAAY,aAAa,WAAW,YAAY,WAAW,UAAU;AAC3F,eAAW,KAAK,YAAY,aAAa,WAAW,UAAU;AAC9D,cAAU,aAAa,YAAY,WAAW,UAAU;AAAA,EAC1D;AAzBS;AAoCT,WAAS,UAAU,UAAU,QAAQ,WAAW,YAAY,cAAc,eAAeC,IAAG;AAE1F,UAAM,qBAAqB,CAAC,GAAG,IAAI,IAAI,SAAS,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC;AAC1E,UAAM,cAAc,mBAAmB,IAAI,CAACC,QAAO,SAAS,KAAK,CAAC,SAAS,KAAK,UAAUA,GAAE,CAAC;AAG7F,QACG,OAAO,GAAG,EACV,UAAU,MAAM,EAChB,KAAK,WAAW,EAChB,MAAM,EACN,OAAO,MAAM,EACb,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,SAAU,GAAG,GAAG;AAEzB,UAAI,EAAE;AACN,aAAO,IAAI,SAAS,YAAY;AAAA,IAClC,CAAC,EACA,KAAK,SAAS,WAAY;AACzB,aAAOD,KAAI,KAAK,eAAe;AAAA,IACjC,CAAC,EACA,KAAK,UAAU,MAAM,EACrB,KAAK,SAAS,SAAU,GAAG;AAC1B,iBAAW,CAAC,GAAG,QAAQ,KAAK,WAAW,QAAQ,GAAG;AAChD,YAAI,EAAE,SAAS,UAAU;AACvB,iBAAO,oBAAqB,IAAI,KAAK;AAAA,QACvC;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AAGH,UAAM,aAAa,IAAI,OAAO,GAAG,EAAE,UAAU,MAAM,EAAE,KAAK,QAAQ,EAAE,MAAM;AAE1E,UAAME,SAAQ,QAAQ,GAAG,SAAS;AAIlC,eACG,OAAO,MAAM,EACb,KAAK,MAAM,SAAU,GAAG;AACvB,aAAO,EAAE;AAAA,IACX,CAAC,EACA,KAAK,MAAM,CAAC,EACZ,KAAK,MAAM,CAAC,EACZ,KAAK,KAAK,SAAU,GAAG;AACtB,UAAI,EAAE,WAAW;AACf,eACE,UAAU,EAAE,SAAS,IACrB,aACA,OAAO,UAAU,EAAE,OAAO,IAAI,UAAU,EAAE,SAAS,KACnD,MAAM;AAAA,MAEV;AACA,aAAO,UAAU,EAAE,SAAS,IAAI;AAAA,IAClC,CAAC,EACA,KAAK,KAAK,SAAU,GAAG,GAAG;AAEzB,UAAI,EAAE;AACN,aAAO,IAAI,SAAS;AAAA,IACtB,CAAC,EACA,KAAK,SAAS,SAAU,GAAG;AAC1B,UAAI,EAAE,WAAW;AACf,eAAO;AAAA,MACT;AACA,aAAO,UAAU,EAAE,iBAAiB,EAAE,OAAO,IAAI,UAAU,EAAE,SAAS;AAAA,IACxE,CAAC,EACA,KAAK,UAAU,YAAY,EAC3B,KAAK,oBAAoB,SAAU,GAAG,GAAG;AAExC,UAAI,EAAE;AAEN,cAEI,UAAU,EAAE,SAAS,IACrB,aACA,OAAO,UAAU,EAAE,OAAO,IAAI,UAAU,EAAE,SAAS,IACnD,SAAS,IACX,SACC,IAAI,SAAS,YAAY,MAAM,cAAc,SAAS,IACvD;AAAA,IAEJ,CAAC,EACA,KAAK,SAAS,SAAU,GAAG;AAC1B,YAAM,MAAM;AAEZ,UAAI,WAAW;AACf,UAAI,EAAE,QAAQ,SAAS,GAAG;AACxB,mBAAW,EAAE,QAAQ,KAAK,GAAG;AAAA,MAC/B;AAEA,UAAI,SAAS;AACb,iBAAW,CAAC,GAAG,QAAQ,KAAK,WAAW,QAAQ,GAAG;AAChD,YAAI,EAAE,SAAS,UAAU;AACvB,mBAAS,IAAI,KAAK;AAAA,QACpB;AAAA,MACF;AAEA,UAAI,YAAY;AAChB,UAAI,EAAE,QAAQ;AACZ,YAAI,EAAE,MAAM;AACV,uBAAa;AAAA,QACf,OAAO;AACL,sBAAY;AAAA,QACd;AAAA,MACF,WAAW,EAAE,MAAM;AACjB,YAAI,EAAE,MAAM;AACV,sBAAY;AAAA,QACd,OAAO;AACL,sBAAY;AAAA,QACd;AAAA,MACF,OAAO;AACL,YAAI,EAAE,MAAM;AACV,uBAAa;AAAA,QACf;AAAA,MACF;AAEA,UAAI,UAAU,WAAW,GAAG;AAC1B,oBAAY;AAAA,MACd;AAEA,UAAI,EAAE,WAAW;AACf,oBAAY,gBAAgB;AAAA,MAC9B;AAEA,mBAAa;AAEb,mBAAa,MAAM;AAEnB,aAAO,MAAM;AAAA,IACf,CAAC;AAGH,eACG,OAAO,MAAM,EACb,KAAK,MAAM,SAAU,GAAG;AACvB,aAAO,EAAE,KAAK;AAAA,IAChB,CAAC,EACA,KAAK,SAAU,GAAG;AACjB,aAAO,EAAE;AAAA,IACX,CAAC,EACA,KAAK,aAAa,KAAK,QAAQ,EAC/B,KAAK,KAAK,SAAU,GAAG;AACtB,UAAI,SAAS,UAAU,EAAE,SAAS;AAClC,UAAI,OAAO,UAAU,EAAE,iBAAiB,EAAE,OAAO;AACjD,UAAI,EAAE,WAAW;AACf,kBAAU,OAAO,UAAU,EAAE,OAAO,IAAI,UAAU,EAAE,SAAS,KAAK,MAAM;AAAA,MAC1E;AACA,UAAI,EAAE,WAAW;AACf,eAAO,SAAS;AAAA,MAClB;AACA,YAAM,YAAY,KAAK,QAAQ,EAAE;AAGjC,UAAI,YAAY,OAAO,QAAQ;AAC7B,YAAI,OAAO,YAAY,MAAM,KAAK,cAAcF,IAAG;AACjD,iBAAO,SAAS,aAAa;AAAA,QAC/B,OAAO;AACL,iBAAO,OAAO,aAAa;AAAA,QAC7B;AAAA,MACF,OAAO;AACL,gBAAQ,OAAO,UAAU,IAAI,SAAS;AAAA,MACxC;AAAA,IACF,CAAC,EACA,KAAK,KAAK,SAAU,GAAG,GAAG;AAEzB,UAAI,EAAE;AACN,aAAO,IAAI,SAAS,KAAK,YAAY,KAAK,KAAK,WAAW,IAAI,KAAK;AAAA,IACrE,CAAC,EACA,KAAK,eAAe,YAAY,EAChC,KAAK,SAAS,SAAU,GAAG;AAC1B,YAAM,SAAS,UAAU,EAAE,SAAS;AACpC,UAAI,OAAO,UAAU,EAAE,OAAO;AAC9B,UAAI,EAAE,WAAW;AACf,eAAO,SAAS;AAAA,MAClB;AACA,YAAM,YAAY,KAAK,QAAQ,EAAE;AAEjC,UAAI,WAAW;AACf,UAAI,EAAE,QAAQ,SAAS,GAAG;AACxB,mBAAW,EAAE,QAAQ,KAAK,GAAG;AAAA,MAC/B;AAEA,UAAI,SAAS;AACb,iBAAW,CAAC,GAAG,QAAQ,KAAK,WAAW,QAAQ,GAAG;AAChD,YAAI,EAAE,SAAS,UAAU;AACvB,mBAAS,IAAI,KAAK;AAAA,QACpB;AAAA,MACF;AAEA,UAAI,WAAW;AACf,UAAI,EAAE,QAAQ;AACZ,YAAI,EAAE,MAAM;AACV,qBAAW,mBAAmB;AAAA,QAChC,OAAO;AACL,qBAAW,eAAe;AAAA,QAC5B;AAAA,MACF;AAEA,UAAI,EAAE,MAAM;AACV,YAAI,EAAE,MAAM;AACV,qBAAW,WAAW,kBAAkB;AAAA,QAC1C,OAAO;AACL,qBAAW,WAAW,cAAc;AAAA,QACtC;AAAA,MACF,OAAO;AACL,YAAI,EAAE,MAAM;AACV,qBAAW,WAAW,cAAc;AAAA,QACtC;AAAA,MACF;AAEA,UAAI,EAAE,WAAW;AACf,oBAAY;AAAA,MACd;AAGA,UAAI,YAAY,OAAO,QAAQ;AAC7B,YAAI,OAAO,YAAY,MAAM,KAAK,cAAcA,IAAG;AACjD,iBAAO,WAAW,yCAAyC,SAAS,MAAM;AAAA,QAC5E,OAAO;AACL,iBACE,WACA,0CACA,SACA,MACA,WACA,YACA;AAAA,QAEJ;AAAA,MACF,OAAO;AACL,eAAO,WAAW,uBAAuB,SAAS,MAAM,WAAW,YAAY;AAAA,MACjF;AAAA,IACF,CAAC;AAEH,UAAMG,iBAAgB,UAAU,EAAE;AAGlC,QAAIA,mBAAkB,WAAW;AAC/B,UAAIC;AACJ,MAAAA,kBAAiB,OAAO,OAAO,EAAE;AACjC,YAAMC,OAAMD,gBAAe,MAAM,EAAE,CAAC,EAAE;AAEtC,iBACG,OAAO,SAAU,GAAG;AACnB,eAAOF,OAAM,IAAI,EAAE,EAAE;AAAA,MACvB,CAAC,EACA,KAAK,SAAU,GAAG;AACjB,YAAI,WAAWG,KAAI,cAAc,MAAM,EAAE,EAAE;AAC3C,YAAI,WAAWA,KAAI,cAAc,MAAM,EAAE,KAAK,OAAO;AACrD,cAAM,YAAY,SAAS;AAC3B,YAAI,OAAOA,KAAI,cAAc,GAAG;AAChC,aAAK,aAAa,cAAcH,OAAM,IAAI,EAAE,EAAE,CAAC;AAC/C,aAAK,aAAa,UAAU,MAAM;AAClC,kBAAU,YAAY,IAAI;AAC1B,aAAK,YAAY,QAAQ;AACzB,aAAK,YAAY,QAAQ;AAAA,MAC3B,CAAC;AAAA,IACL;AAAA,EACF;AAnQS;AA8QT,WAAS,gBAAgB,QAAQ,WAAW,YAAYF,IAAGM,IAAGP,QAAOQ,WAAUC,WAAU;AACvF,QAAID,UAAS,WAAW,KAAKC,UAAS,WAAW,GAAG;AAClD;AAAA,IACF;AAEA,QAAI;AACJ,QAAI;AACJ,eAAW,EAAE,WAAW,QAAQ,KAAKT,QAAO;AAC1C,UAAI,YAAY,UAAa,YAAY,SAAS;AAChD,kBAAU;AAAA,MACZ;AACA,UAAI,YAAY,UAAa,UAAU,SAAS;AAC9C,kBAAU;AAAA,MACZ;AAAA,IACF;AAEA,QAAI,CAAC,WAAW,CAAC,SAAS;AACxB;AAAA,IACF;AAEA,QAAIU,OAAM,OAAO,EAAE,KAAKA,OAAM,OAAO,GAAG,MAAM,IAAI,GAAG;AACnD,UAAI;AAAA,QACF;AAAA,MACF;AACA;AAAA,IACF;AAEA,UAAMC,cAAa,QAAQ,GAAG,cAAc;AAC5C,UAAM,gBAAgB,CAAC;AACvB,QAAI,QAAQ;AACZ,QAAI,IAAID,OAAM,OAAO;AACrB,WAAO,EAAE,QAAQ,KAAK,SAAS;AAC7B,UAAI,QAAQ,GAAG,cAAc,GAAGC,aAAYH,WAAUC,SAAQ,GAAG;AAC/D,YAAI,CAAC,OAAO;AACV,kBAAQ;AAAA,YACN,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF,OAAO;AACL,gBAAM,MAAM;AAAA,QACd;AAAA,MACF,OAAO;AACL,YAAI,OAAO;AACT,wBAAc,KAAK,KAAK;AACxB,kBAAQ;AAAA,QACV;AAAA,MACF;AACA,UAAI,EAAE,IAAI,GAAG,GAAG;AAAA,IAClB;AAEA,UAAM,aAAa,IAAI,OAAO,GAAG,EAAE,UAAU,MAAM,EAAE,KAAK,aAAa,EAAE,MAAM;AAE/E,eACG,OAAO,MAAM,EACb,KAAK,MAAM,SAAUG,IAAG;AACvB,aAAO,aAAaA,GAAE,MAAM,OAAO,YAAY;AAAA,IACjD,CAAC,EACA,KAAK,KAAK,SAAUA,IAAG;AACtB,aAAO,UAAUA,GAAE,KAAK,IAAI;AAAA,IAC9B,CAAC,EACA,KAAK,KAAK,KAAK,oBAAoB,EACnC,KAAK,SAAS,SAAUA,IAAG;AAC1B,YAAM,YAAYA,GAAE,IAAI,IAAI,GAAG,KAAK;AACpC,aAAO,UAAU,SAAS,IAAI,UAAUA,GAAE,KAAK;AAAA,IACjD,CAAC,EACA,KAAK,UAAUL,KAAI,YAAY,KAAK,oBAAoB,EACxD,KAAK,oBAAoB,SAAUK,IAAG,GAAG;AACxC,cAEI,UAAUA,GAAE,KAAK,IACjB,aACA,OAAO,UAAUA,GAAE,GAAG,IAAI,UAAUA,GAAE,KAAK,IAC3C,SAAS,IACX,SACC,IAAI,SAAS,MAAML,IAAG,SAAS,IAChC;AAAA,IAEJ,CAAC,EACA,KAAK,SAAS,eAAe;AAAA,EAClC;AA/ES;AAuFT,WAAS,SAAS,YAAY,WAAWN,IAAGM,IAAG;AAC7C,QAAI,cAAc,WAAW,SAAS,EACnC,SAAS,CAACA,KAAI,YAAY,KAAK,oBAAoB,EACnD,WAAW,WAAW,QAAQ,GAAG,cAAc,KAAK,KAAK,cAAc,UAAU,CAAC;AAErF,UAAM,iBAAiB;AACvB,UAAM,qBAAqB,eAAe;AAAA,MACxC,QAAQ,GAAG,gBAAgB,KAAK,KAAK;AAAA,IACvC;AAEA,QAAI,uBAAuB,MAAM;AAC/B,YAAM,QAAQ,mBAAmB,CAAC;AAClC,YAAM,WAAW,mBAAmB,CAAC;AACrC,YAAMM,WAAU,QAAQ,GAAG,WAAW,KAAK,KAAK;AAEhD,cAAQ,UAAU;AAAA,QAChB,KAAK;AACH,sBAAY,MAAM,gBAAgB,MAAM,KAAK,CAAC;AAC9C;AAAA,QACF,KAAK;AACH,sBAAY,MAAM,WAAW,MAAM,KAAK,CAAC;AACzC;AAAA,QACF,KAAK;AACH,sBAAY,MAAM,WAAW,MAAM,KAAK,CAAC;AACzC;AAAA,QACF,KAAK;AACH,sBAAY,MAAM,SAAS,MAAM,KAAK,CAAC;AACvC;AAAA,QACF,KAAK;AACH,sBAAY,MAAM,QAAQ,MAAM,KAAK,CAAC;AACtC;AAAA,QACF,KAAK;AACH,sBAAY,MAAM,yBAAyBA,QAAO,EAAE,MAAM,KAAK,CAAC;AAChE;AAAA,QACF,KAAK;AACH,sBAAY,MAAM,UAAU,MAAM,KAAK,CAAC;AACxC;AAAA,MACJ;AAAA,IACF;AAEA,QACG,OAAO,GAAG,EACV,KAAK,SAAS,MAAM,EACpB,KAAK,aAAa,eAAe,aAAa,QAAQN,KAAI,MAAM,GAAG,EACnE,KAAK,WAAW,EAChB,UAAU,MAAM,EAChB,MAAM,eAAe,QAAQ,EAC7B,KAAK,QAAQ,MAAM,EACnB,KAAK,UAAU,MAAM,EACrB,KAAK,aAAa,EAAE,EACpB,KAAK,MAAM,KAAK;AAEnB,QAAI,QAAQ,GAAG,eAAe,KAAK,KAAK,SAAS;AAC/C,UAAI,WAAW,QAAQ,SAAS,EAC7B,SAAS,CAACA,KAAI,YAAY,KAAK,oBAAoB,EACnD,WAAW,WAAW,QAAQ,GAAG,cAAc,KAAK,KAAK,cAAc,UAAU,CAAC;AAErF,UAAI,uBAAuB,MAAM;AAC/B,cAAM,QAAQ,mBAAmB,CAAC;AAClC,cAAM,WAAW,mBAAmB,CAAC;AACrC,cAAMM,WAAU,QAAQ,GAAG,WAAW,KAAK,KAAK;AAEhD,gBAAQ,UAAU;AAAA,UAChB,KAAK;AACH,qBAAS,MAAM,gBAAgB,MAAM,KAAK,CAAC;AAC3C;AAAA,UACF,KAAK;AACH,qBAAS,MAAM,WAAW,MAAM,KAAK,CAAC;AACtC;AAAA,UACF,KAAK;AACH,qBAAS,MAAM,WAAW,MAAM,KAAK,CAAC;AACtC;AAAA,UACF,KAAK;AACH,qBAAS,MAAM,SAAS,MAAM,KAAK,CAAC;AACpC;AAAA,UACF,KAAK;AACH,qBAAS,MAAM,QAAQ,MAAM,KAAK,CAAC;AACnC;AAAA,UACF,KAAK;AACH,qBAAS,MAAM,yBAAyBA,QAAO,EAAE,MAAM,KAAK,CAAC;AAC7D;AAAA,UACF,KAAK;AACH,qBAAS,MAAM,UAAU,MAAM,KAAK,CAAC;AACrC;AAAA,QACJ;AAAA,MACF;AAEA,UACG,OAAO,GAAG,EACV,KAAK,SAAS,MAAM,EACpB,KAAK,aAAa,eAAe,aAAa,OAAO,YAAY,GAAG,EACpE,KAAK,QAAQ,EACb,UAAU,MAAM,EAChB,MAAM,eAAe,QAAQ,EAC7B,KAAK,QAAQ,MAAM,EACnB,KAAK,UAAU,MAAM,EACrB,KAAK,aAAa,EAAE;AAAA,IAEzB;AAAA,EACF;AAnGS;AAyGT,WAAS,WAAW,QAAQ,WAAW;AACrC,QAAI,UAAU;AAEd,UAAM,iBAAiB,OAAO,KAAK,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC;AAEtF,QACG,OAAO,GAAG,EACV,UAAU,MAAM,EAChB,KAAK,cAAc,EACnB,MAAM,EACN,OAAO,SAAU,GAAG;AACnB,YAAM,OAAO,EAAE,CAAC,EAAE,MAAM,eAAO,cAAc;AAC7C,YAAM,KAAK,EAAE,KAAK,SAAS,KAAK;AAEhC,YAAM,WAAW,IAAI,gBAAgB,8BAA8B,MAAM;AACzE,eAAS,aAAa,MAAM,KAAK,IAAI;AAErC,iBAAW,CAAC,GAAG,GAAG,KAAK,KAAK,QAAQ,GAAG;AACrC,cAAM,QAAQ,IAAI,gBAAgB,8BAA8B,OAAO;AACvE,cAAM,aAAa,sBAAsB,SAAS;AAClD,cAAM,aAAa,KAAK,IAAI;AAC5B,YAAI,IAAI,GAAG;AACT,gBAAM,aAAa,MAAM,KAAK;AAAA,QAChC;AACA,cAAM,cAAc;AACpB,iBAAS,YAAY,KAAK;AAAA,MAC5B;AACA,aAAO;AAAA,IACT,CAAC,EACA,KAAK,KAAK,EAAE,EACZ,KAAK,KAAK,SAAU,GAAG,GAAG;AACzB,UAAI,IAAI,GAAG;AACT,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,qBAAW,eAAe,IAAI,CAAC,EAAE,CAAC;AAClC,iBAAQ,EAAE,CAAC,IAAI,SAAU,IAAI,UAAU,SAAS;AAAA,QAClD;AAAA,MACF,OAAO;AACL,eAAQ,EAAE,CAAC,IAAI,SAAU,IAAI;AAAA,MAC/B;AAAA,IACF,CAAC,EACA,KAAK,aAAa,KAAK,eAAe,EACtC,KAAK,SAAS,SAAU,GAAG;AAC1B,iBAAW,CAAC,GAAG,QAAQ,KAAK,WAAW,QAAQ,GAAG;AAChD,YAAI,EAAE,CAAC,MAAM,UAAU;AACrB,iBAAO,8BAA+B,IAAI,KAAK;AAAA,QACjD;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACL;AAjDS;AAyDT,WAAS,UAAU,YAAY,WAAWZ,IAAGM,IAAG;AAC9C,UAAMO,eAAc,QAAQ,GAAG,eAAe;AAC9C,QAAIA,iBAAgB,OAAO;AACzB;AAAA,IACF;AAEA,UAAM,SAAS,IAAI,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AACpD,UAAM,QAAQ,oBAAI,KAAK;AACvB,UAAM,YAAY,OAAO,OAAO,MAAM;AAEtC,cACG,KAAK,MAAM,UAAU,KAAK,IAAI,UAAU,EACxC,KAAK,MAAM,UAAU,KAAK,IAAI,UAAU,EACxC,KAAK,MAAM,KAAK,cAAc,EAC9B,KAAK,MAAMP,KAAI,KAAK,cAAc,EAClC,KAAK,SAAS,OAAO;AAExB,QAAIO,iBAAgB,IAAI;AACtB,gBAAU,KAAK,SAASA,aAAY,QAAQ,MAAM,GAAG,CAAC;AAAA,IACxD;AAAA,EACF;AApBS;AA4BT,WAAS,YAAY,KAAK;AACxB,UAAM,OAAO,CAAC;AACd,UAAM,SAAS,CAAC;AAChB,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC1C,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,CAAC,CAAC,GAAG;AAEvD,aAAK,IAAI,CAAC,CAAC,IAAI;AACf,eAAO,KAAK,IAAI,CAAC,CAAC;AAAA,MACpB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAXS;AAYX,GAvsBoB;AAysBpB,IAAO,wBAAQ;AAAA,EACb;AAAA,EACA;AACF;;;AC5xBA,IAAM,YAAY,wBAAC,YACjB;AAAA;AAAA,uBAEqB,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,YAI7B,QAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YASvB,QAAQ,eAAe;AAAA;AAAA;AAAA;AAAA,YAIvB,QAAQ,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,YAKxB,QAAQ,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,YAK1B,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,YAIlB,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,YAIlB,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,YAIlB,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,mBAKX,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAOvB,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAMZ,QAAQ,UAAU;AAAA,YACzB,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAYf,QAAQ,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAejB,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,YAIzB,QAAQ,iBAAiB;AAAA;AAAA,mBAElB,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,YAIzB,QAAQ,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAazB,QAAQ,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAM9B,QAAQ,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAM9B,QAAQ,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAW9B,QAAQ,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAOrB,QAAQ,YAAY;AAAA,cAClB,QAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAMzB,QAAQ,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,YAK5B,QAAQ,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAU5B,QAAQ,kBAAkB;AAAA,cACxB,QAAQ,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAO/B,QAAQ,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAUvB,QAAQ,mBAAmB;AAAA,YAC7B,QAAQ,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAQxB,QAAQ,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAUvB,QAAQ,eAAe;AAAA,YACzB,QAAQ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAQlB,QAAQ,eAAe;AAAA,YACzB,QAAQ,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAQxB,QAAQ,eAAe;AAAA,YACzB,QAAQ,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAiBxB,QAAQ,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAOzB,QAAQ,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAMzB,QAAQ,cAAc,QAAQ,SAAS;AAAA,mBAChC,QAAQ,UAAU;AAAA;AAAA,GA1PnB;AA8PlB,IAAO,iBAAQ;;;ACvPR,IAAM,UAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,IAAI;AAAA,EACJ,UAAU;AAAA,EACV,QAAQ;AACV;", "names": ["o", "parser", "lexer", "clear", "dateFormat", "excludes", "includes", "tags", "dayjs", "tasks", "w", "id", "links", "securityLevel", "sandboxElement", "doc", "h", "excludes", "includes", "dayjs", "dateFormat", "d", "weekday", "todayMarker"]}