{"version": 3, "sources": ["../../../src/utils.ts"], "sourcesContent": ["import { sanitizeUrl } from '@braintree/sanitize-url';\nimport type { BaseType, CurveFactory } from 'd3';\nimport {\n  curveBasis,\n  curveBasisClosed,\n  curveBasisOpen,\n  curveBumpX,\n  curveBumpY,\n  curveBundle,\n  curveCardinalClosed,\n  curveCardinalOpen,\n  curveCardinal,\n  curveCatmullRomClosed,\n  curveCatmullRomOpen,\n  curveCatmullRom,\n  curveLinear,\n  curveLinearClosed,\n  curveMonotoneX,\n  curveMonotoneY,\n  curveNatural,\n  curveStep,\n  curveStepAfter,\n  curveStepBefore,\n  select,\n} from 'd3';\nimport common from './diagrams/common/common.js';\nimport { sanitizeDirective } from './utils/sanitizeDirective.js';\nimport { log } from './logger.js';\nimport { detectType } from './diagram-api/detectType.js';\nimport assignWithDepth from './assignWithDepth.js';\nimport type { MermaidConfig } from './config.type.js';\nimport memoize from 'lodash-es/memoize.js';\nimport merge from 'lodash-es/merge.js';\nimport { directiveRegex } from './diagram-api/regexes.js';\nimport type { D3Element, Point, TextDimensionConfig, TextDimensions } from './types.js';\n\nexport const ZERO_WIDTH_SPACE = '\\u200b';\n\n// Effectively an enum of the supported curve types, accessible by name\nconst d3CurveTypes = {\n  curveBasis: curveBasis,\n  curveBasisClosed: curveBasisClosed,\n  curveBasisOpen: curveBasisOpen,\n  curveBumpX: curveBumpX,\n  curveBumpY: curveBumpY,\n  curveBundle: curveBundle,\n  curveCardinalClosed: curveCardinalClosed,\n  curveCardinalOpen: curveCardinalOpen,\n  curveCardinal: curveCardinal,\n  curveCatmullRomClosed: curveCatmullRomClosed,\n  curveCatmullRomOpen: curveCatmullRomOpen,\n  curveCatmullRom: curveCatmullRom,\n  curveLinear: curveLinear,\n  curveLinearClosed: curveLinearClosed,\n  curveMonotoneX: curveMonotoneX,\n  curveMonotoneY: curveMonotoneY,\n  curveNatural: curveNatural,\n  curveStep: curveStep,\n  curveStepAfter: curveStepAfter,\n  curveStepBefore: curveStepBefore,\n} as const;\n\nconst directiveWithoutOpen =\n  /\\s*(?:(\\w+)(?=:):|(\\w+))\\s*(?:(\\w+)|((?:(?!}%{2}).|\\r?\\n)*))?\\s*(?:}%{2})?/gi;\n/**\n * Detects the init config object from the text\n *\n * @param text - The text defining the graph. For example:\n *\n * ```mermaid\n * %%{init: {\"theme\": \"debug\", \"logLevel\": 1 }}%%\n * graph LR\n *      a-->b\n *      b-->c\n *      c-->d\n *      d-->e\n *      e-->f\n *      f-->g\n *      g-->h\n * ```\n *\n * Or\n *\n * ```mermaid\n * %%{initialize: {\"theme\": \"dark\", logLevel: \"debug\" }}%%\n * graph LR\n *    a-->b\n *    b-->c\n *    c-->d\n *    d-->e\n *    e-->f\n *    f-->g\n *    g-->h\n * ```\n *\n * @param config - Optional mermaid configuration object.\n * @returns The json object representing the init passed to mermaid.initialize()\n */\nexport const detectInit = function (\n  text: string,\n  config?: MermaidConfig\n): MermaidConfig | undefined {\n  const inits = detectDirective(text, /(?:init\\b)|(?:initialize\\b)/);\n  let results: MermaidConfig & { config?: unknown } = {};\n\n  if (Array.isArray(inits)) {\n    const args = inits.map((init) => init.args);\n    sanitizeDirective(args);\n    results = assignWithDepth(results, [...args]);\n  } else {\n    results = inits.args as MermaidConfig;\n  }\n\n  if (!results) {\n    return;\n  }\n\n  let type = detectType(text, config);\n\n  // Move the `config` value to appropriate diagram type value\n  const prop = 'config';\n  if (results[prop] !== undefined) {\n    if (type === 'flowchart-v2') {\n      type = 'flowchart';\n    }\n    results[type as keyof MermaidConfig] = results[prop];\n    delete results[prop];\n  }\n\n  return results;\n};\n\ninterface Directive {\n  type?: string;\n  args?: unknown;\n}\n/**\n * Detects the directive from the text.\n *\n * Text can be single line or multiline. If type is null or omitted,\n * the first directive encountered in text will be returned\n *\n * ```mermaid\n * graph LR\n * %%{someDirective}%%\n *    a-->b\n *    b-->c\n *    c-->d\n *    d-->e\n *    e-->f\n *    f-->g\n *    g-->h\n * ```\n *\n * @param text - The text defining the graph\n * @param type - The directive to return (default: `null`)\n * @returns An object or Array representing the directive(s) matched by the input type.\n * If a single directive was found, that directive object will be returned.\n */\nexport const detectDirective = function (\n  text: string,\n  type: string | RegExp | null = null\n): Directive | Directive[] {\n  try {\n    const commentWithoutDirectives = new RegExp(\n      `[%]{2}(?![{]${directiveWithoutOpen.source})(?=[}][%]{2}).*\\n`,\n      'ig'\n    );\n    text = text.trim().replace(commentWithoutDirectives, '').replace(/'/gm, '\"');\n    log.debug(\n      `Detecting diagram directive${type !== null ? ' type:' + type : ''} based on the text:${text}`\n    );\n    let match: RegExpExecArray | null;\n    const result: Directive[] = [];\n    while ((match = directiveRegex.exec(text)) !== null) {\n      // This is necessary to avoid infinite loops with zero-width matches\n      if (match.index === directiveRegex.lastIndex) {\n        directiveRegex.lastIndex++;\n      }\n      if ((match && !type) || (type && match[1]?.match(type)) || (type && match[2]?.match(type))) {\n        const type = match[1] ? match[1] : match[2];\n        const args = match[3] ? match[3].trim() : match[4] ? JSON.parse(match[4].trim()) : null;\n        result.push({ type, args });\n      }\n    }\n    if (result.length === 0) {\n      return { type: text, args: null };\n    }\n\n    return result.length === 1 ? result[0] : result;\n  } catch (error) {\n    log.error(\n      `ERROR: ${\n        (error as Error).message\n      } - Unable to parse directive type: '${type}' based on the text: '${text}'`\n    );\n    return { type: undefined, args: null };\n  }\n};\n\nexport const removeDirectives = function (text: string): string {\n  return text.replace(directiveRegex, '');\n};\n\n/**\n * Detects whether a substring in present in a given array\n *\n * @param str - The substring to detect\n * @param arr - The array to search\n * @returns The array index containing the substring or -1 if not present\n */\nexport const isSubstringInArray = function (str: string, arr: string[]): number {\n  for (const [i, element] of arr.entries()) {\n    if (element.match(str)) {\n      return i;\n    }\n  }\n  return -1;\n};\n\n/**\n * Returns a d3 curve given a curve name\n *\n * @param interpolate - The interpolation name\n * @param defaultCurve - The default curve to return\n * @returns The curve factory to use\n */\nexport function interpolateToCurve(\n  interpolate: string | undefined,\n  defaultCurve: CurveFactory\n): CurveFactory {\n  if (!interpolate) {\n    return defaultCurve;\n  }\n  const curveName = `curve${interpolate.charAt(0).toUpperCase() + interpolate.slice(1)}`;\n\n  // @ts-ignore TODO: Fix issue with curve type\n  return d3CurveTypes[curveName as keyof typeof d3CurveTypes] ?? defaultCurve;\n}\n\n/**\n * Formats a URL string\n *\n * @param linkStr - String of the URL\n * @param config - Configuration passed to MermaidJS\n * @returns The formatted URL or `undefined`.\n */\nexport function formatUrl(linkStr: string, config: MermaidConfig): string | undefined {\n  const url = linkStr.trim();\n\n  if (!url) {\n    return undefined;\n  }\n\n  if (config.securityLevel !== 'loose') {\n    return sanitizeUrl(url);\n  }\n\n  return url;\n}\n\n/**\n * Runs a function\n *\n * @param functionName - A dot separated path to the function relative to the `window`\n * @param params - Parameters to pass to the function\n */\nexport const runFunc = (functionName: string, ...params: unknown[]) => {\n  const arrPaths = functionName.split('.');\n\n  const len = arrPaths.length - 1;\n  const fnName = arrPaths[len];\n\n  let obj = window;\n  for (let i = 0; i < len; i++) {\n    obj = obj[arrPaths[i] as keyof typeof obj];\n    if (!obj) {\n      log.error(`Function name: ${functionName} not found in window`);\n      return;\n    }\n  }\n\n  obj[fnName as keyof typeof obj](...params);\n};\n\n/**\n * Finds the distance between two points using the Distance Formula\n *\n * @param p1 - The first point\n * @param p2 - The second point\n * @returns The distance between the two points.\n */\nfunction distance(p1?: Point, p2?: Point): number {\n  if (!p1 || !p2) {\n    return 0;\n  }\n  return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));\n}\n\n/**\n * TODO: Give this a description\n *\n * @param points - List of points\n */\nfunction traverseEdge(points: Point[]): Point {\n  let prevPoint: Point | undefined;\n  let totalDistance = 0;\n\n  points.forEach((point) => {\n    totalDistance += distance(point, prevPoint);\n    prevPoint = point;\n  });\n\n  // Traverse half of total distance along points\n  const remainingDistance = totalDistance / 2;\n  return calculatePoint(points, remainingDistance);\n}\n\n/**\n * {@inheritdoc traverseEdge}\n */\nfunction calcLabelPosition(points: Point[]): Point {\n  if (points.length === 1) {\n    return points[0];\n  }\n  return traverseEdge(points);\n}\n\nexport const roundNumber = (num: number, precision = 2) => {\n  const factor = Math.pow(10, precision);\n  return Math.round(num * factor) / factor;\n};\n\nexport const calculatePoint = (points: Point[], distanceToTraverse: number): Point => {\n  let prevPoint: Point | undefined = undefined;\n  let remainingDistance = distanceToTraverse;\n  for (const point of points) {\n    if (prevPoint) {\n      const vectorDistance = distance(point, prevPoint);\n      if (vectorDistance === 0) {\n        return prevPoint;\n      }\n      if (vectorDistance < remainingDistance) {\n        remainingDistance -= vectorDistance;\n      } else {\n        // The point is remainingDistance from prevPoint in the vector between prevPoint and point\n        // Calculate the coordinates\n        const distanceRatio = remainingDistance / vectorDistance;\n        if (distanceRatio <= 0) {\n          return prevPoint;\n        }\n        if (distanceRatio >= 1) {\n          return { x: point.x, y: point.y };\n        }\n        if (distanceRatio > 0 && distanceRatio < 1) {\n          return {\n            x: roundNumber((1 - distanceRatio) * prevPoint.x + distanceRatio * point.x, 5),\n            y: roundNumber((1 - distanceRatio) * prevPoint.y + distanceRatio * point.y, 5),\n          };\n        }\n      }\n    }\n    prevPoint = point;\n  }\n  throw new Error('Could not find a suitable point for the given distance');\n};\n\nconst calcCardinalityPosition = (\n  isRelationTypePresent: boolean,\n  points: Point[],\n  initialPosition: Point\n) => {\n  log.info(`our points ${JSON.stringify(points)}`);\n  if (points[0] !== initialPosition) {\n    points = points.reverse();\n  }\n  // Traverse only 25 total distance along points to find cardinality point\n  const distanceToCardinalityPoint = 25;\n  const center = calculatePoint(points, distanceToCardinalityPoint);\n  // if relation is present (Arrows will be added), change cardinality point off-set distance (d)\n  const d = isRelationTypePresent ? 10 : 5;\n  //Calculate Angle for x and y axis\n  const angle = Math.atan2(points[0].y - center.y, points[0].x - center.x);\n  const cardinalityPosition = { x: 0, y: 0 };\n  //Calculation cardinality position using angle, center point on the line/curve but perpendicular and with offset-distance\n  cardinalityPosition.x = Math.sin(angle) * d + (points[0].x + center.x) / 2;\n  cardinalityPosition.y = -Math.cos(angle) * d + (points[0].y + center.y) / 2;\n  return cardinalityPosition;\n};\n\n/**\n * Calculates the terminal label position.\n *\n * @param terminalMarkerSize - Terminal marker size.\n * @param position - Position of label relative to points.\n * @param _points - Array of points.\n * @returns - The `cardinalityPosition`.\n */\nfunction calcTerminalLabelPosition(\n  terminalMarkerSize: number,\n  position: 'start_left' | 'start_right' | 'end_left' | 'end_right',\n  _points: Point[]\n): Point {\n  const points = structuredClone(_points);\n  log.info('our points', points);\n  if (position !== 'start_left' && position !== 'start_right') {\n    points.reverse();\n  }\n\n  // Traverse only 25 total distance along points to find cardinality point\n  const distanceToCardinalityPoint = 25 + terminalMarkerSize;\n  const center = calculatePoint(points, distanceToCardinalityPoint);\n\n  // if relation is present (Arrows will be added), change cardinality point off-set distance (d)\n  const d = 10 + terminalMarkerSize * 0.5;\n  //Calculate Angle for x and y axis\n  const angle = Math.atan2(points[0].y - center.y, points[0].x - center.x);\n\n  const cardinalityPosition: Point = { x: 0, y: 0 };\n  //Calculation cardinality position using angle, center point on the line/curve but perpendicular and with offset-distance\n\n  if (position === 'start_left') {\n    cardinalityPosition.x = Math.sin(angle + Math.PI) * d + (points[0].x + center.x) / 2;\n    cardinalityPosition.y = -Math.cos(angle + Math.PI) * d + (points[0].y + center.y) / 2;\n  } else if (position === 'end_right') {\n    cardinalityPosition.x = Math.sin(angle - Math.PI) * d + (points[0].x + center.x) / 2 - 5;\n    cardinalityPosition.y = -Math.cos(angle - Math.PI) * d + (points[0].y + center.y) / 2 - 5;\n  } else if (position === 'end_left') {\n    cardinalityPosition.x = Math.sin(angle) * d + (points[0].x + center.x) / 2 - 5;\n    cardinalityPosition.y = -Math.cos(angle) * d + (points[0].y + center.y) / 2 - 5;\n  } else {\n    cardinalityPosition.x = Math.sin(angle) * d + (points[0].x + center.x) / 2;\n    cardinalityPosition.y = -Math.cos(angle) * d + (points[0].y + center.y) / 2;\n  }\n  return cardinalityPosition;\n}\n\n/**\n * Gets styles from an array of declarations\n *\n * @param arr - Declarations\n * @returns The styles grouped as strings\n */\nexport function getStylesFromArray(arr: string[]): { style: string; labelStyle: string } {\n  let style = '';\n  let labelStyle = '';\n\n  for (const element of arr) {\n    if (element !== undefined) {\n      // add text properties to label style definition\n      if (element.startsWith('color:') || element.startsWith('text-align:')) {\n        labelStyle = labelStyle + element + ';';\n      } else {\n        style = style + element + ';';\n      }\n    }\n  }\n\n  return { style, labelStyle };\n}\n\nlet cnt = 0;\nexport const generateId = () => {\n  cnt++;\n  return 'id-' + Math.random().toString(36).substr(2, 12) + '-' + cnt;\n};\n\n/**\n * Generates a random hexadecimal id of the given length.\n *\n * @param length - Length of string.\n * @returns The generated string.\n */\nfunction makeRandomHex(length: number): string {\n  let result = '';\n  const characters = '0123456789abcdef';\n  const charactersLength = characters.length;\n  for (let i = 0; i < length; i++) {\n    result += characters.charAt(Math.floor(Math.random() * charactersLength));\n  }\n  return result;\n}\n\nexport const random = (options: { length: number }) => {\n  return makeRandomHex(options.length);\n};\n\nexport const getTextObj = function () {\n  return {\n    x: 0,\n    y: 0,\n    fill: undefined,\n    anchor: 'start',\n    style: '#666',\n    width: 100,\n    height: 100,\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    valign: undefined,\n    text: '',\n  };\n};\n\n/**\n * Adds text to an element\n *\n * @param elem - SVG Element to add text to\n * @param textData - Text options.\n * @returns Text element with given styling and content\n */\nexport const drawSimpleText = function (\n  elem: SVGElement,\n  textData: {\n    text: string;\n    x: number;\n    y: number;\n    anchor: 'start' | 'middle' | 'end';\n    fontFamily: string;\n    fontSize: string | number;\n    fontWeight: string | number;\n    fill: string;\n    class: string | undefined;\n    textMargin: number;\n  }\n): SVGTextElement {\n  // Remove and ignore br:s\n  const nText = textData.text.replace(common.lineBreakRegex, ' ');\n\n  const [, _fontSizePx] = parseFontSize(textData.fontSize);\n\n  const textElem = elem.append('text') as any;\n  textElem.attr('x', textData.x);\n  textElem.attr('y', textData.y);\n  textElem.style('text-anchor', textData.anchor);\n  textElem.style('font-family', textData.fontFamily);\n  textElem.style('font-size', _fontSizePx);\n  textElem.style('font-weight', textData.fontWeight);\n  textElem.attr('fill', textData.fill);\n\n  if (textData.class !== undefined) {\n    textElem.attr('class', textData.class);\n  }\n\n  const span = textElem.append('tspan');\n  span.attr('x', textData.x + textData.textMargin * 2);\n  span.attr('fill', textData.fill);\n  span.text(nText);\n\n  return textElem;\n};\n\ninterface WrapLabelConfig {\n  fontSize: number;\n  fontFamily: string;\n  fontWeight: number;\n  joinWith: string;\n}\n\nexport const wrapLabel: (label: string, maxWidth: number, config: WrapLabelConfig) => string =\n  memoize(\n    (label: string, maxWidth: number, config: WrapLabelConfig): string => {\n      if (!label) {\n        return label;\n      }\n      config = Object.assign(\n        { fontSize: 12, fontWeight: 400, fontFamily: 'Arial', joinWith: '<br/>' },\n        config\n      );\n      if (common.lineBreakRegex.test(label)) {\n        return label;\n      }\n      const words = label.split(' ').filter(Boolean);\n      const completedLines: string[] = [];\n      let nextLine = '';\n      words.forEach((word, index) => {\n        const wordLength = calculateTextWidth(`${word} `, config);\n        const nextLineLength = calculateTextWidth(nextLine, config);\n        if (wordLength > maxWidth) {\n          const { hyphenatedStrings, remainingWord } = breakString(word, maxWidth, '-', config);\n          completedLines.push(nextLine, ...hyphenatedStrings);\n          nextLine = remainingWord;\n        } else if (nextLineLength + wordLength >= maxWidth) {\n          completedLines.push(nextLine);\n          nextLine = word;\n        } else {\n          nextLine = [nextLine, word].filter(Boolean).join(' ');\n        }\n        const currentWord = index + 1;\n        const isLastWord = currentWord === words.length;\n        if (isLastWord) {\n          completedLines.push(nextLine);\n        }\n      });\n      return completedLines.filter((line) => line !== '').join(config.joinWith);\n    },\n    (label, maxWidth, config) =>\n      `${label}${maxWidth}${config.fontSize}${config.fontWeight}${config.fontFamily}${config.joinWith}`\n  );\n\ninterface BreakStringOutput {\n  hyphenatedStrings: string[];\n  remainingWord: string;\n}\n\nconst breakString: (\n  word: string,\n  maxWidth: number,\n  hyphenCharacter: string,\n  config: WrapLabelConfig\n) => BreakStringOutput = memoize(\n  (\n    word: string,\n    maxWidth: number,\n    hyphenCharacter = '-',\n    config: WrapLabelConfig\n  ): BreakStringOutput => {\n    config = Object.assign(\n      { fontSize: 12, fontWeight: 400, fontFamily: 'Arial', margin: 0 },\n      config\n    );\n    const characters = [...word];\n    const lines: string[] = [];\n    let currentLine = '';\n    characters.forEach((character, index) => {\n      const nextLine = `${currentLine}${character}`;\n      const lineWidth = calculateTextWidth(nextLine, config);\n      if (lineWidth >= maxWidth) {\n        const currentCharacter = index + 1;\n        const isLastLine = characters.length === currentCharacter;\n        const hyphenatedNextLine = `${nextLine}${hyphenCharacter}`;\n        lines.push(isLastLine ? nextLine : hyphenatedNextLine);\n        currentLine = '';\n      } else {\n        currentLine = nextLine;\n      }\n    });\n    return { hyphenatedStrings: lines, remainingWord: currentLine };\n  },\n  (word, maxWidth, hyphenCharacter = '-', config) =>\n    `${word}${maxWidth}${hyphenCharacter}${config.fontSize}${config.fontWeight}${config.fontFamily}`\n);\n\n/**\n * This calculates the text's height, taking into account the wrap breaks and both the statically\n * configured height, width, and the length of the text (in pixels).\n *\n * If the wrapped text text has greater height, we extend the height, so it's value won't overflow.\n *\n * @param text - The text to measure\n * @param config - The config for fontSize, fontFamily, and fontWeight all impacting the\n *   resulting size\n * @returns The height for the given text\n */\nexport function calculateTextHeight(\n  text: Parameters<typeof calculateTextDimensions>[0],\n  config: Parameters<typeof calculateTextDimensions>[1]\n): ReturnType<typeof calculateTextDimensions>['height'] {\n  return calculateTextDimensions(text, config).height;\n}\n\n/**\n * This calculates the width of the given text, font size and family.\n *\n * @param text - The text to calculate the width of\n * @param config - The config for fontSize, fontFamily, and fontWeight all impacting the\n *   resulting size\n * @returns The width for the given text\n */\nexport function calculateTextWidth(\n  text: Parameters<typeof calculateTextDimensions>[0],\n  config: Parameters<typeof calculateTextDimensions>[1]\n): ReturnType<typeof calculateTextDimensions>['width'] {\n  return calculateTextDimensions(text, config).width;\n}\n\n/**\n * This calculates the dimensions of the given text, font size, font family, font weight, and\n * margins.\n *\n * @param text - The text to calculate the width of\n * @param config - The config for fontSize, fontFamily, fontWeight, and margin all impacting\n *   the resulting size\n * @returns The dimensions for the given text\n */\nexport const calculateTextDimensions: (\n  text: string,\n  config: TextDimensionConfig\n) => TextDimensions = memoize(\n  (text: string, config: TextDimensionConfig): TextDimensions => {\n    const { fontSize = 12, fontFamily = 'Arial', fontWeight = 400 } = config;\n    if (!text) {\n      return { width: 0, height: 0 };\n    }\n\n    const [, _fontSizePx] = parseFontSize(fontSize);\n\n    // We can't really know if the user supplied font family will render on the user agent;\n    // thus, we'll take the max width between the user supplied font family, and a default\n    // of sans-serif.\n    const fontFamilies = ['sans-serif', fontFamily];\n    const lines = text.split(common.lineBreakRegex);\n    const dims = [];\n\n    const body = select('body');\n    // We don't want to leak DOM elements - if a removal operation isn't available\n    // for any reason, do not continue.\n    if (!body.remove) {\n      return { width: 0, height: 0, lineHeight: 0 };\n    }\n\n    const g = body.append('svg');\n\n    for (const fontFamily of fontFamilies) {\n      let cHeight = 0;\n      const dim = { width: 0, height: 0, lineHeight: 0 };\n      for (const line of lines) {\n        const textObj = getTextObj();\n        textObj.text = line || ZERO_WIDTH_SPACE;\n        // @ts-ignore TODO: Fix D3 types\n        const textElem = drawSimpleText(g, textObj)\n          // @ts-ignore TODO: Fix D3 types\n          .style('font-size', _fontSizePx)\n          .style('font-weight', fontWeight)\n          .style('font-family', fontFamily);\n\n        const bBox = (textElem._groups || textElem)[0][0].getBBox();\n        if (bBox.width === 0 && bBox.height === 0) {\n          throw new Error('svg element not in render tree');\n        }\n        dim.width = Math.round(Math.max(dim.width, bBox.width));\n        cHeight = Math.round(bBox.height);\n        dim.height += cHeight;\n        dim.lineHeight = Math.round(Math.max(dim.lineHeight, cHeight));\n      }\n      dims.push(dim);\n    }\n\n    g.remove();\n\n    const index =\n      isNaN(dims[1].height) ||\n      isNaN(dims[1].width) ||\n      isNaN(dims[1].lineHeight) ||\n      (dims[0].height > dims[1].height &&\n        dims[0].width > dims[1].width &&\n        dims[0].lineHeight > dims[1].lineHeight)\n        ? 0\n        : 1;\n    return dims[index];\n  },\n  (text, config) => `${text}${config.fontSize}${config.fontWeight}${config.fontFamily}`\n);\n\nexport class InitIDGenerator {\n  private count = 0;\n  public next: () => number;\n  constructor(deterministic = false, seed?: string) {\n    // TODO: Seed is only used for length?\n    // v11: Use the actual value of seed string to generate an initial value for count.\n    this.count = seed ? seed.length : 0;\n    this.next = deterministic ? () => this.count++ : () => Date.now();\n  }\n}\n\nlet decoder: HTMLDivElement;\n\n/**\n * Decodes HTML, source: {@link https://github.com/shrpne/entity-decode/blob/v2.0.1/browser.js}\n *\n * @param html - HTML as a string\n * @returns Unescaped HTML\n */\nexport const entityDecode = function (html: string): string {\n  decoder = decoder || document.createElement('div');\n  // Escape HTML before decoding for HTML Entities\n  html = escape(html).replace(/%26/g, '&').replace(/%23/g, '#').replace(/%3B/g, ';');\n  decoder.innerHTML = html;\n\n  return unescape(decoder.textContent!);\n};\n\nexport interface DetailedError {\n  str: string;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  hash: any;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  error?: any;\n  message?: string;\n}\n\n/** @param error - The error to check */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function isDetailedError(error: any): error is DetailedError {\n  return 'str' in error;\n}\n\n/** @param error - The error to convert to an error message */\nexport function getErrorMessage(error: unknown): string {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return String(error);\n}\n\n/**\n * Appends <text> element with the given title and css class.\n *\n * @param parent - d3 svg object to append title to\n * @param cssClass - CSS class for the <text> element containing the title\n * @param titleTopMargin - Margin in pixels between title and rest of the graph\n * @param title - The title. If empty, returns immediately.\n */\nexport const insertTitle = (\n  parent: D3Element,\n  cssClass: string,\n  titleTopMargin: number,\n  title?: string\n): void => {\n  if (!title) {\n    return;\n  }\n  const bounds = parent.node()?.getBBox();\n  if (!bounds) {\n    return;\n  }\n  parent\n    .append('text')\n    .text(title)\n    .attr('text-anchor', 'middle')\n    .attr('x', bounds.x + bounds.width / 2)\n    .attr('y', -titleTopMargin)\n    .attr('class', cssClass);\n};\n\n/**\n * Parses a raw fontSize configuration value into a number and string value.\n *\n * @param fontSize - a string or number font size configuration value\n *\n * @returns parsed number and string style font size values, or nulls if a number value can't\n * be parsed from an input string.\n */\nexport const parseFontSize = (fontSize: string | number | undefined): [number?, string?] => {\n  // if the font size is a number, assume a px string representation\n  if (typeof fontSize === 'number') {\n    return [fontSize, fontSize + 'px'];\n  }\n\n  const fontSizeNumber = parseInt(fontSize ?? '', 10);\n  if (Number.isNaN(fontSizeNumber)) {\n    // if a number value can't be parsed, return null for both values\n    return [undefined, undefined];\n  } else if (fontSize === String(fontSizeNumber)) {\n    // if a string input doesn't contain any units, assume px units\n    return [fontSizeNumber, fontSize + 'px'];\n  } else {\n    return [fontSizeNumber, fontSize];\n  }\n};\n\nexport function cleanAndMerge<T>(defaultData: T, data?: Partial<T>): T {\n  return merge({}, defaultData, data);\n}\n\nexport default {\n  assignWithDepth,\n  wrapLabel,\n  calculateTextHeight,\n  calculateTextWidth,\n  calculateTextDimensions,\n  cleanAndMerge,\n  detectInit,\n  detectDirective,\n  isSubstringInArray,\n  interpolateToCurve,\n  calcLabelPosition,\n  calcCardinalityPosition,\n  calcTerminalLabelPosition,\n  formatUrl,\n  getStylesFromArray,\n  generateId,\n  random,\n  runFunc,\n  entityDecode,\n  insertTitle,\n  parseFontSize,\n  InitIDGenerator,\n};\n\n/**\n * @param  text - text to be encoded\n * @returns\n */\nexport const encodeEntities = function (text: string): string {\n  let txt = text;\n\n  txt = txt.replace(/style.*:\\S*#.*;/g, function (s): string {\n    return s.substring(0, s.length - 1);\n  });\n  txt = txt.replace(/classDef.*:\\S*#.*;/g, function (s): string {\n    return s.substring(0, s.length - 1);\n  });\n\n  txt = txt.replace(/#\\w+;/g, function (s) {\n    const innerTxt = s.substring(1, s.length - 1);\n\n    const isInt = /^\\+?\\d+$/.test(innerTxt);\n    if (isInt) {\n      return 'ﬂ°°' + innerTxt + '¶ß';\n    } else {\n      return 'ﬂ°' + innerTxt + '¶ß';\n    }\n  });\n\n  return txt;\n};\n\n/**\n *\n * @param  text - text to be decoded\n * @returns\n */\nexport const decodeEntities = function (text: string): string {\n  return text.replace(/ﬂ°°/g, '&#').replace(/ﬂ°/g, '&').replace(/¶ß/g, ';');\n};\n\nexport const isString = (value: unknown): value is string => {\n  return typeof value === 'string';\n};\n\nexport const getEdgeId = (\n  from: string,\n  to: string,\n  {\n    counter = 0,\n    prefix,\n    suffix,\n  }: {\n    counter?: number;\n    prefix?: string;\n    suffix?: string;\n  },\n  id?: string\n) => {\n  if (id) {\n    return id;\n  }\n  return `${prefix ? `${prefix}_` : ''}${from}_${to}_${counter}${suffix ? `_${suffix}` : ''}`;\n};\n\n/**\n * D3's `selection.attr` method doesn't officially support `undefined`.\n *\n * However, it seems if you do pass `undefined`, it seems to be treated as `null`\n * (e.g. it removes the attribute).\n */\nexport function handleUndefinedAttr(\n  attrValue: Parameters<d3.Selection<BaseType, unknown, HTMLElement, any>['attr']>[1] | undefined\n) {\n  return attrValue ?? null;\n}\n"], "mappings": "0XAAA,IAAAA,GAA4B,WAoCrB,IAAMC,GAAmB,SAG1BC,GAAe,CACnB,WAAYC,EACZ,iBAAkBC,EAClB,eAAgBC,EAChB,WAAYC,EACZ,WAAYC,EACZ,YAAaC,EACb,oBAAqBC,EACrB,kBAAmBC,EACnB,cAAeC,EACf,sBAAuBC,EACvB,oBAAqBC,EACrB,gBAAiBC,EACjB,YAAaC,EACb,kBAAmBC,EACnB,eAAgBC,EAChB,eAAgBC,EAChB,aAAcC,EACd,UAAWC,EACX,eAAgBC,EAChB,gBAAiBC,CACnB,EAEMC,GACJ,+EAmCWC,GAAaC,EAAA,SACxBC,EACAC,EAC2B,CAC3B,IAAMC,EAAQC,GAAgBH,EAAM,6BAA6B,EAC7DI,EAAgD,CAAC,EAErD,GAAI,MAAM,QAAQF,CAAK,EAAG,CACxB,IAAMG,EAAOH,EAAM,IAAKI,GAASA,EAAK,IAAI,EAC1CC,EAAkBF,CAAI,EACtBD,EAAUI,EAAgBJ,EAAS,CAAC,GAAGC,CAAI,CAAC,CAC9C,MACED,EAAUF,EAAM,KAGlB,GAAI,CAACE,EACH,OAGF,IAAIK,EAAOC,EAAWV,EAAMC,CAAM,EAG5BU,EAAO,SACb,OAAIP,EAAQO,CAAI,IAAM,SAChBF,IAAS,iBACXA,EAAO,aAETL,EAAQK,CAA2B,EAAIL,EAAQO,CAAI,EACnD,OAAOP,EAAQO,CAAI,GAGdP,CACT,EAhC0B,cA6DbD,GAAkBJ,EAAA,SAC7BC,EACAS,EAA+B,KACN,CACzB,GAAI,CACF,IAAMG,EAA2B,IAAI,OACnC,eAAef,GAAqB,MAAM;AAAA,EAC1C,IACF,EACAG,EAAOA,EAAK,KAAK,EAAE,QAAQY,EAA0B,EAAE,EAAE,QAAQ,MAAO,GAAG,EAC3EC,EAAI,MACF,8BAA8BJ,IAAS,KAAO,SAAWA,EAAO,EAAE,sBAAsBT,CAAI,EAC9F,EACA,IAAIc,EACEC,EAAsB,CAAC,EAC7B,MAAQD,EAAQE,EAAe,KAAKhB,CAAI,KAAO,MAK7C,GAHIc,EAAM,QAAUE,EAAe,WACjCA,EAAe,YAEZF,GAAS,CAACL,GAAUA,GAAQK,EAAM,CAAC,GAAG,MAAML,CAAI,GAAOA,GAAQK,EAAM,CAAC,GAAG,MAAML,CAAI,EAAI,CAC1F,IAAMA,EAAOK,EAAM,CAAC,EAAIA,EAAM,CAAC,EAAIA,EAAM,CAAC,EACpCT,EAAOS,EAAM,CAAC,EAAIA,EAAM,CAAC,EAAE,KAAK,EAAIA,EAAM,CAAC,EAAI,KAAK,MAAMA,EAAM,CAAC,EAAE,KAAK,CAAC,EAAI,KACnFC,EAAO,KAAK,CAAE,KAAAN,EAAM,KAAAJ,CAAK,CAAC,CAC5B,CAEF,OAAIU,EAAO,SAAW,EACb,CAAE,KAAMf,EAAM,KAAM,IAAK,EAG3Be,EAAO,SAAW,EAAIA,EAAO,CAAC,EAAIA,CAC3C,OAASE,EAAO,CACd,OAAAJ,EAAI,MACF,UACGI,EAAgB,OACnB,uCAAuCR,CAAI,yBAAyBT,CAAI,GAC1E,EACO,CAAE,KAAM,OAAW,KAAM,IAAK,CACvC,CACF,EAvC+B,mBAyClBkB,GAAmBnB,EAAA,SAAUC,EAAsB,CAC9D,OAAOA,EAAK,QAAQgB,EAAgB,EAAE,CACxC,EAFgC,oBAWnBG,GAAqBpB,EAAA,SAAUqB,EAAaC,EAAuB,CAC9E,OAAW,CAACC,EAAGC,CAAO,IAAKF,EAAI,QAAQ,EACrC,GAAIE,EAAQ,MAAMH,CAAG,EACnB,OAAOE,EAGX,MAAO,EACT,EAPkC,sBAgB3B,SAASE,GACdC,EACAC,EACc,CACd,GAAI,CAACD,EACH,OAAOC,EAET,IAAMC,EAAY,QAAQF,EAAY,OAAO,CAAC,EAAE,YAAY,EAAIA,EAAY,MAAM,CAAC,CAAC,GAGpF,OAAOjD,GAAamD,CAAsC,GAAKD,CACjE,CAXgB3B,EAAAyB,GAAA,sBAoBT,SAASI,GAAUC,EAAiB5B,EAA2C,CACpF,IAAM6B,EAAMD,EAAQ,KAAK,EAEzB,GAAKC,EAIL,OAAI7B,EAAO,gBAAkB,WACpB,gBAAY6B,CAAG,EAGjBA,CACT,CAZgB/B,EAAA6B,GAAA,aAoBT,IAAMG,GAAUhC,EAAA,CAACiC,KAAyBC,IAAsB,CACrE,IAAMC,EAAWF,EAAa,MAAM,GAAG,EAEjCG,EAAMD,EAAS,OAAS,EACxBE,EAASF,EAASC,CAAG,EAEvBE,EAAM,OACV,QAASf,EAAI,EAAGA,EAAIa,EAAKb,IAEvB,GADAe,EAAMA,EAAIH,EAASZ,CAAC,CAAqB,EACrC,CAACe,EAAK,CACRxB,EAAI,MAAM,kBAAkBmB,CAAY,sBAAsB,EAC9D,MACF,CAGFK,EAAID,CAA0B,EAAE,GAAGH,CAAM,CAC3C,EAhBuB,WAyBvB,SAASK,GAASC,EAAYC,EAAoB,CAChD,MAAI,CAACD,GAAM,CAACC,EACH,EAEF,KAAK,KAAK,KAAK,IAAIA,EAAG,EAAID,EAAG,EAAG,CAAC,EAAI,KAAK,IAAIC,EAAG,EAAID,EAAG,EAAG,CAAC,CAAC,CACtE,CALSxC,EAAAuC,GAAA,YAYT,SAASG,GAAaC,EAAwB,CAC5C,IAAIC,EACAC,EAAgB,EAEpBF,EAAO,QAASG,GAAU,CACxBD,GAAiBN,GAASO,EAAOF,CAAS,EAC1CA,EAAYE,CACd,CAAC,EAGD,IAAMC,EAAoBF,EAAgB,EAC1C,OAAOG,EAAeL,EAAQI,CAAiB,CACjD,CAZS/C,EAAA0C,GAAA,gBAiBT,SAASO,GAAkBN,EAAwB,CACjD,OAAIA,EAAO,SAAW,EACbA,EAAO,CAAC,EAEVD,GAAaC,CAAM,CAC5B,CALS3C,EAAAiD,GAAA,qBAOF,IAAMC,GAAclD,EAAA,CAACmD,EAAaC,EAAY,IAAM,CACzD,IAAMC,EAAS,KAAK,IAAI,GAAID,CAAS,EACrC,OAAO,KAAK,MAAMD,EAAME,CAAM,EAAIA,CACpC,EAH2B,eAKdL,EAAiBhD,EAAA,CAAC2C,EAAiBW,IAAsC,CACpF,IAAIV,EACAG,EAAoBO,EACxB,QAAWR,KAASH,EAAQ,CAC1B,GAAIC,EAAW,CACb,IAAMW,EAAiBhB,GAASO,EAAOF,CAAS,EAChD,GAAIW,IAAmB,EACrB,OAAOX,EAET,GAAIW,EAAiBR,EACnBA,GAAqBQ,MAChB,CAGL,IAAMC,EAAgBT,EAAoBQ,EAC1C,GAAIC,GAAiB,EACnB,OAAOZ,EAET,GAAIY,GAAiB,EACnB,MAAO,CAAE,EAAGV,EAAM,EAAG,EAAGA,EAAM,CAAE,EAElC,GAAIU,EAAgB,GAAKA,EAAgB,EACvC,MAAO,CACL,EAAGN,IAAa,EAAIM,GAAiBZ,EAAU,EAAIY,EAAgBV,EAAM,EAAG,CAAC,EAC7E,EAAGI,IAAa,EAAIM,GAAiBZ,EAAU,EAAIY,EAAgBV,EAAM,EAAG,CAAC,CAC/E,CAEJ,CACF,CACAF,EAAYE,CACd,CACA,MAAM,IAAI,MAAM,wDAAwD,CAC1E,EAhC8B,kBAkCxBW,GAA0BzD,EAAA,CAC9B0D,EACAf,EACAgB,IACG,CACH7C,EAAI,KAAK,cAAc,KAAK,UAAU6B,CAAM,CAAC,EAAE,EAC3CA,EAAO,CAAC,IAAMgB,IAChBhB,EAASA,EAAO,QAAQ,GAI1B,IAAMiB,EAASZ,EAAeL,EADK,EAC6B,EAE1DkB,EAAIH,EAAwB,GAAK,EAEjCI,EAAQ,KAAK,MAAMnB,EAAO,CAAC,EAAE,EAAIiB,EAAO,EAAGjB,EAAO,CAAC,EAAE,EAAIiB,EAAO,CAAC,EACjEG,EAAsB,CAAE,EAAG,EAAG,EAAG,CAAE,EAEzC,OAAAA,EAAoB,EAAI,KAAK,IAAID,CAAK,EAAID,GAAKlB,EAAO,CAAC,EAAE,EAAIiB,EAAO,GAAK,EACzEG,EAAoB,EAAI,CAAC,KAAK,IAAID,CAAK,EAAID,GAAKlB,EAAO,CAAC,EAAE,EAAIiB,EAAO,GAAK,EACnEG,CACT,EArBgC,2BA+BhC,SAASC,GACPC,EACAC,EACAC,EACO,CACP,IAAMxB,EAAS,gBAAgBwB,CAAO,EACtCrD,EAAI,KAAK,aAAc6B,CAAM,EACzBuB,IAAa,cAAgBA,IAAa,eAC5CvB,EAAO,QAAQ,EAIjB,IAAMyB,EAA6B,GAAKH,EAClCL,EAASZ,EAAeL,EAAQyB,CAA0B,EAG1DP,EAAI,GAAKI,EAAqB,GAE9BH,EAAQ,KAAK,MAAMnB,EAAO,CAAC,EAAE,EAAIiB,EAAO,EAAGjB,EAAO,CAAC,EAAE,EAAIiB,EAAO,CAAC,EAEjEG,EAA6B,CAAE,EAAG,EAAG,EAAG,CAAE,EAGhD,OAAIG,IAAa,cACfH,EAAoB,EAAI,KAAK,IAAID,EAAQ,KAAK,EAAE,EAAID,GAAKlB,EAAO,CAAC,EAAE,EAAIiB,EAAO,GAAK,EACnFG,EAAoB,EAAI,CAAC,KAAK,IAAID,EAAQ,KAAK,EAAE,EAAID,GAAKlB,EAAO,CAAC,EAAE,EAAIiB,EAAO,GAAK,GAC3EM,IAAa,aACtBH,EAAoB,EAAI,KAAK,IAAID,EAAQ,KAAK,EAAE,EAAID,GAAKlB,EAAO,CAAC,EAAE,EAAIiB,EAAO,GAAK,EAAI,EACvFG,EAAoB,EAAI,CAAC,KAAK,IAAID,EAAQ,KAAK,EAAE,EAAID,GAAKlB,EAAO,CAAC,EAAE,EAAIiB,EAAO,GAAK,EAAI,GAC/EM,IAAa,YACtBH,EAAoB,EAAI,KAAK,IAAID,CAAK,EAAID,GAAKlB,EAAO,CAAC,EAAE,EAAIiB,EAAO,GAAK,EAAI,EAC7EG,EAAoB,EAAI,CAAC,KAAK,IAAID,CAAK,EAAID,GAAKlB,EAAO,CAAC,EAAE,EAAIiB,EAAO,GAAK,EAAI,IAE9EG,EAAoB,EAAI,KAAK,IAAID,CAAK,EAAID,GAAKlB,EAAO,CAAC,EAAE,EAAIiB,EAAO,GAAK,EACzEG,EAAoB,EAAI,CAAC,KAAK,IAAID,CAAK,EAAID,GAAKlB,EAAO,CAAC,EAAE,EAAIiB,EAAO,GAAK,GAErEG,CACT,CArCS/D,EAAAgE,GAAA,6BA6CF,SAASK,GAAmB/C,EAAsD,CACvF,IAAIgD,EAAQ,GACRC,EAAa,GAEjB,QAAW/C,KAAWF,EAChBE,IAAY,SAEVA,EAAQ,WAAW,QAAQ,GAAKA,EAAQ,WAAW,aAAa,EAClE+C,EAAaA,EAAa/C,EAAU,IAEpC8C,EAAQA,EAAQ9C,EAAU,KAKhC,MAAO,CAAE,MAAA8C,EAAO,WAAAC,CAAW,CAC7B,CAhBgBvE,EAAAqE,GAAA,sBAkBhB,IAAIG,GAAM,EACGC,GAAazE,EAAA,KACxBwE,KACO,MAAQ,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,EAAG,EAAE,EAAI,IAAMA,IAFxC,cAW1B,SAASE,GAAcC,EAAwB,CAC7C,IAAI3D,EAAS,GACP4D,EAAa,mBACbC,EAAmBD,EAAW,OACpC,QAAS,EAAI,EAAG,EAAID,EAAQ,IAC1B3D,GAAU4D,EAAW,OAAO,KAAK,MAAM,KAAK,OAAO,EAAIC,CAAgB,CAAC,EAE1E,OAAO7D,CACT,CARShB,EAAA0E,GAAA,iBAUF,IAAMI,GAAS9E,EAAC+E,GACdL,GAAcK,EAAQ,MAAM,EADf,UAITC,GAAahF,EAAA,UAAY,CACpC,MAAO,CACL,EAAG,EACH,EAAG,EACH,KAAM,OACN,OAAQ,QACR,MAAO,OACP,MAAO,IACP,OAAQ,IACR,WAAY,EACZ,GAAI,EACJ,GAAI,EACJ,OAAQ,OACR,KAAM,EACR,CACF,EAf0B,cAwBbiF,GAAiBjF,EAAA,SAC5BkF,EACAC,EAYgB,CAEhB,IAAMC,EAAQD,EAAS,KAAK,QAAQE,EAAO,eAAgB,GAAG,EAExD,CAAC,CAAEC,CAAW,EAAIC,EAAcJ,EAAS,QAAQ,EAEjDK,EAAWN,EAAK,OAAO,MAAM,EACnCM,EAAS,KAAK,IAAKL,EAAS,CAAC,EAC7BK,EAAS,KAAK,IAAKL,EAAS,CAAC,EAC7BK,EAAS,MAAM,cAAeL,EAAS,MAAM,EAC7CK,EAAS,MAAM,cAAeL,EAAS,UAAU,EACjDK,EAAS,MAAM,YAAaF,CAAW,EACvCE,EAAS,MAAM,cAAeL,EAAS,UAAU,EACjDK,EAAS,KAAK,OAAQL,EAAS,IAAI,EAE/BA,EAAS,QAAU,QACrBK,EAAS,KAAK,QAASL,EAAS,KAAK,EAGvC,IAAMM,EAAOD,EAAS,OAAO,OAAO,EACpC,OAAAC,EAAK,KAAK,IAAKN,EAAS,EAAIA,EAAS,WAAa,CAAC,EACnDM,EAAK,KAAK,OAAQN,EAAS,IAAI,EAC/BM,EAAK,KAAKL,CAAK,EAERI,CACT,EAvC8B,kBAgDjBE,GACXC,EACE,CAACC,EAAeC,EAAkB3F,IAAoC,CAQpE,GAPI,CAAC0F,IAGL1F,EAAS,OAAO,OACd,CAAE,SAAU,GAAI,WAAY,IAAK,WAAY,QAAS,SAAU,OAAQ,EACxEA,CACF,EACImF,EAAO,eAAe,KAAKO,CAAK,GAClC,OAAOA,EAET,IAAME,EAAQF,EAAM,MAAM,GAAG,EAAE,OAAO,OAAO,EACvCG,EAA2B,CAAC,EAC9BC,EAAW,GACf,OAAAF,EAAM,QAAQ,CAACG,EAAMC,IAAU,CAC7B,IAAMC,EAAaC,EAAmB,GAAGH,CAAI,IAAK/F,CAAM,EAClDmG,EAAiBD,EAAmBJ,EAAU9F,CAAM,EAC1D,GAAIiG,EAAaN,EAAU,CACzB,GAAM,CAAE,kBAAAS,EAAmB,cAAAC,CAAc,EAAIC,GAAYP,EAAMJ,EAAU,IAAK3F,CAAM,EACpF6F,EAAe,KAAKC,EAAU,GAAGM,CAAiB,EAClDN,EAAWO,CACb,MAAWF,EAAiBF,GAAcN,GACxCE,EAAe,KAAKC,CAAQ,EAC5BA,EAAWC,GAEXD,EAAW,CAACA,EAAUC,CAAI,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG,EAElCC,EAAQ,IACOJ,EAAM,QAEvCC,EAAe,KAAKC,CAAQ,CAEhC,CAAC,EACMD,EAAe,OAAQU,GAASA,IAAS,EAAE,EAAE,KAAKvG,EAAO,QAAQ,CAC1E,EACA,CAAC0F,EAAOC,EAAU3F,IAChB,GAAG0F,CAAK,GAAGC,CAAQ,GAAG3F,EAAO,QAAQ,GAAGA,EAAO,UAAU,GAAGA,EAAO,UAAU,GAAGA,EAAO,QAAQ,EACnG,EAOIsG,GAKmBb,EACvB,CACEM,EACAJ,EACAa,EAAkB,IAClBxG,IACsB,CACtBA,EAAS,OAAO,OACd,CAAE,SAAU,GAAI,WAAY,IAAK,WAAY,QAAS,OAAQ,CAAE,EAChEA,CACF,EACA,IAAM0E,EAAa,CAAC,GAAGqB,CAAI,EACrBU,EAAkB,CAAC,EACrBC,EAAc,GAClB,OAAAhC,EAAW,QAAQ,CAACiC,EAAWX,IAAU,CACvC,IAAMF,EAAW,GAAGY,CAAW,GAAGC,CAAS,GAE3C,GADkBT,EAAmBJ,EAAU9F,CAAM,GACpC2F,EAAU,CACzB,IAAMiB,EAAmBZ,EAAQ,EAC3Ba,EAAanC,EAAW,SAAWkC,EACnCE,EAAqB,GAAGhB,CAAQ,GAAGU,CAAe,GACxDC,EAAM,KAAKI,EAAaf,EAAWgB,CAAkB,EACrDJ,EAAc,EAChB,MACEA,EAAcZ,CAElB,CAAC,EACM,CAAE,kBAAmBW,EAAO,cAAeC,CAAY,CAChE,EACA,CAACX,EAAMJ,EAAUa,EAAkB,IAAKxG,IACtC,GAAG+F,CAAI,GAAGJ,CAAQ,GAAGa,CAAe,GAAGxG,EAAO,QAAQ,GAAGA,EAAO,UAAU,GAAGA,EAAO,UAAU,EAClG,EAaO,SAAS+G,GACdhH,EACAC,EACsD,CACtD,OAAOgH,EAAwBjH,EAAMC,CAAM,EAAE,MAC/C,CALgBF,EAAAiH,GAAA,uBAeT,SAASb,EACdnG,EACAC,EACqD,CACrD,OAAOgH,EAAwBjH,EAAMC,CAAM,EAAE,KAC/C,CALgBF,EAAAoG,EAAA,sBAgBT,IAAMc,EAGSvB,EACpB,CAAC1F,EAAcC,IAAgD,CAC7D,GAAM,CAAE,SAAAiH,EAAW,GAAI,WAAAC,EAAa,QAAS,WAAAC,EAAa,GAAI,EAAInH,EAClE,GAAI,CAACD,EACH,MAAO,CAAE,MAAO,EAAG,OAAQ,CAAE,EAG/B,GAAM,CAAC,CAAEqF,CAAW,EAAIC,EAAc4B,CAAQ,EAKxCG,EAAe,CAAC,aAAcF,CAAU,EACxCT,EAAQ1G,EAAK,MAAMoF,EAAO,cAAc,EACxCkC,EAAO,CAAC,EAERC,EAAOC,EAAO,MAAM,EAG1B,GAAI,CAACD,EAAK,OACR,MAAO,CAAE,MAAO,EAAG,OAAQ,EAAG,WAAY,CAAE,EAG9C,IAAME,EAAIF,EAAK,OAAO,KAAK,EAE3B,QAAWJ,KAAcE,EAAc,CACrC,IAAIK,EAAU,EACRC,EAAM,CAAE,MAAO,EAAG,OAAQ,EAAG,WAAY,CAAE,EACjD,QAAWnB,MAAQE,EAAO,CACxB,IAAMkB,EAAU7C,GAAW,EAC3B6C,EAAQ,KAAOpB,IAAQjI,GAEvB,IAAMgH,EAAWP,GAAeyC,EAAGG,CAAO,EAEvC,MAAM,YAAavC,CAAW,EAC9B,MAAM,cAAe+B,CAAU,EAC/B,MAAM,cAAeD,CAAU,EAE5BU,GAAQtC,EAAS,SAAWA,GAAU,CAAC,EAAE,CAAC,EAAE,QAAQ,EAC1D,GAAIsC,EAAK,QAAU,GAAKA,EAAK,SAAW,EACtC,MAAM,IAAI,MAAM,gCAAgC,EAElDF,EAAI,MAAQ,KAAK,MAAM,KAAK,IAAIA,EAAI,MAAOE,EAAK,KAAK,CAAC,EACtDH,EAAU,KAAK,MAAMG,EAAK,MAAM,EAChCF,EAAI,QAAUD,EACdC,EAAI,WAAa,KAAK,MAAM,KAAK,IAAIA,EAAI,WAAYD,CAAO,CAAC,CAC/D,CACAJ,EAAK,KAAKK,CAAG,CACf,CAEAF,EAAE,OAAO,EAET,IAAMxB,EACJ,MAAMqB,EAAK,CAAC,EAAE,MAAM,GACpB,MAAMA,EAAK,CAAC,EAAE,KAAK,GACnB,MAAMA,EAAK,CAAC,EAAE,UAAU,GACvBA,EAAK,CAAC,EAAE,OAASA,EAAK,CAAC,EAAE,QACxBA,EAAK,CAAC,EAAE,MAAQA,EAAK,CAAC,EAAE,OACxBA,EAAK,CAAC,EAAE,WAAaA,EAAK,CAAC,EAAE,WAC3B,EACA,EACN,OAAOA,EAAKrB,CAAK,CACnB,EACA,CAACjG,EAAMC,IAAW,GAAGD,CAAI,GAAGC,EAAO,QAAQ,GAAGA,EAAO,UAAU,GAAGA,EAAO,UAAU,EACrF,EAEa6H,EAAN,KAAsB,CAG3B,YAAYC,EAAgB,GAAOC,EAAe,CAFlD,KAAQ,MAAQ,EAKd,KAAK,MAAQA,EAAOA,EAAK,OAAS,EAClC,KAAK,KAAOD,EAAgB,IAAM,KAAK,QAAU,IAAM,KAAK,IAAI,CAClE,CA1vBF,MAkvB6B,CAAAhI,EAAA,wBAS7B,EAEIkI,EAQSC,GAAenI,EAAA,SAAUoI,EAAsB,CAC1D,OAAAF,EAAUA,GAAW,SAAS,cAAc,KAAK,EAEjDE,EAAO,OAAOA,CAAI,EAAE,QAAQ,OAAQ,GAAG,EAAE,QAAQ,OAAQ,GAAG,EAAE,QAAQ,OAAQ,GAAG,EACjFF,EAAQ,UAAYE,EAEb,SAASF,EAAQ,WAAY,CACtC,EAP4B,gBAoBrB,SAASG,GAAgBnH,EAAoC,CAClE,MAAO,QAASA,CAClB,CAFgBlB,EAAAqI,GAAA,mBAoBT,IAAMC,GAAcC,EAAA,CACzBC,EACAC,EACAC,EACAC,IACS,CACT,GAAI,CAACA,EACH,OAEF,IAAMC,EAASJ,EAAO,KAAK,GAAG,QAAQ,EACjCI,GAGLJ,EACG,OAAO,MAAM,EACb,KAAKG,CAAK,EACV,KAAK,cAAe,QAAQ,EAC5B,KAAK,IAAKC,EAAO,EAAIA,EAAO,MAAQ,CAAC,EACrC,KAAK,IAAK,CAACF,CAAc,EACzB,KAAK,QAASD,CAAQ,CAC3B,EApB2B,eA8BdI,EAAgBN,EAACO,GAA8D,CAE1F,GAAI,OAAOA,GAAa,SACtB,MAAO,CAACA,EAAUA,EAAW,IAAI,EAGnC,IAAMC,EAAiB,SAASD,GAAY,GAAI,EAAE,EAClD,OAAI,OAAO,MAAMC,CAAc,EAEtB,CAAC,OAAW,MAAS,EACnBD,IAAa,OAAOC,CAAc,EAEpC,CAACA,EAAgBD,EAAW,IAAI,EAEhC,CAACC,EAAgBD,CAAQ,CAEpC,EAhB6B,iBAkBtB,SAASE,GAAiBC,EAAgBC,EAAsB,CACrE,OAAOC,EAAM,CAAC,EAAGF,EAAaC,CAAI,CACpC,CAFgBX,EAAAS,GAAA,iBAIhB,IAAOI,GAAQ,CACb,gBAAAC,EACA,UAAAC,GACA,oBAAAC,GACA,mBAAAC,EACA,wBAAAC,EACA,cAAAT,GACA,WAAAU,GACA,gBAAAC,GACA,mBAAAC,GACA,mBAAAC,GACA,kBAAAC,GACA,wBAAAC,GACA,0BAAAC,GACA,UAAAC,GACA,mBAAAC,GACA,WAAAC,GACA,OAAAC,GACA,QAAAC,GACA,aAAAC,GACA,YAAAhC,GACA,cAAAO,EACA,gBAAA0B,CACF,EAMaC,GAAiBjC,EAAA,SAAUkC,EAAsB,CAC5D,IAAIC,EAAMD,EAEV,OAAAC,EAAMA,EAAI,QAAQ,mBAAoB,SAAUC,EAAW,CACzD,OAAOA,EAAE,UAAU,EAAGA,EAAE,OAAS,CAAC,CACpC,CAAC,EACDD,EAAMA,EAAI,QAAQ,sBAAuB,SAAUC,EAAW,CAC5D,OAAOA,EAAE,UAAU,EAAGA,EAAE,OAAS,CAAC,CACpC,CAAC,EAEDD,EAAMA,EAAI,QAAQ,SAAU,SAAUC,EAAG,CACvC,IAAMC,EAAWD,EAAE,UAAU,EAAGA,EAAE,OAAS,CAAC,EAG5C,MADc,WAAW,KAAKC,CAAQ,EAE7B,iBAAQA,EAAW,WAEnB,aAAOA,EAAW,UAE7B,CAAC,EAEMF,CACT,EAtB8B,kBA6BjBG,GAAiBtC,EAAA,SAAUkC,EAAsB,CAC5D,OAAOA,EAAK,QAAQ,OAAQ,IAAI,EAAE,QAAQ,MAAO,GAAG,EAAE,QAAQ,MAAO,GAAG,CAC1E,EAF8B,kBAQvB,IAAMK,GAAYC,EAAA,CACvBC,EACAC,EACA,CACE,QAAAC,EAAU,EACV,OAAAC,EACA,OAAAC,CACF,EAKAC,IAEIA,GAGG,GAAGF,EAAS,GAAGA,CAAM,IAAM,EAAE,GAAGH,CAAI,IAAIC,CAAE,IAAIC,CAAO,GAAGE,EAAS,IAAIA,CAAM,GAAK,EAAE,GAjBlE,aA0BlB,SAASE,GACdC,EACA,CACA,OAAOA,GAAa,IACtB,CAJgBR,EAAAO,GAAA", "names": ["import_sanitize_url", "ZERO_WIDTH_SPACE", "d3CurveTypes", "basis_default", "basisClosed_default", "basisOpen_default", "bumpX", "bumpY", "bundle_default", "cardinalClosed_default", "cardinalOpen_default", "cardinal_default", "catmullRomClosed_default", "catmullRomOpen_default", "catmullRom_default", "linear_default", "linearClosed_default", "monotoneX", "monotoneY", "natural_default", "step_default", "stepAfter", "stepBefore", "directiveWithoutOpen", "detectInit", "__name", "text", "config", "inits", "detectDirective", "results", "args", "init", "sanitizeDirective", "assignWithDepth_default", "type", "detectType", "prop", "commentWithoutDirectives", "log", "match", "result", "directiveRegex", "error", "removeDirectives", "isSubstringInArray", "str", "arr", "i", "element", "interpolateToCurve", "interpolate", "defaultCurve", "curveName", "formatUrl", "linkStr", "url", "runFunc", "functionName", "params", "arr<PERSON><PERSON>s", "len", "fnName", "obj", "distance", "p1", "p2", "traverseEdge", "points", "prevPoint", "totalDistance", "point", "remainingDistance", "calculatePoint", "calcLabelPosition", "roundNumber", "num", "precision", "factor", "distanceToTraverse", "vectorDistance", "distanceRatio", "calcCardinalityPosition", "isRelationTypePresent", "initialPosition", "center", "d", "angle", "cardinalityPosition", "calcTerminalLabelPosition", "terminalMarkerSize", "position", "_points", "distanceToCardinalityPoint", "getStylesFromArray", "style", "labelStyle", "cnt", "generateId", "makeRandomHex", "length", "characters", "<PERSON><PERSON><PERSON><PERSON>", "random", "options", "getTextObj", "drawSimpleText", "elem", "textData", "nText", "common_default", "_fontSizePx", "parseFontSize", "textElem", "span", "wrapLabel", "memoize_default", "label", "max<PERSON><PERSON><PERSON>", "words", "completedLines", "nextLine", "word", "index", "wordLength", "calculateTextWidth", "next<PERSON><PERSON><PERSON><PERSON><PERSON>", "hyphenatedStrings", "remainingWord", "breakString", "line", "hyphenCharacter", "lines", "currentLine", "character", "currentCharacter", "isLastLine", "hyphenatedNextLine", "calculateTextHeight", "calculateTextDimensions", "fontSize", "fontFamily", "fontWeight", "fontFamilies", "dims", "body", "select_default", "g", "cHeight", "dim", "textObj", "bBox", "InitIDGenerator", "deterministic", "seed", "decoder", "entityDecode", "html", "isDetailedError", "insertTitle", "__name", "parent", "cssClass", "titleTopMargin", "title", "bounds", "parseFontSize", "fontSize", "fontSizeNumber", "cleanAndMerge", "defaultData", "data", "merge_default", "utils_default", "assignWithDepth_default", "wrapLabel", "calculateTextHeight", "calculateTextWidth", "calculateTextDimensions", "detectInit", "detectDirective", "isSubstringInArray", "interpolateToCurve", "calcLabelPosition", "calcCardinalityPosition", "calcTerminalLabelPosition", "formatUrl", "getStylesFromArray", "generateId", "random", "runFunc", "entityDecode", "InitIDGenerator", "encodeEntities", "text", "txt", "s", "innerTxt", "decodeEntities", "getEdgeId", "__name", "from", "to", "counter", "prefix", "suffix", "id", "handleUndefinedAttr", "attrValue"]}