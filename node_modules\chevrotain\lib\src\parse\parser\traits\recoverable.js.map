{"version": 3, "file": "recoverable.js", "sourceRoot": "", "sources": ["../../../../../src/parse/parser/traits/recoverable.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,mBAAmB,EACnB,GAAG,EACH,YAAY,GACb,MAAM,gCAAgC,CAAC;AAKxC,OAAO,EACL,KAAK,EACL,SAAS,EACT,IAAI,EACJ,OAAO,EACP,GAAG,EACH,QAAQ,EACR,OAAO,EACP,GAAG,GACJ,MAAM,WAAW,CAAC;AAOnB,OAAO,EAAE,wBAAwB,EAAE,MAAM,4BAA4B,CAAC;AACtE,OAAO,EAAE,EAAE,EAAE,MAAM,oBAAoB,CAAC;AAExC,OAAO,EAAE,qBAAqB,EAAE,MAAM,cAAc,CAAC;AAErD,MAAM,CAAC,MAAM,cAAc,GAAQ,EAAE,CAAC;AAQtC,MAAM,CAAC,MAAM,0BAA0B,GAAG,yBAAyB,CAAC;AAEpE,MAAM,OAAO,uBAAwB,SAAQ,KAAK;IAChD,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,0BAA0B,CAAC;IACzC,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,WAAW;IAKtB,eAAe,CAAC,MAAqB;QACnC,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QAExB,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC,MAAM,EAAE,iBAAiB,CAAC;YACnD,CAAC,CAAE,MAAM,CAAC,eAA2B,CAAC,0DAA0D;YAChG,CAAC,CAAC,qBAAqB,CAAC,eAAe,CAAC;QAE1C,uDAAuD;QACvD,8DAA8D;QAC9D,iBAAiB;QACjB,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI,CAAC,2BAA2B,GAAG,2BAA2B,CAAC;SAChE;IACH,CAAC;IAEM,gBAAgB,CAAC,OAAkB;QACxC,MAAM,WAAW,GAAG,mBAAmB,CACrC,OAAO,EACP,EAAE,EACF,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,CACJ,CAAC;QACF,WAAW,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACxC,OAAO,WAAW,CAAC;IACrB,CAAC;IAEM,gCAAgC,CAAC,OAAkB;QACxD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,+BAA+B,CAAC,OAAkB;QACvD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,uBAAuB,CAErB,WAAqB,EACrB,eAAsB,EACtB,aAA4B,EAC5B,eAA0B;QAE1B,2CAA2C;QAC3C,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACjD,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAChD,MAAM,cAAc,GAAa,EAAE,CAAC;QACpC,IAAI,iBAAiB,GAAG,KAAK,CAAC;QAE9B,MAAM,sBAAsB,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1C,IAAI,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAE3B,MAAM,oBAAoB,GAAG,GAAG,EAAE;YAChC,MAAM,aAAa,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACjC,+FAA+F;YAC/F,wCAAwC;YACxC,MAAM,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAC,yBAAyB,CAAC;gBAC9D,QAAQ,EAAE,eAAe;gBACzB,MAAM,EAAE,sBAAsB;gBAC9B,QAAQ,EAAE,aAAa;gBACvB,QAAQ,EAAE,IAAI,CAAC,mBAAmB,EAAE;aACrC,CAAC,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,wBAAwB,CACxC,GAAG,EACH,sBAAsB,EACtB,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CACX,CAAC;YACF,iHAAiH;YACjH,KAAK,CAAC,cAAc,GAAG,SAAS,CAAC,cAAc,CAAC,CAAC;YACjD,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC,CAAC;QAEF,OAAO,CAAC,iBAAiB,EAAE;YACzB,gEAAgE;YAChE,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,eAAe,CAAC,EAAE;gBACjD,oBAAoB,EAAE,CAAC;gBACvB,OAAO,CAAC,mDAAmD;aAC5D;iBAAM,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACnC,6GAA6G;gBAC7G,oBAAoB,EAAE,CAAC;gBACvB,2GAA2G;gBAC3G,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;gBACzC,OAAO,CAAC,mDAAmD;aAC5D;iBAAM,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC,EAAE;gBACtD,iBAAiB,GAAG,IAAI,CAAC;aAC1B;iBAAM;gBACL,SAAS,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;gBAC9B,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;aACnD;SACF;QAED,0FAA0F;QAC1F,6GAA6G;QAC7G,qDAAqD;QACrD,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;IACzC,CAAC;IAED,iCAAiC,CAE/B,uBAAkC,EAClC,UAAkB,EAClB,QAA6B;QAE7B,8DAA8D;QAC9D,gEAAgE;QAChE,IAAI,QAAQ,KAAK,KAAK,EAAE;YACtB,OAAO,KAAK,CAAC;SACd;QAED,sDAAsD;QACtD,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,uBAAuB,CAAC,EAAE;YAC1D,OAAO,KAAK,CAAC;SACd;QAED,uGAAuG;QACvG,oEAAoE;QACpE,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE;YACzB,OAAO,KAAK,CAAC;SACd;QAED,kHAAkH;QAClH,mGAAmG;QACnG,qCAAqC;QACrC,IACE,IAAI,CAAC,wBAAwB,CAC3B,uBAAuB,EACvB,IAAI,CAAC,2BAA2B,CAAC,uBAAuB,EAAE,UAAU,CAAC,CACtE,EACD;YACA,OAAO,KAAK,CAAC;SACd;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,+BAA+B;IAC/B,2BAA2B,CAEzB,OAAkB,EAClB,YAAoB;QAEpB,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QACtE,MAAM,OAAO,GAAG,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;QAC5D,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,iBAAiB,CAEf,eAA0B,EAC1B,OAAoB;QAEpB,IAAI,IAAI,CAAC,kCAAkC,CAAC,eAAe,EAAE,OAAO,CAAC,EAAE;YACrE,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;YAC3D,OAAO,WAAW,CAAC;SACpB;QAED,IAAI,IAAI,CAAC,iCAAiC,CAAC,eAAe,CAAC,EAAE;YAC3D,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YAClC,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,OAAO,OAAO,CAAC;SAChB;QAED,MAAM,IAAI,uBAAuB,CAAC,eAAe,CAAC,CAAC;IACrD,CAAC;IAED,wBAAwB,CAEtB,aAAwB,EACxB,OAAoB;QAEpB,OAAO,CACL,IAAI,CAAC,kCAAkC,CAAC,aAAa,EAAE,OAAO,CAAC;YAC/D,IAAI,CAAC,iCAAiC,CAAC,aAAa,CAAC,CACtD,CAAC;IACJ,CAAC;IAED,kCAAkC,CAEhC,eAA0B,EAC1B,OAAoB;QAEpB,IAAI,CAAC,IAAI,CAAC,gCAAgC,CAAC,eAAe,CAAC,EAAE;YAC3D,OAAO,KAAK,CAAC;SACd;QAED,4EAA4E;QAC5E,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;YACpB,OAAO,KAAK,CAAC;SACd;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,wBAAwB,GAC5B,IAAI,CAAC,OAAO,EAAE,CAAC,sBAAiC,EAAE,EAAE;YAClD,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,sBAAsB,CAAC,CAAC;QAClE,CAAC,CAAC,KAAK,SAAS,CAAC;QAEnB,OAAO,wBAAwB,CAAC;IAClC,CAAC;IAED,iCAAiC,CAE/B,eAA0B;QAE1B,IAAI,CAAC,IAAI,CAAC,+BAA+B,CAAC,eAAe,CAAC,EAAE;YAC1D,OAAO,KAAK,CAAC;SACd;QAED,MAAM,yBAAyB,GAAG,IAAI,CAAC,YAAY,CACjD,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EACV,eAAe,CAChB,CAAC;QACF,OAAO,yBAAyB,CAAC;IACnC,CAAC;IAED,wBAAwB,CAEtB,YAAuB;QAEvB,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1C,MAAM,oBAAoB,GAAG,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC;QACvE,OAAO,QAAQ,CAAC,oBAAoB,EAAE,YAAY,CAAC,CAAC;IACtD,CAAC;IAED,mBAAmB;QACjB,MAAM,yBAAyB,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1D,gHAAgH;QAChH,IAAI,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC3B,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,OAAO,IAAI,EAAE;YACX,MAAM,UAAU,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC,aAAa,EAAE,EAAE;gBACnE,MAAM,QAAQ,GAAG,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;gBACxD,OAAO,QAAQ,CAAC;YAClB,CAAC,CAAC,CAAC;YACH,IAAI,UAAU,KAAK,SAAS,EAAE;gBAC5B,OAAO,UAAU,CAAC;aACnB;YACD,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,EAAE,CAAC;SACL;IACH,CAAC;IAED,gBAAgB;QACd,kGAAkG;QAClG,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAChC,OAAO,cAAc,CAAC;SACvB;QACD,MAAM,iBAAiB,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAC9D,MAAM,WAAW,GAAG,IAAI,CAAC,kCAAkC,EAAE,CAAC;QAC9D,MAAM,iBAAiB,GAAG,IAAI,CAAC,gCAAgC,EAAE,CAAC;QAElE,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC;YACzD,gBAAgB,EAAE,WAAW;YAC7B,MAAM,EAAE,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC;SACxD,CAAC;IACJ,CAAC;IAED,uBAAuB;QACrB,MAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAC;QAC1C,MAAM,uBAAuB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QAE3D,OAAO,GAAG,CAAC,iBAAiB,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,EAAE;YAC9C,IAAI,GAAG,KAAK,CAAC,EAAE;gBACb,OAAO,cAAc,CAAC;aACvB;YACD,OAAO;gBACL,QAAQ,EAAE,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC;gBAChD,gBAAgB,EAAE,uBAAuB,CAAC,GAAG,CAAC;gBAC9C,MAAM,EAAE,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;aACjE,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,gBAAgB;QACd,MAAM,WAAW,GAAG,GAAG,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,CAAC,OAAO,EAAE,EAAE;YAClE,OAAO,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QACH,OAAY,OAAO,CAAC,WAAW,CAAC,CAAC;IACnC,CAAC;IAED,yBAAyB,CAEvB,SAAqB;QAErB,IAAI,SAAS,KAAK,cAAc,EAAE;YAChC,OAAO,CAAC,GAAG,CAAC,CAAC;SACd;QAED,MAAM,UAAU,GACd,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,gBAAgB,GAAG,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC;QAE1E,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IACxC,CAAC;IAED,2FAA2F;IAC3F,sGAAsG;IACtG,iBAAiB,CAEf,KAAa,EACb,YAAsB;QAEtB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE;YAClC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC1B;QACD,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,QAAQ,CAAsB,OAAkB;QAC9C,MAAM,cAAc,GAAa,EAAE,CAAC;QACpC,IAAI,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,KAAK,EAAE;YACpD,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YAC5B,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;SACjD;QACD,2CAA2C;QAC3C,OAAO,SAAS,CAAC,cAAc,CAAC,CAAC;IACnC,CAAC;IAED,2BAA2B,CAEzB,QAAkB,EAClB,IAAW,EACX,aAA4B,EAC5B,YAAoB,EACpB,cAAsB,EACtB,cAAgE,EAChE,QAAkB;QAElB,6BAA6B;QAC7B,mEAAmE;IACrE,CAAC;IAED,qBAAqB,CAEnB,OAAkB,EAClB,YAAoB;QAEpB,MAAM,aAAa,GAAa,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjE,MAAM,mBAAmB,GAAa,KAAK,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACxE,MAAM,WAAW,GAAQ;YACvB,SAAS,EAAE,aAAa;YACxB,eAAe,EAAE,mBAAmB;YACpC,OAAO,EAAE,OAAO;YAChB,iBAAiB,EAAE,YAAY;SAChC,CAAC;QAEF,OAAO,WAAW,CAAC;IACrB,CAAC;IACD,yBAAyB;QACvB,OAAO,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,aAAa,EAAE,EAAE,CAC5C,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAC5C,CAAC;IACJ,CAAC;CACF;AAED,MAAM,UAAU,2BAA2B,CAEzC,QAAkB,EAClB,IAAW,EACX,aAA4B,EAC5B,YAAoB,EACpB,cAAsB,EACtB,cAAgE,EAChE,QAAkB;IAElB,MAAM,GAAG,GAAG,IAAI,CAAC,2BAA2B,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;IAC3E,IAAI,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;IACnD,IAAI,iBAAiB,KAAK,SAAS,EAAE;QACnC,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAChD,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC,YAAY,CAAC,CAAC;QAC5D,MAAM,MAAM,GACV,IAAI,cAAc,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QAClD,iBAAiB,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;QAC1C,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,iBAAiB,CAAC;KAChD;IAED,IAAI,uBAAuB,GAAG,iBAAiB,CAAC,KAAK,CAAC;IACtD,IAAI,UAAU,GAAG,iBAAiB,CAAC,UAAU,CAAC;IAC9C,MAAM,WAAW,GAAG,iBAAiB,CAAC,WAAW,CAAC;IAElD,+EAA+E;IAC/E,mEAAmE;IACnE,IACE,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC;QAC5B,WAAW;QACX,uBAAuB,KAAK,SAAS,EACrC;QACA,uBAAuB,GAAG,GAAG,CAAC;QAC9B,UAAU,GAAG,CAAC,CAAC;KAChB;IAED,0CAA0C;IAC1C,+FAA+F;IAC/F,IAAI,uBAAuB,KAAK,SAAS,IAAI,UAAU,KAAK,SAAS,EAAE;QACrE,OAAO;KACR;IAED,IACE,IAAI,CAAC,iCAAiC,CACpC,uBAAuB,EACvB,UAAU,EACV,QAAQ,CACT,EACD;QACA,uFAAuF;QACvF,+FAA+F;QAC/F,iDAAiD;QACjD,IAAI,CAAC,uBAAuB,CAC1B,QAAQ,EACR,IAAI,EACJ,aAAa,EACb,uBAAuB,CACxB,CAAC;KACH;AACH,CAAC"}