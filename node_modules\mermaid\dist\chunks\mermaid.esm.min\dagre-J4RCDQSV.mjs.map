{"version": 3, "sources": ["../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/graphlib/json.js", "../../../src/rendering-util/layout-algorithms/dagre/mermaid-graphlib.js", "../../../src/rendering-util/layout-algorithms/dagre/index.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { Graph } from './graph.js';\n\nexport { write, read };\n\nfunction write(g) {\n  var json = {\n    options: {\n      directed: g.isDirected(),\n      multigraph: g.isMultigraph(),\n      compound: g.isCompound(),\n    },\n    nodes: writeNodes(g),\n    edges: writeEdges(g),\n  };\n  if (!_.isUndefined(g.graph())) {\n    json.value = _.clone(g.graph());\n  }\n  return json;\n}\n\nfunction writeNodes(g) {\n  return _.map(g.nodes(), function (v) {\n    var nodeValue = g.node(v);\n    var parent = g.parent(v);\n    var node = { v: v };\n    if (!_.isUndefined(nodeValue)) {\n      node.value = nodeValue;\n    }\n    if (!_.isUndefined(parent)) {\n      node.parent = parent;\n    }\n    return node;\n  });\n}\n\nfunction writeEdges(g) {\n  return _.map(g.edges(), function (e) {\n    var edgeValue = g.edge(e);\n    var edge = { v: e.v, w: e.w };\n    if (!_.isUndefined(e.name)) {\n      edge.name = e.name;\n    }\n    if (!_.isUndefined(edgeValue)) {\n      edge.value = edgeValue;\n    }\n    return edge;\n  });\n}\n\nfunction read(json) {\n  var g = new Graph(json.options).setGraph(json.value);\n  _.each(json.nodes, function (entry) {\n    g.setNode(entry.v, entry.value);\n    if (entry.parent) {\n      g.setParent(entry.v, entry.parent);\n    }\n  });\n  _.each(json.edges, function (entry) {\n    g.setEdge({ v: entry.v, w: entry.w, name: entry.name }, entry.value);\n  });\n  return g;\n}\n", "/** Decorates with functions required by mermaids dagre-wrapper. */\nimport { log } from '../../../logger.js';\nimport * as graphlib from 'dagre-d3-es/src/graphlib/index.js';\nimport * as graphlibJson from 'dagre-d3-es/src/graphlib/json.js';\n\nexport let clusterDb = new Map();\nlet descendants = new Map();\nlet parents = new Map();\n\nexport const clear = () => {\n  descendants.clear();\n  parents.clear();\n  clusterDb.clear();\n};\n\nconst isDescendant = (id, ancestorId) => {\n  const ancestorDescendants = descendants.get(ancestorId) || [];\n  log.trace('In isDescendant', ancestorId, ' ', id, ' = ', ancestorDescendants.includes(id));\n  return ancestorDescendants.includes(id);\n};\n\nconst edgeInCluster = (edge, clusterId) => {\n  const clusterDescendants = descendants.get(clusterId) || [];\n  log.info('Descendants of ', clusterId, ' is ', clusterDescendants);\n  log.info('Edge is ', edge);\n  if (edge.v === clusterId || edge.w === clusterId) {\n    return false;\n  }\n\n  if (!clusterDescendants) {\n    log.debug('Tilt, ', clusterId, ',not in descendants');\n    return false;\n  }\n\n  return (\n    clusterDescendants.includes(edge.v) ||\n    isDescendant(edge.v, clusterId) ||\n    isDescendant(edge.w, clusterId) ||\n    clusterDescendants.includes(edge.w)\n  );\n};\n\nconst copy = (clusterId, graph, newGraph, rootId) => {\n  log.warn(\n    'Copying children of ',\n    clusterId,\n    'root',\n    rootId,\n    'data',\n    graph.node(clusterId),\n    rootId\n  );\n  const nodes = graph.children(clusterId) || [];\n\n  if (clusterId !== rootId) {\n    nodes.push(clusterId);\n  }\n\n  log.warn('Copying (nodes) clusterId', clusterId, 'nodes', nodes);\n\n  nodes.forEach((node) => {\n    if (graph.children(node).length > 0) {\n      copy(node, graph, newGraph, rootId);\n    } else {\n      const data = graph.node(node);\n      log.info('cp ', node, ' to ', rootId, ' with parent ', clusterId);\n      newGraph.setNode(node, data);\n      if (rootId !== graph.parent(node)) {\n        log.warn('Setting parent', node, graph.parent(node));\n        newGraph.setParent(node, graph.parent(node));\n      }\n\n      if (clusterId !== rootId && node !== clusterId) {\n        log.debug('Setting parent', node, clusterId);\n        newGraph.setParent(node, clusterId);\n      } else {\n        log.info('In copy ', clusterId, 'root', rootId, 'data', graph.node(clusterId), rootId);\n        log.debug(\n          'Not Setting parent for node=',\n          node,\n          'cluster!==rootId',\n          clusterId !== rootId,\n          'node!==clusterId',\n          node !== clusterId\n        );\n      }\n      const edges = graph.edges(node);\n      log.debug('Copying Edges', edges);\n      edges.forEach((edge) => {\n        log.info('Edge', edge);\n        const data = graph.edge(edge.v, edge.w, edge.name);\n        log.info('Edge data', data, rootId);\n        try {\n          if (edgeInCluster(edge, rootId)) {\n            log.info('Copying as ', edge.v, edge.w, data, edge.name);\n            newGraph.setEdge(edge.v, edge.w, data, edge.name);\n            log.info('newGraph edges ', newGraph.edges(), newGraph.edge(newGraph.edges()[0]));\n          } else {\n            log.info(\n              'Skipping copy of edge ',\n              edge.v,\n              '-->',\n              edge.w,\n              ' rootId: ',\n              rootId,\n              ' clusterId:',\n              clusterId\n            );\n          }\n        } catch (e) {\n          log.error(e);\n        }\n      });\n    }\n    log.debug('Removing node', node);\n    graph.removeNode(node);\n  });\n};\n\nexport const extractDescendants = (id, graph) => {\n  const children = graph.children(id);\n  let res = [...children];\n\n  for (const child of children) {\n    parents.set(child, id);\n    res = [...res, ...extractDescendants(child, graph)];\n  }\n\n  return res;\n};\n\nexport const validate = (graph) => {\n  const edges = graph.edges();\n  log.trace('Edges: ', edges);\n  for (const edge of edges) {\n    if (graph.children(edge.v).length > 0) {\n      log.trace('The node ', edge.v, ' is part of and edge even though it has children');\n      return false;\n    }\n    if (graph.children(edge.w).length > 0) {\n      log.trace('The node ', edge.w, ' is part of and edge even though it has children');\n      return false;\n    }\n  }\n  return true;\n};\n\nconst findCommonEdges = (graph, id1, id2) => {\n  const edges1 = graph.edges().filter((edge) => edge.v === id1 || edge.w === id1);\n  const edges2 = graph.edges().filter((edge) => edge.v === id2 || edge.w === id2);\n  const edges1Prim = edges1.map((edge) => {\n    return { v: edge.v === id1 ? id2 : edge.v, w: edge.w === id1 ? id1 : edge.w };\n  });\n  const edges2Prim = edges2.map((edge) => {\n    return { v: edge.v, w: edge.w };\n  });\n  const result = edges1Prim.filter((edgeIn1) => {\n    return edges2Prim.some((edge) => edgeIn1.v === edge.v && edgeIn1.w === edge.w);\n  });\n\n  return result;\n};\n\nexport const findNonClusterChild = (id, graph, clusterId) => {\n  const children = graph.children(id);\n  log.trace('Searching children of id ', id, children);\n  if (children.length < 1) {\n    return id;\n  }\n  let reserve;\n  for (const child of children) {\n    const _id = findNonClusterChild(child, graph, clusterId);\n\n    const commonEdges = findCommonEdges(graph, clusterId, _id);\n\n    if (_id) {\n      if (commonEdges.length > 0) {\n        reserve = _id;\n      } else {\n        return _id;\n      }\n    }\n  }\n  return reserve;\n};\n\nconst getAnchorId = (id) => {\n  if (!clusterDb.has(id)) {\n    return id;\n  }\n  if (!clusterDb.get(id).externalConnections) {\n    return id;\n  }\n\n  if (clusterDb.has(id)) {\n    return clusterDb.get(id).id;\n  }\n  return id;\n};\n\nexport const adjustClustersAndEdges = (graph, depth) => {\n  if (!graph || depth > 10) {\n    log.debug('Opting out, no graph ');\n    return;\n  } else {\n    log.debug('Opting in, graph ');\n  }\n\n  graph.nodes().forEach(function (id) {\n    const children = graph.children(id);\n    if (children.length > 0) {\n      log.warn(\n        'Cluster identified',\n        id,\n        ' Replacement id in edges: ',\n        findNonClusterChild(id, graph, id)\n      );\n      descendants.set(id, extractDescendants(id, graph));\n      clusterDb.set(id, { id: findNonClusterChild(id, graph, id), clusterData: graph.node(id) });\n    }\n  });\n\n  graph.nodes().forEach(function (id) {\n    const children = graph.children(id);\n    const edges = graph.edges();\n    if (children.length > 0) {\n      log.debug('Cluster identified', id, descendants);\n      edges.forEach((edge) => {\n        const d1 = isDescendant(edge.v, id);\n        const d2 = isDescendant(edge.w, id);\n\n        if (d1 ^ d2) {\n          log.warn('Edge: ', edge, ' leaves cluster ', id);\n          log.warn('Descendants of XXX ', id, ': ', descendants.get(id));\n          clusterDb.get(id).externalConnections = true;\n        }\n      });\n    } else {\n      log.debug('Not a cluster ', id, descendants);\n    }\n  });\n\n  for (let id of clusterDb.keys()) {\n    const nonClusterChild = clusterDb.get(id).id;\n    const parent = graph.parent(nonClusterChild);\n\n    if (parent !== id && clusterDb.has(parent) && !clusterDb.get(parent).externalConnections) {\n      clusterDb.get(id).id = parent;\n    }\n  }\n\n  graph.edges().forEach(function (e) {\n    const edge = graph.edge(e);\n    log.warn('Edge ' + e.v + ' -> ' + e.w + ': ' + JSON.stringify(e));\n    log.warn('Edge ' + e.v + ' -> ' + e.w + ': ' + JSON.stringify(graph.edge(e)));\n\n    let v = e.v;\n    let w = e.w;\n    log.warn(\n      'Fix XXX',\n      clusterDb,\n      'ids:',\n      e.v,\n      e.w,\n      'Translating: ',\n      clusterDb.get(e.v),\n      ' --- ',\n      clusterDb.get(e.w)\n    );\n    if (clusterDb.get(e.v) || clusterDb.get(e.w)) {\n      log.warn('Fixing and trying - removing XXX', e.v, e.w, e.name);\n      v = getAnchorId(e.v);\n      w = getAnchorId(e.w);\n      graph.removeEdge(e.v, e.w, e.name);\n      if (v !== e.v) {\n        const parent = graph.parent(v);\n        clusterDb.get(parent).externalConnections = true;\n        edge.fromCluster = e.v;\n      }\n      if (w !== e.w) {\n        const parent = graph.parent(w);\n        clusterDb.get(parent).externalConnections = true;\n        edge.toCluster = e.w;\n      }\n      log.warn('Fix Replacing with XXX', v, w, e.name);\n      graph.setEdge(v, w, edge, e.name);\n    }\n  });\n  log.warn('Adjusted Graph', graphlibJson.write(graph));\n  extractor(graph, 0);\n\n  log.trace(clusterDb);\n};\n\nexport const extractor = (graph, depth) => {\n  log.warn('extractor - ', depth, graphlibJson.write(graph), graph.children('D'));\n  if (depth > 10) {\n    log.error('Bailing out');\n    return;\n  }\n  let nodes = graph.nodes();\n  let hasChildren = false;\n  for (const node of nodes) {\n    const children = graph.children(node);\n    hasChildren = hasChildren || children.length > 0;\n  }\n\n  if (!hasChildren) {\n    log.debug('Done, no node has children', graph.nodes());\n    return;\n  }\n  log.debug('Nodes = ', nodes, depth);\n  for (const node of nodes) {\n    log.debug(\n      'Extracting node',\n      node,\n      clusterDb,\n      clusterDb.has(node) && !clusterDb.get(node).externalConnections,\n      !graph.parent(node),\n      graph.node(node),\n      graph.children('D'),\n      ' Depth ',\n      depth\n    );\n    if (!clusterDb.has(node)) {\n      log.debug('Not a cluster', node, depth);\n    } else if (\n      !clusterDb.get(node).externalConnections &&\n      graph.children(node) &&\n      graph.children(node).length > 0\n    ) {\n      log.warn(\n        'Cluster without external connections, without a parent and with children',\n        node,\n        depth\n      );\n\n      const graphSettings = graph.graph();\n      let dir = graphSettings.rankdir === 'TB' ? 'LR' : 'TB';\n      if (clusterDb.get(node)?.clusterData?.dir) {\n        dir = clusterDb.get(node).clusterData.dir;\n        log.warn('Fixing dir', clusterDb.get(node).clusterData.dir, dir);\n      }\n\n      const clusterGraph = new graphlib.Graph({\n        multigraph: true,\n        compound: true,\n      })\n        .setGraph({\n          rankdir: dir,\n          nodesep: 50,\n          ranksep: 50,\n          marginx: 8,\n          marginy: 8,\n        })\n        .setDefaultEdgeLabel(function () {\n          return {};\n        });\n\n      log.warn('Old graph before copy', graphlibJson.write(graph));\n      copy(node, graph, clusterGraph, node);\n      graph.setNode(node, {\n        clusterNode: true,\n        id: node,\n        clusterData: clusterDb.get(node).clusterData,\n        label: clusterDb.get(node).label,\n        graph: clusterGraph,\n      });\n      log.warn('New graph after copy node: (', node, ')', graphlibJson.write(clusterGraph));\n      log.debug('Old graph after copy', graphlibJson.write(graph));\n    } else {\n      log.warn(\n        'Cluster ** ',\n        node,\n        ' **not meeting the criteria !externalConnections:',\n        !clusterDb.get(node).externalConnections,\n        ' no parent: ',\n        !graph.parent(node),\n        ' children ',\n        graph.children(node) && graph.children(node).length > 0,\n        graph.children('D'),\n        depth\n      );\n      log.debug(clusterDb);\n    }\n  }\n\n  nodes = graph.nodes();\n  log.warn('New list of nodes', nodes);\n  for (const node of nodes) {\n    const data = graph.node(node);\n    log.warn(' Now next level', node, data);\n    if (data?.clusterNode) {\n      extractor(data.graph, depth + 1);\n    }\n  }\n};\n\nconst sorter = (graph, nodes) => {\n  if (nodes.length === 0) {\n    return [];\n  }\n  let result = Object.assign([], nodes);\n  nodes.forEach((node) => {\n    const children = graph.children(node);\n    const sorted = sorter(graph, children);\n    result = [...result, ...sorted];\n  });\n\n  return result;\n};\n\nexport const sortNodesByHierarchy = (graph) => sorter(graph, graph.children());\n", "import { layout as dagreLayout } from 'dagre-d3-es/src/dagre/index.js';\nimport * as graphlibJson from 'dagre-d3-es/src/graphlib/json.js';\nimport * as graphlib from 'dagre-d3-es/src/graphlib/index.js';\nimport insertMarkers from '../../rendering-elements/markers.js';\nimport { updateNodeBounds } from '../../rendering-elements/shapes/util.js';\nimport {\n  clear as clearGraphlib,\n  clusterDb,\n  adjustClustersAndEdges,\n  findNonClusterChild,\n  sortNodesByHierarchy,\n} from './mermaid-graphlib.js';\nimport {\n  insertNode,\n  positionNode,\n  clear as clearNodes,\n  setNodeElem,\n} from '../../rendering-elements/nodes.js';\nimport { insertCluster, clear as clearClusters } from '../../rendering-elements/clusters.js';\nimport {\n  insertEdgeLabel,\n  positionEdgeLabel,\n  insertEdge,\n  clear as clearEdges,\n} from '../../rendering-elements/edges.js';\nimport { log } from '../../../logger.js';\nimport { getSubGraphTitleMargins } from '../../../utils/subGraphTitleMargins.js';\nimport { getConfig } from '../../../diagram-api/diagramAPI.js';\n\nconst recursiveRender = async (_elem, graph, diagramType, id, parentCluster, siteConfig) => {\n  log.warn('Graph in recursive render:XAX', graphlibJson.write(graph), parentCluster);\n  const dir = graph.graph().rankdir;\n  log.trace('Dir in recursive render - dir:', dir);\n\n  const elem = _elem.insert('g').attr('class', 'root');\n  if (!graph.nodes()) {\n    log.info('No nodes found for', graph);\n  } else {\n    log.info('Recursive render XXX', graph.nodes());\n  }\n  if (graph.edges().length > 0) {\n    log.info('Recursive edges', graph.edge(graph.edges()[0]));\n  }\n  const clusters = elem.insert('g').attr('class', 'clusters');\n  const edgePaths = elem.insert('g').attr('class', 'edgePaths');\n  const edgeLabels = elem.insert('g').attr('class', 'edgeLabels');\n  const nodes = elem.insert('g').attr('class', 'nodes');\n\n  // Insert nodes, this will insert them into the dom and each node will get a size. The size is updated\n  // to the abstract node and is later used by dagre for the layout\n  await Promise.all(\n    graph.nodes().map(async function (v) {\n      const node = graph.node(v);\n      if (parentCluster !== undefined) {\n        const data = JSON.parse(JSON.stringify(parentCluster.clusterData));\n        // data.clusterPositioning = true;\n        log.trace(\n          'Setting data for parent cluster XXX\\n Node.id = ',\n          v,\n          '\\n data=',\n          data.height,\n          '\\nParent cluster',\n          parentCluster.height\n        );\n        graph.setNode(parentCluster.id, data);\n        if (!graph.parent(v)) {\n          log.trace('Setting parent', v, parentCluster.id);\n          graph.setParent(v, parentCluster.id, data);\n        }\n      }\n      log.info('(Insert) Node XXX' + v + ': ' + JSON.stringify(graph.node(v)));\n      if (node?.clusterNode) {\n        // const children = graph.children(v);\n        log.info('Cluster identified XBX', v, node.width, graph.node(v));\n\n        // `node.graph.setGraph` applies the graph configurations such as nodeSpacing to subgraphs as without this the default values would be used\n        // We override only the `ranksep` and `nodesep` configurations to allow for setting subgraph spacing while avoiding overriding other properties\n        const { ranksep, nodesep } = graph.graph();\n        node.graph.setGraph({\n          ...node.graph.graph(),\n          ranksep: ranksep + 25,\n          nodesep,\n        });\n\n        // \"o\" will contain the full cluster not just the children\n        const o = await recursiveRender(\n          nodes,\n          node.graph,\n          diagramType,\n          id,\n          graph.node(v),\n          siteConfig\n        );\n        const newEl = o.elem;\n        updateNodeBounds(node, newEl);\n        // node.height = o.diff;\n        node.diff = o.diff || 0;\n        log.info(\n          'New compound node after recursive render XAX',\n          v,\n          'width',\n          // node,\n          node.width,\n          'height',\n          node.height\n          // node.x,\n          // node.y\n        );\n        setNodeElem(newEl, node);\n      } else {\n        if (graph.children(v).length > 0) {\n          // This is a cluster but not to be rendered recursively\n          // Render as before\n          log.trace(\n            'Cluster - the non recursive path XBX',\n            v,\n            node.id,\n            node,\n            node.width,\n            'Graph:',\n            graph\n          );\n          log.trace(findNonClusterChild(node.id, graph));\n          clusterDb.set(node.id, { id: findNonClusterChild(node.id, graph), node });\n          // insertCluster(clusters, graph.node(v));\n        } else {\n          log.trace('Node - the non recursive path XAX', v, nodes, graph.node(v), dir);\n          await insertNode(nodes, graph.node(v), { config: siteConfig, dir });\n        }\n      }\n    })\n  );\n\n  const processEdges = async () => {\n    const edgePromises = graph.edges().map(async function (e) {\n      const edge = graph.edge(e.v, e.w, e.name);\n      log.info('Edge ' + e.v + ' -> ' + e.w + ': ' + JSON.stringify(e));\n      log.info('Edge ' + e.v + ' -> ' + e.w + ': ', e, ' ', JSON.stringify(graph.edge(e)));\n\n      // Check if link is either from or to a cluster\n      log.info(\n        'Fix',\n        clusterDb,\n        'ids:',\n        e.v,\n        e.w,\n        'Translating: ',\n        clusterDb.get(e.v),\n        clusterDb.get(e.w)\n      );\n      await insertEdgeLabel(edgeLabels, edge);\n    });\n\n    await Promise.all(edgePromises);\n  };\n\n  await processEdges();\n\n  log.info('Graph before layout:', JSON.stringify(graphlibJson.write(graph)));\n\n  log.info('############################################# XXX');\n  log.info('###                Layout                 ### XXX');\n  log.info('############################################# XXX');\n\n  dagreLayout(graph);\n\n  log.info('Graph after layout:', JSON.stringify(graphlibJson.write(graph)));\n  // Move the nodes to the correct place\n  let diff = 0;\n  let { subGraphTitleTotalMargin } = getSubGraphTitleMargins(siteConfig);\n  await Promise.all(\n    sortNodesByHierarchy(graph).map(async function (v) {\n      const node = graph.node(v);\n      log.info(\n        'Position XBX => ' + v + ': (' + node.x,\n        ',' + node.y,\n        ') width: ',\n        node.width,\n        ' height: ',\n        node.height\n      );\n      if (node?.clusterNode) {\n        // Adjust for padding when on root level\n        node.y += subGraphTitleTotalMargin;\n\n        log.info(\n          'A tainted cluster node XBX1',\n          v,\n          node.id,\n          node.width,\n          node.height,\n          node.x,\n          node.y,\n          graph.parent(v)\n        );\n        clusterDb.get(node.id).node = node;\n        positionNode(node);\n      } else {\n        // A tainted cluster node\n        if (graph.children(v).length > 0) {\n          log.info(\n            'A pure cluster node XBX1',\n            v,\n            node.id,\n            node.x,\n            node.y,\n            node.width,\n            node.height,\n            graph.parent(v)\n          );\n          node.height += subGraphTitleTotalMargin;\n          graph.node(node.parentId);\n          const halfPadding = node?.padding / 2 || 0;\n          const labelHeight = node?.labelBBox?.height || 0;\n          const offsetY = labelHeight - halfPadding || 0;\n          log.debug('OffsetY', offsetY, 'labelHeight', labelHeight, 'halfPadding', halfPadding);\n          await insertCluster(clusters, node);\n\n          // A cluster in the non-recursive way\n          clusterDb.get(node.id).node = node;\n        } else {\n          // Regular node\n          const parent = graph.node(node.parentId);\n          node.y += subGraphTitleTotalMargin / 2;\n          log.info(\n            'A regular node XBX1 - using the padding',\n            node.id,\n            'parent',\n            node.parentId,\n            node.width,\n            node.height,\n            node.x,\n            node.y,\n            'offsetY',\n            node.offsetY,\n            'parent',\n            parent,\n            parent?.offsetY,\n            node\n          );\n\n          positionNode(node);\n        }\n      }\n    })\n  );\n\n  // Move the edge labels to the correct place after layout\n  graph.edges().forEach(function (e) {\n    const edge = graph.edge(e);\n    log.info('Edge ' + e.v + ' -> ' + e.w + ': ' + JSON.stringify(edge), edge);\n\n    edge.points.forEach((point) => (point.y += subGraphTitleTotalMargin / 2));\n    const startNode = graph.node(e.v);\n    var endNode = graph.node(e.w);\n    const paths = insertEdge(edgePaths, edge, clusterDb, diagramType, startNode, endNode, id);\n    positionEdgeLabel(edge, paths);\n  });\n\n  graph.nodes().forEach(function (v) {\n    const n = graph.node(v);\n    log.info(v, n.type, n.diff);\n    if (n.isGroup) {\n      diff = n.diff;\n    }\n  });\n  log.warn('Returning from recursive render XAX', elem, diff);\n  return { elem, diff };\n};\n\nexport const render = async (data4Layout, svg) => {\n  const graph = new graphlib.Graph({\n    multigraph: true,\n    compound: true,\n  })\n    .setGraph({\n      rankdir: data4Layout.direction,\n      nodesep:\n        data4Layout.config?.nodeSpacing ||\n        data4Layout.config?.flowchart?.nodeSpacing ||\n        data4Layout.nodeSpacing,\n      ranksep:\n        data4Layout.config?.rankSpacing ||\n        data4Layout.config?.flowchart?.rankSpacing ||\n        data4Layout.rankSpacing,\n      marginx: 8,\n      marginy: 8,\n    })\n    .setDefaultEdgeLabel(function () {\n      return {};\n    });\n  const element = svg.select('g');\n  insertMarkers(element, data4Layout.markers, data4Layout.type, data4Layout.diagramId);\n  clearNodes();\n  clearEdges();\n  clearClusters();\n  clearGraphlib();\n\n  data4Layout.nodes.forEach((node) => {\n    graph.setNode(node.id, { ...node });\n    if (node.parentId) {\n      graph.setParent(node.id, node.parentId);\n    }\n  });\n\n  log.debug('Edges:', data4Layout.edges);\n  data4Layout.edges.forEach((edge) => {\n    // Handle self-loops\n    if (edge.start === edge.end) {\n      const nodeId = edge.start;\n      const specialId1 = nodeId + '---' + nodeId + '---1';\n      const specialId2 = nodeId + '---' + nodeId + '---2';\n      const node = graph.node(nodeId);\n      graph.setNode(specialId1, {\n        domId: specialId1,\n        id: specialId1,\n        parentId: node.parentId,\n        labelStyle: '',\n        label: '',\n        padding: 0,\n        shape: 'labelRect',\n        // shape: 'rect',\n        style: '',\n        width: 10,\n        height: 10,\n      });\n      graph.setParent(specialId1, node.parentId);\n      graph.setNode(specialId2, {\n        domId: specialId2,\n        id: specialId2,\n        parentId: node.parentId,\n        labelStyle: '',\n        padding: 0,\n        // shape: 'rect',\n        shape: 'labelRect',\n        label: '',\n        style: '',\n        width: 10,\n        height: 10,\n      });\n      graph.setParent(specialId2, node.parentId);\n\n      const edge1 = structuredClone(edge);\n      const edgeMid = structuredClone(edge);\n      const edge2 = structuredClone(edge);\n      edge1.label = '';\n      edge1.arrowTypeEnd = 'none';\n      edge1.id = nodeId + '-cyclic-special-1';\n      edgeMid.arrowTypeStart = 'none';\n      edgeMid.arrowTypeEnd = 'none';\n      edgeMid.id = nodeId + '-cyclic-special-mid';\n      edge2.label = '';\n      if (node.isGroup) {\n        edge1.fromCluster = nodeId;\n        edge2.toCluster = nodeId;\n      }\n      edge2.id = nodeId + '-cyclic-special-2';\n      edge2.arrowTypeStart = 'none';\n      graph.setEdge(nodeId, specialId1, edge1, nodeId + '-cyclic-special-0');\n      graph.setEdge(specialId1, specialId2, edgeMid, nodeId + '-cyclic-special-1');\n      graph.setEdge(specialId2, nodeId, edge2, nodeId + '-cyc<lic-special-2');\n    } else {\n      graph.setEdge(edge.start, edge.end, { ...edge }, edge.id);\n    }\n  });\n\n  log.warn('Graph at first:', JSON.stringify(graphlibJson.write(graph)));\n  adjustClustersAndEdges(graph);\n  log.warn('Graph after XAX:', JSON.stringify(graphlibJson.write(graph)));\n  const siteConfig = getConfig();\n  await recursiveRender(\n    element,\n    graph,\n    data4Layout.type,\n    data4Layout.diagramId,\n    undefined,\n    siteConfig\n  );\n};\n"], "mappings": "ymBAKA,SAASA,EAAMC,EAAG,CAChB,IAAIC,EAAO,CACT,QAAS,CACP,SAAUD,EAAE,WAAW,EACvB,WAAYA,EAAE,aAAa,EAC3B,SAAUA,EAAE,WAAW,CACzB,EACA,MAAOE,GAAWF,CAAC,EACnB,MAAOG,GAAWH,CAAC,CACrB,EACA,OAAOI,EAAYJ,EAAE,MAAM,CAAC,IAC1BC,EAAK,MAAUI,EAAML,EAAE,MAAM,CAAC,GAEzBC,CACT,CAdSK,EAAAP,EAAA,SAgBT,SAASG,GAAWF,EAAG,CACrB,OAASO,EAAIP,EAAE,MAAM,EAAG,SAAUQ,EAAG,CACnC,IAAIC,EAAYT,EAAE,KAAKQ,CAAC,EACpBE,EAASV,EAAE,OAAOQ,CAAC,EACnBG,EAAO,CAAE,EAAGH,CAAE,EAClB,OAAOJ,EAAYK,CAAS,IAC1BE,EAAK,MAAQF,GAERL,EAAYM,CAAM,IACvBC,EAAK,OAASD,GAETC,CACT,CAAC,CACH,CAbSL,EAAAJ,GAAA,cAeT,SAASC,GAAWH,EAAG,CACrB,OAASO,EAAIP,EAAE,MAAM,EAAG,SAAUY,EAAG,CACnC,IAAIC,EAAYb,EAAE,KAAKY,CAAC,EACpBE,EAAO,CAAE,EAAGF,EAAE,EAAG,EAAGA,EAAE,CAAE,EAC5B,OAAOR,EAAYQ,EAAE,IAAI,IACvBE,EAAK,KAAOF,EAAE,MAETR,EAAYS,CAAS,IAC1BC,EAAK,MAAQD,GAERC,CACT,CAAC,CACH,CAZSR,EAAAH,GAAA,cC/BF,IAAIY,EAAY,IAAI,IACvBC,EAAc,IAAI,IAClBC,EAAU,IAAI,IAELC,EAAQC,EAAA,IAAM,CACzBH,EAAY,MAAM,EAClBC,EAAQ,MAAM,EACdF,EAAU,MAAM,CAClB,EAJqB,SAMfK,EAAeD,EAAA,CAACE,EAAIC,IAAe,CACvC,IAAMC,EAAsBP,EAAY,IAAIM,CAAU,GAAK,CAAC,EAC5D,OAAAE,EAAI,MAAM,kBAAmBF,EAAY,IAAKD,EAAI,MAAOE,EAAoB,SAASF,CAAE,CAAC,EAClFE,EAAoB,SAASF,CAAE,CACxC,EAJqB,gBAMfI,GAAgBN,EAAA,CAACO,EAAMC,IAAc,CACzC,IAAMC,EAAqBZ,EAAY,IAAIW,CAAS,GAAK,CAAC,EAG1D,OAFAH,EAAI,KAAK,kBAAmBG,EAAW,OAAQC,CAAkB,EACjEJ,EAAI,KAAK,WAAYE,CAAI,EACrBA,EAAK,IAAMC,GAAaD,EAAK,IAAMC,EAC9B,GAGJC,EAMHA,EAAmB,SAASF,EAAK,CAAC,GAClCN,EAAaM,EAAK,EAAGC,CAAS,GAC9BP,EAAaM,EAAK,EAAGC,CAAS,GAC9BC,EAAmB,SAASF,EAAK,CAAC,GARlCF,EAAI,MAAM,SAAUG,EAAW,qBAAqB,EAC7C,GASX,EAnBsB,iBAqBhBE,EAAOV,EAAA,CAACQ,EAAWG,EAAOC,EAAUC,IAAW,CACnDR,EAAI,KACF,uBACAG,EACA,OACAK,EACA,OACAF,EAAM,KAAKH,CAAS,EACpBK,CACF,EACA,IAAMC,EAAQH,EAAM,SAASH,CAAS,GAAK,CAAC,EAExCA,IAAcK,GAChBC,EAAM,KAAKN,CAAS,EAGtBH,EAAI,KAAK,4BAA6BG,EAAW,QAASM,CAAK,EAE/DA,EAAM,QAASC,GAAS,CACtB,GAAIJ,EAAM,SAASI,CAAI,EAAE,OAAS,EAChCL,EAAKK,EAAMJ,EAAOC,EAAUC,CAAM,MAC7B,CACL,IAAMG,EAAOL,EAAM,KAAKI,CAAI,EAC5BV,EAAI,KAAK,MAAOU,EAAM,OAAQF,EAAQ,gBAAiBL,CAAS,EAChEI,EAAS,QAAQG,EAAMC,CAAI,EACvBH,IAAWF,EAAM,OAAOI,CAAI,IAC9BV,EAAI,KAAK,iBAAkBU,EAAMJ,EAAM,OAAOI,CAAI,CAAC,EACnDH,EAAS,UAAUG,EAAMJ,EAAM,OAAOI,CAAI,CAAC,GAGzCP,IAAcK,GAAUE,IAASP,GACnCH,EAAI,MAAM,iBAAkBU,EAAMP,CAAS,EAC3CI,EAAS,UAAUG,EAAMP,CAAS,IAElCH,EAAI,KAAK,WAAYG,EAAW,OAAQK,EAAQ,OAAQF,EAAM,KAAKH,CAAS,EAAGK,CAAM,EACrFR,EAAI,MACF,+BACAU,EACA,mBACAP,IAAcK,EACd,mBACAE,IAASP,CACX,GAEF,IAAMS,EAAQN,EAAM,MAAMI,CAAI,EAC9BV,EAAI,MAAM,gBAAiBY,CAAK,EAChCA,EAAM,QAASV,GAAS,CACtBF,EAAI,KAAK,OAAQE,CAAI,EACrB,IAAMS,EAAOL,EAAM,KAAKJ,EAAK,EAAGA,EAAK,EAAGA,EAAK,IAAI,EACjDF,EAAI,KAAK,YAAaW,EAAMH,CAAM,EAClC,GAAI,CACEP,GAAcC,EAAMM,CAAM,GAC5BR,EAAI,KAAK,cAAeE,EAAK,EAAGA,EAAK,EAAGS,EAAMT,EAAK,IAAI,EACvDK,EAAS,QAAQL,EAAK,EAAGA,EAAK,EAAGS,EAAMT,EAAK,IAAI,EAChDF,EAAI,KAAK,kBAAmBO,EAAS,MAAM,EAAGA,EAAS,KAAKA,EAAS,MAAM,EAAE,CAAC,CAAC,CAAC,GAEhFP,EAAI,KACF,yBACAE,EAAK,EACL,MACAA,EAAK,EACL,YACAM,EACA,cACAL,CACF,CAEJ,OAASU,EAAG,CACVb,EAAI,MAAMa,CAAC,CACb,CACF,CAAC,CACH,CACAb,EAAI,MAAM,gBAAiBU,CAAI,EAC/BJ,EAAM,WAAWI,CAAI,CACvB,CAAC,CACH,EA3Ea,QA6EAI,EAAqBnB,EAAA,CAACE,EAAIS,IAAU,CAC/C,IAAMS,EAAWT,EAAM,SAAST,CAAE,EAC9BmB,EAAM,CAAC,GAAGD,CAAQ,EAEtB,QAAWE,KAASF,EAClBtB,EAAQ,IAAIwB,EAAOpB,CAAE,EACrBmB,EAAM,CAAC,GAAGA,EAAK,GAAGF,EAAmBG,EAAOX,CAAK,CAAC,EAGpD,OAAOU,CACT,EAVkC,sBA4BlC,IAAME,GAAkBC,EAAA,CAACC,EAAOC,EAAKC,IAAQ,CAC3C,IAAMC,EAASH,EAAM,MAAM,EAAE,OAAQI,GAASA,EAAK,IAAMH,GAAOG,EAAK,IAAMH,CAAG,EACxEI,EAASL,EAAM,MAAM,EAAE,OAAQI,GAASA,EAAK,IAAMF,GAAOE,EAAK,IAAMF,CAAG,EACxEI,EAAaH,EAAO,IAAKC,IACtB,CAAE,EAAGA,EAAK,IAAMH,EAAMC,EAAME,EAAK,EAAG,EAAGA,EAAK,IAAMH,EAAMA,EAAMG,EAAK,CAAE,EAC7E,EACKG,EAAaF,EAAO,IAAKD,IACtB,CAAE,EAAGA,EAAK,EAAG,EAAGA,EAAK,CAAE,EAC/B,EAKD,OAJeE,EAAW,OAAQE,GACzBD,EAAW,KAAMH,GAASI,EAAQ,IAAMJ,EAAK,GAAKI,EAAQ,IAAMJ,EAAK,CAAC,CAC9E,CAGH,EAdwB,mBAgBXK,EAAsBV,EAAA,CAACW,EAAIV,EAAOW,IAAc,CAC3D,IAAMC,EAAWZ,EAAM,SAASU,CAAE,EAElC,GADAG,EAAI,MAAM,4BAA6BH,EAAIE,CAAQ,EAC/CA,EAAS,OAAS,EACpB,OAAOF,EAET,IAAII,EACJ,QAAWC,KAASH,EAAU,CAC5B,IAAMI,EAAMP,EAAoBM,EAAOf,EAAOW,CAAS,EAEjDM,EAAcnB,GAAgBE,EAAOW,EAAWK,CAAG,EAEzD,GAAIA,EACF,GAAIC,EAAY,OAAS,EACvBH,EAAUE,MAEV,QAAOA,CAGb,CACA,OAAOF,CACT,EArBmC,uBAuB7BI,EAAcnB,EAACW,GACf,CAACS,EAAU,IAAIT,CAAE,GAGjB,CAACS,EAAU,IAAIT,CAAE,EAAE,oBACdA,EAGLS,EAAU,IAAIT,CAAE,EACXS,EAAU,IAAIT,CAAE,EAAE,GAEpBA,EAXW,eAcPU,EAAyBrB,EAAA,CAACC,EAAOqB,IAAU,CACtD,GAAI,CAACrB,GAASqB,EAAQ,GAAI,CACxBR,EAAI,MAAM,uBAAuB,EACjC,MACF,MACEA,EAAI,MAAM,mBAAmB,EAG/Bb,EAAM,MAAM,EAAE,QAAQ,SAAUU,EAAI,CACjBV,EAAM,SAASU,CAAE,EACrB,OAAS,IACpBG,EAAI,KACF,qBACAH,EACA,6BACAD,EAAoBC,EAAIV,EAAOU,CAAE,CACnC,EACAY,EAAY,IAAIZ,EAAIa,EAAmBb,EAAIV,CAAK,CAAC,EACjDmB,EAAU,IAAIT,EAAI,CAAE,GAAID,EAAoBC,EAAIV,EAAOU,CAAE,EAAG,YAAaV,EAAM,KAAKU,CAAE,CAAE,CAAC,EAE7F,CAAC,EAEDV,EAAM,MAAM,EAAE,QAAQ,SAAUU,EAAI,CAClC,IAAME,EAAWZ,EAAM,SAASU,CAAE,EAC5Bc,EAAQxB,EAAM,MAAM,EACtBY,EAAS,OAAS,GACpBC,EAAI,MAAM,qBAAsBH,EAAIY,CAAW,EAC/CE,EAAM,QAASpB,GAAS,CACtB,IAAMqB,EAAKC,EAAatB,EAAK,EAAGM,CAAE,EAC5BiB,EAAKD,EAAatB,EAAK,EAAGM,CAAE,EAE9Be,EAAKE,IACPd,EAAI,KAAK,SAAUT,EAAM,mBAAoBM,CAAE,EAC/CG,EAAI,KAAK,sBAAuBH,EAAI,KAAMY,EAAY,IAAIZ,CAAE,CAAC,EAC7DS,EAAU,IAAIT,CAAE,EAAE,oBAAsB,GAE5C,CAAC,GAEDG,EAAI,MAAM,iBAAkBH,EAAIY,CAAW,CAE/C,CAAC,EAED,QAASZ,KAAMS,EAAU,KAAK,EAAG,CAC/B,IAAMS,EAAkBT,EAAU,IAAIT,CAAE,EAAE,GACpCmB,EAAS7B,EAAM,OAAO4B,CAAe,EAEvCC,IAAWnB,GAAMS,EAAU,IAAIU,CAAM,GAAK,CAACV,EAAU,IAAIU,CAAM,EAAE,sBACnEV,EAAU,IAAIT,CAAE,EAAE,GAAKmB,EAE3B,CAEA7B,EAAM,MAAM,EAAE,QAAQ,SAAU8B,EAAG,CACjC,IAAM1B,EAAOJ,EAAM,KAAK8B,CAAC,EACzBjB,EAAI,KAAK,QAAUiB,EAAE,EAAI,OAASA,EAAE,EAAI,KAAO,KAAK,UAAUA,CAAC,CAAC,EAChEjB,EAAI,KAAK,QAAUiB,EAAE,EAAI,OAASA,EAAE,EAAI,KAAO,KAAK,UAAU9B,EAAM,KAAK8B,CAAC,CAAC,CAAC,EAE5E,IAAIC,EAAID,EAAE,EACNE,EAAIF,EAAE,EAYV,GAXAjB,EAAI,KACF,UACAM,EACA,OACAW,EAAE,EACFA,EAAE,EACF,gBACAX,EAAU,IAAIW,EAAE,CAAC,EACjB,QACAX,EAAU,IAAIW,EAAE,CAAC,CACnB,EACIX,EAAU,IAAIW,EAAE,CAAC,GAAKX,EAAU,IAAIW,EAAE,CAAC,EAAG,CAK5C,GAJAjB,EAAI,KAAK,mCAAoCiB,EAAE,EAAGA,EAAE,EAAGA,EAAE,IAAI,EAC7DC,EAAIb,EAAYY,EAAE,CAAC,EACnBE,EAAId,EAAYY,EAAE,CAAC,EACnB9B,EAAM,WAAW8B,EAAE,EAAGA,EAAE,EAAGA,EAAE,IAAI,EAC7BC,IAAMD,EAAE,EAAG,CACb,IAAMD,EAAS7B,EAAM,OAAO+B,CAAC,EAC7BZ,EAAU,IAAIU,CAAM,EAAE,oBAAsB,GAC5CzB,EAAK,YAAc0B,EAAE,CACvB,CACA,GAAIE,IAAMF,EAAE,EAAG,CACb,IAAMD,EAAS7B,EAAM,OAAOgC,CAAC,EAC7Bb,EAAU,IAAIU,CAAM,EAAE,oBAAsB,GAC5CzB,EAAK,UAAY0B,EAAE,CACrB,CACAjB,EAAI,KAAK,yBAA0BkB,EAAGC,EAAGF,EAAE,IAAI,EAC/C9B,EAAM,QAAQ+B,EAAGC,EAAG5B,EAAM0B,EAAE,IAAI,CAClC,CACF,CAAC,EACDjB,EAAI,KAAK,iBAA+BoB,EAAMjC,CAAK,CAAC,EACpDkC,GAAUlC,EAAO,CAAC,EAElBa,EAAI,MAAMM,CAAS,CACrB,EA5FsC,0BA8FzBe,GAAYnC,EAAA,CAACC,EAAOqB,IAAU,CAEzC,GADAR,EAAI,KAAK,eAAgBQ,EAAoBY,EAAMjC,CAAK,EAAGA,EAAM,SAAS,GAAG,CAAC,EAC1EqB,EAAQ,GAAI,CACdR,EAAI,MAAM,aAAa,EACvB,MACF,CACA,IAAIsB,EAAQnC,EAAM,MAAM,EACpBoC,EAAc,GAClB,QAAWC,KAAQF,EAAO,CACxB,IAAMvB,EAAWZ,EAAM,SAASqC,CAAI,EACpCD,EAAcA,GAAexB,EAAS,OAAS,CACjD,CAEA,GAAI,CAACwB,EAAa,CAChBvB,EAAI,MAAM,6BAA8Bb,EAAM,MAAM,CAAC,EACrD,MACF,CACAa,EAAI,MAAM,WAAYsB,EAAOd,CAAK,EAClC,QAAWgB,KAAQF,EAYjB,GAXAtB,EAAI,MACF,kBACAwB,EACAlB,EACAA,EAAU,IAAIkB,CAAI,GAAK,CAAClB,EAAU,IAAIkB,CAAI,EAAE,oBAC5C,CAACrC,EAAM,OAAOqC,CAAI,EAClBrC,EAAM,KAAKqC,CAAI,EACfrC,EAAM,SAAS,GAAG,EAClB,UACAqB,CACF,EACI,CAACF,EAAU,IAAIkB,CAAI,EACrBxB,EAAI,MAAM,gBAAiBwB,EAAMhB,CAAK,UAEtC,CAACF,EAAU,IAAIkB,CAAI,EAAE,qBACrBrC,EAAM,SAASqC,CAAI,GACnBrC,EAAM,SAASqC,CAAI,EAAE,OAAS,EAC9B,CACAxB,EAAI,KACF,2EACAwB,EACAhB,CACF,EAGA,IAAIiB,EADkBtC,EAAM,MAAM,EACV,UAAY,KAAO,KAAO,KAC9CmB,EAAU,IAAIkB,CAAI,GAAG,aAAa,MACpCC,EAAMnB,EAAU,IAAIkB,CAAI,EAAE,YAAY,IACtCxB,EAAI,KAAK,aAAcM,EAAU,IAAIkB,CAAI,EAAE,YAAY,IAAKC,CAAG,GAGjE,IAAMC,EAAe,IAAaC,EAAM,CACtC,WAAY,GACZ,SAAU,EACZ,CAAC,EACE,SAAS,CACR,QAASF,EACT,QAAS,GACT,QAAS,GACT,QAAS,EACT,QAAS,CACX,CAAC,EACA,oBAAoB,UAAY,CAC/B,MAAO,CAAC,CACV,CAAC,EAEHzB,EAAI,KAAK,wBAAsCoB,EAAMjC,CAAK,CAAC,EAC3DyC,EAAKJ,EAAMrC,EAAOuC,EAAcF,CAAI,EACpCrC,EAAM,QAAQqC,EAAM,CAClB,YAAa,GACb,GAAIA,EACJ,YAAalB,EAAU,IAAIkB,CAAI,EAAE,YACjC,MAAOlB,EAAU,IAAIkB,CAAI,EAAE,MAC3B,MAAOE,CACT,CAAC,EACD1B,EAAI,KAAK,+BAAgCwB,EAAM,IAAkBJ,EAAMM,CAAY,CAAC,EACpF1B,EAAI,MAAM,uBAAqCoB,EAAMjC,CAAK,CAAC,CAC7D,MACEa,EAAI,KACF,cACAwB,EACA,oDACA,CAAClB,EAAU,IAAIkB,CAAI,EAAE,oBACrB,eACA,CAACrC,EAAM,OAAOqC,CAAI,EAClB,aACArC,EAAM,SAASqC,CAAI,GAAKrC,EAAM,SAASqC,CAAI,EAAE,OAAS,EACtDrC,EAAM,SAAS,GAAG,EAClBqB,CACF,EACAR,EAAI,MAAMM,CAAS,EAIvBgB,EAAQnC,EAAM,MAAM,EACpBa,EAAI,KAAK,oBAAqBsB,CAAK,EACnC,QAAWE,KAAQF,EAAO,CACxB,IAAMO,EAAO1C,EAAM,KAAKqC,CAAI,EAC5BxB,EAAI,KAAK,kBAAmBwB,EAAMK,CAAI,EAClCA,GAAM,aACRR,GAAUQ,EAAK,MAAOrB,EAAQ,CAAC,CAEnC,CACF,EAtGyB,aAwGnBsB,GAAS5C,EAAA,CAACC,EAAOmC,IAAU,CAC/B,GAAIA,EAAM,SAAW,EACnB,MAAO,CAAC,EAEV,IAAIS,EAAS,OAAO,OAAO,CAAC,EAAGT,CAAK,EACpC,OAAAA,EAAM,QAASE,GAAS,CACtB,IAAMzB,EAAWZ,EAAM,SAASqC,CAAI,EAC9BQ,EAASF,GAAO3C,EAAOY,CAAQ,EACrCgC,EAAS,CAAC,GAAGA,EAAQ,GAAGC,CAAM,CAChC,CAAC,EAEMD,CACT,EAZe,UAcFE,GAAuB/C,EAACC,GAAU2C,GAAO3C,EAAOA,EAAM,SAAS,CAAC,EAAzC,wBC/XpC,IAAM+C,GAAkBC,EAAA,MAAOC,EAAOC,EAAOC,EAAaC,EAAIC,EAAeC,IAAe,CAC1FC,EAAI,KAAK,gCAA8CC,EAAMN,CAAK,EAAGG,CAAa,EAClF,IAAMI,EAAMP,EAAM,MAAM,EAAE,QAC1BK,EAAI,MAAM,iCAAkCE,CAAG,EAE/C,IAAMC,EAAOT,EAAM,OAAO,GAAG,EAAE,KAAK,QAAS,MAAM,EAC9CC,EAAM,MAAM,EAGfK,EAAI,KAAK,uBAAwBL,EAAM,MAAM,CAAC,EAF9CK,EAAI,KAAK,qBAAsBL,CAAK,EAIlCA,EAAM,MAAM,EAAE,OAAS,GACzBK,EAAI,KAAK,kBAAmBL,EAAM,KAAKA,EAAM,MAAM,EAAE,CAAC,CAAC,CAAC,EAE1D,IAAMS,EAAWD,EAAK,OAAO,GAAG,EAAE,KAAK,QAAS,UAAU,EACpDE,EAAYF,EAAK,OAAO,GAAG,EAAE,KAAK,QAAS,WAAW,EACtDG,EAAaH,EAAK,OAAO,GAAG,EAAE,KAAK,QAAS,YAAY,EACxDI,EAAQJ,EAAK,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EAIpD,MAAM,QAAQ,IACZR,EAAM,MAAM,EAAE,IAAI,eAAgBa,EAAG,CACnC,IAAMC,EAAOd,EAAM,KAAKa,CAAC,EACzB,GAAIV,IAAkB,OAAW,CAC/B,IAAMY,EAAO,KAAK,MAAM,KAAK,UAAUZ,EAAc,WAAW,CAAC,EAEjEE,EAAI,MACF;AAAA,aACAQ,EACA;AAAA,QACAE,EAAK,OACL;AAAA,gBACAZ,EAAc,MAChB,EACAH,EAAM,QAAQG,EAAc,GAAIY,CAAI,EAC/Bf,EAAM,OAAOa,CAAC,IACjBR,EAAI,MAAM,iBAAkBQ,EAAGV,EAAc,EAAE,EAC/CH,EAAM,UAAUa,EAAGV,EAAc,GAAIY,CAAI,EAE7C,CAEA,GADAV,EAAI,KAAK,oBAAsBQ,EAAI,KAAO,KAAK,UAAUb,EAAM,KAAKa,CAAC,CAAC,CAAC,EACnEC,GAAM,YAAa,CAErBT,EAAI,KAAK,yBAA0BQ,EAAGC,EAAK,MAAOd,EAAM,KAAKa,CAAC,CAAC,EAI/D,GAAM,CAAE,QAAAG,EAAS,QAAAC,CAAQ,EAAIjB,EAAM,MAAM,EACzCc,EAAK,MAAM,SAAS,CAClB,GAAGA,EAAK,MAAM,MAAM,EACpB,QAASE,EAAU,GACnB,QAAAC,CACF,CAAC,EAGD,IAAMC,EAAI,MAAMrB,GACde,EACAE,EAAK,MACLb,EACAC,EACAF,EAAM,KAAKa,CAAC,EACZT,CACF,EACMe,EAAQD,EAAE,KAChBE,EAAiBN,EAAMK,CAAK,EAE5BL,EAAK,KAAOI,EAAE,MAAQ,EACtBb,EAAI,KACF,+CACAQ,EACA,QAEAC,EAAK,MACL,SACAA,EAAK,MAGP,EACAO,EAAYF,EAAOL,CAAI,CACzB,MACMd,EAAM,SAASa,CAAC,EAAE,OAAS,GAG7BR,EAAI,MACF,uCACAQ,EACAC,EAAK,GACLA,EACAA,EAAK,MACL,SACAd,CACF,EACAK,EAAI,MAAMiB,EAAoBR,EAAK,GAAId,CAAK,CAAC,EAC7CuB,EAAU,IAAIT,EAAK,GAAI,CAAE,GAAIQ,EAAoBR,EAAK,GAAId,CAAK,EAAG,KAAAc,CAAK,CAAC,IAGxET,EAAI,MAAM,oCAAqCQ,EAAGD,EAAOZ,EAAM,KAAKa,CAAC,EAAGN,CAAG,EAC3E,MAAMiB,EAAWZ,EAAOZ,EAAM,KAAKa,CAAC,EAAG,CAAE,OAAQT,EAAY,IAAAG,CAAI,CAAC,EAGxE,CAAC,CACH,EAyBA,MAvBqBT,EAAA,SAAY,CAC/B,IAAM2B,EAAezB,EAAM,MAAM,EAAE,IAAI,eAAgB0B,EAAG,CACxD,IAAMC,EAAO3B,EAAM,KAAK0B,EAAE,EAAGA,EAAE,EAAGA,EAAE,IAAI,EACxCrB,EAAI,KAAK,QAAUqB,EAAE,EAAI,OAASA,EAAE,EAAI,KAAO,KAAK,UAAUA,CAAC,CAAC,EAChErB,EAAI,KAAK,QAAUqB,EAAE,EAAI,OAASA,EAAE,EAAI,KAAMA,EAAG,IAAK,KAAK,UAAU1B,EAAM,KAAK0B,CAAC,CAAC,CAAC,EAGnFrB,EAAI,KACF,MACAkB,EACA,OACAG,EAAE,EACFA,EAAE,EACF,gBACAH,EAAU,IAAIG,EAAE,CAAC,EACjBH,EAAU,IAAIG,EAAE,CAAC,CACnB,EACA,MAAME,EAAgBjB,EAAYgB,CAAI,CACxC,CAAC,EAED,MAAM,QAAQ,IAAIF,CAAY,CAChC,EArBqB,gBAuBF,EAEnBpB,EAAI,KAAK,uBAAwB,KAAK,UAAuBC,EAAMN,CAAK,CAAC,CAAC,EAE1EK,EAAI,KAAK,mDAAmD,EAC5DA,EAAI,KAAK,mDAAmD,EAC5DA,EAAI,KAAK,mDAAmD,EAE5DwB,EAAY7B,CAAK,EAEjBK,EAAI,KAAK,sBAAuB,KAAK,UAAuBC,EAAMN,CAAK,CAAC,CAAC,EAEzE,IAAI8B,EAAO,EACP,CAAE,yBAAAC,CAAyB,EAAIC,EAAwB5B,CAAU,EACrE,aAAM,QAAQ,IACZ6B,GAAqBjC,CAAK,EAAE,IAAI,eAAgBa,EAAG,CACjD,IAAMC,EAAOd,EAAM,KAAKa,CAAC,EASzB,GARAR,EAAI,KACF,mBAAqBQ,EAAI,MAAQC,EAAK,EACtC,IAAMA,EAAK,EACX,YACAA,EAAK,MACL,YACAA,EAAK,MACP,EACIA,GAAM,YAERA,EAAK,GAAKiB,EAEV1B,EAAI,KACF,8BACAQ,EACAC,EAAK,GACLA,EAAK,MACLA,EAAK,OACLA,EAAK,EACLA,EAAK,EACLd,EAAM,OAAOa,CAAC,CAChB,EACAU,EAAU,IAAIT,EAAK,EAAE,EAAE,KAAOA,EAC9BoB,EAAapB,CAAI,UAGbd,EAAM,SAASa,CAAC,EAAE,OAAS,EAAG,CAChCR,EAAI,KACF,2BACAQ,EACAC,EAAK,GACLA,EAAK,EACLA,EAAK,EACLA,EAAK,MACLA,EAAK,OACLd,EAAM,OAAOa,CAAC,CAChB,EACAC,EAAK,QAAUiB,EACf/B,EAAM,KAAKc,EAAK,QAAQ,EACxB,IAAMqB,EAAcrB,GAAM,QAAU,GAAK,EACnCsB,EAActB,GAAM,WAAW,QAAU,EACzCuB,EAAUD,EAAcD,GAAe,EAC7C9B,EAAI,MAAM,UAAWgC,EAAS,cAAeD,EAAa,cAAeD,CAAW,EACpF,MAAMG,EAAc7B,EAAUK,CAAI,EAGlCS,EAAU,IAAIT,EAAK,EAAE,EAAE,KAAOA,CAChC,KAAO,CAEL,IAAMyB,EAASvC,EAAM,KAAKc,EAAK,QAAQ,EACvCA,EAAK,GAAKiB,EAA2B,EACrC1B,EAAI,KACF,0CACAS,EAAK,GACL,SACAA,EAAK,SACLA,EAAK,MACLA,EAAK,OACLA,EAAK,EACLA,EAAK,EACL,UACAA,EAAK,QACL,SACAyB,EACAA,GAAQ,QACRzB,CACF,EAEAoB,EAAapB,CAAI,CACnB,CAEJ,CAAC,CACH,EAGAd,EAAM,MAAM,EAAE,QAAQ,SAAU0B,EAAG,CACjC,IAAMC,EAAO3B,EAAM,KAAK0B,CAAC,EACzBrB,EAAI,KAAK,QAAUqB,EAAE,EAAI,OAASA,EAAE,EAAI,KAAO,KAAK,UAAUC,CAAI,EAAGA,CAAI,EAEzEA,EAAK,OAAO,QAASa,GAAWA,EAAM,GAAKT,EAA2B,CAAE,EACxE,IAAMU,EAAYzC,EAAM,KAAK0B,EAAE,CAAC,EAChC,IAAIgB,EAAU1C,EAAM,KAAK0B,EAAE,CAAC,EAC5B,IAAMiB,EAAQC,EAAWlC,EAAWiB,EAAMJ,EAAWtB,EAAawC,EAAWC,EAASxC,CAAE,EACxF2C,EAAkBlB,EAAMgB,CAAK,CAC/B,CAAC,EAED3C,EAAM,MAAM,EAAE,QAAQ,SAAUa,EAAG,CACjC,IAAMiC,EAAI9C,EAAM,KAAKa,CAAC,EACtBR,EAAI,KAAKQ,EAAGiC,EAAE,KAAMA,EAAE,IAAI,EACtBA,EAAE,UACJhB,EAAOgB,EAAE,KAEb,CAAC,EACDzC,EAAI,KAAK,sCAAuCG,EAAMsB,CAAI,EACnD,CAAE,KAAAtB,EAAM,KAAAsB,CAAK,CACtB,EA/OwB,mBAiPXiB,GAASjD,EAAA,MAAOkD,EAAaC,IAAQ,CAChD,IAAMjD,EAAQ,IAAakD,EAAM,CAC/B,WAAY,GACZ,SAAU,EACZ,CAAC,EACE,SAAS,CACR,QAASF,EAAY,UACrB,QACEA,EAAY,QAAQ,aACpBA,EAAY,QAAQ,WAAW,aAC/BA,EAAY,YACd,QACEA,EAAY,QAAQ,aACpBA,EAAY,QAAQ,WAAW,aAC/BA,EAAY,YACd,QAAS,EACT,QAAS,CACX,CAAC,EACA,oBAAoB,UAAY,CAC/B,MAAO,CAAC,CACV,CAAC,EACGG,EAAUF,EAAI,OAAO,GAAG,EAC9BG,EAAcD,EAASH,EAAY,QAASA,EAAY,KAAMA,EAAY,SAAS,EACnFK,EAAW,EACXA,EAAW,EACXA,EAAc,EACdA,EAAc,EAEdL,EAAY,MAAM,QAASlC,GAAS,CAClCd,EAAM,QAAQc,EAAK,GAAI,CAAE,GAAGA,CAAK,CAAC,EAC9BA,EAAK,UACPd,EAAM,UAAUc,EAAK,GAAIA,EAAK,QAAQ,CAE1C,CAAC,EAEDT,EAAI,MAAM,SAAU2C,EAAY,KAAK,EACrCA,EAAY,MAAM,QAASrB,GAAS,CAElC,GAAIA,EAAK,QAAUA,EAAK,IAAK,CAC3B,IAAM2B,EAAS3B,EAAK,MACd4B,EAAaD,EAAS,MAAQA,EAAS,OACvCE,EAAaF,EAAS,MAAQA,EAAS,OACvCxC,EAAOd,EAAM,KAAKsD,CAAM,EAC9BtD,EAAM,QAAQuD,EAAY,CACxB,MAAOA,EACP,GAAIA,EACJ,SAAUzC,EAAK,SACf,WAAY,GACZ,MAAO,GACP,QAAS,EACT,MAAO,YAEP,MAAO,GACP,MAAO,GACP,OAAQ,EACV,CAAC,EACDd,EAAM,UAAUuD,EAAYzC,EAAK,QAAQ,EACzCd,EAAM,QAAQwD,EAAY,CACxB,MAAOA,EACP,GAAIA,EACJ,SAAU1C,EAAK,SACf,WAAY,GACZ,QAAS,EAET,MAAO,YACP,MAAO,GACP,MAAO,GACP,MAAO,GACP,OAAQ,EACV,CAAC,EACDd,EAAM,UAAUwD,EAAY1C,EAAK,QAAQ,EAEzC,IAAM2C,EAAQ,gBAAgB9B,CAAI,EAC5B+B,EAAU,gBAAgB/B,CAAI,EAC9BgC,EAAQ,gBAAgBhC,CAAI,EAClC8B,EAAM,MAAQ,GACdA,EAAM,aAAe,OACrBA,EAAM,GAAKH,EAAS,oBACpBI,EAAQ,eAAiB,OACzBA,EAAQ,aAAe,OACvBA,EAAQ,GAAKJ,EAAS,sBACtBK,EAAM,MAAQ,GACV7C,EAAK,UACP2C,EAAM,YAAcH,EACpBK,EAAM,UAAYL,GAEpBK,EAAM,GAAKL,EAAS,oBACpBK,EAAM,eAAiB,OACvB3D,EAAM,QAAQsD,EAAQC,EAAYE,EAAOH,EAAS,mBAAmB,EACrEtD,EAAM,QAAQuD,EAAYC,EAAYE,EAASJ,EAAS,mBAAmB,EAC3EtD,EAAM,QAAQwD,EAAYF,EAAQK,EAAOL,EAAS,oBAAoB,CACxE,MACEtD,EAAM,QAAQ2B,EAAK,MAAOA,EAAK,IAAK,CAAE,GAAGA,CAAK,EAAGA,EAAK,EAAE,CAE5D,CAAC,EAEDtB,EAAI,KAAK,kBAAmB,KAAK,UAAuBC,EAAMN,CAAK,CAAC,CAAC,EACrE4D,EAAuB5D,CAAK,EAC5BK,EAAI,KAAK,mBAAoB,KAAK,UAAuBC,EAAMN,CAAK,CAAC,CAAC,EACtE,IAAMI,EAAayD,EAAU,EAC7B,MAAMhE,GACJsD,EACAnD,EACAgD,EAAY,KACZA,EAAY,UACZ,OACA5C,CACF,CACF,EA5GsB", "names": ["write", "g", "json", "writeNodes", "writeEdges", "isUndefined_default", "clone_default", "__name", "map_default", "v", "nodeValue", "parent", "node", "e", "edgeValue", "edge", "clusterDb", "descendants", "parents", "clear", "__name", "isDescendant", "id", "ancestorId", "ancestorDescendants", "log", "edgeInCluster", "edge", "clusterId", "clusterDescendants", "copy", "graph", "newGraph", "rootId", "nodes", "node", "data", "edges", "e", "extractDescendants", "children", "res", "child", "find<PERSON><PERSON><PERSON><PERSON>dges", "__name", "graph", "id1", "id2", "edges1", "edge", "edges2", "edges1Prim", "edges2Prim", "edgeIn1", "findNonClusterChild", "id", "clusterId", "children", "log", "reserve", "child", "_id", "commonEdges", "getAnchorId", "clusterDb", "adjustClustersAndEdges", "depth", "descendants", "extractDescendants", "edges", "d1", "isDescendant", "d2", "nonClusterChild", "parent", "e", "v", "w", "write", "extractor", "nodes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "dir", "clusterGraph", "Graph", "copy", "data", "sorter", "result", "sorted", "sortNodesByHierarchy", "recursiveRender", "__name", "_elem", "graph", "diagramType", "id", "parentCluster", "siteConfig", "log", "write", "dir", "elem", "clusters", "edgePaths", "edgeLabels", "nodes", "v", "node", "data", "ranksep", "nodesep", "o", "newEl", "updateNodeBounds", "setNodeElem", "findNonClusterChild", "clusterDb", "insertNode", "edgePromises", "e", "edge", "insertEdgeLabel", "layout", "diff", "subGraphTitleTotalMargin", "getSubGraphTitleMargins", "sortNodesByHierarchy", "positionNode", "halfPadding", "labelHeight", "offsetY", "insertCluster", "parent", "point", "startNode", "endNode", "paths", "insertEdge", "positionEdgeLabel", "n", "render", "data4Layout", "svg", "Graph", "element", "markers_default", "clear", "nodeId", "specialId1", "specialId2", "edge1", "edgeMid", "edge2", "adjustClustersAndEdges", "getConfig"]}