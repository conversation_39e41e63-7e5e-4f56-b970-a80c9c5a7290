#server.py

from fastapi.middleware.cors import CORSMiddleware
from fastapi import FastAP<PERSON>, File, UploadFile, HTTPException
from fastapi.responses import StreamingResponse, FileResponse
from pydantic import BaseModel
from PIL import Image, ImageDraw, ImageFont
import torch
import numpy as np
import cv2
import io
import os
import base64
import requests
from dotenv import load_dotenv
import json
from ultralytics import YOLO

# Ajouter cette classe pour la validation des données
class TextUpdate(BaseModel):
    text: str

# Importer le service de traitement des relations
from relation_service import append_relations_to_results

# Configuration de l'application avec taille maximale
app = FastAPI(
    title="UML Detector API",
    description="API pour la détection d'éléments UML"
)

# IMPORTANT: Configurer la taille maximale des fichiers (50MB par exemple)
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response

class FileSizeMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, max_size: int = 50 * 1024 * 1024):  # 50MB par défaut
        super().__init__(app)
        self.max_size = max_size
    
    async def dispatch(self, request: Request, call_next):
        if request.method == "POST" and "multipart/form-data" in request.headers.get("content-type", ""):
            content_length = request.headers.get("content-length")
            if content_length and int(content_length) > self.max_size:
                return Response(
                    content=f"Fichier trop volumineux. Taille maximale autorisée: {self.max_size // (1024*1024)}MB",
                    status_code=413
                )
        return await call_next(request)

# Ajouter le middleware
app.add_middleware(FileSizeMiddleware, max_size=50 * 1024 * 1024)  # 50MB

# Charger les variables d'environnement
load_dotenv()
api_key = os.getenv("GROQ_API_KEY")
if not api_key:
    raise ValueError("La clé API GROQ_API_KEY n'est pas définie dans le fichier .env")

# Charger les modèles YOLO avec des configurations explicites
model1 = YOLO("train12 700imgsmandat150epochsV8s.pt")  # Modèle 1
model1.conf = 0.25  # Seuil de confiance général pour le modèle 1

# Pour le modèle ONNX, ajuster les configurations pour correspondre à votre test Jupyter
model2 = YOLO("bestyarabi15-05-2025.onnx")  # Modèle 2
model2.conf = 0.25  # IMPORTANT: Ajusté à 0.25 pour correspondre au test Jupyter
model2.iou = 0.45   # Ajout du seuil IOU explicitement

# Paramètres des modèles
MODEL1_NAMES = {0: 'arrow', 1: 'class'}
MODEL2_NAMES = {0: 'A', 1: 'C', 2: 'generalization', 3: 'endpoin', 
               4: 'one-way-association', 5: 'composition', 6: 'aggregation'}

# Paramètres de couleurs pour chaque modèle
# Modèle 1
COLOR_CLASS = (0, 128, 0)  # vert
COLOR_ARROW = (0, 0, 255)  # bleu
TEXT_COLOR_CLASS = (0, 128, 0)  # vert
TEXT_COLOR_ARROW = (0, 0, 255)  # bleu
MEMORY_FILE_PATH = "memoire.txt"
# Modèle 2 (couleurs différentes pour chaque classe)
MODEL2_COLORS = {
    'A': (255, 0, 0),  # rouge
    'C': (255, 165, 0),  # orange
    'generalization': (128, 0, 128),  # violet
    'endpoin': (255, 0, 255),  # magenta
    'one-way-association': (0, 128, 128),  # teal
    'composition': (255, 255, 0),  # jaune
    'aggregation': (165, 42, 42)  # marron
}

# Paramètres généraux
CONFIDENCE_THRESHOLD_MODEL1 = 0.25  # Seuil général du modèle 1
CONFIDENCE_THRESHOLD_ARROW = 0.40   # NOUVEAU: Seuil spécifique pour les flèches (plus élevé)
CONFIDENCE_THRESHOLD_MODEL2 = 0.25  # Seuil pour le modèle 2
TEXT_SIZE = 30

# Chemin pour sauvegarder l'image annotée
ANNOTATED_IMAGE_PATH = "annotated_image.jpg"
DEBUG_PATH = "debug_output.txt"  # Pour le debug
RELATIONS_PATH = "relations_detectees.txt"  # Pour les relations détectées

# Fonction pour extraire le texte avec Llama 4 via Groq
def extract_text_with_llama(image_bytes, class_name):
    encoded_image = base64.b64encode(image_bytes).decode("utf-8")
    
    url = "https://api.groq.com/openai/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Prompt optimisé selon le type de classe
    prompt = ""
    if "entity" in class_name.lower() or "class" in class_name.lower():
        prompt = (
            "Tu es un système d'extraction de texte OCR pour diagrammes UML. "
            "Identifie uniquement les éléments suivants dans cette image de classe UML et renvoie-les selon ce format exact:\n\n"
            "NOM_CLASSE: [nom de la classe]\n"
            "ATTRIBUTS: [liste des attributs, un par ligne]\n"
            "MÉTHODES: [liste des méthodes, une par ligne]\n\n"
            "N'ajoute aucun commentaire, explication ou autre texte. Respecte strictement ce format"
        )
    else:
        prompt = (
            "Extrais uniquement le texte visible dans cette image. "
            "Ne fais aucun commentaire. Renvoie uniquement le texte brut."
        )
    
    payload = {
        "model": "meta-llama/llama-4-scout-17b-16e-instruct",
        "messages": [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{encoded_image}"
                        }
                    }
                ]
            }
        ],
        "temperature": 0.1,
        "max_tokens": 1024
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()
        result = response.json()
        
        if "choices" in result and len(result["choices"]) > 0:
            text = result["choices"][0]["message"]["content"]
            
            # Post-traitement pour éliminer les commentaires éventuels
            if "entity" in class_name.lower() or "class" in class_name.lower():
                # Extraction des sections importantes seulement
                processed_text = ""
                lines = text.strip().split("\n")
                for line in lines:
                    if line.startswith("NOM_CLASSE:") or line.startswith("ATTRIBUTS:") or line.startswith("MÉTHODES:"):
                        processed_text += line + "\n"
                    elif processed_text and line.strip() and not line.startswith("```") and not line.startswith("#"):
                        processed_text += line + "\n"
                return processed_text.strip()
            else:
                # Pour les autres éléments, éliminer les commentaires potentiels
                return text.strip().split("```")[0].strip()
        else:
            print(f"Réponse inattendue de l'API: {json.dumps(result, indent=2)}")
            return ""
    except Exception as e:
        print(f"Erreur lors de l'extraction du texte: {str(e)}")
        return ""

# Conversion des couleurs
def pil_color_to_bgr(color):
    if isinstance(color, str):
        # Conversion des noms de couleurs en RGB
        if color == "green":
            return (0, 128, 0)
        elif color == "blue":
            return (0, 0, 255)
        elif color == "red":
            return (255, 0, 0)
    return color

def bgr_to_rgb(bgr):
    return (bgr[2], bgr[1], bgr[0])

@app.post("/detect/")
async def detect_objects(file: UploadFile = File(...)):
    try:
        contents = await file.read()
        image_pil = Image.open(io.BytesIO(contents)).convert("RGB")
        
        # Obtenir le nom du fichier original
        original_filename = file.filename
        # Créer un nom de fichier sécurisé pour l'image annotée
        safe_filename = ''.join(c if c.isalnum() or c in '._-' else '_' for c in original_filename) if original_filename else "default"
        annotated_filename = f"annotated_image_{safe_filename}"
        
        # Chemin personnalisé pour l'image annotée
        custom_annotated_path = annotated_filename
        
        # Conversion en format numpy pour OpenCV
        image_np = np.array(image_pil)
        image_cv = cv2.cvtColor(image_np, cv2.COLOR_RGB2BGR)
        
        # Sauvegarde d'une copie de l'image originale pour debug
        cv2.imwrite("original_image.jpg", image_cv)
        
        # Initialisation du dessin
        draw = ImageDraw.Draw(image_pil)

        # Police
        try:
            font = ImageFont.truetype("arial.ttf", TEXT_SIZE)
        except IOError:
            font = ImageFont.load_default()

        # Création d'un fichier de debug
        with open(DEBUG_PATH, "w", encoding="utf-8") as debug_file:
            debug_file.write("=== DÉMARRAGE DÉTECTION ===\n")
            
        class_counter = {}
        extracted_texts = []

        # ==================== MODÈLE 1 ====================
        with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
            debug_file.write("Exécution du modèle 1 (PT)...\n")
        
        # Utiliser les mêmes paramètres d'inférence que dans le test Jupyter
        results1 = model1(image_np, imgsz=640)
        
        with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
            debug_file.write(f"Résultats modèle 1: {len(results1)} éléments traités\n")
        
        # Traitement des détections du modèle 1
        for result in results1:
            boxes = result.boxes.xyxy.cpu().numpy()
            classes = result.boxes.cls.cpu().numpy()
            scores = result.boxes.conf.cpu().numpy()
            
            with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
                debug_file.write(f"Modèle 1: {len(boxes)} boîtes détectées\n")
            
            for i in range(len(boxes)):
                x1, y1, x2, y2 = map(int, boxes[i])
                class_index = int(classes[i])
                class_name = MODEL1_NAMES[class_index]
                confidence = scores[i]
                
                with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
                    debug_file.write(f"  Détection: {class_name}, confiance: {confidence:.2f}\n")
                
                # Utiliser un seuil différent selon le type d'objet
                applicable_threshold = CONFIDENCE_THRESHOLD_ARROW if class_name == "arrow" else CONFIDENCE_THRESHOLD_MODEL1
                
                if confidence < applicable_threshold:
                    with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
                        debug_file.write(f"  [!] Confiance trop basse pour {class_name}: {confidence:.2f} < {applicable_threshold}\n")
                    continue
                    
                if class_name not in class_counter:
                    class_counter[class_name] = 1
                else:
                    class_counter[class_name] += 1
                    
                label_text = f"{class_name} {class_counter[class_name]}"
                text_x, text_y = x1, y1 - TEXT_SIZE - 5
                
                # Traitement différent selon le type d'objet détecté
                if class_name == "arrow":
                    # Pour les flèches du modèle 1
                    color = pil_color_to_bgr(COLOR_ARROW)
                    cv2.rectangle(image_cv, (x1, y1), (x2, y2), color, 3)
                    
                    # Ajouter le texte avec PIL
                    draw.text((text_x, text_y), label_text, fill=TEXT_COLOR_ARROW, font=font)
                    
                    with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
                        debug_file.write(f"  [OK] Flèche acceptée avec confiance {confidence:.2f} >= {CONFIDENCE_THRESHOLD_ARROW}\n")
                elif class_name == "class":
                    # Pour les classes du modèle 1
                    color = pil_color_to_bgr(COLOR_CLASS)
                    cv2.rectangle(image_cv, (x1, y1), (x2, y2), color, 3)
                    
                    # Ajouter le texte avec PIL
                    draw.text((text_x, text_y), label_text, fill=TEXT_COLOR_CLASS, font=font)
                    
                    # Extraire le texte avec Llama 4
                    cropped = image_pil.crop((x1, y1, x2, y2))
                    cropped_buffer = io.BytesIO()
                    cropped.save(cropped_buffer, format="JPEG")
                    cropped_bytes = cropped_buffer.getvalue()
                    
                    extracted_text = extract_text_with_llama(cropped_bytes, class_name)
                    
                    if extracted_text:
                        extracted_texts.append(f"{label_text}:\n{extracted_text}")
                        
                    with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
                        debug_file.write(f"  [OK] Classe acceptée avec confiance {confidence:.2f} >= {CONFIDENCE_THRESHOLD_MODEL1}\n")

        # ==================== MODÈLE 2 (ONNX) ====================
        with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
            debug_file.write("\nExécution du modèle 2 (ONNX)...\n")
        
        # *** CORRECTION: Utiliser l'image numpy brute comme dans Jupyter ***
        with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
            debug_file.write("Passage de l'image brute (numpy array) au modèle ONNX...\n")
        
        # Configuration spécifique pour le modèle ONNX, identique à celle du notebook
        results2 = model2(image_np, imgsz=640, conf=0.25, iou=0.45)
        
        with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
            debug_file.write(f"Résultats modèle 2: {len(results2)} éléments traités\n")
            for r in results2:
                debug_file.write(f"  Nombre de détections: {len(r.boxes)}\n")
                if hasattr(r.boxes, 'cls') and len(r.boxes.cls) > 0:
                    for cls_idx in r.boxes.cls:
                        class_idx = int(cls_idx)
                        if class_idx in MODEL2_NAMES:
                            debug_file.write(f"  Classe détectée: {MODEL2_NAMES[class_idx]}\n")
        
        # Traitement des détections du modèle 2
        model2_counter = {}
        
        # Sauvegarde des prédictions brutes du modèle 2 pour debug
        with open("model2_raw_predictions.txt", "w") as m2_file:
            for i, result in enumerate(results2):
                m2_file.write(f"Résultat {i}:\n")
                m2_file.write(f"  Boxes: {result.boxes}\n")
                m2_file.write(f"  Classes: {result.boxes.cls.tolist() if hasattr(result.boxes, 'cls') else 'No classes'}\n")
                m2_file.write(f"  Scores: {result.boxes.conf.tolist() if hasattr(result.boxes, 'conf') else 'No confidence'}\n")
                
        for result in results2:
            # Vérifier que nous avons bien des boîtes
            if not hasattr(result, 'boxes') or not hasattr(result.boxes, 'xyxy'):
                with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
                    debug_file.write("  [!] Pas de boîtes détectées par le modèle 2\n")
                continue
                
            boxes = result.boxes.xyxy.cpu().numpy()
            classes = result.boxes.cls.cpu().numpy()
            scores = result.boxes.conf.cpu().numpy()
            
            with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
                debug_file.write(f"Modèle 2: {len(boxes)} boîtes détectées\n")
            
            for i in range(len(boxes)):
                x1, y1, x2, y2 = map(int, boxes[i])
                class_index = int(classes[i])
                confidence = scores[i]
                
                # Log complet pour le debug
                with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
                    debug_file.write(f"  Box {i}: coords={x1},{y1},{x2},{y2} class_idx={class_index} conf={confidence:.2f}\n")
                
                # Vérifier si l'index de classe est valide
                if class_index not in MODEL2_NAMES:
                    with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
                        debug_file.write(f"  [!] Index de classe invalide: {class_index}\n")
                    continue
                
                class_name = MODEL2_NAMES[class_index]
                
                with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
                    debug_file.write(f"  Détection: {class_name}, confiance: {confidence:.2f}\n")
                
                if confidence < CONFIDENCE_THRESHOLD_MODEL2:
                    with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
                        debug_file.write(f"  [!] Confiance trop basse: {confidence:.2f} < {CONFIDENCE_THRESHOLD_MODEL2}\n")
                    continue
                    
                if class_name not in model2_counter:
                    model2_counter[class_name] = 1
                else:
                    model2_counter[class_name] += 1
                    
                label_text = f"{class_name} {model2_counter[class_name]}"
                text_x, text_y = x1, y1 - TEXT_SIZE - 5
                
                # Utiliser la couleur spécifique pour cette classe du modèle 2
                color = MODEL2_COLORS.get(class_name, (255, 0, 0))  # rouge par défaut si non trouvé
                
                # Dessiner le rectangle sur l'image OpenCV
                cv2.rectangle(image_cv, (x1, y1), (x2, y2), color, 3)
                
                # Dessiner le texte sur l'image PIL
                rgb_color = bgr_to_rgb(color)
                draw.text((text_x, text_y), label_text, fill=rgb_color, font=font)

        # ==================== TRAITEMENT DES RELATIONS ====================
        # Traiter les relations détectées et générer des phrases explicatives
        with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
            debug_file.write("\nTraitement des relations entre classes...\n")
        
        # Utiliser le service de traitement des relations pour obtenir les phrases de relation
        combined_text = await append_relations_to_results(extracted_texts, results1, results2)
        
        # Résumé des détections
        with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
            debug_file.write("\nRésumé des détections:\n")
            debug_file.write(f"  Modèle 1: {class_counter}\n")
            debug_file.write(f"  Modèle 2: {model2_counter}\n")

        # Reconvertir l'image OpenCV en PIL
        image_cv_rgb = cv2.cvtColor(image_cv, cv2.COLOR_BGR2RGB)
        final_image = Image.fromarray(image_cv_rgb)

        # Enregistrer les résultats dans un fichier
        with open("resultats_classes.txt", "w", encoding="utf-8") as f:
            f.write(combined_text)  # Utiliser le texte combiné avec les relations

        # Sauvegarder dans le fichier mémoire
        save_to_memory_file(extracted_texts)
        
        # Sauvegarder l'image annotée sur le disque
        final_image.save(custom_annotated_path, format="JPEG")
        
        # Sauvegarder également avec le nom standard pour la compatibilité
        final_image.save(ANNOTATED_IMAGE_PATH, format="JPEG")

        # Renvoyer l'image annotée
        img_byte_arr = io.BytesIO()
        final_image.save(img_byte_arr, format="PNG")
        img_byte_arr.seek(0)

        with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
            debug_file.write("\n=== FIN DÉTECTION ===\n")

        return StreamingResponse(img_byte_arr, media_type="image/png")

    except Exception as e:
        print(f"Erreur : {str(e)}")
        import traceback
        stack_trace = traceback.format_exc()
        with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
            debug_file.write(f"ERREUR: {str(e)}\n")
            debug_file.write(f"STACK TRACE: {stack_trace}\n")
        raise HTTPException(status_code=500, detail=f"Erreur pendant l'analyse: {str(e)}")

@app.get("/resultats_classes.txt")
def get_resultats_txt():
    if os.path.exists("resultats_classes.txt"):
        return FileResponse("resultats_classes.txt", media_type="text/plain", filename="resultats_classes.txt")
    raise HTTPException(status_code=404, detail="Fichier introuvable.")

@app.get("/relations_detectees.txt")
def get_relations_txt():
    if os.path.exists(RELATIONS_PATH):
        return FileResponse(RELATIONS_PATH, media_type="text/plain", filename="relations_detectees.txt")
    raise HTTPException(status_code=404, detail="Fichier de relations introuvable.")

# Route existante pour la compatibilité
@app.get("/annotated_image.jpg")
def get_annotated_image():
    if os.path.exists(ANNOTATED_IMAGE_PATH):
        return FileResponse(ANNOTATED_IMAGE_PATH, media_type="image/jpeg")
    raise HTTPException(status_code=404, detail="Image annotée introuvable.")

# Nouvelle route pour les images annotées avec nom de fichier dynamique
@app.get("/annotated_image_{filename}")
def get_annotated_image_by_filename(filename: str):
    # Construire le chemin du fichier
    file_path = f"annotated_image_{filename}"
    
    if os.path.exists(file_path):
        return FileResponse(file_path, media_type="image/jpeg")
    raise HTTPException(status_code=404, detail=f"Image annotée {filename} introuvable.")

@app.get("/debug_output.txt")
def get_debug_output():
    if os.path.exists(DEBUG_PATH):
        return FileResponse(DEBUG_PATH, media_type="text/plain", filename="debug_output.txt")
    raise HTTPException(status_code=404, detail="Fichier de debug introuvable.")

@app.get("/model2_raw_predictions.txt")
def get_model2_predictions():
    if os.path.exists("model2_raw_predictions.txt"):
        return FileResponse("model2_raw_predictions.txt", media_type="text/plain", filename="model2_raw_predictions.txt")
    raise HTTPException(status_code=404, detail="Fichier de prédictions brutes introuvable.")

@app.post("/update_text/")
async def update_text(text_update: TextUpdate):
    """
    Met à jour le fichier resultats_classes.txt avec le texte fourni.
    """
    try:
        with open("resultats_classes.txt", "w", encoding="utf-8") as f:
            f.write(text_update.text)
        
        # Journalisation pour le débogage
        with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
            debug_file.write(f"\nMise à jour du texte: {text_update.text[:100]}...\n")
        
        return {"status": "success", "message": "Texte mis à jour avec succès"}
    except Exception as e:
        # Journalisation de l'erreur
        with open(DEBUG_PATH, "a", encoding="utf-8") as debug_file:
            debug_file.write(f"ERREUR lors de la mise à jour du texte: {str(e)}\n")
        
        raise HTTPException(status_code=500, detail=f"Erreur lors de la mise à jour du texte: {str(e)}")

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Permettre tous les domaines (pour le développement)
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Déplacer cette fonction avant le bloc if __name__ == "__main__"
def save_to_memory_file(extracted_texts):
    """Sauvegarde les informations des classes dans un fichier mémoire
    en fusionnant avec les données existantes sans duplication.
    
    Args:
        extracted_texts: Liste des textes extraits des classes
    """
    memory_data = {}
    
    # Charger les données existantes si le fichier existe
    if os.path.exists(MEMORY_FILE_PATH):
        with open(MEMORY_FILE_PATH, "r", encoding="utf-8") as f:
            content = f.read()
            
        # Extraire les informations des classes existantes
        class_sections = content.split("\n\n")
        for section in class_sections:
            if not section.strip():
                continue
                
            lines = section.strip().split("\n")
            class_name = None
            attributes = []
            methods = []
            current_section = None
            
            for line in lines:
                if line.startswith("NOM_CLASSE:"):
                    class_name = line.replace("NOM_CLASSE:", "").strip()
                elif line.startswith("ATTRIBUTS:"):
                    current_section = "attributs"
                elif line.startswith("MÉTHODES:"):
                    current_section = "methodes"
                elif line.strip() and current_section == "attributs":
                    attributes.append(line.strip())
                elif line.strip() and current_section == "methodes":
                    methods.append(line.strip())
            
            if class_name:
                memory_data[class_name] = {
                    "attributes": attributes,
                    "methods": methods
                }
    
    # Traiter les nouvelles données
    for text_item in extracted_texts:
        # Format attendu: "class X:\nNOM_CLASSE: [nom]\nATTRIBUTS: ...\nMÉTHODES: ..."
        class_section = text_item.strip()
        lines = class_section.split("\n")
        
        class_name = None
        attributes = []
        methods = []
        current_section = None
        
        for line in lines:
            if line.startswith("class"):
                continue  # Ignorer la ligne "class X:"
            elif line.startswith("NOM_CLASSE:"):
                class_name = line.replace("NOM_CLASSE:", "").strip()
            elif line.startswith("ATTRIBUTS:"):
                current_section = "attributs"
            elif line.startswith("MÉTHODES:"):
                current_section = "methodes"
            elif line.strip() and current_section == "attributs":
                attributes.append(line.strip())
            elif line.strip() and current_section == "methodes":
                methods.append(line.strip())
        
        if class_name:
            # Si la classe existe déjà, fusionner les attributs et méthodes
            if class_name in memory_data:
                # Ajouter uniquement les attributs qui n'existent pas déjà
                for attr in attributes:
                    if attr not in memory_data[class_name]["attributes"]:
                        memory_data[class_name]["attributes"].append(attr)
                
                # Ajouter uniquement les méthodes qui n'existent pas déjà
                for method in methods:
                    if method not in memory_data[class_name]["methods"]:
                        memory_data[class_name]["methods"].append(method)
            else:
                # Nouvelle classe
                memory_data[class_name] = {
                    "attributes": attributes,
                    "methods": methods
                }
    
    # Écrire les données fusionnées dans le fichier
    with open(MEMORY_FILE_PATH, "w", encoding="utf-8") as f:
        for class_name, data in memory_data.items():
            f.write(f"NOM_CLASSE: {class_name}\n")
            f.write("ATTRIBUTS:\n")
            for attr in data["attributes"]:
                f.write(f"{attr}\n")
            f.write("MÉTHODES:\n")
            for method in data["methods"]:
                f.write(f"{method}\n")
            f.write("\n\n")
    
    return MEMORY_FILE_PATH

@app.get("/memoire.txt")
def get_memory_file():
    if os.path.exists(MEMORY_FILE_PATH):
        return FileResponse(MEMORY_FILE_PATH, media_type="text/plain", filename="memoire.txt")
    raise HTTPException(status_code=404, detail="Fichier mémoire introuvable.")

# Ajouter cette ligne au début du fichier, avec les autres constantes (vers la ligne 80)


# Et supprimer ou commenter cette ligne à la fin du fichier (ligne 606)
# MEMORY_FILE_PATH = "memoire.txt"

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("server:app", host="0.0.0.0", port=8000, reload=True)
