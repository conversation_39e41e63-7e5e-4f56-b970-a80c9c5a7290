{"version": 3, "file": "parser.js", "sourceRoot": "", "sources": ["../../../../src/parse/parser/parser.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AACtE,OAAO,EAAE,gBAAgB,EAAE,MAAM,mBAAmB,CAAC;AACrD,OAAO,EAAE,sBAAsB,EAAE,MAAM,sBAAsB,CAAC;AAC9D,OAAO,EAAE,mBAAmB,EAAE,GAAG,EAAE,MAAM,6BAA6B,CAAC;AACvE,OAAO,EACL,oCAAoC,EACpC,0BAA0B,GAC3B,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EACL,cAAc,EACd,eAAe,GAChB,MAAM,yCAAyC,CAAC;AAUjD,OAAO,EAAE,WAAW,EAAE,MAAM,yBAAyB,CAAC;AACtD,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AACpD,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AACvD,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AACzD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AAEjE,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AAEzD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AACzD,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,yBAAyB,CAAC;AAItD,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AAEzD,MAAM,CAAC,MAAM,WAAW,GAAG,mBAAmB,CAC5C,GAAG,EACH,EAAE,EACF,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,CACJ,CAAC;AACF,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;AAI3B,MAAM,CAAC,MAAM,qBAAqB,GAE9B,MAAM,CAAC,MAAM,CAAC;IAChB,eAAe,EAAE,KAAK;IACtB,YAAY,EAAE,CAAC;IACf,oBAAoB,EAAE,KAAK;IAC3B,SAAS,EAAE,IAAI;IACf,oBAAoB,EAAE,0BAA0B;IAChD,oBAAoB,EAAE,MAAM;IAC5B,aAAa,EAAE,KAAK;IACpB,eAAe,EAAE,KAAK;CACvB,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,mBAAmB,GAA+B,MAAM,CAAC,MAAM,CAAC;IAC3E,iBAAiB,EAAE,GAAG,EAAE,CAAC,SAAS;IAClC,aAAa,EAAE,IAAI;CACpB,CAAC,CAAC;AAEH,MAAM,CAAN,IAAY,yBAeX;AAfD,WAAY,yBAAyB;IACnC,mGAAqB,CAAA;IACrB,uGAAuB,CAAA;IACvB,2GAAyB,CAAA;IACzB,2GAAyB,CAAA;IACzB,6GAA0B,CAAA;IAC1B,6FAAkB,CAAA;IAClB,uGAAuB,CAAA;IACvB,6FAAkB,CAAA;IAClB,+HAAmC,CAAA;IACnC,qGAAsB,CAAA;IACtB,8GAA2B,CAAA;IAC3B,4GAA0B,CAAA;IAC1B,4FAAkB,CAAA;IAClB,wHAAgC,CAAA;AAClC,CAAC,EAfW,yBAAyB,KAAzB,yBAAyB,QAepC;AAqCD,MAAM,UAAU,SAAS,CAAC,QAAa,SAAS;IAC9C,OAAO;QACL,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,OAAO,MAAM;IASjB;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,cAAsB;QAC/C,MAAM,KAAK,CACT,kEAAkE;YAChE,6DAA6D,CAChE,CAAC;IACJ,CAAC;IAEM,mBAAmB;QACxB,IAAI,CAAC,UAAU,CAAC,qBAAqB,EAAE,GAAG,EAAE;YAC1C,IAAI,aAAa,CAAC;YAElB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YAEjC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,GAAG,EAAE;gBAClC,6DAA6D;gBAC7D,gEAAgE;gBAChE,0EAA0E;gBAC1E,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,UAAU,CAAC,mBAAmB,EAAE,GAAG,EAAE;gBACxC,IAAI;oBACF,IAAI,CAAC,eAAe,EAAE,CAAC;oBACvB,oBAAoB;oBACpB,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,YAAY,EAAE,EAAE;wBAC/C,MAAM,WAAW,GAAI,IAAY,CAC/B,YAAY,CAC+B,CAAC;wBAC9C,MAAM,qBAAqB,GAAG,WAAW,CAAC,uBAAuB,CAAC,CAAC;wBACnE,IAAI,gBAAuB,CAAC;wBAC5B,IAAI,CAAC,UAAU,CAAC,GAAG,YAAY,OAAO,EAAE,GAAG,EAAE;4BAC3C,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CACxC,YAAY,EACZ,qBAAqB,CACtB,CAAC;wBACJ,CAAC,CAAC,CAAC;wBACH,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,GAAG,gBAAgB,CAAC;oBAC7D,CAAC,CAAC,CAAC;iBACJ;wBAAS;oBACR,IAAI,CAAC,gBAAgB,EAAE,CAAC;iBACzB;YACH,CAAC,CAAC,CAAC;YAEH,IAAI,cAAc,GAA6B,EAAE,CAAC;YAClD,IAAI,CAAC,UAAU,CAAC,mBAAmB,EAAE,GAAG,EAAE;gBACxC,cAAc,GAAG,cAAc,CAAC;oBAC9B,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;iBACzC,CAAC,CAAC;gBACH,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,UAAU,CAAC,qBAAqB,EAAE,GAAG,EAAE;gBAC1C,qFAAqF;gBACrF,+FAA+F;gBAC/F,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,eAAe,KAAK,KAAK,EAAE;oBAC7D,MAAM,gBAAgB,GAAG,eAAe,CAAC;wBACvC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;wBACxC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;wBAClC,cAAc,EAAE,oCAAoC;wBACpD,WAAW,EAAE,SAAS;qBACvB,CAAC,CAAC;oBACH,MAAM,yBAAyB,GAAG,iBAAiB,CAAC;wBAClD,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;wBACzC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;wBACxC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;wBAClC,WAAW,EAAE,SAAS;qBACvB,CAAC,CAAC;oBACH,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAClD,gBAAgB,EAChB,yBAAyB,CAC1B,CAAC;iBACH;YACH,CAAC,CAAC,CAAC;YAEH,+DAA+D;YAC/D,IAAI,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE;gBAClC,qFAAqF;gBACrF,IAAI,IAAI,CAAC,eAAe,EAAE;oBACxB,IAAI,CAAC,UAAU,CAAC,wBAAwB,EAAE,GAAG,EAAE;wBAC7C,MAAM,UAAU,GAAG,sBAAsB,CACvC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAClC,CAAC;wBACF,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC;oBAClC,CAAC,CAAC,CAAC;iBACJ;gBAED,IAAI,CAAC,UAAU,CAAC,2BAA2B,EAAE,GAAG,EAAE;;oBAChD,MAAA,MAAA,IAAI,CAAC,iBAAiB,EAAC,UAAU,mDAAG;wBAClC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;qBACzC,CAAC,CAAC;oBACH,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC;gBACvE,CAAC,CAAC,CAAC;aACJ;YAED,IACE,CAAC,MAAM,CAAC,gCAAgC;gBACxC,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAC/B;gBACA,aAAa,GAAG,GAAG,CACjB,IAAI,CAAC,gBAAgB,EACrB,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CAC/B,CAAC;gBACF,MAAM,IAAI,KAAK,CACb,wCAAwC,aAAa,CAAC,IAAI,CACxD,qCAAqC,CACtC,EAAE,CACJ,CAAC;aACH;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAMD,YAAY,eAAgC,EAAE,MAAqB;QAJnE,qBAAgB,GAA6B,EAAE,CAAC;QAChD,qBAAgB,GAAG,KAAK,CAAC;QAIvB,MAAM,IAAI,GAAkB,IAAW,CAAC;QACxC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAC5B,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;QACnD,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAC7B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAC7B,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAEnC,IAAI,GAAG,CAAC,MAAM,EAAE,eAAe,CAAC,EAAE;YAChC,MAAM,IAAI,KAAK,CACb,qEAAqE;gBACnE,kFAAkF;gBAClF,8FAA8F;gBAC9F,sBAAsB,CACzB,CAAC;SACH;QAED,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC,MAAM,EAAE,iBAAiB,CAAC;YACnD,CAAC,CAAE,MAAM,CAAC,eAA2B,CAAC,wDAAwD;YAC9F,CAAC,CAAC,qBAAqB,CAAC,eAAe,CAAC;IAC5C,CAAC;;AAvJD,mHAAmH;AACnH,8CAA8C;AAC9C,oHAAoH;AACpH,+EAA+E;AAC/E,0GAA0G;AAC1G,kFAAkF;AAC3E,uCAAgC,GAAY,KAAK,AAAjB,CAAkB;AAoJ3D,WAAW,CAAC,MAAM,EAAE;IAClB,WAAW;IACX,UAAU;IACV,WAAW;IACX,YAAY;IACZ,gBAAgB;IAChB,aAAa;IACb,YAAY;IACZ,aAAa;IACb,YAAY;IACZ,iBAAiB;CAClB,CAAC,CAAC;AAEH,MAAM,OAAO,SAAU,SAAQ,MAAM;IACnC,YACE,eAAgC,EAChC,SAAgC,qBAAqB;QAErD,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;QAClC,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC;QAC7B,KAAK,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;IACtC,CAAC;CACF;AAED,MAAM,OAAO,qBAAsB,SAAQ,MAAM;IAC/C,YACE,eAAgC,EAChC,SAAgC,qBAAqB;QAErD,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;QAClC,WAAW,CAAC,SAAS,GAAG,KAAK,CAAC;QAC9B,KAAK,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;IACtC,CAAC;CACF"}