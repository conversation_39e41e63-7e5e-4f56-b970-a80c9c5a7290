{"version": 3, "sources": ["../../../src/diagrams/user-journey/parser/journey.jison", "../../../src/diagrams/user-journey/journeyDb.js", "../../../src/diagrams/user-journey/styles.js", "../../../src/diagrams/user-journey/journeyRenderer.ts", "../../../src/diagrams/user-journey/svgDraw.js", "../../../src/diagrams/user-journey/journeyDiagram.ts"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[6,8,10,11,12,14,16,17,18],$V1=[1,9],$V2=[1,10],$V3=[1,11],$V4=[1,12],$V5=[1,13],$V6=[1,14];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"start\":3,\"journey\":4,\"document\":5,\"EOF\":6,\"line\":7,\"SPACE\":8,\"statement\":9,\"NEWLINE\":10,\"title\":11,\"acc_title\":12,\"acc_title_value\":13,\"acc_descr\":14,\"acc_descr_value\":15,\"acc_descr_multiline_value\":16,\"section\":17,\"taskName\":18,\"taskData\":19,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",4:\"journey\",6:\"EOF\",8:\"SPACE\",10:\"NEWLINE\",11:\"title\",12:\"acc_title\",13:\"acc_title_value\",14:\"acc_descr\",15:\"acc_descr_value\",16:\"acc_descr_multiline_value\",17:\"section\",18:\"taskName\",19:\"taskData\"},\nproductions_: [0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[9,1],[9,2],[9,2],[9,1],[9,1],[9,2]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 1:\n return $$[$0-1]; \nbreak;\ncase 2:\n this.$ = [] \nbreak;\ncase 3:\n$$[$0-1].push($$[$0]);this.$ = $$[$0-1]\nbreak;\ncase 4: case 5:\n this.$ = $$[$0] \nbreak;\ncase 6: case 7:\n this.$=[];\nbreak;\ncase 8:\nyy.setDiagramTitle($$[$0].substr(6));this.$=$$[$0].substr(6);\nbreak;\ncase 9:\n this.$=$$[$0].trim();yy.setAccTitle(this.$); \nbreak;\ncase 10: case 11:\n this.$=$$[$0].trim();yy.setAccDescription(this.$); \nbreak;\ncase 12:\nyy.addSection($$[$0].substr(8));this.$=$$[$0].substr(8);\nbreak;\ncase 13:\nyy.addTask($$[$0-1], $$[$0]);this.$='task';\nbreak;\n}\n},\ntable: [{3:1,4:[1,2]},{1:[3]},o($V0,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:$V1,12:$V2,14:$V3,16:$V4,17:$V5,18:$V6},o($V0,[2,7],{1:[2,1]}),o($V0,[2,3]),{9:15,11:$V1,12:$V2,14:$V3,16:$V4,17:$V5,18:$V6},o($V0,[2,5]),o($V0,[2,6]),o($V0,[2,8]),{13:[1,16]},{15:[1,17]},o($V0,[2,11]),o($V0,[2,12]),{19:[1,18]},o($V0,[2,4]),o($V0,[2,9]),o($V0,[2,10]),o($V0,[2,13])],\ndefaultActions: {},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {\"case-insensitive\":true},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0:/* skip comments */\nbreak;\ncase 1:/* skip comments */\nbreak;\ncase 2:return 10;\nbreak;\ncase 3:/* skip whitespace */\nbreak;\ncase 4:/* skip comments */\nbreak;\ncase 5:return 4;\nbreak;\ncase 6:return 11;\nbreak;\ncase 7: this.begin(\"acc_title\");return 12; \nbreak;\ncase 8: this.popState(); return \"acc_title_value\"; \nbreak;\ncase 9: this.begin(\"acc_descr\");return 14; \nbreak;\ncase 10: this.popState(); return \"acc_descr_value\"; \nbreak;\ncase 11: this.begin(\"acc_descr_multiline\");\nbreak;\ncase 12: this.popState(); \nbreak;\ncase 13:return \"acc_descr_multiline_value\";\nbreak;\ncase 14:return 17;\nbreak;\ncase 15:return 18;\nbreak;\ncase 16:return 19;\nbreak;\ncase 17:return ':';\nbreak;\ncase 18:return 6;\nbreak;\ncase 19:return 'INVALID';\nbreak;\n}\n},\nrules: [/^(?:%(?!\\{)[^\\n]*)/i,/^(?:[^\\}]%%[^\\n]*)/i,/^(?:[\\n]+)/i,/^(?:\\s+)/i,/^(?:#[^\\n]*)/i,/^(?:journey\\b)/i,/^(?:title\\s[^#\\n;]+)/i,/^(?:accTitle\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*:\\s*)/i,/^(?:(?!\\n||)*[^\\n]*)/i,/^(?:accDescr\\s*\\{\\s*)/i,/^(?:[\\}])/i,/^(?:[^\\}]*)/i,/^(?:section\\s[^#:\\n;]+)/i,/^(?:[^#:\\n;]+)/i,/^(?::[^#\\n;]+)/i,/^(?::)/i,/^(?:$)/i,/^(?:.)/i],\nconditions: {\"acc_descr_multiline\":{\"rules\":[12,13],\"inclusive\":false},\"acc_descr\":{\"rules\":[10],\"inclusive\":false},\"acc_title\":{\"rules\":[8],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,2,3,4,5,6,7,9,11,14,15,16,17,18,19],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport {\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription,\n  clear as commonClear,\n} from '../common/commonDb.js';\n\nlet currentSection = '';\n\nconst sections = [];\nconst tasks = [];\nconst rawTasks = [];\n\nexport const clear = function () {\n  sections.length = 0;\n  tasks.length = 0;\n  currentSection = '';\n  rawTasks.length = 0;\n  commonClear();\n};\n\nexport const addSection = function (txt) {\n  currentSection = txt;\n  sections.push(txt);\n};\n\nexport const getSections = function () {\n  return sections;\n};\n\nexport const getTasks = function () {\n  let allItemsProcessed = compileTasks();\n  const maxDepth = 100;\n  let iterationCount = 0;\n  while (!allItemsProcessed && iterationCount < maxDepth) {\n    allItemsProcessed = compileTasks();\n    iterationCount++;\n  }\n\n  tasks.push(...rawTasks);\n\n  return tasks;\n};\n\nconst updateActors = function () {\n  const tempActors = [];\n  tasks.forEach((task) => {\n    if (task.people) {\n      tempActors.push(...task.people);\n    }\n  });\n\n  const unique = new Set(tempActors);\n  return [...unique].sort();\n};\n\nexport const addTask = function (descr, taskData) {\n  const pieces = taskData.substr(1).split(':');\n\n  let score = 0;\n  let peeps = [];\n  if (pieces.length === 1) {\n    score = Number(pieces[0]);\n    peeps = [];\n  } else {\n    score = Number(pieces[0]);\n    peeps = pieces[1].split(',');\n  }\n  const peopleList = peeps.map((s) => s.trim());\n\n  const rawTask = {\n    section: currentSection,\n    type: currentSection,\n    people: peopleList,\n    task: descr,\n    score,\n  };\n\n  rawTasks.push(rawTask);\n};\n\nexport const addTaskOrg = function (descr) {\n  const newTask = {\n    section: currentSection,\n    type: currentSection,\n    description: descr,\n    task: descr,\n    classes: [],\n  };\n  tasks.push(newTask);\n};\n\nconst compileTasks = function () {\n  const compileTask = function (pos) {\n    return rawTasks[pos].processed;\n  };\n\n  let allProcessed = true;\n  for (const [i, rawTask] of rawTasks.entries()) {\n    compileTask(i);\n\n    allProcessed = allProcessed && rawTask.processed;\n  }\n  return allProcessed;\n};\n\nconst getActors = function () {\n  return updateActors();\n};\n\nexport default {\n  getConfig: () => getConfig().journey,\n  clear,\n  setDiagramTitle,\n  getDiagramTitle,\n  setAccTitle,\n  getAccTitle,\n  setAccDescription,\n  getAccDescription,\n  addSection,\n  getSections,\n  getTasks,\n  addTask,\n  addTaskOrg,\n  getActors,\n};\n", "const getStyles = (options) =>\n  `.label {\n    font-family: ${options.fontFamily};\n    color: ${options.textColor};\n  }\n  .mouth {\n    stroke: #666;\n  }\n\n  line {\n    stroke: ${options.textColor}\n  }\n\n  .legend {\n    fill: ${options.textColor};\n    font-family: ${options.fontFamily};\n  }\n\n  .label text {\n    fill: #333;\n  }\n  .label {\n    color: ${options.textColor}\n  }\n\n  .face {\n    ${options.faceColor ? `fill: ${options.faceColor}` : 'fill: #FFF8DC'};\n    stroke: #999;\n  }\n\n  .node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${options.mainBkg};\n    stroke: ${options.nodeBorder};\n    stroke-width: 1px;\n  }\n\n  .node .label {\n    text-align: center;\n  }\n  .node.clickable {\n    cursor: pointer;\n  }\n\n  .arrowheadPath {\n    fill: ${options.arrowheadColor};\n  }\n\n  .edgePath .path {\n    stroke: ${options.lineColor};\n    stroke-width: 1.5px;\n  }\n\n  .flowchart-link {\n    stroke: ${options.lineColor};\n    fill: none;\n  }\n\n  .edgeLabel {\n    background-color: ${options.edgeLabelBackground};\n    rect {\n      opacity: 0.5;\n    }\n    text-align: center;\n  }\n\n  .cluster rect {\n  }\n\n  .cluster text {\n    fill: ${options.titleColor};\n  }\n\n  div.mermaidTooltip {\n    position: absolute;\n    text-align: center;\n    max-width: 200px;\n    padding: 2px;\n    font-family: ${options.fontFamily};\n    font-size: 12px;\n    background: ${options.tertiaryColor};\n    border: 1px solid ${options.border2};\n    border-radius: 2px;\n    pointer-events: none;\n    z-index: 100;\n  }\n\n  .task-type-0, .section-type-0  {\n    ${options.fillType0 ? `fill: ${options.fillType0}` : ''};\n  }\n  .task-type-1, .section-type-1  {\n    ${options.fillType0 ? `fill: ${options.fillType1}` : ''};\n  }\n  .task-type-2, .section-type-2  {\n    ${options.fillType0 ? `fill: ${options.fillType2}` : ''};\n  }\n  .task-type-3, .section-type-3  {\n    ${options.fillType0 ? `fill: ${options.fillType3}` : ''};\n  }\n  .task-type-4, .section-type-4  {\n    ${options.fillType0 ? `fill: ${options.fillType4}` : ''};\n  }\n  .task-type-5, .section-type-5  {\n    ${options.fillType0 ? `fill: ${options.fillType5}` : ''};\n  }\n  .task-type-6, .section-type-6  {\n    ${options.fillType0 ? `fill: ${options.fillType6}` : ''};\n  }\n  .task-type-7, .section-type-7  {\n    ${options.fillType0 ? `fill: ${options.fillType7}` : ''};\n  }\n\n  .actor-0 {\n    ${options.actor0 ? `fill: ${options.actor0}` : ''};\n  }\n  .actor-1 {\n    ${options.actor1 ? `fill: ${options.actor1}` : ''};\n  }\n  .actor-2 {\n    ${options.actor2 ? `fill: ${options.actor2}` : ''};\n  }\n  .actor-3 {\n    ${options.actor3 ? `fill: ${options.actor3}` : ''};\n  }\n  .actor-4 {\n    ${options.actor4 ? `fill: ${options.actor4}` : ''};\n  }\n  .actor-5 {\n    ${options.actor5 ? `fill: ${options.actor5}` : ''};\n  }\n`;\n\nexport default getStyles;\n", "// @ts-nocheck TODO: fix file\nimport { select } from 'd3';\nimport svgDraw from './svgDraw.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\n\nexport const setConf = function (cnf) {\n  const keys = Object.keys(cnf);\n\n  keys.forEach(function (key) {\n    conf[key] = cnf[key];\n  });\n};\n\nconst actors = {};\n\n/** @param diagram - The diagram to draw to. */\nfunction drawActorLegend(diagram) {\n  const conf = getConfig().journey;\n  // Draw the actors\n  let yPos = 60;\n  Object.keys(actors).forEach((person) => {\n    const colour = actors[person].color;\n\n    const circleData = {\n      cx: 20,\n      cy: yPos,\n      r: 7,\n      fill: colour,\n      stroke: '#000',\n      pos: actors[person].position,\n    };\n    svgDraw.drawCircle(diagram, circleData);\n\n    const labelData = {\n      x: 40,\n      y: yPos + 7,\n      fill: '#666',\n      text: person,\n      textMargin: conf.boxTextMargin | 5,\n    };\n    svgDraw.drawText(diagram, labelData);\n\n    yPos += 20;\n  });\n}\n// TODO: Cleanup?\nconst conf = getConfig().journey;\nconst LEFT_MARGIN = conf.leftMargin;\nexport const draw = function (text, id, version, diagObj) {\n  const conf = getConfig().journey;\n\n  const securityLevel = getConfig().securityLevel;\n  // Handle root and Document for when rendering in sandbox mode\n  let sandboxElement;\n  if (securityLevel === 'sandbox') {\n    sandboxElement = select('#i' + id);\n  }\n  const root =\n    securityLevel === 'sandbox'\n      ? select(sandboxElement.nodes()[0].contentDocument.body)\n      : select('body');\n  // const doc = securityLevel === 'sandbox' ? sandboxElement.nodes()[0].contentDocument : document;\n\n  bounds.init();\n  const diagram = root.select('#' + id);\n\n  svgDraw.initGraphics(diagram);\n\n  const tasks = diagObj.db.getTasks();\n  const title = diagObj.db.getDiagramTitle();\n\n  const actorNames = diagObj.db.getActors();\n  for (const member in actors) {\n    delete actors[member];\n  }\n  let actorPos = 0;\n  actorNames.forEach((actorName) => {\n    actors[actorName] = {\n      color: conf.actorColours[actorPos % conf.actorColours.length],\n      position: actorPos,\n    };\n    actorPos++;\n  });\n\n  drawActorLegend(diagram);\n  bounds.insert(0, 0, LEFT_MARGIN, Object.keys(actors).length * 50);\n  drawTasks(diagram, tasks, 0);\n\n  const box = bounds.getBounds();\n  if (title) {\n    diagram\n      .append('text')\n      .text(title)\n      .attr('x', LEFT_MARGIN)\n      .attr('font-size', '4ex')\n      .attr('font-weight', 'bold')\n      .attr('y', 25);\n  }\n\n  const height = box.stopy - box.starty + 2 * conf.diagramMarginY;\n  const width = LEFT_MARGIN + box.stopx + 2 * conf.diagramMarginX;\n\n  configureSvgSize(diagram, height, width, conf.useMaxWidth);\n\n  // Draw activity line\n  diagram\n    .append('line')\n    .attr('x1', LEFT_MARGIN)\n    .attr('y1', conf.height * 4) // One section head + one task + margins\n    .attr('x2', width - LEFT_MARGIN - 4) // Subtract stroke width so arrow point is retained\n    .attr('y2', conf.height * 4)\n    .attr('stroke-width', 4)\n    .attr('stroke', 'black')\n    .attr('marker-end', 'url(#arrowhead)');\n\n  const extraVertForTitle = title ? 70 : 0;\n  diagram.attr('viewBox', `${box.startx} -25 ${width} ${height + extraVertForTitle}`);\n  diagram.attr('preserveAspectRatio', 'xMinYMin meet');\n  diagram.attr('height', height + extraVertForTitle + 25);\n};\n\nexport const bounds = {\n  data: {\n    startx: undefined,\n    stopx: undefined,\n    starty: undefined,\n    stopy: undefined,\n  },\n  verticalPos: 0,\n\n  sequenceItems: [],\n  init: function () {\n    this.sequenceItems = [];\n    this.data = {\n      startx: undefined,\n      stopx: undefined,\n      starty: undefined,\n      stopy: undefined,\n    };\n    this.verticalPos = 0;\n  },\n  updateVal: function (obj, key, val, fun) {\n    if (obj[key] === undefined) {\n      obj[key] = val;\n    } else {\n      obj[key] = fun(val, obj[key]);\n    }\n  },\n  updateBounds: function (startx, starty, stopx, stopy) {\n    const conf = getConfig().journey;\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const _self = this;\n    let cnt = 0;\n    /** @param type - Set to `activation` if activation */\n    function updateFn(type?: 'activation') {\n      return function updateItemBounds(item) {\n        cnt++;\n        // The loop sequenceItems is a stack so the biggest margins in the beginning of the sequenceItems\n        const n = _self.sequenceItems.length - cnt + 1;\n        _self.updateVal(item, 'starty', starty - n * conf.boxMargin, Math.min);\n        _self.updateVal(item, 'stopy', stopy + n * conf.boxMargin, Math.max);\n\n        _self.updateVal(bounds.data, 'startx', startx - n * conf.boxMargin, Math.min);\n        _self.updateVal(bounds.data, 'stopx', stopx + n * conf.boxMargin, Math.max);\n\n        if (!(type === 'activation')) {\n          _self.updateVal(item, 'startx', startx - n * conf.boxMargin, Math.min);\n          _self.updateVal(item, 'stopx', stopx + n * conf.boxMargin, Math.max);\n\n          _self.updateVal(bounds.data, 'starty', starty - n * conf.boxMargin, Math.min);\n          _self.updateVal(bounds.data, 'stopy', stopy + n * conf.boxMargin, Math.max);\n        }\n      };\n    }\n\n    this.sequenceItems.forEach(updateFn());\n  },\n  insert: function (startx, starty, stopx, stopy) {\n    const _startx = Math.min(startx, stopx);\n    const _stopx = Math.max(startx, stopx);\n    const _starty = Math.min(starty, stopy);\n    const _stopy = Math.max(starty, stopy);\n\n    this.updateVal(bounds.data, 'startx', _startx, Math.min);\n    this.updateVal(bounds.data, 'starty', _starty, Math.min);\n    this.updateVal(bounds.data, 'stopx', _stopx, Math.max);\n    this.updateVal(bounds.data, 'stopy', _stopy, Math.max);\n\n    this.updateBounds(_startx, _starty, _stopx, _stopy);\n  },\n  bumpVerticalPos: function (bump) {\n    this.verticalPos = this.verticalPos + bump;\n    this.data.stopy = this.verticalPos;\n  },\n  getVerticalPos: function () {\n    return this.verticalPos;\n  },\n  getBounds: function () {\n    return this.data;\n  },\n};\n\nconst fills = conf.sectionFills;\nconst textColours = conf.sectionColours;\n\nexport const drawTasks = function (diagram, tasks, verticalPos) {\n  const conf = getConfig().journey;\n  let lastSection = '';\n  const sectionVHeight = conf.height * 2 + conf.diagramMarginY;\n  const taskPos = verticalPos + sectionVHeight;\n\n  let sectionNumber = 0;\n  let fill = '#CCC';\n  let colour = 'black';\n  let num = 0;\n\n  // Draw the tasks\n  for (const [i, task] of tasks.entries()) {\n    if (lastSection !== task.section) {\n      fill = fills[sectionNumber % fills.length];\n      num = sectionNumber % fills.length;\n      colour = textColours[sectionNumber % textColours.length];\n\n      // count how many consecutive tasks have the same section\n      let taskInSectionCount = 0;\n      const currentSection = task.section;\n      for (let taskIndex = i; taskIndex < tasks.length; taskIndex++) {\n        if (tasks[taskIndex].section == currentSection) {\n          taskInSectionCount = taskInSectionCount + 1;\n        } else {\n          break;\n        }\n      }\n\n      const section = {\n        x: i * conf.taskMargin + i * conf.width + LEFT_MARGIN,\n        y: 50,\n        text: task.section,\n        fill,\n        num,\n        colour,\n        taskCount: taskInSectionCount,\n      };\n\n      svgDraw.drawSection(diagram, section, conf);\n      lastSection = task.section;\n      sectionNumber++;\n    }\n\n    // Collect the actors involved in the task\n    const taskActors = task.people.reduce((acc, actorName) => {\n      if (actors[actorName]) {\n        acc[actorName] = actors[actorName];\n      }\n\n      return acc;\n    }, {});\n\n    // Add some rendering data to the object\n    task.x = i * conf.taskMargin + i * conf.width + LEFT_MARGIN;\n    task.y = taskPos;\n    task.width = conf.diagramMarginX;\n    task.height = conf.diagramMarginY;\n    task.colour = colour;\n    task.fill = fill;\n    task.num = num;\n    task.actors = taskActors;\n\n    // Draw the box with the attached line\n    svgDraw.drawTask(diagram, task, conf);\n    bounds.insert(task.x, task.y, task.x + task.width + conf.taskMargin, 300 + 5 * 30); // stopY is the length of the descenders.\n  }\n};\n\nexport default {\n  setConf,\n  draw,\n};\n", "import { arc as d3arc } from 'd3';\nimport * as svgDrawCommon from '../common/svgDrawCommon.js';\n\nexport const drawRect = function (elem, rectData) {\n  return svgDrawCommon.drawRect(elem, rectData);\n};\n\nexport const drawFace = function (element, faceData) {\n  const radius = 15;\n  const circleElement = element\n    .append('circle')\n    .attr('cx', faceData.cx)\n    .attr('cy', faceData.cy)\n    .attr('class', 'face')\n    .attr('r', radius)\n    .attr('stroke-width', 2)\n    .attr('overflow', 'visible');\n\n  const face = element.append('g');\n\n  //left eye\n  face\n    .append('circle')\n    .attr('cx', faceData.cx - radius / 3)\n    .attr('cy', faceData.cy - radius / 3)\n    .attr('r', 1.5)\n    .attr('stroke-width', 2)\n    .attr('fill', '#666')\n    .attr('stroke', '#666');\n\n  //right eye\n  face\n    .append('circle')\n    .attr('cx', faceData.cx + radius / 3)\n    .attr('cy', faceData.cy - radius / 3)\n    .attr('r', 1.5)\n    .attr('stroke-width', 2)\n    .attr('fill', '#666')\n    .attr('stroke', '#666');\n\n  /** @param {any} face */\n  function smile(face) {\n    const arc = d3arc()\n      .startAngle(Math.PI / 2)\n      .endAngle(3 * (Math.PI / 2))\n      .innerRadius(radius / 2)\n      .outerRadius(radius / 2.2);\n    //mouth\n    face\n      .append('path')\n      .attr('class', 'mouth')\n      .attr('d', arc)\n      .attr('transform', 'translate(' + faceData.cx + ',' + (faceData.cy + 2) + ')');\n  }\n\n  /** @param {any} face */\n  function sad(face) {\n    const arc = d3arc()\n      .startAngle((3 * Math.PI) / 2)\n      .endAngle(5 * (Math.PI / 2))\n      .innerRadius(radius / 2)\n      .outerRadius(radius / 2.2);\n    //mouth\n    face\n      .append('path')\n      .attr('class', 'mouth')\n      .attr('d', arc)\n      .attr('transform', 'translate(' + faceData.cx + ',' + (faceData.cy + 7) + ')');\n  }\n\n  /** @param {any} face */\n  function ambivalent(face) {\n    face\n      .append('line')\n      .attr('class', 'mouth')\n      .attr('stroke', 2)\n      .attr('x1', faceData.cx - 5)\n      .attr('y1', faceData.cy + 7)\n      .attr('x2', faceData.cx + 5)\n      .attr('y2', faceData.cy + 7)\n      .attr('class', 'mouth')\n      .attr('stroke-width', '1px')\n      .attr('stroke', '#666');\n  }\n\n  if (faceData.score > 3) {\n    smile(face);\n  } else if (faceData.score < 3) {\n    sad(face);\n  } else {\n    ambivalent(face);\n  }\n\n  return circleElement;\n};\n\nexport const drawCircle = function (element, circleData) {\n  const circleElement = element.append('circle');\n  circleElement.attr('cx', circleData.cx);\n  circleElement.attr('cy', circleData.cy);\n  circleElement.attr('class', 'actor-' + circleData.pos);\n  circleElement.attr('fill', circleData.fill);\n  circleElement.attr('stroke', circleData.stroke);\n  circleElement.attr('r', circleData.r);\n\n  if (circleElement.class !== undefined) {\n    circleElement.attr('class', circleElement.class);\n  }\n\n  if (circleData.title !== undefined) {\n    circleElement.append('title').text(circleData.title);\n  }\n\n  return circleElement;\n};\n\nexport const drawText = function (elem, textData) {\n  return svgDrawCommon.drawText(elem, textData);\n};\n\nexport const drawLabel = function (elem, txtObject) {\n  /**\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} cut\n   */\n  function genPoints(x, y, width, height, cut) {\n    return (\n      x +\n      ',' +\n      y +\n      ' ' +\n      (x + width) +\n      ',' +\n      y +\n      ' ' +\n      (x + width) +\n      ',' +\n      (y + height - cut) +\n      ' ' +\n      (x + width - cut * 1.2) +\n      ',' +\n      (y + height) +\n      ' ' +\n      x +\n      ',' +\n      (y + height)\n    );\n  }\n  const polygon = elem.append('polygon');\n  polygon.attr('points', genPoints(txtObject.x, txtObject.y, 50, 20, 7));\n  polygon.attr('class', 'labelBox');\n\n  txtObject.y = txtObject.y + txtObject.labelMargin;\n  txtObject.x = txtObject.x + 0.5 * txtObject.labelMargin;\n  drawText(elem, txtObject);\n};\n\nexport const drawSection = function (elem, section, conf) {\n  const g = elem.append('g');\n\n  const rect = svgDrawCommon.getNoteRect();\n  rect.x = section.x;\n  rect.y = section.y;\n  rect.fill = section.fill;\n  // section width covers all nested tasks\n  rect.width =\n    conf.width * section.taskCount + // width of the tasks\n    conf.diagramMarginX * (section.taskCount - 1); // width of space between tasks\n  rect.height = conf.height;\n  rect.class = 'journey-section section-type-' + section.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect(g, rect);\n\n  _drawTextCandidateFunc(conf)(\n    section.text,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: 'journey-section section-type-' + section.num },\n    conf,\n    section.colour\n  );\n};\n\nlet taskCount = -1;\n/**\n * Draws an actor in the diagram with the attached line\n *\n * @param {any} elem The HTML element\n * @param {any} task The task to render\n * @param {any} conf The global configuration\n */\nexport const drawTask = function (elem, task, conf) {\n  const center = task.x + conf.width / 2;\n  const g = elem.append('g');\n  taskCount++;\n  const maxHeight = 300 + 5 * 30;\n  g.append('line')\n    .attr('id', 'task' + taskCount)\n    .attr('x1', center)\n    .attr('y1', task.y)\n    .attr('x2', center)\n    .attr('y2', maxHeight)\n    .attr('class', 'task-line')\n    .attr('stroke-width', '1px')\n    .attr('stroke-dasharray', '4 2')\n    .attr('stroke', '#666');\n\n  drawFace(g, {\n    cx: center,\n    cy: 300 + (5 - task.score) * 30,\n    score: task.score,\n  });\n\n  const rect = svgDrawCommon.getNoteRect();\n  rect.x = task.x;\n  rect.y = task.y;\n  rect.fill = task.fill;\n  rect.width = conf.width;\n  rect.height = conf.height;\n  rect.class = 'task task-type-' + task.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect(g, rect);\n\n  let xPos = task.x + 14;\n  task.people.forEach((person) => {\n    const colour = task.actors[person].color;\n\n    const circle = {\n      cx: xPos,\n      cy: task.y,\n      r: 7,\n      fill: colour,\n      stroke: '#000',\n      title: person,\n      pos: task.actors[person].position,\n    };\n\n    drawCircle(g, circle);\n    xPos += 10;\n  });\n\n  _drawTextCandidateFunc(conf)(\n    task.task,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: 'task' },\n    conf,\n    task.colour\n  );\n};\n\n/**\n * Draws a background rectangle\n *\n * @param {any} elem The html element\n * @param {any} bounds The bounds of the drawing\n */\nexport const drawBackgroundRect = function (elem, bounds) {\n  svgDrawCommon.drawBackgroundRect(elem, bounds);\n};\n\nconst _drawTextCandidateFunc = (function () {\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} colour\n   */\n  function byText(content, g, x, y, width, height, textAttrs, colour) {\n    const text = g\n      .append('text')\n      .attr('x', x + width / 2)\n      .attr('y', y + height / 2 + 5)\n      .style('font-color', colour)\n      .style('text-anchor', 'middle')\n      .text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} conf\n   * @param {any} colour\n   */\n  function byTspan(content, g, x, y, width, height, textAttrs, conf, colour) {\n    const { taskFontSize, taskFontFamily } = conf;\n\n    const lines = content.split(/<br\\s*\\/?>/gi);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * taskFontSize - (taskFontSize * (lines.length - 1)) / 2;\n      const text = g\n        .append('text')\n        .attr('x', x + width / 2)\n        .attr('y', y)\n        .attr('fill', colour)\n        .style('text-anchor', 'middle')\n        .style('font-size', taskFontSize)\n        .style('font-family', taskFontFamily);\n      text\n        .append('tspan')\n        .attr('x', x + width / 2)\n        .attr('dy', dy)\n        .text(lines[i]);\n\n      text\n        .attr('y', y + height / 2.0)\n        .attr('dominant-baseline', 'central')\n        .attr('alignment-baseline', 'central');\n\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} conf\n   */\n  function byFo(content, g, x, y, width, height, textAttrs, conf) {\n    const body = g.append('switch');\n    const f = body\n      .append('foreignObject')\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', width)\n      .attr('height', height)\n      .attr('position', 'fixed');\n\n    const text = f\n      .append('xhtml:div')\n      .style('display', 'table')\n      .style('height', '100%')\n      .style('width', '100%');\n\n    text\n      .append('div')\n      .attr('class', 'label')\n      .style('display', 'table-cell')\n      .style('text-align', 'center')\n      .style('vertical-align', 'middle')\n      .text(content);\n\n    byTspan(content, body, x, y, width, height, textAttrs, conf);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   * @param {any} toText\n   * @param {any} fromTextAttrsDict\n   */\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (key in fromTextAttrsDict) {\n        // noinspection JSUnfilteredForInLoop\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n\n  return function (conf) {\n    return conf.textPlacement === 'fo' ? byFo : conf.textPlacement === 'old' ? byText : byTspan;\n  };\n})();\n\nconst initGraphics = function (graphics) {\n  graphics\n    .append('defs')\n    .append('marker')\n    .attr('id', 'arrowhead')\n    .attr('refX', 5)\n    .attr('refY', 2)\n    .attr('markerWidth', 6)\n    .attr('markerHeight', 4)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 0,0 V 4 L6,2 Z'); // this is actual shape for arrowhead\n};\n\nexport default {\n  drawRect,\n  drawCircle,\n  drawSection,\n  drawText,\n  drawLabel,\n  drawTask,\n  drawBackgroundRect,\n  initGraphics,\n};\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\n// @ts-ignore: JISON doesn't support types\nimport parser from './parser/journey.jison';\nimport db from './journeyDb.js';\nimport styles from './styles.js';\nimport renderer from './journeyRenderer.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  db,\n  renderer,\n  styles,\n  init: (cnf) => {\n    renderer.setConf(cnf.journey);\n    db.clear();\n  },\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAyEA,IAAI,SAAU,WAAU;AACxB,MAAI,IAAE,gCAAS,GAAE,GAAEA,IAAE,GAAE;AAAC,SAAIA,KAAEA,MAAG,CAAC,GAAE,IAAE,EAAE,QAAO,KAAIA,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE;AAAC,WAAOA;AAAA,EAAC,GAAhE,MAAkE,MAAI,CAAC,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,MAAI,CAAC,GAAE,CAAC,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE,GAAE,MAAI,CAAC,GAAE,EAAE;AACtK,MAAIC,UAAS;AAAA,IAAC,OAAO,gCAAS,QAAS;AAAA,IAAE,GAApB;AAAA,IACrB,IAAI,CAAC;AAAA,IACL,UAAU,EAAC,SAAQ,GAAE,SAAQ,GAAE,WAAU,GAAE,YAAW,GAAE,OAAM,GAAE,QAAO,GAAE,SAAQ,GAAE,aAAY,GAAE,WAAU,IAAG,SAAQ,IAAG,aAAY,IAAG,mBAAkB,IAAG,aAAY,IAAG,mBAAkB,IAAG,6BAA4B,IAAG,WAAU,IAAG,YAAW,IAAG,YAAW,IAAG,WAAU,GAAE,QAAO,EAAC;AAAA,IAC7R,YAAY,EAAC,GAAE,SAAQ,GAAE,WAAU,GAAE,OAAM,GAAE,SAAQ,IAAG,WAAU,IAAG,SAAQ,IAAG,aAAY,IAAG,mBAAkB,IAAG,aAAY,IAAG,mBAAkB,IAAG,6BAA4B,IAAG,WAAU,IAAG,YAAW,IAAG,WAAU;AAAA,IAC5N,cAAc,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC;AAAA,IAC9F,eAAe,gCAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAyB,IAAiB,IAAiB;AAG3H,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAS;AAAA,QACjB,KAAK;AACJ,iBAAO,GAAG,KAAG,CAAC;AACf;AAAA,QACA,KAAK;AACJ,eAAK,IAAI,CAAC;AACX;AAAA,QACA,KAAK;AACL,aAAG,KAAG,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC;AAAE,eAAK,IAAI,GAAG,KAAG,CAAC;AACtC;AAAA,QACA,KAAK;AAAA,QAAG,KAAK;AACZ,eAAK,IAAI,GAAG,EAAE;AACf;AAAA,QACA,KAAK;AAAA,QAAG,KAAK;AACZ,eAAK,IAAE,CAAC;AACT;AAAA,QACA,KAAK;AACL,aAAG,gBAAgB,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC;AAAE,eAAK,IAAE,GAAG,EAAE,EAAE,OAAO,CAAC;AAC3D;AAAA,QACA,KAAK;AACJ,eAAK,IAAE,GAAG,EAAE,EAAE,KAAK;AAAE,aAAG,YAAY,KAAK,CAAC;AAC3C;AAAA,QACA,KAAK;AAAA,QAAI,KAAK;AACb,eAAK,IAAE,GAAG,EAAE,EAAE,KAAK;AAAE,aAAG,kBAAkB,KAAK,CAAC;AACjD;AAAA,QACA,KAAK;AACL,aAAG,WAAW,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC;AAAE,eAAK,IAAE,GAAG,EAAE,EAAE,OAAO,CAAC;AACtD;AAAA,QACA,KAAK;AACL,aAAG,QAAQ,GAAG,KAAG,CAAC,GAAG,GAAG,EAAE,CAAC;AAAE,eAAK,IAAE;AACpC;AAAA,MACA;AAAA,IACA,GApCe;AAAA,IAqCf,OAAO,CAAC,EAAC,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,GAAE,CAAC,CAAC,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,EAAC,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,CAAC,GAAE,CAAC,EAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAC,GAAE,IAAG,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAC,IAAG,CAAC,GAAE,EAAE,EAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,KAAI,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAC/W,gBAAgB,CAAC;AAAA,IACjB,YAAY,gCAAS,WAAY,KAAK,MAAM;AACxC,UAAI,KAAK,aAAa;AAClB,aAAK,MAAM,GAAG;AAAA,MAClB,OAAO;AACH,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;AAAA,MACV;AAAA,IACJ,GARY;AAAA,IASZ,OAAO,gCAAS,MAAM,OAAO;AACzB,UAAI,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAIC,SAAQ,OAAO,OAAO,KAAK,KAAK;AACpC,UAAI,cAAc,EAAE,IAAI,CAAC,EAAE;AAC3B,eAAS,KAAK,KAAK,IAAI;AACnB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AAClD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;AAAA,QACjC;AAAA,MACJ;AACA,MAAAA,OAAM,SAAS,OAAO,YAAY,EAAE;AACpC,kBAAY,GAAG,QAAQA;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAOA,OAAM,UAAU,aAAa;AACpC,QAAAA,OAAM,SAAS,CAAC;AAAA,MACpB;AACA,UAAI,QAAQA,OAAM;AAClB,aAAO,KAAK,KAAK;AACjB,UAAI,SAASA,OAAM,WAAWA,OAAM,QAAQ;AAC5C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACjD,aAAK,aAAa,YAAY,GAAG;AAAA,MACrC,OAAO;AACH,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;AAAA,MAClD;AACA,eAAS,SAAS,GAAG;AACjB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;AAAA,MACpC;AAJS;AAKD,eAAS,MAAM;AACf,YAAI;AACJ,gBAAQ,OAAO,IAAI,KAAKA,OAAM,IAAI,KAAK;AACvC,YAAI,OAAO,UAAU,UAAU;AAC3B,cAAI,iBAAiB,OAAO;AACxB,qBAAS;AACT,oBAAQ,OAAO,IAAI;AAAA,UACvB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,QACpC;AACA,eAAO;AAAA,MACX;AAXa;AAYjB,UAAI,QAAQ,gBAAgB,OAAO,QAAQ,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACT,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC5B,mBAAS,KAAK,eAAe,KAAK;AAAA,QACtC,OAAO;AACH,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACjD,qBAAS,IAAI;AAAA,UACjB;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,QAChD;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AAC/D,cAAI,SAAS;AACb,qBAAW,CAAC;AACZ,eAAK,KAAK,MAAM,KAAK,GAAG;AACpB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AAClC,uBAAS,KAAK,MAAO,KAAK,WAAW,CAAC,IAAI,GAAI;AAAA,YAClD;AAAA,UACJ;AACA,cAAIA,OAAM,cAAc;AACpB,qBAAS,0BAA0B,WAAW,KAAK,QAAQA,OAAM,aAAa,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAc,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UAChL,OAAO;AACH,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAQ,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UACxJ;AACA,eAAK,WAAW,QAAQ;AAAA,YACpB,MAAMA,OAAM;AAAA,YACZ,OAAO,KAAK,WAAW,MAAM,KAAK;AAAA,YAClC,MAAMA,OAAM;AAAA,YACZ,KAAK;AAAA,YACL;AAAA,UACJ,CAAC;AAAA,QACL;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACjD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;AAAA,QACtG;AACA,gBAAQ,OAAO,CAAC,GAAG;AAAA,UACnB,KAAK;AACD,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAKA,OAAM,MAAM;AACxB,mBAAO,KAAKA,OAAM,MAAM;AACxB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACT,gBAAI,CAAC,gBAAgB;AACjB,uBAASA,OAAM;AACf,uBAASA,OAAM;AACf,yBAAWA,OAAM;AACjB,sBAAQA,OAAM;AACd,kBAAI,aAAa,GAAG;AAChB;AAAA,cACJ;AAAA,YACJ,OAAO;AACH,uBAAS;AACT,+BAAiB;AAAA,YACrB;AACA;AAAA,UACJ,KAAK;AACD,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;AAAA,cACP,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,YAC3C;AACA,gBAAI,QAAQ;AACR,oBAAM,GAAG,QAAQ;AAAA,gBACb,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;AAAA,gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,cACrC;AAAA,YACJ;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;AAAA,cAChC;AAAA,cACA;AAAA,cACA;AAAA,cACA,YAAY;AAAA,cACZ,OAAO,CAAC;AAAA,cACR;AAAA,cACA;AAAA,YACJ,EAAE,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC1B,qBAAO;AAAA,YACX;AACA,gBAAI,KAAK;AACL,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AAAA,YACrC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;AAAA,UACJ,KAAK;AACD,mBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX,GA3IO;AAAA,EA2IN;AAGD,MAAI,QAAS,2BAAU;AACvB,QAAIA,SAAS;AAAA,MAEb,KAAI;AAAA,MAEJ,YAAW,gCAAS,WAAW,KAAK,MAAM;AAClC,YAAI,KAAK,GAAG,QAAQ;AAChB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;AAAA,QACvC,OAAO;AACH,gBAAM,IAAI,MAAM,GAAG;AAAA,QACvB;AAAA,MACJ,GANO;AAAA;AAAA,MASX,UAAS,gCAAU,OAAO,IAAI;AACtB,aAAK,KAAK,MAAM,KAAK,MAAM,CAAC;AAC5B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;AAAA,UACV,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,WAAW;AAAA,UACX,aAAa;AAAA,QACjB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,GAAE,CAAC;AAAA,QAC5B;AACA,aAAK,SAAS;AACd,eAAO;AAAA,MACX,GAlBK;AAAA;AAAA,MAqBT,OAAM,kCAAY;AACV,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACP,eAAK;AACL,eAAK,OAAO;AAAA,QAChB,OAAO;AACH,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,MAAM,CAAC;AAAA,QACvB;AAEA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;AAAA,MACX,GApBE;AAAA;AAAA,MAuBN,OAAM,gCAAU,IAAI;AACZ,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AAEpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAE5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAE7D,YAAI,MAAM,SAAS,GAAG;AAClB,eAAK,YAAY,MAAM,SAAS;AAAA,QACpC;AACA,YAAI,IAAI,KAAK,OAAO;AAEpB,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,SACR,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAC5D,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAChE,KAAK,OAAO,eAAe;AAAA,QACjC;AAEA,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;AAAA,QACvD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;AAAA,MACX,GAhCE;AAAA;AAAA,MAmCN,MAAK,kCAAY;AACT,aAAK,QAAQ;AACb,eAAO;AAAA,MACX,GAHC;AAAA;AAAA,MAML,QAAO,kCAAY;AACX,YAAI,KAAK,QAAQ,iBAAiB;AAC9B,eAAK,aAAa;AAAA,QACtB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAa,GAAG;AAAA,YAC9N,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QAEL;AACA,eAAO;AAAA,MACX,GAZG;AAAA;AAAA,MAeP,MAAK,gCAAU,GAAG;AACV,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,MAClC,GAFC;AAAA;AAAA,MAKL,WAAU,kCAAY;AACd,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAM,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,MAC7E,GAHM;AAAA;AAAA,MAMV,eAAc,kCAAY;AAClB,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AAClB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAG,KAAK,MAAM;AAAA,QAChD;AACA,gBAAQ,KAAK,OAAO,GAAE,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;AAAA,MAClF,GANU;AAAA;AAAA,MASd,cAAa,kCAAY;AACjB,YAAI,MAAM,KAAK,UAAU;AACzB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAc,IAAI,OAAO,IAAI;AAAA,MACnD,GAJS;AAAA;AAAA,MAOb,YAAW,gCAAS,OAAO,cAAc;AACjC,YAAI,OACA,OACA;AAEJ,YAAI,KAAK,QAAQ,iBAAiB;AAE9B,mBAAS;AAAA,YACL,UAAU,KAAK;AAAA,YACf,QAAQ;AAAA,cACJ,YAAY,KAAK,OAAO;AAAA,cACxB,WAAW,KAAK;AAAA,cAChB,cAAc,KAAK,OAAO;AAAA,cAC1B,aAAa,KAAK,OAAO;AAAA,YAC7B;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,IAAI,KAAK;AAAA,YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;AAAA,YAC3C,MAAM,KAAK;AAAA,UACf;AACA,cAAI,KAAK,QAAQ,QAAQ;AACrB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;AAAA,UACnD;AAAA,QACJ;AAEA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACP,eAAK,YAAY,MAAM;AAAA,QAC3B;AACA,aAAK,SAAS;AAAA,UACV,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,QACA,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAC5E,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;AAAA,QACpD;AACA,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACrB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;AAAA,QAChE;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC1B,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,OAAO;AACP,iBAAO;AAAA,QACX,WAAW,KAAK,YAAY;AAExB,mBAAS,KAAK,QAAQ;AAClB,iBAAK,CAAC,IAAI,OAAO,CAAC;AAAA,UACtB;AACA,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,GArEO;AAAA;AAAA,MAwEX,MAAK,kCAAY;AACT,YAAI,KAAK,MAAM;AACX,iBAAO,KAAK;AAAA,QAChB;AACA,YAAI,CAAC,KAAK,QAAQ;AACd,eAAK,OAAO;AAAA,QAChB;AAEA,YAAI,OACA,OACA,WACA;AACJ,YAAI,CAAC,KAAK,OAAO;AACb,eAAK,SAAS;AACd,eAAK,QAAQ;AAAA,QACjB;AACA,YAAI,QAAQ,KAAK,cAAc;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAChE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAC9B,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACjB,uBAAO;AAAA,cACX,WAAW,KAAK,YAAY;AACxB,wBAAQ;AACR;AAAA,cACJ,OAAO;AAEH,uBAAO;AAAA,cACX;AAAA,YACJ,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC3B;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,OAAO;AACP,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACjB,mBAAO;AAAA,UACX;AAEA,iBAAO;AAAA,QACX;AACA,YAAI,KAAK,WAAW,IAAI;AACpB,iBAAO,KAAK;AAAA,QAChB,OAAO;AACH,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAa,GAAG;AAAA,YACpH,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACf,CAAC;AAAA,QACL;AAAA,MACJ,GAvDC;AAAA;AAAA,MA0DL,KAAI,gCAAS,MAAO;AACZ,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,GAAG;AACH,iBAAO;AAAA,QACX,OAAO;AACH,iBAAO,KAAK,IAAI;AAAA,QACpB;AAAA,MACJ,GAPA;AAAA;AAAA,MAUJ,OAAM,gCAAS,MAAO,WAAW;AACzB,aAAK,eAAe,KAAK,SAAS;AAAA,MACtC,GAFE;AAAA;AAAA,MAKN,UAAS,gCAAS,WAAY;AACtB,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACP,iBAAO,KAAK,eAAe,IAAI;AAAA,QACnC,OAAO;AACH,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,eAAc,gCAAS,gBAAiB;AAChC,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACnF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;AAAA,QAChF,OAAO;AACH,iBAAO,KAAK,WAAW,SAAS,EAAE;AAAA,QACtC;AAAA,MACJ,GANU;AAAA;AAAA,MASd,UAAS,gCAAS,SAAU,GAAG;AACvB,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACR,iBAAO,KAAK,eAAe,CAAC;AAAA,QAChC,OAAO;AACH,iBAAO;AAAA,QACX;AAAA,MACJ,GAPK;AAAA;AAAA,MAUT,WAAU,gCAAS,UAAW,WAAW;AACjC,aAAK,MAAM,SAAS;AAAA,MACxB,GAFM;AAAA;AAAA,MAKV,gBAAe,gCAAS,iBAAiB;AACjC,eAAO,KAAK,eAAe;AAAA,MAC/B,GAFW;AAAA,MAGf,SAAS,EAAC,oBAAmB,KAAI;AAAA,MACjC,eAAe,gCAAS,UAAU,IAAG,KAAI,2BAA0B,UAAU;AAC7E,YAAI,UAAQ;AACZ,gBAAO,2BAA2B;AAAA,UAClC,KAAK;AACL;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AACL;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAE,mBAAO;AACd;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,WAAW;AAAE,mBAAO;AACvC;AAAA,UACA,KAAK;AAAG,iBAAK,SAAS;AAAG,mBAAO;AAChC;AAAA,UACA,KAAK;AAAG,iBAAK,MAAM,WAAW;AAAE,mBAAO;AACvC;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AAAG,mBAAO;AACjC;AAAA,UACA,KAAK;AAAI,iBAAK,MAAM,qBAAqB;AACzC;AAAA,UACA,KAAK;AAAI,iBAAK,SAAS;AACvB;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,UACA,KAAK;AAAG,mBAAO;AACf;AAAA,QACA;AAAA,MACA,GA5Ce;AAAA,MA6Cf,OAAO,CAAC,uBAAsB,uBAAsB,eAAc,aAAY,iBAAgB,mBAAkB,yBAAwB,yBAAwB,yBAAwB,yBAAwB,yBAAwB,0BAAyB,cAAa,gBAAe,4BAA2B,mBAAkB,mBAAkB,WAAU,WAAU,SAAS;AAAA,MACzX,YAAY,EAAC,uBAAsB,EAAC,SAAQ,CAAC,IAAG,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,EAAE,GAAE,aAAY,MAAK,GAAE,aAAY,EAAC,SAAQ,CAAC,CAAC,GAAE,aAAY,MAAK,GAAE,WAAU,EAAC,SAAQ,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,aAAY,KAAI,EAAC;AAAA,IAC7O;AACA,WAAOA;AAAA,EACP,EAAG;AACH,EAAAD,QAAO,QAAQ;AACf,WAAS,SAAU;AACjB,SAAK,KAAK,CAAC;AAAA,EACb;AAFS;AAGT,SAAO,YAAYA;AAAO,EAAAA,QAAO,SAAS;AAC1C,SAAO,IAAI;AACX,EAAG;AACF,OAAO,SAAS;AAEhB,IAAO,kBAAQ;;;ACloBhB,IAAI,iBAAiB;AAErB,IAAM,WAAW,CAAC;AAClB,IAAM,QAAQ,CAAC;AACf,IAAM,WAAW,CAAC;AAEX,IAAME,SAAQ,kCAAY;AAC/B,WAAS,SAAS;AAClB,QAAM,SAAS;AACf,mBAAiB;AACjB,WAAS,SAAS;AAClB,QAAY;AACd,GANqB;AAQd,IAAM,aAAa,gCAAU,KAAK;AACvC,mBAAiB;AACjB,WAAS,KAAK,GAAG;AACnB,GAH0B;AAKnB,IAAM,cAAc,kCAAY;AACrC,SAAO;AACT,GAF2B;AAIpB,IAAM,WAAW,kCAAY;AAClC,MAAI,oBAAoB,aAAa;AACrC,QAAM,WAAW;AACjB,MAAI,iBAAiB;AACrB,SAAO,CAAC,qBAAqB,iBAAiB,UAAU;AACtD,wBAAoB,aAAa;AACjC;AAAA,EACF;AAEA,QAAM,KAAK,GAAG,QAAQ;AAEtB,SAAO;AACT,GAZwB;AAcxB,IAAM,eAAe,kCAAY;AAC/B,QAAM,aAAa,CAAC;AACpB,QAAM,QAAQ,CAAC,SAAS;AACtB,QAAI,KAAK,QAAQ;AACf,iBAAW,KAAK,GAAG,KAAK,MAAM;AAAA,IAChC;AAAA,EACF,CAAC;AAED,QAAM,SAAS,IAAI,IAAI,UAAU;AACjC,SAAO,CAAC,GAAG,MAAM,EAAE,KAAK;AAC1B,GAVqB;AAYd,IAAM,UAAU,gCAAU,OAAO,UAAU;AAChD,QAAM,SAAS,SAAS,OAAO,CAAC,EAAE,MAAM,GAAG;AAE3C,MAAI,QAAQ;AACZ,MAAI,QAAQ,CAAC;AACb,MAAI,OAAO,WAAW,GAAG;AACvB,YAAQ,OAAO,OAAO,CAAC,CAAC;AACxB,YAAQ,CAAC;AAAA,EACX,OAAO;AACL,YAAQ,OAAO,OAAO,CAAC,CAAC;AACxB,YAAQ,OAAO,CAAC,EAAE,MAAM,GAAG;AAAA,EAC7B;AACA,QAAM,aAAa,MAAM,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;AAE5C,QAAM,UAAU;AAAA,IACd,SAAS;AAAA,IACT,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,MAAM;AAAA,IACN;AAAA,EACF;AAEA,WAAS,KAAK,OAAO;AACvB,GAvBuB;AAyBhB,IAAM,aAAa,gCAAU,OAAO;AACzC,QAAM,UAAU;AAAA,IACd,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS,CAAC;AAAA,EACZ;AACA,QAAM,KAAK,OAAO;AACpB,GAT0B;AAW1B,IAAM,eAAe,kCAAY;AAC/B,QAAM,cAAc,gCAAU,KAAK;AACjC,WAAO,SAAS,GAAG,EAAE;AAAA,EACvB,GAFoB;AAIpB,MAAI,eAAe;AACnB,aAAW,CAAC,GAAG,OAAO,KAAK,SAAS,QAAQ,GAAG;AAC7C,gBAAY,CAAC;AAEb,mBAAe,gBAAgB,QAAQ;AAAA,EACzC;AACA,SAAO;AACT,GAZqB;AAcrB,IAAM,YAAY,kCAAY;AAC5B,SAAO,aAAa;AACtB,GAFkB;AAIlB,IAAO,oBAAQ;AAAA,EACb,WAAW,6BAAM,UAAU,EAAE,SAAlB;AAAA,EACX,OAAAA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACjIA,IAAM,YAAY,wBAAC,YACjB;AAAA,mBACiB,QAAQ,UAAU;AAAA,aACxB,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAOhB,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA,YAInB,QAAQ,SAAS;AAAA,mBACV,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAOxB,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA,MAIxB,QAAQ,YAAY,SAAS,QAAQ,SAAS,KAAK,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAS5D,QAAQ,OAAO;AAAA,cACb,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAYpB,QAAQ,cAAc;AAAA;AAAA;AAAA;AAAA,cAIpB,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,cAKjB,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,wBAKP,QAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAWvC,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAQX,QAAQ,UAAU;AAAA;AAAA,kBAEnB,QAAQ,aAAa;AAAA,wBACf,QAAQ,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOjC,QAAQ,YAAY,SAAS,QAAQ,SAAS,KAAK,EAAE;AAAA;AAAA;AAAA,MAGrD,QAAQ,YAAY,SAAS,QAAQ,SAAS,KAAK,EAAE;AAAA;AAAA;AAAA,MAGrD,QAAQ,YAAY,SAAS,QAAQ,SAAS,KAAK,EAAE;AAAA;AAAA;AAAA,MAGrD,QAAQ,YAAY,SAAS,QAAQ,SAAS,KAAK,EAAE;AAAA;AAAA;AAAA,MAGrD,QAAQ,YAAY,SAAS,QAAQ,SAAS,KAAK,EAAE;AAAA;AAAA;AAAA,MAGrD,QAAQ,YAAY,SAAS,QAAQ,SAAS,KAAK,EAAE;AAAA;AAAA;AAAA,MAGrD,QAAQ,YAAY,SAAS,QAAQ,SAAS,KAAK,EAAE;AAAA;AAAA;AAAA,MAGrD,QAAQ,YAAY,SAAS,QAAQ,SAAS,KAAK,EAAE;AAAA;AAAA;AAAA;AAAA,MAIrD,QAAQ,SAAS,SAAS,QAAQ,MAAM,KAAK,EAAE;AAAA;AAAA;AAAA,MAG/C,QAAQ,SAAS,SAAS,QAAQ,MAAM,KAAK,EAAE;AAAA;AAAA;AAAA,MAG/C,QAAQ,SAAS,SAAS,QAAQ,MAAM,KAAK,EAAE;AAAA;AAAA;AAAA,MAG/C,QAAQ,SAAS,SAAS,QAAQ,MAAM,KAAK,EAAE;AAAA;AAAA;AAAA,MAG/C,QAAQ,SAAS,SAAS,QAAQ,MAAM,KAAK,EAAE;AAAA;AAAA;AAAA,MAG/C,QAAQ,SAAS,SAAS,QAAQ,MAAM,KAAK,EAAE;AAAA;AAAA,GAnInC;AAuIlB,IAAO,iBAAQ;;;ACtIf,SAAS,cAAc;;;ACDvB,SAAS,OAAO,aAAa;AAGtB,IAAMC,YAAW,gCAAU,MAAM,UAAU;AAChD,SAAqB,SAAS,MAAM,QAAQ;AAC9C,GAFwB;AAIjB,IAAM,WAAW,gCAAU,SAAS,UAAU;AACnD,QAAM,SAAS;AACf,QAAM,gBAAgB,QACnB,OAAO,QAAQ,EACf,KAAK,MAAM,SAAS,EAAE,EACtB,KAAK,MAAM,SAAS,EAAE,EACtB,KAAK,SAAS,MAAM,EACpB,KAAK,KAAK,MAAM,EAChB,KAAK,gBAAgB,CAAC,EACtB,KAAK,YAAY,SAAS;AAE7B,QAAM,OAAO,QAAQ,OAAO,GAAG;AAG/B,OACG,OAAO,QAAQ,EACf,KAAK,MAAM,SAAS,KAAK,SAAS,CAAC,EACnC,KAAK,MAAM,SAAS,KAAK,SAAS,CAAC,EACnC,KAAK,KAAK,GAAG,EACb,KAAK,gBAAgB,CAAC,EACtB,KAAK,QAAQ,MAAM,EACnB,KAAK,UAAU,MAAM;AAGxB,OACG,OAAO,QAAQ,EACf,KAAK,MAAM,SAAS,KAAK,SAAS,CAAC,EACnC,KAAK,MAAM,SAAS,KAAK,SAAS,CAAC,EACnC,KAAK,KAAK,GAAG,EACb,KAAK,gBAAgB,CAAC,EACtB,KAAK,QAAQ,MAAM,EACnB,KAAK,UAAU,MAAM;AAGxB,WAAS,MAAMC,OAAM;AACnB,UAAM,MAAM,MAAM,EACf,WAAW,KAAK,KAAK,CAAC,EACtB,SAAS,KAAK,KAAK,KAAK,EAAE,EAC1B,YAAY,SAAS,CAAC,EACtB,YAAY,SAAS,GAAG;AAE3B,IAAAA,MACG,OAAO,MAAM,EACb,KAAK,SAAS,OAAO,EACrB,KAAK,KAAK,GAAG,EACb,KAAK,aAAa,eAAe,SAAS,KAAK,OAAO,SAAS,KAAK,KAAK,GAAG;AAAA,EACjF;AAZS;AAeT,WAAS,IAAIA,OAAM;AACjB,UAAM,MAAM,MAAM,EACf,WAAY,IAAI,KAAK,KAAM,CAAC,EAC5B,SAAS,KAAK,KAAK,KAAK,EAAE,EAC1B,YAAY,SAAS,CAAC,EACtB,YAAY,SAAS,GAAG;AAE3B,IAAAA,MACG,OAAO,MAAM,EACb,KAAK,SAAS,OAAO,EACrB,KAAK,KAAK,GAAG,EACb,KAAK,aAAa,eAAe,SAAS,KAAK,OAAO,SAAS,KAAK,KAAK,GAAG;AAAA,EACjF;AAZS;AAeT,WAAS,WAAWA,OAAM;AACxB,IAAAA,MACG,OAAO,MAAM,EACb,KAAK,SAAS,OAAO,EACrB,KAAK,UAAU,CAAC,EAChB,KAAK,MAAM,SAAS,KAAK,CAAC,EAC1B,KAAK,MAAM,SAAS,KAAK,CAAC,EAC1B,KAAK,MAAM,SAAS,KAAK,CAAC,EAC1B,KAAK,MAAM,SAAS,KAAK,CAAC,EAC1B,KAAK,SAAS,OAAO,EACrB,KAAK,gBAAgB,KAAK,EAC1B,KAAK,UAAU,MAAM;AAAA,EAC1B;AAZS;AAcT,MAAI,SAAS,QAAQ,GAAG;AACtB,UAAM,IAAI;AAAA,EACZ,WAAW,SAAS,QAAQ,GAAG;AAC7B,QAAI,IAAI;AAAA,EACV,OAAO;AACL,eAAW,IAAI;AAAA,EACjB;AAEA,SAAO;AACT,GAvFwB;AAyFjB,IAAM,aAAa,gCAAU,SAAS,YAAY;AACvD,QAAM,gBAAgB,QAAQ,OAAO,QAAQ;AAC7C,gBAAc,KAAK,MAAM,WAAW,EAAE;AACtC,gBAAc,KAAK,MAAM,WAAW,EAAE;AACtC,gBAAc,KAAK,SAAS,WAAW,WAAW,GAAG;AACrD,gBAAc,KAAK,QAAQ,WAAW,IAAI;AAC1C,gBAAc,KAAK,UAAU,WAAW,MAAM;AAC9C,gBAAc,KAAK,KAAK,WAAW,CAAC;AAEpC,MAAI,cAAc,UAAU,QAAW;AACrC,kBAAc,KAAK,SAAS,cAAc,KAAK;AAAA,EACjD;AAEA,MAAI,WAAW,UAAU,QAAW;AAClC,kBAAc,OAAO,OAAO,EAAE,KAAK,WAAW,KAAK;AAAA,EACrD;AAEA,SAAO;AACT,GAlB0B;AAoBnB,IAAMC,YAAW,gCAAU,MAAM,UAAU;AAChD,SAAqB,SAAS,MAAM,QAAQ;AAC9C,GAFwB;AAIjB,IAAM,YAAY,gCAAU,MAAM,WAAW;AAQlD,WAAS,UAAU,GAAG,GAAG,OAAO,QAAQ,KAAK;AAC3C,WACE,IACA,MACA,IACA,OACC,IAAI,SACL,MACA,IACA,OACC,IAAI,SACL,OACC,IAAI,SAAS,OACd,OACC,IAAI,QAAQ,MAAM,OACnB,OACC,IAAI,UACL,MACA,IACA,OACC,IAAI;AAAA,EAET;AAtBS;AAuBT,QAAM,UAAU,KAAK,OAAO,SAAS;AACrC,UAAQ,KAAK,UAAU,UAAU,UAAU,GAAG,UAAU,GAAG,IAAI,IAAI,CAAC,CAAC;AACrE,UAAQ,KAAK,SAAS,UAAU;AAEhC,YAAU,IAAI,UAAU,IAAI,UAAU;AACtC,YAAU,IAAI,UAAU,IAAI,MAAM,UAAU;AAC5C,EAAAA,UAAS,MAAM,SAAS;AAC1B,GAtCyB;AAwClB,IAAM,cAAc,gCAAU,MAAM,SAASC,OAAM;AACxD,QAAM,IAAI,KAAK,OAAO,GAAG;AAEzB,QAAM,OAAqB,YAAY;AACvC,OAAK,IAAI,QAAQ;AACjB,OAAK,IAAI,QAAQ;AACjB,OAAK,OAAO,QAAQ;AAEpB,OAAK,QACHA,MAAK,QAAQ,QAAQ;AAAA,EACrBA,MAAK,kBAAkB,QAAQ,YAAY;AAC7C,OAAK,SAASA,MAAK;AACnB,OAAK,QAAQ,kCAAkC,QAAQ;AACvD,OAAK,KAAK;AACV,OAAK,KAAK;AACV,EAAAH,UAAS,GAAG,IAAI;AAEhB,yBAAuBG,KAAI;AAAA,IACzB,QAAQ;AAAA,IACR;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,EAAE,OAAO,kCAAkC,QAAQ,IAAI;AAAA,IACvDA;AAAA,IACA,QAAQ;AAAA,EACV;AACF,GA5B2B;AA8B3B,IAAI,YAAY;AAQT,IAAM,WAAW,gCAAU,MAAM,MAAMA,OAAM;AAClD,QAAM,SAAS,KAAK,IAAIA,MAAK,QAAQ;AACrC,QAAM,IAAI,KAAK,OAAO,GAAG;AACzB;AACA,QAAM,YAAY,MAAM,IAAI;AAC5B,IAAE,OAAO,MAAM,EACZ,KAAK,MAAM,SAAS,SAAS,EAC7B,KAAK,MAAM,MAAM,EACjB,KAAK,MAAM,KAAK,CAAC,EACjB,KAAK,MAAM,MAAM,EACjB,KAAK,MAAM,SAAS,EACpB,KAAK,SAAS,WAAW,EACzB,KAAK,gBAAgB,KAAK,EAC1B,KAAK,oBAAoB,KAAK,EAC9B,KAAK,UAAU,MAAM;AAExB,WAAS,GAAG;AAAA,IACV,IAAI;AAAA,IACJ,IAAI,OAAO,IAAI,KAAK,SAAS;AAAA,IAC7B,OAAO,KAAK;AAAA,EACd,CAAC;AAED,QAAM,OAAqB,YAAY;AACvC,OAAK,IAAI,KAAK;AACd,OAAK,IAAI,KAAK;AACd,OAAK,OAAO,KAAK;AACjB,OAAK,QAAQA,MAAK;AAClB,OAAK,SAASA,MAAK;AACnB,OAAK,QAAQ,oBAAoB,KAAK;AACtC,OAAK,KAAK;AACV,OAAK,KAAK;AACV,EAAAH,UAAS,GAAG,IAAI;AAEhB,MAAI,OAAO,KAAK,IAAI;AACpB,OAAK,OAAO,QAAQ,CAAC,WAAW;AAC9B,UAAM,SAAS,KAAK,OAAO,MAAM,EAAE;AAEnC,UAAM,SAAS;AAAA,MACb,IAAI;AAAA,MACJ,IAAI,KAAK;AAAA,MACT,GAAG;AAAA,MACH,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK,KAAK,OAAO,MAAM,EAAE;AAAA,IAC3B;AAEA,eAAW,GAAG,MAAM;AACpB,YAAQ;AAAA,EACV,CAAC;AAED,yBAAuBG,KAAI;AAAA,IACzB,KAAK;AAAA,IACL;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,EAAE,OAAO,OAAO;AAAA,IAChBA;AAAA,IACA,KAAK;AAAA,EACP;AACF,GA9DwB;AAsEjB,IAAMC,sBAAqB,gCAAU,MAAMC,SAAQ;AACxD,EAAc,mBAAmB,MAAMA,OAAM;AAC/C,GAFkC;AAIlC,IAAM,yBAA0B,2BAAY;AAW1C,WAAS,OAAO,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW,QAAQ;AAClE,UAAM,OAAO,EACV,OAAO,MAAM,EACb,KAAK,KAAK,IAAI,QAAQ,CAAC,EACvB,KAAK,KAAK,IAAI,SAAS,IAAI,CAAC,EAC5B,MAAM,cAAc,MAAM,EAC1B,MAAM,eAAe,QAAQ,EAC7B,KAAK,OAAO;AACf,kBAAc,MAAM,SAAS;AAAA,EAC/B;AATS;AAsBT,WAAS,QAAQ,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAWF,OAAM,QAAQ;AACzE,UAAM,EAAE,cAAc,eAAe,IAAIA;AAEzC,UAAM,QAAQ,QAAQ,MAAM,cAAc;AAC1C,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,KAAK,IAAI,eAAgB,gBAAgB,MAAM,SAAS,KAAM;AACpE,YAAM,OAAO,EACV,OAAO,MAAM,EACb,KAAK,KAAK,IAAI,QAAQ,CAAC,EACvB,KAAK,KAAK,CAAC,EACX,KAAK,QAAQ,MAAM,EACnB,MAAM,eAAe,QAAQ,EAC7B,MAAM,aAAa,YAAY,EAC/B,MAAM,eAAe,cAAc;AACtC,WACG,OAAO,OAAO,EACd,KAAK,KAAK,IAAI,QAAQ,CAAC,EACvB,KAAK,MAAM,EAAE,EACb,KAAK,MAAM,CAAC,CAAC;AAEhB,WACG,KAAK,KAAK,IAAI,SAAS,CAAG,EAC1B,KAAK,qBAAqB,SAAS,EACnC,KAAK,sBAAsB,SAAS;AAEvC,oBAAc,MAAM,SAAS;AAAA,IAC/B;AAAA,EACF;AA3BS;AAuCT,WAAS,KAAK,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAWA,OAAM;AAC9D,UAAM,OAAO,EAAE,OAAO,QAAQ;AAC9B,UAAM,IAAI,KACP,OAAO,eAAe,EACtB,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,CAAC,EACX,KAAK,SAAS,KAAK,EACnB,KAAK,UAAU,MAAM,EACrB,KAAK,YAAY,OAAO;AAE3B,UAAM,OAAO,EACV,OAAO,WAAW,EAClB,MAAM,WAAW,OAAO,EACxB,MAAM,UAAU,MAAM,EACtB,MAAM,SAAS,MAAM;AAExB,SACG,OAAO,KAAK,EACZ,KAAK,SAAS,OAAO,EACrB,MAAM,WAAW,YAAY,EAC7B,MAAM,cAAc,QAAQ,EAC5B,MAAM,kBAAkB,QAAQ,EAChC,KAAK,OAAO;AAEf,YAAQ,SAAS,MAAM,GAAG,GAAG,OAAO,QAAQ,WAAWA,KAAI;AAC3D,kBAAc,MAAM,SAAS;AAAA,EAC/B;AA1BS;AAgCT,WAAS,cAAc,QAAQ,mBAAmB;AAChD,eAAW,OAAO,mBAAmB;AACnC,UAAI,OAAO,mBAAmB;AAE5B,eAAO,KAAK,KAAK,kBAAkB,GAAG,CAAC;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAPS;AAST,SAAO,SAAUA,OAAM;AACrB,WAAOA,MAAK,kBAAkB,OAAO,OAAOA,MAAK,kBAAkB,QAAQ,SAAS;AAAA,EACtF;AACF,EAAG;AAEH,IAAM,eAAe,gCAAU,UAAU;AACvC,WACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,WAAW,EACtB,KAAK,QAAQ,CAAC,EACd,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,CAAC,EACrB,KAAK,gBAAgB,CAAC,EACtB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,kBAAkB;AACjC,GAZqB;AAcrB,IAAO,kBAAQ;AAAA,EACb,UAAAH;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAAE;AAAA,EACA;AAAA,EACA;AAAA,EACA,oBAAAE;AAAA,EACA;AACF;;;ADvZO,IAAM,UAAU,gCAAU,KAAK;AACpC,QAAM,OAAO,OAAO,KAAK,GAAG;AAE5B,OAAK,QAAQ,SAAU,KAAK;AAC1B,SAAK,GAAG,IAAI,IAAI,GAAG;AAAA,EACrB,CAAC;AACH,GANuB;AAQvB,IAAM,SAAS,CAAC;AAGhB,SAAS,gBAAgBE,UAAS;AAChC,QAAMC,QAAO,UAAU,EAAE;AAEzB,MAAI,OAAO;AACX,SAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,WAAW;AACtC,UAAM,SAAS,OAAO,MAAM,EAAE;AAE9B,UAAM,aAAa;AAAA,MACjB,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,GAAG;AAAA,MACH,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,KAAK,OAAO,MAAM,EAAE;AAAA,IACtB;AACA,oBAAQ,WAAWD,UAAS,UAAU;AAEtC,UAAM,YAAY;AAAA,MAChB,GAAG;AAAA,MACH,GAAG,OAAO;AAAA,MACV,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAYC,MAAK,gBAAgB;AAAA,IACnC;AACA,oBAAQ,SAASD,UAAS,SAAS;AAEnC,YAAQ;AAAA,EACV,CAAC;AACH;AA5BS;AA8BT,IAAM,OAAO,UAAU,EAAE;AACzB,IAAM,cAAc,KAAK;AAClB,IAAM,OAAO,gCAAU,MAAM,IAAI,SAAS,SAAS;AACxD,QAAMC,QAAO,UAAU,EAAE;AAEzB,QAAM,gBAAgB,UAAU,EAAE;AAElC,MAAI;AACJ,MAAI,kBAAkB,WAAW;AAC/B,qBAAiB,OAAO,OAAO,EAAE;AAAA,EACnC;AACA,QAAM,OACJ,kBAAkB,YACd,OAAO,eAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,IACrD,OAAO,MAAM;AAGnB,SAAO,KAAK;AACZ,QAAMD,WAAU,KAAK,OAAO,MAAM,EAAE;AAEpC,kBAAQ,aAAaA,QAAO;AAE5B,QAAME,SAAQ,QAAQ,GAAG,SAAS;AAClC,QAAM,QAAQ,QAAQ,GAAG,gBAAgB;AAEzC,QAAM,aAAa,QAAQ,GAAG,UAAU;AACxC,aAAW,UAAU,QAAQ;AAC3B,WAAO,OAAO,MAAM;AAAA,EACtB;AACA,MAAI,WAAW;AACf,aAAW,QAAQ,CAAC,cAAc;AAChC,WAAO,SAAS,IAAI;AAAA,MAClB,OAAOD,MAAK,aAAa,WAAWA,MAAK,aAAa,MAAM;AAAA,MAC5D,UAAU;AAAA,IACZ;AACA;AAAA,EACF,CAAC;AAED,kBAAgBD,QAAO;AACvB,SAAO,OAAO,GAAG,GAAG,aAAa,OAAO,KAAK,MAAM,EAAE,SAAS,EAAE;AAChE,YAAUA,UAASE,QAAO,CAAC;AAE3B,QAAM,MAAM,OAAO,UAAU;AAC7B,MAAI,OAAO;AACT,IAAAF,SACG,OAAO,MAAM,EACb,KAAK,KAAK,EACV,KAAK,KAAK,WAAW,EACrB,KAAK,aAAa,KAAK,EACvB,KAAK,eAAe,MAAM,EAC1B,KAAK,KAAK,EAAE;AAAA,EACjB;AAEA,QAAM,SAAS,IAAI,QAAQ,IAAI,SAAS,IAAIC,MAAK;AACjD,QAAM,QAAQ,cAAc,IAAI,QAAQ,IAAIA,MAAK;AAEjD,mBAAiBD,UAAS,QAAQ,OAAOC,MAAK,WAAW;AAGzD,EAAAD,SACG,OAAO,MAAM,EACb,KAAK,MAAM,WAAW,EACtB,KAAK,MAAMC,MAAK,SAAS,CAAC,EAC1B,KAAK,MAAM,QAAQ,cAAc,CAAC,EAClC,KAAK,MAAMA,MAAK,SAAS,CAAC,EAC1B,KAAK,gBAAgB,CAAC,EACtB,KAAK,UAAU,OAAO,EACtB,KAAK,cAAc,iBAAiB;AAEvC,QAAM,oBAAoB,QAAQ,KAAK;AACvC,EAAAD,SAAQ,KAAK,WAAW,GAAG,IAAI,MAAM,QAAQ,KAAK,IAAI,SAAS,iBAAiB,EAAE;AAClF,EAAAA,SAAQ,KAAK,uBAAuB,eAAe;AACnD,EAAAA,SAAQ,KAAK,UAAU,SAAS,oBAAoB,EAAE;AACxD,GAvEoB;AAyEb,IAAM,SAAS;AAAA,EACpB,MAAM;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,EAEb,eAAe,CAAC;AAAA,EAChB,MAAM,kCAAY;AAChB,SAAK,gBAAgB,CAAC;AACtB,SAAK,OAAO;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AACA,SAAK,cAAc;AAAA,EACrB,GATM;AAAA,EAUN,WAAW,gCAAU,KAAK,KAAK,KAAK,KAAK;AACvC,QAAI,IAAI,GAAG,MAAM,QAAW;AAC1B,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,CAAC;AAAA,IAC9B;AAAA,EACF,GANW;AAAA,EAOX,cAAc,gCAAU,QAAQ,QAAQ,OAAO,OAAO;AACpD,UAAMC,QAAO,UAAU,EAAE;AAEzB,UAAM,QAAQ;AACd,QAAI,MAAM;AAEV,aAAS,SAAS,MAAqB;AACrC,aAAO,gCAAS,iBAAiB,MAAM;AACrC;AAEA,cAAM,IAAI,MAAM,cAAc,SAAS,MAAM;AAC7C,cAAM,UAAU,MAAM,UAAU,SAAS,IAAIA,MAAK,WAAW,KAAK,GAAG;AACrE,cAAM,UAAU,MAAM,SAAS,QAAQ,IAAIA,MAAK,WAAW,KAAK,GAAG;AAEnE,cAAM,UAAU,OAAO,MAAM,UAAU,SAAS,IAAIA,MAAK,WAAW,KAAK,GAAG;AAC5E,cAAM,UAAU,OAAO,MAAM,SAAS,QAAQ,IAAIA,MAAK,WAAW,KAAK,GAAG;AAE1E,YAAI,EAAE,SAAS,eAAe;AAC5B,gBAAM,UAAU,MAAM,UAAU,SAAS,IAAIA,MAAK,WAAW,KAAK,GAAG;AACrE,gBAAM,UAAU,MAAM,SAAS,QAAQ,IAAIA,MAAK,WAAW,KAAK,GAAG;AAEnE,gBAAM,UAAU,OAAO,MAAM,UAAU,SAAS,IAAIA,MAAK,WAAW,KAAK,GAAG;AAC5E,gBAAM,UAAU,OAAO,MAAM,SAAS,QAAQ,IAAIA,MAAK,WAAW,KAAK,GAAG;AAAA,QAC5E;AAAA,MACF,GAjBO;AAAA,IAkBT;AAnBS;AAqBT,SAAK,cAAc,QAAQ,SAAS,CAAC;AAAA,EACvC,GA5Bc;AAAA,EA6Bd,QAAQ,gCAAU,QAAQ,QAAQ,OAAO,OAAO;AAC9C,UAAM,UAAU,KAAK,IAAI,QAAQ,KAAK;AACtC,UAAM,SAAS,KAAK,IAAI,QAAQ,KAAK;AACrC,UAAM,UAAU,KAAK,IAAI,QAAQ,KAAK;AACtC,UAAM,SAAS,KAAK,IAAI,QAAQ,KAAK;AAErC,SAAK,UAAU,OAAO,MAAM,UAAU,SAAS,KAAK,GAAG;AACvD,SAAK,UAAU,OAAO,MAAM,UAAU,SAAS,KAAK,GAAG;AACvD,SAAK,UAAU,OAAO,MAAM,SAAS,QAAQ,KAAK,GAAG;AACrD,SAAK,UAAU,OAAO,MAAM,SAAS,QAAQ,KAAK,GAAG;AAErD,SAAK,aAAa,SAAS,SAAS,QAAQ,MAAM;AAAA,EACpD,GAZQ;AAAA,EAaR,iBAAiB,gCAAU,MAAM;AAC/B,SAAK,cAAc,KAAK,cAAc;AACtC,SAAK,KAAK,QAAQ,KAAK;AAAA,EACzB,GAHiB;AAAA,EAIjB,gBAAgB,kCAAY;AAC1B,WAAO,KAAK;AAAA,EACd,GAFgB;AAAA,EAGhB,WAAW,kCAAY;AACrB,WAAO,KAAK;AAAA,EACd,GAFW;AAGb;AAEA,IAAM,QAAQ,KAAK;AACnB,IAAM,cAAc,KAAK;AAElB,IAAM,YAAY,gCAAUD,UAASE,QAAO,aAAa;AAC9D,QAAMD,QAAO,UAAU,EAAE;AACzB,MAAI,cAAc;AAClB,QAAM,iBAAiBA,MAAK,SAAS,IAAIA,MAAK;AAC9C,QAAM,UAAU,cAAc;AAE9B,MAAI,gBAAgB;AACpB,MAAI,OAAO;AACX,MAAI,SAAS;AACb,MAAI,MAAM;AAGV,aAAW,CAAC,GAAG,IAAI,KAAKC,OAAM,QAAQ,GAAG;AACvC,QAAI,gBAAgB,KAAK,SAAS;AAChC,aAAO,MAAM,gBAAgB,MAAM,MAAM;AACzC,YAAM,gBAAgB,MAAM;AAC5B,eAAS,YAAY,gBAAgB,YAAY,MAAM;AAGvD,UAAI,qBAAqB;AACzB,YAAMC,kBAAiB,KAAK;AAC5B,eAAS,YAAY,GAAG,YAAYD,OAAM,QAAQ,aAAa;AAC7D,YAAIA,OAAM,SAAS,EAAE,WAAWC,iBAAgB;AAC9C,+BAAqB,qBAAqB;AAAA,QAC5C,OAAO;AACL;AAAA,QACF;AAAA,MACF;AAEA,YAAM,UAAU;AAAA,QACd,GAAG,IAAIF,MAAK,aAAa,IAAIA,MAAK,QAAQ;AAAA,QAC1C,GAAG;AAAA,QACH,MAAM,KAAK;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW;AAAA,MACb;AAEA,sBAAQ,YAAYD,UAAS,SAASC,KAAI;AAC1C,oBAAc,KAAK;AACnB;AAAA,IACF;AAGA,UAAM,aAAa,KAAK,OAAO,OAAO,CAAC,KAAK,cAAc;AACxD,UAAI,OAAO,SAAS,GAAG;AACrB,YAAI,SAAS,IAAI,OAAO,SAAS;AAAA,MACnC;AAEA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAGL,SAAK,IAAI,IAAIA,MAAK,aAAa,IAAIA,MAAK,QAAQ;AAChD,SAAK,IAAI;AACT,SAAK,QAAQA,MAAK;AAClB,SAAK,SAASA,MAAK;AACnB,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,SAAK,SAAS;AAGd,oBAAQ,SAASD,UAAS,MAAMC,KAAI;AACpC,WAAO,OAAO,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,QAAQA,MAAK,YAAY,MAAM,IAAI,EAAE;AAAA,EACnF;AACF,GAnEyB;AAqEzB,IAAO,0BAAQ;AAAA,EACb;AAAA,EACA;AACF;;;AE/QO,IAAM,UAA6B;AAAA,EACxC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM,wBAAC,QAAQ;AACb,4BAAS,QAAQ,IAAI,OAAO;AAC5B,sBAAG,MAAM;AAAA,EACX,GAHM;AAIR;", "names": ["o", "parser", "lexer", "clear", "drawRect", "face", "drawText", "conf", "drawBackgroundRect", "bounds", "diagram", "conf", "tasks", "currentSection"]}