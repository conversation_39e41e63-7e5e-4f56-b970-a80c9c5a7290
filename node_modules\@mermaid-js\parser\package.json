{"name": "@mermaid-js/parser", "version": "0.4.0", "description": "MermaidJS parser", "author": "Yokozuna59", "contributors": ["Yokozuna59", "<PERSON><PERSON><PERSON> (https://sidharth.dev)"], "homepage": "https://github.com/mermaid-js/mermaid/tree/develop/packages/mermaid/parser/#readme", "types": "dist/src/index.d.ts", "type": "module", "exports": {".": {"import": "./dist/mermaid-parser.core.mjs", "types": "./dist/src/index.d.ts"}}, "repository": {"type": "git", "url": "https://github.com/mermaid-js/mermaid.git", "directory": "packages/parser"}, "license": "MIT", "keywords": ["mermaid", "parser", "ast"], "dependencies": {"langium": "3.3.1"}, "devDependencies": {"chevrotain": "^11.0.3"}, "files": ["dist/"], "publishConfig": {"access": "public"}, "scripts": {"clean": "rimraf dist src/language/generated", "langium:generate": "langium generate", "langium:watch": "langium generate --watch"}}