{"version": 3, "sources": ["../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/util.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/add-border-segments.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/coordinate-system.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/data/list.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/greedy-fas.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/acyclic.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/normalize.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/rank/util.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/rank/feasible-tree.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/graphlib/alg/dijkstra.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/graphlib/alg/floyd-warshall.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/graphlib/alg/topsort.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/graphlib/alg/dfs.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/graphlib/alg/postorder.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/graphlib/alg/preorder.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/rank/network-simplex.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/rank/index.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/nesting-graph.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/add-subgraph-constraints.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/build-layer-graph.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/cross-count.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/init-order.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/barycenter.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/resolve-conflicts.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/sort.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/sort-subgraph.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/order/index.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/parent-dummy-chains.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/position/bk.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/position/index.js", "../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/dagre/layout.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { Graph } from '../graphlib/index.js';\n\nexport {\n  addDummyNode,\n  simplify,\n  asNonCompoundGraph,\n  successorWeights,\n  predecessorWeights,\n  intersectRect,\n  buildLayerMatrix,\n  normalizeRanks,\n  removeEmptyRanks,\n  addBorderNode,\n  maxRank,\n  partition,\n  time,\n  notime,\n};\n\n/*\n * Adds a dummy node to the graph and return v.\n */\nfunction addDummyNode(g, type, attrs, name) {\n  var v;\n  do {\n    v = _.uniqueId(name);\n  } while (g.hasNode(v));\n\n  attrs.dummy = type;\n  g.setNode(v, attrs);\n  return v;\n}\n\n/*\n * Returns a new graph with only simple edges. Handles aggregation of data\n * associated with multi-edges.\n */\nfunction simplify(g) {\n  var simplified = new Graph().setGraph(g.graph());\n  _.forEach(g.nodes(), function (v) {\n    simplified.setNode(v, g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    var simpleLabel = simplified.edge(e.v, e.w) || { weight: 0, minlen: 1 };\n    var label = g.edge(e);\n    simplified.setEdge(e.v, e.w, {\n      weight: simpleLabel.weight + label.weight,\n      minlen: Math.max(simpleLabel.minlen, label.minlen),\n    });\n  });\n  return simplified;\n}\n\nfunction asNonCompoundGraph(g) {\n  var simplified = new Graph({ multigraph: g.isMultigraph() }).setGraph(g.graph());\n  _.forEach(g.nodes(), function (v) {\n    if (!g.children(v).length) {\n      simplified.setNode(v, g.node(v));\n    }\n  });\n  _.forEach(g.edges(), function (e) {\n    simplified.setEdge(e, g.edge(e));\n  });\n  return simplified;\n}\n\nfunction successorWeights(g) {\n  var weightMap = _.map(g.nodes(), function (v) {\n    var sucs = {};\n    _.forEach(g.outEdges(v), function (e) {\n      sucs[e.w] = (sucs[e.w] || 0) + g.edge(e).weight;\n    });\n    return sucs;\n  });\n  return _.zipObject(g.nodes(), weightMap);\n}\n\nfunction predecessorWeights(g) {\n  var weightMap = _.map(g.nodes(), function (v) {\n    var preds = {};\n    _.forEach(g.inEdges(v), function (e) {\n      preds[e.v] = (preds[e.v] || 0) + g.edge(e).weight;\n    });\n    return preds;\n  });\n  return _.zipObject(g.nodes(), weightMap);\n}\n\n/*\n * Finds where a line starting at point ({x, y}) would intersect a rectangle\n * ({x, y, width, height}) if it were pointing at the rectangle's center.\n */\nfunction intersectRect(rect, point) {\n  var x = rect.x;\n  var y = rect.y;\n\n  // Rectangle intersection algorithm from:\n  // http://math.stackexchange.com/questions/108113/find-edge-between-two-boxes\n  var dx = point.x - x;\n  var dy = point.y - y;\n  var w = rect.width / 2;\n  var h = rect.height / 2;\n\n  if (!dx && !dy) {\n    throw new Error('Not possible to find intersection inside of the rectangle');\n  }\n\n  var sx, sy;\n  if (Math.abs(dy) * w > Math.abs(dx) * h) {\n    // Intersection is top or bottom of rect.\n    if (dy < 0) {\n      h = -h;\n    }\n    sx = (h * dx) / dy;\n    sy = h;\n  } else {\n    // Intersection is left or right of rect.\n    if (dx < 0) {\n      w = -w;\n    }\n    sx = w;\n    sy = (w * dy) / dx;\n  }\n\n  return { x: x + sx, y: y + sy };\n}\n\n/*\n * Given a DAG with each node assigned \"rank\" and \"order\" properties, this\n * function will produce a matrix with the ids of each node.\n */\nfunction buildLayerMatrix(g) {\n  var layering = _.map(_.range(maxRank(g) + 1), function () {\n    return [];\n  });\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    var rank = node.rank;\n    if (!_.isUndefined(rank)) {\n      layering[rank][node.order] = v;\n    }\n  });\n  return layering;\n}\n\n/*\n * Adjusts the ranks for all nodes in the graph such that all nodes v have\n * rank(v) >= 0 and at least one node w has rank(w) = 0.\n */\nfunction normalizeRanks(g) {\n  var min = _.min(\n    _.map(g.nodes(), function (v) {\n      return g.node(v).rank;\n    }),\n  );\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (_.has(node, 'rank')) {\n      node.rank -= min;\n    }\n  });\n}\n\nfunction removeEmptyRanks(g) {\n  // Ranks may not start at 0, so we need to offset them\n  var offset = _.min(\n    _.map(g.nodes(), function (v) {\n      return g.node(v).rank;\n    }),\n  );\n\n  var layers = [];\n  _.forEach(g.nodes(), function (v) {\n    var rank = g.node(v).rank - offset;\n    if (!layers[rank]) {\n      layers[rank] = [];\n    }\n    layers[rank].push(v);\n  });\n\n  var delta = 0;\n  var nodeRankFactor = g.graph().nodeRankFactor;\n  _.forEach(layers, function (vs, i) {\n    if (_.isUndefined(vs) && i % nodeRankFactor !== 0) {\n      --delta;\n    } else if (delta) {\n      _.forEach(vs, function (v) {\n        g.node(v).rank += delta;\n      });\n    }\n  });\n}\n\nfunction addBorderNode(g, prefix, rank, order) {\n  var node = {\n    width: 0,\n    height: 0,\n  };\n  if (arguments.length >= 4) {\n    node.rank = rank;\n    node.order = order;\n  }\n  return addDummyNode(g, 'border', node, prefix);\n}\n\nfunction maxRank(g) {\n  return _.max(\n    _.map(g.nodes(), function (v) {\n      var rank = g.node(v).rank;\n      if (!_.isUndefined(rank)) {\n        return rank;\n      }\n    }),\n  );\n}\n\n/*\n * Partition a collection into two groups: `lhs` and `rhs`. If the supplied\n * function returns true for an entry it goes into `lhs`. Otherwise it goes\n * into `rhs.\n */\nfunction partition(collection, fn) {\n  var result = { lhs: [], rhs: [] };\n  _.forEach(collection, function (value) {\n    if (fn(value)) {\n      result.lhs.push(value);\n    } else {\n      result.rhs.push(value);\n    }\n  });\n  return result;\n}\n\n/*\n * Returns a new function that wraps `fn` with a timer. The wrapper logs the\n * time it takes to execute the function.\n */\nfunction time(name, fn) {\n  var start = _.now();\n  try {\n    return fn();\n  } finally {\n    console.log(name + ' time: ' + (_.now() - start) + 'ms');\n  }\n}\n\nfunction notime(name, fn) {\n  return fn();\n}\n", "import * as _ from 'lodash-es';\nimport * as util from './util.js';\n\nexport { addBorderSegments };\n\nfunction addBorderSegments(g) {\n  function dfs(v) {\n    var children = g.children(v);\n    var node = g.node(v);\n    if (children.length) {\n      _.forEach(children, dfs);\n    }\n\n    if (Object.prototype.hasOwnProperty.call(node, 'minRank')) {\n      node.borderLeft = [];\n      node.borderRight = [];\n      for (var rank = node.minRank, maxRank = node.maxRank + 1; rank < maxRank; ++rank) {\n        addBorderNode(g, 'borderLeft', '_bl', v, node, rank);\n        addBorderNode(g, 'borderRight', '_br', v, node, rank);\n      }\n    }\n  }\n\n  _.forEach(g.children(), dfs);\n}\n\nfunction addBorderNode(g, prop, prefix, sg, sgNode, rank) {\n  var label = { width: 0, height: 0, rank: rank, borderType: prop };\n  var prev = sgNode[prop][rank - 1];\n  var curr = util.addDummyNode(g, 'border', label, prefix);\n  sgNode[prop][rank] = curr;\n  g.setParent(curr, sg);\n  if (prev) {\n    g.setEdge(prev, curr, { weight: 1 });\n  }\n}\n", "import * as _ from 'lodash-es';\n\nexport { adjust, undo };\n\nfunction adjust(g) {\n  var rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === 'lr' || rankDir === 'rl') {\n    swapWidthHeight(g);\n  }\n}\n\nfunction undo(g) {\n  var rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === 'bt' || rankDir === 'rl') {\n    reverseY(g);\n  }\n\n  if (rankDir === 'lr' || rankDir === 'rl') {\n    swapXY(g);\n    swapWidthHeight(g);\n  }\n}\n\nfunction swapWidthHeight(g) {\n  _.forEach(g.nodes(), function (v) {\n    swapWidthHeightOne(g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    swapWidthHeightOne(g.edge(e));\n  });\n}\n\nfunction swapWidthHeightOne(attrs) {\n  var w = attrs.width;\n  attrs.width = attrs.height;\n  attrs.height = w;\n}\n\nfunction reverseY(g) {\n  _.forEach(g.nodes(), function (v) {\n    reverseYOne(g.node(v));\n  });\n\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, reverseYOne);\n    if (Object.prototype.hasOwnProperty.call(edge, 'y')) {\n      reverseYOne(edge);\n    }\n  });\n}\n\nfunction reverseYOne(attrs) {\n  attrs.y = -attrs.y;\n}\n\nfunction swapXY(g) {\n  _.forEach(g.nodes(), function (v) {\n    swapXYOne(g.node(v));\n  });\n\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, swapXYOne);\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      swapXYOne(edge);\n    }\n  });\n}\n\nfunction swapXYOne(attrs) {\n  var x = attrs.x;\n  attrs.x = attrs.y;\n  attrs.y = x;\n}\n", "/*\n * Simple doubly linked list implementation derived from <PERSON><PERSON><PERSON>, et al.,\n * \"Introduction to Algorithms\".\n */\n\nexport { List };\n\nclass List {\n  constructor() {\n    var sentinel = {};\n    sentinel._next = sentinel._prev = sentinel;\n    this._sentinel = sentinel;\n  }\n  dequeue() {\n    var sentinel = this._sentinel;\n    var entry = sentinel._prev;\n    if (entry !== sentinel) {\n      unlink(entry);\n      return entry;\n    }\n  }\n  enqueue(entry) {\n    var sentinel = this._sentinel;\n    if (entry._prev && entry._next) {\n      unlink(entry);\n    }\n    entry._next = sentinel._next;\n    sentinel._next._prev = entry;\n    sentinel._next = entry;\n    entry._prev = sentinel;\n  }\n  toString() {\n    var strs = [];\n    var sentinel = this._sentinel;\n    var curr = sentinel._prev;\n    while (curr !== sentinel) {\n      strs.push(JSON.stringify(curr, filterOutLinks));\n      curr = curr._prev;\n    }\n    return '[' + strs.join(', ') + ']';\n  }\n}\n\nfunction unlink(entry) {\n  entry._prev._next = entry._next;\n  entry._next._prev = entry._prev;\n  delete entry._next;\n  delete entry._prev;\n}\n\nfunction filterOutLinks(k, v) {\n  if (k !== '_next' && k !== '_prev') {\n    return v;\n  }\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../graphlib/index.js';\nimport { List } from './data/list.js';\n\n/*\n * A greedy heuristic for finding a feedback arc set for a graph. A feedback\n * arc set is a set of edges that can be removed to make a graph acyclic.\n * The algorithm comes from: <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> <PERSON><PERSON>, \"A fast and\n * effective heuristic for the feedback arc set problem.\" This implementation\n * adjusts that from the paper to allow for weighted edges.\n */\nexport { greedyFAS };\n\nvar DEFAULT_WEIGHT_FN = _.constant(1);\n\nfunction greedyFAS(g, weightFn) {\n  if (g.nodeCount() <= 1) {\n    return [];\n  }\n  var state = buildState(g, weightFn || DEFAULT_WEIGHT_FN);\n  var results = doGreedyFAS(state.graph, state.buckets, state.zeroIdx);\n\n  // Expand multi-edges\n  return _.flatten(\n    _.map(results, function (e) {\n      return g.outEdges(e.v, e.w);\n    }),\n  );\n}\n\nfunction doGreedyFAS(g, buckets, zeroIdx) {\n  var results = [];\n  var sources = buckets[buckets.length - 1];\n  var sinks = buckets[0];\n\n  var entry;\n  while (g.nodeCount()) {\n    while ((entry = sinks.dequeue())) {\n      removeNode(g, buckets, zeroIdx, entry);\n    }\n    while ((entry = sources.dequeue())) {\n      removeNode(g, buckets, zeroIdx, entry);\n    }\n    if (g.nodeCount()) {\n      for (var i = buckets.length - 2; i > 0; --i) {\n        entry = buckets[i].dequeue();\n        if (entry) {\n          results = results.concat(removeNode(g, buckets, zeroIdx, entry, true));\n          break;\n        }\n      }\n    }\n  }\n\n  return results;\n}\n\nfunction removeNode(g, buckets, zeroIdx, entry, collectPredecessors) {\n  var results = collectPredecessors ? [] : undefined;\n\n  _.forEach(g.inEdges(entry.v), function (edge) {\n    var weight = g.edge(edge);\n    var uEntry = g.node(edge.v);\n\n    if (collectPredecessors) {\n      results.push({ v: edge.v, w: edge.w });\n    }\n\n    uEntry.out -= weight;\n    assignBucket(buckets, zeroIdx, uEntry);\n  });\n\n  _.forEach(g.outEdges(entry.v), function (edge) {\n    var weight = g.edge(edge);\n    var w = edge.w;\n    var wEntry = g.node(w);\n    wEntry['in'] -= weight;\n    assignBucket(buckets, zeroIdx, wEntry);\n  });\n\n  g.removeNode(entry.v);\n\n  return results;\n}\n\nfunction buildState(g, weightFn) {\n  var fasGraph = new Graph();\n  var maxIn = 0;\n  var maxOut = 0;\n\n  _.forEach(g.nodes(), function (v) {\n    fasGraph.setNode(v, { v: v, in: 0, out: 0 });\n  });\n\n  // Aggregate weights on nodes, but also sum the weights across multi-edges\n  // into a single edge for the fasGraph.\n  _.forEach(g.edges(), function (e) {\n    var prevWeight = fasGraph.edge(e.v, e.w) || 0;\n    var weight = weightFn(e);\n    var edgeWeight = prevWeight + weight;\n    fasGraph.setEdge(e.v, e.w, edgeWeight);\n    maxOut = Math.max(maxOut, (fasGraph.node(e.v).out += weight));\n    maxIn = Math.max(maxIn, (fasGraph.node(e.w)['in'] += weight));\n  });\n\n  var buckets = _.range(maxOut + maxIn + 3).map(function () {\n    return new List();\n  });\n  var zeroIdx = maxIn + 1;\n\n  _.forEach(fasGraph.nodes(), function (v) {\n    assignBucket(buckets, zeroIdx, fasGraph.node(v));\n  });\n\n  return { graph: fasGraph, buckets: buckets, zeroIdx: zeroIdx };\n}\n\nfunction assignBucket(buckets, zeroIdx, entry) {\n  if (!entry.out) {\n    buckets[0].enqueue(entry);\n  } else if (!entry['in']) {\n    buckets[buckets.length - 1].enqueue(entry);\n  } else {\n    buckets[entry.out - entry['in'] + zeroIdx].enqueue(entry);\n  }\n}\n", "import * as _ from 'lodash-es';\nimport { greedyFAS } from './greedy-fas.js';\n\nexport { run, undo };\n\nfunction run(g) {\n  var fas = g.graph().acyclicer === 'greedy' ? greedyFAS(g, weightFn(g)) : dfsFAS(g);\n  _.forEach(fas, function (e) {\n    var label = g.edge(e);\n    g.removeEdge(e);\n    label.forwardName = e.name;\n    label.reversed = true;\n    g.setEdge(e.w, e.v, label, _.uniqueId('rev'));\n  });\n\n  function weightFn(g) {\n    return function (e) {\n      return g.edge(e).weight;\n    };\n  }\n}\n\nfunction dfsFAS(g) {\n  var fas = [];\n  var stack = {};\n  var visited = {};\n\n  function dfs(v) {\n    if (Object.prototype.hasOwnProperty.call(visited, v)) {\n      return;\n    }\n    visited[v] = true;\n    stack[v] = true;\n    _.forEach(g.outEdges(v), function (e) {\n      if (Object.prototype.hasOwnProperty.call(stack, e.w)) {\n        fas.push(e);\n      } else {\n        dfs(e.w);\n      }\n    });\n    delete stack[v];\n  }\n\n  _.forEach(g.nodes(), dfs);\n  return fas;\n}\n\nfunction undo(g) {\n  _.forEach(g.edges(), function (e) {\n    var label = g.edge(e);\n    if (label.reversed) {\n      g.removeEdge(e);\n\n      var forwardName = label.forwardName;\n      delete label.reversed;\n      delete label.forwardName;\n      g.setEdge(e.w, e.v, label, forwardName);\n    }\n  });\n}\n", "/**\n * TypeScript type imports:\n *\n * @import { Graph } from '../graphlib/graph.js';\n */\nimport * as _ from 'lodash-es';\nimport * as util from './util.js';\n\nexport { run, undo };\n\n/*\n * Breaks any long edges in the graph into short segments that span 1 layer\n * each. This operation is undoable with the denormalize function.\n *\n * Pre-conditions:\n *\n *    1. The input graph is a DAG.\n *    2. Each node in the graph has a \"rank\" property.\n *\n * Post-condition:\n *\n *    1. All edges in the graph have a length of 1.\n *    2. Dummy nodes are added where edges have been split into segments.\n *    3. The graph is augmented with a \"dummyChains\" attribute which contains\n *       the first dummy in each chain of dummy nodes produced.\n */\nfunction run(g) {\n  g.graph().dummyChains = [];\n  _.forEach(g.edges(), function (edge) {\n    normalizeEdge(g, edge);\n  });\n}\n\n/**\n * @param {Graph} g\n */\nfunction normalizeEdge(g, e) {\n  var v = e.v;\n  var vRank = g.node(v).rank;\n  var w = e.w;\n  var wRank = g.node(w).rank;\n  var name = e.name;\n  var edgeLabel = g.edge(e);\n  var labelRank = edgeLabel.labelRank;\n\n  if (wRank === vRank + 1) return;\n\n  g.removeEdge(e);\n\n  /**\n   * @typedef {Object} Attrs\n   * @property {number} width\n   * @property {number} height\n   * @property {ReturnType<Graph[\"node\"]>} edgeLabel\n   * @property {any} edgeObj\n   * @property {ReturnType<Graph[\"node\"]>[\"rank\"]} rank\n   * @property {string} [dummy]\n   * @property {ReturnType<Graph[\"node\"]>[\"labelpos\"]} [labelpos]\n   */\n\n  /** @type {Attrs | undefined} */\n  var attrs = undefined;\n  var dummy, i;\n  for (i = 0, ++vRank; vRank < wRank; ++i, ++vRank) {\n    edgeLabel.points = [];\n    attrs = {\n      width: 0,\n      height: 0,\n      edgeLabel: edgeLabel,\n      edgeObj: e,\n      rank: vRank,\n    };\n    dummy = util.addDummyNode(g, 'edge', attrs, '_d');\n    if (vRank === labelRank) {\n      attrs.width = edgeLabel.width;\n      attrs.height = edgeLabel.height;\n      attrs.dummy = 'edge-label';\n      attrs.labelpos = edgeLabel.labelpos;\n    }\n    g.setEdge(v, dummy, { weight: edgeLabel.weight }, name);\n    if (i === 0) {\n      g.graph().dummyChains.push(dummy);\n    }\n    v = dummy;\n  }\n\n  g.setEdge(v, w, { weight: edgeLabel.weight }, name);\n}\n\nfunction undo(g) {\n  _.forEach(g.graph().dummyChains, function (v) {\n    var node = g.node(v);\n    var origLabel = node.edgeLabel;\n    var w;\n    g.setEdge(node.edgeObj, origLabel);\n    while (node.dummy) {\n      w = g.successors(v)[0];\n      g.removeNode(v);\n      origLabel.points.push({ x: node.x, y: node.y });\n      if (node.dummy === 'edge-label') {\n        origLabel.x = node.x;\n        origLabel.y = node.y;\n        origLabel.width = node.width;\n        origLabel.height = node.height;\n      }\n      v = w;\n      node = g.node(v);\n    }\n  });\n}\n", "import * as _ from 'lodash-es';\n\nexport { longestPath, slack };\n\n/*\n * Initializes ranks for the input graph using the longest path algorithm. This\n * algorithm scales well and is fast in practice, it yields rather poor\n * solutions. Nodes are pushed to the lowest layer possible, leaving the bottom\n * ranks wide and leaving edges longer than necessary. However, due to its\n * speed, this algorithm is good for getting an initial ranking that can be fed\n * into other algorithms.\n *\n * This algorithm does not normalize layers because it will be used by other\n * algorithms in most cases. If using this algorithm directly, be sure to\n * run normalize at the end.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG.\n *    2. Input graph node labels can be assigned properties.\n *\n * Post-conditions:\n *\n *    1. Each node will be assign an (unnormalized) \"rank\" property.\n */\nfunction longestPath(g) {\n  var visited = {};\n\n  function dfs(v) {\n    var label = g.node(v);\n    if (Object.prototype.hasOwnProperty.call(visited, v)) {\n      return label.rank;\n    }\n    visited[v] = true;\n\n    var rank = _.min(\n      _.map(g.outEdges(v), function (e) {\n        return dfs(e.w) - g.edge(e).minlen;\n      }),\n    );\n\n    if (\n      rank === Number.POSITIVE_INFINITY || // return value of _.map([]) for Lodash 3\n      rank === undefined || // return value of _.map([]) for Lodash 4\n      rank === null\n    ) {\n      // return value of _.map([null])\n      rank = 0;\n    }\n\n    return (label.rank = rank);\n  }\n\n  _.forEach(g.sources(), dfs);\n}\n\n/*\n * Returns the amount of slack for the given edge. The slack is defined as the\n * difference between the length of the edge and its minimum length.\n */\nfunction slack(g, e) {\n  return g.node(e.w).rank - g.node(e.v).rank - g.edge(e).minlen;\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nimport { slack } from './util.js';\n\nexport { feasibleTree };\n\n/*\n * Constructs a spanning tree with tight edges and adjusted the input node's\n * ranks to achieve this. A tight edge is one that is has a length that matches\n * its \"minlen\" attribute.\n *\n * The basic structure for this function is derived from <PERSON><PERSON><PERSON>, et al., \"A\n * Technique for Drawing Directed Graphs.\"\n *\n * Pre-conditions:\n *\n *    1. Graph must be a DAG.\n *    2. Graph must be connected.\n *    3. Graph must have at least one node.\n *    5. Graph nodes must have been previously assigned a \"rank\" property that\n *       respects the \"minlen\" property of incident edges.\n *    6. Graph edges must have a \"minlen\" property.\n *\n * Post-conditions:\n *\n *    - Graph nodes will have their rank adjusted to ensure that all edges are\n *      tight.\n *\n * Returns a tree (undirected graph) that is constructed using only \"tight\"\n * edges.\n */\nfunction feasibleTree(g) {\n  var t = new Graph({ directed: false });\n\n  // Choose arbitrary node from which to start our tree\n  var start = g.nodes()[0];\n  var size = g.nodeCount();\n  t.setNode(start, {});\n\n  var edge, delta;\n  while (tightTree(t, g) < size) {\n    edge = findMinSlackEdge(t, g);\n    delta = t.hasNode(edge.v) ? slack(g, edge) : -slack(g, edge);\n    shiftRanks(t, g, delta);\n  }\n\n  return t;\n}\n\n/*\n * Finds a maximal tree of tight edges and returns the number of nodes in the\n * tree.\n */\nfunction tightTree(t, g) {\n  function dfs(v) {\n    _.forEach(g.nodeEdges(v), function (e) {\n      var edgeV = e.v,\n        w = v === edgeV ? e.w : edgeV;\n      if (!t.hasNode(w) && !slack(g, e)) {\n        t.setNode(w, {});\n        t.setEdge(v, w, {});\n        dfs(w);\n      }\n    });\n  }\n\n  _.forEach(t.nodes(), dfs);\n  return t.nodeCount();\n}\n\n/*\n * Finds the edge with the smallest slack that is incident on tree and returns\n * it.\n */\nfunction findMinSlackEdge(t, g) {\n  return _.minBy(g.edges(), function (e) {\n    if (t.hasNode(e.v) !== t.hasNode(e.w)) {\n      return slack(g, e);\n    }\n  });\n}\n\nfunction shiftRanks(t, g, delta) {\n  _.forEach(t.nodes(), function (v) {\n    g.node(v).rank += delta;\n  });\n}\n", "import * as _ from 'lodash-es';\nimport { PriorityQueue } from '../data/priority-queue.js';\n\nexport { dijkstra };\n\nvar DEFAULT_WEIGHT_FUNC = _.constant(1);\n\nfunction dijkstra(g, source, weightFn, edgeFn) {\n  return runDijkstra(\n    g,\n    String(source),\n    weightFn || DEFAULT_WEIGHT_FUNC,\n    edgeFn ||\n      function (v) {\n        return g.outEdges(v);\n      },\n  );\n}\n\nfunction runDijkstra(g, source, weightFn, edgeFn) {\n  var results = {};\n  var pq = new PriorityQueue();\n  var v, vEntry;\n\n  var updateNeighbors = function (edge) {\n    var w = edge.v !== v ? edge.v : edge.w;\n    var wEntry = results[w];\n    var weight = weightFn(edge);\n    var distance = vEntry.distance + weight;\n\n    if (weight < 0) {\n      throw new Error(\n        'dijkstra does not allow negative edge weights. ' +\n          'Bad edge: ' +\n          edge +\n          ' Weight: ' +\n          weight,\n      );\n    }\n\n    if (distance < wEntry.distance) {\n      wEntry.distance = distance;\n      wEntry.predecessor = v;\n      pq.decrease(w, distance);\n    }\n  };\n\n  g.nodes().forEach(function (v) {\n    var distance = v === source ? 0 : Number.POSITIVE_INFINITY;\n    results[v] = { distance: distance };\n    pq.add(v, distance);\n  });\n\n  while (pq.size() > 0) {\n    v = pq.removeMin();\n    vEntry = results[v];\n    if (vEntry.distance === Number.POSITIVE_INFINITY) {\n      break;\n    }\n\n    edgeFn(v).forEach(updateNeighbors);\n  }\n\n  return results;\n}\n", "import * as _ from 'lodash-es';\n\nexport { floyd<PERSON>ars<PERSON> };\n\nvar DEFAULT_WEIGHT_FUNC = _.constant(1);\n\nfunction floydWarshall(g, weightFn, edgeFn) {\n  return runFloydWarshall(\n    g,\n    weightFn || DEFAULT_WEIGHT_FUNC,\n    edgeFn ||\n      function (v) {\n        return g.outEdges(v);\n      },\n  );\n}\n\nfunction runFloydWarshall(g, weightFn, edgeFn) {\n  var results = {};\n  var nodes = g.nodes();\n\n  nodes.forEach(function (v) {\n    results[v] = {};\n    results[v][v] = { distance: 0 };\n    nodes.forEach(function (w) {\n      if (v !== w) {\n        results[v][w] = { distance: Number.POSITIVE_INFINITY };\n      }\n    });\n    edgeFn(v).forEach(function (edge) {\n      var w = edge.v === v ? edge.w : edge.v;\n      var d = weightFn(edge);\n      results[v][w] = { distance: d, predecessor: v };\n    });\n  });\n\n  nodes.forEach(function (k) {\n    var rowK = results[k];\n    nodes.forEach(function (i) {\n      var rowI = results[i];\n      nodes.forEach(function (j) {\n        var ik = rowI[k];\n        var kj = rowK[j];\n        var ij = rowI[j];\n        var altDistance = ik.distance + kj.distance;\n        if (altDistance < ij.distance) {\n          ij.distance = altDistance;\n          ij.predecessor = kj.predecessor;\n        }\n      });\n    });\n  });\n\n  return results;\n}\n", "import * as _ from 'lodash-es';\n\nexport { topsort, CycleException };\n\ntopsort.CycleException = CycleException;\n\nfunction topsort(g) {\n  var visited = {};\n  var stack = {};\n  var results = [];\n\n  function visit(node) {\n    if (Object.prototype.hasOwnProperty.call(stack, node)) {\n      throw new CycleException();\n    }\n\n    if (!Object.prototype.hasOwnProperty.call(visited, node)) {\n      stack[node] = true;\n      visited[node] = true;\n      _.each(g.predecessors(node), visit);\n      delete stack[node];\n      results.push(node);\n    }\n  }\n\n  _.each(g.sinks(), visit);\n\n  if (_.size(visited) !== g.nodeCount()) {\n    throw new CycleException();\n  }\n\n  return results;\n}\n\nfunction CycleException() {}\nCycleException.prototype = new Error(); // must be an instance of Error to pass testing\n", "import * as _ from 'lodash-es';\n\nexport { dfs };\n\n/*\n * A helper that preforms a pre- or post-order traversal on the input graph\n * and returns the nodes in the order they were visited. If the graph is\n * undirected then this algorithm will navigate using neighbors. If the graph\n * is directed then this algorithm will navigate using successors.\n *\n * Order must be one of \"pre\" or \"post\".\n */\nfunction dfs(g, vs, order) {\n  if (!_.isArray(vs)) {\n    vs = [vs];\n  }\n\n  var navigation = (g.isDirected() ? g.successors : g.neighbors).bind(g);\n\n  var acc = [];\n  var visited = {};\n  _.each(vs, function (v) {\n    if (!g.hasNode(v)) {\n      throw new Error('Graph does not have node: ' + v);\n    }\n\n    doDfs(g, v, order === 'post', visited, navigation, acc);\n  });\n  return acc;\n}\n\nfunction doDfs(g, v, postorder, visited, navigation, acc) {\n  if (!Object.prototype.hasOwnProperty.call(visited, v)) {\n    visited[v] = true;\n\n    if (!postorder) {\n      acc.push(v);\n    }\n    _.each(navigation(v), function (w) {\n      doDfs(g, w, postorder, visited, navigation, acc);\n    });\n    if (postorder) {\n      acc.push(v);\n    }\n  }\n}\n", "import { dfs } from './dfs.js';\n\nexport { postorder };\n\nfunction postorder(g, vs) {\n  return dfs(g, vs, 'post');\n}\n", "import { dfs } from './dfs.js';\n\nexport { preorder };\n\nfunction preorder(g, vs) {\n  return dfs(g, vs, 'pre');\n}\n", "import * as _ from 'lodash-es';\nimport * as alg from '../../graphlib/alg/index.js';\nimport { simplify } from '../util.js';\nimport { feasibleTree } from './feasible-tree.js';\nimport { longestPath, slack } from './util.js';\n\nexport { networkSimplex };\n\n// Expose some internals for testing purposes\nnetworkSimplex.initLowLimValues = initLowLimValues;\nnetworkSimplex.initCutValues = initCutValues;\nnetworkSimplex.calcCutValue = calcCutValue;\nnetworkSimplex.leaveEdge = leaveEdge;\nnetworkSimplex.enterEdge = enterEdge;\nnetworkSimplex.exchangeEdges = exchangeEdges;\n\n/*\n * The network simplex algorithm assigns ranks to each node in the input graph\n * and iteratively improves the ranking to reduce the length of edges.\n *\n * Preconditions:\n *\n *    1. The input graph must be a DAG.\n *    2. All nodes in the graph must have an object value.\n *    3. All edges in the graph must have \"minlen\" and \"weight\" attributes.\n *\n * Postconditions:\n *\n *    1. All nodes in the graph will have an assigned \"rank\" attribute that has\n *       been optimized by the network simplex algorithm. Ranks start at 0.\n *\n *\n * A rough sketch of the algorithm is as follows:\n *\n *    1. Assign initial ranks to each node. We use the longest path algorithm,\n *       which assigns ranks to the lowest position possible. In general this\n *       leads to very wide bottom ranks and unnecessarily long edges.\n *    2. Construct a feasible tight tree. A tight tree is one such that all\n *       edges in the tree have no slack (difference between length of edge\n *       and minlen for the edge). This by itself greatly improves the assigned\n *       rankings by shorting edges.\n *    3. Iteratively find edges that have negative cut values. Generally a\n *       negative cut value indicates that the edge could be removed and a new\n *       tree edge could be added to produce a more compact graph.\n *\n * Much of the algorithms here are derived from Gansner, et al., \"A Technique\n * for Drawing Directed Graphs.\" The structure of the file roughly follows the\n * structure of the overall algorithm.\n */\nfunction networkSimplex(g) {\n  g = simplify(g);\n  longestPath(g);\n  var t = feasibleTree(g);\n  initLowLimValues(t);\n  initCutValues(t, g);\n\n  var e, f;\n  while ((e = leaveEdge(t))) {\n    f = enterEdge(t, g, e);\n    exchangeEdges(t, g, e, f);\n  }\n}\n\n/*\n * Initializes cut values for all edges in the tree.\n */\nfunction initCutValues(t, g) {\n  var vs = alg.postorder(t, t.nodes());\n  vs = vs.slice(0, vs.length - 1);\n  _.forEach(vs, function (v) {\n    assignCutValue(t, g, v);\n  });\n}\n\nfunction assignCutValue(t, g, child) {\n  var childLab = t.node(child);\n  var parent = childLab.parent;\n  t.edge(child, parent).cutvalue = calcCutValue(t, g, child);\n}\n\n/*\n * Given the tight tree, its graph, and a child in the graph calculate and\n * return the cut value for the edge between the child and its parent.\n */\nfunction calcCutValue(t, g, child) {\n  var childLab = t.node(child);\n  var parent = childLab.parent;\n  // True if the child is on the tail end of the edge in the directed graph\n  var childIsTail = true;\n  // The graph's view of the tree edge we're inspecting\n  var graphEdge = g.edge(child, parent);\n  // The accumulated cut value for the edge between this node and its parent\n  var cutValue = 0;\n\n  if (!graphEdge) {\n    childIsTail = false;\n    graphEdge = g.edge(parent, child);\n  }\n\n  cutValue = graphEdge.weight;\n\n  _.forEach(g.nodeEdges(child), function (e) {\n    var isOutEdge = e.v === child,\n      other = isOutEdge ? e.w : e.v;\n\n    if (other !== parent) {\n      var pointsToHead = isOutEdge === childIsTail,\n        otherWeight = g.edge(e).weight;\n\n      cutValue += pointsToHead ? otherWeight : -otherWeight;\n      if (isTreeEdge(t, child, other)) {\n        var otherCutValue = t.edge(child, other).cutvalue;\n        cutValue += pointsToHead ? -otherCutValue : otherCutValue;\n      }\n    }\n  });\n\n  return cutValue;\n}\n\nfunction initLowLimValues(tree, root) {\n  if (arguments.length < 2) {\n    root = tree.nodes()[0];\n  }\n  dfsAssignLowLim(tree, {}, 1, root);\n}\n\nfunction dfsAssignLowLim(tree, visited, nextLim, v, parent) {\n  var low = nextLim;\n  var label = tree.node(v);\n\n  visited[v] = true;\n  _.forEach(tree.neighbors(v), function (w) {\n    if (!Object.prototype.hasOwnProperty.call(visited, w)) {\n      nextLim = dfsAssignLowLim(tree, visited, nextLim, w, v);\n    }\n  });\n\n  label.low = low;\n  label.lim = nextLim++;\n  if (parent) {\n    label.parent = parent;\n  } else {\n    // TODO should be able to remove this when we incrementally update low lim\n    delete label.parent;\n  }\n\n  return nextLim;\n}\n\nfunction leaveEdge(tree) {\n  return _.find(tree.edges(), function (e) {\n    return tree.edge(e).cutvalue < 0;\n  });\n}\n\nfunction enterEdge(t, g, edge) {\n  var v = edge.v;\n  var w = edge.w;\n\n  // For the rest of this function we assume that v is the tail and w is the\n  // head, so if we don't have this edge in the graph we should flip it to\n  // match the correct orientation.\n  if (!g.hasEdge(v, w)) {\n    v = edge.w;\n    w = edge.v;\n  }\n\n  var vLabel = t.node(v);\n  var wLabel = t.node(w);\n  var tailLabel = vLabel;\n  var flip = false;\n\n  // If the root is in the tail of the edge then we need to flip the logic that\n  // checks for the head and tail nodes in the candidates function below.\n  if (vLabel.lim > wLabel.lim) {\n    tailLabel = wLabel;\n    flip = true;\n  }\n\n  var candidates = _.filter(g.edges(), function (edge) {\n    return (\n      flip === isDescendant(t, t.node(edge.v), tailLabel) &&\n      flip !== isDescendant(t, t.node(edge.w), tailLabel)\n    );\n  });\n\n  return _.minBy(candidates, function (edge) {\n    return slack(g, edge);\n  });\n}\n\nfunction exchangeEdges(t, g, e, f) {\n  var v = e.v;\n  var w = e.w;\n  t.removeEdge(v, w);\n  t.setEdge(f.v, f.w, {});\n  initLowLimValues(t);\n  initCutValues(t, g);\n  updateRanks(t, g);\n}\n\nfunction updateRanks(t, g) {\n  var root = _.find(t.nodes(), function (v) {\n    return !g.node(v).parent;\n  });\n  var vs = alg.preorder(t, root);\n  vs = vs.slice(1);\n  _.forEach(vs, function (v) {\n    var parent = t.node(v).parent,\n      edge = g.edge(v, parent),\n      flipped = false;\n\n    if (!edge) {\n      edge = g.edge(parent, v);\n      flipped = true;\n    }\n\n    g.node(v).rank = g.node(parent).rank + (flipped ? edge.minlen : -edge.minlen);\n  });\n}\n\n/*\n * Returns true if the edge is in the tree.\n */\nfunction isTreeEdge(tree, u, v) {\n  return tree.hasEdge(u, v);\n}\n\n/*\n * Returns true if the specified node is descendant of the root node per the\n * assigned low and lim attributes in the tree.\n */\nfunction isDescendant(tree, vLabel, rootLabel) {\n  return rootLabel.low <= vLabel.lim && vLabel.lim <= rootLabel.lim;\n}\n", "import { feasibleTree } from './feasible-tree.js';\nimport { networkSimplex } from './network-simplex.js';\nimport { longestPath } from './util.js';\n\nexport { rank };\n\n/*\n * Assigns a rank to each node in the input graph that respects the \"minlen\"\n * constraint specified on edges between nodes.\n *\n * This basic structure is derived from <PERSON><PERSON><PERSON>, et al., \"A Technique for\n * Drawing Directed Graphs.\"\n *\n * Pre-conditions:\n *\n *    1. Graph must be a connected DAG\n *    2. Graph nodes must be objects\n *    3. Graph edges must have \"weight\" and \"minlen\" attributes\n *\n * Post-conditions:\n *\n *    1. Graph nodes will have a \"rank\" attribute based on the results of the\n *       algorithm. Ranks can start at any index (including negative), we'll\n *       fix them up later.\n */\nfunction rank(g) {\n  switch (g.graph().ranker) {\n    case 'network-simplex':\n      networkSimplexRanker(g);\n      break;\n    case 'tight-tree':\n      tightTreeRanker(g);\n      break;\n    case 'longest-path':\n      longestPathRanker(g);\n      break;\n    default:\n      networkSimplexRanker(g);\n  }\n}\n\n// A fast and simple ranker, but results are far from optimal.\nvar longestPathRanker = longestPath;\n\nfunction tightTreeRanker(g) {\n  longestPath(g);\n  feasibleTree(g);\n}\n\nfunction networkSimplexRanker(g) {\n  networkSimplex(g);\n}\n", "import * as _ from 'lodash-es';\nimport * as util from './util.js';\n\nexport { run, cleanup };\n\n/*\n * A nesting graph creates dummy nodes for the tops and bottoms of subgraphs,\n * adds appropriate edges to ensure that all cluster nodes are placed between\n * these boundries, and ensures that the graph is connected.\n *\n * In addition we ensure, through the use of the minlen property, that nodes\n * and subgraph border nodes to not end up on the same rank.\n *\n * Preconditions:\n *\n *    1. Input graph is a DAG\n *    2. Nodes in the input graph has a minlen attribute\n *\n * Postconditions:\n *\n *    1. Input graph is connected.\n *    2. Dummy nodes are added for the tops and bottoms of subgraphs.\n *    3. The minlen attribute for nodes is adjusted to ensure nodes do not\n *       get placed on the same rank as subgraph border nodes.\n *\n * The nesting graph idea comes from <PERSON><PERSON>, \"Layout of Compound Directed\n * Graphs.\"\n */\nfunction run(g) {\n  var root = util.addDummyNode(g, 'root', {}, '_root');\n  var depths = treeDepths(g);\n  var height = _.max(_.values(depths)) - 1; // Note: depths is an Object not an array\n  var nodeSep = 2 * height + 1;\n\n  g.graph().nestingRoot = root;\n\n  // Multiply minlen by nodeSep to align nodes on non-border ranks.\n  _.forEach(g.edges(), function (e) {\n    g.edge(e).minlen *= nodeSep;\n  });\n\n  // Calculate a weight that is sufficient to keep subgraphs vertically compact\n  var weight = sumWeights(g) + 1;\n\n  // Create border nodes and link them up\n  _.forEach(g.children(), function (child) {\n    dfs(g, root, nodeSep, weight, height, depths, child);\n  });\n\n  // Save the multiplier for node layers for later removal of empty border\n  // layers.\n  g.graph().nodeRankFactor = nodeSep;\n}\n\nfunction dfs(g, root, nodeSep, weight, height, depths, v) {\n  var children = g.children(v);\n  if (!children.length) {\n    if (v !== root) {\n      g.setEdge(root, v, { weight: 0, minlen: nodeSep });\n    }\n    return;\n  }\n\n  var top = util.addBorderNode(g, '_bt');\n  var bottom = util.addBorderNode(g, '_bb');\n  var label = g.node(v);\n\n  g.setParent(top, v);\n  label.borderTop = top;\n  g.setParent(bottom, v);\n  label.borderBottom = bottom;\n\n  _.forEach(children, function (child) {\n    dfs(g, root, nodeSep, weight, height, depths, child);\n\n    var childNode = g.node(child);\n    var childTop = childNode.borderTop ? childNode.borderTop : child;\n    var childBottom = childNode.borderBottom ? childNode.borderBottom : child;\n    var thisWeight = childNode.borderTop ? weight : 2 * weight;\n    var minlen = childTop !== childBottom ? 1 : height - depths[v] + 1;\n\n    g.setEdge(top, childTop, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true,\n    });\n\n    g.setEdge(childBottom, bottom, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true,\n    });\n  });\n\n  if (!g.parent(v)) {\n    g.setEdge(root, top, { weight: 0, minlen: height + depths[v] });\n  }\n}\n\nfunction treeDepths(g) {\n  var depths = {};\n  function dfs(v, depth) {\n    var children = g.children(v);\n    if (children && children.length) {\n      _.forEach(children, function (child) {\n        dfs(child, depth + 1);\n      });\n    }\n    depths[v] = depth;\n  }\n  _.forEach(g.children(), function (v) {\n    dfs(v, 1);\n  });\n  return depths;\n}\n\nfunction sumWeights(g) {\n  return _.reduce(\n    g.edges(),\n    function (acc, e) {\n      return acc + g.edge(e).weight;\n    },\n    0,\n  );\n}\n\nfunction cleanup(g) {\n  var graphLabel = g.graph();\n  g.removeNode(graphLabel.nestingRoot);\n  delete graphLabel.nestingRoot;\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.nestingEdge) {\n      g.removeEdge(e);\n    }\n  });\n}\n", "import * as _ from 'lodash-es';\n\nexport { addSubgraphConstraints };\n\nfunction addSubgraphConstraints(g, cg, vs) {\n  var prev = {},\n    rootPrev;\n\n  _.forEach(vs, function (v) {\n    var child = g.parent(v),\n      parent,\n      prevChild;\n    while (child) {\n      parent = g.parent(child);\n      if (parent) {\n        prevChild = prev[parent];\n        prev[parent] = child;\n      } else {\n        prevChild = rootPrev;\n        rootPrev = child;\n      }\n      if (prevChild && prevChild !== child) {\n        cg.setEdge(prevChild, child);\n        return;\n      }\n      child = parent;\n    }\n  });\n\n  /*\n  function dfs(v) {\n    var children = v ? g.children(v) : g.children();\n    if (children.length) {\n      var min = Number.POSITIVE_INFINITY,\n          subgraphs = [];\n      _.each(children, function(child) {\n        var childMin = dfs(child);\n        if (g.children(child).length) {\n          subgraphs.push({ v: child, order: childMin });\n        }\n        min = Math.min(min, childMin);\n      });\n      _.reduce(_.sortBy(subgraphs, \"order\"), function(prev, curr) {\n        cg.setEdge(prev.v, curr.v);\n        return curr;\n      });\n      return min;\n    }\n    return g.node(v).order;\n  }\n  dfs(undefined);\n  */\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\n\nexport { buildLayerGraph };\n\n/*\n * Constructs a graph that can be used to sort a layer of nodes. The graph will\n * contain all base and subgraph nodes from the request layer in their original\n * hierarchy and any edges that are incident on these nodes and are of the type\n * requested by the \"relationship\" parameter.\n *\n * Nodes from the requested rank that do not have parents are assigned a root\n * node in the output graph, which is set in the root graph attribute. This\n * makes it easy to walk the hierarchy of movable nodes during ordering.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG\n *    2. Base nodes in the input graph have a rank attribute\n *    3. Subgraph nodes in the input graph has minRank and maxRank attributes\n *    4. Edges have an assigned weight\n *\n * Post-conditions:\n *\n *    1. Output graph has all nodes in the movable rank with preserved\n *       hierarchy.\n *    2. Root nodes in the movable layer are made children of the node\n *       indicated by the root attribute of the graph.\n *    3. Non-movable nodes incident on movable nodes, selected by the\n *       relationship parameter, are included in the graph (without hierarchy).\n *    4. Edges incident on movable nodes, selected by the relationship\n *       parameter, are added to the output graph.\n *    5. The weights for copied edges are aggregated as need, since the output\n *       graph is not a multi-graph.\n */\nfunction buildLayerGraph(g, rank, relationship) {\n  var root = createRootNode(g),\n    result = new Graph({ compound: true })\n      .setGraph({ root: root })\n      .setDefaultNodeLabel(function (v) {\n        return g.node(v);\n      });\n\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v),\n      parent = g.parent(v);\n\n    if (node.rank === rank || (node.minRank <= rank && rank <= node.maxRank)) {\n      result.setNode(v);\n      result.setParent(v, parent || root);\n\n      // This assumes we have only short edges!\n      _.forEach(g[relationship](v), function (e) {\n        var u = e.v === v ? e.w : e.v,\n          edge = result.edge(u, v),\n          weight = !_.isUndefined(edge) ? edge.weight : 0;\n        result.setEdge(u, v, { weight: g.edge(e).weight + weight });\n      });\n\n      if (Object.prototype.hasOwnProperty.call(node, 'minRank')) {\n        result.setNode(v, {\n          borderLeft: node.borderLeft[rank],\n          borderRight: node.borderRight[rank],\n        });\n      }\n    }\n  });\n\n  return result;\n}\n\nfunction createRootNode(g) {\n  var v;\n  while (g.hasNode((v = _.uniqueId('_root'))));\n  return v;\n}\n", "import * as _ from 'lodash-es';\n\nexport { crossCount };\n\n/*\n * A function that takes a layering (an array of layers, each with an array of\n * ordererd nodes) and a graph and returns a weighted crossing count.\n *\n * Pre-conditions:\n *\n *    1. Input graph must be simple (not a multigraph), directed, and include\n *       only simple edges.\n *    2. Edges in the input graph must have assigned weights.\n *\n * Post-conditions:\n *\n *    1. The graph and layering matrix are left unchanged.\n *\n * This algorithm is derived from <PERSON><PERSON>, et al., \"Bilayer Cross Counting.\"\n */\nfunction crossCount(g, layering) {\n  var cc = 0;\n  for (var i = 1; i < layering.length; ++i) {\n    cc += twoLayerCrossCount(g, layering[i - 1], layering[i]);\n  }\n  return cc;\n}\n\nfunction twoLayerCrossCount(g, northLayer, southLayer) {\n  // Sort all of the edges between the north and south layers by their position\n  // in the north layer and then the south. Map these edges to the position of\n  // their head in the south layer.\n  var southPos = _.zipObject(\n    southLayer,\n    _.map(southLayer, function (v, i) {\n      return i;\n    }),\n  );\n  var southEntries = _.flatten(\n    _.map(northLayer, function (v) {\n      return _.sortBy(\n        _.map(g.outEdges(v), function (e) {\n          return { pos: southPos[e.w], weight: g.edge(e).weight };\n        }),\n        'pos',\n      );\n    }),\n  );\n\n  // Build the accumulator tree\n  var firstIndex = 1;\n  while (firstIndex < southLayer.length) firstIndex <<= 1;\n  var treeSize = 2 * firstIndex - 1;\n  firstIndex -= 1;\n  var tree = _.map(new Array(treeSize), function () {\n    return 0;\n  });\n\n  // Calculate the weighted crossings\n  var cc = 0;\n  _.forEach(\n    // @ts-expect-error\n    southEntries.forEach(function (entry) {\n      var index = entry.pos + firstIndex;\n      tree[index] += entry.weight;\n      var weightSum = 0;\n      // @ts-expect-error\n      while (index > 0) {\n        // @ts-expect-error\n        if (index % 2) {\n          weightSum += tree[index + 1];\n        }\n        // @ts-expect-error\n        index = (index - 1) >> 1;\n        tree[index] += entry.weight;\n      }\n      cc += entry.weight * weightSum;\n    }),\n  );\n\n  return cc;\n}\n", "import * as _ from 'lodash-es';\n\n/*\n * Assigns an initial order value for each node by performing a DFS search\n * starting from nodes in the first rank. Nodes are assigned an order in their\n * rank as they are first visited.\n *\n * This approach comes from <PERSON><PERSON><PERSON>, et al., \"A Technique for Drawing Directed\n * Graphs.\"\n *\n * Returns a layering matrix with an array per layer and each layer sorted by\n * the order of its nodes.\n */\nexport function initOrder(g) {\n  var visited = {};\n  var simpleNodes = _.filter(g.nodes(), function (v) {\n    return !g.children(v).length;\n  });\n  var maxRank = _.max(\n    _.map(simpleNodes, function (v) {\n      return g.node(v).rank;\n    }),\n  );\n  var layers = _.map(_.range(maxRank + 1), function () {\n    return [];\n  });\n\n  function dfs(v) {\n    if (_.has(visited, v)) return;\n    visited[v] = true;\n    var node = g.node(v);\n    layers[node.rank].push(v);\n    _.forEach(g.successors(v), dfs);\n  }\n\n  var orderedVs = _.sortBy(simpleNodes, function (v) {\n    return g.node(v).rank;\n  });\n  _.forEach(orderedVs, dfs);\n\n  return layers;\n}\n", "import * as _ from 'lodash-es';\n\nexport { barycenter };\n\nfunction barycenter(g, movable) {\n  return _.map(movable, function (v) {\n    var inV = g.inEdges(v);\n    if (!inV.length) {\n      return { v: v };\n    } else {\n      var result = _.reduce(\n        inV,\n        function (acc, e) {\n          var edge = g.edge(e),\n            nodeU = g.node(e.v);\n          return {\n            sum: acc.sum + edge.weight * nodeU.order,\n            weight: acc.weight + edge.weight,\n          };\n        },\n        { sum: 0, weight: 0 },\n      );\n\n      return {\n        v: v,\n        barycenter: result.sum / result.weight,\n        weight: result.weight,\n      };\n    }\n  });\n}\n", "import * as _ from 'lodash-es';\n\nexport { resolveConflicts };\n\n/*\n * Given a list of entries of the form {v, barycenter, weight} and a\n * constraint graph this function will resolve any conflicts between the\n * constraint graph and the barycenters for the entries. If the barycenters for\n * an entry would violate a constraint in the constraint graph then we coalesce\n * the nodes in the conflict into a new node that respects the contraint and\n * aggregates barycenter and weight information.\n *\n * This implementation is based on the description in Forster, \"A Fast and\n * Simple Hueristic for Constrained Two-Level Crossing Reduction,\" thought it\n * differs in some specific details.\n *\n * Pre-conditions:\n *\n *    1. Each entry has the form {v, barycenter, weight}, or if the node has\n *       no barycenter, then {v}.\n *\n * Returns:\n *\n *    A new list of entries of the form {vs, i, barycenter, weight}. The list\n *    `vs` may either be a singleton or it may be an aggregation of nodes\n *    ordered such that they do not violate constraints from the constraint\n *    graph. The property `i` is the lowest original index of any of the\n *    elements in `vs`.\n */\nfunction resolveConflicts(entries, cg) {\n  var mappedEntries = {};\n  _.forEach(entries, function (entry, i) {\n    var tmp = (mappedEntries[entry.v] = {\n      indegree: 0,\n      in: [],\n      out: [],\n      vs: [entry.v],\n      i: i,\n    });\n    if (!_.isUndefined(entry.barycenter)) {\n      // @ts-expect-error\n      tmp.barycenter = entry.barycenter;\n      // @ts-expect-error\n      tmp.weight = entry.weight;\n    }\n  });\n\n  _.forEach(cg.edges(), function (e) {\n    var entryV = mappedEntries[e.v];\n    var entryW = mappedEntries[e.w];\n    if (!_.isUndefined(entryV) && !_.isUndefined(entryW)) {\n      entryW.indegree++;\n      entryV.out.push(mappedEntries[e.w]);\n    }\n  });\n\n  var sourceSet = _.filter(mappedEntries, function (entry) {\n    // @ts-expect-error\n    return !entry.indegree;\n  });\n\n  return doResolveConflicts(sourceSet);\n}\n\nfunction doResolveConflicts(sourceSet) {\n  var entries = [];\n\n  function handleIn(vEntry) {\n    return function (uEntry) {\n      if (uEntry.merged) {\n        return;\n      }\n      if (\n        _.isUndefined(uEntry.barycenter) ||\n        _.isUndefined(vEntry.barycenter) ||\n        uEntry.barycenter >= vEntry.barycenter\n      ) {\n        mergeEntries(vEntry, uEntry);\n      }\n    };\n  }\n\n  function handleOut(vEntry) {\n    return function (wEntry) {\n      wEntry['in'].push(vEntry);\n      if (--wEntry.indegree === 0) {\n        sourceSet.push(wEntry);\n      }\n    };\n  }\n\n  while (sourceSet.length) {\n    var entry = sourceSet.pop();\n    entries.push(entry);\n    _.forEach(entry['in'].reverse(), handleIn(entry));\n    _.forEach(entry.out, handleOut(entry));\n  }\n\n  return _.map(\n    _.filter(entries, function (entry) {\n      return !entry.merged;\n    }),\n    function (entry) {\n      return _.pick(entry, ['vs', 'i', 'barycenter', 'weight']);\n    },\n  );\n}\n\nfunction mergeEntries(target, source) {\n  var sum = 0;\n  var weight = 0;\n\n  if (target.weight) {\n    sum += target.barycenter * target.weight;\n    weight += target.weight;\n  }\n\n  if (source.weight) {\n    sum += source.barycenter * source.weight;\n    weight += source.weight;\n  }\n\n  target.vs = source.vs.concat(target.vs);\n  target.barycenter = sum / weight;\n  target.weight = weight;\n  target.i = Math.min(source.i, target.i);\n  source.merged = true;\n}\n", "import * as _ from 'lodash-es';\nimport * as util from '../util.js';\n\nexport { sort };\n\nfunction sort(entries, biasRight) {\n  var parts = util.partition(entries, function (entry) {\n    return Object.prototype.hasOwnProperty.call(entry, 'barycenter');\n  });\n  var sortable = parts.lhs,\n    unsortable = _.sortBy(parts.rhs, function (entry) {\n      return -entry.i;\n    }),\n    vs = [],\n    sum = 0,\n    weight = 0,\n    vsIndex = 0;\n\n  sortable.sort(compareWithBias(!!biasRight));\n\n  vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n\n  _.forEach(sortable, function (entry) {\n    vsIndex += entry.vs.length;\n    vs.push(entry.vs);\n    sum += entry.barycenter * entry.weight;\n    weight += entry.weight;\n    vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n  });\n\n  var result = { vs: _.flatten(vs) };\n  if (weight) {\n    result.barycenter = sum / weight;\n    result.weight = weight;\n  }\n  return result;\n}\n\nfunction consumeUnsortable(vs, unsortable, index) {\n  var last;\n  while (unsortable.length && (last = _.last(unsortable)).i <= index) {\n    unsortable.pop();\n    vs.push(last.vs);\n    index++;\n  }\n  return index;\n}\n\nfunction compareWithBias(bias) {\n  return function (entryV, entryW) {\n    if (entryV.barycenter < entryW.barycenter) {\n      return -1;\n    } else if (entryV.barycenter > entryW.barycenter) {\n      return 1;\n    }\n\n    return !bias ? entryV.i - entryW.i : entryW.i - entryV.i;\n  };\n}\n", "import * as _ from 'lodash-es';\nimport { barycenter } from './barycenter.js';\nimport { resolveConflicts } from './resolve-conflicts.js';\nimport { sort } from './sort.js';\n\nexport { sortSubgraph };\n\nfunction sortSubgraph(g, v, cg, biasRight) {\n  var movable = g.children(v);\n  var node = g.node(v);\n  var bl = node ? node.borderLeft : undefined;\n  var br = node ? node.borderRight : undefined;\n  var subgraphs = {};\n\n  if (bl) {\n    movable = _.filter(movable, function (w) {\n      return w !== bl && w !== br;\n    });\n  }\n\n  var barycenters = barycenter(g, movable);\n  _.forEach(barycenters, function (entry) {\n    if (g.children(entry.v).length) {\n      var subgraphResult = sortSubgraph(g, entry.v, cg, biasRight);\n      subgraphs[entry.v] = subgraphResult;\n      if (Object.prototype.hasOwnProperty.call(subgraphResult, 'barycenter')) {\n        mergeBarycenters(entry, subgraphResult);\n      }\n    }\n  });\n\n  var entries = resolveConflicts(barycenters, cg);\n  expandSubgraphs(entries, subgraphs);\n\n  var result = sort(entries, biasRight);\n\n  if (bl) {\n    result.vs = _.flatten([bl, result.vs, br]);\n    if (g.predecessors(bl).length) {\n      var blPred = g.node(g.predecessors(bl)[0]),\n        brPred = g.node(g.predecessors(br)[0]);\n      if (!Object.prototype.hasOwnProperty.call(result, 'barycenter')) {\n        result.barycenter = 0;\n        result.weight = 0;\n      }\n      result.barycenter =\n        (result.barycenter * result.weight + blPred.order + brPred.order) / (result.weight + 2);\n      result.weight += 2;\n    }\n  }\n\n  return result;\n}\n\nfunction expandSubgraphs(entries, subgraphs) {\n  _.forEach(entries, function (entry) {\n    entry.vs = _.flatten(\n      entry.vs.map(function (v) {\n        if (subgraphs[v]) {\n          return subgraphs[v].vs;\n        }\n        return v;\n      }),\n    );\n  });\n}\n\nfunction mergeBarycenters(target, other) {\n  if (!_.isUndefined(target.barycenter)) {\n    target.barycenter =\n      (target.barycenter * target.weight + other.barycenter * other.weight) /\n      (target.weight + other.weight);\n    target.weight += other.weight;\n  } else {\n    target.barycenter = other.barycenter;\n    target.weight = other.weight;\n  }\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nimport * as util from '../util.js';\nimport { addSubgraphConstraints } from './add-subgraph-constraints.js';\nimport { buildLayerGraph } from './build-layer-graph.js';\nimport { crossCount } from './cross-count.js';\nimport { initOrder } from './init-order.js';\nimport { sortSubgraph } from './sort-subgraph.js';\n\nexport { order };\n\n/*\n * Applies heuristics to minimize edge crossings in the graph and sets the best\n * order solution as an order attribute on each node.\n *\n * Pre-conditions:\n *\n *    1. Graph must be DAG\n *    2. Graph nodes must be objects with a \"rank\" attribute\n *    3. Graph edges must have the \"weight\" attribute\n *\n * Post-conditions:\n *\n *    1. Graph nodes will have an \"order\" attribute based on the results of the\n *       algorithm.\n */\nfunction order(g) {\n  var maxRank = util.maxRank(g),\n    downLayerGraphs = buildLayerGraphs(g, _.range(1, maxRank + 1), 'inEdges'),\n    upLayerGraphs = buildLayerGraphs(g, _.range(maxRank - 1, -1, -1), 'outEdges');\n\n  var layering = initOrder(g);\n  assignOrder(g, layering);\n\n  var bestCC = Number.POSITIVE_INFINITY,\n    best;\n\n  for (var i = 0, lastBest = 0; lastBest < 4; ++i, ++lastBest) {\n    sweepLayerGraphs(i % 2 ? downLayerGraphs : upLayerGraphs, i % 4 >= 2);\n\n    layering = util.buildLayerMatrix(g);\n    var cc = crossCount(g, layering);\n    if (cc < bestCC) {\n      lastBest = 0;\n      best = _.cloneDeep(layering);\n      bestCC = cc;\n    }\n  }\n\n  assignOrder(g, best);\n}\n\nfunction buildLayerGraphs(g, ranks, relationship) {\n  return _.map(ranks, function (rank) {\n    return buildLayerGraph(g, rank, relationship);\n  });\n}\n\nfunction sweepLayerGraphs(layerGraphs, biasRight) {\n  var cg = new Graph();\n  _.forEach(layerGraphs, function (lg) {\n    var root = lg.graph().root;\n    var sorted = sortSubgraph(lg, root, cg, biasRight);\n    _.forEach(sorted.vs, function (v, i) {\n      lg.node(v).order = i;\n    });\n    addSubgraphConstraints(lg, cg, sorted.vs);\n  });\n}\n\nfunction assignOrder(g, layering) {\n  _.forEach(layering, function (layer) {\n    _.forEach(layer, function (v, i) {\n      g.node(v).order = i;\n    });\n  });\n}\n", "import * as _ from 'lodash-es';\n\nexport { parentDummyChains };\n\nfunction parentDummyChains(g) {\n  var postorderNums = postorder(g);\n\n  _.forEach(g.graph().dummyChains, function (v) {\n    var node = g.node(v);\n    var edgeObj = node.edgeObj;\n    var pathData = findPath(g, postorderNums, edgeObj.v, edgeObj.w);\n    var path = pathData.path;\n    var lca = pathData.lca;\n    var pathIdx = 0;\n    var pathV = path[pathIdx];\n    var ascending = true;\n\n    while (v !== edgeObj.w) {\n      node = g.node(v);\n\n      if (ascending) {\n        while ((pathV = path[pathIdx]) !== lca && g.node(pathV).maxRank < node.rank) {\n          pathIdx++;\n        }\n\n        if (pathV === lca) {\n          ascending = false;\n        }\n      }\n\n      if (!ascending) {\n        while (\n          pathIdx < path.length - 1 &&\n          g.node((pathV = path[pathIdx + 1])).minRank <= node.rank\n        ) {\n          pathIdx++;\n        }\n        pathV = path[pathIdx];\n      }\n\n      g.setParent(v, pathV);\n      v = g.successors(v)[0];\n    }\n  });\n}\n\n// Find a path from v to w through the lowest common ancestor (LCA). Return the\n// full path and the LCA.\nfunction findPath(g, postorderNums, v, w) {\n  var vPath = [];\n  var wPath = [];\n  var low = Math.min(postorderNums[v].low, postorderNums[w].low);\n  var lim = Math.max(postorderNums[v].lim, postorderNums[w].lim);\n  var parent;\n  var lca;\n\n  // Traverse up from v to find the LCA\n  parent = v;\n  do {\n    parent = g.parent(parent);\n    vPath.push(parent);\n  } while (parent && (postorderNums[parent].low > low || lim > postorderNums[parent].lim));\n  lca = parent;\n\n  // Traverse from w to LCA\n  parent = w;\n  while ((parent = g.parent(parent)) !== lca) {\n    wPath.push(parent);\n  }\n\n  return { path: vPath.concat(wPath.reverse()), lca: lca };\n}\n\nfunction postorder(g) {\n  var result = {};\n  var lim = 0;\n\n  function dfs(v) {\n    var low = lim;\n    _.forEach(g.children(v), dfs);\n    result[v] = { low: low, lim: lim++ };\n  }\n  _.forEach(g.children(), dfs);\n\n  return result;\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../../graphlib/index.js';\nimport * as util from '../util.js';\n\n/*\n * This module provides coordinate assignment based on <PERSON><PERSON> and <PERSON>, \"Fast\n * and Simple Horizontal Coordinate Assignment.\"\n */\n\nexport {\n  positionX,\n  findType1Conflicts,\n  findType2Conflicts,\n  addConflict,\n  hasConflict,\n  verticalAlignment,\n  horizontalCompaction,\n  alignCoordinates,\n  findSmallestWidthAlignment,\n  balance,\n};\n\n/*\n * Marks all edges in the graph with a type-1 conflict with the \"type1Conflict\"\n * property. A type-1 conflict is one where a non-inner segment crosses an\n * inner segment. An inner segment is an edge with both incident nodes marked\n * with the \"dummy\" property.\n *\n * This algorithm scans layer by layer, starting with the second, for type-1\n * conflicts between the current layer and the previous layer. For each layer\n * it scans the nodes from left to right until it reaches one that is incident\n * on an inner segment. It then scans predecessors to determine if they have\n * edges that cross that inner segment. At the end a final scan is done for all\n * nodes on the current rank to see if they cross the last visited inner\n * segment.\n *\n * This algorithm (safely) assumes that a dummy node will only be incident on a\n * single node in the layers being scanned.\n */\nfunction findType1Conflicts(g, layering) {\n  var conflicts = {};\n\n  function visitLayer(prevLayer, layer) {\n    var // last visited node in the previous layer that is incident on an inner\n      // segment.\n      k0 = 0,\n      // Tracks the last node in this layer scanned for crossings with a type-1\n      // segment.\n      scanPos = 0,\n      prevLayerLength = prevLayer.length,\n      lastNode = _.last(layer);\n\n    _.forEach(layer, function (v, i) {\n      var w = findOtherInnerSegmentNode(g, v),\n        k1 = w ? g.node(w).order : prevLayerLength;\n\n      if (w || v === lastNode) {\n        _.forEach(layer.slice(scanPos, i + 1), function (scanNode) {\n          _.forEach(g.predecessors(scanNode), function (u) {\n            var uLabel = g.node(u),\n              uPos = uLabel.order;\n            if ((uPos < k0 || k1 < uPos) && !(uLabel.dummy && g.node(scanNode).dummy)) {\n              addConflict(conflicts, u, scanNode);\n            }\n          });\n        });\n        // @ts-expect-error\n        scanPos = i + 1;\n        k0 = k1;\n      }\n    });\n\n    return layer;\n  }\n\n  _.reduce(layering, visitLayer);\n  return conflicts;\n}\n\nfunction findType2Conflicts(g, layering) {\n  var conflicts = {};\n\n  function scan(south, southPos, southEnd, prevNorthBorder, nextNorthBorder) {\n    var v;\n    _.forEach(_.range(southPos, southEnd), function (i) {\n      v = south[i];\n      if (g.node(v).dummy) {\n        _.forEach(g.predecessors(v), function (u) {\n          var uNode = g.node(u);\n          if (uNode.dummy && (uNode.order < prevNorthBorder || uNode.order > nextNorthBorder)) {\n            addConflict(conflicts, u, v);\n          }\n        });\n      }\n    });\n  }\n\n  function visitLayer(north, south) {\n    var prevNorthPos = -1,\n      nextNorthPos,\n      southPos = 0;\n\n    _.forEach(south, function (v, southLookahead) {\n      if (g.node(v).dummy === 'border') {\n        var predecessors = g.predecessors(v);\n        if (predecessors.length) {\n          nextNorthPos = g.node(predecessors[0]).order;\n          scan(south, southPos, southLookahead, prevNorthPos, nextNorthPos);\n          // @ts-expect-error\n          southPos = southLookahead;\n          prevNorthPos = nextNorthPos;\n        }\n      }\n      scan(south, southPos, south.length, nextNorthPos, north.length);\n    });\n\n    return south;\n  }\n\n  _.reduce(layering, visitLayer);\n  return conflicts;\n}\n\nfunction findOtherInnerSegmentNode(g, v) {\n  if (g.node(v).dummy) {\n    return _.find(g.predecessors(v), function (u) {\n      return g.node(u).dummy;\n    });\n  }\n}\n\nfunction addConflict(conflicts, v, w) {\n  if (v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n\n  var conflictsV = conflicts[v];\n  if (!conflictsV) {\n    conflicts[v] = conflictsV = {};\n  }\n  conflictsV[w] = true;\n}\n\nfunction hasConflict(conflicts, v, w) {\n  if (v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  return !!conflicts[v] && Object.prototype.hasOwnProperty.call(conflicts[v], w);\n}\n\n/*\n * Try to align nodes into vertical \"blocks\" where possible. This algorithm\n * attempts to align a node with one of its median neighbors. If the edge\n * connecting a neighbor is a type-1 conflict then we ignore that possibility.\n * If a previous node has already formed a block with a node after the node\n * we're trying to form a block with, we also ignore that possibility - our\n * blocks would be split in that scenario.\n */\nfunction verticalAlignment(g, layering, conflicts, neighborFn) {\n  var root = {},\n    align = {},\n    pos = {};\n\n  // We cache the position here based on the layering because the graph and\n  // layering may be out of sync. The layering matrix is manipulated to\n  // generate different extreme alignments.\n  _.forEach(layering, function (layer) {\n    _.forEach(layer, function (v, order) {\n      root[v] = v;\n      align[v] = v;\n      pos[v] = order;\n    });\n  });\n\n  _.forEach(layering, function (layer) {\n    var prevIdx = -1;\n    _.forEach(layer, function (v) {\n      var ws = neighborFn(v);\n      if (ws.length) {\n        ws = _.sortBy(ws, function (w) {\n          return pos[w];\n        });\n        var mp = (ws.length - 1) / 2;\n        for (var i = Math.floor(mp), il = Math.ceil(mp); i <= il; ++i) {\n          var w = ws[i];\n          if (align[v] === v && prevIdx < pos[w] && !hasConflict(conflicts, v, w)) {\n            align[w] = v;\n            align[v] = root[v] = root[w];\n            prevIdx = pos[w];\n          }\n        }\n      }\n    });\n  });\n\n  return { root: root, align: align };\n}\n\nfunction horizontalCompaction(g, layering, root, align, reverseSep) {\n  // This portion of the algorithm differs from BK due to a number of problems.\n  // Instead of their algorithm we construct a new block graph and do two\n  // sweeps. The first sweep places blocks with the smallest possible\n  // coordinates. The second sweep removes unused space by moving blocks to the\n  // greatest coordinates without violating separation.\n  var xs = {},\n    blockG = buildBlockGraph(g, layering, root, reverseSep),\n    borderType = reverseSep ? 'borderLeft' : 'borderRight';\n\n  function iterate(setXsFunc, nextNodesFunc) {\n    var stack = blockG.nodes();\n    var elem = stack.pop();\n    var visited = {};\n    while (elem) {\n      if (visited[elem]) {\n        setXsFunc(elem);\n      } else {\n        visited[elem] = true;\n        stack.push(elem);\n        stack = stack.concat(nextNodesFunc(elem));\n      }\n\n      elem = stack.pop();\n    }\n  }\n\n  // First pass, assign smallest coordinates\n  function pass1(elem) {\n    xs[elem] = blockG.inEdges(elem).reduce(function (acc, e) {\n      return Math.max(acc, xs[e.v] + blockG.edge(e));\n    }, 0);\n  }\n\n  // Second pass, assign greatest coordinates\n  function pass2(elem) {\n    var min = blockG.outEdges(elem).reduce(function (acc, e) {\n      return Math.min(acc, xs[e.w] - blockG.edge(e));\n    }, Number.POSITIVE_INFINITY);\n\n    var node = g.node(elem);\n    if (min !== Number.POSITIVE_INFINITY && node.borderType !== borderType) {\n      xs[elem] = Math.max(xs[elem], min);\n    }\n  }\n\n  iterate(pass1, blockG.predecessors.bind(blockG));\n  iterate(pass2, blockG.successors.bind(blockG));\n\n  // Assign x coordinates to all nodes\n  _.forEach(align, function (v) {\n    xs[v] = xs[root[v]];\n  });\n\n  return xs;\n}\n\nfunction buildBlockGraph(g, layering, root, reverseSep) {\n  var blockGraph = new Graph(),\n    graphLabel = g.graph(),\n    sepFn = sep(graphLabel.nodesep, graphLabel.edgesep, reverseSep);\n\n  _.forEach(layering, function (layer) {\n    var u;\n    _.forEach(layer, function (v) {\n      var vRoot = root[v];\n      blockGraph.setNode(vRoot);\n      if (u) {\n        var uRoot = root[u],\n          prevMax = blockGraph.edge(uRoot, vRoot);\n        blockGraph.setEdge(uRoot, vRoot, Math.max(sepFn(g, v, u), prevMax || 0));\n      }\n      u = v;\n    });\n  });\n\n  return blockGraph;\n}\n\n/*\n * Returns the alignment that has the smallest width of the given alignments.\n */\nfunction findSmallestWidthAlignment(g, xss) {\n  return _.minBy(_.values(xss), function (xs) {\n    var max = Number.NEGATIVE_INFINITY;\n    var min = Number.POSITIVE_INFINITY;\n\n    _.forIn(xs, function (x, v) {\n      var halfWidth = width(g, v) / 2;\n\n      max = Math.max(x + halfWidth, max);\n      min = Math.min(x - halfWidth, min);\n    });\n\n    return max - min;\n  });\n}\n\n/*\n * Align the coordinates of each of the layout alignments such that\n * left-biased alignments have their minimum coordinate at the same point as\n * the minimum coordinate of the smallest width alignment and right-biased\n * alignments have their maximum coordinate at the same point as the maximum\n * coordinate of the smallest width alignment.\n */\nfunction alignCoordinates(xss, alignTo) {\n  var alignToVals = _.values(alignTo),\n    alignToMin = _.min(alignToVals),\n    alignToMax = _.max(alignToVals);\n\n  _.forEach(['u', 'd'], function (vert) {\n    _.forEach(['l', 'r'], function (horiz) {\n      var alignment = vert + horiz,\n        xs = xss[alignment],\n        delta;\n      if (xs === alignTo) return;\n\n      var xsVals = _.values(xs);\n      delta = horiz === 'l' ? alignToMin - _.min(xsVals) : alignToMax - _.max(xsVals);\n\n      if (delta) {\n        xss[alignment] = _.mapValues(xs, function (x) {\n          return x + delta;\n        });\n      }\n    });\n  });\n}\n\nfunction balance(xss, align) {\n  return _.mapValues(xss.ul, function (ignore, v) {\n    if (align) {\n      return xss[align.toLowerCase()][v];\n    } else {\n      var xs = _.sortBy(_.map(xss, v));\n      return (xs[1] + xs[2]) / 2;\n    }\n  });\n}\n\nfunction positionX(g) {\n  var layering = util.buildLayerMatrix(g);\n  var conflicts = _.merge(findType1Conflicts(g, layering), findType2Conflicts(g, layering));\n\n  var xss = {};\n  var adjustedLayering;\n  _.forEach(['u', 'd'], function (vert) {\n    adjustedLayering = vert === 'u' ? layering : _.values(layering).reverse();\n    _.forEach(['l', 'r'], function (horiz) {\n      if (horiz === 'r') {\n        adjustedLayering = _.map(adjustedLayering, function (inner) {\n          return _.values(inner).reverse();\n        });\n      }\n\n      var neighborFn = (vert === 'u' ? g.predecessors : g.successors).bind(g);\n      var align = verticalAlignment(g, adjustedLayering, conflicts, neighborFn);\n      var xs = horizontalCompaction(g, adjustedLayering, align.root, align.align, horiz === 'r');\n      if (horiz === 'r') {\n        xs = _.mapValues(xs, function (x) {\n          return -x;\n        });\n      }\n      xss[vert + horiz] = xs;\n    });\n  });\n\n  var smallestWidth = findSmallestWidthAlignment(g, xss);\n  alignCoordinates(xss, smallestWidth);\n  return balance(xss, g.graph().align);\n}\n\nfunction sep(nodeSep, edgeSep, reverseSep) {\n  return function (g, v, w) {\n    var vLabel = g.node(v);\n    var wLabel = g.node(w);\n    var sum = 0;\n    var delta;\n\n    sum += vLabel.width / 2;\n    if (Object.prototype.hasOwnProperty.call(vLabel, 'labelpos')) {\n      switch (vLabel.labelpos.toLowerCase()) {\n        case 'l':\n          delta = -vLabel.width / 2;\n          break;\n        case 'r':\n          delta = vLabel.width / 2;\n          break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n\n    sum += (vLabel.dummy ? edgeSep : nodeSep) / 2;\n    sum += (wLabel.dummy ? edgeSep : nodeSep) / 2;\n\n    sum += wLabel.width / 2;\n    if (Object.prototype.hasOwnProperty.call(wLabel, 'labelpos')) {\n      switch (wLabel.labelpos.toLowerCase()) {\n        case 'l':\n          delta = wLabel.width / 2;\n          break;\n        case 'r':\n          delta = -wLabel.width / 2;\n          break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n\n    return sum;\n  };\n}\n\nfunction width(g, v) {\n  return g.node(v).width;\n}\n", "import * as _ from 'lodash-es';\nimport * as util from '../util.js';\nimport { positionX } from './bk.js';\n\nexport { position };\n\nfunction position(g) {\n  g = util.asNonCompoundGraph(g);\n\n  positionY(g);\n  _.forOwn(positionX(g), function (x, v) {\n    g.node(v).x = x;\n  });\n}\n\nfunction positionY(g) {\n  var layering = util.buildLayerMatrix(g);\n  var rankSep = g.graph().ranksep;\n  var prevY = 0;\n  _.forEach(layering, function (layer) {\n    var maxHeight = _.max(\n      _.map(layer, function (v) {\n        return g.node(v).height;\n      }),\n    );\n    _.forEach(layer, function (v) {\n      g.node(v).y = prevY + maxHeight / 2;\n    });\n    prevY += maxHeight + rankSep;\n  });\n}\n", "import * as _ from 'lodash-es';\nimport { Graph } from '../graphlib/index.js';\nimport { addBorderSegments } from './add-border-segments.js';\nimport * as coordinateSystem from './coordinate-system.js';\nimport * as acyclic from './acyclic.js';\nimport * as normalize from './normalize.js';\nimport { rank } from './rank/index.js';\nimport * as nestingGraph from './nesting-graph.js';\nimport { order } from './order/index.js';\nimport { parentDummyChains } from './parent-dummy-chains.js';\nimport { position } from './position/index.js';\nimport * as util from './util.js';\n\nexport { layout };\n\nfunction layout(g, opts) {\n  var time = opts && opts.debugTiming ? util.time : util.notime;\n  time('layout', () => {\n    var layoutGraph = time('  buildLayoutGraph', () => buildLayoutGraph(g));\n    time('  runLayout', () => runLayout(layoutGraph, time));\n    time('  updateInputGraph', () => updateInputGraph(g, layoutGraph));\n  });\n}\n\nfunction runLayout(g, time) {\n  time('    makeSpaceForEdgeLabels', () => makeSpaceForEdgeLabels(g));\n  time('    removeSelfEdges', () => removeSelfEdges(g));\n  time('    acyclic', () => acyclic.run(g));\n  time('    nestingGraph.run', () => nestingGraph.run(g));\n  time('    rank', () => rank(util.asNonCompoundGraph(g)));\n  time('    injectEdgeLabelProxies', () => injectEdgeLabelProxies(g));\n  time('    removeEmptyRanks', () => util.removeEmptyRanks(g));\n  time('    nestingGraph.cleanup', () => nestingGraph.cleanup(g));\n  time('    normalizeRanks', () => util.normalizeRanks(g));\n  time('    assignRankMinMax', () => assignRankMinMax(g));\n  time('    removeEdgeLabelProxies', () => removeEdgeLabelProxies(g));\n  time('    normalize.run', () => normalize.run(g));\n  time('    parentDummyChains', () => parentDummyChains(g));\n  time('    addBorderSegments', () => addBorderSegments(g));\n  time('    order', () => order(g));\n  time('    insertSelfEdges', () => insertSelfEdges(g));\n  time('    adjustCoordinateSystem', () => coordinateSystem.adjust(g));\n  time('    position', () => position(g));\n  time('    positionSelfEdges', () => positionSelfEdges(g));\n  time('    removeBorderNodes', () => removeBorderNodes(g));\n  time('    normalize.undo', () => normalize.undo(g));\n  time('    fixupEdgeLabelCoords', () => fixupEdgeLabelCoords(g));\n  time('    undoCoordinateSystem', () => coordinateSystem.undo(g));\n  time('    translateGraph', () => translateGraph(g));\n  time('    assignNodeIntersects', () => assignNodeIntersects(g));\n  time('    reversePoints', () => reversePointsForReversedEdges(g));\n  time('    acyclic.undo', () => acyclic.undo(g));\n}\n\n/*\n * Copies final layout information from the layout graph back to the input\n * graph. This process only copies whitelisted attributes from the layout graph\n * to the input graph, so it serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction updateInputGraph(inputGraph, layoutGraph) {\n  _.forEach(inputGraph.nodes(), function (v) {\n    var inputLabel = inputGraph.node(v);\n    var layoutLabel = layoutGraph.node(v);\n\n    if (inputLabel) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n\n      if (layoutGraph.children(v).length) {\n        inputLabel.width = layoutLabel.width;\n        inputLabel.height = layoutLabel.height;\n      }\n    }\n  });\n\n  _.forEach(inputGraph.edges(), function (e) {\n    var inputLabel = inputGraph.edge(e);\n    var layoutLabel = layoutGraph.edge(e);\n\n    inputLabel.points = layoutLabel.points;\n    if (Object.prototype.hasOwnProperty.call(layoutLabel, 'x')) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n    }\n  });\n\n  inputGraph.graph().width = layoutGraph.graph().width;\n  inputGraph.graph().height = layoutGraph.graph().height;\n}\n\nvar graphNumAttrs = ['nodesep', 'edgesep', 'ranksep', 'marginx', 'marginy'];\nvar graphDefaults = { ranksep: 50, edgesep: 20, nodesep: 50, rankdir: 'tb' };\nvar graphAttrs = ['acyclicer', 'ranker', 'rankdir', 'align'];\nvar nodeNumAttrs = ['width', 'height'];\nvar nodeDefaults = { width: 0, height: 0 };\nvar edgeNumAttrs = ['minlen', 'weight', 'width', 'height', 'labeloffset'];\nvar edgeDefaults = {\n  minlen: 1,\n  weight: 1,\n  width: 0,\n  height: 0,\n  labeloffset: 10,\n  labelpos: 'r',\n};\nvar edgeAttrs = ['labelpos'];\n\n/*\n * Constructs a new graph from the input graph, which can be used for layout.\n * This process copies only whitelisted attributes from the input graph to the\n * layout graph. Thus this function serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction buildLayoutGraph(inputGraph) {\n  var g = new Graph({ multigraph: true, compound: true });\n  var graph = canonicalize(inputGraph.graph());\n\n  g.setGraph(\n    _.merge({}, graphDefaults, selectNumberAttrs(graph, graphNumAttrs), _.pick(graph, graphAttrs)),\n  );\n\n  _.forEach(inputGraph.nodes(), function (v) {\n    var node = canonicalize(inputGraph.node(v));\n    g.setNode(v, _.defaults(selectNumberAttrs(node, nodeNumAttrs), nodeDefaults));\n    g.setParent(v, inputGraph.parent(v));\n  });\n\n  _.forEach(inputGraph.edges(), function (e) {\n    var edge = canonicalize(inputGraph.edge(e));\n    g.setEdge(\n      e,\n      _.merge({}, edgeDefaults, selectNumberAttrs(edge, edgeNumAttrs), _.pick(edge, edgeAttrs)),\n    );\n  });\n\n  return g;\n}\n\n/*\n * This idea comes from the Gansner paper: to account for edge labels in our\n * layout we split each rank in half by doubling minlen and halving ranksep.\n * Then we can place labels at these mid-points between nodes.\n *\n * We also add some minimal padding to the width to push the label for the edge\n * away from the edge itself a bit.\n */\nfunction makeSpaceForEdgeLabels(g) {\n  var graph = g.graph();\n  graph.ranksep /= 2;\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    edge.minlen *= 2;\n    if (edge.labelpos.toLowerCase() !== 'c') {\n      if (graph.rankdir === 'TB' || graph.rankdir === 'BT') {\n        edge.width += edge.labeloffset;\n      } else {\n        edge.height += edge.labeloffset;\n      }\n    }\n  });\n}\n\n/*\n * Creates temporary dummy nodes that capture the rank in which each edge's\n * label is going to, if it has one of non-zero width and height. We do this\n * so that we can safely remove empty ranks while preserving balance for the\n * label's position.\n */\nfunction injectEdgeLabelProxies(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.width && edge.height) {\n      var v = g.node(e.v);\n      var w = g.node(e.w);\n      var label = { rank: (w.rank - v.rank) / 2 + v.rank, e: e };\n      util.addDummyNode(g, 'edge-proxy', label, '_ep');\n    }\n  });\n}\n\nfunction assignRankMinMax(g) {\n  var maxRank = 0;\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.borderTop) {\n      node.minRank = g.node(node.borderTop).rank;\n      node.maxRank = g.node(node.borderBottom).rank;\n      // @ts-expect-error\n      maxRank = _.max(maxRank, node.maxRank);\n    }\n  });\n  g.graph().maxRank = maxRank;\n}\n\nfunction removeEdgeLabelProxies(g) {\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.dummy === 'edge-proxy') {\n      g.edge(node.e).labelRank = node.rank;\n      g.removeNode(v);\n    }\n  });\n}\n\nfunction translateGraph(g) {\n  var minX = Number.POSITIVE_INFINITY;\n  var maxX = 0;\n  var minY = Number.POSITIVE_INFINITY;\n  var maxY = 0;\n  var graphLabel = g.graph();\n  var marginX = graphLabel.marginx || 0;\n  var marginY = graphLabel.marginy || 0;\n\n  function getExtremes(attrs) {\n    var x = attrs.x;\n    var y = attrs.y;\n    var w = attrs.width;\n    var h = attrs.height;\n    minX = Math.min(minX, x - w / 2);\n    maxX = Math.max(maxX, x + w / 2);\n    minY = Math.min(minY, y - h / 2);\n    maxY = Math.max(maxY, y + h / 2);\n  }\n\n  _.forEach(g.nodes(), function (v) {\n    getExtremes(g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      getExtremes(edge);\n    }\n  });\n\n  minX -= marginX;\n  minY -= marginY;\n\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    node.x -= minX;\n    node.y -= minY;\n  });\n\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, function (p) {\n      p.x -= minX;\n      p.y -= minY;\n    });\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      edge.x -= minX;\n    }\n    if (Object.prototype.hasOwnProperty.call(edge, 'y')) {\n      edge.y -= minY;\n    }\n  });\n\n  graphLabel.width = maxX - minX + marginX;\n  graphLabel.height = maxY - minY + marginY;\n}\n\nfunction assignNodeIntersects(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    var nodeV = g.node(e.v);\n    var nodeW = g.node(e.w);\n    var p1, p2;\n    if (!edge.points) {\n      edge.points = [];\n      p1 = nodeW;\n      p2 = nodeV;\n    } else {\n      p1 = edge.points[0];\n      p2 = edge.points[edge.points.length - 1];\n    }\n    edge.points.unshift(util.intersectRect(nodeV, p1));\n    edge.points.push(util.intersectRect(nodeW, p2));\n  });\n}\n\nfunction fixupEdgeLabelCoords(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (Object.prototype.hasOwnProperty.call(edge, 'x')) {\n      if (edge.labelpos === 'l' || edge.labelpos === 'r') {\n        edge.width -= edge.labeloffset;\n      }\n      switch (edge.labelpos) {\n        case 'l':\n          edge.x -= edge.width / 2 + edge.labeloffset;\n          break;\n        case 'r':\n          edge.x += edge.width / 2 + edge.labeloffset;\n          break;\n      }\n    }\n  });\n}\n\nfunction reversePointsForReversedEdges(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.reversed) {\n      edge.points.reverse();\n    }\n  });\n}\n\nfunction removeBorderNodes(g) {\n  _.forEach(g.nodes(), function (v) {\n    if (g.children(v).length) {\n      var node = g.node(v);\n      var t = g.node(node.borderTop);\n      var b = g.node(node.borderBottom);\n      var l = g.node(_.last(node.borderLeft));\n      var r = g.node(_.last(node.borderRight));\n\n      node.width = Math.abs(r.x - l.x);\n      node.height = Math.abs(b.y - t.y);\n      node.x = l.x + node.width / 2;\n      node.y = t.y + node.height / 2;\n    }\n  });\n\n  _.forEach(g.nodes(), function (v) {\n    if (g.node(v).dummy === 'border') {\n      g.removeNode(v);\n    }\n  });\n}\n\nfunction removeSelfEdges(g) {\n  _.forEach(g.edges(), function (e) {\n    if (e.v === e.w) {\n      var node = g.node(e.v);\n      if (!node.selfEdges) {\n        node.selfEdges = [];\n      }\n      node.selfEdges.push({ e: e, label: g.edge(e) });\n      g.removeEdge(e);\n    }\n  });\n}\n\nfunction insertSelfEdges(g) {\n  var layers = util.buildLayerMatrix(g);\n  _.forEach(layers, function (layer) {\n    var orderShift = 0;\n    _.forEach(layer, function (v, i) {\n      var node = g.node(v);\n      node.order = i + orderShift;\n      _.forEach(node.selfEdges, function (selfEdge) {\n        util.addDummyNode(\n          g,\n          'selfedge',\n          {\n            width: selfEdge.label.width,\n            height: selfEdge.label.height,\n            rank: node.rank,\n            order: i + ++orderShift,\n            e: selfEdge.e,\n            label: selfEdge.label,\n          },\n          '_se',\n        );\n      });\n      delete node.selfEdges;\n    });\n  });\n}\n\nfunction positionSelfEdges(g) {\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.dummy === 'selfedge') {\n      var selfNode = g.node(node.e.v);\n      var x = selfNode.x + selfNode.width / 2;\n      var y = selfNode.y;\n      var dx = node.x - x;\n      var dy = selfNode.height / 2;\n      g.setEdge(node.e, node.label);\n      g.removeNode(v);\n      node.label.points = [\n        { x: x + (2 * dx) / 3, y: y - dy },\n        { x: x + (5 * dx) / 6, y: y - dy },\n        { x: x + dx, y: y },\n        { x: x + (5 * dx) / 6, y: y + dy },\n        { x: x + (2 * dx) / 3, y: y + dy },\n      ];\n      node.label.x = node.x;\n      node.label.y = node.y;\n    }\n  });\n}\n\nfunction selectNumberAttrs(obj, attrs) {\n  return _.mapValues(_.pick(obj, attrs), Number);\n}\n\nfunction canonicalize(attrs) {\n  var newAttrs = {};\n  _.forEach(attrs, function (v, k) {\n    newAttrs[k.toLowerCase()] = v;\n  });\n  return newAttrs;\n}\n"], "mappings": "gWAuBA,SAASA,EAAaC,EAAGC,EAAMC,EAAOC,EAAM,CAC1C,IAAIC,EACJ,GACEA,EAAMC,EAASF,CAAI,QACZH,EAAE,QAAQI,CAAC,GAEpB,OAAAF,EAAM,MAAQD,EACdD,EAAE,QAAQI,EAAGF,CAAK,EACXE,CACT,CATSE,EAAAP,EAAA,gBAeT,SAASQ,GAASP,EAAG,CACnB,IAAIQ,EAAa,IAAIC,EAAM,EAAE,SAAST,EAAE,MAAM,CAAC,EAC/C,OAAEU,EAAQV,EAAE,MAAM,EAAG,SAAUI,EAAG,CAChCI,EAAW,QAAQJ,EAAGJ,EAAE,KAAKI,CAAC,CAAC,CACjC,CAAC,EACCM,EAAQV,EAAE,MAAM,EAAG,SAAUW,EAAG,CAChC,IAAIC,EAAcJ,EAAW,KAAKG,EAAE,EAAGA,EAAE,CAAC,GAAK,CAAE,OAAQ,EAAG,OAAQ,CAAE,EAClEE,EAAQb,EAAE,KAAKW,CAAC,EACpBH,EAAW,QAAQG,EAAE,EAAGA,EAAE,EAAG,CAC3B,OAAQC,EAAY,OAASC,EAAM,OACnC,OAAQ,KAAK,IAAID,EAAY,OAAQC,EAAM,MAAM,CACnD,CAAC,CACH,CAAC,EACML,CACT,CAdSF,EAAAC,GAAA,YAgBT,SAASO,EAAmBd,EAAG,CAC7B,IAAIQ,EAAa,IAAIC,EAAM,CAAE,WAAYT,EAAE,aAAa,CAAE,CAAC,EAAE,SAASA,EAAE,MAAM,CAAC,EAC/E,OAAEU,EAAQV,EAAE,MAAM,EAAG,SAAUI,EAAG,CAC3BJ,EAAE,SAASI,CAAC,EAAE,QACjBI,EAAW,QAAQJ,EAAGJ,EAAE,KAAKI,CAAC,CAAC,CAEnC,CAAC,EACCM,EAAQV,EAAE,MAAM,EAAG,SAAUW,EAAG,CAChCH,EAAW,QAAQG,EAAGX,EAAE,KAAKW,CAAC,CAAC,CACjC,CAAC,EACMH,CACT,CAXSF,EAAAQ,EAAA,sBAuCT,SAASC,EAAcC,EAAMC,EAAO,CAClC,IAAIC,EAAIF,EAAK,EACTG,EAAIH,EAAK,EAITI,EAAKH,EAAM,EAAIC,EACfG,EAAKJ,EAAM,EAAIE,EACfG,EAAIN,EAAK,MAAQ,EACjBO,EAAIP,EAAK,OAAS,EAEtB,GAAI,CAACI,GAAM,CAACC,EACV,MAAM,IAAI,MAAM,2DAA2D,EAG7E,IAAIG,EAAIC,EACR,OAAI,KAAK,IAAIJ,CAAE,EAAIC,EAAI,KAAK,IAAIF,CAAE,EAAIG,GAEhCF,EAAK,IACPE,EAAI,CAACA,GAEPC,EAAMD,EAAIH,EAAMC,EAChBI,EAAKF,IAGDH,EAAK,IACPE,EAAI,CAACA,GAEPE,EAAKF,EACLG,EAAMH,EAAID,EAAMD,GAGX,CAAE,EAAGF,EAAIM,EAAI,EAAGL,EAAIM,CAAG,CAChC,CAjCSC,EAAAX,EAAA,iBAuCT,SAASY,EAAiBC,EAAG,CAC3B,IAAIC,EAAaC,EAAMC,EAAMC,GAAQJ,CAAC,EAAI,CAAC,EAAG,UAAY,CACxD,MAAO,CAAC,CACV,CAAC,EACD,OAAEK,EAAQL,EAAE,MAAM,EAAG,SAAUM,EAAG,CAChC,IAAIC,EAAOP,EAAE,KAAKM,CAAC,EACfE,EAAOD,EAAK,KACTE,EAAYD,CAAI,IACrBP,EAASO,CAAI,EAAED,EAAK,KAAK,EAAID,EAEjC,CAAC,EACML,CACT,CAZSH,EAAAC,EAAA,oBAkBT,SAASW,GAAeV,EAAG,CACzB,IAAIW,EAAQC,EACRV,EAAIF,EAAE,MAAM,EAAG,SAAUM,EAAG,CAC5B,OAAON,EAAE,KAAKM,CAAC,EAAE,IACnB,CAAC,CACH,EACED,EAAQL,EAAE,MAAM,EAAG,SAAUM,EAAG,CAChC,IAAIC,EAAOP,EAAE,KAAKM,CAAC,EACbO,EAAIN,EAAM,MAAM,IACpBA,EAAK,MAAQI,EAEjB,CAAC,CACH,CAZSb,EAAAY,GAAA,kBAcT,SAASI,GAAiBd,EAAG,CAE3B,IAAIe,EAAWH,EACXV,EAAIF,EAAE,MAAM,EAAG,SAAUM,EAAG,CAC5B,OAAON,EAAE,KAAKM,CAAC,EAAE,IACnB,CAAC,CACH,EAEIU,EAAS,CAAC,EACZX,EAAQL,EAAE,MAAM,EAAG,SAAUM,EAAG,CAChC,IAAIE,EAAOR,EAAE,KAAKM,CAAC,EAAE,KAAOS,EACvBC,EAAOR,CAAI,IACdQ,EAAOR,CAAI,EAAI,CAAC,GAElBQ,EAAOR,CAAI,EAAE,KAAKF,CAAC,CACrB,CAAC,EAED,IAAIW,EAAQ,EACRC,EAAiBlB,EAAE,MAAM,EAAE,eAC7BK,EAAQW,EAAQ,SAAUG,EAAI,EAAG,CAC3BV,EAAYU,CAAE,GAAK,EAAID,IAAmB,EAC9C,EAAED,EACOA,GACPZ,EAAQc,EAAI,SAAUb,EAAG,CACzBN,EAAE,KAAKM,CAAC,EAAE,MAAQW,CACpB,CAAC,CAEL,CAAC,CACH,CA5BSnB,EAAAgB,GAAA,oBA8BT,SAASM,GAAcpB,EAAGqB,EAAQb,EAAMc,EAAO,CAC7C,IAAIf,EAAO,CACT,MAAO,EACP,OAAQ,CACV,EACA,OAAI,UAAU,QAAU,IACtBA,EAAK,KAAOC,EACZD,EAAK,MAAQe,GAERC,EAAavB,EAAG,SAAUO,EAAMc,CAAM,CAC/C,CAVSvB,EAAAsB,GAAA,iBAYT,SAAShB,GAAQJ,EAAG,CAClB,OAASwB,EACLtB,EAAIF,EAAE,MAAM,EAAG,SAAUM,EAAG,CAC5B,IAAIE,EAAOR,EAAE,KAAKM,CAAC,EAAE,KACrB,GAAI,CAAGG,EAAYD,CAAI,EACrB,OAAOA,CAEX,CAAC,CACH,CACF,CATSV,EAAAM,GAAA,WAgBT,SAASqB,GAAUC,EAAYC,EAAI,CACjC,IAAIC,EAAS,CAAE,IAAK,CAAC,EAAG,IAAK,CAAC,CAAE,EAChC,OAAEvB,EAAQqB,EAAY,SAAUG,EAAO,CACjCF,EAAGE,CAAK,EACVD,EAAO,IAAI,KAAKC,CAAK,EAErBD,EAAO,IAAI,KAAKC,CAAK,CAEzB,CAAC,EACMD,CACT,CAVS9B,EAAA2B,GAAA,aAgBT,SAASK,GAAKC,EAAMJ,EAAI,CACtB,IAAIK,EAAUC,EAAI,EAClB,GAAI,CACF,OAAON,EAAG,CACZ,QAAE,CACA,QAAQ,IAAII,EAAO,WAAeE,EAAI,EAAID,GAAS,IAAI,CACzD,CACF,CAPSlC,EAAAgC,GAAA,QAST,SAASI,GAAOH,EAAMJ,EAAI,CACxB,OAAOA,EAAG,CACZ,CAFS7B,EAAAoC,GAAA,UClPT,SAASC,GAAkBC,EAAG,CAC5B,SAASC,EAAIC,EAAG,CACd,IAAIC,EAAWH,EAAE,SAASE,CAAC,EACvBE,EAAOJ,EAAE,KAAKE,CAAC,EAKnB,GAJIC,EAAS,QACTE,EAAQF,EAAUF,CAAG,EAGrB,OAAO,UAAU,eAAe,KAAKG,EAAM,SAAS,EAAG,CACzDA,EAAK,WAAa,CAAC,EACnBA,EAAK,YAAc,CAAC,EACpB,QAASE,EAAOF,EAAK,QAASG,EAAUH,EAAK,QAAU,EAAGE,EAAOC,EAAS,EAAED,EAC1EE,GAAcR,EAAG,aAAc,MAAOE,EAAGE,EAAME,CAAI,EACnDE,GAAcR,EAAG,cAAe,MAAOE,EAAGE,EAAME,CAAI,CAExD,CACF,CAfSG,EAAAR,EAAA,OAiBPI,EAAQL,EAAE,SAAS,EAAGC,CAAG,CAC7B,CAnBSQ,EAAAV,GAAA,qBAqBT,SAASS,GAAcR,EAAGU,EAAMC,EAAQC,EAAIC,EAAQP,EAAM,CACxD,IAAIQ,EAAQ,CAAE,MAAO,EAAG,OAAQ,EAAG,KAAMR,EAAM,WAAYI,CAAK,EAC5DK,EAAOF,EAAOH,CAAI,EAAEJ,EAAO,CAAC,EAC5BU,EAAYC,EAAajB,EAAG,SAAUc,EAAOH,CAAM,EACvDE,EAAOH,CAAI,EAAEJ,CAAI,EAAIU,EACrBhB,EAAE,UAAUgB,EAAMJ,CAAE,EAChBG,GACFf,EAAE,QAAQe,EAAMC,EAAM,CAAE,OAAQ,CAAE,CAAC,CAEvC,CATSP,EAAAD,GAAA,iBCtBT,SAASU,GAAOC,EAAG,CACjB,IAAIC,EAAUD,EAAE,MAAM,EAAE,QAAQ,YAAY,GACxCC,IAAY,MAAQA,IAAY,OAClCC,GAAgBF,CAAC,CAErB,CALSG,EAAAJ,GAAA,UAOT,SAASK,GAAKJ,EAAG,CACf,IAAIC,EAAUD,EAAE,MAAM,EAAE,QAAQ,YAAY,GACxCC,IAAY,MAAQA,IAAY,OAClCI,GAASL,CAAC,GAGRC,IAAY,MAAQA,IAAY,QAClCK,GAAON,CAAC,EACRE,GAAgBF,CAAC,EAErB,CAVSG,EAAAC,GAAA,QAYT,SAASF,GAAgBF,EAAG,CACxBO,EAAQP,EAAE,MAAM,EAAG,SAAUQ,EAAG,CAChCC,GAAmBT,EAAE,KAAKQ,CAAC,CAAC,CAC9B,CAAC,EACCD,EAAQP,EAAE,MAAM,EAAG,SAAU,EAAG,CAChCS,GAAmBT,EAAE,KAAK,CAAC,CAAC,CAC9B,CAAC,CACH,CAPSG,EAAAD,GAAA,mBAST,SAASO,GAAmBC,EAAO,CACjC,IAAIC,EAAID,EAAM,MACdA,EAAM,MAAQA,EAAM,OACpBA,EAAM,OAASC,CACjB,CAJSR,EAAAM,GAAA,sBAMT,SAASJ,GAASL,EAAG,CACjBO,EAAQP,EAAE,MAAM,EAAG,SAAUQ,EAAG,CAChCI,GAAYZ,EAAE,KAAKQ,CAAC,CAAC,CACvB,CAAC,EAECD,EAAQP,EAAE,MAAM,EAAG,SAAU,EAAG,CAChC,IAAIa,EAAOb,EAAE,KAAK,CAAC,EACjBO,EAAQM,EAAK,OAAQD,EAAW,EAC9B,OAAO,UAAU,eAAe,KAAKC,EAAM,GAAG,GAChDD,GAAYC,CAAI,CAEpB,CAAC,CACH,CAZSV,EAAAE,GAAA,YAcT,SAASO,GAAYF,EAAO,CAC1BA,EAAM,EAAI,CAACA,EAAM,CACnB,CAFSP,EAAAS,GAAA,eAIT,SAASN,GAAON,EAAG,CACfO,EAAQP,EAAE,MAAM,EAAG,SAAUQ,EAAG,CAChCM,GAAUd,EAAE,KAAKQ,CAAC,CAAC,CACrB,CAAC,EAECD,EAAQP,EAAE,MAAM,EAAG,SAAU,EAAG,CAChC,IAAIa,EAAOb,EAAE,KAAK,CAAC,EACjBO,EAAQM,EAAK,OAAQC,EAAS,EAC5B,OAAO,UAAU,eAAe,KAAKD,EAAM,GAAG,GAChDC,GAAUD,CAAI,CAElB,CAAC,CACH,CAZSV,EAAAG,GAAA,UAcT,SAASQ,GAAUJ,EAAO,CACxB,IAAIK,EAAIL,EAAM,EACdA,EAAM,EAAIA,EAAM,EAChBA,EAAM,EAAIK,CACZ,CAJSZ,EAAAW,GAAA,aC/DT,IAAME,EAAN,KAAW,CAPX,MAOW,CAAAC,EAAA,aACT,aAAc,CACZ,IAAIC,EAAW,CAAC,EAChBA,EAAS,MAAQA,EAAS,MAAQA,EAClC,KAAK,UAAYA,CACnB,CACA,SAAU,CACR,IAAIA,EAAW,KAAK,UAChBC,EAAQD,EAAS,MACrB,GAAIC,IAAUD,EACZ,OAAAE,GAAOD,CAAK,EACLA,CAEX,CACA,QAAQA,EAAO,CACb,IAAID,EAAW,KAAK,UAChBC,EAAM,OAASA,EAAM,OACvBC,GAAOD,CAAK,EAEdA,EAAM,MAAQD,EAAS,MACvBA,EAAS,MAAM,MAAQC,EACvBD,EAAS,MAAQC,EACjBA,EAAM,MAAQD,CAChB,CACA,UAAW,CAIT,QAHIG,EAAO,CAAC,EACRH,EAAW,KAAK,UAChBI,EAAOJ,EAAS,MACbI,IAASJ,GACdG,EAAK,KAAK,KAAK,UAAUC,EAAMC,EAAc,CAAC,EAC9CD,EAAOA,EAAK,MAEd,MAAO,IAAMD,EAAK,KAAK,IAAI,EAAI,GACjC,CACF,EAEA,SAASD,GAAOD,EAAO,CACrBA,EAAM,MAAM,MAAQA,EAAM,MAC1BA,EAAM,MAAM,MAAQA,EAAM,MAC1B,OAAOA,EAAM,MACb,OAAOA,EAAM,KACf,CALSF,EAAAG,GAAA,UAOT,SAASG,GAAeC,EAAGC,EAAG,CAC5B,GAAID,IAAM,SAAWA,IAAM,QACzB,OAAOC,CAEX,CAJSR,EAAAM,GAAA,kBCrCT,IAAIG,GAAsBC,EAAS,CAAC,EAEpC,SAASC,GAAUC,EAAGC,EAAU,CAC9B,GAAID,EAAE,UAAU,GAAK,EACnB,MAAO,CAAC,EAEV,IAAIE,EAAQC,GAAWH,EAAGC,GAAYJ,EAAiB,EACnDO,EAAUC,GAAYH,EAAM,MAAOA,EAAM,QAASA,EAAM,OAAO,EAGnE,OAASI,EACLC,EAAIH,EAAS,SAAUI,EAAG,CAC1B,OAAOR,EAAE,SAASQ,EAAE,EAAGA,EAAE,CAAC,CAC5B,CAAC,CACH,CACF,CAbSC,EAAAV,GAAA,aAeT,SAASM,GAAYL,EAAGU,EAASC,EAAS,CAMxC,QALIP,EAAU,CAAC,EACXQ,EAAUF,EAAQA,EAAQ,OAAS,CAAC,EACpCG,EAAQH,EAAQ,CAAC,EAEjBI,EACGd,EAAE,UAAU,GAAG,CACpB,KAAQc,EAAQD,EAAM,QAAQ,GAC5BE,GAAWf,EAAGU,EAASC,EAASG,CAAK,EAEvC,KAAQA,EAAQF,EAAQ,QAAQ,GAC9BG,GAAWf,EAAGU,EAASC,EAASG,CAAK,EAEvC,GAAId,EAAE,UAAU,GACd,QAASgB,EAAIN,EAAQ,OAAS,EAAGM,EAAI,EAAG,EAAEA,EAExC,GADAF,EAAQJ,EAAQM,CAAC,EAAE,QAAQ,EACvBF,EAAO,CACTV,EAAUA,EAAQ,OAAOW,GAAWf,EAAGU,EAASC,EAASG,EAAO,EAAI,CAAC,EACrE,KACF,EAGN,CAEA,OAAOV,CACT,CAzBSK,EAAAJ,GAAA,eA2BT,SAASU,GAAWf,EAAGU,EAASC,EAASG,EAAOG,EAAqB,CACnE,IAAIb,EAAUa,EAAsB,CAAC,EAAI,OAEzC,OAAEC,EAAQlB,EAAE,QAAQc,EAAM,CAAC,EAAG,SAAUK,EAAM,CAC5C,IAAIC,EAASpB,EAAE,KAAKmB,CAAI,EACpBE,EAASrB,EAAE,KAAKmB,EAAK,CAAC,EAEtBF,GACFb,EAAQ,KAAK,CAAE,EAAGe,EAAK,EAAG,EAAGA,EAAK,CAAE,CAAC,EAGvCE,EAAO,KAAOD,EACdE,GAAaZ,EAASC,EAASU,CAAM,CACvC,CAAC,EAECH,EAAQlB,EAAE,SAASc,EAAM,CAAC,EAAG,SAAUK,EAAM,CAC7C,IAAIC,EAASpB,EAAE,KAAKmB,CAAI,EACpBI,EAAIJ,EAAK,EACTK,EAASxB,EAAE,KAAKuB,CAAC,EACrBC,EAAO,IAASJ,EAChBE,GAAaZ,EAASC,EAASa,CAAM,CACvC,CAAC,EAEDxB,EAAE,WAAWc,EAAM,CAAC,EAEbV,CACT,CA1BSK,EAAAM,GAAA,cA4BT,SAASZ,GAAWH,EAAGC,EAAU,CAC/B,IAAIwB,EAAW,IAAIC,EACfC,EAAQ,EACRC,EAAS,EAEXV,EAAQlB,EAAE,MAAM,EAAG,SAAU6B,EAAG,CAChCJ,EAAS,QAAQI,EAAG,CAAE,EAAGA,EAAG,GAAI,EAAG,IAAK,CAAE,CAAC,CAC7C,CAAC,EAICX,EAAQlB,EAAE,MAAM,EAAG,SAAUQ,EAAG,CAChC,IAAIsB,EAAaL,EAAS,KAAKjB,EAAE,EAAGA,EAAE,CAAC,GAAK,EACxCY,EAASnB,EAASO,CAAC,EACnBuB,EAAaD,EAAaV,EAC9BK,EAAS,QAAQjB,EAAE,EAAGA,EAAE,EAAGuB,CAAU,EACrCH,EAAS,KAAK,IAAIA,EAASH,EAAS,KAAKjB,EAAE,CAAC,EAAE,KAAOY,CAAO,EAC5DO,EAAQ,KAAK,IAAIA,EAAQF,EAAS,KAAKjB,EAAE,CAAC,EAAE,IAASY,CAAO,CAC9D,CAAC,EAED,IAAIV,EAAYsB,EAAMJ,EAASD,EAAQ,CAAC,EAAE,IAAI,UAAY,CACxD,OAAO,IAAIM,CACb,CAAC,EACGtB,EAAUgB,EAAQ,EAEtB,OAAET,EAAQO,EAAS,MAAM,EAAG,SAAUI,EAAG,CACvCP,GAAaZ,EAASC,EAASc,EAAS,KAAKI,CAAC,CAAC,CACjD,CAAC,EAEM,CAAE,MAAOJ,EAAU,QAASf,EAAS,QAASC,CAAQ,CAC/D,CA9BSF,EAAAN,GAAA,cAgCT,SAASmB,GAAaZ,EAASC,EAASG,EAAO,CACxCA,EAAM,IAECA,EAAM,GAGhBJ,EAAQI,EAAM,IAAMA,EAAM,GAAQH,CAAO,EAAE,QAAQG,CAAK,EAFxDJ,EAAQA,EAAQ,OAAS,CAAC,EAAE,QAAQI,CAAK,EAFzCJ,EAAQ,CAAC,EAAE,QAAQI,CAAK,CAM5B,CARSL,EAAAa,GAAA,gBChHT,SAASY,GAAIC,EAAG,CACd,IAAIC,EAAMD,EAAE,MAAM,EAAE,YAAc,SAAWE,GAAUF,EAAGG,EAASH,CAAC,CAAC,EAAII,GAAOJ,CAAC,EAC/EK,EAAQJ,EAAK,SAAUK,EAAG,CAC1B,IAAIC,EAAQP,EAAE,KAAKM,CAAC,EACpBN,EAAE,WAAWM,CAAC,EACdC,EAAM,YAAcD,EAAE,KACtBC,EAAM,SAAW,GACjBP,EAAE,QAAQM,EAAE,EAAGA,EAAE,EAAGC,EAASC,EAAS,KAAK,CAAC,CAC9C,CAAC,EAED,SAASL,EAASH,EAAG,CACnB,OAAO,SAAUM,EAAG,CAClB,OAAON,EAAE,KAAKM,CAAC,EAAE,MACnB,CACF,CAJSG,EAAAN,EAAA,WAKX,CAfSM,EAAAV,GAAA,OAiBT,SAASK,GAAOJ,EAAG,CACjB,IAAIC,EAAM,CAAC,EACPS,EAAQ,CAAC,EACTC,EAAU,CAAC,EAEf,SAASC,EAAIC,EAAG,CACV,OAAO,UAAU,eAAe,KAAKF,EAASE,CAAC,IAGnDF,EAAQE,CAAC,EAAI,GACbH,EAAMG,CAAC,EAAI,GACTR,EAAQL,EAAE,SAASa,CAAC,EAAG,SAAUP,EAAG,CAChC,OAAO,UAAU,eAAe,KAAKI,EAAOJ,EAAE,CAAC,EACjDL,EAAI,KAAKK,CAAC,EAEVM,EAAIN,EAAE,CAAC,CAEX,CAAC,EACD,OAAOI,EAAMG,CAAC,EAChB,CAdS,OAAAJ,EAAAG,EAAA,OAgBPP,EAAQL,EAAE,MAAM,EAAGY,CAAG,EACjBX,CACT,CAvBSQ,EAAAL,GAAA,UAyBT,SAASU,GAAKd,EAAG,CACbK,EAAQL,EAAE,MAAM,EAAG,SAAU,EAAG,CAChC,IAAIO,EAAQP,EAAE,KAAK,CAAC,EACpB,GAAIO,EAAM,SAAU,CAClBP,EAAE,WAAW,CAAC,EAEd,IAAIe,EAAcR,EAAM,YACxB,OAAOA,EAAM,SACb,OAAOA,EAAM,YACbP,EAAE,QAAQ,EAAE,EAAG,EAAE,EAAGO,EAAOQ,CAAW,CACxC,CACF,CAAC,CACH,CAZSN,EAAAK,GAAA,QCrBT,SAASE,GAAIC,EAAG,CACdA,EAAE,MAAM,EAAE,YAAc,CAAC,EACvBC,EAAQD,EAAE,MAAM,EAAG,SAAUE,EAAM,CACnCC,GAAcH,EAAGE,CAAI,CACvB,CAAC,CACH,CALSE,EAAAL,GAAA,OAUT,SAASI,GAAcH,EAAG,EAAG,CAC3B,IAAIK,EAAI,EAAE,EACNC,EAAQN,EAAE,KAAKK,CAAC,EAAE,KAClBE,EAAI,EAAE,EACNC,EAAQR,EAAE,KAAKO,CAAC,EAAE,KAClBE,EAAO,EAAE,KACTC,EAAYV,EAAE,KAAK,CAAC,EACpBW,EAAYD,EAAU,UAE1B,GAAIF,IAAUF,EAAQ,EAEtB,CAAAN,EAAE,WAAW,CAAC,EAcd,IAAIY,EAAQ,OACRC,EAAOC,EACX,IAAKA,EAAI,EAAG,EAAER,EAAOA,EAAQE,EAAO,EAAEM,EAAG,EAAER,EACzCI,EAAU,OAAS,CAAC,EACpBE,EAAQ,CACN,MAAO,EACP,OAAQ,EACR,UAAWF,EACX,QAAS,EACT,KAAMJ,CACR,EACAO,EAAaE,EAAaf,EAAG,OAAQY,EAAO,IAAI,EAC5CN,IAAUK,IACZC,EAAM,MAAQF,EAAU,MACxBE,EAAM,OAASF,EAAU,OACzBE,EAAM,MAAQ,aACdA,EAAM,SAAWF,EAAU,UAE7BV,EAAE,QAAQK,EAAGQ,EAAO,CAAE,OAAQH,EAAU,MAAO,EAAGD,CAAI,EAClDK,IAAM,GACRd,EAAE,MAAM,EAAE,YAAY,KAAKa,CAAK,EAElCR,EAAIQ,EAGNb,EAAE,QAAQK,EAAGE,EAAG,CAAE,OAAQG,EAAU,MAAO,EAAGD,CAAI,EACpD,CAnDSL,EAAAD,GAAA,iBAqDT,SAASa,GAAKhB,EAAG,CACbC,EAAQD,EAAE,MAAM,EAAE,YAAa,SAAUK,EAAG,CAC5C,IAAIY,EAAOjB,EAAE,KAAKK,CAAC,EACfa,EAAYD,EAAK,UACjBV,EAEJ,IADAP,EAAE,QAAQiB,EAAK,QAASC,CAAS,EAC1BD,EAAK,OACVV,EAAIP,EAAE,WAAWK,CAAC,EAAE,CAAC,EACrBL,EAAE,WAAWK,CAAC,EACda,EAAU,OAAO,KAAK,CAAE,EAAGD,EAAK,EAAG,EAAGA,EAAK,CAAE,CAAC,EAC1CA,EAAK,QAAU,eACjBC,EAAU,EAAID,EAAK,EACnBC,EAAU,EAAID,EAAK,EACnBC,EAAU,MAAQD,EAAK,MACvBC,EAAU,OAASD,EAAK,QAE1BZ,EAAIE,EACJU,EAAOjB,EAAE,KAAKK,CAAC,CAEnB,CAAC,CACH,CApBSD,EAAAY,GAAA,QChET,SAASG,EAAYC,EAAG,CACtB,IAAIC,EAAU,CAAC,EAEf,SAASC,EAAIC,EAAG,CACd,IAAIC,EAAQJ,EAAE,KAAKG,CAAC,EACpB,GAAI,OAAO,UAAU,eAAe,KAAKF,EAASE,CAAC,EACjD,OAAOC,EAAM,KAEfH,EAAQE,CAAC,EAAI,GAEb,IAAIE,EAASC,EACTC,EAAIP,EAAE,SAASG,CAAC,EAAG,SAAUK,EAAG,CAChC,OAAON,EAAIM,EAAE,CAAC,EAAIR,EAAE,KAAKQ,CAAC,EAAE,MAC9B,CAAC,CACH,EAEA,OACEH,IAAS,OAAO,mBAChBA,IAAS,QACTA,IAAS,QAGTA,EAAO,GAGDD,EAAM,KAAOC,CACvB,CAvBSI,EAAAP,EAAA,OAyBPQ,EAAQV,EAAE,QAAQ,EAAGE,CAAG,CAC5B,CA7BSO,EAAAV,EAAA,eAmCT,SAASY,EAAMX,EAAG,EAAG,CACnB,OAAOA,EAAE,KAAK,EAAE,CAAC,EAAE,KAAOA,EAAE,KAAK,EAAE,CAAC,EAAE,KAAOA,EAAE,KAAK,CAAC,EAAE,MACzD,CAFSS,EAAAE,EAAA,SC7BT,SAASC,EAAaC,EAAG,CACvB,IAAIC,EAAI,IAAIC,EAAM,CAAE,SAAU,EAAM,CAAC,EAGjCC,EAAQH,EAAE,MAAM,EAAE,CAAC,EACnBI,EAAOJ,EAAE,UAAU,EACvBC,EAAE,QAAQE,EAAO,CAAC,CAAC,EAGnB,QADIE,EAAMC,EACHC,GAAUN,EAAGD,CAAC,EAAII,GACvBC,EAAOG,GAAiBP,EAAGD,CAAC,EAC5BM,EAAQL,EAAE,QAAQI,EAAK,CAAC,EAAII,EAAMT,EAAGK,CAAI,EAAI,CAACI,EAAMT,EAAGK,CAAI,EAC3DK,GAAWT,EAAGD,EAAGM,CAAK,EAGxB,OAAOL,CACT,CAhBSU,EAAAZ,EAAA,gBAsBT,SAASQ,GAAUN,EAAGD,EAAG,CACvB,SAASY,EAAIC,EAAG,CACZC,EAAQd,EAAE,UAAUa,CAAC,EAAG,SAAUE,EAAG,CACrC,IAAIC,EAAQD,EAAE,EACZE,EAAIJ,IAAMG,EAAQD,EAAE,EAAIC,EACtB,CAACf,EAAE,QAAQgB,CAAC,GAAK,CAACR,EAAMT,EAAGe,CAAC,IAC9Bd,EAAE,QAAQgB,EAAG,CAAC,CAAC,EACfhB,EAAE,QAAQY,EAAGI,EAAG,CAAC,CAAC,EAClBL,EAAIK,CAAC,EAET,CAAC,CACH,CAVS,OAAAN,EAAAC,EAAA,OAYPE,EAAQb,EAAE,MAAM,EAAGW,CAAG,EACjBX,EAAE,UAAU,CACrB,CAfSU,EAAAJ,GAAA,aAqBT,SAASC,GAAiBP,EAAGD,EAAG,CAC9B,OAASkB,EAAMlB,EAAE,MAAM,EAAG,SAAUe,EAAG,CACrC,GAAId,EAAE,QAAQc,EAAE,CAAC,IAAMd,EAAE,QAAQc,EAAE,CAAC,EAClC,OAAON,EAAMT,EAAGe,CAAC,CAErB,CAAC,CACH,CANSJ,EAAAH,GAAA,oBAQT,SAASE,GAAWT,EAAGD,EAAGM,EAAO,CAC7BQ,EAAQb,EAAE,MAAM,EAAG,SAAUY,EAAG,CAChCb,EAAE,KAAKa,CAAC,EAAE,MAAQP,CACpB,CAAC,CACH,CAJSK,EAAAD,GAAA,cC7ET,IAAIS,GAAwBC,EAAS,CAAC,ECDtC,IAAIC,GAAwBC,EAAS,CAAC,ECAtCC,GAAQ,eAAiBC,EAEzB,SAASD,GAAQE,EAAG,CAClB,IAAIC,EAAU,CAAC,EACXC,EAAQ,CAAC,EACTC,EAAU,CAAC,EAEf,SAASC,EAAMC,EAAM,CACnB,GAAI,OAAO,UAAU,eAAe,KAAKH,EAAOG,CAAI,EAClD,MAAM,IAAIN,EAGP,OAAO,UAAU,eAAe,KAAKE,EAASI,CAAI,IACrDH,EAAMG,CAAI,EAAI,GACdJ,EAAQI,CAAI,EAAI,GACdC,EAAKN,EAAE,aAAaK,CAAI,EAAGD,CAAK,EAClC,OAAOF,EAAMG,CAAI,EACjBF,EAAQ,KAAKE,CAAI,EAErB,CAIA,GAhBSE,EAAAH,EAAA,SAcPE,EAAKN,EAAE,MAAM,EAAGI,CAAK,EAEjBI,GAAKP,CAAO,IAAMD,EAAE,UAAU,EAClC,MAAM,IAAID,EAGZ,OAAOI,CACT,CA1BSI,EAAAT,GAAA,WA4BT,SAASC,GAAiB,CAAC,CAAlBQ,EAAAR,EAAA,kBACTA,EAAe,UAAY,IAAI,MCvB/B,SAASU,EAAIC,EAAGC,EAAIC,EAAO,CAClBC,GAAQF,CAAE,IACfA,EAAK,CAACA,CAAE,GAGV,IAAIG,GAAcJ,EAAE,WAAW,EAAIA,EAAE,WAAaA,EAAE,WAAW,KAAKA,CAAC,EAEjEK,EAAM,CAAC,EACPC,EAAU,CAAC,EACf,OAAEC,EAAKN,EAAI,SAAUO,EAAG,CACtB,GAAI,CAACR,EAAE,QAAQQ,CAAC,EACd,MAAM,IAAI,MAAM,6BAA+BA,CAAC,EAGlDC,GAAMT,EAAGQ,EAAGN,IAAU,OAAQI,EAASF,EAAYC,CAAG,CACxD,CAAC,EACMA,CACT,CAjBSK,EAAAX,EAAA,OAmBT,SAASU,GAAMT,EAAGQ,EAAGG,EAAWL,EAASF,EAAYC,EAAK,CACnD,OAAO,UAAU,eAAe,KAAKC,EAASE,CAAC,IAClDF,EAAQE,CAAC,EAAI,GAERG,GACHN,EAAI,KAAKG,CAAC,EAEVD,EAAKH,EAAWI,CAAC,EAAG,SAAUI,EAAG,CACjCH,GAAMT,EAAGY,EAAGD,EAAWL,EAASF,EAAYC,CAAG,CACjD,CAAC,EACGM,GACFN,EAAI,KAAKG,CAAC,EAGhB,CAdSE,EAAAD,GAAA,SC3BT,SAASI,GAAUC,EAAGC,EAAI,CACxB,OAAOC,EAAIF,EAAGC,EAAI,MAAM,CAC1B,CAFSE,EAAAJ,GAAA,aCAT,SAASK,GAASC,EAAGC,EAAI,CACvB,OAAOC,EAAIF,EAAGC,EAAI,KAAK,CACzB,CAFSE,EAAAJ,GAAA,YCKTK,EAAe,iBAAmBC,GAClCD,EAAe,cAAgBE,GAC/BF,EAAe,aAAeG,GAC9BH,EAAe,UAAYI,GAC3BJ,EAAe,UAAYK,GAC3BL,EAAe,cAAgBM,GAmC/B,SAASN,EAAeO,EAAG,CACzBA,EAAIC,GAASD,CAAC,EACdE,EAAYF,CAAC,EACb,IAAIG,EAAIC,EAAaJ,CAAC,EACtBN,GAAiBS,CAAC,EAClBR,GAAcQ,EAAGH,CAAC,EAGlB,QADIK,EAAGC,EACCD,EAAIR,GAAUM,CAAC,GACrBG,EAAIR,GAAUK,EAAGH,EAAGK,CAAC,EACrBN,GAAcI,EAAGH,EAAGK,EAAGC,CAAC,CAE5B,CAZSC,EAAAd,EAAA,kBAiBT,SAASE,GAAcQ,EAAGH,EAAG,CAC3B,IAAIQ,EAASC,GAAUN,EAAGA,EAAE,MAAM,CAAC,EACnCK,EAAKA,EAAG,MAAM,EAAGA,EAAG,OAAS,CAAC,EAC5BE,EAAQF,EAAI,SAAUG,EAAG,CACzBC,GAAeT,EAAGH,EAAGW,CAAC,CACxB,CAAC,CACH,CANSJ,EAAAZ,GAAA,iBAQT,SAASiB,GAAeT,EAAGH,EAAGa,EAAO,CACnC,IAAIC,EAAWX,EAAE,KAAKU,CAAK,EACvBE,EAASD,EAAS,OACtBX,EAAE,KAAKU,EAAOE,CAAM,EAAE,SAAWnB,GAAaO,EAAGH,EAAGa,CAAK,CAC3D,CAJSN,EAAAK,GAAA,kBAUT,SAAShB,GAAaO,EAAGH,EAAGa,EAAO,CACjC,IAAIC,EAAWX,EAAE,KAAKU,CAAK,EACvBE,EAASD,EAAS,OAElBE,EAAc,GAEdC,EAAYjB,EAAE,KAAKa,EAAOE,CAAM,EAEhCG,EAAW,EAEf,OAAKD,IACHD,EAAc,GACdC,EAAYjB,EAAE,KAAKe,EAAQF,CAAK,GAGlCK,EAAWD,EAAU,OAEnBP,EAAQV,EAAE,UAAUa,CAAK,EAAG,SAAUR,EAAG,CACzC,IAAIc,EAAYd,EAAE,IAAMQ,EACtBO,EAAQD,EAAYd,EAAE,EAAIA,EAAE,EAE9B,GAAIe,IAAUL,EAAQ,CACpB,IAAIM,EAAeF,IAAcH,EAC/BM,EAActB,EAAE,KAAKK,CAAC,EAAE,OAG1B,GADAa,GAAYG,EAAeC,EAAc,CAACA,EACtCC,GAAWpB,EAAGU,EAAOO,CAAK,EAAG,CAC/B,IAAII,EAAgBrB,EAAE,KAAKU,EAAOO,CAAK,EAAE,SACzCF,GAAYG,EAAe,CAACG,EAAgBA,CAC9C,CACF,CACF,CAAC,EAEMN,CACT,CAlCSX,EAAAX,GAAA,gBAoCT,SAASF,GAAiB+B,EAAMC,EAAM,CAChC,UAAU,OAAS,IACrBA,EAAOD,EAAK,MAAM,EAAE,CAAC,GAEvBE,GAAgBF,EAAM,CAAC,EAAG,EAAGC,CAAI,CACnC,CALSnB,EAAAb,GAAA,oBAOT,SAASiC,GAAgBF,EAAMG,EAASC,EAASlB,EAAGI,EAAQ,CAC1D,IAAIe,EAAMD,EACNE,EAAQN,EAAK,KAAKd,CAAC,EAEvB,OAAAiB,EAAQjB,CAAC,EAAI,GACXD,EAAQe,EAAK,UAAUd,CAAC,EAAG,SAAUqB,EAAG,CACnC,OAAO,UAAU,eAAe,KAAKJ,EAASI,CAAC,IAClDH,EAAUF,GAAgBF,EAAMG,EAASC,EAASG,EAAGrB,CAAC,EAE1D,CAAC,EAEDoB,EAAM,IAAMD,EACZC,EAAM,IAAMF,IACRd,EACFgB,EAAM,OAAShB,EAGf,OAAOgB,EAAM,OAGRF,CACT,CArBStB,EAAAoB,GAAA,mBAuBT,SAAS9B,GAAU4B,EAAM,CACvB,OAASQ,EAAKR,EAAK,MAAM,EAAG,SAAU,EAAG,CACvC,OAAOA,EAAK,KAAK,CAAC,EAAE,SAAW,CACjC,CAAC,CACH,CAJSlB,EAAAV,GAAA,aAMT,SAASC,GAAUK,EAAGH,EAAGkC,EAAM,CAC7B,IAAIvB,EAAIuB,EAAK,EACTF,EAAIE,EAAK,EAKRlC,EAAE,QAAQW,EAAGqB,CAAC,IACjBrB,EAAIuB,EAAK,EACTF,EAAIE,EAAK,GAGX,IAAIC,EAAShC,EAAE,KAAKQ,CAAC,EACjByB,EAASjC,EAAE,KAAK6B,CAAC,EACjBK,EAAYF,EACZG,EAAO,GAIPH,EAAO,IAAMC,EAAO,MACtBC,EAAYD,EACZE,EAAO,IAGT,IAAIC,EAAeC,EAAOxC,EAAE,MAAM,EAAG,SAAUkC,EAAM,CACnD,OACEI,IAASG,GAAatC,EAAGA,EAAE,KAAK+B,EAAK,CAAC,EAAGG,CAAS,GAClDC,IAASG,GAAatC,EAAGA,EAAE,KAAK+B,EAAK,CAAC,EAAGG,CAAS,CAEtD,CAAC,EAED,OAASK,EAAMH,EAAY,SAAUL,EAAM,CACzC,OAAOS,EAAM3C,EAAGkC,CAAI,CACtB,CAAC,CACH,CAlCS3B,EAAAT,GAAA,aAoCT,SAASC,GAAcI,EAAGH,EAAGK,EAAGC,EAAG,CACjC,IAAIK,EAAIN,EAAE,EACN2B,EAAI3B,EAAE,EACVF,EAAE,WAAWQ,EAAGqB,CAAC,EACjB7B,EAAE,QAAQG,EAAE,EAAGA,EAAE,EAAG,CAAC,CAAC,EACtBZ,GAAiBS,CAAC,EAClBR,GAAcQ,EAAGH,CAAC,EAClB4C,GAAYzC,EAAGH,CAAC,CAClB,CARSO,EAAAR,GAAA,iBAUT,SAAS6C,GAAYzC,EAAGH,EAAG,CACzB,IAAI0B,EAASO,EAAK9B,EAAE,MAAM,EAAG,SAAUQ,EAAG,CACxC,MAAO,CAACX,EAAE,KAAKW,CAAC,EAAE,MACpB,CAAC,EACGH,EAASqC,GAAS1C,EAAGuB,CAAI,EAC7BlB,EAAKA,EAAG,MAAM,CAAC,EACbE,EAAQF,EAAI,SAAUG,EAAG,CACzB,IAAII,EAASZ,EAAE,KAAKQ,CAAC,EAAE,OACrBuB,EAAOlC,EAAE,KAAKW,EAAGI,CAAM,EACvB+B,EAAU,GAEPZ,IACHA,EAAOlC,EAAE,KAAKe,EAAQJ,CAAC,EACvBmC,EAAU,IAGZ9C,EAAE,KAAKW,CAAC,EAAE,KAAOX,EAAE,KAAKe,CAAM,EAAE,MAAQ+B,EAAUZ,EAAK,OAAS,CAACA,EAAK,OACxE,CAAC,CACH,CAlBS3B,EAAAqC,GAAA,eAuBT,SAASrB,GAAWE,EAAMsB,EAAGpC,EAAG,CAC9B,OAAOc,EAAK,QAAQsB,EAAGpC,CAAC,CAC1B,CAFSJ,EAAAgB,GAAA,cAQT,SAASkB,GAAahB,EAAMU,EAAQa,EAAW,CAC7C,OAAOA,EAAU,KAAOb,EAAO,KAAOA,EAAO,KAAOa,EAAU,GAChE,CAFSzC,EAAAkC,GAAA,gBChNT,SAASQ,GAAKC,EAAG,CACf,OAAQA,EAAE,MAAM,EAAE,OAAQ,CACxB,IAAK,kBACHC,GAAqBD,CAAC,EACtB,MACF,IAAK,aACHE,GAAgBF,CAAC,EACjB,MACF,IAAK,eACHG,GAAkBH,CAAC,EACnB,MACF,QACEC,GAAqBD,CAAC,CAC1B,CACF,CAdSI,EAAAL,GAAA,QAiBT,IAAII,GAAoBE,EAExB,SAASH,GAAgBF,EAAG,CAC1BK,EAAYL,CAAC,EACbM,EAAaN,CAAC,CAChB,CAHSI,EAAAF,GAAA,mBAKT,SAASD,GAAqBD,EAAG,CAC/BO,EAAeP,CAAC,CAClB,CAFSI,EAAAH,GAAA,wBCrBT,SAASO,GAAIC,EAAG,CACd,IAAIC,EAAYC,EAAaF,EAAG,OAAQ,CAAC,EAAG,OAAO,EAC/CG,EAASC,GAAWJ,CAAC,EACrBK,EAAWC,EAAMC,EAAOJ,CAAM,CAAC,EAAI,EACnCK,EAAU,EAAIH,EAAS,EAE3BL,EAAE,MAAM,EAAE,YAAcC,EAGtBQ,EAAQT,EAAE,MAAM,EAAG,SAAUU,EAAG,CAChCV,EAAE,KAAKU,CAAC,EAAE,QAAUF,CACtB,CAAC,EAGD,IAAIG,EAASC,GAAWZ,CAAC,EAAI,EAG3BS,EAAQT,EAAE,SAAS,EAAG,SAAUa,EAAO,CACvCC,GAAId,EAAGC,EAAMO,EAASG,EAAQN,EAAQF,EAAQU,CAAK,CACrD,CAAC,EAIDb,EAAE,MAAM,EAAE,eAAiBQ,CAC7B,CAxBSO,EAAAhB,GAAA,OA0BT,SAASe,GAAId,EAAGC,EAAMO,EAASG,EAAQN,EAAQF,EAAQa,EAAG,CACxD,IAAIC,EAAWjB,EAAE,SAASgB,CAAC,EAC3B,GAAI,CAACC,EAAS,OAAQ,CAChBD,IAAMf,GACRD,EAAE,QAAQC,EAAMe,EAAG,CAAE,OAAQ,EAAG,OAAQR,CAAQ,CAAC,EAEnD,MACF,CAEA,IAAIU,EAAWC,GAAcnB,EAAG,KAAK,EACjCoB,EAAcD,GAAcnB,EAAG,KAAK,EACpCqB,EAAQrB,EAAE,KAAKgB,CAAC,EAEpBhB,EAAE,UAAUkB,EAAKF,CAAC,EAClBK,EAAM,UAAYH,EAClBlB,EAAE,UAAUoB,EAAQJ,CAAC,EACrBK,EAAM,aAAeD,EAEnBX,EAAQQ,EAAU,SAAUJ,EAAO,CACnCC,GAAId,EAAGC,EAAMO,EAASG,EAAQN,EAAQF,EAAQU,CAAK,EAEnD,IAAIS,EAAYtB,EAAE,KAAKa,CAAK,EACxBU,EAAWD,EAAU,UAAYA,EAAU,UAAYT,EACvDW,EAAcF,EAAU,aAAeA,EAAU,aAAeT,EAChEY,EAAaH,EAAU,UAAYX,EAAS,EAAIA,EAChDe,EAASH,IAAaC,EAAc,EAAInB,EAASF,EAAOa,CAAC,EAAI,EAEjEhB,EAAE,QAAQkB,EAAKK,EAAU,CACvB,OAAQE,EACR,OAAQC,EACR,YAAa,EACf,CAAC,EAED1B,EAAE,QAAQwB,EAAaJ,EAAQ,CAC7B,OAAQK,EACR,OAAQC,EACR,YAAa,EACf,CAAC,CACH,CAAC,EAEI1B,EAAE,OAAOgB,CAAC,GACbhB,EAAE,QAAQC,EAAMiB,EAAK,CAAE,OAAQ,EAAG,OAAQb,EAASF,EAAOa,CAAC,CAAE,CAAC,CAElE,CA3CSD,EAAAD,GAAA,OA6CT,SAASV,GAAWJ,EAAG,CACrB,IAAIG,EAAS,CAAC,EACd,SAASW,EAAIE,EAAGW,EAAO,CACrB,IAAIV,EAAWjB,EAAE,SAASgB,CAAC,EACvBC,GAAYA,EAAS,QACrBR,EAAQQ,EAAU,SAAUJ,EAAO,CACnCC,EAAID,EAAOc,EAAQ,CAAC,CACtB,CAAC,EAEHxB,EAAOa,CAAC,EAAIW,CACd,CARS,OAAAZ,EAAAD,EAAA,OASPL,EAAQT,EAAE,SAAS,EAAG,SAAUgB,EAAG,CACnCF,EAAIE,EAAG,CAAC,CACV,CAAC,EACMb,CACT,CAfSY,EAAAX,GAAA,cAiBT,SAASQ,GAAWZ,EAAG,CACrB,OAAS4B,EACP5B,EAAE,MAAM,EACR,SAAU6B,EAAKnB,EAAG,CAChB,OAAOmB,EAAM7B,EAAE,KAAKU,CAAC,EAAE,MACzB,EACA,CACF,CACF,CARSK,EAAAH,GAAA,cAUT,SAASkB,GAAQ9B,EAAG,CAClB,IAAI+B,EAAa/B,EAAE,MAAM,EACzBA,EAAE,WAAW+B,EAAW,WAAW,EACnC,OAAOA,EAAW,YAChBtB,EAAQT,EAAE,MAAM,EAAG,SAAUU,EAAG,CAChC,IAAIsB,EAAOhC,EAAE,KAAKU,CAAC,EACfsB,EAAK,aACPhC,EAAE,WAAWU,CAAC,CAElB,CAAC,CACH,CAVSK,EAAAe,GAAA,WC1HT,SAASG,GAAuBC,EAAGC,EAAIC,EAAI,CACzC,IAAIC,EAAO,CAAC,EACVC,EAEAC,EAAQH,EAAI,SAAUI,EAAG,CAIzB,QAHIC,EAAQP,EAAE,OAAOM,CAAC,EACpBE,EACAC,EACKF,GAAO,CASZ,GARAC,EAASR,EAAE,OAAOO,CAAK,EACnBC,GACFC,EAAYN,EAAKK,CAAM,EACvBL,EAAKK,CAAM,EAAID,IAEfE,EAAYL,EACZA,EAAWG,GAETE,GAAaA,IAAcF,EAAO,CACpCN,EAAG,QAAQQ,EAAWF,CAAK,EAC3B,MACF,CACAA,EAAQC,CACV,CACF,CAAC,CAyBH,CAhDSE,EAAAX,GAAA,0BC+BT,SAASY,GAAgBC,EAAGC,EAAMC,EAAc,CAC9C,IAAIC,EAAOC,GAAeJ,CAAC,EACzBK,EAAS,IAAIC,EAAM,CAAE,SAAU,EAAK,CAAC,EAClC,SAAS,CAAE,KAAMH,CAAK,CAAC,EACvB,oBAAoB,SAAUI,EAAG,CAChC,OAAOP,EAAE,KAAKO,CAAC,CACjB,CAAC,EAEL,OAAEC,EAAQR,EAAE,MAAM,EAAG,SAAUO,EAAG,CAChC,IAAIE,EAAOT,EAAE,KAAKO,CAAC,EACjBG,EAASV,EAAE,OAAOO,CAAC,GAEjBE,EAAK,OAASR,GAASQ,EAAK,SAAWR,GAAQA,GAAQQ,EAAK,WAC9DJ,EAAO,QAAQE,CAAC,EAChBF,EAAO,UAAUE,EAAGG,GAAUP,CAAI,EAGhCK,EAAQR,EAAEE,CAAY,EAAEK,CAAC,EAAG,SAAUI,EAAG,CACzC,IAAIC,EAAID,EAAE,IAAMJ,EAAII,EAAE,EAAIA,EAAE,EAC1BE,EAAOR,EAAO,KAAKO,EAAGL,CAAC,EACvBO,EAAYC,EAAYF,CAAI,EAAkB,EAAdA,EAAK,OACvCR,EAAO,QAAQO,EAAGL,EAAG,CAAE,OAAQP,EAAE,KAAKW,CAAC,EAAE,OAASG,CAAO,CAAC,CAC5D,CAAC,EAEG,OAAO,UAAU,eAAe,KAAKL,EAAM,SAAS,GACtDJ,EAAO,QAAQE,EAAG,CAChB,WAAYE,EAAK,WAAWR,CAAI,EAChC,YAAaQ,EAAK,YAAYR,CAAI,CACpC,CAAC,EAGP,CAAC,EAEMI,CACT,CAlCSW,EAAAjB,GAAA,mBAoCT,SAASK,GAAeJ,EAAG,CAEzB,QADIO,EACGP,EAAE,QAASO,EAAMU,EAAS,OAAO,CAAE,GAAE,CAC5C,OAAOV,CACT,CAJSS,EAAAZ,GAAA,kBCnDT,SAASc,GAAWC,EAAGC,EAAU,CAE/B,QADIC,EAAK,EACAC,EAAI,EAAGA,EAAIF,EAAS,OAAQ,EAAEE,EACrCD,GAAME,GAAmBJ,EAAGC,EAASE,EAAI,CAAC,EAAGF,EAASE,CAAC,CAAC,EAE1D,OAAOD,CACT,CANSG,EAAAN,GAAA,cAQT,SAASK,GAAmBJ,EAAGM,EAAYC,EAAY,CAuBrD,QAnBIC,EAAaC,EACfF,EACEG,EAAIH,EAAY,SAAUI,EAAGR,EAAG,CAChC,OAAOA,CACT,CAAC,CACH,EACIS,EAAiBC,EACjBH,EAAIJ,EAAY,SAAUK,EAAG,CAC7B,OAASG,EACLJ,EAAIV,EAAE,SAASW,CAAC,EAAG,SAAUI,EAAG,CAChC,MAAO,CAAE,IAAKP,EAASO,EAAE,CAAC,EAAG,OAAQf,EAAE,KAAKe,CAAC,EAAE,MAAO,CACxD,CAAC,EACD,KACF,CACF,CAAC,CACH,EAGIC,EAAa,EACVA,EAAaT,EAAW,QAAQS,IAAe,EACtD,IAAIC,EAAW,EAAID,EAAa,EAChCA,GAAc,EACd,IAAIE,EAASR,EAAI,IAAI,MAAMO,CAAQ,EAAG,UAAY,CAChD,MAAO,EACT,CAAC,EAGGf,EAAK,EACT,OAAEiB,EAEAP,EAAa,QAAQ,SAAUQ,EAAO,CACpC,IAAIC,EAAQD,EAAM,IAAMJ,EACxBE,EAAKG,CAAK,GAAKD,EAAM,OAGrB,QAFIE,EAAY,EAETD,EAAQ,GAETA,EAAQ,IACVC,GAAaJ,EAAKG,EAAQ,CAAC,GAG7BA,EAASA,EAAQ,GAAM,EACvBH,EAAKG,CAAK,GAAKD,EAAM,OAEvBlB,GAAMkB,EAAM,OAASE,CACvB,CAAC,CACH,EAEOpB,CACT,CArDSG,EAAAD,GAAA,sBCfF,SAASmB,GAAUC,EAAG,CAC3B,IAAIC,EAAU,CAAC,EACXC,EAAgBC,EAAOH,EAAE,MAAM,EAAG,SAAUI,EAAG,CACjD,MAAO,CAACJ,EAAE,SAASI,CAAC,EAAE,MACxB,CAAC,EACGC,EAAYC,EACZC,EAAIL,EAAa,SAAUE,EAAG,CAC9B,OAAOJ,EAAE,KAAKI,CAAC,EAAE,IACnB,CAAC,CACH,EACII,EAAWD,EAAME,EAAMJ,EAAU,CAAC,EAAG,UAAY,CACnD,MAAO,CAAC,CACV,CAAC,EAED,SAASK,EAAIN,EAAG,CACd,GAAI,CAAEO,EAAIV,EAASG,CAAC,EACpB,CAAAH,EAAQG,CAAC,EAAI,GACb,IAAIQ,EAAOZ,EAAE,KAAKI,CAAC,EACnBI,EAAOI,EAAK,IAAI,EAAE,KAAKR,CAAC,EACtBS,EAAQb,EAAE,WAAWI,CAAC,EAAGM,CAAG,EAChC,CANSI,EAAAJ,EAAA,OAQT,IAAIK,EAAcC,EAAOd,EAAa,SAAUE,EAAG,CACjD,OAAOJ,EAAE,KAAKI,CAAC,EAAE,IACnB,CAAC,EACD,OAAES,EAAQE,EAAWL,CAAG,EAEjBF,CACT,CA5BgBM,EAAAf,GAAA,aCThB,SAASkB,GAAWC,EAAGC,EAAS,CAC9B,OAASC,EAAID,EAAS,SAAUE,EAAG,CACjC,IAAIC,EAAMJ,EAAE,QAAQG,CAAC,EACrB,GAAKC,EAAI,OAEF,CACL,IAAIC,EAAWC,EACbF,EACA,SAAUG,EAAKC,EAAG,CAChB,IAAIC,EAAOT,EAAE,KAAKQ,CAAC,EACjBE,EAAQV,EAAE,KAAKQ,EAAE,CAAC,EACpB,MAAO,CACL,IAAKD,EAAI,IAAME,EAAK,OAASC,EAAM,MACnC,OAAQH,EAAI,OAASE,EAAK,MAC5B,CACF,EACA,CAAE,IAAK,EAAG,OAAQ,CAAE,CACtB,EAEA,MAAO,CACL,EAAGN,EACH,WAAYE,EAAO,IAAMA,EAAO,OAChC,OAAQA,EAAO,MACjB,CACF,KApBE,OAAO,CAAE,EAAGF,CAAE,CAqBlB,CAAC,CACH,CA1BSQ,EAAAZ,GAAA,cCyBT,SAASa,GAAiBC,EAASC,EAAI,CACrC,IAAIC,EAAgB,CAAC,EACnBC,EAAQH,EAAS,SAAUI,EAAOC,EAAG,CACrC,IAAIC,EAAOJ,EAAcE,EAAM,CAAC,EAAI,CAClC,SAAU,EACV,GAAI,CAAC,EACL,IAAK,CAAC,EACN,GAAI,CAACA,EAAM,CAAC,EACZ,EAAGC,CACL,EACOE,EAAYH,EAAM,UAAU,IAEjCE,EAAI,WAAaF,EAAM,WAEvBE,EAAI,OAASF,EAAM,OAEvB,CAAC,EAECD,EAAQF,EAAG,MAAM,EAAG,SAAUO,EAAG,CACjC,IAAIC,EAASP,EAAcM,EAAE,CAAC,EAC1BE,EAASR,EAAcM,EAAE,CAAC,EAC1B,CAAGD,EAAYE,CAAM,GAAK,CAAGF,EAAYG,CAAM,IACjDA,EAAO,WACPD,EAAO,IAAI,KAAKP,EAAcM,EAAE,CAAC,CAAC,EAEtC,CAAC,EAED,IAAIG,EAAcC,EAAOV,EAAe,SAAUE,EAAO,CAEvD,MAAO,CAACA,EAAM,QAChB,CAAC,EAED,OAAOS,GAAmBF,CAAS,CACrC,CAjCSG,EAAAf,GAAA,oBAmCT,SAASc,GAAmBF,EAAW,CACrC,IAAIX,EAAU,CAAC,EAEf,SAASe,EAASC,EAAQ,CACxB,OAAO,SAAUC,EAAQ,CACnBA,EAAO,SAIPV,EAAYU,EAAO,UAAU,GAC7BV,EAAYS,EAAO,UAAU,GAC/BC,EAAO,YAAcD,EAAO,aAE5BE,GAAaF,EAAQC,CAAM,CAE/B,CACF,CAbSH,EAAAC,EAAA,YAeT,SAASI,EAAUH,EAAQ,CACzB,OAAO,SAAUI,EAAQ,CACvBA,EAAO,GAAM,KAAKJ,CAAM,EACpB,EAAEI,EAAO,WAAa,GACxBT,EAAU,KAAKS,CAAM,CAEzB,CACF,CAEA,IATSN,EAAAK,EAAA,aASFR,EAAU,QAAQ,CACvB,IAAIP,EAAQO,EAAU,IAAI,EAC1BX,EAAQ,KAAKI,CAAK,EAChBD,EAAQC,EAAM,GAAM,QAAQ,EAAGW,EAASX,CAAK,CAAC,EAC9CD,EAAQC,EAAM,IAAKe,EAAUf,CAAK,CAAC,CACvC,CAEA,OAASiB,EACLT,EAAOZ,EAAS,SAAUI,EAAO,CACjC,MAAO,CAACA,EAAM,MAChB,CAAC,EACD,SAAUA,EAAO,CACf,OAASkB,EAAKlB,EAAO,CAAC,KAAM,IAAK,aAAc,QAAQ,CAAC,CAC1D,CACF,CACF,CA1CSU,EAAAD,GAAA,sBA4CT,SAASK,GAAaK,EAAQC,EAAQ,CACpC,IAAIC,EAAM,EACNC,EAAS,EAETH,EAAO,SACTE,GAAOF,EAAO,WAAaA,EAAO,OAClCG,GAAUH,EAAO,QAGfC,EAAO,SACTC,GAAOD,EAAO,WAAaA,EAAO,OAClCE,GAAUF,EAAO,QAGnBD,EAAO,GAAKC,EAAO,GAAG,OAAOD,EAAO,EAAE,EACtCA,EAAO,WAAaE,EAAMC,EAC1BH,EAAO,OAASG,EAChBH,EAAO,EAAI,KAAK,IAAIC,EAAO,EAAGD,EAAO,CAAC,EACtCC,EAAO,OAAS,EAClB,CAnBSV,EAAAI,GAAA,gBCvGT,SAASS,GAAKC,EAASC,EAAW,CAChC,IAAIC,EAAaC,GAAUH,EAAS,SAAUI,EAAO,CACnD,OAAO,OAAO,UAAU,eAAe,KAAKA,EAAO,YAAY,CACjE,CAAC,EACGC,EAAWH,EAAM,IACnBI,EAAeC,EAAOL,EAAM,IAAK,SAAUE,EAAO,CAChD,MAAO,CAACA,EAAM,CAChB,CAAC,EACDI,EAAK,CAAC,EACNC,EAAM,EACNC,EAAS,EACTC,EAAU,EAEZN,EAAS,KAAKO,GAAgB,CAAC,CAACX,CAAS,CAAC,EAE1CU,EAAUE,GAAkBL,EAAIF,EAAYK,CAAO,EAEjDG,EAAQT,EAAU,SAAUD,EAAO,CACnCO,GAAWP,EAAM,GAAG,OACpBI,EAAG,KAAKJ,EAAM,EAAE,EAChBK,GAAOL,EAAM,WAAaA,EAAM,OAChCM,GAAUN,EAAM,OAChBO,EAAUE,GAAkBL,EAAIF,EAAYK,CAAO,CACrD,CAAC,EAED,IAAII,EAAS,CAAE,GAAMC,EAAQR,CAAE,CAAE,EACjC,OAAIE,IACFK,EAAO,WAAaN,EAAMC,EAC1BK,EAAO,OAASL,GAEXK,CACT,CA/BSE,EAAAlB,GAAA,QAiCT,SAASc,GAAkBL,EAAIF,EAAYY,EAAO,CAEhD,QADIC,EACGb,EAAW,SAAWa,EAASC,EAAKd,CAAU,GAAG,GAAKY,GAC3DZ,EAAW,IAAI,EACfE,EAAG,KAAKW,EAAK,EAAE,EACfD,IAEF,OAAOA,CACT,CARSD,EAAAJ,GAAA,qBAUT,SAASD,GAAgBS,EAAM,CAC7B,OAAO,SAAUC,EAAQC,EAAQ,CAC/B,OAAID,EAAO,WAAaC,EAAO,WACtB,GACED,EAAO,WAAaC,EAAO,WAC7B,EAGDF,EAA6BE,EAAO,EAAID,EAAO,EAAxCA,EAAO,EAAIC,EAAO,CACnC,CACF,CAVSN,EAAAL,GAAA,mBCzCT,SAASY,GAAaC,EAAGC,EAAGC,EAAIC,EAAW,CACzC,IAAIC,EAAUJ,EAAE,SAASC,CAAC,EACtBI,EAAOL,EAAE,KAAKC,CAAC,EACfK,EAAKD,EAAOA,EAAK,WAAa,OAC9BE,EAAKF,EAAOA,EAAK,YAAc,OAC/BG,EAAY,CAAC,EAEbF,IACFF,EAAYK,EAAOL,EAAS,SAAUM,EAAG,CACvC,OAAOA,IAAMJ,GAAMI,IAAMH,CAC3B,CAAC,GAGH,IAAII,EAAcC,GAAWZ,EAAGI,CAAO,EACrCS,EAAQF,EAAa,SAAUG,EAAO,CACtC,GAAId,EAAE,SAASc,EAAM,CAAC,EAAE,OAAQ,CAC9B,IAAIC,EAAiBhB,GAAaC,EAAGc,EAAM,EAAGZ,EAAIC,CAAS,EAC3DK,EAAUM,EAAM,CAAC,EAAIC,EACjB,OAAO,UAAU,eAAe,KAAKA,EAAgB,YAAY,GACnEC,GAAiBF,EAAOC,CAAc,CAE1C,CACF,CAAC,EAED,IAAIE,EAAUC,GAAiBP,EAAaT,CAAE,EAC9CiB,GAAgBF,EAAST,CAAS,EAElC,IAAIY,EAASC,GAAKJ,EAASd,CAAS,EAEpC,GAAIG,IACFc,EAAO,GAAOE,EAAQ,CAAChB,EAAIc,EAAO,GAAIb,CAAE,CAAC,EACrCP,EAAE,aAAaM,CAAE,EAAE,QAAQ,CAC7B,IAAIiB,EAASvB,EAAE,KAAKA,EAAE,aAAaM,CAAE,EAAE,CAAC,CAAC,EACvCkB,EAASxB,EAAE,KAAKA,EAAE,aAAaO,CAAE,EAAE,CAAC,CAAC,EAClC,OAAO,UAAU,eAAe,KAAKa,EAAQ,YAAY,IAC5DA,EAAO,WAAa,EACpBA,EAAO,OAAS,GAElBA,EAAO,YACJA,EAAO,WAAaA,EAAO,OAASG,EAAO,MAAQC,EAAO,QAAUJ,EAAO,OAAS,GACvFA,EAAO,QAAU,CACnB,CAGF,OAAOA,CACT,CA7CSK,EAAA1B,GAAA,gBA+CT,SAASoB,GAAgBF,EAAST,EAAW,CACzCK,EAAQI,EAAS,SAAUH,EAAO,CAClCA,EAAM,GAAOQ,EACXR,EAAM,GAAG,IAAI,SAAUb,EAAG,CACxB,OAAIO,EAAUP,CAAC,EACNO,EAAUP,CAAC,EAAE,GAEfA,CACT,CAAC,CACH,CACF,CAAC,CACH,CAXSwB,EAAAN,GAAA,mBAaT,SAASH,GAAiBU,EAAQC,EAAO,CAChCC,EAAYF,EAAO,UAAU,GAMlCA,EAAO,WAAaC,EAAM,WAC1BD,EAAO,OAASC,EAAM,SANtBD,EAAO,YACJA,EAAO,WAAaA,EAAO,OAASC,EAAM,WAAaA,EAAM,SAC7DD,EAAO,OAASC,EAAM,QACzBD,EAAO,QAAUC,EAAM,OAK3B,CAVSF,EAAAT,GAAA,oBCzCT,SAASa,GAAMC,EAAG,CAChB,IAAIC,EAAeA,GAAQD,CAAC,EAC1BE,EAAkBC,GAAiBH,EAAKI,EAAM,EAAGH,EAAU,CAAC,EAAG,SAAS,EACxEI,EAAgBF,GAAiBH,EAAKI,EAAMH,EAAU,EAAG,GAAI,EAAE,EAAG,UAAU,EAE1EK,EAAWC,GAAUP,CAAC,EAC1BQ,GAAYR,EAAGM,CAAQ,EAKvB,QAHIG,EAAS,OAAO,kBAClBC,EAEOC,EAAI,EAAGC,EAAW,EAAGA,EAAW,EAAG,EAAED,EAAG,EAAEC,EAAU,CAC3DC,GAAiBF,EAAI,EAAIT,EAAkBG,EAAeM,EAAI,GAAK,CAAC,EAEpEL,EAAgBQ,EAAiBd,CAAC,EAClC,IAAIe,EAAKC,GAAWhB,EAAGM,CAAQ,EAC3BS,EAAKN,IACPG,EAAW,EACXF,EAASO,GAAUX,CAAQ,EAC3BG,EAASM,EAEb,CAEAP,GAAYR,EAAGU,CAAI,CACrB,CAxBSQ,EAAAnB,GAAA,SA0BT,SAASI,GAAiBH,EAAGmB,EAAOC,EAAc,CAChD,OAASC,EAAIF,EAAO,SAAUG,EAAM,CAClC,OAAOC,GAAgBvB,EAAGsB,EAAMF,CAAY,CAC9C,CAAC,CACH,CAJSF,EAAAf,GAAA,oBAMT,SAASU,GAAiBW,EAAaC,EAAW,CAChD,IAAIC,EAAK,IAAIC,EACXC,EAAQJ,EAAa,SAAUK,EAAI,CACnC,IAAIC,EAAOD,EAAG,MAAM,EAAE,KAClBE,EAASC,GAAaH,EAAIC,EAAMJ,EAAID,CAAS,EAC/CG,EAAQG,EAAO,GAAI,SAAUE,EAAGtB,EAAG,CACnCkB,EAAG,KAAKI,CAAC,EAAE,MAAQtB,CACrB,CAAC,EACDuB,GAAuBL,EAAIH,EAAIK,EAAO,EAAE,CAC1C,CAAC,CACH,CAVSb,EAAAL,GAAA,oBAYT,SAASL,GAAYR,EAAGM,EAAU,CAC9BsB,EAAQtB,EAAU,SAAU6B,EAAO,CACjCP,EAAQO,EAAO,SAAUF,EAAGtB,EAAG,CAC/BX,EAAE,KAAKiC,CAAC,EAAE,MAAQtB,CACpB,CAAC,CACH,CAAC,CACH,CANSO,EAAAV,GAAA,eClET,SAAS4B,GAAkBC,EAAG,CAC5B,IAAIC,EAAgBC,GAAUF,CAAC,EAE7BG,EAAQH,EAAE,MAAM,EAAE,YAAa,SAAUI,EAAG,CAU5C,QATIC,EAAOL,EAAE,KAAKI,CAAC,EACfE,EAAUD,EAAK,QACfE,EAAWC,GAASR,EAAGC,EAAeK,EAAQ,EAAGA,EAAQ,CAAC,EAC1DG,EAAOF,EAAS,KAChBG,EAAMH,EAAS,IACfI,EAAU,EACVC,EAAQH,EAAKE,CAAO,EACpBE,EAAY,GAETT,IAAME,EAAQ,GAAG,CAGtB,GAFAD,EAAOL,EAAE,KAAKI,CAAC,EAEXS,EAAW,CACb,MAAQD,EAAQH,EAAKE,CAAO,KAAOD,GAAOV,EAAE,KAAKY,CAAK,EAAE,QAAUP,EAAK,MACrEM,IAGEC,IAAUF,IACZG,EAAY,GAEhB,CAEA,GAAI,CAACA,EAAW,CACd,KACEF,EAAUF,EAAK,OAAS,GACxBT,EAAE,KAAMY,EAAQH,EAAKE,EAAU,CAAC,CAAE,EAAE,SAAWN,EAAK,MAEpDM,IAEFC,EAAQH,EAAKE,CAAO,CACtB,CAEAX,EAAE,UAAUI,EAAGQ,CAAK,EACpBR,EAAIJ,EAAE,WAAWI,CAAC,EAAE,CAAC,CACvB,CACF,CAAC,CACH,CAxCSU,EAAAf,GAAA,qBA4CT,SAASS,GAASR,EAAGC,EAAeG,EAAGW,EAAG,CACxC,IAAIC,EAAQ,CAAC,EACTC,EAAQ,CAAC,EACTC,EAAM,KAAK,IAAIjB,EAAcG,CAAC,EAAE,IAAKH,EAAcc,CAAC,EAAE,GAAG,EACzDI,EAAM,KAAK,IAAIlB,EAAcG,CAAC,EAAE,IAAKH,EAAcc,CAAC,EAAE,GAAG,EACzDK,EACAV,EAGJU,EAAShB,EACT,GACEgB,EAASpB,EAAE,OAAOoB,CAAM,EACxBJ,EAAM,KAAKI,CAAM,QACVA,IAAWnB,EAAcmB,CAAM,EAAE,IAAMF,GAAOC,EAAMlB,EAAcmB,CAAM,EAAE,MAKnF,IAJAV,EAAMU,EAGNA,EAASL,GACDK,EAASpB,EAAE,OAAOoB,CAAM,KAAOV,GACrCO,EAAM,KAAKG,CAAM,EAGnB,MAAO,CAAE,KAAMJ,EAAM,OAAOC,EAAM,QAAQ,CAAC,EAAG,IAAKP,CAAI,CACzD,CAvBSI,EAAAN,GAAA,YAyBT,SAASN,GAAUF,EAAG,CACpB,IAAIqB,EAAS,CAAC,EACVF,EAAM,EAEV,SAASG,EAAIlB,EAAG,CACd,IAAIc,EAAMC,EACRhB,EAAQH,EAAE,SAASI,CAAC,EAAGkB,CAAG,EAC5BD,EAAOjB,CAAC,EAAI,CAAE,IAAKc,EAAK,IAAKC,GAAM,CACrC,CAJS,OAAAL,EAAAQ,EAAA,OAKPnB,EAAQH,EAAE,SAAS,EAAGsB,CAAG,EAEpBD,CACT,CAZSP,EAAAZ,GAAA,aClCT,SAASqB,GAAmBC,EAAGC,EAAU,CACvC,IAAIC,EAAY,CAAC,EAEjB,SAASC,EAAWC,EAAWC,EAAO,CACpC,IAEEC,EAAK,EAGLC,EAAU,EACVC,EAAkBJ,EAAU,OAC5BK,EAAaC,EAAKL,CAAK,EAEzB,OAAEM,EAAQN,EAAO,SAAUO,EAAGC,EAAG,CAC/B,IAAIC,EAAIC,GAA0Bf,EAAGY,CAAC,EACpCI,EAAKF,EAAId,EAAE,KAAKc,CAAC,EAAE,MAAQN,GAEzBM,GAAKF,IAAMH,KACXE,EAAQN,EAAM,MAAME,EAASM,EAAI,CAAC,EAAG,SAAUI,EAAU,CACvDN,EAAQX,EAAE,aAAaiB,CAAQ,EAAG,SAAUC,EAAG,CAC/C,IAAIC,EAASnB,EAAE,KAAKkB,CAAC,EACnBE,GAAOD,EAAO,OACXC,GAAOd,GAAMU,EAAKI,KAAS,EAAED,EAAO,OAASnB,EAAE,KAAKiB,CAAQ,EAAE,QACjEI,GAAYnB,EAAWgB,EAAGD,CAAQ,CAEtC,CAAC,CACH,CAAC,EAEDV,EAAUM,EAAI,EACdP,EAAKU,EAET,CAAC,EAEMX,CACT,CA/BS,OAAAiB,EAAAnB,EAAA,cAiCPoB,EAAOtB,EAAUE,CAAU,EACtBD,CACT,CAtCSoB,EAAAvB,GAAA,sBAwCT,SAASyB,GAAmBxB,EAAGC,EAAU,CACvC,IAAIC,EAAY,CAAC,EAEjB,SAASuB,EAAKC,EAAOC,EAAUC,EAAUC,EAAiBC,EAAiB,CACzE,IAAIlB,EACFD,EAAUoB,EAAMJ,EAAUC,CAAQ,EAAG,SAAUf,EAAG,CAClDD,EAAIc,EAAMb,CAAC,EACPb,EAAE,KAAKY,CAAC,EAAE,OACVD,EAAQX,EAAE,aAAaY,CAAC,EAAG,SAAUM,EAAG,CACxC,IAAIc,EAAQhC,EAAE,KAAKkB,CAAC,EAChBc,EAAM,QAAUA,EAAM,MAAQH,GAAmBG,EAAM,MAAQF,IACjET,GAAYnB,EAAWgB,EAAGN,CAAC,CAE/B,CAAC,CAEL,CAAC,CACH,CAbSU,EAAAG,EAAA,QAeT,SAAStB,EAAW8B,EAAOP,EAAO,CAChC,IAAIQ,EAAe,GACjBC,EACAR,EAAW,EAEb,OAAEhB,EAAQe,EAAO,SAAUd,EAAGwB,EAAgB,CAC5C,GAAIpC,EAAE,KAAKY,CAAC,EAAE,QAAU,SAAU,CAChC,IAAIyB,EAAerC,EAAE,aAAaY,CAAC,EAC/ByB,EAAa,SACfF,EAAenC,EAAE,KAAKqC,EAAa,CAAC,CAAC,EAAE,MACvCZ,EAAKC,EAAOC,EAAUS,EAAgBF,EAAcC,CAAY,EAEhER,EAAWS,EACXF,EAAeC,EAEnB,CACAV,EAAKC,EAAOC,EAAUD,EAAM,OAAQS,EAAcF,EAAM,MAAM,CAChE,CAAC,EAEMP,CACT,CApBS,OAAAJ,EAAAnB,EAAA,cAsBPoB,EAAOtB,EAAUE,CAAU,EACtBD,CACT,CA1CSoB,EAAAE,GAAA,sBA4CT,SAAST,GAA0Bf,EAAGY,EAAG,CACvC,GAAIZ,EAAE,KAAKY,CAAC,EAAE,MACZ,OAAS0B,EAAKtC,EAAE,aAAaY,CAAC,EAAG,SAAUM,EAAG,CAC5C,OAAOlB,EAAE,KAAKkB,CAAC,EAAE,KACnB,CAAC,CAEL,CANSI,EAAAP,GAAA,6BAQT,SAASM,GAAYnB,EAAWU,EAAGE,EAAG,CACpC,GAAIF,EAAIE,EAAG,CACT,IAAIyB,EAAM3B,EACVA,EAAIE,EACJA,EAAIyB,CACN,CAEA,IAAIC,EAAatC,EAAUU,CAAC,EACvB4B,IACHtC,EAAUU,CAAC,EAAI4B,EAAa,CAAC,GAE/BA,EAAW1B,CAAC,EAAI,EAClB,CAZSQ,EAAAD,GAAA,eAcT,SAASoB,GAAYvC,EAAWU,EAAGE,EAAG,CACpC,GAAIF,EAAIE,EAAG,CACT,IAAIyB,EAAM3B,EACVA,EAAIE,EACJA,EAAIyB,CACN,CACA,MAAO,CAAC,CAACrC,EAAUU,CAAC,GAAK,OAAO,UAAU,eAAe,KAAKV,EAAUU,CAAC,EAAGE,CAAC,CAC/E,CAPSQ,EAAAmB,GAAA,eAiBT,SAASC,GAAkB1C,EAAGC,EAAUC,EAAWyC,EAAY,CAC7D,IAAIC,EAAO,CAAC,EACVC,EAAQ,CAAC,EACTC,EAAM,CAAC,EAKT,OAAEnC,EAAQV,EAAU,SAAUI,EAAO,CACjCM,EAAQN,EAAO,SAAUO,EAAGmC,EAAO,CACnCH,EAAKhC,CAAC,EAAIA,EACViC,EAAMjC,CAAC,EAAIA,EACXkC,EAAIlC,CAAC,EAAImC,CACX,CAAC,CACH,CAAC,EAECpC,EAAQV,EAAU,SAAUI,EAAO,CACnC,IAAI2C,EAAU,GACZrC,EAAQN,EAAO,SAAUO,EAAG,CAC5B,IAAIqC,EAAKN,EAAW/B,CAAC,EACrB,GAAIqC,EAAG,OAAQ,CACbA,EAAOC,EAAOD,EAAI,SAAUnC,EAAG,CAC7B,OAAOgC,EAAIhC,CAAC,CACd,CAAC,EAED,QADIqC,GAAMF,EAAG,OAAS,GAAK,EAClBpC,EAAI,KAAK,MAAMsC,CAAE,EAAGC,EAAK,KAAK,KAAKD,CAAE,EAAGtC,GAAKuC,EAAI,EAAEvC,EAAG,CAC7D,IAAIC,EAAImC,EAAGpC,CAAC,EACRgC,EAAMjC,CAAC,IAAMA,GAAKoC,EAAUF,EAAIhC,CAAC,GAAK,CAAC2B,GAAYvC,EAAWU,EAAGE,CAAC,IACpE+B,EAAM/B,CAAC,EAAIF,EACXiC,EAAMjC,CAAC,EAAIgC,EAAKhC,CAAC,EAAIgC,EAAK9B,CAAC,EAC3BkC,EAAUF,EAAIhC,CAAC,EAEnB,CACF,CACF,CAAC,CACH,CAAC,EAEM,CAAE,KAAM8B,EAAM,MAAOC,CAAM,CACpC,CAtCSvB,EAAAoB,GAAA,qBAwCT,SAASW,GAAqBrD,EAAGC,EAAU2C,EAAMC,EAAOS,EAAY,CAMlE,IAAIC,EAAK,CAAC,EACRC,EAASC,GAAgBzD,EAAGC,EAAU2C,EAAMU,CAAU,EACtDI,EAAaJ,EAAa,aAAe,cAE3C,SAASK,EAAQC,EAAWC,EAAe,CAIzC,QAHIC,EAAQN,EAAO,MAAM,EACrBO,EAAOD,EAAM,IAAI,EACjBE,EAAU,CAAC,EACRD,GACDC,EAAQD,CAAI,EACdH,EAAUG,CAAI,GAEdC,EAAQD,CAAI,EAAI,GAChBD,EAAM,KAAKC,CAAI,EACfD,EAAQA,EAAM,OAAOD,EAAcE,CAAI,CAAC,GAG1CA,EAAOD,EAAM,IAAI,CAErB,CAfSxC,EAAAqC,EAAA,WAkBT,SAASM,EAAMF,EAAM,CACnBR,EAAGQ,CAAI,EAAIP,EAAO,QAAQO,CAAI,EAAE,OAAO,SAAUG,EAAKC,EAAG,CACvD,OAAO,KAAK,IAAID,EAAKX,EAAGY,EAAE,CAAC,EAAIX,EAAO,KAAKW,CAAC,CAAC,CAC/C,EAAG,CAAC,CACN,CAJS7C,EAAA2C,EAAA,SAOT,SAASG,EAAML,EAAM,CACnB,IAAIM,EAAMb,EAAO,SAASO,CAAI,EAAE,OAAO,SAAUG,EAAKC,EAAG,CACvD,OAAO,KAAK,IAAID,EAAKX,EAAGY,EAAE,CAAC,EAAIX,EAAO,KAAKW,CAAC,CAAC,CAC/C,EAAG,OAAO,iBAAiB,EAEvBG,EAAOtE,EAAE,KAAK+D,CAAI,EAClBM,IAAQ,OAAO,mBAAqBC,EAAK,aAAeZ,IAC1DH,EAAGQ,CAAI,EAAI,KAAK,IAAIR,EAAGQ,CAAI,EAAGM,CAAG,EAErC,CATS,OAAA/C,EAAA8C,EAAA,SAWTT,EAAQM,EAAOT,EAAO,aAAa,KAAKA,CAAM,CAAC,EAC/CG,EAAQS,EAAOZ,EAAO,WAAW,KAAKA,CAAM,CAAC,EAG3C7C,EAAQkC,EAAO,SAAUjC,EAAG,CAC5B2C,EAAG3C,CAAC,EAAI2C,EAAGX,EAAKhC,CAAC,CAAC,CACpB,CAAC,EAEM2C,CACT,CAvDSjC,EAAA+B,GAAA,wBAyDT,SAASI,GAAgBzD,EAAGC,EAAU2C,EAAMU,EAAY,CACtD,IAAIiB,EAAa,IAAIC,EACnBC,EAAazE,EAAE,MAAM,EACrB0E,EAAQC,GAAIF,EAAW,QAASA,EAAW,QAASnB,CAAU,EAEhE,OAAE3C,EAAQV,EAAU,SAAUI,EAAO,CACnC,IAAIa,EACFP,EAAQN,EAAO,SAAUO,EAAG,CAC5B,IAAIgE,EAAQhC,EAAKhC,CAAC,EAElB,GADA2D,EAAW,QAAQK,CAAK,EACpB1D,EAAG,CACL,IAAI2D,EAAQjC,EAAK1B,CAAC,EAChB4D,EAAUP,EAAW,KAAKM,EAAOD,CAAK,EACxCL,EAAW,QAAQM,EAAOD,EAAO,KAAK,IAAIF,EAAM1E,EAAGY,EAAGM,CAAC,EAAG4D,GAAW,CAAC,CAAC,CACzE,CACA5D,EAAIN,CACN,CAAC,CACH,CAAC,EAEM2D,CACT,CApBSjD,EAAAmC,GAAA,mBAyBT,SAASsB,GAA2B/E,EAAGgF,EAAK,CAC1C,OAASC,EAAQC,EAAOF,CAAG,EAAG,SAAUzB,EAAI,CAC1C,IAAI4B,EAAM,OAAO,kBACbd,EAAM,OAAO,kBAEjB,OAAEe,GAAM7B,EAAI,SAAU8B,EAAGzE,EAAG,CAC1B,IAAI0E,EAAYC,GAAMvF,EAAGY,CAAC,EAAI,EAE9BuE,EAAM,KAAK,IAAIE,EAAIC,EAAWH,CAAG,EACjCd,EAAM,KAAK,IAAIgB,EAAIC,EAAWjB,CAAG,CACnC,CAAC,EAEMc,EAAMd,CACf,CAAC,CACH,CAdS/C,EAAAyD,GAAA,8BAuBT,SAASS,GAAiBR,EAAKS,EAAS,CACtC,IAAIC,EAAgBR,EAAOO,CAAO,EAChCE,EAAeC,EAAIF,CAAW,EAC9BG,EAAeC,EAAIJ,CAAW,EAE9B/E,EAAQ,CAAC,IAAK,GAAG,EAAG,SAAUoF,EAAM,CAClCpF,EAAQ,CAAC,IAAK,GAAG,EAAG,SAAUqF,EAAO,CACrC,IAAIC,EAAYF,EAAOC,EACrBzC,EAAKyB,EAAIiB,CAAS,EAClBC,EACF,GAAI3C,IAAOkC,EAEX,KAAIU,EAAWjB,EAAO3B,CAAE,EACxB2C,EAAQF,IAAU,IAAML,EAAeC,EAAIO,CAAM,EAAIN,EAAeC,EAAIK,CAAM,EAE1ED,IACFlB,EAAIiB,CAAS,EAAMG,EAAU7C,EAAI,SAAU8B,EAAG,CAC5C,OAAOA,EAAIa,CACb,CAAC,GAEL,CAAC,CACH,CAAC,CACH,CAtBS5E,EAAAkE,GAAA,oBAwBT,SAASa,GAAQrB,EAAKnC,EAAO,CAC3B,OAASuD,EAAUpB,EAAI,GAAI,SAAUsB,EAAQ1F,EAAG,CAC9C,GAAIiC,EACF,OAAOmC,EAAInC,EAAM,YAAY,CAAC,EAAEjC,CAAC,EAEjC,IAAI2C,EAAOL,EAASqD,EAAIvB,EAAKpE,CAAC,CAAC,EAC/B,OAAQ2C,EAAG,CAAC,EAAIA,EAAG,CAAC,GAAK,CAE7B,CAAC,CACH,CATSjC,EAAA+E,GAAA,WAWT,SAASG,GAAUxG,EAAG,CACpB,IAAIC,EAAgBwG,EAAiBzG,CAAC,EAClCE,EAAcwG,EAAM3G,GAAmBC,EAAGC,CAAQ,EAAGuB,GAAmBxB,EAAGC,CAAQ,CAAC,EAEpF+E,EAAM,CAAC,EACP2B,EACFhG,EAAQ,CAAC,IAAK,GAAG,EAAG,SAAUoF,EAAM,CACpCY,EAAmBZ,IAAS,IAAM9F,EAAaiF,EAAOjF,CAAQ,EAAE,QAAQ,EACtEU,EAAQ,CAAC,IAAK,GAAG,EAAG,SAAUqF,EAAO,CACjCA,IAAU,MACZW,EAAqBJ,EAAII,EAAkB,SAAUC,EAAO,CAC1D,OAAS1B,EAAO0B,CAAK,EAAE,QAAQ,CACjC,CAAC,GAGH,IAAIjE,GAAcoD,IAAS,IAAM/F,EAAE,aAAeA,EAAE,YAAY,KAAKA,CAAC,EAClE6C,EAAQH,GAAkB1C,EAAG2G,EAAkBzG,EAAWyC,CAAU,EACpEY,EAAKF,GAAqBrD,EAAG2G,EAAkB9D,EAAM,KAAMA,EAAM,MAAOmD,IAAU,GAAG,EACrFA,IAAU,MACZzC,EAAO6C,EAAU7C,EAAI,SAAU8B,EAAG,CAChC,MAAO,CAACA,CACV,CAAC,GAEHL,EAAIe,EAAOC,CAAK,EAAIzC,CACtB,CAAC,CACH,CAAC,EAED,IAAIsD,EAAgB9B,GAA2B/E,EAAGgF,CAAG,EACrD,OAAAQ,GAAiBR,EAAK6B,CAAa,EAC5BR,GAAQrB,EAAKhF,EAAE,MAAM,EAAE,KAAK,CACrC,CA9BSsB,EAAAkF,GAAA,aAgCT,SAAS7B,GAAImC,EAASC,EAASzD,EAAY,CACzC,OAAO,SAAUtD,EAAGY,EAAGE,EAAG,CACxB,IAAIkG,EAAShH,EAAE,KAAKY,CAAC,EACjBqG,EAASjH,EAAE,KAAKc,CAAC,EACjBoG,EAAM,EACNhB,EAGJ,GADAgB,GAAOF,EAAO,MAAQ,EAClB,OAAO,UAAU,eAAe,KAAKA,EAAQ,UAAU,EACzD,OAAQA,EAAO,SAAS,YAAY,EAAG,CACrC,IAAK,IACHd,EAAQ,CAACc,EAAO,MAAQ,EACxB,MACF,IAAK,IACHd,EAAQc,EAAO,MAAQ,EACvB,KACJ,CAWF,GATId,IACFgB,GAAO5D,EAAa4C,EAAQ,CAACA,GAE/BA,EAAQ,EAERgB,IAAQF,EAAO,MAAQD,EAAUD,GAAW,EAC5CI,IAAQD,EAAO,MAAQF,EAAUD,GAAW,EAE5CI,GAAOD,EAAO,MAAQ,EAClB,OAAO,UAAU,eAAe,KAAKA,EAAQ,UAAU,EACzD,OAAQA,EAAO,SAAS,YAAY,EAAG,CACrC,IAAK,IACHf,EAAQe,EAAO,MAAQ,EACvB,MACF,IAAK,IACHf,EAAQ,CAACe,EAAO,MAAQ,EACxB,KACJ,CAEF,OAAIf,IACFgB,GAAO5D,EAAa4C,EAAQ,CAACA,GAE/BA,EAAQ,EAEDgB,CACT,CACF,CA5CS5F,EAAAqD,GAAA,OA8CT,SAASY,GAAMvF,EAAGY,EAAG,CACnB,OAAOZ,EAAE,KAAKY,CAAC,EAAE,KACnB,CAFSU,EAAAiE,GAAA,SC9ZT,SAAS4B,GAASC,EAAG,CACnBA,EAASC,EAAmBD,CAAC,EAE7BE,GAAUF,CAAC,EACTG,GAAOC,GAAUJ,CAAC,EAAG,SAAUK,EAAGC,EAAG,CACrCN,EAAE,KAAKM,CAAC,EAAE,EAAID,CAChB,CAAC,CACH,CAPSE,EAAAR,GAAA,YAST,SAASG,GAAUF,EAAG,CACpB,IAAIQ,EAAgBC,EAAiBT,CAAC,EAClCU,EAAUV,EAAE,MAAM,EAAE,QACpBW,EAAQ,EACVC,EAAQJ,EAAU,SAAUK,EAAO,CACnC,IAAIC,EAAcC,EACdC,EAAIH,EAAO,SAAUP,EAAG,CACxB,OAAON,EAAE,KAAKM,CAAC,EAAE,MACnB,CAAC,CACH,EACEM,EAAQC,EAAO,SAAUP,EAAG,CAC5BN,EAAE,KAAKM,CAAC,EAAE,EAAIK,EAAQG,EAAY,CACpC,CAAC,EACDH,GAASG,EAAYJ,CACvB,CAAC,CACH,CAfSH,EAAAL,GAAA,aCAT,SAASe,GAAOC,EAAGC,EAAM,CACvB,IAAIC,EAAOD,GAAQA,EAAK,YAAmBC,GAAYC,GACvDD,EAAK,SAAU,IAAM,CACnB,IAAIE,EAAcF,EAAK,qBAAsB,IAAMG,GAAiBL,CAAC,CAAC,EACtEE,EAAK,cAAe,IAAMI,GAAUF,EAAaF,CAAI,CAAC,EACtDA,EAAK,qBAAsB,IAAMK,GAAiBP,EAAGI,CAAW,CAAC,CACnE,CAAC,CACH,CAPSI,EAAAT,GAAA,UAST,SAASO,GAAUN,EAAGE,EAAM,CAC1BA,EAAK,6BAA8B,IAAMO,GAAuBT,CAAC,CAAC,EAClEE,EAAK,sBAAuB,IAAMQ,GAAgBV,CAAC,CAAC,EACpDE,EAAK,cAAe,IAAcS,GAAIX,CAAC,CAAC,EACxCE,EAAK,uBAAwB,IAAmBS,GAAIX,CAAC,CAAC,EACtDE,EAAK,WAAY,IAAMU,GAAUC,EAAmBb,CAAC,CAAC,CAAC,EACvDE,EAAK,6BAA8B,IAAMY,GAAuBd,CAAC,CAAC,EAClEE,EAAK,uBAAwB,IAAWa,GAAiBf,CAAC,CAAC,EAC3DE,EAAK,2BAA4B,IAAmBc,GAAQhB,CAAC,CAAC,EAC9DE,EAAK,qBAAsB,IAAWe,GAAejB,CAAC,CAAC,EACvDE,EAAK,uBAAwB,IAAMgB,GAAiBlB,CAAC,CAAC,EACtDE,EAAK,6BAA8B,IAAMiB,GAAuBnB,CAAC,CAAC,EAClEE,EAAK,oBAAqB,IAAgBS,GAAIX,CAAC,CAAC,EAChDE,EAAK,wBAAyB,IAAMkB,GAAkBpB,CAAC,CAAC,EACxDE,EAAK,wBAAyB,IAAMmB,GAAkBrB,CAAC,CAAC,EACxDE,EAAK,YAAa,IAAMoB,GAAMtB,CAAC,CAAC,EAChCE,EAAK,sBAAuB,IAAMqB,GAAgBvB,CAAC,CAAC,EACpDE,EAAK,6BAA8B,IAAuBsB,GAAOxB,CAAC,CAAC,EACnEE,EAAK,eAAgB,IAAMuB,GAASzB,CAAC,CAAC,EACtCE,EAAK,wBAAyB,IAAMwB,GAAkB1B,CAAC,CAAC,EACxDE,EAAK,wBAAyB,IAAMyB,GAAkB3B,CAAC,CAAC,EACxDE,EAAK,qBAAsB,IAAgB0B,GAAK5B,CAAC,CAAC,EAClDE,EAAK,2BAA4B,IAAM2B,GAAqB7B,CAAC,CAAC,EAC9DE,EAAK,2BAA4B,IAAuB0B,GAAK5B,CAAC,CAAC,EAC/DE,EAAK,qBAAsB,IAAM4B,GAAe9B,CAAC,CAAC,EAClDE,EAAK,2BAA4B,IAAM6B,GAAqB/B,CAAC,CAAC,EAC9DE,EAAK,oBAAqB,IAAM8B,GAA8BhC,CAAC,CAAC,EAChEE,EAAK,mBAAoB,IAAc0B,GAAK5B,CAAC,CAAC,CAChD,CA5BSQ,EAAAF,GAAA,aAoCT,SAASC,GAAiB0B,EAAY7B,EAAa,CAC/C8B,EAAQD,EAAW,MAAM,EAAG,SAAUE,EAAG,CACzC,IAAIC,EAAaH,EAAW,KAAKE,CAAC,EAC9BE,EAAcjC,EAAY,KAAK+B,CAAC,EAEhCC,IACFA,EAAW,EAAIC,EAAY,EAC3BD,EAAW,EAAIC,EAAY,EAEvBjC,EAAY,SAAS+B,CAAC,EAAE,SAC1BC,EAAW,MAAQC,EAAY,MAC/BD,EAAW,OAASC,EAAY,QAGtC,CAAC,EAECH,EAAQD,EAAW,MAAM,EAAG,SAAUK,EAAG,CACzC,IAAIF,EAAaH,EAAW,KAAKK,CAAC,EAC9BD,EAAcjC,EAAY,KAAKkC,CAAC,EAEpCF,EAAW,OAASC,EAAY,OAC5B,OAAO,UAAU,eAAe,KAAKA,EAAa,GAAG,IACvDD,EAAW,EAAIC,EAAY,EAC3BD,EAAW,EAAIC,EAAY,EAE/B,CAAC,EAEDJ,EAAW,MAAM,EAAE,MAAQ7B,EAAY,MAAM,EAAE,MAC/C6B,EAAW,MAAM,EAAE,OAAS7B,EAAY,MAAM,EAAE,MAClD,CA7BSI,EAAAD,GAAA,oBA+BT,IAAIgC,GAAgB,CAAC,UAAW,UAAW,UAAW,UAAW,SAAS,EACtEC,GAAgB,CAAE,QAAS,GAAI,QAAS,GAAI,QAAS,GAAI,QAAS,IAAK,EACvEC,GAAa,CAAC,YAAa,SAAU,UAAW,OAAO,EACvDC,GAAe,CAAC,QAAS,QAAQ,EACjCC,GAAe,CAAE,MAAO,EAAG,OAAQ,CAAE,EACrCC,GAAe,CAAC,SAAU,SAAU,QAAS,SAAU,aAAa,EACpEC,GAAe,CACjB,OAAQ,EACR,OAAQ,EACR,MAAO,EACP,OAAQ,EACR,YAAa,GACb,SAAU,GACZ,EACIC,GAAY,CAAC,UAAU,EAQ3B,SAASzC,GAAiB4B,EAAY,CACpC,IAAIjC,EAAI,IAAI+C,EAAM,CAAE,WAAY,GAAM,SAAU,EAAK,CAAC,EAClDC,EAAQC,GAAahB,EAAW,MAAM,CAAC,EAE3C,OAAAjC,EAAE,SACEkD,EAAM,CAAC,EAAGV,GAAeW,GAAkBH,EAAOT,EAAa,EAAKa,EAAKJ,EAAOP,EAAU,CAAC,CAC/F,EAEEP,EAAQD,EAAW,MAAM,EAAG,SAAUE,EAAG,CACzC,IAAIkB,EAAOJ,GAAahB,EAAW,KAAKE,CAAC,CAAC,EAC1CnC,EAAE,QAAQmC,EAAKmB,GAASH,GAAkBE,EAAMX,EAAY,EAAGC,EAAY,CAAC,EAC5E3C,EAAE,UAAUmC,EAAGF,EAAW,OAAOE,CAAC,CAAC,CACrC,CAAC,EAECD,EAAQD,EAAW,MAAM,EAAG,SAAUK,EAAG,CACzC,IAAIiB,EAAON,GAAahB,EAAW,KAAKK,CAAC,CAAC,EAC1CtC,EAAE,QACAsC,EACEY,EAAM,CAAC,EAAGL,GAAcM,GAAkBI,EAAMX,EAAY,EAAKQ,EAAKG,EAAMT,EAAS,CAAC,CAC1F,CACF,CAAC,EAEM9C,CACT,CAvBSQ,EAAAH,GAAA,oBAiCT,SAASI,GAAuBT,EAAG,CACjC,IAAIgD,EAAQhD,EAAE,MAAM,EACpBgD,EAAM,SAAW,EACfd,EAAQlC,EAAE,MAAM,EAAG,SAAUsC,EAAG,CAChC,IAAIiB,EAAOvD,EAAE,KAAKsC,CAAC,EACnBiB,EAAK,QAAU,EACXA,EAAK,SAAS,YAAY,IAAM,MAC9BP,EAAM,UAAY,MAAQA,EAAM,UAAY,KAC9CO,EAAK,OAASA,EAAK,YAEnBA,EAAK,QAAUA,EAAK,YAG1B,CAAC,CACH,CAdS/C,EAAAC,GAAA,0BAsBT,SAASK,GAAuBd,EAAG,CAC/BkC,EAAQlC,EAAE,MAAM,EAAG,SAAU,EAAG,CAChC,IAAIuD,EAAOvD,EAAE,KAAK,CAAC,EACnB,GAAIuD,EAAK,OAASA,EAAK,OAAQ,CAC7B,IAAIpB,EAAInC,EAAE,KAAK,EAAE,CAAC,EACdwD,EAAIxD,EAAE,KAAK,EAAE,CAAC,EACdyD,EAAQ,CAAE,MAAOD,EAAE,KAAOrB,EAAE,MAAQ,EAAIA,EAAE,KAAM,CAAK,EACpDuB,EAAa1D,EAAG,aAAcyD,EAAO,KAAK,CACjD,CACF,CAAC,CACH,CAVSjD,EAAAM,GAAA,0BAYT,SAASI,GAAiBlB,EAAG,CAC3B,IAAI2D,EAAU,EACZzB,EAAQlC,EAAE,MAAM,EAAG,SAAUmC,EAAG,CAChC,IAAIkB,EAAOrD,EAAE,KAAKmC,CAAC,EACfkB,EAAK,YACPA,EAAK,QAAUrD,EAAE,KAAKqD,EAAK,SAAS,EAAE,KACtCA,EAAK,QAAUrD,EAAE,KAAKqD,EAAK,YAAY,EAAE,KAEzCM,EAAYC,EAAID,EAASN,EAAK,OAAO,EAEzC,CAAC,EACDrD,EAAE,MAAM,EAAE,QAAU2D,CACtB,CAZSnD,EAAAU,GAAA,oBAcT,SAASC,GAAuBnB,EAAG,CAC/BkC,EAAQlC,EAAE,MAAM,EAAG,SAAUmC,EAAG,CAChC,IAAIkB,EAAOrD,EAAE,KAAKmC,CAAC,EACfkB,EAAK,QAAU,eACjBrD,EAAE,KAAKqD,EAAK,CAAC,EAAE,UAAYA,EAAK,KAChCrD,EAAE,WAAWmC,CAAC,EAElB,CAAC,CACH,CARS3B,EAAAW,GAAA,0BAUT,SAASW,GAAe9B,EAAG,CACzB,IAAI6D,EAAO,OAAO,kBACdC,EAAO,EACPC,EAAO,OAAO,kBACdC,EAAO,EACPC,EAAajE,EAAE,MAAM,EACrBkE,EAAUD,EAAW,SAAW,EAChCE,EAAUF,EAAW,SAAW,EAEpC,SAASG,EAAYC,EAAO,CAC1B,IAAIC,EAAID,EAAM,EACVE,EAAIF,EAAM,EACVb,EAAIa,EAAM,MACVG,EAAIH,EAAM,OACdR,EAAO,KAAK,IAAIA,EAAMS,EAAId,EAAI,CAAC,EAC/BM,EAAO,KAAK,IAAIA,EAAMQ,EAAId,EAAI,CAAC,EAC/BO,EAAO,KAAK,IAAIA,EAAMQ,EAAIC,EAAI,CAAC,EAC/BR,EAAO,KAAK,IAAIA,EAAMO,EAAIC,EAAI,CAAC,CACjC,CATShE,EAAA4D,EAAA,eAWPlC,EAAQlC,EAAE,MAAM,EAAG,SAAUmC,EAAG,CAChCiC,EAAYpE,EAAE,KAAKmC,CAAC,CAAC,CACvB,CAAC,EACCD,EAAQlC,EAAE,MAAM,EAAG,SAAUsC,EAAG,CAChC,IAAIiB,EAAOvD,EAAE,KAAKsC,CAAC,EACf,OAAO,UAAU,eAAe,KAAKiB,EAAM,GAAG,GAChDa,EAAYb,CAAI,CAEpB,CAAC,EAEDM,GAAQK,EACRH,GAAQI,EAENjC,EAAQlC,EAAE,MAAM,EAAG,SAAUmC,EAAG,CAChC,IAAIkB,EAAOrD,EAAE,KAAKmC,CAAC,EACnBkB,EAAK,GAAKQ,EACVR,EAAK,GAAKU,CACZ,CAAC,EAEC7B,EAAQlC,EAAE,MAAM,EAAG,SAAUsC,EAAG,CAChC,IAAIiB,EAAOvD,EAAE,KAAKsC,CAAC,EACjBJ,EAAQqB,EAAK,OAAQ,SAAUkB,EAAG,CAClCA,EAAE,GAAKZ,EACPY,EAAE,GAAKV,CACT,CAAC,EACG,OAAO,UAAU,eAAe,KAAKR,EAAM,GAAG,IAChDA,EAAK,GAAKM,GAER,OAAO,UAAU,eAAe,KAAKN,EAAM,GAAG,IAChDA,EAAK,GAAKQ,EAEd,CAAC,EAEDE,EAAW,MAAQH,EAAOD,EAAOK,EACjCD,EAAW,OAASD,EAAOD,EAAOI,CACpC,CAvDS3D,EAAAsB,GAAA,kBAyDT,SAASC,GAAqB/B,EAAG,CAC7BkC,EAAQlC,EAAE,MAAM,EAAG,SAAU,EAAG,CAChC,IAAIuD,EAAOvD,EAAE,KAAK,CAAC,EACf0E,EAAQ1E,EAAE,KAAK,EAAE,CAAC,EAClB2E,EAAQ3E,EAAE,KAAK,EAAE,CAAC,EAClB4E,EAAIC,EACHtB,EAAK,QAKRqB,EAAKrB,EAAK,OAAO,CAAC,EAClBsB,EAAKtB,EAAK,OAAOA,EAAK,OAAO,OAAS,CAAC,IALvCA,EAAK,OAAS,CAAC,EACfqB,EAAKD,EACLE,EAAKH,GAKPnB,EAAK,OAAO,QAAauB,EAAcJ,EAAOE,CAAE,CAAC,EACjDrB,EAAK,OAAO,KAAUuB,EAAcH,EAAOE,CAAE,CAAC,CAChD,CAAC,CACH,CAjBSrE,EAAAuB,GAAA,wBAmBT,SAASF,GAAqB7B,EAAG,CAC7BkC,EAAQlC,EAAE,MAAM,EAAG,SAAU,EAAG,CAChC,IAAIuD,EAAOvD,EAAE,KAAK,CAAC,EACnB,GAAI,OAAO,UAAU,eAAe,KAAKuD,EAAM,GAAG,EAIhD,QAHIA,EAAK,WAAa,KAAOA,EAAK,WAAa,OAC7CA,EAAK,OAASA,EAAK,aAEbA,EAAK,SAAU,CACrB,IAAK,IACHA,EAAK,GAAKA,EAAK,MAAQ,EAAIA,EAAK,YAChC,MACF,IAAK,IACHA,EAAK,GAAKA,EAAK,MAAQ,EAAIA,EAAK,YAChC,KACJ,CAEJ,CAAC,CACH,CAjBS/C,EAAAqB,GAAA,wBAmBT,SAASG,GAA8BhC,EAAG,CACtCkC,EAAQlC,EAAE,MAAM,EAAG,SAAU,EAAG,CAChC,IAAIuD,EAAOvD,EAAE,KAAK,CAAC,EACfuD,EAAK,UACPA,EAAK,OAAO,QAAQ,CAExB,CAAC,CACH,CAPS/C,EAAAwB,GAAA,iCAST,SAASL,GAAkB3B,EAAG,CAC1BkC,EAAQlC,EAAE,MAAM,EAAG,SAAUmC,EAAG,CAChC,GAAInC,EAAE,SAASmC,CAAC,EAAE,OAAQ,CACxB,IAAIkB,EAAOrD,EAAE,KAAKmC,CAAC,EACf,EAAInC,EAAE,KAAKqD,EAAK,SAAS,EACzB0B,EAAI/E,EAAE,KAAKqD,EAAK,YAAY,EAC5B2B,EAAIhF,EAAE,KAAOiF,EAAK5B,EAAK,UAAU,CAAC,EAClC6B,EAAIlF,EAAE,KAAOiF,EAAK5B,EAAK,WAAW,CAAC,EAEvCA,EAAK,MAAQ,KAAK,IAAI6B,EAAE,EAAIF,EAAE,CAAC,EAC/B3B,EAAK,OAAS,KAAK,IAAI0B,EAAE,EAAI,EAAE,CAAC,EAChC1B,EAAK,EAAI2B,EAAE,EAAI3B,EAAK,MAAQ,EAC5BA,EAAK,EAAI,EAAE,EAAIA,EAAK,OAAS,CAC/B,CACF,CAAC,EAECnB,EAAQlC,EAAE,MAAM,EAAG,SAAUmC,EAAG,CAC5BnC,EAAE,KAAKmC,CAAC,EAAE,QAAU,UACtBnC,EAAE,WAAWmC,CAAC,CAElB,CAAC,CACH,CArBS3B,EAAAmB,GAAA,qBAuBT,SAASjB,GAAgBV,EAAG,CACxBkC,EAAQlC,EAAE,MAAM,EAAG,SAAU,EAAG,CAChC,GAAI,EAAE,IAAM,EAAE,EAAG,CACf,IAAIqD,EAAOrD,EAAE,KAAK,EAAE,CAAC,EAChBqD,EAAK,YACRA,EAAK,UAAY,CAAC,GAEpBA,EAAK,UAAU,KAAK,CAAE,EAAM,MAAOrD,EAAE,KAAK,CAAC,CAAE,CAAC,EAC9CA,EAAE,WAAW,CAAC,CAChB,CACF,CAAC,CACH,CAXSQ,EAAAE,GAAA,mBAaT,SAASa,GAAgBvB,EAAG,CAC1B,IAAImF,EAAcC,EAAiBpF,CAAC,EAClCkC,EAAQiD,EAAQ,SAAUE,EAAO,CACjC,IAAIC,EAAa,EACfpD,EAAQmD,EAAO,SAAUlD,EAAGoD,EAAG,CAC/B,IAAIlC,EAAOrD,EAAE,KAAKmC,CAAC,EACnBkB,EAAK,MAAQkC,EAAID,EACfpD,EAAQmB,EAAK,UAAW,SAAUmC,EAAU,CACvC9B,EACH1D,EACA,WACA,CACE,MAAOwF,EAAS,MAAM,MACtB,OAAQA,EAAS,MAAM,OACvB,KAAMnC,EAAK,KACX,MAAOkC,GAAI,EAAED,EACb,EAAGE,EAAS,EACZ,MAAOA,EAAS,KAClB,EACA,KACF,CACF,CAAC,EACD,OAAOnC,EAAK,SACd,CAAC,CACH,CAAC,CACH,CAzBS7C,EAAAe,GAAA,mBA2BT,SAASG,GAAkB1B,EAAG,CAC1BkC,EAAQlC,EAAE,MAAM,EAAG,SAAUmC,EAAG,CAChC,IAAIkB,EAAOrD,EAAE,KAAKmC,CAAC,EACnB,GAAIkB,EAAK,QAAU,WAAY,CAC7B,IAAIoC,EAAWzF,EAAE,KAAKqD,EAAK,EAAE,CAAC,EAC1BiB,EAAImB,EAAS,EAAIA,EAAS,MAAQ,EAClClB,EAAIkB,EAAS,EACbC,EAAKrC,EAAK,EAAIiB,EACdqB,EAAKF,EAAS,OAAS,EAC3BzF,EAAE,QAAQqD,EAAK,EAAGA,EAAK,KAAK,EAC5BrD,EAAE,WAAWmC,CAAC,EACdkB,EAAK,MAAM,OAAS,CAClB,CAAE,EAAGiB,EAAK,EAAIoB,EAAM,EAAG,EAAGnB,EAAIoB,CAAG,EACjC,CAAE,EAAGrB,EAAK,EAAIoB,EAAM,EAAG,EAAGnB,EAAIoB,CAAG,EACjC,CAAE,EAAGrB,EAAIoB,EAAI,EAAGnB,CAAE,EAClB,CAAE,EAAGD,EAAK,EAAIoB,EAAM,EAAG,EAAGnB,EAAIoB,CAAG,EACjC,CAAE,EAAGrB,EAAK,EAAIoB,EAAM,EAAG,EAAGnB,EAAIoB,CAAG,CACnC,EACAtC,EAAK,MAAM,EAAIA,EAAK,EACpBA,EAAK,MAAM,EAAIA,EAAK,CACtB,CACF,CAAC,CACH,CAtBS7C,EAAAkB,GAAA,qBAwBT,SAASyB,GAAkByC,EAAKvB,EAAO,CACrC,OAASwB,EAAYzC,EAAKwC,EAAKvB,CAAK,EAAG,MAAM,CAC/C,CAFS7D,EAAA2C,GAAA,qBAIT,SAASF,GAAaoB,EAAO,CAC3B,IAAIyB,EAAW,CAAC,EAChB,OAAE5D,EAAQmC,EAAO,SAAUlC,EAAG4D,EAAG,CAC/BD,EAASC,EAAE,YAAY,CAAC,EAAI5D,CAC9B,CAAC,EACM2D,CACT,CANStF,EAAAyC,GAAA", "names": ["addDummyNode", "g", "type", "attrs", "name", "v", "uniqueId_default", "__name", "simplify", "simplified", "Graph", "forEach_default", "e", "simpleLabel", "label", "asNonCompoundGraph", "intersectRect", "rect", "point", "x", "y", "dx", "dy", "w", "h", "sx", "sy", "__name", "buildLayerMatrix", "g", "layering", "map_default", "range_default", "maxRank", "forEach_default", "v", "node", "rank", "isUndefined_default", "normalizeRanks", "min", "min_default", "has_default", "removeEmptyRanks", "offset", "layers", "delta", "nodeRankFactor", "vs", "addBorderNode", "prefix", "order", "addDummyNode", "max_default", "partition", "collection", "fn", "result", "value", "time", "name", "start", "now_default", "notime", "addBorderSegments", "g", "dfs", "v", "children", "node", "forEach_default", "rank", "maxRank", "addBorderNode", "__name", "prop", "prefix", "sg", "sgNode", "label", "prev", "curr", "addDummyNode", "adjust", "g", "rankDir", "swapWidthHeight", "__name", "undo", "reverseY", "swapXY", "forEach_default", "v", "swapWidthHeightOne", "attrs", "w", "reverseYOne", "edge", "swapXYOne", "x", "List", "__name", "sentinel", "entry", "unlink", "strs", "curr", "filterOutLinks", "k", "v", "DEFAULT_WEIGHT_FN", "constant_default", "greedyFAS", "g", "weightFn", "state", "buildState", "results", "doGreedyFAS", "flatten_default", "map_default", "e", "__name", "buckets", "zeroIdx", "sources", "sinks", "entry", "removeNode", "i", "collectPredecessors", "forEach_default", "edge", "weight", "uEntry", "assignBucket", "w", "wEntry", "fasGraph", "Graph", "maxIn", "maxOut", "v", "prevWeight", "edgeWeight", "range_default", "List", "run", "g", "fas", "greedyFAS", "weightFn", "dfsFAS", "forEach_default", "e", "label", "uniqueId_default", "__name", "stack", "visited", "dfs", "v", "undo", "<PERSON><PERSON><PERSON>", "run", "g", "forEach_default", "edge", "normalizeEdge", "__name", "v", "vRank", "w", "wRank", "name", "edgeLabel", "labelRank", "attrs", "dummy", "i", "addDummyNode", "undo", "node", "origLabel", "longestPath", "g", "visited", "dfs", "v", "label", "rank", "min_default", "map_default", "e", "__name", "forEach_default", "slack", "feasibleTree", "g", "t", "Graph", "start", "size", "edge", "delta", "tightTree", "find<PERSON>in<PERSON>lack<PERSON>dge", "slack", "shiftRanks", "__name", "dfs", "v", "forEach_default", "e", "edgeV", "w", "minBy_default", "DEFAULT_WEIGHT_FUNC", "constant_default", "DEFAULT_WEIGHT_FUNC", "constant_default", "topsort", "CycleException", "g", "visited", "stack", "results", "visit", "node", "forEach_default", "__name", "size_default", "dfs", "g", "vs", "order", "isArray_default", "navigation", "acc", "visited", "forEach_default", "v", "doDfs", "__name", "postorder", "w", "postorder", "g", "vs", "dfs", "__name", "preorder", "g", "vs", "dfs", "__name", "networkSimplex", "initLowLimValues", "initCutValues", "calcCutValue", "leaveEdge", "enterEdge", "exchangeEdges", "g", "simplify", "longestPath", "t", "feasibleTree", "e", "f", "__name", "vs", "postorder", "forEach_default", "v", "assignCutValue", "child", "childLab", "parent", "childIsTail", "graphEdge", "cutValue", "isOutEdge", "other", "pointsToHead", "otherWeight", "isTreeEdge", "otherCutValue", "tree", "root", "dfsAssignLowLim", "visited", "<PERSON><PERSON><PERSON>", "low", "label", "w", "find_default", "edge", "vLabel", "w<PERSON><PERSON><PERSON>", "tailLabel", "flip", "candidates", "filter_default", "isDescendant", "minBy_default", "slack", "updateRanks", "preorder", "flipped", "u", "rootLabel", "rank", "g", "networkSimplexRanker", "tightTreeRanker", "longestPathRanker", "__name", "longestPath", "feasibleTree", "networkSimplex", "run", "g", "root", "addDummyNode", "depths", "treeDepths", "height", "max_default", "values_default", "nodeSep", "forEach_default", "e", "weight", "sumWeights", "child", "dfs", "__name", "v", "children", "top", "addBorderNode", "bottom", "label", "childNode", "childTop", "childBottom", "thisWeight", "minlen", "depth", "reduce_default", "acc", "cleanup", "graphLabel", "edge", "addSubgraphConstraints", "g", "cg", "vs", "prev", "rootPrev", "forEach_default", "v", "child", "parent", "prev<PERSON><PERSON><PERSON>", "__name", "buildLayerGraph", "g", "rank", "relationship", "root", "createRootNode", "result", "Graph", "v", "forEach_default", "node", "parent", "e", "u", "edge", "weight", "isUndefined_default", "__name", "uniqueId_default", "crossCount", "g", "layering", "cc", "i", "twoLayerCrossCount", "__name", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "southPos", "zipObject_default", "map_default", "v", "southEntries", "flatten_default", "sortBy_default", "e", "firstIndex", "treeSize", "tree", "forEach_default", "entry", "index", "weightSum", "initOrder", "g", "visited", "simpleNodes", "filter_default", "v", "maxRank", "max_default", "map_default", "layers", "range_default", "dfs", "has_default", "node", "forEach_default", "__name", "orderedVs", "sortBy_default", "barycenter", "g", "movable", "map_default", "v", "inV", "result", "reduce_default", "acc", "e", "edge", "nodeU", "__name", "resolveConflicts", "entries", "cg", "mappedEntries", "forEach_default", "entry", "i", "tmp", "isUndefined_default", "e", "entryV", "entryW", "sourceSet", "filter_default", "doResolveConflicts", "__name", "handleIn", "vEntry", "uEntry", "mergeEntries", "handleOut", "wEntry", "map_default", "pick_default", "target", "source", "sum", "weight", "sort", "entries", "biasRight", "parts", "partition", "entry", "sortable", "unsortable", "sortBy_default", "vs", "sum", "weight", "vsIndex", "compareWithBias", "consumeUnsortable", "forEach_default", "result", "flatten_default", "__name", "index", "last", "last_default", "bias", "entryV", "entryW", "sortSubgraph", "g", "v", "cg", "biasRight", "movable", "node", "bl", "br", "subgraphs", "filter_default", "w", "barycenters", "barycenter", "forEach_default", "entry", "subgraphResult", "mergeBarycenters", "entries", "resolveConflicts", "expandSubgraphs", "result", "sort", "flatten_default", "blPred", "br<PERSON><PERSON>", "__name", "target", "other", "isUndefined_default", "order", "g", "maxRank", "downLayerGraphs", "buildLayerGraphs", "range_default", "upLayerGraphs", "layering", "initOrder", "assignOrder", "bestCC", "best", "i", "lastBest", "sweepLayerGraphs", "buildLayerMatrix", "cc", "crossCount", "cloneDeep_default", "__name", "ranks", "relationship", "map_default", "rank", "buildLayerGraph", "layerGraphs", "biasRight", "cg", "Graph", "forEach_default", "lg", "root", "sorted", "sortSubgraph", "v", "addSubgraphConstraints", "layer", "parent<PERSON>ummy<PERSON><PERSON><PERSON>", "g", "postorderNums", "postorder", "forEach_default", "v", "node", "edgeObj", "pathData", "<PERSON><PERSON><PERSON>", "path", "lca", "pathIdx", "pathV", "ascending", "__name", "w", "vPath", "wPath", "low", "lim", "parent", "result", "dfs", "findType1Conflicts", "g", "layering", "conflicts", "<PERSON><PERSON><PERSON><PERSON>", "prevLayer", "layer", "k0", "scanPos", "prevL<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastNode", "last_default", "forEach_default", "v", "i", "w", "findOtherInnerSegmentNode", "k1", "scanNode", "u", "uLabel", "uPos", "addConflict", "__name", "reduce_default", "findType2Conflicts", "scan", "south", "southPos", "southEnd", "prevNorthBorder", "nextNorthBorder", "range_default", "uNode", "north", "prevNorthPos", "nextNorthPos", "southLookahead", "predecessors", "find_default", "tmp", "conflictsV", "hasConflict", "verticalAlignment", "neighborFn", "root", "align", "pos", "order", "prevIdx", "ws", "sortBy_default", "mp", "il", "horizontalCompaction", "reverseSep", "xs", "blockG", "buildBlockGraph", "borderType", "iterate", "setXsFunc", "nextNodesFunc", "stack", "elem", "visited", "pass1", "acc", "e", "pass2", "min", "node", "blockGraph", "Graph", "graphLabel", "sepFn", "sep", "vRoot", "uRoot", "prevMax", "findSmallestWidthAlignment", "xss", "minBy_default", "values_default", "max", "forIn_default", "x", "halfWidth", "width", "alignCoordinates", "alignTo", "alignToVals", "alignToMin", "min_default", "alignToMax", "max_default", "vert", "horiz", "alignment", "delta", "xsVals", "mapValues_default", "balance", "ignore", "map_default", "positionX", "buildLayerMatrix", "merge_default", "adjustedLayering", "inner", "smallestWidth", "nodeSep", "edgeSep", "vLabel", "w<PERSON><PERSON><PERSON>", "sum", "position", "g", "asNonCompoundGraph", "positionY", "forOwn_default", "positionX", "x", "v", "__name", "layering", "buildLayerMatrix", "rankSep", "prevY", "forEach_default", "layer", "maxHeight", "max_default", "map_default", "layout", "g", "opts", "time", "notime", "layoutGraph", "buildLayoutGraph", "runLayout", "updateInputGraph", "__name", "makeSpaceForEdgeLabels", "removeSelfEdges", "run", "rank", "asNonCompoundGraph", "injectEdgeLabelProxies", "removeEmptyRanks", "cleanup", "normalizeRanks", "assignRankMinMax", "removeEdgeLabelProxies", "parent<PERSON>ummy<PERSON><PERSON><PERSON>", "addBorderSegments", "order", "insertSelf<PERSON>dges", "adjust", "position", "position<PERSON><PERSON><PERSON><PERSON>", "removeBorderNodes", "undo", "fixupEdgeLabelCoords", "translateGraph", "assignNodeIntersects", "reversePointsForReversedEdges", "inputGraph", "forEach_default", "v", "inputLabel", "layoutLabel", "e", "graphNumAttrs", "graphDefaults", "graphAttrs", "nodeNumAttrs", "nodeDefaults", "edgeNumAttrs", "edgeDefaults", "edgeAttrs", "Graph", "graph", "canonicalize", "merge_default", "selectNumberAttrs", "pick_default", "node", "defaults_default", "edge", "w", "label", "addDummyNode", "maxRank", "max_default", "minX", "maxX", "minY", "maxY", "graphLabel", "marginX", "marginY", "getExtremes", "attrs", "x", "y", "h", "p", "nodeV", "nodeW", "p1", "p2", "intersectRect", "b", "l", "last_default", "r", "layers", "buildLayerMatrix", "layer", "orderShift", "i", "selfEdge", "selfNode", "dx", "dy", "obj", "mapValues_default", "newAttrs", "k"]}