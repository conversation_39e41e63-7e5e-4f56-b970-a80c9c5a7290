{"version": 3, "file": "generate.js", "sourceRoot": "", "sources": ["../../src/generate.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,WAAW,CAAC;AAU5E,MAAM,UAAU,MAAM,CACpB,KAA8B,EAC9B,OAAqC;IAErC,IAAI,YAAY,GAAa,EAAE,CAAC;IAEhC,YAAY,GAAG,YAAY,CAAC,MAAM,CAChC,iEAAiE,CAClE,CAAC;IAEF,YAAY,GAAG,YAAY,CAAC,MAAM,CAChC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CACrD,CAAC;IAEF,IAAI,OAAO,CAAC,uBAAuB,EAAE;QACnC,YAAY,GAAG,YAAY,CAAC,MAAM,CAChC,UAAU,CAAC,OAAO,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAChD,CAAC;KACH;IAED,OAAO,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC1C,CAAC;AAED,SAAS,eAAe,CAAC,IAA2B;IAClD,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAChD,MAAM,qBAAqB,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAExD,OAAO,CAAC,gBAAgB,EAAE,qBAAqB,CAAC,CAAC;AACnD,CAAC;AAED,SAAS,gBAAgB,CAAC,IAA2B;IACnD,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1D,MAAM,gBAAgB,GAAG,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAE5D,OAAO,oBAAoB,iBAAiB;WACnC,IAAI,CAAC,IAAI;cACN,gBAAgB;EAC5B,CAAC;AACH,CAAC;AAED,SAAS,mBAAmB,CAAC,IAA2B;IACtD,MAAM,QAAQ,GAAG,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEpD,OAAO,eAAe,QAAQ;IAC5B,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;GAC5E,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CAAC,IAA4B;IACpD,MAAM,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5C,OAAO,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,KAAK,CAAC;AACnE,CAAC;AAED,SAAS,UAAU,CAAC,IAAY,EAAE,KAA8B;IAC9D,OAAO,oBAAoB,IAAI;IAC7B,GAAG,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;EAC7D,CAAC;AACH,CAAC;AAED,SAAS,kBAAkB,CAAC,IAA2B;IACrD,MAAM,gBAAgB,GAAG,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5D,OAAO,GAAG,IAAI,CAAC,IAAI,cAAc,gBAAgB,qBAAqB,CAAC;AACzE,CAAC;AAED,SAAS,eAAe,CAAC,IAAuB;IAC9C,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;QACjB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC;QAClE,OAAO,GAAG,GAAG,UAAU,GAAG,GAAG,CAAC;KAC/B;SAAM;QACL,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC;KAC5B;AACH,CAAC;AAED,SAAS,aAAa,CAAC,IAAoC;IACzD,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;QACzB,OAAO,QAAQ,CAAC;KACjB;IACD,OAAO,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzC,CAAC;AAED,SAAS,oBAAoB,CAAC,QAAgB;IAC5C,OAAO,UAAU,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC;AAC1C,CAAC;AAED,SAAS,uBAAuB,CAAC,QAAgB;IAC/C,OAAO,UAAU,CAAC,QAAQ,CAAC,GAAG,aAAa,CAAC;AAC9C,CAAC"}