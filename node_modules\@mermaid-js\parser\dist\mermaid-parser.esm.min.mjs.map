{"version": 3, "sources": ["../src/parse.ts"], "sourcesContent": ["import type { LangiumPars<PERSON>, ParseResult } from 'langium';\n\nimport type { Info, Packet, Pie, Architecture, GitGraph, Radar } from './index.js';\n\nexport type DiagramAST = Info | Packet | Pie | Architecture | GitGraph | Radar;\n\nconst parsers: Record<string, LangiumParser> = {};\nconst initializers = {\n  info: async () => {\n    const { createInfoServices } = await import('./language/info/index.js');\n    const parser = createInfoServices().Info.parser.LangiumParser;\n    parsers.info = parser;\n  },\n  packet: async () => {\n    const { createPacketServices } = await import('./language/packet/index.js');\n    const parser = createPacketServices().Packet.parser.LangiumParser;\n    parsers.packet = parser;\n  },\n  pie: async () => {\n    const { createPieServices } = await import('./language/pie/index.js');\n    const parser = createPieServices().Pie.parser.LangiumParser;\n    parsers.pie = parser;\n  },\n  architecture: async () => {\n    const { createArchitectureServices } = await import('./language/architecture/index.js');\n    const parser = createArchitectureServices().Architecture.parser.LangiumParser;\n    parsers.architecture = parser;\n  },\n  gitGraph: async () => {\n    const { createGitGraphServices } = await import('./language/gitGraph/index.js');\n    const parser = createGitGraphServices().GitGraph.parser.LangiumParser;\n    parsers.gitGraph = parser;\n  },\n  radar: async () => {\n    const { createRadarServices } = await import('./language/radar/index.js');\n    const parser = createRadarServices().Radar.parser.LangiumParser;\n    parsers.radar = parser;\n  },\n} as const;\n\nexport async function parse(diagramType: 'info', text: string): Promise<Info>;\nexport async function parse(diagramType: 'packet', text: string): Promise<Packet>;\nexport async function parse(diagramType: 'pie', text: string): Promise<Pie>;\nexport async function parse(diagramType: 'architecture', text: string): Promise<Architecture>;\nexport async function parse(diagramType: 'gitGraph', text: string): Promise<GitGraph>;\nexport async function parse(diagramType: 'radar', text: string): Promise<Radar>;\n\nexport async function parse<T extends DiagramAST>(\n  diagramType: keyof typeof initializers,\n  text: string\n): Promise<T> {\n  const initializer = initializers[diagramType];\n  if (!initializer) {\n    throw new Error(`Unknown diagram type: ${diagramType}`);\n  }\n  if (!parsers[diagramType]) {\n    await initializer();\n  }\n  const parser: LangiumParser = parsers[diagramType];\n  const result: ParseResult<T> = parser.parse<T>(text);\n  if (result.lexerErrors.length > 0 || result.parserErrors.length > 0) {\n    throw new MermaidParseError(result);\n  }\n  return result.value;\n}\n\nexport class MermaidParseError extends Error {\n  constructor(public result: ParseResult<DiagramAST>) {\n    const lexerErrors: string = result.lexerErrors.map((err) => err.message).join('\\n');\n    const parserErrors: string = result.parserErrors.map((err) => err.message).join('\\n');\n    super(`Parsing failed: ${lexerErrors} ${parserErrors}`);\n  }\n}\n"], "mappings": "2wBAMA,IAAMA,EAAyC,CAAC,EAC1CC,EAAe,CACnB,KAAMC,EAAA,SAAY,CAChB,GAAM,CAAE,mBAAAC,CAAmB,EAAI,KAAM,QAAO,mDAA0B,EAChEC,EAASD,EAAmB,EAAE,KAAK,OAAO,cAChDH,EAAQ,KAAOI,CACjB,EAJM,QAKN,OAAQF,EAAA,SAAY,CAClB,GAAM,CAAE,qBAAAG,CAAqB,EAAI,KAAM,QAAO,qDAA4B,EACpED,EAASC,EAAqB,EAAE,OAAO,OAAO,cACpDL,EAAQ,OAASI,CACnB,EAJQ,UAKR,IAAKF,EAAA,SAAY,CACf,GAAM,CAAE,kBAAAI,CAAkB,EAAI,KAAM,QAAO,kDAAyB,EAC9DF,EAASE,EAAkB,EAAE,IAAI,OAAO,cAC9CN,EAAQ,IAAMI,CAChB,EAJK,OAKL,aAAcF,EAAA,SAAY,CACxB,GAAM,CAAE,2BAAAK,CAA2B,EAAI,KAAM,QAAO,2DAAkC,EAChFH,EAASG,EAA2B,EAAE,aAAa,OAAO,cAChEP,EAAQ,aAAeI,CACzB,EAJc,gBAKd,SAAUF,EAAA,SAAY,CACpB,GAAM,CAAE,uBAAAM,CAAuB,EAAI,KAAM,QAAO,uDAA8B,EACxEJ,EAASI,EAAuB,EAAE,SAAS,OAAO,cACxDR,EAAQ,SAAWI,CACrB,EAJU,YAKV,MAAOF,EAAA,SAAY,CACjB,GAAM,CAAE,oBAAAO,CAAoB,EAAI,KAAM,QAAO,oDAA2B,EAClEL,EAASK,EAAoB,EAAE,MAAM,OAAO,cAClDT,EAAQ,MAAQI,CAClB,EAJO,QAKT,EASA,eAAsBM,GACpBC,EACAC,EACY,CACZ,IAAMC,EAAcZ,EAAaU,CAAW,EAC5C,GAAI,CAACE,EACH,MAAM,IAAI,MAAM,yBAAyBF,CAAW,EAAE,EAEnDX,EAAQW,CAAW,GACtB,MAAME,EAAY,EAGpB,IAAMC,EADwBd,EAAQW,CAAW,EACX,MAASC,CAAI,EACnD,GAAIE,EAAO,YAAY,OAAS,GAAKA,EAAO,aAAa,OAAS,EAChE,MAAM,IAAIC,EAAkBD,CAAM,EAEpC,OAAOA,EAAO,KAChB,CAjBsBZ,EAAAQ,GAAA,SAmBf,IAAMK,EAAN,cAAgC,KAAM,CAC3C,YAAmBD,EAAiC,CAClD,IAAME,EAAsBF,EAAO,YAAY,IAAKG,GAAQA,EAAI,OAAO,EAAE,KAAK;AAAA,CAAI,EAC5EC,EAAuBJ,EAAO,aAAa,IAAKG,GAAQA,EAAI,OAAO,EAAE,KAAK;AAAA,CAAI,EACpF,MAAM,mBAAmBD,CAAW,IAAIE,CAAY,EAAE,EAHrC,YAAAJ,CAInB,CAvEF,MAkE6C,CAAAZ,EAAA,0BAM7C", "names": ["parsers", "initializers", "__name", "createInfoServices", "parser", "createPacketServices", "createPieServices", "createArchitectureServices", "createGitGraphServices", "createRadarServices", "parse", "diagramType", "text", "initializer", "result", "MermaidParseError", "lexerErrors", "err", "parserErrors"]}