import{M as b,b as u,ga as s}from"./chunk-ZKYS2E5M.mjs";import{a as r}from"./chunk-GTKDMUJJ.mjs";var w=r((t,o)=>{let e;return o==="sandbox"&&(e=s("#i"+t)),(o==="sandbox"?s(e.nodes()[0].contentDocument.body):s("body")).select(`[id="${t}"]`)},"getDiagramElement");var $=r((t,o,e,i)=>{t.attr("class",e);let{width:n,height:m,x:h,y:x}=a(t,o);b(t,m,n,i);let c=d(h,x,n,m,o);t.attr("viewBox",c),u.debug(`viewBox configured: ${c} with padding: ${o}`)},"setupViewPortForSVG"),a=r((t,o)=>{let e=t.node()?.getBBox()||{width:0,height:0,x:0,y:0};return{width:e.width+o*2,height:e.height+o*2,x:e.x,y:e.y}},"calculateDimensionsWithPadding"),d=r((t,o,e,i,n)=>`${t-n} ${o-n} ${e} ${i}`,"createViewBox");export{w as a,$ as b};
