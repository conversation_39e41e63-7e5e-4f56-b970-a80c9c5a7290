{"version": 3, "file": "documents.js", "sourceRoot": "", "sources": ["../../src/workspace/documents.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAEhF;;;;;;GAMG;AACH,OAAO,EAAE,YAAY,EAAE,MAAM,oCAAoC,CAAC;AAUlE,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAC7D,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,GAAG,EAAE,MAAM,uBAAuB,CAAC;AAuB5C;;;GAGG;AACH,MAAM,CAAN,IAAY,aAyCX;AAzCD,WAAY,aAAa;IACrB;;;OAGG;IACH,uDAAW,CAAA;IACX;;;;OAIG;IACH,qDAAU,CAAA;IACV;;;OAGG;IACH,qEAAkB,CAAA;IAClB;;;;;;OAMG;IACH,qEAAkB,CAAA;IAClB;;;OAGG;IACH,qDAAU,CAAA;IACV;;;;OAIG;IACH,2EAAqB,CAAA;IACrB;;;OAGG;IACH,2DAAa,CAAA;AACjB,CAAC,EAzCW,aAAa,KAAb,aAAa,QAyCxB;AAqED,MAAM,OAAO,6BAA6B;IAMtC,YAAY,QAAmC;QAC3C,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAC;QAChD,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC;QACtD,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC,SAAS,CAAC,kBAAkB,CAAC;IACpE,CAAC;IAED,KAAK,CAAC,OAAO,CAA8B,GAAQ,EAAE,iBAAiB,GAAG,iBAAiB,CAAC,IAAI;QAC3F,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC,WAAW,CAAI,GAAG,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;IAChE,CAAC;IAID,gBAAgB,CAA8B,YAA0B,EAAE,GAAS,EAAE,KAAyC;QAC1H,GAAG,GAAG,GAAG,aAAH,GAAG,cAAH,GAAG,GAAI,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACzC,IAAI,iBAAiB,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAC,WAAW,CAAI,GAAG,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;QACzD,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,CAAC,MAAM,CAAI,GAAG,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;QACpD,CAAC;IACL,CAAC;IAID,UAAU,CAA8B,IAAY,EAAE,GAAQ,EAAE,KAAyC;QACrG,IAAI,iBAAiB,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAC,WAAW,CAAI,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACjD,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,CAAC,MAAM,CAAI,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAC5C,CAAC;IACL,CAAC;IAED,SAAS,CAA8B,KAAQ,EAAE,GAAQ;QACrD,OAAO,IAAI,CAAC,MAAM,CAAI,GAAG,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;IAClD,CAAC;IAES,MAAM,CAA8B,GAAQ,EAAE,OAA8C,EAAE,OAAuB;QAC3H,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAC9B,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAI,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC,qBAAqB,CAAI,WAAW,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAE/E,CAAC;aAAM,IAAI,QAAQ,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,WAAW,GAAG,EAAE,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,YAAY,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC;YACjF,OAAO,IAAI,CAAC,qBAAqB,CAAI,WAAW,EAAE,GAAG,CAAC,CAAC;QAE3D,CAAC;aAAM,CAAC;YACJ,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAI,GAAG,EAAE,OAAO,CAAC,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC;YACnE,OAAO,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;QACjE,CAAC;IACL,CAAC;IAES,KAAK,CAAC,WAAW,CAA8B,GAAQ,EAAE,OAA8B,EAAE,WAA8B;QAC7H,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAC9B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,UAAU,CAAI,GAAG,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;YACxE,OAAO,IAAI,CAAC,qBAAqB,CAAI,WAAW,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAC/E,CAAC;aAAM,CAAC;YACJ,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,UAAU,CAAI,GAAG,EAAE,OAAO,CAAC,OAAO,EAAE,EAAE,WAAW,CAAC,CAAC;YAClF,OAAO,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;QACjE,CAAC;IACL,CAAC;IAED;;;;;;;;;;OAUG;IACO,qBAAqB,CAA8B,WAA2B,EAAE,GAAQ,EAAE,YAA2B,EAAE,IAAa;QAC1I,IAAI,QAA4B,CAAC;QACjC,IAAI,YAAY,EAAE,CAAC;YACf,QAAQ,GAAG;gBACP,WAAW;gBACX,GAAG;gBACH,KAAK,EAAE,aAAa,CAAC,MAAM;gBAC3B,UAAU,EAAE,EAAE;gBACd,YAAY;aACf,CAAC;QACN,CAAC;aAAM,CAAC;YACJ,MAAM,kBAAkB,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YACpE,QAAQ,GAAG;gBACP,WAAW;gBACX,GAAG;gBACH,KAAK,EAAE,aAAa,CAAC,MAAM;gBAC3B,UAAU,EAAE,EAAE;gBACd,IAAI,YAAY;oBACZ,OAAO,kBAAkB,EAAE,CAAC;gBAChC,CAAC;aACJ,CAAC;QACN,CAAC;QACA,WAAW,CAAC,KAA0B,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC7D,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,KAAK,CAAC,MAAM,CAA8B,QAAqC,EAAE,iBAAoC;;QACjH,yFAAyF;QACzF,MAAM,OAAO,GAAG,MAAA,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,0CAAE,IAAI,CAAC,QAAQ,CAAC;QACnE,MAAM,YAAY,GAAG,MAAA,IAAI,CAAC,aAAa,0CAAE,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;QACtE,MAAM,IAAI,GAAG,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAE1G,IAAI,YAAY,EAAE,CAAC;YACf,MAAM,CAAC,cAAc,CACjB,QAAQ,EACR,cAAc,EACd;gBACI,KAAK,EAAE,YAAY;aACtB,CACJ,CAAC;QACN,CAAC;aAAM,CAAC;YACJ,MAAM,kBAAkB,GAAG,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YAC7E,MAAM,CAAC,cAAc,CACjB,QAAQ,EACR,cAAc,EACd;gBACI,GAAG,EAAE,kBAAkB;aAC1B,CACJ,CAAC;QACN,CAAC;QAED,6FAA6F;QAC7F,6DAA6D;QAC7D,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;YACnB,QAAQ,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;YACnF,QAAQ,CAAC,WAAW,CAAC,KAA0B,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1E,CAAC;QACD,QAAQ,CAAC,KAAK,GAAG,aAAa,CAAC,MAAM,CAAC;QACtC,OAAO,QAAQ,CAAC;IACpB,CAAC;IAES,KAAK,CAAoB,GAAQ,EAAE,IAAY,EAAE,OAAuB;QAC9E,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACvD,OAAO,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAI,IAAI,EAAE,OAAO,CAAC,CAAC;IACjE,CAAC;IAES,UAAU,CAAoB,GAAQ,EAAE,IAAY,EAAE,iBAAoC;QAChG,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACvD,OAAO,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAI,IAAI,EAAE,iBAAiB,CAAC,CAAC;IACzE,CAAC;IAES,wBAAwB,CAAC,GAAQ,EAAE,IAAa;QACtD,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAC7C,IAAI,OAAO,GAA6B,SAAS,CAAC;QAClD,OAAO,GAAG,EAAE;YACR,OAAO,OAAO,aAAP,OAAO,cAAP,OAAO,IAAP,OAAO,GAAK,YAAY,CAAC,MAAM,CAClC,GAAG,CAAC,QAAQ,EAAE,EAAE,eAAe,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,EAAE,CAC9F,EAAC;QACN,CAAC,CAAC;IACN,CAAC;CACJ;AAsED,MAAM,OAAO,uBAAuB;IAOhC,YAAY,QAAmC;QAF5B,gBAAW,GAAiC,IAAI,GAAG,EAAE,CAAC;QAGrE,IAAI,CAAC,sBAAsB,GAAG,QAAQ,CAAC,SAAS,CAAC,sBAAsB,CAAC;QACxE,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAC;IACpD,CAAC;IAED,IAAI,GAAG;QACH,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED,WAAW,CAAC,QAAyB;QACjC,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;QAC1C,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,4BAA4B,SAAS,uBAAuB,CAAC,CAAC;QAClF,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAC9C,CAAC;IAED,WAAW,CAAC,GAAQ;QAChB,MAAM,SAAS,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,GAAQ,EAAE,iBAAqC;QACrE,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACrC,IAAI,QAAQ,EAAE,CAAC;YACX,OAAO,QAAQ,CAAC;QACpB,CAAC;QACD,QAAQ,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;QAC7E,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC3B,OAAO,QAAQ,CAAC;IACpB,CAAC;IAID,cAAc,CAAC,GAAQ,EAAE,IAAY,EAAE,iBAAqC;QACxE,IAAI,iBAAiB,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE,iBAAiB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;gBACxF,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;gBAC3B,OAAO,QAAQ,CAAC;YACpB,CAAC,CAAC,CAAC;QACP,CAAC;aAAM,CAAC;YACJ,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YACnE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAC3B,OAAO,QAAQ,CAAC;QACpB,CAAC;IACL,CAAC;IAED,WAAW,CAAC,GAAQ;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;IAChD,CAAC;IAED,kBAAkB,CAAC,GAAQ;QACvB,MAAM,SAAS,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;QACjC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACnD,IAAI,UAAU,EAAE,CAAC;YACb,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC;YACvE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAC1B,UAAU,CAAC,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC;YACzC,UAAU,CAAC,iBAAiB,GAAG,SAAS,CAAC;YACzC,UAAU,CAAC,WAAW,GAAG,SAAS,CAAC;QACvC,CAAC;QACD,OAAO,UAAU,CAAC;IACtB,CAAC;IAED,cAAc,CAAC,GAAQ;QACnB,MAAM,SAAS,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;QACjC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACnD,IAAI,UAAU,EAAE,CAAC;YACb,UAAU,CAAC,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC;YACzC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACvC,CAAC;QACD,OAAO,UAAU,CAAC;IACtB,CAAC;CACJ"}