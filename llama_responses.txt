=== Réponse pour class ===
Status: 200
Response: {
  "id": "chatcmpl-4be7c928-7d62-4b5e-813d-107091333fcf",
  "object": "chat.completion",
  "created": 1748028495,
  "model": "meta-llama/llama-4-scout-17b-16e-instruct",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "NOM_CLASSE: Personne\nATTRIBUTS: \n- name\n- sex\nM\u00c9THODES:"
      },
      "logprobs": null,
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "queue_time": 0.103686214,
    "prompt_tokens": 253,
    "prompt_time": 0.010145845,
    "completion_tokens": 24,
    "completion_time": 0.047540313,
    "total_tokens": 277,
    "total_time": 0.057686158
  },
  "usage_breakdown": {
    "models": null
  },
  "system_fingerprint": "fp_37da608fc1",
  "x_groq": {
    "id": "req_01jvz9w5mpfznvh70wj0z7kce6"
  }
}
--------------------------------------------------
Texte extrait: NOM_CLASSE: Personne
ATTRIBUTS: 
- name
- sex
MÉTHODES:
==================================================
=== Réponse pour class ===
Status: 200
Response: {
  "id": "chatcmpl-bc0a88e2-4299-4d68-9406-8cdc057c4243",
  "object": "chat.completion",
  "created": 1748028496,
  "model": "meta-llama/llama-4-scout-17b-16e-instruct",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "NOM_CLASSE: A\nATTRIBUTS: \n- Type\n- color\nM\u00c9THODES:"
      },
      "logprobs": null,
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "queue_time": 0.106136837,
    "prompt_tokens": 253,
    "prompt_time": 0.009699897,
    "completion_tokens": 23,
    "completion_time": 0.046318489,
    "total_tokens": 276,
    "total_time": 0.056018386
  },
  "usage_breakdown": {
    "models": null
  },
  "system_fingerprint": "fp_37da608fc1",
  "x_groq": {
    "id": "req_01jvz9w62xfznsssddxfs1jxt2"
  }
}
--------------------------------------------------
Texte extrait: NOM_CLASSE: A
ATTRIBUTS: 
- Type
- color
MÉTHODES:
==================================================
=== Réponse pour class ===
Status: 200
Response: {
  "id": "chatcmpl-76540b55-b44a-4d07-8299-7a74db94e7e9",
  "object": "chat.completion",
  "created": 1748028496,
  "model": "meta-llama/llama-4-scout-17b-16e-instruct",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "NOM_CLASSE: C\nATTRIBUTS: \nstring\nint\nM\u00c9THODES:"
      },
      "logprobs": null,
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "queue_time": 0.104909167,
    "prompt_tokens": 253,
    "prompt_time": 0.009733187,
    "completion_tokens": 21,
    "completion_time": 0.043626823,
    "total_tokens": 274,
    "total_time": 0.05336001
  },
  "usage_breakdown": {
    "models": null
  },
  "system_fingerprint": "fp_37da608fc1",
  "x_groq": {
    "id": "req_01jvz9w6gkfzqa1fnec3aa15mj"
  }
}
--------------------------------------------------
Texte extrait: NOM_CLASSE: C
ATTRIBUTS: 
string
int
MÉTHODES:
==================================================
=== Réponse pour class ===
Status: 200
Response: {
  "id": "chatcmpl-29e1d430-564b-447e-938e-219865990441",
  "object": "chat.completion",
  "created": 1748028496,
  "model": "meta-llama/llama-4-scout-17b-16e-instruct",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "NOM_CLASSE: B\nATTRIBUTS: voiture\n       serie\nM\u00c9THODES:"
      },
      "logprobs": null,
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "queue_time": 0.102525265,
    "prompt_tokens": 253,
    "prompt_time": 0.009642948,
    "completion_tokens": 21,
    "completion_time": 0.041663304,
    "total_tokens": 274,
    "total_time": 0.051306252
  },
  "usage_breakdown": {
    "models": null
  },
  "system_fingerprint": "fp_37da608fc1",
  "x_groq": {
    "id": "req_01jvz9w6xtfkz9tsa5kdr6403s"
  }
}
--------------------------------------------------
Texte extrait: NOM_CLASSE: B
ATTRIBUTS: voiture
       serie
MÉTHODES:
==================================================
=== Réponse pour class ===
Status: 200
Response: {
  "id": "chatcmpl-9064b903-5b6c-4341-bf75-e0272ea934d3",
  "object": "chat.completion",
  "created": 1748028497,
  "model": "meta-llama/llama-4-scout-17b-16e-instruct",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "NOM_CLASSE: L\nATTRIBUTS: \nrelation\nNombre\nM\u00c9THODES:"
      },
      "logprobs": null,
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "queue_time": 0.101238707,
    "prompt_tokens": 253,
    "prompt_time": 0.009665118,
    "completion_tokens": 21,
    "completion_time": 0.04244777,
    "total_tokens": 274,
    "total_time": 0.052112888
  },
  "usage_breakdown": {
    "models": null
  },
  "system_fingerprint": "fp_37da608fc1",
  "x_groq": {
    "id": "req_01jvz9w7d9fzr8qervrqvp3842"
  }
}
--------------------------------------------------
Texte extrait: NOM_CLASSE: L
ATTRIBUTS: 
relation
Nombre
MÉTHODES:
==================================================
=== Réponse pour class ===
Status: 200
Response: {
  "id": "chatcmpl-4370da43-05ce-49cb-9e07-b19180e21b5d",
  "object": "chat.completion",
  "created": 1748028497,
  "model": "meta-llama/llama-4-scout-17b-16e-instruct",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "NOM_CLASSE: F\nATTRIBUTS: \n- lieu\n- temp\nM\u00c9THODES:"
      },
      "logprobs": null,
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "queue_time": 0.100849915,
    "prompt_tokens": 253,
    "prompt_time": 0.009948366,
    "completion_tokens": 23,
    "completion_time": 0.045564733,
    "total_tokens": 276,
    "total_time": 0.055513099
  },
  "usage_breakdown": {
    "models": null
  },
  "system_fingerprint": "fp_37da608fc1",
  "x_groq": {
    "id": "req_01jvz9w7tafm0vjkmh3pcznwqs"
  }
}
--------------------------------------------------
Texte extrait: NOM_CLASSE: F
ATTRIBUTS: 
- lieu
- temp
MÉTHODES:
==================================================
=== Réponse pour class ===
Status: 200
Response: {
  "id": "chatcmpl-408c1356-23fc-47f0-9fe8-9fe3ba85bf4e",
  "object": "chat.completion",
  "created": 1748028498,
  "model": "meta-llama/llama-4-scout-17b-16e-instruct",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "NOM_CLASSE: M\nATTRIBUTS: \nM\u00c9THODES:"
      },
      "logprobs": null,
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "queue_time": 0.10239533099999999,
    "prompt_tokens": 253,
    "prompt_time": 0.009795357,
    "completion_tokens": 17,
    "completion_time": 0.033745927,
    "total_tokens": 270,
    "total_time": 0.043541284
  },
  "usage_breakdown": {
    "models": null
  },
  "system_fingerprint": "fp_37da608fc1",
  "x_groq": {
    "id": "req_01jvz9w87gfzrt4terhpkwketm"
  }
}
--------------------------------------------------
Texte extrait: NOM_CLASSE: M
ATTRIBUTS: 
MÉTHODES:
==================================================
=== Réponse pour class ===
Status: 200
Response: {
  "id": "chatcmpl-73e002c9-d3c5-48b6-90d6-303e753777b7",
  "object": "chat.completion",
  "created": 1748028498,
  "model": "meta-llama/llama-4-scout-17b-16e-instruct",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "NOM_CLASSE: Taxi car\nATTRIBUTS: \nM\u00c9THODES:"
      },
      "logprobs": null,
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "queue_time": 0.10163043199999999,
    "prompt_tokens": 253,
    "prompt_time": 0.*********,
    "completion_tokens": 18,
    "completion_time": 0.*********,
    "total_tokens": 271,
    "total_time": 0.*********
  },
  "usage_breakdown": {
    "models": null
  },
  "system_fingerprint": "fp_37da608fc1",
  "x_groq": {
    "id": "req_01jvz9w8mtfm1823aryp7fwx2g"
  }
}
--------------------------------------------------
Texte extrait: NOM_CLASSE: Taxi car
ATTRIBUTS: 
MÉTHODES:
==================================================
=== Réponse pour class ===
Status: 200
Response: {
  "id": "chatcmpl-082686d6-5024-478b-83b1-00d7d7b8d161",
  "object": "chat.completion",
  "created": 1748028499,
  "model": "meta-llama/llama-4-scout-17b-16e-instruct",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "NOM_CLASSE: H\nATTRIBUTS: \n  Num\n  Prenom\nM\u00c9THODES:"
      },
      "logprobs": null,
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "queue_time": 0.*********,
    "prompt_tokens": 253,
    "prompt_time": 0.*********,
    "completion_tokens": 24,
    "completion_time": 0.*********,
    "total_tokens": 277,
    "total_time": 0.*********
  },
  "usage_breakdown": {
    "models": null
  },
  "system_fingerprint": "fp_37da608fc1",
  "x_groq": {
    "id": "req_01jvz9w91wfm19x3av32yr3wgg"
  }
}
--------------------------------------------------
Texte extrait: NOM_CLASSE: H
ATTRIBUTS: 
  Num
  Prenom
MÉTHODES:
==================================================
=== Réponse pour class ===
Status: 200
Response: {
  "id": "chatcmpl-dc6b6864-55a7-478f-bcb0-81df38098dd9",
  "object": "chat.completion",
  "created": 1748028499,
  "model": "meta-llama/llama-4-scout-17b-16e-instruct",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "NOM_CLASSE: G\nATTRIBUTS: \nid\nCin\nM\u00c9THODES:"
      },
      "logprobs": null,
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "queue_time": 0.101902312,
    "prompt_tokens": 253,
    "prompt_time": 0.009937816,
    "completion_tokens": 21,
    "completion_time": 0.047219682,
    "total_tokens": 274,
    "total_time": 0.057157498
  },
  "usage_breakdown": {
    "models": null
  },
  "system_fingerprint": "fp_37da608fc1",
  "x_groq": {
    "id": "req_01jvz9w9dyfzt87c0g6s0ta4vv"
  }
}
--------------------------------------------------
Texte extrait: NOM_CLASSE: G
ATTRIBUTS: 
id
Cin
MÉTHODES:
==================================================
