export { FullIconCustomisations, IconifyIconCustomisations, IconifyIconSize, IconifyIconSizeCustomisations, defaultIconCustomisations, defaultIconSizeCustomisations } from './customisations/defaults.cjs';
export { mergeCustomisations } from './customisations/merge.cjs';
export { toBoolean } from './customisations/bool.cjs';
export { flipFromString } from './customisations/flip.cjs';
export { rotateFromString } from './customisations/rotate.cjs';
export { IconifyIconName, IconifyIconSource, matchIconName, stringToIcon, validateIconName } from './icon/name.cjs';
export { mergeIconData } from './icon/merge.cjs';
export { mergeIconTransformations } from './icon/transformations.cjs';
export { FullExtendedIconifyIcon, FullIconifyIcon, PartialExtendedIconifyIcon, defaultExtendedIconProps, defaultIconDimensions, defaultIconProps, defaultIconTransformations } from './icon/defaults.cjs';
export { makeIconSquare } from './icon/square.cjs';
export { ParentIconsList, ParentIconsTree, getIconsTree } from './icon-set/tree.cjs';
export { parseIconSet, parseIconSetAsync } from './icon-set/parse.cjs';
export { validateIconSet } from './icon-set/validate.cjs';
export { quicklyValidateIconSet } from './icon-set/validate-basic.cjs';
export { expandIconSet } from './icon-set/expand.cjs';
export { minifyIconSet } from './icon-set/minify.cjs';
export { getIcons } from './icon-set/get-icons.cjs';
export { getIconData } from './icon-set/get-icon.cjs';
export { convertIconSetInfo } from './icon-set/convert-info.cjs';
export { IconifyIconBuildResult, iconToSVG } from './svg/build.cjs';
export { mergeDefsAndContent, splitSVGDefs, wrapSVGContent } from './svg/defs.cjs';
export { replaceIDs } from './svg/id.cjs';
export { calculateSize } from './svg/size.cjs';
export { encodeSvgForCss } from './svg/encode-svg-for-css.cjs';
export { trimSVG } from './svg/trim.cjs';
export { prettifySVG } from './svg/pretty.cjs';
export { iconToHTML } from './svg/html.cjs';
export { svgToData, svgToURL } from './svg/url.cjs';
export { cleanUpInnerHTML } from './svg/inner-html.cjs';
export { SVGViewBox, getSVGViewBox } from './svg/viewbox.cjs';
export { ParsedSVGContent, buildParsedSVG, convertParsedSVG, parseSVGContent } from './svg/parse.cjs';
export { colorKeywords } from './colors/keywords.cjs';
export { colorToString, compareColors, stringToColor } from './colors/index.cjs';
export { getIconCSS, getIconContentCSS } from './css/icon.cjs';
export { getIconsCSS, getIconsContentCSS } from './css/icons.cjs';
export { CustomCollections, CustomIconLoader, ExternalPkgName, IconCustomizations, IconCustomizer, IconifyLoaderOptions, InlineCollection, UniversalIconLoader } from './loader/types.cjs';
export { mergeIconProps } from './loader/utils.cjs';
export { getCustomIcon } from './loader/custom.cjs';
export { searchForIcon } from './loader/modern.cjs';
export { loadIcon } from './loader/loader.cjs';
export { getEmojiSequenceFromString, getUnqualifiedEmojiSequence } from './emoji/cleanup.cjs';
export { convertEmojiSequenceToUTF16, convertEmojiSequenceToUTF32, getEmojiCodePoint, getEmojiUnicode, isUTF32SplitNumber, mergeUTF32Numbers, splitUTF32Number } from './emoji/convert.cjs';
export { getEmojiSequenceKeyword, getEmojiSequenceString, getEmojiUnicodeString } from './emoji/format.cjs';
export { parseEmojiTestFile } from './emoji/test/parse.cjs';
export { getQualifiedEmojiVariations } from './emoji/test/variations.cjs';
export { findMissingEmojis } from './emoji/test/missing.cjs';
export { createOptimisedRegex, createOptimisedRegexForEmojiSequences } from './emoji/regex/create.cjs';
export { prepareEmojiForIconSet, prepareEmojiForIconsList } from './emoji/parse.cjs';
export { findAndReplaceEmojisInText } from './emoji/replace/replace.cjs';
export { camelToKebab, camelize, pascalize, snakelize } from './misc/strings.cjs';
export { commonObjectProps, compareObjects, unmergeObjects } from './misc/objects.cjs';
export { sanitiseTitleAttribute } from './misc/title.cjs';
export { IconifyIcon } from '@iconify/types';
import './colors/types.cjs';
import './css/types.cjs';
import '@antfu/utils';
import './emoji/test/tree.cjs';
import './emoji/data.cjs';
import './emoji/test/similar.cjs';
import './emoji/test/components.cjs';
import './emoji/test/name.cjs';
import './emoji/replace/find.cjs';
