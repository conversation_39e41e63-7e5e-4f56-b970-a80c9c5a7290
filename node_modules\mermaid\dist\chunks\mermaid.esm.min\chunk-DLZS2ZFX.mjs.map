{"version": 3, "sources": ["../../../src/diagrams/common/svgDrawCommon.ts"], "sourcesContent": ["import { sanitizeUrl } from '@braintree/sanitize-url';\nimport type { SVG, SVGGroup } from '../../diagram-api/types.js';\nimport { lineBreakRegex } from './common.js';\nimport type {\n  Bound,\n  D3ImageElement,\n  D3RectElement,\n  D3TSpanElement,\n  D3TextElement,\n  D3UseElement,\n  RectData,\n  TextData,\n  TextObject,\n} from './commonTypes.js';\n\nexport const drawRect = (element: SVG | SVGGroup, rectData: RectData): D3RectElement => {\n  const rectElement: D3RectElement = element.append('rect');\n  rectElement.attr('x', rectData.x);\n  rectElement.attr('y', rectData.y);\n  rectElement.attr('fill', rectData.fill);\n  rectElement.attr('stroke', rectData.stroke);\n  rectElement.attr('width', rectData.width);\n  rectElement.attr('height', rectData.height);\n  if (rectData.name) {\n    rectElement.attr('name', rectData.name);\n  }\n  if (rectData.rx) {\n    rectElement.attr('rx', rectData.rx);\n  }\n  if (rectData.ry) {\n    rectElement.attr('ry', rectData.ry);\n  }\n\n  if (rectData.attrs !== undefined) {\n    for (const attrKey in rectData.attrs) {\n      rectElement.attr(attrKey, rectData.attrs[attrKey]);\n    }\n  }\n\n  if (rectData.class) {\n    rectElement.attr('class', rectData.class);\n  }\n\n  return rectElement;\n};\n\n/**\n * Draws a background rectangle\n *\n * @param element - Diagram (reference for bounds)\n * @param bounds - Shape of the rectangle\n */\nexport const drawBackgroundRect = (element: SVG | SVGGroup, bounds: Bound): void => {\n  const rectData: RectData = {\n    x: bounds.startx,\n    y: bounds.starty,\n    width: bounds.stopx - bounds.startx,\n    height: bounds.stopy - bounds.starty,\n    fill: bounds.fill,\n    stroke: bounds.stroke,\n    class: 'rect',\n  };\n  const rectElement: D3RectElement = drawRect(element, rectData);\n  rectElement.lower();\n};\n\nexport const drawText = (element: SVG | SVGGroup, textData: TextData): D3TextElement => {\n  const nText: string = textData.text.replace(lineBreakRegex, ' ');\n\n  const textElem: D3TextElement = element.append('text');\n  textElem.attr('x', textData.x);\n  textElem.attr('y', textData.y);\n  textElem.attr('class', 'legend');\n\n  textElem.style('text-anchor', textData.anchor);\n  if (textData.class) {\n    textElem.attr('class', textData.class);\n  }\n\n  const tspan: D3TSpanElement = textElem.append('tspan');\n  tspan.attr('x', textData.x + textData.textMargin * 2);\n  tspan.text(nText);\n\n  return textElem;\n};\n\nexport const drawImage = (elem: SVG | SVGGroup, x: number, y: number, link: string): void => {\n  const imageElement: D3ImageElement = elem.append('image');\n  imageElement.attr('x', x);\n  imageElement.attr('y', y);\n  const sanitizedLink: string = sanitizeUrl(link);\n  imageElement.attr('xlink:href', sanitizedLink);\n};\n\nexport const drawEmbeddedImage = (\n  element: SVG | SVGGroup,\n  x: number,\n  y: number,\n  link: string\n): void => {\n  const imageElement: D3UseElement = element.append('use');\n  imageElement.attr('x', x);\n  imageElement.attr('y', y);\n  const sanitizedLink: string = sanitizeUrl(link);\n  imageElement.attr('xlink:href', `#${sanitizedLink}`);\n};\n\nexport const getNoteRect = (): RectData => {\n  const noteRectData: RectData = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    fill: '#EDF2AE',\n    stroke: '#666',\n    anchor: 'start',\n    rx: 0,\n    ry: 0,\n  };\n  return noteRectData;\n};\n\nexport const getTextObj = (): TextObject => {\n  const testObject: TextObject = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    'text-anchor': 'start',\n    style: '#666',\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    tspan: true,\n  };\n  return testObject;\n};\n"], "mappings": "kIAAA,IAAAA,EAA4B,SAerB,IAAMC,EAAWC,EAAA,CAACC,EAAyBC,IAAsC,CACtF,IAAMC,EAA6BF,EAAQ,OAAO,MAAM,EAiBxD,GAhBAE,EAAY,KAAK,IAAKD,EAAS,CAAC,EAChCC,EAAY,KAAK,IAAKD,EAAS,CAAC,EAChCC,EAAY,KAAK,OAAQD,EAAS,IAAI,EACtCC,EAAY,KAAK,SAAUD,EAAS,MAAM,EAC1CC,EAAY,KAAK,QAASD,EAAS,KAAK,EACxCC,EAAY,KAAK,SAAUD,EAAS,MAAM,EACtCA,EAAS,MACXC,EAAY,KAAK,OAAQD,EAAS,IAAI,EAEpCA,EAAS,IACXC,EAAY,KAAK,KAAMD,EAAS,EAAE,EAEhCA,EAAS,IACXC,EAAY,KAAK,KAAMD,EAAS,EAAE,EAGhCA,EAAS,QAAU,OACrB,QAAWE,KAAWF,EAAS,MAC7BC,EAAY,KAAKC,EAASF,EAAS,MAAME,CAAO,CAAC,EAIrD,OAAIF,EAAS,OACXC,EAAY,KAAK,QAASD,EAAS,KAAK,EAGnCC,CACT,EA7BwB,YAqCXE,EAAqBL,EAAA,CAACC,EAAyBK,IAAwB,CAClF,IAAMJ,EAAqB,CACzB,EAAGI,EAAO,OACV,EAAGA,EAAO,OACV,MAAOA,EAAO,MAAQA,EAAO,OAC7B,OAAQA,EAAO,MAAQA,EAAO,OAC9B,KAAMA,EAAO,KACb,OAAQA,EAAO,OACf,MAAO,MACT,EACmCP,EAASE,EAASC,CAAQ,EACjD,MAAM,CACpB,EAZkC,sBAcrBK,EAAWP,EAAA,CAACC,EAAyBO,IAAsC,CACtF,IAAMC,EAAgBD,EAAS,KAAK,QAAQE,EAAgB,GAAG,EAEzDC,EAA0BV,EAAQ,OAAO,MAAM,EACrDU,EAAS,KAAK,IAAKH,EAAS,CAAC,EAC7BG,EAAS,KAAK,IAAKH,EAAS,CAAC,EAC7BG,EAAS,KAAK,QAAS,QAAQ,EAE/BA,EAAS,MAAM,cAAeH,EAAS,MAAM,EACzCA,EAAS,OACXG,EAAS,KAAK,QAASH,EAAS,KAAK,EAGvC,IAAMI,EAAwBD,EAAS,OAAO,OAAO,EACrD,OAAAC,EAAM,KAAK,IAAKJ,EAAS,EAAIA,EAAS,WAAa,CAAC,EACpDI,EAAM,KAAKH,CAAK,EAETE,CACT,EAlBwB,YAoBXE,EAAYb,EAAA,CAACc,EAAsBC,EAAWC,EAAWC,IAAuB,CAC3F,IAAMC,EAA+BJ,EAAK,OAAO,OAAO,EACxDI,EAAa,KAAK,IAAKH,CAAC,EACxBG,EAAa,KAAK,IAAKF,CAAC,EACxB,IAAMG,KAAwB,eAAYF,CAAI,EAC9CC,EAAa,KAAK,aAAcC,CAAa,CAC/C,EANyB,aAQZC,EAAoBpB,EAAA,CAC/BC,EACAc,EACAC,EACAC,IACS,CACT,IAAMC,EAA6BjB,EAAQ,OAAO,KAAK,EACvDiB,EAAa,KAAK,IAAKH,CAAC,EACxBG,EAAa,KAAK,IAAKF,CAAC,EACxB,IAAMG,KAAwB,eAAYF,CAAI,EAC9CC,EAAa,KAAK,aAAc,IAAIC,CAAa,EAAE,CACrD,EAXiC,qBAapBE,EAAcrB,EAAA,KACM,CAC7B,EAAG,EACH,EAAG,EACH,MAAO,IACP,OAAQ,IACR,KAAM,UACN,OAAQ,OACR,OAAQ,QACR,GAAI,EACJ,GAAI,CACN,GAXyB,eAedsB,EAAatB,EAAA,KACO,CAC7B,EAAG,EACH,EAAG,EACH,MAAO,IACP,OAAQ,IACR,cAAe,QACf,MAAO,OACP,WAAY,EACZ,GAAI,EACJ,GAAI,EACJ,MAAO,EACT,GAZwB", "names": ["import_sanitize_url", "drawRect", "__name", "element", "rectData", "rectElement", "attrKey", "drawBackgroundRect", "bounds", "drawText", "textData", "nText", "lineBreakRegex", "textElem", "tspan", "drawImage", "elem", "x", "y", "link", "imageElement", "sanitizedLink", "drawEmbeddedImage", "getNoteRect", "getTextObj"]}