{"version": 3, "sources": ["../../../src/rendering-util/rendering-elements/shapes/util.ts", "../../../../../node_modules/.pnpm/roughjs@4.6.6_patch_hash=3543d47108cb41b68ec6a671c0e1f9d0cfe2ce524fea5b0992511ae84c3c6b64/node_modules/roughjs/bundled/rough.esm.js", "../../../src/rendering-util/rendering-elements/intersect/intersect-rect.js", "../../../src/rendering-util/rendering-elements/createLabel.js", "../../../src/rendering-util/rendering-elements/shapes/roundedRectPath.ts", "../../../src/rendering-util/rendering-elements/shapes/handDrawnShapeStyles.ts", "../../../src/rendering-util/rendering-elements/clusters.js", "../../../src/rendering-util/rendering-elements/intersect/intersect-node.js", "../../../src/rendering-util/rendering-elements/intersect/intersect-ellipse.js", "../../../src/rendering-util/rendering-elements/intersect/intersect-circle.js", "../../../src/rendering-util/rendering-elements/intersect/intersect-line.js", "../../../src/rendering-util/rendering-elements/intersect/intersect-polygon.js", "../../../src/rendering-util/rendering-elements/intersect/index.js", "../../../src/rendering-util/rendering-elements/shapes/anchor.ts", "../../../src/rendering-util/rendering-elements/shapes/bowTieRect.ts", "../../../src/rendering-util/rendering-elements/shapes/insertPolygonShape.ts", "../../../src/rendering-util/rendering-elements/shapes/card.ts", "../../../src/rendering-util/rendering-elements/shapes/choice.ts", "../../../src/rendering-util/rendering-elements/shapes/circle.ts", "../../../src/rendering-util/rendering-elements/shapes/crossedCircle.ts", "../../../src/rendering-util/rendering-elements/shapes/curlyBraceLeft.ts", "../../../src/rendering-util/rendering-elements/shapes/curlyBraceRight.ts", "../../../src/rendering-util/rendering-elements/shapes/curlyBraces.ts", "../../../src/rendering-util/rendering-elements/shapes/curvedTrapezoid.ts", "../../../src/rendering-util/rendering-elements/shapes/cylinder.ts", "../../../src/rendering-util/rendering-elements/shapes/dividedRect.ts", "../../../src/rendering-util/rendering-elements/shapes/doubleCircle.ts", "../../../src/rendering-util/rendering-elements/shapes/filledCircle.ts", "../../../src/rendering-util/rendering-elements/shapes/flippedTriangle.ts", "../../../src/rendering-util/rendering-elements/shapes/forkJoin.ts", "../../../src/rendering-util/rendering-elements/shapes/halfRoundedRectangle.ts", "../../../src/rendering-util/rendering-elements/shapes/hexagon.ts", "../../../src/rendering-util/rendering-elements/shapes/hourglass.ts", "../../../src/rendering-util/rendering-elements/shapes/icon.ts", "../../../src/rendering-util/rendering-elements/shapes/iconCircle.ts", "../../../src/rendering-util/rendering-elements/shapes/iconRounded.ts", "../../../src/rendering-util/rendering-elements/shapes/iconSquare.ts", "../../../src/rendering-util/rendering-elements/shapes/imageSquare.ts", "../../../src/rendering-util/rendering-elements/shapes/invertedTrapezoid.ts", "../../../src/rendering-util/rendering-elements/shapes/drawRect.ts", "../../../src/rendering-util/rendering-elements/shapes/labelRect.ts", "../../../src/rendering-util/rendering-elements/shapes/leanLeft.ts", "../../../src/rendering-util/rendering-elements/shapes/leanRight.ts", "../../../src/rendering-util/rendering-elements/shapes/lightningBolt.ts", "../../../src/rendering-util/rendering-elements/shapes/linedCylinder.ts", "../../../src/rendering-util/rendering-elements/shapes/linedWaveEdgedRect.ts", "../../../src/rendering-util/rendering-elements/shapes/multiRect.ts", "../../../src/rendering-util/rendering-elements/shapes/multiWaveEdgedRectangle.ts", "../../../src/rendering-util/rendering-elements/shapes/note.ts", "../../../src/rendering-util/rendering-elements/shapes/question.ts", "../../../src/rendering-util/rendering-elements/shapes/rectLeftInvArrow.ts", "../../../src/rendering-util/rendering-elements/shapes/rectWithTitle.ts", "../../../src/rendering-util/rendering-elements/shapes/roundedRect.ts", "../../../src/rendering-util/rendering-elements/shapes/shadedProcess.ts", "../../../src/rendering-util/rendering-elements/shapes/slopedRect.ts", "../../../src/rendering-util/rendering-elements/shapes/squareRect.ts", "../../../src/rendering-util/rendering-elements/shapes/stadium.ts", "../../../src/rendering-util/rendering-elements/shapes/state.ts", "../../../src/rendering-util/rendering-elements/shapes/stateEnd.ts", "../../../src/rendering-util/rendering-elements/shapes/stateStart.ts", "../../../src/rendering-util/rendering-elements/shapes/subroutine.ts", "../../../src/rendering-util/rendering-elements/shapes/taggedRect.ts", "../../../src/rendering-util/rendering-elements/shapes/taggedWaveEdgedRectangle.ts", "../../../src/rendering-util/rendering-elements/shapes/text.ts", "../../../src/rendering-util/rendering-elements/shapes/tiltedCylinder.ts", "../../../src/rendering-util/rendering-elements/shapes/trapezoid.ts", "../../../src/rendering-util/rendering-elements/shapes/trapezoidalPentagon.ts", "../../../src/rendering-util/rendering-elements/shapes/triangle.ts", "../../../src/rendering-util/rendering-elements/shapes/waveEdgedRectangle.ts", "../../../src/rendering-util/rendering-elements/shapes/waveRectangle.ts", "../../../src/rendering-util/rendering-elements/shapes/windowPane.ts", "../../../src/rendering-util/rendering-elements/shapes/erBox.ts", "../../../src/diagrams/class/shapeUtil.ts", "../../../src/rendering-util/rendering-elements/shapes/classBox.ts", "../../../src/rendering-util/rendering-elements/shapes/requirementBox.ts", "../../../src/rendering-util/rendering-elements/shapes/kanbanItem.ts", "../../../src/rendering-util/rendering-elements/shapes.ts", "../../../src/rendering-util/rendering-elements/nodes.ts"], "sourcesContent": ["import { createText } from '../../createText.js';\nimport type { Node } from '../../types.js';\nimport { getConfig } from '../../../diagram-api/diagramAPI.js';\nimport { select } from 'd3';\nimport defaultConfig from '../../../defaultConfig.js';\nimport { evaluate, sanitizeText } from '../../../diagrams/common/common.js';\nimport { decodeEntities, handleUndefinedAttr, parseFontSize } from '../../../utils.js';\nimport type { D3Selection, Point } from '../../../types.js';\n\nexport const labelHelper = async <T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  _classes?: string\n) => {\n  let cssClasses;\n  const useHtmlLabels = node.useHtmlLabels || evaluate(getConfig()?.htmlLabels);\n  if (!_classes) {\n    cssClasses = 'node default';\n  } else {\n    cssClasses = _classes;\n  }\n\n  // Add outer g element\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', cssClasses)\n    .attr('id', node.domId || node.id);\n\n  // Create the label and insert it after the rect\n  const labelEl = shapeSvg\n    .insert('g')\n    .attr('class', 'label')\n    .attr('style', handleUndefinedAttr(node.labelStyle));\n\n  // Replace label with default value if undefined\n  let label;\n  if (node.label === undefined) {\n    label = '';\n  } else {\n    label = typeof node.label === 'string' ? node.label : node.label[0];\n  }\n\n  const text = await createText(labelEl, sanitizeText(decodeEntities(label), getConfig()), {\n    useHtmlLabels,\n    width: node.width || getConfig().flowchart?.wrappingWidth,\n    // @ts-expect-error -- This is currently not used. Should this be `classes` instead?\n    cssClasses: 'markdown-node-label',\n    style: node.labelStyle,\n    addSvgBackground: !!node.icon || !!node.img,\n  });\n  // Get the size of the label\n  let bbox = text.getBBox();\n  const halfPadding = (node?.padding ?? 0) / 2;\n\n  if (useHtmlLabels) {\n    const div = text.children[0];\n    const dv = select(text);\n\n    // if there are images, need to wait for them to load before getting the bounding box\n    const images = div.getElementsByTagName('img');\n    if (images) {\n      const noImgText = label.replace(/<img[^>]*>/g, '').trim() === '';\n\n      await Promise.all(\n        [...images].map(\n          (img) =>\n            new Promise((res) => {\n              /**\n               *\n               */\n              function setupImage() {\n                img.style.display = 'flex';\n                img.style.flexDirection = 'column';\n\n                if (noImgText) {\n                  // default size if no text\n                  const bodyFontSize = getConfig().fontSize\n                    ? getConfig().fontSize\n                    : window.getComputedStyle(document.body).fontSize;\n                  const enlargingFactor = 5;\n                  const [parsedBodyFontSize = defaultConfig.fontSize] = parseFontSize(bodyFontSize);\n                  const width = parsedBodyFontSize * enlargingFactor + 'px';\n                  img.style.minWidth = width;\n                  img.style.maxWidth = width;\n                } else {\n                  img.style.width = '100%';\n                }\n                res(img);\n              }\n              setTimeout(() => {\n                if (img.complete) {\n                  setupImage();\n                }\n              });\n              img.addEventListener('error', setupImage);\n              img.addEventListener('load', setupImage);\n            })\n        )\n      );\n    }\n\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n\n  // Center the label\n  if (useHtmlLabels) {\n    labelEl.attr('transform', 'translate(' + -bbox.width / 2 + ', ' + -bbox.height / 2 + ')');\n  } else {\n    labelEl.attr('transform', 'translate(' + 0 + ', ' + -bbox.height / 2 + ')');\n  }\n  if (node.centerLabel) {\n    labelEl.attr('transform', 'translate(' + -bbox.width / 2 + ', ' + -bbox.height / 2 + ')');\n  }\n  labelEl.insert('rect', ':first-child');\n  return { shapeSvg, bbox, halfPadding, label: labelEl };\n};\nexport const insertLabel = async <T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  label: string,\n  options: {\n    labelStyle?: string | undefined;\n    icon?: boolean | undefined;\n    img?: string | undefined;\n    useHtmlLabels?: boolean | undefined;\n    padding: number;\n    width?: number | undefined;\n    centerLabel?: boolean | undefined;\n    addSvgBackground?: boolean | undefined;\n  }\n) => {\n  const useHtmlLabels = options.useHtmlLabels || evaluate(getConfig()?.flowchart?.htmlLabels);\n\n  // Create the label and insert it after the rect\n  const labelEl = parent\n    .insert('g')\n    .attr('class', 'label')\n    .attr('style', options.labelStyle || '');\n\n  const text = await createText(labelEl, sanitizeText(decodeEntities(label), getConfig()), {\n    useHtmlLabels,\n    width: options.width || getConfig()?.flowchart?.wrappingWidth,\n    style: options.labelStyle,\n    addSvgBackground: !!options.icon || !!options.img,\n  });\n  // Get the size of the label\n  let bbox = text.getBBox();\n  const halfPadding = options.padding / 2;\n\n  if (evaluate(getConfig()?.flowchart?.htmlLabels)) {\n    const div = text.children[0];\n    const dv = select(text);\n\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n\n  // Center the label\n  if (useHtmlLabels) {\n    labelEl.attr('transform', 'translate(' + -bbox.width / 2 + ', ' + -bbox.height / 2 + ')');\n  } else {\n    labelEl.attr('transform', 'translate(' + 0 + ', ' + -bbox.height / 2 + ')');\n  }\n  if (options.centerLabel) {\n    labelEl.attr('transform', 'translate(' + -bbox.width / 2 + ', ' + -bbox.height / 2 + ')');\n  }\n  labelEl.insert('rect', ':first-child');\n  return { shapeSvg: parent, bbox, halfPadding, label: labelEl };\n};\nexport const updateNodeBounds = <T extends SVGGraphicsElement>(\n  node: Node,\n  // D3Selection<SVGGElement> is for the roughjs case, D3Selection<T> is for the non-roughjs case\n  element: D3Selection<SVGGElement> | D3Selection<T>\n) => {\n  const bbox = element.node()!.getBBox();\n  node.width = bbox.width;\n  node.height = bbox.height;\n};\n\n/**\n * @param parent - Parent element to append the polygon to\n * @param w - Width of the polygon\n * @param h - Height of the polygon\n * @param points - Array of points to create the polygon\n */\nexport function insertPolygonShape(\n  parent: D3Selection<SVGGElement>,\n  w: number,\n  h: number,\n  points: Point[]\n) {\n  return parent\n    .insert('polygon', ':first-child')\n    .attr(\n      'points',\n      points\n        .map(function (d) {\n          return d.x + ',' + d.y;\n        })\n        .join(' ')\n    )\n    .attr('class', 'label-container')\n    .attr('transform', 'translate(' + -w / 2 + ',' + h / 2 + ')');\n}\n\nexport const getNodeClasses = (node: Node, extra?: string) =>\n  (node.look === 'handDrawn' ? 'rough-node' : 'node') + ' ' + node.cssClasses + ' ' + (extra || '');\n\nexport function createPathFromPoints(points: Point[]) {\n  const pointStrings = points.map((p, i) => `${i === 0 ? 'M' : 'L'}${p.x},${p.y}`);\n  pointStrings.push('Z');\n  return pointStrings.join(' ');\n}\n\nexport function generateFullSineWavePoints(\n  x1: number,\n  y1: number,\n  x2: number,\n  y2: number,\n  amplitude: number,\n  numCycles: number\n) {\n  const points = [];\n  const steps = 50; // Number of segments to create a smooth curve\n  const deltaX = x2 - x1;\n  const deltaY = y2 - y1;\n  const cycleLength = deltaX / numCycles;\n\n  // Calculate frequency and phase shift\n  const frequency = (2 * Math.PI) / cycleLength;\n  const midY = y1 + deltaY / 2;\n\n  for (let i = 0; i <= steps; i++) {\n    const t = i / steps;\n    const x = x1 + t * deltaX;\n    const y = midY + amplitude * Math.sin(frequency * (x - x1));\n\n    points.push({ x, y });\n  }\n\n  return points;\n}\n\n/**\n * @param centerX - x-coordinate of center of circle\n * @param centerY - y-coordinate of center of circle\n * @param radius - radius of circle\n * @param numPoints - total points required\n * @param startAngle - angle where arc will start\n * @param endAngle - angle where arc will end\n */\nexport function generateCirclePoints(\n  centerX: number,\n  centerY: number,\n  radius: number,\n  numPoints: number,\n  startAngle: number,\n  endAngle: number\n) {\n  const points = [];\n\n  // Convert angles to radians\n  const startAngleRad = (startAngle * Math.PI) / 180;\n  const endAngleRad = (endAngle * Math.PI) / 180;\n\n  // Calculate the angle range in radians\n  const angleRange = endAngleRad - startAngleRad;\n\n  // Calculate the angle step\n  const angleStep = angleRange / (numPoints - 1);\n\n  for (let i = 0; i < numPoints; i++) {\n    const angle = startAngleRad + i * angleStep;\n    const x = centerX + radius * Math.cos(angle);\n    const y = centerY + radius * Math.sin(angle);\n    points.push({ x: -x, y: -y });\n  }\n\n  return points;\n}\n", "function t(t,e,s){if(t&&t.length){const[n,o]=e,a=Math.PI/180*s,h=Math.cos(a),r=Math.sin(a);for(const e of t){const[t,s]=e;e[0]=(t-n)*h-(s-o)*r+n,e[1]=(t-n)*r+(s-o)*h+o}}}function e(t,e){return t[0]===e[0]&&t[1]===e[1]}function s(s,n,o,a=1){const h=o,r=Math.max(n,.1),i=s[0]&&s[0][0]&&\"number\"==typeof s[0][0]?[s]:s,c=[0,0];if(h)for(const e of i)t(e,c,h);const l=function(t,s,n){const o=[];for(const s of t){const t=[...s];e(t[0],t[t.length-1])||t.push([t[0][0],t[0][1]]),t.length>2&&o.push(t)}const a=[];s=Math.max(s,.1);const h=[];for(const t of o)for(let e=0;e<t.length-1;e++){const s=t[e],n=t[e+1];if(s[1]!==n[1]){const t=Math.min(s[1],n[1]);h.push({ymin:t,ymax:Math.max(s[1],n[1]),x:t===s[1]?s[0]:n[0],islope:(n[0]-s[0])/(n[1]-s[1])})}}if(h.sort(((t,e)=>t.ymin<e.ymin?-1:t.ymin>e.ymin?1:t.x<e.x?-1:t.x>e.x?1:t.ymax===e.ymax?0:(t.ymax-e.ymax)/Math.abs(t.ymax-e.ymax))),!h.length)return a;let r=[],i=h[0].ymin,c=0;for(;r.length||h.length;){if(h.length){let t=-1;for(let e=0;e<h.length&&!(h[e].ymin>i);e++)t=e;h.splice(0,t+1).forEach((t=>{r.push({s:i,edge:t})}))}if(r=r.filter((t=>!(t.edge.ymax<=i))),r.sort(((t,e)=>t.edge.x===e.edge.x?0:(t.edge.x-e.edge.x)/Math.abs(t.edge.x-e.edge.x))),(1!==n||c%s==0)&&r.length>1)for(let t=0;t<r.length;t+=2){const e=t+1;if(e>=r.length)break;const s=r[t].edge,n=r[e].edge;a.push([[Math.round(s.x),i],[Math.round(n.x),i]])}i+=n,r.forEach((t=>{t.edge.x=t.edge.x+n*t.edge.islope})),c++}return a}(i,r,a);if(h){for(const e of i)t(e,c,-h);!function(e,s,n){const o=[];e.forEach((t=>o.push(...t))),t(o,s,n)}(l,c,-h)}return l}function n(t,e){var n;const o=e.hachureAngle+90;let a=e.hachureGap;a<0&&(a=4*e.strokeWidth),a=Math.round(Math.max(a,.1));let h=1;return e.roughness>=1&&((null===(n=e.randomizer)||void 0===n?void 0:n.next())||Math.random())>.7&&(h=a),s(t,a,o,h||1)}class o{constructor(t){this.helper=t}fillPolygons(t,e){return this._fillPolygons(t,e)}_fillPolygons(t,e){const s=n(t,e);return{type:\"fillSketch\",ops:this.renderLines(s,e)}}renderLines(t,e){const s=[];for(const n of t)s.push(...this.helper.doubleLineOps(n[0][0],n[0][1],n[1][0],n[1][1],e));return s}}function a(t){const e=t[0],s=t[1];return Math.sqrt(Math.pow(e[0]-s[0],2)+Math.pow(e[1]-s[1],2))}class h extends o{fillPolygons(t,e){let s=e.hachureGap;s<0&&(s=4*e.strokeWidth),s=Math.max(s,.1);const o=n(t,Object.assign({},e,{hachureGap:s})),h=Math.PI/180*e.hachureAngle,r=[],i=.5*s*Math.cos(h),c=.5*s*Math.sin(h);for(const[t,e]of o)a([t,e])&&r.push([[t[0]-i,t[1]+c],[...e]],[[t[0]+i,t[1]-c],[...e]]);return{type:\"fillSketch\",ops:this.renderLines(r,e)}}}class r extends o{fillPolygons(t,e){const s=this._fillPolygons(t,e),n=Object.assign({},e,{hachureAngle:e.hachureAngle+90}),o=this._fillPolygons(t,n);return s.ops=s.ops.concat(o.ops),s}}class i{constructor(t){this.helper=t}fillPolygons(t,e){const s=n(t,e=Object.assign({},e,{hachureAngle:0}));return this.dotsOnLines(s,e)}dotsOnLines(t,e){const s=[];let n=e.hachureGap;n<0&&(n=4*e.strokeWidth),n=Math.max(n,.1);let o=e.fillWeight;o<0&&(o=e.strokeWidth/2);const h=n/4;for(const r of t){const t=a(r),i=t/n,c=Math.ceil(i)-1,l=t-c*n,u=(r[0][0]+r[1][0])/2-n/4,p=Math.min(r[0][1],r[1][1]);for(let t=0;t<c;t++){const a=p+l+t*n,r=u-h+2*Math.random()*h,i=a-h+2*Math.random()*h,c=this.helper.ellipse(r,i,o,o,e);s.push(...c.ops)}}return{type:\"fillSketch\",ops:s}}}class c{constructor(t){this.helper=t}fillPolygons(t,e){const s=n(t,e);return{type:\"fillSketch\",ops:this.dashedLine(s,e)}}dashedLine(t,e){const s=e.dashOffset<0?e.hachureGap<0?4*e.strokeWidth:e.hachureGap:e.dashOffset,n=e.dashGap<0?e.hachureGap<0?4*e.strokeWidth:e.hachureGap:e.dashGap,o=[];return t.forEach((t=>{const h=a(t),r=Math.floor(h/(s+n)),i=(h+n-r*(s+n))/2;let c=t[0],l=t[1];c[0]>l[0]&&(c=t[1],l=t[0]);const u=Math.atan((l[1]-c[1])/(l[0]-c[0]));for(let t=0;t<r;t++){const a=t*(s+n),h=a+s,r=[c[0]+a*Math.cos(u)+i*Math.cos(u),c[1]+a*Math.sin(u)+i*Math.sin(u)],l=[c[0]+h*Math.cos(u)+i*Math.cos(u),c[1]+h*Math.sin(u)+i*Math.sin(u)];o.push(...this.helper.doubleLineOps(r[0],r[1],l[0],l[1],e))}})),o}}class l{constructor(t){this.helper=t}fillPolygons(t,e){const s=e.hachureGap<0?4*e.strokeWidth:e.hachureGap,o=e.zigzagOffset<0?s:e.zigzagOffset,a=n(t,e=Object.assign({},e,{hachureGap:s+o}));return{type:\"fillSketch\",ops:this.zigzagLines(a,o,e)}}zigzagLines(t,e,s){const n=[];return t.forEach((t=>{const o=a(t),h=Math.round(o/(2*e));let r=t[0],i=t[1];r[0]>i[0]&&(r=t[1],i=t[0]);const c=Math.atan((i[1]-r[1])/(i[0]-r[0]));for(let t=0;t<h;t++){const o=2*t*e,a=2*(t+1)*e,h=Math.sqrt(2*Math.pow(e,2)),i=[r[0]+o*Math.cos(c),r[1]+o*Math.sin(c)],l=[r[0]+a*Math.cos(c),r[1]+a*Math.sin(c)],u=[i[0]+h*Math.cos(c+Math.PI/4),i[1]+h*Math.sin(c+Math.PI/4)];n.push(...this.helper.doubleLineOps(i[0],i[1],u[0],u[1],s),...this.helper.doubleLineOps(u[0],u[1],l[0],l[1],s))}})),n}}const u={};class p{constructor(t){this.seed=t}next(){return this.seed?(2**31-1&(this.seed=Math.imul(48271,this.seed)))/2**31:Math.random()}}const f=0,d=1,g=2,M={A:7,a:7,C:6,c:6,H:1,h:1,L:2,l:2,M:2,m:2,Q:4,q:4,S:4,s:4,T:2,t:2,V:1,v:1,Z:0,z:0};function k(t,e){return t.type===e}function b(t){const e=[],s=function(t){const e=new Array;for(;\"\"!==t;)if(t.match(/^([ \\t\\r\\n,]+)/))t=t.substr(RegExp.$1.length);else if(t.match(/^([aAcChHlLmMqQsStTvVzZ])/))e[e.length]={type:f,text:RegExp.$1},t=t.substr(RegExp.$1.length);else{if(!t.match(/^(([-+]?[0-9]+(\\.[0-9]*)?|[-+]?\\.[0-9]+)([eE][-+]?[0-9]+)?)/))return[];e[e.length]={type:d,text:`${parseFloat(RegExp.$1)}`},t=t.substr(RegExp.$1.length)}return e[e.length]={type:g,text:\"\"},e}(t);let n=\"BOD\",o=0,a=s[o];for(;!k(a,g);){let h=0;const r=[];if(\"BOD\"===n){if(\"M\"!==a.text&&\"m\"!==a.text)return b(\"M0,0\"+t);o++,h=M[a.text],n=a.text}else k(a,d)?h=M[n]:(o++,h=M[a.text],n=a.text);if(!(o+h<s.length))throw new Error(\"Path data ended short\");for(let t=o;t<o+h;t++){const e=s[t];if(!k(e,d))throw new Error(\"Param not a number: \"+n+\",\"+e.text);r[r.length]=+e.text}if(\"number\"!=typeof M[n])throw new Error(\"Bad segment: \"+n);{const t={key:n,data:r};e.push(t),o+=h,a=s[o],\"M\"===n&&(n=\"L\"),\"m\"===n&&(n=\"l\")}}return e}function y(t){let e=0,s=0,n=0,o=0;const a=[];for(const{key:h,data:r}of t)switch(h){case\"M\":a.push({key:\"M\",data:[...r]}),[e,s]=r,[n,o]=r;break;case\"m\":e+=r[0],s+=r[1],a.push({key:\"M\",data:[e,s]}),n=e,o=s;break;case\"L\":a.push({key:\"L\",data:[...r]}),[e,s]=r;break;case\"l\":e+=r[0],s+=r[1],a.push({key:\"L\",data:[e,s]});break;case\"C\":a.push({key:\"C\",data:[...r]}),e=r[4],s=r[5];break;case\"c\":{const t=r.map(((t,n)=>n%2?t+s:t+e));a.push({key:\"C\",data:t}),e=t[4],s=t[5];break}case\"Q\":a.push({key:\"Q\",data:[...r]}),e=r[2],s=r[3];break;case\"q\":{const t=r.map(((t,n)=>n%2?t+s:t+e));a.push({key:\"Q\",data:t}),e=t[2],s=t[3];break}case\"A\":a.push({key:\"A\",data:[...r]}),e=r[5],s=r[6];break;case\"a\":e+=r[5],s+=r[6],a.push({key:\"A\",data:[r[0],r[1],r[2],r[3],r[4],e,s]});break;case\"H\":a.push({key:\"H\",data:[...r]}),e=r[0];break;case\"h\":e+=r[0],a.push({key:\"H\",data:[e]});break;case\"V\":a.push({key:\"V\",data:[...r]}),s=r[0];break;case\"v\":s+=r[0],a.push({key:\"V\",data:[s]});break;case\"S\":a.push({key:\"S\",data:[...r]}),e=r[2],s=r[3];break;case\"s\":{const t=r.map(((t,n)=>n%2?t+s:t+e));a.push({key:\"S\",data:t}),e=t[2],s=t[3];break}case\"T\":a.push({key:\"T\",data:[...r]}),e=r[0],s=r[1];break;case\"t\":e+=r[0],s+=r[1],a.push({key:\"T\",data:[e,s]});break;case\"Z\":case\"z\":a.push({key:\"Z\",data:[]}),e=n,s=o}return a}function m(t){const e=[];let s=\"\",n=0,o=0,a=0,h=0,r=0,i=0;for(const{key:c,data:l}of t){switch(c){case\"M\":e.push({key:\"M\",data:[...l]}),[n,o]=l,[a,h]=l;break;case\"C\":e.push({key:\"C\",data:[...l]}),n=l[4],o=l[5],r=l[2],i=l[3];break;case\"L\":e.push({key:\"L\",data:[...l]}),[n,o]=l;break;case\"H\":n=l[0],e.push({key:\"L\",data:[n,o]});break;case\"V\":o=l[0],e.push({key:\"L\",data:[n,o]});break;case\"S\":{let t=0,a=0;\"C\"===s||\"S\"===s?(t=n+(n-r),a=o+(o-i)):(t=n,a=o),e.push({key:\"C\",data:[t,a,...l]}),r=l[0],i=l[1],n=l[2],o=l[3];break}case\"T\":{const[t,a]=l;let h=0,c=0;\"Q\"===s||\"T\"===s?(h=n+(n-r),c=o+(o-i)):(h=n,c=o);const u=n+2*(h-n)/3,p=o+2*(c-o)/3,f=t+2*(h-t)/3,d=a+2*(c-a)/3;e.push({key:\"C\",data:[u,p,f,d,t,a]}),r=h,i=c,n=t,o=a;break}case\"Q\":{const[t,s,a,h]=l,c=n+2*(t-n)/3,u=o+2*(s-o)/3,p=a+2*(t-a)/3,f=h+2*(s-h)/3;e.push({key:\"C\",data:[c,u,p,f,a,h]}),r=t,i=s,n=a,o=h;break}case\"A\":{const t=Math.abs(l[0]),s=Math.abs(l[1]),a=l[2],h=l[3],r=l[4],i=l[5],c=l[6];if(0===t||0===s)e.push({key:\"C\",data:[n,o,i,c,i,c]}),n=i,o=c;else if(n!==i||o!==c){x(n,o,i,c,t,s,a,h,r).forEach((function(t){e.push({key:\"C\",data:t})})),n=i,o=c}break}case\"Z\":e.push({key:\"Z\",data:[]}),n=a,o=h}s=c}return e}function w(t,e,s){return[t*Math.cos(s)-e*Math.sin(s),t*Math.sin(s)+e*Math.cos(s)]}function x(t,e,s,n,o,a,h,r,i,c){const l=(u=h,Math.PI*u/180);var u;let p=[],f=0,d=0,g=0,M=0;if(c)[f,d,g,M]=c;else{[t,e]=w(t,e,-l),[s,n]=w(s,n,-l);const h=(t-s)/2,c=(e-n)/2;let u=h*h/(o*o)+c*c/(a*a);u>1&&(u=Math.sqrt(u),o*=u,a*=u);const p=o*o,k=a*a,b=p*k-p*c*c-k*h*h,y=p*c*c+k*h*h,m=(r===i?-1:1)*Math.sqrt(Math.abs(b/y));g=m*o*c/a+(t+s)/2,M=m*-a*h/o+(e+n)/2,f=Math.asin(parseFloat(((e-M)/a).toFixed(9))),d=Math.asin(parseFloat(((n-M)/a).toFixed(9))),t<g&&(f=Math.PI-f),s<g&&(d=Math.PI-d),f<0&&(f=2*Math.PI+f),d<0&&(d=2*Math.PI+d),i&&f>d&&(f-=2*Math.PI),!i&&d>f&&(d-=2*Math.PI)}let k=d-f;if(Math.abs(k)>120*Math.PI/180){const t=d,e=s,r=n;d=i&&d>f?f+120*Math.PI/180*1:f+120*Math.PI/180*-1,p=x(s=g+o*Math.cos(d),n=M+a*Math.sin(d),e,r,o,a,h,0,i,[d,t,g,M])}k=d-f;const b=Math.cos(f),y=Math.sin(f),m=Math.cos(d),P=Math.sin(d),v=Math.tan(k/4),S=4/3*o*v,O=4/3*a*v,L=[t,e],T=[t+S*y,e-O*b],D=[s+S*P,n-O*m],A=[s,n];if(T[0]=2*L[0]-T[0],T[1]=2*L[1]-T[1],c)return[T,D,A].concat(p);{p=[T,D,A].concat(p);const t=[];for(let e=0;e<p.length;e+=3){const s=w(p[e][0],p[e][1],l),n=w(p[e+1][0],p[e+1][1],l),o=w(p[e+2][0],p[e+2][1],l);t.push([s[0],s[1],n[0],n[1],o[0],o[1]])}return t}}const P={randOffset:function(t,e){return G(t,e)},randOffsetWithRange:function(t,e,s){return E(t,e,s)},ellipse:function(t,e,s,n,o){const a=T(s,n,o);return D(t,e,o,a).opset},doubleLineOps:function(t,e,s,n,o){return $(t,e,s,n,o,!0)}};function v(t,e,s,n,o){return{type:\"path\",ops:$(t,e,s,n,o)}}function S(t,e,s){const n=(t||[]).length;if(n>2){const o=[];for(let e=0;e<n-1;e++)o.push(...$(t[e][0],t[e][1],t[e+1][0],t[e+1][1],s));return e&&o.push(...$(t[n-1][0],t[n-1][1],t[0][0],t[0][1],s)),{type:\"path\",ops:o}}return 2===n?v(t[0][0],t[0][1],t[1][0],t[1][1],s):{type:\"path\",ops:[]}}function O(t,e,s,n,o){return function(t,e){return S(t,!0,e)}([[t,e],[t+s,e],[t+s,e+n],[t,e+n]],o)}function L(t,e){if(t.length){const s=\"number\"==typeof t[0][0]?[t]:t,n=j(s[0],1*(1+.2*e.roughness),e),o=e.disableMultiStroke?[]:j(s[0],1.5*(1+.22*e.roughness),z(e));for(let t=1;t<s.length;t++){const a=s[t];if(a.length){const t=j(a,1*(1+.2*e.roughness),e),s=e.disableMultiStroke?[]:j(a,1.5*(1+.22*e.roughness),z(e));for(const e of t)\"move\"!==e.op&&n.push(e);for(const t of s)\"move\"!==t.op&&o.push(t)}}return{type:\"path\",ops:n.concat(o)}}return{type:\"path\",ops:[]}}function T(t,e,s){const n=Math.sqrt(2*Math.PI*Math.sqrt((Math.pow(t/2,2)+Math.pow(e/2,2))/2)),o=Math.ceil(Math.max(s.curveStepCount,s.curveStepCount/Math.sqrt(200)*n)),a=2*Math.PI/o;let h=Math.abs(t/2),r=Math.abs(e/2);const i=1-s.curveFitting;return h+=G(h*i,s),r+=G(r*i,s),{increment:a,rx:h,ry:r}}function D(t,e,s,n){const[o,a]=F(n.increment,t,e,n.rx,n.ry,1,n.increment*E(.1,E(.4,1,s),s),s);let h=q(o,null,s);if(!s.disableMultiStroke&&0!==s.roughness){const[o]=F(n.increment,t,e,n.rx,n.ry,1.5,0,s),a=q(o,null,s);h=h.concat(a)}return{estimatedPoints:a,opset:{type:\"path\",ops:h}}}function A(t,e,s,n,o,a,h,r,i){const c=t,l=e;let u=Math.abs(s/2),p=Math.abs(n/2);u+=G(.01*u,i),p+=G(.01*p,i);let f=o,d=a;for(;f<0;)f+=2*Math.PI,d+=2*Math.PI;d-f>2*Math.PI&&(f=0,d=2*Math.PI);const g=2*Math.PI/i.curveStepCount,M=Math.min(g/2,(d-f)/2),k=V(M,c,l,u,p,f,d,1,i);if(!i.disableMultiStroke){const t=V(M,c,l,u,p,f,d,1.5,i);k.push(...t)}return h&&(r?k.push(...$(c,l,c+u*Math.cos(f),l+p*Math.sin(f),i),...$(c,l,c+u*Math.cos(d),l+p*Math.sin(d),i)):k.push({op:\"lineTo\",data:[c,l]},{op:\"lineTo\",data:[c+u*Math.cos(f),l+p*Math.sin(f)]})),{type:\"path\",ops:k}}function _(t,e){const s=m(y(b(t))),n=[];let o=[0,0],a=[0,0];for(const{key:t,data:h}of s)switch(t){case\"M\":a=[h[0],h[1]],o=[h[0],h[1]];break;case\"L\":n.push(...$(a[0],a[1],h[0],h[1],e)),a=[h[0],h[1]];break;case\"C\":{const[t,s,o,r,i,c]=h;n.push(...Z(t,s,o,r,i,c,a,e)),a=[i,c];break}case\"Z\":n.push(...$(a[0],a[1],o[0],o[1],e)),a=[o[0],o[1]]}return{type:\"path\",ops:n}}function I(t,e){const s=[];for(const n of t)if(n.length){const t=e.maxRandomnessOffset||0,o=n.length;if(o>2){s.push({op:\"move\",data:[n[0][0]+G(t,e),n[0][1]+G(t,e)]});for(let a=1;a<o;a++)s.push({op:\"lineTo\",data:[n[a][0]+G(t,e),n[a][1]+G(t,e)]})}}return{type:\"fillPath\",ops:s}}function C(t,e){return function(t,e){let s=t.fillStyle||\"hachure\";if(!u[s])switch(s){case\"zigzag\":u[s]||(u[s]=new h(e));break;case\"cross-hatch\":u[s]||(u[s]=new r(e));break;case\"dots\":u[s]||(u[s]=new i(e));break;case\"dashed\":u[s]||(u[s]=new c(e));break;case\"zigzag-line\":u[s]||(u[s]=new l(e));break;default:s=\"hachure\",u[s]||(u[s]=new o(e))}return u[s]}(e,P).fillPolygons(t,e)}function z(t){const e=Object.assign({},t);return e.randomizer=void 0,t.seed&&(e.seed=t.seed+1),e}function W(t){return t.randomizer||(t.randomizer=new p(t.seed||0)),t.randomizer.next()}function E(t,e,s,n=1){return s.roughness*n*(W(s)*(e-t)+t)}function G(t,e,s=1){return E(-t,t,e,s)}function $(t,e,s,n,o,a=!1){const h=a?o.disableMultiStrokeFill:o.disableMultiStroke,r=R(t,e,s,n,o,!0,!1);if(h)return r;const i=R(t,e,s,n,o,!0,!0);return r.concat(i)}function R(t,e,s,n,o,a,h){const r=Math.pow(t-s,2)+Math.pow(e-n,2),i=Math.sqrt(r);let c=1;c=i<200?1:i>500?.4:-.0016668*i+1.233334;let l=o.maxRandomnessOffset||0;l*l*100>r&&(l=i/10);const u=l/2,p=.2+.2*W(o);let f=o.bowing*o.maxRandomnessOffset*(n-e)/200,d=o.bowing*o.maxRandomnessOffset*(t-s)/200;f=G(f,o,c),d=G(d,o,c);const g=[],M=()=>G(u,o,c),k=()=>G(l,o,c),b=o.preserveVertices;return a&&(h?g.push({op:\"move\",data:[t+(b?0:M()),e+(b?0:M())]}):g.push({op:\"move\",data:[t+(b?0:G(l,o,c)),e+(b?0:G(l,o,c))]})),h?g.push({op:\"bcurveTo\",data:[f+t+(s-t)*p+M(),d+e+(n-e)*p+M(),f+t+2*(s-t)*p+M(),d+e+2*(n-e)*p+M(),s+(b?0:M()),n+(b?0:M())]}):g.push({op:\"bcurveTo\",data:[f+t+(s-t)*p+k(),d+e+(n-e)*p+k(),f+t+2*(s-t)*p+k(),d+e+2*(n-e)*p+k(),s+(b?0:k()),n+(b?0:k())]}),g}function j(t,e,s){if(!t.length)return[];const n=[];n.push([t[0][0]+G(e,s),t[0][1]+G(e,s)]),n.push([t[0][0]+G(e,s),t[0][1]+G(e,s)]);for(let o=1;o<t.length;o++)n.push([t[o][0]+G(e,s),t[o][1]+G(e,s)]),o===t.length-1&&n.push([t[o][0]+G(e,s),t[o][1]+G(e,s)]);return q(n,null,s)}function q(t,e,s){const n=t.length,o=[];if(n>3){const a=[],h=1-s.curveTightness;o.push({op:\"move\",data:[t[1][0],t[1][1]]});for(let e=1;e+2<n;e++){const s=t[e];a[0]=[s[0],s[1]],a[1]=[s[0]+(h*t[e+1][0]-h*t[e-1][0])/6,s[1]+(h*t[e+1][1]-h*t[e-1][1])/6],a[2]=[t[e+1][0]+(h*t[e][0]-h*t[e+2][0])/6,t[e+1][1]+(h*t[e][1]-h*t[e+2][1])/6],a[3]=[t[e+1][0],t[e+1][1]],o.push({op:\"bcurveTo\",data:[a[1][0],a[1][1],a[2][0],a[2][1],a[3][0],a[3][1]]})}if(e&&2===e.length){const t=s.maxRandomnessOffset;o.push({op:\"lineTo\",data:[e[0]+G(t,s),e[1]+G(t,s)]})}}else 3===n?(o.push({op:\"move\",data:[t[1][0],t[1][1]]}),o.push({op:\"bcurveTo\",data:[t[1][0],t[1][1],t[2][0],t[2][1],t[2][0],t[2][1]]})):2===n&&o.push(...R(t[0][0],t[0][1],t[1][0],t[1][1],s,!0,!0));return o}function F(t,e,s,n,o,a,h,r){const i=[],c=[];if(0===r.roughness){t/=4,c.push([e+n*Math.cos(-t),s+o*Math.sin(-t)]);for(let a=0;a<=2*Math.PI;a+=t){const t=[e+n*Math.cos(a),s+o*Math.sin(a)];i.push(t),c.push(t)}c.push([e+n*Math.cos(0),s+o*Math.sin(0)]),c.push([e+n*Math.cos(t),s+o*Math.sin(t)])}else{const l=G(.5,r)-Math.PI/2;c.push([G(a,r)+e+.9*n*Math.cos(l-t),G(a,r)+s+.9*o*Math.sin(l-t)]);const u=2*Math.PI+l-.01;for(let h=l;h<u;h+=t){const t=[G(a,r)+e+n*Math.cos(h),G(a,r)+s+o*Math.sin(h)];i.push(t),c.push(t)}c.push([G(a,r)+e+n*Math.cos(l+2*Math.PI+.5*h),G(a,r)+s+o*Math.sin(l+2*Math.PI+.5*h)]),c.push([G(a,r)+e+.98*n*Math.cos(l+h),G(a,r)+s+.98*o*Math.sin(l+h)]),c.push([G(a,r)+e+.9*n*Math.cos(l+.5*h),G(a,r)+s+.9*o*Math.sin(l+.5*h)])}return[c,i]}function V(t,e,s,n,o,a,h,r,i){const c=a+G(.1,i),l=[];l.push([G(r,i)+e+.9*n*Math.cos(c-t),G(r,i)+s+.9*o*Math.sin(c-t)]);for(let a=c;a<=h;a+=t)l.push([G(r,i)+e+n*Math.cos(a),G(r,i)+s+o*Math.sin(a)]);return l.push([e+n*Math.cos(h),s+o*Math.sin(h)]),l.push([e+n*Math.cos(h),s+o*Math.sin(h)]),q(l,null,i)}function Z(t,e,s,n,o,a,h,r){const i=[],c=[r.maxRandomnessOffset||1,(r.maxRandomnessOffset||1)+.3];let l=[0,0];const u=r.disableMultiStroke?1:2,p=r.preserveVertices;for(let f=0;f<u;f++)0===f?i.push({op:\"move\",data:[h[0],h[1]]}):i.push({op:\"move\",data:[h[0]+(p?0:G(c[0],r)),h[1]+(p?0:G(c[0],r))]}),l=p?[o,a]:[o+G(c[f],r),a+G(c[f],r)],i.push({op:\"bcurveTo\",data:[t+G(c[f],r),e+G(c[f],r),s+G(c[f],r),n+G(c[f],r),l[0],l[1]]});return i}function Q(t){return[...t]}function H(t,e=0){const s=t.length;if(s<3)throw new Error(\"A curve must have at least three points.\");const n=[];if(3===s)n.push(Q(t[0]),Q(t[1]),Q(t[2]),Q(t[2]));else{const s=[];s.push(t[0],t[0]);for(let e=1;e<t.length;e++)s.push(t[e]),e===t.length-1&&s.push(t[e]);const o=[],a=1-e;n.push(Q(s[0]));for(let t=1;t+2<s.length;t++){const e=s[t];o[0]=[e[0],e[1]],o[1]=[e[0]+(a*s[t+1][0]-a*s[t-1][0])/6,e[1]+(a*s[t+1][1]-a*s[t-1][1])/6],o[2]=[s[t+1][0]+(a*s[t][0]-a*s[t+2][0])/6,s[t+1][1]+(a*s[t][1]-a*s[t+2][1])/6],o[3]=[s[t+1][0],s[t+1][1]],n.push(o[1],o[2],o[3])}}return n}function N(t,e){return Math.pow(t[0]-e[0],2)+Math.pow(t[1]-e[1],2)}function B(t,e,s){const n=N(e,s);if(0===n)return N(t,e);let o=((t[0]-e[0])*(s[0]-e[0])+(t[1]-e[1])*(s[1]-e[1]))/n;return o=Math.max(0,Math.min(1,o)),N(t,J(e,s,o))}function J(t,e,s){return[t[0]+(e[0]-t[0])*s,t[1]+(e[1]-t[1])*s]}function K(t,e,s,n){const o=n||[];if(function(t,e){const s=t[e+0],n=t[e+1],o=t[e+2],a=t[e+3];let h=3*n[0]-2*s[0]-a[0];h*=h;let r=3*n[1]-2*s[1]-a[1];r*=r;let i=3*o[0]-2*a[0]-s[0];i*=i;let c=3*o[1]-2*a[1]-s[1];return c*=c,h<i&&(h=i),r<c&&(r=c),h+r}(t,e)<s){const s=t[e+0];if(o.length){(a=o[o.length-1],h=s,Math.sqrt(N(a,h)))>1&&o.push(s)}else o.push(s);o.push(t[e+3])}else{const n=.5,a=t[e+0],h=t[e+1],r=t[e+2],i=t[e+3],c=J(a,h,n),l=J(h,r,n),u=J(r,i,n),p=J(c,l,n),f=J(l,u,n),d=J(p,f,n);K([a,c,p,d],0,s,o),K([d,f,u,i],0,s,o)}var a,h;return o}function U(t,e){return X(t,0,t.length,e)}function X(t,e,s,n,o){const a=o||[],h=t[e],r=t[s-1];let i=0,c=1;for(let n=e+1;n<s-1;++n){const e=B(t[n],h,r);e>i&&(i=e,c=n)}return Math.sqrt(i)>n?(X(t,e,c+1,n,a),X(t,c,s,n,a)):(a.length||a.push(h),a.push(r)),a}function Y(t,e=.15,s){const n=[],o=(t.length-1)/3;for(let s=0;s<o;s++){K(t,3*s,e,n)}return s&&s>0?X(n,0,n.length,s):n}const tt=\"none\";class et{constructor(t){this.defaultOptions={maxRandomnessOffset:2,roughness:1,bowing:1,stroke:\"#000\",strokeWidth:1,curveTightness:0,curveFitting:.95,curveStepCount:9,fillStyle:\"hachure\",fillWeight:-1,hachureAngle:-41,hachureGap:-1,dashOffset:-1,dashGap:-1,zigzagOffset:-1,seed:0,disableMultiStroke:!1,disableMultiStrokeFill:!1,preserveVertices:!1,fillShapeRoughnessGain:.8},this.config=t||{},this.config.options&&(this.defaultOptions=this._o(this.config.options))}static newSeed(){return Math.floor(Math.random()*2**31)}_o(t){return t?Object.assign({},this.defaultOptions,t):this.defaultOptions}_d(t,e,s){return{shape:t,sets:e||[],options:s||this.defaultOptions}}line(t,e,s,n,o){const a=this._o(o);return this._d(\"line\",[v(t,e,s,n,a)],a)}rectangle(t,e,s,n,o){const a=this._o(o),h=[],r=O(t,e,s,n,a);if(a.fill){const o=[[t,e],[t+s,e],[t+s,e+n],[t,e+n]];\"solid\"===a.fillStyle?h.push(I([o],a)):h.push(C([o],a))}return a.stroke!==tt&&h.push(r),this._d(\"rectangle\",h,a)}ellipse(t,e,s,n,o){const a=this._o(o),h=[],r=T(s,n,a),i=D(t,e,a,r);if(a.fill)if(\"solid\"===a.fillStyle){const s=D(t,e,a,r).opset;s.type=\"fillPath\",h.push(s)}else h.push(C([i.estimatedPoints],a));return a.stroke!==tt&&h.push(i.opset),this._d(\"ellipse\",h,a)}circle(t,e,s,n){const o=this.ellipse(t,e,s,s,n);return o.shape=\"circle\",o}linearPath(t,e){const s=this._o(e);return this._d(\"linearPath\",[S(t,!1,s)],s)}arc(t,e,s,n,o,a,h=!1,r){const i=this._o(r),c=[],l=A(t,e,s,n,o,a,h,!0,i);if(h&&i.fill)if(\"solid\"===i.fillStyle){const h=Object.assign({},i);h.disableMultiStroke=!0;const r=A(t,e,s,n,o,a,!0,!1,h);r.type=\"fillPath\",c.push(r)}else c.push(function(t,e,s,n,o,a,h){const r=t,i=e;let c=Math.abs(s/2),l=Math.abs(n/2);c+=G(.01*c,h),l+=G(.01*l,h);let u=o,p=a;for(;u<0;)u+=2*Math.PI,p+=2*Math.PI;p-u>2*Math.PI&&(u=0,p=2*Math.PI);const f=(p-u)/h.curveStepCount,d=[];for(let t=u;t<=p;t+=f)d.push([r+c*Math.cos(t),i+l*Math.sin(t)]);return d.push([r+c*Math.cos(p),i+l*Math.sin(p)]),d.push([r,i]),C([d],h)}(t,e,s,n,o,a,i));return i.stroke!==tt&&c.push(l),this._d(\"arc\",c,i)}curve(t,e){const s=this._o(e),n=[],o=L(t,s);if(s.fill&&s.fill!==tt)if(\"solid\"===s.fillStyle){const e=L(t,Object.assign(Object.assign({},s),{disableMultiStroke:!0,roughness:s.roughness?s.roughness+s.fillShapeRoughnessGain:0}));n.push({type:\"fillPath\",ops:this._mergedShape(e.ops)})}else{const e=[],o=t;if(o.length){const t=\"number\"==typeof o[0][0]?[o]:o;for(const n of t)n.length<3?e.push(...n):3===n.length?e.push(...Y(H([n[0],n[0],n[1],n[2]]),10,(1+s.roughness)/2)):e.push(...Y(H(n),10,(1+s.roughness)/2))}e.length&&n.push(C([e],s))}return s.stroke!==tt&&n.push(o),this._d(\"curve\",n,s)}polygon(t,e){const s=this._o(e),n=[],o=S(t,!0,s);return s.fill&&(\"solid\"===s.fillStyle?n.push(I([t],s)):n.push(C([t],s))),s.stroke!==tt&&n.push(o),this._d(\"polygon\",n,s)}path(t,e){const s=this._o(e),n=[];if(!t)return this._d(\"path\",n,s);t=(t||\"\").replace(/\\n/g,\" \").replace(/(-\\s)/g,\"-\").replace(\"/(ss)/g\",\" \");const o=s.fill&&\"transparent\"!==s.fill&&s.fill!==tt,a=s.stroke!==tt,h=!!(s.simplification&&s.simplification<1),r=function(t,e,s){const n=m(y(b(t))),o=[];let a=[],h=[0,0],r=[];const i=()=>{r.length>=4&&a.push(...Y(r,e)),r=[]},c=()=>{i(),a.length&&(o.push(a),a=[])};for(const{key:t,data:e}of n)switch(t){case\"M\":c(),h=[e[0],e[1]],a.push(h);break;case\"L\":i(),a.push([e[0],e[1]]);break;case\"C\":if(!r.length){const t=a.length?a[a.length-1]:h;r.push([t[0],t[1]])}r.push([e[0],e[1]]),r.push([e[2],e[3]]),r.push([e[4],e[5]]);break;case\"Z\":i(),a.push([h[0],h[1]])}if(c(),!s)return o;const l=[];for(const t of o){const e=U(t,s);e.length&&l.push(e)}return l}(t,1,h?4-4*(s.simplification||1):(1+s.roughness)/2),i=_(t,s);if(o)if(\"solid\"===s.fillStyle)if(1===r.length){const e=_(t,Object.assign(Object.assign({},s),{disableMultiStroke:!0,roughness:s.roughness?s.roughness+s.fillShapeRoughnessGain:0}));n.push({type:\"fillPath\",ops:this._mergedShape(e.ops)})}else n.push(I(r,s));else n.push(C(r,s));return a&&(h?r.forEach((t=>{n.push(S(t,!1,s))})):n.push(i)),this._d(\"path\",n,s)}opsToPath(t,e){let s=\"\";for(const n of t.ops){const t=\"number\"==typeof e&&e>=0?n.data.map((t=>+t.toFixed(e))):n.data;switch(n.op){case\"move\":s+=`M${t[0]} ${t[1]} `;break;case\"bcurveTo\":s+=`C${t[0]} ${t[1]}, ${t[2]} ${t[3]}, ${t[4]} ${t[5]} `;break;case\"lineTo\":s+=`L${t[0]} ${t[1]} `}}return s.trim()}toPaths(t){const e=t.sets||[],s=t.options||this.defaultOptions,n=[];for(const t of e){let e=null;switch(t.type){case\"path\":e={d:this.opsToPath(t),stroke:s.stroke,strokeWidth:s.strokeWidth,fill:tt};break;case\"fillPath\":e={d:this.opsToPath(t),stroke:tt,strokeWidth:0,fill:s.fill||tt};break;case\"fillSketch\":e=this.fillSketch(t,s)}e&&n.push(e)}return n}fillSketch(t,e){let s=e.fillWeight;return s<0&&(s=e.strokeWidth/2),{d:this.opsToPath(t),stroke:e.fill||tt,strokeWidth:s,fill:tt}}_mergedShape(t){return t.filter(((t,e)=>0===e||\"move\"!==t.op))}}class st{constructor(t,e){this.canvas=t,this.ctx=this.canvas.getContext(\"2d\"),this.gen=new et(e)}draw(t){const e=t.sets||[],s=t.options||this.getDefaultOptions(),n=this.ctx,o=t.options.fixedDecimalPlaceDigits;for(const a of e)switch(a.type){case\"path\":n.save(),n.strokeStyle=\"none\"===s.stroke?\"transparent\":s.stroke,n.lineWidth=s.strokeWidth,s.strokeLineDash&&n.setLineDash(s.strokeLineDash),s.strokeLineDashOffset&&(n.lineDashOffset=s.strokeLineDashOffset),this._drawToContext(n,a,o),n.restore();break;case\"fillPath\":{n.save(),n.fillStyle=s.fill||\"\";const e=\"curve\"===t.shape||\"polygon\"===t.shape||\"path\"===t.shape?\"evenodd\":\"nonzero\";this._drawToContext(n,a,o,e),n.restore();break}case\"fillSketch\":this.fillSketch(n,a,s)}}fillSketch(t,e,s){let n=s.fillWeight;n<0&&(n=s.strokeWidth/2),t.save(),s.fillLineDash&&t.setLineDash(s.fillLineDash),s.fillLineDashOffset&&(t.lineDashOffset=s.fillLineDashOffset),t.strokeStyle=s.fill||\"\",t.lineWidth=n,this._drawToContext(t,e,s.fixedDecimalPlaceDigits),t.restore()}_drawToContext(t,e,s,n=\"nonzero\"){t.beginPath();for(const n of e.ops){const e=\"number\"==typeof s&&s>=0?n.data.map((t=>+t.toFixed(s))):n.data;switch(n.op){case\"move\":t.moveTo(e[0],e[1]);break;case\"bcurveTo\":t.bezierCurveTo(e[0],e[1],e[2],e[3],e[4],e[5]);break;case\"lineTo\":t.lineTo(e[0],e[1])}}\"fillPath\"===e.type?t.fill(n):t.stroke()}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}line(t,e,s,n,o){const a=this.gen.line(t,e,s,n,o);return this.draw(a),a}rectangle(t,e,s,n,o){const a=this.gen.rectangle(t,e,s,n,o);return this.draw(a),a}ellipse(t,e,s,n,o){const a=this.gen.ellipse(t,e,s,n,o);return this.draw(a),a}circle(t,e,s,n){const o=this.gen.circle(t,e,s,n);return this.draw(o),o}linearPath(t,e){const s=this.gen.linearPath(t,e);return this.draw(s),s}polygon(t,e){const s=this.gen.polygon(t,e);return this.draw(s),s}arc(t,e,s,n,o,a,h=!1,r){const i=this.gen.arc(t,e,s,n,o,a,h,r);return this.draw(i),i}curve(t,e){const s=this.gen.curve(t,e);return this.draw(s),s}path(t,e){const s=this.gen.path(t,e);return this.draw(s),s}}const nt=\"http://www.w3.org/2000/svg\";class ot{constructor(t,e){this.svg=t,this.gen=new et(e)}draw(t){const e=t.sets||[],s=t.options||this.getDefaultOptions(),n=this.svg.ownerDocument||window.document,o=n.createElementNS(nt,\"g\"),a=t.options.fixedDecimalPlaceDigits;for(const h of e){let e=null;switch(h.type){case\"path\":e=n.createElementNS(nt,\"path\"),e.setAttribute(\"d\",this.opsToPath(h,a)),e.setAttribute(\"stroke\",s.stroke),e.setAttribute(\"stroke-width\",s.strokeWidth+\"\"),e.setAttribute(\"fill\",\"none\"),s.strokeLineDash&&e.setAttribute(\"stroke-dasharray\",s.strokeLineDash.join(\" \").trim()),s.strokeLineDashOffset&&e.setAttribute(\"stroke-dashoffset\",`${s.strokeLineDashOffset}`);break;case\"fillPath\":e=n.createElementNS(nt,\"path\"),e.setAttribute(\"d\",this.opsToPath(h,a)),e.setAttribute(\"stroke\",\"none\"),e.setAttribute(\"stroke-width\",\"0\"),e.setAttribute(\"fill\",s.fill||\"\"),\"curve\"!==t.shape&&\"polygon\"!==t.shape||e.setAttribute(\"fill-rule\",\"evenodd\");break;case\"fillSketch\":e=this.fillSketch(n,h,s)}e&&o.appendChild(e)}return o}fillSketch(t,e,s){let n=s.fillWeight;n<0&&(n=s.strokeWidth/2);const o=t.createElementNS(nt,\"path\");return o.setAttribute(\"d\",this.opsToPath(e,s.fixedDecimalPlaceDigits)),o.setAttribute(\"stroke\",s.fill||\"\"),o.setAttribute(\"stroke-width\",n+\"\"),o.setAttribute(\"fill\",\"none\"),s.fillLineDash&&o.setAttribute(\"stroke-dasharray\",s.fillLineDash.join(\" \").trim()),s.fillLineDashOffset&&o.setAttribute(\"stroke-dashoffset\",`${s.fillLineDashOffset}`),o}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}opsToPath(t,e){return this.gen.opsToPath(t,e)}line(t,e,s,n,o){const a=this.gen.line(t,e,s,n,o);return this.draw(a)}rectangle(t,e,s,n,o){const a=this.gen.rectangle(t,e,s,n,o);return this.draw(a)}ellipse(t,e,s,n,o){const a=this.gen.ellipse(t,e,s,n,o);return this.draw(a)}circle(t,e,s,n){const o=this.gen.circle(t,e,s,n);return this.draw(o)}linearPath(t,e){const s=this.gen.linearPath(t,e);return this.draw(s)}polygon(t,e){const s=this.gen.polygon(t,e);return this.draw(s)}arc(t,e,s,n,o,a,h=!1,r){const i=this.gen.arc(t,e,s,n,o,a,h,r);return this.draw(i)}curve(t,e){const s=this.gen.curve(t,e);return this.draw(s)}path(t,e){const s=this.gen.path(t,e);return this.draw(s)}}var at={canvas:(t,e)=>new st(t,e),svg:(t,e)=>new ot(t,e),generator:t=>new et(t),newSeed:()=>et.newSeed()};export{at as default};\n", "const intersectRect = (node, point) => {\n  var x = node.x;\n  var y = node.y;\n\n  // Rectangle intersection algorithm from:\n  // https://math.stackexchange.com/questions/108113/find-edge-between-two-boxes\n  var dx = point.x - x;\n  var dy = point.y - y;\n  var w = node.width / 2;\n  var h = node.height / 2;\n\n  var sx, sy;\n  if (Math.abs(dy) * w > Math.abs(dx) * h) {\n    // Intersection is top or bottom of rect.\n    if (dy < 0) {\n      h = -h;\n    }\n    sx = dy === 0 ? 0 : (h * dx) / dy;\n    sy = h;\n  } else {\n    // Intersection is left or right of rect.\n    if (dx < 0) {\n      w = -w;\n    }\n    sx = w;\n    sy = dx === 0 ? 0 : (w * dy) / dx;\n  }\n\n  return { x: x + sx, y: y + sy };\n};\n\nexport default intersectRect;\n", "import { select } from 'd3';\nimport { log } from '../../logger.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport common, { evaluate, renderKatex, hasKatex } from '../../diagrams/common/common.js';\nimport { decodeEntities } from '../../utils.js';\n\n/**\n * @param dom\n * @param styleFn\n */\nfunction applyStyle(dom, styleFn) {\n  if (styleFn) {\n    dom.attr('style', styleFn);\n  }\n}\n\n/**\n * @param {any} node\n * @returns {Promise<SVGForeignObjectElement>} Node\n */\nasync function addHtmlLabel(node) {\n  const fo = select(document.createElementNS('http://www.w3.org/2000/svg', 'foreignObject'));\n  const div = fo.append('xhtml:div');\n\n  let label = node.label;\n  if (node.label && hasKatex(node.label)) {\n    label = await renderKatex(node.label.replace(common.lineBreakRegex, '\\n'), getConfig());\n  }\n  const labelClass = node.isNode ? 'nodeLabel' : 'edgeLabel';\n  div.html(\n    '<span class=\"' +\n      labelClass +\n      '\" ' +\n      (node.labelStyle ? 'style=\"' + node.labelStyle + '\"' : '') + // codeql [js/html-constructed-from-input] : false positive\n      '>' +\n      label +\n      '</span>'\n  );\n\n  applyStyle(div, node.labelStyle);\n  div.style('display', 'inline-block');\n  div.style('padding-right', '1px');\n  // Fix for firefox\n  div.style('white-space', 'nowrap');\n  div.attr('xmlns', 'http://www.w3.org/1999/xhtml');\n  return fo.node();\n}\n/**\n * @param _vertexText\n * @param style\n * @param isTitle\n * @param isNode\n * @deprecated svg-util/createText instead\n */\nconst createLabel = async (_vertexText, style, isTitle, isNode) => {\n  let vertexText = _vertexText || '';\n  if (typeof vertexText === 'object') {\n    vertexText = vertexText[0];\n  }\n\n  if (evaluate(getConfig().flowchart.htmlLabels)) {\n    // TODO: addHtmlLabel accepts a labelStyle. Do we possibly have that?\n    vertexText = vertexText.replace(/\\\\n|\\n/g, '<br />');\n    log.info('vertexText' + vertexText);\n    const node = {\n      isNode,\n      label: decodeEntities(vertexText).replace(\n        /fa[blrs]?:fa-[\\w-]+/g,\n        (s) => `<i class='${s.replace(':', ' ')}'></i>`\n      ),\n      labelStyle: style ? style.replace('fill:', 'color:') : style,\n    };\n    let vertexNode = await addHtmlLabel(node);\n    // vertexNode.parentNode.removeChild(vertexNode);\n    return vertexNode;\n  } else {\n    const svgLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');\n    svgLabel.setAttribute('style', style.replace('color:', 'fill:'));\n    let rows = [];\n    if (typeof vertexText === 'string') {\n      rows = vertexText.split(/\\\\n|\\n|<br\\s*\\/?>/gi);\n    } else if (Array.isArray(vertexText)) {\n      rows = vertexText;\n    } else {\n      rows = [];\n    }\n\n    for (const row of rows) {\n      const tspan = document.createElementNS('http://www.w3.org/2000/svg', 'tspan');\n      tspan.setAttributeNS('http://www.w3.org/XML/1998/namespace', 'xml:space', 'preserve');\n      tspan.setAttribute('dy', '1em');\n      tspan.setAttribute('x', '0');\n      if (isTitle) {\n        tspan.setAttribute('class', 'title-row');\n      } else {\n        tspan.setAttribute('class', 'row');\n      }\n      tspan.textContent = row.trim();\n      svgLabel.appendChild(tspan);\n    }\n    return svgLabel;\n  }\n};\n\nexport default createLabel;\n", "export const createRoundedRectPathD = (\n  x: number,\n  y: number,\n  totalWidth: number,\n  totalHeight: number,\n  radius: number\n) =>\n  [\n    'M',\n    x + radius,\n    y, // Move to the first point\n    'H',\n    x + totalWidth - radius, // Draw horizontal line to the beginning of the right corner\n    'A',\n    radius,\n    radius,\n    0,\n    0,\n    1,\n    x + totalWidth,\n    y + radius, // Draw arc to the right top corner\n    'V',\n    y + totalHeight - radius, // Draw vertical line down to the beginning of the right bottom corner\n    'A',\n    radius,\n    radius,\n    0,\n    0,\n    1,\n    x + totalWidth - radius,\n    y + totalHeight, // Draw arc to the right bottom corner\n    'H',\n    x + radius, // Draw horizontal line to the beginning of the left bottom corner\n    'A',\n    radius,\n    radius,\n    0,\n    0,\n    1,\n    x,\n    y + totalHeight - radius, // Draw arc to the left bottom corner\n    'V',\n    y + radius, // Draw vertical line up to the beginning of the left top corner\n    'A',\n    radius,\n    radius,\n    0,\n    0,\n    1,\n    x + radius,\n    y, // Draw arc to the left top corner\n    'Z', // Close the path\n  ].join(' ');\n", "import { getConfig } from '../../../diagram-api/diagramAPI.js';\nimport type { Node } from '../../types.js';\n\n// Striped fill like start or fork nodes in state diagrams\nexport const solidStateFill = (color: string) => {\n  const { handDrawnSeed } = getConfig();\n  return {\n    fill: color,\n    hachureAngle: 120, // angle of hachure,\n    hachureGap: 4,\n    fillWeight: 2,\n    roughness: 0.7,\n    stroke: color,\n    seed: handDrawnSeed,\n  };\n};\n\nexport const compileStyles = (node: Node) => {\n  // node.cssCompiledStyles is an array of strings in the form of 'key: value' where jey is the css property and value is the value\n  // the array is the styles of node node from the classes it is using\n  // node.cssStyles is an array of styles directly set on the node\n  // concat the arrays and remove duplicates such that the values from node.cssStyles are used if there are duplicates\n  const stylesMap = styles2Map([...(node.cssCompiledStyles || []), ...(node.cssStyles || [])]);\n  return { stylesMap, stylesArray: [...stylesMap] };\n};\n\nexport const styles2Map = (styles: string[]) => {\n  const styleMap = new Map<string, string>();\n  styles.forEach((style) => {\n    const [key, value] = style.split(':');\n    styleMap.set(key.trim(), value?.trim());\n  });\n  return styleMap;\n};\nexport const isLabelStyle = (key: string) => {\n  return (\n    key === 'color' ||\n    key === 'font-size' ||\n    key === 'font-family' ||\n    key === 'font-weight' ||\n    key === 'font-style' ||\n    key === 'text-decoration' ||\n    key === 'text-align' ||\n    key === 'text-transform' ||\n    key === 'line-height' ||\n    key === 'letter-spacing' ||\n    key === 'word-spacing' ||\n    key === 'text-shadow' ||\n    key === 'text-overflow' ||\n    key === 'white-space' ||\n    key === 'word-wrap' ||\n    key === 'word-break' ||\n    key === 'overflow-wrap' ||\n    key === 'hyphens'\n  );\n};\nexport const styles2String = (node: Node) => {\n  const { stylesArray } = compileStyles(node);\n  const labelStyles: string[] = [];\n  const nodeStyles: string[] = [];\n  const borderStyles: string[] = [];\n  const backgroundStyles: string[] = [];\n\n  stylesArray.forEach((style) => {\n    const key = style[0];\n    if (isLabelStyle(key)) {\n      labelStyles.push(style.join(':') + ' !important');\n    } else {\n      nodeStyles.push(style.join(':') + ' !important');\n      if (key.includes('stroke')) {\n        borderStyles.push(style.join(':') + ' !important');\n      }\n      if (key === 'fill') {\n        backgroundStyles.push(style.join(':') + ' !important');\n      }\n    }\n  });\n\n  return {\n    labelStyles: labelStyles.join(';'),\n    nodeStyles: nodeStyles.join(';'),\n    stylesArray,\n    borderStyles,\n    backgroundStyles,\n  };\n};\n\n// Striped fill like start or fork nodes in state diagrams\n// TODO remove any\nexport const userNodeOverrides = (node: Node, options: any) => {\n  const { themeVariables, handDrawnSeed } = getConfig();\n  const { nodeBorder, mainBkg } = themeVariables;\n  const { stylesMap } = compileStyles(node);\n\n  // index the style array to a map object\n  const result = Object.assign(\n    {\n      roughness: 0.7,\n      fill: stylesMap.get('fill') || mainBkg,\n      fillStyle: 'hachure', // solid fill\n      fillWeight: 4,\n      hachureGap: 5.2,\n      stroke: stylesMap.get('stroke') || nodeBorder,\n      seed: handDrawnSeed,\n      strokeWidth: stylesMap.get('stroke-width')?.replace('px', '') || 1.3,\n      fillLineDash: [0, 0],\n    },\n    options\n  );\n  return result;\n};\n", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { evaluate } from '../../diagrams/common/common.js';\nimport { log } from '../../logger.js';\nimport { getSubGraphTitleMargins } from '../../utils/subGraphTitleMargins.js';\nimport { select } from 'd3';\nimport rough from 'roughjs';\nimport { createText } from '../createText.ts';\nimport intersectRect from '../rendering-elements/intersect/intersect-rect.js';\nimport createLabel from './createLabel.js';\nimport { createRoundedRectPathD } from './shapes/roundedRectPath.ts';\nimport { styles2String, userNodeOverrides } from './shapes/handDrawnShapeStyles.js';\n\nconst rect = async (parent, node) => {\n  log.info('Creating subgraph rect for ', node.id, node);\n  const siteConfig = getConfig();\n  const { themeVariables, handDrawnSeed } = siteConfig;\n  const { clusterBkg, clusterBorder } = themeVariables;\n\n  const { labelStyles, nodeStyles, borderStyles, backgroundStyles } = styles2String(node);\n\n  // Add outer g element\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', 'cluster ' + node.cssClasses)\n    .attr('id', node.id)\n    .attr('data-look', node.look);\n\n  const useHtmlLabels = evaluate(siteConfig.flowchart.htmlLabels);\n\n  // Create the label and insert it after the rect\n  const labelEl = shapeSvg.insert('g').attr('class', 'cluster-label ');\n\n  const text = await createText(labelEl, node.label, {\n    style: node.labelStyle,\n    useHtmlLabels,\n    isNode: true,\n  });\n\n  // Get the size of the label\n  let bbox = text.getBBox();\n\n  if (evaluate(siteConfig.flowchart.htmlLabels)) {\n    const div = text.children[0];\n    const dv = select(text);\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n\n  const width = node.width <= bbox.width + node.padding ? bbox.width + node.padding : node.width;\n  if (node.width <= bbox.width + node.padding) {\n    node.diff = (width - node.width) / 2 - node.padding;\n  } else {\n    node.diff = -node.padding;\n  }\n\n  const height = node.height;\n  const x = node.x - width / 2;\n  const y = node.y - height / 2;\n\n  log.trace('Data ', node, JSON.stringify(node));\n  let rect;\n  if (node.look === 'handDrawn') {\n    // @ts-ignore TODO: Fix rough typings\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {\n      roughness: 0.7,\n      fill: clusterBkg,\n      // fill: 'red',\n      stroke: clusterBorder,\n      fillWeight: 3,\n      seed: handDrawnSeed,\n    });\n    const roughNode = rc.path(createRoundedRectPathD(x, y, width, height, 0), options);\n    rect = shapeSvg.insert(() => {\n      log.debug('Rough node insert CXC', roughNode);\n      return roughNode;\n    }, ':first-child');\n    // Should we affect the options instead of doing this?\n    rect.select('path:nth-child(2)').attr('style', borderStyles.join(';'));\n    rect.select('path').attr('style', backgroundStyles.join(';').replace('fill', 'stroke'));\n  } else {\n    // add the rect\n    rect = shapeSvg.insert('rect', ':first-child');\n    // center the rect around its coordinate\n    rect\n      .attr('style', nodeStyles)\n      .attr('rx', node.rx)\n      .attr('ry', node.ry)\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', width)\n      .attr('height', height);\n  }\n  const { subGraphTitleTopMargin } = getSubGraphTitleMargins(siteConfig);\n  labelEl.attr(\n    'transform',\n    // This puts the label on top of the box instead of inside it\n    `translate(${node.x - bbox.width / 2}, ${node.y - node.height / 2 + subGraphTitleTopMargin})`\n  );\n\n  if (labelStyles) {\n    const span = labelEl.select('span');\n    if (span) {\n      span.attr('style', labelStyles);\n    }\n  }\n  // Center the label\n\n  const rectBox = rect.node().getBBox();\n  node.offsetX = 0;\n  node.width = rectBox.width;\n  node.height = rectBox.height;\n  // Used by layout engine to position subgraph in parent\n  node.offsetY = bbox.height - node.padding / 2;\n\n  node.intersect = function (point) {\n    return intersectRect(node, point);\n  };\n\n  return { cluster: shapeSvg, labelBBox: bbox };\n};\n\n/**\n * Non visible cluster where the note is group with its\n *\n * @param {any} parent\n * @param {any} node\n * @returns {any} ShapeSvg\n */\nconst noteGroup = (parent, node) => {\n  // Add outer g element\n  const shapeSvg = parent.insert('g').attr('class', 'note-cluster').attr('id', node.id);\n\n  // add the rect\n  const rect = shapeSvg.insert('rect', ':first-child');\n\n  const padding = 0 * node.padding;\n  const halfPadding = padding / 2;\n\n  // center the rect around its coordinate\n  rect\n    .attr('rx', node.rx)\n    .attr('ry', node.ry)\n    .attr('x', node.x - node.width / 2 - halfPadding)\n    .attr('y', node.y - node.height / 2 - halfPadding)\n    .attr('width', node.width + padding)\n    .attr('height', node.height + padding)\n    .attr('fill', 'none');\n\n  const rectBox = rect.node().getBBox();\n  node.width = rectBox.width;\n  node.height = rectBox.height;\n\n  node.intersect = function (point) {\n    return intersectRect(node, point);\n  };\n\n  return { cluster: shapeSvg, labelBBox: { width: 0, height: 0 } };\n};\n\nconst roundedWithTitle = async (parent, node) => {\n  const siteConfig = getConfig();\n\n  const { themeVariables, handDrawnSeed } = siteConfig;\n  const { altBackground, compositeBackground, compositeTitleBackground, nodeBorder } =\n    themeVariables;\n\n  // Add outer g element\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', node.cssClasses)\n    .attr('id', node.id)\n    .attr('data-id', node.id)\n    .attr('data-look', node.look);\n\n  // add the rect\n  const outerRectG = shapeSvg.insert('g', ':first-child');\n\n  // Create the label and insert it after the rect\n  const label = shapeSvg.insert('g').attr('class', 'cluster-label');\n  let innerRect = shapeSvg.append('rect');\n\n  const text = label\n    .node()\n    .appendChild(await createLabel(node.label, node.labelStyle, undefined, true));\n\n  // Get the size of the label\n  let bbox = text.getBBox();\n\n  if (evaluate(siteConfig.flowchart.htmlLabels)) {\n    const div = text.children[0];\n    const dv = select(text);\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n\n  // Rounded With Title\n  const padding = 0 * node.padding;\n  const halfPadding = padding / 2;\n\n  const width =\n    (node.width <= bbox.width + node.padding ? bbox.width + node.padding : node.width) + padding;\n  if (node.width <= bbox.width + node.padding) {\n    node.diff = (width - node.width) / 2 - node.padding;\n  } else {\n    node.diff = -node.padding;\n  }\n\n  const height = node.height + padding;\n  // const height = node.height + padding;\n  const innerHeight = node.height + padding - bbox.height - 6;\n  const x = node.x - width / 2;\n  const y = node.y - height / 2;\n  node.width = width;\n  const innerY = node.y - node.height / 2 - halfPadding + bbox.height + 2;\n\n  // add the rect\n  let rect;\n  if (node.look === 'handDrawn') {\n    const isAlt = node.cssClasses.includes('statediagram-cluster-alt');\n    const rc = rough.svg(shapeSvg);\n    const roughOuterNode =\n      node.rx || node.ry\n        ? rc.path(createRoundedRectPathD(x, y, width, height, 10), {\n            roughness: 0.7,\n            fill: compositeTitleBackground,\n            fillStyle: 'solid',\n            stroke: nodeBorder,\n            seed: handDrawnSeed,\n          })\n        : rc.rectangle(x, y, width, height, { seed: handDrawnSeed });\n\n    rect = shapeSvg.insert(() => roughOuterNode, ':first-child');\n    const roughInnerNode = rc.rectangle(x, innerY, width, innerHeight, {\n      fill: isAlt ? altBackground : compositeBackground,\n      fillStyle: isAlt ? 'hachure' : 'solid',\n      stroke: nodeBorder,\n      seed: handDrawnSeed,\n    });\n\n    rect = shapeSvg.insert(() => roughOuterNode, ':first-child');\n    innerRect = shapeSvg.insert(() => roughInnerNode);\n  } else {\n    rect = outerRectG.insert('rect', ':first-child');\n    const outerRectClass = 'outer';\n\n    // center the rect around its coordinate\n    rect\n      .attr('class', outerRectClass)\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', width)\n      .attr('height', height)\n      .attr('data-look', node.look);\n    innerRect\n      .attr('class', 'inner')\n      .attr('x', x)\n      .attr('y', innerY)\n      .attr('width', width)\n      .attr('height', innerHeight);\n  }\n\n  label.attr(\n    'transform',\n    `translate(${node.x - bbox.width / 2}, ${y + 1 - (evaluate(siteConfig.flowchart.htmlLabels) ? 0 : 3)})`\n  );\n\n  const rectBox = rect.node().getBBox();\n  node.height = rectBox.height;\n  node.offsetX = 0;\n  // Used by layout engine to position subgraph in parent\n  node.offsetY = bbox.height - node.padding / 2;\n  node.labelBBox = bbox;\n\n  node.intersect = function (point) {\n    return intersectRect(node, point);\n  };\n\n  return { cluster: shapeSvg, labelBBox: bbox };\n};\nconst kanbanSection = async (parent, node) => {\n  log.info('Creating subgraph rect for ', node.id, node);\n  const siteConfig = getConfig();\n  const { themeVariables, handDrawnSeed } = siteConfig;\n  const { clusterBkg, clusterBorder } = themeVariables;\n\n  const { labelStyles, nodeStyles, borderStyles, backgroundStyles } = styles2String(node);\n\n  // Add outer g element\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', 'cluster ' + node.cssClasses)\n    .attr('id', node.id)\n    .attr('data-look', node.look);\n\n  const useHtmlLabels = evaluate(siteConfig.flowchart.htmlLabels);\n\n  // Create the label and insert it after the rect\n  const labelEl = shapeSvg.insert('g').attr('class', 'cluster-label ');\n\n  const text = await createText(labelEl, node.label, {\n    style: node.labelStyle,\n    useHtmlLabels,\n    isNode: true,\n    width: node.width,\n  });\n\n  // Get the size of the label\n  let bbox = text.getBBox();\n\n  if (evaluate(siteConfig.flowchart.htmlLabels)) {\n    const div = text.children[0];\n    const dv = select(text);\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n\n  const width = node.width <= bbox.width + node.padding ? bbox.width + node.padding : node.width;\n  if (node.width <= bbox.width + node.padding) {\n    node.diff = (width - node.width) / 2 - node.padding;\n  } else {\n    node.diff = -node.padding;\n  }\n\n  const height = node.height;\n  const x = node.x - width / 2;\n  const y = node.y - height / 2;\n\n  log.trace('Data ', node, JSON.stringify(node));\n  let rect;\n  if (node.look === 'handDrawn') {\n    // @ts-ignore TODO: Fix rough typings\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {\n      roughness: 0.7,\n      fill: clusterBkg,\n      // fill: 'red',\n      stroke: clusterBorder,\n      fillWeight: 4,\n      seed: handDrawnSeed,\n    });\n    const roughNode = rc.path(createRoundedRectPathD(x, y, width, height, node.rx), options);\n    rect = shapeSvg.insert(() => {\n      log.debug('Rough node insert CXC', roughNode);\n      return roughNode;\n    }, ':first-child');\n    // Should we affect the options instead of doing this?\n    rect.select('path:nth-child(2)').attr('style', borderStyles.join(';'));\n    rect.select('path').attr('style', backgroundStyles.join(';').replace('fill', 'stroke'));\n  } else {\n    // add the rect\n    rect = shapeSvg.insert('rect', ':first-child');\n    // center the rect around its coordinate\n    rect\n      .attr('style', nodeStyles)\n      .attr('rx', node.rx)\n      .attr('ry', node.ry)\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', width)\n      .attr('height', height);\n  }\n  const { subGraphTitleTopMargin } = getSubGraphTitleMargins(siteConfig);\n  labelEl.attr(\n    'transform',\n    // This puts the label on top of the box instead of inside it\n    `translate(${node.x - bbox.width / 2}, ${node.y - node.height / 2 + subGraphTitleTopMargin})`\n  );\n\n  if (labelStyles) {\n    const span = labelEl.select('span');\n    if (span) {\n      span.attr('style', labelStyles);\n    }\n  }\n  // Center the label\n\n  const rectBox = rect.node().getBBox();\n  node.offsetX = 0;\n  node.width = rectBox.width;\n  node.height = rectBox.height;\n  // Used by layout engine to position subgraph in parent\n  node.offsetY = bbox.height - node.padding / 2;\n\n  node.intersect = function (point) {\n    return intersectRect(node, point);\n  };\n\n  return { cluster: shapeSvg, labelBBox: bbox };\n};\nconst divider = (parent, node) => {\n  const siteConfig = getConfig();\n\n  const { themeVariables, handDrawnSeed } = siteConfig;\n  const { nodeBorder } = themeVariables;\n\n  // Add outer g element\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', node.cssClasses)\n    .attr('id', node.id)\n    .attr('data-look', node.look);\n\n  // add the rect\n  const outerRectG = shapeSvg.insert('g', ':first-child');\n\n  const padding = 0 * node.padding;\n\n  const width = node.width + padding;\n\n  node.diff = -node.padding;\n\n  const height = node.height + padding;\n  // const height = node.height + padding;\n  const x = node.x - width / 2;\n  const y = node.y - height / 2;\n  node.width = width;\n\n  // add the rect\n  let rect;\n  if (node.look === 'handDrawn') {\n    const rc = rough.svg(shapeSvg);\n    const roughOuterNode = rc.rectangle(x, y, width, height, {\n      fill: 'lightgrey',\n      roughness: 0.5,\n      strokeLineDash: [5],\n      stroke: nodeBorder,\n      seed: handDrawnSeed,\n    });\n\n    rect = shapeSvg.insert(() => roughOuterNode, ':first-child');\n  } else {\n    rect = outerRectG.insert('rect', ':first-child');\n    const outerRectClass = 'divider';\n\n    // center the rect around its coordinate\n    rect\n      .attr('class', outerRectClass)\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', width)\n      .attr('height', height)\n      .attr('data-look', node.look);\n  }\n\n  const rectBox = rect.node().getBBox();\n  node.height = rectBox.height;\n  node.offsetX = 0;\n  // Used by layout engine to position subgraph in parent\n  node.offsetY = 0;\n\n  node.intersect = function (point) {\n    return intersectRect(node, point);\n  };\n\n  return { cluster: shapeSvg, labelBBox: {} };\n};\n\nconst squareRect = rect;\nconst shapes = {\n  rect,\n  squareRect,\n  roundedWithTitle,\n  noteGroup,\n  divider,\n  kanbanSection,\n};\n\nlet clusterElems = new Map();\n\n/**\n * @typedef {keyof typeof shapes} ClusterShapeID\n */\n\n/**\n * @param {import('../types.js').ClusterNode} node - Shape defaults to 'rect'\n */\nexport const insertCluster = async (elem, node) => {\n  const shape = node.shape || 'rect';\n  const cluster = await shapes[shape](elem, node);\n  clusterElems.set(node.id, cluster);\n  return cluster;\n};\n\nexport const getClusterTitleWidth = (elem, node) => {\n  const label = createLabel(node.label, node.labelStyle, undefined, true);\n  elem.node().appendChild(label);\n  const width = label.getBBox().width;\n  elem.node().removeChild(label);\n  return width;\n};\n\nexport const clear = () => {\n  clusterElems = new Map();\n};\n\nexport const positionCluster = (node) => {\n  log.info(\n    'Position cluster (' +\n      node.id +\n      ', ' +\n      node.x +\n      ', ' +\n      node.y +\n      ') (' +\n      node?.width +\n      ', ' +\n      node?.height +\n      ')',\n    clusterElems.get(node.id)\n  );\n  const el = clusterElems.get(node.id);\n  el.cluster.attr('transform', 'translate(' + node.x + ', ' + node.y + ')');\n};\n", "function intersectNode(node, point) {\n  return node.intersect(point);\n}\n\nexport default intersectNode;\n", "function intersectEllipse(node, rx, ry, point) {\n  // Formulae from: https://mathworld.wolfram.com/Ellipse-LineIntersection.html\n\n  var cx = node.x;\n  var cy = node.y;\n\n  var px = cx - point.x;\n  var py = cy - point.y;\n\n  var det = Math.sqrt(rx * rx * py * py + ry * ry * px * px);\n\n  var dx = Math.abs((rx * ry * px) / det);\n  if (point.x < cx) {\n    dx = -dx;\n  }\n  var dy = Math.abs((rx * ry * py) / det);\n  if (point.y < cy) {\n    dy = -dy;\n  }\n\n  return { x: cx + dx, y: cy + dy };\n}\n\nexport default intersectEllipse;\n", "import intersectEllipse from './intersect-ellipse.js';\n\nfunction intersectCircle(node, rx, point) {\n  return intersectEllipse(node, rx, rx, point);\n}\n\nexport default intersectCircle;\n", "/**\n * Returns the point at which two lines, p and q, intersect or returns undefined if they do not intersect.\n */\nfunction intersectLine(p1, p2, q1, q2) {\n  // Algorithm from <PERSON><PERSON>, (ed.) Graphics Gems, No 2, <PERSON>, 1994,\n  // p7 and p473.\n\n  var a1, a2, b1, b2, c1, c2;\n  var r1, r2, r3, r4;\n  var denom, offset, num;\n  var x, y;\n\n  // Compute a1, b1, c1, where line joining points 1 and 2 is F(x,y) = a1 x +\n  // b1 y + c1 = 0.\n  a1 = p2.y - p1.y;\n  b1 = p1.x - p2.x;\n  c1 = p2.x * p1.y - p1.x * p2.y;\n\n  // Compute r3 and r4.\n  r3 = a1 * q1.x + b1 * q1.y + c1;\n  r4 = a1 * q2.x + b1 * q2.y + c1;\n\n  // Check signs of r3 and r4. If both point 3 and point 4 lie on\n  // same side of line 1, the line segments do not intersect.\n  if (r3 !== 0 && r4 !== 0 && sameSign(r3, r4)) {\n    return /*DON'T_INTERSECT*/;\n  }\n\n  // Compute a2, b2, c2 where line joining points 3 and 4 is G(x,y) = a2 x + b2 y + c2 = 0\n  a2 = q2.y - q1.y;\n  b2 = q1.x - q2.x;\n  c2 = q2.x * q1.y - q1.x * q2.y;\n\n  // Compute r1 and r2\n  r1 = a2 * p1.x + b2 * p1.y + c2;\n  r2 = a2 * p2.x + b2 * p2.y + c2;\n\n  // Check signs of r1 and r2. If both point 1 and point 2 lie\n  // on same side of second line segment, the line segments do\n  // not intersect.\n  if (r1 !== 0 && r2 !== 0 && sameSign(r1, r2)) {\n    return /*DON'T_INTERSECT*/;\n  }\n\n  // Line segments intersect: compute intersection point.\n  denom = a1 * b2 - a2 * b1;\n  if (denom === 0) {\n    return /*COLLINEAR*/;\n  }\n\n  offset = Math.abs(denom / 2);\n\n  // The denom/2 is to get rounding instead of truncating. It\n  // is added or subtracted to the numerator, depending upon the\n  // sign of the numerator.\n  num = b1 * c2 - b2 * c1;\n  x = num < 0 ? (num - offset) / denom : (num + offset) / denom;\n\n  num = a2 * c1 - a1 * c2;\n  y = num < 0 ? (num - offset) / denom : (num + offset) / denom;\n\n  return { x: x, y: y };\n}\n\nfunction sameSign(r1, r2) {\n  return r1 * r2 > 0;\n}\n\nexport default intersectLine;\n", "import intersectLine from './intersect-line.js';\n\n/**\n * Returns the point ({x, y}) at which the point argument intersects with the node argument assuming\n * that it has the shape specified by polygon.\n */\nfunction intersectPolygon(node, polyPoints, point) {\n  let x1 = node.x;\n  let y1 = node.y;\n\n  let intersections = [];\n\n  let minX = Number.POSITIVE_INFINITY;\n  let minY = Number.POSITIVE_INFINITY;\n  if (typeof polyPoints.forEach === 'function') {\n    polyPoints.forEach(function (entry) {\n      minX = Math.min(minX, entry.x);\n      minY = Math.min(minY, entry.y);\n    });\n  } else {\n    minX = Math.min(minX, polyPoints.x);\n    minY = Math.min(minY, polyPoints.y);\n  }\n\n  let left = x1 - node.width / 2 - minX;\n  let top = y1 - node.height / 2 - minY;\n\n  for (let i = 0; i < polyPoints.length; i++) {\n    let p1 = polyPoints[i];\n    let p2 = polyPoints[i < polyPoints.length - 1 ? i + 1 : 0];\n    let intersect = intersectLine(\n      node,\n      point,\n      { x: left + p1.x, y: top + p1.y },\n      { x: left + p2.x, y: top + p2.y }\n    );\n    if (intersect) {\n      intersections.push(intersect);\n    }\n  }\n\n  if (!intersections.length) {\n    return node;\n  }\n\n  if (intersections.length > 1) {\n    // More intersections, find the one nearest to edge end point\n    intersections.sort(function (p, q) {\n      let pdx = p.x - point.x;\n      let pdy = p.y - point.y;\n      let distp = Math.sqrt(pdx * pdx + pdy * pdy);\n\n      let qdx = q.x - point.x;\n      let qdy = q.y - point.y;\n      let distq = Math.sqrt(qdx * qdx + qdy * qdy);\n\n      return distp < distq ? -1 : distp === distq ? 0 : 1;\n    });\n  }\n  return intersections[0];\n}\n\nexport default intersectPolygon;\n", "/*\n * Borrowed with love from from dagre-d3. Many thanks to c<PERSON><PERSON><PERSON>!\n */\n\nimport node from './intersect-node.js';\nimport circle from './intersect-circle.js';\nimport ellipse from './intersect-ellipse.js';\nimport polygon from './intersect-polygon.js';\nimport rect from './intersect-rect.js';\n\nexport default {\n  node,\n  circle,\n  ellipse,\n  polygon,\n  rect,\n};\n", "import { log } from '../../../logger.js';\nimport { updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { handleUndefinedAttr } from '../../../utils.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport function anchor<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const classes = getNodeClasses(node);\n  let cssClasses = classes;\n  if (!classes) {\n    cssClasses = 'anchor';\n  }\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', cssClasses)\n    .attr('id', node.domId || node.id);\n\n  const radius = 1;\n\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, { fill: 'black', stroke: 'none', fillStyle: 'solid' });\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n  }\n  const roughNode = rc.circle(0, 0, radius * 2, options);\n  const circleElem = shapeSvg.insert(() => roughNode, ':first-child');\n  circleElem.attr('class', 'anchor').attr('style', handleUndefinedAttr(cssStyles));\n\n  updateNodeBounds(node, circleElem);\n\n  node.intersect = function (point) {\n    log.info('Circle intersect', node, radius, point);\n    return intersect.circle(node, radius, point);\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nfunction generateArcPoints(\n  x1: number,\n  y1: number,\n  x2: number,\n  y2: number,\n  rx: number,\n  ry: number,\n  clockwise: boolean\n) {\n  const numPoints = 20;\n  // Calculate midpoint\n  const midX = (x1 + x2) / 2;\n  const midY = (y1 + y2) / 2;\n\n  // Calculate the angle of the line connecting the points\n  const angle = Math.atan2(y2 - y1, x2 - x1);\n\n  // Calculate transformed coordinates for the ellipse\n  const dx = (x2 - x1) / 2;\n  const dy = (y2 - y1) / 2;\n\n  // Scale to unit circle\n  const transformedX = dx / rx;\n  const transformedY = dy / ry;\n\n  // Calculate the distance between points on the unit circle\n  const distance = Math.sqrt(transformedX ** 2 + transformedY ** 2);\n\n  // Check if the ellipse can be drawn with the given radii\n  if (distance > 1) {\n    throw new Error('The given radii are too small to create an arc between the points.');\n  }\n\n  // Calculate the distance from the midpoint to the center of the ellipse\n  const scaledCenterDistance = Math.sqrt(1 - distance ** 2);\n\n  // Calculate the center of the ellipse\n  const centerX = midX + scaledCenterDistance * ry * Math.sin(angle) * (clockwise ? -1 : 1);\n  const centerY = midY - scaledCenterDistance * rx * Math.cos(angle) * (clockwise ? -1 : 1);\n\n  // Calculate the start and end angles on the ellipse\n  const startAngle = Math.atan2((y1 - centerY) / ry, (x1 - centerX) / rx);\n  const endAngle = Math.atan2((y2 - centerY) / ry, (x2 - centerX) / rx);\n\n  // Adjust angles for clockwise/counterclockwise\n  let angleRange = endAngle - startAngle;\n  if (clockwise && angleRange < 0) {\n    angleRange += 2 * Math.PI;\n  }\n  if (!clockwise && angleRange > 0) {\n    angleRange -= 2 * Math.PI;\n  }\n\n  // Generate points\n  const points = [];\n  for (let i = 0; i < numPoints; i++) {\n    const t = i / (numPoints - 1);\n    const angle = startAngle + t * angleRange;\n    const x = centerX + rx * Math.cos(angle);\n    const y = centerY + ry * Math.sin(angle);\n    points.push({ x, y });\n  }\n\n  return points;\n}\n\nexport async function bowTieRect<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + node.padding + 20;\n  const h = bbox.height + node.padding;\n\n  const ry = h / 2;\n  const rx = ry / (2.5 + h / 50);\n\n  // let shape: d3.Selection<SVGPathElement | SVGGElement, unknown, null, undefined>;\n  const { cssStyles } = node;\n\n  const points = [\n    { x: w / 2, y: -h / 2 },\n    { x: -w / 2, y: -h / 2 },\n    ...generateArcPoints(-w / 2, -h / 2, -w / 2, h / 2, rx, ry, false),\n    { x: w / 2, y: h / 2 },\n    ...generateArcPoints(w / 2, h / 2, w / 2, -h / 2, rx, ry, true),\n  ];\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n  const bowTieRectPath = createPathFromPoints(points);\n  const bowTieRectShapePath = rc.path(bowTieRectPath, options);\n  const bowTieRectShape = shapeSvg.insert(() => bowTieRectShapePath, ':first-child');\n\n  bowTieRectShape.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    bowTieRectShape.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    bowTieRectShape.selectAll('path').attr('style', nodeStyles);\n  }\n\n  bowTieRectShape.attr('transform', `translate(${rx / 2}, 0)`);\n\n  updateNodeBounds(node, bowTieRectShape);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import type { D3Selection } from '../../../types.js';\n\nexport function insertPolygonShape<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  w: number,\n  h: number,\n  points: { x: number; y: number }[]\n) {\n  return parent\n    .insert('polygon', ':first-child')\n    .attr(\n      'points',\n      points\n        .map(function (d) {\n          return d.x + ',' + d.y;\n        })\n        .join(' ')\n    )\n    .attr('class', 'label-container')\n    .attr('transform', 'translate(' + -w / 2 + ',' + h / 2 + ')');\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\n\nimport { insertPolygonShape } from './insertPolygonShape.js';\nimport { createPathFromPoints } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\n// const createPathFromPoints = (points: { x: number; y: number }[]): string => {\n//   const pointStrings = points.map((p, i) => `${i === 0 ? 'M' : 'L'}${p.x},${p.y}`);\n//   pointStrings.push('Z');\n//   return pointStrings.join(' ');\n// };\n\nexport async function card<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const h = bbox.height + node.padding;\n  const padding = 12;\n  const w = bbox.width + node.padding + padding;\n  const left = 0;\n  const right = w;\n  const top = -h;\n  const bottom = 0;\n  const points = [\n    { x: left + padding, y: top },\n    { x: right, y: top },\n    { x: right, y: bottom },\n    { x: left, y: bottom },\n    { x: left, y: top + padding },\n    { x: left + padding, y: top },\n  ];\n\n  let polygon: D3Selection<SVGGElement> | Awaited<ReturnType<typeof insertPolygonShape>>;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createPathFromPoints(points);\n    const roughNode = rc.path(pathData, options);\n\n    polygon = shapeSvg\n      .insert(() => roughNode, ':first-child')\n      .attr('transform', `translate(${-w / 2}, ${h / 2})`);\n\n    if (cssStyles) {\n      polygon.attr('style', cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n\n  if (nodeStyles) {\n    polygon.attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport rough from 'roughjs';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport { createPathFromPoints, getNodeClasses } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport function choice<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { nodeStyles } = styles2String(node);\n  node.label = '';\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', getNodeClasses(node))\n    .attr('id', node.domId ?? node.id);\n  const { cssStyles } = node;\n\n  const s = Math.max(28, node.width ?? 0);\n\n  const points = [\n    { x: 0, y: s / 2 },\n    { x: s / 2, y: 0 },\n    { x: 0, y: -s / 2 },\n    { x: -s / 2, y: 0 },\n  ];\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const choicePath = createPathFromPoints(points);\n  const roughNode = rc.path(choicePath, options);\n  const choiceShape = shapeSvg.insert(() => roughNode, ':first-child');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    choiceShape.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    choiceShape.selectAll('path').attr('style', nodeStyles);\n  }\n\n  node.width = 28;\n  node.height = 28;\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import { log } from '../../../logger.js';\nimport { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\nimport { handleUndefinedAttr } from '../../../utils.js';\n\nexport async function circle<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const radius = bbox.width / 2 + halfPadding;\n  let circleElem;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const roughNode = rc.circle(0, 0, radius * 2, options);\n\n    circleElem = shapeSvg.insert(() => roughNode, ':first-child');\n    circleElem.attr('class', 'basic label-container').attr('style', handleUndefinedAttr(cssStyles));\n  } else {\n    circleElem = shapeSvg\n      .insert('circle', ':first-child')\n      .attr('class', 'basic label-container')\n      .attr('style', nodeStyles)\n      .attr('r', radius)\n      .attr('cx', 0)\n      .attr('cy', 0);\n  }\n\n  updateNodeBounds(node, circleElem);\n\n  node.intersect = function (point) {\n    log.info('Circle intersect', node, radius, point);\n    return intersect.circle(node, radius, point);\n  };\n\n  return shapeSvg;\n}\n", "import { log } from '../../../logger.js';\nimport { getNodeClasses, updateNodeBounds } from './util.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport intersect from '../intersect/index.js';\nimport type { D3Selection } from '../../../types.js';\n\nfunction createLine(r: number) {\n  const xAxis45 = Math.cos(Math.PI / 4); // cosine of 45 degrees\n  const yAxis45 = Math.sin(Math.PI / 4); // sine of 45 degrees\n  const lineLength = r * 2;\n\n  const pointQ1 = { x: (lineLength / 2) * xAxis45, y: (lineLength / 2) * yAxis45 }; // Quadrant I\n  const pointQ2 = { x: -(lineLength / 2) * xAxis45, y: (lineLength / 2) * yAxis45 }; // Quadrant II\n  const pointQ3 = { x: -(lineLength / 2) * xAxis45, y: -(lineLength / 2) * yAxis45 }; // Quadrant III\n  const pointQ4 = { x: (lineLength / 2) * xAxis45, y: -(lineLength / 2) * yAxis45 }; // Quadrant IV\n\n  return `M ${pointQ2.x},${pointQ2.y} L ${pointQ4.x},${pointQ4.y}\n                   M ${pointQ1.x},${pointQ1.y} L ${pointQ3.x},${pointQ3.y}`;\n}\n\nexport function crossedCircle<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  node.label = '';\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', getNodeClasses(node))\n    .attr('id', node.domId ?? node.id);\n  const radius = Math.max(30, node?.width ?? 0);\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const circleNode = rc.circle(0, 0, radius * 2, options);\n  const linePath = createLine(radius);\n  const lineNode = rc.path(linePath, options);\n\n  const crossedCircle = shapeSvg.insert(() => circleNode, ':first-child');\n  crossedCircle.insert(() => lineNode);\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    crossedCircle.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    crossedCircle.selectAll('path').attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, crossedCircle);\n\n  node.intersect = function (point) {\n    log.info('crossedCircle intersect', node, { radius, point });\n    const pos = intersect.circle(node, radius, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nfunction generateCirclePoints(\n  centerX: number,\n  centerY: number,\n  radius: number,\n  numPoints = 100,\n  startAngle = 0,\n  endAngle = 180\n) {\n  const points = [];\n\n  // Convert angles to radians\n  const startAngleRad = (startAngle * Math.PI) / 180;\n  const endAngleRad = (endAngle * Math.PI) / 180;\n\n  // Calculate the angle range in radians\n  const angleRange = endAngleRad - startAngleRad;\n\n  // Calculate the angle step\n  const angleStep = angleRange / (numPoints - 1);\n\n  for (let i = 0; i < numPoints; i++) {\n    const angle = startAngleRad + i * angleStep;\n    const x = centerX + radius * Math.cos(angle);\n    const y = centerY + radius * Math.sin(angle);\n    points.push({ x: -x, y: -y });\n  }\n\n  return points;\n}\n\nexport async function curlyBraceLeft<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + (node.padding ?? 0);\n  const h = bbox.height + (node.padding ?? 0);\n  const radius = Math.max(5, h * 0.1);\n\n  const { cssStyles } = node;\n\n  const points = [\n    ...generateCirclePoints(w / 2, -h / 2, radius, 30, -90, 0),\n    { x: -w / 2 - radius, y: radius },\n    ...generateCirclePoints(w / 2 + radius * 2, -radius, radius, 20, -180, -270),\n    ...generateCirclePoints(w / 2 + radius * 2, radius, radius, 20, -90, -180),\n    { x: -w / 2 - radius, y: -h / 2 },\n    ...generateCirclePoints(w / 2, h / 2, radius, 20, 0, 90),\n  ];\n\n  const rectPoints = [\n    { x: w / 2, y: -h / 2 - radius },\n    { x: -w / 2, y: -h / 2 - radius },\n    ...generateCirclePoints(w / 2, -h / 2, radius, 20, -90, 0),\n    { x: -w / 2 - radius, y: -radius },\n    ...generateCirclePoints(w / 2 + w * 0.1, -radius, radius, 20, -180, -270),\n    ...generateCirclePoints(w / 2 + w * 0.1, radius, radius, 20, -90, -180),\n    { x: -w / 2 - radius, y: h / 2 },\n    ...generateCirclePoints(w / 2, h / 2, radius, 20, 0, 90),\n    { x: -w / 2, y: h / 2 + radius },\n    { x: w / 2, y: h / 2 + radius },\n  ];\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, { fill: 'none' });\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n  const curlyBraceLeftPath = createPathFromPoints(points);\n  const newCurlyBracePath = curlyBraceLeftPath.replace('Z', '');\n  const curlyBraceLeftNode = rc.path(newCurlyBracePath, options);\n  const rectPath = createPathFromPoints(rectPoints);\n  const rectShape = rc.path(rectPath, { ...options });\n  const curlyBraceLeftShape = shapeSvg.insert('g', ':first-child');\n  curlyBraceLeftShape.insert(() => rectShape, ':first-child').attr('stroke-opacity', 0);\n  curlyBraceLeftShape.insert(() => curlyBraceLeftNode, ':first-child');\n  curlyBraceLeftShape.attr('class', 'text');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    curlyBraceLeftShape.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    curlyBraceLeftShape.selectAll('path').attr('style', nodeStyles);\n  }\n\n  curlyBraceLeftShape.attr('transform', `translate(${radius}, 0)`);\n\n  label.attr(\n    'transform',\n    `translate(${-w / 2 + radius - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, curlyBraceLeftShape);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, rectPoints, point);\n\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nfunction generateCirclePoints(\n  centerX: number,\n  centerY: number,\n  radius: number,\n  numPoints = 100,\n  startAngle = 0,\n  endAngle = 180\n) {\n  const points = [];\n\n  // Convert angles to radians\n  const startAngleRad = (startAngle * Math.PI) / 180;\n  const endAngleRad = (endAngle * Math.PI) / 180;\n\n  // Calculate the angle range in radians\n  const angleRange = endAngleRad - startAngleRad;\n\n  // Calculate the angle step\n  const angleStep = angleRange / (numPoints - 1);\n\n  for (let i = 0; i < numPoints; i++) {\n    const angle = startAngleRad + i * angleStep;\n    const x = centerX + radius * Math.cos(angle);\n    const y = centerY + radius * Math.sin(angle);\n    points.push({ x, y });\n  }\n\n  return points;\n}\n\nexport async function curlyBraceRight<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + (node.padding ?? 0);\n  const h = bbox.height + (node.padding ?? 0);\n  const radius = Math.max(5, h * 0.1);\n\n  const { cssStyles } = node;\n\n  const points = [\n    ...generateCirclePoints(w / 2, -h / 2, radius, 20, -90, 0),\n    { x: w / 2 + radius, y: -radius },\n    ...generateCirclePoints(w / 2 + radius * 2, -radius, radius, 20, -180, -270),\n    ...generateCirclePoints(w / 2 + radius * 2, radius, radius, 20, -90, -180),\n    { x: w / 2 + radius, y: h / 2 },\n    ...generateCirclePoints(w / 2, h / 2, radius, 20, 0, 90),\n  ];\n\n  const rectPoints = [\n    { x: -w / 2, y: -h / 2 - radius },\n    { x: w / 2, y: -h / 2 - radius },\n    ...generateCirclePoints(w / 2, -h / 2, radius, 20, -90, 0),\n    { x: w / 2 + radius, y: -radius },\n    ...generateCirclePoints(w / 2 + radius * 2, -radius, radius, 20, -180, -270),\n    ...generateCirclePoints(w / 2 + radius * 2, radius, radius, 20, -90, -180),\n    { x: w / 2 + radius, y: h / 2 },\n    ...generateCirclePoints(w / 2, h / 2, radius, 20, 0, 90),\n    { x: w / 2, y: h / 2 + radius },\n    { x: -w / 2, y: h / 2 + radius },\n  ];\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, { fill: 'none' });\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n  const curlyBraceRightPath = createPathFromPoints(points);\n  const newCurlyBracePath = curlyBraceRightPath.replace('Z', '');\n  const curlyBraceRightNode = rc.path(newCurlyBracePath, options);\n  const rectPath = createPathFromPoints(rectPoints);\n  const rectShape = rc.path(rectPath, { ...options });\n  const curlyBraceRightShape = shapeSvg.insert('g', ':first-child');\n  curlyBraceRightShape.insert(() => rectShape, ':first-child').attr('stroke-opacity', 0);\n  curlyBraceRightShape.insert(() => curlyBraceRightNode, ':first-child');\n  curlyBraceRightShape.attr('class', 'text');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    curlyBraceRightShape.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    curlyBraceRightShape.selectAll('path').attr('style', nodeStyles);\n  }\n\n  curlyBraceRightShape.attr('transform', `translate(${-radius}, 0)`);\n\n  label.attr(\n    'transform',\n    `translate(${-w / 2 + (node.padding ?? 0) / 2 - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, curlyBraceRightShape);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, rectPoints, point);\n\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nfunction generateCirclePoints(\n  centerX: number,\n  centerY: number,\n  radius: number,\n  numPoints = 100,\n  startAngle = 0,\n  endAngle = 180\n) {\n  const points = [];\n\n  // Convert angles to radians\n  const startAngleRad = (startAngle * Math.PI) / 180;\n  const endAngleRad = (endAngle * Math.PI) / 180;\n\n  // Calculate the angle range in radians\n  const angleRange = endAngleRad - startAngleRad;\n\n  // Calculate the angle step\n  const angleStep = angleRange / (numPoints - 1);\n\n  for (let i = 0; i < numPoints; i++) {\n    const angle = startAngleRad + i * angleStep;\n    const x = centerX + radius * Math.cos(angle);\n    const y = centerY + radius * Math.sin(angle);\n    points.push({ x: -x, y: -y });\n  }\n\n  return points;\n}\n\nexport async function curlyBraces<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + (node.padding ?? 0);\n  const h = bbox.height + (node.padding ?? 0);\n  const radius = Math.max(5, h * 0.1);\n\n  const { cssStyles } = node;\n\n  const leftCurlyBracePoints = [\n    ...generateCirclePoints(w / 2, -h / 2, radius, 30, -90, 0),\n    { x: -w / 2 - radius, y: radius },\n    ...generateCirclePoints(w / 2 + radius * 2, -radius, radius, 20, -180, -270),\n    ...generateCirclePoints(w / 2 + radius * 2, radius, radius, 20, -90, -180),\n    { x: -w / 2 - radius, y: -h / 2 },\n    ...generateCirclePoints(w / 2, h / 2, radius, 20, 0, 90),\n  ];\n\n  const rightCurlyBracePoints = [\n    ...generateCirclePoints(-w / 2 + radius + radius / 2, -h / 2, radius, 20, -90, -180),\n    { x: w / 2 - radius / 2, y: radius },\n    ...generateCirclePoints(-w / 2 - radius / 2, -radius, radius, 20, 0, 90),\n    ...generateCirclePoints(-w / 2 - radius / 2, radius, radius, 20, -90, 0),\n    { x: w / 2 - radius / 2, y: -radius },\n    ...generateCirclePoints(-w / 2 + radius + radius / 2, h / 2, radius, 30, -180, -270),\n  ];\n\n  const rectPoints = [\n    { x: w / 2, y: -h / 2 - radius },\n    { x: -w / 2, y: -h / 2 - radius },\n    ...generateCirclePoints(w / 2, -h / 2, radius, 20, -90, 0),\n    { x: -w / 2 - radius, y: -radius },\n    ...generateCirclePoints(w / 2 + radius * 2, -radius, radius, 20, -180, -270),\n    ...generateCirclePoints(w / 2 + radius * 2, radius, radius, 20, -90, -180),\n    { x: -w / 2 - radius, y: h / 2 },\n    ...generateCirclePoints(w / 2, h / 2, radius, 20, 0, 90),\n    { x: -w / 2, y: h / 2 + radius },\n    { x: w / 2 - radius - radius / 2, y: h / 2 + radius },\n    ...generateCirclePoints(-w / 2 + radius + radius / 2, -h / 2, radius, 20, -90, -180),\n    { x: w / 2 - radius / 2, y: radius },\n    ...generateCirclePoints(-w / 2 - radius / 2, -radius, radius, 20, 0, 90),\n    ...generateCirclePoints(-w / 2 - radius / 2, radius, radius, 20, -90, 0),\n    { x: w / 2 - radius / 2, y: -radius },\n    ...generateCirclePoints(-w / 2 + radius + radius / 2, h / 2, radius, 30, -180, -270),\n  ];\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, { fill: 'none' });\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n  const leftCurlyBracePath = createPathFromPoints(leftCurlyBracePoints);\n  const newLeftCurlyBracePath = leftCurlyBracePath.replace('Z', '');\n  const leftCurlyBraceNode = rc.path(newLeftCurlyBracePath, options);\n  const rightCurlyBracePath = createPathFromPoints(rightCurlyBracePoints);\n  const newRightCurlyBracePath = rightCurlyBracePath.replace('Z', '');\n  const rightCurlyBraceNode = rc.path(newRightCurlyBracePath, options);\n  const rectPath = createPathFromPoints(rectPoints);\n  const rectShape = rc.path(rectPath, { ...options });\n  const curlyBracesShape = shapeSvg.insert('g', ':first-child');\n  curlyBracesShape.insert(() => rectShape, ':first-child').attr('stroke-opacity', 0);\n  curlyBracesShape.insert(() => leftCurlyBraceNode, ':first-child');\n  curlyBracesShape.insert(() => rightCurlyBraceNode, ':first-child');\n  curlyBracesShape.attr('class', 'text');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    curlyBracesShape.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    curlyBracesShape.selectAll('path').attr('style', nodeStyles);\n  }\n\n  curlyBracesShape.attr('transform', `translate(${radius - radius / 4}, 0)`);\n\n  label.attr(\n    'transform',\n    `translate(${-w / 2 + (node.padding ?? 0) / 2 - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, curlyBracesShape);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, rectPoints, point);\n\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import {\n  labelHelper,\n  updateNodeBounds,\n  getNodeClasses,\n  createPathFromPoints,\n  generateCirclePoints,\n} from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function curvedTrapezoid<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const minWidth = 80,\n    minHeight = 20;\n  const w = Math.max(minWidth, (bbox.width + (node.padding ?? 0) * 2) * 1.25, node?.width ?? 0);\n  const h = Math.max(minHeight, bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const radius = h / 2;\n\n  const { cssStyles } = node;\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const totalWidth = w,\n    totalHeight = h;\n  const rw = totalWidth - radius;\n  const tw = totalHeight / 4;\n\n  const points = [\n    { x: rw, y: 0 },\n    { x: tw, y: 0 },\n    { x: 0, y: totalHeight / 2 },\n    { x: tw, y: totalHeight },\n    { x: rw, y: totalHeight },\n    ...generateCirclePoints(-rw, -totalHeight / 2, radius, 50, 270, 90),\n  ];\n\n  const pathData = createPathFromPoints(points);\n  const shapeNode = rc.path(pathData, options);\n\n  const polygon = shapeSvg.insert(() => shapeNode, ':first-child');\n  polygon.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', nodeStyles);\n  }\n\n  polygon.attr('transform', `translate(${-w / 2}, ${-h / 2})`);\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\nimport { handleUndefinedAttr } from '../../../utils.js';\n\nexport const createCylinderPathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number,\n  rx: number,\n  ry: number\n): string => {\n  return [\n    `M${x},${y + ry}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n    `a${rx},${ry} 0,0,0 ${-width},0`,\n    `l0,${height}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n    `l0,${-height}`,\n  ].join(' ');\n};\nexport const createOuterCylinderPathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number,\n  rx: number,\n  ry: number\n): string => {\n  return [\n    `M${x},${y + ry}`,\n    `M${x + width},${y + ry}`,\n    `a${rx},${ry} 0,0,0 ${-width},0`,\n    `l0,${height}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n    `l0,${-height}`,\n  ].join(' ');\n};\nexport const createInnerCylinderPathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number,\n  rx: number,\n  ry: number\n): string => {\n  return [`M${x - width / 2},${-height / 2}`, `a${rx},${ry} 0,0,0 ${width},0`].join(' ');\n};\nexport async function cylinder<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + node.padding, node.width ?? 0);\n  const rx = w / 2;\n  const ry = rx / (2.5 + w / 50);\n  const h = Math.max(bbox.height + ry + node.padding, node.height ?? 0);\n\n  let cylinder: D3Selection<SVGPathElement> | D3Selection<SVGGElement>;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const outerPathData = createOuterCylinderPathD(0, 0, w, h, rx, ry);\n    const innerPathData = createInnerCylinderPathD(0, ry, w, h, rx, ry);\n    const outerNode = rc.path(outerPathData, userNodeOverrides(node, {}));\n    const innerLine = rc.path(innerPathData, userNodeOverrides(node, { fill: 'none' }));\n\n    cylinder = shapeSvg.insert(() => innerLine, ':first-child');\n    cylinder = shapeSvg.insert(() => outerNode, ':first-child');\n    cylinder.attr('class', 'basic label-container');\n    if (cssStyles) {\n      cylinder.attr('style', cssStyles);\n    }\n  } else {\n    const pathData = createCylinderPathD(0, 0, w, h, rx, ry);\n    cylinder = shapeSvg\n      .insert('path', ':first-child')\n      .attr('d', pathData)\n      .attr('class', 'basic label-container')\n      .attr('style', handleUndefinedAttr(cssStyles))\n      .attr('style', nodeStyles);\n  }\n\n  cylinder.attr('label-offset-y', ry);\n  cylinder.attr('transform', `translate(${-w / 2}, ${-(h / 2 + ry)})`);\n\n  updateNodeBounds(node, cylinder);\n\n  label.attr(\n    'transform',\n    `translate(${-(bbox.width / 2) - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) + (node.padding ?? 0) / 1.5 - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  node.intersect = function (point) {\n    const pos = intersect.rect(node, point);\n    const x = pos.x - (node.x ?? 0);\n\n    if (\n      rx != 0 &&\n      (Math.abs(x) < (node.width ?? 0) / 2 ||\n        (Math.abs(x) == (node.width ?? 0) / 2 &&\n          Math.abs(pos.y - (node.y ?? 0)) > (node.height ?? 0) / 2 - ry))\n    ) {\n      let y = ry * ry * (1 - (x * x) / (rx * rx));\n      if (y > 0) {\n        y = Math.sqrt(y);\n      }\n      y = ry - y;\n      if (point.y - (node.y ?? 0) > 0) {\n        y = -y;\n      }\n\n      pos.y += y;\n    }\n\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function dividedRectangle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const rectOffset = h * 0.2;\n\n  const x = -w / 2;\n  const y = -h / 2 - rectOffset / 2;\n\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const pts = [\n    { x, y: y + rectOffset },\n    { x: -x, y: y + rectOffset },\n    { x: -x, y: -y },\n    { x, y: -y },\n    { x, y },\n    { x: -x, y },\n    { x: -x, y: y + rectOffset },\n  ];\n\n  const poly = rc.polygon(\n    pts.map((p) => [p.x, p.y]),\n    options\n  );\n\n  const polygon = shapeSvg.insert(() => poly, ':first-child');\n  polygon.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    polygon.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    polygon.selectAll('path').attr('style', nodeStyles);\n  }\n\n  label.attr(\n    'transform',\n    `translate(${x + (node.padding ?? 0) / 2 - (bbox.x - (bbox.left ?? 0))}, ${y + rectOffset + (node.padding ?? 0) / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    const pos = intersect.rect(node, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { log } from '../../../logger.js';\nimport { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\nimport { handleUndefinedAttr } from '../../../utils.js';\n\nexport async function doublecircle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, halfPadding } = await labelHelper(parent, node, getNodeClasses(node));\n  const gap = 5;\n  const outerRadius = bbox.width / 2 + halfPadding + gap;\n  const innerRadius = bbox.width / 2 + halfPadding;\n\n  let circleGroup;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const outerOptions = userNodeOverrides(node, { roughness: 0.2, strokeWidth: 2.5 });\n\n    const innerOptions = userNodeOverrides(node, { roughness: 0.2, strokeWidth: 1.5 });\n    const outerRoughNode = rc.circle(0, 0, outerRadius * 2, outerOptions);\n    const innerRoughNode = rc.circle(0, 0, innerRadius * 2, innerOptions);\n\n    circleGroup = shapeSvg.insert('g', ':first-child');\n    // circleGroup = circleGroup.insert(() => outerRoughNode, ':first-child');\n    circleGroup\n      .attr('class', handleUndefinedAttr(node.cssClasses))\n      .attr('style', handleUndefinedAttr(cssStyles));\n\n    circleGroup.node()?.appendChild(outerRoughNode);\n    circleGroup.node()?.appendChild(innerRoughNode);\n  } else {\n    circleGroup = shapeSvg.insert('g', ':first-child');\n\n    const outerCircle = circleGroup.insert('circle', ':first-child');\n    const innerCircle = circleGroup.insert('circle');\n    circleGroup.attr('class', 'basic label-container').attr('style', nodeStyles);\n\n    outerCircle\n      .attr('class', 'outer-circle')\n      .attr('style', nodeStyles)\n      .attr('r', outerRadius)\n      .attr('cx', 0)\n      .attr('cy', 0);\n\n    innerCircle\n      .attr('class', 'inner-circle')\n      .attr('style', nodeStyles)\n      .attr('r', innerRadius)\n      .attr('cx', 0)\n      .attr('cy', 0);\n  }\n\n  updateNodeBounds(node, circleGroup);\n\n  node.intersect = function (point) {\n    log.info('DoubleCircle intersect', node, outerRadius, point);\n    return intersect.circle(node, outerRadius, point);\n  };\n\n  return shapeSvg;\n}\n", "import rough from 'roughjs';\nimport { log } from '../../../logger.js';\nimport type { Node, ShapeRenderOptions } from '../../types.js';\nimport intersect from '../intersect/index.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport { getNodeClasses, updateNodeBounds } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport function filledCircle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  { config: { themeVariables } }: ShapeRenderOptions\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.label = '';\n  node.labelStyle = labelStyles;\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', getNodeClasses(node))\n    .attr('id', node.domId ?? node.id);\n  const radius = 7;\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const { nodeBorder } = themeVariables;\n  const options = userNodeOverrides(node, { fillStyle: 'solid' });\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n  }\n\n  const circleNode = rc.circle(0, 0, radius * 2, options);\n\n  const filledCircle = shapeSvg.insert(() => circleNode, ':first-child');\n\n  filledCircle.selectAll('path').attr('style', `fill: ${nodeBorder} !important;`);\n\n  if (cssStyles && cssStyles.length > 0 && node.look !== 'handDrawn') {\n    filledCircle.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    filledCircle.selectAll('path').attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, filledCircle);\n\n  node.intersect = function (point) {\n    log.info('filledCircle intersect', node, { radius, point });\n    const pos = intersect.circle(node, radius, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { log } from '../../../logger.js';\nimport { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { createPathFromPoints } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function flippedTriangle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const w = bbox.width + (node.padding ?? 0);\n  const h = w + bbox.height;\n\n  const tw = w + bbox.height;\n  const points = [\n    { x: 0, y: -h },\n    { x: tw, y: -h },\n    { x: tw / 2, y: 0 },\n  ];\n\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n  const pathData = createPathFromPoints(points);\n  const roughNode = rc.path(pathData, options);\n\n  const flippedTriangle = shapeSvg\n    .insert(() => roughNode, ':first-child')\n    .attr('transform', `translate(${-h / 2}, ${h / 2})`);\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    flippedTriangle.selectChildren('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    flippedTriangle.selectChildren('path').attr('style', nodeStyles);\n  }\n\n  node.width = w;\n  node.height = h;\n\n  updateNodeBounds(node, flippedTriangle);\n\n  label.attr(\n    'transform',\n    `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))}, ${-h / 2 + (node.padding ?? 0) / 2 + (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  node.intersect = function (point) {\n    log.info('Triangle intersect', node, points, point);\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import rough from 'roughjs';\nimport type { Node, ShapeRenderOptions } from '../../types.js';\nimport intersect from '../intersect/index.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport { getNodeClasses, updateNodeBounds } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport function forkJoin<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  { dir, config: { state, themeVariables } }: ShapeRenderOptions\n) {\n  const { nodeStyles } = styles2String(node);\n  node.label = '';\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', getNodeClasses(node))\n    .attr('id', node.domId ?? node.id);\n\n  const { cssStyles } = node;\n  let width = Math.max(70, node?.width ?? 0);\n  let height = Math.max(10, node?.height ?? 0);\n\n  if (dir === 'LR') {\n    width = Math.max(10, node?.width ?? 0);\n    height = Math.max(70, node?.height ?? 0);\n  }\n\n  const x = (-1 * width) / 2;\n  const y = (-1 * height) / 2;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {\n    stroke: themeVariables.lineColor,\n    fill: themeVariables.lineColor,\n  });\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const roughNode = rc.rectangle(x, y, width, height, options);\n\n  const shape = shapeSvg.insert(() => roughNode, ':first-child');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    shape.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    shape.selectAll('path').attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, shape);\n  const padding = state?.padding ?? 0;\n  if (node.width && node.height) {\n    node.width += padding / 2 || 0;\n    node.height += padding / 2 || 0;\n  }\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n  return shapeSvg;\n}\n", "import { log } from '../../../logger.js';\nimport {\n  labelHelper,\n  updateNodeBounds,\n  getNodeClasses,\n  createPathFromPoints,\n  generateCirclePoints,\n} from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function halfRoundedRectangle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const minWidth = 80,\n    minHeight = 50;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(minWidth, bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(minHeight, bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const radius = h / 2;\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const points = [\n    { x: -w / 2, y: -h / 2 },\n    { x: w / 2 - radius, y: -h / 2 },\n    ...generateCirclePoints(-w / 2 + radius, 0, radius, 50, 90, 270),\n    { x: w / 2 - radius, y: h / 2 },\n    { x: -w / 2, y: h / 2 },\n  ];\n\n  const pathData = createPathFromPoints(points);\n  const shapeNode = rc.path(pathData, options);\n  const polygon = shapeSvg.insert(() => shapeNode, ':first-child');\n  polygon.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', nodeStyles);\n  }\n\n  // label.attr(\n  //   'transform',\n  //   `translate(${-w / 2 + (node.padding ?? 0) - (bbox.x - (bbox.left ?? 0))}, ${-h / 2 + (node.padding ?? 0) - (bbox.y - (bbox.top ?? 0))})`\n  // );\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    log.info('Pill intersect', node, { radius, point });\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { insertPolygonShape } from './insertPolygonShape.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport const createHexagonPathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number,\n  m: number\n): string => {\n  return [\n    `M${x + m},${y}`,\n    `L${x + width - m},${y}`,\n    `L${x + width},${y - height / 2}`,\n    `L${x + width - m},${y - height}`,\n    `L${x + m},${y - height}`,\n    `L${x},${y - height / 2}`,\n    'Z',\n  ].join(' ');\n};\n\nexport async function hexagon<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const f = 4;\n  const h = bbox.height + node.padding;\n  const m = h / f;\n  const w = bbox.width + 2 * m + node.padding;\n  const points = [\n    { x: m, y: 0 },\n    { x: w - m, y: 0 },\n    { x: w, y: -h / 2 },\n    { x: w - m, y: -h },\n    { x: m, y: -h },\n    { x: 0, y: -h / 2 },\n  ];\n\n  let polygon: D3Selection<SVGGElement> | Awaited<ReturnType<typeof insertPolygonShape>>;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createHexagonPathD(0, 0, w, h, m);\n    const roughNode = rc.path(pathData, options);\n\n    polygon = shapeSvg\n      .insert(() => roughNode, ':first-child')\n      .attr('transform', `translate(${-w / 2}, ${h / 2})`);\n\n    if (cssStyles) {\n      polygon.attr('style', cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n\n  if (nodeStyles) {\n    polygon.attr('style', nodeStyles);\n  }\n\n  node.width = w;\n  node.height = h;\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import { log } from '../../../logger.js';\nimport { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function hourglass<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.label = '';\n  node.labelStyle = labelStyles;\n  const { shapeSvg } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const w = Math.max(30, node?.width ?? 0);\n  const h = Math.max(30, node?.height ?? 0);\n\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const points = [\n    { x: 0, y: 0 },\n    { x: w, y: 0 },\n    { x: 0, y: h },\n    { x: w, y: h },\n  ];\n\n  const pathData = createPathFromPoints(points);\n  const shapeNode = rc.path(pathData, options);\n  const polygon = shapeSvg.insert(() => shapeNode, ':first-child');\n  polygon.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', nodeStyles);\n  }\n\n  polygon.attr('transform', `translate(${-w / 2}, ${-h / 2})`);\n\n  updateNodeBounds(node, polygon);\n\n  // label.attr('transform', `translate(${-bbox.width / 2}, ${(h/2)})`); // To transform text below hourglass shape\n\n  node.intersect = function (point) {\n    log.info('Pill intersect', node, { points });\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import rough from 'roughjs';\nimport { log } from '../../../logger.js';\nimport { getIconSVG } from '../../icons.js';\nimport type { Node, ShapeRenderOptions } from '../../types.js';\nimport intersect from '../intersect/index.js';\nimport { compileStyles, styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport { labelHelper, updateNodeBounds } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function icon<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  { config: { themeVariables, flowchart } }: ShapeRenderOptions\n) {\n  const { labelStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const assetHeight = node.assetHeight ?? 48;\n  const assetWidth = node.assetWidth ?? 48;\n  const iconSize = Math.max(assetHeight, assetWidth);\n  const defaultWidth = flowchart?.wrappingWidth;\n  node.width = Math.max(iconSize, defaultWidth ?? 0);\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, 'icon-shape default');\n\n  const topLabel = node.pos === 't';\n\n  const height = iconSize;\n  const width = iconSize;\n  const { nodeBorder } = themeVariables;\n  const { stylesMap } = compileStyles(node);\n\n  const x = -width / 2;\n  const y = -height / 2;\n\n  const labelPadding = node.label ? 8 : 0;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, { stroke: 'none', fill: 'none' });\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const iconNode = rc.rectangle(x, y, width, height, options);\n\n  const outerWidth = Math.max(width, bbox.width);\n  const outerHeight = height + bbox.height + labelPadding;\n\n  const outerNode = rc.rectangle(-outerWidth / 2, -outerHeight / 2, outerWidth, outerHeight, {\n    ...options,\n    fill: 'transparent',\n    stroke: 'none',\n  });\n\n  const iconShape = shapeSvg.insert(() => iconNode, ':first-child');\n  const outerShape = shapeSvg.insert(() => outerNode);\n\n  if (node.icon) {\n    const iconElem = shapeSvg.append('g');\n    iconElem.html(\n      `<g>${await getIconSVG(node.icon, {\n        height: iconSize,\n        width: iconSize,\n        fallbackPrefix: '',\n      })}</g>`\n    );\n    const iconBBox = iconElem.node()!.getBBox();\n    const iconWidth = iconBBox.width;\n    const iconHeight = iconBBox.height;\n    const iconX = iconBBox.x;\n    const iconY = iconBBox.y;\n    iconElem.attr(\n      'transform',\n      `translate(${-iconWidth / 2 - iconX},${\n        topLabel\n          ? bbox.height / 2 + labelPadding / 2 - iconHeight / 2 - iconY\n          : -bbox.height / 2 - labelPadding / 2 - iconHeight / 2 - iconY\n      })`\n    );\n    iconElem.attr('style', `color: ${stylesMap.get('stroke') ?? nodeBorder};`);\n  }\n\n  label.attr(\n    'transform',\n    `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))},${\n      topLabel ? -outerHeight / 2 : outerHeight / 2 - bbox.height\n    })`\n  );\n\n  iconShape.attr(\n    'transform',\n    `translate(${0},${\n      topLabel ? bbox.height / 2 + labelPadding / 2 : -bbox.height / 2 - labelPadding / 2\n    })`\n  );\n\n  updateNodeBounds(node, outerShape);\n\n  node.intersect = function (point) {\n    log.info('iconSquare intersect', node, point);\n    if (!node.label) {\n      return intersect.rect(node, point);\n    }\n    const dx = node.x ?? 0;\n    const dy = node.y ?? 0;\n    const nodeHeight = node.height ?? 0;\n    let points = [];\n    if (topLabel) {\n      points = [\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n      ];\n    } else {\n      points = [\n        { x: dx - width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx + bbox.width / 2 / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx - width / 2, y: dy - nodeHeight / 2 + height },\n      ];\n    }\n\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import rough from 'roughjs';\nimport { log } from '../../../logger.js';\nimport { getIconSVG } from '../../icons.js';\nimport type { Node, ShapeRenderOptions } from '../../types.js';\nimport intersect from '../intersect/index.js';\nimport { compileStyles, styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport { labelHelper, updateNodeBounds } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function iconCircle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  { config: { themeVariables, flowchart } }: ShapeRenderOptions\n) {\n  const { labelStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const assetHeight = node.assetHeight ?? 48;\n  const assetWidth = node.assetWidth ?? 48;\n  const iconSize = Math.max(assetHeight, assetWidth);\n  const defaultWidth = flowchart?.wrappingWidth;\n  node.width = Math.max(iconSize, defaultWidth ?? 0);\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, 'icon-shape default');\n\n  const padding = 20;\n  const labelPadding = node.label ? 8 : 0;\n\n  const topLabel = node.pos === 't';\n\n  const { nodeBorder, mainBkg } = themeVariables;\n  const { stylesMap } = compileStyles(node);\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n  const fill = stylesMap.get('fill');\n  options.stroke = fill ?? mainBkg;\n\n  const iconElem = shapeSvg.append('g');\n  if (node.icon) {\n    iconElem.html(\n      `<g>${await getIconSVG(node.icon, {\n        height: iconSize,\n        width: iconSize,\n        fallbackPrefix: '',\n      })}</g>`\n    );\n  }\n  const iconBBox = iconElem.node()!.getBBox();\n  const iconWidth = iconBBox.width;\n  const iconHeight = iconBBox.height;\n  const iconX = iconBBox.x;\n  const iconY = iconBBox.y;\n\n  const diameter = Math.max(iconWidth, iconHeight) * Math.SQRT2 + padding * 2;\n  const iconNode = rc.circle(0, 0, diameter, options);\n\n  const outerWidth = Math.max(diameter, bbox.width);\n  const outerHeight = diameter + bbox.height + labelPadding;\n\n  const outerNode = rc.rectangle(-outerWidth / 2, -outerHeight / 2, outerWidth, outerHeight, {\n    ...options,\n    fill: 'transparent',\n    stroke: 'none',\n  });\n\n  const iconShape = shapeSvg.insert(() => iconNode, ':first-child');\n  const outerShape = shapeSvg.insert(() => outerNode);\n  iconElem.attr(\n    'transform',\n    `translate(${-iconWidth / 2 - iconX},${\n      topLabel\n        ? bbox.height / 2 + labelPadding / 2 - iconHeight / 2 - iconY\n        : -bbox.height / 2 - labelPadding / 2 - iconHeight / 2 - iconY\n    })`\n  );\n  iconElem.attr('style', `color: ${stylesMap.get('stroke') ?? nodeBorder};`);\n  label.attr(\n    'transform',\n    `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))},${\n      topLabel ? -outerHeight / 2 : outerHeight / 2 - bbox.height\n    })`\n  );\n\n  iconShape.attr(\n    'transform',\n    `translate(${0},${\n      topLabel ? bbox.height / 2 + labelPadding / 2 : -bbox.height / 2 - labelPadding / 2\n    })`\n  );\n\n  updateNodeBounds(node, outerShape);\n\n  node.intersect = function (point) {\n    log.info('iconSquare intersect', node, point);\n    const pos = intersect.rect(node, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import rough from 'roughjs';\nimport { log } from '../../../logger.js';\nimport { getIconSVG } from '../../icons.js';\nimport type { Node, ShapeRenderOptions } from '../../types.js';\nimport intersect from '../intersect/index.js';\nimport { compileStyles, styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport { createRoundedRectPathD } from './roundedRectPath.js';\nimport { labelHelper, updateNodeBounds } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function iconRounded<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  { config: { themeVariables, flowchart } }: ShapeRenderOptions\n) {\n  const { labelStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const assetHeight = node.assetHeight ?? 48;\n  const assetWidth = node.assetWidth ?? 48;\n  const iconSize = Math.max(assetHeight, assetWidth);\n  const defaultWidth = flowchart?.wrappingWidth;\n  node.width = Math.max(iconSize, defaultWidth ?? 0);\n  const { shapeSvg, bbox, halfPadding, label } = await labelHelper(\n    parent,\n    node,\n    'icon-shape default'\n  );\n\n  const topLabel = node.pos === 't';\n\n  const height = iconSize + halfPadding * 2;\n  const width = iconSize + halfPadding * 2;\n  const { nodeBorder, mainBkg } = themeVariables;\n  const { stylesMap } = compileStyles(node);\n\n  const x = -width / 2;\n  const y = -height / 2;\n\n  const labelPadding = node.label ? 8 : 0;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n  const fill = stylesMap.get('fill');\n  options.stroke = fill ?? mainBkg;\n\n  const iconNode = rc.path(createRoundedRectPathD(x, y, width, height, 5), options);\n\n  const outerWidth = Math.max(width, bbox.width);\n  const outerHeight = height + bbox.height + labelPadding;\n\n  const outerNode = rc.rectangle(-outerWidth / 2, -outerHeight / 2, outerWidth, outerHeight, {\n    ...options,\n    fill: 'transparent',\n    stroke: 'none',\n  });\n\n  const iconShape = shapeSvg.insert(() => iconNode, ':first-child').attr('class', 'icon-shape2');\n  const outerShape = shapeSvg.insert(() => outerNode);\n\n  if (node.icon) {\n    const iconElem = shapeSvg.append('g');\n    iconElem.html(\n      `<g>${await getIconSVG(node.icon, {\n        height: iconSize,\n        width: iconSize,\n        fallbackPrefix: '',\n      })}</g>`\n    );\n    const iconBBox = iconElem.node()!.getBBox();\n    const iconWidth = iconBBox.width;\n    const iconHeight = iconBBox.height;\n    const iconX = iconBBox.x;\n    const iconY = iconBBox.y;\n    iconElem.attr(\n      'transform',\n      `translate(${-iconWidth / 2 - iconX},${\n        topLabel\n          ? bbox.height / 2 + labelPadding / 2 - iconHeight / 2 - iconY\n          : -bbox.height / 2 - labelPadding / 2 - iconHeight / 2 - iconY\n      })`\n    );\n    iconElem.attr('style', `color: ${stylesMap.get('stroke') ?? nodeBorder};`);\n  }\n\n  label.attr(\n    'transform',\n    `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))},${\n      topLabel ? -outerHeight / 2 : outerHeight / 2 - bbox.height\n    })`\n  );\n\n  iconShape.attr(\n    'transform',\n    `translate(${0},${\n      topLabel ? bbox.height / 2 + labelPadding / 2 : -bbox.height / 2 - labelPadding / 2\n    })`\n  );\n\n  updateNodeBounds(node, outerShape);\n\n  node.intersect = function (point) {\n    log.info('iconSquare intersect', node, point);\n    if (!node.label) {\n      return intersect.rect(node, point);\n    }\n    const dx = node.x ?? 0;\n    const dy = node.y ?? 0;\n    const nodeHeight = node.height ?? 0;\n    let points = [];\n    if (topLabel) {\n      points = [\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n      ];\n    } else {\n      points = [\n        { x: dx - width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx + bbox.width / 2 / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx - width / 2, y: dy - nodeHeight / 2 + height },\n      ];\n    }\n\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import rough from 'roughjs';\nimport { log } from '../../../logger.js';\nimport { getIconSVG } from '../../icons.js';\nimport type { Node, ShapeRenderOptions } from '../../types.js';\nimport intersect from '../intersect/index.js';\nimport { createRoundedRectPathD } from './roundedRectPath.js';\nimport { compileStyles, styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport { labelHelper, updateNodeBounds } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function iconSquare<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  { config: { themeVariables, flowchart } }: ShapeRenderOptions\n) {\n  const { labelStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const assetHeight = node.assetHeight ?? 48;\n  const assetWidth = node.assetWidth ?? 48;\n  const iconSize = Math.max(assetHeight, assetWidth);\n  const defaultWidth = flowchart?.wrappingWidth;\n  node.width = Math.max(iconSize, defaultWidth ?? 0);\n  const { shapeSvg, bbox, halfPadding, label } = await labelHelper(\n    parent,\n    node,\n    'icon-shape default'\n  );\n\n  const topLabel = node.pos === 't';\n\n  const height = iconSize + halfPadding * 2;\n  const width = iconSize + halfPadding * 2;\n  const { nodeBorder, mainBkg } = themeVariables;\n  const { stylesMap } = compileStyles(node);\n\n  const x = -width / 2;\n  const y = -height / 2;\n\n  const labelPadding = node.label ? 8 : 0;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n  const fill = stylesMap.get('fill');\n  options.stroke = fill ?? mainBkg;\n\n  const iconNode = rc.path(createRoundedRectPathD(x, y, width, height, 0.1), options);\n\n  const outerWidth = Math.max(width, bbox.width);\n  const outerHeight = height + bbox.height + labelPadding;\n\n  const outerNode = rc.rectangle(-outerWidth / 2, -outerHeight / 2, outerWidth, outerHeight, {\n    ...options,\n    fill: 'transparent',\n    stroke: 'none',\n  });\n\n  const iconShape = shapeSvg.insert(() => iconNode, ':first-child');\n  const outerShape = shapeSvg.insert(() => outerNode);\n\n  if (node.icon) {\n    const iconElem = shapeSvg.append('g');\n    iconElem.html(\n      `<g>${await getIconSVG(node.icon, {\n        height: iconSize,\n        width: iconSize,\n        fallbackPrefix: '',\n      })}</g>`\n    );\n    const iconBBox = iconElem.node()!.getBBox();\n    const iconWidth = iconBBox.width;\n    const iconHeight = iconBBox.height;\n    const iconX = iconBBox.x;\n    const iconY = iconBBox.y;\n    iconElem.attr(\n      'transform',\n      `translate(${-iconWidth / 2 - iconX},${\n        topLabel\n          ? bbox.height / 2 + labelPadding / 2 - iconHeight / 2 - iconY\n          : -bbox.height / 2 - labelPadding / 2 - iconHeight / 2 - iconY\n      })`\n    );\n    iconElem.attr('style', `color: ${stylesMap.get('stroke') ?? nodeBorder};`);\n  }\n\n  label.attr(\n    'transform',\n    `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))},${\n      topLabel ? -outerHeight / 2 : outerHeight / 2 - bbox.height\n    })`\n  );\n\n  iconShape.attr(\n    'transform',\n    `translate(${0},${\n      topLabel ? bbox.height / 2 + labelPadding / 2 : -bbox.height / 2 - labelPadding / 2\n    })`\n  );\n\n  updateNodeBounds(node, outerShape);\n\n  node.intersect = function (point) {\n    log.info('iconSquare intersect', node, point);\n    if (!node.label) {\n      return intersect.rect(node, point);\n    }\n    const dx = node.x ?? 0;\n    const dy = node.y ?? 0;\n    const nodeHeight = node.height ?? 0;\n    let points = [];\n    if (topLabel) {\n      points = [\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n      ];\n    } else {\n      points = [\n        { x: dx - width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx + bbox.width / 2 / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + height },\n        { x: dx - width / 2, y: dy - nodeHeight / 2 + height },\n      ];\n    }\n\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import rough from 'roughjs';\nimport { log } from '../../../logger.js';\nimport type { Node, ShapeRenderOptions } from '../../types.js';\nimport intersect from '../intersect/index.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport { labelHelper, updateNodeBounds } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function imageSquare<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  { config: { flowchart } }: ShapeRenderOptions\n) {\n  const img = new Image();\n  img.src = node?.img ?? '';\n  await img.decode();\n\n  const imageNaturalWidth = Number(img.naturalWidth.toString().replace('px', ''));\n  const imageNaturalHeight = Number(img.naturalHeight.toString().replace('px', ''));\n  node.imageAspectRatio = imageNaturalWidth / imageNaturalHeight;\n\n  const { labelStyles } = styles2String(node);\n\n  node.labelStyle = labelStyles;\n\n  const defaultWidth = flowchart?.wrappingWidth;\n  node.defaultWidth = flowchart?.wrappingWidth;\n\n  const imageRawWidth = Math.max(\n    node.label ? (defaultWidth ?? 0) : 0,\n    node?.assetWidth ?? imageNaturalWidth\n  );\n\n  const imageWidth =\n    node.constraint === 'on'\n      ? node?.assetHeight\n        ? node.assetHeight * node.imageAspectRatio\n        : imageRawWidth\n      : imageRawWidth;\n\n  const imageHeight =\n    node.constraint === 'on'\n      ? imageWidth / node.imageAspectRatio\n      : (node?.assetHeight ?? imageNaturalHeight);\n  node.width = Math.max(imageWidth, defaultWidth ?? 0);\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, 'image-shape default');\n\n  const topLabel = node.pos === 't';\n\n  const x = -imageWidth / 2;\n  const y = -imageHeight / 2;\n\n  const labelPadding = node.label ? 8 : 0;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const imageNode = rc.rectangle(x, y, imageWidth, imageHeight, options);\n\n  const outerWidth = Math.max(imageWidth, bbox.width);\n  const outerHeight = imageHeight + bbox.height + labelPadding;\n\n  const outerNode = rc.rectangle(-outerWidth / 2, -outerHeight / 2, outerWidth, outerHeight, {\n    ...options,\n    fill: 'none',\n    stroke: 'none',\n  });\n\n  const iconShape = shapeSvg.insert(() => imageNode, ':first-child');\n  const outerShape = shapeSvg.insert(() => outerNode);\n\n  if (node.img) {\n    const image = shapeSvg.append('image');\n\n    // Set the image attributes\n    image.attr('href', node.img);\n    image.attr('width', imageWidth);\n    image.attr('height', imageHeight);\n    image.attr('preserveAspectRatio', 'none');\n\n    image.attr(\n      'transform',\n      `translate(${-imageWidth / 2},${topLabel ? outerHeight / 2 - imageHeight : -outerHeight / 2})`\n    );\n  }\n\n  label.attr(\n    'transform',\n    `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))},${\n      topLabel\n        ? -imageHeight / 2 - bbox.height / 2 - labelPadding / 2\n        : imageHeight / 2 - bbox.height / 2 + labelPadding / 2\n    })`\n  );\n\n  iconShape.attr(\n    'transform',\n    `translate(${0},${\n      topLabel ? bbox.height / 2 + labelPadding / 2 : -bbox.height / 2 - labelPadding / 2\n    })`\n  );\n\n  updateNodeBounds(node, outerShape);\n\n  node.intersect = function (point) {\n    log.info('iconSquare intersect', node, point);\n    if (!node.label) {\n      return intersect.rect(node, point);\n    }\n    const dx = node.x ?? 0;\n    const dy = node.y ?? 0;\n    const nodeHeight = node.height ?? 0;\n    let points = [];\n    if (topLabel) {\n      points = [\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + imageWidth / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx + imageWidth / 2, y: dy + nodeHeight / 2 },\n        { x: dx - imageWidth / 2, y: dy + nodeHeight / 2 },\n        { x: dx - imageWidth / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + bbox.height + labelPadding },\n      ];\n    } else {\n      points = [\n        { x: dx - imageWidth / 2, y: dy - nodeHeight / 2 },\n        { x: dx + imageWidth / 2, y: dy - nodeHeight / 2 },\n        { x: dx + imageWidth / 2, y: dy - nodeHeight / 2 + imageHeight },\n        { x: dx + bbox.width / 2, y: dy - nodeHeight / 2 + imageHeight },\n        { x: dx + bbox.width / 2 / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy + nodeHeight / 2 },\n        { x: dx - bbox.width / 2, y: dy - nodeHeight / 2 + imageHeight },\n        { x: dx - imageWidth / 2, y: dy - nodeHeight / 2 + imageHeight },\n      ];\n    }\n\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { insertPolygonShape } from './insertPolygonShape.js';\nimport type { D3Selection } from '../../../types.js';\n\n// export const createInvertedTrapezoidPathD = (\n//   x: number,\n//   y: number,\n//   width: number,\n//   height: number\n// ): string => {\n//   return [\n//     `M${x + height / 6},${y}`,\n//     `L${x + width - height / 6},${y}`,\n//     `L${x + width + (2 * height) / 6},${y - height}`,\n//     `L${x - (2 * height) / 6},${y - height}`,\n//     'Z',\n//   ].join(' ');\n// };\n\nexport async function inv_trapezoid<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n\n  const points = [\n    { x: 0, y: 0 },\n    { x: w, y: 0 },\n    { x: w + (3 * h) / 6, y: -h },\n    { x: (-3 * h) / 6, y: -h },\n  ];\n\n  let polygon: typeof shapeSvg | ReturnType<typeof insertPolygonShape>;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createPathFromPoints(points);\n    // const pathData = createInvertedTrapezoidPathD(0, 0, w, h);\n    const roughNode = rc.path(pathData, options);\n\n    polygon = shapeSvg\n      .insert(() => roughNode, ':first-child')\n      .attr('transform', `translate(${-w / 2}, ${h / 2})`);\n\n    if (cssStyles) {\n      polygon.attr('style', cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n\n  if (nodeStyles) {\n    polygon.attr('style', nodeStyles);\n  }\n\n  node.width = w;\n  node.height = h;\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node, RectOptions } from '../../types.js';\nimport { createRoundedRectPathD } from './roundedRectPath.js';\nimport { userNodeOverrides, styles2String } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\nimport { handleUndefinedAttr } from '../../../utils.js';\n\nexport async function drawRect<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  options: RectOptions\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  // console.log('IPI labelStyles:', labelStyles);\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const totalWidth = Math.max(bbox.width + options.labelPaddingX * 2, node?.width || 0);\n  const totalHeight = Math.max(bbox.height + options.labelPaddingY * 2, node?.height || 0);\n  const x = -totalWidth / 2;\n  const y = -totalHeight / 2;\n\n  // log.info('IPI node = ', node);\n\n  let rect;\n  let { rx, ry } = node;\n  const { cssStyles } = node;\n\n  //use options rx, ry overrides if present\n  if (options?.rx && options.ry) {\n    rx = options.rx;\n    ry = options.ry;\n  }\n\n  if (node.look === 'handDrawn') {\n    // @ts-ignore TODO: Fix rough typings\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n\n    const roughNode =\n      rx || ry\n        ? rc.path(createRoundedRectPathD(x, y, totalWidth, totalHeight, rx || 0), options)\n        : rc.rectangle(x, y, totalWidth, totalHeight, options);\n\n    rect = shapeSvg.insert(() => roughNode, ':first-child');\n    rect.attr('class', 'basic label-container').attr('style', handleUndefinedAttr(cssStyles));\n  } else {\n    rect = shapeSvg.insert('rect', ':first-child');\n\n    rect\n      .attr('class', 'basic label-container')\n      .attr('style', nodeStyles)\n      .attr('rx', handleUndefinedAttr(rx))\n      .attr('ry', handleUndefinedAttr(ry))\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', totalWidth)\n      .attr('height', totalHeight);\n  }\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n}\n", "import type { Node, RectOptions } from '../../types.js';\nimport { drawRect } from './drawRect.js';\nimport { labelHelper, updateNodeBounds } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function roundedRect<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const options = {\n    rx: 5,\n    ry: 5,\n    classes: '',\n    labelPaddingX: (node?.padding || 0) * 1,\n    labelPaddingY: (node?.padding || 0) * 1,\n  } as RectOptions;\n\n  return drawRect(parent, node, options);\n}\n\nexport async function labelRect<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, 'label');\n\n  // log.trace('Classes = ', node.class);\n  // add the rect\n  const rect = shapeSvg.insert('rect', ':first-child');\n\n  // Hide the rect we are only after the label\n  const totalWidth = 0.1;\n  const totalHeight = 0.1;\n  rect.attr('width', totalWidth).attr('height', totalHeight);\n  shapeSvg.attr('class', 'label edgeLabel');\n  label.attr(\n    'transform',\n    `translate(${-(bbox.width / 2) - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  // if (node.props) {\n  //   const propKeys = new Set(Object.keys(node.props));\n  //   if (node.props.borders) {\n  //     applyNodePropertyBorders(rect, node.borders, totalWidth, totalHeight);\n  //     propKeys.delete('borders');\n  //   }\n  //   propKeys.forEach((propKey) => {\n  //     log.warn(`Unknown node property ${propKey}`);\n  //   });\n  // }\n\n  updateNodeBounds(node, rect);\n  // node.width = 1;\n  // node.height = 1;\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { insertPolygonShape } from './insertPolygonShape.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function lean_left<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0), node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0), node?.height ?? 0);\n  const points = [\n    { x: 0, y: 0 },\n    { x: w + (3 * h) / 6, y: 0 },\n    { x: w, y: -h },\n    { x: -(3 * h) / 6, y: -h },\n  ];\n\n  let polygon: typeof shapeSvg | ReturnType<typeof insertPolygonShape>;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createPathFromPoints(points);\n    // const pathData = createLeanLeftPathD(0, 0, w, h);\n    const roughNode = rc.path(pathData, options);\n\n    polygon = shapeSvg\n      .insert(() => roughNode, ':first-child')\n      .attr('transform', `translate(${-w / 2}, ${h / 2})`);\n\n    if (cssStyles) {\n      polygon.attr('style', cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n\n  if (nodeStyles) {\n    polygon.attr('style', nodeStyles);\n  }\n\n  node.width = w;\n  node.height = h;\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { insertPolygonShape } from './insertPolygonShape.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function lean_right<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0), node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0), node?.height ?? 0);\n  const points = [\n    { x: (-3 * h) / 6, y: 0 },\n    { x: w, y: 0 },\n    { x: w + (3 * h) / 6, y: -h },\n    { x: 0, y: -h },\n  ];\n\n  let polygon: typeof shapeSvg | ReturnType<typeof insertPolygonShape>;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createPathFromPoints(points);\n    const roughNode = rc.path(pathData, options);\n\n    polygon = shapeSvg\n      .insert(() => roughNode, ':first-child')\n      .attr('transform', `translate(${-w / 2}, ${h / 2})`);\n\n    if (cssStyles) {\n      polygon.attr('style', cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n\n  if (nodeStyles) {\n    polygon.attr('style', nodeStyles);\n  }\n\n  node.width = w;\n  node.height = h;\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import { log } from '../../../logger.js';\nimport { getNodeClasses, updateNodeBounds } from './util.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport intersect from '../intersect/index.js';\nimport { createPathFromPoints } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport function lightningBolt<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.label = '';\n  node.labelStyle = labelStyles;\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', getNodeClasses(node))\n    .attr('id', node.domId ?? node.id);\n  const { cssStyles } = node;\n  const width = Math.max(35, node?.width ?? 0);\n  const height = Math.max(35, node?.height ?? 0);\n  const gap = 7;\n\n  const points = [\n    { x: width, y: 0 },\n    { x: 0, y: height + gap / 2 },\n    { x: width - 2 * gap, y: height + gap / 2 },\n    { x: 0, y: 2 * height },\n    { x: width, y: height - gap / 2 },\n    { x: 2 * gap, y: height - gap / 2 },\n  ];\n\n  // @ts-expect-error shapeSvg d3 class is incorrect?\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const linePath = createPathFromPoints(points);\n  const lineNode = rc.path(linePath, options);\n\n  const lightningBolt = shapeSvg.insert(() => lineNode, ':first-child');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    lightningBolt.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    lightningBolt.selectAll('path').attr('style', nodeStyles);\n  }\n\n  lightningBolt.attr('transform', `translate(-${width / 2},${-height})`);\n\n  updateNodeBounds(node, lightningBolt);\n\n  node.intersect = function (point) {\n    log.info('lightningBolt intersect', node, point);\n    const pos = intersect.polygon(node, points, point);\n\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\nimport { handleUndefinedAttr } from '../../../utils.js';\n\nexport const createCylinderPathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number,\n  rx: number,\n  ry: number,\n  outerOffset: number\n): string => {\n  return [\n    `M${x},${y + ry}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n    `a${rx},${ry} 0,0,0 ${-width},0`,\n    `l0,${height}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n    `l0,${-height}`,\n    `M${x},${y + ry + outerOffset}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n  ].join(' ');\n};\nexport const createOuterCylinderPathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number,\n  rx: number,\n  ry: number,\n  outerOffset: number\n): string => {\n  return [\n    `M${x},${y + ry}`,\n    `M${x + width},${y + ry}`,\n    `a${rx},${ry} 0,0,0 ${-width},0`,\n    `l0,${height}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n    `l0,${-height}`,\n    `M${x},${y + ry + outerOffset}`,\n    `a${rx},${ry} 0,0,0 ${width},0`,\n  ].join(' ');\n};\nexport const createInnerCylinderPathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number,\n  rx: number,\n  ry: number\n): string => {\n  return [`M${x - width / 2},${-height / 2}`, `a${rx},${ry} 0,0,0 ${width},0`].join(' ');\n};\nexport async function linedCylinder<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0), node.width ?? 0);\n  const rx = w / 2;\n  const ry = rx / (2.5 + w / 50);\n  const h = Math.max(bbox.height + ry + (node.padding ?? 0), node.height ?? 0);\n  const outerOffset = h * 0.1; // 10% of height\n\n  let cylinder: typeof shapeSvg | D3Selection<SVGPathElement>;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const outerPathData = createOuterCylinderPathD(0, 0, w, h, rx, ry, outerOffset);\n    const innerPathData = createInnerCylinderPathD(0, ry, w, h, rx, ry);\n    const options = userNodeOverrides(node, {});\n\n    const outerNode = rc.path(outerPathData, options);\n    const innerLine = rc.path(innerPathData, options);\n\n    const innerLineEl = shapeSvg.insert(() => innerLine, ':first-child');\n    innerLineEl.attr('class', 'line');\n    cylinder = shapeSvg.insert(() => outerNode, ':first-child');\n    cylinder.attr('class', 'basic label-container');\n    if (cssStyles) {\n      cylinder.attr('style', cssStyles);\n    }\n  } else {\n    const pathData = createCylinderPathD(0, 0, w, h, rx, ry, outerOffset);\n    cylinder = shapeSvg\n      .insert('path', ':first-child')\n      .attr('d', pathData)\n      .attr('class', 'basic label-container')\n      .attr('style', handleUndefinedAttr(cssStyles))\n      .attr('style', nodeStyles);\n  }\n\n  // find label and move it down\n  cylinder.attr('label-offset-y', ry);\n  cylinder.attr('transform', `translate(${-w / 2}, ${-(h / 2 + ry)})`);\n\n  updateNodeBounds(node, cylinder);\n\n  label.attr(\n    'transform',\n    `translate(${-(bbox.width / 2) - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) + ry - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  node.intersect = function (point) {\n    const pos = intersect.rect(node, point);\n    const x = pos.x - (node.x ?? 0);\n\n    if (\n      rx != 0 &&\n      (Math.abs(x) < (node.width ?? 0) / 2 ||\n        (Math.abs(x) == (node.width ?? 0) / 2 &&\n          Math.abs(pos.y - (node.y ?? 0)) > (node.height ?? 0) / 2 - ry))\n    ) {\n      let y = ry * ry * (1 - (x * x) / (rx * rx));\n      if (y > 0) {\n        y = Math.sqrt(y);\n      }\n      y = ry - y;\n      if (point.y - (node.y ?? 0) > 0) {\n        y = -y;\n      }\n\n      pos.y += y;\n    }\n\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import {\n  labelHelper,\n  updateNodeBounds,\n  getNodeClasses,\n  generateFullSineWavePoints,\n} from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport rough from 'roughjs';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function linedWaveEdgedRect<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const waveAmplitude = h / 4;\n  const finalH = h + waveAmplitude;\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const points = [\n    { x: -w / 2 - (w / 2) * 0.1, y: -finalH / 2 },\n    { x: -w / 2 - (w / 2) * 0.1, y: finalH / 2 },\n    ...generateFullSineWavePoints(\n      -w / 2 - (w / 2) * 0.1,\n      finalH / 2,\n      w / 2 + (w / 2) * 0.1,\n      finalH / 2,\n      waveAmplitude,\n      0.8\n    ),\n    { x: w / 2 + (w / 2) * 0.1, y: -finalH / 2 },\n    { x: -w / 2 - (w / 2) * 0.1, y: -finalH / 2 },\n    { x: -w / 2, y: -finalH / 2 },\n    { x: -w / 2, y: (finalH / 2) * 1.1 },\n    { x: -w / 2, y: -finalH / 2 },\n  ];\n\n  const poly = rc.polygon(\n    points.map((p) => [p.x, p.y]),\n    options\n  );\n\n  const waveEdgeRect = shapeSvg.insert(() => poly, ':first-child');\n\n  waveEdgeRect.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    waveEdgeRect.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    waveEdgeRect.selectAll('path').attr('style', nodeStyles);\n  }\n\n  waveEdgeRect.attr('transform', `translate(0,${-waveAmplitude / 2})`);\n  label.attr(\n    'transform',\n    `translate(${-w / 2 + (node.padding ?? 0) + ((w / 2) * 0.1) / 2 - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) - waveAmplitude / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, waveEdgeRect);\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, getNodeClasses, updateNodeBounds, createPathFromPoints } from './util.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport intersect from '../intersect/index.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function multiRect<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const rectOffset = 5;\n  const x = -w / 2;\n  const y = -h / 2;\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  const outerPathPoints = [\n    { x: x - rectOffset, y: y + rectOffset },\n    { x: x - rectOffset, y: y + h + rectOffset },\n    { x: x + w - rectOffset, y: y + h + rectOffset },\n    { x: x + w - rectOffset, y: y + h },\n    { x: x + w, y: y + h },\n    { x: x + w, y: y + h - rectOffset },\n    { x: x + w + rectOffset, y: y + h - rectOffset },\n    { x: x + w + rectOffset, y: y - rectOffset },\n    { x: x + rectOffset, y: y - rectOffset },\n    { x: x + rectOffset, y: y },\n    { x, y },\n    { x, y: y + rectOffset },\n  ];\n\n  const innerPathPoints = [\n    { x, y: y + rectOffset },\n    { x: x + w - rectOffset, y: y + rectOffset },\n    { x: x + w - rectOffset, y: y + h },\n    { x: x + w, y: y + h },\n    { x: x + w, y },\n    { x, y },\n  ];\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const outerPath = createPathFromPoints(outerPathPoints);\n  const outerNode = rc.path(outerPath, options);\n  const innerPath = createPathFromPoints(innerPathPoints);\n  const innerNode = rc.path(innerPath, { ...options, fill: 'none' });\n\n  const multiRect = shapeSvg.insert(() => innerNode, ':first-child');\n  multiRect.insert(() => outerNode, ':first-child');\n\n  multiRect.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    multiRect.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    multiRect.selectAll('path').attr('style', nodeStyles);\n  }\n\n  label.attr(\n    'transform',\n    `translate(${-(bbox.width / 2) - rectOffset - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) + rectOffset - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, multiRect);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, outerPathPoints, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import {\n  labelHelper,\n  updateNodeBounds,\n  getNodeClasses,\n  createPathFromPoints,\n  generateFullSineWavePoints,\n} from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport rough from 'roughjs';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function multiWaveEdgedRectangle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const waveAmplitude = h / 4;\n  const finalH = h + waveAmplitude;\n  const x = -w / 2;\n  const y = -finalH / 2;\n  const rectOffset = 5;\n\n  const { cssStyles } = node;\n\n  const wavePoints = generateFullSineWavePoints(\n    x - rectOffset,\n    y + finalH + rectOffset,\n    x + w - rectOffset,\n    y + finalH + rectOffset,\n    waveAmplitude,\n    0.8\n  );\n\n  const lastWavePoint = wavePoints?.[wavePoints.length - 1];\n\n  const outerPathPoints = [\n    { x: x - rectOffset, y: y + rectOffset },\n    { x: x - rectOffset, y: y + finalH + rectOffset },\n    ...wavePoints,\n    { x: x + w - rectOffset, y: lastWavePoint.y - rectOffset },\n    { x: x + w, y: lastWavePoint.y - rectOffset },\n    { x: x + w, y: lastWavePoint.y - 2 * rectOffset },\n    { x: x + w + rectOffset, y: lastWavePoint.y - 2 * rectOffset },\n    { x: x + w + rectOffset, y: y - rectOffset },\n    { x: x + rectOffset, y: y - rectOffset },\n    { x: x + rectOffset, y: y },\n    { x, y },\n    { x, y: y + rectOffset },\n  ];\n\n  const innerPathPoints = [\n    { x, y: y + rectOffset },\n    { x: x + w - rectOffset, y: y + rectOffset },\n    { x: x + w - rectOffset, y: lastWavePoint.y - rectOffset },\n    { x: x + w, y: lastWavePoint.y - rectOffset },\n    { x: x + w, y },\n    { x, y },\n  ];\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const outerPath = createPathFromPoints(outerPathPoints);\n  const outerNode = rc.path(outerPath, options);\n  const innerPath = createPathFromPoints(innerPathPoints);\n  const innerNode = rc.path(innerPath, options);\n\n  const shape = shapeSvg.insert(() => outerNode, ':first-child');\n  shape.insert(() => innerNode);\n\n  shape.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    shape.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    shape.selectAll('path').attr('style', nodeStyles);\n  }\n\n  shape.attr('transform', `translate(0,${-waveAmplitude / 2})`);\n\n  label.attr(\n    'transform',\n    `translate(${-(bbox.width / 2) - rectOffset - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) + rectOffset - waveAmplitude / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, shape);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, outerPathPoints, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import rough from 'roughjs';\nimport type { Node, ShapeRenderOptions } from '../../types.js';\nimport intersect from '../intersect/index.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport { getNodeClasses, labelHelper, updateNodeBounds } from './util.js';\nimport type { D3Selection } from '../../../types.js';\nimport { getConfig } from '../../../config.js';\n\nexport async function note<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  { config: { themeVariables } }: ShapeRenderOptions\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const useHtmlLabels = node.useHtmlLabels || getConfig().flowchart?.htmlLabels !== false;\n  if (!useHtmlLabels) {\n    node.centerLabel = true;\n  }\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const totalWidth = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const totalHeight = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const x = -totalWidth / 2;\n  const y = -totalHeight / 2;\n  const { cssStyles } = node;\n\n  // add the rect\n  // @ts-ignore TODO: Fix rough typings\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {\n    fill: themeVariables.noteBkgColor,\n    stroke: themeVariables.noteBorderColor,\n  });\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const noteShapeNode = rc.rectangle(x, y, totalWidth, totalHeight, options);\n\n  const rect = shapeSvg.insert(() => noteShapeNode, ':first-child');\n  rect.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    rect.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    rect.selectAll('path').attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n}\n", "import { log } from '../../../logger.js';\nimport { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { insertPolygonShape } from './insertPolygonShape.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport const createDecisionBoxPathD = (x: number, y: number, size: number): string => {\n  return [\n    `M${x + size / 2},${y}`,\n    `L${x + size},${y - size / 2}`,\n    `L${x + size / 2},${y - size}`,\n    `L${x},${y - size / 2}`,\n    'Z',\n  ].join(' ');\n};\n\nexport async function question<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const s = w + h;\n\n  const points = [\n    { x: s / 2, y: 0 },\n    { x: s, y: -s / 2 },\n    { x: s / 2, y: -s },\n    { x: 0, y: -s / 2 },\n  ];\n\n  let polygon: typeof shapeSvg | ReturnType<typeof insertPolygonShape>;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createDecisionBoxPathD(0, 0, s);\n    const roughNode = rc.path(pathData, options);\n\n    polygon = shapeSvg\n      .insert(() => roughNode, ':first-child')\n      .attr('transform', `translate(${-s / 2}, ${s / 2})`);\n\n    if (cssStyles) {\n      polygon.attr('style', cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, s, s, points);\n  }\n\n  if (nodeStyles) {\n    polygon.attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    log.debug(\n      'APA12 Intersect called SPLIT\\npoint:',\n      point,\n      '\\nnode:\\n',\n      node,\n      '\\nres:',\n      intersect.polygon(node, points, point)\n    );\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function rect_left_inv_arrow<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const w = Math.max(bbox.width + (node.padding ?? 0), node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0), node?.height ?? 0);\n\n  const x = -w / 2;\n  const y = -h / 2;\n  const notch = y / 2;\n\n  const points = [\n    { x: x + notch, y },\n    { x: x, y: 0 },\n    { x: x + notch, y: -y },\n    { x: -x, y: -y },\n    { x: -x, y },\n  ];\n\n  const { cssStyles } = node;\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const pathData = createPathFromPoints(points);\n  const roughNode = rc.path(pathData, options);\n\n  const polygon = shapeSvg.insert(() => roughNode, ':first-child');\n\n  polygon.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    polygon.selectAll('path').attr('style', cssStyles);\n  }\n  if (nodeStyles && node.look !== 'handDrawn') {\n    polygon.selectAll('path').attr('style', nodeStyles);\n  }\n\n  polygon.attr('transform', `translate(${-notch / 2},0)`);\n\n  label.attr(\n    'transform',\n    `translate(${-notch / 2 - bbox.width / 2 - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) - (bbox.y - (bbox.top ?? 0))})`\n  );\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import type { Node } from '../../types.js';\nimport { select } from 'd3';\nimport { evaluate } from '../../../diagrams/common/common.js';\nimport { updateNodeBounds } from './util.js';\nimport createLabel from '../createLabel.js';\nimport intersect from '../intersect/index.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { getConfig } from '../../../diagram-api/diagramAPI.js';\nimport { createRoundedRectPathD } from './roundedRectPath.js';\nimport { log } from '../../../logger.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function rectWithTitle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  let classes;\n  if (!node.cssClasses) {\n    classes = 'node default';\n  } else {\n    classes = 'node ' + node.cssClasses;\n  }\n\n  // Add outer g element\n  const shapeSvg = parent\n    // @ts-ignore - d3 typings are not correct\n    .insert('g')\n    .attr('class', classes)\n    .attr('id', node.domId || node.id);\n\n  // Create the title label and insert it after the rect\n  const g = shapeSvg.insert('g');\n\n  const label = shapeSvg.insert('g').attr('class', 'label').attr('style', nodeStyles);\n\n  const description = node.description;\n\n  const title = node.label;\n\n  const text = label.node()!.appendChild(await createLabel(title, node.labelStyle, true, true));\n  let bbox = { width: 0, height: 0 };\n  if (evaluate(getConfig()?.flowchart?.htmlLabels)) {\n    const div = text.children[0];\n    const dv = select(text);\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n  log.info('Text 2', description);\n  const textRows = description || [];\n  const titleBox = text.getBBox();\n  const descr = label\n    .node()!\n    .appendChild(\n      await createLabel(\n        textRows.join ? textRows.join('<br/>') : textRows,\n        node.labelStyle,\n        true,\n        true\n      )\n    );\n\n  //if (evaluate(getConfig()?.flowchart?.htmlLabels)) {\n  const div = descr.children[0];\n  const dv = select(descr);\n  bbox = div.getBoundingClientRect();\n  dv.attr('width', bbox.width);\n  dv.attr('height', bbox.height);\n  // }\n\n  const halfPadding = (node.padding || 0) / 2;\n  select(descr).attr(\n    'transform',\n    'translate( ' +\n      (bbox.width > titleBox.width ? 0 : (titleBox.width - bbox.width) / 2) +\n      ', ' +\n      (titleBox.height + halfPadding + 5) +\n      ')'\n  );\n  select(text).attr(\n    'transform',\n    'translate( ' +\n      (bbox.width < titleBox.width ? 0 : -(titleBox.width - bbox.width) / 2) +\n      ', ' +\n      0 +\n      ')'\n  );\n  // Get the size of the label\n\n  // Bounding box for title and text\n  bbox = label.node()!.getBBox();\n\n  // Center the label\n  label.attr(\n    'transform',\n    'translate(' + -bbox.width / 2 + ', ' + (-bbox.height / 2 - halfPadding + 3) + ')'\n  );\n\n  const totalWidth = bbox.width + (node.padding || 0);\n  const totalHeight = bbox.height + (node.padding || 0);\n  const x = -bbox.width / 2 - halfPadding;\n  const y = -bbox.height / 2 - halfPadding;\n  let rect;\n  let innerLine;\n  if (node.look === 'handDrawn') {\n    // @ts-ignore No typings for rough\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const roughNode = rc.path(\n      createRoundedRectPathD(x, y, totalWidth, totalHeight, node.rx || 0),\n      options\n    );\n\n    const roughLine = rc.line(\n      -bbox.width / 2 - halfPadding,\n      -bbox.height / 2 - halfPadding + titleBox.height + halfPadding,\n      bbox.width / 2 + halfPadding,\n      -bbox.height / 2 - halfPadding + titleBox.height + halfPadding,\n      options\n    );\n\n    innerLine = shapeSvg.insert(() => {\n      log.debug('Rough node insert CXC', roughNode);\n      return roughLine;\n    }, ':first-child');\n    rect = shapeSvg.insert(() => {\n      log.debug('Rough node insert CXC', roughNode);\n      return roughNode;\n    }, ':first-child');\n  } else {\n    rect = g.insert('rect', ':first-child');\n    innerLine = g.insert('line');\n    rect\n      .attr('class', 'outer title-state')\n      .attr('style', nodeStyles)\n      .attr('x', -bbox.width / 2 - halfPadding)\n      .attr('y', -bbox.height / 2 - halfPadding)\n      .attr('width', bbox.width + (node.padding || 0))\n      .attr('height', bbox.height + (node.padding || 0));\n\n    innerLine\n      .attr('class', 'divider')\n      .attr('x1', -bbox.width / 2 - halfPadding)\n      .attr('x2', bbox.width / 2 + halfPadding)\n      .attr('y1', -bbox.height / 2 - halfPadding + titleBox.height + halfPadding)\n      .attr('y2', -bbox.height / 2 - halfPadding + titleBox.height + halfPadding);\n  }\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n}\n", "import type { Node, RectOptions } from '../../types.js';\nimport type { D3Selection } from '../../../types.js';\nimport { drawRect } from './drawRect.js';\n\nexport async function roundedRect<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const options = {\n    rx: 5,\n    ry: 5,\n    classes: '',\n    labelPaddingX: (node?.padding || 0) * 1,\n    labelPaddingY: (node?.padding || 0) * 1,\n  } as RectOptions;\n\n  return drawRect(parent, node, options);\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\nimport { handleUndefinedAttr } from '../../../utils.js';\n\nexport async function shadedProcess<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const halfPadding = node?.padding ?? 0;\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const x = -bbox.width / 2 - halfPadding;\n  const y = -bbox.height / 2 - halfPadding;\n\n  const { cssStyles } = node;\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const points = [\n    { x, y },\n    { x: x + w + 8, y },\n    { x: x + w + 8, y: y + h },\n    { x: x - 8, y: y + h },\n    { x: x - 8, y: y },\n    { x, y },\n    { x, y: y + h },\n  ];\n\n  const roughNode = rc.polygon(\n    points.map((p) => [p.x, p.y]),\n    options\n  );\n\n  const rect = shapeSvg.insert(() => roughNode, ':first-child');\n\n  rect.attr('class', 'basic label-container').attr('style', handleUndefinedAttr(cssStyles));\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    rect.selectAll('path').attr('style', nodeStyles);\n  }\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    rect.selectAll('path').attr('style', nodeStyles);\n  }\n\n  label.attr(\n    'transform',\n    `translate(${-w / 2 + 4 + (node.padding ?? 0) - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function slopedRect<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const x = -w / 2;\n  const y = -h / 2;\n\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const points = [\n    { x, y },\n    { x, y: y + h },\n    { x: x + w, y: y + h },\n    { x: x + w, y: y - h / 2 },\n  ];\n\n  const pathData = createPathFromPoints(points);\n  const shapeNode = rc.path(pathData, options);\n\n  const polygon = shapeSvg.insert(() => shapeNode, ':first-child');\n  polygon.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', nodeStyles);\n  }\n\n  polygon.attr('transform', `translate(0, ${h / 4})`);\n  label.attr(\n    'transform',\n    `translate(${-w / 2 + (node.padding ?? 0) - (bbox.x - (bbox.left ?? 0))}, ${-h / 4 + (node.padding ?? 0) - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import type { Node, RectOptions } from '../../types.js';\nimport type { D3Selection } from '../../../types.js';\nimport { drawRect } from './drawRect.js';\n\nexport async function squareRect<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const options = {\n    rx: 0,\n    ry: 0,\n    classes: '',\n    labelPaddingX: (node?.padding || 0) * 2,\n    labelPaddingY: (node?.padding || 0) * 1,\n  } as RectOptions;\n  return drawRect(parent, node, options);\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { createRoundedRectPathD } from './roundedRectPath.js';\nimport type { D3Selection } from '../../../types.js';\nimport { handleUndefinedAttr } from '../../../utils.js';\n\nexport const createStadiumPathD = (\n  x: number,\n  y: number,\n  totalWidth: number,\n  totalHeight: number\n) => {\n  const radius = totalHeight / 2;\n  return [\n    'M',\n    x + radius,\n    y, // Move to the start of the top-left arc\n    'H',\n    x + totalWidth - radius, // Draw horizontal line to the start of the top-right arc\n    'A',\n    radius,\n    radius,\n    0,\n    0,\n    1,\n    x + totalWidth,\n    y + radius, // Draw top-right arc\n    'H',\n    x, // Draw horizontal line to the start of the bottom-right arc\n    'A',\n    radius,\n    radius,\n    0,\n    0,\n    1,\n    x + totalWidth - radius,\n    y + totalHeight, // Draw bottom-right arc\n    'H',\n    x + radius, // Draw horizontal line to the start of the bottom-left arc\n    'A',\n    radius,\n    radius,\n    0,\n    0,\n    1,\n    x,\n    y + radius, // Draw bottom-left arc\n    'Z', // Close the path\n  ].join(' ');\n};\n\nexport async function stadium<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const h = bbox.height + node.padding;\n  const w = bbox.width + h / 4 + node.padding;\n\n  let rect;\n  const { cssStyles } = node;\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n\n    const pathData = createRoundedRectPathD(-w / 2, -h / 2, w, h, h / 2);\n    const roughNode = rc.path(pathData, options);\n\n    rect = shapeSvg.insert(() => roughNode, ':first-child');\n    rect.attr('class', 'basic label-container').attr('style', handleUndefinedAttr(cssStyles));\n  } else {\n    rect = shapeSvg.insert('rect', ':first-child');\n\n    rect\n      .attr('class', 'basic label-container')\n      .attr('style', nodeStyles)\n      .attr('rx', h / 2)\n      .attr('ry', h / 2)\n      .attr('x', -w / 2)\n      .attr('y', -h / 2)\n      .attr('width', w)\n      .attr('height', h);\n  }\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n}\n", "import type { Node, RectOptions } from '../../types.js';\nimport type { D3Selection } from '../../../types.js';\nimport { drawRect } from './drawRect.js';\n\nexport async function state<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const options = {\n    rx: 5,\n    ry: 5,\n    classes: 'flowchart-node',\n  } as RectOptions;\n  return drawRect(parent, node, options);\n}\n", "import rough from 'roughjs';\nimport type { Node, ShapeRenderOptions } from '../../types.js';\nimport intersect from '../intersect/index.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport { updateNodeBounds } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport function stateEnd<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  { config: { themeVariables } }: ShapeRenderOptions\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { cssStyles } = node;\n  const { lineColor, stateBorder, nodeBorder } = themeVariables;\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', 'node default')\n    .attr('id', node.domId || node.id);\n\n  // @ts-ignore TODO: Fix rough typings\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const roughNode = rc.circle(0, 0, 14, {\n    ...options,\n    stroke: lineColor,\n    strokeWidth: 2,\n  });\n  const innerFill = stateBorder ?? nodeBorder;\n  const roughInnerNode = rc.circle(0, 0, 5, {\n    ...options,\n    fill: innerFill,\n    stroke: innerFill,\n    strokeWidth: 2,\n    fillStyle: 'solid',\n  });\n  const circle = shapeSvg.insert(() => roughNode, ':first-child');\n  circle.insert(() => roughInnerNode);\n\n  if (cssStyles) {\n    circle.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles) {\n    circle.selectAll('path').attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, circle);\n\n  node.intersect = function (point) {\n    return intersect.circle(node, 7, point);\n  };\n\n  return shapeSvg;\n}\n", "import rough from 'roughjs';\nimport type { Node, ShapeRenderOptions } from '../../types.js';\nimport intersect from '../intersect/index.js';\nimport { solidStateFill } from './handDrawnShapeStyles.js';\nimport { updateNodeBounds } from './util.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport function stateStart<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  { config: { themeVariables } }: ShapeRenderOptions\n) {\n  const { lineColor } = themeVariables;\n\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', 'node default')\n    .attr('id', node.domId || node.id);\n\n  let circle: D3Selection<SVGCircleElement> | D3Selection<SVGGElement>;\n  if (node.look === 'handDrawn') {\n    // @ts-ignore TODO: Fix rough typings\n    const rc = rough.svg(shapeSvg);\n    const roughNode = rc.circle(0, 0, 14, solidStateFill(lineColor));\n    circle = shapeSvg.insert(() => roughNode);\n    // center the circle around its coordinate\n    circle.attr('class', 'state-start').attr('r', 7).attr('width', 14).attr('height', 14);\n  } else {\n    circle = shapeSvg.insert('circle', ':first-child');\n    // center the circle around its coordinate\n    circle.attr('class', 'state-start').attr('r', 7).attr('width', 14).attr('height', 14);\n  }\n\n  updateNodeBounds(node, circle);\n\n  node.intersect = function (point) {\n    return intersect.circle(node, 7, point);\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { insertPolygonShape } from './insertPolygonShape.js';\nimport type { D3Selection } from '../../../types.js';\nimport { handleUndefinedAttr } from '../../../utils.js';\n\nexport const createSubroutinePathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number\n): string => {\n  const offset = 8;\n  return [\n    `M${x - offset},${y}`,\n    `H${x + width + offset}`,\n    `V${y + height}`,\n    `H${x - offset}`,\n    `V${y}`,\n    'M',\n    x,\n    y,\n    'H',\n    x + width,\n    'V',\n    y + height,\n    'H',\n    x,\n    'Z',\n  ].join(' ');\n};\n\nexport async function subroutine<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const halfPadding = (node?.padding || 0) / 2;\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const x = -bbox.width / 2 - halfPadding;\n  const y = -bbox.height / 2 - halfPadding;\n\n  const points = [\n    { x: 0, y: 0 },\n    { x: w, y: 0 },\n    { x: w, y: -h },\n    { x: 0, y: -h },\n    { x: 0, y: 0 },\n    { x: -8, y: 0 },\n    { x: w + 8, y: 0 },\n    { x: w + 8, y: -h },\n    { x: -8, y: -h },\n    { x: -8, y: 0 },\n  ];\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n\n    const roughNode = rc.rectangle(x - 8, y, w + 16, h, options);\n    const l1 = rc.line(x, y, x, y + h, options);\n    const l2 = rc.line(x + w, y, x + w, y + h, options);\n\n    shapeSvg.insert(() => l1, ':first-child');\n    shapeSvg.insert(() => l2, ':first-child');\n    const rect = shapeSvg.insert(() => roughNode, ':first-child');\n    const { cssStyles } = node;\n    rect.attr('class', 'basic label-container').attr('style', handleUndefinedAttr(cssStyles));\n    updateNodeBounds(node, rect);\n  } else {\n    const el = insertPolygonShape(shapeSvg, w, h, points);\n    if (nodeStyles) {\n      el.attr('style', nodeStyles);\n    }\n    updateNodeBounds(node, el);\n  }\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, getNodeClasses, updateNodeBounds, createPathFromPoints } from './util.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport intersect from '../intersect/index.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function taggedRect<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const x = -w / 2;\n  const y = -h / 2;\n  const tagWidth = 0.2 * h;\n  const tagHeight = 0.2 * h;\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  const rectPoints = [\n    { x: x - tagWidth / 2, y },\n    { x: x + w + tagWidth / 2, y },\n    { x: x + w + tagWidth / 2, y: y + h },\n    { x: x - tagWidth / 2, y: y + h },\n  ];\n\n  const tagPoints = [\n    { x: x + w - tagWidth / 2, y: y + h },\n    { x: x + w + tagWidth / 2, y: y + h },\n    { x: x + w + tagWidth / 2, y: y + h - tagHeight },\n  ];\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const rectPath = createPathFromPoints(rectPoints);\n  const rectNode = rc.path(rectPath, options);\n\n  const tagPath = createPathFromPoints(tagPoints);\n  const tagNode = rc.path(tagPath, { ...options, fillStyle: 'solid' });\n\n  const taggedRect = shapeSvg.insert(() => tagNode, ':first-child');\n  taggedRect.insert(() => rectNode, ':first-child');\n\n  taggedRect.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    taggedRect.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    taggedRect.selectAll('path').attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, taggedRect);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, rectPoints, point);\n\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import {\n  labelHelper,\n  updateNodeBounds,\n  getNodeClasses,\n  generateFullSineWavePoints,\n  createPathFromPoints,\n} from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport rough from 'roughjs';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function taggedWaveEdgedRectangle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const waveAmplitude = h / 4;\n  const tagWidth = 0.2 * w;\n  const tagHeight = 0.2 * h;\n  const finalH = h + waveAmplitude;\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const points = [\n    { x: -w / 2 - (w / 2) * 0.1, y: finalH / 2 },\n    ...generateFullSineWavePoints(\n      -w / 2 - (w / 2) * 0.1,\n      finalH / 2,\n      w / 2 + (w / 2) * 0.1,\n      finalH / 2,\n      waveAmplitude,\n      0.8\n    ),\n\n    { x: w / 2 + (w / 2) * 0.1, y: -finalH / 2 },\n    { x: -w / 2 - (w / 2) * 0.1, y: -finalH / 2 },\n  ];\n\n  const x = -w / 2 + (w / 2) * 0.1;\n  const y = -finalH / 2 - tagHeight * 0.4;\n\n  const tagPoints = [\n    { x: x + w - tagWidth, y: (y + h) * 1.4 },\n    { x: x + w, y: y + h - tagHeight },\n    { x: x + w, y: (y + h) * 0.9 },\n    ...generateFullSineWavePoints(\n      x + w,\n      (y + h) * 1.3,\n      x + w - tagWidth,\n      (y + h) * 1.5,\n      -h * 0.03,\n      0.5\n    ),\n  ];\n\n  const waveEdgeRectPath = createPathFromPoints(points);\n  const waveEdgeRectNode = rc.path(waveEdgeRectPath, options);\n\n  const taggedWaveEdgeRectPath = createPathFromPoints(tagPoints);\n  const taggedWaveEdgeRectNode = rc.path(taggedWaveEdgeRectPath, {\n    ...options,\n    fillStyle: 'solid',\n  });\n\n  const waveEdgeRect = shapeSvg.insert(() => taggedWaveEdgeRectNode, ':first-child');\n  waveEdgeRect.insert(() => waveEdgeRectNode, ':first-child');\n\n  waveEdgeRect.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    waveEdgeRect.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    waveEdgeRect.selectAll('path').attr('style', nodeStyles);\n  }\n\n  waveEdgeRect.attr('transform', `translate(0,${-waveAmplitude / 2})`);\n  label.attr(\n    'transform',\n    `translate(${-w / 2 + (node.padding ?? 0) - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) - waveAmplitude / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, waveEdgeRect);\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String } from './handDrawnShapeStyles.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function text<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const totalWidth = Math.max(bbox.width + node.padding, node?.width || 0);\n  const totalHeight = Math.max(bbox.height + node.padding, node?.height || 0);\n  const x = -totalWidth / 2;\n  const y = -totalHeight / 2;\n\n  const rect = shapeSvg.insert('rect', ':first-child');\n\n  rect\n    .attr('class', 'text')\n    .attr('style', nodeStyles)\n    .attr('rx', 0)\n    .attr('ry', 0)\n    .attr('x', x)\n    .attr('y', y)\n    .attr('width', totalWidth)\n    .attr('height', totalHeight);\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, getNodeClasses, updateNodeBounds } from './util.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport intersect from '../intersect/index.js';\nimport type { D3Selection } from '../../../types.js';\nimport { handleUndefinedAttr } from '../../../utils.js';\n\nexport const createCylinderPathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number,\n  rx: number,\n  ry: number\n): string => {\n  return `M${x},${y}\n    a${rx},${ry} 0,0,1 ${0},${-height}\n    l${width},${0}\n    a${rx},${ry} 0,0,1 ${0},${height}\n    M${width},${-height}\n    a${rx},${ry} 0,0,0 ${0},${height}\n    l${-width},${0}`;\n};\n\nexport const createOuterCylinderPathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number,\n  rx: number,\n  ry: number\n): string => {\n  return [\n    `M${x},${y}`,\n    `M${x + width},${y}`,\n    `a${rx},${ry} 0,0,0 ${0},${-height}`,\n    `l${-width},0`,\n    `a${rx},${ry} 0,0,0 ${0},${height}`,\n    `l${width},0`,\n  ].join(' ');\n};\nexport const createInnerCylinderPathD = (\n  x: number,\n  y: number,\n  width: number,\n  height: number,\n  rx: number,\n  ry: number\n): string => {\n  return [`M${x + width / 2},${-height / 2}`, `a${rx},${ry} 0,0,0 0,${height}`].join(' ');\n};\n\nexport async function tiltedCylinder<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label, halfPadding } = await labelHelper(\n    parent,\n    node,\n    getNodeClasses(node)\n  );\n  const labelPadding = node.look === 'neo' ? halfPadding * 2 : halfPadding;\n  const h = bbox.height + labelPadding;\n  const ry = h / 2;\n  const rx = ry / (2.5 + h / 50);\n  const w = bbox.width + rx + labelPadding;\n  const { cssStyles } = node;\n\n  let cylinder: D3Selection<SVGGElement> | D3Selection<SVGPathElement>;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const outerPathData = createOuterCylinderPathD(0, 0, w, h, rx, ry);\n    const innerPathData = createInnerCylinderPathD(0, 0, w, h, rx, ry);\n    const outerNode = rc.path(outerPathData, userNodeOverrides(node, {}));\n    const innerLine = rc.path(innerPathData, userNodeOverrides(node, { fill: 'none' }));\n    cylinder = shapeSvg.insert(() => innerLine, ':first-child');\n    cylinder = shapeSvg.insert(() => outerNode, ':first-child');\n    cylinder.attr('class', 'basic label-container');\n    if (cssStyles) {\n      cylinder.attr('style', cssStyles);\n    }\n  } else {\n    const pathData = createCylinderPathD(0, 0, w, h, rx, ry);\n    cylinder = shapeSvg\n      .insert('path', ':first-child')\n      .attr('d', pathData)\n      .attr('class', 'basic label-container')\n      .attr('style', handleUndefinedAttr(cssStyles))\n      .attr('style', nodeStyles);\n    cylinder.attr('class', 'basic label-container');\n\n    if (cssStyles) {\n      cylinder.selectAll('path').attr('style', cssStyles);\n    }\n\n    if (nodeStyles) {\n      cylinder.selectAll('path').attr('style', nodeStyles);\n    }\n  }\n\n  cylinder.attr('label-offset-x', rx);\n  cylinder.attr('transform', `translate(${-w / 2}, ${h / 2} )`);\n\n  label.attr(\n    'transform',\n    `translate(${-(bbox.width / 2) - rx - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, cylinder);\n\n  node.intersect = function (point) {\n    const pos = intersect.rect(node, point);\n    const y = pos.y - (node.y ?? 0);\n\n    if (\n      ry != 0 &&\n      (Math.abs(y) < (node.height ?? 0) / 2 ||\n        (Math.abs(y) == (node.height ?? 0) / 2 &&\n          Math.abs(pos.x - (node.x ?? 0)) > (node.width ?? 0) / 2 - rx))\n    ) {\n      let x = rx * rx * (1 - (y * y) / (ry * ry));\n      if (x != 0) {\n        x = Math.sqrt(Math.abs(x));\n      }\n      x = rx - x;\n      if (point.x - (node.x ?? 0) > 0) {\n        x = -x;\n      }\n\n      pos.x += x;\n    }\n\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { insertPolygonShape } from './insertPolygonShape.js';\nimport type { D3Selection } from '../../../types.js';\n\n// export const createTrapezoidPathD = (\n//   x: number,\n//   y: number,\n//   width: number,\n//   height: number\n// ): string => {\n//   return [\n//     `M${x - (2 * height) / 6},${y}`,\n//     `L${x + width + (2 * height) / 6},${y}`,\n//     `L${x + width - height / 6},${y - height}`,\n//     `L${x + height / 6},${y - height}`,\n//     'Z',\n//   ].join(' ');\n// };\n\nexport async function trapezoid<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const w = bbox.width + node.padding;\n  const h = bbox.height + node.padding;\n  const points = [\n    { x: (-3 * h) / 6, y: 0 },\n    { x: w + (3 * h) / 6, y: 0 },\n    { x: w, y: -h },\n    { x: 0, y: -h },\n  ];\n\n  let polygon: typeof shapeSvg | ReturnType<typeof insertPolygonShape>;\n  const { cssStyles } = node;\n\n  if (node.look === 'handDrawn') {\n    // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(node, {});\n    const pathData = createPathFromPoints(points);\n    const roughNode = rc.path(pathData, options);\n\n    polygon = shapeSvg\n      .insert(() => roughNode, ':first-child')\n      .attr('transform', `translate(${-w / 2}, ${h / 2})`);\n\n    if (cssStyles) {\n      polygon.attr('style', cssStyles);\n    }\n  } else {\n    polygon = insertPolygonShape(shapeSvg, w, h, points);\n  }\n\n  if (nodeStyles) {\n    polygon.attr('style', nodeStyles);\n  }\n\n  node.width = w;\n  node.height = h;\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, updateNodeBounds, getNodeClasses, createPathFromPoints } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function trapezoidalPentagon<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n  const minWidth = 60,\n    minHeight = 20;\n  const w = Math.max(minWidth, bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(minHeight, bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n\n  const { cssStyles } = node;\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const points = [\n    { x: (-w / 2) * 0.8, y: -h / 2 },\n    { x: (w / 2) * 0.8, y: -h / 2 },\n    { x: w / 2, y: (-h / 2) * 0.6 },\n    { x: w / 2, y: h / 2 },\n    { x: -w / 2, y: h / 2 },\n    { x: -w / 2, y: (-h / 2) * 0.6 },\n  ];\n\n  const pathData = createPathFromPoints(points);\n  const shapeNode = rc.path(pathData, options);\n\n  const polygon = shapeSvg.insert(() => shapeNode, ':first-child');\n  polygon.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, polygon);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { log } from '../../../logger.js';\nimport { labelHelper, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { createPathFromPoints } from './util.js';\nimport { evaluate } from '../../../diagrams/common/common.js';\nimport { getConfig } from '../../../diagram-api/diagramAPI.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function triangle<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const useHtmlLabels = evaluate(getConfig().flowchart?.htmlLabels);\n\n  const w = bbox.width + (node.padding ?? 0);\n  const h = w + bbox.height;\n\n  const tw = w + bbox.height;\n  const points = [\n    { x: 0, y: 0 },\n    { x: tw, y: 0 },\n    { x: tw / 2, y: -h },\n  ];\n\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n  const pathData = createPathFromPoints(points);\n  const roughNode = rc.path(pathData, options);\n\n  const polygon = shapeSvg\n    .insert(() => roughNode, ':first-child')\n    .attr('transform', `translate(${-h / 2}, ${h / 2})`);\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    polygon.selectChildren('path').attr('style', nodeStyles);\n  }\n\n  node.width = w;\n  node.height = h;\n\n  updateNodeBounds(node, polygon);\n\n  label.attr(\n    'transform',\n    `translate(${-bbox.width / 2 - (bbox.x - (bbox.left ?? 0))}, ${h / 2 - (bbox.height + (node.padding ?? 0) / (useHtmlLabels ? 2 : 1) - (bbox.y - (bbox.top ?? 0)))})`\n  );\n\n  node.intersect = function (point) {\n    log.info('Triangle intersect', node, points, point);\n    return intersect.polygon(node, points, point);\n  };\n\n  return shapeSvg;\n}\n", "import {\n  labelHelper,\n  updateNodeBounds,\n  getNodeClasses,\n  generateFullSineWavePoints,\n  createPathFromPoints,\n} from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport rough from 'roughjs';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function waveEdgedRectangle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const waveAmplitude = h / 8;\n  const finalH = h + waveAmplitude;\n  const { cssStyles } = node;\n\n  // To maintain minimum width\n  const minWidth = 70;\n  const widthDif = minWidth - w;\n  const extraW = widthDif > 0 ? widthDif / 2 : 0;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const points = [\n    { x: -w / 2 - extraW, y: finalH / 2 },\n    ...generateFullSineWavePoints(\n      -w / 2 - extraW,\n      finalH / 2,\n      w / 2 + extraW,\n      finalH / 2,\n      waveAmplitude,\n      0.8\n    ),\n    { x: w / 2 + extraW, y: -finalH / 2 },\n    { x: -w / 2 - extraW, y: -finalH / 2 },\n  ];\n\n  const waveEdgeRectPath = createPathFromPoints(points);\n  const waveEdgeRectNode = rc.path(waveEdgeRectPath, options);\n\n  const waveEdgeRect = shapeSvg.insert(() => waveEdgeRectNode, ':first-child');\n\n  waveEdgeRect.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    waveEdgeRect.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    waveEdgeRect.selectAll('path').attr('style', nodeStyles);\n  }\n\n  waveEdgeRect.attr('transform', `translate(0,${-waveAmplitude / 2})`);\n  label.attr(\n    'transform',\n    `translate(${-w / 2 + (node.padding ?? 0) - (bbox.x - (bbox.left ?? 0))},${-h / 2 + (node.padding ?? 0) - waveAmplitude - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, waveEdgeRect);\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import {\n  labelHelper,\n  updateNodeBounds,\n  getNodeClasses,\n  createPathFromPoints,\n  generateFullSineWavePoints,\n} from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function waveRectangle<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox } = await labelHelper(parent, node, getNodeClasses(node));\n\n  const minWidth = 100; // Minimum width\n  const minHeight = 50; // Minimum height\n\n  const baseWidth = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const baseHeight = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n\n  const aspectRatio = baseWidth / baseHeight;\n\n  let w = baseWidth;\n  let h = baseHeight;\n\n  if (w > h * aspectRatio) {\n    h = w / aspectRatio;\n  } else {\n    w = h * aspectRatio;\n  }\n\n  w = Math.max(w, minWidth);\n  h = Math.max(h, minHeight);\n\n  const waveAmplitude = Math.min(h * 0.2, h / 4);\n  const finalH = h + waveAmplitude * 2;\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const points = [\n    { x: -w / 2, y: finalH / 2 },\n    ...generateFullSineWavePoints(-w / 2, finalH / 2, w / 2, finalH / 2, waveAmplitude, 1),\n    { x: w / 2, y: -finalH / 2 },\n    ...generateFullSineWavePoints(w / 2, -finalH / 2, -w / 2, -finalH / 2, waveAmplitude, -1),\n  ];\n\n  const waveRectPath = createPathFromPoints(points);\n  const waveRectNode = rc.path(waveRectPath, options);\n\n  const waveRect = shapeSvg.insert(() => waveRectNode, ':first-child');\n\n  waveRect.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    waveRect.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    waveRect.selectAll('path').attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, waveRect);\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, points, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { labelHelper, getNodeClasses, updateNodeBounds } from './util.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport intersect from '../intersect/index.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function windowPane<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const { shapeSvg, bbox, label } = await labelHelper(parent, node, getNodeClasses(node));\n  const w = Math.max(bbox.width + (node.padding ?? 0) * 2, node?.width ?? 0);\n  const h = Math.max(bbox.height + (node.padding ?? 0) * 2, node?.height ?? 0);\n  const rectOffset = 5;\n  const x = -w / 2;\n  const y = -h / 2;\n  const { cssStyles } = node;\n\n  // @ts-expect-error -- Passing a D3.Selection seems to work for some reason\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  const outerPathPoints = [\n    { x: x - rectOffset, y: y - rectOffset },\n    { x: x - rectOffset, y: y + h },\n    { x: x + w, y: y + h },\n    { x: x + w, y: y - rectOffset },\n  ];\n\n  const path = `M${x - rectOffset},${y - rectOffset} L${x + w},${y - rectOffset} L${x + w},${y + h} L${x - rectOffset},${y + h} L${x - rectOffset},${y - rectOffset}\n                M${x - rectOffset},${y} L${x + w},${y}\n                M${x},${y - rectOffset} L${x},${y + h}`;\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const no = rc.path(path, options);\n\n  const windowPane = shapeSvg.insert(() => no, ':first-child');\n  windowPane.attr('transform', `translate(${rectOffset / 2}, ${rectOffset / 2})`);\n\n  windowPane.attr('class', 'basic label-container');\n\n  if (cssStyles && node.look !== 'handDrawn') {\n    windowPane.selectAll('path').attr('style', cssStyles);\n  }\n\n  if (nodeStyles && node.look !== 'handDrawn') {\n    windowPane.selectAll('path').attr('style', nodeStyles);\n  }\n\n  label.attr(\n    'transform',\n    `translate(${-(bbox.width / 2) + rectOffset / 2 - (bbox.x - (bbox.left ?? 0))}, ${-(bbox.height / 2) + rectOffset / 2 - (bbox.y - (bbox.top ?? 0))})`\n  );\n\n  updateNodeBounds(node, windowPane);\n\n  node.intersect = function (point) {\n    const pos = intersect.polygon(node, outerPathPoints, point);\n    return pos;\n  };\n\n  return shapeSvg;\n}\n", "import { updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { userNodeOverrides, styles2String } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport { drawRect } from './drawRect.js';\nimport { getConfig } from '../../../config.js';\nimport type { EntityNode } from '../../../diagrams/er/erTypes.js';\nimport { createText } from '../../createText.js';\nimport { evaluate, parseGenericTypes } from '../../../diagrams/common/common.js';\nimport { select } from 'd3';\nimport { calculateTextWidth } from '../../../utils.js';\nimport type { MermaidConfig } from '../../../config.type.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function erBox<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  // Treat node as entityNode for certain entityNode checks\n  const entityNode = node as unknown as EntityNode;\n  if (entityNode.alias) {\n    node.label = entityNode.alias;\n  }\n\n  // Background shapes are drawn to fill in the background color and cover up the ER diagram edge markers.\n  // Draw background shape once.\n  if (node.look === 'handDrawn') {\n    const { themeVariables } = getConfig();\n    const { background } = themeVariables;\n    const backgroundNode = {\n      ...node,\n      id: node.id + '-background',\n      look: 'default',\n      cssStyles: ['stroke: none', `fill: ${background}`],\n    };\n    await erBox(parent, backgroundNode);\n  }\n\n  const config = getConfig();\n  node.useHtmlLabels = config.htmlLabels;\n  let PADDING = config.er?.diagramPadding ?? 10;\n  let TEXT_PADDING = config.er?.entityPadding ?? 6;\n\n  const { cssStyles } = node;\n  const { labelStyles } = styles2String(node);\n\n  // Draw rect if no attributes are found\n  if (entityNode.attributes.length === 0 && node.label) {\n    const options = {\n      rx: 0,\n      ry: 0,\n      labelPaddingX: PADDING,\n      labelPaddingY: PADDING * 1.5,\n      classes: '',\n    };\n    // Set minimum width\n    if (\n      calculateTextWidth(node.label, config) + options.labelPaddingX * 2 <\n      config.er!.minEntityWidth!\n    ) {\n      node.width = config.er!.minEntityWidth;\n    }\n    const shapeSvg = await drawRect(parent, node, options);\n\n    // drawRect doesn't center non-htmlLabels correctly as of now, so translate label\n    if (!evaluate(config.htmlLabels)) {\n      const textElement = shapeSvg.select('text');\n      const bbox = (textElement.node() as SVGTextElement)?.getBBox();\n      textElement.attr('transform', `translate(${-bbox.width / 2}, 0)`);\n    }\n    return shapeSvg;\n  }\n\n  if (!config.htmlLabels) {\n    PADDING *= 1.25;\n    TEXT_PADDING *= 1.25;\n  }\n\n  let cssClasses = getNodeClasses(node);\n  if (!cssClasses) {\n    cssClasses = 'node default';\n  }\n\n  const shapeSvg = parent\n    // @ts-ignore Ignore .insert on SVGAElement\n    .insert('g')\n    .attr('class', cssClasses)\n    .attr('id', node.domId || node.id);\n\n  const nameBBox = await addText(shapeSvg, node.label ?? '', config, 0, 0, ['name'], labelStyles);\n  nameBBox.height += TEXT_PADDING;\n  let yOffset = 0;\n  const yOffsets = [];\n  let maxTypeWidth = 0;\n  let maxNameWidth = 0;\n  let maxKeysWidth = 0;\n  let maxCommentWidth = 0;\n  let keysPresent = true;\n  let commentPresent = true;\n  for (const attribute of entityNode.attributes) {\n    const typeBBox = await addText(\n      shapeSvg,\n      attribute.type,\n      config,\n      0,\n      yOffset,\n      ['attribute-type'],\n      labelStyles\n    );\n    maxTypeWidth = Math.max(maxTypeWidth, typeBBox.width + PADDING);\n    const nameBBox = await addText(\n      shapeSvg,\n      attribute.name,\n      config,\n      0,\n      yOffset,\n      ['attribute-name'],\n      labelStyles\n    );\n    maxNameWidth = Math.max(maxNameWidth, nameBBox.width + PADDING);\n    const keysBBox = await addText(\n      shapeSvg,\n      attribute.keys.join(),\n      config,\n      0,\n      yOffset,\n      ['attribute-keys'],\n      labelStyles\n    );\n    maxKeysWidth = Math.max(maxKeysWidth, keysBBox.width + PADDING);\n    const commentBBox = await addText(\n      shapeSvg,\n      attribute.comment,\n      config,\n      0,\n      yOffset,\n      ['attribute-comment'],\n      labelStyles\n    );\n    maxCommentWidth = Math.max(maxCommentWidth, commentBBox.width + PADDING);\n\n    yOffset +=\n      Math.max(typeBBox.height, nameBBox.height, keysBBox.height, commentBBox.height) +\n      TEXT_PADDING;\n    yOffsets.push(yOffset);\n  }\n  yOffsets.pop();\n  let totalWidthSections = 4;\n\n  if (maxKeysWidth <= PADDING) {\n    keysPresent = false;\n    maxKeysWidth = 0;\n    totalWidthSections--;\n  }\n  if (maxCommentWidth <= PADDING) {\n    commentPresent = false;\n    maxCommentWidth = 0;\n    totalWidthSections--;\n  }\n\n  const shapeBBox = shapeSvg.node()!.getBBox();\n  // Add extra padding to attribute components to accommodate for difference in width\n  if (\n    nameBBox.width + PADDING * 2 - (maxTypeWidth + maxNameWidth + maxKeysWidth + maxCommentWidth) >\n    0\n  ) {\n    const difference =\n      nameBBox.width + PADDING * 2 - (maxTypeWidth + maxNameWidth + maxKeysWidth + maxCommentWidth);\n    maxTypeWidth += difference / totalWidthSections;\n    maxNameWidth += difference / totalWidthSections;\n    if (maxKeysWidth > 0) {\n      maxKeysWidth += difference / totalWidthSections;\n    }\n    if (maxCommentWidth > 0) {\n      maxCommentWidth += difference / totalWidthSections;\n    }\n  }\n\n  const maxWidth = maxTypeWidth + maxNameWidth + maxKeysWidth + maxCommentWidth;\n\n  // @ts-ignore TODO: Fix rough typings\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const w = Math.max(shapeBBox.width + PADDING * 2, node?.width || 0, maxWidth);\n  const h = Math.max(shapeBBox.height + (yOffsets[0] || yOffset) + TEXT_PADDING, node?.height || 0);\n  const x = -w / 2;\n  const y = -h / 2;\n\n  // Translate attribute text labels\n  shapeSvg.selectAll('g:not(:first-child)').each((_: any, i: number, nodes: any) => {\n    const text = select<any, unknown>(nodes[i]);\n    const transform = text.attr('transform');\n    let translateX = 0;\n    let translateY = 0;\n\n    if (transform) {\n      const regex = RegExp(/translate\\(([^,]+),([^)]+)\\)/);\n      const translate = regex.exec(transform);\n      if (translate) {\n        translateX = parseFloat(translate[1]);\n        translateY = parseFloat(translate[2]);\n        if (text.attr('class').includes('attribute-name')) {\n          translateX += maxTypeWidth;\n        } else if (text.attr('class').includes('attribute-keys')) {\n          translateX += maxTypeWidth + maxNameWidth;\n        } else if (text.attr('class').includes('attribute-comment')) {\n          translateX += maxTypeWidth + maxNameWidth + maxKeysWidth;\n        }\n      }\n    }\n\n    text.attr(\n      'transform',\n      `translate(${x + PADDING / 2 + translateX}, ${translateY + y + nameBBox.height + TEXT_PADDING / 2})`\n    );\n  });\n  // Center the name\n  shapeSvg\n    .select('.name')\n    .attr('transform', 'translate(' + -nameBBox.width / 2 + ', ' + (y + TEXT_PADDING / 2) + ')');\n\n  // Draw shape\n  const roughRect = rc.rectangle(x, y, w, h, options);\n  const rect = shapeSvg.insert(() => roughRect, ':first-child').attr('style', cssStyles!.join(''));\n\n  const { themeVariables } = getConfig();\n  const { rowEven, rowOdd, nodeBorder } = themeVariables;\n\n  yOffsets.push(0);\n  // Draw row rects\n  for (const [i, yOffset] of yOffsets.entries()) {\n    if (i === 0 && yOffsets.length > 1) {\n      continue;\n      // Skip first row\n    }\n    const isEven = i % 2 === 0 && yOffset !== 0;\n    const roughRect = rc.rectangle(x, nameBBox.height + y + yOffset, w, nameBBox.height, {\n      ...options,\n      fill: isEven ? rowEven : rowOdd,\n      stroke: nodeBorder,\n    });\n    shapeSvg\n      .insert(() => roughRect, 'g.label')\n      .attr('style', cssStyles!.join(''))\n      .attr('class', `row-rect-${i % 2 === 0 ? 'even' : 'odd'}`);\n  }\n\n  // Draw divider lines\n  // Name line\n  let roughLine = rc.line(x, nameBBox.height + y, w + x, nameBBox.height + y, options);\n  shapeSvg.insert(() => roughLine).attr('class', 'divider');\n  // First line\n  roughLine = rc.line(maxTypeWidth + x, nameBBox.height + y, maxTypeWidth + x, h + y, options);\n  shapeSvg.insert(() => roughLine).attr('class', 'divider');\n  // Second line\n  if (keysPresent) {\n    roughLine = rc.line(\n      maxTypeWidth + maxNameWidth + x,\n      nameBBox.height + y,\n      maxTypeWidth + maxNameWidth + x,\n      h + y,\n      options\n    );\n    shapeSvg.insert(() => roughLine).attr('class', 'divider');\n  }\n  // Third line\n  if (commentPresent) {\n    roughLine = rc.line(\n      maxTypeWidth + maxNameWidth + maxKeysWidth + x,\n      nameBBox.height + y,\n      maxTypeWidth + maxNameWidth + maxKeysWidth + x,\n      h + y,\n      options\n    );\n    shapeSvg.insert(() => roughLine).attr('class', 'divider');\n  }\n\n  // Attribute divider lines\n  for (const yOffset of yOffsets) {\n    roughLine = rc.line(\n      x,\n      nameBBox.height + y + yOffset,\n      w + x,\n      nameBBox.height + y + yOffset,\n      options\n    );\n    shapeSvg.insert(() => roughLine).attr('class', 'divider');\n  }\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n  return shapeSvg;\n}\n\n// Helper function to add label text g with translate position and style\nasync function addText<T extends SVGGraphicsElement>(\n  shapeSvg: D3Selection<T>,\n  labelText: string,\n  config: MermaidConfig,\n  translateX = 0,\n  translateY = 0,\n  classes: string[] = [],\n  style = ''\n) {\n  const label = shapeSvg\n    .insert('g')\n    .attr('class', `label ${classes.join(' ')}`)\n    .attr('transform', `translate(${translateX}, ${translateY})`)\n    .attr('style', style);\n\n  // Return types need to be parsed\n  if (labelText !== parseGenericTypes(labelText)) {\n    labelText = parseGenericTypes(labelText);\n    // Work around\n    labelText = labelText.replaceAll('<', '&lt;').replaceAll('>', '&gt;');\n  }\n\n  const text = label.node()!.appendChild(\n    await createText(\n      label,\n      labelText,\n      {\n        width: calculateTextWidth(labelText, config) + 100,\n        style,\n        useHtmlLabels: config.htmlLabels,\n      },\n      config\n    )\n  );\n  // Undo work around now that text passed through correctly\n  if (labelText.includes('&lt;') || labelText.includes('&gt;')) {\n    let child = text.children[0];\n    child.textContent = child.textContent.replaceAll('&lt;', '<').replaceAll('&gt;', '>');\n    while (child.childNodes[0]) {\n      child = child.childNodes[0];\n      // Replace its text content\n      child.textContent = child.textContent.replaceAll('&lt;', '<').replaceAll('&gt;', '>');\n    }\n  }\n\n  let bbox = text.getBBox();\n  if (evaluate(config.htmlLabels)) {\n    const div = text.children[0];\n    div.style.textAlign = 'start';\n    const dv = select(text);\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n\n  return bbox;\n}\n", "import { select } from 'd3';\nimport { getConfig } from '../../config.js';\nimport { getNodeClasses } from '../../rendering-util/rendering-elements/shapes/util.js';\nimport { calculateTextWidth, decodeEntities } from '../../utils.js';\nimport type { ClassMember, ClassNode } from './classTypes.js';\nimport { sanitizeText } from '../../diagram-api/diagramAPI.js';\nimport { createText } from '../../rendering-util/createText.js';\nimport { evaluate, hasKatex } from '../common/common.js';\nimport type { Node } from '../../rendering-util/types.js';\nimport type { MermaidConfig } from '../../config.type.js';\nimport type { D3Selection } from '../../types.js';\n\n// Creates the shapeSvg and inserts text\nexport async function textHelper<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: any,\n  config: MermaidConfig,\n  useHtmlLabels: boolean,\n  GAP = config.class!.padding ?? 12\n) {\n  const TEXT_PADDING = !useHtmlLabels ? 3 : 0;\n  const shapeSvg = parent\n    // @ts-ignore: Ignore error for using .insert on SVGAElement\n    .insert('g')\n    .attr('class', getNodeClasses(node))\n    .attr('id', node.domId || node.id);\n\n  let annotationGroup = null;\n  let labelGroup = null;\n  let membersGroup = null;\n  let methodsGroup = null;\n\n  let annotationGroupHeight = 0;\n  let labelGroupHeight = 0;\n  let membersGroupHeight = 0;\n\n  annotationGroup = shapeSvg.insert('g').attr('class', 'annotation-group text');\n  if (node.annotations.length > 0) {\n    const annotation = node.annotations[0];\n    await addText(annotationGroup, { text: `«${annotation}»` } as unknown as ClassMember, 0);\n\n    const annotationGroupBBox = annotationGroup.node()!.getBBox();\n    annotationGroupHeight = annotationGroupBBox.height;\n  }\n\n  labelGroup = shapeSvg.insert('g').attr('class', 'label-group text');\n  await addText(labelGroup, node, 0, ['font-weight: bolder']);\n  const labelGroupBBox = labelGroup.node()!.getBBox();\n  labelGroupHeight = labelGroupBBox.height;\n\n  membersGroup = shapeSvg.insert('g').attr('class', 'members-group text');\n  let yOffset = 0;\n  for (const member of node.members) {\n    const height = await addText(membersGroup, member, yOffset, [member.parseClassifier()]);\n    yOffset += height + TEXT_PADDING;\n  }\n  membersGroupHeight = membersGroup.node()!.getBBox().height;\n  if (membersGroupHeight <= 0) {\n    membersGroupHeight = GAP / 2;\n  }\n\n  methodsGroup = shapeSvg.insert('g').attr('class', 'methods-group text');\n  let methodsYOffset = 0;\n  for (const method of node.methods) {\n    const height = await addText(methodsGroup, method, methodsYOffset, [method.parseClassifier()]);\n    methodsYOffset += height + TEXT_PADDING;\n  }\n\n  let bbox = shapeSvg.node()!.getBBox();\n\n  // Center annotation\n  if (annotationGroup !== null) {\n    const annotationGroupBBox = annotationGroup.node()!.getBBox();\n    annotationGroup.attr('transform', `translate(${-annotationGroupBBox.width / 2})`);\n  }\n\n  // Adjust label\n  labelGroup.attr('transform', `translate(${-labelGroupBBox.width / 2}, ${annotationGroupHeight})`);\n\n  bbox = shapeSvg.node()!.getBBox();\n\n  membersGroup.attr(\n    'transform',\n    `translate(${0}, ${annotationGroupHeight + labelGroupHeight + GAP * 2})`\n  );\n  bbox = shapeSvg.node()!.getBBox();\n  methodsGroup.attr(\n    'transform',\n    `translate(${0}, ${annotationGroupHeight + labelGroupHeight + (membersGroupHeight ? membersGroupHeight + GAP * 4 : GAP * 2)})`\n  );\n\n  bbox = shapeSvg.node()!.getBBox();\n\n  return { shapeSvg, bbox };\n}\n\n// Modified version of labelHelper() to help create and place text for classes\nasync function addText<T extends SVGGraphicsElement>(\n  parentGroup: D3Selection<T>,\n  node: Node | ClassNode | ClassMember,\n  yOffset: number,\n  styles: string[] = []\n) {\n  const textEl = parentGroup.insert('g').attr('class', 'label').attr('style', styles.join('; '));\n  const config = getConfig();\n  let useHtmlLabels =\n    'useHtmlLabels' in node ? node.useHtmlLabels : (evaluate(config.htmlLabels) ?? true);\n\n  let textContent = '';\n  // Support regular node type (.label) and classNodes (.text)\n  if ('text' in node) {\n    textContent = node.text;\n  } else {\n    textContent = node.label!;\n  }\n\n  // createText() will cause unwanted behavior because of classDiagram syntax so workarounds are needed\n\n  if (!useHtmlLabels && textContent.startsWith('\\\\')) {\n    textContent = textContent.substring(1);\n  }\n\n  if (hasKatex(textContent)) {\n    useHtmlLabels = true;\n  }\n\n  const text = await createText(\n    textEl,\n    sanitizeText(decodeEntities(textContent)),\n    {\n      width: calculateTextWidth(textContent, config) + 50, // Add room for error when splitting text into multiple lines\n      classes: 'markdown-node-label',\n      useHtmlLabels,\n    },\n    config\n  );\n  let bbox;\n  let numberOfLines = 1;\n\n  if (!useHtmlLabels) {\n    // Undo font-weight normal\n    if (styles.includes('font-weight: bolder')) {\n      select(text).selectAll('tspan').attr('font-weight', '');\n    }\n\n    numberOfLines = text.children.length;\n\n    const textChild = text.children[0];\n    if (text.textContent === '' || text.textContent.includes('&gt')) {\n      textChild.textContent =\n        textContent[0] +\n        textContent.substring(1).replaceAll('&gt;', '>').replaceAll('&lt;', '<').trim();\n\n      // Text was improperly removed due to spaces (preserve one space if present)\n      const preserveSpace = textContent[1] === ' ';\n      if (preserveSpace) {\n        textChild.textContent = textChild.textContent[0] + ' ' + textChild.textContent.substring(1);\n      }\n    }\n\n    // To support empty boxes\n    if (textChild.textContent === 'undefined') {\n      textChild.textContent = '';\n    }\n\n    // Get the bounding box after the text update\n    bbox = text.getBBox();\n  } else {\n    const div = text.children[0];\n    const dv = select(text);\n\n    numberOfLines = div.innerHTML.split('<br>').length;\n    // Katex math support\n    if (div.innerHTML.includes('</math>')) {\n      numberOfLines += div.innerHTML.split('<mrow>').length - 1;\n    }\n\n    // Support images\n    const images = div.getElementsByTagName('img');\n    if (images) {\n      const noImgText = textContent.replace(/<img[^>]*>/g, '').trim() === '';\n      await Promise.all(\n        [...images].map(\n          (img) =>\n            new Promise((res) => {\n              function setupImage() {\n                img.style.display = 'flex';\n                img.style.flexDirection = 'column';\n\n                if (noImgText) {\n                  // default size if no text\n                  const bodyFontSize =\n                    config.fontSize?.toString() ?? window.getComputedStyle(document.body).fontSize;\n                  const enlargingFactor = 5;\n                  const width = parseInt(bodyFontSize, 10) * enlargingFactor + 'px';\n                  img.style.minWidth = width;\n                  img.style.maxWidth = width;\n                } else {\n                  img.style.width = '100%';\n                }\n                res(img);\n              }\n              setTimeout(() => {\n                if (img.complete) {\n                  setupImage();\n                }\n              });\n              img.addEventListener('error', setupImage);\n              img.addEventListener('load', setupImage);\n            })\n        )\n      );\n    }\n\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n\n  // Center text and offset by yOffset\n  textEl.attr('transform', 'translate(0,' + (-bbox.height / (2 * numberOfLines) + yOffset) + ')');\n  return bbox.height;\n}\n", "import { updateNodeBounds } from './util.js';\nimport { getConfig } from '../../../diagram-api/diagramAPI.js';\nimport { select } from 'd3';\nimport type { Node } from '../../types.js';\nimport type { ClassNode } from '../../../diagrams/class/classTypes.js';\nimport rough from 'roughjs';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport intersect from '../intersect/index.js';\nimport { textHelper } from '../../../diagrams/class/shapeUtil.js';\nimport { evaluate } from '../../../diagrams/common/common.js';\nimport type { D3Selection } from '../../../types.js';\n\nexport async function classBox<T extends SVGGraphicsElement>(parent: D3Selection<T>, node: Node) {\n  const config = getConfig();\n  const PADDING = config.class!.padding ?? 12;\n  const GAP = PADDING;\n  const useHtmlLabels = node.useHtmlLabels ?? evaluate(config.htmlLabels) ?? true;\n  // Treat node as classNode\n  const classNode = node as unknown as ClassNode;\n  classNode.annotations = classNode.annotations ?? [];\n  classNode.members = classNode.members ?? [];\n  classNode.methods = classNode.methods ?? [];\n\n  const { shapeSvg, bbox } = await textHelper(parent, node, config, useHtmlLabels, GAP);\n\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n\n  node.cssStyles = classNode.styles || '';\n\n  const styles = classNode.styles?.join(';') || nodeStyles || '';\n\n  if (!node.cssStyles) {\n    node.cssStyles = styles.replaceAll('!important', '').split(';');\n  }\n\n  const renderExtraBox =\n    classNode.members.length === 0 &&\n    classNode.methods.length === 0 &&\n    !config.class?.hideEmptyMembersBox;\n\n  // Setup roughjs\n  // @ts-ignore TODO: Fix rough typings\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  const w = bbox.width;\n  let h = bbox.height;\n  if (classNode.members.length === 0 && classNode.methods.length === 0) {\n    h += GAP;\n  } else if (classNode.members.length > 0 && classNode.methods.length === 0) {\n    h += GAP * 2;\n  }\n  const x = -w / 2;\n  const y = -h / 2;\n\n  // Create and center rectangle\n  const roughRect = rc.rectangle(\n    x - PADDING,\n    y -\n      PADDING -\n      (renderExtraBox\n        ? PADDING\n        : classNode.members.length === 0 && classNode.methods.length === 0\n          ? -PADDING / 2\n          : 0),\n    w + 2 * PADDING,\n    h +\n      2 * PADDING +\n      (renderExtraBox\n        ? PADDING * 2\n        : classNode.members.length === 0 && classNode.methods.length === 0\n          ? -PADDING\n          : 0),\n    options\n  );\n\n  const rect = shapeSvg.insert(() => roughRect, ':first-child');\n  rect.attr('class', 'basic label-container');\n  const rectBBox = rect.node()!.getBBox();\n\n  // Rect is centered so now adjust labels.\n  // TODO: Fix types\n  shapeSvg.selectAll('.text').each((_: any, i: number, nodes: any) => {\n    const text = select<any, unknown>(nodes[i]);\n    // Get the current transform attribute\n    const transform = text.attr('transform');\n    // Initialize variables for the translation values\n    let translateY = 0;\n    // Check if the transform attribute exists\n    if (transform) {\n      const regex = RegExp(/translate\\(([^,]+),([^)]+)\\)/);\n      const translate = regex.exec(transform);\n      if (translate) {\n        translateY = parseFloat(translate[2]);\n      }\n    }\n    // Add to the y value\n    let newTranslateY =\n      translateY +\n      y +\n      PADDING -\n      (renderExtraBox\n        ? PADDING\n        : classNode.members.length === 0 && classNode.methods.length === 0\n          ? -PADDING / 2\n          : 0);\n    if (!useHtmlLabels) {\n      // Fix so non html labels are better centered.\n      // BBox of text seems to be slightly different when calculated so we offset\n      newTranslateY -= 4;\n    }\n    let newTranslateX = x;\n    if (\n      text.attr('class').includes('label-group') ||\n      text.attr('class').includes('annotation-group')\n    ) {\n      newTranslateX = -text.node()?.getBBox().width / 2 || 0;\n      shapeSvg.selectAll('text').each(function (_: any, i: number, nodes: any) {\n        if (window.getComputedStyle(nodes[i]).textAnchor === 'middle') {\n          newTranslateX = 0;\n        }\n      });\n    }\n    // Set the updated transform attribute\n    text.attr('transform', `translate(${newTranslateX}, ${newTranslateY})`);\n  });\n\n  // Render divider lines.\n  const annotationGroupHeight =\n    (shapeSvg.select('.annotation-group').node() as SVGGraphicsElement).getBBox().height -\n      (renderExtraBox ? PADDING / 2 : 0) || 0;\n  const labelGroupHeight =\n    (shapeSvg.select('.label-group').node() as SVGGraphicsElement).getBBox().height -\n      (renderExtraBox ? PADDING / 2 : 0) || 0;\n  const membersGroupHeight =\n    (shapeSvg.select('.members-group').node() as SVGGraphicsElement).getBBox().height -\n      (renderExtraBox ? PADDING / 2 : 0) || 0;\n  // First line (under label)\n  if (classNode.members.length > 0 || classNode.methods.length > 0 || renderExtraBox) {\n    const roughLine = rc.line(\n      rectBBox.x,\n      annotationGroupHeight + labelGroupHeight + y + PADDING,\n      rectBBox.x + rectBBox.width,\n      annotationGroupHeight + labelGroupHeight + y + PADDING,\n      options\n    );\n    const line = shapeSvg.insert(() => roughLine);\n    line.attr('class', 'divider').attr('style', styles);\n  }\n\n  // Second line (under members)\n  if (renderExtraBox || classNode.members.length > 0 || classNode.methods.length > 0) {\n    const roughLine = rc.line(\n      rectBBox.x,\n      annotationGroupHeight + labelGroupHeight + membersGroupHeight + y + GAP * 2 + PADDING,\n      rectBBox.x + rectBBox.width,\n      annotationGroupHeight + labelGroupHeight + membersGroupHeight + y + PADDING + GAP * 2,\n      options\n    );\n    const line = shapeSvg.insert(() => roughLine);\n    line.attr('class', 'divider').attr('style', styles);\n  }\n\n  /// Apply styles ///\n  if (classNode.look !== 'handDrawn') {\n    shapeSvg.selectAll('path').attr('style', styles);\n  }\n  // Apply other styles like stroke-width and stroke-dasharray to border (not background of shape)\n  rect.select(':nth-child(2)').attr('style', styles);\n  // Divider lines\n  shapeSvg.selectAll('.divider').select('path').attr('style', styles);\n  // Text elements\n  if (node.labelStyle) {\n    shapeSvg.selectAll('span').attr('style', node.labelStyle);\n  } else {\n    shapeSvg.selectAll('span').attr('style', styles);\n  }\n  // SVG text uses fill not color\n  if (!useHtmlLabels) {\n    // We just want to apply color to the text\n    const colorRegex = RegExp(/color\\s*:\\s*([^;]*)/);\n    const match = colorRegex.exec(styles);\n    if (match) {\n      const colorStyle = match[0].replace('color', 'fill');\n      shapeSvg.selectAll('tspan').attr('style', colorStyle);\n    } else if (labelStyles) {\n      const match = colorRegex.exec(labelStyles);\n      if (match) {\n        const colorStyle = match[0].replace('color', 'fill');\n        shapeSvg.selectAll('tspan').attr('style', colorStyle);\n      }\n    }\n  }\n\n  updateNodeBounds(node, rect);\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n}\n", "import { getNodeClasses, updateNodeBounds } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node } from '../../types.js';\nimport { styles2String, userNodeOverrides } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\nimport { calculateTextWidth, decodeEntities } from '../../../utils.js';\nimport { getConfig, sanitizeText } from '../../../diagram-api/diagramAPI.js';\nimport { createText } from '../../createText.js';\nimport { select } from 'd3';\nimport type { Requirement, Element } from '../../../diagrams/requirement/types.js';\n\nexport async function requirementBox<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node\n) {\n  const { labelStyles, nodeStyles } = styles2String(node);\n  node.labelStyle = labelStyles;\n  const requirementNode = node as unknown as Requirement;\n  const elementNode = node as unknown as Element;\n  const padding = 20;\n  const gap = 20;\n  const isRequirementNode = 'verifyMethod' in node;\n  const classes = getNodeClasses(node);\n\n  // Add outer g element\n  const shapeSvg = parent\n    .insert('g')\n    .attr('class', classes)\n    .attr('id', node.domId ?? node.id);\n\n  let typeHeight;\n  if (isRequirementNode) {\n    typeHeight = await addText(\n      shapeSvg,\n      `&lt;&lt;${requirementNode.type}&gt;&gt;`,\n      0,\n      node.labelStyle\n    );\n  } else {\n    typeHeight = await addText(shapeSvg, '&lt;&lt;Element&gt;&gt;', 0, node.labelStyle);\n  }\n\n  let accumulativeHeight = typeHeight;\n  const nameHeight = await addText(\n    shapeSvg,\n    requirementNode.name,\n    accumulativeHeight,\n    node.labelStyle + '; font-weight: bold;'\n  );\n  accumulativeHeight += nameHeight + gap;\n\n  // Requirement\n  if (isRequirementNode) {\n    const idHeight = await addText(\n      shapeSvg,\n      `${requirementNode.requirementId ? `Id: ${requirementNode.requirementId}` : ''}`,\n      accumulativeHeight,\n      node.labelStyle\n    );\n\n    accumulativeHeight += idHeight;\n    const textHeight = await addText(\n      shapeSvg,\n      `${requirementNode.text ? `Text: ${requirementNode.text}` : ''}`,\n      accumulativeHeight,\n      node.labelStyle\n    );\n    accumulativeHeight += textHeight;\n    const riskHeight = await addText(\n      shapeSvg,\n      `${requirementNode.risk ? `Risk: ${requirementNode.risk}` : ''}`,\n      accumulativeHeight,\n      node.labelStyle\n    );\n    accumulativeHeight += riskHeight;\n    await addText(\n      shapeSvg,\n      `${requirementNode.verifyMethod ? `Verification: ${requirementNode.verifyMethod}` : ''}`,\n      accumulativeHeight,\n      node.labelStyle\n    );\n  } else {\n    // Element\n    const typeHeight = await addText(\n      shapeSvg,\n      `${elementNode.type ? `Type: ${elementNode.type}` : ''}`,\n      accumulativeHeight,\n      node.labelStyle\n    );\n    accumulativeHeight += typeHeight;\n    await addText(\n      shapeSvg,\n      `${elementNode.docRef ? `Doc Ref: ${elementNode.docRef}` : ''}`,\n      accumulativeHeight,\n      node.labelStyle\n    );\n  }\n\n  const totalWidth = (shapeSvg.node()?.getBBox().width ?? 200) + padding;\n  const totalHeight = (shapeSvg.node()?.getBBox().height ?? 200) + padding;\n  const x = -totalWidth / 2;\n  const y = -totalHeight / 2;\n\n  // Setup roughjs\n  // @ts-ignore TODO: Fix rough typings\n  const rc = rough.svg(shapeSvg);\n  const options = userNodeOverrides(node, {});\n\n  if (node.look !== 'handDrawn') {\n    options.roughness = 0;\n    options.fillStyle = 'solid';\n  }\n\n  // Create and center rectangle\n  const roughRect = rc.rectangle(x, y, totalWidth, totalHeight, options);\n\n  const rect = shapeSvg.insert(() => roughRect, ':first-child');\n  rect.attr('class', 'basic label-container').attr('style', nodeStyles);\n\n  // Re-translate labels now that rect is centered\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  shapeSvg.selectAll('.label').each((_: any, i: number, nodes: any) => {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const text = select<any, unknown>(nodes[i]);\n\n    const transform = text.attr('transform');\n    let translateX = 0;\n    let translateY = 0;\n    if (transform) {\n      const regex = RegExp(/translate\\(([^,]+),([^)]+)\\)/);\n      const translate = regex.exec(transform);\n      if (translate) {\n        translateX = parseFloat(translate[1]);\n        translateY = parseFloat(translate[2]);\n      }\n    }\n\n    const newTranslateY = translateY - totalHeight / 2;\n    let newTranslateX = x + padding / 2;\n\n    // Keep type and name labels centered.\n    if (i === 0 || i === 1) {\n      newTranslateX = translateX;\n    }\n    // Set the updated transform attribute\n    text.attr('transform', `translate(${newTranslateX}, ${newTranslateY + padding})`);\n  });\n\n  // Insert divider line if there is body text\n  if (accumulativeHeight > typeHeight + nameHeight + gap) {\n    const roughLine = rc.line(\n      x,\n      y + typeHeight + nameHeight + gap,\n      x + totalWidth,\n      y + typeHeight + nameHeight + gap,\n      options\n    );\n    const dividerLine = shapeSvg.insert(() => roughLine);\n    dividerLine.attr('style', nodeStyles);\n  }\n\n  updateNodeBounds(node, rect);\n\n  node.intersect = function (point) {\n    return intersect.rect(node, point);\n  };\n\n  return shapeSvg;\n}\n\nasync function addText<T extends SVGGraphicsElement>(\n  parentGroup: D3Selection<T>,\n  inputText: string,\n  yOffset: number,\n  style = ''\n) {\n  if (inputText === '') {\n    return 0;\n  }\n  const textEl = parentGroup.insert('g').attr('class', 'label').attr('style', style);\n  const config = getConfig();\n  const useHtmlLabels = config.htmlLabels ?? true;\n\n  const text = await createText(\n    textEl,\n    sanitizeText(decodeEntities(inputText)),\n    {\n      width: calculateTextWidth(inputText, config) + 50, // Add room for error when splitting text into multiple lines\n      classes: 'markdown-node-label',\n      useHtmlLabels,\n      style,\n    },\n    config\n  );\n  let bbox;\n\n  if (!useHtmlLabels) {\n    const textChild = text.children[0];\n    for (const child of textChild.children) {\n      child.textContent = child.textContent.replaceAll('&gt;', '>').replaceAll('&lt;', '<');\n      if (style) {\n        child.setAttribute('style', style);\n      }\n    }\n    // Get the bounding box after the text update\n    bbox = text.getBBox();\n    // Add extra height so it is similar to the html labels\n    bbox.height += 6;\n  } else {\n    const div = text.children[0];\n    const dv = select(text);\n\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n\n  // Center text and offset by yOffset\n  textEl.attr('transform', `translate(${-bbox.width / 2},${-bbox.height / 2 + yOffset})`);\n  return bbox.height;\n}\n", "import { labelHelper, insert<PERSON>abel, updateNodeBounds, getNodeClasses } from './util.js';\nimport intersect from '../intersect/index.js';\nimport type { Node, KanbanNode, ShapeRenderOptions } from '../../types.js';\nimport { createRoundedRectPathD } from './roundedRectPath.js';\nimport { userNodeOverrides, styles2String } from './handDrawnShapeStyles.js';\nimport rough from 'roughjs';\nimport type { D3Selection } from '../../../types.js';\n\nconst colorFromPriority = (priority: NonNullable<KanbanNode['priority']>) => {\n  switch (priority) {\n    case 'Very High':\n      return 'red';\n    case 'High':\n      return 'orange';\n    case 'Medium':\n      return null; // no stroke\n    case 'Low':\n      return 'blue';\n    case 'Very Low':\n      return 'lightblue';\n  }\n};\nexport async function kanbanItem<T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  // Omit the 'shape' prop since otherwise, it causes a TypeScript circular dependency error\n  kanbanNode: Omit<Node, 'shape'> | Omit<KanbanNode, 'level' | 'shape'>,\n  { config }: ShapeRenderOptions\n) {\n  const { labelStyles, nodeStyles } = styles2String(kanbanNode);\n  kanbanNode.labelStyle = labelStyles || '';\n\n  const labelPaddingX = 10;\n  const orgWidth = kanbanNode.width;\n  kanbanNode.width = (kanbanNode.width ?? 200) - 10;\n\n  const {\n    shapeSvg,\n    bbox,\n    label: labelElTitle,\n  } = await labelHelper(parent, kanbanNode, getNodeClasses(kanbanNode));\n  const padding = kanbanNode.padding || 10;\n\n  let ticketUrl = '';\n  let link;\n\n  if ('ticket' in kanbanNode && kanbanNode.ticket && config?.kanban?.ticketBaseUrl) {\n    ticketUrl = config?.kanban?.ticketBaseUrl.replace('#TICKET#', kanbanNode.ticket);\n    link = shapeSvg\n      .insert<SVGAElement>('svg:a', ':first-child')\n      .attr('class', 'kanban-ticket-link')\n      .attr('xlink:href', ticketUrl)\n      .attr('target', '_blank');\n  }\n\n  const options = {\n    useHtmlLabels: kanbanNode.useHtmlLabels,\n    labelStyle: kanbanNode.labelStyle || '',\n    width: kanbanNode.width,\n    img: kanbanNode.img,\n    padding: kanbanNode.padding || 8,\n    centerLabel: false,\n  };\n  let labelEl, bbox2;\n  if (link) {\n    ({ label: labelEl, bbox: bbox2 } = await insertLabel(\n      link,\n      ('ticket' in kanbanNode && kanbanNode.ticket) || '',\n      options\n    ));\n  } else {\n    ({ label: labelEl, bbox: bbox2 } = await insertLabel(\n      shapeSvg,\n      ('ticket' in kanbanNode && kanbanNode.ticket) || '',\n      options\n    ));\n  }\n  const { label: labelElAssigned, bbox: bboxAssigned } = await insertLabel(\n    shapeSvg,\n    ('assigned' in kanbanNode && kanbanNode.assigned) || '',\n    options\n  );\n  kanbanNode.width = orgWidth;\n  const labelPaddingY = 10;\n  const totalWidth = kanbanNode?.width || 0;\n  const heightAdj = Math.max(bbox2.height, bboxAssigned.height) / 2;\n  const totalHeight =\n    Math.max(bbox.height + labelPaddingY * 2, kanbanNode?.height || 0) + heightAdj;\n  const x = -totalWidth / 2;\n  const y = -totalHeight / 2;\n  labelElTitle.attr(\n    'transform',\n    'translate(' + (padding - totalWidth / 2) + ', ' + (-heightAdj - bbox.height / 2) + ')'\n  );\n  labelEl.attr(\n    'transform',\n    'translate(' + (padding - totalWidth / 2) + ', ' + (-heightAdj + bbox.height / 2) + ')'\n  );\n  labelElAssigned.attr(\n    'transform',\n    'translate(' +\n      (padding + totalWidth / 2 - bboxAssigned.width - 2 * labelPaddingX) +\n      ', ' +\n      (-heightAdj + bbox.height / 2) +\n      ')'\n  );\n\n  let rect;\n\n  const { rx, ry } = kanbanNode;\n  const { cssStyles } = kanbanNode;\n\n  if (kanbanNode.look === 'handDrawn') {\n    // @ts-ignore TODO: Fix rough typings\n    const rc = rough.svg(shapeSvg);\n    const options = userNodeOverrides(kanbanNode, {});\n\n    const roughNode =\n      rx || ry\n        ? rc.path(createRoundedRectPathD(x, y, totalWidth, totalHeight, rx || 0), options)\n        : rc.rectangle(x, y, totalWidth, totalHeight, options);\n\n    rect = shapeSvg.insert(() => roughNode, ':first-child');\n    rect.attr('class', 'basic label-container').attr('style', cssStyles ? cssStyles : null);\n  } else {\n    rect = shapeSvg.insert('rect', ':first-child');\n\n    rect\n      .attr('class', 'basic label-container __APA__')\n      .attr('style', nodeStyles)\n      .attr('rx', rx ?? 5)\n      .attr('ry', ry ?? 5)\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', totalWidth)\n      .attr('height', totalHeight);\n\n    const priority = 'priority' in kanbanNode && kanbanNode.priority;\n    if (priority) {\n      const line = shapeSvg.append('line');\n      const lineX = x + 2;\n\n      const y1 = y + Math.floor((rx ?? 0) / 2);\n      const y2 = y + totalHeight - Math.floor((rx ?? 0) / 2);\n      line\n        .attr('x1', lineX)\n        .attr('y1', y1)\n        .attr('x2', lineX)\n        .attr('y2', y2)\n\n        .attr('stroke-width', '4')\n        .attr('stroke', colorFromPriority(priority));\n    }\n  }\n\n  updateNodeBounds(kanbanNode, rect);\n  kanbanNode.height = totalHeight;\n\n  kanbanNode.intersect = function (point) {\n    return intersect.rect(kanbanNode, point);\n  };\n\n  return shapeSvg;\n}\n", "import type { Entries } from 'type-fest';\nimport type { D3Selection, MaybePromise } from '../../types.js';\nimport type { Node, ShapeRenderOptions } from '../types.js';\nimport { anchor } from './shapes/anchor.js';\nimport { bowTieRect } from './shapes/bowTieRect.js';\nimport { card } from './shapes/card.js';\nimport { choice } from './shapes/choice.js';\nimport { circle } from './shapes/circle.js';\nimport { crossedCircle } from './shapes/crossedCircle.js';\nimport { curlyBraceLeft } from './shapes/curlyBraceLeft.js';\nimport { curlyBraceRight } from './shapes/curlyBraceRight.js';\nimport { curlyBraces } from './shapes/curlyBraces.js';\nimport { curvedTrapezoid } from './shapes/curvedTrapezoid.js';\nimport { cylinder } from './shapes/cylinder.js';\nimport { dividedRectangle } from './shapes/dividedRect.js';\nimport { doublecircle } from './shapes/doubleCircle.js';\nimport { filledCircle } from './shapes/filledCircle.js';\nimport { flippedTriangle } from './shapes/flippedTriangle.js';\nimport { forkJoin } from './shapes/forkJoin.js';\nimport { halfRoundedRectangle } from './shapes/halfRoundedRectangle.js';\nimport { hexagon } from './shapes/hexagon.js';\nimport { hourglass } from './shapes/hourglass.js';\nimport { icon } from './shapes/icon.js';\nimport { iconCircle } from './shapes/iconCircle.js';\nimport { iconRounded } from './shapes/iconRounded.js';\nimport { iconSquare } from './shapes/iconSquare.js';\nimport { imageSquare } from './shapes/imageSquare.js';\nimport { inv_trapezoid } from './shapes/invertedTrapezoid.js';\nimport { labelRect } from './shapes/labelRect.js';\nimport { lean_left } from './shapes/leanLeft.js';\nimport { lean_right } from './shapes/leanRight.js';\nimport { lightningBolt } from './shapes/lightningBolt.js';\nimport { linedCylinder } from './shapes/linedCylinder.js';\nimport { linedWaveEdgedRect } from './shapes/linedWaveEdgedRect.js';\nimport { multiRect } from './shapes/multiRect.js';\nimport { multiWaveEdgedRectangle } from './shapes/multiWaveEdgedRectangle.js';\nimport { note } from './shapes/note.js';\nimport { question } from './shapes/question.js';\nimport { rect_left_inv_arrow } from './shapes/rectLeftInvArrow.js';\nimport { rectWithTitle } from './shapes/rectWithTitle.js';\nimport { roundedRect } from './shapes/roundedRect.js';\nimport { shadedProcess } from './shapes/shadedProcess.js';\nimport { slopedRect } from './shapes/slopedRect.js';\nimport { squareRect } from './shapes/squareRect.js';\nimport { stadium } from './shapes/stadium.js';\nimport { state } from './shapes/state.js';\nimport { stateEnd } from './shapes/stateEnd.js';\nimport { stateStart } from './shapes/stateStart.js';\nimport { subroutine } from './shapes/subroutine.js';\nimport { taggedRect } from './shapes/taggedRect.js';\nimport { taggedWaveEdgedRectangle } from './shapes/taggedWaveEdgedRectangle.js';\nimport { text } from './shapes/text.js';\nimport { tiltedCylinder } from './shapes/tiltedCylinder.js';\nimport { trapezoid } from './shapes/trapezoid.js';\nimport { trapezoidalPentagon } from './shapes/trapezoidalPentagon.js';\nimport { triangle } from './shapes/triangle.js';\nimport { waveEdgedRectangle } from './shapes/waveEdgedRectangle.js';\nimport { waveRectangle } from './shapes/waveRectangle.js';\nimport { windowPane } from './shapes/windowPane.js';\nimport { erBox } from './shapes/erBox.js';\nimport { classBox } from './shapes/classBox.js';\nimport { requirementBox } from './shapes/requirementBox.js';\nimport { kanbanItem } from './shapes/kanbanItem.js';\n\ntype ShapeHandler = <T extends SVGGraphicsElement>(\n  parent: D3Selection<T>,\n  node: Node,\n  options: ShapeRenderOptions\n) => MaybePromise<D3Selection<SVGGElement>>;\n\nexport interface ShapeDefinition {\n  semanticName: string;\n  name: string;\n  shortName: string;\n  description: string;\n  /**\n   * Aliases can include descriptive names, other short names, etc.\n   */\n  aliases?: string[];\n  /**\n   * These are names used by mermaid before the introduction of new shapes. These will not be in standard formats, and shouldn't be used by the users\n   */\n  internalAliases?: string[];\n  handler: ShapeHandler;\n}\n\nexport const shapesDefs = [\n  {\n    semanticName: 'Process',\n    name: 'Rectangle',\n    shortName: 'rect',\n    description: 'Standard process shape',\n    aliases: ['proc', 'process', 'rectangle'],\n    internalAliases: ['squareRect'],\n    handler: squareRect,\n  },\n  {\n    semanticName: 'Event',\n    name: 'Rounded Rectangle',\n    shortName: 'rounded',\n    description: 'Represents an event',\n    aliases: ['event'],\n    internalAliases: ['roundedRect'],\n    handler: roundedRect,\n  },\n  {\n    semanticName: 'Terminal Point',\n    name: 'Stadium',\n    shortName: 'stadium',\n    description: 'Terminal point',\n    aliases: ['terminal', 'pill'],\n    handler: stadium,\n  },\n  {\n    semanticName: 'Subprocess',\n    name: 'Framed Rectangle',\n    shortName: 'fr-rect',\n    description: 'Subprocess',\n    aliases: ['subprocess', 'subproc', 'framed-rectangle', 'subroutine'],\n    handler: subroutine,\n  },\n  {\n    semanticName: 'Database',\n    name: 'Cylinder',\n    shortName: 'cyl',\n    description: 'Database storage',\n    aliases: ['db', 'database', 'cylinder'],\n    handler: cylinder,\n  },\n  {\n    semanticName: 'Start',\n    name: 'Circle',\n    shortName: 'circle',\n    description: 'Starting point',\n    aliases: ['circ'],\n    handler: circle,\n  },\n  {\n    semanticName: 'Decision',\n    name: 'Diamond',\n    shortName: 'diam',\n    description: 'Decision-making step',\n    aliases: ['decision', 'diamond', 'question'],\n    handler: question,\n  },\n  {\n    semanticName: 'Prepare Conditional',\n    name: 'Hexagon',\n    shortName: 'hex',\n    description: 'Preparation or condition step',\n    aliases: ['hexagon', 'prepare'],\n    handler: hexagon,\n  },\n  {\n    semanticName: 'Data Input/Output',\n    name: 'Lean Right',\n    shortName: 'lean-r',\n    description: 'Represents input or output',\n    aliases: ['lean-right', 'in-out'],\n    internalAliases: ['lean_right'],\n    handler: lean_right,\n  },\n  {\n    semanticName: 'Data Input/Output',\n    name: 'Lean Left',\n    shortName: 'lean-l',\n    description: 'Represents output or input',\n    aliases: ['lean-left', 'out-in'],\n    internalAliases: ['lean_left'],\n    handler: lean_left,\n  },\n  {\n    semanticName: 'Priority Action',\n    name: 'Trapezoid Base Bottom',\n    shortName: 'trap-b',\n    description: 'Priority action',\n    aliases: ['priority', 'trapezoid-bottom', 'trapezoid'],\n    handler: trapezoid,\n  },\n  {\n    semanticName: 'Manual Operation',\n    name: 'Trapezoid Base Top',\n    shortName: 'trap-t',\n    description: 'Represents a manual task',\n    aliases: ['manual', 'trapezoid-top', 'inv-trapezoid'],\n    internalAliases: ['inv_trapezoid'],\n    handler: inv_trapezoid,\n  },\n  {\n    semanticName: 'Stop',\n    name: 'Double Circle',\n    shortName: 'dbl-circ',\n    description: 'Represents a stop point',\n    aliases: ['double-circle'],\n    internalAliases: ['doublecircle'],\n    handler: doublecircle,\n  },\n  {\n    semanticName: 'Text Block',\n    name: 'Text Block',\n    shortName: 'text',\n    description: 'Text block',\n    handler: text,\n  },\n  {\n    semanticName: 'Card',\n    name: 'Notched Rectangle',\n    shortName: 'notch-rect',\n    description: 'Represents a card',\n    aliases: ['card', 'notched-rectangle'],\n    handler: card,\n  },\n  {\n    semanticName: 'Lined/Shaded Process',\n    name: 'Lined Rectangle',\n    shortName: 'lin-rect',\n    description: 'Lined process shape',\n    aliases: ['lined-rectangle', 'lined-process', 'lin-proc', 'shaded-process'],\n    handler: shadedProcess,\n  },\n  {\n    semanticName: 'Start',\n    name: 'Small Circle',\n    shortName: 'sm-circ',\n    description: 'Small starting point',\n    aliases: ['start', 'small-circle'],\n    internalAliases: ['stateStart'],\n    handler: stateStart,\n  },\n  {\n    semanticName: 'Stop',\n    name: 'Framed Circle',\n    shortName: 'fr-circ',\n    description: 'Stop point',\n    aliases: ['stop', 'framed-circle'],\n    internalAliases: ['stateEnd'],\n    handler: stateEnd,\n  },\n  {\n    semanticName: 'Fork/Join',\n    name: 'Filled Rectangle',\n    shortName: 'fork',\n    description: 'Fork or join in process flow',\n    aliases: ['join'],\n    internalAliases: ['forkJoin'],\n    handler: forkJoin,\n  },\n  {\n    semanticName: 'Collate',\n    name: 'Hourglass',\n    shortName: 'hourglass',\n    description: 'Represents a collate operation',\n    aliases: ['hourglass', 'collate'],\n    handler: hourglass,\n  },\n  {\n    semanticName: 'Comment',\n    name: 'Curly Brace',\n    shortName: 'brace',\n    description: 'Adds a comment',\n    aliases: ['comment', 'brace-l'],\n    handler: curlyBraceLeft,\n  },\n  {\n    semanticName: 'Comment Right',\n    name: 'Curly Brace',\n    shortName: 'brace-r',\n    description: 'Adds a comment',\n    handler: curlyBraceRight,\n  },\n  {\n    semanticName: 'Comment with braces on both sides',\n    name: 'Curly Braces',\n    shortName: 'braces',\n    description: 'Adds a comment',\n    handler: curlyBraces,\n  },\n  {\n    semanticName: 'Com Link',\n    name: 'Lightning Bolt',\n    shortName: 'bolt',\n    description: 'Communication link',\n    aliases: ['com-link', 'lightning-bolt'],\n    handler: lightningBolt,\n  },\n  {\n    semanticName: 'Document',\n    name: 'Document',\n    shortName: 'doc',\n    description: 'Represents a document',\n    aliases: ['doc', 'document'],\n    handler: waveEdgedRectangle,\n  },\n  {\n    semanticName: 'Delay',\n    name: 'Half-Rounded Rectangle',\n    shortName: 'delay',\n    description: 'Represents a delay',\n    aliases: ['half-rounded-rectangle'],\n    handler: halfRoundedRectangle,\n  },\n  {\n    semanticName: 'Direct Access Storage',\n    name: 'Horizontal Cylinder',\n    shortName: 'h-cyl',\n    description: 'Direct access storage',\n    aliases: ['das', 'horizontal-cylinder'],\n    handler: tiltedCylinder,\n  },\n  {\n    semanticName: 'Disk Storage',\n    name: 'Lined Cylinder',\n    shortName: 'lin-cyl',\n    description: 'Disk storage',\n    aliases: ['disk', 'lined-cylinder'],\n    handler: linedCylinder,\n  },\n  {\n    semanticName: 'Display',\n    name: 'Curved Trapezoid',\n    shortName: 'curv-trap',\n    description: 'Represents a display',\n    aliases: ['curved-trapezoid', 'display'],\n    handler: curvedTrapezoid,\n  },\n  {\n    semanticName: 'Divided Process',\n    name: 'Divided Rectangle',\n    shortName: 'div-rect',\n    description: 'Divided process shape',\n    aliases: ['div-proc', 'divided-rectangle', 'divided-process'],\n    handler: dividedRectangle,\n  },\n  {\n    semanticName: 'Extract',\n    name: 'Triangle',\n    shortName: 'tri',\n    description: 'Extraction process',\n    aliases: ['extract', 'triangle'],\n    handler: triangle,\n  },\n  {\n    semanticName: 'Internal Storage',\n    name: 'Window Pane',\n    shortName: 'win-pane',\n    description: 'Internal storage',\n    aliases: ['internal-storage', 'window-pane'],\n    handler: windowPane,\n  },\n  {\n    semanticName: 'Junction',\n    name: 'Filled Circle',\n    shortName: 'f-circ',\n    description: 'Junction point',\n    aliases: ['junction', 'filled-circle'],\n    handler: filledCircle,\n  },\n  {\n    semanticName: 'Loop Limit',\n    name: 'Trapezoidal Pentagon',\n    shortName: 'notch-pent',\n    description: 'Loop limit step',\n    aliases: ['loop-limit', 'notched-pentagon'],\n    handler: trapezoidalPentagon,\n  },\n  {\n    semanticName: 'Manual File',\n    name: 'Flipped Triangle',\n    shortName: 'flip-tri',\n    description: 'Manual file operation',\n    aliases: ['manual-file', 'flipped-triangle'],\n    handler: flippedTriangle,\n  },\n  {\n    semanticName: 'Manual Input',\n    name: 'Sloped Rectangle',\n    shortName: 'sl-rect',\n    description: 'Manual input step',\n    aliases: ['manual-input', 'sloped-rectangle'],\n    handler: slopedRect,\n  },\n  {\n    semanticName: 'Multi-Document',\n    name: 'Stacked Document',\n    shortName: 'docs',\n    description: 'Multiple documents',\n    aliases: ['documents', 'st-doc', 'stacked-document'],\n    handler: multiWaveEdgedRectangle,\n  },\n  {\n    semanticName: 'Multi-Process',\n    name: 'Stacked Rectangle',\n    shortName: 'st-rect',\n    description: 'Multiple processes',\n    aliases: ['procs', 'processes', 'stacked-rectangle'],\n    handler: multiRect,\n  },\n  {\n    semanticName: 'Stored Data',\n    name: 'Bow Tie Rectangle',\n    shortName: 'bow-rect',\n    description: 'Stored data',\n    aliases: ['stored-data', 'bow-tie-rectangle'],\n    handler: bowTieRect,\n  },\n  {\n    semanticName: 'Summary',\n    name: 'Crossed Circle',\n    shortName: 'cross-circ',\n    description: 'Summary',\n    aliases: ['summary', 'crossed-circle'],\n    handler: crossedCircle,\n  },\n  {\n    semanticName: 'Tagged Document',\n    name: 'Tagged Document',\n    shortName: 'tag-doc',\n    description: 'Tagged document',\n    aliases: ['tag-doc', 'tagged-document'],\n    handler: taggedWaveEdgedRectangle,\n  },\n  {\n    semanticName: 'Tagged Process',\n    name: 'Tagged Rectangle',\n    shortName: 'tag-rect',\n    description: 'Tagged process',\n    aliases: ['tagged-rectangle', 'tag-proc', 'tagged-process'],\n    handler: taggedRect,\n  },\n  {\n    semanticName: 'Paper Tape',\n    name: 'Flag',\n    shortName: 'flag',\n    description: 'Paper tape',\n    aliases: ['paper-tape'],\n    handler: waveRectangle,\n  },\n  {\n    semanticName: 'Odd',\n    name: 'Odd',\n    shortName: 'odd',\n    description: 'Odd shape',\n    internalAliases: ['rect_left_inv_arrow'],\n    handler: rect_left_inv_arrow,\n  },\n  {\n    semanticName: 'Lined Document',\n    name: 'Lined Document',\n    shortName: 'lin-doc',\n    description: 'Lined document',\n    aliases: ['lined-document'],\n    handler: linedWaveEdgedRect,\n  },\n] as const satisfies ShapeDefinition[];\n\nconst generateShapeMap = () => {\n  // These are the shapes that didn't have documentation present\n  const undocumentedShapes = {\n    // States\n    state,\n    choice,\n    note,\n\n    // Rectangles\n    rectWithTitle,\n    labelRect,\n\n    // Icons\n    iconSquare,\n    iconCircle,\n    icon,\n    iconRounded,\n    imageSquare,\n    anchor,\n\n    // Kanban diagram\n    kanbanItem,\n\n    // class diagram\n    classBox,\n\n    // er diagram\n    erBox,\n\n    // Requirement diagram\n    requirementBox,\n  } as const;\n\n  const entries = [\n    ...(Object.entries(undocumentedShapes) as Entries<typeof undocumentedShapes>),\n    ...shapesDefs.flatMap((shape) => {\n      const aliases = [\n        shape.shortName,\n        ...('aliases' in shape ? shape.aliases : []),\n        ...('internalAliases' in shape ? shape.internalAliases : []),\n      ];\n      return aliases.map((alias) => [alias, shape.handler] as const);\n    }),\n  ];\n  return Object.fromEntries(entries) as Record<\n    (typeof entries)[number][0],\n    (typeof entries)[number][1]\n  > satisfies Record<string, ShapeHandler>;\n};\n\nexport const shapes = generateShapeMap();\n\nexport function isValidShape(shape: string): shape is ShapeID {\n  return shape in shapes;\n}\n\nexport type ShapeID = keyof typeof shapes;\n", "import { log } from '../../logger.js';\nimport { shapes } from './shapes.js';\nimport type { Node, NonClusterNode, ShapeRenderOptions } from '../types.js';\nimport type { SVGGroup } from '../../mermaid.js';\nimport type { D3Selection } from '../../types.js';\nimport type { graphlib } from 'dagre-d3-es';\n\ntype ShapeHandler = (typeof shapes)[keyof typeof shapes];\ntype NodeElement = D3Selection<SVGAElement> | Awaited<ReturnType<ShapeHandler>>;\n\nconst nodeElems = new Map<string, NodeElement>();\n\nexport async function insertNode(\n  elem: SVGGroup,\n  node: NonClusterNode,\n  renderOptions: ShapeRenderOptions\n) {\n  let newEl: NodeElement | undefined;\n  let el;\n\n  //special check for rect shape (with or without rounded corners)\n  if (node.shape === 'rect') {\n    if (node.rx && node.ry) {\n      node.shape = 'roundedRect';\n    } else {\n      node.shape = 'squareRect';\n    }\n  }\n\n  const shapeHandler = node.shape ? shapes[node.shape] : undefined;\n\n  if (!shapeHandler) {\n    throw new Error(`No such shape: ${node.shape}. Please check your syntax.`);\n  }\n\n  if (node.link) {\n    // Add link when appropriate\n    let target;\n    if (renderOptions.config.securityLevel === 'sandbox') {\n      target = '_top';\n    } else if (node.linkTarget) {\n      target = node.linkTarget || '_blank';\n    }\n    newEl = elem\n      .insert<SVGAElement>('svg:a')\n      .attr('xlink:href', node.link)\n      .attr('target', target ?? null);\n    el = await shapeHandler(newEl, node, renderOptions);\n  } else {\n    el = await shapeHandler(elem, node, renderOptions);\n    newEl = el;\n  }\n  if (node.tooltip) {\n    el.attr('title', node.tooltip);\n  }\n\n  nodeElems.set(node.id, newEl);\n\n  if (node.haveCallback) {\n    newEl.attr('class', newEl.attr('class') + ' clickable');\n  }\n  return newEl;\n}\n\nexport const setNodeElem = (elem: NodeElement, node: Pick<Node, 'id'>) => {\n  nodeElems.set(node.id, elem);\n};\n\nexport const clear = () => {\n  nodeElems.clear();\n};\n\nexport const positionNode = (node: ReturnType<graphlib.Graph['node']>) => {\n  const el = nodeElems.get(node.id)!;\n  log.trace(\n    'Transforming node',\n    node.diff,\n    node,\n    'translate(' + (node.x - node.width / 2 - 5) + ', ' + node.width / 2 + ')'\n  );\n  const padding = 8;\n  const diff = node.diff || 0;\n  if (node.clusterNode) {\n    el.attr(\n      'transform',\n      'translate(' +\n        (node.x + diff - node.width / 2) +\n        ', ' +\n        (node.y - node.height / 2 - padding) +\n        ')'\n    );\n  } else {\n    el.attr('transform', 'translate(' + node.x + ', ' + node.y + ')');\n  }\n  return diff;\n};\n"], "mappings": "kWASO,IAAMA,EAAcC,EAAA,MACzBC,EACAC,EACAC,IACG,CACH,IAAIC,EACEC,EAAgBH,EAAK,eAAiBI,EAASC,EAAU,GAAG,UAAU,EACvEJ,EAGHC,EAAaD,EAFbC,EAAa,eAMf,IAAMI,EAAWP,EACd,OAAO,GAAG,EACV,KAAK,QAASG,CAAU,EACxB,KAAK,KAAMF,EAAK,OAASA,EAAK,EAAE,EAG7BO,EAAUD,EACb,OAAO,GAAG,EACV,KAAK,QAAS,OAAO,EACrB,KAAK,QAASE,EAAoBR,EAAK,UAAU,CAAC,EAGjDS,EACAT,EAAK,QAAU,OACjBS,EAAQ,GAERA,EAAQ,OAAOT,EAAK,OAAU,SAAWA,EAAK,MAAQA,EAAK,MAAM,CAAC,EAGpE,IAAMU,EAAO,MAAMC,GAAWJ,EAASK,GAAaC,GAAeJ,CAAK,EAAGJ,EAAU,CAAC,EAAG,CACvF,cAAAF,EACA,MAAOH,EAAK,OAASK,EAAU,EAAE,WAAW,cAE5C,WAAY,sBACZ,MAAOL,EAAK,WACZ,iBAAkB,CAAC,CAACA,EAAK,MAAQ,CAAC,CAACA,EAAK,GAC1C,CAAC,EAEGc,EAAOJ,EAAK,QAAQ,EAClBK,GAAef,GAAM,SAAW,GAAK,EAE3C,GAAIG,EAAe,CACjB,IAAMa,EAAMN,EAAK,SAAS,CAAC,EACrBO,EAAKC,EAAOR,CAAI,EAGhBS,EAASH,EAAI,qBAAqB,KAAK,EAC7C,GAAIG,EAAQ,CACV,IAAMC,EAAYX,EAAM,QAAQ,cAAe,EAAE,EAAE,KAAK,IAAM,GAE9D,MAAM,QAAQ,IACZ,CAAC,GAAGU,CAAM,EAAE,IACTE,GACC,IAAI,QAASC,GAAQ,CAInB,SAASC,GAAa,CAIpB,GAHAF,EAAI,MAAM,QAAU,OACpBA,EAAI,MAAM,cAAgB,SAEtBD,EAAW,CAEb,IAAMI,EAAenB,EAAU,EAAE,SAC7BA,EAAU,EAAE,SACZ,OAAO,iBAAiB,SAAS,IAAI,EAAE,SACrCoB,EAAkB,EAClB,CAACC,EAAqBC,GAAc,QAAQ,EAAIC,GAAcJ,CAAY,EAC1EK,EAAQH,EAAqBD,EAAkB,KACrDJ,EAAI,MAAM,SAAWQ,EACrBR,EAAI,MAAM,SAAWQ,CACvB,MACER,EAAI,MAAM,MAAQ,OAEpBC,EAAID,CAAG,CACT,CAlBSvB,EAAAyB,EAAA,cAmBT,WAAW,IAAM,CACXF,EAAI,UACNE,EAAW,CAEf,CAAC,EACDF,EAAI,iBAAiB,QAASE,CAAU,EACxCF,EAAI,iBAAiB,OAAQE,CAAU,CACzC,CAAC,CACL,CACF,CACF,CAEAT,EAAOE,EAAI,sBAAsB,EACjCC,EAAG,KAAK,QAASH,EAAK,KAAK,EAC3BG,EAAG,KAAK,SAAUH,EAAK,MAAM,CAC/B,CAGA,OAAIX,EACFI,EAAQ,KAAK,YAAa,aAAe,CAACO,EAAK,MAAQ,EAAI,KAAO,CAACA,EAAK,OAAS,EAAI,GAAG,EAExFP,EAAQ,KAAK,YAAa,gBAA0B,CAACO,EAAK,OAAS,EAAI,GAAG,EAExEd,EAAK,aACPO,EAAQ,KAAK,YAAa,aAAe,CAACO,EAAK,MAAQ,EAAI,KAAO,CAACA,EAAK,OAAS,EAAI,GAAG,EAE1FP,EAAQ,OAAO,OAAQ,cAAc,EAC9B,CAAE,SAAAD,EAAU,KAAAQ,EAAM,YAAAC,EAAa,MAAOR,CAAQ,CACvD,EA5G2B,eA6GduB,GAAchC,EAAA,MACzBC,EACAU,EACAsB,IAUG,CACH,IAAM5B,EAAgB4B,EAAQ,eAAiB3B,EAASC,EAAU,GAAG,WAAW,UAAU,EAGpFE,EAAUR,EACb,OAAO,GAAG,EACV,KAAK,QAAS,OAAO,EACrB,KAAK,QAASgC,EAAQ,YAAc,EAAE,EAEnCrB,EAAO,MAAMC,GAAWJ,EAASK,GAAaC,GAAeJ,CAAK,EAAGJ,EAAU,CAAC,EAAG,CACvF,cAAAF,EACA,MAAO4B,EAAQ,OAAS1B,EAAU,GAAG,WAAW,cAChD,MAAO0B,EAAQ,WACf,iBAAkB,CAAC,CAACA,EAAQ,MAAQ,CAAC,CAACA,EAAQ,GAChD,CAAC,EAEGjB,EAAOJ,EAAK,QAAQ,EAClBK,EAAcgB,EAAQ,QAAU,EAEtC,GAAI3B,EAASC,EAAU,GAAG,WAAW,UAAU,EAAG,CAChD,IAAMW,EAAMN,EAAK,SAAS,CAAC,EACrBO,EAAKC,EAAOR,CAAI,EAEtBI,EAAOE,EAAI,sBAAsB,EACjCC,EAAG,KAAK,QAASH,EAAK,KAAK,EAC3BG,EAAG,KAAK,SAAUH,EAAK,MAAM,CAC/B,CAGA,OAAIX,EACFI,EAAQ,KAAK,YAAa,aAAe,CAACO,EAAK,MAAQ,EAAI,KAAO,CAACA,EAAK,OAAS,EAAI,GAAG,EAExFP,EAAQ,KAAK,YAAa,gBAA0B,CAACO,EAAK,OAAS,EAAI,GAAG,EAExEiB,EAAQ,aACVxB,EAAQ,KAAK,YAAa,aAAe,CAACO,EAAK,MAAQ,EAAI,KAAO,CAACA,EAAK,OAAS,EAAI,GAAG,EAE1FP,EAAQ,OAAO,OAAQ,cAAc,EAC9B,CAAE,SAAUR,EAAQ,KAAAe,EAAM,YAAAC,EAAa,MAAOR,CAAQ,CAC/D,EApD2B,eAqDdyB,EAAmBlC,EAAA,CAC9BE,EAEAiC,IACG,CACH,IAAMnB,EAAOmB,EAAQ,KAAK,EAAG,QAAQ,EACrCjC,EAAK,MAAQc,EAAK,MAClBd,EAAK,OAASc,EAAK,MACrB,EARgC,oBAoCzB,IAAMoB,EAAiBC,EAAA,CAACC,EAAYC,KACxCD,EAAK,OAAS,YAAc,aAAe,QAAU,IAAMA,EAAK,WAAa,KAAOC,GAAS,IADlE,kBAGvB,SAASC,EAAqBC,EAAiB,CACpD,IAAMC,EAAeD,EAAO,IAAI,CAACE,EAAGC,IAAM,GAAGA,IAAM,EAAI,IAAM,GAAG,GAAGD,EAAE,CAAC,IAAIA,EAAE,CAAC,EAAE,EAC/E,OAAAD,EAAa,KAAK,GAAG,EACdA,EAAa,KAAK,GAAG,CAC9B,CAJgBL,EAAAG,EAAA,wBAMT,SAASK,GACdC,EACAC,EACAC,EACAC,EACAC,EACAC,EACA,CACA,IAAMV,EAAS,CAAC,EAEVW,EAASJ,EAAKF,EACdO,EAASJ,EAAKF,EACdO,EAAcF,EAASD,EAGvBI,EAAa,EAAI,KAAK,GAAMD,EAC5BE,EAAOT,EAAKM,EAAS,EAE3B,QAAST,EAAI,EAAGA,GAAK,GAAOA,IAAK,CAC/B,IAAMa,EAAIb,EAAI,GACRc,EAAIZ,EAAKW,EAAIL,EACb,EAAII,EAAON,EAAY,KAAK,IAAIK,GAAaG,EAAIZ,EAAG,EAE1DL,EAAO,KAAK,CAAE,EAAAiB,EAAG,CAAE,CAAC,CACtB,CAEA,OAAOjB,CACT,CA3BgBJ,EAAAQ,GAAA,8BAqCT,SAASc,GACdC,EACAC,EACAC,EACAC,EACAC,EACAC,EACA,CACA,IAAMxB,EAAS,CAAC,EAGVyB,EAAiBF,EAAa,KAAK,GAAM,IAOzCG,GANeF,EAAW,KAAK,GAAM,IAGVC,IAGDH,EAAY,GAE5C,QAASnB,EAAI,EAAGA,EAAImB,EAAWnB,IAAK,CAClC,IAAMwB,EAAQF,EAAgBtB,EAAIuB,EAC5BT,EAAIE,EAAUE,EAAS,KAAK,IAAIM,CAAK,EACrCC,EAAIR,EAAUC,EAAS,KAAK,IAAIM,CAAK,EAC3C3B,EAAO,KAAK,CAAE,EAAG,CAACiB,EAAG,EAAG,CAACW,CAAE,CAAC,CAC9B,CAEA,OAAO5B,CACT,CA5BgBJ,EAAAsB,GAAA,wBC7PhB,SAASW,GAAEA,EAAEC,EAAEC,EAAE,CAAC,GAAGF,GAAGA,EAAE,OAAO,CAAC,GAAK,CAACG,EAAEC,CAAC,EAAEH,EAAE,EAAE,KAAK,GAAG,IAAIC,EAAE,EAAE,KAAK,IAAI,CAAC,EAAEG,EAAE,KAAK,IAAI,CAAC,EAAE,QAAUJ,KAAKD,EAAE,CAAC,GAAK,CAACA,EAAEE,CAAC,EAAED,EAAEA,EAAE,CAAC,GAAGD,EAAEG,GAAG,GAAGD,EAAEE,GAAGC,EAAEF,EAAEF,EAAE,CAAC,GAAGD,EAAEG,GAAGE,GAAGH,EAAEE,GAAG,EAAEA,CAAC,CAAC,CAAC,CAAhKE,EAAAN,GAAA,KAAiK,SAASC,GAAED,EAAEC,EAAE,CAAC,OAAOD,EAAE,CAAC,IAAIC,EAAE,CAAC,GAAGD,EAAE,CAAC,IAAIC,EAAE,CAAC,CAAC,CAAtCK,EAAAL,GAAA,KAAuC,SAASC,GAAEA,EAAEC,EAAE,EAAEI,EAAE,EAAE,CAAC,IAAMC,EAAE,EAAEH,EAAE,KAAK,IAAIF,EAAE,EAAE,EAAEM,EAAEP,EAAE,CAAC,GAAGA,EAAE,CAAC,EAAE,CAAC,GAAa,OAAOA,EAAE,CAAC,EAAE,CAAC,GAAvB,SAAyB,CAACA,CAAC,EAAEA,EAAEQ,EAAE,CAAC,EAAE,CAAC,EAAE,GAAGF,EAAE,QAAUP,KAAKQ,EAAET,GAAEC,EAAES,EAAEF,CAAC,EAAE,IAAMG,EAAE,SAASX,EAAEE,EAAEC,EAAE,CAAC,IAAMC,EAAE,CAAC,EAAE,QAAUF,KAAKF,EAAE,CAAC,IAAMA,EAAE,CAAC,GAAGE,CAAC,EAAED,GAAED,EAAE,CAAC,EAAEA,EAAEA,EAAE,OAAO,CAAC,CAAC,GAAGA,EAAE,KAAK,CAACA,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,OAAO,GAAGI,EAAE,KAAKJ,CAAC,CAAC,CAAC,IAAMO,EAAE,CAAC,EAAEL,EAAE,KAAK,IAAIA,EAAE,EAAE,EAAE,IAAMM,EAAE,CAAC,EAAE,QAAUR,KAAKI,EAAE,QAAQH,EAAE,EAAEA,EAAED,EAAE,OAAO,EAAEC,IAAI,CAAC,IAAMC,EAAEF,EAAEC,CAAC,EAAEE,EAAEH,EAAEC,EAAE,CAAC,EAAE,GAAGC,EAAE,CAAC,IAAIC,EAAE,CAAC,EAAE,CAAC,IAAMH,EAAE,KAAK,IAAIE,EAAE,CAAC,EAAEC,EAAE,CAAC,CAAC,EAAEK,EAAE,KAAK,CAAC,KAAKR,EAAE,KAAK,KAAK,IAAIE,EAAE,CAAC,EAAEC,EAAE,CAAC,CAAC,EAAE,EAAEH,IAAIE,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAEC,EAAE,CAAC,EAAE,QAAQA,EAAE,CAAC,EAAED,EAAE,CAAC,IAAIC,EAAE,CAAC,EAAED,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAGM,EAAE,KAAM,CAACR,EAAEC,IAAID,EAAE,KAAKC,EAAE,KAAK,GAAGD,EAAE,KAAKC,EAAE,KAAK,EAAED,EAAE,EAAEC,EAAE,EAAE,GAAGD,EAAE,EAAEC,EAAE,EAAE,EAAED,EAAE,OAAOC,EAAE,KAAK,GAAGD,EAAE,KAAKC,EAAE,MAAM,KAAK,IAAID,EAAE,KAAKC,EAAE,IAAI,CAAE,EAAE,CAACO,EAAE,OAAO,OAAOD,EAAE,IAAIF,EAAE,CAAC,EAAEI,EAAED,EAAE,CAAC,EAAE,KAAKE,EAAE,EAAE,KAAKL,EAAE,QAAQG,EAAE,QAAQ,CAAC,GAAGA,EAAE,OAAO,CAAC,IAAIR,EAAE,GAAG,QAAQC,EAAE,EAAEA,EAAEO,EAAE,QAAQ,EAAEA,EAAEP,CAAC,EAAE,KAAKQ,GAAGR,IAAID,EAAEC,EAAEO,EAAE,OAAO,EAAER,EAAE,CAAC,EAAE,QAASA,GAAG,CAACK,EAAE,KAAK,CAAC,EAAEI,EAAE,KAAKT,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC,GAAGK,EAAEA,EAAE,OAAQL,GAAG,EAAEA,EAAE,KAAK,MAAMS,EAAG,EAAEJ,EAAE,KAAM,CAACL,EAAEC,IAAID,EAAE,KAAK,IAAIC,EAAE,KAAK,EAAE,GAAGD,EAAE,KAAK,EAAEC,EAAE,KAAK,GAAG,KAAK,IAAID,EAAE,KAAK,EAAEC,EAAE,KAAK,CAAC,CAAE,GAAOE,IAAJ,GAAOO,EAAER,GAAG,IAAIG,EAAE,OAAO,EAAE,QAAQL,EAAE,EAAEA,EAAEK,EAAE,OAAOL,GAAG,EAAE,CAAC,IAAMC,EAAED,EAAE,EAAE,GAAGC,GAAGI,EAAE,OAAO,MAAM,IAAMH,EAAEG,EAAEL,CAAC,EAAE,KAAKG,EAAEE,EAAEJ,CAAC,EAAE,KAAKM,EAAE,KAAK,CAAC,CAAC,KAAK,MAAML,EAAE,CAAC,EAAEO,CAAC,EAAE,CAAC,KAAK,MAAMN,EAAE,CAAC,EAAEM,CAAC,CAAC,CAAC,CAAC,CAACA,GAAGN,EAAEE,EAAE,QAASL,GAAG,CAACA,EAAE,KAAK,EAAEA,EAAE,KAAK,EAAEG,EAAEH,EAAE,KAAK,MAAM,CAAE,EAAEU,GAAG,CAAC,OAAOH,CAAC,EAAEE,EAAEJ,EAAEE,CAAC,EAAE,GAAGC,EAAE,CAAC,QAAUP,KAAKQ,EAAET,GAAEC,EAAES,EAAE,CAACF,CAAC,GAAG,SAASP,EAAEC,EAAEC,EAAE,CAAC,IAAMC,EAAE,CAAC,EAAEH,EAAE,QAASD,GAAGI,EAAE,KAAK,GAAGJ,CAAC,CAAE,EAAEA,GAAEI,EAAEF,EAAEC,CAAC,CAAC,GAAEQ,EAAED,EAAE,CAACF,CAAC,CAAC,CAAC,OAAOG,CAAC,CAA9yCL,EAAAJ,GAAA,KAA+yC,SAASC,GAAEH,EAAEC,EAAE,CAAC,IAAIE,EAAE,IAAMC,EAAEH,EAAE,aAAa,GAAOM,EAAEN,EAAE,WAAWM,EAAE,IAAIA,EAAE,EAAEN,EAAE,aAAaM,EAAE,KAAK,MAAM,KAAK,IAAIA,EAAE,EAAE,CAAC,EAAE,IAAIC,EAAE,EAAE,OAAOP,EAAE,WAAW,MAAaE,EAAEF,EAAE,cAAZ,MAAkCE,IAAT,OAAW,OAAOA,EAAE,KAAK,IAAI,KAAK,OAAO,GAAG,KAAKK,EAAED,GAAGL,GAAEF,EAAEO,EAAEH,EAAEI,GAAG,CAAC,CAAC,CAA7OF,EAAAH,GAAA,KAA8O,IAAMC,GAAN,KAAO,CAAhxD,MAAgxD,CAAAE,EAAA,UAAC,YAAY,EAAE,CAAC,KAAK,OAAO,CAAC,CAAC,aAAa,EAAEL,EAAE,CAAC,OAAO,KAAK,cAAc,EAAEA,CAAC,CAAC,CAAC,cAAc,EAAEA,EAAE,CAAC,IAAMC,EAAEC,GAAE,EAAEF,CAAC,EAAE,MAAM,CAAC,KAAK,aAAa,IAAI,KAAK,YAAYC,EAAED,CAAC,CAAC,CAAC,CAAC,YAAY,EAAEA,EAAE,CAAC,IAAMC,EAAE,CAAC,EAAE,QAAUC,KAAK,EAAED,EAAE,KAAK,GAAG,KAAK,OAAO,cAAcC,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,CAAC,EAAEF,CAAC,CAAC,EAAE,OAAOC,CAAC,CAAC,EAAC,SAASK,GAAEP,EAAE,CAAC,IAAMC,EAAED,EAAE,CAAC,EAAEE,EAAEF,EAAE,CAAC,EAAE,OAAO,KAAK,KAAK,KAAK,IAAIC,EAAE,CAAC,EAAEC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,IAAID,EAAE,CAAC,EAAEC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAtFI,EAAAC,GAAA,KAAuF,IAAMC,GAAN,cAAgBJ,EAAC,CAArqE,MAAqqE,CAAAE,EAAA,UAAC,aAAa,EAAEL,EAAE,CAAC,IAAIC,EAAED,EAAE,WAAWC,EAAE,IAAIA,EAAE,EAAED,EAAE,aAAaC,EAAE,KAAK,IAAIA,EAAE,EAAE,EAAE,IAAME,EAAED,GAAE,EAAE,OAAO,OAAO,CAAC,EAAEF,EAAE,CAAC,WAAWC,CAAC,CAAC,CAAC,EAAEM,EAAE,KAAK,GAAG,IAAIP,EAAE,aAAaI,EAAE,CAAC,EAAEI,EAAE,GAAGP,EAAE,KAAK,IAAIM,CAAC,EAAE,EAAE,GAAGN,EAAE,KAAK,IAAIM,CAAC,EAAE,OAAS,CAACR,EAAEC,CAAC,IAAIG,EAAEG,GAAE,CAACP,EAAEC,CAAC,CAAC,GAAGI,EAAE,KAAK,CAAC,CAACL,EAAE,CAAC,EAAES,EAAET,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,CAAC,CAAC,EAAE,CAAC,CAACD,EAAE,CAAC,EAAES,EAAET,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,aAAa,IAAI,KAAK,YAAYI,EAAEJ,CAAC,CAAC,CAAC,CAAC,EAAOI,GAAN,cAAgBD,EAAC,CAA1gF,MAA0gF,CAAAE,EAAA,UAAC,aAAa,EAAEL,EAAE,CAAC,IAAMC,EAAE,KAAK,cAAc,EAAED,CAAC,EAAEE,EAAE,OAAO,OAAO,CAAC,EAAEF,EAAE,CAAC,aAAaA,EAAE,aAAa,EAAE,CAAC,EAAEG,EAAE,KAAK,cAAc,EAAED,CAAC,EAAE,OAAOD,EAAE,IAAIA,EAAE,IAAI,OAAOE,EAAE,GAAG,EAAEF,CAAC,CAAC,EAAOO,GAAN,KAAO,CAAzrF,MAAyrF,CAAAH,EAAA,UAAC,YAAY,EAAE,CAAC,KAAK,OAAO,CAAC,CAAC,aAAa,EAAEL,EAAE,CAAC,IAAMC,EAAEC,GAAE,EAAEF,EAAE,OAAO,OAAO,CAAC,EAAEA,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK,YAAYC,EAAED,CAAC,CAAC,CAAC,YAAY,EAAEA,EAAE,CAAC,IAAMC,EAAE,CAAC,EAAMC,EAAEF,EAAE,WAAWE,EAAE,IAAIA,EAAE,EAAEF,EAAE,aAAaE,EAAE,KAAK,IAAIA,EAAE,EAAE,EAAE,IAAIC,EAAEH,EAAE,WAAWG,EAAE,IAAIA,EAAEH,EAAE,YAAY,GAAG,IAAM,EAAEE,EAAE,EAAE,QAAUE,KAAK,EAAE,CAAC,IAAML,EAAEO,GAAEF,CAAC,EAAE,EAAEL,EAAEG,EAAEO,EAAE,KAAK,KAAK,CAAC,EAAE,EAAEC,EAAEX,EAAEU,EAAEP,EAAES,GAAGP,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,CAAC,GAAG,EAAEF,EAAE,EAAEU,EAAE,KAAK,IAAIR,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQL,EAAE,EAAEA,EAAEU,EAAEV,IAAI,CAAC,IAAMO,EAAEM,EAAEF,EAAEX,EAAEG,EAAEE,EAAEO,EAAE,EAAE,EAAE,KAAK,OAAO,EAAE,EAAEH,EAAEF,EAAE,EAAE,EAAE,KAAK,OAAO,EAAE,EAAEG,EAAE,KAAK,OAAO,QAAQL,EAAEI,EAAEL,EAAEA,EAAEH,CAAC,EAAEC,EAAE,KAAK,GAAGQ,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,aAAa,IAAIR,CAAC,CAAC,CAAC,EAAOQ,GAAN,KAAO,CAA/uG,MAA+uG,CAAAJ,EAAA,UAAC,YAAY,EAAE,CAAC,KAAK,OAAO,CAAC,CAAC,aAAa,EAAEL,EAAE,CAAC,IAAMC,EAAEC,GAAE,EAAEF,CAAC,EAAE,MAAM,CAAC,KAAK,aAAa,IAAI,KAAK,WAAWC,EAAED,CAAC,CAAC,CAAC,CAAC,WAAW,EAAEA,EAAE,CAAC,IAAMC,EAAED,EAAE,WAAW,EAAEA,EAAE,WAAW,EAAE,EAAEA,EAAE,YAAYA,EAAE,WAAWA,EAAE,WAAWE,EAAEF,EAAE,QAAQ,EAAEA,EAAE,WAAW,EAAE,EAAEA,EAAE,YAAYA,EAAE,WAAWA,EAAE,QAAQG,EAAE,CAAC,EAAE,OAAO,EAAE,QAASJ,GAAG,CAAC,IAAMQ,EAAED,GAAEP,CAAC,EAAEK,EAAE,KAAK,MAAMG,GAAGN,EAAEC,EAAE,EAAE,GAAGK,EAAEL,EAAEE,GAAGH,EAAEC,IAAI,EAAMO,EAAEV,EAAE,CAAC,EAAEW,EAAEX,EAAE,CAAC,EAAEU,EAAE,CAAC,EAAEC,EAAE,CAAC,IAAID,EAAEV,EAAE,CAAC,EAAEW,EAAEX,EAAE,CAAC,GAAG,IAAMY,EAAE,KAAK,MAAMD,EAAE,CAAC,EAAED,EAAE,CAAC,IAAIC,EAAE,CAAC,EAAED,EAAE,CAAC,EAAE,EAAE,QAAQV,EAAE,EAAEA,EAAEK,EAAEL,IAAI,CAAC,IAAMO,EAAEP,GAAGE,EAAEC,GAAGK,EAAED,EAAEL,EAAEG,EAAE,CAACK,EAAE,CAAC,EAAEH,EAAE,KAAK,IAAIK,CAAC,EAAE,EAAE,KAAK,IAAIA,CAAC,EAAEF,EAAE,CAAC,EAAEH,EAAE,KAAK,IAAIK,CAAC,EAAE,EAAE,KAAK,IAAIA,CAAC,CAAC,EAAED,EAAE,CAACD,EAAE,CAAC,EAAEF,EAAE,KAAK,IAAII,CAAC,EAAE,EAAE,KAAK,IAAIA,CAAC,EAAEF,EAAE,CAAC,EAAEF,EAAE,KAAK,IAAII,CAAC,EAAE,EAAE,KAAK,IAAIA,CAAC,CAAC,EAAER,EAAE,KAAK,GAAG,KAAK,OAAO,cAAcC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAEM,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAEV,CAAC,CAAC,CAAC,CAAC,CAAE,EAAEG,CAAC,CAAC,EAAOO,GAAN,KAAO,CAA96H,MAA86H,CAAAL,EAAA,UAAC,YAAY,EAAE,CAAC,KAAK,OAAO,CAAC,CAAC,aAAa,EAAEL,EAAE,CAAC,IAAMC,EAAED,EAAE,WAAW,EAAE,EAAEA,EAAE,YAAYA,EAAE,WAAWG,EAAEH,EAAE,aAAa,EAAEC,EAAED,EAAE,aAAa,EAAEE,GAAE,EAAEF,EAAE,OAAO,OAAO,CAAC,EAAEA,EAAE,CAAC,WAAWC,EAAEE,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,aAAa,IAAI,KAAK,YAAY,EAAEA,EAAEH,CAAC,CAAC,CAAC,CAAC,YAAY,EAAEA,EAAEC,EAAE,CAAC,IAAMC,EAAE,CAAC,EAAE,OAAO,EAAE,QAASH,GAAG,CAAC,IAAMI,EAAEG,GAAEP,CAAC,EAAEQ,EAAE,KAAK,MAAMJ,GAAG,EAAEH,EAAE,EAAMI,EAAEL,EAAE,CAAC,EAAE,EAAEA,EAAE,CAAC,EAAEK,EAAE,CAAC,EAAE,EAAE,CAAC,IAAIA,EAAEL,EAAE,CAAC,EAAE,EAAEA,EAAE,CAAC,GAAG,IAAMU,EAAE,KAAK,MAAM,EAAE,CAAC,EAAEL,EAAE,CAAC,IAAI,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,EAAE,QAAQL,EAAE,EAAEA,EAAEQ,EAAER,IAAI,CAAC,IAAMI,EAAE,EAAEJ,EAAEC,EAAEM,EAAE,GAAGP,EAAE,GAAGC,EAAEO,EAAE,KAAK,KAAK,EAAE,KAAK,IAAIP,EAAE,CAAC,CAAC,EAAEQ,EAAE,CAACJ,EAAE,CAAC,EAAED,EAAE,KAAK,IAAIM,CAAC,EAAEL,EAAE,CAAC,EAAED,EAAE,KAAK,IAAIM,CAAC,CAAC,EAAEC,EAAE,CAACN,EAAE,CAAC,EAAEE,EAAE,KAAK,IAAIG,CAAC,EAAEL,EAAE,CAAC,EAAEE,EAAE,KAAK,IAAIG,CAAC,CAAC,EAAEE,EAAE,CAACH,EAAE,CAAC,EAAED,EAAE,KAAK,IAAIE,EAAE,KAAK,GAAG,CAAC,EAAED,EAAE,CAAC,EAAED,EAAE,KAAK,IAAIE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAEP,EAAE,KAAK,GAAG,KAAK,OAAO,cAAcM,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAEG,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAEV,CAAC,EAAE,GAAG,KAAK,OAAO,cAAcU,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAED,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAET,CAAC,CAAC,CAAC,CAAC,CAAE,EAAEC,CAAC,CAAC,EAAOS,GAAE,CAAC,EAAQC,GAAN,KAAO,CAAhrJ,MAAgrJ,CAAAP,EAAA,UAAC,YAAY,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,KAAK,MAAM,GAAG,GAAG,GAAG,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,IAAI,IAAI,GAAG,GAAG,KAAK,OAAO,CAAC,CAAC,EAAOQ,GAAE,EAAEC,GAAE,EAAEC,GAAE,EAAEC,GAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,SAASC,GAAElB,EAAEC,EAAE,CAAC,OAAOD,EAAE,OAAOC,CAAC,CAAxBK,EAAAY,GAAA,KAAyB,SAASC,GAAEnB,EAAE,CAAC,IAAMC,EAAE,CAAC,EAAEC,EAAE,SAASF,EAAE,CAAC,IAAMC,EAAE,IAAI,MAAM,KAAUD,IAAL,IAAQ,GAAGA,EAAE,MAAM,gBAAgB,EAAEA,EAAEA,EAAE,OAAO,OAAO,GAAG,MAAM,UAAUA,EAAE,MAAM,2BAA2B,EAAEC,EAAEA,EAAE,MAAM,EAAE,CAAC,KAAKa,GAAE,KAAK,OAAO,EAAE,EAAEd,EAAEA,EAAE,OAAO,OAAO,GAAG,MAAM,MAAM,CAAC,GAAG,CAACA,EAAE,MAAM,6DAA6D,EAAE,MAAM,CAAC,EAAEC,EAAEA,EAAE,MAAM,EAAE,CAAC,KAAKc,GAAE,KAAK,GAAG,WAAW,OAAO,EAAE,CAAC,EAAE,EAAEf,EAAEA,EAAE,OAAO,OAAO,GAAG,MAAM,CAAC,CAAC,OAAOC,EAAEA,EAAE,MAAM,EAAE,CAAC,KAAKe,GAAE,KAAK,EAAE,EAAEf,CAAC,EAAED,CAAC,EAAMG,EAAE,MAAMC,EAAE,EAAE,EAAEF,EAAEE,CAAC,EAAE,KAAK,CAACc,GAAE,EAAEF,EAAC,GAAG,CAAC,IAAI,EAAE,EAAQX,EAAE,CAAC,EAAE,GAAWF,IAAR,MAAU,CAAC,GAAS,EAAE,OAAR,KAAoB,EAAE,OAAR,IAAa,OAAOgB,GAAE,OAAOnB,CAAC,EAAEI,IAAI,EAAEa,GAAE,EAAE,IAAI,EAAEd,EAAE,EAAE,IAAI,MAAMe,GAAE,EAAEH,EAAC,EAAE,EAAEE,GAAEd,CAAC,GAAGC,IAAI,EAAEa,GAAE,EAAE,IAAI,EAAEd,EAAE,EAAE,MAAM,GAAG,EAAEC,EAAE,EAAEF,EAAE,QAAQ,MAAM,IAAI,MAAM,uBAAuB,EAAE,QAAQF,EAAEI,EAAEJ,EAAEI,EAAE,EAAEJ,IAAI,CAAC,IAAMC,EAAEC,EAAEF,CAAC,EAAE,GAAG,CAACkB,GAAEjB,EAAEc,EAAC,EAAE,MAAM,IAAI,MAAM,uBAAuBZ,EAAE,IAAIF,EAAE,IAAI,EAAEI,EAAEA,EAAE,MAAM,EAAE,CAACJ,EAAE,IAAI,CAAC,GAAa,OAAOgB,GAAEd,CAAC,GAApB,SAAsB,MAAM,IAAI,MAAM,gBAAgBA,CAAC,EAAE,CAAC,IAAMH,EAAE,CAAC,IAAIG,EAAE,KAAKE,CAAC,EAAEJ,EAAE,KAAKD,CAAC,EAAEI,GAAG,EAAE,EAAEF,EAAEE,CAAC,EAAQD,IAAN,MAAUA,EAAE,KAAWA,IAAN,MAAUA,EAAE,IAAI,CAAC,CAAC,OAAOF,CAAC,CAAl8BK,EAAAa,GAAA,KAAm8B,SAASC,GAAEpB,EAAE,CAAC,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAQ,EAAE,CAAC,EAAE,OAAS,CAAC,IAAI,EAAE,KAAKC,CAAC,IAAIL,EAAE,OAAO,EAAE,CAAC,IAAI,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,GAAGK,CAAC,CAAC,CAAC,EAAE,CAACJ,EAAEC,CAAC,EAAEG,EAAE,CAACF,EAAEC,CAAC,EAAEC,EAAE,MAAM,IAAI,IAAIJ,GAAGI,EAAE,CAAC,EAAEH,GAAGG,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAACJ,EAAEC,CAAC,CAAC,CAAC,EAAEC,EAAEF,EAAEG,EAAEF,EAAE,MAAM,IAAI,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,GAAGG,CAAC,CAAC,CAAC,EAAE,CAACJ,EAAEC,CAAC,EAAEG,EAAE,MAAM,IAAI,IAAIJ,GAAGI,EAAE,CAAC,EAAEH,GAAGG,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAACJ,EAAEC,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,GAAGG,CAAC,CAAC,CAAC,EAAEJ,EAAEI,EAAE,CAAC,EAAEH,EAAEG,EAAE,CAAC,EAAE,MAAM,IAAI,IAAI,CAAC,IAAML,EAAEK,EAAE,IAAK,CAACL,EAAE,IAAI,EAAE,EAAEA,EAAEE,EAAEF,EAAEC,CAAE,EAAE,EAAE,KAAK,CAAC,IAAI,IAAI,KAAKD,CAAC,CAAC,EAAEC,EAAED,EAAE,CAAC,EAAEE,EAAEF,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,GAAGK,CAAC,CAAC,CAAC,EAAEJ,EAAEI,EAAE,CAAC,EAAEH,EAAEG,EAAE,CAAC,EAAE,MAAM,IAAI,IAAI,CAAC,IAAML,EAAEK,EAAE,IAAK,CAACL,EAAE,IAAI,EAAE,EAAEA,EAAEE,EAAEF,EAAEC,CAAE,EAAE,EAAE,KAAK,CAAC,IAAI,IAAI,KAAKD,CAAC,CAAC,EAAEC,EAAED,EAAE,CAAC,EAAEE,EAAEF,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,GAAGK,CAAC,CAAC,CAAC,EAAEJ,EAAEI,EAAE,CAAC,EAAEH,EAAEG,EAAE,CAAC,EAAE,MAAM,IAAI,IAAIJ,GAAGI,EAAE,CAAC,EAAEH,GAAGG,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAACA,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAEJ,EAAEC,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,GAAGG,CAAC,CAAC,CAAC,EAAEJ,EAAEI,EAAE,CAAC,EAAE,MAAM,IAAI,IAAIJ,GAAGI,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAACJ,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,GAAGI,CAAC,CAAC,CAAC,EAAEH,EAAEG,EAAE,CAAC,EAAE,MAAM,IAAI,IAAIH,GAAGG,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAACH,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,GAAGG,CAAC,CAAC,CAAC,EAAEJ,EAAEI,EAAE,CAAC,EAAEH,EAAEG,EAAE,CAAC,EAAE,MAAM,IAAI,IAAI,CAAC,IAAML,EAAEK,EAAE,IAAK,CAACL,EAAE,IAAI,EAAE,EAAEA,EAAEE,EAAEF,EAAEC,CAAE,EAAE,EAAE,KAAK,CAAC,IAAI,IAAI,KAAKD,CAAC,CAAC,EAAEC,EAAED,EAAE,CAAC,EAAEE,EAAEF,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,GAAGK,CAAC,CAAC,CAAC,EAAEJ,EAAEI,EAAE,CAAC,EAAEH,EAAEG,EAAE,CAAC,EAAE,MAAM,IAAI,IAAIJ,GAAGI,EAAE,CAAC,EAAEH,GAAGG,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAACJ,EAAEC,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,IAAI,IAAI,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,EAAED,EAAEE,EAAED,EAAEE,CAAC,CAAC,OAAO,CAAC,CAAzvCE,EAAAc,GAAA,KAA0vC,SAASC,GAAErB,EAAE,CAAC,IAAMC,EAAE,CAAC,EAAMC,EAAE,GAAGC,EAAE,EAAEC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAEC,EAAE,EAAEI,EAAE,EAAE,OAAS,CAAC,IAAIC,EAAE,KAAKC,CAAC,IAAIX,EAAE,CAAC,OAAOU,EAAE,CAAC,IAAI,IAAIT,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,GAAGU,CAAC,CAAC,CAAC,EAAE,CAACR,EAAEC,CAAC,EAAEO,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,MAAM,IAAI,IAAIV,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,GAAGU,CAAC,CAAC,CAAC,EAAER,EAAEQ,EAAE,CAAC,EAAEP,EAAEO,EAAE,CAAC,EAAEN,EAAEM,EAAE,CAAC,EAAEF,EAAEE,EAAE,CAAC,EAAE,MAAM,IAAI,IAAIV,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,GAAGU,CAAC,CAAC,CAAC,EAAE,CAACR,EAAEC,CAAC,EAAEO,EAAE,MAAM,IAAI,IAAIR,EAAEQ,EAAE,CAAC,EAAEV,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAACE,EAAEC,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,IAAIA,EAAEO,EAAE,CAAC,EAAEV,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAACE,EAAEC,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,IAAI,CAAC,IAAIJ,EAAE,EAAEO,EAAE,EAAQL,IAAN,KAAeA,IAAN,KAASF,EAAEG,GAAGA,EAAEE,GAAGE,EAAEH,GAAGA,EAAEK,KAAKT,EAAEG,EAAEI,EAAEH,GAAGH,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAACD,EAAEO,EAAE,GAAGI,CAAC,CAAC,CAAC,EAAEN,EAAEM,EAAE,CAAC,EAAEF,EAAEE,EAAE,CAAC,EAAER,EAAEQ,EAAE,CAAC,EAAEP,EAAEO,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,GAAK,CAACX,EAAEO,CAAC,EAAEI,EAAMH,EAAE,EAAEE,EAAE,EAAQR,IAAN,KAAeA,IAAN,KAASM,EAAEL,GAAGA,EAAEE,GAAGK,EAAEN,GAAGA,EAAEK,KAAKD,EAAEL,EAAEO,EAAEN,GAAG,IAAMQ,EAAET,EAAE,GAAGK,EAAEL,GAAG,EAAEU,EAAET,EAAE,GAAGM,EAAEN,GAAG,EAAEU,EAAEd,EAAE,GAAGQ,EAAER,GAAG,EAAEe,EAAER,EAAE,GAAGG,EAAEH,GAAG,EAAEN,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAACW,EAAEC,EAAEC,EAAEC,EAAEf,EAAEO,CAAC,CAAC,CAAC,EAAEF,EAAEG,EAAEC,EAAEC,EAAEP,EAAEH,EAAEI,EAAEG,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,GAAK,CAACP,EAAEE,EAAEK,EAAEC,CAAC,EAAEG,EAAED,EAAEP,EAAE,GAAGH,EAAEG,GAAG,EAAES,EAAER,EAAE,GAAGF,EAAEE,GAAG,EAAES,EAAEN,EAAE,GAAGP,EAAEO,GAAG,EAAEO,EAAEN,EAAE,GAAGN,EAAEM,GAAG,EAAEP,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAACS,EAAEE,EAAEC,EAAEC,EAAEP,EAAEC,CAAC,CAAC,CAAC,EAAEH,EAAEL,EAAES,EAAEP,EAAEC,EAAEI,EAAEH,EAAEI,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,IAAMR,EAAE,KAAK,IAAIW,EAAE,CAAC,CAAC,EAAET,EAAE,KAAK,IAAIS,EAAE,CAAC,CAAC,EAAEJ,EAAEI,EAAE,CAAC,EAAEH,EAAEG,EAAE,CAAC,EAAEN,EAAEM,EAAE,CAAC,EAAEF,EAAEE,EAAE,CAAC,EAAED,EAAEC,EAAE,CAAC,EAASX,IAAJ,GAAWE,IAAJ,GAAMD,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAACE,EAAEC,EAAEK,EAAEC,EAAED,EAAEC,CAAC,CAAC,CAAC,EAAEP,EAAEM,EAAEL,EAAEM,IAAUP,IAAIM,GAAGL,IAAIM,KAAGY,GAAEnB,EAAEC,EAAEK,EAAEC,EAAEV,EAAEE,EAAEK,EAAEC,EAAEH,CAAC,EAAE,QAAS,SAASL,EAAE,CAACC,EAAE,KAAK,CAAC,IAAI,IAAI,KAAKD,CAAC,CAAC,CAAC,CAAE,EAAEG,EAAEM,EAAEL,EAAEM,GAAE,KAAK,CAAC,IAAI,IAAIT,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,EAAEE,EAAE,EAAEC,EAAE,CAAC,CAACF,EAAEQ,CAAC,CAAC,OAAOT,CAAC,CAAxoCK,EAAAe,GAAA,KAAyoC,SAASE,GAAEvB,EAAEC,EAAEC,EAAE,CAAC,MAAM,CAACF,EAAE,KAAK,IAAIE,CAAC,EAAED,EAAE,KAAK,IAAIC,CAAC,EAAEF,EAAE,KAAK,IAAIE,CAAC,EAAED,EAAE,KAAK,IAAIC,CAAC,CAAC,CAAC,CAAxEI,EAAAiB,GAAA,KAAyE,SAASD,GAAEtB,EAAEC,EAAEC,EAAEC,EAAEC,EAAE,EAAE,EAAEC,EAAEI,EAAEC,EAAE,CAAC,IAAMC,GAAGC,EAAE,EAAE,KAAK,GAAGA,EAAE,KAAK,IAAIA,EAAE,IAAIC,EAAE,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE,GAAGP,EAAE,CAACI,EAAEC,EAAEC,EAAEC,CAAC,EAAEP,MAAM,CAAC,CAACV,EAAEC,CAAC,EAAEsB,GAAEvB,EAAEC,EAAE,CAACU,CAAC,EAAE,CAACT,EAAEC,CAAC,EAAEoB,GAAErB,EAAEC,EAAE,CAACQ,CAAC,EAAE,IAAMH,GAAGR,EAAEE,GAAG,EAAEQ,GAAGT,EAAEE,GAAG,EAAMS,EAAEJ,EAAEA,GAAGJ,EAAEA,GAAGM,EAAEA,GAAG,EAAE,GAAGE,EAAE,IAAIA,EAAE,KAAK,KAAKA,CAAC,EAAER,GAAGQ,EAAE,GAAGA,GAAG,IAAMC,EAAET,EAAEA,EAAEc,EAAE,EAAE,EAAEC,EAAEN,EAAEK,EAAEL,EAAEH,EAAEA,EAAEQ,EAAEV,EAAEA,EAAEY,EAAEP,EAAEH,EAAEA,EAAEQ,EAAEV,EAAEA,EAAEa,GAAGhB,IAAII,EAAE,GAAG,GAAG,KAAK,KAAK,KAAK,IAAIU,EAAEC,CAAC,CAAC,EAAEJ,EAAEK,EAAEjB,EAAEM,EAAE,GAAGV,EAAEE,GAAG,EAAEe,EAAEI,EAAE,CAAC,EAAEb,EAAEJ,GAAGH,EAAEE,GAAG,EAAEW,EAAE,KAAK,KAAK,aAAab,EAAEgB,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAEF,EAAE,KAAK,KAAK,aAAaZ,EAAEc,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAEjB,EAAEgB,IAAIF,EAAE,KAAK,GAAGA,GAAGZ,EAAEc,IAAID,EAAE,KAAK,GAAGA,GAAGD,EAAE,IAAIA,EAAE,EAAE,KAAK,GAAGA,GAAGC,EAAE,IAAIA,EAAE,EAAE,KAAK,GAAGA,GAAGN,GAAGK,EAAEC,IAAID,GAAG,EAAE,KAAK,IAAI,CAACL,GAAGM,EAAED,IAAIC,GAAG,EAAE,KAAK,GAAG,CAAC,IAAIG,EAAEH,EAAED,EAAE,GAAG,KAAK,IAAII,CAAC,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,IAAMlB,EAAEe,EAAEd,EAAEC,EAAEG,EAAEF,EAAEY,EAAEN,GAAGM,EAAED,EAAEA,EAAE,IAAI,KAAK,GAAG,IAAI,EAAEA,EAAE,IAAI,KAAK,GAAG,IAAI,GAAGD,EAAES,GAAEpB,EAAEc,EAAEZ,EAAE,KAAK,IAAIW,CAAC,EAAEZ,EAAEc,EAAE,EAAE,KAAK,IAAIF,CAAC,EAAEd,EAAEI,EAAED,EAAE,EAAE,EAAE,EAAEK,EAAE,CAACM,EAAEf,EAAEgB,EAAEC,CAAC,CAAC,CAAC,CAACC,EAAEH,EAAED,EAAE,IAAM,EAAE,KAAK,IAAIA,CAAC,EAAEM,EAAE,KAAK,IAAIN,CAAC,EAAEO,EAAE,KAAK,IAAIN,CAAC,EAAES,EAAE,KAAK,IAAIT,CAAC,EAAEU,EAAE,KAAK,IAAIP,EAAE,CAAC,EAAEQ,EAAE,EAAE,EAAEtB,EAAEqB,EAAEE,EAAE,EAAE,EAAE,EAAEF,EAAEG,EAAE,CAAC5B,EAAEC,CAAC,EAAE4B,EAAE,CAAC7B,EAAE0B,EAAEN,EAAEnB,EAAE0B,EAAE,CAAC,EAAEG,EAAE,CAAC5B,EAAEwB,EAAEF,EAAErB,EAAEwB,EAAEN,CAAC,EAAEU,EAAE,CAAC7B,EAAEC,CAAC,EAAE,GAAG0B,EAAE,CAAC,EAAE,EAAED,EAAE,CAAC,EAAEC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,EAAED,EAAE,CAAC,EAAEC,EAAE,CAAC,EAAEnB,EAAE,MAAM,CAACmB,EAAEC,EAAEC,CAAC,EAAE,OAAOlB,CAAC,EAAE,CAACA,EAAE,CAACgB,EAAEC,EAAEC,CAAC,EAAE,OAAOlB,CAAC,EAAE,IAAMb,EAAE,CAAC,EAAE,QAAQC,EAAE,EAAEA,EAAEY,EAAE,OAAOZ,GAAG,EAAE,CAAC,IAAMC,EAAEqB,GAAEV,EAAEZ,CAAC,EAAE,CAAC,EAAEY,EAAEZ,CAAC,EAAE,CAAC,EAAEU,CAAC,EAAER,EAAEoB,GAAEV,EAAEZ,EAAE,CAAC,EAAE,CAAC,EAAEY,EAAEZ,EAAE,CAAC,EAAE,CAAC,EAAEU,CAAC,EAAEP,EAAEmB,GAAEV,EAAEZ,EAAE,CAAC,EAAE,CAAC,EAAEY,EAAEZ,EAAE,CAAC,EAAE,CAAC,EAAEU,CAAC,EAAEX,EAAE,KAAK,CAACE,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAEC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAEC,EAAE,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAOJ,CAAC,CAAC,CAA7nCM,EAAAgB,GAAA,KAA8nC,IAAME,GAAE,CAAC,WAAWlB,EAAA,SAASN,EAAEC,EAAE,CAAC,OAAO+B,EAAEhC,EAAEC,CAAC,CAAC,EAA3B,cAA6B,oBAAoBK,EAAA,SAASN,EAAEC,EAAEC,EAAE,CAAC,OAAO+B,GAAEjC,EAAEC,EAAEC,CAAC,CAAC,EAA/B,uBAAiC,QAAQI,EAAA,SAASN,EAAEC,EAAEC,EAAEC,EAAEC,EAAE,CAAC,IAAM,EAAEyB,GAAE3B,EAAEC,EAAEC,CAAC,EAAE,OAAO0B,GAAE9B,EAAEC,EAAEG,EAAE,CAAC,EAAE,KAAK,EAA5D,WAA8D,cAAcE,EAAA,SAASN,EAAEC,EAAEC,EAAEC,EAAEC,EAAE,CAAC,OAAO8B,GAAElC,EAAEC,EAAEC,EAAEC,EAAEC,EAAE,EAAE,CAAC,EAA1C,gBAA2C,EAAE,SAASqB,GAAEzB,EAAEC,EAAEC,EAAEC,EAAEC,EAAE,CAAC,MAAM,CAAC,KAAK,OAAO,IAAI8B,GAAElC,EAAEC,EAAEC,EAAEC,EAAEC,CAAC,CAAC,CAAC,CAAjDE,EAAAmB,GAAA,KAAkD,SAASC,GAAE1B,EAAEC,EAAEC,EAAE,CAAC,IAAMC,GAAGH,GAAG,CAAC,GAAG,OAAO,GAAGG,EAAE,EAAE,CAAC,IAAMC,EAAE,CAAC,EAAE,QAAQH,EAAE,EAAEA,EAAEE,EAAE,EAAEF,IAAIG,EAAE,KAAK,GAAG8B,GAAElC,EAAEC,CAAC,EAAE,CAAC,EAAED,EAAEC,CAAC,EAAE,CAAC,EAAED,EAAEC,EAAE,CAAC,EAAE,CAAC,EAAED,EAAEC,EAAE,CAAC,EAAE,CAAC,EAAEC,CAAC,CAAC,EAAE,OAAOD,GAAGG,EAAE,KAAK,GAAG8B,GAAElC,EAAEG,EAAE,CAAC,EAAE,CAAC,EAAEH,EAAEG,EAAE,CAAC,EAAE,CAAC,EAAEH,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,CAAC,EAAEE,CAAC,CAAC,EAAE,CAAC,KAAK,OAAO,IAAIE,CAAC,CAAC,CAAC,OAAWD,IAAJ,EAAMsB,GAAEzB,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,CAAC,EAAEE,CAAC,EAAE,CAAC,KAAK,OAAO,IAAI,CAAC,CAAC,CAAC,CAArRI,EAAAoB,GAAA,KAAsR,SAASC,GAAE3B,EAAEC,EAAEC,EAAEC,EAAEC,EAAE,CAAC,OAAO,SAASJ,EAAEC,EAAE,CAAC,OAAOyB,GAAE1B,EAAE,GAAGC,CAAC,CAAC,EAAE,CAAC,CAACD,EAAEC,CAAC,EAAE,CAACD,EAAEE,EAAED,CAAC,EAAE,CAACD,EAAEE,EAAED,EAAEE,CAAC,EAAE,CAACH,EAAEC,EAAEE,CAAC,CAAC,EAAEC,CAAC,CAAC,CAAxFE,EAAAqB,GAAA,KAAyF,SAASC,GAAE5B,EAAEC,EAAE,CAAC,GAAGD,EAAE,OAAO,CAAC,IAAME,EAAY,OAAOF,EAAE,CAAC,EAAE,CAAC,GAAvB,SAAyB,CAACA,CAAC,EAAEA,EAAEG,EAAEgC,GAAEjC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAGD,EAAE,WAAWA,CAAC,EAAEG,EAAEH,EAAE,mBAAmB,CAAC,EAAEkC,GAAEjC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAID,EAAE,WAAWmC,GAAEnC,CAAC,CAAC,EAAE,QAAQD,EAAE,EAAEA,EAAEE,EAAE,OAAOF,IAAI,CAAC,IAAMO,EAAEL,EAAEF,CAAC,EAAE,GAAGO,EAAE,OAAO,CAAC,IAAMP,EAAEmC,GAAE5B,EAAE,GAAG,EAAE,GAAGN,EAAE,WAAWA,CAAC,EAAEC,EAAED,EAAE,mBAAmB,CAAC,EAAEkC,GAAE5B,EAAE,KAAK,EAAE,IAAIN,EAAE,WAAWmC,GAAEnC,CAAC,CAAC,EAAE,QAAUA,KAAKD,EAAWC,EAAE,KAAX,QAAeE,EAAE,KAAKF,CAAC,EAAE,QAAUD,KAAKE,EAAWF,EAAE,KAAX,QAAeI,EAAE,KAAKJ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,OAAO,IAAIG,EAAE,OAAOC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,OAAO,IAAI,CAAC,CAAC,CAAC,CAApcE,EAAAsB,GAAA,KAAqc,SAASC,GAAE7B,EAAEC,EAAEC,EAAE,CAAC,IAAMC,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG,KAAK,MAAM,KAAK,IAAIH,EAAE,EAAE,CAAC,EAAE,KAAK,IAAIC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,EAAEG,EAAE,KAAK,KAAK,KAAK,IAAIF,EAAE,eAAeA,EAAE,eAAe,KAAK,KAAK,GAAG,EAAEC,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,GAAGC,EAAM,EAAE,KAAK,IAAIJ,EAAE,CAAC,EAAEK,EAAE,KAAK,IAAIJ,EAAE,CAAC,EAAQQ,EAAE,EAAEP,EAAE,aAAa,OAAO,GAAG8B,EAAE,EAAEvB,EAAEP,CAAC,EAAEG,GAAG2B,EAAE3B,EAAEI,EAAEP,CAAC,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE,GAAGG,CAAC,CAAC,CAAhSC,EAAAuB,GAAA,KAAiS,SAASC,GAAE9B,EAAEC,EAAEC,EAAEC,EAAE,CAAC,GAAK,CAACC,EAAE,CAAC,EAAEiC,GAAElC,EAAE,UAAUH,EAAEC,EAAEE,EAAE,GAAGA,EAAE,GAAG,EAAEA,EAAE,UAAU8B,GAAE,GAAGA,GAAE,GAAG,EAAE/B,CAAC,EAAEA,CAAC,EAAEA,CAAC,EAAM,EAAEoC,GAAElC,EAAE,KAAKF,CAAC,EAAE,GAAG,CAACA,EAAE,oBAAwBA,EAAE,YAAN,EAAgB,CAAC,GAAK,CAACE,CAAC,EAAEiC,GAAElC,EAAE,UAAUH,EAAEC,EAAEE,EAAE,GAAGA,EAAE,GAAG,IAAI,EAAED,CAAC,EAAEK,EAAE+B,GAAElC,EAAE,KAAKF,CAAC,EAAE,EAAE,EAAE,OAAOK,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,EAAE,MAAM,CAAC,KAAK,OAAO,IAAI,CAAC,CAAC,CAAC,CAA/QD,EAAAwB,GAAA,KAAgR,SAASC,GAAE/B,EAAEC,EAAEC,EAAEC,EAAEC,EAAE,EAAE,EAAEC,EAAEI,EAAE,CAAC,IAAMC,EAAEV,EAAEW,EAAEV,EAAMW,EAAE,KAAK,IAAIV,EAAE,CAAC,EAAEW,EAAE,KAAK,IAAIV,EAAE,CAAC,EAAES,GAAGoB,EAAE,IAAIpB,EAAEH,CAAC,EAAEI,GAAGmB,EAAE,IAAInB,EAAEJ,CAAC,EAAE,IAAIK,EAAEV,EAAEW,EAAE,EAAE,KAAKD,EAAE,GAAGA,GAAG,EAAE,KAAK,GAAGC,GAAG,EAAE,KAAK,GAAGA,EAAED,EAAE,EAAE,KAAK,KAAKA,EAAE,EAAEC,EAAE,EAAE,KAAK,IAAI,IAAMC,EAAE,EAAE,KAAK,GAAGP,EAAE,eAAeQ,EAAE,KAAK,IAAID,EAAE,GAAGD,EAAED,GAAG,CAAC,EAAEI,EAAEqB,GAAEtB,EAAEP,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAE,EAAEN,CAAC,EAAE,GAAG,CAACA,EAAE,mBAAmB,CAAC,IAAMT,EAAEuC,GAAEtB,EAAEP,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAE,IAAIN,CAAC,EAAES,EAAE,KAAK,GAAGlB,CAAC,CAAC,CAAC,OAAO,IAAIK,EAAEa,EAAE,KAAK,GAAGgB,GAAExB,EAAEC,EAAED,EAAEE,EAAE,KAAK,IAAIE,CAAC,EAAEH,EAAEE,EAAE,KAAK,IAAIC,CAAC,EAAEL,CAAC,EAAE,GAAGyB,GAAExB,EAAEC,EAAED,EAAEE,EAAE,KAAK,IAAIG,CAAC,EAAEJ,EAAEE,EAAE,KAAK,IAAIE,CAAC,EAAEN,CAAC,CAAC,EAAES,EAAE,KAAK,CAAC,GAAG,SAAS,KAAK,CAACR,EAAEC,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,KAAK,CAACD,EAAEE,EAAE,KAAK,IAAIE,CAAC,EAAEH,EAAEE,EAAE,KAAK,IAAIC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,OAAO,IAAII,CAAC,CAAC,CAAniBZ,EAAAyB,GAAA,KAAoiB,SAASS,GAAExC,EAAEC,EAAE,CAAC,IAAMC,EAAEmB,GAAED,GAAED,GAAEnB,CAAC,CAAC,CAAC,EAAEG,EAAE,CAAC,EAAMC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,OAAS,CAAC,IAAIJ,EAAE,KAAKQ,CAAC,IAAIN,EAAE,OAAOF,EAAE,CAAC,IAAI,IAAI,EAAE,CAACQ,EAAE,CAAC,EAAEA,EAAE,CAAC,CAAC,EAAEJ,EAAE,CAACI,EAAE,CAAC,EAAEA,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,IAAIL,EAAE,KAAK,GAAG+B,GAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE1B,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAEP,CAAC,CAAC,EAAE,EAAE,CAACO,EAAE,CAAC,EAAEA,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,IAAI,CAAC,GAAK,CAACR,EAAEE,EAAEE,EAAEC,EAAEI,EAAEC,CAAC,EAAEF,EAAEL,EAAE,KAAK,GAAGsC,GAAEzC,EAAEE,EAAEE,EAAEC,EAAEI,EAAEC,EAAE,EAAET,CAAC,CAAC,EAAE,EAAE,CAACQ,EAAEC,CAAC,EAAE,KAAK,CAAC,IAAI,IAAIP,EAAE,KAAK,GAAG+B,GAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE9B,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAEH,CAAC,CAAC,EAAE,EAAE,CAACG,EAAE,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,OAAO,IAAID,CAAC,CAAC,CAAhWG,EAAAkC,GAAA,KAAiW,SAASE,GAAE1C,EAAEC,EAAE,CAAC,IAAMC,EAAE,CAAC,EAAE,QAAUC,KAAKH,EAAE,GAAGG,EAAE,OAAO,CAAC,IAAMH,EAAEC,EAAE,qBAAqB,EAAEG,EAAED,EAAE,OAAO,GAAGC,EAAE,EAAE,CAACF,EAAE,KAAK,CAAC,GAAG,OAAO,KAAK,CAACC,EAAE,CAAC,EAAE,CAAC,EAAE6B,EAAEhC,EAAEC,CAAC,EAAEE,EAAE,CAAC,EAAE,CAAC,EAAE6B,EAAEhC,EAAEC,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQM,EAAE,EAAEA,EAAEH,EAAEG,IAAIL,EAAE,KAAK,CAAC,GAAG,SAAS,KAAK,CAACC,EAAEI,CAAC,EAAE,CAAC,EAAEyB,EAAEhC,EAAEC,CAAC,EAAEE,EAAEI,CAAC,EAAE,CAAC,EAAEyB,EAAEhC,EAAEC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,WAAW,IAAIC,CAAC,CAAC,CAA1QI,EAAAoC,GAAA,KAA2Q,SAASC,GAAE3C,EAAEC,EAAE,CAAC,OAAO,SAASD,EAAEC,EAAE,CAAC,IAAIC,EAAEF,EAAE,WAAW,UAAU,GAAG,CAACY,GAAEV,CAAC,EAAE,OAAOA,EAAE,CAAC,IAAI,SAASU,GAAEV,CAAC,IAAIU,GAAEV,CAAC,EAAE,IAAIM,GAAEP,CAAC,GAAG,MAAM,IAAI,cAAcW,GAAEV,CAAC,IAAIU,GAAEV,CAAC,EAAE,IAAIG,GAAEJ,CAAC,GAAG,MAAM,IAAI,OAAOW,GAAEV,CAAC,IAAIU,GAAEV,CAAC,EAAE,IAAIO,GAAER,CAAC,GAAG,MAAM,IAAI,SAASW,GAAEV,CAAC,IAAIU,GAAEV,CAAC,EAAE,IAAIQ,GAAET,CAAC,GAAG,MAAM,IAAI,cAAcW,GAAEV,CAAC,IAAIU,GAAEV,CAAC,EAAE,IAAIS,GAAEV,CAAC,GAAG,MAAM,QAAQC,EAAE,UAAUU,GAAEV,CAAC,IAAIU,GAAEV,CAAC,EAAE,IAAIE,GAAEH,CAAC,EAAE,CAAC,OAAOW,GAAEV,CAAC,CAAC,EAAED,EAAEuB,EAAC,EAAE,aAAaxB,EAAEC,CAAC,CAAC,CAA9WK,EAAAqC,GAAA,KAA+W,SAASP,GAAEpC,EAAE,CAAC,IAAMC,EAAE,OAAO,OAAO,CAAC,EAAED,CAAC,EAAE,OAAOC,EAAE,WAAW,OAAOD,EAAE,OAAOC,EAAE,KAAKD,EAAE,KAAK,GAAGC,CAAC,CAAvFK,EAAA8B,GAAA,KAAwF,SAASQ,GAAE5C,EAAE,CAAC,OAAOA,EAAE,aAAaA,EAAE,WAAW,IAAIa,GAAEb,EAAE,MAAM,CAAC,GAAGA,EAAE,WAAW,KAAK,CAAC,CAA7EM,EAAAsC,GAAA,KAA8E,SAASX,GAAEjC,EAAEC,EAAEC,EAAEC,EAAE,EAAE,CAAC,OAAOD,EAAE,UAAUC,GAAGyC,GAAE1C,CAAC,GAAGD,EAAED,GAAGA,EAAE,CAAhDM,EAAA2B,GAAA,KAAiD,SAASD,EAAEhC,EAAEC,EAAEC,EAAE,EAAE,CAAC,OAAO+B,GAAE,CAACjC,EAAEA,EAAEC,EAAEC,CAAC,CAAC,CAA7BI,EAAA0B,EAAA,KAA8B,SAASE,GAAElC,EAAEC,EAAEC,EAAEC,EAAEC,EAAE,EAAE,GAAG,CAAC,IAAM,EAAE,EAAEA,EAAE,uBAAuBA,EAAE,mBAAmBC,EAAEwC,GAAE7C,EAAEC,EAAEC,EAAEC,EAAEC,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,OAAOC,EAAE,IAAMI,EAAEoC,GAAE7C,EAAEC,EAAEC,EAAEC,EAAEC,EAAE,GAAG,EAAE,EAAE,OAAOC,EAAE,OAAOI,CAAC,CAAC,CAA1JH,EAAA4B,GAAA,KAA2J,SAASW,GAAE7C,EAAEC,EAAEC,EAAEC,EAAEC,EAAE,EAAE,EAAE,CAAC,IAAMC,EAAE,KAAK,IAAIL,EAAEE,EAAE,CAAC,EAAE,KAAK,IAAID,EAAEE,EAAE,CAAC,EAAEM,EAAE,KAAK,KAAKJ,CAAC,EAAMK,EAAE,EAAEA,EAAED,EAAE,IAAI,EAAEA,EAAE,IAAI,GAAG,UAAUA,EAAE,SAAS,IAAIE,EAAEP,EAAE,qBAAqB,EAAEO,EAAEA,EAAE,IAAIN,IAAIM,EAAEF,EAAE,IAAI,IAAMG,EAAED,EAAE,EAAEE,EAAE,GAAG,GAAG+B,GAAExC,CAAC,EAAMU,EAAEV,EAAE,OAAOA,EAAE,qBAAqBD,EAAEF,GAAG,IAAIc,EAAEX,EAAE,OAAOA,EAAE,qBAAqBJ,EAAEE,GAAG,IAAIY,EAAEkB,EAAElB,EAAEV,EAAEM,CAAC,EAAEK,EAAEiB,EAAEjB,EAAEX,EAAEM,CAAC,EAAE,IAAMM,EAAE,CAAC,EAAEC,EAAEX,EAAA,IAAI0B,EAAEpB,EAAER,EAAEM,CAAC,EAAX,KAAaQ,EAAEZ,EAAA,IAAI0B,EAAErB,EAAEP,EAAEM,CAAC,EAAX,KAAa,EAAEN,EAAE,iBAAiB,OAAO,IAAI,EAAEY,EAAE,KAAK,CAAC,GAAG,OAAO,KAAK,CAAChB,GAAG,EAAE,EAAEiB,EAAE,GAAGhB,GAAG,EAAE,EAAEgB,EAAE,EAAE,CAAC,CAAC,EAAED,EAAE,KAAK,CAAC,GAAG,OAAO,KAAK,CAAChB,GAAG,EAAE,EAAEgC,EAAErB,EAAEP,EAAEM,CAAC,GAAGT,GAAG,EAAE,EAAE+B,EAAErB,EAAEP,EAAEM,CAAC,EAAE,CAAC,CAAC,GAAG,EAAEM,EAAE,KAAK,CAAC,GAAG,WAAW,KAAK,CAACF,EAAEd,GAAGE,EAAEF,GAAGa,EAAEI,EAAE,EAAEF,EAAEd,GAAGE,EAAEF,GAAGY,EAAEI,EAAE,EAAEH,EAAEd,EAAE,GAAGE,EAAEF,GAAGa,EAAEI,EAAE,EAAEF,EAAEd,EAAE,GAAGE,EAAEF,GAAGY,EAAEI,EAAE,EAAEf,GAAG,EAAE,EAAEe,EAAE,GAAGd,GAAG,EAAE,EAAEc,EAAE,EAAE,CAAC,CAAC,EAAED,EAAE,KAAK,CAAC,GAAG,WAAW,KAAK,CAACF,EAAEd,GAAGE,EAAEF,GAAGa,EAAEK,EAAE,EAAEH,EAAEd,GAAGE,EAAEF,GAAGY,EAAEK,EAAE,EAAEJ,EAAEd,EAAE,GAAGE,EAAEF,GAAGa,EAAEK,EAAE,EAAEH,EAAEd,EAAE,GAAGE,EAAEF,GAAGY,EAAEK,EAAE,EAAEhB,GAAG,EAAE,EAAEgB,EAAE,GAAGf,GAAG,EAAE,EAAEe,EAAE,EAAE,CAAC,CAAC,EAAEF,CAAC,CAAzuBV,EAAAuC,GAAA,KAA0uB,SAASV,GAAEnC,EAAEC,EAAEC,EAAE,CAAC,GAAG,CAACF,EAAE,OAAO,MAAM,CAAC,EAAE,IAAMG,EAAE,CAAC,EAAEA,EAAE,KAAK,CAACH,EAAE,CAAC,EAAE,CAAC,EAAEgC,EAAE/B,EAAEC,CAAC,EAAEF,EAAE,CAAC,EAAE,CAAC,EAAEgC,EAAE/B,EAAEC,CAAC,CAAC,CAAC,EAAEC,EAAE,KAAK,CAACH,EAAE,CAAC,EAAE,CAAC,EAAEgC,EAAE/B,EAAEC,CAAC,EAAEF,EAAE,CAAC,EAAE,CAAC,EAAEgC,EAAE/B,EAAEC,CAAC,CAAC,CAAC,EAAE,QAAQE,EAAE,EAAEA,EAAEJ,EAAE,OAAOI,IAAID,EAAE,KAAK,CAACH,EAAEI,CAAC,EAAE,CAAC,EAAE4B,EAAE/B,EAAEC,CAAC,EAAEF,EAAEI,CAAC,EAAE,CAAC,EAAE4B,EAAE/B,EAAEC,CAAC,CAAC,CAAC,EAAEE,IAAIJ,EAAE,OAAO,GAAGG,EAAE,KAAK,CAACH,EAAEI,CAAC,EAAE,CAAC,EAAE4B,EAAE/B,EAAEC,CAAC,EAAEF,EAAEI,CAAC,EAAE,CAAC,EAAE4B,EAAE/B,EAAEC,CAAC,CAAC,CAAC,EAAE,OAAOoC,GAAEnC,EAAE,KAAKD,CAAC,CAAC,CAAvQI,EAAA6B,GAAA,KAAwQ,SAASG,GAAEtC,EAAEC,EAAEC,EAAE,CAAC,IAAMC,EAAEH,EAAE,OAAOI,EAAE,CAAC,EAAE,GAAGD,EAAE,EAAE,CAAC,IAAM,EAAE,CAAC,EAAE,EAAE,EAAED,EAAE,eAAeE,EAAE,KAAK,CAAC,GAAG,OAAO,KAAK,CAACJ,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQC,EAAE,EAAEA,EAAE,EAAEE,EAAEF,IAAI,CAAC,IAAMC,EAAEF,EAAEC,CAAC,EAAE,EAAE,CAAC,EAAE,CAACC,EAAE,CAAC,EAAEA,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAACA,EAAE,CAAC,GAAG,EAAEF,EAAEC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAED,EAAEC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAEC,EAAE,CAAC,GAAG,EAAEF,EAAEC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAED,EAAEC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAACD,EAAEC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAED,EAAEC,CAAC,EAAE,CAAC,EAAE,EAAED,EAAEC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAED,EAAEC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAED,EAAEC,CAAC,EAAE,CAAC,EAAE,EAAED,EAAEC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAACD,EAAEC,EAAE,CAAC,EAAE,CAAC,EAAED,EAAEC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEG,EAAE,KAAK,CAAC,GAAG,WAAW,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGH,GAAOA,EAAE,SAAN,EAAa,CAAC,IAAMD,EAAEE,EAAE,oBAAoBE,EAAE,KAAK,CAAC,GAAG,SAAS,KAAK,CAACH,EAAE,CAAC,EAAE+B,EAAEhC,EAAEE,CAAC,EAAED,EAAE,CAAC,EAAE+B,EAAEhC,EAAEE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAUC,IAAJ,GAAOC,EAAE,KAAK,CAAC,GAAG,OAAO,KAAK,CAACJ,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAEI,EAAE,KAAK,CAAC,GAAG,WAAW,KAAK,CAACJ,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAOG,IAAJ,GAAOC,EAAE,KAAK,GAAGyC,GAAE7C,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,CAAC,EAAEE,EAAE,GAAG,EAAE,CAAC,EAAE,OAAOE,CAAC,CAA7tBE,EAAAgC,GAAA,KAA8tB,SAASD,GAAErC,EAAEC,EAAEC,EAAEC,EAAEC,EAAE,EAAE,EAAEC,EAAE,CAAC,IAAMI,EAAE,CAAC,EAAEC,EAAE,CAAC,EAAE,GAAOL,EAAE,YAAN,EAAgB,CAACL,GAAG,EAAEU,EAAE,KAAK,CAACT,EAAEE,EAAE,KAAK,IAAI,CAACH,CAAC,EAAEE,EAAEE,EAAE,KAAK,IAAI,CAACJ,CAAC,CAAC,CAAC,EAAE,QAAQO,EAAE,EAAEA,GAAG,EAAE,KAAK,GAAGA,GAAGP,EAAE,CAAC,IAAMA,EAAE,CAACC,EAAEE,EAAE,KAAK,IAAII,CAAC,EAAEL,EAAEE,EAAE,KAAK,IAAIG,CAAC,CAAC,EAAEE,EAAE,KAAKT,CAAC,EAAEU,EAAE,KAAKV,CAAC,CAAC,CAACU,EAAE,KAAK,CAACT,EAAEE,EAAE,KAAK,IAAI,CAAC,EAAED,EAAEE,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,EAAEM,EAAE,KAAK,CAACT,EAAEE,EAAE,KAAK,IAAIH,CAAC,EAAEE,EAAEE,EAAE,KAAK,IAAIJ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAMW,EAAEqB,EAAE,GAAG3B,CAAC,EAAE,KAAK,GAAG,EAAEK,EAAE,KAAK,CAACsB,EAAE,EAAE3B,CAAC,EAAEJ,EAAE,GAAGE,EAAE,KAAK,IAAIQ,EAAEX,CAAC,EAAEgC,EAAE,EAAE3B,CAAC,EAAEH,EAAE,GAAGE,EAAE,KAAK,IAAIO,EAAEX,CAAC,CAAC,CAAC,EAAE,IAAMY,EAAE,EAAE,KAAK,GAAGD,EAAE,IAAI,QAAQH,EAAEG,EAAEH,EAAEI,EAAEJ,GAAGR,EAAE,CAAC,IAAMA,EAAE,CAACgC,EAAE,EAAE3B,CAAC,EAAEJ,EAAEE,EAAE,KAAK,IAAIK,CAAC,EAAEwB,EAAE,EAAE3B,CAAC,EAAEH,EAAEE,EAAE,KAAK,IAAII,CAAC,CAAC,EAAEC,EAAE,KAAKT,CAAC,EAAEU,EAAE,KAAKV,CAAC,CAAC,CAACU,EAAE,KAAK,CAACsB,EAAE,EAAE3B,CAAC,EAAEJ,EAAEE,EAAE,KAAK,IAAIQ,EAAE,EAAE,KAAK,GAAG,GAAG,CAAC,EAAEqB,EAAE,EAAE3B,CAAC,EAAEH,EAAEE,EAAE,KAAK,IAAIO,EAAE,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,EAAED,EAAE,KAAK,CAACsB,EAAE,EAAE3B,CAAC,EAAEJ,EAAE,IAAIE,EAAE,KAAK,IAAIQ,EAAE,CAAC,EAAEqB,EAAE,EAAE3B,CAAC,EAAEH,EAAE,IAAIE,EAAE,KAAK,IAAIO,EAAE,CAAC,CAAC,CAAC,EAAED,EAAE,KAAK,CAACsB,EAAE,EAAE3B,CAAC,EAAEJ,EAAE,GAAGE,EAAE,KAAK,IAAIQ,EAAE,GAAG,CAAC,EAAEqB,EAAE,EAAE3B,CAAC,EAAEH,EAAE,GAAGE,EAAE,KAAK,IAAIO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAACD,EAAED,CAAC,CAAC,CAAjuBH,EAAA+B,GAAA,KAAkuB,SAASE,GAAEvC,EAAEC,EAAEC,EAAEC,EAAEC,EAAE,EAAE,EAAEC,EAAEI,EAAE,CAAC,IAAMC,EAAE,EAAEsB,EAAE,GAAGvB,CAAC,EAAEE,EAAE,CAAC,EAAEA,EAAE,KAAK,CAACqB,EAAE3B,EAAEI,CAAC,EAAER,EAAE,GAAGE,EAAE,KAAK,IAAIO,EAAEV,CAAC,EAAEgC,EAAE3B,EAAEI,CAAC,EAAEP,EAAE,GAAGE,EAAE,KAAK,IAAIM,EAAEV,CAAC,CAAC,CAAC,EAAE,QAAQO,EAAEG,EAAEH,GAAG,EAAEA,GAAGP,EAAEW,EAAE,KAAK,CAACqB,EAAE3B,EAAEI,CAAC,EAAER,EAAEE,EAAE,KAAK,IAAII,CAAC,EAAEyB,EAAE3B,EAAEI,CAAC,EAAEP,EAAEE,EAAE,KAAK,IAAIG,CAAC,CAAC,CAAC,EAAE,OAAOI,EAAE,KAAK,CAACV,EAAEE,EAAE,KAAK,IAAI,CAAC,EAAED,EAAEE,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,EAAEO,EAAE,KAAK,CAACV,EAAEE,EAAE,KAAK,IAAI,CAAC,EAAED,EAAEE,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,EAAEkC,GAAE3B,EAAE,KAAKF,CAAC,CAAC,CAAlSH,EAAAiC,GAAA,KAAmS,SAASE,GAAEzC,EAAEC,EAAEC,EAAEC,EAAEC,EAAE,EAAE,EAAEC,EAAE,CAAC,IAAMI,EAAE,CAAC,EAAEC,EAAE,CAACL,EAAE,qBAAqB,GAAGA,EAAE,qBAAqB,GAAG,EAAE,EAAMM,EAAE,CAAC,EAAE,CAAC,EAAQC,EAAEP,EAAE,mBAAmB,EAAE,EAAEQ,EAAER,EAAE,iBAAiB,QAAQS,EAAE,EAAEA,EAAEF,EAAEE,IAAQA,IAAJ,EAAML,EAAE,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAEA,EAAE,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,EAAE,CAAC,GAAGI,EAAE,EAAEmB,EAAEtB,EAAE,CAAC,EAAEL,CAAC,GAAG,EAAE,CAAC,GAAGQ,EAAE,EAAEmB,EAAEtB,EAAE,CAAC,EAAEL,CAAC,EAAE,CAAC,CAAC,EAAEM,EAAEE,EAAE,CAACT,EAAE,CAAC,EAAE,CAACA,EAAE4B,EAAEtB,EAAEI,CAAC,EAAET,CAAC,EAAE,EAAE2B,EAAEtB,EAAEI,CAAC,EAAET,CAAC,CAAC,EAAEI,EAAE,KAAK,CAAC,GAAG,WAAW,KAAK,CAACT,EAAEgC,EAAEtB,EAAEI,CAAC,EAAET,CAAC,EAAEJ,EAAE+B,EAAEtB,EAAEI,CAAC,EAAET,CAAC,EAAEH,EAAE8B,EAAEtB,EAAEI,CAAC,EAAET,CAAC,EAAEF,EAAE6B,EAAEtB,EAAEI,CAAC,EAAET,CAAC,EAAEM,EAAE,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,OAAOF,CAAC,CAApaH,EAAAmC,GAAA,KAAqa,SAASK,GAAE9C,EAAE,CAAC,MAAM,CAAC,GAAGA,CAAC,CAAC,CAAjBM,EAAAwC,GAAA,KAAkB,SAASC,GAAE/C,EAAEC,EAAE,EAAE,CAAC,IAAMC,EAAEF,EAAE,OAAO,GAAGE,EAAE,EAAE,MAAM,IAAI,MAAM,0CAA0C,EAAE,IAAMC,EAAE,CAAC,EAAE,GAAOD,IAAJ,EAAMC,EAAE,KAAK2C,GAAE9C,EAAE,CAAC,CAAC,EAAE8C,GAAE9C,EAAE,CAAC,CAAC,EAAE8C,GAAE9C,EAAE,CAAC,CAAC,EAAE8C,GAAE9C,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,IAAME,EAAE,CAAC,EAAEA,EAAE,KAAKF,EAAE,CAAC,EAAEA,EAAE,CAAC,CAAC,EAAE,QAAQC,EAAE,EAAEA,EAAED,EAAE,OAAOC,IAAIC,EAAE,KAAKF,EAAEC,CAAC,CAAC,EAAEA,IAAID,EAAE,OAAO,GAAGE,EAAE,KAAKF,EAAEC,CAAC,CAAC,EAAE,IAAMG,EAAE,CAAC,EAAEG,EAAE,EAAEN,EAAEE,EAAE,KAAK2C,GAAE5C,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQF,EAAE,EAAEA,EAAE,EAAEE,EAAE,OAAOF,IAAI,CAAC,IAAMC,EAAEC,EAAEF,CAAC,EAAEI,EAAE,CAAC,EAAE,CAACH,EAAE,CAAC,EAAEA,EAAE,CAAC,CAAC,EAAEG,EAAE,CAAC,EAAE,CAACH,EAAE,CAAC,GAAGM,EAAEL,EAAEF,EAAE,CAAC,EAAE,CAAC,EAAEO,EAAEL,EAAEF,EAAE,CAAC,EAAE,CAAC,GAAG,EAAEC,EAAE,CAAC,GAAGM,EAAEL,EAAEF,EAAE,CAAC,EAAE,CAAC,EAAEO,EAAEL,EAAEF,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAEI,EAAE,CAAC,EAAE,CAACF,EAAEF,EAAE,CAAC,EAAE,CAAC,GAAGO,EAAEL,EAAEF,CAAC,EAAE,CAAC,EAAEO,EAAEL,EAAEF,EAAE,CAAC,EAAE,CAAC,GAAG,EAAEE,EAAEF,EAAE,CAAC,EAAE,CAAC,GAAGO,EAAEL,EAAEF,CAAC,EAAE,CAAC,EAAEO,EAAEL,EAAEF,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAEI,EAAE,CAAC,EAAE,CAACF,EAAEF,EAAE,CAAC,EAAE,CAAC,EAAEE,EAAEF,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEG,EAAE,KAAKC,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAOD,CAAC,CAAhjBG,EAAAyC,GAAA,KAAijB,SAASC,GAAEhD,EAAEC,EAAE,CAAC,OAAO,KAAK,IAAID,EAAE,CAAC,EAAEC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,IAAID,EAAE,CAAC,EAAEC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAzDK,EAAA0C,GAAA,KAA0D,SAASC,GAAEjD,EAAEC,EAAEC,EAAE,CAAC,IAAMC,EAAE6C,GAAE/C,EAAEC,CAAC,EAAE,GAAOC,IAAJ,EAAM,OAAO6C,GAAEhD,EAAEC,CAAC,EAAE,IAAIG,IAAIJ,EAAE,CAAC,EAAEC,EAAE,CAAC,IAAIC,EAAE,CAAC,EAAED,EAAE,CAAC,IAAID,EAAE,CAAC,EAAEC,EAAE,CAAC,IAAIC,EAAE,CAAC,EAAED,EAAE,CAAC,IAAIE,EAAE,OAAOC,EAAE,KAAK,IAAI,EAAE,KAAK,IAAI,EAAEA,CAAC,CAAC,EAAE4C,GAAEhD,EAAEkD,GAAEjD,EAAEC,EAAEE,CAAC,CAAC,CAAC,CAAzJE,EAAA2C,GAAA,KAA0J,SAASC,GAAElD,EAAEC,EAAEC,EAAE,CAAC,MAAM,CAACF,EAAE,CAAC,GAAGC,EAAE,CAAC,EAAED,EAAE,CAAC,GAAGE,EAAEF,EAAE,CAAC,GAAGC,EAAE,CAAC,EAAED,EAAE,CAAC,GAAGE,CAAC,CAAC,CAAtDI,EAAA4C,GAAA,KAAuD,SAASC,GAAEnD,EAAEC,EAAEC,EAAEC,EAAE,CAAC,IAAMC,EAAED,GAAG,CAAC,EAAE,GAAG,SAASH,EAAEC,EAAE,CAAC,IAAMC,EAAEF,EAAEC,EAAE,CAAC,EAAE,EAAED,EAAEC,EAAE,CAAC,EAAEG,EAAEJ,EAAEC,EAAE,CAAC,EAAEM,EAAEP,EAAEC,EAAE,CAAC,EAAMO,EAAE,EAAE,EAAE,CAAC,EAAE,EAAEN,EAAE,CAAC,EAAEK,EAAE,CAAC,EAAEC,GAAGA,EAAE,IAAIH,EAAE,EAAE,EAAE,CAAC,EAAE,EAAEH,EAAE,CAAC,EAAEK,EAAE,CAAC,EAAEF,GAAGA,EAAE,IAAII,EAAE,EAAEL,EAAE,CAAC,EAAE,EAAEG,EAAE,CAAC,EAAEL,EAAE,CAAC,EAAEO,GAAGA,EAAE,IAAIC,EAAE,EAAEN,EAAE,CAAC,EAAE,EAAEG,EAAE,CAAC,EAAEL,EAAE,CAAC,EAAE,OAAOQ,GAAGA,EAAEF,EAAEC,IAAID,EAAEC,GAAGJ,EAAEK,IAAIL,EAAEK,GAAGF,EAAEH,CAAC,EAAEL,EAAEC,CAAC,EAAEC,EAAE,CAAC,IAAM,EAAEF,EAAEC,EAAE,CAAC,EAAKG,EAAE,QAAS,EAAEA,EAAEA,EAAE,OAAO,CAAC,EAAE,EAAE,EAAE,KAAK,KAAK4C,GAAE,EAAE,CAAC,CAAC,EAAG,GAAG5C,EAAE,KAAK,CAAC,GAAOA,EAAE,KAAK,CAAC,EAAEA,EAAE,KAAKJ,EAAEC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAWM,EAAEP,EAAEC,EAAE,CAAC,EAAEO,EAAER,EAAEC,EAAE,CAAC,EAAEI,EAAEL,EAAEC,EAAE,CAAC,EAAEQ,EAAET,EAAEC,EAAE,CAAC,EAAES,EAAEwC,GAAE3C,EAAEC,EAAE,EAAC,EAAEG,EAAEuC,GAAE1C,EAAEH,EAAE,EAAC,EAAEO,EAAEsC,GAAE7C,EAAEI,EAAE,EAAC,EAAEI,EAAEqC,GAAExC,EAAEC,EAAE,EAAC,EAAEG,EAAEoC,GAAEvC,EAAEC,EAAE,EAAC,EAAEG,EAAEmC,GAAErC,EAAEC,EAAE,EAAC,EAAEqC,GAAE,CAAC5C,EAAEG,EAAEG,EAAEE,CAAC,EAAE,EAAEb,EAAEE,CAAC,EAAE+C,GAAE,CAACpC,EAAED,EAAEF,EAAEH,CAAC,EAAE,EAAEP,EAAEE,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,OAAOA,CAAC,CAAjhBE,EAAA6C,GAAA,KAAkhB,SAASC,GAAEpD,EAAEC,EAAE,CAAC,OAAOoD,GAAErD,EAAE,EAAEA,EAAE,OAAOC,CAAC,CAAC,CAA/BK,EAAA8C,GAAA,KAAgC,SAASC,GAAErD,EAAEC,EAAEC,EAAEC,EAAEC,EAAE,CAAC,IAAM,EAAEA,GAAG,CAAC,EAAE,EAAEJ,EAAEC,CAAC,EAAEI,EAAEL,EAAEE,EAAE,CAAC,EAAMO,EAAE,EAAEC,EAAE,EAAE,QAAQ,EAAET,EAAE,EAAE,EAAEC,EAAE,EAAE,EAAE,EAAE,CAAC,IAAMD,EAAEgD,GAAEjD,EAAE,CAAC,EAAE,EAAEK,CAAC,EAAEJ,EAAEQ,IAAIA,EAAER,EAAES,EAAE,EAAE,CAAC,OAAO,KAAK,KAAKD,CAAC,EAAEN,GAAGkD,GAAErD,EAAEC,EAAES,EAAE,EAAEP,EAAE,CAAC,EAAEkD,GAAErD,EAAEU,EAAER,EAAEC,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE,EAAE,KAAKE,CAAC,GAAG,CAAC,CAAxMC,EAAA+C,GAAA,KAAyM,SAASC,GAAEtD,EAAEC,EAAE,IAAIC,EAAE,CAAC,IAAMC,EAAE,CAAC,EAAEC,GAAGJ,EAAE,OAAO,GAAG,EAAE,QAAQE,EAAE,EAAEA,EAAEE,EAAEF,IAAKiD,GAAEnD,EAAE,EAAEE,EAAED,EAAEE,CAAC,EAAE,OAAOD,GAAGA,EAAE,EAAEmD,GAAElD,EAAE,EAAEA,EAAE,OAAOD,CAAC,EAAEC,CAAC,CAA5GG,EAAAgD,GAAA,KAA6G,IAAMC,GAAG,OAAaC,GAAN,KAAQ,CAA/kkB,MAA+kkB,CAAAlD,EAAA,WAAC,YAAY,EAAE,CAAC,KAAK,eAAe,CAAC,oBAAoB,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,OAAO,YAAY,EAAE,eAAe,EAAE,aAAa,IAAI,eAAe,EAAE,UAAU,UAAU,WAAW,GAAG,aAAa,IAAI,WAAW,GAAG,WAAW,GAAG,QAAQ,GAAG,aAAa,GAAG,KAAK,EAAE,mBAAmB,GAAG,uBAAuB,GAAG,iBAAiB,GAAG,uBAAuB,EAAE,EAAE,KAAK,OAAO,GAAG,CAAC,EAAE,KAAK,OAAO,UAAU,KAAK,eAAe,KAAK,GAAG,KAAK,OAAO,OAAO,EAAE,CAAC,OAAO,SAAS,CAAC,OAAO,KAAK,MAAM,KAAK,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,OAAO,CAAC,EAAE,KAAK,eAAe,CAAC,EAAE,KAAK,cAAc,CAAC,GAAG,EAAEL,EAAEC,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,KAAKD,GAAG,CAAC,EAAE,QAAQC,GAAG,KAAK,cAAc,CAAC,CAAC,KAAK,EAAED,EAAEC,EAAEC,EAAEC,EAAE,CAAC,IAAMG,EAAE,KAAK,GAAGH,CAAC,EAAE,OAAO,KAAK,GAAG,OAAO,CAACqB,GAAE,EAAExB,EAAEC,EAAEC,EAAEI,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,UAAU,EAAEN,EAAEC,EAAEC,EAAEC,EAAE,CAAC,IAAMG,EAAE,KAAK,GAAGH,CAAC,EAAEI,EAAE,CAAC,EAAEH,EAAEsB,GAAE,EAAE1B,EAAEC,EAAEC,EAAEI,CAAC,EAAE,GAAGA,EAAE,KAAK,CAAC,IAAMH,EAAE,CAAC,CAAC,EAAEH,CAAC,EAAE,CAAC,EAAEC,EAAED,CAAC,EAAE,CAAC,EAAEC,EAAED,EAAEE,CAAC,EAAE,CAAC,EAAEF,EAAEE,CAAC,CAAC,EAAYI,EAAE,YAAZ,QAAsBC,EAAE,KAAKkC,GAAE,CAACtC,CAAC,EAAEG,CAAC,CAAC,EAAEC,EAAE,KAAKmC,GAAE,CAACvC,CAAC,EAAEG,CAAC,CAAC,CAAC,CAAC,OAAOA,EAAE,SAASgD,IAAI/C,EAAE,KAAKH,CAAC,EAAE,KAAK,GAAG,YAAYG,EAAED,CAAC,CAAC,CAAC,QAAQ,EAAEN,EAAEC,EAAEC,EAAEC,EAAE,CAAC,IAAMG,EAAE,KAAK,GAAGH,CAAC,EAAEI,EAAE,CAAC,EAAEH,EAAEwB,GAAE3B,EAAEC,EAAEI,CAAC,EAAE,EAAEuB,GAAE,EAAE7B,EAAEM,EAAEF,CAAC,EAAE,GAAGE,EAAE,KAAK,GAAaA,EAAE,YAAZ,QAAsB,CAAC,IAAML,EAAE4B,GAAE,EAAE7B,EAAEM,EAAEF,CAAC,EAAE,MAAMH,EAAE,KAAK,WAAWM,EAAE,KAAKN,CAAC,CAAC,MAAMM,EAAE,KAAKmC,GAAE,CAAC,EAAE,eAAe,EAAEpC,CAAC,CAAC,EAAE,OAAOA,EAAE,SAASgD,IAAI/C,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,GAAG,UAAUA,EAAED,CAAC,CAAC,CAAC,OAAO,EAAEN,EAAEC,EAAEC,EAAE,CAAC,IAAMC,EAAE,KAAK,QAAQ,EAAEH,EAAEC,EAAEA,EAAEC,CAAC,EAAE,OAAOC,EAAE,MAAM,SAASA,CAAC,CAAC,WAAW,EAAEH,EAAE,CAAC,IAAMC,EAAE,KAAK,GAAGD,CAAC,EAAE,OAAO,KAAK,GAAG,aAAa,CAACyB,GAAE,EAAE,GAAGxB,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,IAAI,EAAED,EAAEC,EAAEC,EAAEC,EAAEG,EAAEC,EAAE,GAAGH,EAAE,CAAC,IAAM,EAAE,KAAK,GAAGA,CAAC,EAAEK,EAAE,CAAC,EAAEC,EAAEoB,GAAE,EAAE9B,EAAEC,EAAEC,EAAEC,EAAEG,EAAEC,EAAE,GAAG,CAAC,EAAE,GAAGA,GAAG,EAAE,KAAK,GAAa,EAAE,YAAZ,QAAsB,CAAC,IAAMA,EAAE,OAAO,OAAO,CAAC,EAAE,CAAC,EAAEA,EAAE,mBAAmB,GAAG,IAAMH,EAAE0B,GAAE,EAAE9B,EAAEC,EAAEC,EAAEC,EAAEG,EAAE,GAAG,GAAGC,CAAC,EAAEH,EAAE,KAAK,WAAWK,EAAE,KAAKL,CAAC,CAAC,MAAMK,EAAE,KAAK,SAASV,EAAEC,EAAEC,EAAEC,EAAEC,EAAEG,EAAEC,EAAE,CAAC,IAAMH,EAAEL,EAAES,EAAER,EAAMS,EAAE,KAAK,IAAIR,EAAE,CAAC,EAAES,EAAE,KAAK,IAAIR,EAAE,CAAC,EAAEO,GAAGsB,EAAE,IAAItB,EAAEF,CAAC,EAAEG,GAAGqB,EAAE,IAAIrB,EAAEH,CAAC,EAAE,IAAII,EAAER,EAAES,EAAEN,EAAE,KAAKK,EAAE,GAAGA,GAAG,EAAE,KAAK,GAAGC,GAAG,EAAE,KAAK,GAAGA,EAAED,EAAE,EAAE,KAAK,KAAKA,EAAE,EAAEC,EAAE,EAAE,KAAK,IAAI,IAAMC,GAAGD,EAAED,GAAGJ,EAAE,eAAeO,EAAE,CAAC,EAAE,QAAQf,EAAEY,EAAEZ,GAAGa,EAAEb,GAAGc,EAAEC,EAAE,KAAK,CAACV,EAAEK,EAAE,KAAK,IAAIV,CAAC,EAAES,EAAEE,EAAE,KAAK,IAAIX,CAAC,CAAC,CAAC,EAAE,OAAOe,EAAE,KAAK,CAACV,EAAEK,EAAE,KAAK,IAAIG,CAAC,EAAEJ,EAAEE,EAAE,KAAK,IAAIE,CAAC,CAAC,CAAC,EAAEE,EAAE,KAAK,CAACV,EAAEI,CAAC,CAAC,EAAEkC,GAAE,CAAC5B,CAAC,EAAEP,CAAC,CAAC,EAAE,EAAEP,EAAEC,EAAEC,EAAEC,EAAEG,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,SAASgD,IAAI7C,EAAE,KAAKC,CAAC,EAAE,KAAK,GAAG,MAAMD,EAAE,CAAC,CAAC,CAAC,MAAM,EAAET,EAAE,CAAC,IAAMC,EAAE,KAAK,GAAGD,CAAC,EAAEE,EAAE,CAAC,EAAEC,EAAEwB,GAAE,EAAE1B,CAAC,EAAE,GAAGA,EAAE,MAAMA,EAAE,OAAOqD,GAAG,GAAarD,EAAE,YAAZ,QAAsB,CAAC,IAAMD,EAAE2B,GAAE,EAAE,OAAO,OAAO,OAAO,OAAO,CAAC,EAAE1B,CAAC,EAAE,CAAC,mBAAmB,GAAG,UAAUA,EAAE,UAAUA,EAAE,UAAUA,EAAE,uBAAuB,CAAC,CAAC,CAAC,EAAEC,EAAE,KAAK,CAAC,KAAK,WAAW,IAAI,KAAK,aAAaF,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,IAAMA,EAAE,CAAC,EAAEG,EAAE,EAAE,GAAGA,EAAE,OAAO,CAAC,IAAMJ,EAAY,OAAOI,EAAE,CAAC,EAAE,CAAC,GAAvB,SAAyB,CAACA,CAAC,EAAEA,EAAE,QAAUD,KAAKH,EAAEG,EAAE,OAAO,EAAEF,EAAE,KAAK,GAAGE,CAAC,EAAMA,EAAE,SAAN,EAAaF,EAAE,KAAK,GAAGqD,GAAEP,GAAE,CAAC5C,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAED,EAAE,WAAW,CAAC,CAAC,EAAED,EAAE,KAAK,GAAGqD,GAAEP,GAAE5C,CAAC,EAAE,IAAI,EAAED,EAAE,WAAW,CAAC,CAAC,CAAC,CAACD,EAAE,QAAQE,EAAE,KAAKwC,GAAE,CAAC1C,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC,OAAOA,EAAE,SAASqD,IAAIpD,EAAE,KAAKC,CAAC,EAAE,KAAK,GAAG,QAAQD,EAAED,CAAC,CAAC,CAAC,QAAQ,EAAED,EAAE,CAAC,IAAMC,EAAE,KAAK,GAAGD,CAAC,EAAEE,EAAE,CAAC,EAAEC,EAAEsB,GAAE,EAAE,GAAGxB,CAAC,EAAE,OAAOA,EAAE,OAAiBA,EAAE,YAAZ,QAAsBC,EAAE,KAAKuC,GAAE,CAAC,CAAC,EAAExC,CAAC,CAAC,EAAEC,EAAE,KAAKwC,GAAE,CAAC,CAAC,EAAEzC,CAAC,CAAC,GAAGA,EAAE,SAASqD,IAAIpD,EAAE,KAAKC,CAAC,EAAE,KAAK,GAAG,UAAUD,EAAED,CAAC,CAAC,CAAC,KAAK,EAAED,EAAE,CAAC,IAAMC,EAAE,KAAK,GAAGD,CAAC,EAAEE,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,OAAO,KAAK,GAAG,OAAOA,EAAED,CAAC,EAAE,GAAG,GAAG,IAAI,QAAQ,MAAM,GAAG,EAAE,QAAQ,SAAS,GAAG,EAAE,QAAQ,UAAU,GAAG,EAAE,IAAME,EAAEF,EAAE,MAAsBA,EAAE,OAAlB,eAAwBA,EAAE,OAAOqD,GAAGhD,EAAEL,EAAE,SAASqD,GAAG/C,EAAE,CAAC,EAAEN,EAAE,gBAAgBA,EAAE,eAAe,GAAGG,EAAE,SAASL,EAAEC,EAAEC,EAAE,CAAC,IAAMC,EAAEkB,GAAED,GAAED,GAAEnB,CAAC,CAAC,CAAC,EAAEI,EAAE,CAAC,EAAMG,EAAE,CAAC,EAAEC,EAAE,CAAC,EAAE,CAAC,EAAEH,EAAE,CAAC,EAAQI,EAAEH,EAAA,IAAI,CAACD,EAAE,QAAQ,GAAGE,EAAE,KAAK,GAAG+C,GAAEjD,EAAEJ,CAAC,CAAC,EAAEI,EAAE,CAAC,CAAC,EAAxC,KAA0CK,EAAEJ,EAAA,IAAI,CAACG,EAAE,EAAEF,EAAE,SAASH,EAAE,KAAKG,CAAC,EAAEA,EAAE,CAAC,EAAE,EAAnC,KAAqC,OAAS,CAAC,IAAIP,EAAE,KAAKC,CAAC,IAAIE,EAAE,OAAOH,EAAE,CAAC,IAAI,IAAIU,EAAE,EAAEF,EAAE,CAACP,EAAE,CAAC,EAAEA,EAAE,CAAC,CAAC,EAAEM,EAAE,KAAKC,CAAC,EAAE,MAAM,IAAI,IAAIC,EAAE,EAAEF,EAAE,KAAK,CAACN,EAAE,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,IAAI,GAAG,CAACI,EAAE,OAAO,CAAC,IAAML,EAAEO,EAAE,OAAOA,EAAEA,EAAE,OAAO,CAAC,EAAEC,EAAEH,EAAE,KAAK,CAACL,EAAE,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAACK,EAAE,KAAK,CAACJ,EAAE,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,EAAEI,EAAE,KAAK,CAACJ,EAAE,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,EAAEI,EAAE,KAAK,CAACJ,EAAE,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,IAAIQ,EAAE,EAAEF,EAAE,KAAK,CAACC,EAAE,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGE,EAAE,EAAE,CAACR,EAAE,OAAOE,EAAE,IAAMO,EAAE,CAAC,EAAE,QAAUX,KAAKI,EAAE,CAAC,IAAMH,EAAEmD,GAAEpD,EAAEE,CAAC,EAAED,EAAE,QAAQU,EAAE,KAAKV,CAAC,CAAC,CAAC,OAAOU,CAAC,EAAE,EAAE,EAAEH,EAAE,EAAE,GAAGN,EAAE,gBAAgB,IAAI,EAAEA,EAAE,WAAW,CAAC,EAAE,EAAEsC,GAAE,EAAEtC,CAAC,EAAE,GAAGE,EAAE,GAAaF,EAAE,YAAZ,QAAsB,GAAOG,EAAE,SAAN,EAAa,CAAC,IAAMJ,EAAEuC,GAAE,EAAE,OAAO,OAAO,OAAO,OAAO,CAAC,EAAEtC,CAAC,EAAE,CAAC,mBAAmB,GAAG,UAAUA,EAAE,UAAUA,EAAE,UAAUA,EAAE,uBAAuB,CAAC,CAAC,CAAC,EAAEC,EAAE,KAAK,CAAC,KAAK,WAAW,IAAI,KAAK,aAAaF,EAAE,GAAG,CAAC,CAAC,CAAC,MAAME,EAAE,KAAKuC,GAAErC,EAAEH,CAAC,CAAC,OAAOC,EAAE,KAAKwC,GAAEtC,EAAEH,CAAC,CAAC,EAAE,OAAOK,IAAIC,EAAEH,EAAE,QAASL,GAAG,CAACG,EAAE,KAAKuB,GAAE1B,EAAE,GAAGE,CAAC,CAAC,CAAC,CAAE,EAAEC,EAAE,KAAK,CAAC,GAAG,KAAK,GAAG,OAAOA,EAAED,CAAC,CAAC,CAAC,UAAU,EAAED,EAAE,CAAC,IAAIC,EAAE,GAAG,QAAUC,KAAK,EAAE,IAAI,CAAC,IAAMH,EAAY,OAAOC,GAAjB,UAAoBA,GAAG,EAAEE,EAAE,KAAK,IAAKH,GAAG,CAACA,EAAE,QAAQC,CAAC,CAAE,EAAEE,EAAE,KAAK,OAAOA,EAAE,GAAG,CAAC,IAAI,OAAOD,GAAG,IAAIF,EAAE,CAAC,CAAC,IAAIA,EAAE,CAAC,CAAC,IAAI,MAAM,IAAI,WAAWE,GAAG,IAAIF,EAAE,CAAC,CAAC,IAAIA,EAAE,CAAC,CAAC,KAAKA,EAAE,CAAC,CAAC,IAAIA,EAAE,CAAC,CAAC,KAAKA,EAAE,CAAC,CAAC,IAAIA,EAAE,CAAC,CAAC,IAAI,MAAM,IAAI,SAASE,GAAG,IAAIF,EAAE,CAAC,CAAC,IAAIA,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,OAAOE,EAAE,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC,IAAMD,EAAE,EAAE,MAAM,CAAC,EAAEC,EAAE,EAAE,SAAS,KAAK,eAAeC,EAAE,CAAC,EAAE,QAAUH,KAAKC,EAAE,CAAC,IAAIA,EAAE,KAAK,OAAOD,EAAE,KAAK,CAAC,IAAI,OAAOC,EAAE,CAAC,EAAE,KAAK,UAAUD,CAAC,EAAE,OAAOE,EAAE,OAAO,YAAYA,EAAE,YAAY,KAAKqD,EAAE,EAAE,MAAM,IAAI,WAAWtD,EAAE,CAAC,EAAE,KAAK,UAAUD,CAAC,EAAE,OAAOuD,GAAG,YAAY,EAAE,KAAKrD,EAAE,MAAMqD,EAAE,EAAE,MAAM,IAAI,aAAatD,EAAE,KAAK,WAAWD,EAAEE,CAAC,CAAC,CAACD,GAAGE,EAAE,KAAKF,CAAC,CAAC,CAAC,OAAOE,CAAC,CAAC,WAAW,EAAEF,EAAE,CAAC,IAAIC,EAAED,EAAE,WAAW,OAAOC,EAAE,IAAIA,EAAED,EAAE,YAAY,GAAG,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,OAAOA,EAAE,MAAMsD,GAAG,YAAYrD,EAAE,KAAKqD,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,OAAO,EAAE,OAAQ,CAACvD,EAAEC,IAAQA,IAAJ,GAAgBD,EAAE,KAAX,MAAc,CAAC,CAAC,EAAOyD,GAAN,KAAQ,CAAjztB,MAAiztB,CAAAnD,EAAA,WAAC,YAAY,EAAEL,EAAE,CAAC,KAAK,OAAO,EAAE,KAAK,IAAI,KAAK,OAAO,WAAW,IAAI,EAAE,KAAK,IAAI,IAAIuD,GAAGvD,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAMA,EAAE,EAAE,MAAM,CAAC,EAAEC,EAAE,EAAE,SAAS,KAAK,kBAAkB,EAAEC,EAAE,KAAK,IAAIC,EAAE,EAAE,QAAQ,wBAAwB,QAAUG,KAAKN,EAAE,OAAOM,EAAE,KAAK,CAAC,IAAI,OAAOJ,EAAE,KAAK,EAAEA,EAAE,YAAqBD,EAAE,SAAX,OAAkB,cAAcA,EAAE,OAAOC,EAAE,UAAUD,EAAE,YAAYA,EAAE,gBAAgBC,EAAE,YAAYD,EAAE,cAAc,EAAEA,EAAE,uBAAuBC,EAAE,eAAeD,EAAE,sBAAsB,KAAK,eAAeC,EAAEI,EAAEH,CAAC,EAAED,EAAE,QAAQ,EAAE,MAAM,IAAI,WAAW,CAACA,EAAE,KAAK,EAAEA,EAAE,UAAUD,EAAE,MAAM,GAAG,IAAMD,EAAY,EAAE,QAAZ,SAA+B,EAAE,QAAd,WAA8B,EAAE,QAAX,OAAiB,UAAU,UAAU,KAAK,eAAeE,EAAEI,EAAEH,EAAEH,CAAC,EAAEE,EAAE,QAAQ,EAAE,KAAK,CAAC,IAAI,aAAa,KAAK,WAAWA,EAAEI,EAAEL,CAAC,CAAC,CAAC,CAAC,WAAW,EAAED,EAAEC,EAAE,CAAC,IAAIC,EAAED,EAAE,WAAWC,EAAE,IAAIA,EAAED,EAAE,YAAY,GAAG,EAAE,KAAK,EAAEA,EAAE,cAAc,EAAE,YAAYA,EAAE,YAAY,EAAEA,EAAE,qBAAqB,EAAE,eAAeA,EAAE,oBAAoB,EAAE,YAAYA,EAAE,MAAM,GAAG,EAAE,UAAUC,EAAE,KAAK,eAAe,EAAEF,EAAEC,EAAE,uBAAuB,EAAE,EAAE,QAAQ,CAAC,CAAC,eAAe,EAAED,EAAEC,EAAEC,EAAE,UAAU,CAAC,EAAE,UAAU,EAAE,QAAUA,KAAKF,EAAE,IAAI,CAAC,IAAMA,EAAY,OAAOC,GAAjB,UAAoBA,GAAG,EAAEC,EAAE,KAAK,IAAKH,GAAG,CAACA,EAAE,QAAQE,CAAC,CAAE,EAAEC,EAAE,KAAK,OAAOA,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,OAAOF,EAAE,CAAC,EAAEA,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,WAAW,EAAE,cAAcA,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAEA,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,SAAS,EAAE,OAAOA,EAAE,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAcA,EAAE,OAAf,WAAoB,EAAE,KAAKE,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,IAAI,WAAW,CAAC,OAAO,KAAK,GAAG,CAAC,mBAAmB,CAAC,OAAO,KAAK,IAAI,cAAc,CAAC,KAAK,EAAEF,EAAEC,EAAEC,EAAEC,EAAE,CAAC,IAAMG,EAAE,KAAK,IAAI,KAAK,EAAEN,EAAEC,EAAEC,EAAEC,CAAC,EAAE,OAAO,KAAK,KAAKG,CAAC,EAAEA,CAAC,CAAC,UAAU,EAAEN,EAAEC,EAAEC,EAAEC,EAAE,CAAC,IAAMG,EAAE,KAAK,IAAI,UAAU,EAAEN,EAAEC,EAAEC,EAAEC,CAAC,EAAE,OAAO,KAAK,KAAKG,CAAC,EAAEA,CAAC,CAAC,QAAQ,EAAEN,EAAEC,EAAEC,EAAEC,EAAE,CAAC,IAAMG,EAAE,KAAK,IAAI,QAAQ,EAAEN,EAAEC,EAAEC,EAAEC,CAAC,EAAE,OAAO,KAAK,KAAKG,CAAC,EAAEA,CAAC,CAAC,OAAO,EAAEN,EAAEC,EAAEC,EAAE,CAAC,IAAMC,EAAE,KAAK,IAAI,OAAO,EAAEH,EAAEC,EAAEC,CAAC,EAAE,OAAO,KAAK,KAAKC,CAAC,EAAEA,CAAC,CAAC,WAAW,EAAEH,EAAE,CAAC,IAAMC,EAAE,KAAK,IAAI,WAAW,EAAED,CAAC,EAAE,OAAO,KAAK,KAAKC,CAAC,EAAEA,CAAC,CAAC,QAAQ,EAAED,EAAE,CAAC,IAAMC,EAAE,KAAK,IAAI,QAAQ,EAAED,CAAC,EAAE,OAAO,KAAK,KAAKC,CAAC,EAAEA,CAAC,CAAC,IAAI,EAAED,EAAEC,EAAEC,EAAEC,EAAEG,EAAEC,EAAE,GAAGH,EAAE,CAAC,IAAM,EAAE,KAAK,IAAI,IAAI,EAAEJ,EAAEC,EAAEC,EAAEC,EAAEG,EAAEC,EAAEH,CAAC,EAAE,OAAO,KAAK,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAEJ,EAAE,CAAC,IAAMC,EAAE,KAAK,IAAI,MAAM,EAAED,CAAC,EAAE,OAAO,KAAK,KAAKC,CAAC,EAAEA,CAAC,CAAC,KAAK,EAAED,EAAE,CAAC,IAAMC,EAAE,KAAK,IAAI,KAAK,EAAED,CAAC,EAAE,OAAO,KAAK,KAAKC,CAAC,EAAEA,CAAC,CAAC,EAAOwD,GAAG,6BAAmCC,GAAN,KAAQ,CAAt2xB,MAAs2xB,CAAArD,EAAA,WAAC,YAAY,EAAEL,EAAE,CAAC,KAAK,IAAI,EAAE,KAAK,IAAI,IAAIuD,GAAGvD,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,IAAMA,EAAE,EAAE,MAAM,CAAC,EAAEC,EAAE,EAAE,SAAS,KAAK,kBAAkB,EAAEC,EAAE,KAAK,IAAI,eAAe,OAAO,SAASC,EAAED,EAAE,gBAAgBuD,GAAG,GAAG,EAAEnD,EAAE,EAAE,QAAQ,wBAAwB,QAAUC,KAAKP,EAAE,CAAC,IAAIA,EAAE,KAAK,OAAOO,EAAE,KAAK,CAAC,IAAI,OAAOP,EAAEE,EAAE,gBAAgBuD,GAAG,MAAM,EAAEzD,EAAE,aAAa,IAAI,KAAK,UAAUO,EAAED,CAAC,CAAC,EAAEN,EAAE,aAAa,SAASC,EAAE,MAAM,EAAED,EAAE,aAAa,eAAeC,EAAE,YAAY,EAAE,EAAED,EAAE,aAAa,OAAO,MAAM,EAAEC,EAAE,gBAAgBD,EAAE,aAAa,mBAAmBC,EAAE,eAAe,KAAK,GAAG,EAAE,KAAK,CAAC,EAAEA,EAAE,sBAAsBD,EAAE,aAAa,oBAAoB,GAAGC,EAAE,oBAAoB,EAAE,EAAE,MAAM,IAAI,WAAWD,EAAEE,EAAE,gBAAgBuD,GAAG,MAAM,EAAEzD,EAAE,aAAa,IAAI,KAAK,UAAUO,EAAED,CAAC,CAAC,EAAEN,EAAE,aAAa,SAAS,MAAM,EAAEA,EAAE,aAAa,eAAe,GAAG,EAAEA,EAAE,aAAa,OAAOC,EAAE,MAAM,EAAE,EAAY,EAAE,QAAZ,SAA+B,EAAE,QAAd,WAAqBD,EAAE,aAAa,YAAY,SAAS,EAAE,MAAM,IAAI,aAAaA,EAAE,KAAK,WAAWE,EAAEK,EAAEN,CAAC,CAAC,CAACD,GAAGG,EAAE,YAAYH,CAAC,CAAC,CAAC,OAAOG,CAAC,CAAC,WAAW,EAAEH,EAAEC,EAAE,CAAC,IAAIC,EAAED,EAAE,WAAWC,EAAE,IAAIA,EAAED,EAAE,YAAY,GAAG,IAAME,EAAE,EAAE,gBAAgBsD,GAAG,MAAM,EAAE,OAAOtD,EAAE,aAAa,IAAI,KAAK,UAAUH,EAAEC,EAAE,uBAAuB,CAAC,EAAEE,EAAE,aAAa,SAASF,EAAE,MAAM,EAAE,EAAEE,EAAE,aAAa,eAAeD,EAAE,EAAE,EAAEC,EAAE,aAAa,OAAO,MAAM,EAAEF,EAAE,cAAcE,EAAE,aAAa,mBAAmBF,EAAE,aAAa,KAAK,GAAG,EAAE,KAAK,CAAC,EAAEA,EAAE,oBAAoBE,EAAE,aAAa,oBAAoB,GAAGF,EAAE,kBAAkB,EAAE,EAAEE,CAAC,CAAC,IAAI,WAAW,CAAC,OAAO,KAAK,GAAG,CAAC,mBAAmB,CAAC,OAAO,KAAK,IAAI,cAAc,CAAC,UAAU,EAAEH,EAAE,CAAC,OAAO,KAAK,IAAI,UAAU,EAAEA,CAAC,CAAC,CAAC,KAAK,EAAEA,EAAEC,EAAEC,EAAEC,EAAE,CAAC,IAAMG,EAAE,KAAK,IAAI,KAAK,EAAEN,EAAEC,EAAEC,EAAEC,CAAC,EAAE,OAAO,KAAK,KAAKG,CAAC,CAAC,CAAC,UAAU,EAAEN,EAAEC,EAAEC,EAAEC,EAAE,CAAC,IAAMG,EAAE,KAAK,IAAI,UAAU,EAAEN,EAAEC,EAAEC,EAAEC,CAAC,EAAE,OAAO,KAAK,KAAKG,CAAC,CAAC,CAAC,QAAQ,EAAEN,EAAEC,EAAEC,EAAEC,EAAE,CAAC,IAAMG,EAAE,KAAK,IAAI,QAAQ,EAAEN,EAAEC,EAAEC,EAAEC,CAAC,EAAE,OAAO,KAAK,KAAKG,CAAC,CAAC,CAAC,OAAO,EAAEN,EAAEC,EAAEC,EAAE,CAAC,IAAMC,EAAE,KAAK,IAAI,OAAO,EAAEH,EAAEC,EAAEC,CAAC,EAAE,OAAO,KAAK,KAAKC,CAAC,CAAC,CAAC,WAAW,EAAEH,EAAE,CAAC,IAAMC,EAAE,KAAK,IAAI,WAAW,EAAED,CAAC,EAAE,OAAO,KAAK,KAAKC,CAAC,CAAC,CAAC,QAAQ,EAAED,EAAE,CAAC,IAAMC,EAAE,KAAK,IAAI,QAAQ,EAAED,CAAC,EAAE,OAAO,KAAK,KAAKC,CAAC,CAAC,CAAC,IAAI,EAAED,EAAEC,EAAEC,EAAEC,EAAEG,EAAEC,EAAE,GAAGH,EAAE,CAAC,IAAM,EAAE,KAAK,IAAI,IAAI,EAAEJ,EAAEC,EAAEC,EAAEC,EAAEG,EAAEC,EAAEH,CAAC,EAAE,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC,MAAM,EAAEJ,EAAE,CAAC,IAAMC,EAAE,KAAK,IAAI,MAAM,EAAED,CAAC,EAAE,OAAO,KAAK,KAAKC,CAAC,CAAC,CAAC,KAAK,EAAED,EAAE,CAAC,IAAMC,EAAE,KAAK,IAAI,KAAK,EAAED,CAAC,EAAE,OAAO,KAAK,KAAKC,CAAC,CAAC,CAAC,EAAK0D,EAAG,CAAC,OAAOtD,EAAA,CAACN,EAAEC,IAAI,IAAIwD,GAAGzD,EAAEC,CAAC,EAAjB,UAAmB,IAAIK,EAAA,CAACN,EAAEC,IAAI,IAAI0D,GAAG3D,EAAEC,CAAC,EAAjB,OAAmB,UAAUK,EAAAN,GAAG,IAAIwD,GAAGxD,CAAC,EAAX,aAAa,QAAQM,EAAA,IAAIkD,GAAG,QAAQ,EAAf,UAAgB,ECA3k2B,IAAMK,GAAgBC,EAAA,CAACC,EAAMC,IAAU,CACrC,IAAIC,EAAIF,EAAK,EACTG,EAAIH,EAAK,EAITI,EAAKH,EAAM,EAAIC,EACfG,EAAKJ,EAAM,EAAIE,EACfG,EAAIN,EAAK,MAAQ,EACjBO,EAAIP,EAAK,OAAS,EAElBQ,EAAIC,EACR,OAAI,KAAK,IAAIJ,CAAE,EAAIC,EAAI,KAAK,IAAIF,CAAE,EAAIG,GAEhCF,EAAK,IACPE,EAAI,CAACA,GAEPC,EAAKH,IAAO,EAAI,EAAKE,EAAIH,EAAMC,EAC/BI,EAAKF,IAGDH,EAAK,IACPE,EAAI,CAACA,GAEPE,EAAKF,EACLG,EAAKL,IAAO,EAAI,EAAKE,EAAID,EAAMD,GAG1B,CAAE,EAAGF,EAAIM,EAAI,EAAGL,EAAIM,CAAG,CAChC,EA7BsB,iBA+BfC,GAAQZ,GCrBf,SAASa,GAAWC,EAAKC,EAAS,CAC5BA,GACFD,EAAI,KAAK,QAASC,CAAO,CAE7B,CAJSC,EAAAH,GAAA,cAUT,eAAeI,GAAaC,EAAM,CAChC,IAAMC,EAAKC,EAAO,SAAS,gBAAgB,6BAA8B,eAAe,CAAC,EACnFC,EAAMF,EAAG,OAAO,WAAW,EAE7BG,EAAQJ,EAAK,MACbA,EAAK,OAASK,GAASL,EAAK,KAAK,IACnCI,EAAQ,MAAME,GAAYN,EAAK,MAAM,QAAQO,GAAO,eAAgB;AAAA,CAAI,EAAGC,EAAU,CAAC,GAExF,IAAMC,EAAaT,EAAK,OAAS,YAAc,YAC/C,OAAAG,EAAI,KACF,gBACEM,EACA,MACCT,EAAK,WAAa,UAAYA,EAAK,WAAa,IAAM,IACvD,IACAI,EACA,SACJ,EAEAT,GAAWQ,EAAKH,EAAK,UAAU,EAC/BG,EAAI,MAAM,UAAW,cAAc,EACnCA,EAAI,MAAM,gBAAiB,KAAK,EAEhCA,EAAI,MAAM,cAAe,QAAQ,EACjCA,EAAI,KAAK,QAAS,8BAA8B,EACzCF,EAAG,KAAK,CACjB,CA1BeH,EAAAC,GAAA,gBAkCf,IAAMW,GAAcZ,EAAA,MAAOa,EAAaC,EAAOC,EAASC,IAAW,CACjE,IAAIC,EAAaJ,GAAe,GAKhC,GAJI,OAAOI,GAAe,WACxBA,EAAaA,EAAW,CAAC,GAGvBC,EAASR,EAAU,EAAE,UAAU,UAAU,EAAG,CAE9CO,EAAaA,EAAW,QAAQ,UAAW,QAAQ,EACnDE,EAAI,KAAK,aAAeF,CAAU,EAClC,IAAMf,EAAO,CACX,OAAAc,EACA,MAAOI,GAAeH,CAAU,EAAE,QAChC,uBACC,GAAM,aAAa,EAAE,QAAQ,IAAK,GAAG,CAAC,QACzC,EACA,WAAYH,GAAQA,EAAM,QAAQ,QAAS,QAAQ,CACrD,EAGA,OAFiB,MAAMb,GAAaC,CAAI,CAG1C,KAAO,CACL,IAAMmB,EAAW,SAAS,gBAAgB,6BAA8B,MAAM,EAC9EA,EAAS,aAAa,QAASP,EAAM,QAAQ,SAAU,OAAO,CAAC,EAC/D,IAAIQ,EAAO,CAAC,EACR,OAAOL,GAAe,SACxBK,EAAOL,EAAW,MAAM,qBAAqB,EACpC,MAAM,QAAQA,CAAU,EACjCK,EAAOL,EAEPK,EAAO,CAAC,EAGV,QAAWC,KAAOD,EAAM,CACtB,IAAME,EAAQ,SAAS,gBAAgB,6BAA8B,OAAO,EAC5EA,EAAM,eAAe,uCAAwC,YAAa,UAAU,EACpFA,EAAM,aAAa,KAAM,KAAK,EAC9BA,EAAM,aAAa,IAAK,GAAG,EACvBT,EACFS,EAAM,aAAa,QAAS,WAAW,EAEvCA,EAAM,aAAa,QAAS,KAAK,EAEnCA,EAAM,YAAcD,EAAI,KAAK,EAC7BF,EAAS,YAAYG,CAAK,CAC5B,CACA,OAAOH,CACT,CACF,EAhDoB,eAkDbI,GAAQb,GCxGR,IAAMc,EAAyBC,EAAA,CACpCC,EACAC,EACAC,EACAC,EACAC,IAEA,CACE,IACAJ,EAAII,EACJH,EACA,IACAD,EAAIE,EAAaE,EACjB,IACAA,EACAA,EACA,EACA,EACA,EACAJ,EAAIE,EACJD,EAAIG,EACJ,IACAH,EAAIE,EAAcC,EAClB,IACAA,EACAA,EACA,EACA,EACA,EACAJ,EAAIE,EAAaE,EACjBH,EAAIE,EACJ,IACAH,EAAII,EACJ,IACAA,EACAA,EACA,EACA,EACA,EACAJ,EACAC,EAAIE,EAAcC,EAClB,IACAH,EAAIG,EACJ,IACAA,EACAA,EACA,EACA,EACA,EACAJ,EAAII,EACJH,EACA,GACF,EAAE,KAAK,GAAG,EApD0B,0BCI/B,IAAMI,GAAiBC,EAACC,GAAkB,CAC/C,GAAM,CAAE,cAAAC,CAAc,EAAIC,EAAU,EACpC,MAAO,CACL,KAAMF,EACN,aAAc,IACd,WAAY,EACZ,WAAY,EACZ,UAAW,GACX,OAAQA,EACR,KAAMC,CACR,CACF,EAX8B,kBAajBE,GAAgBJ,EAACK,GAAe,CAK3C,IAAMC,EAAYC,GAAW,CAAC,GAAIF,EAAK,mBAAqB,CAAC,EAAI,GAAIA,EAAK,WAAa,CAAC,CAAE,CAAC,EAC3F,MAAO,CAAE,UAAAC,EAAW,YAAa,CAAC,GAAGA,CAAS,CAAE,CAClD,EAP6B,iBAShBC,GAAaP,EAACQ,GAAqB,CAC9C,IAAMC,EAAW,IAAI,IACrB,OAAAD,EAAO,QAASE,GAAU,CACxB,GAAM,CAACC,EAAKC,CAAK,EAAIF,EAAM,MAAM,GAAG,EACpCD,EAAS,IAAIE,EAAI,KAAK,EAAGC,GAAO,KAAK,CAAC,CACxC,CAAC,EACMH,CACT,EAP0B,cAQbI,GAAeb,EAACW,GAEzBA,IAAQ,SACRA,IAAQ,aACRA,IAAQ,eACRA,IAAQ,eACRA,IAAQ,cACRA,IAAQ,mBACRA,IAAQ,cACRA,IAAQ,kBACRA,IAAQ,eACRA,IAAQ,kBACRA,IAAQ,gBACRA,IAAQ,eACRA,IAAQ,iBACRA,IAAQ,eACRA,IAAQ,aACRA,IAAQ,cACRA,IAAQ,iBACRA,IAAQ,UAnBgB,gBAsBfG,EAAgBd,EAACK,GAAe,CAC3C,GAAM,CAAE,YAAAU,CAAY,EAAIX,GAAcC,CAAI,EACpCW,EAAwB,CAAC,EACzBC,EAAuB,CAAC,EACxBC,EAAyB,CAAC,EAC1BC,EAA6B,CAAC,EAEpC,OAAAJ,EAAY,QAASL,GAAU,CAC7B,IAAMC,EAAMD,EAAM,CAAC,EACfG,GAAaF,CAAG,EAClBK,EAAY,KAAKN,EAAM,KAAK,GAAG,EAAI,aAAa,GAEhDO,EAAW,KAAKP,EAAM,KAAK,GAAG,EAAI,aAAa,EAC3CC,EAAI,SAAS,QAAQ,GACvBO,EAAa,KAAKR,EAAM,KAAK,GAAG,EAAI,aAAa,EAE/CC,IAAQ,QACVQ,EAAiB,KAAKT,EAAM,KAAK,GAAG,EAAI,aAAa,EAG3D,CAAC,EAEM,CACL,YAAaM,EAAY,KAAK,GAAG,EACjC,WAAYC,EAAW,KAAK,GAAG,EAC/B,YAAAF,EACA,aAAAG,EACA,iBAAAC,CACF,CACF,EA7B6B,iBAiChBC,EAAoBpB,EAAA,CAACK,EAAYgB,IAAiB,CAC7D,GAAM,CAAE,eAAAC,EAAgB,cAAApB,CAAc,EAAIC,EAAU,EAC9C,CAAE,WAAAoB,EAAY,QAAAC,CAAQ,EAAIF,EAC1B,CAAE,UAAAhB,CAAU,EAAIF,GAAcC,CAAI,EAiBxC,OAde,OAAO,OACpB,CACE,UAAW,GACX,KAAMC,EAAU,IAAI,MAAM,GAAKkB,EAC/B,UAAW,UACX,WAAY,EACZ,WAAY,IACZ,OAAQlB,EAAU,IAAI,QAAQ,GAAKiB,EACnC,KAAMrB,EACN,YAAaI,EAAU,IAAI,cAAc,GAAG,QAAQ,KAAM,EAAE,GAAK,IACjE,aAAc,CAAC,EAAG,CAAC,CACrB,EACAe,CACF,CAEF,EArBiC,qBC7EjC,IAAMI,GAAOC,EAAA,MAAOC,EAAQC,IAAS,CACnCC,EAAI,KAAK,8BAA+BD,EAAK,GAAIA,CAAI,EACrD,IAAME,EAAaC,EAAU,EACvB,CAAE,eAAAC,EAAgB,cAAAC,CAAc,EAAIH,EACpC,CAAE,WAAAI,EAAY,cAAAC,CAAc,EAAIH,EAEhC,CAAE,YAAAI,EAAa,WAAAC,EAAY,aAAAC,EAAc,iBAAAC,CAAiB,EAAIC,EAAcZ,CAAI,EAGhFa,EAAWd,EACd,OAAO,GAAG,EACV,KAAK,QAAS,WAAaC,EAAK,UAAU,EAC1C,KAAK,KAAMA,EAAK,EAAE,EAClB,KAAK,YAAaA,EAAK,IAAI,EAExBc,EAAgBC,EAASb,EAAW,UAAU,UAAU,EAGxDc,EAAUH,EAAS,OAAO,GAAG,EAAE,KAAK,QAAS,gBAAgB,EAE7DI,EAAO,MAAMC,GAAWF,EAAShB,EAAK,MAAO,CACjD,MAAOA,EAAK,WACZ,cAAAc,EACA,OAAQ,EACV,CAAC,EAGGK,EAAOF,EAAK,QAAQ,EAExB,GAAIF,EAASb,EAAW,UAAU,UAAU,EAAG,CAC7C,IAAMkB,EAAMH,EAAK,SAAS,CAAC,EACrBI,EAAKC,EAAOL,CAAI,EACtBE,EAAOC,EAAI,sBAAsB,EACjCC,EAAG,KAAK,QAASF,EAAK,KAAK,EAC3BE,EAAG,KAAK,SAAUF,EAAK,MAAM,CAC/B,CAEA,IAAMI,EAAQvB,EAAK,OAASmB,EAAK,MAAQnB,EAAK,QAAUmB,EAAK,MAAQnB,EAAK,QAAUA,EAAK,MACrFA,EAAK,OAASmB,EAAK,MAAQnB,EAAK,QAClCA,EAAK,MAAQuB,EAAQvB,EAAK,OAAS,EAAIA,EAAK,QAE5CA,EAAK,KAAO,CAACA,EAAK,QAGpB,IAAMwB,EAASxB,EAAK,OACdyB,EAAIzB,EAAK,EAAIuB,EAAQ,EACrBG,EAAI1B,EAAK,EAAIwB,EAAS,EAE5BvB,EAAI,MAAM,QAASD,EAAM,KAAK,UAAUA,CAAI,CAAC,EAC7C,IAAIH,EACJ,GAAIG,EAAK,OAAS,YAAa,CAE7B,IAAM2B,EAAKC,EAAM,IAAIf,CAAQ,EACvBgB,EAAUC,EAAkB9B,EAAM,CACtC,UAAW,GACX,KAAMM,EAEN,OAAQC,EACR,WAAY,EACZ,KAAMF,CACR,CAAC,EACK0B,EAAYJ,EAAG,KAAKK,EAAuBP,EAAGC,EAAGH,EAAOC,EAAQ,CAAC,EAAGK,CAAO,EACjFhC,EAAOgB,EAAS,OAAO,KACrBZ,EAAI,MAAM,wBAAyB8B,CAAS,EACrCA,GACN,cAAc,EAEjBlC,EAAK,OAAO,mBAAmB,EAAE,KAAK,QAASa,EAAa,KAAK,GAAG,CAAC,EACrEb,EAAK,OAAO,MAAM,EAAE,KAAK,QAASc,EAAiB,KAAK,GAAG,EAAE,QAAQ,OAAQ,QAAQ,CAAC,CACxF,MAEEd,EAAOgB,EAAS,OAAO,OAAQ,cAAc,EAE7ChB,EACG,KAAK,QAASY,CAAU,EACxB,KAAK,KAAMT,EAAK,EAAE,EAClB,KAAK,KAAMA,EAAK,EAAE,EAClB,KAAK,IAAKyB,CAAC,EACX,KAAK,IAAKC,CAAC,EACX,KAAK,QAASH,CAAK,EACnB,KAAK,SAAUC,CAAM,EAE1B,GAAM,CAAE,uBAAAS,CAAuB,EAAIC,GAAwBhC,CAAU,EAOrE,GANAc,EAAQ,KACN,YAEA,aAAahB,EAAK,EAAImB,EAAK,MAAQ,CAAC,KAAKnB,EAAK,EAAIA,EAAK,OAAS,EAAIiC,CAAsB,GAC5F,EAEIzB,EAAa,CACf,IAAM2B,EAAOnB,EAAQ,OAAO,MAAM,EAC9BmB,GACFA,EAAK,KAAK,QAAS3B,CAAW,CAElC,CAGA,IAAM4B,EAAUvC,EAAK,KAAK,EAAE,QAAQ,EACpC,OAAAG,EAAK,QAAU,EACfA,EAAK,MAAQoC,EAAQ,MACrBpC,EAAK,OAASoC,EAAQ,OAEtBpC,EAAK,QAAUmB,EAAK,OAASnB,EAAK,QAAU,EAE5CA,EAAK,UAAY,SAAUqC,EAAO,CAChC,OAAOC,GAActC,EAAMqC,CAAK,CAClC,EAEO,CAAE,QAASxB,EAAU,UAAWM,CAAK,CAC9C,EA7Ga,QAsHPoB,GAAYzC,EAAA,CAACC,EAAQC,IAAS,CAElC,IAAMa,EAAWd,EAAO,OAAO,GAAG,EAAE,KAAK,QAAS,cAAc,EAAE,KAAK,KAAMC,EAAK,EAAE,EAG9EH,EAAOgB,EAAS,OAAO,OAAQ,cAAc,EAE7C2B,EAAU,EAAIxC,EAAK,QACnByC,EAAcD,EAAU,EAG9B3C,EACG,KAAK,KAAMG,EAAK,EAAE,EAClB,KAAK,KAAMA,EAAK,EAAE,EAClB,KAAK,IAAKA,EAAK,EAAIA,EAAK,MAAQ,EAAIyC,CAAW,EAC/C,KAAK,IAAKzC,EAAK,EAAIA,EAAK,OAAS,EAAIyC,CAAW,EAChD,KAAK,QAASzC,EAAK,MAAQwC,CAAO,EAClC,KAAK,SAAUxC,EAAK,OAASwC,CAAO,EACpC,KAAK,OAAQ,MAAM,EAEtB,IAAMJ,EAAUvC,EAAK,KAAK,EAAE,QAAQ,EACpC,OAAAG,EAAK,MAAQoC,EAAQ,MACrBpC,EAAK,OAASoC,EAAQ,OAEtBpC,EAAK,UAAY,SAAUqC,EAAO,CAChC,OAAOC,GAActC,EAAMqC,CAAK,CAClC,EAEO,CAAE,QAASxB,EAAU,UAAW,CAAE,MAAO,EAAG,OAAQ,CAAE,CAAE,CACjE,EA7BkB,aA+BZ6B,GAAmB5C,EAAA,MAAOC,EAAQC,IAAS,CAC/C,IAAME,EAAaC,EAAU,EAEvB,CAAE,eAAAC,EAAgB,cAAAC,CAAc,EAAIH,EACpC,CAAE,cAAAyC,EAAe,oBAAAC,EAAqB,yBAAAC,EAA0B,WAAAC,CAAW,EAC/E1C,EAGIS,EAAWd,EACd,OAAO,GAAG,EACV,KAAK,QAASC,EAAK,UAAU,EAC7B,KAAK,KAAMA,EAAK,EAAE,EAClB,KAAK,UAAWA,EAAK,EAAE,EACvB,KAAK,YAAaA,EAAK,IAAI,EAGxB+C,EAAalC,EAAS,OAAO,IAAK,cAAc,EAGhDmC,EAAQnC,EAAS,OAAO,GAAG,EAAE,KAAK,QAAS,eAAe,EAC5DoC,EAAYpC,EAAS,OAAO,MAAM,EAEhCI,EAAO+B,EACV,KAAK,EACL,YAAY,MAAME,GAAYlD,EAAK,MAAOA,EAAK,WAAY,OAAW,EAAI,CAAC,EAG1EmB,EAAOF,EAAK,QAAQ,EAExB,GAAIF,EAASb,EAAW,UAAU,UAAU,EAAG,CAC7C,IAAMkB,EAAMH,EAAK,SAAS,CAAC,EACrBI,EAAKC,EAAOL,CAAI,EACtBE,EAAOC,EAAI,sBAAsB,EACjCC,EAAG,KAAK,QAASF,EAAK,KAAK,EAC3BE,EAAG,KAAK,SAAUF,EAAK,MAAM,CAC/B,CAGA,IAAMqB,EAAU,EAAIxC,EAAK,QACnByC,EAAcD,EAAU,EAExBjB,GACHvB,EAAK,OAASmB,EAAK,MAAQnB,EAAK,QAAUmB,EAAK,MAAQnB,EAAK,QAAUA,EAAK,OAASwC,EACnFxC,EAAK,OAASmB,EAAK,MAAQnB,EAAK,QAClCA,EAAK,MAAQuB,EAAQvB,EAAK,OAAS,EAAIA,EAAK,QAE5CA,EAAK,KAAO,CAACA,EAAK,QAGpB,IAAMwB,EAASxB,EAAK,OAASwC,EAEvBW,EAAcnD,EAAK,OAASwC,EAAUrB,EAAK,OAAS,EACpDM,EAAIzB,EAAK,EAAIuB,EAAQ,EACrBG,EAAI1B,EAAK,EAAIwB,EAAS,EAC5BxB,EAAK,MAAQuB,EACb,IAAM6B,EAASpD,EAAK,EAAIA,EAAK,OAAS,EAAIyC,EAActB,EAAK,OAAS,EAGlEtB,EACJ,GAAIG,EAAK,OAAS,YAAa,CAC7B,IAAMqD,EAAQrD,EAAK,WAAW,SAAS,0BAA0B,EAC3D2B,EAAKC,EAAM,IAAIf,CAAQ,EACvByC,EACJtD,EAAK,IAAMA,EAAK,GACZ2B,EAAG,KAAKK,EAAuBP,EAAGC,EAAGH,EAAOC,EAAQ,EAAE,EAAG,CACvD,UAAW,GACX,KAAMqB,EACN,UAAW,QACX,OAAQC,EACR,KAAMzC,CACR,CAAC,EACDsB,EAAG,UAAUF,EAAGC,EAAGH,EAAOC,EAAQ,CAAE,KAAMnB,CAAc,CAAC,EAE/DR,EAAOgB,EAAS,OAAO,IAAMyC,EAAgB,cAAc,EAC3D,IAAMC,EAAiB5B,EAAG,UAAUF,EAAG2B,EAAQ7B,EAAO4B,EAAa,CACjE,KAAME,EAAQV,EAAgBC,EAC9B,UAAWS,EAAQ,UAAY,QAC/B,OAAQP,EACR,KAAMzC,CACR,CAAC,EAEDR,EAAOgB,EAAS,OAAO,IAAMyC,EAAgB,cAAc,EAC3DL,EAAYpC,EAAS,OAAO,IAAM0C,CAAc,CAClD,MACE1D,EAAOkD,EAAW,OAAO,OAAQ,cAAc,EAI/ClD,EACG,KAAK,QAJe,OAIQ,EAC5B,KAAK,IAAK4B,CAAC,EACX,KAAK,IAAKC,CAAC,EACX,KAAK,QAASH,CAAK,EACnB,KAAK,SAAUC,CAAM,EACrB,KAAK,YAAaxB,EAAK,IAAI,EAC9BiD,EACG,KAAK,QAAS,OAAO,EACrB,KAAK,IAAKxB,CAAC,EACX,KAAK,IAAK2B,CAAM,EAChB,KAAK,QAAS7B,CAAK,EACnB,KAAK,SAAU4B,CAAW,EAG/BH,EAAM,KACJ,YACA,aAAahD,EAAK,EAAImB,EAAK,MAAQ,CAAC,KAAKO,EAAI,GAAKX,EAASb,EAAW,UAAU,UAAU,EAAI,EAAI,EAAE,GACtG,EAEA,IAAMkC,EAAUvC,EAAK,KAAK,EAAE,QAAQ,EACpC,OAAAG,EAAK,OAASoC,EAAQ,OACtBpC,EAAK,QAAU,EAEfA,EAAK,QAAUmB,EAAK,OAASnB,EAAK,QAAU,EAC5CA,EAAK,UAAYmB,EAEjBnB,EAAK,UAAY,SAAUqC,EAAO,CAChC,OAAOC,GAActC,EAAMqC,CAAK,CAClC,EAEO,CAAE,QAASxB,EAAU,UAAWM,CAAK,CAC9C,EAxHyB,oBAyHnBqC,GAAgB1D,EAAA,MAAOC,EAAQC,IAAS,CAC5CC,EAAI,KAAK,8BAA+BD,EAAK,GAAIA,CAAI,EACrD,IAAME,EAAaC,EAAU,EACvB,CAAE,eAAAC,EAAgB,cAAAC,CAAc,EAAIH,EACpC,CAAE,WAAAI,EAAY,cAAAC,CAAc,EAAIH,EAEhC,CAAE,YAAAI,EAAa,WAAAC,EAAY,aAAAC,EAAc,iBAAAC,CAAiB,EAAIC,EAAcZ,CAAI,EAGhFa,EAAWd,EACd,OAAO,GAAG,EACV,KAAK,QAAS,WAAaC,EAAK,UAAU,EAC1C,KAAK,KAAMA,EAAK,EAAE,EAClB,KAAK,YAAaA,EAAK,IAAI,EAExBc,EAAgBC,EAASb,EAAW,UAAU,UAAU,EAGxDc,EAAUH,EAAS,OAAO,GAAG,EAAE,KAAK,QAAS,gBAAgB,EAE7DI,EAAO,MAAMC,GAAWF,EAAShB,EAAK,MAAO,CACjD,MAAOA,EAAK,WACZ,cAAAc,EACA,OAAQ,GACR,MAAOd,EAAK,KACd,CAAC,EAGGmB,EAAOF,EAAK,QAAQ,EAExB,GAAIF,EAASb,EAAW,UAAU,UAAU,EAAG,CAC7C,IAAMkB,EAAMH,EAAK,SAAS,CAAC,EACrBI,EAAKC,EAAOL,CAAI,EACtBE,EAAOC,EAAI,sBAAsB,EACjCC,EAAG,KAAK,QAASF,EAAK,KAAK,EAC3BE,EAAG,KAAK,SAAUF,EAAK,MAAM,CAC/B,CAEA,IAAMI,EAAQvB,EAAK,OAASmB,EAAK,MAAQnB,EAAK,QAAUmB,EAAK,MAAQnB,EAAK,QAAUA,EAAK,MACrFA,EAAK,OAASmB,EAAK,MAAQnB,EAAK,QAClCA,EAAK,MAAQuB,EAAQvB,EAAK,OAAS,EAAIA,EAAK,QAE5CA,EAAK,KAAO,CAACA,EAAK,QAGpB,IAAMwB,EAASxB,EAAK,OACdyB,EAAIzB,EAAK,EAAIuB,EAAQ,EACrBG,EAAI1B,EAAK,EAAIwB,EAAS,EAE5BvB,EAAI,MAAM,QAASD,EAAM,KAAK,UAAUA,CAAI,CAAC,EAC7C,IAAIH,EACJ,GAAIG,EAAK,OAAS,YAAa,CAE7B,IAAM2B,EAAKC,EAAM,IAAIf,CAAQ,EACvBgB,EAAUC,EAAkB9B,EAAM,CACtC,UAAW,GACX,KAAMM,EAEN,OAAQC,EACR,WAAY,EACZ,KAAMF,CACR,CAAC,EACK0B,EAAYJ,EAAG,KAAKK,EAAuBP,EAAGC,EAAGH,EAAOC,EAAQxB,EAAK,EAAE,EAAG6B,CAAO,EACvFhC,EAAOgB,EAAS,OAAO,KACrBZ,EAAI,MAAM,wBAAyB8B,CAAS,EACrCA,GACN,cAAc,EAEjBlC,EAAK,OAAO,mBAAmB,EAAE,KAAK,QAASa,EAAa,KAAK,GAAG,CAAC,EACrEb,EAAK,OAAO,MAAM,EAAE,KAAK,QAASc,EAAiB,KAAK,GAAG,EAAE,QAAQ,OAAQ,QAAQ,CAAC,CACxF,MAEEd,EAAOgB,EAAS,OAAO,OAAQ,cAAc,EAE7ChB,EACG,KAAK,QAASY,CAAU,EACxB,KAAK,KAAMT,EAAK,EAAE,EAClB,KAAK,KAAMA,EAAK,EAAE,EAClB,KAAK,IAAKyB,CAAC,EACX,KAAK,IAAKC,CAAC,EACX,KAAK,QAASH,CAAK,EACnB,KAAK,SAAUC,CAAM,EAE1B,GAAM,CAAE,uBAAAS,CAAuB,EAAIC,GAAwBhC,CAAU,EAOrE,GANAc,EAAQ,KACN,YAEA,aAAahB,EAAK,EAAImB,EAAK,MAAQ,CAAC,KAAKnB,EAAK,EAAIA,EAAK,OAAS,EAAIiC,CAAsB,GAC5F,EAEIzB,EAAa,CACf,IAAM2B,EAAOnB,EAAQ,OAAO,MAAM,EAC9BmB,GACFA,EAAK,KAAK,QAAS3B,CAAW,CAElC,CAGA,IAAM4B,EAAUvC,EAAK,KAAK,EAAE,QAAQ,EACpC,OAAAG,EAAK,QAAU,EACfA,EAAK,MAAQoC,EAAQ,MACrBpC,EAAK,OAASoC,EAAQ,OAEtBpC,EAAK,QAAUmB,EAAK,OAASnB,EAAK,QAAU,EAE5CA,EAAK,UAAY,SAAUqC,EAAO,CAChC,OAAOC,GAActC,EAAMqC,CAAK,CAClC,EAEO,CAAE,QAASxB,EAAU,UAAWM,CAAK,CAC9C,EA9GsB,iBA+GhBsC,GAAU3D,EAAA,CAACC,EAAQC,IAAS,CAChC,IAAME,EAAaC,EAAU,EAEvB,CAAE,eAAAC,EAAgB,cAAAC,CAAc,EAAIH,EACpC,CAAE,WAAA4C,CAAW,EAAI1C,EAGjBS,EAAWd,EACd,OAAO,GAAG,EACV,KAAK,QAASC,EAAK,UAAU,EAC7B,KAAK,KAAMA,EAAK,EAAE,EAClB,KAAK,YAAaA,EAAK,IAAI,EAGxB+C,EAAalC,EAAS,OAAO,IAAK,cAAc,EAEhD2B,EAAU,EAAIxC,EAAK,QAEnBuB,EAAQvB,EAAK,MAAQwC,EAE3BxC,EAAK,KAAO,CAACA,EAAK,QAElB,IAAMwB,EAASxB,EAAK,OAASwC,EAEvBf,EAAIzB,EAAK,EAAIuB,EAAQ,EACrBG,EAAI1B,EAAK,EAAIwB,EAAS,EAC5BxB,EAAK,MAAQuB,EAGb,IAAI1B,EACJ,GAAIG,EAAK,OAAS,YAAa,CAE7B,IAAMsD,EADK1B,EAAM,IAAIf,CAAQ,EACH,UAAUY,EAAGC,EAAGH,EAAOC,EAAQ,CACvD,KAAM,YACN,UAAW,GACX,eAAgB,CAAC,CAAC,EAClB,OAAQsB,EACR,KAAMzC,CACR,CAAC,EAEDR,EAAOgB,EAAS,OAAO,IAAMyC,EAAgB,cAAc,CAC7D,MACEzD,EAAOkD,EAAW,OAAO,OAAQ,cAAc,EAI/ClD,EACG,KAAK,QAJe,SAIQ,EAC5B,KAAK,IAAK4B,CAAC,EACX,KAAK,IAAKC,CAAC,EACX,KAAK,QAASH,CAAK,EACnB,KAAK,SAAUC,CAAM,EACrB,KAAK,YAAaxB,EAAK,IAAI,EAGhC,IAAMoC,EAAUvC,EAAK,KAAK,EAAE,QAAQ,EACpC,OAAAG,EAAK,OAASoC,EAAQ,OACtBpC,EAAK,QAAU,EAEfA,EAAK,QAAU,EAEfA,EAAK,UAAY,SAAUqC,EAAO,CAChC,OAAOC,GAActC,EAAMqC,CAAK,CAClC,EAEO,CAAE,QAASxB,EAAU,UAAW,CAAC,CAAE,CAC5C,EAlEgB,WAoEV6C,GAAa7D,GACb8D,GAAS,CACb,KAAA9D,GACA,WAAA6D,GACA,iBAAAhB,GACA,UAAAH,GACA,QAAAkB,GACA,cAAAD,EACF,EAEII,GAAe,IAAI,IASVC,GAAgB/D,EAAA,MAAOgE,EAAM9D,IAAS,CACjD,IAAM+D,EAAQ/D,EAAK,OAAS,OACtBgE,EAAU,MAAML,GAAOI,CAAK,EAAED,EAAM9D,CAAI,EAC9C,OAAA4D,GAAa,IAAI5D,EAAK,GAAIgE,CAAO,EAC1BA,CACT,EAL6B,iBAetB,IAAMC,GAAQC,EAAA,IAAM,CACzBC,GAAe,IAAI,GACrB,EAFqB,SC/erB,SAASC,GAAcC,EAAMC,EAAO,CAClC,OAAOD,EAAK,UAAUC,CAAK,CAC7B,CAFSC,EAAAH,GAAA,iBAIT,IAAOI,GAAQJ,GCJf,SAASK,GAAiBC,EAAMC,EAAIC,EAAIC,EAAO,CAG7C,IAAIC,EAAKJ,EAAK,EACVK,EAAKL,EAAK,EAEVM,EAAKF,EAAKD,EAAM,EAChBI,EAAKF,EAAKF,EAAM,EAEhBK,EAAM,KAAK,KAAKP,EAAKA,EAAKM,EAAKA,EAAKL,EAAKA,EAAKI,EAAKA,CAAE,EAErDG,EAAK,KAAK,IAAKR,EAAKC,EAAKI,EAAME,CAAG,EAClCL,EAAM,EAAIC,IACZK,EAAK,CAACA,GAER,IAAIC,EAAK,KAAK,IAAKT,EAAKC,EAAKK,EAAMC,CAAG,EACtC,OAAIL,EAAM,EAAIE,IACZK,EAAK,CAACA,GAGD,CAAE,EAAGN,EAAKK,EAAI,EAAGJ,EAAKK,CAAG,CAClC,CArBSC,EAAAZ,GAAA,oBAuBT,IAAOa,GAAQb,GCrBf,SAASc,GAAgBC,EAAMC,EAAIC,EAAO,CACxC,OAAOC,GAAiBH,EAAMC,EAAIA,EAAIC,CAAK,CAC7C,CAFSE,EAAAL,GAAA,mBAIT,IAAOM,GAAQN,GCHf,SAASO,GAAcC,EAAIC,EAAIC,EAAIC,EAAI,CAIrC,IAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EACpBC,EAAIC,EAAIC,EAAIC,EACZC,EAAOC,EAAQC,EACf,EAAGC,EAcP,GAVAb,EAAKH,EAAG,EAAID,EAAG,EACfM,EAAKN,EAAG,EAAIC,EAAG,EACfO,EAAKP,EAAG,EAAID,EAAG,EAAIA,EAAG,EAAIC,EAAG,EAG7BW,EAAKR,EAAKF,EAAG,EAAII,EAAKJ,EAAG,EAAIM,EAC7BK,EAAKT,EAAKD,EAAG,EAAIG,EAAKH,EAAG,EAAIK,EAIzB,EAAAI,IAAO,GAAKC,IAAO,GAAKK,GAASN,EAAIC,CAAE,KAK3CR,EAAKF,EAAG,EAAID,EAAG,EACfK,EAAKL,EAAG,EAAIC,EAAG,EACfM,EAAKN,EAAG,EAAID,EAAG,EAAIA,EAAG,EAAIC,EAAG,EAG7BO,EAAKL,EAAKL,EAAG,EAAIO,EAAKP,EAAG,EAAIS,EAC7BE,EAAKN,EAAKJ,EAAG,EAAIM,EAAKN,EAAG,EAAIQ,EAKzB,EAAAC,IAAO,GAAKC,IAAO,GAAKO,GAASR,EAAIC,CAAE,KAK3CG,EAAQV,EAAKG,EAAKF,EAAKC,EACnBQ,IAAU,IAId,OAAAC,EAAS,KAAK,IAAID,EAAQ,CAAC,EAK3BE,EAAMV,EAAKG,EAAKF,EAAKC,EACrB,EAAIQ,EAAM,GAAKA,EAAMD,GAAUD,GAASE,EAAMD,GAAUD,EAExDE,EAAMX,EAAKG,EAAKJ,EAAKK,EACrBQ,EAAID,EAAM,GAAKA,EAAMD,GAAUD,GAASE,EAAMD,GAAUD,EAEjD,CAAE,EAAM,EAAGG,CAAE,CACtB,CA3DSE,EAAApB,GAAA,iBA6DT,SAASmB,GAASR,EAAIC,EAAI,CACxB,OAAOD,EAAKC,EAAK,CACnB,CAFSQ,EAAAD,GAAA,YAIT,IAAOE,GAAQrB,GC9Df,SAASsB,GAAiBC,EAAMC,EAAYC,EAAO,CACjD,IAAIC,EAAKH,EAAK,EACVI,EAAKJ,EAAK,EAEVK,EAAgB,CAAC,EAEjBC,EAAO,OAAO,kBACdC,EAAO,OAAO,kBACd,OAAON,EAAW,SAAY,WAChCA,EAAW,QAAQ,SAAUO,EAAO,CAClCF,EAAO,KAAK,IAAIA,EAAME,EAAM,CAAC,EAC7BD,EAAO,KAAK,IAAIA,EAAMC,EAAM,CAAC,CAC/B,CAAC,GAEDF,EAAO,KAAK,IAAIA,EAAML,EAAW,CAAC,EAClCM,EAAO,KAAK,IAAIA,EAAMN,EAAW,CAAC,GAGpC,IAAIQ,EAAON,EAAKH,EAAK,MAAQ,EAAIM,EAC7BI,EAAMN,EAAKJ,EAAK,OAAS,EAAIO,EAEjC,QAASI,EAAI,EAAGA,EAAIV,EAAW,OAAQU,IAAK,CAC1C,IAAIC,EAAKX,EAAWU,CAAC,EACjBE,EAAKZ,EAAWU,EAAIV,EAAW,OAAS,EAAIU,EAAI,EAAI,CAAC,EACrDG,EAAYC,GACdf,EACAE,EACA,CAAE,EAAGO,EAAOG,EAAG,EAAG,EAAGF,EAAME,EAAG,CAAE,EAChC,CAAE,EAAGH,EAAOI,EAAG,EAAG,EAAGH,EAAMG,EAAG,CAAE,CAClC,EACIC,GACFT,EAAc,KAAKS,CAAS,CAEhC,CAEA,OAAKT,EAAc,QAIfA,EAAc,OAAS,GAEzBA,EAAc,KAAK,SAAUW,EAAGC,EAAG,CACjC,IAAIC,EAAMF,EAAE,EAAId,EAAM,EAClBiB,EAAMH,EAAE,EAAId,EAAM,EAClBkB,EAAQ,KAAK,KAAKF,EAAMA,EAAMC,EAAMA,CAAG,EAEvCE,EAAMJ,EAAE,EAAIf,EAAM,EAClBoB,EAAML,EAAE,EAAIf,EAAM,EAClBqB,EAAQ,KAAK,KAAKF,EAAMA,EAAMC,EAAMA,CAAG,EAE3C,OAAOF,EAAQG,EAAQ,GAAKH,IAAUG,EAAQ,EAAI,CACpD,CAAC,EAEIlB,EAAc,CAAC,GAjBbL,CAkBX,CAtDSwB,EAAAzB,GAAA,oBAwDT,IAAO0B,GAAQ1B,GCpDf,IAAO2B,EAAQ,CACb,KAAAC,GACA,OAAAC,GACA,QAAAC,GACA,QAAAC,GACA,KAAAC,EACF,ECPO,SAASC,GAAqCC,EAAwBC,EAAY,CACvF,GAAM,CAAE,YAAAC,CAAY,EAAIC,EAAcF,CAAI,EAC1CA,EAAK,WAAaC,EAClB,IAAME,EAAUC,EAAeJ,CAAI,EAC/BK,EAAaF,EACZA,IACHE,EAAa,UAEf,IAAMC,EAAWP,EACd,OAAO,GAAG,EACV,KAAK,QAASM,CAAU,EACxB,KAAK,KAAML,EAAK,OAASA,EAAK,EAAE,EAE7BO,EAAS,EAET,CAAE,UAAAC,CAAU,EAAIR,EAGhBS,EAAKC,EAAM,IAAIJ,CAAQ,EACvBK,EAAUC,EAAkBZ,EAAM,CAAE,KAAM,QAAS,OAAQ,OAAQ,UAAW,OAAQ,CAAC,EAEzFA,EAAK,OAAS,cAChBW,EAAQ,UAAY,GAEtB,IAAME,EAAYJ,EAAG,OAAO,EAAG,EAAGF,EAAS,EAAGI,CAAO,EAC/CG,EAAaR,EAAS,OAAO,IAAMO,EAAW,cAAc,EAClE,OAAAC,EAAW,KAAK,QAAS,QAAQ,EAAE,KAAK,QAASC,EAAoBP,CAAS,CAAC,EAE/EQ,EAAiBhB,EAAMc,CAAU,EAEjCd,EAAK,UAAY,SAAUiB,EAAO,CAChC,OAAAC,EAAI,KAAK,mBAAoBlB,EAAMO,EAAQU,CAAK,EACzCE,EAAU,OAAOnB,EAAMO,EAAQU,CAAK,CAC7C,EAEOX,CACT,CApCgBc,EAAAtB,GAAA,UCFhB,SAASuB,GACPC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACA,CAGA,IAAMC,GAAQP,EAAKE,GAAM,EACnBM,GAAQP,EAAKE,GAAM,EAGnBM,EAAQ,KAAK,MAAMN,EAAKF,EAAIC,EAAKF,CAAE,EAGnCU,GAAMR,EAAKF,GAAM,EACjBW,GAAMR,EAAKF,GAAM,EAGjBW,EAAeF,EAAKN,EACpBS,EAAeF,EAAKN,EAGpBS,EAAW,KAAK,KAAKF,GAAgB,EAAIC,GAAgB,CAAC,EAGhE,GAAIC,EAAW,EACb,MAAM,IAAI,MAAM,oEAAoE,EAItF,IAAMC,EAAuB,KAAK,KAAK,EAAID,GAAY,CAAC,EAGlDE,EAAUT,EAAOQ,EAAuBV,EAAK,KAAK,IAAII,CAAK,GAAKH,EAAY,GAAK,GACjFW,EAAUT,EAAOO,EAAuBX,EAAK,KAAK,IAAIK,CAAK,GAAKH,EAAY,GAAK,GAGjFY,EAAa,KAAK,OAAOjB,EAAKgB,GAAWZ,GAAKL,EAAKgB,GAAWZ,CAAE,EAIlEe,EAHa,KAAK,OAAOhB,EAAKc,GAAWZ,GAAKH,EAAKc,GAAWZ,CAAE,EAGxCc,EACxBZ,GAAaa,EAAa,IAC5BA,GAAc,EAAI,KAAK,IAErB,CAACb,GAAaa,EAAa,IAC7BA,GAAc,EAAI,KAAK,IAIzB,IAAMC,EAAS,CAAC,EAChB,QAASC,EAAI,EAAGA,EAAI,GAAWA,IAAK,CAClC,IAAMC,EAAID,EAAK,GACTZ,EAAQS,EAAaI,EAAIH,EACzBI,EAAIP,EAAUZ,EAAK,KAAK,IAAIK,CAAK,EACjCe,EAAIP,EAAUZ,EAAK,KAAK,IAAII,CAAK,EACvCW,EAAO,KAAK,CAAE,EAAAG,EAAG,EAAAC,CAAE,CAAC,CACtB,CAEA,OAAOJ,CACT,CAhESK,EAAA1B,GAAA,qBAkET,eAAsB2B,GAAyCC,EAAwBC,EAAY,CACjG,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAAYP,EAAQC,EAAMO,EAAeP,CAAI,CAAC,EACzEQ,EAAIH,EAAK,MAAQL,EAAK,QAAU,GAChCS,EAAIJ,EAAK,OAASL,EAAK,QAEvBvB,EAAKgC,EAAI,EACTjC,EAAKC,GAAM,IAAMgC,EAAI,IAGrB,CAAE,UAAAC,CAAU,EAAIV,EAEhBR,EAAS,CACb,CAAE,EAAGgB,EAAI,EAAG,EAAG,CAACC,EAAI,CAAE,EACtB,CAAE,EAAG,CAACD,EAAI,EAAG,EAAG,CAACC,EAAI,CAAE,EACvB,GAAGtC,GAAkB,CAACqC,EAAI,EAAG,CAACC,EAAI,EAAG,CAACD,EAAI,EAAGC,EAAI,EAAGjC,EAAIC,EAAI,EAAK,EACjE,CAAE,EAAG+B,EAAI,EAAG,EAAGC,EAAI,CAAE,EACrB,GAAGtC,GAAkBqC,EAAI,EAAGC,EAAI,EAAGD,EAAI,EAAG,CAACC,EAAI,EAAGjC,EAAIC,EAAI,EAAI,CAChE,EAGMkC,EAAKC,EAAM,IAAIR,CAAQ,EACvBS,EAAUC,EAAkBd,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChBa,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAEtB,IAAME,EAAiBC,EAAqBxB,CAAM,EAC5CyB,EAAsBN,EAAG,KAAKI,EAAgBF,CAAO,EACrDK,EAAkBd,EAAS,OAAO,IAAMa,EAAqB,cAAc,EAEjF,OAAAC,EAAgB,KAAK,QAAS,uBAAuB,EAEjDR,GAAaV,EAAK,OAAS,aAC7BkB,EAAgB,UAAU,MAAM,EAAE,KAAK,QAASR,CAAS,EAGvDR,GAAcF,EAAK,OAAS,aAC9BkB,EAAgB,UAAU,MAAM,EAAE,KAAK,QAAShB,CAAU,EAG5DgB,EAAgB,KAAK,YAAa,aAAa1C,EAAK,CAAC,MAAM,EAE3D2C,EAAiBnB,EAAMkB,CAAe,EAEtClB,EAAK,UAAY,SAAUoB,EAAO,CAEhC,OADYC,EAAU,QAAQrB,EAAMR,EAAQ4B,CAAK,CAEnD,EAEOhB,CACT,CArDsBP,EAAAC,GAAA,cCvEf,SAASwB,EACdC,EACAC,EACAC,EACAC,EACA,CACA,OAAOH,EACJ,OAAO,UAAW,cAAc,EAChC,KACC,SACAG,EACG,IAAI,SAAUC,EAAG,CAChB,OAAOA,EAAE,EAAI,IAAMA,EAAE,CACvB,CAAC,EACA,KAAK,GAAG,CACb,EACC,KAAK,QAAS,iBAAiB,EAC/B,KAAK,YAAa,aAAe,CAACH,EAAI,EAAI,IAAMC,EAAI,EAAI,GAAG,CAChE,CAlBgBG,EAAAN,EAAA,sBCchB,eAAsBO,GAAmCC,EAAwBC,EAAY,CAC3F,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAAYP,EAAQC,EAAMO,EAAeP,CAAI,CAAC,EAEzE,EAAIK,EAAK,OAASL,EAAK,QACvBQ,EAAU,GACVC,EAAIJ,EAAK,MAAQL,EAAK,QAAUQ,EAChCE,EAAO,EACPC,EAAQF,EACRG,EAAM,CAAC,EACPC,EAAS,EACTC,EAAS,CACb,CAAE,EAAGJ,EAAOF,EAAS,EAAGI,CAAI,EAC5B,CAAE,EAAGD,EAAO,EAAGC,CAAI,EACnB,CAAE,EAAGD,EAAO,EAAGE,CAAO,EACtB,CAAE,EAAGH,EAAM,EAAGG,CAAO,EACrB,CAAE,EAAGH,EAAM,EAAGE,EAAMJ,CAAQ,EAC5B,CAAE,EAAGE,EAAOF,EAAS,EAAGI,CAAI,CAC9B,EAEIG,EACE,CAAE,UAAAC,CAAU,EAAIhB,EAEtB,GAAIA,EAAK,OAAS,YAAa,CAE7B,IAAMiB,EAAKC,EAAM,IAAId,CAAQ,EACvBe,EAAUC,EAAkBpB,EAAM,CAAC,CAAC,EACpCqB,EAAWC,EAAqBR,CAAM,EACtCS,EAAYN,EAAG,KAAKI,EAAUF,CAAO,EAE3CJ,EAAUX,EACP,OAAO,IAAMmB,EAAW,cAAc,EACtC,KAAK,YAAa,aAAa,CAACd,EAAI,CAAC,KAAK,EAAI,CAAC,GAAG,EAEjDO,GACFD,EAAQ,KAAK,QAASC,CAAS,CAEnC,MACED,EAAUS,EAAmBpB,EAAUK,EAAG,EAAGK,CAAM,EAGrD,OAAIZ,GACFa,EAAQ,KAAK,QAASb,CAAU,EAGlCuB,EAAiBzB,EAAMe,CAAO,EAE9Bf,EAAK,UAAY,SAAU0B,EAAO,CAChC,OAAOC,EAAU,QAAQ3B,EAAMc,EAAQY,CAAK,CAC9C,EAEOtB,CACT,CArDsBwB,EAAA9B,GAAA,QCTf,SAAS+B,GAAqCC,EAAwBC,EAAY,CACvF,GAAM,CAAE,WAAAC,CAAW,EAAIC,EAAcF,CAAI,EACzCA,EAAK,MAAQ,GACb,IAAMG,EAAWJ,EACd,OAAO,GAAG,EACV,KAAK,QAASK,EAAeJ,CAAI,CAAC,EAClC,KAAK,KAAMA,EAAK,OAASA,EAAK,EAAE,EAC7B,CAAE,UAAAK,CAAU,EAAIL,EAEhBM,EAAI,KAAK,IAAI,GAAIN,EAAK,OAAS,CAAC,EAEhCO,EAAS,CACb,CAAE,EAAG,EAAG,EAAGD,EAAI,CAAE,EACjB,CAAE,EAAGA,EAAI,EAAG,EAAG,CAAE,EACjB,CAAE,EAAG,EAAG,EAAG,CAACA,EAAI,CAAE,EAClB,CAAE,EAAG,CAACA,EAAI,EAAG,EAAG,CAAE,CACpB,EAGME,EAAKC,EAAM,IAAIN,CAAQ,EACvBO,EAAUC,EAAkBX,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChBU,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAaC,EAAqBN,CAAM,EACxCO,EAAYN,EAAG,KAAKI,EAAYF,CAAO,EACvCK,EAAcZ,EAAS,OAAO,IAAMW,EAAW,cAAc,EAEnE,OAAIT,GAAaL,EAAK,OAAS,aAC7Be,EAAY,UAAU,MAAM,EAAE,KAAK,QAASV,CAAS,EAGnDJ,GAAcD,EAAK,OAAS,aAC9Be,EAAY,UAAU,MAAM,EAAE,KAAK,QAASd,CAAU,EAGxDD,EAAK,MAAQ,GACbA,EAAK,OAAS,GAEdA,EAAK,UAAY,SAAUgB,EAAO,CAChC,OAAOC,EAAU,QAAQjB,EAAMO,EAAQS,CAAK,CAC9C,EAEOb,CACT,CA/CgBe,EAAApB,GAAA,UCEhB,eAAsBqB,GAAqCC,EAAwBC,EAAY,CAC7F,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,EAAM,YAAAC,CAAY,EAAI,MAAMC,EAAYR,EAAQC,EAAMQ,EAAeR,CAAI,CAAC,EAEtFS,EAASJ,EAAK,MAAQ,EAAIC,EAC5BI,EACE,CAAE,UAAAC,CAAU,EAAIX,EAEtB,GAAIA,EAAK,OAAS,YAAa,CAE7B,IAAMY,EAAKC,EAAM,IAAIT,CAAQ,EACvBU,EAAUC,EAAkBf,EAAM,CAAC,CAAC,EACpCgB,EAAYJ,EAAG,OAAO,EAAG,EAAGH,EAAS,EAAGK,CAAO,EAErDJ,EAAaN,EAAS,OAAO,IAAMY,EAAW,cAAc,EAC5DN,EAAW,KAAK,QAAS,uBAAuB,EAAE,KAAK,QAASO,EAAoBN,CAAS,CAAC,CAChG,MACED,EAAaN,EACV,OAAO,SAAU,cAAc,EAC/B,KAAK,QAAS,uBAAuB,EACrC,KAAK,QAASF,CAAU,EACxB,KAAK,IAAKO,CAAM,EAChB,KAAK,KAAM,CAAC,EACZ,KAAK,KAAM,CAAC,EAGjB,OAAAS,EAAiBlB,EAAMU,CAAU,EAEjCV,EAAK,UAAY,SAAUmB,EAAO,CAChC,OAAAC,EAAI,KAAK,mBAAoBpB,EAAMS,EAAQU,CAAK,EACzCE,EAAU,OAAOrB,EAAMS,EAAQU,CAAK,CAC7C,EAEOf,CACT,CAnCsBkB,EAAAxB,GAAA,UCDtB,SAASyB,GAAWC,EAAW,CAC7B,IAAMC,EAAU,KAAK,IAAI,KAAK,GAAK,CAAC,EAC9BC,EAAU,KAAK,IAAI,KAAK,GAAK,CAAC,EAC9BC,EAAaH,EAAI,EAEjBI,EAAU,CAAE,EAAID,EAAa,EAAKF,EAAS,EAAIE,EAAa,EAAKD,CAAQ,EACzEG,EAAU,CAAE,EAAG,EAAEF,EAAa,GAAKF,EAAS,EAAIE,EAAa,EAAKD,CAAQ,EAC1EI,EAAU,CAAE,EAAG,EAAEH,EAAa,GAAKF,EAAS,EAAG,EAAEE,EAAa,GAAKD,CAAQ,EAC3EK,EAAU,CAAE,EAAIJ,EAAa,EAAKF,EAAS,EAAG,EAAEE,EAAa,GAAKD,CAAQ,EAEhF,MAAO,KAAKG,EAAQ,CAAC,IAAIA,EAAQ,CAAC,MAAME,EAAQ,CAAC,IAAIA,EAAQ,CAAC;AAAA,uBACzCH,EAAQ,CAAC,IAAIA,EAAQ,CAAC,MAAME,EAAQ,CAAC,IAAIA,EAAQ,CAAC,EACzE,CAZSE,EAAAT,GAAA,cAcF,SAASU,GAA4CC,EAAwBC,EAAY,CAC9F,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClBD,EAAK,MAAQ,GACb,IAAMI,EAAWL,EACd,OAAO,GAAG,EACV,KAAK,QAASM,EAAeL,CAAI,CAAC,EAClC,KAAK,KAAMA,EAAK,OAASA,EAAK,EAAE,EAC7BM,EAAS,KAAK,IAAI,GAAIN,GAAM,OAAS,CAAC,EACtC,CAAE,UAAAO,CAAU,EAAIP,EAGhBQ,EAAKC,EAAM,IAAIL,CAAQ,EACvBM,EAAUC,EAAkBX,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChBU,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAaJ,EAAG,OAAO,EAAG,EAAGF,EAAS,EAAGI,CAAO,EAChDG,EAAWzB,GAAWkB,CAAM,EAC5BQ,EAAWN,EAAG,KAAKK,EAAUH,CAAO,EAEpCZ,EAAgBM,EAAS,OAAO,IAAMQ,EAAY,cAAc,EACtE,OAAAd,EAAc,OAAO,IAAMgB,CAAQ,EAE/BP,GAAaP,EAAK,OAAS,aAC7BF,EAAc,UAAU,MAAM,EAAE,KAAK,QAASS,CAAS,EAGrDL,GAAcF,EAAK,OAAS,aAC9BF,EAAc,UAAU,MAAM,EAAE,KAAK,QAASI,CAAU,EAG1Da,EAAiBf,EAAMF,CAAa,EAEpCE,EAAK,UAAY,SAAUgB,EAAO,CAChC,OAAAC,EAAI,KAAK,0BAA2BjB,EAAM,CAAE,OAAAM,EAAQ,MAAAU,CAAM,CAAC,EAC/CE,EAAU,OAAOlB,EAAMM,EAAQU,CAAK,CAElD,EAEOZ,CACT,CA5CgBP,EAAAC,GAAA,iBCfhB,SAASqB,GACPC,EACAC,EACAC,EACAC,EAAY,IACZC,EAAa,EACbC,EAAW,IACX,CACA,IAAMC,EAAS,CAAC,EAGVC,EAAiBH,EAAa,KAAK,GAAM,IAOzCI,GANeH,EAAW,KAAK,GAAM,IAGVE,IAGDJ,EAAY,GAE5C,QAASM,EAAI,EAAGA,EAAIN,EAAWM,IAAK,CAClC,IAAMC,EAAQH,EAAgBE,EAAID,EAC5BG,EAAIX,EAAUE,EAAS,KAAK,IAAIQ,CAAK,EACrCE,EAAIX,EAAUC,EAAS,KAAK,IAAIQ,CAAK,EAC3CJ,EAAO,KAAK,CAAE,EAAG,CAACK,EAAG,EAAG,CAACC,CAAE,CAAC,CAC9B,CAEA,OAAON,CACT,CA5BSO,EAAAd,GAAA,wBA8BT,eAAsBe,GACpBC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAAI,MAAMC,EAAYR,EAAQC,EAAMQ,EAAeR,CAAI,CAAC,EAChFS,EAAIJ,EAAK,OAASL,EAAK,SAAW,GAClCU,EAAIL,EAAK,QAAUL,EAAK,SAAW,GACnCd,EAAS,KAAK,IAAI,EAAGwB,EAAI,EAAG,EAE5B,CAAE,UAAAC,CAAU,EAAIX,EAEhBV,EAAS,CACb,GAAGP,GAAqB0B,EAAI,EAAG,CAACC,EAAI,EAAGxB,EAAQ,GAAI,IAAK,CAAC,EACzD,CAAE,EAAG,CAACuB,EAAI,EAAIvB,EAAQ,EAAGA,CAAO,EAChC,GAAGH,GAAqB0B,EAAI,EAAIvB,EAAS,EAAG,CAACA,EAAQA,EAAQ,GAAI,KAAM,IAAI,EAC3E,GAAGH,GAAqB0B,EAAI,EAAIvB,EAAS,EAAGA,EAAQA,EAAQ,GAAI,IAAK,IAAI,EACzE,CAAE,EAAG,CAACuB,EAAI,EAAIvB,EAAQ,EAAG,CAACwB,EAAI,CAAE,EAChC,GAAG3B,GAAqB0B,EAAI,EAAGC,EAAI,EAAGxB,EAAQ,GAAI,EAAG,EAAE,CACzD,EAEM0B,EAAa,CACjB,CAAE,EAAGH,EAAI,EAAG,EAAG,CAACC,EAAI,EAAIxB,CAAO,EAC/B,CAAE,EAAG,CAACuB,EAAI,EAAG,EAAG,CAACC,EAAI,EAAIxB,CAAO,EAChC,GAAGH,GAAqB0B,EAAI,EAAG,CAACC,EAAI,EAAGxB,EAAQ,GAAI,IAAK,CAAC,EACzD,CAAE,EAAG,CAACuB,EAAI,EAAIvB,EAAQ,EAAG,CAACA,CAAO,EACjC,GAAGH,GAAqB0B,EAAI,EAAIA,EAAI,GAAK,CAACvB,EAAQA,EAAQ,GAAI,KAAM,IAAI,EACxE,GAAGH,GAAqB0B,EAAI,EAAIA,EAAI,GAAKvB,EAAQA,EAAQ,GAAI,IAAK,IAAI,EACtE,CAAE,EAAG,CAACuB,EAAI,EAAIvB,EAAQ,EAAGwB,EAAI,CAAE,EAC/B,GAAG3B,GAAqB0B,EAAI,EAAGC,EAAI,EAAGxB,EAAQ,GAAI,EAAG,EAAE,EACvD,CAAE,EAAG,CAACuB,EAAI,EAAG,EAAGC,EAAI,EAAIxB,CAAO,EAC/B,CAAE,EAAGuB,EAAI,EAAG,EAAGC,EAAI,EAAIxB,CAAO,CAChC,EAGM2B,EAAKC,EAAM,IAAIV,CAAQ,EACvBW,EAAUC,EAAkBhB,EAAM,CAAE,KAAM,MAAO,CAAC,EAEpDA,EAAK,OAAS,cAChBe,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EADqBC,EAAqB5B,CAAM,EACT,QAAQ,IAAK,EAAE,EACtD6B,EAAqBN,EAAG,KAAKI,EAAmBF,CAAO,EACvDK,EAAWF,EAAqBN,CAAU,EAC1CS,EAAYR,EAAG,KAAKO,EAAU,CAAE,GAAGL,CAAQ,CAAC,EAC5CO,EAAsBlB,EAAS,OAAO,IAAK,cAAc,EAC/D,OAAAkB,EAAoB,OAAO,IAAMD,EAAW,cAAc,EAAE,KAAK,iBAAkB,CAAC,EACpFC,EAAoB,OAAO,IAAMH,EAAoB,cAAc,EACnEG,EAAoB,KAAK,QAAS,MAAM,EAEpCX,GAAaX,EAAK,OAAS,aAC7BsB,EAAoB,UAAU,MAAM,EAAE,KAAK,QAASX,CAAS,EAG3DT,GAAcF,EAAK,OAAS,aAC9BsB,EAAoB,UAAU,MAAM,EAAE,KAAK,QAASpB,CAAU,EAGhEoB,EAAoB,KAAK,YAAa,aAAapC,CAAM,MAAM,EAE/DoB,EAAM,KACJ,YACA,aAAa,CAACG,EAAI,EAAIvB,GAAUmB,EAAK,GAAKA,EAAK,MAAQ,GAAG,IAAI,CAACK,EAAI,GAAKV,EAAK,SAAW,GAAK,GAAKK,EAAK,GAAKA,EAAK,KAAO,GAAG,GAC7H,EAEAkB,EAAiBvB,EAAMsB,CAAmB,EAE1CtB,EAAK,UAAY,SAAUwB,EAAO,CAGhC,OAFYC,EAAU,QAAQzB,EAAMY,EAAYY,CAAK,CAGvD,EAEOpB,CACT,CA7EsBP,EAAAC,GAAA,kBC9BtB,SAAS4B,GACPC,EACAC,EACAC,EACAC,EAAY,IACZC,EAAa,EACbC,EAAW,IACX,CACA,IAAMC,EAAS,CAAC,EAGVC,EAAiBH,EAAa,KAAK,GAAM,IAOzCI,GANeH,EAAW,KAAK,GAAM,IAGVE,IAGDJ,EAAY,GAE5C,QAASM,EAAI,EAAGA,EAAIN,EAAWM,IAAK,CAClC,IAAMC,EAAQH,EAAgBE,EAAID,EAC5BG,EAAIX,EAAUE,EAAS,KAAK,IAAIQ,CAAK,EACrCE,EAAIX,EAAUC,EAAS,KAAK,IAAIQ,CAAK,EAC3CJ,EAAO,KAAK,CAAE,EAAAK,EAAG,EAAAC,CAAE,CAAC,CACtB,CAEA,OAAON,CACT,CA5BSO,EAAAd,GAAA,wBA8BT,eAAsBe,GACpBC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAAI,MAAMC,EAAYR,EAAQC,EAAMQ,EAAeR,CAAI,CAAC,EAChFS,EAAIJ,EAAK,OAASL,EAAK,SAAW,GAClCU,EAAIL,EAAK,QAAUL,EAAK,SAAW,GACnCd,EAAS,KAAK,IAAI,EAAGwB,EAAI,EAAG,EAE5B,CAAE,UAAAC,CAAU,EAAIX,EAEhBV,EAAS,CACb,GAAGP,GAAqB0B,EAAI,EAAG,CAACC,EAAI,EAAGxB,EAAQ,GAAI,IAAK,CAAC,EACzD,CAAE,EAAGuB,EAAI,EAAIvB,EAAQ,EAAG,CAACA,CAAO,EAChC,GAAGH,GAAqB0B,EAAI,EAAIvB,EAAS,EAAG,CAACA,EAAQA,EAAQ,GAAI,KAAM,IAAI,EAC3E,GAAGH,GAAqB0B,EAAI,EAAIvB,EAAS,EAAGA,EAAQA,EAAQ,GAAI,IAAK,IAAI,EACzE,CAAE,EAAGuB,EAAI,EAAIvB,EAAQ,EAAGwB,EAAI,CAAE,EAC9B,GAAG3B,GAAqB0B,EAAI,EAAGC,EAAI,EAAGxB,EAAQ,GAAI,EAAG,EAAE,CACzD,EAEM0B,EAAa,CACjB,CAAE,EAAG,CAACH,EAAI,EAAG,EAAG,CAACC,EAAI,EAAIxB,CAAO,EAChC,CAAE,EAAGuB,EAAI,EAAG,EAAG,CAACC,EAAI,EAAIxB,CAAO,EAC/B,GAAGH,GAAqB0B,EAAI,EAAG,CAACC,EAAI,EAAGxB,EAAQ,GAAI,IAAK,CAAC,EACzD,CAAE,EAAGuB,EAAI,EAAIvB,EAAQ,EAAG,CAACA,CAAO,EAChC,GAAGH,GAAqB0B,EAAI,EAAIvB,EAAS,EAAG,CAACA,EAAQA,EAAQ,GAAI,KAAM,IAAI,EAC3E,GAAGH,GAAqB0B,EAAI,EAAIvB,EAAS,EAAGA,EAAQA,EAAQ,GAAI,IAAK,IAAI,EACzE,CAAE,EAAGuB,EAAI,EAAIvB,EAAQ,EAAGwB,EAAI,CAAE,EAC9B,GAAG3B,GAAqB0B,EAAI,EAAGC,EAAI,EAAGxB,EAAQ,GAAI,EAAG,EAAE,EACvD,CAAE,EAAGuB,EAAI,EAAG,EAAGC,EAAI,EAAIxB,CAAO,EAC9B,CAAE,EAAG,CAACuB,EAAI,EAAG,EAAGC,EAAI,EAAIxB,CAAO,CACjC,EAGM2B,EAAKC,EAAM,IAAIV,CAAQ,EACvBW,EAAUC,EAAkBhB,EAAM,CAAE,KAAM,MAAO,CAAC,EAEpDA,EAAK,OAAS,cAChBe,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EADsBC,EAAqB5B,CAAM,EACT,QAAQ,IAAK,EAAE,EACvD6B,EAAsBN,EAAG,KAAKI,EAAmBF,CAAO,EACxDK,EAAWF,EAAqBN,CAAU,EAC1CS,EAAYR,EAAG,KAAKO,EAAU,CAAE,GAAGL,CAAQ,CAAC,EAC5CO,EAAuBlB,EAAS,OAAO,IAAK,cAAc,EAChE,OAAAkB,EAAqB,OAAO,IAAMD,EAAW,cAAc,EAAE,KAAK,iBAAkB,CAAC,EACrFC,EAAqB,OAAO,IAAMH,EAAqB,cAAc,EACrEG,EAAqB,KAAK,QAAS,MAAM,EAErCX,GAAaX,EAAK,OAAS,aAC7BsB,EAAqB,UAAU,MAAM,EAAE,KAAK,QAASX,CAAS,EAG5DT,GAAcF,EAAK,OAAS,aAC9BsB,EAAqB,UAAU,MAAM,EAAE,KAAK,QAASpB,CAAU,EAGjEoB,EAAqB,KAAK,YAAa,aAAa,CAACpC,CAAM,MAAM,EAEjEoB,EAAM,KACJ,YACA,aAAa,CAACG,EAAI,GAAKT,EAAK,SAAW,GAAK,GAAKK,EAAK,GAAKA,EAAK,MAAQ,GAAG,IAAI,CAACK,EAAI,GAAKV,EAAK,SAAW,GAAK,GAAKK,EAAK,GAAKA,EAAK,KAAO,GAAG,GAC9I,EAEAkB,EAAiBvB,EAAMsB,CAAoB,EAE3CtB,EAAK,UAAY,SAAUwB,EAAO,CAGhC,OAFYC,EAAU,QAAQzB,EAAMY,EAAYY,CAAK,CAGvD,EAEOpB,CACT,CA7EsBP,EAAAC,GAAA,mBC9BtB,SAAS4B,GACPC,EACAC,EACAC,EACAC,EAAY,IACZC,EAAa,EACbC,EAAW,IACX,CACA,IAAMC,EAAS,CAAC,EAGVC,EAAiBH,EAAa,KAAK,GAAM,IAOzCI,GANeH,EAAW,KAAK,GAAM,IAGVE,IAGDJ,EAAY,GAE5C,QAASM,EAAI,EAAGA,EAAIN,EAAWM,IAAK,CAClC,IAAMC,EAAQH,EAAgBE,EAAID,EAC5BG,EAAIX,EAAUE,EAAS,KAAK,IAAIQ,CAAK,EACrCE,EAAIX,EAAUC,EAAS,KAAK,IAAIQ,CAAK,EAC3CJ,EAAO,KAAK,CAAE,EAAG,CAACK,EAAG,EAAG,CAACC,CAAE,CAAC,CAC9B,CAEA,OAAON,CACT,CA5BSO,EAAAd,GAAA,wBA8BT,eAAsBe,GACpBC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAAI,MAAMC,EAAYR,EAAQC,EAAMQ,EAAeR,CAAI,CAAC,EAChFS,EAAIJ,EAAK,OAASL,EAAK,SAAW,GAClCU,EAAIL,EAAK,QAAUL,EAAK,SAAW,GACnCd,EAAS,KAAK,IAAI,EAAGwB,EAAI,EAAG,EAE5B,CAAE,UAAAC,CAAU,EAAIX,EAEhBY,EAAuB,CAC3B,GAAG7B,GAAqB0B,EAAI,EAAG,CAACC,EAAI,EAAGxB,EAAQ,GAAI,IAAK,CAAC,EACzD,CAAE,EAAG,CAACuB,EAAI,EAAIvB,EAAQ,EAAGA,CAAO,EAChC,GAAGH,GAAqB0B,EAAI,EAAIvB,EAAS,EAAG,CAACA,EAAQA,EAAQ,GAAI,KAAM,IAAI,EAC3E,GAAGH,GAAqB0B,EAAI,EAAIvB,EAAS,EAAGA,EAAQA,EAAQ,GAAI,IAAK,IAAI,EACzE,CAAE,EAAG,CAACuB,EAAI,EAAIvB,EAAQ,EAAG,CAACwB,EAAI,CAAE,EAChC,GAAG3B,GAAqB0B,EAAI,EAAGC,EAAI,EAAGxB,EAAQ,GAAI,EAAG,EAAE,CACzD,EAEM2B,EAAwB,CAC5B,GAAG9B,GAAqB,CAAC0B,EAAI,EAAIvB,EAASA,EAAS,EAAG,CAACwB,EAAI,EAAGxB,EAAQ,GAAI,IAAK,IAAI,EACnF,CAAE,EAAGuB,EAAI,EAAIvB,EAAS,EAAG,EAAGA,CAAO,EACnC,GAAGH,GAAqB,CAAC0B,EAAI,EAAIvB,EAAS,EAAG,CAACA,EAAQA,EAAQ,GAAI,EAAG,EAAE,EACvE,GAAGH,GAAqB,CAAC0B,EAAI,EAAIvB,EAAS,EAAGA,EAAQA,EAAQ,GAAI,IAAK,CAAC,EACvE,CAAE,EAAGuB,EAAI,EAAIvB,EAAS,EAAG,EAAG,CAACA,CAAO,EACpC,GAAGH,GAAqB,CAAC0B,EAAI,EAAIvB,EAASA,EAAS,EAAGwB,EAAI,EAAGxB,EAAQ,GAAI,KAAM,IAAI,CACrF,EAEM4B,EAAa,CACjB,CAAE,EAAGL,EAAI,EAAG,EAAG,CAACC,EAAI,EAAIxB,CAAO,EAC/B,CAAE,EAAG,CAACuB,EAAI,EAAG,EAAG,CAACC,EAAI,EAAIxB,CAAO,EAChC,GAAGH,GAAqB0B,EAAI,EAAG,CAACC,EAAI,EAAGxB,EAAQ,GAAI,IAAK,CAAC,EACzD,CAAE,EAAG,CAACuB,EAAI,EAAIvB,EAAQ,EAAG,CAACA,CAAO,EACjC,GAAGH,GAAqB0B,EAAI,EAAIvB,EAAS,EAAG,CAACA,EAAQA,EAAQ,GAAI,KAAM,IAAI,EAC3E,GAAGH,GAAqB0B,EAAI,EAAIvB,EAAS,EAAGA,EAAQA,EAAQ,GAAI,IAAK,IAAI,EACzE,CAAE,EAAG,CAACuB,EAAI,EAAIvB,EAAQ,EAAGwB,EAAI,CAAE,EAC/B,GAAG3B,GAAqB0B,EAAI,EAAGC,EAAI,EAAGxB,EAAQ,GAAI,EAAG,EAAE,EACvD,CAAE,EAAG,CAACuB,EAAI,EAAG,EAAGC,EAAI,EAAIxB,CAAO,EAC/B,CAAE,EAAGuB,EAAI,EAAIvB,EAASA,EAAS,EAAG,EAAGwB,EAAI,EAAIxB,CAAO,EACpD,GAAGH,GAAqB,CAAC0B,EAAI,EAAIvB,EAASA,EAAS,EAAG,CAACwB,EAAI,EAAGxB,EAAQ,GAAI,IAAK,IAAI,EACnF,CAAE,EAAGuB,EAAI,EAAIvB,EAAS,EAAG,EAAGA,CAAO,EACnC,GAAGH,GAAqB,CAAC0B,EAAI,EAAIvB,EAAS,EAAG,CAACA,EAAQA,EAAQ,GAAI,EAAG,EAAE,EACvE,GAAGH,GAAqB,CAAC0B,EAAI,EAAIvB,EAAS,EAAGA,EAAQA,EAAQ,GAAI,IAAK,CAAC,EACvE,CAAE,EAAGuB,EAAI,EAAIvB,EAAS,EAAG,EAAG,CAACA,CAAO,EACpC,GAAGH,GAAqB,CAAC0B,EAAI,EAAIvB,EAASA,EAAS,EAAGwB,EAAI,EAAGxB,EAAQ,GAAI,KAAM,IAAI,CACrF,EAGM6B,EAAKC,EAAM,IAAIZ,CAAQ,EACvBa,EAAUC,EAAkBlB,EAAM,CAAE,KAAM,MAAO,CAAC,EAEpDA,EAAK,OAAS,cAChBiB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EADqBC,EAAqBR,CAAoB,EACnB,QAAQ,IAAK,EAAE,EAC1DS,EAAqBN,EAAG,KAAKI,EAAuBF,CAAO,EAE3DK,EADsBF,EAAqBP,CAAqB,EACnB,QAAQ,IAAK,EAAE,EAC5DU,EAAsBR,EAAG,KAAKO,EAAwBL,CAAO,EAC7DO,EAAWJ,EAAqBN,CAAU,EAC1CW,EAAYV,EAAG,KAAKS,EAAU,CAAE,GAAGP,CAAQ,CAAC,EAC5CS,EAAmBtB,EAAS,OAAO,IAAK,cAAc,EAC5D,OAAAsB,EAAiB,OAAO,IAAMD,EAAW,cAAc,EAAE,KAAK,iBAAkB,CAAC,EACjFC,EAAiB,OAAO,IAAML,EAAoB,cAAc,EAChEK,EAAiB,OAAO,IAAMH,EAAqB,cAAc,EACjEG,EAAiB,KAAK,QAAS,MAAM,EAEjCf,GAAaX,EAAK,OAAS,aAC7B0B,EAAiB,UAAU,MAAM,EAAE,KAAK,QAASf,CAAS,EAGxDT,GAAcF,EAAK,OAAS,aAC9B0B,EAAiB,UAAU,MAAM,EAAE,KAAK,QAASxB,CAAU,EAG7DwB,EAAiB,KAAK,YAAa,aAAaxC,EAASA,EAAS,CAAC,MAAM,EAEzEoB,EAAM,KACJ,YACA,aAAa,CAACG,EAAI,GAAKT,EAAK,SAAW,GAAK,GAAKK,EAAK,GAAKA,EAAK,MAAQ,GAAG,IAAI,CAACK,EAAI,GAAKV,EAAK,SAAW,GAAK,GAAKK,EAAK,GAAKA,EAAK,KAAO,GAAG,GAC9I,EAEAsB,EAAiB3B,EAAM0B,CAAgB,EAEvC1B,EAAK,UAAY,SAAU4B,EAAO,CAGhC,OAFYC,EAAU,QAAQ7B,EAAMc,EAAYc,CAAK,CAGvD,EAEOxB,CACT,CAhGsBP,EAAAC,GAAA,eCxBtB,eAAsBgC,GACpBC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAAYP,EAAQC,EAAMO,EAAeP,CAAI,CAAC,EACzEQ,EAAW,GACfC,EAAY,GACRC,EAAI,KAAK,IAAIF,GAAWH,EAAK,OAASL,EAAK,SAAW,GAAK,GAAK,KAAMA,GAAM,OAAS,CAAC,EACtFW,EAAI,KAAK,IAAIF,EAAWJ,EAAK,QAAUL,EAAK,SAAW,GAAK,EAAGA,GAAM,QAAU,CAAC,EAChFY,EAASD,EAAI,EAEb,CAAE,UAAAE,CAAU,EAAIb,EAEhBc,EAAKC,EAAM,IAAIX,CAAQ,EACvBY,EAAUC,EAAkBjB,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChBgB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAaR,EACjBS,EAAcR,EACVS,EAAKF,EAAaN,EAClBS,EAAKF,EAAc,EAEnBG,EAAS,CACb,CAAE,EAAGF,EAAI,EAAG,CAAE,EACd,CAAEC,EAAO,EAAG,CAAE,EACd,CAAE,EAAG,EAAG,EAAGF,EAAc,CAAE,EAC3B,CAAEE,EAAO,EAAGF,CAAY,EACxB,CAAE,EAAGC,EAAI,EAAGD,CAAY,EACxB,GAAGI,GAAqB,CAACH,EAAI,CAACD,EAAc,EAAGP,EAAQ,GAAI,IAAK,EAAE,CACpE,EAEMY,EAAWC,EAAqBH,CAAM,EACtCI,EAAYZ,EAAG,KAAKU,EAAUR,CAAO,EAErCW,EAAUvB,EAAS,OAAO,IAAMsB,EAAW,cAAc,EAC/D,OAAAC,EAAQ,KAAK,QAAS,uBAAuB,EAEzCd,GAAab,EAAK,OAAS,aAC7B2B,EAAQ,eAAe,MAAM,EAAE,KAAK,QAASd,CAAS,EAGpDX,GAAcF,EAAK,OAAS,aAC9B2B,EAAQ,eAAe,MAAM,EAAE,KAAK,QAASzB,CAAU,EAGzDyB,EAAQ,KAAK,YAAa,aAAa,CAACjB,EAAI,CAAC,KAAK,CAACC,EAAI,CAAC,GAAG,EAE3DiB,EAAiB5B,EAAM2B,CAAO,EAE9B3B,EAAK,UAAY,SAAU6B,EAAO,CAEhC,OADYC,EAAU,QAAQ9B,EAAMsB,EAAQO,CAAK,CAEnD,EAEOzB,CACT,CA7DsB2B,EAAAjC,GAAA,mBCLf,IAAMkC,GAAsBC,EAAA,CACjCC,EACAC,EACAC,EACAC,EACAC,EACAC,IAEO,CACL,IAAIL,CAAC,IAAIC,EAAII,CAAE,GACf,IAAID,CAAE,IAAIC,CAAE,UAAUH,CAAK,KAC3B,IAAIE,CAAE,IAAIC,CAAE,UAAU,CAACH,CAAK,KAC5B,MAAMC,CAAM,GACZ,IAAIC,CAAE,IAAIC,CAAE,UAAUH,CAAK,KAC3B,MAAM,CAACC,CAAM,EACf,EAAE,KAAK,GAAG,EAfuB,uBAiBtBG,GAA2BP,EAAA,CACtCC,EACAC,EACAC,EACAC,EACAC,EACAC,IAEO,CACL,IAAIL,CAAC,IAAIC,EAAII,CAAE,GACf,IAAIL,EAAIE,CAAK,IAAID,EAAII,CAAE,GACvB,IAAID,CAAE,IAAIC,CAAE,UAAU,CAACH,CAAK,KAC5B,MAAMC,CAAM,GACZ,IAAIC,CAAE,IAAIC,CAAE,UAAUH,CAAK,KAC3B,MAAM,CAACC,CAAM,EACf,EAAE,KAAK,GAAG,EAf4B,4BAiB3BI,GAA2BR,EAAA,CACtCC,EACAC,EACAC,EACAC,EACAC,EACAC,IAEO,CAAC,IAAIL,EAAIE,EAAQ,CAAC,IAAI,CAACC,EAAS,CAAC,GAAI,IAAIC,CAAE,IAAIC,CAAE,UAAUH,CAAK,IAAI,EAAE,KAAK,GAAG,EAR/C,4BAUxC,eAAsBM,GAAuCC,EAAwBC,EAAY,CAC/F,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAAI,MAAMC,EAAYR,EAAQC,EAAMQ,EAAeR,CAAI,CAAC,EAChFS,EAAI,KAAK,IAAIJ,EAAK,MAAQL,EAAK,QAASA,EAAK,OAAS,CAAC,EACvDN,EAAKe,EAAI,EACTd,EAAKD,GAAM,IAAMe,EAAI,IACrBC,EAAI,KAAK,IAAIL,EAAK,OAASV,EAAKK,EAAK,QAASA,EAAK,QAAU,CAAC,EAEhEF,EACE,CAAE,UAAAa,CAAU,EAAIX,EAEtB,GAAIA,EAAK,OAAS,YAAa,CAE7B,IAAMY,EAAKC,EAAM,IAAIT,CAAQ,EACvBU,EAAgBlB,GAAyB,EAAG,EAAGa,EAAGC,EAAGhB,EAAIC,CAAE,EAC3DoB,EAAgBlB,GAAyB,EAAGF,EAAIc,EAAGC,EAAGhB,EAAIC,CAAE,EAC5DqB,EAAYJ,EAAG,KAAKE,EAAeG,EAAkBjB,EAAM,CAAC,CAAC,CAAC,EAC9DkB,EAAYN,EAAG,KAAKG,EAAeE,EAAkBjB,EAAM,CAAE,KAAM,MAAO,CAAC,CAAC,EAElFF,EAAWM,EAAS,OAAO,IAAMc,EAAW,cAAc,EAC1DpB,EAAWM,EAAS,OAAO,IAAMY,EAAW,cAAc,EAC1DlB,EAAS,KAAK,QAAS,uBAAuB,EAC1Ca,GACFb,EAAS,KAAK,QAASa,CAAS,CAEpC,KAAO,CACL,IAAMQ,EAAW/B,GAAoB,EAAG,EAAGqB,EAAGC,EAAGhB,EAAIC,CAAE,EACvDG,EAAWM,EACR,OAAO,OAAQ,cAAc,EAC7B,KAAK,IAAKe,CAAQ,EAClB,KAAK,QAAS,uBAAuB,EACrC,KAAK,QAASC,EAAoBT,CAAS,CAAC,EAC5C,KAAK,QAAST,CAAU,CAC7B,CAEA,OAAAJ,EAAS,KAAK,iBAAkBH,CAAE,EAClCG,EAAS,KAAK,YAAa,aAAa,CAACW,EAAI,CAAC,KAAK,EAAEC,EAAI,EAAIf,EAAG,GAAG,EAEnE0B,EAAiBrB,EAAMF,CAAQ,EAE/BQ,EAAM,KACJ,YACA,aAAa,EAAED,EAAK,MAAQ,IAAMA,EAAK,GAAKA,EAAK,MAAQ,GAAG,KAAK,EAAEA,EAAK,OAAS,IAAML,EAAK,SAAW,GAAK,KAAOK,EAAK,GAAKA,EAAK,KAAO,GAAG,GAC9I,EAEAL,EAAK,UAAY,SAAUsB,EAAO,CAChC,IAAMC,EAAMC,EAAU,KAAKxB,EAAMsB,CAAK,EAChChC,EAAIiC,EAAI,GAAKvB,EAAK,GAAK,GAE7B,GACEN,GAAM,IACL,KAAK,IAAIJ,CAAC,GAAKU,EAAK,OAAS,GAAK,GAChC,KAAK,IAAIV,CAAC,IAAMU,EAAK,OAAS,GAAK,GAClC,KAAK,IAAIuB,EAAI,GAAKvB,EAAK,GAAK,EAAE,GAAKA,EAAK,QAAU,GAAK,EAAIL,GAC/D,CACA,IAAI,EAAIA,EAAKA,GAAM,EAAKL,EAAIA,GAAMI,EAAKA,IACnC,EAAI,IACN,EAAI,KAAK,KAAK,CAAC,GAEjB,EAAIC,EAAK,EACL2B,EAAM,GAAKtB,EAAK,GAAK,GAAK,IAC5B,EAAI,CAAC,GAGPuB,EAAI,GAAK,CACX,CAEA,OAAOA,CACT,EAEOnB,CACT,CAxEsBf,EAAAS,GAAA,YC7CtB,eAAsB2B,GACpBC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAAI,MAAMC,EAAYR,EAAQC,EAAMQ,EAAeR,CAAI,CAAC,EAChFS,EAAIJ,EAAK,MAAQL,EAAK,QACtBU,EAAIL,EAAK,OAASL,EAAK,QACvBW,EAAaD,EAAI,GAEjBE,EAAI,CAACH,EAAI,EACTI,EAAI,CAACH,EAAI,EAAIC,EAAa,EAE1B,CAAE,UAAAG,CAAU,EAAId,EAGhBe,EAAKC,EAAM,IAAIZ,CAAQ,EACvBa,EAAUC,EAAkBlB,EAAM,CAAC,CAAC,EACtCA,EAAK,OAAS,cAChBiB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAM,CACV,CAAE,EAAAP,EAAG,EAAGC,EAAIF,CAAW,EACvB,CAAE,EAAG,CAACC,EAAG,EAAGC,EAAIF,CAAW,EAC3B,CAAE,EAAG,CAACC,EAAG,EAAG,CAACC,CAAE,EACf,CAAE,EAAAD,EAAG,EAAG,CAACC,CAAE,EACX,CAAE,EAAAD,EAAG,EAAAC,CAAE,EACP,CAAE,EAAG,CAACD,EAAG,EAAAC,CAAE,EACX,CAAE,EAAG,CAACD,EAAG,EAAGC,EAAIF,CAAW,CAC7B,EAEMS,EAAOL,EAAG,QACdI,EAAI,IAAKE,GAAM,CAACA,EAAE,EAAGA,EAAE,CAAC,CAAC,EACzBJ,CACF,EAEMK,EAAUlB,EAAS,OAAO,IAAMgB,EAAM,cAAc,EAC1D,OAAAE,EAAQ,KAAK,QAAS,uBAAuB,EAEzCR,GAAad,EAAK,OAAS,aAC7BsB,EAAQ,UAAU,MAAM,EAAE,KAAK,QAASR,CAAS,EAG/CZ,GAAcF,EAAK,OAAS,aAC9BsB,EAAQ,UAAU,MAAM,EAAE,KAAK,QAASpB,CAAU,EAGpDI,EAAM,KACJ,YACA,aAAaM,GAAKZ,EAAK,SAAW,GAAK,GAAKK,EAAK,GAAKA,EAAK,MAAQ,GAAG,KAAKQ,EAAIF,GAAcX,EAAK,SAAW,GAAK,GAAKK,EAAK,GAAKA,EAAK,KAAO,GAAG,GAClJ,EAEAkB,EAAiBvB,EAAMsB,CAAO,EAE9BtB,EAAK,UAAY,SAAUwB,EAAO,CAEhC,OADYC,EAAU,KAAKzB,EAAMwB,CAAK,CAExC,EAEOpB,CACT,CA/DsBsB,EAAA5B,GAAA,oBCEtB,eAAsB6B,GACpBC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,EAAM,YAAAC,CAAY,EAAI,MAAMC,EAAYR,EAAQC,EAAMQ,EAAeR,CAAI,CAAC,EAEtFS,EAAcJ,EAAK,MAAQ,EAAIC,EADzB,EAENI,EAAcL,EAAK,MAAQ,EAAIC,EAEjCK,EACE,CAAE,UAAAC,CAAU,EAAIZ,EAEtB,GAAIA,EAAK,OAAS,YAAa,CAE7B,IAAMa,EAAKC,EAAM,IAAIV,CAAQ,EACvBW,EAAeC,EAAkBhB,EAAM,CAAE,UAAW,GAAK,YAAa,GAAI,CAAC,EAE3EiB,EAAeD,EAAkBhB,EAAM,CAAE,UAAW,GAAK,YAAa,GAAI,CAAC,EAC3EkB,EAAiBL,EAAG,OAAO,EAAG,EAAGJ,EAAc,EAAGM,CAAY,EAC9DI,EAAiBN,EAAG,OAAO,EAAG,EAAGH,EAAc,EAAGO,CAAY,EAEpEN,EAAcP,EAAS,OAAO,IAAK,cAAc,EAEjDO,EACG,KAAK,QAASS,EAAoBpB,EAAK,UAAU,CAAC,EAClD,KAAK,QAASoB,EAAoBR,CAAS,CAAC,EAE/CD,EAAY,KAAK,GAAG,YAAYO,CAAc,EAC9CP,EAAY,KAAK,GAAG,YAAYQ,CAAc,CAChD,KAAO,CACLR,EAAcP,EAAS,OAAO,IAAK,cAAc,EAEjD,IAAMiB,EAAcV,EAAY,OAAO,SAAU,cAAc,EACzDW,EAAcX,EAAY,OAAO,QAAQ,EAC/CA,EAAY,KAAK,QAAS,uBAAuB,EAAE,KAAK,QAAST,CAAU,EAE3EmB,EACG,KAAK,QAAS,cAAc,EAC5B,KAAK,QAASnB,CAAU,EACxB,KAAK,IAAKO,CAAW,EACrB,KAAK,KAAM,CAAC,EACZ,KAAK,KAAM,CAAC,EAEfa,EACG,KAAK,QAAS,cAAc,EAC5B,KAAK,QAASpB,CAAU,EACxB,KAAK,IAAKQ,CAAW,EACrB,KAAK,KAAM,CAAC,EACZ,KAAK,KAAM,CAAC,CACjB,CAEA,OAAAa,EAAiBvB,EAAMW,CAAW,EAElCX,EAAK,UAAY,SAAUwB,EAAO,CAChC,OAAAC,EAAI,KAAK,yBAA0BzB,EAAMS,EAAae,CAAK,EACpDE,EAAU,OAAO1B,EAAMS,EAAae,CAAK,CAClD,EAEOpB,CACT,CA7DsBuB,EAAA7B,GAAA,gBCDf,SAAS8B,GACdC,EACAC,EACA,CAAE,OAAQ,CAAE,eAAAC,CAAe,CAAE,EAC7B,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcJ,CAAI,EACtDA,EAAK,MAAQ,GACbA,EAAK,WAAaE,EAClB,IAAMG,EAAWN,EACd,OAAO,GAAG,EACV,KAAK,QAASO,EAAeN,CAAI,CAAC,EAClC,KAAK,KAAMA,EAAK,OAASA,EAAK,EAAE,EAC7BO,EAAS,EACT,CAAE,UAAAC,CAAU,EAAIR,EAGhBS,EAAKC,EAAM,IAAIL,CAAQ,EACvB,CAAE,WAAAM,CAAW,EAAIV,EACjBW,EAAUC,EAAkBb,EAAM,CAAE,UAAW,OAAQ,CAAC,EAE1DA,EAAK,OAAS,cAChBY,EAAQ,UAAY,GAGtB,IAAME,EAAaL,EAAG,OAAO,EAAG,EAAGF,EAAS,EAAGK,CAAO,EAEhDd,EAAeO,EAAS,OAAO,IAAMS,EAAY,cAAc,EAErE,OAAAhB,EAAa,UAAU,MAAM,EAAE,KAAK,QAAS,SAASa,CAAU,cAAc,EAE1EH,GAAaA,EAAU,OAAS,GAAKR,EAAK,OAAS,aACrDF,EAAa,UAAU,MAAM,EAAE,KAAK,QAASU,CAAS,EAGpDL,GAAcH,EAAK,OAAS,aAC9BF,EAAa,UAAU,MAAM,EAAE,KAAK,QAASK,CAAU,EAGzDY,EAAiBf,EAAMF,CAAY,EAEnCE,EAAK,UAAY,SAAUgB,EAAO,CAChC,OAAAC,EAAI,KAAK,yBAA0BjB,EAAM,CAAE,OAAAO,EAAQ,MAAAS,CAAM,CAAC,EAC9CE,EAAU,OAAOlB,EAAMO,EAAQS,CAAK,CAElD,EAEOX,CACT,CA/CgBc,EAAArB,GAAA,gBCChB,eAAsBsB,GACpBC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAAI,MAAMC,EAAYR,EAAQC,EAAMQ,EAAeR,CAAI,CAAC,EAEhFS,EAAIJ,EAAK,OAASL,EAAK,SAAW,GAClCU,EAAID,EAAIJ,EAAK,OAEbM,EAAKF,EAAIJ,EAAK,OACdO,EAAS,CACb,CAAE,EAAG,EAAG,EAAG,CAACF,CAAE,EACd,CAAE,EAAGC,EAAI,EAAG,CAACD,CAAE,EACf,CAAE,EAAGC,EAAK,EAAG,EAAG,CAAE,CACpB,EAEM,CAAE,UAAAE,CAAU,EAAIb,EAGhBc,EAAKC,EAAM,IAAIX,CAAQ,EACvBY,EAAUC,EAAkBjB,EAAM,CAAC,CAAC,EACtCA,EAAK,OAAS,cAChBgB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAEtB,IAAME,EAAWC,EAAqBP,CAAM,EACtCQ,EAAYN,EAAG,KAAKI,EAAUF,CAAO,EAErClB,EAAkBM,EACrB,OAAO,IAAMgB,EAAW,cAAc,EACtC,KAAK,YAAa,aAAa,CAACV,EAAI,CAAC,KAAKA,EAAI,CAAC,GAAG,EAErD,OAAIG,GAAab,EAAK,OAAS,aAC7BF,EAAgB,eAAe,MAAM,EAAE,KAAK,QAASe,CAAS,EAG5DX,GAAcF,EAAK,OAAS,aAC9BF,EAAgB,eAAe,MAAM,EAAE,KAAK,QAASI,CAAU,EAGjEF,EAAK,MAAQS,EACbT,EAAK,OAASU,EAEdW,EAAiBrB,EAAMF,CAAe,EAEtCQ,EAAM,KACJ,YACA,aAAa,CAACD,EAAK,MAAQ,GAAKA,EAAK,GAAKA,EAAK,MAAQ,GAAG,KAAK,CAACK,EAAI,GAAKV,EAAK,SAAW,GAAK,GAAKK,EAAK,GAAKA,EAAK,KAAO,GAAG,GAC9H,EAEAL,EAAK,UAAY,SAAUsB,EAAO,CAChC,OAAAC,EAAI,KAAK,qBAAsBvB,EAAMY,EAAQU,CAAK,EAC3CE,EAAU,QAAQxB,EAAMY,EAAQU,CAAK,CAC9C,EAEOlB,CACT,CA1DsBqB,EAAA3B,GAAA,mBCFf,SAAS4B,GACdC,EACAC,EACA,CAAE,IAAAC,EAAK,OAAQ,CAAE,MAAAC,EAAO,eAAAC,CAAe,CAAE,EACzC,CACA,GAAM,CAAE,WAAAC,CAAW,EAAIC,EAAcL,CAAI,EACzCA,EAAK,MAAQ,GACb,IAAMM,EAAWP,EACd,OAAO,GAAG,EACV,KAAK,QAASQ,EAAeP,CAAI,CAAC,EAClC,KAAK,KAAMA,EAAK,OAASA,EAAK,EAAE,EAE7B,CAAE,UAAAQ,CAAU,EAAIR,EAClBS,EAAQ,KAAK,IAAI,GAAIT,GAAM,OAAS,CAAC,EACrCU,EAAS,KAAK,IAAI,GAAIV,GAAM,QAAU,CAAC,EAEvCC,IAAQ,OACVQ,EAAQ,KAAK,IAAI,GAAIT,GAAM,OAAS,CAAC,EACrCU,EAAS,KAAK,IAAI,GAAIV,GAAM,QAAU,CAAC,GAGzC,IAAMW,EAAK,GAAKF,EAAS,EACnBG,EAAK,GAAKF,EAAU,EAGpBG,EAAKC,EAAM,IAAIR,CAAQ,EACvBS,EAAUC,EAAkBhB,EAAM,CACtC,OAAQG,EAAe,UACvB,KAAMA,EAAe,SACvB,CAAC,EAEGH,EAAK,OAAS,cAChBe,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAYJ,EAAG,UAAUF,EAAGC,EAAGH,EAAOC,EAAQK,CAAO,EAErDG,EAAQZ,EAAS,OAAO,IAAMW,EAAW,cAAc,EAEzDT,GAAaR,EAAK,OAAS,aAC7BkB,EAAM,UAAU,MAAM,EAAE,KAAK,QAASV,CAAS,EAG7CJ,GAAcJ,EAAK,OAAS,aAC9BkB,EAAM,UAAU,MAAM,EAAE,KAAK,QAASd,CAAU,EAGlDe,EAAiBnB,EAAMkB,CAAK,EAC5B,IAAME,EAAUlB,GAAO,SAAW,EAClC,OAAIF,EAAK,OAASA,EAAK,SACrBA,EAAK,OAASoB,EAAU,GAAK,EAC7BpB,EAAK,QAAUoB,EAAU,GAAK,GAEhCpB,EAAK,UAAY,SAAUqB,EAAO,CAChC,OAAOC,EAAU,KAAKtB,EAAMqB,CAAK,CACnC,EACOf,CACT,CA1DgBiB,EAAAzB,GAAA,YCOhB,eAAsB0B,GACpBC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,IAAMG,EAAW,GACfC,EAAY,GACR,CAAE,SAAAC,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAAYT,EAAQC,EAAMS,EAAeT,CAAI,CAAC,EACzEU,EAAI,KAAK,IAAIN,EAAUG,EAAK,OAASP,EAAK,SAAW,GAAK,EAAGA,GAAM,OAAS,CAAC,EAC7EW,EAAI,KAAK,IAAIN,EAAWE,EAAK,QAAUP,EAAK,SAAW,GAAK,EAAGA,GAAM,QAAU,CAAC,EAChFY,EAASD,EAAI,EACb,CAAE,UAAAE,CAAU,EAAIb,EAGhBc,EAAKC,EAAM,IAAIT,CAAQ,EACvBU,EAAUC,EAAkBjB,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChBgB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAS,CACb,CAAE,EAAG,CAACR,EAAI,EAAG,EAAG,CAACC,EAAI,CAAE,EACvB,CAAE,EAAGD,EAAI,EAAIE,EAAQ,EAAG,CAACD,EAAI,CAAE,EAC/B,GAAGQ,GAAqB,CAACT,EAAI,EAAIE,EAAQ,EAAGA,EAAQ,GAAI,GAAI,GAAG,EAC/D,CAAE,EAAGF,EAAI,EAAIE,EAAQ,EAAGD,EAAI,CAAE,EAC9B,CAAE,EAAG,CAACD,EAAI,EAAG,EAAGC,EAAI,CAAE,CACxB,EAEMS,EAAWC,EAAqBH,CAAM,EACtCI,EAAYR,EAAG,KAAKM,EAAUJ,CAAO,EACrCO,EAAUjB,EAAS,OAAO,IAAMgB,EAAW,cAAc,EAC/D,OAAAC,EAAQ,KAAK,QAAS,uBAAuB,EAEzCV,GAAab,EAAK,OAAS,aAC7BuB,EAAQ,eAAe,MAAM,EAAE,KAAK,QAASV,CAAS,EAGpDX,GAAcF,EAAK,OAAS,aAC9BuB,EAAQ,eAAe,MAAM,EAAE,KAAK,QAASrB,CAAU,EAQzDsB,EAAiBxB,EAAMuB,CAAO,EAE9BvB,EAAK,UAAY,SAAUyB,EAAO,CAChC,OAAAC,EAAI,KAAK,iBAAkB1B,EAAM,CAAE,OAAAY,EAAQ,MAAAa,CAAM,CAAC,EACtCE,EAAU,QAAQ3B,EAAMkB,EAAQO,CAAK,CAEnD,EACOnB,CACT,CAzDsBsB,EAAA9B,GAAA,wBCNf,IAAM+B,GAAqBC,EAAA,CAChCC,EACAC,EACAC,EACAC,EACAC,IAEO,CACL,IAAIJ,EAAII,CAAC,IAAIH,CAAC,GACd,IAAID,EAAIE,EAAQE,CAAC,IAAIH,CAAC,GACtB,IAAID,EAAIE,CAAK,IAAID,EAAIE,EAAS,CAAC,GAC/B,IAAIH,EAAIE,EAAQE,CAAC,IAAIH,EAAIE,CAAM,GAC/B,IAAIH,EAAII,CAAC,IAAIH,EAAIE,CAAM,GACvB,IAAIH,CAAC,IAAIC,EAAIE,EAAS,CAAC,GACvB,GACF,EAAE,KAAK,GAAG,EAfsB,sBAkBlC,eAAsBE,GAAsCC,EAAwBC,EAAY,CAC9F,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAAYP,EAAQC,EAAMO,EAAeP,CAAI,CAAC,EAEzEQ,EAAI,EACJC,EAAIJ,EAAK,OAASL,EAAK,QACvBH,EAAIY,EAAID,EACRE,EAAIL,EAAK,MAAQ,EAAIR,EAAIG,EAAK,QAC9BW,EAAS,CACb,CAAE,EAAGd,EAAG,EAAG,CAAE,EACb,CAAE,EAAGa,EAAIb,EAAG,EAAG,CAAE,EACjB,CAAE,EAAGa,EAAG,EAAG,CAACD,EAAI,CAAE,EAClB,CAAE,EAAGC,EAAIb,EAAG,EAAG,CAACY,CAAE,EAClB,CAAE,EAAGZ,EAAG,EAAG,CAACY,CAAE,EACd,CAAE,EAAG,EAAG,EAAG,CAACA,EAAI,CAAE,CACpB,EAEIG,EACE,CAAE,UAAAC,CAAU,EAAIb,EAEtB,GAAIA,EAAK,OAAS,YAAa,CAE7B,IAAMc,EAAKC,EAAM,IAAIX,CAAQ,EACvBY,EAAUC,EAAkBjB,EAAM,CAAC,CAAC,EACpCkB,EAAW3B,GAAmB,EAAG,EAAGmB,EAAGD,EAAGZ,CAAC,EAC3CsB,EAAYL,EAAG,KAAKI,EAAUF,CAAO,EAE3CJ,EAAUR,EACP,OAAO,IAAMe,EAAW,cAAc,EACtC,KAAK,YAAa,aAAa,CAACT,EAAI,CAAC,KAAKD,EAAI,CAAC,GAAG,EAEjDI,GACFD,EAAQ,KAAK,QAASC,CAAS,CAEnC,MACED,EAAUQ,EAAmBhB,EAAUM,EAAGD,EAAGE,CAAM,EAGrD,OAAIT,GACFU,EAAQ,KAAK,QAASV,CAAU,EAGlCF,EAAK,MAAQU,EACbV,EAAK,OAASS,EAEdY,EAAiBrB,EAAMY,CAAO,EAE9BZ,EAAK,UAAY,SAAUsB,EAAO,CAChC,OAAOC,EAAU,QAAQvB,EAAMW,EAAQW,CAAK,CAC9C,EAEOlB,CACT,CArDsBZ,EAAAM,GAAA,WClBtB,eAAsB0B,GAAwCC,EAAwBC,EAAY,CAChG,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,MAAQ,GACbA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,CAAS,EAAI,MAAMC,EAAYN,EAAQC,EAAMM,EAAeN,CAAI,CAAC,EAEnEO,EAAI,KAAK,IAAI,GAAIP,GAAM,OAAS,CAAC,EACjC,EAAI,KAAK,IAAI,GAAIA,GAAM,QAAU,CAAC,EAElC,CAAE,UAAAQ,CAAU,EAAIR,EAGhBS,EAAKC,EAAM,IAAIN,CAAQ,EACvBO,EAAUC,EAAkBZ,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChBW,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAS,CACb,CAAE,EAAG,EAAG,EAAG,CAAE,EACb,CAAE,EAAGN,EAAG,EAAG,CAAE,EACb,CAAE,EAAG,EAAG,EAAG,CAAE,EACb,CAAE,EAAGA,EAAG,EAAG,CAAE,CACf,EAEMO,EAAWC,EAAqBF,CAAM,EACtCG,EAAYP,EAAG,KAAKK,EAAUH,CAAO,EACrCM,EAAUb,EAAS,OAAO,IAAMY,EAAW,cAAc,EAC/D,OAAAC,EAAQ,KAAK,QAAS,uBAAuB,EAEzCT,GAAaR,EAAK,OAAS,aAC7BiB,EAAQ,eAAe,MAAM,EAAE,KAAK,QAAST,CAAS,EAGpDN,GAAcF,EAAK,OAAS,aAC9BiB,EAAQ,eAAe,MAAM,EAAE,KAAK,QAASf,CAAU,EAGzDe,EAAQ,KAAK,YAAa,aAAa,CAACV,EAAI,CAAC,KAAK,CAAC,EAAI,CAAC,GAAG,EAE3DW,EAAiBlB,EAAMiB,CAAO,EAI9BjB,EAAK,UAAY,SAAUmB,EAAO,CAChC,OAAAC,EAAI,KAAK,iBAAkBpB,EAAM,CAAE,OAAAa,CAAO,CAAC,EAC/BQ,EAAU,QAAQrB,EAAMa,EAAQM,CAAK,CAEnD,EAEOf,CACT,CArDsBkB,EAAAxB,GAAA,aCCtB,eAAsByB,GACpBC,EACAC,EACA,CAAE,OAAQ,CAAE,eAAAC,EAAgB,UAAAC,CAAU,CAAE,EACxC,CACA,GAAM,CAAE,YAAAC,CAAY,EAAIC,EAAcJ,CAAI,EAC1CA,EAAK,WAAaG,EAClB,IAAME,EAAcL,EAAK,aAAe,GAClCM,EAAaN,EAAK,YAAc,GAChCO,EAAW,KAAK,IAAIF,EAAaC,CAAU,EAC3CE,EAAeN,GAAW,cAChCF,EAAK,MAAQ,KAAK,IAAIO,EAAUC,GAAgB,CAAC,EACjD,GAAM,CAAE,SAAAC,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAAI,MAAMC,EAAYb,EAAQC,EAAM,oBAAoB,EAEhFa,EAAWb,EAAK,MAAQ,IAExBc,EAASP,EACTQ,EAAQR,EACR,CAAE,WAAAS,CAAW,EAAIf,EACjB,CAAE,UAAAgB,CAAU,EAAIC,GAAclB,CAAI,EAElC,EAAI,CAACe,EAAQ,EACbI,EAAI,CAACL,EAAS,EAEdM,EAAepB,EAAK,MAAQ,EAAI,EAGhCqB,EAAKC,EAAM,IAAIb,CAAQ,EACvBc,EAAUC,EAAkBxB,EAAM,CAAE,OAAQ,OAAQ,KAAM,MAAO,CAAC,EAEpEA,EAAK,OAAS,cAChBuB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAWJ,EAAG,UAAU,EAAGF,EAAGJ,EAAOD,EAAQS,CAAO,EAEpDG,EAAa,KAAK,IAAIX,EAAOL,EAAK,KAAK,EACvCiB,EAAcb,EAASJ,EAAK,OAASU,EAErCQ,EAAYP,EAAG,UAAU,CAACK,EAAa,EAAG,CAACC,EAAc,EAAGD,EAAYC,EAAa,CACzF,GAAGJ,EACH,KAAM,cACN,OAAQ,MACV,CAAC,EAEKM,EAAYpB,EAAS,OAAO,IAAMgB,EAAU,cAAc,EAC1DK,EAAarB,EAAS,OAAO,IAAMmB,CAAS,EAElD,GAAI5B,EAAK,KAAM,CACb,IAAM+B,EAAWtB,EAAS,OAAO,GAAG,EACpCsB,EAAS,KACP,MAAM,MAAMC,GAAWhC,EAAK,KAAM,CAChC,OAAQO,EACR,MAAOA,EACP,eAAgB,EAClB,CAAC,CAAC,MACJ,EACA,IAAM0B,EAAWF,EAAS,KAAK,EAAG,QAAQ,EACpCG,EAAYD,EAAS,MACrBE,EAAaF,EAAS,OACtBG,EAAQH,EAAS,EACjBI,EAAQJ,EAAS,EACvBF,EAAS,KACP,YACA,aAAa,CAACG,EAAY,EAAIE,CAAK,IACjCvB,EACIH,EAAK,OAAS,EAAIU,EAAe,EAAIe,EAAa,EAAIE,EACtD,CAAC3B,EAAK,OAAS,EAAIU,EAAe,EAAIe,EAAa,EAAIE,CAC7D,GACF,EACAN,EAAS,KAAK,QAAS,UAAUd,EAAU,IAAI,QAAQ,GAAKD,CAAU,GAAG,CAC3E,CAEA,OAAAL,EAAM,KACJ,YACA,aAAa,CAACD,EAAK,MAAQ,GAAKA,EAAK,GAAKA,EAAK,MAAQ,GAAG,IACxDG,EAAW,CAACc,EAAc,EAAIA,EAAc,EAAIjB,EAAK,MACvD,GACF,EAEAmB,EAAU,KACR,YACA,eACEhB,EAAWH,EAAK,OAAS,EAAIU,EAAe,EAAI,CAACV,EAAK,OAAS,EAAIU,EAAe,CACpF,GACF,EAEAkB,EAAiBtC,EAAM8B,CAAU,EAEjC9B,EAAK,UAAY,SAAUuC,EAAO,CAEhC,GADAC,EAAI,KAAK,uBAAwBxC,EAAMuC,CAAK,EACxC,CAACvC,EAAK,MACR,OAAOyC,EAAU,KAAKzC,EAAMuC,CAAK,EAEnC,IAAMG,EAAK1C,EAAK,GAAK,EACf2C,EAAK3C,EAAK,GAAK,EACf4C,EAAa5C,EAAK,QAAU,EAC9B6C,EAAS,CAAC,EACd,OAAIhC,EACFgC,EAAS,CACP,CAAE,EAAGH,EAAKhC,EAAK,MAAQ,EAAG,EAAGiC,EAAKC,EAAa,CAAE,EACjD,CAAE,EAAGF,EAAKhC,EAAK,MAAQ,EAAG,EAAGiC,EAAKC,EAAa,CAAE,EACjD,CAAE,EAAGF,EAAKhC,EAAK,MAAQ,EAAG,EAAGiC,EAAKC,EAAa,EAAIlC,EAAK,OAASU,CAAa,EAC9E,CAAE,EAAGsB,EAAK3B,EAAQ,EAAG,EAAG4B,EAAKC,EAAa,EAAIlC,EAAK,OAASU,CAAa,EACzE,CAAE,EAAGsB,EAAK3B,EAAQ,EAAG,EAAG4B,EAAKC,EAAa,CAAE,EAC5C,CAAE,EAAGF,EAAK3B,EAAQ,EAAG,EAAG4B,EAAKC,EAAa,CAAE,EAC5C,CAAE,EAAGF,EAAK3B,EAAQ,EAAG,EAAG4B,EAAKC,EAAa,EAAIlC,EAAK,OAASU,CAAa,EACzE,CAAE,EAAGsB,EAAKhC,EAAK,MAAQ,EAAG,EAAGiC,EAAKC,EAAa,EAAIlC,EAAK,OAASU,CAAa,CAChF,EAEAyB,EAAS,CACP,CAAE,EAAGH,EAAK3B,EAAQ,EAAG,EAAG4B,EAAKC,EAAa,CAAE,EAC5C,CAAE,EAAGF,EAAK3B,EAAQ,EAAG,EAAG4B,EAAKC,EAAa,CAAE,EAC5C,CAAE,EAAGF,EAAK3B,EAAQ,EAAG,EAAG4B,EAAKC,EAAa,EAAI9B,CAAO,EACrD,CAAE,EAAG4B,EAAKhC,EAAK,MAAQ,EAAG,EAAGiC,EAAKC,EAAa,EAAI9B,CAAO,EAC1D,CAAE,EAAG4B,EAAKhC,EAAK,MAAQ,EAAI,EAAG,EAAGiC,EAAKC,EAAa,CAAE,EACrD,CAAE,EAAGF,EAAKhC,EAAK,MAAQ,EAAG,EAAGiC,EAAKC,EAAa,CAAE,EACjD,CAAE,EAAGF,EAAKhC,EAAK,MAAQ,EAAG,EAAGiC,EAAKC,EAAa,EAAI9B,CAAO,EAC1D,CAAE,EAAG4B,EAAK3B,EAAQ,EAAG,EAAG4B,EAAKC,EAAa,EAAI9B,CAAO,CACvD,EAGU2B,EAAU,QAAQzC,EAAM6C,EAAQN,CAAK,CAEnD,EAEO9B,CACT,CAhIsBqC,EAAAhD,GAAA,QCAtB,eAAsBiD,GACpBC,EACAC,EACA,CAAE,OAAQ,CAAE,eAAAC,EAAgB,UAAAC,CAAU,CAAE,EACxC,CACA,GAAM,CAAE,YAAAC,CAAY,EAAIC,EAAcJ,CAAI,EAC1CA,EAAK,WAAaG,EAClB,IAAME,EAAcL,EAAK,aAAe,GAClCM,EAAaN,EAAK,YAAc,GAChCO,EAAW,KAAK,IAAIF,EAAaC,CAAU,EAC3CE,EAAeN,GAAW,cAChCF,EAAK,MAAQ,KAAK,IAAIO,EAAUC,GAAgB,CAAC,EACjD,GAAM,CAAE,SAAAC,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAAI,MAAMC,EAAYb,EAAQC,EAAM,oBAAoB,EAEhFa,EAAU,GACVC,EAAed,EAAK,MAAQ,EAAI,EAEhCe,EAAWf,EAAK,MAAQ,IAExB,CAAE,WAAAgB,EAAY,QAAAC,CAAQ,EAAIhB,EAC1B,CAAE,UAAAiB,CAAU,EAAIC,GAAcnB,CAAI,EAElCoB,EAAKC,EAAM,IAAIZ,CAAQ,EACvBa,EAAUC,EAAkBvB,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChBsB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAEtB,IAAME,EAAON,EAAU,IAAI,MAAM,EACjCI,EAAQ,OAASE,GAAQP,EAEzB,IAAMQ,EAAWhB,EAAS,OAAO,GAAG,EAChCT,EAAK,MACPyB,EAAS,KACP,MAAM,MAAMC,GAAW1B,EAAK,KAAM,CAChC,OAAQO,EACR,MAAOA,EACP,eAAgB,EAClB,CAAC,CAAC,MACJ,EAEF,IAAMoB,EAAWF,EAAS,KAAK,EAAG,QAAQ,EACpCG,EAAYD,EAAS,MACrBE,EAAaF,EAAS,OACtBG,EAAQH,EAAS,EACjBI,EAAQJ,EAAS,EAEjBK,EAAW,KAAK,IAAIJ,EAAWC,CAAU,EAAI,KAAK,MAAQhB,EAAU,EACpEoB,EAAWb,EAAG,OAAO,EAAG,EAAGY,EAAUV,CAAO,EAE5CY,EAAa,KAAK,IAAIF,EAAUtB,EAAK,KAAK,EAC1CyB,EAAcH,EAAWtB,EAAK,OAASI,EAEvCsB,EAAYhB,EAAG,UAAU,CAACc,EAAa,EAAG,CAACC,EAAc,EAAGD,EAAYC,EAAa,CACzF,GAAGb,EACH,KAAM,cACN,OAAQ,MACV,CAAC,EAEKe,EAAY5B,EAAS,OAAO,IAAMwB,EAAU,cAAc,EAC1DK,EAAa7B,EAAS,OAAO,IAAM2B,CAAS,EAClD,OAAAX,EAAS,KACP,YACA,aAAa,CAACG,EAAY,EAAIE,CAAK,IACjCf,EACIL,EAAK,OAAS,EAAII,EAAe,EAAIe,EAAa,EAAIE,EACtD,CAACrB,EAAK,OAAS,EAAII,EAAe,EAAIe,EAAa,EAAIE,CAC7D,GACF,EACAN,EAAS,KAAK,QAAS,UAAUP,EAAU,IAAI,QAAQ,GAAKF,CAAU,GAAG,EACzEL,EAAM,KACJ,YACA,aAAa,CAACD,EAAK,MAAQ,GAAKA,EAAK,GAAKA,EAAK,MAAQ,GAAG,IACxDK,EAAW,CAACoB,EAAc,EAAIA,EAAc,EAAIzB,EAAK,MACvD,GACF,EAEA2B,EAAU,KACR,YACA,eACEtB,EAAWL,EAAK,OAAS,EAAII,EAAe,EAAI,CAACJ,EAAK,OAAS,EAAII,EAAe,CACpF,GACF,EAEAyB,EAAiBvC,EAAMsC,CAAU,EAEjCtC,EAAK,UAAY,SAAUwC,EAAO,CAChC,OAAAC,EAAI,KAAK,uBAAwBzC,EAAMwC,CAAK,EAChCE,EAAU,KAAK1C,EAAMwC,CAAK,CAExC,EAEO/B,CACT,CA9FsBkC,EAAA7C,GAAA,cCCtB,eAAsB8C,GACpBC,EACAC,EACA,CAAE,OAAQ,CAAE,eAAAC,EAAgB,UAAAC,CAAU,CAAE,EACxC,CACA,GAAM,CAAE,YAAAC,CAAY,EAAIC,EAAcJ,CAAI,EAC1CA,EAAK,WAAaG,EAClB,IAAME,EAAcL,EAAK,aAAe,GAClCM,EAAaN,EAAK,YAAc,GAChCO,EAAW,KAAK,IAAIF,EAAaC,CAAU,EAC3CE,EAAeN,GAAW,cAChCF,EAAK,MAAQ,KAAK,IAAIO,EAAUC,GAAgB,CAAC,EACjD,GAAM,CAAE,SAAAC,EAAU,KAAAC,EAAM,YAAAC,EAAa,MAAAC,CAAM,EAAI,MAAMC,EACnDd,EACAC,EACA,oBACF,EAEMc,EAAWd,EAAK,MAAQ,IAExBe,EAASR,EAAWI,EAAc,EAClCK,EAAQT,EAAWI,EAAc,EACjC,CAAE,WAAAM,EAAY,QAAAC,CAAQ,EAAIjB,EAC1B,CAAE,UAAAkB,CAAU,EAAIC,GAAcpB,CAAI,EAElCqB,EAAI,CAACL,EAAQ,EACbM,EAAI,CAACP,EAAS,EAEdQ,EAAevB,EAAK,MAAQ,EAAI,EAGhCwB,EAAKC,EAAM,IAAIhB,CAAQ,EACvBiB,EAAUC,EAAkB3B,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChB0B,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAEtB,IAAME,EAAOT,EAAU,IAAI,MAAM,EACjCO,EAAQ,OAASE,GAAQV,EAEzB,IAAMW,EAAWL,EAAG,KAAKM,EAAuBT,EAAGC,EAAGN,EAAOD,EAAQ,CAAC,EAAGW,CAAO,EAE1EK,EAAa,KAAK,IAAIf,EAAON,EAAK,KAAK,EACvCsB,EAAcjB,EAASL,EAAK,OAASa,EAErCU,EAAYT,EAAG,UAAU,CAACO,EAAa,EAAG,CAACC,EAAc,EAAGD,EAAYC,EAAa,CACzF,GAAGN,EACH,KAAM,cACN,OAAQ,MACV,CAAC,EAEKQ,EAAYzB,EAAS,OAAO,IAAMoB,EAAU,cAAc,EAAE,KAAK,QAAS,aAAa,EACvFM,EAAa1B,EAAS,OAAO,IAAMwB,CAAS,EAElD,GAAIjC,EAAK,KAAM,CACb,IAAMoC,EAAW3B,EAAS,OAAO,GAAG,EACpC2B,EAAS,KACP,MAAM,MAAMC,GAAWrC,EAAK,KAAM,CAChC,OAAQO,EACR,MAAOA,EACP,eAAgB,EAClB,CAAC,CAAC,MACJ,EACA,IAAM+B,EAAWF,EAAS,KAAK,EAAG,QAAQ,EACpCG,EAAYD,EAAS,MACrBE,EAAaF,EAAS,OACtBG,EAAQH,EAAS,EACjBI,EAAQJ,EAAS,EACvBF,EAAS,KACP,YACA,aAAa,CAACG,EAAY,EAAIE,CAAK,IACjC3B,EACIJ,EAAK,OAAS,EAAIa,EAAe,EAAIiB,EAAa,EAAIE,EACtD,CAAChC,EAAK,OAAS,EAAIa,EAAe,EAAIiB,EAAa,EAAIE,CAC7D,GACF,EACAN,EAAS,KAAK,QAAS,UAAUjB,EAAU,IAAI,QAAQ,GAAKF,CAAU,GAAG,CAC3E,CAEA,OAAAL,EAAM,KACJ,YACA,aAAa,CAACF,EAAK,MAAQ,GAAKA,EAAK,GAAKA,EAAK,MAAQ,GAAG,IACxDI,EAAW,CAACkB,EAAc,EAAIA,EAAc,EAAItB,EAAK,MACvD,GACF,EAEAwB,EAAU,KACR,YACA,eACEpB,EAAWJ,EAAK,OAAS,EAAIa,EAAe,EAAI,CAACb,EAAK,OAAS,EAAIa,EAAe,CACpF,GACF,EAEAoB,EAAiB3C,EAAMmC,CAAU,EAEjCnC,EAAK,UAAY,SAAU4C,EAAO,CAEhC,GADAC,EAAI,KAAK,uBAAwB7C,EAAM4C,CAAK,EACxC,CAAC5C,EAAK,MACR,OAAO8C,EAAU,KAAK9C,EAAM4C,CAAK,EAEnC,IAAMG,EAAK/C,EAAK,GAAK,EACfgD,EAAKhD,EAAK,GAAK,EACfiD,EAAajD,EAAK,QAAU,EAC9BkD,EAAS,CAAC,EACd,OAAIpC,EACFoC,EAAS,CACP,CAAE,EAAGH,EAAKrC,EAAK,MAAQ,EAAG,EAAGsC,EAAKC,EAAa,CAAE,EACjD,CAAE,EAAGF,EAAKrC,EAAK,MAAQ,EAAG,EAAGsC,EAAKC,EAAa,CAAE,EACjD,CAAE,EAAGF,EAAKrC,EAAK,MAAQ,EAAG,EAAGsC,EAAKC,EAAa,EAAIvC,EAAK,OAASa,CAAa,EAC9E,CAAE,EAAGwB,EAAK/B,EAAQ,EAAG,EAAGgC,EAAKC,EAAa,EAAIvC,EAAK,OAASa,CAAa,EACzE,CAAE,EAAGwB,EAAK/B,EAAQ,EAAG,EAAGgC,EAAKC,EAAa,CAAE,EAC5C,CAAE,EAAGF,EAAK/B,EAAQ,EAAG,EAAGgC,EAAKC,EAAa,CAAE,EAC5C,CAAE,EAAGF,EAAK/B,EAAQ,EAAG,EAAGgC,EAAKC,EAAa,EAAIvC,EAAK,OAASa,CAAa,EACzE,CAAE,EAAGwB,EAAKrC,EAAK,MAAQ,EAAG,EAAGsC,EAAKC,EAAa,EAAIvC,EAAK,OAASa,CAAa,CAChF,EAEA2B,EAAS,CACP,CAAE,EAAGH,EAAK/B,EAAQ,EAAG,EAAGgC,EAAKC,EAAa,CAAE,EAC5C,CAAE,EAAGF,EAAK/B,EAAQ,EAAG,EAAGgC,EAAKC,EAAa,CAAE,EAC5C,CAAE,EAAGF,EAAK/B,EAAQ,EAAG,EAAGgC,EAAKC,EAAa,EAAIlC,CAAO,EACrD,CAAE,EAAGgC,EAAKrC,EAAK,MAAQ,EAAG,EAAGsC,EAAKC,EAAa,EAAIlC,CAAO,EAC1D,CAAE,EAAGgC,EAAKrC,EAAK,MAAQ,EAAI,EAAG,EAAGsC,EAAKC,EAAa,CAAE,EACrD,CAAE,EAAGF,EAAKrC,EAAK,MAAQ,EAAG,EAAGsC,EAAKC,EAAa,CAAE,EACjD,CAAE,EAAGF,EAAKrC,EAAK,MAAQ,EAAG,EAAGsC,EAAKC,EAAa,EAAIlC,CAAO,EAC1D,CAAE,EAAGgC,EAAK/B,EAAQ,EAAG,EAAGgC,EAAKC,EAAa,EAAIlC,CAAO,CACvD,EAGU+B,EAAU,QAAQ9C,EAAMkD,EAAQN,CAAK,CAEnD,EAEOnC,CACT,CAtIsB0C,EAAArD,GAAA,eCAtB,eAAsBsD,GACpBC,EACAC,EACA,CAAE,OAAQ,CAAE,eAAAC,EAAgB,UAAAC,CAAU,CAAE,EACxC,CACA,GAAM,CAAE,YAAAC,CAAY,EAAIC,EAAcJ,CAAI,EAC1CA,EAAK,WAAaG,EAClB,IAAME,EAAcL,EAAK,aAAe,GAClCM,EAAaN,EAAK,YAAc,GAChCO,EAAW,KAAK,IAAIF,EAAaC,CAAU,EAC3CE,EAAeN,GAAW,cAChCF,EAAK,MAAQ,KAAK,IAAIO,EAAUC,GAAgB,CAAC,EACjD,GAAM,CAAE,SAAAC,EAAU,KAAAC,EAAM,YAAAC,EAAa,MAAAC,CAAM,EAAI,MAAMC,EACnDd,EACAC,EACA,oBACF,EAEMc,EAAWd,EAAK,MAAQ,IAExBe,EAASR,EAAWI,EAAc,EAClCK,EAAQT,EAAWI,EAAc,EACjC,CAAE,WAAAM,EAAY,QAAAC,CAAQ,EAAIjB,EAC1B,CAAE,UAAAkB,CAAU,EAAIC,GAAcpB,CAAI,EAElCqB,EAAI,CAACL,EAAQ,EACbM,EAAI,CAACP,EAAS,EAEdQ,EAAevB,EAAK,MAAQ,EAAI,EAGhCwB,EAAKC,EAAM,IAAIhB,CAAQ,EACvBiB,EAAUC,EAAkB3B,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChB0B,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAEtB,IAAME,EAAOT,EAAU,IAAI,MAAM,EACjCO,EAAQ,OAASE,GAAQV,EAEzB,IAAMW,EAAWL,EAAG,KAAKM,EAAuBT,EAAGC,EAAGN,EAAOD,EAAQ,EAAG,EAAGW,CAAO,EAE5EK,EAAa,KAAK,IAAIf,EAAON,EAAK,KAAK,EACvCsB,EAAcjB,EAASL,EAAK,OAASa,EAErCU,EAAYT,EAAG,UAAU,CAACO,EAAa,EAAG,CAACC,EAAc,EAAGD,EAAYC,EAAa,CACzF,GAAGN,EACH,KAAM,cACN,OAAQ,MACV,CAAC,EAEKQ,EAAYzB,EAAS,OAAO,IAAMoB,EAAU,cAAc,EAC1DM,EAAa1B,EAAS,OAAO,IAAMwB,CAAS,EAElD,GAAIjC,EAAK,KAAM,CACb,IAAMoC,EAAW3B,EAAS,OAAO,GAAG,EACpC2B,EAAS,KACP,MAAM,MAAMC,GAAWrC,EAAK,KAAM,CAChC,OAAQO,EACR,MAAOA,EACP,eAAgB,EAClB,CAAC,CAAC,MACJ,EACA,IAAM+B,EAAWF,EAAS,KAAK,EAAG,QAAQ,EACpCG,EAAYD,EAAS,MACrBE,EAAaF,EAAS,OACtBG,EAAQH,EAAS,EACjBI,EAAQJ,EAAS,EACvBF,EAAS,KACP,YACA,aAAa,CAACG,EAAY,EAAIE,CAAK,IACjC3B,EACIJ,EAAK,OAAS,EAAIa,EAAe,EAAIiB,EAAa,EAAIE,EACtD,CAAChC,EAAK,OAAS,EAAIa,EAAe,EAAIiB,EAAa,EAAIE,CAC7D,GACF,EACAN,EAAS,KAAK,QAAS,UAAUjB,EAAU,IAAI,QAAQ,GAAKF,CAAU,GAAG,CAC3E,CAEA,OAAAL,EAAM,KACJ,YACA,aAAa,CAACF,EAAK,MAAQ,GAAKA,EAAK,GAAKA,EAAK,MAAQ,GAAG,IACxDI,EAAW,CAACkB,EAAc,EAAIA,EAAc,EAAItB,EAAK,MACvD,GACF,EAEAwB,EAAU,KACR,YACA,eACEpB,EAAWJ,EAAK,OAAS,EAAIa,EAAe,EAAI,CAACb,EAAK,OAAS,EAAIa,EAAe,CACpF,GACF,EAEAoB,EAAiB3C,EAAMmC,CAAU,EAEjCnC,EAAK,UAAY,SAAU4C,EAAO,CAEhC,GADAC,EAAI,KAAK,uBAAwB7C,EAAM4C,CAAK,EACxC,CAAC5C,EAAK,MACR,OAAO8C,EAAU,KAAK9C,EAAM4C,CAAK,EAEnC,IAAMG,EAAK/C,EAAK,GAAK,EACfgD,EAAKhD,EAAK,GAAK,EACfiD,EAAajD,EAAK,QAAU,EAC9BkD,EAAS,CAAC,EACd,OAAIpC,EACFoC,EAAS,CACP,CAAE,EAAGH,EAAKrC,EAAK,MAAQ,EAAG,EAAGsC,EAAKC,EAAa,CAAE,EACjD,CAAE,EAAGF,EAAKrC,EAAK,MAAQ,EAAG,EAAGsC,EAAKC,EAAa,CAAE,EACjD,CAAE,EAAGF,EAAKrC,EAAK,MAAQ,EAAG,EAAGsC,EAAKC,EAAa,EAAIvC,EAAK,OAASa,CAAa,EAC9E,CAAE,EAAGwB,EAAK/B,EAAQ,EAAG,EAAGgC,EAAKC,EAAa,EAAIvC,EAAK,OAASa,CAAa,EACzE,CAAE,EAAGwB,EAAK/B,EAAQ,EAAG,EAAGgC,EAAKC,EAAa,CAAE,EAC5C,CAAE,EAAGF,EAAK/B,EAAQ,EAAG,EAAGgC,EAAKC,EAAa,CAAE,EAC5C,CAAE,EAAGF,EAAK/B,EAAQ,EAAG,EAAGgC,EAAKC,EAAa,EAAIvC,EAAK,OAASa,CAAa,EACzE,CAAE,EAAGwB,EAAKrC,EAAK,MAAQ,EAAG,EAAGsC,EAAKC,EAAa,EAAIvC,EAAK,OAASa,CAAa,CAChF,EAEA2B,EAAS,CACP,CAAE,EAAGH,EAAK/B,EAAQ,EAAG,EAAGgC,EAAKC,EAAa,CAAE,EAC5C,CAAE,EAAGF,EAAK/B,EAAQ,EAAG,EAAGgC,EAAKC,EAAa,CAAE,EAC5C,CAAE,EAAGF,EAAK/B,EAAQ,EAAG,EAAGgC,EAAKC,EAAa,EAAIlC,CAAO,EACrD,CAAE,EAAGgC,EAAKrC,EAAK,MAAQ,EAAG,EAAGsC,EAAKC,EAAa,EAAIlC,CAAO,EAC1D,CAAE,EAAGgC,EAAKrC,EAAK,MAAQ,EAAI,EAAG,EAAGsC,EAAKC,EAAa,CAAE,EACrD,CAAE,EAAGF,EAAKrC,EAAK,MAAQ,EAAG,EAAGsC,EAAKC,EAAa,CAAE,EACjD,CAAE,EAAGF,EAAKrC,EAAK,MAAQ,EAAG,EAAGsC,EAAKC,EAAa,EAAIlC,CAAO,EAC1D,CAAE,EAAGgC,EAAK/B,EAAQ,EAAG,EAAGgC,EAAKC,EAAa,EAAIlC,CAAO,CACvD,EAGU+B,EAAU,QAAQ9C,EAAMkD,EAAQN,CAAK,CAEnD,EAEOnC,CACT,CAtIsB0C,EAAArD,GAAA,cCFtB,eAAsBsD,GACpBC,EACAC,EACA,CAAE,OAAQ,CAAE,UAAAC,CAAU,CAAE,EACxB,CACA,IAAMC,EAAM,IAAI,MAChBA,EAAI,IAAMF,GAAM,KAAO,GACvB,MAAME,EAAI,OAAO,EAEjB,IAAMC,EAAoB,OAAOD,EAAI,aAAa,SAAS,EAAE,QAAQ,KAAM,EAAE,CAAC,EACxEE,EAAqB,OAAOF,EAAI,cAAc,SAAS,EAAE,QAAQ,KAAM,EAAE,CAAC,EAChFF,EAAK,iBAAmBG,EAAoBC,EAE5C,GAAM,CAAE,YAAAC,CAAY,EAAIC,EAAcN,CAAI,EAE1CA,EAAK,WAAaK,EAElB,IAAME,EAAeN,GAAW,cAChCD,EAAK,aAAeC,GAAW,cAE/B,IAAMO,EAAgB,KAAK,IACzBR,EAAK,MAASO,GAAgB,EAAK,EACnCP,GAAM,YAAcG,CACtB,EAEMM,EACJT,EAAK,aAAe,MAChBA,GAAM,YACJA,EAAK,YAAcA,EAAK,iBAE1BQ,EAEAE,EACJV,EAAK,aAAe,KAChBS,EAAaT,EAAK,iBACjBA,GAAM,aAAeI,EAC5BJ,EAAK,MAAQ,KAAK,IAAIS,EAAYF,GAAgB,CAAC,EACnD,GAAM,CAAE,SAAAI,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAAI,MAAMC,EAAYf,EAAQC,EAAM,qBAAqB,EAEjFe,EAAWf,EAAK,MAAQ,IAExBgB,EAAI,CAACP,EAAa,EAClB,EAAI,CAACC,EAAc,EAEnBO,EAAejB,EAAK,MAAQ,EAAI,EAGhCkB,EAAKC,EAAM,IAAIR,CAAQ,EACvBS,EAAUC,EAAkBrB,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChBoB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAYJ,EAAG,UAAUF,EAAG,EAAGP,EAAYC,EAAaU,CAAO,EAE/DG,EAAa,KAAK,IAAId,EAAYG,EAAK,KAAK,EAC5CY,EAAcd,EAAcE,EAAK,OAASK,EAE1CQ,EAAYP,EAAG,UAAU,CAACK,EAAa,EAAG,CAACC,EAAc,EAAGD,EAAYC,EAAa,CACzF,GAAGJ,EACH,KAAM,OACN,OAAQ,MACV,CAAC,EAEKM,EAAYf,EAAS,OAAO,IAAMW,EAAW,cAAc,EAC3DK,EAAahB,EAAS,OAAO,IAAMc,CAAS,EAElD,GAAIzB,EAAK,IAAK,CACZ,IAAM4B,EAAQjB,EAAS,OAAO,OAAO,EAGrCiB,EAAM,KAAK,OAAQ5B,EAAK,GAAG,EAC3B4B,EAAM,KAAK,QAASnB,CAAU,EAC9BmB,EAAM,KAAK,SAAUlB,CAAW,EAChCkB,EAAM,KAAK,sBAAuB,MAAM,EAExCA,EAAM,KACJ,YACA,aAAa,CAACnB,EAAa,CAAC,IAAIM,EAAWS,EAAc,EAAId,EAAc,CAACc,EAAc,CAAC,GAC7F,CACF,CAEA,OAAAX,EAAM,KACJ,YACA,aAAa,CAACD,EAAK,MAAQ,GAAKA,EAAK,GAAKA,EAAK,MAAQ,GAAG,IACxDG,EACI,CAACL,EAAc,EAAIE,EAAK,OAAS,EAAIK,EAAe,EACpDP,EAAc,EAAIE,EAAK,OAAS,EAAIK,EAAe,CACzD,GACF,EAEAS,EAAU,KACR,YACA,eACEX,EAAWH,EAAK,OAAS,EAAIK,EAAe,EAAI,CAACL,EAAK,OAAS,EAAIK,EAAe,CACpF,GACF,EAEAY,EAAiB7B,EAAM2B,CAAU,EAEjC3B,EAAK,UAAY,SAAU8B,EAAO,CAEhC,GADAC,EAAI,KAAK,uBAAwB/B,EAAM8B,CAAK,EACxC,CAAC9B,EAAK,MACR,OAAOgC,EAAU,KAAKhC,EAAM8B,CAAK,EAEnC,IAAMG,EAAKjC,EAAK,GAAK,EACfkC,EAAKlC,EAAK,GAAK,EACfmC,EAAanC,EAAK,QAAU,EAC9BoC,EAAS,CAAC,EACd,OAAIrB,EACFqB,EAAS,CACP,CAAE,EAAGH,EAAKrB,EAAK,MAAQ,EAAG,EAAGsB,EAAKC,EAAa,CAAE,EACjD,CAAE,EAAGF,EAAKrB,EAAK,MAAQ,EAAG,EAAGsB,EAAKC,EAAa,CAAE,EACjD,CAAE,EAAGF,EAAKrB,EAAK,MAAQ,EAAG,EAAGsB,EAAKC,EAAa,EAAIvB,EAAK,OAASK,CAAa,EAC9E,CAAE,EAAGgB,EAAKxB,EAAa,EAAG,EAAGyB,EAAKC,EAAa,EAAIvB,EAAK,OAASK,CAAa,EAC9E,CAAE,EAAGgB,EAAKxB,EAAa,EAAG,EAAGyB,EAAKC,EAAa,CAAE,EACjD,CAAE,EAAGF,EAAKxB,EAAa,EAAG,EAAGyB,EAAKC,EAAa,CAAE,EACjD,CAAE,EAAGF,EAAKxB,EAAa,EAAG,EAAGyB,EAAKC,EAAa,EAAIvB,EAAK,OAASK,CAAa,EAC9E,CAAE,EAAGgB,EAAKrB,EAAK,MAAQ,EAAG,EAAGsB,EAAKC,EAAa,EAAIvB,EAAK,OAASK,CAAa,CAChF,EAEAmB,EAAS,CACP,CAAE,EAAGH,EAAKxB,EAAa,EAAG,EAAGyB,EAAKC,EAAa,CAAE,EACjD,CAAE,EAAGF,EAAKxB,EAAa,EAAG,EAAGyB,EAAKC,EAAa,CAAE,EACjD,CAAE,EAAGF,EAAKxB,EAAa,EAAG,EAAGyB,EAAKC,EAAa,EAAIzB,CAAY,EAC/D,CAAE,EAAGuB,EAAKrB,EAAK,MAAQ,EAAG,EAAGsB,EAAKC,EAAa,EAAIzB,CAAY,EAC/D,CAAE,EAAGuB,EAAKrB,EAAK,MAAQ,EAAI,EAAG,EAAGsB,EAAKC,EAAa,CAAE,EACrD,CAAE,EAAGF,EAAKrB,EAAK,MAAQ,EAAG,EAAGsB,EAAKC,EAAa,CAAE,EACjD,CAAE,EAAGF,EAAKrB,EAAK,MAAQ,EAAG,EAAGsB,EAAKC,EAAa,EAAIzB,CAAY,EAC/D,CAAE,EAAGuB,EAAKxB,EAAa,EAAG,EAAGyB,EAAKC,EAAa,EAAIzB,CAAY,CACjE,EAGUsB,EAAU,QAAQhC,EAAMoC,EAAQN,CAAK,CAEnD,EAEOnB,CACT,CA5IsB0B,EAAAvC,GAAA,eCetB,eAAsBwC,GACpBC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAAYP,EAAQC,EAAMO,EAAeP,CAAI,CAAC,EAEzEQ,EAAI,KAAK,IAAIH,EAAK,OAASL,EAAK,SAAW,GAAK,EAAGA,GAAM,OAAS,CAAC,EACnES,EAAI,KAAK,IAAIJ,EAAK,QAAUL,EAAK,SAAW,GAAK,EAAGA,GAAM,QAAU,CAAC,EAErEU,EAAS,CACb,CAAE,EAAG,EAAG,EAAG,CAAE,EACb,CAAE,EAAGF,EAAG,EAAG,CAAE,EACb,CAAE,EAAGA,EAAK,EAAIC,EAAK,EAAG,EAAG,CAACA,CAAE,EAC5B,CAAE,EAAI,GAAKA,EAAK,EAAG,EAAG,CAACA,CAAE,CAC3B,EAEIE,EACE,CAAE,UAAAC,CAAU,EAAIZ,EAEtB,GAAIA,EAAK,OAAS,YAAa,CAE7B,IAAMa,EAAKC,EAAM,IAAIV,CAAQ,EACvBW,EAAUC,EAAkBhB,EAAM,CAAC,CAAC,EACpCiB,EAAWC,EAAqBR,CAAM,EAEtCS,EAAYN,EAAG,KAAKI,EAAUF,CAAO,EAE3CJ,EAAUP,EACP,OAAO,IAAMe,EAAW,cAAc,EACtC,KAAK,YAAa,aAAa,CAACX,EAAI,CAAC,KAAKC,EAAI,CAAC,GAAG,EAEjDG,GACFD,EAAQ,KAAK,QAASC,CAAS,CAEnC,MACED,EAAUS,EAAmBhB,EAAUI,EAAGC,EAAGC,CAAM,EAGrD,OAAIR,GACFS,EAAQ,KAAK,QAAST,CAAU,EAGlCF,EAAK,MAAQQ,EACbR,EAAK,OAASS,EAEdY,EAAiBrB,EAAMW,CAAO,EAE9BX,EAAK,UAAY,SAAUsB,EAAO,CAChC,OAAOC,EAAU,QAAQvB,EAAMU,EAAQY,CAAK,CAC9C,EAEOlB,CACT,CAtDsBoB,EAAA1B,GAAA,iBCdtB,eAAsB2B,GACpBC,EACAC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcJ,CAAI,EACtDA,EAAK,WAAaE,EAElB,GAAM,CAAE,SAAAG,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAAYR,EAAQC,EAAMQ,EAAeR,CAAI,CAAC,EAEzES,EAAa,KAAK,IAAIH,EAAK,MAAQL,EAAQ,cAAgB,EAAGD,GAAM,OAAS,CAAC,EAC9EU,EAAc,KAAK,IAAIJ,EAAK,OAASL,EAAQ,cAAgB,EAAGD,GAAM,QAAU,CAAC,EACjFW,EAAI,CAACF,EAAa,EAClBG,EAAI,CAACF,EAAc,EAIrBG,EACA,CAAE,GAAAC,EAAI,GAAAC,CAAG,EAAIf,EACX,CAAE,UAAAgB,CAAU,EAAIhB,EAQtB,GALIC,GAAS,IAAMA,EAAQ,KACzBa,EAAKb,EAAQ,GACbc,EAAKd,EAAQ,IAGXD,EAAK,OAAS,YAAa,CAE7B,IAAMiB,EAAKC,EAAM,IAAIb,CAAQ,EACvBJ,EAAUkB,EAAkBnB,EAAM,CAAC,CAAC,EAEpCoB,EACJN,GAAMC,EACFE,EAAG,KAAKI,EAAuBV,EAAGC,EAAGH,EAAYC,EAAaI,GAAM,CAAC,EAAGb,CAAO,EAC/EgB,EAAG,UAAUN,EAAGC,EAAGH,EAAYC,EAAaT,CAAO,EAEzDY,EAAOR,EAAS,OAAO,IAAMe,EAAW,cAAc,EACtDP,EAAK,KAAK,QAAS,uBAAuB,EAAE,KAAK,QAASS,EAAoBN,CAAS,CAAC,CAC1F,MACEH,EAAOR,EAAS,OAAO,OAAQ,cAAc,EAE7CQ,EACG,KAAK,QAAS,uBAAuB,EACrC,KAAK,QAASV,CAAU,EACxB,KAAK,KAAMmB,EAAoBR,CAAE,CAAC,EAClC,KAAK,KAAMQ,EAAoBP,CAAE,CAAC,EAClC,KAAK,IAAKJ,CAAC,EACX,KAAK,IAAKC,CAAC,EACX,KAAK,QAASH,CAAU,EACxB,KAAK,SAAUC,CAAW,EAG/B,OAAAa,EAAiBvB,EAAMa,CAAI,EAE3Bb,EAAK,UAAY,SAAUwB,EAAO,CAChC,OAAOC,EAAU,KAAKzB,EAAMwB,CAAK,CACnC,EAEOnB,CACT,CA5DsBqB,EAAA5B,GAAA,YCYtB,eAAsB6B,GAAwCC,EAAwBC,EAAY,CAChG,GAAM,CAAE,SAAAC,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAAI,MAAMC,EAAYL,EAAQC,EAAM,OAAO,EAInEK,EAAOJ,EAAS,OAAO,OAAQ,cAAc,EAKnD,OAAAI,EAAK,KAAK,QAFS,EAEU,EAAE,KAAK,SADhB,EACqC,EACzDJ,EAAS,KAAK,QAAS,iBAAiB,EACxCE,EAAM,KACJ,YACA,aAAa,EAAED,EAAK,MAAQ,IAAMA,EAAK,GAAKA,EAAK,MAAQ,GAAG,KAAK,EAAEA,EAAK,OAAS,IAAMA,EAAK,GAAKA,EAAK,KAAO,GAAG,GAClH,EAaAI,EAAiBN,EAAMK,CAAI,EAI3BL,EAAK,UAAY,SAAUO,EAAO,CAChC,OAAOC,EAAU,KAAKR,EAAMO,CAAK,CACnC,EAEON,CACT,CArCsBQ,EAAAX,GAAA,aCbtB,eAAsBY,GAAwCC,EAAwBC,EAAY,CAChG,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAAYP,EAAQC,EAAMO,EAAeP,CAAI,CAAC,EACzEQ,EAAI,KAAK,IAAIH,EAAK,OAASL,EAAK,SAAW,GAAIA,GAAM,OAAS,CAAC,EAC/DS,EAAI,KAAK,IAAIJ,EAAK,QAAUL,EAAK,SAAW,GAAIA,GAAM,QAAU,CAAC,EACjEU,EAAS,CACb,CAAE,EAAG,EAAG,EAAG,CAAE,EACb,CAAE,EAAGF,EAAK,EAAIC,EAAK,EAAG,EAAG,CAAE,EAC3B,CAAE,EAAGD,EAAG,EAAG,CAACC,CAAE,EACd,CAAE,EAAG,EAAE,EAAIA,GAAK,EAAG,EAAG,CAACA,CAAE,CAC3B,EAEIE,EACE,CAAE,UAAAC,CAAU,EAAIZ,EAEtB,GAAIA,EAAK,OAAS,YAAa,CAE7B,IAAMa,EAAKC,EAAM,IAAIV,CAAQ,EACvBW,EAAUC,EAAkBhB,EAAM,CAAC,CAAC,EACpCiB,EAAWC,EAAqBR,CAAM,EAEtCS,EAAYN,EAAG,KAAKI,EAAUF,CAAO,EAE3CJ,EAAUP,EACP,OAAO,IAAMe,EAAW,cAAc,EACtC,KAAK,YAAa,aAAa,CAACX,EAAI,CAAC,KAAKC,EAAI,CAAC,GAAG,EAEjDG,GACFD,EAAQ,KAAK,QAASC,CAAS,CAEnC,MACED,EAAUS,EAAmBhB,EAAUI,EAAGC,EAAGC,CAAM,EAGrD,OAAIR,GACFS,EAAQ,KAAK,QAAST,CAAU,EAGlCF,EAAK,MAAQQ,EACbR,EAAK,OAASS,EAEdY,EAAiBrB,EAAMW,CAAO,EAE9BX,EAAK,UAAY,SAAUsB,EAAO,CAChC,OAAOC,EAAU,QAAQvB,EAAMU,EAAQY,CAAK,CAC9C,EAEOlB,CACT,CAjDsBoB,EAAA1B,GAAA,aCAtB,eAAsB2B,GAAyCC,EAAwBC,EAAY,CACjG,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAAYP,EAAQC,EAAMO,EAAeP,CAAI,CAAC,EACzEQ,EAAI,KAAK,IAAIH,EAAK,OAASL,EAAK,SAAW,GAAIA,GAAM,OAAS,CAAC,EAC/DS,EAAI,KAAK,IAAIJ,EAAK,QAAUL,EAAK,SAAW,GAAIA,GAAM,QAAU,CAAC,EACjEU,EAAS,CACb,CAAE,EAAI,GAAKD,EAAK,EAAG,EAAG,CAAE,EACxB,CAAE,EAAGD,EAAG,EAAG,CAAE,EACb,CAAE,EAAGA,EAAK,EAAIC,EAAK,EAAG,EAAG,CAACA,CAAE,EAC5B,CAAE,EAAG,EAAG,EAAG,CAACA,CAAE,CAChB,EAEIE,EACE,CAAE,UAAAC,CAAU,EAAIZ,EAEtB,GAAIA,EAAK,OAAS,YAAa,CAE7B,IAAMa,EAAKC,EAAM,IAAIV,CAAQ,EACvBW,EAAUC,EAAkBhB,EAAM,CAAC,CAAC,EACpCiB,EAAWC,EAAqBR,CAAM,EACtCS,EAAYN,EAAG,KAAKI,EAAUF,CAAO,EAE3CJ,EAAUP,EACP,OAAO,IAAMe,EAAW,cAAc,EACtC,KAAK,YAAa,aAAa,CAACX,EAAI,CAAC,KAAKC,EAAI,CAAC,GAAG,EAEjDG,GACFD,EAAQ,KAAK,QAASC,CAAS,CAEnC,MACED,EAAUS,EAAmBhB,EAAUI,EAAGC,EAAGC,CAAM,EAGrD,OAAIR,GACFS,EAAQ,KAAK,QAAST,CAAU,EAGlCF,EAAK,MAAQQ,EACbR,EAAK,OAASS,EAEdY,EAAiBrB,EAAMW,CAAO,EAE9BX,EAAK,UAAY,SAAUsB,EAAO,CAChC,OAAOC,EAAU,QAAQvB,EAAMU,EAAQY,CAAK,CAC9C,EAEOlB,CACT,CAhDsBoB,EAAA1B,GAAA,cCCf,SAAS2B,GAA4CC,EAAwBC,EAAY,CAC9F,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,MAAQ,GACbA,EAAK,WAAaC,EAClB,IAAMG,EAAWL,EACd,OAAO,GAAG,EACV,KAAK,QAASM,EAAeL,CAAI,CAAC,EAClC,KAAK,KAAMA,EAAK,OAASA,EAAK,EAAE,EAC7B,CAAE,UAAAM,CAAU,EAAIN,EAChBO,EAAQ,KAAK,IAAI,GAAIP,GAAM,OAAS,CAAC,EACrCQ,EAAS,KAAK,IAAI,GAAIR,GAAM,QAAU,CAAC,EACvCS,EAAM,EAENC,EAAS,CACb,CAAE,EAAGH,EAAO,EAAG,CAAE,EACjB,CAAE,EAAG,EAAG,EAAGC,EAASC,EAAM,CAAE,EAC5B,CAAE,EAAGF,EAAQ,EAAIE,EAAK,EAAGD,EAASC,EAAM,CAAE,EAC1C,CAAE,EAAG,EAAG,EAAG,EAAID,CAAO,EACtB,CAAE,EAAGD,EAAO,EAAGC,EAASC,EAAM,CAAE,EAChC,CAAE,EAAG,EAAIA,EAAK,EAAGD,EAASC,EAAM,CAAE,CACpC,EAGME,EAAKC,EAAM,IAAIR,CAAQ,EACvBS,EAAUC,EAAkBd,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChBa,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAWC,EAAqBN,CAAM,EACtCO,EAAWN,EAAG,KAAKI,EAAUF,CAAO,EAEpCf,EAAgBM,EAAS,OAAO,IAAMa,EAAU,cAAc,EAEpE,OAAIX,GAAaN,EAAK,OAAS,aAC7BF,EAAc,UAAU,MAAM,EAAE,KAAK,QAASQ,CAAS,EAGrDJ,GAAcF,EAAK,OAAS,aAC9BF,EAAc,UAAU,MAAM,EAAE,KAAK,QAASI,CAAU,EAG1DJ,EAAc,KAAK,YAAa,cAAcS,EAAQ,CAAC,IAAI,CAACC,CAAM,GAAG,EAErEU,EAAiBlB,EAAMF,CAAa,EAEpCE,EAAK,UAAY,SAAUmB,EAAO,CAChC,OAAAC,EAAI,KAAK,0BAA2BpB,EAAMmB,CAAK,EACnCE,EAAU,QAAQrB,EAAMU,EAAQS,CAAK,CAGnD,EAEOf,CACT,CAxDgBkB,EAAAxB,GAAA,iBCDT,IAAMyB,GAAsBC,EAAA,CACjCC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,IAEO,CACL,IAAIN,CAAC,IAAIC,EAAII,CAAE,GACf,IAAID,CAAE,IAAIC,CAAE,UAAUH,CAAK,KAC3B,IAAIE,CAAE,IAAIC,CAAE,UAAU,CAACH,CAAK,KAC5B,MAAMC,CAAM,GACZ,IAAIC,CAAE,IAAIC,CAAE,UAAUH,CAAK,KAC3B,MAAM,CAACC,CAAM,GACb,IAAIH,CAAC,IAAIC,EAAII,EAAKC,CAAW,GAC7B,IAAIF,CAAE,IAAIC,CAAE,UAAUH,CAAK,IAC7B,EAAE,KAAK,GAAG,EAlBuB,uBAoBtBK,GAA2BR,EAAA,CACtCC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,IAEO,CACL,IAAIN,CAAC,IAAIC,EAAII,CAAE,GACf,IAAIL,EAAIE,CAAK,IAAID,EAAII,CAAE,GACvB,IAAID,CAAE,IAAIC,CAAE,UAAU,CAACH,CAAK,KAC5B,MAAMC,CAAM,GACZ,IAAIC,CAAE,IAAIC,CAAE,UAAUH,CAAK,KAC3B,MAAM,CAACC,CAAM,GACb,IAAIH,CAAC,IAAIC,EAAII,EAAKC,CAAW,GAC7B,IAAIF,CAAE,IAAIC,CAAE,UAAUH,CAAK,IAC7B,EAAE,KAAK,GAAG,EAlB4B,4BAoB3BM,GAA2BT,EAAA,CACtCC,EACAC,EACAC,EACAC,EACAC,EACAC,IAEO,CAAC,IAAIL,EAAIE,EAAQ,CAAC,IAAI,CAACC,EAAS,CAAC,GAAI,IAAIC,CAAE,IAAIC,CAAE,UAAUH,CAAK,IAAI,EAAE,KAAK,GAAG,EAR/C,4BAUxC,eAAsBO,GACpBC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAAI,MAAMC,EAAYR,EAAQC,EAAMQ,EAAeR,CAAI,CAAC,EAChFS,EAAI,KAAK,IAAIJ,EAAK,OAASL,EAAK,SAAW,GAAIA,EAAK,OAAS,CAAC,EAC9DP,EAAKgB,EAAI,EACTf,EAAKD,GAAM,IAAMgB,EAAI,IACrBC,EAAI,KAAK,IAAIL,EAAK,OAASX,GAAMM,EAAK,SAAW,GAAIA,EAAK,QAAU,CAAC,EACrEL,EAAce,EAAI,GAEpBC,EACE,CAAE,UAAAC,CAAU,EAAIZ,EAEtB,GAAIA,EAAK,OAAS,YAAa,CAE7B,IAAMa,EAAKC,EAAM,IAAIV,CAAQ,EACvBW,EAAgBnB,GAAyB,EAAG,EAAGa,EAAGC,EAAGjB,EAAIC,EAAIC,CAAW,EACxEqB,EAAgBnB,GAAyB,EAAGH,EAAIe,EAAGC,EAAGjB,EAAIC,CAAE,EAC5DuB,EAAUC,EAAkBlB,EAAM,CAAC,CAAC,EAEpCmB,EAAYN,EAAG,KAAKE,EAAeE,CAAO,EAC1CG,EAAYP,EAAG,KAAKG,EAAeC,CAAO,EAE5Bb,EAAS,OAAO,IAAMgB,EAAW,cAAc,EACvD,KAAK,QAAS,MAAM,EAChCT,EAAWP,EAAS,OAAO,IAAMe,EAAW,cAAc,EAC1DR,EAAS,KAAK,QAAS,uBAAuB,EAC1CC,GACFD,EAAS,KAAK,QAASC,CAAS,CAEpC,KAAO,CACL,IAAMS,EAAWlC,GAAoB,EAAG,EAAGsB,EAAGC,EAAGjB,EAAIC,EAAIC,CAAW,EACpEgB,EAAWP,EACR,OAAO,OAAQ,cAAc,EAC7B,KAAK,IAAKiB,CAAQ,EAClB,KAAK,QAAS,uBAAuB,EACrC,KAAK,QAASC,EAAoBV,CAAS,CAAC,EAC5C,KAAK,QAASV,CAAU,CAC7B,CAGA,OAAAS,EAAS,KAAK,iBAAkBjB,CAAE,EAClCiB,EAAS,KAAK,YAAa,aAAa,CAACF,EAAI,CAAC,KAAK,EAAEC,EAAI,EAAIhB,EAAG,GAAG,EAEnE6B,EAAiBvB,EAAMW,CAAQ,EAE/BL,EAAM,KACJ,YACA,aAAa,EAAED,EAAK,MAAQ,IAAMA,EAAK,GAAKA,EAAK,MAAQ,GAAG,KAAK,EAAEA,EAAK,OAAS,GAAKX,GAAMW,EAAK,GAAKA,EAAK,KAAO,GAAG,GACvH,EAEAL,EAAK,UAAY,SAAUwB,EAAO,CAChC,IAAMC,EAAMC,EAAU,KAAK1B,EAAMwB,CAAK,EAChCnC,EAAIoC,EAAI,GAAKzB,EAAK,GAAK,GAE7B,GACEP,GAAM,IACL,KAAK,IAAIJ,CAAC,GAAKW,EAAK,OAAS,GAAK,GAChC,KAAK,IAAIX,CAAC,IAAMW,EAAK,OAAS,GAAK,GAClC,KAAK,IAAIyB,EAAI,GAAKzB,EAAK,GAAK,EAAE,GAAKA,EAAK,QAAU,GAAK,EAAIN,GAC/D,CACA,IAAIJ,EAAII,EAAKA,GAAM,EAAKL,EAAIA,GAAMI,EAAKA,IACnCH,EAAI,IACNA,EAAI,KAAK,KAAKA,CAAC,GAEjBA,EAAII,EAAKJ,EACLkC,EAAM,GAAKxB,EAAK,GAAK,GAAK,IAC5BV,EAAI,CAACA,GAGPmC,EAAI,GAAKnC,CACX,CAEA,OAAOmC,CACT,EAEOrB,CACT,CAhFsBhB,EAAAU,GAAA,iBC9CtB,eAAsB6B,GACpBC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAAI,MAAMC,EAAYR,EAAQC,EAAMQ,EAAeR,CAAI,CAAC,EAChFS,EAAI,KAAK,IAAIJ,EAAK,OAASL,EAAK,SAAW,GAAK,EAAGA,GAAM,OAAS,CAAC,EACnEU,EAAI,KAAK,IAAIL,EAAK,QAAUL,EAAK,SAAW,GAAK,EAAGA,GAAM,QAAU,CAAC,EACrEW,EAAgBD,EAAI,EACpBE,EAASF,EAAIC,EACb,CAAE,UAAAE,CAAU,EAAIb,EAGhBc,EAAKC,EAAM,IAAIX,CAAQ,EACvBY,EAAUC,EAAkBjB,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChBgB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAS,CACb,CAAE,EAAG,CAACT,EAAI,EAAKA,EAAI,EAAK,GAAK,EAAG,CAACG,EAAS,CAAE,EAC5C,CAAE,EAAG,CAACH,EAAI,EAAKA,EAAI,EAAK,GAAK,EAAGG,EAAS,CAAE,EAC3C,GAAGO,GACD,CAACV,EAAI,EAAKA,EAAI,EAAK,GACnBG,EAAS,EACTH,EAAI,EAAKA,EAAI,EAAK,GAClBG,EAAS,EACTD,EACA,EACF,EACA,CAAE,EAAGF,EAAI,EAAKA,EAAI,EAAK,GAAK,EAAG,CAACG,EAAS,CAAE,EAC3C,CAAE,EAAG,CAACH,EAAI,EAAKA,EAAI,EAAK,GAAK,EAAG,CAACG,EAAS,CAAE,EAC5C,CAAE,EAAG,CAACH,EAAI,EAAG,EAAG,CAACG,EAAS,CAAE,EAC5B,CAAE,EAAG,CAACH,EAAI,EAAG,EAAIG,EAAS,EAAK,GAAI,EACnC,CAAE,EAAG,CAACH,EAAI,EAAG,EAAG,CAACG,EAAS,CAAE,CAC9B,EAEMQ,EAAON,EAAG,QACdI,EAAO,IAAKG,GAAM,CAACA,EAAE,EAAGA,EAAE,CAAC,CAAC,EAC5BL,CACF,EAEMM,EAAelB,EAAS,OAAO,IAAMgB,EAAM,cAAc,EAE/D,OAAAE,EAAa,KAAK,QAAS,uBAAuB,EAE9CT,GAAab,EAAK,OAAS,aAC7BsB,EAAa,UAAU,MAAM,EAAE,KAAK,QAAST,CAAS,EAGpDX,GAAcF,EAAK,OAAS,aAC9BsB,EAAa,UAAU,MAAM,EAAE,KAAK,QAASpB,CAAU,EAGzDoB,EAAa,KAAK,YAAa,eAAe,CAACX,EAAgB,CAAC,GAAG,EACnEL,EAAM,KACJ,YACA,aAAa,CAACG,EAAI,GAAKT,EAAK,SAAW,GAAOS,EAAI,EAAK,GAAO,GAAKJ,EAAK,GAAKA,EAAK,MAAQ,GAAG,IAAI,CAACK,EAAI,GAAKV,EAAK,SAAW,GAAKW,EAAgB,GAAKN,EAAK,GAAKA,EAAK,KAAO,GAAG,GAChL,EAEAkB,EAAiBvB,EAAMsB,CAAY,EACnCtB,EAAK,UAAY,SAAUwB,EAAO,CAEhC,OADYC,EAAU,QAAQzB,EAAMkB,EAAQM,CAAK,CAEnD,EAEOpB,CACT,CAtEsBsB,EAAA5B,GAAA,sBCLtB,eAAsB6B,GAAwCC,EAAwBC,EAAY,CAChG,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAAI,MAAMC,EAAYR,EAAQC,EAAMQ,EAAeR,CAAI,CAAC,EAChFS,EAAI,KAAK,IAAIJ,EAAK,OAASL,EAAK,SAAW,GAAK,EAAGA,GAAM,OAAS,CAAC,EACnEU,EAAI,KAAK,IAAIL,EAAK,QAAUL,EAAK,SAAW,GAAK,EAAGA,GAAM,QAAU,CAAC,EACrEW,EAAa,EACbC,EAAI,CAACH,EAAI,EACTI,EAAI,CAACH,EAAI,EACT,CAAE,UAAAI,CAAU,EAAId,EAGhBe,EAAKC,EAAM,IAAIZ,CAAQ,EACvBa,EAAUC,EAAkBlB,EAAM,CAAC,CAAC,EAEpCmB,EAAkB,CACtB,CAAE,EAAGP,EAAID,EAAY,EAAGE,EAAIF,CAAW,EACvC,CAAE,EAAGC,EAAID,EAAY,EAAGE,EAAIH,EAAIC,CAAW,EAC3C,CAAE,EAAGC,EAAIH,EAAIE,EAAY,EAAGE,EAAIH,EAAIC,CAAW,EAC/C,CAAE,EAAGC,EAAIH,EAAIE,EAAY,EAAGE,EAAIH,CAAE,EAClC,CAAE,EAAGE,EAAIH,EAAG,EAAGI,EAAIH,CAAE,EACrB,CAAE,EAAGE,EAAIH,EAAG,EAAGI,EAAIH,EAAIC,CAAW,EAClC,CAAE,EAAGC,EAAIH,EAAIE,EAAY,EAAGE,EAAIH,EAAIC,CAAW,EAC/C,CAAE,EAAGC,EAAIH,EAAIE,EAAY,EAAGE,EAAIF,CAAW,EAC3C,CAAE,EAAGC,EAAID,EAAY,EAAGE,EAAIF,CAAW,EACvC,CAAE,EAAGC,EAAID,EAAY,EAAGE,CAAE,EAC1B,CAAE,EAAAD,EAAG,EAAAC,CAAE,EACP,CAAE,EAAAD,EAAG,EAAGC,EAAIF,CAAW,CACzB,EAEMS,EAAkB,CACtB,CAAE,EAAAR,EAAG,EAAGC,EAAIF,CAAW,EACvB,CAAE,EAAGC,EAAIH,EAAIE,EAAY,EAAGE,EAAIF,CAAW,EAC3C,CAAE,EAAGC,EAAIH,EAAIE,EAAY,EAAGE,EAAIH,CAAE,EAClC,CAAE,EAAGE,EAAIH,EAAG,EAAGI,EAAIH,CAAE,EACrB,CAAE,EAAGE,EAAIH,EAAG,EAAAI,CAAE,EACd,CAAE,EAAAD,EAAG,EAAAC,CAAE,CACT,EAEIb,EAAK,OAAS,cAChBiB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAMI,EAAYC,EAAqBH,CAAe,EAChDI,EAAYR,EAAG,KAAKM,EAAWJ,CAAO,EACtCO,EAAYF,EAAqBF,CAAe,EAChDK,EAAYV,EAAG,KAAKS,EAAW,CAAE,GAAGP,EAAS,KAAM,MAAO,CAAC,EAE3DnB,EAAYM,EAAS,OAAO,IAAMqB,EAAW,cAAc,EACjE,OAAA3B,EAAU,OAAO,IAAMyB,EAAW,cAAc,EAEhDzB,EAAU,KAAK,QAAS,uBAAuB,EAE3CgB,GAAad,EAAK,OAAS,aAC7BF,EAAU,UAAU,MAAM,EAAE,KAAK,QAASgB,CAAS,EAGjDZ,GAAcF,EAAK,OAAS,aAC9BF,EAAU,UAAU,MAAM,EAAE,KAAK,QAASI,CAAU,EAGtDI,EAAM,KACJ,YACA,aAAa,EAAED,EAAK,MAAQ,GAAKM,GAAcN,EAAK,GAAKA,EAAK,MAAQ,GAAG,KAAK,EAAEA,EAAK,OAAS,GAAKM,GAAcN,EAAK,GAAKA,EAAK,KAAO,GAAG,GAC5I,EAEAqB,EAAiB1B,EAAMF,CAAS,EAEhCE,EAAK,UAAY,SAAU2B,EAAO,CAEhC,OADYC,EAAU,QAAQ5B,EAAMmB,EAAiBQ,CAAK,CAE5D,EAEOvB,CACT,CA3EsByB,EAAA/B,GAAA,aCMtB,eAAsBgC,GACpBC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAAI,MAAMC,EAAYR,EAAQC,EAAMQ,EAAeR,CAAI,CAAC,EAChFS,EAAI,KAAK,IAAIJ,EAAK,OAASL,EAAK,SAAW,GAAK,EAAGA,GAAM,OAAS,CAAC,EACnEU,EAAI,KAAK,IAAIL,EAAK,QAAUL,EAAK,SAAW,GAAK,EAAGA,GAAM,QAAU,CAAC,EACrEW,EAAgBD,EAAI,EACpBE,EAASF,EAAIC,EACbE,EAAI,CAACJ,EAAI,EACTK,EAAI,CAACF,EAAS,EACdG,EAAa,EAEb,CAAE,UAAAC,CAAU,EAAIhB,EAEhBiB,EAAaC,GACjBL,EAAIE,EACJD,EAAIF,EAASG,EACbF,EAAIJ,EAAIM,EACRD,EAAIF,EAASG,EACbJ,EACA,EACF,EAEMQ,EAAgBF,IAAaA,EAAW,OAAS,CAAC,EAElDG,EAAkB,CACtB,CAAE,EAAGP,EAAIE,EAAY,EAAGD,EAAIC,CAAW,EACvC,CAAE,EAAGF,EAAIE,EAAY,EAAGD,EAAIF,EAASG,CAAW,EAChD,GAAGE,EACH,CAAE,EAAGJ,EAAIJ,EAAIM,EAAY,EAAGI,EAAc,EAAIJ,CAAW,EACzD,CAAE,EAAGF,EAAIJ,EAAG,EAAGU,EAAc,EAAIJ,CAAW,EAC5C,CAAE,EAAGF,EAAIJ,EAAG,EAAGU,EAAc,EAAI,EAAIJ,CAAW,EAChD,CAAE,EAAGF,EAAIJ,EAAIM,EAAY,EAAGI,EAAc,EAAI,EAAIJ,CAAW,EAC7D,CAAE,EAAGF,EAAIJ,EAAIM,EAAY,EAAGD,EAAIC,CAAW,EAC3C,CAAE,EAAGF,EAAIE,EAAY,EAAGD,EAAIC,CAAW,EACvC,CAAE,EAAGF,EAAIE,EAAY,EAAGD,CAAE,EAC1B,CAAE,EAAAD,EAAG,EAAAC,CAAE,EACP,CAAE,EAAAD,EAAG,EAAGC,EAAIC,CAAW,CACzB,EAEMM,EAAkB,CACtB,CAAE,EAAAR,EAAG,EAAGC,EAAIC,CAAW,EACvB,CAAE,EAAGF,EAAIJ,EAAIM,EAAY,EAAGD,EAAIC,CAAW,EAC3C,CAAE,EAAGF,EAAIJ,EAAIM,EAAY,EAAGI,EAAc,EAAIJ,CAAW,EACzD,CAAE,EAAGF,EAAIJ,EAAG,EAAGU,EAAc,EAAIJ,CAAW,EAC5C,CAAE,EAAGF,EAAIJ,EAAG,EAAAK,CAAE,EACd,CAAE,EAAAD,EAAG,EAAAC,CAAE,CACT,EAGMQ,EAAKC,EAAM,IAAInB,CAAQ,EACvBoB,EAAUC,EAAkBzB,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChBwB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAYC,EAAqBP,CAAe,EAChDQ,EAAYN,EAAG,KAAKI,EAAWF,CAAO,EACtCK,EAAYF,EAAqBN,CAAe,EAChDS,EAAYR,EAAG,KAAKO,EAAWL,CAAO,EAEtCO,EAAQ3B,EAAS,OAAO,IAAMwB,EAAW,cAAc,EAC7D,OAAAG,EAAM,OAAO,IAAMD,CAAS,EAE5BC,EAAM,KAAK,QAAS,uBAAuB,EAEvCf,GAAahB,EAAK,OAAS,aAC7B+B,EAAM,UAAU,MAAM,EAAE,KAAK,QAASf,CAAS,EAG7Cd,GAAcF,EAAK,OAAS,aAC9B+B,EAAM,UAAU,MAAM,EAAE,KAAK,QAAS7B,CAAU,EAGlD6B,EAAM,KAAK,YAAa,eAAe,CAACpB,EAAgB,CAAC,GAAG,EAE5DL,EAAM,KACJ,YACA,aAAa,EAAED,EAAK,MAAQ,GAAKU,GAAcV,EAAK,GAAKA,EAAK,MAAQ,GAAG,KAAK,EAAEA,EAAK,OAAS,GAAKU,EAAaJ,EAAgB,GAAKN,EAAK,GAAKA,EAAK,KAAO,GAAG,GAChK,EAEA2B,EAAiBhC,EAAM+B,CAAK,EAE5B/B,EAAK,UAAY,SAAUiC,EAAO,CAEhC,OADYC,EAAU,QAAQlC,EAAMoB,EAAiBa,CAAK,CAE5D,EAEO7B,CACT,CA9FsB+B,EAAArC,GAAA,2BCLtB,eAAsBsC,GACpBC,EACAC,EACA,CAAE,OAAQ,CAAE,eAAAC,CAAe,CAAE,EAC7B,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcJ,CAAI,EACtDA,EAAK,WAAaE,EACIF,EAAK,eAAiBK,GAAU,EAAE,WAAW,aAAe,KAEhFL,EAAK,YAAc,IAErB,GAAM,CAAE,SAAAM,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAAYT,EAAQC,EAAMS,EAAeT,CAAI,CAAC,EACzEU,EAAa,KAAK,IAAIH,EAAK,OAASP,EAAK,SAAW,GAAK,EAAGA,GAAM,OAAS,CAAC,EAC5EW,EAAc,KAAK,IAAIJ,EAAK,QAAUP,EAAK,SAAW,GAAK,EAAGA,GAAM,QAAU,CAAC,EAC/EY,EAAI,CAACF,EAAa,EAClBG,EAAI,CAACF,EAAc,EACnB,CAAE,UAAAG,CAAU,EAAId,EAIhBe,EAAKC,EAAM,IAAIV,CAAQ,EACvBW,EAAUC,EAAkBlB,EAAM,CACtC,KAAMC,EAAe,aACrB,OAAQA,EAAe,eACzB,CAAC,EAEGD,EAAK,OAAS,cAChBiB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAgBJ,EAAG,UAAUH,EAAGC,EAAGH,EAAYC,EAAaM,CAAO,EAEnEG,EAAOd,EAAS,OAAO,IAAMa,EAAe,cAAc,EAChE,OAAAC,EAAK,KAAK,QAAS,uBAAuB,EAEtCN,GAAad,EAAK,OAAS,aAC7BoB,EAAK,UAAU,MAAM,EAAE,KAAK,QAASN,CAAS,EAG5CX,GAAcH,EAAK,OAAS,aAC9BoB,EAAK,UAAU,MAAM,EAAE,KAAK,QAASjB,CAAU,EAGjDkB,EAAiBrB,EAAMoB,CAAI,EAE3BpB,EAAK,UAAY,SAAUsB,EAAO,CAChC,OAAOC,EAAU,KAAKvB,EAAMsB,CAAK,CACnC,EAEOhB,CACT,CAnDsBkB,EAAA1B,GAAA,QCCf,IAAM2B,GAAyBC,EAAA,CAACC,EAAWC,EAAWC,IACpD,CACL,IAAIF,EAAIE,EAAO,CAAC,IAAID,CAAC,GACrB,IAAID,EAAIE,CAAI,IAAID,EAAIC,EAAO,CAAC,GAC5B,IAAIF,EAAIE,EAAO,CAAC,IAAID,EAAIC,CAAI,GAC5B,IAAIF,CAAC,IAAIC,EAAIC,EAAO,CAAC,GACrB,GACF,EAAE,KAAK,GAAG,EAP0B,0BAUtC,eAAsBC,GAAuCC,EAAwBC,EAAY,CAC/F,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAAYP,EAAQC,EAAMO,EAAeP,CAAI,CAAC,EAEzEQ,EAAIH,EAAK,MAAQL,EAAK,QACtBS,EAAIJ,EAAK,OAASL,EAAK,QACvBU,EAAIF,EAAIC,EAERE,EAAS,CACb,CAAE,EAAGD,EAAI,EAAG,EAAG,CAAE,EACjB,CAAE,EAAGA,EAAG,EAAG,CAACA,EAAI,CAAE,EAClB,CAAE,EAAGA,EAAI,EAAG,EAAG,CAACA,CAAE,EAClB,CAAE,EAAG,EAAG,EAAG,CAACA,EAAI,CAAE,CACpB,EAEIE,EACE,CAAE,UAAAC,CAAU,EAAIb,EAEtB,GAAIA,EAAK,OAAS,YAAa,CAE7B,IAAMc,EAAKC,EAAM,IAAIX,CAAQ,EACvBY,EAAUC,EAAkBjB,EAAM,CAAC,CAAC,EACpCkB,EAAWzB,GAAuB,EAAG,EAAGiB,CAAC,EACzCS,EAAYL,EAAG,KAAKI,EAAUF,CAAO,EAE3CJ,EAAUR,EACP,OAAO,IAAMe,EAAW,cAAc,EACtC,KAAK,YAAa,aAAa,CAACT,EAAI,CAAC,KAAKA,EAAI,CAAC,GAAG,EAEjDG,GACFD,EAAQ,KAAK,QAASC,CAAS,CAEnC,MACED,EAAUQ,EAAmBhB,EAAUM,EAAGA,EAAGC,CAAM,EAGrD,OAAIT,GACFU,EAAQ,KAAK,QAASV,CAAU,EAGlCmB,EAAiBrB,EAAMY,CAAO,EAE9BZ,EAAK,UAAY,SAAUsB,EAAO,CAChC,OAAAC,EAAI,MACF;AAAA,QACAD,EACA;AAAA;AAAA,EACAtB,EACA;AAAA,MACAwB,EAAU,QAAQxB,EAAMW,EAAQW,CAAK,CACvC,EACOE,EAAU,QAAQxB,EAAMW,EAAQW,CAAK,CAC9C,EAEOlB,CACT,CAxDsBV,EAAAI,GAAA,YCZtB,eAAsB2B,GACpBC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAAI,MAAMC,EAAYR,EAAQC,EAAMQ,EAAeR,CAAI,CAAC,EAEhFS,EAAI,KAAK,IAAIJ,EAAK,OAASL,EAAK,SAAW,GAAIA,GAAM,OAAS,CAAC,EAC/DU,EAAI,KAAK,IAAIL,EAAK,QAAUL,EAAK,SAAW,GAAIA,GAAM,QAAU,CAAC,EAEjEW,EAAI,CAACF,EAAI,EACTG,EAAI,CAACF,EAAI,EACTG,EAAQD,EAAI,EAEZE,EAAS,CACb,CAAE,EAAGH,EAAIE,EAAO,EAAAD,CAAE,EAClB,CAAE,EAAGD,EAAG,EAAG,CAAE,EACb,CAAE,EAAGA,EAAIE,EAAO,EAAG,CAACD,CAAE,EACtB,CAAE,EAAG,CAACD,EAAG,EAAG,CAACC,CAAE,EACf,CAAE,EAAG,CAACD,EAAG,EAAAC,CAAE,CACb,EAEM,CAAE,UAAAG,CAAU,EAAIf,EAEhBgB,EAAKC,EAAM,IAAIb,CAAQ,EACvBc,EAAUC,EAAkBnB,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChBkB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAWC,EAAqBP,CAAM,EACtCQ,EAAYN,EAAG,KAAKI,EAAUF,CAAO,EAErCK,EAAUnB,EAAS,OAAO,IAAMkB,EAAW,cAAc,EAE/D,OAAAC,EAAQ,KAAK,QAAS,uBAAuB,EAEzCR,GAAaf,EAAK,OAAS,aAC7BuB,EAAQ,UAAU,MAAM,EAAE,KAAK,QAASR,CAAS,EAE/Cb,GAAcF,EAAK,OAAS,aAC9BuB,EAAQ,UAAU,MAAM,EAAE,KAAK,QAASrB,CAAU,EAGpDqB,EAAQ,KAAK,YAAa,aAAa,CAACV,EAAQ,CAAC,KAAK,EAEtDP,EAAM,KACJ,YACA,aAAa,CAACO,EAAQ,EAAIR,EAAK,MAAQ,GAAKA,EAAK,GAAKA,EAAK,MAAQ,GAAG,KAAK,EAAEA,EAAK,OAAS,IAAMA,EAAK,GAAKA,EAAK,KAAO,GAAG,GAC5H,EACAmB,EAAiBxB,EAAMuB,CAAO,EAE9BvB,EAAK,UAAY,SAAUyB,EAAO,CAChC,OAAOC,EAAU,QAAQ1B,EAAMc,EAAQW,CAAK,CAC9C,EAEOrB,CACT,CA5DsBuB,EAAA7B,GAAA,uBCMtB,eAAsB8B,GACpBC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,IAAIG,EACCJ,EAAK,WAGRI,EAAU,QAAUJ,EAAK,WAFzBI,EAAU,eAMZ,IAAMC,EAAWN,EAEd,OAAO,GAAG,EACV,KAAK,QAASK,CAAO,EACrB,KAAK,KAAMJ,EAAK,OAASA,EAAK,EAAE,EAG7BM,EAAID,EAAS,OAAO,GAAG,EAEvBE,EAAQF,EAAS,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EAAE,KAAK,QAASH,CAAU,EAE5EM,EAAcR,EAAK,YAEnBS,EAAQT,EAAK,MAEbU,EAAOH,EAAM,KAAK,EAAG,YAAY,MAAMI,GAAYF,EAAOT,EAAK,WAAY,GAAM,EAAI,CAAC,EACxFY,EAAO,CAAE,MAAO,EAAG,OAAQ,CAAE,EACjC,GAAIC,EAASC,EAAU,GAAG,WAAW,UAAU,EAAG,CAChD,IAAMC,EAAML,EAAK,SAAS,CAAC,EACrBM,EAAKC,EAAOP,CAAI,EACtBE,EAAOG,EAAI,sBAAsB,EACjCC,EAAG,KAAK,QAASJ,EAAK,KAAK,EAC3BI,EAAG,KAAK,SAAUJ,EAAK,MAAM,CAC/B,CACAM,EAAI,KAAK,SAAUV,CAAW,EAC9B,IAAMW,EAAWX,GAAe,CAAC,EAC3BY,EAAWV,EAAK,QAAQ,EACxBW,EAAQd,EACX,KAAK,EACL,YACC,MAAMI,GACJQ,EAAS,KAAOA,EAAS,KAAK,OAAO,EAAIA,EACzCnB,EAAK,WACL,GACA,EACF,CACF,EAGIe,EAAMM,EAAM,SAAS,CAAC,EACtBL,EAAKC,EAAOI,CAAK,EACvBT,EAAOG,EAAI,sBAAsB,EACjCC,EAAG,KAAK,QAASJ,EAAK,KAAK,EAC3BI,EAAG,KAAK,SAAUJ,EAAK,MAAM,EAG7B,IAAMU,GAAetB,EAAK,SAAW,GAAK,EAC1CiB,EAAOI,CAAK,EAAE,KACZ,YACA,eACGT,EAAK,MAAQQ,EAAS,MAAQ,GAAKA,EAAS,MAAQR,EAAK,OAAS,GACnE,MACCQ,EAAS,OAASE,EAAc,GACjC,GACJ,EACAL,EAAOP,CAAI,EAAE,KACX,YACA,eACGE,EAAK,MAAQQ,EAAS,MAAQ,EAAI,EAAEA,EAAS,MAAQR,EAAK,OAAS,GACpE,MAGJ,EAIAA,EAAOL,EAAM,KAAK,EAAG,QAAQ,EAG7BA,EAAM,KACJ,YACA,aAAe,CAACK,EAAK,MAAQ,EAAI,MAAQ,CAACA,EAAK,OAAS,EAAIU,EAAc,GAAK,GACjF,EAEA,IAAMC,EAAaX,EAAK,OAASZ,EAAK,SAAW,GAC3CwB,EAAcZ,EAAK,QAAUZ,EAAK,SAAW,GAC7CyB,EAAI,CAACb,EAAK,MAAQ,EAAIU,EACtBI,EAAI,CAACd,EAAK,OAAS,EAAIU,EACzBK,EACAC,EACJ,GAAI5B,EAAK,OAAS,YAAa,CAE7B,IAAM6B,EAAKC,EAAM,IAAIzB,CAAQ,EACvB0B,EAAUC,EAAkBhC,EAAM,CAAC,CAAC,EACpCiC,EAAYJ,EAAG,KACnBK,EAAuBT,EAAGC,EAAGH,EAAYC,EAAaxB,EAAK,IAAM,CAAC,EAClE+B,CACF,EAEMI,EAAYN,EAAG,KACnB,CAACjB,EAAK,MAAQ,EAAIU,EAClB,CAACV,EAAK,OAAS,EAAIU,EAAcF,EAAS,OAASE,EACnDV,EAAK,MAAQ,EAAIU,EACjB,CAACV,EAAK,OAAS,EAAIU,EAAcF,EAAS,OAASE,EACnDS,CACF,EAEAH,EAAYvB,EAAS,OAAO,KAC1Ba,EAAI,MAAM,wBAAyBe,CAAS,EACrCE,GACN,cAAc,EACjBR,EAAOtB,EAAS,OAAO,KACrBa,EAAI,MAAM,wBAAyBe,CAAS,EACrCA,GACN,cAAc,CACnB,MACEN,EAAOrB,EAAE,OAAO,OAAQ,cAAc,EACtCsB,EAAYtB,EAAE,OAAO,MAAM,EAC3BqB,EACG,KAAK,QAAS,mBAAmB,EACjC,KAAK,QAASzB,CAAU,EACxB,KAAK,IAAK,CAACU,EAAK,MAAQ,EAAIU,CAAW,EACvC,KAAK,IAAK,CAACV,EAAK,OAAS,EAAIU,CAAW,EACxC,KAAK,QAASV,EAAK,OAASZ,EAAK,SAAW,EAAE,EAC9C,KAAK,SAAUY,EAAK,QAAUZ,EAAK,SAAW,EAAE,EAEnD4B,EACG,KAAK,QAAS,SAAS,EACvB,KAAK,KAAM,CAAChB,EAAK,MAAQ,EAAIU,CAAW,EACxC,KAAK,KAAMV,EAAK,MAAQ,EAAIU,CAAW,EACvC,KAAK,KAAM,CAACV,EAAK,OAAS,EAAIU,EAAcF,EAAS,OAASE,CAAW,EACzE,KAAK,KAAM,CAACV,EAAK,OAAS,EAAIU,EAAcF,EAAS,OAASE,CAAW,EAE9E,OAAAc,EAAiBpC,EAAM2B,CAAI,EAE3B3B,EAAK,UAAY,SAAUqC,EAAO,CAChC,OAAOC,EAAU,KAAKtC,EAAMqC,CAAK,CACnC,EAEOhC,CACT,CAhJsBkC,EAAAzC,GAAA,iBCTtB,eAAsB0C,GACpBC,EACAC,EACA,CACA,IAAMC,EAAU,CACd,GAAI,EACJ,GAAI,EACJ,QAAS,GACT,eAAgBD,GAAM,SAAW,GAAK,EACtC,eAAgBA,GAAM,SAAW,GAAK,CACxC,EAEA,OAAOE,GAASH,EAAQC,EAAMC,CAAO,CACvC,CAbsBE,EAAAL,GAAA,eCItB,eAAsBM,GACpBC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAAI,MAAMC,EAAYR,EAAQC,EAAMQ,EAAeR,CAAI,CAAC,EAChFS,EAAcT,GAAM,SAAW,EAC/BU,EAAI,KAAK,IAAIL,EAAK,OAASL,EAAK,SAAW,GAAK,EAAGA,GAAM,OAAS,CAAC,EACnEW,EAAI,KAAK,IAAIN,EAAK,QAAUL,EAAK,SAAW,GAAK,EAAGA,GAAM,QAAU,CAAC,EACrEY,EAAI,CAACP,EAAK,MAAQ,EAAII,EACtBI,EAAI,CAACR,EAAK,OAAS,EAAII,EAEvB,CAAE,UAAAK,CAAU,EAAId,EAEhBe,EAAKC,EAAM,IAAIZ,CAAQ,EACvBa,EAAUC,EAAkBlB,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChBiB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAS,CACb,CAAE,EAAAP,EAAG,EAAAC,CAAE,EACP,CAAE,EAAGD,EAAIF,EAAI,EAAG,EAAAG,CAAE,EAClB,CAAE,EAAGD,EAAIF,EAAI,EAAG,EAAGG,EAAIF,CAAE,EACzB,CAAE,EAAGC,EAAI,EAAG,EAAGC,EAAIF,CAAE,EACrB,CAAE,EAAGC,EAAI,EAAG,EAAGC,CAAE,EACjB,CAAE,EAAAD,EAAG,EAAAC,CAAE,EACP,CAAE,EAAAD,EAAG,EAAGC,EAAIF,CAAE,CAChB,EAEMS,EAAYL,EAAG,QACnBI,EAAO,IAAKE,GAAM,CAACA,EAAE,EAAGA,EAAE,CAAC,CAAC,EAC5BJ,CACF,EAEMK,EAAOlB,EAAS,OAAO,IAAMgB,EAAW,cAAc,EAE5D,OAAAE,EAAK,KAAK,QAAS,uBAAuB,EAAE,KAAK,QAASC,EAAoBT,CAAS,CAAC,EAEpFZ,GAAcF,EAAK,OAAS,aAC9BsB,EAAK,UAAU,MAAM,EAAE,KAAK,QAASpB,CAAU,EAG7CY,GAAad,EAAK,OAAS,aAC7BsB,EAAK,UAAU,MAAM,EAAE,KAAK,QAASpB,CAAU,EAGjDI,EAAM,KACJ,YACA,aAAa,CAACI,EAAI,EAAI,GAAKV,EAAK,SAAW,IAAMK,EAAK,GAAKA,EAAK,MAAQ,GAAG,IAAI,CAACM,EAAI,GAAKX,EAAK,SAAW,IAAMK,EAAK,GAAKA,EAAK,KAAO,GAAG,GAC1I,EAEAmB,EAAiBxB,EAAMsB,CAAI,EAE3BtB,EAAK,UAAY,SAAUyB,EAAO,CAChC,OAAOC,EAAU,KAAK1B,EAAMyB,CAAK,CACnC,EAEOrB,CACT,CA9DsBuB,EAAA7B,GAAA,iBCDtB,eAAsB8B,GAAyCC,EAAwBC,EAAY,CACjG,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAAI,MAAMC,EAAYR,EAAQC,EAAMQ,EAAeR,CAAI,CAAC,EAChFS,EAAI,KAAK,IAAIJ,EAAK,OAASL,EAAK,SAAW,GAAK,EAAGA,GAAM,OAAS,CAAC,EACnEU,EAAI,KAAK,IAAIL,EAAK,QAAUL,EAAK,SAAW,GAAK,EAAGA,GAAM,QAAU,CAAC,EACrEW,EAAI,CAACF,EAAI,EACTG,EAAI,CAACF,EAAI,EAET,CAAE,UAAAG,CAAU,EAAIb,EAGhBc,EAAKC,EAAM,IAAIX,CAAQ,EACvBY,EAAUC,EAAkBjB,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChBgB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAS,CACb,CAAE,EAAAP,EAAG,EAAAC,CAAE,EACP,CAAE,EAAAD,EAAG,EAAGC,EAAIF,CAAE,EACd,CAAE,EAAGC,EAAIF,EAAG,EAAGG,EAAIF,CAAE,EACrB,CAAE,EAAGC,EAAIF,EAAG,EAAGG,EAAIF,EAAI,CAAE,CAC3B,EAEMS,EAAWC,EAAqBF,CAAM,EACtCG,EAAYP,EAAG,KAAKK,EAAUH,CAAO,EAErCM,EAAUlB,EAAS,OAAO,IAAMiB,EAAW,cAAc,EAC/D,OAAAC,EAAQ,KAAK,QAAS,uBAAuB,EAEzCT,GAAab,EAAK,OAAS,aAC7BsB,EAAQ,eAAe,MAAM,EAAE,KAAK,QAAST,CAAS,EAGpDX,GAAcF,EAAK,OAAS,aAC9BsB,EAAQ,eAAe,MAAM,EAAE,KAAK,QAASpB,CAAU,EAGzDoB,EAAQ,KAAK,YAAa,gBAAgBZ,EAAI,CAAC,GAAG,EAClDJ,EAAM,KACJ,YACA,aAAa,CAACG,EAAI,GAAKT,EAAK,SAAW,IAAMK,EAAK,GAAKA,EAAK,MAAQ,GAAG,KAAK,CAACK,EAAI,GAAKV,EAAK,SAAW,IAAMK,EAAK,GAAKA,EAAK,KAAO,GAAG,GACvI,EAEAkB,EAAiBvB,EAAMsB,CAAO,EAE9BtB,EAAK,UAAY,SAAUwB,EAAO,CAEhC,OADYC,EAAU,QAAQzB,EAAMkB,EAAQM,CAAK,CAEnD,EAEOpB,CACT,CAvDsBsB,EAAA5B,GAAA,cCHtB,eAAsB6B,GAAyCC,EAAwBC,EAAY,CACjG,IAAMC,EAAU,CACd,GAAI,EACJ,GAAI,EACJ,QAAS,GACT,eAAgBD,GAAM,SAAW,GAAK,EACtC,eAAgBA,GAAM,SAAW,GAAK,CACxC,EACA,OAAOE,GAASH,EAAQC,EAAMC,CAAO,CACvC,CATsBE,EAAAL,GAAA,cCkDtB,eAAsBM,GAAsCC,EAAwBC,EAAY,CAC9F,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAAYP,EAAQC,EAAMO,EAAeP,CAAI,CAAC,EAEzE,EAAIK,EAAK,OAASL,EAAK,QACvBQ,EAAIH,EAAK,MAAQ,EAAI,EAAIL,EAAK,QAEhCS,EACE,CAAE,UAAAC,CAAU,EAAIV,EACtB,GAAIA,EAAK,OAAS,YAAa,CAE7B,IAAMW,EAAKC,EAAM,IAAIR,CAAQ,EACvBS,EAAUC,EAAkBd,EAAM,CAAC,CAAC,EAEpCe,EAAWC,EAAuB,CAACR,EAAI,EAAG,CAAC,EAAI,EAAGA,EAAG,EAAG,EAAI,CAAC,EAC7DS,EAAYN,EAAG,KAAKI,EAAUF,CAAO,EAE3CJ,EAAOL,EAAS,OAAO,IAAMa,EAAW,cAAc,EACtDR,EAAK,KAAK,QAAS,uBAAuB,EAAE,KAAK,QAASS,EAAoBR,CAAS,CAAC,CAC1F,MACED,EAAOL,EAAS,OAAO,OAAQ,cAAc,EAE7CK,EACG,KAAK,QAAS,uBAAuB,EACrC,KAAK,QAASP,CAAU,EACxB,KAAK,KAAM,EAAI,CAAC,EAChB,KAAK,KAAM,EAAI,CAAC,EAChB,KAAK,IAAK,CAACM,EAAI,CAAC,EAChB,KAAK,IAAK,CAAC,EAAI,CAAC,EAChB,KAAK,QAASA,CAAC,EACf,KAAK,SAAU,CAAC,EAGrB,OAAAW,EAAiBnB,EAAMS,CAAI,EAE3BT,EAAK,UAAY,SAAUoB,EAAO,CAChC,OAAOC,EAAU,KAAKrB,EAAMoB,CAAK,CACnC,EAEOhB,CACT,CAzCsBkB,EAAAxB,GAAA,WClDtB,eAAsByB,GAAoCC,EAAwBC,EAAY,CAM5F,OAAOC,GAASF,EAAQC,EALR,CACd,GAAI,EACJ,GAAI,EACJ,QAAS,gBACX,CACqC,CACvC,CAPsBE,EAAAJ,GAAA,SCGf,SAASK,GACdC,EACAC,EACA,CAAE,OAAQ,CAAE,eAAAC,CAAe,CAAE,EAC7B,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcJ,CAAI,EACtDA,EAAK,WAAaE,EAClB,GAAM,CAAE,UAAAG,CAAU,EAAIL,EAChB,CAAE,UAAAM,EAAW,YAAAC,EAAa,WAAAC,CAAW,EAAIP,EACzCQ,EAAWV,EACd,OAAO,GAAG,EACV,KAAK,QAAS,cAAc,EAC5B,KAAK,KAAMC,EAAK,OAASA,EAAK,EAAE,EAG7BU,EAAKC,EAAM,IAAIF,CAAQ,EACvBG,EAAUC,EAAkBb,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChBY,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAYJ,EAAG,OAAO,EAAG,EAAG,GAAI,CACpC,GAAGE,EACH,OAAQN,EACR,YAAa,CACf,CAAC,EACKS,EAAYR,GAAeC,EAC3BQ,EAAiBN,EAAG,OAAO,EAAG,EAAG,EAAG,CACxC,GAAGE,EACH,KAAMG,EACN,OAAQA,EACR,YAAa,EACb,UAAW,OACb,CAAC,EACKE,EAASR,EAAS,OAAO,IAAMK,EAAW,cAAc,EAC9D,OAAAG,EAAO,OAAO,IAAMD,CAAc,EAE9BX,GACFY,EAAO,UAAU,MAAM,EAAE,KAAK,QAASZ,CAAS,EAG9CF,GACFc,EAAO,UAAU,MAAM,EAAE,KAAK,QAASd,CAAU,EAGnDe,EAAiBlB,EAAMiB,CAAM,EAE7BjB,EAAK,UAAY,SAAUmB,EAAO,CAChC,OAAOC,EAAU,OAAOpB,EAAM,EAAGmB,CAAK,CACxC,EAEOV,CACT,CAtDgBY,EAAAvB,GAAA,YCAT,SAASwB,GACdC,EACAC,EACA,CAAE,OAAQ,CAAE,eAAAC,CAAe,CAAE,EAC7B,CACA,GAAM,CAAE,UAAAC,CAAU,EAAID,EAEhBE,EAAWJ,EACd,OAAO,GAAG,EACV,KAAK,QAAS,cAAc,EAC5B,KAAK,KAAMC,EAAK,OAASA,EAAK,EAAE,EAE/BI,EACJ,GAAIJ,EAAK,OAAS,YAAa,CAG7B,IAAMK,EADKC,EAAM,IAAIH,CAAQ,EACR,OAAO,EAAG,EAAG,GAAII,GAAeL,CAAS,CAAC,EAC/DE,EAASD,EAAS,OAAO,IAAME,CAAS,EAExCD,EAAO,KAAK,QAAS,aAAa,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,QAAS,EAAE,EAAE,KAAK,SAAU,EAAE,CACtF,MACEA,EAASD,EAAS,OAAO,SAAU,cAAc,EAEjDC,EAAO,KAAK,QAAS,aAAa,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,QAAS,EAAE,EAAE,KAAK,SAAU,EAAE,EAGtF,OAAAI,EAAiBR,EAAMI,CAAM,EAE7BJ,EAAK,UAAY,SAAUS,EAAO,CAChC,OAAOC,EAAU,OAAOV,EAAM,EAAGS,CAAK,CACxC,EAEON,CACT,CAjCgBQ,EAAAb,GAAA,cC4BhB,eAAsBc,GAAyCC,EAAwBC,EAAY,CACjG,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAAYP,EAAQC,EAAMO,EAAeP,CAAI,CAAC,EACzEQ,GAAeR,GAAM,SAAW,GAAK,EACrCS,EAAIJ,EAAK,MAAQL,EAAK,QACtBU,EAAIL,EAAK,OAASL,EAAK,QACvBW,EAAI,CAACN,EAAK,MAAQ,EAAIG,EACtBI,EAAI,CAACP,EAAK,OAAS,EAAIG,EAEvBK,EAAS,CACb,CAAE,EAAG,EAAG,EAAG,CAAE,EACb,CAAE,EAAGJ,EAAG,EAAG,CAAE,EACb,CAAE,EAAGA,EAAG,EAAG,CAACC,CAAE,EACd,CAAE,EAAG,EAAG,EAAG,CAACA,CAAE,EACd,CAAE,EAAG,EAAG,EAAG,CAAE,EACb,CAAE,EAAG,GAAI,EAAG,CAAE,EACd,CAAE,EAAGD,EAAI,EAAG,EAAG,CAAE,EACjB,CAAE,EAAGA,EAAI,EAAG,EAAG,CAACC,CAAE,EAClB,CAAE,EAAG,GAAI,EAAG,CAACA,CAAE,EACf,CAAE,EAAG,GAAI,EAAG,CAAE,CAChB,EAEA,GAAIV,EAAK,OAAS,YAAa,CAE7B,IAAMc,EAAKC,EAAM,IAAIX,CAAQ,EACvBY,EAAUC,EAAkBjB,EAAM,CAAC,CAAC,EAEpCkB,EAAYJ,EAAG,UAAUH,EAAI,EAAGC,EAAGH,EAAI,GAAIC,EAAGM,CAAO,EACrDG,EAAKL,EAAG,KAAKH,EAAGC,EAAGD,EAAGC,EAAIF,EAAGM,CAAO,EACpCI,EAAKN,EAAG,KAAKH,EAAIF,EAAGG,EAAGD,EAAIF,EAAGG,EAAIF,EAAGM,CAAO,EAElDZ,EAAS,OAAO,IAAMe,EAAI,cAAc,EACxCf,EAAS,OAAO,IAAMgB,EAAI,cAAc,EACxC,IAAMC,EAAOjB,EAAS,OAAO,IAAMc,EAAW,cAAc,EACtD,CAAE,UAAAI,CAAU,EAAItB,EACtBqB,EAAK,KAAK,QAAS,uBAAuB,EAAE,KAAK,QAASE,EAAoBD,CAAS,CAAC,EACxFE,EAAiBxB,EAAMqB,CAAI,CAC7B,KAAO,CACL,IAAMI,EAAKC,EAAmBtB,EAAUK,EAAGC,EAAGG,CAAM,EAChDX,GACFuB,EAAG,KAAK,QAASvB,CAAU,EAE7BsB,EAAiBxB,EAAMyB,CAAE,CAC3B,CAEA,OAAAzB,EAAK,UAAY,SAAU2B,EAAO,CAChC,OAAOC,EAAU,QAAQ5B,EAAMa,EAAQc,CAAK,CAC9C,EAEOvB,CACT,CAnDsByB,EAAA/B,GAAA,cC5BtB,eAAsBgC,GAAyCC,EAAwBC,EAAY,CACjG,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAAYP,EAAQC,EAAMO,EAAeP,CAAI,CAAC,EACzEQ,EAAI,KAAK,IAAIH,EAAK,OAASL,EAAK,SAAW,GAAK,EAAGA,GAAM,OAAS,CAAC,EACnES,EAAI,KAAK,IAAIJ,EAAK,QAAUL,EAAK,SAAW,GAAK,EAAGA,GAAM,QAAU,CAAC,EACrEU,EAAI,CAACF,EAAI,EACTG,EAAI,CAACF,EAAI,EACTG,EAAW,GAAMH,EACjBI,EAAY,GAAMJ,EAClB,CAAE,UAAAK,CAAU,EAAId,EAGhBe,EAAKC,EAAM,IAAIZ,CAAQ,EACvBa,EAAUC,EAAkBlB,EAAM,CAAC,CAAC,EAEpCmB,EAAa,CACjB,CAAE,EAAGT,EAAIE,EAAW,EAAG,EAAAD,CAAE,EACzB,CAAE,EAAGD,EAAIF,EAAII,EAAW,EAAG,EAAAD,CAAE,EAC7B,CAAE,EAAGD,EAAIF,EAAII,EAAW,EAAG,EAAGD,EAAIF,CAAE,EACpC,CAAE,EAAGC,EAAIE,EAAW,EAAG,EAAGD,EAAIF,CAAE,CAClC,EAEMW,EAAY,CAChB,CAAE,EAAGV,EAAIF,EAAII,EAAW,EAAG,EAAGD,EAAIF,CAAE,EACpC,CAAE,EAAGC,EAAIF,EAAII,EAAW,EAAG,EAAGD,EAAIF,CAAE,EACpC,CAAE,EAAGC,EAAIF,EAAII,EAAW,EAAG,EAAGD,EAAIF,EAAII,CAAU,CAClD,EAEIb,EAAK,OAAS,cAChBiB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAMI,EAAWC,EAAqBH,CAAU,EAC1CI,EAAWR,EAAG,KAAKM,EAAUJ,CAAO,EAEpCO,EAAUF,EAAqBF,CAAS,EACxCK,EAAUV,EAAG,KAAKS,EAAS,CAAE,GAAGP,EAAS,UAAW,OAAQ,CAAC,EAE7DnB,EAAaM,EAAS,OAAO,IAAMqB,EAAS,cAAc,EAChE,OAAA3B,EAAW,OAAO,IAAMyB,EAAU,cAAc,EAEhDzB,EAAW,KAAK,QAAS,uBAAuB,EAE5CgB,GAAad,EAAK,OAAS,aAC7BF,EAAW,UAAU,MAAM,EAAE,KAAK,QAASgB,CAAS,EAGlDZ,GAAcF,EAAK,OAAS,aAC9BF,EAAW,UAAU,MAAM,EAAE,KAAK,QAASI,CAAU,EAGvDwB,EAAiB1B,EAAMF,CAAU,EAEjCE,EAAK,UAAY,SAAU2B,EAAO,CAGhC,OAFYC,EAAU,QAAQ5B,EAAMmB,EAAYQ,CAAK,CAGvD,EAEOvB,CACT,CA9DsByB,EAAA/B,GAAA,cCMtB,eAAsBgC,GACpBC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAAI,MAAMC,EAAYR,EAAQC,EAAMQ,EAAeR,CAAI,CAAC,EAChFS,EAAI,KAAK,IAAIJ,EAAK,OAASL,EAAK,SAAW,GAAK,EAAGA,GAAM,OAAS,CAAC,EACnEU,EAAI,KAAK,IAAIL,EAAK,QAAUL,EAAK,SAAW,GAAK,EAAGA,GAAM,QAAU,CAAC,EACrEW,EAAgBD,EAAI,EACpBE,EAAW,GAAMH,EACjBI,EAAY,GAAMH,EAClBI,EAASJ,EAAIC,EACb,CAAE,UAAAI,CAAU,EAAIf,EAGhBgB,EAAKC,EAAM,IAAIb,CAAQ,EACvBc,EAAUC,EAAkBnB,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChBkB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAS,CACb,CAAE,EAAG,CAACX,EAAI,EAAKA,EAAI,EAAK,GAAK,EAAGK,EAAS,CAAE,EAC3C,GAAGO,GACD,CAACZ,EAAI,EAAKA,EAAI,EAAK,GACnBK,EAAS,EACTL,EAAI,EAAKA,EAAI,EAAK,GAClBK,EAAS,EACTH,EACA,EACF,EAEA,CAAE,EAAGF,EAAI,EAAKA,EAAI,EAAK,GAAK,EAAG,CAACK,EAAS,CAAE,EAC3C,CAAE,EAAG,CAACL,EAAI,EAAKA,EAAI,EAAK,GAAK,EAAG,CAACK,EAAS,CAAE,CAC9C,EAEM,EAAI,CAACL,EAAI,EAAKA,EAAI,EAAK,GACvBa,EAAI,CAACR,EAAS,EAAID,EAAY,GAE9BU,EAAY,CAChB,CAAE,EAAG,EAAId,EAAIG,EAAU,GAAIU,EAAIZ,GAAK,GAAI,EACxC,CAAE,EAAG,EAAID,EAAG,EAAGa,EAAIZ,EAAIG,CAAU,EACjC,CAAE,EAAG,EAAIJ,EAAG,GAAIa,EAAIZ,GAAK,EAAI,EAC7B,GAAGW,GACD,EAAIZ,GACHa,EAAIZ,GAAK,IACV,EAAID,EAAIG,GACPU,EAAIZ,GAAK,IACV,CAACA,EAAI,IACL,EACF,CACF,EAEMc,EAAmBC,EAAqBL,CAAM,EAC9CM,EAAmBV,EAAG,KAAKQ,EAAkBN,CAAO,EAEpDS,EAAyBF,EAAqBF,CAAS,EACvDK,EAAyBZ,EAAG,KAAKW,EAAwB,CAC7D,GAAGT,EACH,UAAW,OACb,CAAC,EAEKW,EAAezB,EAAS,OAAO,IAAMwB,EAAwB,cAAc,EACjF,OAAAC,EAAa,OAAO,IAAMH,EAAkB,cAAc,EAE1DG,EAAa,KAAK,QAAS,uBAAuB,EAE9Cd,GAAaf,EAAK,OAAS,aAC7B6B,EAAa,UAAU,MAAM,EAAE,KAAK,QAASd,CAAS,EAGpDb,GAAcF,EAAK,OAAS,aAC9B6B,EAAa,UAAU,MAAM,EAAE,KAAK,QAAS3B,CAAU,EAGzD2B,EAAa,KAAK,YAAa,eAAe,CAAClB,EAAgB,CAAC,GAAG,EACnEL,EAAM,KACJ,YACA,aAAa,CAACG,EAAI,GAAKT,EAAK,SAAW,IAAMK,EAAK,GAAKA,EAAK,MAAQ,GAAG,IAAI,CAACK,EAAI,GAAKV,EAAK,SAAW,GAAKW,EAAgB,GAAKN,EAAK,GAAKA,EAAK,KAAO,GAAG,GAC1J,EAEAyB,EAAiB9B,EAAM6B,CAAY,EACnC7B,EAAK,UAAY,SAAU+B,EAAO,CAEhC,OADYC,EAAU,QAAQhC,EAAMoB,EAAQW,CAAK,CAEnD,EAEO3B,CACT,CA3FsB6B,EAAAnC,GAAA,4BCPtB,eAAsBoC,GAAmCC,EAAwBC,EAAY,CAC3F,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAElB,GAAM,CAAE,SAAAG,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAAYP,EAAQC,EAAMO,EAAeP,CAAI,CAAC,EAEzEQ,EAAa,KAAK,IAAIH,EAAK,MAAQL,EAAK,QAASA,GAAM,OAAS,CAAC,EACjES,EAAc,KAAK,IAAIJ,EAAK,OAASL,EAAK,QAASA,GAAM,QAAU,CAAC,EACpEU,EAAI,CAACF,EAAa,EAClBG,EAAI,CAACF,EAAc,EAEnBG,EAAOR,EAAS,OAAO,OAAQ,cAAc,EAEnD,OAAAQ,EACG,KAAK,QAAS,MAAM,EACpB,KAAK,QAASV,CAAU,EACxB,KAAK,KAAM,CAAC,EACZ,KAAK,KAAM,CAAC,EACZ,KAAK,IAAKQ,CAAC,EACX,KAAK,IAAKC,CAAC,EACX,KAAK,QAASH,CAAU,EACxB,KAAK,SAAUC,CAAW,EAE7BI,EAAiBb,EAAMY,CAAI,EAE3BZ,EAAK,UAAY,SAAUc,EAAO,CAChC,OAAOC,EAAU,KAAKf,EAAMc,CAAK,CACnC,EAEOV,CACT,CA9BsBY,EAAAlB,GAAA,QCEf,IAAMmB,GAAsBC,EAAA,CACjCC,EACAC,EACAC,EACAC,EACAC,EACAC,IAEO,IAAIL,CAAC,IAAIC,CAAC;AAAA,OACZG,CAAE,IAAIC,CAAE,YAAe,CAACF,CAAM;AAAA,OAC9BD,CAAK;AAAA,OACLE,CAAE,IAAIC,CAAE,YAAeF,CAAM;AAAA,OAC7BD,CAAK,IAAI,CAACC,CAAM;AAAA,OAChBC,CAAE,IAAIC,CAAE,YAAeF,CAAM;AAAA,OAC7B,CAACD,CAAK,KAdsB,uBAiBtBI,GAA2BP,EAAA,CACtCC,EACAC,EACAC,EACAC,EACAC,EACAC,IAEO,CACL,IAAIL,CAAC,IAAIC,CAAC,GACV,IAAID,EAAIE,CAAK,IAAID,CAAC,GAClB,IAAIG,CAAE,IAAIC,CAAE,YAAe,CAACF,CAAM,GAClC,IAAI,CAACD,CAAK,KACV,IAAIE,CAAE,IAAIC,CAAE,YAAeF,CAAM,GACjC,IAAID,CAAK,IACX,EAAE,KAAK,GAAG,EAf4B,4BAiB3BK,GAA2BR,EAAA,CACtCC,EACAC,EACAC,EACAC,EACAC,EACAC,IAEO,CAAC,IAAIL,EAAIE,EAAQ,CAAC,IAAI,CAACC,EAAS,CAAC,GAAI,IAAIC,CAAE,IAAIC,CAAE,YAAYF,CAAM,EAAE,EAAE,KAAK,GAAG,EARhD,4BAWxC,eAAsBK,GACpBC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,EAAM,MAAAC,EAAO,YAAAC,CAAY,EAAI,MAAMC,EACnDT,EACAC,EACAS,EAAeT,CAAI,CACrB,EACMU,EAAeV,EAAK,OAAS,MAAQO,EAAc,EAAIA,EACvDI,EAAIN,EAAK,OAASK,EAClBf,EAAKgB,EAAI,EACTjB,EAAKC,GAAM,IAAMgB,EAAI,IACrBC,EAAIP,EAAK,MAAQX,EAAKgB,EACtB,CAAE,UAAAG,CAAU,EAAIb,EAElBc,EAEJ,GAAId,EAAK,OAAS,YAAa,CAE7B,IAAMe,EAAKC,EAAM,IAAIZ,CAAQ,EACvBa,EAAgBrB,GAAyB,EAAG,EAAGgB,EAAGD,EAAGjB,EAAIC,CAAE,EAC3DuB,EAAgBrB,GAAyB,EAAG,EAAGe,EAAGD,EAAGjB,EAAIC,CAAE,EAC3DwB,EAAYJ,EAAG,KAAKE,EAAeG,EAAkBpB,EAAM,CAAC,CAAC,CAAC,EAC9DqB,EAAYN,EAAG,KAAKG,EAAeE,EAAkBpB,EAAM,CAAE,KAAM,MAAO,CAAC,CAAC,EAClFc,EAAWV,EAAS,OAAO,IAAMiB,EAAW,cAAc,EAC1DP,EAAWV,EAAS,OAAO,IAAMe,EAAW,cAAc,EAC1DL,EAAS,KAAK,QAAS,uBAAuB,EAC1CD,GACFC,EAAS,KAAK,QAASD,CAAS,CAEpC,KAAO,CACL,IAAMS,EAAWlC,GAAoB,EAAG,EAAGwB,EAAGD,EAAGjB,EAAIC,CAAE,EACvDmB,EAAWV,EACR,OAAO,OAAQ,cAAc,EAC7B,KAAK,IAAKkB,CAAQ,EAClB,KAAK,QAAS,uBAAuB,EACrC,KAAK,QAASC,EAAoBV,CAAS,CAAC,EAC5C,KAAK,QAASX,CAAU,EAC3BY,EAAS,KAAK,QAAS,uBAAuB,EAE1CD,GACFC,EAAS,UAAU,MAAM,EAAE,KAAK,QAASD,CAAS,EAGhDX,GACFY,EAAS,UAAU,MAAM,EAAE,KAAK,QAASZ,CAAU,CAEvD,CAEA,OAAAY,EAAS,KAAK,iBAAkBpB,CAAE,EAClCoB,EAAS,KAAK,YAAa,aAAa,CAACF,EAAI,CAAC,KAAKD,EAAI,CAAC,IAAI,EAE5DL,EAAM,KACJ,YACA,aAAa,EAAED,EAAK,MAAQ,GAAKX,GAAMW,EAAK,GAAKA,EAAK,MAAQ,GAAG,KAAK,EAAEA,EAAK,OAAS,IAAMA,EAAK,GAAKA,EAAK,KAAO,GAAG,GACvH,EAEAmB,EAAiBxB,EAAMc,CAAQ,EAE/Bd,EAAK,UAAY,SAAUyB,EAAO,CAChC,IAAMC,EAAMC,EAAU,KAAK3B,EAAMyB,CAAK,EAChClC,EAAImC,EAAI,GAAK1B,EAAK,GAAK,GAE7B,GACEL,GAAM,IACL,KAAK,IAAIJ,CAAC,GAAKS,EAAK,QAAU,GAAK,GACjC,KAAK,IAAIT,CAAC,IAAMS,EAAK,QAAU,GAAK,GACnC,KAAK,IAAI0B,EAAI,GAAK1B,EAAK,GAAK,EAAE,GAAKA,EAAK,OAAS,GAAK,EAAIN,GAC9D,CACA,IAAIJ,EAAII,EAAKA,GAAM,EAAKH,EAAIA,GAAMI,EAAKA,IACnCL,GAAK,IACPA,EAAI,KAAK,KAAK,KAAK,IAAIA,CAAC,CAAC,GAE3BA,EAAII,EAAKJ,EACLmC,EAAM,GAAKzB,EAAK,GAAK,GAAK,IAC5BV,EAAI,CAACA,GAGPoC,EAAI,GAAKpC,CACX,CAEA,OAAOoC,CACT,EAEOtB,CACT,CAxFsBf,EAAAS,GAAA,kBC9BtB,eAAsB8B,GAAwCC,EAAwBC,EAAY,CAChG,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAAYP,EAAQC,EAAMO,EAAeP,CAAI,CAAC,EAEzEQ,EAAIH,EAAK,MAAQL,EAAK,QACtBS,EAAIJ,EAAK,OAASL,EAAK,QACvBU,EAAS,CACb,CAAE,EAAI,GAAKD,EAAK,EAAG,EAAG,CAAE,EACxB,CAAE,EAAGD,EAAK,EAAIC,EAAK,EAAG,EAAG,CAAE,EAC3B,CAAE,EAAGD,EAAG,EAAG,CAACC,CAAE,EACd,CAAE,EAAG,EAAG,EAAG,CAACA,CAAE,CAChB,EAEIE,EACE,CAAE,UAAAC,CAAU,EAAIZ,EAEtB,GAAIA,EAAK,OAAS,YAAa,CAE7B,IAAMa,EAAKC,EAAM,IAAIV,CAAQ,EACvBW,EAAUC,EAAkBhB,EAAM,CAAC,CAAC,EACpCiB,EAAWC,EAAqBR,CAAM,EACtCS,EAAYN,EAAG,KAAKI,EAAUF,CAAO,EAE3CJ,EAAUP,EACP,OAAO,IAAMe,EAAW,cAAc,EACtC,KAAK,YAAa,aAAa,CAACX,EAAI,CAAC,KAAKC,EAAI,CAAC,GAAG,EAEjDG,GACFD,EAAQ,KAAK,QAASC,CAAS,CAEnC,MACED,EAAUS,EAAmBhB,EAAUI,EAAGC,EAAGC,CAAM,EAGrD,OAAIR,GACFS,EAAQ,KAAK,QAAST,CAAU,EAGlCF,EAAK,MAAQQ,EACbR,EAAK,OAASS,EAEdY,EAAiBrB,EAAMW,CAAO,EAE9BX,EAAK,UAAY,SAAUsB,EAAO,CAChC,OAAOC,EAAU,QAAQvB,EAAMU,EAAQY,CAAK,CAC9C,EAEOlB,CACT,CAjDsBoB,EAAA1B,GAAA,aChBtB,eAAsB2B,GACpBC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAAYP,EAAQC,EAAMO,EAAeP,CAAI,CAAC,EACzEQ,EAAW,GACfC,EAAY,GACRC,EAAI,KAAK,IAAIF,EAAUH,EAAK,OAASL,EAAK,SAAW,GAAK,EAAGA,GAAM,OAAS,CAAC,EAC7EW,EAAI,KAAK,IAAIF,EAAWJ,EAAK,QAAUL,EAAK,SAAW,GAAK,EAAGA,GAAM,QAAU,CAAC,EAEhF,CAAE,UAAAY,CAAU,EAAIZ,EAEhBa,EAAKC,EAAM,IAAIV,CAAQ,EACvBW,EAAUC,EAAkBhB,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChBe,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAS,CACb,CAAE,EAAI,CAACP,EAAI,EAAK,GAAK,EAAG,CAACC,EAAI,CAAE,EAC/B,CAAE,EAAID,EAAI,EAAK,GAAK,EAAG,CAACC,EAAI,CAAE,EAC9B,CAAE,EAAGD,EAAI,EAAG,EAAI,CAACC,EAAI,EAAK,EAAI,EAC9B,CAAE,EAAGD,EAAI,EAAG,EAAGC,EAAI,CAAE,EACrB,CAAE,EAAG,CAACD,EAAI,EAAG,EAAGC,EAAI,CAAE,EACtB,CAAE,EAAG,CAACD,EAAI,EAAG,EAAI,CAACC,EAAI,EAAK,EAAI,CACjC,EAEMO,EAAWC,EAAqBF,CAAM,EACtCG,EAAYP,EAAG,KAAKK,EAAUH,CAAO,EAErCM,EAAUjB,EAAS,OAAO,IAAMgB,EAAW,cAAc,EAC/D,OAAAC,EAAQ,KAAK,QAAS,uBAAuB,EAEzCT,GAAaZ,EAAK,OAAS,aAC7BqB,EAAQ,eAAe,MAAM,EAAE,KAAK,QAAST,CAAS,EAGpDV,GAAcF,EAAK,OAAS,aAC9BqB,EAAQ,eAAe,MAAM,EAAE,KAAK,QAASnB,CAAU,EAGzDoB,EAAiBtB,EAAMqB,CAAO,EAE9BrB,EAAK,UAAY,SAAUuB,EAAO,CAEhC,OADYC,EAAU,QAAQxB,EAAMiB,EAAQM,CAAK,CAEnD,EAEOnB,CACT,CArDsBqB,EAAA3B,GAAA,uBCItB,eAAsB4B,GAAuCC,EAAwBC,EAAY,CAC/F,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAAI,MAAMC,EAAYR,EAAQC,EAAMQ,EAAeR,CAAI,CAAC,EAChFS,EAAgBC,EAASC,EAAU,EAAE,WAAW,UAAU,EAE1DC,EAAIP,EAAK,OAASL,EAAK,SAAW,GAClCa,EAAID,EAAIP,EAAK,OAEbS,EAAKF,EAAIP,EAAK,OACdU,EAAS,CACb,CAAE,EAAG,EAAG,EAAG,CAAE,EACb,CAAE,EAAGD,EAAI,EAAG,CAAE,EACd,CAAE,EAAGA,EAAK,EAAG,EAAG,CAACD,CAAE,CACrB,EAEM,CAAE,UAAAG,CAAU,EAAIhB,EAGhBiB,EAAKC,EAAM,IAAId,CAAQ,EACvBe,EAAUC,EAAkBpB,EAAM,CAAC,CAAC,EACtCA,EAAK,OAAS,cAChBmB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAEtB,IAAME,EAAWC,EAAqBP,CAAM,EACtCQ,EAAYN,EAAG,KAAKI,EAAUF,CAAO,EAErCK,EAAUpB,EACb,OAAO,IAAMmB,EAAW,cAAc,EACtC,KAAK,YAAa,aAAa,CAACV,EAAI,CAAC,KAAKA,EAAI,CAAC,GAAG,EAErD,OAAIG,GAAahB,EAAK,OAAS,aAC7BwB,EAAQ,eAAe,MAAM,EAAE,KAAK,QAASR,CAAS,EAGpDd,GAAcF,EAAK,OAAS,aAC9BwB,EAAQ,eAAe,MAAM,EAAE,KAAK,QAAStB,CAAU,EAGzDF,EAAK,MAAQY,EACbZ,EAAK,OAASa,EAEdY,EAAiBzB,EAAMwB,CAAO,EAE9BlB,EAAM,KACJ,YACA,aAAa,CAACD,EAAK,MAAQ,GAAKA,EAAK,GAAKA,EAAK,MAAQ,GAAG,KAAKQ,EAAI,GAAKR,EAAK,QAAUL,EAAK,SAAW,IAAMS,EAAgB,EAAI,IAAMJ,EAAK,GAAKA,EAAK,KAAO,IAAI,GACnK,EAEAL,EAAK,UAAY,SAAU0B,EAAO,CAChC,OAAAC,EAAI,KAAK,qBAAsB3B,EAAMe,EAAQW,CAAK,EAC3CE,EAAU,QAAQ5B,EAAMe,EAAQW,CAAK,CAC9C,EAEOtB,CACT,CAxDsByB,EAAA/B,GAAA,YCEtB,eAAsBgC,GACpBC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAAI,MAAMC,EAAYR,EAAQC,EAAMQ,EAAeR,CAAI,CAAC,EAChFS,EAAI,KAAK,IAAIJ,EAAK,OAASL,EAAK,SAAW,GAAK,EAAGA,GAAM,OAAS,CAAC,EACnEU,EAAI,KAAK,IAAIL,EAAK,QAAUL,EAAK,SAAW,GAAK,EAAGA,GAAM,QAAU,CAAC,EACrEW,EAAgBD,EAAI,EACpBE,EAASF,EAAIC,EACb,CAAE,UAAAE,CAAU,EAAIb,EAIhBc,EADW,GACWL,EACtBM,EAASD,EAAW,EAAIA,EAAW,EAAI,EAGvCE,EAAKC,EAAM,IAAIb,CAAQ,EACvBc,EAAUC,EAAkBnB,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChBkB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAS,CACb,CAAE,EAAG,CAACX,EAAI,EAAIM,EAAQ,EAAGH,EAAS,CAAE,EACpC,GAAGS,GACD,CAACZ,EAAI,EAAIM,EACTH,EAAS,EACTH,EAAI,EAAIM,EACRH,EAAS,EACTD,EACA,EACF,EACA,CAAE,EAAGF,EAAI,EAAIM,EAAQ,EAAG,CAACH,EAAS,CAAE,EACpC,CAAE,EAAG,CAACH,EAAI,EAAIM,EAAQ,EAAG,CAACH,EAAS,CAAE,CACvC,EAEMU,EAAmBC,EAAqBH,CAAM,EAC9CI,EAAmBR,EAAG,KAAKM,EAAkBJ,CAAO,EAEpDO,EAAerB,EAAS,OAAO,IAAMoB,EAAkB,cAAc,EAE3E,OAAAC,EAAa,KAAK,QAAS,uBAAuB,EAE9CZ,GAAab,EAAK,OAAS,aAC7ByB,EAAa,UAAU,MAAM,EAAE,KAAK,QAASZ,CAAS,EAGpDX,GAAcF,EAAK,OAAS,aAC9ByB,EAAa,UAAU,MAAM,EAAE,KAAK,QAASvB,CAAU,EAGzDuB,EAAa,KAAK,YAAa,eAAe,CAACd,EAAgB,CAAC,GAAG,EACnEL,EAAM,KACJ,YACA,aAAa,CAACG,EAAI,GAAKT,EAAK,SAAW,IAAMK,EAAK,GAAKA,EAAK,MAAQ,GAAG,IAAI,CAACK,EAAI,GAAKV,EAAK,SAAW,GAAKW,GAAiBN,EAAK,GAAKA,EAAK,KAAO,GAAG,GACtJ,EAEAqB,EAAiB1B,EAAMyB,CAAY,EACnCzB,EAAK,UAAY,SAAU2B,EAAO,CAEhC,OADYC,EAAU,QAAQ5B,EAAMoB,EAAQO,CAAK,CAEnD,EAEOvB,CACT,CArEsByB,EAAA/B,GAAA,sBCAtB,eAAsBgC,GACpBC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,CAAK,EAAI,MAAMC,EAAYP,EAAQC,EAAMO,EAAeP,CAAI,CAAC,EAEzEQ,EAAW,IACXC,EAAY,GAEZC,EAAY,KAAK,IAAIL,EAAK,OAASL,EAAK,SAAW,GAAK,EAAGA,GAAM,OAAS,CAAC,EAC3EW,EAAa,KAAK,IAAIN,EAAK,QAAUL,EAAK,SAAW,GAAK,EAAGA,GAAM,QAAU,CAAC,EAE9EY,EAAcF,EAAYC,EAE5BE,EAAIH,EACJI,EAAIH,EAEJE,EAAIC,EAAIF,EACVE,EAAID,EAAID,EAERC,EAAIC,EAAIF,EAGVC,EAAI,KAAK,IAAIA,EAAGL,CAAQ,EACxBM,EAAI,KAAK,IAAIA,EAAGL,CAAS,EAEzB,IAAMM,EAAgB,KAAK,IAAID,EAAI,GAAKA,EAAI,CAAC,EACvCE,EAASF,EAAIC,EAAgB,EAC7B,CAAE,UAAAE,CAAU,EAAIjB,EAGhBkB,EAAKC,EAAM,IAAIf,CAAQ,EACvBgB,EAAUC,EAAkBrB,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChBoB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAS,CACb,CAAE,EAAG,CAACT,EAAI,EAAG,EAAGG,EAAS,CAAE,EAC3B,GAAGO,GAA2B,CAACV,EAAI,EAAGG,EAAS,EAAGH,EAAI,EAAGG,EAAS,EAAGD,EAAe,CAAC,EACrF,CAAE,EAAGF,EAAI,EAAG,EAAG,CAACG,EAAS,CAAE,EAC3B,GAAGO,GAA2BV,EAAI,EAAG,CAACG,EAAS,EAAG,CAACH,EAAI,EAAG,CAACG,EAAS,EAAGD,EAAe,EAAE,CAC1F,EAEMS,EAAeC,EAAqBH,CAAM,EAC1CI,EAAeR,EAAG,KAAKM,EAAcJ,CAAO,EAE5CO,EAAWvB,EAAS,OAAO,IAAMsB,EAAc,cAAc,EAEnE,OAAAC,EAAS,KAAK,QAAS,uBAAuB,EAE1CV,GAAajB,EAAK,OAAS,aAC7B2B,EAAS,UAAU,MAAM,EAAE,KAAK,QAASV,CAAS,EAGhDf,GAAcF,EAAK,OAAS,aAC9B2B,EAAS,UAAU,MAAM,EAAE,KAAK,QAASzB,CAAU,EAGrD0B,EAAiB5B,EAAM2B,CAAQ,EAC/B3B,EAAK,UAAY,SAAU6B,EAAO,CAEhC,OADYC,EAAU,QAAQ9B,EAAMsB,EAAQO,CAAK,CAEnD,EAEOzB,CACT,CAtEsB2B,EAAAjC,GAAA,iBCNtB,eAAsBkC,GAAyCC,EAAwBC,EAAY,CACjG,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,GAAM,CAAE,SAAAG,EAAU,KAAAC,EAAM,MAAAC,CAAM,EAAI,MAAMC,EAAYR,EAAQC,EAAMQ,EAAeR,CAAI,CAAC,EAChFS,EAAI,KAAK,IAAIJ,EAAK,OAASL,EAAK,SAAW,GAAK,EAAGA,GAAM,OAAS,CAAC,EACnEU,EAAI,KAAK,IAAIL,EAAK,QAAUL,EAAK,SAAW,GAAK,EAAGA,GAAM,QAAU,CAAC,EACrEW,EAAa,EACbC,EAAI,CAACH,EAAI,EACTI,EAAI,CAACH,EAAI,EACT,CAAE,UAAAI,CAAU,EAAId,EAGhBe,EAAKC,EAAM,IAAIZ,CAAQ,EACvBa,EAAUC,EAAkBlB,EAAM,CAAC,CAAC,EAEpCmB,EAAkB,CACtB,CAAE,EAAGP,EAAID,EAAY,EAAGE,EAAIF,CAAW,EACvC,CAAE,EAAGC,EAAID,EAAY,EAAGE,EAAIH,CAAE,EAC9B,CAAE,EAAGE,EAAIH,EAAG,EAAGI,EAAIH,CAAE,EACrB,CAAE,EAAGE,EAAIH,EAAG,EAAGI,EAAIF,CAAW,CAChC,EAEMS,EAAO,IAAIR,EAAID,CAAU,IAAIE,EAAIF,CAAU,KAAKC,EAAIH,CAAC,IAAII,EAAIF,CAAU,KAAKC,EAAIH,CAAC,IAAII,EAAIH,CAAC,KAAKE,EAAID,CAAU,IAAIE,EAAIH,CAAC,KAAKE,EAAID,CAAU,IAAIE,EAAIF,CAAU;AAAA,mBAChJC,EAAID,CAAU,IAAIE,CAAC,KAAKD,EAAIH,CAAC,IAAII,CAAC;AAAA,mBAClCD,CAAC,IAAIC,EAAIF,CAAU,KAAKC,CAAC,IAAIC,EAAIH,CAAC,GAE/CV,EAAK,OAAS,cAChBiB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAMI,EAAKN,EAAG,KAAKK,EAAMH,CAAO,EAE1BnB,EAAaM,EAAS,OAAO,IAAMiB,EAAI,cAAc,EAC3D,OAAAvB,EAAW,KAAK,YAAa,aAAaa,EAAa,CAAC,KAAKA,EAAa,CAAC,GAAG,EAE9Eb,EAAW,KAAK,QAAS,uBAAuB,EAE5CgB,GAAad,EAAK,OAAS,aAC7BF,EAAW,UAAU,MAAM,EAAE,KAAK,QAASgB,CAAS,EAGlDZ,GAAcF,EAAK,OAAS,aAC9BF,EAAW,UAAU,MAAM,EAAE,KAAK,QAASI,CAAU,EAGvDI,EAAM,KACJ,YACA,aAAa,EAAED,EAAK,MAAQ,GAAKM,EAAa,GAAKN,EAAK,GAAKA,EAAK,MAAQ,GAAG,KAAK,EAAEA,EAAK,OAAS,GAAKM,EAAa,GAAKN,EAAK,GAAKA,EAAK,KAAO,GAAG,GACpJ,EAEAiB,EAAiBtB,EAAMF,CAAU,EAEjCE,EAAK,UAAY,SAAUuB,EAAO,CAEhC,OADYC,EAAU,QAAQxB,EAAMmB,EAAiBI,CAAK,CAE5D,EAEOnB,CACT,CA3DsBqB,EAAA3B,GAAA,cCQtB,eAAsB4B,GAAoCC,EAAwBC,EAAY,CAE5F,IAAMC,EAAaD,EAOnB,GANIC,EAAW,QACbD,EAAK,MAAQC,EAAW,OAKtBD,EAAK,OAAS,YAAa,CAC7B,GAAM,CAAE,eAAAE,CAAe,EAAIC,GAAU,EAC/B,CAAE,WAAAC,CAAW,EAAIF,EACjBG,GAAiB,CACrB,GAAGL,EACH,GAAIA,EAAK,GAAK,cACd,KAAM,UACN,UAAW,CAAC,eAAgB,SAASI,CAAU,EAAE,CACnD,EACA,MAAMN,GAAMC,EAAQM,EAAc,CACpC,CAEA,IAAMC,EAASH,GAAU,EACzBH,EAAK,cAAgBM,EAAO,WAC5B,IAAIC,EAAUD,EAAO,IAAI,gBAAkB,GACvCE,EAAeF,EAAO,IAAI,eAAiB,EAEzC,CAAE,UAAAG,CAAU,EAAIT,EAChB,CAAE,YAAAU,CAAY,EAAIC,EAAcX,CAAI,EAG1C,GAAIC,EAAW,WAAW,SAAW,GAAKD,EAAK,MAAO,CACpD,IAAMY,EAAU,CACd,GAAI,EACJ,GAAI,EACJ,cAAeL,EACf,cAAeA,EAAU,IACzB,QAAS,EACX,EAGEM,GAAmBb,EAAK,MAAOM,CAAM,EAAIM,EAAQ,cAAgB,EACjEN,EAAO,GAAI,iBAEXN,EAAK,MAAQM,EAAO,GAAI,gBAE1B,IAAMQ,EAAW,MAAMC,GAAShB,EAAQC,EAAMY,CAAO,EAGrD,GAAI,CAACI,EAASV,EAAO,UAAU,EAAG,CAChC,IAAMW,GAAcH,EAAS,OAAO,MAAM,EACpCI,GAAQD,GAAY,KAAK,GAAsB,QAAQ,EAC7DA,GAAY,KAAK,YAAa,aAAa,CAACC,GAAK,MAAQ,CAAC,MAAM,CAClE,CACA,OAAOJ,CACT,CAEKR,EAAO,aACVC,GAAW,KACXC,GAAgB,MAGlB,IAAIW,EAAaC,EAAepB,CAAI,EAC/BmB,IACHA,EAAa,gBAGf,IAAML,EAAWf,EAEd,OAAO,GAAG,EACV,KAAK,QAASoB,CAAU,EACxB,KAAK,KAAMnB,EAAK,OAASA,EAAK,EAAE,EAE7BqB,EAAW,MAAMC,GAAQR,EAAUd,EAAK,OAAS,GAAIM,EAAQ,EAAG,EAAG,CAAC,MAAM,EAAGI,CAAW,EAC9FW,EAAS,QAAUb,EACnB,IAAIe,EAAU,EACRC,EAAW,CAAC,EACdC,EAAe,EACfC,EAAe,EACfC,EAAe,EACfC,EAAkB,EAClBC,EAAc,GACdC,EAAiB,GACrB,QAAWC,KAAa9B,EAAW,WAAY,CAC7C,IAAM+B,EAAW,MAAMV,GACrBR,EACAiB,EAAU,KACVzB,EACA,EACAiB,EACA,CAAC,gBAAgB,EACjBb,CACF,EACAe,EAAe,KAAK,IAAIA,EAAcO,EAAS,MAAQzB,CAAO,EAC9D,IAAMc,GAAW,MAAMC,GACrBR,EACAiB,EAAU,KACVzB,EACA,EACAiB,EACA,CAAC,gBAAgB,EACjBb,CACF,EACAgB,EAAe,KAAK,IAAIA,EAAcL,GAAS,MAAQd,CAAO,EAC9D,IAAM0B,GAAW,MAAMX,GACrBR,EACAiB,EAAU,KAAK,KAAK,EACpBzB,EACA,EACAiB,EACA,CAAC,gBAAgB,EACjBb,CACF,EACAiB,EAAe,KAAK,IAAIA,EAAcM,GAAS,MAAQ1B,CAAO,EAC9D,IAAM2B,GAAc,MAAMZ,GACxBR,EACAiB,EAAU,QACVzB,EACA,EACAiB,EACA,CAAC,mBAAmB,EACpBb,CACF,EACAkB,EAAkB,KAAK,IAAIA,EAAiBM,GAAY,MAAQ3B,CAAO,EAEvEgB,GACE,KAAK,IAAIS,EAAS,OAAQX,GAAS,OAAQY,GAAS,OAAQC,GAAY,MAAM,EAC9E1B,EACFgB,EAAS,KAAKD,CAAO,CACvB,CACAC,EAAS,IAAI,EACb,IAAIW,EAAqB,EAErBR,GAAgBpB,IAClBsB,EAAc,GACdF,EAAe,EACfQ,KAEEP,GAAmBrB,IACrBuB,EAAiB,GACjBF,EAAkB,EAClBO,KAGF,IAAMC,EAAYtB,EAAS,KAAK,EAAG,QAAQ,EAE3C,GACEO,EAAS,MAAQd,EAAU,GAAKkB,EAAeC,EAAeC,EAAeC,GAC7E,EACA,CACA,IAAMS,EACJhB,EAAS,MAAQd,EAAU,GAAKkB,EAAeC,EAAeC,EAAeC,GAC/EH,GAAgBY,EAAaF,EAC7BT,GAAgBW,EAAaF,EACzBR,EAAe,IACjBA,GAAgBU,EAAaF,GAE3BP,EAAkB,IACpBA,GAAmBS,EAAaF,EAEpC,CAEA,IAAMG,EAAWb,EAAeC,EAAeC,EAAeC,EAGxDW,EAAKC,EAAM,IAAI1B,CAAQ,EACvBF,EAAU6B,EAAkBzC,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChBY,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAM8B,EAAI,KAAK,IAAIN,EAAU,MAAQ7B,EAAU,EAAGP,GAAM,OAAS,EAAGsC,CAAQ,EACtEK,EAAI,KAAK,IAAIP,EAAU,QAAUZ,EAAS,CAAC,GAAKD,GAAWf,EAAcR,GAAM,QAAU,CAAC,EAC1F4C,EAAI,CAACF,EAAI,EACTG,EAAI,CAACF,EAAI,EAGf7B,EAAS,UAAU,qBAAqB,EAAE,KAAK,CAACgC,EAAQC,EAAWC,KAAe,CAChF,IAAMC,GAAOC,EAAqBF,GAAMD,CAAC,CAAC,EACpCI,GAAYF,GAAK,KAAK,WAAW,EACnCG,GAAa,EACbC,GAAa,EAEjB,GAAIF,GAAW,CAEb,IAAMG,GADQ,OAAO,8BAA8B,EAC3B,KAAKH,EAAS,EAClCG,KACFF,GAAa,WAAWE,GAAU,CAAC,CAAC,EACpCD,GAAa,WAAWC,GAAU,CAAC,CAAC,EAChCL,GAAK,KAAK,OAAO,EAAE,SAAS,gBAAgB,EAC9CG,IAAc3B,EACLwB,GAAK,KAAK,OAAO,EAAE,SAAS,gBAAgB,EACrDG,IAAc3B,EAAeC,EACpBuB,GAAK,KAAK,OAAO,EAAE,SAAS,mBAAmB,IACxDG,IAAc3B,EAAeC,EAAeC,GAGlD,CAEAsB,GAAK,KACH,YACA,aAAaL,EAAIrC,EAAU,EAAI6C,EAAU,KAAKC,GAAaR,EAAIxB,EAAS,OAASb,EAAe,CAAC,GACnG,CACF,CAAC,EAEDM,EACG,OAAO,OAAO,EACd,KAAK,YAAa,aAAe,CAACO,EAAS,MAAQ,EAAI,MAAQwB,EAAIrC,EAAe,GAAK,GAAG,EAG7F,IAAM+C,EAAYhB,EAAG,UAAUK,EAAGC,EAAGH,EAAGC,EAAG/B,CAAO,EAC5C4C,EAAO1C,EAAS,OAAO,IAAMyC,EAAW,cAAc,EAAE,KAAK,QAAS9C,EAAW,KAAK,EAAE,CAAC,EAEzF,CAAE,eAAAP,CAAe,EAAIC,GAAU,EAC/B,CAAE,QAAAsD,EAAS,OAAAC,EAAQ,WAAAC,CAAW,EAAIzD,EAExCsB,EAAS,KAAK,CAAC,EAEf,OAAW,CAACuB,EAAGxB,CAAO,IAAKC,EAAS,QAAQ,EAAG,CAC7C,GAAIuB,IAAM,GAAKvB,EAAS,OAAS,EAC/B,SAGF,IAAMoC,GAASb,EAAI,IAAM,GAAKxB,IAAY,EACpCgC,GAAYhB,EAAG,UAAUK,EAAGvB,EAAS,OAASwB,EAAItB,EAASmB,EAAGrB,EAAS,OAAQ,CACnF,GAAGT,EACH,KAAMgD,GAASH,EAAUC,EACzB,OAAQC,CACV,CAAC,EACD7C,EACG,OAAO,IAAMyC,GAAW,SAAS,EACjC,KAAK,QAAS9C,EAAW,KAAK,EAAE,CAAC,EACjC,KAAK,QAAS,YAAYsC,EAAI,IAAM,EAAI,OAAS,KAAK,EAAE,CAC7D,CAIA,IAAIc,EAAYtB,EAAG,KAAKK,EAAGvB,EAAS,OAASwB,EAAGH,EAAIE,EAAGvB,EAAS,OAASwB,EAAGjC,CAAO,EACnFE,EAAS,OAAO,IAAM+C,CAAS,EAAE,KAAK,QAAS,SAAS,EAExDA,EAAYtB,EAAG,KAAKd,EAAemB,EAAGvB,EAAS,OAASwB,EAAGpB,EAAemB,EAAGD,EAAIE,EAAGjC,CAAO,EAC3FE,EAAS,OAAO,IAAM+C,CAAS,EAAE,KAAK,QAAS,SAAS,EAEpDhC,IACFgC,EAAYtB,EAAG,KACbd,EAAeC,EAAekB,EAC9BvB,EAAS,OAASwB,EAClBpB,EAAeC,EAAekB,EAC9BD,EAAIE,EACJjC,CACF,EACAE,EAAS,OAAO,IAAM+C,CAAS,EAAE,KAAK,QAAS,SAAS,GAGtD/B,IACF+B,EAAYtB,EAAG,KACbd,EAAeC,EAAeC,EAAeiB,EAC7CvB,EAAS,OAASwB,EAClBpB,EAAeC,EAAeC,EAAeiB,EAC7CD,EAAIE,EACJjC,CACF,EACAE,EAAS,OAAO,IAAM+C,CAAS,EAAE,KAAK,QAAS,SAAS,GAI1D,QAAWtC,KAAWC,EACpBqC,EAAYtB,EAAG,KACbK,EACAvB,EAAS,OAASwB,EAAItB,EACtBmB,EAAIE,EACJvB,EAAS,OAASwB,EAAItB,EACtBX,CACF,EACAE,EAAS,OAAO,IAAM+C,CAAS,EAAE,KAAK,QAAS,SAAS,EAG1D,OAAAC,EAAiB9D,EAAMwD,CAAI,EAE3BxD,EAAK,UAAY,SAAU+D,EAAO,CAChC,OAAOC,EAAU,KAAKhE,EAAM+D,CAAK,CACnC,EACOjD,CACT,CA5RsBmD,EAAAnE,GAAA,SA+RtB,eAAewB,GACbR,EACAoD,EACA5D,EACA8C,EAAa,EACbC,EAAa,EACbc,EAAoB,CAAC,EACrBC,EAAQ,GACR,CACA,IAAMC,EAAQvD,EACX,OAAO,GAAG,EACV,KAAK,QAAS,SAASqD,EAAQ,KAAK,GAAG,CAAC,EAAE,EAC1C,KAAK,YAAa,aAAaf,CAAU,KAAKC,CAAU,GAAG,EAC3D,KAAK,QAASe,CAAK,EAGlBF,IAAcI,GAAkBJ,CAAS,IAC3CA,EAAYI,GAAkBJ,CAAS,EAEvCA,EAAYA,EAAU,WAAW,IAAK,MAAM,EAAE,WAAW,IAAK,MAAM,GAGtE,IAAMjB,EAAOoB,EAAM,KAAK,EAAG,YACzB,MAAME,GACJF,EACAH,EACA,CACE,MAAOrD,GAAmBqD,EAAW5D,CAAM,EAAI,IAC/C,MAAA8D,EACA,cAAe9D,EAAO,UACxB,EACAA,CACF,CACF,EAEA,GAAI4D,EAAU,SAAS,MAAM,GAAKA,EAAU,SAAS,MAAM,EAAG,CAC5D,IAAIM,EAAQvB,EAAK,SAAS,CAAC,EAE3B,IADAuB,EAAM,YAAcA,EAAM,YAAY,WAAW,OAAQ,GAAG,EAAE,WAAW,OAAQ,GAAG,EAC7EA,EAAM,WAAW,CAAC,GACvBA,EAAQA,EAAM,WAAW,CAAC,EAE1BA,EAAM,YAAcA,EAAM,YAAY,WAAW,OAAQ,GAAG,EAAE,WAAW,OAAQ,GAAG,CAExF,CAEA,IAAItD,EAAO+B,EAAK,QAAQ,EACxB,GAAIjC,EAASV,EAAO,UAAU,EAAG,CAC/B,IAAMmE,EAAMxB,EAAK,SAAS,CAAC,EAC3BwB,EAAI,MAAM,UAAY,QACtB,IAAMC,EAAKxB,EAAOD,CAAI,EACtB/B,EAAOuD,EAAI,sBAAsB,EACjCC,EAAG,KAAK,QAASxD,EAAK,KAAK,EAC3BwD,EAAG,KAAK,SAAUxD,EAAK,MAAM,CAC/B,CAEA,OAAOA,CACT,CAxDe+C,EAAA3C,GAAA,WCjSf,eAAsBqD,GACpBC,EACAC,EACAC,EACAC,EACAC,EAAMF,EAAO,MAAO,SAAW,GAC/B,CACA,IAAMG,EAAgBF,EAAoB,EAAJ,EAChCG,EAAWN,EAEd,OAAO,GAAG,EACV,KAAK,QAASO,EAAeN,CAAI,CAAC,EAClC,KAAK,KAAMA,EAAK,OAASA,EAAK,EAAE,EAE/BO,EAAkB,KAClBC,EAAa,KACbC,EAAe,KACfC,EAAe,KAEfC,EAAwB,EACxBC,EAAmB,EACnBC,EAAqB,EAGzB,GADAN,EAAkBF,EAAS,OAAO,GAAG,EAAE,KAAK,QAAS,uBAAuB,EACxEL,EAAK,YAAY,OAAS,EAAG,CAC/B,IAAMc,EAAad,EAAK,YAAY,CAAC,EACrC,MAAMe,GAAQR,EAAiB,CAAE,KAAM,OAAIO,CAAU,MAAI,EAA6B,CAAC,EAGvFH,EAD4BJ,EAAgB,KAAK,EAAG,QAAQ,EAChB,MAC9C,CAEAC,EAAaH,EAAS,OAAO,GAAG,EAAE,KAAK,QAAS,kBAAkB,EAClE,MAAMU,GAAQP,EAAYR,EAAM,EAAG,CAAC,qBAAqB,CAAC,EAC1D,IAAMgB,EAAiBR,EAAW,KAAK,EAAG,QAAQ,EAClDI,EAAmBI,EAAe,OAElCP,EAAeJ,EAAS,OAAO,GAAG,EAAE,KAAK,QAAS,oBAAoB,EACtE,IAAIY,EAAU,EACd,QAAWC,KAAUlB,EAAK,QAAS,CACjC,IAAMmB,EAAS,MAAMJ,GAAQN,EAAcS,EAAQD,EAAS,CAACC,EAAO,gBAAgB,CAAC,CAAC,EACtFD,GAAWE,EAASf,CACtB,CACAS,EAAqBJ,EAAa,KAAK,EAAG,QAAQ,EAAE,OAChDI,GAAsB,IACxBA,EAAqBV,EAAM,GAG7BO,EAAeL,EAAS,OAAO,GAAG,EAAE,KAAK,QAAS,oBAAoB,EACtE,IAAIe,EAAiB,EACrB,QAAWC,KAAUrB,EAAK,QAAS,CACjC,IAAMmB,EAAS,MAAMJ,GAAQL,EAAcW,EAAQD,EAAgB,CAACC,EAAO,gBAAgB,CAAC,CAAC,EAC7FD,GAAkBD,EAASf,CAC7B,CAEA,IAAIkB,EAAOjB,EAAS,KAAK,EAAG,QAAQ,EAGpC,GAAIE,IAAoB,KAAM,CAC5B,IAAMgB,EAAsBhB,EAAgB,KAAK,EAAG,QAAQ,EAC5DA,EAAgB,KAAK,YAAa,aAAa,CAACgB,EAAoB,MAAQ,CAAC,GAAG,CAClF,CAGA,OAAAf,EAAW,KAAK,YAAa,aAAa,CAACQ,EAAe,MAAQ,CAAC,KAAKL,CAAqB,GAAG,EAEhGW,EAAOjB,EAAS,KAAK,EAAG,QAAQ,EAEhCI,EAAa,KACX,YACA,gBAAmBE,EAAwBC,EAAmBT,EAAM,CAAC,GACvE,EACAmB,EAAOjB,EAAS,KAAK,EAAG,QAAQ,EAChCK,EAAa,KACX,YACA,gBAAmBC,EAAwBC,GAAoBC,EAAqBA,EAAqBV,EAAM,EAAIA,EAAM,EAAE,GAC7H,EAEAmB,EAAOjB,EAAS,KAAK,EAAG,QAAQ,EAEzB,CAAE,SAAAA,EAAU,KAAAiB,CAAK,CAC1B,CAjFsBE,EAAA1B,GAAA,cAoFtB,eAAeiB,GACbU,EACAzB,EACAiB,EACAS,EAAmB,CAAC,EACpB,CACA,IAAMC,EAASF,EAAY,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EAAE,KAAK,QAASC,EAAO,KAAK,IAAI,CAAC,EACvFzB,EAAS2B,GAAU,EACrB1B,EACF,kBAAmBF,EAAOA,EAAK,cAAiB6B,EAAS5B,EAAO,UAAU,GAAK,GAE7E6B,EAAc,GAEd,SAAU9B,EACZ8B,EAAc9B,EAAK,KAEnB8B,EAAc9B,EAAK,MAKjB,CAACE,GAAiB4B,EAAY,WAAW,IAAI,IAC/CA,EAAcA,EAAY,UAAU,CAAC,GAGnCC,GAASD,CAAW,IACtB5B,EAAgB,IAGlB,IAAM8B,EAAO,MAAMC,GACjBN,EACAO,GAAaC,GAAeL,CAAW,CAAC,EACxC,CACE,MAAOM,GAAmBN,EAAa7B,CAAM,EAAI,GACjD,QAAS,sBACT,cAAAC,CACF,EACAD,CACF,EACIqB,EACAe,EAAgB,EAEpB,GAAKnC,EA4BE,CACL,IAAMoC,EAAMN,EAAK,SAAS,CAAC,EACrBO,EAAKC,EAAOR,CAAI,EAEtBK,EAAgBC,EAAI,UAAU,MAAM,MAAM,EAAE,OAExCA,EAAI,UAAU,SAAS,SAAS,IAClCD,GAAiBC,EAAI,UAAU,MAAM,QAAQ,EAAE,OAAS,GAI1D,IAAMG,EAASH,EAAI,qBAAqB,KAAK,EAC7C,GAAIG,EAAQ,CACV,IAAMC,EAAYZ,EAAY,QAAQ,cAAe,EAAE,EAAE,KAAK,IAAM,GACpE,MAAM,QAAQ,IACZ,CAAC,GAAGW,CAAM,EAAE,IACTE,GACC,IAAI,QAASC,GAAQ,CACnB,SAASC,GAAa,CAIpB,GAHAF,EAAI,MAAM,QAAU,OACpBA,EAAI,MAAM,cAAgB,SAEtBD,EAAW,CAEb,IAAMI,EACJ7C,EAAO,UAAU,SAAS,GAAK,OAAO,iBAAiB,SAAS,IAAI,EAAE,SAElE8C,EAAQ,SAASD,EAAc,EAAE,EADf,EACqC,KAC7DH,EAAI,MAAM,SAAWI,EACrBJ,EAAI,MAAM,SAAWI,CACvB,MACEJ,EAAI,MAAM,MAAQ,OAEpBC,EAAID,CAAG,CACT,CAhBSnB,EAAAqB,EAAA,cAiBT,WAAW,IAAM,CACXF,EAAI,UACNE,EAAW,CAEf,CAAC,EACDF,EAAI,iBAAiB,QAASE,CAAU,EACxCF,EAAI,iBAAiB,OAAQE,CAAU,CACzC,CAAC,CACL,CACF,CACF,CAEAvB,EAAOgB,EAAI,sBAAsB,EACjCC,EAAG,KAAK,QAASjB,EAAK,KAAK,EAC3BiB,EAAG,KAAK,SAAUjB,EAAK,MAAM,CAC/B,KA9EoB,CAEdI,EAAO,SAAS,qBAAqB,GACvCc,EAAOR,CAAI,EAAE,UAAU,OAAO,EAAE,KAAK,cAAe,EAAE,EAGxDK,EAAgBL,EAAK,SAAS,OAE9B,IAAMgB,EAAYhB,EAAK,SAAS,CAAC,GAC7BA,EAAK,cAAgB,IAAMA,EAAK,YAAY,SAAS,KAAK,KAC5DgB,EAAU,YACRlB,EAAY,CAAC,EACbA,EAAY,UAAU,CAAC,EAAE,WAAW,OAAQ,GAAG,EAAE,WAAW,OAAQ,GAAG,EAAE,KAAK,EAG1DA,EAAY,CAAC,IAAM,MAEvCkB,EAAU,YAAcA,EAAU,YAAY,CAAC,EAAI,IAAMA,EAAU,YAAY,UAAU,CAAC,IAK1FA,EAAU,cAAgB,cAC5BA,EAAU,YAAc,IAI1B1B,EAAOU,EAAK,QAAQ,CACtB,CAqDA,OAAAL,EAAO,KAAK,YAAa,gBAAkB,CAACL,EAAK,QAAU,EAAIe,GAAiBpB,GAAW,GAAG,EACvFK,EAAK,MACd,CA7HeE,EAAAT,GAAA,WCrFf,eAAsBkC,GAAuCC,EAAwBC,EAAY,CAC/F,IAAMC,EAASC,EAAU,EACnBC,EAAUF,EAAO,MAAO,SAAW,GACnCG,EAAMD,EACNE,EAAgBL,EAAK,eAAiBM,EAASL,EAAO,UAAU,GAAK,GAErEM,EAAYP,EAClBO,EAAU,YAAcA,EAAU,aAAe,CAAC,EAClDA,EAAU,QAAUA,EAAU,SAAW,CAAC,EAC1CA,EAAU,QAAUA,EAAU,SAAW,CAAC,EAE1C,GAAM,CAAE,SAAAC,EAAU,KAAAC,CAAK,EAAI,MAAMC,GAAWX,EAAQC,EAAMC,EAAQI,EAAeD,CAAG,EAE9E,CAAE,YAAAO,EAAa,WAAAC,CAAW,EAAIC,EAAcb,CAAI,EACtDA,EAAK,WAAaW,EAElBX,EAAK,UAAYO,EAAU,QAAU,GAErC,IAAMO,EAASP,EAAU,QAAQ,KAAK,GAAG,GAAKK,GAAc,GAEvDZ,EAAK,YACRA,EAAK,UAAYc,EAAO,WAAW,aAAc,EAAE,EAAE,MAAM,GAAG,GAGhE,IAAMC,EACJR,EAAU,QAAQ,SAAW,GAC7BA,EAAU,QAAQ,SAAW,GAC7B,CAACN,EAAO,OAAO,oBAIXe,EAAKC,EAAM,IAAIT,CAAQ,EACvBU,EAAUC,EAAkBnB,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChBkB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAGtB,IAAME,EAAIX,EAAK,MACXY,EAAIZ,EAAK,OACTF,EAAU,QAAQ,SAAW,GAAKA,EAAU,QAAQ,SAAW,EACjEc,GAAKjB,EACIG,EAAU,QAAQ,OAAS,GAAKA,EAAU,QAAQ,SAAW,IACtEc,GAAKjB,EAAM,GAEb,IAAM,EAAI,CAACgB,EAAI,EACTE,EAAI,CAACD,EAAI,EAGTE,EAAYP,EAAG,UACnB,EAAIb,EACJmB,EACEnB,GACCY,EACGZ,EACAI,EAAU,QAAQ,SAAW,GAAKA,EAAU,QAAQ,SAAW,EAC7D,CAACJ,EAAU,EACX,GACRiB,EAAI,EAAIjB,EACRkB,EACE,EAAIlB,GACHY,EACGZ,EAAU,EACVI,EAAU,QAAQ,SAAW,GAAKA,EAAU,QAAQ,SAAW,EAC7D,CAACJ,EACD,GACRe,CACF,EAEMM,EAAOhB,EAAS,OAAO,IAAMe,EAAW,cAAc,EAC5DC,EAAK,KAAK,QAAS,uBAAuB,EAC1C,IAAMC,EAAWD,EAAK,KAAK,EAAG,QAAQ,EAItChB,EAAS,UAAU,OAAO,EAAE,KAAK,CAACkB,EAAQC,EAAWC,IAAe,CAClE,IAAMC,EAAOC,EAAqBF,EAAMD,CAAC,CAAC,EAEpCI,EAAYF,EAAK,KAAK,WAAW,EAEnCG,EAAa,EAEjB,GAAID,EAAW,CAEb,IAAME,EADQ,OAAO,8BAA8B,EAC3B,KAAKF,CAAS,EAClCE,IACFD,EAAa,WAAWC,EAAU,CAAC,CAAC,EAExC,CAEA,IAAIC,EACFF,EACAV,EACAnB,GACCY,EACGZ,EACAI,EAAU,QAAQ,SAAW,GAAKA,EAAU,QAAQ,SAAW,EAC7D,CAACJ,EAAU,EACX,GACHE,IAGH6B,GAAiB,GAEnB,IAAIC,EAAgB,GAElBN,EAAK,KAAK,OAAO,EAAE,SAAS,aAAa,GACzCA,EAAK,KAAK,OAAO,EAAE,SAAS,kBAAkB,KAE9CM,EAAgB,CAACN,EAAK,KAAK,GAAG,QAAQ,EAAE,MAAQ,GAAK,EACrDrB,EAAS,UAAU,MAAM,EAAE,KAAK,SAAUkB,EAAQC,EAAWC,EAAY,CACnE,OAAO,iBAAiBA,EAAMD,CAAC,CAAC,EAAE,aAAe,WACnDQ,EAAgB,EAEpB,CAAC,GAGHN,EAAK,KAAK,YAAa,aAAaM,CAAa,KAAKD,CAAa,GAAG,CACxE,CAAC,EAGD,IAAME,EACH5B,EAAS,OAAO,mBAAmB,EAAE,KAAK,EAAyB,QAAQ,EAAE,QAC3EO,EAAiBZ,EAAU,EAAI,IAAM,EACpCkC,EACH7B,EAAS,OAAO,cAAc,EAAE,KAAK,EAAyB,QAAQ,EAAE,QACtEO,EAAiBZ,EAAU,EAAI,IAAM,EACpCmC,EACH9B,EAAS,OAAO,gBAAgB,EAAE,KAAK,EAAyB,QAAQ,EAAE,QACxEO,EAAiBZ,EAAU,EAAI,IAAM,EAE1C,GAAII,EAAU,QAAQ,OAAS,GAAKA,EAAU,QAAQ,OAAS,GAAKQ,EAAgB,CAClF,IAAMwB,EAAYvB,EAAG,KACnBS,EAAS,EACTW,EAAwBC,EAAmBf,EAAInB,EAC/CsB,EAAS,EAAIA,EAAS,MACtBW,EAAwBC,EAAmBf,EAAInB,EAC/Ce,CACF,EACaV,EAAS,OAAO,IAAM+B,CAAS,EACvC,KAAK,QAAS,SAAS,EAAE,KAAK,QAASzB,CAAM,CACpD,CAGA,GAAIC,GAAkBR,EAAU,QAAQ,OAAS,GAAKA,EAAU,QAAQ,OAAS,EAAG,CAClF,IAAMgC,EAAYvB,EAAG,KACnBS,EAAS,EACTW,EAAwBC,EAAmBC,EAAqBhB,EAAIlB,EAAM,EAAID,EAC9EsB,EAAS,EAAIA,EAAS,MACtBW,EAAwBC,EAAmBC,EAAqBhB,EAAInB,EAAUC,EAAM,EACpFc,CACF,EACaV,EAAS,OAAO,IAAM+B,CAAS,EACvC,KAAK,QAAS,SAAS,EAAE,KAAK,QAASzB,CAAM,CACpD,CAiBA,GAdIP,EAAU,OAAS,aACrBC,EAAS,UAAU,MAAM,EAAE,KAAK,QAASM,CAAM,EAGjDU,EAAK,OAAO,eAAe,EAAE,KAAK,QAASV,CAAM,EAEjDN,EAAS,UAAU,UAAU,EAAE,OAAO,MAAM,EAAE,KAAK,QAASM,CAAM,EAE9Dd,EAAK,WACPQ,EAAS,UAAU,MAAM,EAAE,KAAK,QAASR,EAAK,UAAU,EAExDQ,EAAS,UAAU,MAAM,EAAE,KAAK,QAASM,CAAM,EAG7C,CAACT,EAAe,CAElB,IAAMmC,EAAa,OAAO,qBAAqB,EACzCC,EAAQD,EAAW,KAAK1B,CAAM,EACpC,GAAI2B,EAAO,CACT,IAAMC,EAAaD,EAAM,CAAC,EAAE,QAAQ,QAAS,MAAM,EACnDjC,EAAS,UAAU,OAAO,EAAE,KAAK,QAASkC,CAAU,CACtD,SAAW/B,EAAa,CACtB,IAAM8B,EAAQD,EAAW,KAAK7B,CAAW,EACzC,GAAI8B,EAAO,CACT,IAAMC,EAAaD,EAAM,CAAC,EAAE,QAAQ,QAAS,MAAM,EACnDjC,EAAS,UAAU,OAAO,EAAE,KAAK,QAASkC,CAAU,CACtD,CACF,CACF,CAEA,OAAAC,EAAiB3C,EAAMwB,CAAI,EAC3BxB,EAAK,UAAY,SAAU4C,EAAO,CAChC,OAAOC,EAAU,KAAK7C,EAAM4C,CAAK,CACnC,EAEOpC,CACT,CAlMsBsC,EAAAhD,GAAA,YCAtB,eAAsBiD,GACpBC,EACAC,EACA,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcH,CAAI,EACtDA,EAAK,WAAaC,EAClB,IAAMG,EAAkBJ,EAClBK,EAAcL,EACdM,EAAU,GACVC,EAAM,GACNC,EAAoB,iBAAkBR,EACtCS,EAAUC,EAAeV,CAAI,EAG7BW,EAAWZ,EACd,OAAO,GAAG,EACV,KAAK,QAASU,CAAO,EACrB,KAAK,KAAMT,EAAK,OAASA,EAAK,EAAE,EAE/BY,EACAJ,EACFI,EAAa,MAAMC,GACjBF,EACA,WAAWP,EAAgB,IAAI,WAC/B,EACAJ,EAAK,UACP,EAEAY,EAAa,MAAMC,GAAQF,EAAU,0BAA2B,EAAGX,EAAK,UAAU,EAGpF,IAAIc,EAAqBF,EACnBG,EAAa,MAAMF,GACvBF,EACAP,EAAgB,KAChBU,EACAd,EAAK,WAAa,sBACpB,EAIA,GAHAc,GAAsBC,EAAaR,EAG/BC,EAAmB,CACrB,IAAMQ,EAAW,MAAMH,GACrBF,EACA,GAAGP,EAAgB,cAAgB,OAAOA,EAAgB,aAAa,GAAK,EAAE,GAC9EU,EACAd,EAAK,UACP,EAEAc,GAAsBE,EACtB,IAAMC,EAAa,MAAMJ,GACvBF,EACA,GAAGP,EAAgB,KAAO,SAASA,EAAgB,IAAI,GAAK,EAAE,GAC9DU,EACAd,EAAK,UACP,EACAc,GAAsBG,EACtB,IAAMC,EAAa,MAAML,GACvBF,EACA,GAAGP,EAAgB,KAAO,SAASA,EAAgB,IAAI,GAAK,EAAE,GAC9DU,EACAd,EAAK,UACP,EACAc,GAAsBI,EACtB,MAAML,GACJF,EACA,GAAGP,EAAgB,aAAe,iBAAiBA,EAAgB,YAAY,GAAK,EAAE,GACtFU,EACAd,EAAK,UACP,CACF,KAAO,CAEL,IAAMY,EAAa,MAAMC,GACvBF,EACA,GAAGN,EAAY,KAAO,SAASA,EAAY,IAAI,GAAK,EAAE,GACtDS,EACAd,EAAK,UACP,EACAc,GAAsBF,EACtB,MAAMC,GACJF,EACA,GAAGN,EAAY,OAAS,YAAYA,EAAY,MAAM,GAAK,EAAE,GAC7DS,EACAd,EAAK,UACP,CACF,CAEA,IAAMmB,GAAcR,EAAS,KAAK,GAAG,QAAQ,EAAE,OAAS,KAAOL,EACzDc,GAAeT,EAAS,KAAK,GAAG,QAAQ,EAAE,QAAU,KAAOL,EAC3De,EAAI,CAACF,EAAa,EAClBG,EAAI,CAACF,EAAc,EAInBG,EAAKC,EAAM,IAAIb,CAAQ,EACvBc,EAAUC,EAAkB1B,EAAM,CAAC,CAAC,EAEtCA,EAAK,OAAS,cAChByB,EAAQ,UAAY,EACpBA,EAAQ,UAAY,SAItB,IAAME,EAAYJ,EAAG,UAAUF,EAAGC,EAAGH,EAAYC,EAAaK,CAAO,EAE/DG,EAAOjB,EAAS,OAAO,IAAMgB,EAAW,cAAc,EAiC5D,GAhCAC,EAAK,KAAK,QAAS,uBAAuB,EAAE,KAAK,QAAS1B,CAAU,EAIpES,EAAS,UAAU,QAAQ,EAAE,KAAK,CAACkB,EAAQC,EAAWC,IAAe,CAEnE,IAAMC,EAAOC,EAAqBF,EAAMD,CAAC,CAAC,EAEpCI,EAAYF,EAAK,KAAK,WAAW,EACnCG,EAAa,EACbC,EAAa,EACjB,GAAIF,EAAW,CAEb,IAAMG,EADQ,OAAO,8BAA8B,EAC3B,KAAKH,CAAS,EAClCG,IACFF,EAAa,WAAWE,EAAU,CAAC,CAAC,EACpCD,EAAa,WAAWC,EAAU,CAAC,CAAC,EAExC,CAEA,IAAMC,EAAgBF,EAAahB,EAAc,EAC7CmB,EAAgBlB,EAAIf,EAAU,GAG9BwB,IAAM,GAAKA,IAAM,KACnBS,EAAgBJ,GAGlBH,EAAK,KAAK,YAAa,aAAaO,CAAa,KAAKD,EAAgBhC,CAAO,GAAG,CAClF,CAAC,EAGGQ,EAAqBF,EAAaG,EAAaR,EAAK,CACtD,IAAMiC,EAAYjB,EAAG,KACnBF,EACAC,EAAIV,EAAaG,EAAaR,EAC9Bc,EAAIF,EACJG,EAAIV,EAAaG,EAAaR,EAC9BkB,CACF,EACoBd,EAAS,OAAO,IAAM6B,CAAS,EACvC,KAAK,QAAStC,CAAU,CACtC,CAEA,OAAAuC,EAAiBzC,EAAM4B,CAAI,EAE3B5B,EAAK,UAAY,SAAU0C,EAAO,CAChC,OAAOC,EAAU,KAAK3C,EAAM0C,CAAK,CACnC,EAEO/B,CACT,CA7JsBiC,EAAA9C,GAAA,kBA+JtB,eAAee,GACbgC,EACAC,EACAC,EACAC,EAAQ,GACR,CACA,GAAIF,IAAc,GAChB,MAAO,GAET,IAAMG,EAASJ,EAAY,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EAAE,KAAK,QAASG,CAAK,EAC3EE,EAASC,EAAU,EACnBC,EAAgBF,EAAO,YAAc,GAErClB,EAAO,MAAMqB,GACjBJ,EACAK,GAAaC,GAAeT,CAAS,CAAC,EACtC,CACE,MAAOU,GAAmBV,EAAWI,CAAM,EAAI,GAC/C,QAAS,sBACT,cAAAE,EACA,MAAAJ,CACF,EACAE,CACF,EACIO,EAEJ,GAAKL,EAYE,CACL,IAAMM,EAAM1B,EAAK,SAAS,CAAC,EACrB2B,EAAK1B,EAAOD,CAAI,EAEtByB,EAAOC,EAAI,sBAAsB,EACjCC,EAAG,KAAK,QAASF,EAAK,KAAK,EAC3BE,EAAG,KAAK,SAAUF,EAAK,MAAM,CAC/B,KAnBoB,CAClB,IAAMG,EAAY5B,EAAK,SAAS,CAAC,EACjC,QAAW6B,KAASD,EAAU,SAC5BC,EAAM,YAAcA,EAAM,YAAY,WAAW,OAAQ,GAAG,EAAE,WAAW,OAAQ,GAAG,EAChFb,GACFa,EAAM,aAAa,QAASb,CAAK,EAIrCS,EAAOzB,EAAK,QAAQ,EAEpByB,EAAK,QAAU,CACjB,CAUA,OAAAR,EAAO,KAAK,YAAa,aAAa,CAACQ,EAAK,MAAQ,CAAC,IAAI,CAACA,EAAK,OAAS,EAAIV,CAAO,GAAG,EAC/EU,EAAK,MACd,CAlDeb,EAAA/B,GAAA,WCnKf,IAAMiD,GAAoBC,EAACC,GAAkD,CAC3E,OAAQA,EAAU,CAChB,IAAK,YACH,MAAO,MACT,IAAK,OACH,MAAO,SACT,IAAK,SACH,OAAO,KACT,IAAK,MACH,MAAO,OACT,IAAK,WACH,MAAO,WACX,CACF,EAb0B,qBAc1B,eAAsBC,GACpBC,EAEAC,EACA,CAAE,OAAAC,CAAO,EACT,CACA,GAAM,CAAE,YAAAC,EAAa,WAAAC,CAAW,EAAIC,EAAcJ,CAAU,EAC5DA,EAAW,WAAaE,GAAe,GAEvC,IAAMG,EAAgB,GAChBC,EAAWN,EAAW,MAC5BA,EAAW,OAASA,EAAW,OAAS,KAAO,GAE/C,GAAM,CACJ,SAAAO,EACA,KAAAC,EACA,MAAOC,CACT,EAAI,MAAMC,EAAYX,EAAQC,EAAYW,EAAeX,CAAU,CAAC,EAC9DY,EAAUZ,EAAW,SAAW,GAElCa,EAAY,GACZC,EAEA,WAAYd,GAAcA,EAAW,QAAUC,GAAQ,QAAQ,gBACjEY,EAAYZ,GAAQ,QAAQ,cAAc,QAAQ,WAAYD,EAAW,MAAM,EAC/Ec,EAAOP,EACJ,OAAoB,QAAS,cAAc,EAC3C,KAAK,QAAS,oBAAoB,EAClC,KAAK,aAAcM,CAAS,EAC5B,KAAK,SAAU,QAAQ,GAG5B,IAAME,EAAU,CACd,cAAef,EAAW,cAC1B,WAAYA,EAAW,YAAc,GACrC,MAAOA,EAAW,MAClB,IAAKA,EAAW,IAChB,QAASA,EAAW,SAAW,EAC/B,YAAa,EACf,EACIgB,EAASC,EACTH,EACD,CAAE,MAAOE,EAAS,KAAMC,CAAM,EAAI,MAAMC,GACvCJ,EACC,WAAYd,GAAcA,EAAW,QAAW,GACjDe,CACF,EAEC,CAAE,MAAOC,EAAS,KAAMC,CAAM,EAAI,MAAMC,GACvCX,EACC,WAAYP,GAAcA,EAAW,QAAW,GACjDe,CACF,EAEF,GAAM,CAAE,MAAOI,EAAiB,KAAMC,CAAa,EAAI,MAAMF,GAC3DX,EACC,aAAcP,GAAcA,EAAW,UAAa,GACrDe,CACF,EACAf,EAAW,MAAQM,EACnB,IAAMe,EAAgB,GAChBC,EAAatB,GAAY,OAAS,EAClCuB,EAAY,KAAK,IAAIN,EAAM,OAAQG,EAAa,MAAM,EAAI,EAC1DI,EACJ,KAAK,IAAIhB,EAAK,OAASa,EAAgB,EAAGrB,GAAY,QAAU,CAAC,EAAIuB,EACjEE,EAAI,CAACH,EAAa,EAClBI,EAAI,CAACF,EAAc,EACzBf,EAAa,KACX,YACA,cAAgBG,EAAUU,EAAa,GAAK,MAAQ,CAACC,EAAYf,EAAK,OAAS,GAAK,GACtF,EACAQ,EAAQ,KACN,YACA,cAAgBJ,EAAUU,EAAa,GAAK,MAAQ,CAACC,EAAYf,EAAK,OAAS,GAAK,GACtF,EACAW,EAAgB,KACd,YACA,cACGP,EAAUU,EAAa,EAAIF,EAAa,MAAQ,EAAIf,GACrD,MACC,CAACkB,EAAYf,EAAK,OAAS,GAC5B,GACJ,EAEA,IAAImB,EAEE,CAAE,GAAAC,EAAI,GAAAC,CAAG,EAAI7B,EACb,CAAE,UAAA8B,CAAU,EAAI9B,EAEtB,GAAIA,EAAW,OAAS,YAAa,CAEnC,IAAM+B,EAAKC,EAAM,IAAIzB,CAAQ,EACvBQ,EAAUkB,EAAkBjC,EAAY,CAAC,CAAC,EAE1CkC,EACJN,GAAMC,EACFE,EAAG,KAAKI,EAAuBV,EAAGC,EAAGJ,EAAYE,EAAaI,GAAM,CAAC,EAAGb,CAAO,EAC/EgB,EAAG,UAAUN,EAAGC,EAAGJ,EAAYE,EAAaT,CAAO,EAEzDY,EAAOpB,EAAS,OAAO,IAAM2B,EAAW,cAAc,EACtDP,EAAK,KAAK,QAAS,uBAAuB,EAAE,KAAK,QAASG,GAAwB,IAAI,CACxF,KAAO,CACLH,EAAOpB,EAAS,OAAO,OAAQ,cAAc,EAE7CoB,EACG,KAAK,QAAS,+BAA+B,EAC7C,KAAK,QAASxB,CAAU,EACxB,KAAK,KAAMyB,GAAM,CAAC,EAClB,KAAK,KAAMC,GAAM,CAAC,EAClB,KAAK,IAAKJ,CAAC,EACX,KAAK,IAAKC,CAAC,EACX,KAAK,QAASJ,CAAU,EACxB,KAAK,SAAUE,CAAW,EAE7B,IAAM3B,EAAW,aAAcG,GAAcA,EAAW,SACxD,GAAIH,EAAU,CACZ,IAAMuC,EAAO7B,EAAS,OAAO,MAAM,EAC7B8B,EAAQZ,EAAI,EAEZa,EAAKZ,EAAI,KAAK,OAAOE,GAAM,GAAK,CAAC,EACjCW,EAAKb,EAAIF,EAAc,KAAK,OAAOI,GAAM,GAAK,CAAC,EACrDQ,EACG,KAAK,KAAMC,CAAK,EAChB,KAAK,KAAMC,CAAE,EACb,KAAK,KAAMD,CAAK,EAChB,KAAK,KAAME,CAAE,EAEb,KAAK,eAAgB,GAAG,EACxB,KAAK,SAAU5C,GAAkBE,CAAQ,CAAC,CAC/C,CACF,CAEA,OAAA2C,EAAiBxC,EAAY2B,CAAI,EACjC3B,EAAW,OAASwB,EAEpBxB,EAAW,UAAY,SAAUyC,EAAO,CACtC,OAAOC,EAAU,KAAK1C,EAAYyC,CAAK,CACzC,EAEOlC,CACT,CA5IsBX,EAAAE,GAAA,cCgEf,IAAM6C,GAAa,CACxB,CACE,aAAc,UACd,KAAM,YACN,UAAW,OACX,YAAa,yBACb,QAAS,CAAC,OAAQ,UAAW,WAAW,EACxC,gBAAiB,CAAC,YAAY,EAC9B,QAASC,EACX,EACA,CACE,aAAc,QACd,KAAM,oBACN,UAAW,UACX,YAAa,sBACb,QAAS,CAAC,OAAO,EACjB,gBAAiB,CAAC,aAAa,EAC/B,QAASC,EACX,EACA,CACE,aAAc,iBACd,KAAM,UACN,UAAW,UACX,YAAa,iBACb,QAAS,CAAC,WAAY,MAAM,EAC5B,QAASC,EACX,EACA,CACE,aAAc,aACd,KAAM,mBACN,UAAW,UACX,YAAa,aACb,QAAS,CAAC,aAAc,UAAW,mBAAoB,YAAY,EACnE,QAASC,EACX,EACA,CACE,aAAc,WACd,KAAM,WACN,UAAW,MACX,YAAa,mBACb,QAAS,CAAC,KAAM,WAAY,UAAU,EACtC,QAASC,EACX,EACA,CACE,aAAc,QACd,KAAM,SACN,UAAW,SACX,YAAa,iBACb,QAAS,CAAC,MAAM,EAChB,QAASC,EACX,EACA,CACE,aAAc,WACd,KAAM,UACN,UAAW,OACX,YAAa,uBACb,QAAS,CAAC,WAAY,UAAW,UAAU,EAC3C,QAASC,EACX,EACA,CACE,aAAc,sBACd,KAAM,UACN,UAAW,MACX,YAAa,gCACb,QAAS,CAAC,UAAW,SAAS,EAC9B,QAASC,EACX,EACA,CACE,aAAc,oBACd,KAAM,aACN,UAAW,SACX,YAAa,6BACb,QAAS,CAAC,aAAc,QAAQ,EAChC,gBAAiB,CAAC,YAAY,EAC9B,QAASC,EACX,EACA,CACE,aAAc,oBACd,KAAM,YACN,UAAW,SACX,YAAa,6BACb,QAAS,CAAC,YAAa,QAAQ,EAC/B,gBAAiB,CAAC,WAAW,EAC7B,QAASC,EACX,EACA,CACE,aAAc,kBACd,KAAM,wBACN,UAAW,SACX,YAAa,kBACb,QAAS,CAAC,WAAY,mBAAoB,WAAW,EACrD,QAASC,EACX,EACA,CACE,aAAc,mBACd,KAAM,qBACN,UAAW,SACX,YAAa,2BACb,QAAS,CAAC,SAAU,gBAAiB,eAAe,EACpD,gBAAiB,CAAC,eAAe,EACjC,QAASC,EACX,EACA,CACE,aAAc,OACd,KAAM,gBACN,UAAW,WACX,YAAa,0BACb,QAAS,CAAC,eAAe,EACzB,gBAAiB,CAAC,cAAc,EAChC,QAASC,EACX,EACA,CACE,aAAc,aACd,KAAM,aACN,UAAW,OACX,YAAa,aACb,QAASC,EACX,EACA,CACE,aAAc,OACd,KAAM,oBACN,UAAW,aACX,YAAa,oBACb,QAAS,CAAC,OAAQ,mBAAmB,EACrC,QAASC,EACX,EACA,CACE,aAAc,uBACd,KAAM,kBACN,UAAW,WACX,YAAa,sBACb,QAAS,CAAC,kBAAmB,gBAAiB,WAAY,gBAAgB,EAC1E,QAASC,EACX,EACA,CACE,aAAc,QACd,KAAM,eACN,UAAW,UACX,YAAa,uBACb,QAAS,CAAC,QAAS,cAAc,EACjC,gBAAiB,CAAC,YAAY,EAC9B,QAASC,EACX,EACA,CACE,aAAc,OACd,KAAM,gBACN,UAAW,UACX,YAAa,aACb,QAAS,CAAC,OAAQ,eAAe,EACjC,gBAAiB,CAAC,UAAU,EAC5B,QAASC,EACX,EACA,CACE,aAAc,YACd,KAAM,mBACN,UAAW,OACX,YAAa,+BACb,QAAS,CAAC,MAAM,EAChB,gBAAiB,CAAC,UAAU,EAC5B,QAASC,EACX,EACA,CACE,aAAc,UACd,KAAM,YACN,UAAW,YACX,YAAa,iCACb,QAAS,CAAC,YAAa,SAAS,EAChC,QAASC,EACX,EACA,CACE,aAAc,UACd,KAAM,cACN,UAAW,QACX,YAAa,iBACb,QAAS,CAAC,UAAW,SAAS,EAC9B,QAASC,EACX,EACA,CACE,aAAc,gBACd,KAAM,cACN,UAAW,UACX,YAAa,iBACb,QAASC,EACX,EACA,CACE,aAAc,oCACd,KAAM,eACN,UAAW,SACX,YAAa,iBACb,QAASC,EACX,EACA,CACE,aAAc,WACd,KAAM,iBACN,UAAW,OACX,YAAa,qBACb,QAAS,CAAC,WAAY,gBAAgB,EACtC,QAASC,EACX,EACA,CACE,aAAc,WACd,KAAM,WACN,UAAW,MACX,YAAa,wBACb,QAAS,CAAC,MAAO,UAAU,EAC3B,QAASC,EACX,EACA,CACE,aAAc,QACd,KAAM,yBACN,UAAW,QACX,YAAa,qBACb,QAAS,CAAC,wBAAwB,EAClC,QAASC,EACX,EACA,CACE,aAAc,wBACd,KAAM,sBACN,UAAW,QACX,YAAa,wBACb,QAAS,CAAC,MAAO,qBAAqB,EACtC,QAASC,EACX,EACA,CACE,aAAc,eACd,KAAM,iBACN,UAAW,UACX,YAAa,eACb,QAAS,CAAC,OAAQ,gBAAgB,EAClC,QAASC,EACX,EACA,CACE,aAAc,UACd,KAAM,mBACN,UAAW,YACX,YAAa,uBACb,QAAS,CAAC,mBAAoB,SAAS,EACvC,QAASC,EACX,EACA,CACE,aAAc,kBACd,KAAM,oBACN,UAAW,WACX,YAAa,wBACb,QAAS,CAAC,WAAY,oBAAqB,iBAAiB,EAC5D,QAASC,EACX,EACA,CACE,aAAc,UACd,KAAM,WACN,UAAW,MACX,YAAa,qBACb,QAAS,CAAC,UAAW,UAAU,EAC/B,QAASC,EACX,EACA,CACE,aAAc,mBACd,KAAM,cACN,UAAW,WACX,YAAa,mBACb,QAAS,CAAC,mBAAoB,aAAa,EAC3C,QAASC,EACX,EACA,CACE,aAAc,WACd,KAAM,gBACN,UAAW,SACX,YAAa,iBACb,QAAS,CAAC,WAAY,eAAe,EACrC,QAASC,EACX,EACA,CACE,aAAc,aACd,KAAM,uBACN,UAAW,aACX,YAAa,kBACb,QAAS,CAAC,aAAc,kBAAkB,EAC1C,QAASC,EACX,EACA,CACE,aAAc,cACd,KAAM,mBACN,UAAW,WACX,YAAa,wBACb,QAAS,CAAC,cAAe,kBAAkB,EAC3C,QAASC,EACX,EACA,CACE,aAAc,eACd,KAAM,mBACN,UAAW,UACX,YAAa,oBACb,QAAS,CAAC,eAAgB,kBAAkB,EAC5C,QAASC,EACX,EACA,CACE,aAAc,iBACd,KAAM,mBACN,UAAW,OACX,YAAa,qBACb,QAAS,CAAC,YAAa,SAAU,kBAAkB,EACnD,QAASC,EACX,EACA,CACE,aAAc,gBACd,KAAM,oBACN,UAAW,UACX,YAAa,qBACb,QAAS,CAAC,QAAS,YAAa,mBAAmB,EACnD,QAASC,EACX,EACA,CACE,aAAc,cACd,KAAM,oBACN,UAAW,WACX,YAAa,cACb,QAAS,CAAC,cAAe,mBAAmB,EAC5C,QAASC,EACX,EACA,CACE,aAAc,UACd,KAAM,iBACN,UAAW,aACX,YAAa,UACb,QAAS,CAAC,UAAW,gBAAgB,EACrC,QAASC,EACX,EACA,CACE,aAAc,kBACd,KAAM,kBACN,UAAW,UACX,YAAa,kBACb,QAAS,CAAC,UAAW,iBAAiB,EACtC,QAASC,EACX,EACA,CACE,aAAc,iBACd,KAAM,mBACN,UAAW,WACX,YAAa,iBACb,QAAS,CAAC,mBAAoB,WAAY,gBAAgB,EAC1D,QAASC,EACX,EACA,CACE,aAAc,aACd,KAAM,OACN,UAAW,OACX,YAAa,aACb,QAAS,CAAC,YAAY,EACtB,QAASC,EACX,EACA,CACE,aAAc,MACd,KAAM,MACN,UAAW,MACX,YAAa,YACb,gBAAiB,CAAC,qBAAqB,EACvC,QAASC,EACX,EACA,CACE,aAAc,iBACd,KAAM,iBACN,UAAW,UACX,YAAa,iBACb,QAAS,CAAC,gBAAgB,EAC1B,QAASC,EACX,CACF,EAEMC,GAAmBC,EAAA,IAAM,CAiC7B,IAAMC,EAAU,CACd,GAAI,OAAO,QAhCc,CAEzB,MAAAC,GACA,OAAAC,GACA,KAAAC,GAGA,cAAAC,GACA,UAAAC,GAGA,WAAAC,GACA,WAAAC,GACA,KAAAC,GACA,YAAAC,GACA,YAAAC,GACA,OAAAC,GAGA,WAAAC,GAGA,SAAAC,GAGA,MAAAC,GAGA,eAAAC,EACF,CAGuC,EACrC,GAAG/D,GAAW,QAASgE,GACL,CACdA,EAAM,UACN,GAAI,YAAaA,EAAQA,EAAM,QAAU,CAAC,EAC1C,GAAI,oBAAqBA,EAAQA,EAAM,gBAAkB,CAAC,CAC5D,EACe,IAAKC,GAAU,CAACA,EAAOD,EAAM,OAAO,CAAU,CAC9D,CACH,EACA,OAAO,OAAO,YAAYhB,CAAO,CAInC,EAhDyB,oBAkDZkB,GAASpB,GAAiB,EAEhC,SAASqB,GAAaH,EAAiC,CAC5D,OAAOA,KAASE,EAClB,CAFgBnB,EAAAoB,GAAA,gBCjfhB,IAAMC,GAAY,IAAI,IAEtB,eAAsBC,GACpBC,EACAC,EACAC,EACA,CACA,IAAIC,EACAC,EAGAH,EAAK,QAAU,SACbA,EAAK,IAAMA,EAAK,GAClBA,EAAK,MAAQ,cAEbA,EAAK,MAAQ,cAIjB,IAAMI,EAAeJ,EAAK,MAAQK,GAAOL,EAAK,KAAK,EAAI,OAEvD,GAAI,CAACI,EACH,MAAM,IAAI,MAAM,kBAAkBJ,EAAK,KAAK,6BAA6B,EAG3E,GAAIA,EAAK,KAAM,CAEb,IAAIM,EACAL,EAAc,OAAO,gBAAkB,UACzCK,EAAS,OACAN,EAAK,aACdM,EAASN,EAAK,YAAc,UAE9BE,EAAQH,EACL,OAAoB,OAAO,EAC3B,KAAK,aAAcC,EAAK,IAAI,EAC5B,KAAK,SAAUM,GAAU,IAAI,EAChCH,EAAK,MAAMC,EAAaF,EAAOF,EAAMC,CAAa,CACpD,MACEE,EAAK,MAAMC,EAAaL,EAAMC,EAAMC,CAAa,EACjDC,EAAQC,EAEV,OAAIH,EAAK,SACPG,EAAG,KAAK,QAASH,EAAK,OAAO,EAG/BH,GAAU,IAAIG,EAAK,GAAIE,CAAK,EAExBF,EAAK,cACPE,EAAM,KAAK,QAASA,EAAM,KAAK,OAAO,EAAI,YAAY,EAEjDA,CACT,CAlDsBK,EAAAT,GAAA,cAoDf,IAAMU,GAAcD,EAAA,CAACR,EAAmBC,IAA2B,CACxEH,GAAU,IAAIG,EAAK,GAAID,CAAI,CAC7B,EAF2B,eAIdU,GAAQF,EAAA,IAAM,CACzBV,GAAU,MAAM,CAClB,EAFqB,SAIRa,GAAeH,EAACP,GAA6C,CACxE,IAAMG,EAAKN,GAAU,IAAIG,EAAK,EAAE,EAChCW,EAAI,MACF,oBACAX,EAAK,KACLA,EACA,cAAgBA,EAAK,EAAIA,EAAK,MAAQ,EAAI,GAAK,KAAOA,EAAK,MAAQ,EAAI,GACzE,EACA,IAAMY,EAAU,EACVC,EAAOb,EAAK,MAAQ,EAC1B,OAAIA,EAAK,YACPG,EAAG,KACD,YACA,cACGH,EAAK,EAAIa,EAAOb,EAAK,MAAQ,GAC9B,MACCA,EAAK,EAAIA,EAAK,OAAS,EAAIY,GAC5B,GACJ,EAEAT,EAAG,KAAK,YAAa,aAAeH,EAAK,EAAI,KAAOA,EAAK,EAAI,GAAG,EAE3Da,CACT,EAvB4B", "names": ["labelHelper", "__name", "parent", "node", "_classes", "cssClasses", "useHtmlLabels", "evaluate", "getConfig", "shapeSvg", "labelEl", "handleUndefinedAttr", "label", "text", "createText", "sanitizeText", "decodeEntities", "bbox", "halfPadding", "div", "dv", "select_default", "images", "noImgText", "img", "res", "setupImage", "bodyFontSize", "enlargingFactor", "parsedBodyFontSize", "defaultConfig_default", "parseFontSize", "width", "insertLabel", "options", "updateNodeBounds", "element", "getNodeClasses", "__name", "node", "extra", "createPathFromPoints", "points", "pointStrings", "p", "i", "generateFullSineWavePoints", "x1", "y1", "x2", "y2", "amplitude", "numCycles", "deltaX", "deltaY", "cycle<PERSON><PERSON><PERSON>", "frequency", "midY", "t", "x", "generateCirclePoints", "centerX", "centerY", "radius", "numPoints", "startAngle", "endAngle", "startAngleRad", "angleStep", "angle", "y", "t", "e", "s", "n", "o", "r", "__name", "a", "h", "i", "c", "l", "u", "p", "f", "d", "g", "M", "k", "b", "y", "m", "x", "w", "P", "v", "S", "O", "L", "T", "D", "A", "G", "E", "$", "j", "z", "F", "q", "V", "_", "Z", "I", "C", "W", "R", "Q", "H", "N", "B", "J", "K", "U", "X", "Y", "tt", "et", "st", "nt", "ot", "at", "intersectRect", "__name", "node", "point", "x", "y", "dx", "dy", "w", "h", "sx", "sy", "intersect_rect_default", "applyStyle", "dom", "styleFn", "__name", "addHtmlLabel", "node", "fo", "select_default", "div", "label", "hasKatex", "renderKatex", "common_default", "getConfig", "labelClass", "createLabel", "_vertexText", "style", "isTitle", "isNode", "vertexText", "evaluate", "log", "decodeEntities", "svgLabel", "rows", "row", "tspan", "createLabel_default", "createRoundedRectPathD", "__name", "x", "y", "totalWidth", "totalHeight", "radius", "solidStateFill", "__name", "color", "handDrawnSeed", "getConfig", "compileStyles", "node", "stylesMap", "styles2Map", "styles", "styleMap", "style", "key", "value", "isLabelStyle", "styles2String", "stylesArray", "labelStyles", "nodeStyles", "borderStyles", "backgroundStyles", "userNodeOverrides", "options", "themeVariables", "nodeBorder", "mainBkg", "rect", "__name", "parent", "node", "log", "siteConfig", "getConfig", "themeVariables", "handDrawnSeed", "clusterBkg", "clusterBorder", "labelStyles", "nodeStyles", "borderStyles", "backgroundStyles", "styles2String", "shapeSvg", "useHtmlLabels", "evaluate", "labelEl", "text", "createText", "bbox", "div", "dv", "select_default", "width", "height", "x", "y", "rc", "at", "options", "userNodeOverrides", "roughNode", "createRoundedRectPathD", "subGraphTitleTopMargin", "getSubGraphTitleMargins", "span", "rectBox", "point", "intersect_rect_default", "noteGroup", "padding", "halfPadding", "roundedWithTitle", "altBackground", "compositeBackground", "compositeTitleBackground", "nodeBorder", "outerRectG", "label", "innerRect", "createLabel_default", "innerHeight", "innerY", "isAlt", "roughOuterNode", "roughInnerNode", "kanbanSection", "divider", "squareRect", "shapes", "clusterElems", "insertCluster", "elem", "shape", "cluster", "clear", "__name", "clusterElems", "intersectNode", "node", "point", "__name", "intersect_node_default", "intersectEllipse", "node", "rx", "ry", "point", "cx", "cy", "px", "py", "det", "dx", "dy", "__name", "intersect_ellipse_default", "intersectCircle", "node", "rx", "point", "intersect_ellipse_default", "__name", "intersect_circle_default", "intersectLine", "p1", "p2", "q1", "q2", "a1", "a2", "b1", "b2", "c1", "c2", "r1", "r2", "r3", "r4", "denom", "offset", "num", "y", "sameSign", "__name", "intersect_line_default", "intersectPolygon", "node", "polyPoints", "point", "x1", "y1", "intersections", "minX", "minY", "entry", "left", "top", "i", "p1", "p2", "intersect", "intersect_line_default", "p", "q", "pdx", "pdy", "distp", "qdx", "qdy", "distq", "__name", "intersect_polygon_default", "intersect_default", "intersect_node_default", "intersect_circle_default", "intersect_ellipse_default", "intersect_polygon_default", "intersect_rect_default", "anchor", "parent", "node", "labelStyles", "styles2String", "classes", "getNodeClasses", "cssClasses", "shapeSvg", "radius", "cssStyles", "rc", "at", "options", "userNodeOverrides", "roughNode", "circleElem", "handleUndefinedAttr", "updateNodeBounds", "point", "log", "intersect_default", "__name", "generateArcPoints", "x1", "y1", "x2", "y2", "rx", "ry", "clockwise", "midX", "midY", "angle", "dx", "dy", "transformedX", "transformedY", "distance", "scaledCenterDistance", "centerX", "centerY", "startAngle", "angleRange", "points", "i", "t", "x", "y", "__name", "bowTieRect", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "labelHelper", "getNodeClasses", "w", "h", "cssStyles", "rc", "at", "options", "userNodeOverrides", "bowTieRectPath", "createPathFromPoints", "bowTieRectShapePath", "bowTieRectShape", "updateNodeBounds", "point", "intersect_default", "insertPolygonShape", "parent", "w", "h", "points", "d", "__name", "card", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "labelHelper", "getNodeClasses", "padding", "w", "left", "right", "top", "bottom", "points", "polygon", "cssStyles", "rc", "at", "options", "userNodeOverrides", "pathData", "createPathFromPoints", "roughNode", "insertPolygonShape", "updateNodeBounds", "point", "intersect_default", "__name", "choice", "parent", "node", "nodeStyles", "styles2String", "shapeSvg", "getNodeClasses", "cssStyles", "s", "points", "rc", "at", "options", "userNodeOverrides", "choice<PERSON>ath", "createPathFromPoints", "roughNode", "choiceShape", "point", "intersect_default", "__name", "circle", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "halfPadding", "labelHelper", "getNodeClasses", "radius", "circleElem", "cssStyles", "rc", "at", "options", "userNodeOverrides", "roughNode", "handleUndefinedAttr", "updateNodeBounds", "point", "log", "intersect_default", "__name", "createLine", "r", "xAxis45", "yAxis45", "lineLength", "pointQ1", "pointQ2", "pointQ3", "pointQ4", "__name", "crossedCircle", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "getNodeClasses", "radius", "cssStyles", "rc", "at", "options", "userNodeOverrides", "circleNode", "linePath", "lineNode", "updateNodeBounds", "point", "log", "intersect_default", "generateCirclePoints", "centerX", "centerY", "radius", "numPoints", "startAngle", "endAngle", "points", "startAngleRad", "angleStep", "i", "angle", "x", "y", "__name", "curlyBraceLeft", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "label", "labelHelper", "getNodeClasses", "w", "h", "cssStyles", "rectPoints", "rc", "at", "options", "userNodeOverrides", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createPathFromPoints", "curlyBraceLeftNode", "rectPath", "rectShape", "curlyBraceLeftShape", "updateNodeBounds", "point", "intersect_default", "generateCirclePoints", "centerX", "centerY", "radius", "numPoints", "startAngle", "endAngle", "points", "startAngleRad", "angleStep", "i", "angle", "x", "y", "__name", "curlyBraceRight", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "label", "labelHelper", "getNodeClasses", "w", "h", "cssStyles", "rectPoints", "rc", "at", "options", "userNodeOverrides", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createPathFromPoints", "curlyBraceRightNode", "rectPath", "rectShape", "curlyBraceRightShape", "updateNodeBounds", "point", "intersect_default", "generateCirclePoints", "centerX", "centerY", "radius", "numPoints", "startAngle", "endAngle", "points", "startAngleRad", "angleStep", "i", "angle", "x", "y", "__name", "curlyBraces", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "label", "labelHelper", "getNodeClasses", "w", "h", "cssStyles", "leftCurlyBracePoints", "rightCurlyBracePoints", "rectPoints", "rc", "at", "options", "userNodeOverrides", "newLeftCurlyBracePath", "createPathFromPoints", "leftCurlyBraceNode", "newRightCurlyBracePath", "rightCurlyBraceNode", "rectPath", "rectShape", "curlyBracesShape", "updateNodeBounds", "point", "intersect_default", "curvedTrapezoid", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "labelHelper", "getNodeClasses", "min<PERSON><PERSON><PERSON>", "minHeight", "w", "h", "radius", "cssStyles", "rc", "at", "options", "userNodeOverrides", "totalWidth", "totalHeight", "rw", "tw", "points", "generateCirclePoints", "pathData", "createPathFromPoints", "shapeNode", "polygon", "updateNodeBounds", "point", "intersect_default", "__name", "createCylinderPathD", "__name", "x", "y", "width", "height", "rx", "ry", "createOuterCylinderPathD", "createInnerCylinderPathD", "cylinder", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "label", "labelHelper", "getNodeClasses", "w", "h", "cssStyles", "rc", "at", "outerPathData", "innerPathData", "outerNode", "userNodeOverrides", "innerLine", "pathData", "handleUndefinedAttr", "updateNodeBounds", "point", "pos", "intersect_default", "dividedRectangle", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "label", "labelHelper", "getNodeClasses", "w", "h", "rectOffset", "x", "y", "cssStyles", "rc", "at", "options", "userNodeOverrides", "pts", "poly", "p", "polygon", "updateNodeBounds", "point", "intersect_default", "__name", "doublecircle", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "halfPadding", "labelHelper", "getNodeClasses", "outerRadius", "innerRadius", "circleGroup", "cssStyles", "rc", "at", "outerOptions", "userNodeOverrides", "innerOptions", "outerRoughNode", "innerRoughNode", "handleUndefinedAttr", "outerCircle", "innerCircle", "updateNodeBounds", "point", "log", "intersect_default", "__name", "filledCircle", "parent", "node", "themeVariables", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "getNodeClasses", "radius", "cssStyles", "rc", "at", "nodeBorder", "options", "userNodeOverrides", "circleNode", "updateNodeBounds", "point", "log", "intersect_default", "__name", "flippedTriangle", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "label", "labelHelper", "getNodeClasses", "w", "h", "tw", "points", "cssStyles", "rc", "at", "options", "userNodeOverrides", "pathData", "createPathFromPoints", "roughNode", "updateNodeBounds", "point", "log", "intersect_default", "__name", "fork<PERSON><PERSON>n", "parent", "node", "dir", "state", "themeVariables", "nodeStyles", "styles2String", "shapeSvg", "getNodeClasses", "cssStyles", "width", "height", "x", "y", "rc", "at", "options", "userNodeOverrides", "roughNode", "shape", "updateNodeBounds", "padding", "point", "intersect_default", "__name", "halfRoundedRectangle", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "min<PERSON><PERSON><PERSON>", "minHeight", "shapeSvg", "bbox", "labelHelper", "getNodeClasses", "w", "h", "radius", "cssStyles", "rc", "at", "options", "userNodeOverrides", "points", "generateCirclePoints", "pathData", "createPathFromPoints", "shapeNode", "polygon", "updateNodeBounds", "point", "log", "intersect_default", "__name", "createHexagonPathD", "__name", "x", "y", "width", "height", "m", "hexagon", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "labelHelper", "getNodeClasses", "f", "h", "w", "points", "polygon", "cssStyles", "rc", "at", "options", "userNodeOverrides", "pathData", "roughNode", "insertPolygonShape", "updateNodeBounds", "point", "intersect_default", "hourglass", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "labelHelper", "getNodeClasses", "w", "cssStyles", "rc", "at", "options", "userNodeOverrides", "points", "pathData", "createPathFromPoints", "shapeNode", "polygon", "updateNodeBounds", "point", "log", "intersect_default", "__name", "icon", "parent", "node", "themeVariables", "flowchart", "labelStyles", "styles2String", "assetHeight", "assetWidth", "iconSize", "defaultWidth", "shapeSvg", "bbox", "label", "labelHelper", "topLabel", "height", "width", "nodeBorder", "stylesMap", "compileStyles", "y", "labelPadding", "rc", "at", "options", "userNodeOverrides", "iconNode", "outerWidth", "outerHeight", "outerNode", "iconShape", "outerShape", "iconElem", "getIconSVG", "iconBBox", "iconWidth", "iconHeight", "iconX", "iconY", "updateNodeBounds", "point", "log", "intersect_default", "dx", "dy", "nodeHeight", "points", "__name", "iconCircle", "parent", "node", "themeVariables", "flowchart", "labelStyles", "styles2String", "assetHeight", "assetWidth", "iconSize", "defaultWidth", "shapeSvg", "bbox", "label", "labelHelper", "padding", "labelPadding", "topLabel", "nodeBorder", "mainBkg", "stylesMap", "compileStyles", "rc", "at", "options", "userNodeOverrides", "fill", "iconElem", "getIconSVG", "iconBBox", "iconWidth", "iconHeight", "iconX", "iconY", "diameter", "iconNode", "outerWidth", "outerHeight", "outerNode", "iconShape", "outerShape", "updateNodeBounds", "point", "log", "intersect_default", "__name", "iconRounded", "parent", "node", "themeVariables", "flowchart", "labelStyles", "styles2String", "assetHeight", "assetWidth", "iconSize", "defaultWidth", "shapeSvg", "bbox", "halfPadding", "label", "labelHelper", "topLabel", "height", "width", "nodeBorder", "mainBkg", "stylesMap", "compileStyles", "x", "y", "labelPadding", "rc", "at", "options", "userNodeOverrides", "fill", "iconNode", "createRoundedRectPathD", "outerWidth", "outerHeight", "outerNode", "iconShape", "outerShape", "iconElem", "getIconSVG", "iconBBox", "iconWidth", "iconHeight", "iconX", "iconY", "updateNodeBounds", "point", "log", "intersect_default", "dx", "dy", "nodeHeight", "points", "__name", "iconSquare", "parent", "node", "themeVariables", "flowchart", "labelStyles", "styles2String", "assetHeight", "assetWidth", "iconSize", "defaultWidth", "shapeSvg", "bbox", "halfPadding", "label", "labelHelper", "topLabel", "height", "width", "nodeBorder", "mainBkg", "stylesMap", "compileStyles", "x", "y", "labelPadding", "rc", "at", "options", "userNodeOverrides", "fill", "iconNode", "createRoundedRectPathD", "outerWidth", "outerHeight", "outerNode", "iconShape", "outerShape", "iconElem", "getIconSVG", "iconBBox", "iconWidth", "iconHeight", "iconX", "iconY", "updateNodeBounds", "point", "log", "intersect_default", "dx", "dy", "nodeHeight", "points", "__name", "imageSquare", "parent", "node", "flowchart", "img", "imageNaturalWidth", "imageNaturalHeight", "labelStyles", "styles2String", "defaultWidth", "imageRawWidth", "imageWidth", "imageHeight", "shapeSvg", "bbox", "label", "labelHelper", "topLabel", "x", "labelPadding", "rc", "at", "options", "userNodeOverrides", "imageNode", "outerWidth", "outerHeight", "outerNode", "iconShape", "outerShape", "image", "updateNodeBounds", "point", "log", "intersect_default", "dx", "dy", "nodeHeight", "points", "__name", "inv_trapezoid", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "labelHelper", "getNodeClasses", "w", "h", "points", "polygon", "cssStyles", "rc", "at", "options", "userNodeOverrides", "pathData", "createPathFromPoints", "roughNode", "insertPolygonShape", "updateNodeBounds", "point", "intersect_default", "__name", "drawRect", "parent", "node", "options", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "labelHelper", "getNodeClasses", "totalWidth", "totalHeight", "x", "y", "rect", "rx", "ry", "cssStyles", "rc", "at", "userNodeOverrides", "roughNode", "createRoundedRectPathD", "handleUndefinedAttr", "updateNodeBounds", "point", "intersect_default", "__name", "labelRect", "parent", "node", "shapeSvg", "bbox", "label", "labelHelper", "rect", "updateNodeBounds", "point", "intersect_default", "__name", "lean_left", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "labelHelper", "getNodeClasses", "w", "h", "points", "polygon", "cssStyles", "rc", "at", "options", "userNodeOverrides", "pathData", "createPathFromPoints", "roughNode", "insertPolygonShape", "updateNodeBounds", "point", "intersect_default", "__name", "lean_right", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "labelHelper", "getNodeClasses", "w", "h", "points", "polygon", "cssStyles", "rc", "at", "options", "userNodeOverrides", "pathData", "createPathFromPoints", "roughNode", "insertPolygonShape", "updateNodeBounds", "point", "intersect_default", "__name", "lightningBolt", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "getNodeClasses", "cssStyles", "width", "height", "gap", "points", "rc", "at", "options", "userNodeOverrides", "linePath", "createPathFromPoints", "lineNode", "updateNodeBounds", "point", "log", "intersect_default", "__name", "createCylinderPathD", "__name", "x", "y", "width", "height", "rx", "ry", "outerOffset", "createOuterCylinderPathD", "createInnerCylinderPathD", "<PERSON><PERSON><PERSON><PERSON>", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "label", "labelHelper", "getNodeClasses", "w", "h", "cylinder", "cssStyles", "rc", "at", "outerPathData", "innerPathData", "options", "userNodeOverrides", "outerNode", "innerLine", "pathData", "handleUndefinedAttr", "updateNodeBounds", "point", "pos", "intersect_default", "linedWaveEdgedRect", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "label", "labelHelper", "getNodeClasses", "w", "h", "waveAmplitude", "finalH", "cssStyles", "rc", "at", "options", "userNodeOverrides", "points", "generateFullSineWavePoints", "poly", "p", "waveEdgeRect", "updateNodeBounds", "point", "intersect_default", "__name", "multiRect", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "label", "labelHelper", "getNodeClasses", "w", "h", "rectOffset", "x", "y", "cssStyles", "rc", "at", "options", "userNodeOverrides", "outerPathPoints", "innerPathPoints", "outerPath", "createPathFromPoints", "outerNode", "innerPath", "innerNode", "updateNodeBounds", "point", "intersect_default", "__name", "multiWaveEdgedRectangle", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "label", "labelHelper", "getNodeClasses", "w", "h", "waveAmplitude", "finalH", "x", "y", "rectOffset", "cssStyles", "wavePoints", "generateFullSineWavePoints", "lastWavePoint", "outerPathPoints", "innerPathPoints", "rc", "at", "options", "userNodeOverrides", "outerPath", "createPathFromPoints", "outerNode", "innerPath", "innerNode", "shape", "updateNodeBounds", "point", "intersect_default", "__name", "note", "parent", "node", "themeVariables", "labelStyles", "nodeStyles", "styles2String", "getConfig", "shapeSvg", "bbox", "labelHelper", "getNodeClasses", "totalWidth", "totalHeight", "x", "y", "cssStyles", "rc", "at", "options", "userNodeOverrides", "noteShapeNode", "rect", "updateNodeBounds", "point", "intersect_default", "__name", "createDecisionBoxPathD", "__name", "x", "y", "size", "question", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "labelHelper", "getNodeClasses", "w", "h", "s", "points", "polygon", "cssStyles", "rc", "at", "options", "userNodeOverrides", "pathData", "roughNode", "insertPolygonShape", "updateNodeBounds", "point", "log", "intersect_default", "rect_left_inv_arrow", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "label", "labelHelper", "getNodeClasses", "w", "h", "x", "y", "notch", "points", "cssStyles", "rc", "at", "options", "userNodeOverrides", "pathData", "createPathFromPoints", "roughNode", "polygon", "updateNodeBounds", "point", "intersect_default", "__name", "rectWithTitle", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "classes", "shapeSvg", "g", "label", "description", "title", "text", "createLabel_default", "bbox", "evaluate", "getConfig", "div", "dv", "select_default", "log", "textRows", "titleBox", "descr", "halfPadding", "totalWidth", "totalHeight", "x", "y", "rect", "innerLine", "rc", "at", "options", "userNodeOverrides", "roughNode", "createRoundedRectPathD", "roughLine", "updateNodeBounds", "point", "intersect_default", "__name", "roundedRect", "parent", "node", "options", "drawRect", "__name", "shadedProcess", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "label", "labelHelper", "getNodeClasses", "halfPadding", "w", "h", "x", "y", "cssStyles", "rc", "at", "options", "userNodeOverrides", "points", "roughNode", "p", "rect", "handleUndefinedAttr", "updateNodeBounds", "point", "intersect_default", "__name", "slopedRect", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "label", "labelHelper", "getNodeClasses", "w", "h", "x", "y", "cssStyles", "rc", "at", "options", "userNodeOverrides", "points", "pathData", "createPathFromPoints", "shapeNode", "polygon", "updateNodeBounds", "point", "intersect_default", "__name", "squareRect", "parent", "node", "options", "drawRect", "__name", "stadium", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "labelHelper", "getNodeClasses", "w", "rect", "cssStyles", "rc", "at", "options", "userNodeOverrides", "pathData", "createRoundedRectPathD", "roughNode", "handleUndefinedAttr", "updateNodeBounds", "point", "intersect_default", "__name", "state", "parent", "node", "drawRect", "__name", "stateEnd", "parent", "node", "themeVariables", "labelStyles", "nodeStyles", "styles2String", "cssStyles", "lineColor", "stateBorder", "nodeBorder", "shapeSvg", "rc", "at", "options", "userNodeOverrides", "roughNode", "innerFill", "roughInnerNode", "circle", "updateNodeBounds", "point", "intersect_default", "__name", "stateStart", "parent", "node", "themeVariables", "lineColor", "shapeSvg", "circle", "roughNode", "at", "solidStateFill", "updateNodeBounds", "point", "intersect_default", "__name", "subroutine", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "labelHelper", "getNodeClasses", "halfPadding", "w", "h", "x", "y", "points", "rc", "at", "options", "userNodeOverrides", "roughNode", "l1", "l2", "rect", "cssStyles", "handleUndefinedAttr", "updateNodeBounds", "el", "insertPolygonShape", "point", "intersect_default", "__name", "taggedRect", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "labelHelper", "getNodeClasses", "w", "h", "x", "y", "tagWidth", "tagHeight", "cssStyles", "rc", "at", "options", "userNodeOverrides", "rectPoints", "tagPoints", "rectPath", "createPathFromPoints", "rectNode", "tagPath", "tagNode", "updateNodeBounds", "point", "intersect_default", "__name", "taggedWaveEdgedRectangle", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "label", "labelHelper", "getNodeClasses", "w", "h", "waveAmplitude", "tagWidth", "tagHeight", "finalH", "cssStyles", "rc", "at", "options", "userNodeOverrides", "points", "generateFullSineWavePoints", "y", "tagPoints", "waveEdgeRectPath", "createPathFromPoints", "waveEdgeRectNode", "taggedWaveEdgeRectPath", "taggedWaveEdgeRectNode", "waveEdgeRect", "updateNodeBounds", "point", "intersect_default", "__name", "text", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "labelHelper", "getNodeClasses", "totalWidth", "totalHeight", "x", "y", "rect", "updateNodeBounds", "point", "intersect_default", "__name", "createCylinderPathD", "__name", "x", "y", "width", "height", "rx", "ry", "createOuterCylinderPathD", "createInnerCylinderPathD", "tilted<PERSON><PERSON><PERSON>", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "label", "halfPadding", "labelHelper", "getNodeClasses", "labelPadding", "h", "w", "cssStyles", "cylinder", "rc", "at", "outerPathData", "innerPathData", "outerNode", "userNodeOverrides", "innerLine", "pathData", "handleUndefinedAttr", "updateNodeBounds", "point", "pos", "intersect_default", "trapezoid", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "labelHelper", "getNodeClasses", "w", "h", "points", "polygon", "cssStyles", "rc", "at", "options", "userNodeOverrides", "pathData", "createPathFromPoints", "roughNode", "insertPolygonShape", "updateNodeBounds", "point", "intersect_default", "__name", "trapezoidalPentagon", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "labelHelper", "getNodeClasses", "min<PERSON><PERSON><PERSON>", "minHeight", "w", "h", "cssStyles", "rc", "at", "options", "userNodeOverrides", "points", "pathData", "createPathFromPoints", "shapeNode", "polygon", "updateNodeBounds", "point", "intersect_default", "__name", "triangle", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "label", "labelHelper", "getNodeClasses", "useHtmlLabels", "evaluate", "getConfig", "w", "h", "tw", "points", "cssStyles", "rc", "at", "options", "userNodeOverrides", "pathData", "createPathFromPoints", "roughNode", "polygon", "updateNodeBounds", "point", "log", "intersect_default", "__name", "waveEdgedRectangle", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "label", "labelHelper", "getNodeClasses", "w", "h", "waveAmplitude", "finalH", "cssStyles", "widthDif", "extraW", "rc", "at", "options", "userNodeOverrides", "points", "generateFullSineWavePoints", "waveEdgeRectPath", "createPathFromPoints", "waveEdgeRectNode", "waveEdgeRect", "updateNodeBounds", "point", "intersect_default", "__name", "waveRectangle", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "labelHelper", "getNodeClasses", "min<PERSON><PERSON><PERSON>", "minHeight", "baseWidth", "baseHeight", "aspectRatio", "w", "h", "waveAmplitude", "finalH", "cssStyles", "rc", "at", "options", "userNodeOverrides", "points", "generateFullSineWavePoints", "waveRectPath", "createPathFromPoints", "waveRectNode", "waveRect", "updateNodeBounds", "point", "intersect_default", "__name", "windowPane", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "shapeSvg", "bbox", "label", "labelHelper", "getNodeClasses", "w", "h", "rectOffset", "x", "y", "cssStyles", "rc", "at", "options", "userNodeOverrides", "outerPathPoints", "path", "no", "updateNodeBounds", "point", "intersect_default", "__name", "erBox", "parent", "node", "entityNode", "themeVariables", "getConfig", "background", "backgroundNode", "config", "PADDING", "TEXT_PADDING", "cssStyles", "labelStyles", "styles2String", "options", "calculateTextWidth", "shapeSvg", "drawRect", "evaluate", "textElement", "bbox", "cssClasses", "getNodeClasses", "nameBBox", "addText", "yOffset", "yOffsets", "maxType<PERSON>idth", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON>om<PERSON><PERSON><PERSON><PERSON>", "keysPresent", "commentPresent", "attribute", "typeBBox", "keysBBox", "commentBBox", "totalWidthSections", "shapeBBox", "difference", "max<PERSON><PERSON><PERSON>", "rc", "at", "userNodeOverrides", "w", "h", "x", "y", "_", "i", "nodes", "text", "select_default", "transform", "translateX", "translateY", "translate", "roughRect", "rect", "rowEven", "row<PERSON><PERSON>", "nodeBorder", "isEven", "roughLine", "updateNodeBounds", "point", "intersect_default", "__name", "labelText", "classes", "style", "label", "parseGenericTypes", "createText", "child", "div", "dv", "text<PERSON>elper", "parent", "node", "config", "useHtmlLabels", "GAP", "TEXT_PADDING", "shapeSvg", "getNodeClasses", "annotationGroup", "labelGroup", "membersGroup", "methodsGroup", "annotationGroupHeight", "labelGroupHeight", "membersGroupHeight", "annotation", "addText", "labelGroupBBox", "yOffset", "member", "height", "methodsYOffset", "method", "bbox", "annotationGroupBBox", "__name", "parentGroup", "styles", "textEl", "getConfig", "evaluate", "textContent", "hasKatex", "text", "createText", "sanitizeText", "decodeEntities", "calculateTextWidth", "numberOfLines", "div", "dv", "select_default", "images", "noImgText", "img", "res", "setupImage", "bodyFontSize", "width", "textChild", "classBox", "parent", "node", "config", "getConfig", "PADDING", "GAP", "useHtmlLabels", "evaluate", "classNode", "shapeSvg", "bbox", "text<PERSON>elper", "labelStyles", "nodeStyles", "styles2String", "styles", "renderExtraBox", "rc", "at", "options", "userNodeOverrides", "w", "h", "y", "roughRect", "rect", "rectBBox", "_", "i", "nodes", "text", "select_default", "transform", "translateY", "translate", "newTranslateY", "newTranslateX", "annotationGroupHeight", "labelGroupHeight", "membersGroupHeight", "roughLine", "colorRegex", "match", "colorStyle", "updateNodeBounds", "point", "intersect_default", "__name", "requirementBox", "parent", "node", "labelStyles", "nodeStyles", "styles2String", "requirementNode", "elementNode", "padding", "gap", "isRequirementNode", "classes", "getNodeClasses", "shapeSvg", "typeHeight", "addText", "accumulativeHeight", "nameHeight", "idHeight", "textHeight", "riskHeight", "totalWidth", "totalHeight", "x", "y", "rc", "at", "options", "userNodeOverrides", "roughRect", "rect", "_", "i", "nodes", "text", "select_default", "transform", "translateX", "translateY", "translate", "newTranslateY", "newTranslateX", "roughLine", "updateNodeBounds", "point", "intersect_default", "__name", "parentGroup", "inputText", "yOffset", "style", "textEl", "config", "getConfig", "useHtmlLabels", "createText", "sanitizeText", "decodeEntities", "calculateTextWidth", "bbox", "div", "dv", "textChild", "child", "colorFromPriority", "__name", "priority", "kanbanItem", "parent", "kanbanNode", "config", "labelStyles", "nodeStyles", "styles2String", "labelPaddingX", "orgWidth", "shapeSvg", "bbox", "labelElTitle", "labelHelper", "getNodeClasses", "padding", "ticketUrl", "link", "options", "labelEl", "bbox2", "insertLabel", "labelElAssigned", "bboxAssigned", "labelPaddingY", "totalWidth", "heightAdj", "totalHeight", "x", "y", "rect", "rx", "ry", "cssStyles", "rc", "at", "userNodeOverrides", "roughNode", "createRoundedRectPathD", "line", "lineX", "y1", "y2", "updateNodeBounds", "point", "intersect_default", "shapesDefs", "squareRect", "roundedRect", "stadium", "subroutine", "cylinder", "circle", "question", "hexagon", "lean_right", "lean_left", "trapezoid", "inv_trapezoid", "doublecircle", "text", "card", "shadedProcess", "stateStart", "stateEnd", "fork<PERSON><PERSON>n", "hourglass", "curlyBraceLeft", "curlyBraceRight", "curlyBraces", "lightningBolt", "waveEdgedRectangle", "halfRoundedRectangle", "tilted<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "curvedTrapezoid", "dividedRectangle", "triangle", "windowPane", "filledCircle", "trapezoidalPentagon", "flippedTriangle", "slopedRect", "multiWaveEdgedRectangle", "multiRect", "bowTieRect", "crossedCircle", "taggedWaveEdgedRectangle", "taggedRect", "waveRectangle", "rect_left_inv_arrow", "linedWaveEdgedRect", "generateShapeMap", "__name", "entries", "state", "choice", "note", "rectWithTitle", "labelRect", "iconSquare", "iconCircle", "icon", "iconRounded", "imageSquare", "anchor", "kanbanItem", "classBox", "erBox", "requirementBox", "shape", "alias", "shapes", "isValidShape", "nodeElems", "insertNode", "elem", "node", "renderOptions", "newEl", "el", "<PERSON><PERSON><PERSON><PERSON>", "shapes", "target", "__name", "setNodeElem", "clear", "positionNode", "log", "padding", "diff"]}