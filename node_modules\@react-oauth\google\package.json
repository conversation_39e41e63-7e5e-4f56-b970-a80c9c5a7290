{"name": "@react-oauth/google", "version": "0.12.1", "description": "Google OAuth2 using Google Identity Services for React 🚀", "main": "./dist/index.js", "module": "./dist/index.esm.js", "types": "./dist/index.d.ts", "files": ["dist"], "license": "MIT", "scripts": {"dev": "rollup -c -w", "build": "rollup -c", "prerelease": "npm run build"}, "publishConfig": {"access": "public"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/MomenSherif"}, "homepage": "https://github.com/MomenSherif/react-oauth", "repository": {"type": "git", "url": "https://github.com/MomenSherif/react-oauth"}, "bugs": {"url": "https://github.com/MomenSherif/react-oauth/issues", "email": "<EMAIL>"}, "keywords": ["react", "reactjs", "react-component", "react-oauth-google", "react-google-login", "react-social-login", "react-oauth", "react-login", "google-login", "google-oAuth2", "google-oAuth"], "devDependencies": {"@types/react": "^18.0.8", "cross-env": "^7.0.3", "rollup": "^2.71.1", "typescript": "^4.6.4"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}}