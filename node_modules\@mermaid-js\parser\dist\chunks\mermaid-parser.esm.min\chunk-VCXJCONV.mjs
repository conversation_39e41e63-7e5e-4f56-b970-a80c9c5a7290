import{D as s,E as c,L as m,M as f,a as e,b as a,c as d,d as n,e as u,f as l}from"./chunk-U22FQTB5.mjs";var r=class extends f{static{e(this,"InfoTokenBuilder")}constructor(){super(["info","showInfo"])}};var S={parser:{TokenBuilder:e(()=>new r,"TokenBuilder"),ValueConverter:e(()=>new m,"ValueConverter")}};function x(t=u){let o=n(d(t),s),i=n(a({shared:o}),c,S);return o.ServiceRegistry.register(i),{shared:o,Info:i}}e(x,"createInfoServices");export{S as a,x as b};
