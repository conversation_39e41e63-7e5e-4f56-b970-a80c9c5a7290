{"version": 3, "sources": ["../../../src/rendering-util/rendering-elements/edges.js", "../../../src/rendering-util/rendering-elements/edgeMarker.ts", "../../../src/rendering-util/rendering-elements/markers.js"], "sourcesContent": ["import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { evaluate } from '../../diagrams/common/common.js';\nimport { log } from '../../logger.js';\nimport { createText } from '../createText.js';\nimport utils from '../../utils.js';\nimport { getLineFunctionsWithOffset } from '../../utils/lineWithOffset.js';\nimport { getSubGraphTitleMargins } from '../../utils/subGraphTitleMargins.js';\n\nimport {\n  curveBasis,\n  curveLinear,\n  curveCardinal,\n  curveBumpX,\n  curveBumpY,\n  curveCatmullRom,\n  curveMonotoneX,\n  curveMonotoneY,\n  curveNatural,\n  curveStep,\n  curveStepAfter,\n  curveStepBefore,\n  line,\n  select,\n} from 'd3';\nimport rough from 'roughjs';\nimport createLabel from './createLabel.js';\nimport { addEdgeMarkers } from './edgeMarker.ts';\nimport { isLabelStyle } from './shapes/handDrawnShapeStyles.js';\n\nconst edgeLabels = new Map();\nconst terminalLabels = new Map();\n\nexport const clear = () => {\n  edgeLabels.clear();\n  terminalLabels.clear();\n};\n\nexport const getLabelStyles = (styleArray) => {\n  let styles = styleArray ? styleArray.reduce((acc, style) => acc + ';' + style, '') : '';\n  return styles;\n};\n\nexport const insertEdgeLabel = async (elem, edge) => {\n  let useHtmlLabels = evaluate(getConfig().flowchart.htmlLabels);\n\n  const labelElement = await createText(elem, edge.label, {\n    style: getLabelStyles(edge.labelStyle),\n    useHtmlLabels,\n    addSvgBackground: true,\n    isNode: false,\n  });\n  log.info('abc82', edge, edge.labelType);\n\n  // Create outer g, edgeLabel, this will be positioned after graph layout\n  const edgeLabel = elem.insert('g').attr('class', 'edgeLabel');\n\n  // Create inner g, label, this will be positioned now for centering the text\n  const label = edgeLabel.insert('g').attr('class', 'label');\n  label.node().appendChild(labelElement);\n\n  // Center the label\n  let bbox = labelElement.getBBox();\n  if (useHtmlLabels) {\n    const div = labelElement.children[0];\n    const dv = select(labelElement);\n    bbox = div.getBoundingClientRect();\n    dv.attr('width', bbox.width);\n    dv.attr('height', bbox.height);\n  }\n  label.attr('transform', 'translate(' + -bbox.width / 2 + ', ' + -bbox.height / 2 + ')');\n\n  // Make element accessible by id for positioning\n  edgeLabels.set(edge.id, edgeLabel);\n\n  // Update the abstract data of the edge with the new information about its width and height\n  edge.width = bbox.width;\n  edge.height = bbox.height;\n\n  let fo;\n  if (edge.startLabelLeft) {\n    // Create the actual text element\n    const startLabelElement = await createLabel(\n      edge.startLabelLeft,\n      getLabelStyles(edge.labelStyle)\n    );\n    const startEdgeLabelLeft = elem.insert('g').attr('class', 'edgeTerminals');\n    const inner = startEdgeLabelLeft.insert('g').attr('class', 'inner');\n    fo = inner.node().appendChild(startLabelElement);\n    const slBox = startLabelElement.getBBox();\n    inner.attr('transform', 'translate(' + -slBox.width / 2 + ', ' + -slBox.height / 2 + ')');\n    if (!terminalLabels.get(edge.id)) {\n      terminalLabels.set(edge.id, {});\n    }\n    terminalLabels.get(edge.id).startLeft = startEdgeLabelLeft;\n    setTerminalWidth(fo, edge.startLabelLeft);\n  }\n  if (edge.startLabelRight) {\n    // Create the actual text element\n    const startLabelElement = await createLabel(\n      edge.startLabelRight,\n      getLabelStyles(edge.labelStyle)\n    );\n    const startEdgeLabelRight = elem.insert('g').attr('class', 'edgeTerminals');\n    const inner = startEdgeLabelRight.insert('g').attr('class', 'inner');\n    fo = startEdgeLabelRight.node().appendChild(startLabelElement);\n    inner.node().appendChild(startLabelElement);\n    const slBox = startLabelElement.getBBox();\n    inner.attr('transform', 'translate(' + -slBox.width / 2 + ', ' + -slBox.height / 2 + ')');\n\n    if (!terminalLabels.get(edge.id)) {\n      terminalLabels.set(edge.id, {});\n    }\n    terminalLabels.get(edge.id).startRight = startEdgeLabelRight;\n    setTerminalWidth(fo, edge.startLabelRight);\n  }\n  if (edge.endLabelLeft) {\n    // Create the actual text element\n    const endLabelElement = await createLabel(edge.endLabelLeft, getLabelStyles(edge.labelStyle));\n    const endEdgeLabelLeft = elem.insert('g').attr('class', 'edgeTerminals');\n    const inner = endEdgeLabelLeft.insert('g').attr('class', 'inner');\n    fo = inner.node().appendChild(endLabelElement);\n    const slBox = endLabelElement.getBBox();\n    inner.attr('transform', 'translate(' + -slBox.width / 2 + ', ' + -slBox.height / 2 + ')');\n\n    endEdgeLabelLeft.node().appendChild(endLabelElement);\n\n    if (!terminalLabels.get(edge.id)) {\n      terminalLabels.set(edge.id, {});\n    }\n    terminalLabels.get(edge.id).endLeft = endEdgeLabelLeft;\n    setTerminalWidth(fo, edge.endLabelLeft);\n  }\n  if (edge.endLabelRight) {\n    // Create the actual text element\n    const endLabelElement = await createLabel(edge.endLabelRight, getLabelStyles(edge.labelStyle));\n    const endEdgeLabelRight = elem.insert('g').attr('class', 'edgeTerminals');\n    const inner = endEdgeLabelRight.insert('g').attr('class', 'inner');\n\n    fo = inner.node().appendChild(endLabelElement);\n    const slBox = endLabelElement.getBBox();\n    inner.attr('transform', 'translate(' + -slBox.width / 2 + ', ' + -slBox.height / 2 + ')');\n\n    endEdgeLabelRight.node().appendChild(endLabelElement);\n    if (!terminalLabels.get(edge.id)) {\n      terminalLabels.set(edge.id, {});\n    }\n    terminalLabels.get(edge.id).endRight = endEdgeLabelRight;\n    setTerminalWidth(fo, edge.endLabelRight);\n  }\n  return labelElement;\n};\n\n/**\n * @param {any} fo\n * @param {any} value\n */\nfunction setTerminalWidth(fo, value) {\n  if (getConfig().flowchart.htmlLabels && fo) {\n    fo.style.width = value.length * 9 + 'px';\n    fo.style.height = '12px';\n  }\n}\n\nexport const positionEdgeLabel = (edge, paths) => {\n  log.debug('Moving label abc88 ', edge.id, edge.label, edgeLabels.get(edge.id), paths);\n  let path = paths.updatedPath ? paths.updatedPath : paths.originalPath;\n  const siteConfig = getConfig();\n  const { subGraphTitleTotalMargin } = getSubGraphTitleMargins(siteConfig);\n  if (edge.label) {\n    const el = edgeLabels.get(edge.id);\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      const pos = utils.calcLabelPosition(path);\n      log.debug(\n        'Moving label ' + edge.label + ' from (',\n        x,\n        ',',\n        y,\n        ') to (',\n        pos.x,\n        ',',\n        pos.y,\n        ') abc88'\n      );\n      if (paths.updatedPath) {\n        x = pos.x;\n        y = pos.y;\n      }\n    }\n    el.attr('transform', `translate(${x}, ${y + subGraphTitleTotalMargin / 2})`);\n  }\n\n  if (edge.startLabelLeft) {\n    const el = terminalLabels.get(edge.id).startLeft;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      const pos = utils.calcTerminalLabelPosition(edge.arrowTypeStart ? 10 : 0, 'start_left', path);\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr('transform', `translate(${x}, ${y})`);\n  }\n  if (edge.startLabelRight) {\n    const el = terminalLabels.get(edge.id).startRight;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      const pos = utils.calcTerminalLabelPosition(\n        edge.arrowTypeStart ? 10 : 0,\n        'start_right',\n        path\n      );\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr('transform', `translate(${x}, ${y})`);\n  }\n  if (edge.endLabelLeft) {\n    const el = terminalLabels.get(edge.id).endLeft;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      const pos = utils.calcTerminalLabelPosition(edge.arrowTypeEnd ? 10 : 0, 'end_left', path);\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr('transform', `translate(${x}, ${y})`);\n  }\n  if (edge.endLabelRight) {\n    const el = terminalLabels.get(edge.id).endRight;\n    let x = edge.x;\n    let y = edge.y;\n    if (path) {\n      const pos = utils.calcTerminalLabelPosition(edge.arrowTypeEnd ? 10 : 0, 'end_right', path);\n      x = pos.x;\n      y = pos.y;\n    }\n    el.attr('transform', `translate(${x}, ${y})`);\n  }\n};\n\nconst outsideNode = (node, point) => {\n  const x = node.x;\n  const y = node.y;\n  const dx = Math.abs(point.x - x);\n  const dy = Math.abs(point.y - y);\n  const w = node.width / 2;\n  const h = node.height / 2;\n  return dx >= w || dy >= h;\n};\n\nexport const intersection = (node, outsidePoint, insidePoint) => {\n  log.debug(`intersection calc abc89:\n  outsidePoint: ${JSON.stringify(outsidePoint)}\n  insidePoint : ${JSON.stringify(insidePoint)}\n  node        : x:${node.x} y:${node.y} w:${node.width} h:${node.height}`);\n  const x = node.x;\n  const y = node.y;\n\n  const dx = Math.abs(x - insidePoint.x);\n  const w = node.width / 2;\n  let r = insidePoint.x < outsidePoint.x ? w - dx : w + dx;\n  const h = node.height / 2;\n\n  const Q = Math.abs(outsidePoint.y - insidePoint.y);\n  const R = Math.abs(outsidePoint.x - insidePoint.x);\n\n  if (Math.abs(y - outsidePoint.y) * w > Math.abs(x - outsidePoint.x) * h) {\n    // Intersection is top or bottom of rect.\n    let q = insidePoint.y < outsidePoint.y ? outsidePoint.y - h - y : y - h - outsidePoint.y;\n    r = (R * q) / Q;\n    const res = {\n      x: insidePoint.x < outsidePoint.x ? insidePoint.x + r : insidePoint.x - R + r,\n      y: insidePoint.y < outsidePoint.y ? insidePoint.y + Q - q : insidePoint.y - Q + q,\n    };\n\n    if (r === 0) {\n      res.x = outsidePoint.x;\n      res.y = outsidePoint.y;\n    }\n    if (R === 0) {\n      res.x = outsidePoint.x;\n    }\n    if (Q === 0) {\n      res.y = outsidePoint.y;\n    }\n\n    log.debug(`abc89 top/bottom calc, Q ${Q}, q ${q}, R ${R}, r ${r}`, res);\n\n    return res;\n  } else {\n    // Intersection on sides of rect\n    if (insidePoint.x < outsidePoint.x) {\n      r = outsidePoint.x - w - x;\n    } else {\n      r = x - w - outsidePoint.x;\n    }\n    let q = (Q * r) / R;\n    let _x = insidePoint.x < outsidePoint.x ? insidePoint.x + R - r : insidePoint.x - R + r;\n    let _y = insidePoint.y < outsidePoint.y ? insidePoint.y + q : insidePoint.y - q;\n    log.debug(`sides calc abc89, Q ${Q}, q ${q}, R ${R}, r ${r}`, { _x, _y });\n    if (r === 0) {\n      _x = outsidePoint.x;\n      _y = outsidePoint.y;\n    }\n    if (R === 0) {\n      _x = outsidePoint.x;\n    }\n    if (Q === 0) {\n      _y = outsidePoint.y;\n    }\n\n    return { x: _x, y: _y };\n  }\n};\n\nconst cutPathAtIntersect = (_points, boundaryNode) => {\n  log.warn('abc88 cutPathAtIntersect', _points, boundaryNode);\n  let points = [];\n  let lastPointOutside = _points[0];\n  let isInside = false;\n  _points.forEach((point) => {\n    log.info('abc88 checking point', point, boundaryNode);\n\n    if (!outsideNode(boundaryNode, point) && !isInside) {\n      const inter = intersection(boundaryNode, lastPointOutside, point);\n      log.debug('abc88 inside', point, lastPointOutside, inter);\n      log.debug('abc88 intersection', inter, boundaryNode);\n\n      let pointPresent = false;\n      points.forEach((p) => {\n        pointPresent = pointPresent || (p.x === inter.x && p.y === inter.y);\n      });\n\n      if (!points.some((e) => e.x === inter.x && e.y === inter.y)) {\n        points.push(inter);\n      } else {\n        log.warn('abc88 no intersect', inter, points);\n      }\n      isInside = true;\n    } else {\n      log.warn('abc88 outside', point, lastPointOutside);\n      lastPointOutside = point;\n      if (!isInside) {\n        points.push(point);\n      }\n    }\n  });\n  log.debug('returning points', points);\n  return points;\n};\n\nfunction extractCornerPoints(points) {\n  const cornerPoints = [];\n  const cornerPointPositions = [];\n  for (let i = 1; i < points.length - 1; i++) {\n    const prev = points[i - 1];\n    const curr = points[i];\n    const next = points[i + 1];\n    if (\n      prev.x === curr.x &&\n      curr.y === next.y &&\n      Math.abs(curr.x - next.x) > 5 &&\n      Math.abs(curr.y - prev.y) > 5\n    ) {\n      cornerPoints.push(curr);\n      cornerPointPositions.push(i);\n    } else if (\n      prev.y === curr.y &&\n      curr.x === next.x &&\n      Math.abs(curr.x - prev.x) > 5 &&\n      Math.abs(curr.y - next.y) > 5\n    ) {\n      cornerPoints.push(curr);\n      cornerPointPositions.push(i);\n    }\n  }\n  return { cornerPoints, cornerPointPositions };\n}\n\nconst findAdjacentPoint = function (pointA, pointB, distance) {\n  const xDiff = pointB.x - pointA.x;\n  const yDiff = pointB.y - pointA.y;\n  const length = Math.sqrt(xDiff * xDiff + yDiff * yDiff);\n  const ratio = distance / length;\n  return { x: pointB.x - ratio * xDiff, y: pointB.y - ratio * yDiff };\n};\n\nconst fixCorners = function (lineData) {\n  const { cornerPointPositions } = extractCornerPoints(lineData);\n  const newLineData = [];\n  for (let i = 0; i < lineData.length; i++) {\n    if (cornerPointPositions.includes(i)) {\n      const prevPoint = lineData[i - 1];\n      const nextPoint = lineData[i + 1];\n      const cornerPoint = lineData[i];\n\n      const newPrevPoint = findAdjacentPoint(prevPoint, cornerPoint, 5);\n      const newNextPoint = findAdjacentPoint(nextPoint, cornerPoint, 5);\n\n      const xDiff = newNextPoint.x - newPrevPoint.x;\n      const yDiff = newNextPoint.y - newPrevPoint.y;\n      newLineData.push(newPrevPoint);\n\n      const a = Math.sqrt(2) * 2;\n      let newCornerPoint = { x: cornerPoint.x, y: cornerPoint.y };\n      if (Math.abs(nextPoint.x - prevPoint.x) > 10 && Math.abs(nextPoint.y - prevPoint.y) >= 10) {\n        log.debug(\n          'Corner point fixing',\n          Math.abs(nextPoint.x - prevPoint.x),\n          Math.abs(nextPoint.y - prevPoint.y)\n        );\n        const r = 5;\n        if (cornerPoint.x === newPrevPoint.x) {\n          newCornerPoint = {\n            x: xDiff < 0 ? newPrevPoint.x - r + a : newPrevPoint.x + r - a,\n            y: yDiff < 0 ? newPrevPoint.y - a : newPrevPoint.y + a,\n          };\n        } else {\n          newCornerPoint = {\n            x: xDiff < 0 ? newPrevPoint.x - a : newPrevPoint.x + a,\n            y: yDiff < 0 ? newPrevPoint.y - r + a : newPrevPoint.y + r - a,\n          };\n        }\n      } else {\n        log.debug(\n          'Corner point skipping fixing',\n          Math.abs(nextPoint.x - prevPoint.x),\n          Math.abs(nextPoint.y - prevPoint.y)\n        );\n      }\n      newLineData.push(newCornerPoint, newNextPoint);\n    } else {\n      newLineData.push(lineData[i]);\n    }\n  }\n  return newLineData;\n};\n\nexport const insertEdge = function (elem, edge, clusterDb, diagramType, startNode, endNode, id) {\n  const { handDrawnSeed } = getConfig();\n  let points = edge.points;\n  let pointsHasChanged = false;\n  const tail = startNode;\n  var head = endNode;\n  const edgeClassStyles = [];\n  for (const key in edge.cssCompiledStyles) {\n    if (isLabelStyle(key)) {\n      continue;\n    }\n    edgeClassStyles.push(edge.cssCompiledStyles[key]);\n  }\n\n  if (head.intersect && tail.intersect) {\n    points = points.slice(1, edge.points.length - 1);\n    points.unshift(tail.intersect(points[0]));\n    log.debug(\n      'Last point APA12',\n      edge.start,\n      '-->',\n      edge.end,\n      points[points.length - 1],\n      head,\n      head.intersect(points[points.length - 1])\n    );\n    points.push(head.intersect(points[points.length - 1]));\n  }\n  if (edge.toCluster) {\n    log.info('to cluster abc88', clusterDb.get(edge.toCluster));\n    points = cutPathAtIntersect(edge.points, clusterDb.get(edge.toCluster).node);\n\n    pointsHasChanged = true;\n  }\n\n  if (edge.fromCluster) {\n    log.debug(\n      'from cluster abc88',\n      clusterDb.get(edge.fromCluster),\n      JSON.stringify(points, null, 2)\n    );\n    points = cutPathAtIntersect(points.reverse(), clusterDb.get(edge.fromCluster).node).reverse();\n\n    pointsHasChanged = true;\n  }\n\n  let lineData = points.filter((p) => !Number.isNaN(p.y));\n  lineData = fixCorners(lineData);\n  let curve = curveBasis;\n  curve = curveLinear;\n  switch (edge.curve) {\n    case 'linear':\n      curve = curveLinear;\n      break;\n    case 'basis':\n      curve = curveBasis;\n      break;\n    case 'cardinal':\n      curve = curveCardinal;\n      break;\n    case 'bumpX':\n      curve = curveBumpX;\n      break;\n    case 'bumpY':\n      curve = curveBumpY;\n      break;\n    case 'catmullRom':\n      curve = curveCatmullRom;\n      break;\n    case 'monotoneX':\n      curve = curveMonotoneX;\n      break;\n    case 'monotoneY':\n      curve = curveMonotoneY;\n      break;\n    case 'natural':\n      curve = curveNatural;\n      break;\n    case 'step':\n      curve = curveStep;\n      break;\n    case 'stepAfter':\n      curve = curveStepAfter;\n      break;\n    case 'stepBefore':\n      curve = curveStepBefore;\n      break;\n    default:\n      curve = curveBasis;\n  }\n\n  const { x, y } = getLineFunctionsWithOffset(edge);\n  const lineFunction = line().x(x).y(y).curve(curve);\n\n  let strokeClasses;\n  switch (edge.thickness) {\n    case 'normal':\n      strokeClasses = 'edge-thickness-normal';\n      break;\n    case 'thick':\n      strokeClasses = 'edge-thickness-thick';\n      break;\n    case 'invisible':\n      strokeClasses = 'edge-thickness-invisible';\n      break;\n    default:\n      strokeClasses = 'edge-thickness-normal';\n  }\n  switch (edge.pattern) {\n    case 'solid':\n      strokeClasses += ' edge-pattern-solid';\n      break;\n    case 'dotted':\n      strokeClasses += ' edge-pattern-dotted';\n      break;\n    case 'dashed':\n      strokeClasses += ' edge-pattern-dashed';\n      break;\n    default:\n      strokeClasses += ' edge-pattern-solid';\n  }\n  let svgPath;\n  let linePath = lineFunction(lineData);\n  const edgeStyles = Array.isArray(edge.style) ? edge.style : [edge.style];\n  let strokeColor = edgeStyles.find((style) => style?.startsWith('stroke:'));\n\n  if (edge.look === 'handDrawn') {\n    const rc = rough.svg(elem);\n    Object.assign([], lineData);\n\n    const svgPathNode = rc.path(linePath, {\n      roughness: 0.3,\n      seed: handDrawnSeed,\n    });\n\n    strokeClasses += ' transition';\n\n    svgPath = select(svgPathNode)\n      .select('path')\n      .attr('id', edge.id)\n      .attr('class', ' ' + strokeClasses + (edge.classes ? ' ' + edge.classes : ''))\n      .attr('style', edgeStyles ? edgeStyles.reduce((acc, style) => acc + ';' + style, '') : '');\n    let d = svgPath.attr('d');\n    svgPath.attr('d', d);\n    elem.node().appendChild(svgPath.node());\n  } else {\n    const stylesFromClasses = edgeClassStyles.join(';');\n    const styles = edgeStyles ? edgeStyles.reduce((acc, style) => acc + style + ';', '') : '';\n    let animationClass = '';\n    if (edge.animate) {\n      animationClass = ' edge-animation-fast';\n    }\n    if (edge.animation) {\n      animationClass = ' edge-animation-' + edge.animation;\n    }\n\n    const pathStyle = stylesFromClasses ? stylesFromClasses + ';' + styles + ';' : styles;\n    svgPath = elem\n      .append('path')\n      .attr('d', linePath)\n      .attr('id', edge.id)\n      .attr(\n        'class',\n        ' ' + strokeClasses + (edge.classes ? ' ' + edge.classes : '') + (animationClass ?? '')\n      )\n      .attr('style', pathStyle);\n    strokeColor = pathStyle.match(/stroke:([^;]+)/)?.[1];\n  }\n\n  // DEBUG code, DO NOT REMOVE\n  // adds a red circle at each edge coordinate\n  // cornerPoints.forEach((point) => {\n  //   elem\n  //     .append('circle')\n  //     .style('stroke', 'blue')\n  //     .style('fill', 'blue')\n  //     .attr('r', 3)\n  //     .attr('cx', point.x)\n  //     .attr('cy', point.y);\n  // });\n  // lineData.forEach((point) => {\n  //   elem\n  //     .append('circle')\n  //     .style('stroke', 'blue')\n  //     .style('fill', 'blue')\n  //     .attr('r', 3)\n  //     .attr('cx', point.x)\n  //     .attr('cy', point.y);\n  // });\n\n  let url = '';\n  if (getConfig().flowchart.arrowMarkerAbsolute || getConfig().state.arrowMarkerAbsolute) {\n    url =\n      window.location.protocol +\n      '//' +\n      window.location.host +\n      window.location.pathname +\n      window.location.search;\n    url = url.replace(/\\(/g, '\\\\(').replace(/\\)/g, '\\\\)');\n  }\n  log.info('arrowTypeStart', edge.arrowTypeStart);\n  log.info('arrowTypeEnd', edge.arrowTypeEnd);\n\n  addEdgeMarkers(svgPath, edge, url, id, diagramType, strokeColor);\n\n  let paths = {};\n  if (pointsHasChanged) {\n    paths.updatedPath = points;\n  }\n  paths.originalPath = edge.points;\n  return paths;\n};\n", "import type { SVG } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport type { EdgeData } from '../../types.js';\n/**\n * Adds SVG markers to a path element based on the arrow types specified in the edge.\n *\n * @param svgPath - The SVG path element to add markers to.\n * @param edge - The edge data object containing the arrow types.\n * @param url - The URL of the SVG marker definitions.\n * @param id - The ID prefix for the SVG marker definitions.\n * @param diagramType - The type of diagram being rendered.\n */\nexport const addEdgeMarkers = (\n  svgPath: SVG,\n  edge: Pick<EdgeData, 'arrowTypeStart' | 'arrowTypeEnd'>,\n  url: string,\n  id: string,\n  diagramType: string,\n  strokeColor?: string\n) => {\n  if (edge.arrowTypeStart) {\n    addEdgeMarker(svgPath, 'start', edge.arrowTypeStart, url, id, diagramType, strokeColor);\n  }\n  if (edge.arrowTypeEnd) {\n    addEdgeMarker(svgPath, 'end', edge.arrowTypeEnd, url, id, diagramType, strokeColor);\n  }\n};\n\nconst arrowTypesMap = {\n  arrow_cross: { type: 'cross', fill: false },\n  arrow_point: { type: 'point', fill: true },\n  arrow_barb: { type: 'barb', fill: true },\n  arrow_circle: { type: 'circle', fill: false },\n  aggregation: { type: 'aggregation', fill: false },\n  extension: { type: 'extension', fill: false },\n  composition: { type: 'composition', fill: true },\n  dependency: { type: 'dependency', fill: true },\n  lollipop: { type: 'lollipop', fill: false },\n  only_one: { type: 'onlyOne', fill: false },\n  zero_or_one: { type: 'zeroOrOne', fill: false },\n  one_or_more: { type: 'oneOrMore', fill: false },\n  zero_or_more: { type: 'zeroOrMore', fill: false },\n  requirement_arrow: { type: 'requirement_arrow', fill: false },\n  requirement_contains: { type: 'requirement_contains', fill: false },\n} as const;\n\nconst addEdgeMarker = (\n  svgPath: SVG,\n  position: 'start' | 'end',\n  arrowType: string,\n  url: string,\n  id: string,\n  diagramType: string,\n  strokeColor?: string\n) => {\n  const arrowTypeInfo = arrowTypesMap[arrowType as keyof typeof arrowTypesMap];\n\n  if (!arrowTypeInfo) {\n    log.warn(`Unknown arrow type: ${arrowType}`);\n    return; // unknown arrow type, ignore\n  }\n\n  const endMarkerType = arrowTypeInfo.type;\n  const suffix = position === 'start' ? 'Start' : 'End';\n  const originalMarkerId = `${id}_${diagramType}-${endMarkerType}${suffix}`;\n\n  // If stroke color is specified and non-empty, create or use a colored variant of the marker\n  if (strokeColor && strokeColor.trim() !== '') {\n    // Create a sanitized color value for use in IDs\n    const colorId = strokeColor.replace(/[^\\dA-Za-z]/g, '_');\n    const coloredMarkerId = `${originalMarkerId}_${colorId}`;\n\n    // Check if the colored marker already exists\n    if (!document.getElementById(coloredMarkerId)) {\n      // Get the original marker\n      const originalMarker = document.getElementById(originalMarkerId);\n      if (originalMarker) {\n        // Clone the marker and create colored version\n        const coloredMarker = originalMarker.cloneNode(true) as Element;\n        coloredMarker.id = coloredMarkerId;\n\n        // Apply colors to the paths inside the marker\n        const paths = coloredMarker.querySelectorAll('path, circle, line');\n        paths.forEach((path) => {\n          path.setAttribute('stroke', strokeColor);\n\n          // Apply fill only to markers that should be filled\n          if (arrowTypeInfo.fill) {\n            path.setAttribute('fill', strokeColor);\n          }\n        });\n\n        // Add the new colored marker to the defs section\n        originalMarker.parentNode?.appendChild(coloredMarker);\n      }\n    }\n\n    // Use the colored marker\n    svgPath.attr(`marker-${position}`, `url(${url}#${coloredMarkerId})`);\n  } else {\n    // Always use the original marker for unstyled edges\n    svgPath.attr(`marker-${position}`, `url(${url}#${originalMarkerId})`);\n  }\n};\n", "/** Setup arrow head and define the marker. The result is appended to the svg. */\nimport { log } from '../../logger.js';\n\n// Only add the number of markers that the diagram needs\nconst insertMarkers = (elem, markerArray, type, id) => {\n  markerArray.forEach((markerName) => {\n    markers[markerName](elem, type, id);\n  });\n};\n\nconst extension = (elem, type, id) => {\n  log.trace('Making markers for ', id);\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-extensionStart')\n    .attr('class', 'marker extension ' + type)\n    .attr('refX', 18)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 1,7 L18,13 V 1 Z');\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-extensionEnd')\n    .attr('class', 'marker extension ' + type)\n    .attr('refX', 1)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 1,1 V 13 L18,7 Z'); // this is actual shape for arrowhead\n};\n\nconst composition = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-compositionStart')\n    .attr('class', 'marker composition ' + type)\n    .attr('refX', 18)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L1,7 L9,1 Z');\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-compositionEnd')\n    .attr('class', 'marker composition ' + type)\n    .attr('refX', 1)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L1,7 L9,1 Z');\n};\nconst aggregation = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-aggregationStart')\n    .attr('class', 'marker aggregation ' + type)\n    .attr('refX', 18)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L1,7 L9,1 Z');\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-aggregationEnd')\n    .attr('class', 'marker aggregation ' + type)\n    .attr('refX', 1)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L1,7 L9,1 Z');\n};\nconst dependency = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-dependencyStart')\n    .attr('class', 'marker dependency ' + type)\n    .attr('refX', 6)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 5,7 L9,13 L1,7 L9,1 Z');\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-dependencyEnd')\n    .attr('class', 'marker dependency ' + type)\n    .attr('refX', 13)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L14,7 L9,1 Z');\n};\nconst lollipop = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-lollipopStart')\n    .attr('class', 'marker lollipop ' + type)\n    .attr('refX', 13)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('circle')\n    .attr('stroke', 'black')\n    .attr('fill', 'transparent')\n    .attr('cx', 7)\n    .attr('cy', 7)\n    .attr('r', 6);\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-lollipopEnd')\n    .attr('class', 'marker lollipop ' + type)\n    .attr('refX', 1)\n    .attr('refY', 7)\n    .attr('markerWidth', 190)\n    .attr('markerHeight', 240)\n    .attr('orient', 'auto')\n    .append('circle')\n    .attr('stroke', 'black')\n    .attr('fill', 'transparent')\n    .attr('cx', 7)\n    .attr('cy', 7)\n    .attr('r', 6);\n};\nconst point = (elem, type, id) => {\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-pointEnd')\n    .attr('class', 'marker ' + type)\n    .attr('viewBox', '0 0 10 10')\n    .attr('refX', 5)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 8)\n    .attr('markerHeight', 8)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 0 0 L 10 5 L 0 10 z')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 1)\n    .style('stroke-dasharray', '1,0');\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-pointStart')\n    .attr('class', 'marker ' + type)\n    .attr('viewBox', '0 0 10 10')\n    .attr('refX', 4.5)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 8)\n    .attr('markerHeight', 8)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 0 5 L 10 10 L 10 0 z')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 1)\n    .style('stroke-dasharray', '1,0');\n};\nconst circle = (elem, type, id) => {\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-circleEnd')\n    .attr('class', 'marker ' + type)\n    .attr('viewBox', '0 0 10 10')\n    .attr('refX', 11)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 11)\n    .attr('markerHeight', 11)\n    .attr('orient', 'auto')\n    .append('circle')\n    .attr('cx', '5')\n    .attr('cy', '5')\n    .attr('r', '5')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 1)\n    .style('stroke-dasharray', '1,0');\n\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-circleStart')\n    .attr('class', 'marker ' + type)\n    .attr('viewBox', '0 0 10 10')\n    .attr('refX', -1)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 11)\n    .attr('markerHeight', 11)\n    .attr('orient', 'auto')\n    .append('circle')\n    .attr('cx', '5')\n    .attr('cy', '5')\n    .attr('r', '5')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 1)\n    .style('stroke-dasharray', '1,0');\n};\nconst cross = (elem, type, id) => {\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-crossEnd')\n    .attr('class', 'marker cross ' + type)\n    .attr('viewBox', '0 0 11 11')\n    .attr('refX', 12)\n    .attr('refY', 5.2)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 11)\n    .attr('markerHeight', 11)\n    .attr('orient', 'auto')\n    .append('path')\n    // .attr('stroke', 'black')\n    .attr('d', 'M 1,1 l 9,9 M 10,1 l -9,9')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 2)\n    .style('stroke-dasharray', '1,0');\n\n  elem\n    .append('marker')\n    .attr('id', id + '_' + type + '-crossStart')\n    .attr('class', 'marker cross ' + type)\n    .attr('viewBox', '0 0 11 11')\n    .attr('refX', -1)\n    .attr('refY', 5.2)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 11)\n    .attr('markerHeight', 11)\n    .attr('orient', 'auto')\n    .append('path')\n    // .attr('stroke', 'black')\n    .attr('d', 'M 1,1 l 9,9 M 10,1 l -9,9')\n    .attr('class', 'arrowMarkerPath')\n    .style('stroke-width', 2)\n    .style('stroke-dasharray', '1,0');\n};\nconst barb = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-barbEnd')\n    .attr('refX', 19)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 14)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 19,7 L9,13 L14,7 L9,1 Z');\n};\n// erDiagram specific markers\nconst only_one = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-onlyOneStart')\n    .attr('class', 'marker onlyOne ' + type)\n    .attr('refX', 0)\n    .attr('refY', 9)\n    .attr('markerWidth', 18)\n    .attr('markerHeight', 18)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M9,0 L9,18 M15,0 L15,18');\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-onlyOneEnd')\n    .attr('class', 'marker onlyOne ' + type)\n    .attr('refX', 18)\n    .attr('refY', 9)\n    .attr('markerWidth', 18)\n    .attr('markerHeight', 18)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M3,0 L3,18 M9,0 L9,18');\n};\n\nconst zero_or_one = (elem, type, id) => {\n  const startMarker = elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-zeroOrOneStart')\n    .attr('class', 'marker zeroOrOne ' + type)\n    .attr('refX', 0)\n    .attr('refY', 9)\n    .attr('markerWidth', 30)\n    .attr('markerHeight', 18)\n    .attr('orient', 'auto');\n  startMarker\n    .append('circle')\n    .attr('fill', 'white') // Fill white for now?\n    .attr('cx', 21)\n    .attr('cy', 9)\n    .attr('r', 6);\n  startMarker.append('path').attr('d', 'M9,0 L9,18');\n\n  const endMarker = elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-zeroOrOneEnd')\n    .attr('class', 'marker zeroOrOne ' + type)\n    .attr('refX', 30)\n    .attr('refY', 9)\n    .attr('markerWidth', 30)\n    .attr('markerHeight', 18)\n    .attr('orient', 'auto');\n  endMarker\n    .append('circle')\n    .attr('fill', 'white') // Fill white for now?\n    .attr('cx', 9)\n    .attr('cy', 9)\n    .attr('r', 6);\n  endMarker.append('path').attr('d', 'M21,0 L21,18');\n};\n\nconst one_or_more = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-oneOrMoreStart')\n    .attr('class', 'marker oneOrMore ' + type)\n    .attr('refX', 18)\n    .attr('refY', 18)\n    .attr('markerWidth', 45)\n    .attr('markerHeight', 36)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M0,18 Q 18,0 36,18 Q 18,36 0,18 M42,9 L42,27');\n\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-oneOrMoreEnd')\n    .attr('class', 'marker oneOrMore ' + type)\n    .attr('refX', 27)\n    .attr('refY', 18)\n    .attr('markerWidth', 45)\n    .attr('markerHeight', 36)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M3,9 L3,27 M9,18 Q27,0 45,18 Q27,36 9,18');\n};\n\nconst zero_or_more = (elem, type, id) => {\n  const startMarker = elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-zeroOrMoreStart')\n    .attr('class', 'marker zeroOrMore ' + type)\n    .attr('refX', 18)\n    .attr('refY', 18)\n    .attr('markerWidth', 57)\n    .attr('markerHeight', 36)\n    .attr('orient', 'auto');\n  startMarker.append('circle').attr('fill', 'white').attr('cx', 48).attr('cy', 18).attr('r', 6);\n  startMarker.append('path').attr('d', 'M0,18 Q18,0 36,18 Q18,36 0,18');\n\n  const endMarker = elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-zeroOrMoreEnd')\n    .attr('class', 'marker zeroOrMore ' + type)\n    .attr('refX', 39)\n    .attr('refY', 18)\n    .attr('markerWidth', 57)\n    .attr('markerHeight', 36)\n    .attr('orient', 'auto');\n  endMarker.append('circle').attr('fill', 'white').attr('cx', 9).attr('cy', 18).attr('r', 6);\n  endMarker.append('path').attr('d', 'M21,18 Q39,0 57,18 Q39,36 21,18');\n};\n\nconst requirement_arrow = (elem, type, id) => {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-requirement_arrowEnd')\n    .attr('refX', 20)\n    .attr('refY', 10)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 20)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr(\n      'd',\n      `M0,0\n      L20,10\n      M20,10\n      L0,20`\n    );\n};\nconst requirement_contains = (elem, type, id) => {\n  const containsNode = elem\n    .append('defs')\n    .append('marker')\n    .attr('id', id + '_' + type + '-requirement_containsStart')\n    .attr('refX', 0)\n    .attr('refY', 10)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 20)\n    .attr('orient', 'auto')\n    .append('g');\n\n  containsNode.append('circle').attr('cx', 10).attr('cy', 10).attr('r', 9).attr('fill', 'none');\n\n  containsNode.append('line').attr('x1', 1).attr('x2', 19).attr('y1', 10).attr('y2', 10);\n\n  containsNode.append('line').attr('y1', 1).attr('y2', 19).attr('x1', 10).attr('x2', 10);\n};\n\n// TODO rename the class diagram markers to something shape descriptive and semantic free\nconst markers = {\n  extension,\n  composition,\n  aggregation,\n  dependency,\n  lollipop,\n  point,\n  circle,\n  cross,\n  barb,\n  only_one,\n  zero_or_one,\n  one_or_more,\n  zero_or_more,\n  requirement_arrow,\n  requirement_contains,\n};\nexport default insertMarkers;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAQA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,OAAO,WAAW;;;ACZX,IAAM,iBAAiB,wBAC5B,SACA,MACA,KACA,IACA,aACA,gBACG;AACH,MAAI,KAAK,gBAAgB;AACvB,kBAAc,SAAS,SAAS,KAAK,gBAAgB,KAAK,IAAI,aAAa,WAAW;AAAA,EACxF;AACA,MAAI,KAAK,cAAc;AACrB,kBAAc,SAAS,OAAO,KAAK,cAAc,KAAK,IAAI,aAAa,WAAW;AAAA,EACpF;AACF,GAd8B;AAgB9B,IAAM,gBAAgB;AAAA,EACpB,aAAa,EAAE,MAAM,SAAS,MAAM,MAAM;AAAA,EAC1C,aAAa,EAAE,MAAM,SAAS,MAAM,KAAK;AAAA,EACzC,YAAY,EAAE,MAAM,QAAQ,MAAM,KAAK;AAAA,EACvC,cAAc,EAAE,MAAM,UAAU,MAAM,MAAM;AAAA,EAC5C,aAAa,EAAE,MAAM,eAAe,MAAM,MAAM;AAAA,EAChD,WAAW,EAAE,MAAM,aAAa,MAAM,MAAM;AAAA,EAC5C,aAAa,EAAE,MAAM,eAAe,MAAM,KAAK;AAAA,EAC/C,YAAY,EAAE,MAAM,cAAc,MAAM,KAAK;AAAA,EAC7C,UAAU,EAAE,MAAM,YAAY,MAAM,MAAM;AAAA,EAC1C,UAAU,EAAE,MAAM,WAAW,MAAM,MAAM;AAAA,EACzC,aAAa,EAAE,MAAM,aAAa,MAAM,MAAM;AAAA,EAC9C,aAAa,EAAE,MAAM,aAAa,MAAM,MAAM;AAAA,EAC9C,cAAc,EAAE,MAAM,cAAc,MAAM,MAAM;AAAA,EAChD,mBAAmB,EAAE,MAAM,qBAAqB,MAAM,MAAM;AAAA,EAC5D,sBAAsB,EAAE,MAAM,wBAAwB,MAAM,MAAM;AACpE;AAEA,IAAM,gBAAgB,wBACpB,SACA,UACA,WACA,KACA,IACA,aACA,gBACG;AACH,QAAM,gBAAgB,cAAc,SAAuC;AAE3E,MAAI,CAAC,eAAe;AAClB,QAAI,KAAK,uBAAuB,SAAS,EAAE;AAC3C;AAAA,EACF;AAEA,QAAM,gBAAgB,cAAc;AACpC,QAAM,SAAS,aAAa,UAAU,UAAU;AAChD,QAAM,mBAAmB,GAAG,EAAE,IAAI,WAAW,IAAI,aAAa,GAAG,MAAM;AAGvE,MAAI,eAAe,YAAY,KAAK,MAAM,IAAI;AAE5C,UAAM,UAAU,YAAY,QAAQ,gBAAgB,GAAG;AACvD,UAAM,kBAAkB,GAAG,gBAAgB,IAAI,OAAO;AAGtD,QAAI,CAAC,SAAS,eAAe,eAAe,GAAG;AAE7C,YAAM,iBAAiB,SAAS,eAAe,gBAAgB;AAC/D,UAAI,gBAAgB;AAElB,cAAM,gBAAgB,eAAe,UAAU,IAAI;AACnD,sBAAc,KAAK;AAGnB,cAAM,QAAQ,cAAc,iBAAiB,oBAAoB;AACjE,cAAM,QAAQ,CAAC,SAAS;AACtB,eAAK,aAAa,UAAU,WAAW;AAGvC,cAAI,cAAc,MAAM;AACtB,iBAAK,aAAa,QAAQ,WAAW;AAAA,UACvC;AAAA,QACF,CAAC;AAGD,uBAAe,YAAY,YAAY,aAAa;AAAA,MACtD;AAAA,IACF;AAGA,YAAQ,KAAK,UAAU,QAAQ,IAAI,OAAO,GAAG,IAAI,eAAe,GAAG;AAAA,EACrE,OAAO;AAEL,YAAQ,KAAK,UAAU,QAAQ,IAAI,OAAO,GAAG,IAAI,gBAAgB,GAAG;AAAA,EACtE;AACF,GAzDsB;;;ADjBtB,IAAM,aAAa,oBAAI,IAAI;AAC3B,IAAM,iBAAiB,oBAAI,IAAI;AAExB,IAAM,QAAQ,6BAAM;AACzB,aAAW,MAAM;AACjB,iBAAe,MAAM;AACvB,GAHqB;AAKd,IAAM,iBAAiB,wBAAC,eAAe;AAC5C,MAAI,SAAS,aAAa,WAAW,OAAO,CAAC,KAAK,UAAU,MAAM,MAAM,OAAO,EAAE,IAAI;AACrF,SAAO;AACT,GAH8B;AAKvB,IAAM,kBAAkB,8BAAO,MAAM,SAAS;AACnD,MAAI,gBAAgB,SAAS,UAAU,EAAE,UAAU,UAAU;AAE7D,QAAM,eAAe,MAAM,WAAW,MAAM,KAAK,OAAO;AAAA,IACtD,OAAO,eAAe,KAAK,UAAU;AAAA,IACrC;AAAA,IACA,kBAAkB;AAAA,IAClB,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,KAAK,SAAS,MAAM,KAAK,SAAS;AAGtC,QAAM,YAAY,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,WAAW;AAG5D,QAAM,QAAQ,UAAU,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AACzD,QAAM,KAAK,EAAE,YAAY,YAAY;AAGrC,MAAI,OAAO,aAAa,QAAQ;AAChC,MAAI,eAAe;AACjB,UAAM,MAAM,aAAa,SAAS,CAAC;AACnC,UAAM,KAAK,OAAO,YAAY;AAC9B,WAAO,IAAI,sBAAsB;AACjC,OAAG,KAAK,SAAS,KAAK,KAAK;AAC3B,OAAG,KAAK,UAAU,KAAK,MAAM;AAAA,EAC/B;AACA,QAAM,KAAK,aAAa,eAAe,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,SAAS,IAAI,GAAG;AAGtF,aAAW,IAAI,KAAK,IAAI,SAAS;AAGjC,OAAK,QAAQ,KAAK;AAClB,OAAK,SAAS,KAAK;AAEnB,MAAI;AACJ,MAAI,KAAK,gBAAgB;AAEvB,UAAM,oBAAoB,MAAM;AAAA,MAC9B,KAAK;AAAA,MACL,eAAe,KAAK,UAAU;AAAA,IAChC;AACA,UAAM,qBAAqB,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe;AACzE,UAAM,QAAQ,mBAAmB,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AAClE,SAAK,MAAM,KAAK,EAAE,YAAY,iBAAiB;AAC/C,UAAM,QAAQ,kBAAkB,QAAQ;AACxC,UAAM,KAAK,aAAa,eAAe,CAAC,MAAM,QAAQ,IAAI,OAAO,CAAC,MAAM,SAAS,IAAI,GAAG;AACxF,QAAI,CAAC,eAAe,IAAI,KAAK,EAAE,GAAG;AAChC,qBAAe,IAAI,KAAK,IAAI,CAAC,CAAC;AAAA,IAChC;AACA,mBAAe,IAAI,KAAK,EAAE,EAAE,YAAY;AACxC,qBAAiB,IAAI,KAAK,cAAc;AAAA,EAC1C;AACA,MAAI,KAAK,iBAAiB;AAExB,UAAM,oBAAoB,MAAM;AAAA,MAC9B,KAAK;AAAA,MACL,eAAe,KAAK,UAAU;AAAA,IAChC;AACA,UAAM,sBAAsB,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe;AAC1E,UAAM,QAAQ,oBAAoB,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AACnE,SAAK,oBAAoB,KAAK,EAAE,YAAY,iBAAiB;AAC7D,UAAM,KAAK,EAAE,YAAY,iBAAiB;AAC1C,UAAM,QAAQ,kBAAkB,QAAQ;AACxC,UAAM,KAAK,aAAa,eAAe,CAAC,MAAM,QAAQ,IAAI,OAAO,CAAC,MAAM,SAAS,IAAI,GAAG;AAExF,QAAI,CAAC,eAAe,IAAI,KAAK,EAAE,GAAG;AAChC,qBAAe,IAAI,KAAK,IAAI,CAAC,CAAC;AAAA,IAChC;AACA,mBAAe,IAAI,KAAK,EAAE,EAAE,aAAa;AACzC,qBAAiB,IAAI,KAAK,eAAe;AAAA,EAC3C;AACA,MAAI,KAAK,cAAc;AAErB,UAAM,kBAAkB,MAAM,oBAAY,KAAK,cAAc,eAAe,KAAK,UAAU,CAAC;AAC5F,UAAM,mBAAmB,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe;AACvE,UAAM,QAAQ,iBAAiB,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AAChE,SAAK,MAAM,KAAK,EAAE,YAAY,eAAe;AAC7C,UAAM,QAAQ,gBAAgB,QAAQ;AACtC,UAAM,KAAK,aAAa,eAAe,CAAC,MAAM,QAAQ,IAAI,OAAO,CAAC,MAAM,SAAS,IAAI,GAAG;AAExF,qBAAiB,KAAK,EAAE,YAAY,eAAe;AAEnD,QAAI,CAAC,eAAe,IAAI,KAAK,EAAE,GAAG;AAChC,qBAAe,IAAI,KAAK,IAAI,CAAC,CAAC;AAAA,IAChC;AACA,mBAAe,IAAI,KAAK,EAAE,EAAE,UAAU;AACtC,qBAAiB,IAAI,KAAK,YAAY;AAAA,EACxC;AACA,MAAI,KAAK,eAAe;AAEtB,UAAM,kBAAkB,MAAM,oBAAY,KAAK,eAAe,eAAe,KAAK,UAAU,CAAC;AAC7F,UAAM,oBAAoB,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe;AACxE,UAAM,QAAQ,kBAAkB,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AAEjE,SAAK,MAAM,KAAK,EAAE,YAAY,eAAe;AAC7C,UAAM,QAAQ,gBAAgB,QAAQ;AACtC,UAAM,KAAK,aAAa,eAAe,CAAC,MAAM,QAAQ,IAAI,OAAO,CAAC,MAAM,SAAS,IAAI,GAAG;AAExF,sBAAkB,KAAK,EAAE,YAAY,eAAe;AACpD,QAAI,CAAC,eAAe,IAAI,KAAK,EAAE,GAAG;AAChC,qBAAe,IAAI,KAAK,IAAI,CAAC,CAAC;AAAA,IAChC;AACA,mBAAe,IAAI,KAAK,EAAE,EAAE,WAAW;AACvC,qBAAiB,IAAI,KAAK,aAAa;AAAA,EACzC;AACA,SAAO;AACT,GA5G+B;AAkH/B,SAAS,iBAAiB,IAAI,OAAO;AACnC,MAAI,UAAU,EAAE,UAAU,cAAc,IAAI;AAC1C,OAAG,MAAM,QAAQ,MAAM,SAAS,IAAI;AACpC,OAAG,MAAM,SAAS;AAAA,EACpB;AACF;AALS;AAOF,IAAM,oBAAoB,wBAAC,MAAM,UAAU;AAChD,MAAI,MAAM,uBAAuB,KAAK,IAAI,KAAK,OAAO,WAAW,IAAI,KAAK,EAAE,GAAG,KAAK;AACpF,MAAI,OAAO,MAAM,cAAc,MAAM,cAAc,MAAM;AACzD,QAAM,aAAa,UAAU;AAC7B,QAAM,EAAE,yBAAyB,IAAI,wBAAwB,UAAU;AACvE,MAAI,KAAK,OAAO;AACd,UAAM,KAAK,WAAW,IAAI,KAAK,EAAE;AACjC,QAAI,IAAI,KAAK;AACb,QAAI,IAAI,KAAK;AACb,QAAI,MAAM;AACR,YAAM,MAAM,cAAM,kBAAkB,IAAI;AACxC,UAAI;AAAA,QACF,kBAAkB,KAAK,QAAQ;AAAA,QAC/B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,IAAI;AAAA,QACJ;AAAA,QACA,IAAI;AAAA,QACJ;AAAA,MACF;AACA,UAAI,MAAM,aAAa;AACrB,YAAI,IAAI;AACR,YAAI,IAAI;AAAA,MACV;AAAA,IACF;AACA,OAAG,KAAK,aAAa,aAAa,CAAC,KAAK,IAAI,2BAA2B,CAAC,GAAG;AAAA,EAC7E;AAEA,MAAI,KAAK,gBAAgB;AACvB,UAAM,KAAK,eAAe,IAAI,KAAK,EAAE,EAAE;AACvC,QAAI,IAAI,KAAK;AACb,QAAI,IAAI,KAAK;AACb,QAAI,MAAM;AACR,YAAM,MAAM,cAAM,0BAA0B,KAAK,iBAAiB,KAAK,GAAG,cAAc,IAAI;AAC5F,UAAI,IAAI;AACR,UAAI,IAAI;AAAA,IACV;AACA,OAAG,KAAK,aAAa,aAAa,CAAC,KAAK,CAAC,GAAG;AAAA,EAC9C;AACA,MAAI,KAAK,iBAAiB;AACxB,UAAM,KAAK,eAAe,IAAI,KAAK,EAAE,EAAE;AACvC,QAAI,IAAI,KAAK;AACb,QAAI,IAAI,KAAK;AACb,QAAI,MAAM;AACR,YAAM,MAAM,cAAM;AAAA,QAChB,KAAK,iBAAiB,KAAK;AAAA,QAC3B;AAAA,QACA;AAAA,MACF;AACA,UAAI,IAAI;AACR,UAAI,IAAI;AAAA,IACV;AACA,OAAG,KAAK,aAAa,aAAa,CAAC,KAAK,CAAC,GAAG;AAAA,EAC9C;AACA,MAAI,KAAK,cAAc;AACrB,UAAM,KAAK,eAAe,IAAI,KAAK,EAAE,EAAE;AACvC,QAAI,IAAI,KAAK;AACb,QAAI,IAAI,KAAK;AACb,QAAI,MAAM;AACR,YAAM,MAAM,cAAM,0BAA0B,KAAK,eAAe,KAAK,GAAG,YAAY,IAAI;AACxF,UAAI,IAAI;AACR,UAAI,IAAI;AAAA,IACV;AACA,OAAG,KAAK,aAAa,aAAa,CAAC,KAAK,CAAC,GAAG;AAAA,EAC9C;AACA,MAAI,KAAK,eAAe;AACtB,UAAM,KAAK,eAAe,IAAI,KAAK,EAAE,EAAE;AACvC,QAAI,IAAI,KAAK;AACb,QAAI,IAAI,KAAK;AACb,QAAI,MAAM;AACR,YAAM,MAAM,cAAM,0BAA0B,KAAK,eAAe,KAAK,GAAG,aAAa,IAAI;AACzF,UAAI,IAAI;AACR,UAAI,IAAI;AAAA,IACV;AACA,OAAG,KAAK,aAAa,aAAa,CAAC,KAAK,CAAC,GAAG;AAAA,EAC9C;AACF,GA9EiC;AAgFjC,IAAM,cAAc,wBAAC,MAAMA,WAAU;AACnC,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,KAAK;AACf,QAAM,KAAK,KAAK,IAAIA,OAAM,IAAI,CAAC;AAC/B,QAAM,KAAK,KAAK,IAAIA,OAAM,IAAI,CAAC;AAC/B,QAAM,IAAI,KAAK,QAAQ;AACvB,QAAM,IAAI,KAAK,SAAS;AACxB,SAAO,MAAM,KAAK,MAAM;AAC1B,GARoB;AAUb,IAAM,eAAe,wBAAC,MAAM,cAAc,gBAAgB;AAC/D,MAAI,MAAM;AAAA,kBACM,KAAK,UAAU,YAAY,CAAC;AAAA,kBAC5B,KAAK,UAAU,WAAW,CAAC;AAAA,oBACzB,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,KAAK,KAAK,MAAM,KAAK,MAAM,EAAE;AACvE,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,KAAK;AAEf,QAAM,KAAK,KAAK,IAAI,IAAI,YAAY,CAAC;AACrC,QAAM,IAAI,KAAK,QAAQ;AACvB,MAAI,IAAI,YAAY,IAAI,aAAa,IAAI,IAAI,KAAK,IAAI;AACtD,QAAM,IAAI,KAAK,SAAS;AAExB,QAAM,IAAI,KAAK,IAAI,aAAa,IAAI,YAAY,CAAC;AACjD,QAAM,IAAI,KAAK,IAAI,aAAa,IAAI,YAAY,CAAC;AAEjD,MAAI,KAAK,IAAI,IAAI,aAAa,CAAC,IAAI,IAAI,KAAK,IAAI,IAAI,aAAa,CAAC,IAAI,GAAG;AAEvE,QAAI,IAAI,YAAY,IAAI,aAAa,IAAI,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,aAAa;AACvF,QAAK,IAAI,IAAK;AACd,UAAM,MAAM;AAAA,MACV,GAAG,YAAY,IAAI,aAAa,IAAI,YAAY,IAAI,IAAI,YAAY,IAAI,IAAI;AAAA,MAC5E,GAAG,YAAY,IAAI,aAAa,IAAI,YAAY,IAAI,IAAI,IAAI,YAAY,IAAI,IAAI;AAAA,IAClF;AAEA,QAAI,MAAM,GAAG;AACX,UAAI,IAAI,aAAa;AACrB,UAAI,IAAI,aAAa;AAAA,IACvB;AACA,QAAI,MAAM,GAAG;AACX,UAAI,IAAI,aAAa;AAAA,IACvB;AACA,QAAI,MAAM,GAAG;AACX,UAAI,IAAI,aAAa;AAAA,IACvB;AAEA,QAAI,MAAM,4BAA4B,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG;AAEtE,WAAO;AAAA,EACT,OAAO;AAEL,QAAI,YAAY,IAAI,aAAa,GAAG;AAClC,UAAI,aAAa,IAAI,IAAI;AAAA,IAC3B,OAAO;AACL,UAAI,IAAI,IAAI,aAAa;AAAA,IAC3B;AACA,QAAI,IAAK,IAAI,IAAK;AAClB,QAAI,KAAK,YAAY,IAAI,aAAa,IAAI,YAAY,IAAI,IAAI,IAAI,YAAY,IAAI,IAAI;AACtF,QAAI,KAAK,YAAY,IAAI,aAAa,IAAI,YAAY,IAAI,IAAI,YAAY,IAAI;AAC9E,QAAI,MAAM,uBAAuB,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC;AACxE,QAAI,MAAM,GAAG;AACX,WAAK,aAAa;AAClB,WAAK,aAAa;AAAA,IACpB;AACA,QAAI,MAAM,GAAG;AACX,WAAK,aAAa;AAAA,IACpB;AACA,QAAI,MAAM,GAAG;AACX,WAAK,aAAa;AAAA,IACpB;AAEA,WAAO,EAAE,GAAG,IAAI,GAAG,GAAG;AAAA,EACxB;AACF,GA/D4B;AAiE5B,IAAM,qBAAqB,wBAAC,SAAS,iBAAiB;AACpD,MAAI,KAAK,4BAA4B,SAAS,YAAY;AAC1D,MAAI,SAAS,CAAC;AACd,MAAI,mBAAmB,QAAQ,CAAC;AAChC,MAAI,WAAW;AACf,UAAQ,QAAQ,CAACA,WAAU;AACzB,QAAI,KAAK,wBAAwBA,QAAO,YAAY;AAEpD,QAAI,CAAC,YAAY,cAAcA,MAAK,KAAK,CAAC,UAAU;AAClD,YAAM,QAAQ,aAAa,cAAc,kBAAkBA,MAAK;AAChE,UAAI,MAAM,gBAAgBA,QAAO,kBAAkB,KAAK;AACxD,UAAI,MAAM,sBAAsB,OAAO,YAAY;AAEnD,UAAI,eAAe;AACnB,aAAO,QAAQ,CAAC,MAAM;AACpB,uBAAe,gBAAiB,EAAE,MAAM,MAAM,KAAK,EAAE,MAAM,MAAM;AAAA,MACnE,CAAC;AAED,UAAI,CAAC,OAAO,KAAK,CAAC,MAAM,EAAE,MAAM,MAAM,KAAK,EAAE,MAAM,MAAM,CAAC,GAAG;AAC3D,eAAO,KAAK,KAAK;AAAA,MACnB,OAAO;AACL,YAAI,KAAK,sBAAsB,OAAO,MAAM;AAAA,MAC9C;AACA,iBAAW;AAAA,IACb,OAAO;AACL,UAAI,KAAK,iBAAiBA,QAAO,gBAAgB;AACjD,yBAAmBA;AACnB,UAAI,CAAC,UAAU;AACb,eAAO,KAAKA,MAAK;AAAA,MACnB;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,MAAM,oBAAoB,MAAM;AACpC,SAAO;AACT,GAlC2B;AAoC3B,SAAS,oBAAoB,QAAQ;AACnC,QAAM,eAAe,CAAC;AACtB,QAAM,uBAAuB,CAAC;AAC9B,WAAS,IAAI,GAAG,IAAI,OAAO,SAAS,GAAG,KAAK;AAC1C,UAAM,OAAO,OAAO,IAAI,CAAC;AACzB,UAAM,OAAO,OAAO,CAAC;AACrB,UAAM,OAAO,OAAO,IAAI,CAAC;AACzB,QACE,KAAK,MAAM,KAAK,KAChB,KAAK,MAAM,KAAK,KAChB,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,KAC5B,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,GAC5B;AACA,mBAAa,KAAK,IAAI;AACtB,2BAAqB,KAAK,CAAC;AAAA,IAC7B,WACE,KAAK,MAAM,KAAK,KAChB,KAAK,MAAM,KAAK,KAChB,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,KAC5B,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,GAC5B;AACA,mBAAa,KAAK,IAAI;AACtB,2BAAqB,KAAK,CAAC;AAAA,IAC7B;AAAA,EACF;AACA,SAAO,EAAE,cAAc,qBAAqB;AAC9C;AA1BS;AA4BT,IAAM,oBAAoB,gCAAU,QAAQ,QAAQ,UAAU;AAC5D,QAAM,QAAQ,OAAO,IAAI,OAAO;AAChC,QAAM,QAAQ,OAAO,IAAI,OAAO;AAChC,QAAM,SAAS,KAAK,KAAK,QAAQ,QAAQ,QAAQ,KAAK;AACtD,QAAM,QAAQ,WAAW;AACzB,SAAO,EAAE,GAAG,OAAO,IAAI,QAAQ,OAAO,GAAG,OAAO,IAAI,QAAQ,MAAM;AACpE,GAN0B;AAQ1B,IAAM,aAAa,gCAAU,UAAU;AACrC,QAAM,EAAE,qBAAqB,IAAI,oBAAoB,QAAQ;AAC7D,QAAM,cAAc,CAAC;AACrB,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,QAAI,qBAAqB,SAAS,CAAC,GAAG;AACpC,YAAM,YAAY,SAAS,IAAI,CAAC;AAChC,YAAM,YAAY,SAAS,IAAI,CAAC;AAChC,YAAM,cAAc,SAAS,CAAC;AAE9B,YAAM,eAAe,kBAAkB,WAAW,aAAa,CAAC;AAChE,YAAM,eAAe,kBAAkB,WAAW,aAAa,CAAC;AAEhE,YAAM,QAAQ,aAAa,IAAI,aAAa;AAC5C,YAAM,QAAQ,aAAa,IAAI,aAAa;AAC5C,kBAAY,KAAK,YAAY;AAE7B,YAAM,IAAI,KAAK,KAAK,CAAC,IAAI;AACzB,UAAI,iBAAiB,EAAE,GAAG,YAAY,GAAG,GAAG,YAAY,EAAE;AAC1D,UAAI,KAAK,IAAI,UAAU,IAAI,UAAU,CAAC,IAAI,MAAM,KAAK,IAAI,UAAU,IAAI,UAAU,CAAC,KAAK,IAAI;AACzF,YAAI;AAAA,UACF;AAAA,UACA,KAAK,IAAI,UAAU,IAAI,UAAU,CAAC;AAAA,UAClC,KAAK,IAAI,UAAU,IAAI,UAAU,CAAC;AAAA,QACpC;AACA,cAAM,IAAI;AACV,YAAI,YAAY,MAAM,aAAa,GAAG;AACpC,2BAAiB;AAAA,YACf,GAAG,QAAQ,IAAI,aAAa,IAAI,IAAI,IAAI,aAAa,IAAI,IAAI;AAAA,YAC7D,GAAG,QAAQ,IAAI,aAAa,IAAI,IAAI,aAAa,IAAI;AAAA,UACvD;AAAA,QACF,OAAO;AACL,2BAAiB;AAAA,YACf,GAAG,QAAQ,IAAI,aAAa,IAAI,IAAI,aAAa,IAAI;AAAA,YACrD,GAAG,QAAQ,IAAI,aAAa,IAAI,IAAI,IAAI,aAAa,IAAI,IAAI;AAAA,UAC/D;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAI;AAAA,UACF;AAAA,UACA,KAAK,IAAI,UAAU,IAAI,UAAU,CAAC;AAAA,UAClC,KAAK,IAAI,UAAU,IAAI,UAAU,CAAC;AAAA,QACpC;AAAA,MACF;AACA,kBAAY,KAAK,gBAAgB,YAAY;AAAA,IAC/C,OAAO;AACL,kBAAY,KAAK,SAAS,CAAC,CAAC;AAAA,IAC9B;AAAA,EACF;AACA,SAAO;AACT,GAjDmB;AAmDZ,IAAM,aAAa,gCAAU,MAAM,MAAM,WAAW,aAAa,WAAW,SAAS,IAAI;AAC9F,QAAM,EAAE,cAAc,IAAI,UAAU;AACpC,MAAI,SAAS,KAAK;AAClB,MAAI,mBAAmB;AACvB,QAAM,OAAO;AACb,MAAI,OAAO;AACX,QAAM,kBAAkB,CAAC;AACzB,aAAW,OAAO,KAAK,mBAAmB;AACxC,QAAI,aAAa,GAAG,GAAG;AACrB;AAAA,IACF;AACA,oBAAgB,KAAK,KAAK,kBAAkB,GAAG,CAAC;AAAA,EAClD;AAEA,MAAI,KAAK,aAAa,KAAK,WAAW;AACpC,aAAS,OAAO,MAAM,GAAG,KAAK,OAAO,SAAS,CAAC;AAC/C,WAAO,QAAQ,KAAK,UAAU,OAAO,CAAC,CAAC,CAAC;AACxC,QAAI;AAAA,MACF;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,OAAO,OAAO,SAAS,CAAC;AAAA,MACxB;AAAA,MACA,KAAK,UAAU,OAAO,OAAO,SAAS,CAAC,CAAC;AAAA,IAC1C;AACA,WAAO,KAAK,KAAK,UAAU,OAAO,OAAO,SAAS,CAAC,CAAC,CAAC;AAAA,EACvD;AACA,MAAI,KAAK,WAAW;AAClB,QAAI,KAAK,oBAAoB,UAAU,IAAI,KAAK,SAAS,CAAC;AAC1D,aAAS,mBAAmB,KAAK,QAAQ,UAAU,IAAI,KAAK,SAAS,EAAE,IAAI;AAE3E,uBAAmB;AAAA,EACrB;AAEA,MAAI,KAAK,aAAa;AACpB,QAAI;AAAA,MACF;AAAA,MACA,UAAU,IAAI,KAAK,WAAW;AAAA,MAC9B,KAAK,UAAU,QAAQ,MAAM,CAAC;AAAA,IAChC;AACA,aAAS,mBAAmB,OAAO,QAAQ,GAAG,UAAU,IAAI,KAAK,WAAW,EAAE,IAAI,EAAE,QAAQ;AAE5F,uBAAmB;AAAA,EACrB;AAEA,MAAI,WAAW,OAAO,OAAO,CAAC,MAAM,CAAC,OAAO,MAAM,EAAE,CAAC,CAAC;AACtD,aAAW,WAAW,QAAQ;AAC9B,MAAI,QAAQ;AACZ,UAAQ;AACR,UAAQ,KAAK,OAAO;AAAA,IAClB,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF,KAAK;AACH,cAAQ;AACR;AAAA,IACF;AACE,cAAQ;AAAA,EACZ;AAEA,QAAM,EAAE,GAAG,EAAE,IAAI,2BAA2B,IAAI;AAChD,QAAM,eAAe,KAAK,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,MAAM,KAAK;AAEjD,MAAI;AACJ,UAAQ,KAAK,WAAW;AAAA,IACtB,KAAK;AACH,sBAAgB;AAChB;AAAA,IACF,KAAK;AACH,sBAAgB;AAChB;AAAA,IACF,KAAK;AACH,sBAAgB;AAChB;AAAA,IACF;AACE,sBAAgB;AAAA,EACpB;AACA,UAAQ,KAAK,SAAS;AAAA,IACpB,KAAK;AACH,uBAAiB;AACjB;AAAA,IACF,KAAK;AACH,uBAAiB;AACjB;AAAA,IACF,KAAK;AACH,uBAAiB;AACjB;AAAA,IACF;AACE,uBAAiB;AAAA,EACrB;AACA,MAAI;AACJ,MAAI,WAAW,aAAa,QAAQ;AACpC,QAAM,aAAa,MAAM,QAAQ,KAAK,KAAK,IAAI,KAAK,QAAQ,CAAC,KAAK,KAAK;AACvE,MAAI,cAAc,WAAW,KAAK,CAAC,UAAU,OAAO,WAAW,SAAS,CAAC;AAEzE,MAAI,KAAK,SAAS,aAAa;AAC7B,UAAM,KAAK,MAAM,IAAI,IAAI;AACzB,WAAO,OAAO,CAAC,GAAG,QAAQ;AAE1B,UAAM,cAAc,GAAG,KAAK,UAAU;AAAA,MACpC,WAAW;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAED,qBAAiB;AAEjB,cAAU,OAAO,WAAW,EACzB,OAAO,MAAM,EACb,KAAK,MAAM,KAAK,EAAE,EAClB,KAAK,SAAS,MAAM,iBAAiB,KAAK,UAAU,MAAM,KAAK,UAAU,GAAG,EAC5E,KAAK,SAAS,aAAa,WAAW,OAAO,CAAC,KAAK,UAAU,MAAM,MAAM,OAAO,EAAE,IAAI,EAAE;AAC3F,QAAI,IAAI,QAAQ,KAAK,GAAG;AACxB,YAAQ,KAAK,KAAK,CAAC;AACnB,SAAK,KAAK,EAAE,YAAY,QAAQ,KAAK,CAAC;AAAA,EACxC,OAAO;AACL,UAAM,oBAAoB,gBAAgB,KAAK,GAAG;AAClD,UAAM,SAAS,aAAa,WAAW,OAAO,CAAC,KAAK,UAAU,MAAM,QAAQ,KAAK,EAAE,IAAI;AACvF,QAAI,iBAAiB;AACrB,QAAI,KAAK,SAAS;AAChB,uBAAiB;AAAA,IACnB;AACA,QAAI,KAAK,WAAW;AAClB,uBAAiB,qBAAqB,KAAK;AAAA,IAC7C;AAEA,UAAM,YAAY,oBAAoB,oBAAoB,MAAM,SAAS,MAAM;AAC/E,cAAU,KACP,OAAO,MAAM,EACb,KAAK,KAAK,QAAQ,EAClB,KAAK,MAAM,KAAK,EAAE,EAClB;AAAA,MACC;AAAA,MACA,MAAM,iBAAiB,KAAK,UAAU,MAAM,KAAK,UAAU,OAAO,kBAAkB;AAAA,IACtF,EACC,KAAK,SAAS,SAAS;AAC1B,kBAAc,UAAU,MAAM,gBAAgB,IAAI,CAAC;AAAA,EACrD;AAuBA,MAAI,MAAM;AACV,MAAI,UAAU,EAAE,UAAU,uBAAuB,UAAU,EAAE,MAAM,qBAAqB;AACtF,UACE,OAAO,SAAS,WAChB,OACA,OAAO,SAAS,OAChB,OAAO,SAAS,WAChB,OAAO,SAAS;AAClB,UAAM,IAAI,QAAQ,OAAO,KAAK,EAAE,QAAQ,OAAO,KAAK;AAAA,EACtD;AACA,MAAI,KAAK,kBAAkB,KAAK,cAAc;AAC9C,MAAI,KAAK,gBAAgB,KAAK,YAAY;AAE1C,iBAAe,SAAS,MAAM,KAAK,IAAI,aAAa,WAAW;AAE/D,MAAI,QAAQ,CAAC;AACb,MAAI,kBAAkB;AACpB,UAAM,cAAc;AAAA,EACtB;AACA,QAAM,eAAe,KAAK;AAC1B,SAAO;AACT,GAnN0B;;;AErb1B,IAAM,gBAAgB,wBAAC,MAAM,aAAa,MAAM,OAAO;AACrD,cAAY,QAAQ,CAAC,eAAe;AAClC,YAAQ,UAAU,EAAE,MAAM,MAAM,EAAE;AAAA,EACpC,CAAC;AACH,GAJsB;AAMtB,IAAM,YAAY,wBAAC,MAAM,MAAM,OAAO;AACpC,MAAI,MAAM,uBAAuB,EAAE;AACnC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,iBAAiB,EAC9C,KAAK,SAAS,sBAAsB,IAAI,EACxC,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,GAAG,EACvB,KAAK,gBAAgB,GAAG,EACxB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,oBAAoB;AAEjC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,eAAe,EAC5C,KAAK,SAAS,sBAAsB,IAAI,EACxC,KAAK,QAAQ,CAAC,EACd,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,oBAAoB;AACnC,GA3BkB;AA6BlB,IAAM,cAAc,wBAAC,MAAM,MAAM,OAAO;AACtC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,mBAAmB,EAChD,KAAK,SAAS,wBAAwB,IAAI,EAC1C,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,GAAG,EACvB,KAAK,gBAAgB,GAAG,EACxB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,0BAA0B;AAEvC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,iBAAiB,EAC9C,KAAK,SAAS,wBAAwB,IAAI,EAC1C,KAAK,QAAQ,CAAC,EACd,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,0BAA0B;AACzC,GA1BoB;AA2BpB,IAAM,cAAc,wBAAC,MAAM,MAAM,OAAO;AACtC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,mBAAmB,EAChD,KAAK,SAAS,wBAAwB,IAAI,EAC1C,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,GAAG,EACvB,KAAK,gBAAgB,GAAG,EACxB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,0BAA0B;AAEvC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,iBAAiB,EAC9C,KAAK,SAAS,wBAAwB,IAAI,EAC1C,KAAK,QAAQ,CAAC,EACd,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,0BAA0B;AACzC,GA1BoB;AA2BpB,IAAM,aAAa,wBAAC,MAAM,MAAM,OAAO;AACrC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,kBAAkB,EAC/C,KAAK,SAAS,uBAAuB,IAAI,EACzC,KAAK,QAAQ,CAAC,EACd,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,GAAG,EACvB,KAAK,gBAAgB,GAAG,EACxB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,yBAAyB;AAEtC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,gBAAgB,EAC7C,KAAK,SAAS,uBAAuB,IAAI,EACzC,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,2BAA2B;AAC1C,GA1BmB;AA2BnB,IAAM,WAAW,wBAAC,MAAM,MAAM,OAAO;AACnC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,gBAAgB,EAC7C,KAAK,SAAS,qBAAqB,IAAI,EACvC,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,GAAG,EACvB,KAAK,gBAAgB,GAAG,EACxB,KAAK,UAAU,MAAM,EACrB,OAAO,QAAQ,EACf,KAAK,UAAU,OAAO,EACtB,KAAK,QAAQ,aAAa,EAC1B,KAAK,MAAM,CAAC,EACZ,KAAK,MAAM,CAAC,EACZ,KAAK,KAAK,CAAC;AAEd,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,cAAc,EAC3C,KAAK,SAAS,qBAAqB,IAAI,EACvC,KAAK,QAAQ,CAAC,EACd,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,GAAG,EACvB,KAAK,gBAAgB,GAAG,EACxB,KAAK,UAAU,MAAM,EACrB,OAAO,QAAQ,EACf,KAAK,UAAU,OAAO,EACtB,KAAK,QAAQ,aAAa,EAC1B,KAAK,MAAM,CAAC,EACZ,KAAK,MAAM,CAAC,EACZ,KAAK,KAAK,CAAC;AAChB,GAlCiB;AAmCjB,IAAM,QAAQ,wBAAC,MAAM,MAAM,OAAO;AAChC,OACG,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,WAAW,EACxC,KAAK,SAAS,YAAY,IAAI,EAC9B,KAAK,WAAW,WAAW,EAC3B,KAAK,QAAQ,CAAC,EACd,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,gBAAgB,EACpC,KAAK,eAAe,CAAC,EACrB,KAAK,gBAAgB,CAAC,EACtB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,uBAAuB,EACjC,KAAK,SAAS,iBAAiB,EAC/B,MAAM,gBAAgB,CAAC,EACvB,MAAM,oBAAoB,KAAK;AAClC,OACG,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,aAAa,EAC1C,KAAK,SAAS,YAAY,IAAI,EAC9B,KAAK,WAAW,WAAW,EAC3B,KAAK,QAAQ,GAAG,EAChB,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,gBAAgB,EACpC,KAAK,eAAe,CAAC,EACrB,KAAK,gBAAgB,CAAC,EACtB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,wBAAwB,EAClC,KAAK,SAAS,iBAAiB,EAC/B,MAAM,gBAAgB,CAAC,EACvB,MAAM,oBAAoB,KAAK;AACpC,GAjCc;AAkCd,IAAM,SAAS,wBAAC,MAAM,MAAM,OAAO;AACjC,OACG,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,YAAY,EACzC,KAAK,SAAS,YAAY,IAAI,EAC9B,KAAK,WAAW,WAAW,EAC3B,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,gBAAgB,EACpC,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,QAAQ,EACf,KAAK,MAAM,GAAG,EACd,KAAK,MAAM,GAAG,EACd,KAAK,KAAK,GAAG,EACb,KAAK,SAAS,iBAAiB,EAC/B,MAAM,gBAAgB,CAAC,EACvB,MAAM,oBAAoB,KAAK;AAElC,OACG,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,cAAc,EAC3C,KAAK,SAAS,YAAY,IAAI,EAC9B,KAAK,WAAW,WAAW,EAC3B,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,gBAAgB,EACpC,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,QAAQ,EACf,KAAK,MAAM,GAAG,EACd,KAAK,MAAM,GAAG,EACd,KAAK,KAAK,GAAG,EACb,KAAK,SAAS,iBAAiB,EAC/B,MAAM,gBAAgB,CAAC,EACvB,MAAM,oBAAoB,KAAK;AACpC,GAtCe;AAuCf,IAAM,QAAQ,wBAAC,MAAM,MAAM,OAAO;AAChC,OACG,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,WAAW,EACxC,KAAK,SAAS,kBAAkB,IAAI,EACpC,KAAK,WAAW,WAAW,EAC3B,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,GAAG,EAChB,KAAK,eAAe,gBAAgB,EACpC,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EAEb,KAAK,KAAK,2BAA2B,EACrC,KAAK,SAAS,iBAAiB,EAC/B,MAAM,gBAAgB,CAAC,EACvB,MAAM,oBAAoB,KAAK;AAElC,OACG,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,aAAa,EAC1C,KAAK,SAAS,kBAAkB,IAAI,EACpC,KAAK,WAAW,WAAW,EAC3B,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,GAAG,EAChB,KAAK,eAAe,gBAAgB,EACpC,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EAEb,KAAK,KAAK,2BAA2B,EACrC,KAAK,SAAS,iBAAiB,EAC/B,MAAM,gBAAgB,CAAC,EACvB,MAAM,oBAAoB,KAAK;AACpC,GApCc;AAqCd,IAAM,OAAO,wBAAC,MAAM,MAAM,OAAO;AAC/B,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,UAAU,EACvC,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,eAAe,gBAAgB,EACpC,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,2BAA2B;AAC1C,GAba;AAeb,IAAM,WAAW,wBAAC,MAAM,MAAM,OAAO;AACnC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,eAAe,EAC5C,KAAK,SAAS,oBAAoB,IAAI,EACtC,KAAK,QAAQ,CAAC,EACd,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,yBAAyB;AAEtC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,aAAa,EAC1C,KAAK,SAAS,oBAAoB,IAAI,EACtC,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,uBAAuB;AACtC,GA1BiB;AA4BjB,IAAM,cAAc,wBAAC,MAAM,MAAM,OAAO;AACtC,QAAM,cAAc,KACjB,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,iBAAiB,EAC9C,KAAK,SAAS,sBAAsB,IAAI,EACxC,KAAK,QAAQ,CAAC,EACd,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM;AACxB,cACG,OAAO,QAAQ,EACf,KAAK,QAAQ,OAAO,EACpB,KAAK,MAAM,EAAE,EACb,KAAK,MAAM,CAAC,EACZ,KAAK,KAAK,CAAC;AACd,cAAY,OAAO,MAAM,EAAE,KAAK,KAAK,YAAY;AAEjD,QAAM,YAAY,KACf,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,eAAe,EAC5C,KAAK,SAAS,sBAAsB,IAAI,EACxC,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM;AACxB,YACG,OAAO,QAAQ,EACf,KAAK,QAAQ,OAAO,EACpB,KAAK,MAAM,CAAC,EACZ,KAAK,MAAM,CAAC,EACZ,KAAK,KAAK,CAAC;AACd,YAAU,OAAO,MAAM,EAAE,KAAK,KAAK,cAAc;AACnD,GApCoB;AAsCpB,IAAM,cAAc,wBAAC,MAAM,MAAM,OAAO;AACtC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,iBAAiB,EAC9C,KAAK,SAAS,sBAAsB,IAAI,EACxC,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,EAAE,EACf,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,8CAA8C;AAE3D,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,eAAe,EAC5C,KAAK,SAAS,sBAAsB,IAAI,EACxC,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,EAAE,EACf,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,0CAA0C;AACzD,GA1BoB;AA4BpB,IAAM,eAAe,wBAAC,MAAM,MAAM,OAAO;AACvC,QAAM,cAAc,KACjB,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,kBAAkB,EAC/C,KAAK,SAAS,uBAAuB,IAAI,EACzC,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,EAAE,EACf,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM;AACxB,cAAY,OAAO,QAAQ,EAAE,KAAK,QAAQ,OAAO,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,KAAK,CAAC;AAC5F,cAAY,OAAO,MAAM,EAAE,KAAK,KAAK,+BAA+B;AAEpE,QAAM,YAAY,KACf,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,gBAAgB,EAC7C,KAAK,SAAS,uBAAuB,IAAI,EACzC,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,EAAE,EACf,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM;AACxB,YAAU,OAAO,QAAQ,EAAE,KAAK,QAAQ,OAAO,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,KAAK,CAAC;AACzF,YAAU,OAAO,MAAM,EAAE,KAAK,KAAK,iCAAiC;AACtE,GA1BqB;AA4BrB,IAAM,oBAAoB,wBAAC,MAAM,MAAM,OAAO;AAC5C,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,uBAAuB,EACpD,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,EAAE,EACf,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb;AAAA,IACC;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,EAIF;AACJ,GAlB0B;AAmB1B,IAAM,uBAAuB,wBAAC,MAAM,MAAM,OAAO;AAC/C,QAAM,eAAe,KAClB,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,KAAK,MAAM,OAAO,4BAA4B,EACzD,KAAK,QAAQ,CAAC,EACd,KAAK,QAAQ,EAAE,EACf,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,GAAG;AAEb,eAAa,OAAO,QAAQ,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,QAAQ,MAAM;AAE5F,eAAa,OAAO,MAAM,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,MAAM,EAAE;AAErF,eAAa,OAAO,MAAM,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,MAAM,EAAE;AACvF,GAjB6B;AAoB7B,IAAM,UAAU;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAO,kBAAQ;", "names": ["point"]}