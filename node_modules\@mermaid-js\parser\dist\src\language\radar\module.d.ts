import type { DefaultSharedCoreModuleContext, LangiumCoreServices, LangiumSharedCoreServices, Module, PartialLangiumCoreServices } from 'langium';
import { CommonValueConverter } from '../common/valueConverter.js';
import { RadarTokenBuilder } from './tokenBuilder.js';
/**
 * Declaration of `Radar` services.
 */
interface RadarAddedServices {
    parser: {
        TokenBuilder: RadarTokenBuilder;
        ValueConverter: CommonValueConverter;
    };
}
/**
 * Union of Langium default services and `Radar` services.
 */
export type RadarServices = LangiumCoreServices & RadarAddedServices;
/**
 * Dependency injection module that overrides Langium default services and
 * contributes the declared `Radar` services.
 */
export declare const RadarModule: Module<RadarServices, PartialLangiumCoreServices & RadarAddedServices>;
/**
 * Create the full set of services required by Langium.
 *
 * First inject the shared services by merging two modules:
 *  - Langium default shared services
 *  - Services generated by langium-cli
 *
 * Then inject the language-specific services by merging three modules:
 *  - Langium default language-specific services
 *  - Services generated by langium-cli
 *  - Services specified in this file
 * @param context - Optional module context with the LSP connection
 * @returns An object wrapping the shared services and the language-specific services
 */
export declare function createRadarServices(context?: DefaultSharedCoreModuleContext): {
    shared: LangiumSharedCoreServices;
    Radar: RadarServices;
};
export {};
