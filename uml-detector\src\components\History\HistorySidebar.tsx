import React, { useState, useEffect } from 'react';
import { useHistory } from '../../context/HistoryContext';
import { useAuth } from '../../context/AuthContext';
import { sidebarStyles } from './HistorySidebar.styles';

interface HistorySidebarProps {
  darkMode: boolean;
  isOpen: boolean;
  onToggle: () => void;
}

const HistorySidebar: React.FC<HistorySidebarProps> = ({ darkMode, isOpen, onToggle }) => {
  const { historyItems, deleteHistoryItem, loadHistoryItem, searchHistory, loading, deleteAllUserHistory } = useHistory();
  const { currentUser } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredItems, setFilteredItems] = useState(historyItems);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      setFilteredItems(searchHistory(query));
    } else {
      setFilteredItems(historyItems);
    }
  };

  const handleDelete = async (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cet élément ?')) {
      await deleteHistoryItem(id);
    }
  };

  const handleDeleteAll = async () => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer tout votre historique ? Cette action est irréversible.')) {
      await deleteAllUserHistory();
    }
  };

  const handleLoad = async (id: string) => {
    await loadHistoryItem(id);
  };

  const handleItemHover = (e: React.MouseEvent<HTMLDivElement>, isEntering: boolean) => {
    if (isEntering) {
      e.currentTarget.style.backgroundColor = darkMode ? "rgba(59, 130, 246, 0.1)" : "rgba(59, 130, 246, 0.05)";
    } else {
      e.currentTarget.style.backgroundColor = "transparent";
    }
  };

  useEffect(() => {
    setFilteredItems(historyItems);
  }, [historyItems]);

  if (!currentUser) {
    return (
      <div style={sidebarStyles.container(isOpen, darkMode)}>
        {isOpen && (
          <div style={sidebarStyles.loginPrompt}>
            <p>Connectez-vous pour voir votre historique</p>
          </div>
        )}
      </div>
    );
  }

  return (
    <div style={sidebarStyles.container(isOpen, darkMode)}>
      {isOpen && (
        <>
          {/* Header avec recherche */}
          <div style={sidebarStyles.header}>
            <h3 style={sidebarStyles.title}>
              Historique UML
            </h3>
            
            {/* Barre de recherche */}
            <div style={sidebarStyles.searchContainer}>
              <input
                type="text"
                placeholder="Rechercher..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                style={sidebarStyles.searchInput(darkMode)}
              />
              <svg 
                style={sidebarStyles.searchIcon}
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
              </svg>
            </div>
            
            <div style={sidebarStyles.buttonContainer}>
              <button 
                onClick={handleDeleteAll}
                style={sidebarStyles.deleteAllButton}
              >
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M3 6h18"></path>
                  <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                  <path d="M8 6V4c0-1 1-2 2-2h4c0-1 1-2 2-2v2"></path>
                </svg>
                Supprimer tout l'historique
              </button>
            </div>
          </div>

          {/* Liste des éléments */}
          <div style={sidebarStyles.listContainer}>
            {loading ? (
              <div style={sidebarStyles.loadingContainer}>
                <p>Chargement...</p>
              </div>
            ) : filteredItems.length === 0 ? (
              <div style={sidebarStyles.emptyContainer}>
                <p>{searchQuery ? "Aucun résultat trouvé" : "Aucun historique"}</p>
              </div>
            ) : (
              filteredItems.map(item => (
                <div 
                  key={item.id}
                  onClick={() => handleLoad(item.id)}
                  style={sidebarStyles.listItem}
                  onMouseEnter={(e) => handleItemHover(e, true)}
                  onMouseLeave={(e) => handleItemHover(e, false)}
                >
                  {/* Miniature */}
                  <div style={sidebarStyles.thumbnailContainer}>
                    <img 
                      src={item.originalImageUrl}
                      alt="Miniature" 
                      style={sidebarStyles.thumbnail}
                    />
                  </div>
                  <div style={sidebarStyles.itemContent}>
                    <div style={sidebarStyles.itemTitle}>
                      {item.title}
                    </div>
                    <div style={sidebarStyles.itemDate}>
                      {item.createdAt.toLocaleDateString('fr-FR')}
                    </div>
                  </div>
                  
                  {/* Actions */}
                  <div style={sidebarStyles.actionsContainer}>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleLoad(item.id);
                      }}
                      style={sidebarStyles.actionButton("#60a5fa")}
                      title="Recharger"
                    >
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
                        <path d="M21 3v5h-5"></path>
                        <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
                        <path d="M3 21v-5h5"></path>
                      </svg>
                    </button>
                    <button
                      onClick={(e) => handleDelete(item.id, e)}
                      style={sidebarStyles.actionButton("#ef4444")}
                      title="Supprimer"
                    >
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M3 6h18"></path>
                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                        <path d="M8 6V4c0-1 1-2 2-2h4c0-1 1-2 2-2v2"></path>
                      </svg>
                    </button>
                  </div>
                </div>
              ))
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default HistorySidebar;