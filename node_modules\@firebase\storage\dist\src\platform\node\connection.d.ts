/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { Connection, ConnectionType, ErrorCode } from '../../implementation/connection';
/**
 * Network layer that works in Node.
 *
 * This network implementation should not be used in browsers as it does not
 * support progress updates.
 */
declare abstract class FetchConnection<T extends ConnectionType> implements Connection<T> {
    protected errorCode_: ErrorCode;
    protected statusCode_: number | undefined;
    protected body_: ArrayBuffer | undefined;
    protected errorText_: string;
    protected headers_: Headers | undefined;
    protected sent_: boolean;
    constructor();
    send(url: string, method: string, body?: NodeJS.ArrayBufferView | Blob | string, headers?: Record<string, string>): Promise<void>;
    getErrorCode(): ErrorCode;
    getStatus(): number;
    abstract getResponse(): T;
    getErrorText(): string;
    abort(): void;
    getResponseHeader(header: string): string | null;
    addUploadProgressListener(listener: (p1: ProgressEvent) => void): void;
    removeUploadProgressListener(listener: (p1: ProgressEvent) => void): void;
}
export declare class FetchTextConnection extends FetchConnection<string> {
    getResponse(): string;
}
export declare function newTextConnection(): Connection<string>;
export declare class FetchBytesConnection extends FetchConnection<ArrayBuffer> {
    getResponse(): ArrayBuffer;
}
export declare function newBytesConnection(): Connection<ArrayBuffer>;
export declare class FetchStreamConnection extends FetchConnection<ReadableStream<Uint8Array>> {
    private stream_;
    send(url: string, method: string, body?: NodeJS.ArrayBufferView | Blob | string, headers?: Record<string, string>): Promise<void>;
    getResponse(): ReadableStream;
}
export declare function newStreamConnection(): Connection<ReadableStream<Uint8Array>>;
export declare function newBlobConnection(): Connection<Blob>;
export declare function injectTestConnection(factory: (() => Connection<string>) | null): void;
export {};
