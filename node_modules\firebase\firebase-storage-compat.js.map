{"version": 3, "file": "firebase-storage-compat.js", "sources": ["../util/src/crypt.ts", "../util/src/errors.ts", "../util/src/compat.ts", "../component/src/component.ts", "../storage/src/implementation/constants.ts", "../storage/src/implementation/error.ts", "../storage/src/implementation/connection.ts", "../storage-compat/src/index.ts", "../storage/src/implementation/location.ts", "../storage/src/implementation/failrequest.ts", "../storage/src/implementation/type.ts", "../storage/src/implementation/url.ts", "../storage/src/implementation/utils.ts", "../storage/src/implementation/request.ts", "../storage/src/implementation/backoff.ts", "../storage/src/implementation/fs.ts", "../storage/src/platform/browser/base64.ts", "../storage/src/implementation/string.ts", "../storage/src/implementation/blob.ts", "../storage/src/implementation/json.ts", "../storage/src/implementation/path.ts", "../storage/src/implementation/metadata.ts", "../storage/src/implementation/list.ts", "../storage/src/implementation/requestinfo.ts", "../storage/src/implementation/requests.ts", "../storage/src/implementation/taskenums.ts", "../storage/src/implementation/observer.ts", "../storage/src/implementation/async.ts", "../storage/src/platform/browser/connection.ts", "../storage/src/task.ts", "../storage/src/reference.ts", "../storage/src/service.ts", "../util/src/emulator.ts", "../storage/src/api.ts", "../storage/src/index.ts", "../storage/src/constants.ts", "../storage-compat/src/tasksnapshot.ts", "../storage-compat/src/task.ts", "../storage-compat/src/list.ts", "../storage-compat/src/reference.ts", "../storage-compat/src/service.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst stringToByteArray = function (str: string): number[] {\n  // TODO(user): Use native implementations if/when available\n  const out: number[] = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = (c >> 6) | 192;\n      out[p++] = (c & 63) | 128;\n    } else if (\n      (c & 0xfc00) === 0xd800 &&\n      i + 1 < str.length &&\n      (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00\n    ) {\n      // Surrogate Pair\n      c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);\n      out[p++] = (c >> 18) | 240;\n      out[p++] = ((c >> 12) & 63) | 128;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    } else {\n      out[p++] = (c >> 12) | 224;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    }\n  }\n  return out;\n};\n\n/**\n * Turns an array of numbers into the string given by the concatenation of the\n * characters to which the numbers correspond.\n * @param bytes Array of numbers representing characters.\n * @return Stringification of the array.\n */\nconst byteArrayToString = function (bytes: number[]): string {\n  // TODO(user): Use native implementations if/when available\n  const out: string[] = [];\n  let pos = 0,\n    c = 0;\n  while (pos < bytes.length) {\n    const c1 = bytes[pos++];\n    if (c1 < 128) {\n      out[c++] = String.fromCharCode(c1);\n    } else if (c1 > 191 && c1 < 224) {\n      const c2 = bytes[pos++];\n      out[c++] = String.fromCharCode(((c1 & 31) << 6) | (c2 & 63));\n    } else if (c1 > 239 && c1 < 365) {\n      // Surrogate Pair\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      const c4 = bytes[pos++];\n      const u =\n        (((c1 & 7) << 18) | ((c2 & 63) << 12) | ((c3 & 63) << 6) | (c4 & 63)) -\n        0x10000;\n      out[c++] = String.fromCharCode(0xd800 + (u >> 10));\n      out[c++] = String.fromCharCode(0xdc00 + (u & 1023));\n    } else {\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      out[c++] = String.fromCharCode(\n        ((c1 & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)\n      );\n    }\n  }\n  return out.join('');\n};\n\ninterface Base64 {\n  byteToCharMap_: { [key: number]: string } | null;\n  charToByteMap_: { [key: string]: number } | null;\n  byteToCharMapWebSafe_: { [key: number]: string } | null;\n  charToByteMapWebSafe_: { [key: string]: number } | null;\n  ENCODED_VALS_BASE: string;\n  readonly ENCODED_VALS: string;\n  readonly ENCODED_VALS_WEBSAFE: string;\n  HAS_NATIVE_SUPPORT: boolean;\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string;\n  encodeString(input: string, webSafe?: boolean): string;\n  decodeString(input: string, webSafe: boolean): string;\n  decodeStringToByteArray(input: string, webSafe: boolean): number[];\n  init_(): void;\n}\n\n// We define it as an object literal instead of a class because a class compiled down to es5 can't\n// be treeshaked. https://github.com/rollup/rollup/issues/1691\n// Static lookup maps, lazily populated by init_()\n// TODO(dlarocque): Define this as a class, since we no longer target ES5.\nexport const base64: Base64 = {\n  /**\n   * Maps bytes to characters.\n   */\n  byteToCharMap_: null,\n\n  /**\n   * Maps characters to bytes.\n   */\n  charToByteMap_: null,\n\n  /**\n   * Maps bytes to websafe characters.\n   * @private\n   */\n  byteToCharMapWebSafe_: null,\n\n  /**\n   * Maps websafe characters to bytes.\n   * @private\n   */\n  charToByteMapWebSafe_: null,\n\n  /**\n   * Our default alphabet, shared between\n   * ENCODED_VALS and ENCODED_VALS_WEBSAFE\n   */\n  ENCODED_VALS_BASE:\n    'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz' + '0123456789',\n\n  /**\n   * Our default alphabet. Value 64 (=) is special; it means \"nothing.\"\n   */\n  get ENCODED_VALS() {\n    return this.ENCODED_VALS_BASE + '+/=';\n  },\n\n  /**\n   * Our websafe alphabet.\n   */\n  get ENCODED_VALS_WEBSAFE() {\n    return this.ENCODED_VALS_BASE + '-_.';\n  },\n\n  /**\n   * Whether this browser supports the atob and btoa functions. This extension\n   * started at Mozilla but is now implemented by many browsers. We use the\n   * ASSUME_* variables to avoid pulling in the full useragent detection library\n   * but still allowing the standard per-browser compilations.\n   *\n   */\n  HAS_NATIVE_SUPPORT: typeof atob === 'function',\n\n  /**\n   * Base64-encode an array of bytes.\n   *\n   * @param input An array of bytes (numbers with\n   *     value in [0, 255]) to encode.\n   * @param webSafe Boolean indicating we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string {\n    if (!Array.isArray(input)) {\n      throw Error('encodeByteArray takes an array as a parameter');\n    }\n\n    this.init_();\n\n    const byteToCharMap = webSafe\n      ? this.byteToCharMapWebSafe_!\n      : this.byteToCharMap_!;\n\n    const output = [];\n\n    for (let i = 0; i < input.length; i += 3) {\n      const byte1 = input[i];\n      const haveByte2 = i + 1 < input.length;\n      const byte2 = haveByte2 ? input[i + 1] : 0;\n      const haveByte3 = i + 2 < input.length;\n      const byte3 = haveByte3 ? input[i + 2] : 0;\n\n      const outByte1 = byte1 >> 2;\n      const outByte2 = ((byte1 & 0x03) << 4) | (byte2 >> 4);\n      let outByte3 = ((byte2 & 0x0f) << 2) | (byte3 >> 6);\n      let outByte4 = byte3 & 0x3f;\n\n      if (!haveByte3) {\n        outByte4 = 64;\n\n        if (!haveByte2) {\n          outByte3 = 64;\n        }\n      }\n\n      output.push(\n        byteToCharMap[outByte1],\n        byteToCharMap[outByte2],\n        byteToCharMap[outByte3],\n        byteToCharMap[outByte4]\n      );\n    }\n\n    return output.join('');\n  },\n\n  /**\n   * Base64-encode a string.\n   *\n   * @param input A string to encode.\n   * @param webSafe If true, we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeString(input: string, webSafe?: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return btoa(input);\n    }\n    return this.encodeByteArray(stringToByteArray(input), webSafe);\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * @param input to decode.\n   * @param webSafe True if we should use the\n   *     alternative alphabet.\n   * @return string representing the decoded value.\n   */\n  decodeString(input: string, webSafe: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return atob(input);\n    }\n    return byteArrayToString(this.decodeStringToByteArray(input, webSafe));\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * In base-64 decoding, groups of four characters are converted into three\n   * bytes.  If the encoder did not apply padding, the input length may not\n   * be a multiple of 4.\n   *\n   * In this case, the last group will have fewer than 4 characters, and\n   * padding will be inferred.  If the group has one or two characters, it decodes\n   * to one byte.  If the group has three characters, it decodes to two bytes.\n   *\n   * @param input Input to decode.\n   * @param webSafe True if we should use the web-safe alphabet.\n   * @return bytes representing the decoded value.\n   */\n  decodeStringToByteArray(input: string, webSafe: boolean): number[] {\n    this.init_();\n\n    const charToByteMap = webSafe\n      ? this.charToByteMapWebSafe_!\n      : this.charToByteMap_!;\n\n    const output: number[] = [];\n\n    for (let i = 0; i < input.length; ) {\n      const byte1 = charToByteMap[input.charAt(i++)];\n\n      const haveByte2 = i < input.length;\n      const byte2 = haveByte2 ? charToByteMap[input.charAt(i)] : 0;\n      ++i;\n\n      const haveByte3 = i < input.length;\n      const byte3 = haveByte3 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      const haveByte4 = i < input.length;\n      const byte4 = haveByte4 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      if (byte1 == null || byte2 == null || byte3 == null || byte4 == null) {\n        throw new DecodeBase64StringError();\n      }\n\n      const outByte1 = (byte1 << 2) | (byte2 >> 4);\n      output.push(outByte1);\n\n      if (byte3 !== 64) {\n        const outByte2 = ((byte2 << 4) & 0xf0) | (byte3 >> 2);\n        output.push(outByte2);\n\n        if (byte4 !== 64) {\n          const outByte3 = ((byte3 << 6) & 0xc0) | byte4;\n          output.push(outByte3);\n        }\n      }\n    }\n\n    return output;\n  },\n\n  /**\n   * Lazy static initialization function. Called before\n   * accessing any of the static map variables.\n   * @private\n   */\n  init_() {\n    if (!this.byteToCharMap_) {\n      this.byteToCharMap_ = {};\n      this.charToByteMap_ = {};\n      this.byteToCharMapWebSafe_ = {};\n      this.charToByteMapWebSafe_ = {};\n\n      // We want quick mappings back and forth, so we precompute two maps.\n      for (let i = 0; i < this.ENCODED_VALS.length; i++) {\n        this.byteToCharMap_[i] = this.ENCODED_VALS.charAt(i);\n        this.charToByteMap_[this.byteToCharMap_[i]] = i;\n        this.byteToCharMapWebSafe_[i] = this.ENCODED_VALS_WEBSAFE.charAt(i);\n        this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[i]] = i;\n\n        // Be forgiving when decoding and correctly decode both encodings.\n        if (i >= this.ENCODED_VALS_BASE.length) {\n          this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(i)] = i;\n          this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(i)] = i;\n        }\n      }\n    }\n  }\n};\n\n/**\n * An error encountered while decoding base64 string.\n */\nexport class DecodeBase64StringError extends Error {\n  readonly name = 'DecodeBase64StringError';\n}\n\n/**\n * URL-safe base64 encoding\n */\nexport const base64Encode = function (str: string): string {\n  const utf8Bytes = stringToByteArray(str);\n  return base64.encodeByteArray(utf8Bytes, true);\n};\n\n/**\n * URL-safe base64 encoding (without \".\" padding in the end).\n * e.g. Used in JSON Web Token (JWT) parts.\n */\nexport const base64urlEncodeWithoutPadding = function (str: string): string {\n  // Use base64url encoding and remove padding in the end (dot characters).\n  return base64Encode(str).replace(/\\./g, '');\n};\n\n/**\n * URL-safe base64 decoding\n *\n * NOTE: DO NOT use the global atob() function - it does NOT support the\n * base64Url variant encoding.\n *\n * @param str To be decoded\n * @return Decoded result, if possible\n */\nexport const base64Decode = function (str: string): string | null {\n  try {\n    return base64.decodeString(str, true);\n  } catch (e) {\n    console.error('base64Decode failed: ', e);\n  }\n  return null;\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Constants used in the Firebase Storage library.\n */\n\n/**\n * Domain name for firebase storage.\n */\nexport const DEFAULT_HOST = 'firebasestorage.googleapis.com';\n\n/**\n * The key in Firebase config json for the storage bucket.\n */\nexport const CONFIG_STORAGE_BUCKET_KEY = 'storageBucket';\n\n/**\n * 2 minutes\n *\n * The timeout for all operations except upload.\n */\nexport const DEFAULT_MAX_OPERATION_RETRY_TIME = 2 * 60 * 1000;\n\n/**\n * 10 minutes\n *\n * The timeout for upload.\n */\nexport const DEFAULT_MAX_UPLOAD_RETRY_TIME = 10 * 60 * 1000;\n\n/**\n * 1 second\n */\nexport const DEFAULT_MIN_SLEEP_TIME_MILLIS = 1000;\n\n/**\n * This is the value of Number.MIN_SAFE_INTEGER, which is not well supported\n * enough for us to use it directly.\n */\nexport const MIN_SAFE_INTEGER = -9007199254740991;\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\n\nimport { CONFIG_STORAGE_BUCKET_KEY } from './constants';\n\n/**\n * An error returned by the Firebase Storage SDK.\n * @public\n */\nexport class StorageError extends FirebaseError {\n  private readonly _baseMessage: string;\n  /**\n   * Stores custom error data unique to the `StorageError`.\n   */\n  customData: { serverResponse: string | null } = { serverResponse: null };\n\n  /**\n   * @param code - A `StorageErrorCode` string to be prefixed with 'storage/' and\n   *  added to the end of the message.\n   * @param message  - Error message.\n   * @param status_ - Corresponding HTTP Status Code\n   */\n  constructor(code: StorageErrorCode, message: string, private status_ = 0) {\n    super(\n      prependCode(code),\n      `Firebase Storage: ${message} (${prependCode(code)})`\n    );\n    this._baseMessage = this.message;\n    // Without this, `instanceof StorageError`, in tests for example,\n    // returns false.\n    Object.setPrototypeOf(this, StorageError.prototype);\n  }\n\n  get status(): number {\n    return this.status_;\n  }\n\n  set status(status: number) {\n    this.status_ = status;\n  }\n\n  /**\n   * Compares a `StorageErrorCode` against this error's code, filtering out the prefix.\n   */\n  _codeEquals(code: StorageErrorCode): boolean {\n    return prependCode(code) === this.code;\n  }\n\n  /**\n   * Optional response message that was added by the server.\n   */\n  get serverResponse(): null | string {\n    return this.customData.serverResponse;\n  }\n\n  set serverResponse(serverResponse: string | null) {\n    this.customData.serverResponse = serverResponse;\n    if (this.customData.serverResponse) {\n      this.message = `${this._baseMessage}\\n${this.customData.serverResponse}`;\n    } else {\n      this.message = this._baseMessage;\n    }\n  }\n}\n\nexport const errors = {};\n\n/**\n * @public\n * Error codes that can be attached to `StorageError` objects.\n */\nexport enum StorageErrorCode {\n  // Shared between all platforms\n  UNKNOWN = 'unknown',\n  OBJECT_NOT_FOUND = 'object-not-found',\n  BUCKET_NOT_FOUND = 'bucket-not-found',\n  PROJECT_NOT_FOUND = 'project-not-found',\n  QUOTA_EXCEEDED = 'quota-exceeded',\n  UNAUTHENTICATED = 'unauthenticated',\n  UNAUTHORIZED = 'unauthorized',\n  UNAUTHORIZED_APP = 'unauthorized-app',\n  RETRY_LIMIT_EXCEEDED = 'retry-limit-exceeded',\n  INVALID_CHECKSUM = 'invalid-checksum',\n  CANCELED = 'canceled',\n  // JS specific\n  INVALID_EVENT_NAME = 'invalid-event-name',\n  INVALID_URL = 'invalid-url',\n  INVALID_DEFAULT_BUCKET = 'invalid-default-bucket',\n  NO_DEFAULT_BUCKET = 'no-default-bucket',\n  CANNOT_SLICE_BLOB = 'cannot-slice-blob',\n  SERVER_FILE_WRONG_SIZE = 'server-file-wrong-size',\n  NO_DOWNLOAD_URL = 'no-download-url',\n  INVALID_ARGUMENT = 'invalid-argument',\n  INVALID_ARGUMENT_COUNT = 'invalid-argument-count',\n  APP_DELETED = 'app-deleted',\n  INVALID_ROOT_OPERATION = 'invalid-root-operation',\n  INVALID_FORMAT = 'invalid-format',\n  INTERNAL_ERROR = 'internal-error',\n  UNSUPPORTED_ENVIRONMENT = 'unsupported-environment'\n}\n\nexport function prependCode(code: StorageErrorCode): string {\n  return 'storage/' + code;\n}\n\nexport function unknown(): StorageError {\n  const message =\n    'An unknown error occurred, please check the error payload for ' +\n    'server response.';\n  return new StorageError(StorageErrorCode.UNKNOWN, message);\n}\n\nexport function objectNotFound(path: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.OBJECT_NOT_FOUND,\n    \"Object '\" + path + \"' does not exist.\"\n  );\n}\n\nexport function bucketNotFound(bucket: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.BUCKET_NOT_FOUND,\n    \"Bucket '\" + bucket + \"' does not exist.\"\n  );\n}\n\nexport function projectNotFound(project: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.PROJECT_NOT_FOUND,\n    \"Project '\" + project + \"' does not exist.\"\n  );\n}\n\nexport function quotaExceeded(bucket: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.QUOTA_EXCEEDED,\n    \"Quota for bucket '\" +\n      bucket +\n      \"' exceeded, please view quota on \" +\n      'https://firebase.google.com/pricing/.'\n  );\n}\n\nexport function unauthenticated(): StorageError {\n  const message =\n    'User is not authenticated, please authenticate using Firebase ' +\n    'Authentication and try again.';\n  return new StorageError(StorageErrorCode.UNAUTHENTICATED, message);\n}\n\nexport function unauthorizedApp(): StorageError {\n  return new StorageError(\n    StorageErrorCode.UNAUTHORIZED_APP,\n    'This app does not have permission to access Firebase Storage on this project.'\n  );\n}\n\nexport function unauthorized(path: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.UNAUTHORIZED,\n    \"User does not have permission to access '\" + path + \"'.\"\n  );\n}\n\nexport function retryLimitExceeded(): StorageError {\n  return new StorageError(\n    StorageErrorCode.RETRY_LIMIT_EXCEEDED,\n    'Max retry time for operation exceeded, please try again.'\n  );\n}\n\nexport function invalidChecksum(\n  path: string,\n  checksum: string,\n  calculated: string\n): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_CHECKSUM,\n    \"Uploaded/downloaded object '\" +\n      path +\n      \"' has checksum '\" +\n      checksum +\n      \"' which does not match '\" +\n      calculated +\n      \"'. Please retry the upload/download.\"\n  );\n}\n\nexport function canceled(): StorageError {\n  return new StorageError(\n    StorageErrorCode.CANCELED,\n    'User canceled the upload/download.'\n  );\n}\n\nexport function invalidEventName(name: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_EVENT_NAME,\n    \"Invalid event name '\" + name + \"'.\"\n  );\n}\n\nexport function invalidUrl(url: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_URL,\n    \"Invalid URL '\" + url + \"'.\"\n  );\n}\n\nexport function invalidDefaultBucket(bucket: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_DEFAULT_BUCKET,\n    \"Invalid default bucket '\" + bucket + \"'.\"\n  );\n}\n\nexport function noDefaultBucket(): StorageError {\n  return new StorageError(\n    StorageErrorCode.NO_DEFAULT_BUCKET,\n    'No default bucket ' +\n      \"found. Did you set the '\" +\n      CONFIG_STORAGE_BUCKET_KEY +\n      \"' property when initializing the app?\"\n  );\n}\n\nexport function cannotSliceBlob(): StorageError {\n  return new StorageError(\n    StorageErrorCode.CANNOT_SLICE_BLOB,\n    'Cannot slice blob for upload. Please retry the upload.'\n  );\n}\n\nexport function serverFileWrongSize(): StorageError {\n  return new StorageError(\n    StorageErrorCode.SERVER_FILE_WRONG_SIZE,\n    'Server recorded incorrect upload file size, please retry the upload.'\n  );\n}\n\nexport function noDownloadURL(): StorageError {\n  return new StorageError(\n    StorageErrorCode.NO_DOWNLOAD_URL,\n    'The given file does not have any download URLs.'\n  );\n}\n\nexport function missingPolyFill(polyFill: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.UNSUPPORTED_ENVIRONMENT,\n    `${polyFill} is missing. Make sure to install the required polyfills. See https://firebase.google.com/docs/web/environments-js-sdk#polyfills for more information.`\n  );\n}\n\n/**\n * @internal\n */\nexport function invalidArgument(message: string): StorageError {\n  return new StorageError(StorageErrorCode.INVALID_ARGUMENT, message);\n}\n\nexport function invalidArgumentCount(\n  argMin: number,\n  argMax: number,\n  fnName: string,\n  real: number\n): StorageError {\n  let countPart;\n  let plural;\n  if (argMin === argMax) {\n    countPart = argMin;\n    plural = argMin === 1 ? 'argument' : 'arguments';\n  } else {\n    countPart = 'between ' + argMin + ' and ' + argMax;\n    plural = 'arguments';\n  }\n  return new StorageError(\n    StorageErrorCode.INVALID_ARGUMENT_COUNT,\n    'Invalid argument count in `' +\n      fnName +\n      '`: Expected ' +\n      countPart +\n      ' ' +\n      plural +\n      ', received ' +\n      real +\n      '.'\n  );\n}\n\nexport function appDeleted(): StorageError {\n  return new StorageError(\n    StorageErrorCode.APP_DELETED,\n    'The Firebase app was deleted.'\n  );\n}\n\n/**\n * @param name - The name of the operation that was invalid.\n *\n * @internal\n */\nexport function invalidRootOperation(name: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_ROOT_OPERATION,\n    \"The operation '\" +\n      name +\n      \"' cannot be performed on a root reference, create a non-root \" +\n      \"reference using child, such as .child('file.png').\"\n  );\n}\n\n/**\n * @param format - The format that was not valid.\n * @param message - A message describing the format violation.\n */\nexport function invalidFormat(format: string, message: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_FORMAT,\n    \"String does not match format '\" + format + \"': \" + message\n  );\n}\n\n/**\n * @param message - A message describing the internal error.\n */\nexport function unsupportedEnvironment(message: string): StorageError {\n  throw new StorageError(StorageErrorCode.UNSUPPORTED_ENVIRONMENT, message);\n}\n\n/**\n * @param message - A message describing the internal error.\n */\nexport function internalError(message: string): StorageError {\n  throw new StorageError(\n    StorageErrorCode.INTERNAL_ERROR,\n    'Internal error: ' + message\n  );\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** Network headers */\nexport type Headers = Record<string, string>;\n\n/** Response type exposed by the networking APIs. */\nexport type ConnectionType =\n  | string\n  | ArrayBuffer\n  | Blob\n  | ReadableStream<Uint8Array>;\n\n/**\n * A lightweight wrapper around XMLHttpRequest with a\n * goog.net.XhrIo-like interface.\n *\n * You can create a new connection by invoking `newTextConnection()`,\n * `newBytesConnection()` or `newStreamConnection()`.\n */\nexport interface Connection<T extends ConnectionType> {\n  /**\n   * Sends a request to the provided URL.\n   *\n   * This method never rejects its promise. In case of encountering an error,\n   * it sets an error code internally which can be accessed by calling\n   * getErrorCode() by callers.\n   */\n  send(\n    url: string,\n    method: string,\n    body?: ArrayBufferView | Blob | string | null,\n    headers?: Headers\n  ): Promise<void>;\n\n  getErrorCode(): ErrorCode;\n\n  getStatus(): number;\n\n  getResponse(): T;\n\n  getErrorText(): string;\n\n  /**\n   * Abort the request.\n   */\n  abort(): void;\n\n  getResponseHeader(header: string): string | null;\n\n  addUploadProgressListener(listener: (p1: ProgressEvent) => void): void;\n\n  removeUploadProgressListener(listener: (p1: ProgressEvent) => void): void;\n}\n\n/**\n * Error codes for requests made by the XhrIo wrapper.\n */\nexport enum ErrorCode {\n  NO_ERROR = 0,\n  NETWORK_ERROR = 1,\n  ABORT = 2\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport firebase from '@firebase/app-compat';\nimport { _FirebaseNamespace } from '@firebase/app-types/private';\nimport {\n  StringFormat,\n  _TaskEvent as TaskEvent,\n  _TaskState as TaskState\n} from '@firebase/storage';\n\nimport { ReferenceCompat } from './reference';\nimport { StorageServiceCompat } from './service';\nimport * as types from '@firebase/storage-types';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactoryOptions\n} from '@firebase/component';\n\nimport { name, version } from '../package.json';\n\n/**\n * Type constant for Firebase Storage.\n */\nconst STORAGE_TYPE = 'storage-compat';\n\nfunction factory(\n  container: ComponentContainer,\n  { instanceIdentifier: url }: InstanceFactoryOptions\n): types.FirebaseStorage {\n  // Dependencies\n  const app = container.getProvider('app-compat').getImmediate();\n  const storageExp = container\n    .getProvider('storage')\n    .getImmediate({ identifier: url });\n\n  const storageServiceCompat: StorageServiceCompat = new StorageServiceCompat(\n    app,\n    storageExp\n  );\n  return storageServiceCompat;\n}\n\nexport function registerStorage(instance: _FirebaseNamespace): void {\n  const namespaceExports = {\n    // no-inline\n    TaskState,\n    TaskEvent,\n    StringFormat,\n    Storage: StorageServiceCompat,\n    Reference: ReferenceCompat\n  };\n  instance.INTERNAL.registerComponent(\n    new Component(STORAGE_TYPE, factory, ComponentType.PUBLIC)\n      .setServiceProps(namespaceExports)\n      .setMultipleInstances(true)\n  );\n\n  instance.registerVersion(name, version);\n}\n\nregisterStorage(firebase as unknown as _FirebaseNamespace);\n\n/**\n * Define extension behavior for `registerStorage`\n */\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    storage?: {\n      (app?: FirebaseApp, url?: string): types.FirebaseStorage;\n      Storage: typeof types.FirebaseStorage;\n\n      StringFormat: {\n        BASE64: types.StringFormat;\n        BASE64URL: types.StringFormat;\n        DATA_URL: types.StringFormat;\n        RAW: types.StringFormat;\n      };\n      TaskEvent: {\n        STATE_CHANGED: types.TaskEvent;\n      };\n      TaskState: {\n        CANCELED: types.TaskState;\n        ERROR: types.TaskState;\n        PAUSED: types.TaskState;\n        RUNNING: types.TaskState;\n        SUCCESS: types.TaskState;\n      };\n    };\n  }\n  interface FirebaseApp {\n    storage?(storageBucket?: string): types.FirebaseStorage;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Functionality related to the parsing/composition of bucket/\n * object location.\n */\n\nimport { invalidDefaultBucket, invalidUrl } from './error';\nimport { DEFAULT_HOST } from './constants';\n\n/**\n * Firebase Storage location data.\n *\n * @internal\n */\nexport class Location {\n  private path_: string;\n\n  constructor(public readonly bucket: string, path: string) {\n    this.path_ = path;\n  }\n\n  get path(): string {\n    return this.path_;\n  }\n\n  get isRoot(): boolean {\n    return this.path.length === 0;\n  }\n\n  fullServerUrl(): string {\n    const encode = encodeURIComponent;\n    return '/b/' + encode(this.bucket) + '/o/' + encode(this.path);\n  }\n\n  bucketOnlyServerUrl(): string {\n    const encode = encodeURIComponent;\n    return '/b/' + encode(this.bucket) + '/o';\n  }\n\n  static makeFromBucketSpec(bucketString: string, host: string): Location {\n    let bucketLocation;\n    try {\n      bucketLocation = Location.makeFromUrl(bucketString, host);\n    } catch (e) {\n      // Not valid URL, use as-is. This lets you put bare bucket names in\n      // config.\n      return new Location(bucketString, '');\n    }\n    if (bucketLocation.path === '') {\n      return bucketLocation;\n    } else {\n      throw invalidDefaultBucket(bucketString);\n    }\n  }\n\n  static makeFromUrl(url: string, host: string): Location {\n    let location: Location | null = null;\n    const bucketDomain = '([A-Za-z0-9.\\\\-_]+)';\n\n    function gsModify(loc: Location): void {\n      if (loc.path.charAt(loc.path.length - 1) === '/') {\n        loc.path_ = loc.path_.slice(0, -1);\n      }\n    }\n    const gsPath = '(/(.*))?$';\n    const gsRegex = new RegExp('^gs://' + bucketDomain + gsPath, 'i');\n    const gsIndices = { bucket: 1, path: 3 };\n\n    function httpModify(loc: Location): void {\n      loc.path_ = decodeURIComponent(loc.path);\n    }\n    const version = 'v[A-Za-z0-9_]+';\n    const firebaseStorageHost = host.replace(/[.]/g, '\\\\.');\n    const firebaseStoragePath = '(/([^?#]*).*)?$';\n    const firebaseStorageRegExp = new RegExp(\n      `^https?://${firebaseStorageHost}/${version}/b/${bucketDomain}/o${firebaseStoragePath}`,\n      'i'\n    );\n    const firebaseStorageIndices = { bucket: 1, path: 3 };\n\n    const cloudStorageHost =\n      host === DEFAULT_HOST\n        ? '(?:storage.googleapis.com|storage.cloud.google.com)'\n        : host;\n    const cloudStoragePath = '([^?#]*)';\n    const cloudStorageRegExp = new RegExp(\n      `^https?://${cloudStorageHost}/${bucketDomain}/${cloudStoragePath}`,\n      'i'\n    );\n    const cloudStorageIndices = { bucket: 1, path: 2 };\n\n    const groups = [\n      { regex: gsRegex, indices: gsIndices, postModify: gsModify },\n      {\n        regex: firebaseStorageRegExp,\n        indices: firebaseStorageIndices,\n        postModify: httpModify\n      },\n      {\n        regex: cloudStorageRegExp,\n        indices: cloudStorageIndices,\n        postModify: httpModify\n      }\n    ];\n    for (let i = 0; i < groups.length; i++) {\n      const group = groups[i];\n      const captures = group.regex.exec(url);\n      if (captures) {\n        const bucketValue = captures[group.indices.bucket];\n        let pathValue = captures[group.indices.path];\n        if (!pathValue) {\n          pathValue = '';\n        }\n        location = new Location(bucketValue, pathValue);\n        group.postModify(location);\n        break;\n      }\n    }\n    if (location == null) {\n      throw invalidUrl(url);\n    }\n    return location;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { StorageError } from './error';\nimport { Request } from './request';\n\n/**\n * A request whose promise always fails.\n */\nexport class FailRequest<T> implements Request<T> {\n  promise_: Promise<T>;\n\n  constructor(error: StorageError) {\n    this.promise_ = Promise.reject<T>(error);\n  }\n\n  /** @inheritDoc */\n  getPromise(): Promise<T> {\n    return this.promise_;\n  }\n\n  /** @inheritDoc */\n  cancel(_appDelete = false): void {}\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { invalidArgument } from './error';\n\nexport function isJustDef<T>(p: T | null | undefined): p is T | null {\n  return p !== void 0;\n}\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function isFunction(p: unknown): p is Function {\n  return typeof p === 'function';\n}\n\nexport function isNonArrayObject(p: unknown): boolean {\n  return typeof p === 'object' && !Array.isArray(p);\n}\n\nexport function isString(p: unknown): p is string {\n  return typeof p === 'string' || p instanceof String;\n}\n\nexport function isNativeBlob(p: unknown): p is Blob {\n  return isNativeBlobDefined() && p instanceof Blob;\n}\n\nexport function isNativeBlobDefined(): boolean {\n  return typeof Blob !== 'undefined';\n}\n\nexport function validateNumber(\n  argument: string,\n  minValue: number,\n  maxValue: number,\n  value: number\n): void {\n  if (value < minValue) {\n    throw invalidArgument(\n      `Invalid value for '${argument}'. Expected ${minValue} or greater.`\n    );\n  }\n  if (value > maxValue) {\n    throw invalidArgument(\n      `Invalid value for '${argument}'. Expected ${maxValue} or less.`\n    );\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Functions to create and manipulate URLs for the server API.\n */\nimport { UrlParams } from './requestinfo';\n\nexport function makeUrl(\n  urlPart: string,\n  host: string,\n  protocol: string\n): string {\n  let origin = host;\n  if (protocol == null) {\n    origin = `https://${host}`;\n  }\n  return `${protocol}://${origin}/v0${urlPart}`;\n}\n\nexport function makeQueryString(params: UrlParams): string {\n  const encode = encodeURIComponent;\n  let queryPart = '?';\n  for (const key in params) {\n    if (params.hasOwnProperty(key)) {\n      const nextPart = encode(key) + '=' + encode(params[key]);\n      queryPart = queryPart + nextPart + '&';\n    }\n  }\n\n  // Chop off the extra '&' or '?' on the end\n  queryPart = queryPart.slice(0, -1);\n  return queryPart;\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Checks the status code to see if the action should be retried.\n *\n * @param status Current HTTP status code returned by server.\n * @param additionalRetryCodes additional retry codes to check against\n */\nexport function isRetryStatusCode(\n  status: number,\n  additionalRetryCodes: number[]\n): boolean {\n  // The codes for which to retry came from this page:\n  // https://cloud.google.com/storage/docs/exponential-backoff\n  const isFiveHundredCode = status >= 500 && status < 600;\n  const extraRetryCodes = [\n    // Request Timeout: web server didn't receive full request in time.\n    408,\n    // Too Many Requests: you're getting rate-limited, basically.\n    429\n  ];\n  const isExtraRetryCode = extraRetryCodes.indexOf(status) !== -1;\n  const isAdditionalRetryCode = additionalRetryCodes.indexOf(status) !== -1;\n  return isFiveHundredCode || isExtraRetryCode || isAdditionalRetryCode;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Defines methods used to actually send HTTP requests from\n * abstract representations.\n */\n\nimport { id as backoffId, start, stop } from './backoff';\nimport { appDeleted, canceled, retryLimitExceeded, unknown } from './error';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RequestHandler, RequestInfo } from './requestinfo';\nimport { isJustDef } from './type';\nimport { makeQueryString } from './url';\nimport { Connection, ErrorCode, Headers, ConnectionType } from './connection';\nimport { isRetryStatusCode } from './utils';\n\nexport interface Request<T> {\n  getPromise(): Promise<T>;\n\n  /**\n   * Cancels the request. IMPORTANT: the promise may still be resolved with an\n   * appropriate value (if the request is finished before you call this method,\n   * but the promise has not yet been resolved), so don't just assume it will be\n   * rejected if you call this function.\n   * @param appDelete - True if the cancelation came from the app being deleted.\n   */\n  cancel(appDelete?: boolean): void;\n}\n\n/**\n * Handles network logic for all Storage Requests, including error reporting and\n * retries with backoff.\n *\n * @param I - the type of the backend's network response.\n * @param - O the output type used by the rest of the SDK. The conversion\n * happens in the specified `callback_`.\n */\nclass NetworkRequest<I extends ConnectionType, O> implements Request<O> {\n  private pendingConnection_: Connection<I> | null = null;\n  private backoffId_: backoffId | null = null;\n  private resolve_!: (value?: O | PromiseLike<O>) => void;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private reject_!: (reason?: any) => void;\n  private canceled_: boolean = false;\n  private appDelete_: boolean = false;\n  private promise_: Promise<O>;\n\n  constructor(\n    private url_: string,\n    private method_: string,\n    private headers_: Headers,\n    private body_: string | Blob | Uint8Array | null,\n    private successCodes_: number[],\n    private additionalRetryCodes_: number[],\n    private callback_: RequestHandler<I, O>,\n    private errorCallback_: ErrorHandler | null,\n    private timeout_: number,\n    private progressCallback_: ((p1: number, p2: number) => void) | null,\n    private connectionFactory_: () => Connection<I>,\n    private retry = true\n  ) {\n    this.promise_ = new Promise((resolve, reject) => {\n      this.resolve_ = resolve as (value?: O | PromiseLike<O>) => void;\n      this.reject_ = reject;\n      this.start_();\n    });\n  }\n\n  /**\n   * Actually starts the retry loop.\n   */\n  private start_(): void {\n    const doTheRequest: (\n      backoffCallback: (success: boolean, ...p2: unknown[]) => void,\n      canceled: boolean\n    ) => void = (backoffCallback, canceled) => {\n      if (canceled) {\n        backoffCallback(false, new RequestEndStatus(false, null, true));\n        return;\n      }\n      const connection = this.connectionFactory_();\n      this.pendingConnection_ = connection;\n\n      const progressListener: (\n        progressEvent: ProgressEvent\n      ) => void = progressEvent => {\n        const loaded = progressEvent.loaded;\n        const total = progressEvent.lengthComputable ? progressEvent.total : -1;\n        if (this.progressCallback_ !== null) {\n          this.progressCallback_(loaded, total);\n        }\n      };\n      if (this.progressCallback_ !== null) {\n        connection.addUploadProgressListener(progressListener);\n      }\n\n      // connection.send() never rejects, so we don't need to have a error handler or use catch on the returned promise.\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      connection\n        .send(this.url_, this.method_, this.body_, this.headers_)\n        .then(() => {\n          if (this.progressCallback_ !== null) {\n            connection.removeUploadProgressListener(progressListener);\n          }\n          this.pendingConnection_ = null;\n          const hitServer = connection.getErrorCode() === ErrorCode.NO_ERROR;\n          const status = connection.getStatus();\n          if (\n            !hitServer ||\n            (isRetryStatusCode(status, this.additionalRetryCodes_) &&\n              this.retry)\n          ) {\n            const wasCanceled = connection.getErrorCode() === ErrorCode.ABORT;\n            backoffCallback(\n              false,\n              new RequestEndStatus(false, null, wasCanceled)\n            );\n            return;\n          }\n          const successCode = this.successCodes_.indexOf(status) !== -1;\n          backoffCallback(true, new RequestEndStatus(successCode, connection));\n        });\n    };\n\n    /**\n     * @param requestWentThrough - True if the request eventually went\n     *     through, false if it hit the retry limit or was canceled.\n     */\n    const backoffDone: (\n      requestWentThrough: boolean,\n      status: RequestEndStatus<I>\n    ) => void = (requestWentThrough, status) => {\n      const resolve = this.resolve_;\n      const reject = this.reject_;\n      const connection = status.connection as Connection<I>;\n      if (status.wasSuccessCode) {\n        try {\n          const result = this.callback_(connection, connection.getResponse());\n          if (isJustDef(result)) {\n            resolve(result);\n          } else {\n            resolve();\n          }\n        } catch (e) {\n          reject(e);\n        }\n      } else {\n        if (connection !== null) {\n          const err = unknown();\n          err.serverResponse = connection.getErrorText();\n          if (this.errorCallback_) {\n            reject(this.errorCallback_(connection, err));\n          } else {\n            reject(err);\n          }\n        } else {\n          if (status.canceled) {\n            const err = this.appDelete_ ? appDeleted() : canceled();\n            reject(err);\n          } else {\n            const err = retryLimitExceeded();\n            reject(err);\n          }\n        }\n      }\n    };\n    if (this.canceled_) {\n      backoffDone(false, new RequestEndStatus(false, null, true));\n    } else {\n      this.backoffId_ = start(doTheRequest, backoffDone, this.timeout_);\n    }\n  }\n\n  /** @inheritDoc */\n  getPromise(): Promise<O> {\n    return this.promise_;\n  }\n\n  /** @inheritDoc */\n  cancel(appDelete?: boolean): void {\n    this.canceled_ = true;\n    this.appDelete_ = appDelete || false;\n    if (this.backoffId_ !== null) {\n      stop(this.backoffId_);\n    }\n    if (this.pendingConnection_ !== null) {\n      this.pendingConnection_.abort();\n    }\n  }\n}\n\n/**\n * A collection of information about the result of a network request.\n * @param opt_canceled - Defaults to false.\n */\nexport class RequestEndStatus<I extends ConnectionType> {\n  /**\n   * True if the request was canceled.\n   */\n  canceled: boolean;\n\n  constructor(\n    public wasSuccessCode: boolean,\n    public connection: Connection<I> | null,\n    canceled?: boolean\n  ) {\n    this.canceled = !!canceled;\n  }\n}\n\nexport function addAuthHeader_(\n  headers: Headers,\n  authToken: string | null\n): void {\n  if (authToken !== null && authToken.length > 0) {\n    headers['Authorization'] = 'Firebase ' + authToken;\n  }\n}\n\nexport function addVersionHeader_(\n  headers: Headers,\n  firebaseVersion?: string\n): void {\n  headers['X-Firebase-Storage-Version'] =\n    'webjs/' + (firebaseVersion ?? 'AppManager');\n}\n\nexport function addGmpidHeader_(headers: Headers, appId: string | null): void {\n  if (appId) {\n    headers['X-Firebase-GMPID'] = appId;\n  }\n}\n\nexport function addAppCheckHeader_(\n  headers: Headers,\n  appCheckToken: string | null\n): void {\n  if (appCheckToken !== null) {\n    headers['X-Firebase-AppCheck'] = appCheckToken;\n  }\n}\n\nexport function makeRequest<I extends ConnectionType, O>(\n  requestInfo: RequestInfo<I, O>,\n  appId: string | null,\n  authToken: string | null,\n  appCheckToken: string | null,\n  requestFactory: () => Connection<I>,\n  firebaseVersion?: string,\n  retry = true\n): Request<O> {\n  const queryPart = makeQueryString(requestInfo.urlParams);\n  const url = requestInfo.url + queryPart;\n  const headers = Object.assign({}, requestInfo.headers);\n  addGmpidHeader_(headers, appId);\n  addAuthHeader_(headers, authToken);\n  addVersionHeader_(headers, firebaseVersion);\n  addAppCheckHeader_(headers, appCheckToken);\n  return new NetworkRequest<I, O>(\n    url,\n    requestInfo.method,\n    headers,\n    requestInfo.body,\n    requestInfo.successCodes,\n    requestInfo.additionalRetryCodes,\n    requestInfo.handler,\n    requestInfo.errorHandler,\n    requestInfo.timeout,\n    requestInfo.progressCallback,\n    requestFactory,\n    retry\n  );\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Provides a method for running a function with exponential\n * backoff.\n */\ntype id = (p1: boolean) => void;\n\nexport { id };\n\n/**\n * Accepts a callback for an action to perform (`doRequest`),\n * and then a callback for when the backoff has completed (`backoffCompleteCb`).\n * The callback sent to start requires an argument to call (`onRequestComplete`).\n * When `start` calls `doRequest`, it passes a callback for when the request has\n * completed, `onRequestComplete`. Based on this, the backoff continues, with\n * another call to `doRequest` and the above loop continues until the timeout\n * is hit, or a successful response occurs.\n * @description\n * @param doRequest Callback to perform request\n * @param backoffCompleteCb Callback to call when backoff has been completed\n */\nexport function start(\n  doRequest: (\n    onRequestComplete: (success: boolean) => void,\n    canceled: boolean\n  ) => void,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  backoffCompleteCb: (...args: any[]) => unknown,\n  timeout: number\n): id {\n  // TODO(andysoto): make this code cleaner (probably refactor into an actual\n  // type instead of a bunch of functions with state shared in the closure)\n  let waitSeconds = 1;\n  // Would type this as \"number\" but that doesn't work for Node so ¯\\_(ツ)_/¯\n  // TODO: find a way to exclude Node type definition for storage because storage only works in browser\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let retryTimeoutId: any = null;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let globalTimeoutId: any = null;\n  let hitTimeout = false;\n  let cancelState = 0;\n\n  function canceled(): boolean {\n    return cancelState === 2;\n  }\n  let triggeredCallback = false;\n\n  function triggerCallback(...args: any[]): void {\n    if (!triggeredCallback) {\n      triggeredCallback = true;\n      backoffCompleteCb.apply(null, args);\n    }\n  }\n\n  function callWithDelay(millis: number): void {\n    retryTimeoutId = setTimeout(() => {\n      retryTimeoutId = null;\n      doRequest(responseHandler, canceled());\n    }, millis);\n  }\n\n  function clearGlobalTimeout(): void {\n    if (globalTimeoutId) {\n      clearTimeout(globalTimeoutId);\n    }\n  }\n\n  function responseHandler(success: boolean, ...args: any[]): void {\n    if (triggeredCallback) {\n      clearGlobalTimeout();\n      return;\n    }\n    if (success) {\n      clearGlobalTimeout();\n      triggerCallback.call(null, success, ...args);\n      return;\n    }\n    const mustStop = canceled() || hitTimeout;\n    if (mustStop) {\n      clearGlobalTimeout();\n      triggerCallback.call(null, success, ...args);\n      return;\n    }\n    if (waitSeconds < 64) {\n      /* TODO(andysoto): don't back off so quickly if we know we're offline. */\n      waitSeconds *= 2;\n    }\n    let waitMillis;\n    if (cancelState === 1) {\n      cancelState = 2;\n      waitMillis = 0;\n    } else {\n      waitMillis = (waitSeconds + Math.random()) * 1000;\n    }\n    callWithDelay(waitMillis);\n  }\n  let stopped = false;\n\n  function stop(wasTimeout: boolean): void {\n    if (stopped) {\n      return;\n    }\n    stopped = true;\n    clearGlobalTimeout();\n    if (triggeredCallback) {\n      return;\n    }\n    if (retryTimeoutId !== null) {\n      if (!wasTimeout) {\n        cancelState = 2;\n      }\n      clearTimeout(retryTimeoutId);\n      callWithDelay(0);\n    } else {\n      if (!wasTimeout) {\n        cancelState = 1;\n      }\n    }\n  }\n  callWithDelay(0);\n  globalTimeoutId = setTimeout(() => {\n    hitTimeout = true;\n    stop(true);\n  }, timeout);\n  return stop;\n}\n\n/**\n * Stops the retry loop from repeating.\n * If the function is currently \"in between\" retries, it is invoked immediately\n * with the second parameter as \"true\". Otherwise, it will be invoked once more\n * after the current invocation finishes iff the current invocation would have\n * triggered another retry.\n */\nexport function stop(id: id): void {\n  id(false);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Some methods copied from goog.fs.\n * We don't include goog.fs because it pulls in a bunch of Deferred code that\n * bloats the size of the released binary.\n */\nimport { isNativeBlobDefined } from './type';\nimport { StorageErrorCode, StorageError } from './error';\n\nfunction getBlobBuilder(): typeof IBlobBuilder | undefined {\n  if (typeof BlobBuilder !== 'undefined') {\n    return BlobBuilder;\n  } else if (typeof WebKitBlobBuilder !== 'undefined') {\n    return WebKitBlobBuilder;\n  } else {\n    return undefined;\n  }\n}\n\n/**\n * Concatenates one or more values together and converts them to a Blob.\n *\n * @param args The values that will make up the resulting blob.\n * @return The blob.\n */\nexport function getBlob(...args: Array<string | Blob | ArrayBuffer>): Blob {\n  const BlobBuilder = getBlobBuilder();\n  if (BlobBuilder !== undefined) {\n    const bb = new BlobBuilder();\n    for (let i = 0; i < args.length; i++) {\n      bb.append(args[i]);\n    }\n    return bb.getBlob();\n  } else {\n    if (isNativeBlobDefined()) {\n      return new Blob(args);\n    } else {\n      throw new StorageError(\n        StorageErrorCode.UNSUPPORTED_ENVIRONMENT,\n        \"This browser doesn't seem to support creating Blobs\"\n      );\n    }\n  }\n}\n\n/**\n * Slices the blob. The returned blob contains data from the start byte\n * (inclusive) till the end byte (exclusive). Negative indices cannot be used.\n *\n * @param blob The blob to be sliced.\n * @param start Index of the starting byte.\n * @param end Index of the ending byte.\n * @return The blob slice or null if not supported.\n */\nexport function sliceBlob(blob: Blob, start: number, end: number): Blob | null {\n  if (blob.webkitSlice) {\n    return blob.webkitSlice(start, end);\n  } else if (blob.mozSlice) {\n    return blob.mozSlice(start, end);\n  } else if (blob.slice) {\n    return blob.slice(start, end);\n  }\n  return null;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { missingPolyFill } from '../../implementation/error';\n\n/** Converts a Base64 encoded string to a binary string. */\nexport function decodeBase64(encoded: string): string {\n  if (typeof atob === 'undefined') {\n    throw missingPolyFill('base-64');\n  }\n  return atob(encoded);\n}\n\nexport function decodeUint8Array(data: Uint8Array): string {\n  return new TextDecoder().decode(data);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { unknown, invalidFormat } from './error';\nimport { decodeBase64 } from '../platform/base64';\n\n/**\n * An enumeration of the possible string formats for upload.\n * @public\n */\nexport type StringFormat = (typeof StringFormat)[keyof typeof StringFormat];\n/**\n * An enumeration of the possible string formats for upload.\n * @public\n */\nexport const StringFormat = {\n  /**\n   * Indicates the string should be interpreted \"raw\", that is, as normal text.\n   * The string will be interpreted as UTF-16, then uploaded as a UTF-8 byte\n   * sequence.\n   * Example: The string 'Hello! \\\\ud83d\\\\ude0a' becomes the byte sequence\n   * 48 65 6c 6c 6f 21 20 f0 9f 98 8a\n   */\n  RAW: 'raw',\n  /**\n   * Indicates the string should be interpreted as base64-encoded data.\n   * Padding characters (trailing '='s) are optional.\n   * Example: The string 'rWmO++E6t7/rlw==' becomes the byte sequence\n   * ad 69 8e fb e1 3a b7 bf eb 97\n   */\n  BASE64: 'base64',\n  /**\n   * Indicates the string should be interpreted as base64url-encoded data.\n   * Padding characters (trailing '='s) are optional.\n   * Example: The string 'rWmO--E6t7_rlw==' becomes the byte sequence\n   * ad 69 8e fb e1 3a b7 bf eb 97\n   */\n  BASE64URL: 'base64url',\n  /**\n   * Indicates the string is a data URL, such as one obtained from\n   * canvas.toDataURL().\n   * Example: the string 'data:application/octet-stream;base64,aaaa'\n   * becomes the byte sequence\n   * 69 a6 9a\n   * (the content-type \"application/octet-stream\" is also applied, but can\n   * be overridden in the metadata object).\n   */\n  DATA_URL: 'data_url'\n} as const;\n\nexport class StringData {\n  contentType: string | null;\n\n  constructor(public data: Uint8Array, contentType?: string | null) {\n    this.contentType = contentType || null;\n  }\n}\n\n/**\n * @internal\n */\nexport function dataFromString(\n  format: StringFormat,\n  stringData: string\n): StringData {\n  switch (format) {\n    case StringFormat.RAW:\n      return new StringData(utf8Bytes_(stringData));\n    case StringFormat.BASE64:\n    case StringFormat.BASE64URL:\n      return new StringData(base64Bytes_(format, stringData));\n    case StringFormat.DATA_URL:\n      return new StringData(\n        dataURLBytes_(stringData),\n        dataURLContentType_(stringData)\n      );\n    default:\n    // do nothing\n  }\n\n  // assert(false);\n  throw unknown();\n}\n\nexport function utf8Bytes_(value: string): Uint8Array {\n  const b: number[] = [];\n  for (let i = 0; i < value.length; i++) {\n    let c = value.charCodeAt(i);\n    if (c <= 127) {\n      b.push(c);\n    } else {\n      if (c <= 2047) {\n        b.push(192 | (c >> 6), 128 | (c & 63));\n      } else {\n        if ((c & 64512) === 55296) {\n          // The start of a surrogate pair.\n          const valid =\n            i < value.length - 1 && (value.charCodeAt(i + 1) & 64512) === 56320;\n          if (!valid) {\n            // The second surrogate wasn't there.\n            b.push(239, 191, 189);\n          } else {\n            const hi = c;\n            const lo = value.charCodeAt(++i);\n            c = 65536 | ((hi & 1023) << 10) | (lo & 1023);\n            b.push(\n              240 | (c >> 18),\n              128 | ((c >> 12) & 63),\n              128 | ((c >> 6) & 63),\n              128 | (c & 63)\n            );\n          }\n        } else {\n          if ((c & 64512) === 56320) {\n            // Invalid low surrogate.\n            b.push(239, 191, 189);\n          } else {\n            b.push(224 | (c >> 12), 128 | ((c >> 6) & 63), 128 | (c & 63));\n          }\n        }\n      }\n    }\n  }\n  return new Uint8Array(b);\n}\n\nexport function percentEncodedBytes_(value: string): Uint8Array {\n  let decoded;\n  try {\n    decoded = decodeURIComponent(value);\n  } catch (e) {\n    throw invalidFormat(StringFormat.DATA_URL, 'Malformed data URL.');\n  }\n  return utf8Bytes_(decoded);\n}\n\nexport function base64Bytes_(format: StringFormat, value: string): Uint8Array {\n  switch (format) {\n    case StringFormat.BASE64: {\n      const hasMinus = value.indexOf('-') !== -1;\n      const hasUnder = value.indexOf('_') !== -1;\n      if (hasMinus || hasUnder) {\n        const invalidChar = hasMinus ? '-' : '_';\n        throw invalidFormat(\n          format,\n          \"Invalid character '\" +\n            invalidChar +\n            \"' found: is it base64url encoded?\"\n        );\n      }\n      break;\n    }\n    case StringFormat.BASE64URL: {\n      const hasPlus = value.indexOf('+') !== -1;\n      const hasSlash = value.indexOf('/') !== -1;\n      if (hasPlus || hasSlash) {\n        const invalidChar = hasPlus ? '+' : '/';\n        throw invalidFormat(\n          format,\n          \"Invalid character '\" + invalidChar + \"' found: is it base64 encoded?\"\n        );\n      }\n      value = value.replace(/-/g, '+').replace(/_/g, '/');\n      break;\n    }\n    default:\n    // do nothing\n  }\n  let bytes;\n  try {\n    bytes = decodeBase64(value);\n  } catch (e) {\n    if ((e as Error).message.includes('polyfill')) {\n      throw e;\n    }\n    throw invalidFormat(format, 'Invalid character found');\n  }\n  const array = new Uint8Array(bytes.length);\n  for (let i = 0; i < bytes.length; i++) {\n    array[i] = bytes.charCodeAt(i);\n  }\n  return array;\n}\n\nclass DataURLParts {\n  base64: boolean = false;\n  contentType: string | null = null;\n  rest: string;\n\n  constructor(dataURL: string) {\n    const matches = dataURL.match(/^data:([^,]+)?,/);\n    if (matches === null) {\n      throw invalidFormat(\n        StringFormat.DATA_URL,\n        \"Must be formatted 'data:[<mediatype>][;base64],<data>\"\n      );\n    }\n    const middle = matches[1] || null;\n    if (middle != null) {\n      this.base64 = endsWith(middle, ';base64');\n      this.contentType = this.base64\n        ? middle.substring(0, middle.length - ';base64'.length)\n        : middle;\n    }\n    this.rest = dataURL.substring(dataURL.indexOf(',') + 1);\n  }\n}\n\nexport function dataURLBytes_(dataUrl: string): Uint8Array {\n  const parts = new DataURLParts(dataUrl);\n  if (parts.base64) {\n    return base64Bytes_(StringFormat.BASE64, parts.rest);\n  } else {\n    return percentEncodedBytes_(parts.rest);\n  }\n}\n\nexport function dataURLContentType_(dataUrl: string): string | null {\n  const parts = new DataURLParts(dataUrl);\n  return parts.contentType;\n}\n\nfunction endsWith(s: string, end: string): boolean {\n  const longEnough = s.length >= end.length;\n  if (!longEnough) {\n    return false;\n  }\n\n  return s.substring(s.length - end.length) === end;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @file Provides a Blob-like wrapper for various binary types (including the\n * native Blob type). This makes it possible to upload types like ArrayBuffers,\n * making uploads possible in environments without the native Blob type.\n */\nimport { sliceBlob, getBlob } from './fs';\nimport { StringFormat, dataFromString } from './string';\nimport { isNativeBlob, isNativeBlobDefined, isString } from './type';\n\n/**\n * @param opt_elideCopy - If true, doesn't copy mutable input data\n *     (e.g. Uint8Arrays). Pass true only if you know the objects will not be\n *     modified after this blob's construction.\n *\n * @internal\n */\nexport class FbsBlob {\n  private data_!: Blob | Uint8Array;\n  private size_: number;\n  private type_: string;\n\n  constructor(data: Blob | Uint8Array | ArrayBuffer, elideCopy?: boolean) {\n    let size: number = 0;\n    let blobType: string = '';\n    if (isNativeBlob(data)) {\n      this.data_ = data as Blob;\n      size = (data as Blob).size;\n      blobType = (data as Blob).type;\n    } else if (data instanceof ArrayBuffer) {\n      if (elideCopy) {\n        this.data_ = new Uint8Array(data);\n      } else {\n        this.data_ = new Uint8Array(data.byteLength);\n        this.data_.set(new Uint8Array(data));\n      }\n      size = this.data_.length;\n    } else if (data instanceof Uint8Array) {\n      if (elideCopy) {\n        this.data_ = data as Uint8Array;\n      } else {\n        this.data_ = new Uint8Array(data.length);\n        this.data_.set(data as Uint8Array);\n      }\n      size = data.length;\n    }\n    this.size_ = size;\n    this.type_ = blobType;\n  }\n\n  size(): number {\n    return this.size_;\n  }\n\n  type(): string {\n    return this.type_;\n  }\n\n  slice(startByte: number, endByte: number): FbsBlob | null {\n    if (isNativeBlob(this.data_)) {\n      const realBlob = this.data_ as Blob;\n      const sliced = sliceBlob(realBlob, startByte, endByte);\n      if (sliced === null) {\n        return null;\n      }\n      return new FbsBlob(sliced);\n    } else {\n      const slice = new Uint8Array(\n        (this.data_ as Uint8Array).buffer,\n        startByte,\n        endByte - startByte\n      );\n      return new FbsBlob(slice, true);\n    }\n  }\n\n  static getBlob(...args: Array<string | FbsBlob>): FbsBlob | null {\n    if (isNativeBlobDefined()) {\n      const blobby: Array<Blob | Uint8Array | string> = args.map(\n        (val: string | FbsBlob): Blob | Uint8Array | string => {\n          if (val instanceof FbsBlob) {\n            return val.data_;\n          } else {\n            return val;\n          }\n        }\n      );\n      return new FbsBlob(getBlob.apply(null, blobby));\n    } else {\n      const uint8Arrays: Uint8Array[] = args.map(\n        (val: string | FbsBlob): Uint8Array => {\n          if (isString(val)) {\n            return dataFromString(StringFormat.RAW, val as string).data;\n          } else {\n            // Blobs don't exist, so this has to be a Uint8Array.\n            return (val as FbsBlob).data_ as Uint8Array;\n          }\n        }\n      );\n      let finalLength = 0;\n      uint8Arrays.forEach((array: Uint8Array): void => {\n        finalLength += array.byteLength;\n      });\n      const merged = new Uint8Array(finalLength);\n      let index = 0;\n      uint8Arrays.forEach((array: Uint8Array) => {\n        for (let i = 0; i < array.length; i++) {\n          merged[index++] = array[i];\n        }\n      });\n      return new FbsBlob(merged, true);\n    }\n  }\n\n  uploadData(): Blob | Uint8Array {\n    return this.data_;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { isNonArrayObject } from './type';\n\n/**\n * Returns the Object resulting from parsing the given JSON, or null if the\n * given string does not represent a JSON object.\n */\nexport function jsonObjectOrNull(\n  s: string\n): { [name: string]: unknown } | null {\n  let obj;\n  try {\n    obj = JSON.parse(s);\n  } catch (e) {\n    return null;\n  }\n  if (isNonArrayObject(obj)) {\n    return obj;\n  } else {\n    return null;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Contains helper methods for manipulating paths.\n */\n\n/**\n * @return Null if the path is already at the root.\n */\nexport function parent(path: string): string | null {\n  if (path.length === 0) {\n    return null;\n  }\n  const index = path.lastIndexOf('/');\n  if (index === -1) {\n    return '';\n  }\n  const newPath = path.slice(0, index);\n  return newPath;\n}\n\nexport function child(path: string, childPath: string): string {\n  const canonicalChildPath = childPath\n    .split('/')\n    .filter(component => component.length > 0)\n    .join('/');\n  if (path.length === 0) {\n    return canonicalChildPath;\n  } else {\n    return path + '/' + canonicalChildPath;\n  }\n}\n\n/**\n * Returns the last component of a path.\n * '/foo/bar' -> 'bar'\n * '/foo/bar/baz/' -> 'baz/'\n * '/a' -> 'a'\n */\nexport function lastComponent(path: string): string {\n  const index = path.lastIndexOf('/', path.length - 2);\n  if (index === -1) {\n    return path;\n  } else {\n    return path.slice(index + 1);\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Documentation for the metadata format\n */\nimport { Metadata } from '../metadata';\n\nimport { jsonObjectOrNull } from './json';\nimport { Location } from './location';\nimport { lastComponent } from './path';\nimport { isString } from './type';\nimport { makeUrl, makeQueryString } from './url';\nimport { Reference } from '../reference';\nimport { FirebaseStorageImpl } from '../service';\n\nexport function noXform_<T>(metadata: Metadata, value: T): T {\n  return value;\n}\n\nclass Mapping<T> {\n  local: string;\n  writable: boolean;\n  xform: (p1: Metadata, p2?: T) => T | undefined;\n\n  constructor(\n    public server: string,\n    local?: string | null,\n    writable?: boolean,\n    xform?: ((p1: Metadata, p2?: T) => T | undefined) | null\n  ) {\n    this.local = local || server;\n    this.writable = !!writable;\n    this.xform = xform || noXform_;\n  }\n}\ntype Mappings = Array<Mapping<string> | Mapping<number>>;\n\nexport { Mappings };\n\nlet mappings_: Mappings | null = null;\n\nexport function xformPath(fullPath: string | undefined): string | undefined {\n  if (!isString(fullPath) || fullPath.length < 2) {\n    return fullPath;\n  } else {\n    return lastComponent(fullPath);\n  }\n}\n\nexport function getMappings(): Mappings {\n  if (mappings_) {\n    return mappings_;\n  }\n  const mappings: Mappings = [];\n  mappings.push(new Mapping<string>('bucket'));\n  mappings.push(new Mapping<string>('generation'));\n  mappings.push(new Mapping<string>('metageneration'));\n  mappings.push(new Mapping<string>('name', 'fullPath', true));\n\n  function mappingsXformPath(\n    _metadata: Metadata,\n    fullPath: string | undefined\n  ): string | undefined {\n    return xformPath(fullPath);\n  }\n  const nameMapping = new Mapping<string>('name');\n  nameMapping.xform = mappingsXformPath;\n  mappings.push(nameMapping);\n\n  /**\n   * Coerces the second param to a number, if it is defined.\n   */\n  function xformSize(\n    _metadata: Metadata,\n    size?: number | string\n  ): number | undefined {\n    if (size !== undefined) {\n      return Number(size);\n    } else {\n      return size;\n    }\n  }\n  const sizeMapping = new Mapping<number>('size');\n  sizeMapping.xform = xformSize;\n  mappings.push(sizeMapping);\n  mappings.push(new Mapping<number>('timeCreated'));\n  mappings.push(new Mapping<string>('updated'));\n  mappings.push(new Mapping<string>('md5Hash', null, true));\n  mappings.push(new Mapping<string>('cacheControl', null, true));\n  mappings.push(new Mapping<string>('contentDisposition', null, true));\n  mappings.push(new Mapping<string>('contentEncoding', null, true));\n  mappings.push(new Mapping<string>('contentLanguage', null, true));\n  mappings.push(new Mapping<string>('contentType', null, true));\n  mappings.push(new Mapping<string>('metadata', 'customMetadata', true));\n  mappings_ = mappings;\n  return mappings_;\n}\n\nexport function addRef(metadata: Metadata, service: FirebaseStorageImpl): void {\n  function generateRef(): Reference {\n    const bucket: string = metadata['bucket'] as string;\n    const path: string = metadata['fullPath'] as string;\n    const loc = new Location(bucket, path);\n    return service._makeStorageReference(loc);\n  }\n  Object.defineProperty(metadata, 'ref', { get: generateRef });\n}\n\nexport function fromResource(\n  service: FirebaseStorageImpl,\n  resource: { [name: string]: unknown },\n  mappings: Mappings\n): Metadata {\n  const metadata: Metadata = {} as Metadata;\n  metadata['type'] = 'file';\n  const len = mappings.length;\n  for (let i = 0; i < len; i++) {\n    const mapping = mappings[i];\n    metadata[mapping.local] = (mapping as Mapping<unknown>).xform(\n      metadata,\n      resource[mapping.server]\n    );\n  }\n  addRef(metadata, service);\n  return metadata;\n}\n\nexport function fromResourceString(\n  service: FirebaseStorageImpl,\n  resourceString: string,\n  mappings: Mappings\n): Metadata | null {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  const resource = obj as Metadata;\n  return fromResource(service, resource, mappings);\n}\n\nexport function downloadUrlFromResourceString(\n  metadata: Metadata,\n  resourceString: string,\n  host: string,\n  protocol: string\n): string | null {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  if (!isString(obj['downloadTokens'])) {\n    // This can happen if objects are uploaded through GCS and retrieved\n    // through list, so we don't want to throw an Error.\n    return null;\n  }\n  const tokens: string = obj['downloadTokens'] as string;\n  if (tokens.length === 0) {\n    return null;\n  }\n  const encode = encodeURIComponent;\n  const tokensList = tokens.split(',');\n  const urls = tokensList.map((token: string): string => {\n    const bucket: string = metadata['bucket'] as string;\n    const path: string = metadata['fullPath'] as string;\n    const urlPart = '/b/' + encode(bucket) + '/o/' + encode(path);\n    const base = makeUrl(urlPart, host, protocol);\n    const queryString = makeQueryString({\n      alt: 'media',\n      token\n    });\n    return base + queryString;\n  });\n  return urls[0];\n}\n\nexport function toResourceString(\n  metadata: Partial<Metadata>,\n  mappings: Mappings\n): string {\n  const resource: {\n    [prop: string]: unknown;\n  } = {};\n  const len = mappings.length;\n  for (let i = 0; i < len; i++) {\n    const mapping = mappings[i];\n    if (mapping.writable) {\n      resource[mapping.server] = metadata[mapping.local];\n    }\n  }\n  return JSON.stringify(resource);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Documentation for the listOptions and listResult format\n */\nimport { Location } from './location';\nimport { jsonObjectOrNull } from './json';\nimport { ListResult } from '../list';\nimport { FirebaseStorageImpl } from '../service';\n\n/**\n * Represents the simplified object metadata returned by List API.\n * Other fields are filtered because list in Firebase Rules does not grant\n * the permission to read the metadata.\n */\ninterface ListMetadataResponse {\n  name: string;\n  bucket: string;\n}\n\n/**\n * Represents the JSON response of List API.\n */\ninterface ListResultResponse {\n  prefixes: string[];\n  items: ListMetadataResponse[];\n  nextPageToken?: string;\n}\n\nconst PREFIXES_KEY = 'prefixes';\nconst ITEMS_KEY = 'items';\n\nfunction fromBackendResponse(\n  service: FirebaseStorageImpl,\n  bucket: string,\n  resource: ListResultResponse\n): ListResult {\n  const listResult: ListResult = {\n    prefixes: [],\n    items: [],\n    nextPageToken: resource['nextPageToken']\n  };\n  if (resource[PREFIXES_KEY]) {\n    for (const path of resource[PREFIXES_KEY]) {\n      const pathWithoutTrailingSlash = path.replace(/\\/$/, '');\n      const reference = service._makeStorageReference(\n        new Location(bucket, pathWithoutTrailingSlash)\n      );\n      listResult.prefixes.push(reference);\n    }\n  }\n\n  if (resource[ITEMS_KEY]) {\n    for (const item of resource[ITEMS_KEY]) {\n      const reference = service._makeStorageReference(\n        new Location(bucket, item['name'])\n      );\n      listResult.items.push(reference);\n    }\n  }\n  return listResult;\n}\n\nexport function fromResponseString(\n  service: FirebaseStorageImpl,\n  bucket: string,\n  resourceString: string\n): ListResult | null {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  const resource = obj as unknown as ListResultResponse;\n  return fromBackendResponse(service, bucket, resource);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { StorageError } from './error';\nimport { Headers, Connection, ConnectionType } from './connection';\n\n/**\n * Type for url params stored in RequestInfo.\n */\nexport interface UrlParams {\n  [name: string]: string | number;\n}\n\n/**\n * A function that converts a server response to the API type expected by the\n * SDK.\n *\n * @param I - the type of the backend's network response\n * @param O - the output response type used by the rest of the SDK.\n */\nexport type RequestHandler<I extends ConnectionType, O> = (\n  connection: Connection<I>,\n  response: I\n) => O;\n\n/** A function to handle an error. */\nexport type ErrorHandler = (\n  connection: Connection<ConnectionType>,\n  response: StorageError\n) => StorageError;\n\n/**\n * Contains a fully specified request.\n *\n * @param I - the type of the backend's network response.\n * @param O - the output response type used by the rest of the SDK.\n */\nexport class RequestInfo<I extends ConnectionType, O> {\n  urlParams: UrlParams = {};\n  headers: Headers = {};\n  body: Blob | string | Uint8Array | null = null;\n  errorHandler: ErrorHandler | null = null;\n\n  /**\n   * Called with the current number of bytes uploaded and total size (-1 if not\n   * computable) of the request body (i.e. used to report upload progress).\n   */\n  progressCallback: ((p1: number, p2: number) => void) | null = null;\n  successCodes: number[] = [200];\n  additionalRetryCodes: number[] = [];\n\n  constructor(\n    public url: string,\n    public method: string,\n    /**\n     * Returns the value with which to resolve the request's promise. Only called\n     * if the request is successful. Throw from this function to reject the\n     * returned Request's promise with the thrown error.\n     * Note: The XhrIo passed to this function may be reused after this callback\n     * returns. Do not keep a reference to it in any way.\n     */\n    public handler: RequestHandler<I, O>,\n    public timeout: number\n  ) {}\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Defines methods for interacting with the network.\n */\n\nimport { Metadata } from '../metadata';\nimport { ListResult } from '../list';\nimport { FbsBlob } from './blob';\nimport {\n  StorageError,\n  cannotSliceBlob,\n  unauthenticated,\n  quotaExceeded,\n  unauthorized,\n  objectNotFound,\n  serverFileWrongSize,\n  unknown,\n  unauthorizedApp\n} from './error';\nimport { Location } from './location';\nimport {\n  Mappings,\n  fromResourceString,\n  downloadUrlFromResourceString,\n  toResourceString\n} from './metadata';\nimport { fromResponseString } from './list';\nimport { RequestInfo, UrlParams } from './requestinfo';\nimport { isString } from './type';\nimport { makeUrl } from './url';\nimport { Connection, ConnectionType } from './connection';\nimport { FirebaseStorageImpl } from '../service';\n\n/**\n * Throws the UNKNOWN StorageError if cndn is false.\n */\nexport function handlerCheck(cndn: boolean): void {\n  if (!cndn) {\n    throw unknown();\n  }\n}\n\nexport function metadataHandler(\n  service: FirebaseStorageImpl,\n  mappings: Mappings\n): (p1: Connection<string>, p2: string) => Metadata {\n  function handler(xhr: Connection<string>, text: string): Metadata {\n    const metadata = fromResourceString(service, text, mappings);\n    handlerCheck(metadata !== null);\n    return metadata as Metadata;\n  }\n  return handler;\n}\n\nexport function listHandler(\n  service: FirebaseStorageImpl,\n  bucket: string\n): (p1: Connection<string>, p2: string) => ListResult {\n  function handler(xhr: Connection<string>, text: string): ListResult {\n    const listResult = fromResponseString(service, bucket, text);\n    handlerCheck(listResult !== null);\n    return listResult as ListResult;\n  }\n  return handler;\n}\n\nexport function downloadUrlHandler(\n  service: FirebaseStorageImpl,\n  mappings: Mappings\n): (p1: Connection<string>, p2: string) => string | null {\n  function handler(xhr: Connection<string>, text: string): string | null {\n    const metadata = fromResourceString(service, text, mappings);\n    handlerCheck(metadata !== null);\n    return downloadUrlFromResourceString(\n      metadata as Metadata,\n      text,\n      service.host,\n      service._protocol\n    );\n  }\n  return handler;\n}\n\nexport function sharedErrorHandler(\n  location: Location\n): (p1: Connection<ConnectionType>, p2: StorageError) => StorageError {\n  function errorHandler(\n    xhr: Connection<ConnectionType>,\n    err: StorageError\n  ): StorageError {\n    let newErr: StorageError;\n    if (xhr.getStatus() === 401) {\n      if (\n        // This exact message string is the only consistent part of the\n        // server's error response that identifies it as an App Check error.\n        xhr.getErrorText().includes('Firebase App Check token is invalid')\n      ) {\n        newErr = unauthorizedApp();\n      } else {\n        newErr = unauthenticated();\n      }\n    } else {\n      if (xhr.getStatus() === 402) {\n        newErr = quotaExceeded(location.bucket);\n      } else {\n        if (xhr.getStatus() === 403) {\n          newErr = unauthorized(location.path);\n        } else {\n          newErr = err;\n        }\n      }\n    }\n    newErr.status = xhr.getStatus();\n    newErr.serverResponse = err.serverResponse;\n    return newErr;\n  }\n  return errorHandler;\n}\n\nexport function objectErrorHandler(\n  location: Location\n): (p1: Connection<ConnectionType>, p2: StorageError) => StorageError {\n  const shared = sharedErrorHandler(location);\n\n  function errorHandler(\n    xhr: Connection<ConnectionType>,\n    err: StorageError\n  ): StorageError {\n    let newErr = shared(xhr, err);\n    if (xhr.getStatus() === 404) {\n      newErr = objectNotFound(location.path);\n    }\n    newErr.serverResponse = err.serverResponse;\n    return newErr;\n  }\n  return errorHandler;\n}\n\nexport function getMetadata(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings\n): RequestInfo<string, Metadata> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    metadataHandler(service, mappings),\n    timeout\n  );\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function list(\n  service: FirebaseStorageImpl,\n  location: Location,\n  delimiter?: string,\n  pageToken?: string | null,\n  maxResults?: number | null\n): RequestInfo<string, ListResult> {\n  const urlParams: UrlParams = {};\n  if (location.isRoot) {\n    urlParams['prefix'] = '';\n  } else {\n    urlParams['prefix'] = location.path + '/';\n  }\n  if (delimiter && delimiter.length > 0) {\n    urlParams['delimiter'] = delimiter;\n  }\n  if (pageToken) {\n    urlParams['pageToken'] = pageToken;\n  }\n  if (maxResults) {\n    urlParams['maxResults'] = maxResults;\n  }\n  const urlPart = location.bucketOnlyServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    listHandler(service, location.bucket),\n    timeout\n  );\n  requestInfo.urlParams = urlParams;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\nexport function getBytes<I extends ConnectionType>(\n  service: FirebaseStorageImpl,\n  location: Location,\n  maxDownloadSizeBytes?: number\n): RequestInfo<I, I> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol) + '?alt=media';\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    (_: Connection<I>, data: I) => data,\n    timeout\n  );\n  requestInfo.errorHandler = objectErrorHandler(location);\n  if (maxDownloadSizeBytes !== undefined) {\n    requestInfo.headers['Range'] = `bytes=0-${maxDownloadSizeBytes}`;\n    requestInfo.successCodes = [200 /* OK */, 206 /* Partial Content */];\n  }\n  return requestInfo;\n}\n\nexport function getDownloadUrl(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings\n): RequestInfo<string, string | null> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    downloadUrlHandler(service, mappings),\n    timeout\n  );\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function updateMetadata(\n  service: FirebaseStorageImpl,\n  location: Location,\n  metadata: Partial<Metadata>,\n  mappings: Mappings\n): RequestInfo<string, Metadata> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'PATCH';\n  const body = toResourceString(metadata, mappings);\n  const headers = { 'Content-Type': 'application/json; charset=utf-8' };\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    metadataHandler(service, mappings),\n    timeout\n  );\n  requestInfo.headers = headers;\n  requestInfo.body = body;\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function deleteObject(\n  service: FirebaseStorageImpl,\n  location: Location\n): RequestInfo<string, void> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'DELETE';\n  const timeout = service.maxOperationRetryTime;\n\n  function handler(_xhr: Connection<string>, _text: string): void {}\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.successCodes = [200, 204];\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function determineContentType_(\n  metadata: Metadata | null,\n  blob: FbsBlob | null\n): string {\n  return (\n    (metadata && metadata['contentType']) ||\n    (blob && blob.type()) ||\n    'application/octet-stream'\n  );\n}\n\nexport function metadataForUpload_(\n  location: Location,\n  blob: FbsBlob,\n  metadata?: Metadata | null\n): Metadata {\n  const metadataClone = Object.assign({}, metadata);\n  metadataClone['fullPath'] = location.path;\n  metadataClone['size'] = blob.size();\n  if (!metadataClone['contentType']) {\n    metadataClone['contentType'] = determineContentType_(null, blob);\n  }\n  return metadataClone;\n}\n\n/**\n * Prepare RequestInfo for uploads as Content-Type: multipart.\n */\nexport function multipartUpload(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings,\n  blob: FbsBlob,\n  metadata?: Metadata | null\n): RequestInfo<string, Metadata> {\n  const urlPart = location.bucketOnlyServerUrl();\n  const headers: { [prop: string]: string } = {\n    'X-Goog-Upload-Protocol': 'multipart'\n  };\n\n  function genBoundary(): string {\n    let str = '';\n    for (let i = 0; i < 2; i++) {\n      str = str + Math.random().toString().slice(2);\n    }\n    return str;\n  }\n  const boundary = genBoundary();\n  headers['Content-Type'] = 'multipart/related; boundary=' + boundary;\n  const metadata_ = metadataForUpload_(location, blob, metadata);\n  const metadataString = toResourceString(metadata_, mappings);\n  const preBlobPart =\n    '--' +\n    boundary +\n    '\\r\\n' +\n    'Content-Type: application/json; charset=utf-8\\r\\n\\r\\n' +\n    metadataString +\n    '\\r\\n--' +\n    boundary +\n    '\\r\\n' +\n    'Content-Type: ' +\n    metadata_['contentType'] +\n    '\\r\\n\\r\\n';\n  const postBlobPart = '\\r\\n--' + boundary + '--';\n  const body = FbsBlob.getBlob(preBlobPart, blob, postBlobPart);\n  if (body === null) {\n    throw cannotSliceBlob();\n  }\n  const urlParams: UrlParams = { name: metadata_['fullPath']! };\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    metadataHandler(service, mappings),\n    timeout\n  );\n  requestInfo.urlParams = urlParams;\n  requestInfo.headers = headers;\n  requestInfo.body = body.uploadData();\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\n/**\n * @param current The number of bytes that have been uploaded so far.\n * @param total The total number of bytes in the upload.\n * @param opt_finalized True if the server has finished the upload.\n * @param opt_metadata The upload metadata, should\n *     only be passed if opt_finalized is true.\n */\nexport class ResumableUploadStatus {\n  finalized: boolean;\n  metadata: Metadata | null;\n\n  constructor(\n    public current: number,\n    public total: number,\n    finalized?: boolean,\n    metadata?: Metadata | null\n  ) {\n    this.finalized = !!finalized;\n    this.metadata = metadata || null;\n  }\n}\n\nexport function checkResumeHeader_(\n  xhr: Connection<string>,\n  allowed?: string[]\n): string {\n  let status: string | null = null;\n  try {\n    status = xhr.getResponseHeader('X-Goog-Upload-Status');\n  } catch (e) {\n    handlerCheck(false);\n  }\n  const allowedStatus = allowed || ['active'];\n  handlerCheck(!!status && allowedStatus.indexOf(status) !== -1);\n  return status as string;\n}\n\nexport function createResumableUpload(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings,\n  blob: FbsBlob,\n  metadata?: Metadata | null\n): RequestInfo<string, string> {\n  const urlPart = location.bucketOnlyServerUrl();\n  const metadataForUpload = metadataForUpload_(location, blob, metadata);\n  const urlParams: UrlParams = { name: metadataForUpload['fullPath']! };\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'POST';\n  const headers = {\n    'X-Goog-Upload-Protocol': 'resumable',\n    'X-Goog-Upload-Command': 'start',\n    'X-Goog-Upload-Header-Content-Length': `${blob.size()}`,\n    'X-Goog-Upload-Header-Content-Type': metadataForUpload['contentType']!,\n    'Content-Type': 'application/json; charset=utf-8'\n  };\n  const body = toResourceString(metadataForUpload, mappings);\n  const timeout = service.maxUploadRetryTime;\n\n  function handler(xhr: Connection<string>): string {\n    checkResumeHeader_(xhr);\n    let url;\n    try {\n      url = xhr.getResponseHeader('X-Goog-Upload-URL');\n    } catch (e) {\n      handlerCheck(false);\n    }\n    handlerCheck(isString(url));\n    return url as string;\n  }\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.urlParams = urlParams;\n  requestInfo.headers = headers;\n  requestInfo.body = body;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\n/**\n * @param url From a call to fbs.requests.createResumableUpload.\n */\nexport function getResumableUploadStatus(\n  service: FirebaseStorageImpl,\n  location: Location,\n  url: string,\n  blob: FbsBlob\n): RequestInfo<string, ResumableUploadStatus> {\n  const headers = { 'X-Goog-Upload-Command': 'query' };\n\n  function handler(xhr: Connection<string>): ResumableUploadStatus {\n    const status = checkResumeHeader_(xhr, ['active', 'final']);\n    let sizeString: string | null = null;\n    try {\n      sizeString = xhr.getResponseHeader('X-Goog-Upload-Size-Received');\n    } catch (e) {\n      handlerCheck(false);\n    }\n\n    if (!sizeString) {\n      // null or empty string\n      handlerCheck(false);\n    }\n\n    const size = Number(sizeString);\n    handlerCheck(!isNaN(size));\n    return new ResumableUploadStatus(size, blob.size(), status === 'final');\n  }\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.headers = headers;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\n/**\n * Any uploads via the resumable upload API must transfer a number of bytes\n * that is a multiple of this number.\n */\nexport const RESUMABLE_UPLOAD_CHUNK_SIZE: number = 256 * 1024;\n\n/**\n * @param url From a call to fbs.requests.createResumableUpload.\n * @param chunkSize Number of bytes to upload.\n * @param status The previous status.\n *     If not passed or null, we start from the beginning.\n * @throws fbs.Error If the upload is already complete, the passed in status\n *     has a final size inconsistent with the blob, or the blob cannot be sliced\n *     for upload.\n */\nexport function continueResumableUpload(\n  location: Location,\n  service: FirebaseStorageImpl,\n  url: string,\n  blob: FbsBlob,\n  chunkSize: number,\n  mappings: Mappings,\n  status?: ResumableUploadStatus | null,\n  progressCallback?: ((p1: number, p2: number) => void) | null\n): RequestInfo<string, ResumableUploadStatus> {\n  // TODO(andysoto): standardize on internal asserts\n  // assert(!(opt_status && opt_status.finalized));\n  const status_ = new ResumableUploadStatus(0, 0);\n  if (status) {\n    status_.current = status.current;\n    status_.total = status.total;\n  } else {\n    status_.current = 0;\n    status_.total = blob.size();\n  }\n  if (blob.size() !== status_.total) {\n    throw serverFileWrongSize();\n  }\n  const bytesLeft = status_.total - status_.current;\n  let bytesToUpload = bytesLeft;\n  if (chunkSize > 0) {\n    bytesToUpload = Math.min(bytesToUpload, chunkSize);\n  }\n  const startByte = status_.current;\n  const endByte = startByte + bytesToUpload;\n  let uploadCommand = '';\n  if (bytesToUpload === 0) {\n    uploadCommand = 'finalize';\n  } else if (bytesLeft === bytesToUpload) {\n    uploadCommand = 'upload, finalize';\n  } else {\n    uploadCommand = 'upload';\n  }\n  const headers = {\n    'X-Goog-Upload-Command': uploadCommand,\n    'X-Goog-Upload-Offset': `${status_.current}`\n  };\n  const body = blob.slice(startByte, endByte);\n  if (body === null) {\n    throw cannotSliceBlob();\n  }\n\n  function handler(\n    xhr: Connection<string>,\n    text: string\n  ): ResumableUploadStatus {\n    // TODO(andysoto): Verify the MD5 of each uploaded range:\n    // the 'x-range-md5' header comes back with status code 308 responses.\n    // We'll only be able to bail out though, because you can't re-upload a\n    // range that you previously uploaded.\n    const uploadStatus = checkResumeHeader_(xhr, ['active', 'final']);\n    const newCurrent = status_.current + bytesToUpload;\n    const size = blob.size();\n    let metadata;\n    if (uploadStatus === 'final') {\n      metadata = metadataHandler(service, mappings)(xhr, text);\n    } else {\n      metadata = null;\n    }\n    return new ResumableUploadStatus(\n      newCurrent,\n      size,\n      uploadStatus === 'final',\n      metadata\n    );\n  }\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.headers = headers;\n  requestInfo.body = body.uploadData();\n  requestInfo.progressCallback = progressCallback || null;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Enumerations used for upload tasks.\n */\n\n/**\n * An event that is triggered on a task.\n * @internal\n */\nexport type TaskEvent = string;\n\n/**\n * An event that is triggered on a task.\n * @internal\n */\nexport const TaskEvent = {\n  /**\n   * For this event,\n   * <ul>\n   *   <li>The `next` function is triggered on progress updates and when the\n   *       task is paused/resumed with an `UploadTaskSnapshot` as the first\n   *       argument.</li>\n   *   <li>The `error` function is triggered if the upload is canceled or fails\n   *       for another reason.</li>\n   *   <li>The `complete` function is triggered if the upload completes\n   *       successfully.</li>\n   * </ul>\n   */\n  STATE_CHANGED: 'state_changed'\n};\n\n/**\n * Internal enum for task state.\n */\nexport const enum InternalTaskState {\n  RUNNING = 'running',\n  PAUSING = 'pausing',\n  PAUSED = 'paused',\n  SUCCESS = 'success',\n  CANCELING = 'canceling',\n  CANCELED = 'canceled',\n  ERROR = 'error'\n}\n\n/**\n * Represents the current state of a running upload.\n * @internal\n */\nexport type TaskState = (typeof TaskState)[keyof typeof TaskState];\n\n// type keys = keyof TaskState\n/**\n * Represents the current state of a running upload.\n * @internal\n */\nexport const TaskState = {\n  /** The task is currently transferring data. */\n  RUNNING: 'running',\n\n  /** The task was paused by the user. */\n  PAUSED: 'paused',\n\n  /** The task completed successfully. */\n  SUCCESS: 'success',\n\n  /** The task was canceled. */\n  CANCELED: 'canceled',\n\n  /** The task failed with an error. */\n  ERROR: 'error'\n} as const;\n\nexport function taskStateFromInternalTaskState(\n  state: InternalTaskState\n): TaskState {\n  switch (state) {\n    case InternalTaskState.RUNNING:\n    case InternalTaskState.PAUSING:\n    case InternalTaskState.CANCELING:\n      return TaskState.RUNNING;\n    case InternalTaskState.PAUSED:\n      return TaskState.PAUSED;\n    case InternalTaskState.SUCCESS:\n      return TaskState.SUCCESS;\n    case InternalTaskState.CANCELED:\n      return TaskState.CANCELED;\n    case InternalTaskState.ERROR:\n      return TaskState.ERROR;\n    default:\n      // TODO(andysoto): assert(false);\n      return TaskState.ERROR;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { isFunction } from './type';\nimport { StorageError } from './error';\n\n/**\n * Function that is called once for each value in a stream of values.\n */\nexport type NextFn<T> = (value: T) => void;\n\n/**\n * A function that is called with a `StorageError`\n * if the event stream ends due to an error.\n */\nexport type ErrorFn = (error: StorageError) => void;\n\n/**\n * A function that is called if the event stream ends normally.\n */\nexport type CompleteFn = () => void;\n\n/**\n * Unsubscribes from a stream.\n */\nexport type Unsubscribe = () => void;\n\n/**\n * An observer identical to the `Observer` defined in packages/util except the\n * error passed into the ErrorFn is specifically a `StorageError`.\n */\nexport interface StorageObserver<T> {\n  /**\n   * Function that is called once for each value in the event stream.\n   */\n  next?: NextFn<T>;\n  /**\n   * A function that is called with a `StorageError`\n   * if the event stream ends due to an error.\n   */\n  error?: ErrorFn;\n  /**\n   * A function that is called if the event stream ends normally.\n   */\n  complete?: CompleteFn;\n}\n\n/**\n * Subscribes to an event stream.\n */\nexport type Subscribe<T> = (\n  next?: NextFn<T> | StorageObserver<T>,\n  error?: ErrorFn,\n  complete?: CompleteFn\n) => Unsubscribe;\n\nexport class Observer<T> implements StorageObserver<T> {\n  next?: NextFn<T>;\n  error?: ErrorFn;\n  complete?: CompleteFn;\n\n  constructor(\n    nextOrObserver?: NextFn<T> | StorageObserver<T>,\n    error?: ErrorFn,\n    complete?: CompleteFn\n  ) {\n    const asFunctions =\n      isFunction(nextOrObserver) || error != null || complete != null;\n    if (asFunctions) {\n      this.next = nextOrObserver as NextFn<T>;\n      this.error = error ?? undefined;\n      this.complete = complete ?? undefined;\n    } else {\n      const observer = nextOrObserver as {\n        next?: NextFn<T>;\n        error?: ErrorFn;\n        complete?: CompleteFn;\n      };\n      this.next = observer.next;\n      this.error = observer.error;\n      this.complete = observer.complete;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Returns a function that invokes f with its arguments asynchronously as a\n * microtask, i.e. as soon as possible after the current script returns back\n * into browser code.\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function async(f: Function): Function {\n  return (...argsToForward: unknown[]) => {\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    Promise.resolve().then(() => f(...argsToForward));\n  };\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Connection,\n  ConnectionType,\n  ErrorCode,\n  Headers\n} from '../../implementation/connection';\nimport { internalError } from '../../implementation/error';\n\n/** An override for the text-based Connection. Used in tests. */\nlet textFactoryOverride: (() => Connection<string>) | null = null;\n\n/**\n * Network layer for browsers. We use this instead of goog.net.XhrIo because\n * goog.net.XhrIo is hyuuuuge and doesn't work in React Native on Android.\n */\nabstract class XhrConnection<T extends ConnectionType>\n  implements Connection<T>\n{\n  protected xhr_: XMLHttpRequest;\n  private errorCode_: ErrorCode;\n  private sendPromise_: Promise<void>;\n  protected sent_: boolean = false;\n\n  constructor() {\n    this.xhr_ = new XMLHttpRequest();\n    this.initXhr();\n    this.errorCode_ = ErrorCode.NO_ERROR;\n    this.sendPromise_ = new Promise(resolve => {\n      this.xhr_.addEventListener('abort', () => {\n        this.errorCode_ = ErrorCode.ABORT;\n        resolve();\n      });\n      this.xhr_.addEventListener('error', () => {\n        this.errorCode_ = ErrorCode.NETWORK_ERROR;\n        resolve();\n      });\n      this.xhr_.addEventListener('load', () => {\n        resolve();\n      });\n    });\n  }\n\n  abstract initXhr(): void;\n\n  send(\n    url: string,\n    method: string,\n    body?: ArrayBufferView | Blob | string,\n    headers?: Headers\n  ): Promise<void> {\n    if (this.sent_) {\n      throw internalError('cannot .send() more than once');\n    }\n    this.sent_ = true;\n    this.xhr_.open(method, url, true);\n    if (headers !== undefined) {\n      for (const key in headers) {\n        if (headers.hasOwnProperty(key)) {\n          this.xhr_.setRequestHeader(key, headers[key].toString());\n        }\n      }\n    }\n    if (body !== undefined) {\n      this.xhr_.send(body);\n    } else {\n      this.xhr_.send();\n    }\n    return this.sendPromise_;\n  }\n\n  getErrorCode(): ErrorCode {\n    if (!this.sent_) {\n      throw internalError('cannot .getErrorCode() before sending');\n    }\n    return this.errorCode_;\n  }\n\n  getStatus(): number {\n    if (!this.sent_) {\n      throw internalError('cannot .getStatus() before sending');\n    }\n    try {\n      return this.xhr_.status;\n    } catch (e) {\n      return -1;\n    }\n  }\n\n  getResponse(): T {\n    if (!this.sent_) {\n      throw internalError('cannot .getResponse() before sending');\n    }\n    return this.xhr_.response;\n  }\n\n  getErrorText(): string {\n    if (!this.sent_) {\n      throw internalError('cannot .getErrorText() before sending');\n    }\n    return this.xhr_.statusText;\n  }\n\n  /** Aborts the request. */\n  abort(): void {\n    this.xhr_.abort();\n  }\n\n  getResponseHeader(header: string): string | null {\n    return this.xhr_.getResponseHeader(header);\n  }\n\n  addUploadProgressListener(listener: (p1: ProgressEvent) => void): void {\n    if (this.xhr_.upload != null) {\n      this.xhr_.upload.addEventListener('progress', listener);\n    }\n  }\n\n  removeUploadProgressListener(listener: (p1: ProgressEvent) => void): void {\n    if (this.xhr_.upload != null) {\n      this.xhr_.upload.removeEventListener('progress', listener);\n    }\n  }\n}\n\nexport class XhrTextConnection extends XhrConnection<string> {\n  initXhr(): void {\n    this.xhr_.responseType = 'text';\n  }\n}\n\nexport function newTextConnection(): Connection<string> {\n  return textFactoryOverride ? textFactoryOverride() : new XhrTextConnection();\n}\n\nexport class XhrBytesConnection extends XhrConnection<ArrayBuffer> {\n  private data_?: ArrayBuffer;\n\n  initXhr(): void {\n    this.xhr_.responseType = 'arraybuffer';\n  }\n}\n\nexport function newBytesConnection(): Connection<ArrayBuffer> {\n  return new XhrBytesConnection();\n}\n\nexport class XhrBlobConnection extends XhrConnection<Blob> {\n  initXhr(): void {\n    this.xhr_.responseType = 'blob';\n  }\n}\n\nexport function newBlobConnection(): Connection<Blob> {\n  return new XhrBlobConnection();\n}\n\nexport function newStreamConnection(): Connection<ReadableStream> {\n  throw new Error('Streams are only supported on Node');\n}\n\nexport function injectTestConnection(\n  factory: (() => Connection<string>) | null\n): void {\n  textFactoryOverride = factory;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Defines types for interacting with blob transfer tasks.\n */\n\nimport { FbsBlob } from './implementation/blob';\nimport {\n  canceled,\n  StorageErrorCode,\n  StorageError,\n  retryLimitExceeded\n} from './implementation/error';\nimport {\n  InternalTaskState,\n  TaskEvent,\n  TaskState,\n  taskStateFromInternalTaskState\n} from './implementation/taskenums';\nimport { Metadata } from './metadata';\nimport {\n  Observer,\n  Subscribe,\n  Unsubscribe,\n  StorageObserver as StorageObserverInternal,\n  NextFn\n} from './implementation/observer';\nimport { Request } from './implementation/request';\nimport { UploadTaskSnapshot, StorageObserver } from './public-types';\nimport { async as fbsAsync } from './implementation/async';\nimport { Mappings, getMappings } from './implementation/metadata';\nimport {\n  createResumableUpload,\n  getResumableUploadStatus,\n  RESUMABLE_UPLOAD_CHUNK_SIZE,\n  ResumableUploadStatus,\n  continueResumableUpload,\n  getMetadata,\n  multipartUpload\n} from './implementation/requests';\nimport { Reference } from './reference';\nimport { newTextConnection } from './platform/connection';\nimport { isRetryStatusCode } from './implementation/utils';\nimport { CompleteFn } from '@firebase/util';\nimport { DEFAULT_MIN_SLEEP_TIME_MILLIS } from './implementation/constants';\n\n/**\n * Represents a blob being uploaded. Can be used to pause/resume/cancel the\n * upload and manage callbacks for various events.\n * @internal\n */\nexport class UploadTask {\n  private _ref: Reference;\n  /**\n   * The data to be uploaded.\n   */\n  _blob: FbsBlob;\n  /**\n   * Metadata related to the upload.\n   */\n  _metadata: Metadata | null;\n  private _mappings: Mappings;\n  /**\n   * Number of bytes transferred so far.\n   */\n  _transferred: number = 0;\n  private _needToFetchStatus: boolean = false;\n  private _needToFetchMetadata: boolean = false;\n  private _observers: Array<StorageObserverInternal<UploadTaskSnapshot>> = [];\n  private _resumable: boolean;\n  /**\n   * Upload state.\n   */\n  _state: InternalTaskState;\n  private _error?: StorageError = undefined;\n  private _uploadUrl?: string = undefined;\n  private _request?: Request<unknown> = undefined;\n  private _chunkMultiplier: number = 1;\n  private _errorHandler: (p1: StorageError) => void;\n  private _metadataErrorHandler: (p1: StorageError) => void;\n  private _resolve?: (p1: UploadTaskSnapshot) => void = undefined;\n  private _reject?: (p1: StorageError) => void = undefined;\n  private pendingTimeout?: ReturnType<typeof setTimeout>;\n  private _promise: Promise<UploadTaskSnapshot>;\n\n  private sleepTime: number;\n\n  private maxSleepTime: number;\n\n  isExponentialBackoffExpired(): boolean {\n    return this.sleepTime > this.maxSleepTime;\n  }\n\n  /**\n   * @param ref - The firebaseStorage.Reference object this task came\n   *     from, untyped to avoid cyclic dependencies.\n   * @param blob - The blob to upload.\n   */\n  constructor(ref: Reference, blob: FbsBlob, metadata: Metadata | null = null) {\n    this._ref = ref;\n    this._blob = blob;\n    this._metadata = metadata;\n    this._mappings = getMappings();\n    this._resumable = this._shouldDoResumable(this._blob);\n    this._state = InternalTaskState.RUNNING;\n    this._errorHandler = error => {\n      this._request = undefined;\n      this._chunkMultiplier = 1;\n      if (error._codeEquals(StorageErrorCode.CANCELED)) {\n        this._needToFetchStatus = true;\n        this.completeTransitions_();\n      } else {\n        const backoffExpired = this.isExponentialBackoffExpired();\n        if (isRetryStatusCode(error.status, [])) {\n          if (backoffExpired) {\n            error = retryLimitExceeded();\n          } else {\n            this.sleepTime = Math.max(\n              this.sleepTime * 2,\n              DEFAULT_MIN_SLEEP_TIME_MILLIS\n            );\n            this._needToFetchStatus = true;\n            this.completeTransitions_();\n            return;\n          }\n        }\n        this._error = error;\n        this._transition(InternalTaskState.ERROR);\n      }\n    };\n    this._metadataErrorHandler = error => {\n      this._request = undefined;\n      if (error._codeEquals(StorageErrorCode.CANCELED)) {\n        this.completeTransitions_();\n      } else {\n        this._error = error;\n        this._transition(InternalTaskState.ERROR);\n      }\n    };\n    this.sleepTime = 0;\n    this.maxSleepTime = this._ref.storage.maxUploadRetryTime;\n    this._promise = new Promise((resolve, reject) => {\n      this._resolve = resolve;\n      this._reject = reject;\n      this._start();\n    });\n\n    // Prevent uncaught rejections on the internal promise from bubbling out\n    // to the top level with a dummy handler.\n    this._promise.then(null, () => {});\n  }\n\n  private _makeProgressCallback(): (p1: number, p2: number) => void {\n    const sizeBefore = this._transferred;\n    return loaded => this._updateProgress(sizeBefore + loaded);\n  }\n\n  private _shouldDoResumable(blob: FbsBlob): boolean {\n    return blob.size() > 256 * 1024;\n  }\n\n  private _start(): void {\n    if (this._state !== InternalTaskState.RUNNING) {\n      // This can happen if someone pauses us in a resume callback, for example.\n      return;\n    }\n    if (this._request !== undefined) {\n      return;\n    }\n    if (this._resumable) {\n      if (this._uploadUrl === undefined) {\n        this._createResumable();\n      } else {\n        if (this._needToFetchStatus) {\n          this._fetchStatus();\n        } else {\n          if (this._needToFetchMetadata) {\n            // Happens if we miss the metadata on upload completion.\n            this._fetchMetadata();\n          } else {\n            this.pendingTimeout = setTimeout(() => {\n              this.pendingTimeout = undefined;\n              this._continueUpload();\n            }, this.sleepTime);\n          }\n        }\n      }\n    } else {\n      this._oneShotUpload();\n    }\n  }\n\n  private _resolveToken(\n    callback: (authToken: string | null, appCheckToken: string | null) => void\n  ): void {\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    Promise.all([\n      this._ref.storage._getAuthToken(),\n      this._ref.storage._getAppCheckToken()\n    ]).then(([authToken, appCheckToken]) => {\n      switch (this._state) {\n        case InternalTaskState.RUNNING:\n          callback(authToken, appCheckToken);\n          break;\n        case InternalTaskState.CANCELING:\n          this._transition(InternalTaskState.CANCELED);\n          break;\n        case InternalTaskState.PAUSING:\n          this._transition(InternalTaskState.PAUSED);\n          break;\n        default:\n      }\n    });\n  }\n\n  // TODO(andysoto): assert false\n\n  private _createResumable(): void {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = createResumableUpload(\n        this._ref.storage,\n        this._ref._location,\n        this._mappings,\n        this._blob,\n        this._metadata\n      );\n      const createRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = createRequest;\n      createRequest.getPromise().then((url: string) => {\n        this._request = undefined;\n        this._uploadUrl = url;\n        this._needToFetchStatus = false;\n        this.completeTransitions_();\n      }, this._errorHandler);\n    });\n  }\n\n  private _fetchStatus(): void {\n    // TODO(andysoto): assert(this.uploadUrl_ !== null);\n    const url = this._uploadUrl as string;\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = getResumableUploadStatus(\n        this._ref.storage,\n        this._ref._location,\n        url,\n        this._blob\n      );\n      const statusRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = statusRequest;\n      statusRequest.getPromise().then(status => {\n        status = status as ResumableUploadStatus;\n        this._request = undefined;\n        this._updateProgress(status.current);\n        this._needToFetchStatus = false;\n        if (status.finalized) {\n          this._needToFetchMetadata = true;\n        }\n        this.completeTransitions_();\n      }, this._errorHandler);\n    });\n  }\n\n  private _continueUpload(): void {\n    const chunkSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\n    const status = new ResumableUploadStatus(\n      this._transferred,\n      this._blob.size()\n    );\n\n    // TODO(andysoto): assert(this.uploadUrl_ !== null);\n    const url = this._uploadUrl as string;\n    this._resolveToken((authToken, appCheckToken) => {\n      let requestInfo;\n      try {\n        requestInfo = continueResumableUpload(\n          this._ref._location,\n          this._ref.storage,\n          url,\n          this._blob,\n          chunkSize,\n          this._mappings,\n          status,\n          this._makeProgressCallback()\n        );\n      } catch (e) {\n        this._error = e as StorageError;\n        this._transition(InternalTaskState.ERROR);\n        return;\n      }\n      const uploadRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken,\n        /*retry=*/ false // Upload requests should not be retried as each retry should be preceded by another query request. Which is handled in this file.\n      );\n      this._request = uploadRequest;\n      uploadRequest.getPromise().then((newStatus: ResumableUploadStatus) => {\n        this._increaseMultiplier();\n        this._request = undefined;\n        this._updateProgress(newStatus.current);\n        if (newStatus.finalized) {\n          this._metadata = newStatus.metadata;\n          this._transition(InternalTaskState.SUCCESS);\n        } else {\n          this.completeTransitions_();\n        }\n      }, this._errorHandler);\n    });\n  }\n\n  private _increaseMultiplier(): void {\n    const currentSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\n\n    // Max chunk size is 32M.\n    if (currentSize * 2 < 32 * 1024 * 1024) {\n      this._chunkMultiplier *= 2;\n    }\n  }\n\n  private _fetchMetadata(): void {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = getMetadata(\n        this._ref.storage,\n        this._ref._location,\n        this._mappings\n      );\n      const metadataRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = metadataRequest;\n      metadataRequest.getPromise().then(metadata => {\n        this._request = undefined;\n        this._metadata = metadata;\n        this._transition(InternalTaskState.SUCCESS);\n      }, this._metadataErrorHandler);\n    });\n  }\n\n  private _oneShotUpload(): void {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = multipartUpload(\n        this._ref.storage,\n        this._ref._location,\n        this._mappings,\n        this._blob,\n        this._metadata\n      );\n      const multipartRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = multipartRequest;\n      multipartRequest.getPromise().then(metadata => {\n        this._request = undefined;\n        this._metadata = metadata;\n        this._updateProgress(this._blob.size());\n        this._transition(InternalTaskState.SUCCESS);\n      }, this._errorHandler);\n    });\n  }\n\n  private _updateProgress(transferred: number): void {\n    const old = this._transferred;\n    this._transferred = transferred;\n\n    // A progress update can make the \"transferred\" value smaller (e.g. a\n    // partial upload not completed by server, after which the \"transferred\"\n    // value may reset to the value at the beginning of the request).\n    if (this._transferred !== old) {\n      this._notifyObservers();\n    }\n  }\n\n  private _transition(state: InternalTaskState): void {\n    if (this._state === state) {\n      return;\n    }\n    switch (state) {\n      case InternalTaskState.CANCELING:\n      case InternalTaskState.PAUSING:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING);\n        this._state = state;\n        if (this._request !== undefined) {\n          this._request.cancel();\n        } else if (this.pendingTimeout) {\n          clearTimeout(this.pendingTimeout);\n          this.pendingTimeout = undefined;\n          this.completeTransitions_();\n        }\n        break;\n      case InternalTaskState.RUNNING:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSED ||\n        //        this.state_ === InternalTaskState.PAUSING);\n        const wasPaused = this._state === InternalTaskState.PAUSED;\n        this._state = state;\n        if (wasPaused) {\n          this._notifyObservers();\n          this._start();\n        }\n        break;\n      case InternalTaskState.PAUSED:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case InternalTaskState.CANCELED:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSED ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._error = canceled();\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case InternalTaskState.ERROR:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case InternalTaskState.SUCCESS:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      default: // Ignore\n    }\n  }\n\n  private completeTransitions_(): void {\n    switch (this._state) {\n      case InternalTaskState.PAUSING:\n        this._transition(InternalTaskState.PAUSED);\n        break;\n      case InternalTaskState.CANCELING:\n        this._transition(InternalTaskState.CANCELED);\n        break;\n      case InternalTaskState.RUNNING:\n        this._start();\n        break;\n      default:\n        // TODO(andysoto): assert(false);\n        break;\n    }\n  }\n\n  /**\n   * A snapshot of the current task state.\n   */\n  get snapshot(): UploadTaskSnapshot {\n    const externalState = taskStateFromInternalTaskState(this._state);\n    return {\n      bytesTransferred: this._transferred,\n      totalBytes: this._blob.size(),\n      state: externalState,\n      metadata: this._metadata!,\n      task: this,\n      ref: this._ref\n    };\n  }\n\n  /**\n   * Adds a callback for an event.\n   * @param type - The type of event to listen for.\n   * @param nextOrObserver -\n   *     The `next` function, which gets called for each item in\n   *     the event stream, or an observer object with some or all of these three\n   *     properties (`next`, `error`, `complete`).\n   * @param error - A function that gets called with a `StorageError`\n   *     if the event stream ends due to an error.\n   * @param completed - A function that gets called if the\n   *     event stream ends normally.\n   * @returns\n   *     If only the event argument is passed, returns a function you can use to\n   *     add callbacks (see the examples above). If more than just the event\n   *     argument is passed, returns a function you can call to unregister the\n   *     callbacks.\n   */\n  on(\n    type: TaskEvent,\n    nextOrObserver?:\n      | StorageObserver<UploadTaskSnapshot>\n      | null\n      | ((snapshot: UploadTaskSnapshot) => unknown),\n    error?: ((a: StorageError) => unknown) | null,\n    completed?: CompleteFn | null\n  ): Unsubscribe | Subscribe<UploadTaskSnapshot> {\n    // Note: `type` isn't being used. Its type is also incorrect. TaskEvent should not be a string.\n    const observer = new Observer(\n      (nextOrObserver as\n        | StorageObserverInternal<UploadTaskSnapshot>\n        | NextFn<UploadTaskSnapshot>) || undefined,\n      error || undefined,\n      completed || undefined\n    );\n    this._addObserver(observer);\n    return () => {\n      this._removeObserver(observer);\n    };\n  }\n\n  /**\n   * This object behaves like a Promise, and resolves with its snapshot data\n   * when the upload completes.\n   * @param onFulfilled - The fulfillment callback. Promise chaining works as normal.\n   * @param onRejected - The rejection callback.\n   */\n  then<U>(\n    onFulfilled?: ((value: UploadTaskSnapshot) => U | Promise<U>) | null,\n    onRejected?: ((error: StorageError) => U | Promise<U>) | null\n  ): Promise<U> {\n    // These casts are needed so that TypeScript can infer the types of the\n    // resulting Promise.\n    return this._promise.then<U>(\n      onFulfilled as (value: UploadTaskSnapshot) => U | Promise<U>,\n      onRejected as ((error: unknown) => Promise<never>) | null\n    );\n  }\n\n  /**\n   * Equivalent to calling `then(null, onRejected)`.\n   */\n  catch<T>(onRejected: (p1: StorageError) => T | Promise<T>): Promise<T> {\n    return this.then(null, onRejected);\n  }\n\n  /**\n   * Adds the given observer.\n   */\n  private _addObserver(observer: Observer<UploadTaskSnapshot>): void {\n    this._observers.push(observer);\n    this._notifyObserver(observer);\n  }\n\n  /**\n   * Removes the given observer.\n   */\n  private _removeObserver(observer: Observer<UploadTaskSnapshot>): void {\n    const i = this._observers.indexOf(observer);\n    if (i !== -1) {\n      this._observers.splice(i, 1);\n    }\n  }\n\n  private _notifyObservers(): void {\n    this._finishPromise();\n    const observers = this._observers.slice();\n    observers.forEach(observer => {\n      this._notifyObserver(observer);\n    });\n  }\n\n  private _finishPromise(): void {\n    if (this._resolve !== undefined) {\n      let triggered = true;\n      switch (taskStateFromInternalTaskState(this._state)) {\n        case TaskState.SUCCESS:\n          fbsAsync(this._resolve.bind(null, this.snapshot))();\n          break;\n        case TaskState.CANCELED:\n        case TaskState.ERROR:\n          const toCall = this._reject as (p1: StorageError) => void;\n          fbsAsync(toCall.bind(null, this._error as StorageError))();\n          break;\n        default:\n          triggered = false;\n          break;\n      }\n      if (triggered) {\n        this._resolve = undefined;\n        this._reject = undefined;\n      }\n    }\n  }\n\n  private _notifyObserver(observer: Observer<UploadTaskSnapshot>): void {\n    const externalState = taskStateFromInternalTaskState(this._state);\n    switch (externalState) {\n      case TaskState.RUNNING:\n      case TaskState.PAUSED:\n        if (observer.next) {\n          fbsAsync(observer.next.bind(observer, this.snapshot))();\n        }\n        break;\n      case TaskState.SUCCESS:\n        if (observer.complete) {\n          fbsAsync(observer.complete.bind(observer))();\n        }\n        break;\n      case TaskState.CANCELED:\n      case TaskState.ERROR:\n        if (observer.error) {\n          fbsAsync(\n            observer.error.bind(observer, this._error as StorageError)\n          )();\n        }\n        break;\n      default:\n        // TODO(andysoto): assert(false);\n        if (observer.error) {\n          fbsAsync(\n            observer.error.bind(observer, this._error as StorageError)\n          )();\n        }\n    }\n  }\n\n  /**\n   * Resumes a paused task. Has no effect on a currently running or failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  resume(): boolean {\n    const valid =\n      this._state === InternalTaskState.PAUSED ||\n      this._state === InternalTaskState.PAUSING;\n    if (valid) {\n      this._transition(InternalTaskState.RUNNING);\n    }\n    return valid;\n  }\n\n  /**\n   * Pauses a currently running task. Has no effect on a paused or failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  pause(): boolean {\n    const valid = this._state === InternalTaskState.RUNNING;\n    if (valid) {\n      this._transition(InternalTaskState.PAUSING);\n    }\n    return valid;\n  }\n\n  /**\n   * Cancels a currently running or paused task. Has no effect on a complete or\n   * failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  cancel(): boolean {\n    const valid =\n      this._state === InternalTaskState.RUNNING ||\n      this._state === InternalTaskState.PAUSING;\n    if (valid) {\n      this._transition(InternalTaskState.CANCELING);\n    }\n    return valid;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Defines the Firebase StorageReference class.\n */\n\nimport { FbsBlob } from './implementation/blob';\nimport { Location } from './implementation/location';\nimport { getMappings } from './implementation/metadata';\nimport { child, lastComponent, parent } from './implementation/path';\nimport {\n  deleteObject as requestsDeleteObject,\n  getBytes,\n  getDownloadUrl as requestsGetDownloadUrl,\n  getMetadata as requestsGetMetadata,\n  list as requestsList,\n  multipartUpload,\n  updateMetadata as requestsUpdateMetadata\n} from './implementation/requests';\nimport { ListOptions, UploadResult } from './public-types';\nimport { dataFromString, StringFormat } from './implementation/string';\nimport { Metadata } from './metadata';\nimport { FirebaseStorageImpl } from './service';\nimport { ListResult } from './list';\nimport { UploadTask } from './task';\nimport { invalidRootOperation, noDownloadURL } from './implementation/error';\nimport { validateNumber } from './implementation/type';\nimport {\n  newBlobConnection,\n  newBytesConnection,\n  newStreamConnection,\n  newTextConnection\n} from './platform/connection';\nimport { RequestInfo } from './implementation/requestinfo';\n\n/**\n * Provides methods to interact with a bucket in the Firebase Storage service.\n * @internal\n * @param _location - An fbs.location, or the URL at\n *     which to base this object, in one of the following forms:\n *         gs://<bucket>/<object-path>\n *         http[s]://firebasestorage.googleapis.com/\n *                     <api-version>/b/<bucket>/o/<object-path>\n *     Any query or fragment strings will be ignored in the http[s]\n *     format. If no value is passed, the storage object will use a URL based on\n *     the project ID of the base firebase.App instance.\n */\nexport class Reference {\n  _location: Location;\n\n  constructor(\n    private _service: FirebaseStorageImpl,\n    location: string | Location\n  ) {\n    if (location instanceof Location) {\n      this._location = location;\n    } else {\n      this._location = Location.makeFromUrl(location, _service.host);\n    }\n  }\n\n  /**\n   * Returns the URL for the bucket and path this object references,\n   *     in the form gs://<bucket>/<object-path>\n   * @override\n   */\n  toString(): string {\n    return 'gs://' + this._location.bucket + '/' + this._location.path;\n  }\n\n  protected _newRef(\n    service: FirebaseStorageImpl,\n    location: Location\n  ): Reference {\n    return new Reference(service, location);\n  }\n\n  /**\n   * A reference to the root of this object's bucket.\n   */\n  get root(): Reference {\n    const location = new Location(this._location.bucket, '');\n    return this._newRef(this._service, location);\n  }\n\n  /**\n   * The name of the bucket containing this reference's object.\n   */\n  get bucket(): string {\n    return this._location.bucket;\n  }\n\n  /**\n   * The full path of this object.\n   */\n  get fullPath(): string {\n    return this._location.path;\n  }\n\n  /**\n   * The short name of this object, which is the last component of the full path.\n   * For example, if fullPath is 'full/path/image.png', name is 'image.png'.\n   */\n  get name(): string {\n    return lastComponent(this._location.path);\n  }\n\n  /**\n   * The `StorageService` instance this `StorageReference` is associated with.\n   */\n  get storage(): FirebaseStorageImpl {\n    return this._service;\n  }\n\n  /**\n   * A `StorageReference` pointing to the parent location of this `StorageReference`, or null if\n   * this reference is the root.\n   */\n  get parent(): Reference | null {\n    const newPath = parent(this._location.path);\n    if (newPath === null) {\n      return null;\n    }\n    const location = new Location(this._location.bucket, newPath);\n    return new Reference(this._service, location);\n  }\n\n  /**\n   * Utility function to throw an error in methods that do not accept a root reference.\n   */\n  _throwIfRoot(name: string): void {\n    if (this._location.path === '') {\n      throw invalidRootOperation(name);\n    }\n  }\n}\n\n/**\n * Download the bytes at the object's location.\n * @returns A Promise containing the downloaded bytes.\n */\nexport function getBytesInternal(\n  ref: Reference,\n  maxDownloadSizeBytes?: number\n): Promise<ArrayBuffer> {\n  ref._throwIfRoot('getBytes');\n  const requestInfo = getBytes(\n    ref.storage,\n    ref._location,\n    maxDownloadSizeBytes\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newBytesConnection)\n    .then(bytes =>\n      maxDownloadSizeBytes !== undefined\n        ? // GCS may not honor the Range header for small files\n          (bytes as ArrayBuffer).slice(0, maxDownloadSizeBytes)\n        : (bytes as ArrayBuffer)\n    );\n}\n\n/**\n * Download the bytes at the object's location.\n * @returns A Promise containing the downloaded blob.\n */\nexport function getBlobInternal(\n  ref: Reference,\n  maxDownloadSizeBytes?: number\n): Promise<Blob> {\n  ref._throwIfRoot('getBlob');\n  const requestInfo = getBytes(\n    ref.storage,\n    ref._location,\n    maxDownloadSizeBytes\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newBlobConnection)\n    .then(blob =>\n      maxDownloadSizeBytes !== undefined\n        ? // GCS may not honor the Range header for small files\n          (blob as Blob).slice(0, maxDownloadSizeBytes)\n        : (blob as Blob)\n    );\n}\n\n/** Stream the bytes at the object's location. */\nexport function getStreamInternal(\n  ref: Reference,\n  maxDownloadSizeBytes?: number\n): ReadableStream {\n  ref._throwIfRoot('getStream');\n  const requestInfo: RequestInfo<ReadableStream, ReadableStream> = getBytes(\n    ref.storage,\n    ref._location,\n    maxDownloadSizeBytes\n  );\n\n  // Transforms the stream so that only `maxDownloadSizeBytes` bytes are piped to the result\n  const newMaxSizeTransform = (n: number): Transformer => {\n    let missingBytes = n;\n    return {\n      transform(chunk, controller: TransformStreamDefaultController) {\n        // GCS may not honor the Range header for small files\n        if (chunk.length < missingBytes) {\n          controller.enqueue(chunk);\n          missingBytes -= chunk.length;\n        } else {\n          controller.enqueue(chunk.slice(0, missingBytes));\n          controller.terminate();\n        }\n      }\n    };\n  };\n\n  const result =\n    maxDownloadSizeBytes !== undefined\n      ? new TransformStream(newMaxSizeTransform(maxDownloadSizeBytes))\n      : new TransformStream(); // The default transformer forwards all chunks to its readable side\n\n  ref.storage\n    .makeRequestWithTokens(requestInfo, newStreamConnection)\n    .then(readableStream => readableStream.pipeThrough(result))\n    .catch(err => result.writable.abort(err));\n\n  return result.readable;\n}\n\n/**\n * Uploads data to this object's location.\n * The upload is not resumable.\n *\n * @param ref - StorageReference where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the newly uploaded data.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadBytes(\n  ref: Reference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: Metadata\n): Promise<UploadResult> {\n  ref._throwIfRoot('uploadBytes');\n  const requestInfo = multipartUpload(\n    ref.storage,\n    ref._location,\n    getMappings(),\n    new FbsBlob(data, true),\n    metadata\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newTextConnection)\n    .then(finalMetadata => {\n      return {\n        metadata: finalMetadata,\n        ref\n      };\n    });\n}\n\n/**\n * Uploads data to this object's location.\n * The upload can be paused and resumed, and exposes progress updates.\n * @public\n * @param ref - StorageReference where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the newly uploaded data.\n * @returns An UploadTask\n */\nexport function uploadBytesResumable(\n  ref: Reference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: Metadata\n): UploadTask {\n  ref._throwIfRoot('uploadBytesResumable');\n  return new UploadTask(ref, new FbsBlob(data), metadata);\n}\n\n/**\n * Uploads a string to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - StorageReference where string should be uploaded.\n * @param value - The string to upload.\n * @param format - The format of the string to upload.\n * @param metadata - Metadata for the newly uploaded string.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadString(\n  ref: Reference,\n  value: string,\n  format: StringFormat = StringFormat.RAW,\n  metadata?: Metadata\n): Promise<UploadResult> {\n  ref._throwIfRoot('uploadString');\n  const data = dataFromString(format, value);\n  const metadataClone = { ...metadata } as Metadata;\n  if (metadataClone['contentType'] == null && data.contentType != null) {\n    metadataClone['contentType'] = data.contentType!;\n  }\n  return uploadBytes(ref, data.data, metadataClone);\n}\n\n/**\n * List all items (files) and prefixes (folders) under this storage reference.\n *\n * This is a helper method for calling list() repeatedly until there are\n * no more results. The default pagination size is 1000.\n *\n * Note: The results may not be consistent if objects are changed while this\n * operation is running.\n *\n * Warning: listAll may potentially consume too many resources if there are\n * too many results.\n * @public\n * @param ref - StorageReference to get list from.\n *\n * @returns A Promise that resolves with all the items and prefixes under\n *      the current storage reference. `prefixes` contains references to\n *      sub-directories and `items` contains references to objects in this\n *      folder. `nextPageToken` is never returned.\n */\nexport function listAll(ref: Reference): Promise<ListResult> {\n  const accumulator: ListResult = {\n    prefixes: [],\n    items: []\n  };\n  return listAllHelper(ref, accumulator).then(() => accumulator);\n}\n\n/**\n * Separated from listAll because async functions can't use \"arguments\".\n * @param ref\n * @param accumulator\n * @param pageToken\n */\nasync function listAllHelper(\n  ref: Reference,\n  accumulator: ListResult,\n  pageToken?: string\n): Promise<void> {\n  const opt: ListOptions = {\n    // maxResults is 1000 by default.\n    pageToken\n  };\n  const nextPage = await list(ref, opt);\n  accumulator.prefixes.push(...nextPage.prefixes);\n  accumulator.items.push(...nextPage.items);\n  if (nextPage.nextPageToken != null) {\n    await listAllHelper(ref, accumulator, nextPage.nextPageToken);\n  }\n}\n\n/**\n * List items (files) and prefixes (folders) under this storage reference.\n *\n * List API is only available for Firebase Rules Version 2.\n *\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n * delimited folder structure.\n * Refer to GCS's List API if you want to learn more.\n *\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n * support objects whose paths end with \"/\" or contain two consecutive\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n * list() may fail if there are too many unsupported objects in the bucket.\n * @public\n *\n * @param ref - StorageReference to get list from.\n * @param options - See ListOptions for details.\n * @returns A Promise that resolves with the items and prefixes.\n *      `prefixes` contains references to sub-folders and `items`\n *      contains references to objects in this folder. `nextPageToken`\n *      can be used to get the rest of the results.\n */\nexport function list(\n  ref: Reference,\n  options?: ListOptions | null\n): Promise<ListResult> {\n  if (options != null) {\n    if (typeof options.maxResults === 'number') {\n      validateNumber(\n        'options.maxResults',\n        /* minValue= */ 1,\n        /* maxValue= */ 1000,\n        options.maxResults\n      );\n    }\n  }\n  const op = options || {};\n  const requestInfo = requestsList(\n    ref.storage,\n    ref._location,\n    /*delimiter= */ '/',\n    op.pageToken,\n    op.maxResults\n  );\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * A `Promise` that resolves with the metadata for this object. If this\n * object doesn't exist or metadata cannot be retrieved, the promise is\n * rejected.\n * @public\n * @param ref - StorageReference to get metadata from.\n */\nexport function getMetadata(ref: Reference): Promise<Metadata> {\n  ref._throwIfRoot('getMetadata');\n  const requestInfo = requestsGetMetadata(\n    ref.storage,\n    ref._location,\n    getMappings()\n  );\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * Updates the metadata for this object.\n * @public\n * @param ref - StorageReference to update metadata for.\n * @param metadata - The new metadata for the object.\n *     Only values that have been explicitly set will be changed. Explicitly\n *     setting a value to null will remove the metadata.\n * @returns A `Promise` that resolves\n *     with the new metadata for this object.\n *     See `firebaseStorage.Reference.prototype.getMetadata`\n */\nexport function updateMetadata(\n  ref: Reference,\n  metadata: Partial<Metadata>\n): Promise<Metadata> {\n  ref._throwIfRoot('updateMetadata');\n  const requestInfo = requestsUpdateMetadata(\n    ref.storage,\n    ref._location,\n    metadata,\n    getMappings()\n  );\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * Returns the download URL for the given Reference.\n * @public\n * @returns A `Promise` that resolves with the download\n *     URL for this object.\n */\nexport function getDownloadURL(ref: Reference): Promise<string> {\n  ref._throwIfRoot('getDownloadURL');\n  const requestInfo = requestsGetDownloadUrl(\n    ref.storage,\n    ref._location,\n    getMappings()\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newTextConnection)\n    .then(url => {\n      if (url === null) {\n        throw noDownloadURL();\n      }\n      return url;\n    });\n}\n\n/**\n * Deletes the object at this location.\n * @public\n * @param ref - StorageReference for object to delete.\n * @returns A `Promise` that resolves if the deletion succeeds.\n */\nexport function deleteObject(ref: Reference): Promise<void> {\n  ref._throwIfRoot('deleteObject');\n  const requestInfo = requestsDeleteObject(ref.storage, ref._location);\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * Returns reference for object obtained by appending `childPath` to `ref`.\n *\n * @param ref - StorageReference to get child of.\n * @param childPath - Child path from provided ref.\n * @returns A reference to the object obtained by\n * appending childPath, removing any duplicate, beginning, or trailing\n * slashes.\n *\n */\nexport function _getChild(ref: Reference, childPath: string): Reference {\n  const newPath = child(ref._location.path, childPath);\n  const location = new Location(ref._location.bucket, newPath);\n  return new Reference(ref.storage, location);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Location } from './implementation/location';\nimport { FailRequest } from './implementation/failrequest';\nimport { Request, makeRequest } from './implementation/request';\nimport { RequestInfo } from './implementation/requestinfo';\nimport { Reference, _getChild } from './reference';\nimport { Provider } from '@firebase/component';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport {\n  FirebaseApp,\n  FirebaseOptions,\n  _isFirebaseServerApp\n} from '@firebase/app';\nimport {\n  CONFIG_STORAGE_BUCKET_KEY,\n  DEFAULT_HOST,\n  DEFAULT_MAX_OPERATION_RETRY_TIME,\n  DEFAULT_MAX_UPLOAD_RETRY_TIME\n} from './implementation/constants';\nimport {\n  invalidArgument,\n  appDeleted,\n  noDefaultBucket\n} from './implementation/error';\nimport { validateNumber } from './implementation/type';\nimport { FirebaseStorage } from './public-types';\nimport { createMockUserToken, EmulatorMockTokenOptions } from '@firebase/util';\nimport { Connection, ConnectionType } from './implementation/connection';\n\nexport function isUrl(path?: string): boolean {\n  return /^[A-Za-z]+:\\/\\//.test(path as string);\n}\n\n/**\n * Returns a firebaseStorage.Reference for the given url.\n */\nfunction refFromURL(service: FirebaseStorageImpl, url: string): Reference {\n  return new Reference(service, url);\n}\n\n/**\n * Returns a firebaseStorage.Reference for the given path in the default\n * bucket.\n */\nfunction refFromPath(\n  ref: FirebaseStorageImpl | Reference,\n  path?: string\n): Reference {\n  if (ref instanceof FirebaseStorageImpl) {\n    const service = ref;\n    if (service._bucket == null) {\n      throw noDefaultBucket();\n    }\n    const reference = new Reference(service, service._bucket!);\n    if (path != null) {\n      return refFromPath(reference, path);\n    } else {\n      return reference;\n    }\n  } else {\n    // ref is a Reference\n    if (path !== undefined) {\n      return _getChild(ref, path);\n    } else {\n      return ref;\n    }\n  }\n}\n\n/**\n * Returns a storage Reference for the given url.\n * @param storage - `Storage` instance.\n * @param url - URL. If empty, returns root reference.\n * @public\n */\nexport function ref(storage: FirebaseStorageImpl, url?: string): Reference;\n/**\n * Returns a storage Reference for the given path in the\n * default bucket.\n * @param storageOrRef - `Storage` service or storage `Reference`.\n * @param pathOrUrlStorage - path. If empty, returns root reference (if Storage\n * instance provided) or returns same reference (if Reference provided).\n * @public\n */\nexport function ref(\n  storageOrRef: FirebaseStorageImpl | Reference,\n  path?: string\n): Reference;\nexport function ref(\n  serviceOrRef: FirebaseStorageImpl | Reference,\n  pathOrUrl?: string\n): Reference | null {\n  if (pathOrUrl && isUrl(pathOrUrl)) {\n    if (serviceOrRef instanceof FirebaseStorageImpl) {\n      return refFromURL(serviceOrRef, pathOrUrl);\n    } else {\n      throw invalidArgument(\n        'To use ref(service, url), the first argument must be a Storage instance.'\n      );\n    }\n  } else {\n    return refFromPath(serviceOrRef, pathOrUrl);\n  }\n}\n\nfunction extractBucket(\n  host: string,\n  config?: FirebaseOptions\n): Location | null {\n  const bucketString = config?.[CONFIG_STORAGE_BUCKET_KEY];\n  if (bucketString == null) {\n    return null;\n  }\n  return Location.makeFromBucketSpec(bucketString, host);\n}\n\nexport function connectStorageEmulator(\n  storage: FirebaseStorageImpl,\n  host: string,\n  port: number,\n  options: {\n    mockUserToken?: EmulatorMockTokenOptions | string;\n  } = {}\n): void {\n  storage.host = `${host}:${port}`;\n  storage._protocol = 'http';\n  const { mockUserToken } = options;\n  if (mockUserToken) {\n    storage._overrideAuthToken =\n      typeof mockUserToken === 'string'\n        ? mockUserToken\n        : createMockUserToken(mockUserToken, storage.app.options.projectId);\n  }\n}\n\n/**\n * A service that provides Firebase Storage Reference instances.\n * @param opt_url - gs:// url to a custom Storage Bucket\n *\n * @internal\n */\nexport class FirebaseStorageImpl implements FirebaseStorage {\n  _bucket: Location | null = null;\n  /**\n   * This string can be in the formats:\n   * - host\n   * - host:port\n   */\n  private _host: string = DEFAULT_HOST;\n  _protocol: string = 'https';\n  protected readonly _appId: string | null = null;\n  private readonly _requests: Set<Request<unknown>>;\n  private _deleted: boolean = false;\n  private _maxOperationRetryTime: number;\n  private _maxUploadRetryTime: number;\n  _overrideAuthToken?: string;\n\n  constructor(\n    /**\n     * FirebaseApp associated with this StorageService instance.\n     */\n    readonly app: FirebaseApp,\n    readonly _authProvider: Provider<FirebaseAuthInternalName>,\n    /**\n     * @internal\n     */\n    readonly _appCheckProvider: Provider<AppCheckInternalComponentName>,\n    /**\n     * @internal\n     */\n    readonly _url?: string,\n    readonly _firebaseVersion?: string\n  ) {\n    this._maxOperationRetryTime = DEFAULT_MAX_OPERATION_RETRY_TIME;\n    this._maxUploadRetryTime = DEFAULT_MAX_UPLOAD_RETRY_TIME;\n    this._requests = new Set();\n    if (_url != null) {\n      this._bucket = Location.makeFromBucketSpec(_url, this._host);\n    } else {\n      this._bucket = extractBucket(this._host, this.app.options);\n    }\n  }\n\n  /**\n   * The host string for this service, in the form of `host` or\n   * `host:port`.\n   */\n  get host(): string {\n    return this._host;\n  }\n\n  set host(host: string) {\n    this._host = host;\n    if (this._url != null) {\n      this._bucket = Location.makeFromBucketSpec(this._url, host);\n    } else {\n      this._bucket = extractBucket(host, this.app.options);\n    }\n  }\n\n  /**\n   * The maximum time to retry uploads in milliseconds.\n   */\n  get maxUploadRetryTime(): number {\n    return this._maxUploadRetryTime;\n  }\n\n  set maxUploadRetryTime(time: number) {\n    validateNumber(\n      'time',\n      /* minValue=*/ 0,\n      /* maxValue= */ Number.POSITIVE_INFINITY,\n      time\n    );\n    this._maxUploadRetryTime = time;\n  }\n\n  /**\n   * The maximum time to retry operations other than uploads or downloads in\n   * milliseconds.\n   */\n  get maxOperationRetryTime(): number {\n    return this._maxOperationRetryTime;\n  }\n\n  set maxOperationRetryTime(time: number) {\n    validateNumber(\n      'time',\n      /* minValue=*/ 0,\n      /* maxValue= */ Number.POSITIVE_INFINITY,\n      time\n    );\n    this._maxOperationRetryTime = time;\n  }\n\n  async _getAuthToken(): Promise<string | null> {\n    if (this._overrideAuthToken) {\n      return this._overrideAuthToken;\n    }\n    const auth = this._authProvider.getImmediate({ optional: true });\n    if (auth) {\n      const tokenData = await auth.getToken();\n      if (tokenData !== null) {\n        return tokenData.accessToken;\n      }\n    }\n    return null;\n  }\n\n  async _getAppCheckToken(): Promise<string | null> {\n    if (_isFirebaseServerApp(this.app) && this.app.settings.appCheckToken) {\n      return this.app.settings.appCheckToken;\n    }\n    const appCheck = this._appCheckProvider.getImmediate({ optional: true });\n    if (appCheck) {\n      const result = await appCheck.getToken();\n      // TODO: What do we want to do if there is an error getting the token?\n      // Context: appCheck.getToken() will never throw even if an error happened. In the error case, a dummy token will be\n      // returned along with an error field describing the error. In general, we shouldn't care about the error condition and just use\n      // the token (actual or dummy) to send requests.\n      return result.token;\n    }\n    return null;\n  }\n\n  /**\n   * Stop running requests and prevent more from being created.\n   */\n  _delete(): Promise<void> {\n    if (!this._deleted) {\n      this._deleted = true;\n      this._requests.forEach(request => request.cancel());\n      this._requests.clear();\n    }\n    return Promise.resolve();\n  }\n\n  /**\n   * Returns a new firebaseStorage.Reference object referencing this StorageService\n   * at the given Location.\n   */\n  _makeStorageReference(loc: Location): Reference {\n    return new Reference(this, loc);\n  }\n\n  /**\n   * @param requestInfo - HTTP RequestInfo object\n   * @param authToken - Firebase auth token\n   */\n  _makeRequest<I extends ConnectionType, O>(\n    requestInfo: RequestInfo<I, O>,\n    requestFactory: () => Connection<I>,\n    authToken: string | null,\n    appCheckToken: string | null,\n    retry = true\n  ): Request<O> {\n    if (!this._deleted) {\n      const request = makeRequest(\n        requestInfo,\n        this._appId,\n        authToken,\n        appCheckToken,\n        requestFactory,\n        this._firebaseVersion,\n        retry\n      );\n      this._requests.add(request);\n      // Request removes itself from set when complete.\n      request.getPromise().then(\n        () => this._requests.delete(request),\n        () => this._requests.delete(request)\n      );\n      return request;\n    } else {\n      return new FailRequest(appDeleted());\n    }\n  }\n\n  async makeRequestWithTokens<I extends ConnectionType, O>(\n    requestInfo: RequestInfo<I, O>,\n    requestFactory: () => Connection<I>\n  ): Promise<O> {\n    const [authToken, appCheckToken] = await Promise.all([\n      this._getAuthToken(),\n      this._getAppCheckToken()\n    ]);\n\n    return this._makeRequest(\n      requestInfo,\n      requestFactory,\n      authToken,\n      appCheckToken\n    ).getPromise();\n  }\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64urlEncodeWithoutPadding } from './crypt';\n\n// Firebase Auth tokens contain snake_case claims following the JWT standard / convention.\n/* eslint-disable camelcase */\n\nexport type FirebaseSignInProvider =\n  | 'custom'\n  | 'email'\n  | 'password'\n  | 'phone'\n  | 'anonymous'\n  | 'google.com'\n  | 'facebook.com'\n  | 'github.com'\n  | 'twitter.com'\n  | 'microsoft.com'\n  | 'apple.com';\n\ninterface FirebaseIdToken {\n  // Always set to https://securetoken.google.com/PROJECT_ID\n  iss: string;\n\n  // Always set to PROJECT_ID\n  aud: string;\n\n  // The user's unique ID\n  sub: string;\n\n  // The token issue time, in seconds since epoch\n  iat: number;\n\n  // The token expiry time, normally 'iat' + 3600\n  exp: number;\n\n  // The user's unique ID. Must be equal to 'sub'\n  user_id: string;\n\n  // The time the user authenticated, normally 'iat'\n  auth_time: number;\n\n  // The sign in provider, only set when the provider is 'anonymous'\n  provider_id?: 'anonymous';\n\n  // The user's primary email\n  email?: string;\n\n  // The user's email verification status\n  email_verified?: boolean;\n\n  // The user's primary phone number\n  phone_number?: string;\n\n  // The user's display name\n  name?: string;\n\n  // The user's profile photo URL\n  picture?: string;\n\n  // Information on all identities linked to this user\n  firebase: {\n    // The primary sign-in provider\n    sign_in_provider: FirebaseSignInProvider;\n\n    // A map of providers to the user's list of unique identifiers from\n    // each provider\n    identities?: { [provider in FirebaseSignInProvider]?: string[] };\n  };\n\n  // Custom claims set by the developer\n  [claim: string]: unknown;\n\n  uid?: never; // Try to catch a common mistake of \"uid\" (should be \"sub\" instead).\n}\n\nexport type EmulatorMockTokenOptions = ({ user_id: string } | { sub: string }) &\n  Partial<FirebaseIdToken>;\n\nexport function createMockUserToken(\n  token: EmulatorMockTokenOptions,\n  projectId?: string\n): string {\n  if (token.uid) {\n    throw new Error(\n      'The \"uid\" field is no longer supported by mockUserToken. Please use \"sub\" instead for Firebase Auth User ID.'\n    );\n  }\n  // Unsecured JWTs use \"none\" as the algorithm.\n  const header = {\n    alg: 'none',\n    type: 'JWT'\n  };\n\n  const project = projectId || 'demo-project';\n  const iat = token.iat || 0;\n  const sub = token.sub || token.user_id;\n  if (!sub) {\n    throw new Error(\"mockUserToken must contain 'sub' or 'user_id' field!\");\n  }\n\n  const payload: FirebaseIdToken = {\n    // Set all required fields to decent defaults\n    iss: `https://securetoken.google.com/${project}`,\n    aud: project,\n    iat,\n    exp: iat + 3600,\n    auth_time: iat,\n    sub,\n    user_id: sub,\n    firebase: {\n      sign_in_provider: 'custom',\n      identities: {}\n    },\n\n    // Override with user options\n    ...token\n  };\n\n  // Unsecured JWTs use the empty string as a signature.\n  const signature = '';\n  return [\n    base64urlEncodeWithoutPadding(JSON.stringify(header)),\n    base64urlEncodeWithoutPadding(JSON.stringify(payload)),\n    signature\n  ].join('.');\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\n\nimport {\n  ref as refInternal,\n  FirebaseStorageImpl,\n  connectStorageEmulator as connectEmulatorInternal\n} from './service';\nimport { Provider } from '@firebase/component';\n\nimport {\n  StorageReference,\n  FirebaseStorage,\n  UploadResult,\n  ListOptions,\n  ListResult,\n  UploadTask,\n  SettableMetadata,\n  UploadMetadata,\n  FullMetadata\n} from './public-types';\nimport { Metadata as MetadataInternal } from './metadata';\nimport {\n  uploadBytes as uploadBytesInternal,\n  uploadBytesResumable as uploadBytesResumableInternal,\n  uploadString as uploadStringInternal,\n  getMetadata as getMetadataInternal,\n  updateMetadata as updateMetadataInternal,\n  list as listInternal,\n  listAll as listAllInternal,\n  getDownloadURL as getDownloadURLInternal,\n  deleteObject as deleteObjectInternal,\n  Reference,\n  _getChild as _getChildInternal,\n  getBytesInternal\n} from './reference';\nimport { STORAGE_TYPE } from './constants';\nimport {\n  EmulatorMockTokenOptions,\n  getModularInstance,\n  getDefaultEmulatorHostnameAndPort\n} from '@firebase/util';\nimport { StringFormat } from './implementation/string';\n\nexport { EmulatorMockTokenOptions } from '@firebase/util';\n\nexport { StorageError, StorageErrorCode } from './implementation/error';\n\n/**\n * Public types.\n */\nexport * from './public-types';\n\nexport { Location as _Location } from './implementation/location';\nexport { UploadTask as _UploadTask } from './task';\nexport type { Reference as _Reference } from './reference';\nexport type { FirebaseStorageImpl as _FirebaseStorageImpl } from './service';\nexport { FbsBlob as _FbsBlob } from './implementation/blob';\nexport { dataFromString as _dataFromString } from './implementation/string';\nexport {\n  invalidRootOperation as _invalidRootOperation,\n  invalidArgument as _invalidArgument\n} from './implementation/error';\nexport {\n  TaskEvent as _TaskEvent,\n  TaskState as _TaskState\n} from './implementation/taskenums';\nexport { StringFormat };\n\n/**\n * Downloads the data at the object's location. Returns an error if the object\n * is not found.\n *\n * To use this functionality, you have to whitelist your app's origin in your\n * Cloud Storage bucket. See also\n * https://cloud.google.com/storage/docs/configuring-cors\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A Promise containing the object's bytes\n */\nexport function getBytes(\n  ref: StorageReference,\n  maxDownloadSizeBytes?: number\n): Promise<ArrayBuffer> {\n  ref = getModularInstance(ref);\n  return getBytesInternal(ref as Reference, maxDownloadSizeBytes);\n}\n\n/**\n * Uploads data to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - {@link StorageReference} where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the data to upload.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadBytes(\n  ref: StorageReference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: UploadMetadata\n): Promise<UploadResult> {\n  ref = getModularInstance(ref);\n  return uploadBytesInternal(\n    ref as Reference,\n    data,\n    metadata as MetadataInternal\n  );\n}\n\n/**\n * Uploads a string to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - {@link StorageReference} where string should be uploaded.\n * @param value - The string to upload.\n * @param format - The format of the string to upload.\n * @param metadata - Metadata for the string to upload.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadString(\n  ref: StorageReference,\n  value: string,\n  format?: StringFormat,\n  metadata?: UploadMetadata\n): Promise<UploadResult> {\n  ref = getModularInstance(ref);\n  return uploadStringInternal(\n    ref as Reference,\n    value,\n    format,\n    metadata as MetadataInternal\n  );\n}\n\n/**\n * Uploads data to this object's location.\n * The upload can be paused and resumed, and exposes progress updates.\n * @public\n * @param ref - {@link StorageReference} where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the data to upload.\n * @returns An UploadTask\n */\nexport function uploadBytesResumable(\n  ref: StorageReference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: UploadMetadata\n): UploadTask {\n  ref = getModularInstance(ref);\n  return uploadBytesResumableInternal(\n    ref as Reference,\n    data,\n    metadata as MetadataInternal\n  ) as UploadTask;\n}\n\n/**\n * A `Promise` that resolves with the metadata for this object. If this\n * object doesn't exist or metadata cannot be retrieved, the promise is\n * rejected.\n * @public\n * @param ref - {@link StorageReference} to get metadata from.\n */\nexport function getMetadata(ref: StorageReference): Promise<FullMetadata> {\n  ref = getModularInstance(ref);\n  return getMetadataInternal(ref as Reference) as Promise<FullMetadata>;\n}\n\n/**\n * Updates the metadata for this object.\n * @public\n * @param ref - {@link StorageReference} to update metadata for.\n * @param metadata - The new metadata for the object.\n *     Only values that have been explicitly set will be changed. Explicitly\n *     setting a value to null will remove the metadata.\n * @returns A `Promise` that resolves with the new metadata for this object.\n */\nexport function updateMetadata(\n  ref: StorageReference,\n  metadata: SettableMetadata\n): Promise<FullMetadata> {\n  ref = getModularInstance(ref);\n  return updateMetadataInternal(\n    ref as Reference,\n    metadata as Partial<MetadataInternal>\n  ) as Promise<FullMetadata>;\n}\n\n/**\n * List items (files) and prefixes (folders) under this storage reference.\n *\n * List API is only available for Firebase Rules Version 2.\n *\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n * delimited folder structure.\n * Refer to GCS's List API if you want to learn more.\n *\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n * support objects whose paths end with \"/\" or contain two consecutive\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n * list() may fail if there are too many unsupported objects in the bucket.\n * @public\n *\n * @param ref - {@link StorageReference} to get list from.\n * @param options - See {@link ListOptions} for details.\n * @returns A `Promise` that resolves with the items and prefixes.\n *      `prefixes` contains references to sub-folders and `items`\n *      contains references to objects in this folder. `nextPageToken`\n *      can be used to get the rest of the results.\n */\nexport function list(\n  ref: StorageReference,\n  options?: ListOptions\n): Promise<ListResult> {\n  ref = getModularInstance(ref);\n  return listInternal(ref as Reference, options);\n}\n\n/**\n * List all items (files) and prefixes (folders) under this storage reference.\n *\n * This is a helper method for calling list() repeatedly until there are\n * no more results. The default pagination size is 1000.\n *\n * Note: The results may not be consistent if objects are changed while this\n * operation is running.\n *\n * Warning: `listAll` may potentially consume too many resources if there are\n * too many results.\n * @public\n * @param ref - {@link StorageReference} to get list from.\n *\n * @returns A `Promise` that resolves with all the items and prefixes under\n *      the current storage reference. `prefixes` contains references to\n *      sub-directories and `items` contains references to objects in this\n *      folder. `nextPageToken` is never returned.\n */\nexport function listAll(ref: StorageReference): Promise<ListResult> {\n  ref = getModularInstance(ref);\n  return listAllInternal(ref as Reference);\n}\n\n/**\n * Returns the download URL for the given {@link StorageReference}.\n * @public\n * @param ref - {@link StorageReference} to get the download URL for.\n * @returns A `Promise` that resolves with the download\n *     URL for this object.\n */\nexport function getDownloadURL(ref: StorageReference): Promise<string> {\n  ref = getModularInstance(ref);\n  return getDownloadURLInternal(ref as Reference);\n}\n\n/**\n * Deletes the object at this location.\n * @public\n * @param ref - {@link StorageReference} for object to delete.\n * @returns A `Promise` that resolves if the deletion succeeds.\n */\nexport function deleteObject(ref: StorageReference): Promise<void> {\n  ref = getModularInstance(ref);\n  return deleteObjectInternal(ref as Reference);\n}\n\n/**\n * Returns a {@link StorageReference} for the given url.\n * @param storage - {@link FirebaseStorage} instance.\n * @param url - URL. If empty, returns root reference.\n * @public\n */\nexport function ref(storage: FirebaseStorage, url?: string): StorageReference;\n/**\n * Returns a {@link StorageReference} for the given path in the\n * default bucket.\n * @param storageOrRef - {@link FirebaseStorage} or {@link StorageReference}.\n * @param pathOrUrlStorage - path. If empty, returns root reference (if {@link FirebaseStorage}\n * instance provided) or returns same reference (if {@link StorageReference} provided).\n * @public\n */\nexport function ref(\n  storageOrRef: FirebaseStorage | StorageReference,\n  path?: string\n): StorageReference;\nexport function ref(\n  serviceOrRef: FirebaseStorage | StorageReference,\n  pathOrUrl?: string\n): StorageReference | null {\n  serviceOrRef = getModularInstance(serviceOrRef);\n  return refInternal(\n    serviceOrRef as FirebaseStorageImpl | Reference,\n    pathOrUrl\n  );\n}\n\n/**\n * @internal\n */\nexport function _getChild(ref: StorageReference, childPath: string): Reference {\n  return _getChildInternal(ref as Reference, childPath);\n}\n\n/**\n * Gets a {@link FirebaseStorage} instance for the given Firebase app.\n * @public\n * @param app - Firebase app to get {@link FirebaseStorage} instance for.\n * @param bucketUrl - The gs:// url to your Firebase Storage Bucket.\n * If not passed, uses the app's default Storage Bucket.\n * @returns A {@link FirebaseStorage} instance.\n */\nexport function getStorage(\n  app: FirebaseApp = getApp(),\n  bucketUrl?: string\n): FirebaseStorage {\n  app = getModularInstance(app);\n  const storageProvider: Provider<'storage'> = _getProvider(app, STORAGE_TYPE);\n  const storageInstance = storageProvider.getImmediate({\n    identifier: bucketUrl\n  });\n  const emulator = getDefaultEmulatorHostnameAndPort('storage');\n  if (emulator) {\n    connectStorageEmulator(storageInstance, ...emulator);\n  }\n  return storageInstance;\n}\n\n/**\n * Modify this {@link FirebaseStorage} instance to communicate with the Cloud Storage emulator.\n *\n * @param storage - The {@link FirebaseStorage} instance\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @param options - Emulator options. `options.mockUserToken` is the mock auth\n * token to use for unit testing Security Rules.\n * @public\n */\nexport function connectStorageEmulator(\n  storage: FirebaseStorage,\n  host: string,\n  port: number,\n  options: {\n    mockUserToken?: EmulatorMockTokenOptions | string;\n  } = {}\n): void {\n  connectEmulatorInternal(storage as FirebaseStorageImpl, host, port, options);\n}\n", "/**\n * Cloud Storage for Firebase\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport {\n  _registerComponent,\n  registerVersion,\n  SDK_VERSION\n} from '@firebase/app';\n\nimport { FirebaseStorageImpl } from '../src/service';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactoryOptions\n} from '@firebase/component';\n\nimport { name, version } from '../package.json';\n\nimport { FirebaseStorage } from './public-types';\nimport { STORAGE_TYPE } from './constants';\n\nexport * from './api';\nexport * from './api.browser';\n\nfunction factory(\n  container: ComponentContainer,\n  { instanceIdentifier: url }: InstanceFactoryOptions\n): FirebaseStorage {\n  const app = container.getProvider('app').getImmediate();\n  const authProvider = container.getProvider('auth-internal');\n  const appCheckProvider = container.getProvider('app-check-internal');\n\n  return new FirebaseStorageImpl(\n    app,\n    authProvider,\n    appCheckProvider,\n    url,\n    SDK_VERSION\n  );\n}\n\nfunction registerStorage(): void {\n  _registerComponent(\n    new Component(\n      STORAGE_TYPE,\n      factory,\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n  //RUNTIME_ENV will be replaced during the compilation to \"node\" for nodejs and an empty string for browser\n  registerVersion(name, version, '__RUNTIME_ENV__');\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n\nregisterStorage();\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Type constant for Firebase Storage.\n */\nexport const STORAGE_TYPE = 'storage';\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { UploadTaskSnapshot } from '@firebase/storage';\nimport { ReferenceCompat } from './reference';\nimport { UploadTaskCompat } from './task';\nimport * as types from '@firebase/storage-types';\nimport { Compat } from '@firebase/util';\n\nexport class UploadTaskSnapshotCompat\n  implements types.UploadTaskSnapshot, Compat<UploadTaskSnapshot>\n{\n  constructor(\n    readonly _delegate: UploadTaskSnapshot,\n    readonly task: UploadTaskCompat,\n    readonly ref: ReferenceCompat\n  ) {}\n\n  get bytesTransferred(): number {\n    return this._delegate.bytesTransferred;\n  }\n  get metadata(): types.FullMetadata {\n    return this._delegate.metadata as types.FullMetadata;\n  }\n  get state(): string {\n    return this._delegate.state;\n  }\n  get totalBytes(): number {\n    return this._delegate.totalBytes;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  UploadTask,\n  StorageError,\n  UploadTaskSnapshot,\n  TaskEvent,\n  StorageObserver\n} from '@firebase/storage';\nimport { UploadTaskSnapshotCompat } from './tasksnapshot';\nimport { ReferenceCompat } from './reference';\nimport * as types from '@firebase/storage-types';\nimport { Compat } from '@firebase/util';\n\nexport class UploadTaskCompat implements types.UploadTask, Compat<UploadTask> {\n  constructor(\n    readonly _delegate: UploadTask,\n    private readonly _ref: ReferenceCompat\n  ) {}\n\n  get snapshot(): UploadTaskSnapshotCompat {\n    return new UploadTaskSnapshotCompat(\n      this._delegate.snapshot,\n      this,\n      this._ref\n    );\n  }\n\n  cancel = this._delegate.cancel.bind(this._delegate);\n  catch = this._delegate.catch.bind(this._delegate);\n  pause = this._delegate.pause.bind(this._delegate);\n  resume = this._delegate.resume.bind(this._delegate);\n\n  then(\n    onFulfilled?: ((a: UploadTaskSnapshotCompat) => unknown) | null,\n    onRejected?: ((a: StorageError) => unknown) | null\n  ): Promise<unknown> {\n    return this._delegate.then(snapshot => {\n      if (onFulfilled) {\n        return onFulfilled(\n          new UploadTaskSnapshotCompat(snapshot, this, this._ref)\n        );\n      }\n    }, onRejected);\n  }\n\n  on(\n    type: TaskEvent,\n    nextOrObserver?:\n      | types.StorageObserver<UploadTaskSnapshotCompat>\n      | null\n      | ((a: UploadTaskSnapshotCompat) => unknown),\n    error?: ((error: StorageError) => void) | null,\n    completed?: () => void | null\n  ): Unsubscribe | Subscribe<UploadTaskSnapshotCompat> {\n    let wrappedNextOrObserver:\n      | StorageObserver<UploadTaskSnapshot>\n      | undefined\n      | ((a: UploadTaskSnapshot) => unknown) = undefined;\n    if (!!nextOrObserver) {\n      if (typeof nextOrObserver === 'function') {\n        wrappedNextOrObserver = (taskSnapshot: UploadTaskSnapshot) =>\n          nextOrObserver(\n            new UploadTaskSnapshotCompat(taskSnapshot, this, this._ref)\n          );\n      } else {\n        wrappedNextOrObserver = {\n          next: !!nextOrObserver.next\n            ? (taskSnapshot: UploadTaskSnapshot) =>\n                nextOrObserver.next!(\n                  new UploadTaskSnapshotCompat(taskSnapshot, this, this._ref)\n                )\n            : undefined,\n          complete: nextOrObserver.complete || undefined,\n          error: nextOrObserver.error || undefined\n        };\n      }\n    }\n    return this._delegate.on(\n      type,\n      wrappedNextOrObserver,\n      error || undefined,\n      completed || undefined\n    );\n  }\n}\n\n/**\n * Subscribes to an event stream.\n */\nexport type Subscribe<T> = (\n  next?: NextFn<T> | StorageObserver<T>,\n  error?: ErrorFn,\n  complete?: CompleteFn\n) => Unsubscribe;\n\n/**\n * Unsubscribes from a stream.\n */\nexport type Unsubscribe = () => void;\n\n/**\n * Function that is called once for each value in a stream of values.\n */\nexport type NextFn<T> = (value: T) => void;\n\n/**\n * A function that is called with a `FirebaseStorageError`\n * if the event stream ends due to an error.\n */\nexport type ErrorFn = (error: StorageError) => void;\n\n/**\n * A function that is called if the event stream ends normally.\n */\nexport type CompleteFn = () => void;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { ListResult } from '@firebase/storage';\nimport * as types from '@firebase/storage-types';\nimport { ReferenceCompat } from './reference';\nimport { StorageServiceCompat } from './service';\nimport { Compat } from '@firebase/util';\n\nexport class ListResultCompat implements types.ListResult, Compat<ListResult> {\n  constructor(\n    readonly _delegate: ListResult,\n    private readonly _service: StorageServiceCompat\n  ) {}\n\n  get prefixes(): ReferenceCompat[] {\n    return this._delegate.prefixes.map(\n      ref => new ReferenceCompat(ref, this._service)\n    );\n  }\n  get items(): ReferenceCompat[] {\n    return this._delegate.items.map(\n      ref => new ReferenceCompat(ref, this._service)\n    );\n  }\n  get nextPageToken(): string | null {\n    return this._delegate.nextPageToken || null;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *  http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  StorageReference,\n  uploadBytesResumable,\n  list,\n  listAll,\n  getDownloadURL,\n  getMetadata,\n  updateMetadata,\n  deleteObject,\n  UploadTask,\n  StringFormat,\n  UploadMetadata,\n  FullMetadata,\n  SettableMetadata,\n  _UploadTask,\n  _getChild,\n  _Reference,\n  _FbsBlob,\n  _dataFromString,\n  _invalidRootOperation\n} from '@firebase/storage';\n\nimport { UploadTaskCompat } from './task';\nimport { ListResultCompat } from './list';\nimport { StorageServiceCompat } from './service';\n\nimport * as types from '@firebase/storage-types';\nimport { Compat } from '@firebase/util';\n\nexport class ReferenceCompat\n  implements types.Reference, Compat<StorageReference>\n{\n  constructor(\n    readonly _delegate: StorageReference,\n    public storage: StorageServiceCompat\n  ) {}\n\n  get name(): string {\n    return this._delegate.name;\n  }\n\n  get bucket(): string {\n    return this._delegate.bucket;\n  }\n\n  get fullPath(): string {\n    return this._delegate.fullPath;\n  }\n\n  toString(): string {\n    return this._delegate.toString();\n  }\n\n  /**\n   * @returns A reference to the object obtained by\n   * appending childPath, removing any duplicate, beginning, or trailing\n   * slashes.\n   */\n  child(childPath: string): types.Reference {\n    const reference = _getChild(this._delegate, childPath);\n    return new ReferenceCompat(reference, this.storage);\n  }\n\n  get root(): types.Reference {\n    return new ReferenceCompat(this._delegate.root, this.storage);\n  }\n\n  /**\n   * @returns A reference to the parent of the\n   * current object, or null if the current object is the root.\n   */\n  get parent(): types.Reference | null {\n    const reference = this._delegate.parent;\n    if (reference == null) {\n      return null;\n    }\n    return new ReferenceCompat(reference, this.storage);\n  }\n\n  /**\n   * Uploads a blob to this object's location.\n   * @param data - The blob to upload.\n   * @returns An UploadTask that lets you control and\n   * observe the upload.\n   */\n  put(\n    data: Blob | Uint8Array | ArrayBuffer,\n    metadata?: types.FullMetadata\n  ): types.UploadTask {\n    this._throwIfRoot('put');\n    return new UploadTaskCompat(\n      uploadBytesResumable(this._delegate, data, metadata as UploadMetadata),\n      this\n    );\n  }\n\n  /**\n   * Uploads a string to this object's location.\n   * @param value - The string to upload.\n   * @param format - The format of the string to upload.\n   * @returns An UploadTask that lets you control and\n   * observe the upload.\n   */\n  putString(\n    value: string,\n    format: StringFormat = StringFormat.RAW,\n    metadata?: types.UploadMetadata\n  ): types.UploadTask {\n    this._throwIfRoot('putString');\n    const data = _dataFromString(format, value);\n    const metadataClone = { ...metadata };\n    if (metadataClone['contentType'] == null && data.contentType != null) {\n      metadataClone['contentType'] = data.contentType;\n    }\n    return new UploadTaskCompat(\n      new _UploadTask(\n        this._delegate as _Reference,\n        new _FbsBlob(data.data, true),\n        metadataClone as FullMetadata & { [k: string]: string }\n      ) as UploadTask,\n      this\n    );\n  }\n\n  /**\n   * List all items (files) and prefixes (folders) under this storage reference.\n   *\n   * This is a helper method for calling list() repeatedly until there are\n   * no more results. The default pagination size is 1000.\n   *\n   * Note: The results may not be consistent if objects are changed while this\n   * operation is running.\n   *\n   * Warning: listAll may potentially consume too many resources if there are\n   * too many results.\n   *\n   * @returns A Promise that resolves with all the items and prefixes under\n   *  the current storage reference. `prefixes` contains references to\n   *  sub-directories and `items` contains references to objects in this\n   *  folder. `nextPageToken` is never returned.\n   */\n  listAll(): Promise<types.ListResult> {\n    return listAll(this._delegate).then(\n      r => new ListResultCompat(r, this.storage)\n    );\n  }\n\n  /**\n   * List items (files) and prefixes (folders) under this storage reference.\n   *\n   * List API is only available for Firebase Rules Version 2.\n   *\n   * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n   * delimited folder structure. Refer to GCS's List API if you want to learn more.\n   *\n   * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n   * support objects whose paths end with \"/\" or contain two consecutive\n   * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n   * list() may fail if there are too many unsupported objects in the bucket.\n   *\n   * @param options - See ListOptions for details.\n   * @returns A Promise that resolves with the items and prefixes.\n   * `prefixes` contains references to sub-folders and `items`\n   * contains references to objects in this folder. `nextPageToken`\n   * can be used to get the rest of the results.\n   */\n  list(options?: types.ListOptions | null): Promise<types.ListResult> {\n    return list(this._delegate, options || undefined).then(\n      r => new ListResultCompat(r, this.storage)\n    );\n  }\n\n  /**\n   * A `Promise` that resolves with the metadata for this object. If this\n   * object doesn't exist or metadata cannot be retrieved, the promise is\n   * rejected.\n   */\n  getMetadata(): Promise<types.FullMetadata> {\n    return getMetadata(this._delegate) as Promise<types.FullMetadata>;\n  }\n\n  /**\n   * Updates the metadata for this object.\n   * @param metadata - The new metadata for the object.\n   * Only values that have been explicitly set will be changed. Explicitly\n   * setting a value to null will remove the metadata.\n   * @returns A `Promise` that resolves\n   * with the new metadata for this object.\n   * @see firebaseStorage.Reference.prototype.getMetadata\n   */\n  updateMetadata(\n    metadata: types.SettableMetadata\n  ): Promise<types.FullMetadata> {\n    return updateMetadata(\n      this._delegate,\n      metadata as SettableMetadata\n    ) as Promise<types.FullMetadata>;\n  }\n\n  /**\n   * @returns A `Promise` that resolves with the download\n   * URL for this object.\n   */\n  getDownloadURL(): Promise<string> {\n    return getDownloadURL(this._delegate);\n  }\n\n  /**\n   * Deletes the object at this location.\n   * @returns A `Promise` that resolves if the deletion succeeds.\n   */\n  delete(): Promise<void> {\n    this._throwIfRoot('delete');\n    return deleteObject(this._delegate);\n  }\n\n  private _throwIfRoot(name: string): void {\n    if ((this._delegate as _Reference)._location.path === '') {\n      throw _invalidRootOperation(name);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as types from '@firebase/storage-types';\nimport { FirebaseApp } from '@firebase/app-types';\n\nimport {\n  ref,\n  connectStorageEmulator,\n  FirebaseStorage,\n  _Location,\n  _invalidArgument,\n  _FirebaseStorageImpl\n} from '@firebase/storage';\nimport { ReferenceCompat } from './reference';\nimport { Compat, EmulatorMockTokenOptions } from '@firebase/util';\n\n/**\n * A service that provides firebaseStorage.Reference instances.\n * @param opt_url gs:// url to a custom Storage Bucket\n */\nexport class StorageServiceCompat\n  implements types.FirebaseStorage, Compat<FirebaseStorage>\n{\n  constructor(public app: FirebaseApp, readonly _delegate: FirebaseStorage) {}\n\n  get maxOperationRetryTime(): number {\n    return this._delegate.maxOperationRetryTime;\n  }\n\n  get maxUploadRetryTime(): number {\n    return this._delegate.maxUploadRetryTime;\n  }\n\n  /**\n   * Returns a firebaseStorage.Reference for the given path in the default\n   * bucket.\n   */\n  ref(path?: string): types.Reference {\n    if (isUrl(path)) {\n      throw _invalidArgument(\n        'ref() expected a child path but got a URL, use refFromURL instead.'\n      );\n    }\n    return new ReferenceCompat(ref(this._delegate, path), this);\n  }\n\n  /**\n   * Returns a firebaseStorage.Reference object for the given absolute URL,\n   * which must be a gs:// or http[s]:// URL.\n   */\n  refFromURL(url: string): types.Reference {\n    if (!isUrl(url)) {\n      throw _invalidArgument(\n        'refFromURL() expected a full URL but got a child path, use ref() instead.'\n      );\n    }\n    try {\n      _Location.makeFromUrl(url, (this._delegate as _FirebaseStorageImpl).host);\n    } catch (e) {\n      throw _invalidArgument(\n        'refFromUrl() expected a valid full URL but got an invalid one.'\n      );\n    }\n    return new ReferenceCompat(ref(this._delegate, url), this);\n  }\n\n  setMaxUploadRetryTime(time: number): void {\n    this._delegate.maxUploadRetryTime = time;\n  }\n\n  setMaxOperationRetryTime(time: number): void {\n    this._delegate.maxOperationRetryTime = time;\n  }\n\n  useEmulator(\n    host: string,\n    port: number,\n    options: {\n      mockUserToken?: EmulatorMockTokenOptions | string;\n    } = {}\n  ): void {\n    connectStorageEmulator(this._delegate, host, port, options);\n  }\n}\n\nfunction isUrl(path?: string): boolean {\n  return /^[A-Za-z]+:\\/\\//.test(path as string);\n}\n"], "names": ["stringToByteArray", "str", "out", "let", "p", "i", "length", "c", "charCodeAt", "base64", "byteToCharMap_", "charToByteMap_", "byteToCharMapWebSafe_", "charToByteMapWebSafe_", "ENCODED_VALS_BASE", "ENCODED_VALS", "this", "ENCODED_VALS_WEBSAFE", "HAS_NATIVE_SUPPORT", "atob", "encodeByteArray", "input", "webSafe", "Array", "isArray", "Error", "init_", "byteToCharMap", "output", "byte1", "haveByte2", "byte2", "haveByte3", "byte3", "outByte3", "outByte4", "push", "join", "encodeString", "btoa", "decodeString", "byteArrayToString", "bytes", "decodeStringToByteArray", "pos", "u", "c2", "c3", "c1", "String", "fromCharCode", "charToByteMap", "char<PERSON>t", "byte4", "DecodeBase64StringError", "constructor", "name", "base64Encode", "utf8Bytes", "base64urlEncodeWithoutPadding", "replace", "FirebaseError", "code", "message", "customData", "super", "Object", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "PATTERN", "_", "key", "value", "fullMessage", "getModularInstance", "_delegate", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "DEFAULT_HOST", "CONFIG_STORAGE_BUCKET_KEY", "StorageError", "status_", "prependCode", "serverResponse", "_baseMessage", "status", "_codeEquals", "StorageErrorCode", "ErrorCode", "instance", "namespaceExports", "unknown", "UNKNOWN", "retryLimitExceeded", "RETRY_LIMIT_EXCEEDED", "canceled", "CANCELED", "cannotSliceBlob", "CANNOT_SLICE_BLOB", "invalidArgument", "INVALID_ARGUMENT", "appDeleted", "APP_DELETED", "invalidRootOperation", "INVALID_ROOT_OPERATION", "invalidFormat", "format", "INVALID_FORMAT", "internalError", "INTERNAL_ERROR", "Location", "bucket", "path", "path_", "isRoot", "fullServerUrl", "encode", "encodeURIComponent", "bucketOnlyServerUrl", "makeFromBucketSpec", "bucketString", "host", "bucketLocation", "makeFromUrl", "e", "INVALID_DEFAULT_BUCKET", "url", "location", "bucketDomain", "gsRegex", "RegExp", "httpModify", "loc", "decodeURIComponent", "firebaseStorageHost", "firebaseStorageRegExp", "cloudStorageHost", "groups", "regex", "indices", "postModify", "slice", "group", "captures", "exec", "bucketValue", "pathValue", "INVALID_URL", "FailRequest", "error", "promise_", "Promise", "reject", "getPromise", "cancel", "_appDelete", "isString", "isNativeBlob", "isNativeBlobDefined", "Blob", "validateNumber", "argument", "minValue", "maxValue", "makeUrl", "urlPart", "protocol", "origin", "makeQueryString", "params", "nextPart", "query<PERSON>art", "hasOwnProperty", "isRetryStatusCode", "additionalRetryCodes", "isFiveHundredCode", "isExtraRetryCode", "indexOf", "isAdditionalRetryCode", "NetworkRequest", "url_", "method_", "headers_", "body_", "successCodes_", "additionalRetryCodes_", "callback_", "errorCallback_", "timeout_", "progressCallback_", "connectionFactory_", "retry", "pendingConnection_", "backoffId_", "canceled_", "appDelete_", "resolve", "resolve_", "reject_", "start_", "doTheRequest", "backoff<PERSON>allback", "RequestEndStatus", "connection", "progressListener", "progressEvent", "loaded", "total", "lengthComputable", "addUploadProgressListener", "send", "then", "removeUploadProgressListener", "hitServer", "getErrorCode", "NO_ERROR", "getStatus", "wasCanceled", "ABORT", "successCode", "backoffDone", "requestWentThrough", "wasSuccessCode", "result", "getResponse", "err", "getErrorText", "doRequest", "backoffCompleteCb", "timeout", "waitSeconds", "retryTimeoutId", "globalTimeoutId", "hitTimeout", "cancelState", "triggeredCallback", "triggerCallback", "args", "apply", "callWithDelay", "millis", "setTimeout", "response<PERSON><PERSON>ler", "clearGlobalTimeout", "clearTimeout", "success", "call", "<PERSON><PERSON><PERSON><PERSON>", "Math", "random", "stopped", "stop", "wasTimeout", "appDelete", "id", "abort", "getBlob", "BlobBuilder", "WebKitBlobBuilder", "undefined", "bb", "append", "UNSUPPORTED_ENVIRONMENT", "decodeBase64", "encoded", "StringFormat", "RAW", "BASE64", "BASE64URL", "DATA_URL", "StringData", "contentType", "dataFromString", "stringData", "utf8Bytes_", "base64Bytes_", "dataUrl", "parts", "DataURLParts", "rest", "decoded", "hi", "lo", "b", "Uint8Array", "hasMinus", "hasUnder", "hasPlus", "hasSlash", "includes", "array", "dataURL", "matches", "match", "s", "end", "middle", "substring", "FbsBlob", "elideCopy", "size", "blobType", "data_", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "byteLength", "set", "size_", "type_", "startByte", "endByte", "blob", "start", "realBlob", "sliced", "webkitSlice", "mozSlice", "buffer", "blobby", "map", "val", "uint8Arrays", "finalLength", "merged", "for<PERSON>ach", "index", "uploadData", "jsonObjectOrNull", "obj", "JSON", "parse", "lastComponent", "lastIndexOf", "noXform_", "metadata", "Mapping", "server", "local", "writable", "xform", "mappings_", "getMappings", "mappings", "sizeMapping", "nameMapping", "_metadata", "fullPath", "Number", "addRef", "defineProperty", "get", "_makeStorageReference", "fromResourceString", "resourceString", "resource", "len", "mapping", "toResourceString", "stringify", "PREFIXES_KEY", "ITEMS_KEY", "fromResponseString", "listResult", "prefixes", "items", "nextPageToken", "pathWithoutTrailingSlash", "reference", "item", "RequestInfo", "method", "handler", "urlParams", "headers", "body", "<PERSON><PERSON><PERSON><PERSON>", "progressCallback", "successCodes", "handler<PERSON><PERSON><PERSON>", "cndn", "metadataHandler", "xhr", "text", "<PERSON><PERSON><PERSON><PERSON>", "downloadUrlHandler", "downloadUrlFromResourceString", "_protocol", "tokens", "split", "alt", "token", "sharedErrorHandler", "newErr", "UNAUTHORIZED_APP", "UNAUTHENTICATED", "QUOTA_EXCEEDED", "UNAUTHORIZED", "objectErrorHandler", "shared", "OBJECT_NOT_FOUND", "getMetadata", "maxOperationRetryTime", "requestInfo", "metadataForUpload_", "metadataClone", "assign", "multipartUpload", "X-Goog-Upload-Protocol", "boundary", "toString", "metadata_", "preBlobPart", "postBlobPart", "maxUploadRetryTime", "ResumableUploadStatus", "current", "finalized", "checkResumeHeader_", "allowed", "getResponseHeader", "createResumableUpload", "metadataForUpload", "X-Goog-Upload-Command", "X-Goog-Upload-Header-Content-Length", "X-Goog-Upload-Header-Content-Type", "Content-Type", "getResumableUploadStatus", "sizeString", "isNaN", "continueResumableUpload", "chunkSize", "SERVER_FILE_WRONG_SIZE", "bytesLeft", "bytesToUpload", "min", "uploadCommand", "X-Goog-Upload-Offset", "uploadStatus", "newCurrent", "TaskEvent", "STATE_CHANGED", "TaskState", "RUNNING", "PAUSED", "SUCCESS", "ERROR", "taskStateFromInternalTaskState", "state", "Observer", "nextOrObserver", "complete", "observer", "next", "async", "f", "argsToForward", "XhrTextConnection", "sent_", "xhr_", "XMLHttpRequest", "initXhr", "errorCode_", "sendPromise_", "addEventListener", "NETWORK_ERROR", "open", "setRequestHeader", "response", "statusText", "header", "listener", "upload", "removeEventListener", "responseType", "newTextConnection", "UploadTask", "isExponentialBackoffExpired", "sleepTime", "maxSleepTime", "ref", "_transferred", "_needToFetchStatus", "_needToFetchMetadata", "_observers", "_error", "_uploadUrl", "_request", "_chunkMultiplier", "_resolve", "_reject", "_ref", "_blob", "_mappings", "_resumable", "_shouldDoResumable", "_state", "_error<PERSON><PERSON><PERSON>", "completeTransitions_", "backoffExpired", "max", "_transition", "_metadataErrorHandler", "storage", "_promise", "_start", "_makeProgressCallback", "sizeBefore", "_updateProgress", "_createResumable", "_fetchStatus", "_fetchMetadata", "pendingTimeout", "_continueUpload", "_oneShotUpload", "_resolveToken", "all", "_getAuthToken", "_getAppCheckToken", "authToken", "appCheckToken", "_location", "createRequest", "_makeRequest", "statusRequest", "uploadRequest", "_increaseMultiplier", "newStatus", "metadataRequest", "multipartRequest", "transferred", "old", "_notifyObservers", "wasPaused", "snapshot", "externalState", "bytesTransferred", "totalBytes", "task", "on", "completed", "_addObserver", "_removeObserver", "onFulfilled", "onRejected", "catch", "_notifyObserver", "splice", "_finishPromise", "triggered", "fbsAsync", "bind", "resume", "valid", "pause", "Reference", "_service", "_newRef", "root", "parent", "newPath", "_throwIfRoot", "listAll", "accumulator", "listAllHelper", "pageToken", "opt", "nextPage", "await", "list", "options", "maxResults", "delimiter", "op", "makeRequestWithTokens", "updateMetadata", "getDownloadURL", "NO_DOWNLOAD_URL", "deleteObject", "_xhr", "_text", "_get<PERSON><PERSON>d", "child<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "component", "ref<PERSON><PERSON><PERSON><PERSON>", "FirebaseStorageImpl", "_bucket", "NO_DEFAULT_BUCKET", "serviceOrRef", "pathOrUrl", "test", "extractBucket", "config", "connectStorageEmulator", "port", "mockUserToken", "_overrideAuthToken", "projectId", "uid", "project", "iat", "sub", "user_id", "payload", "iss", "aud", "exp", "auth_time", "firebase", "sign_in_provider", "identities", "alg", "app", "_authProvider", "_appCheckProvider", "_url", "_firebaseVersion", "_host", "_appId", "_deleted", "_maxOperationRetryTime", "_maxUploadRetryTime", "_requests", "Set", "time", "POSITIVE_INFINITY", "auth", "getImmediate", "optional", "tokenData", "getToken", "accessToken", "appCheck", "_isFirebaseServerApp", "settings", "_delete", "request", "clear", "requestFactory", "makeRequest", "appId", "firebaseVersion", "add", "delete", "uploadBytesResumable", "requestsGetMetadata", "refInternal", "factory", "container", "instanceIdentifier", "get<PERSON><PERSON><PERSON>", "authProvider", "appCheckProvider", "SDK_VERSION", "_registerComponent", "registerVersion", "UploadTaskSnapshotCompat", "UploadTaskCompat", "wrappedNextOrObserver", "taskSnapshot", "ListResultCompat", "ReferenceCompat", "child", "_getChildInternal", "put", "putString", "_dataFromString", "_UploadTask", "_FbsBlob", "listAllInternal", "r", "listInternal", "updateMetadataInternal", "getDownloadURLInternal", "deleteObjectInternal", "_invalidRootOperation", "StorageServiceCompat", "isUrl", "_invalidArgument", "refFromURL", "_Location", "setMaxUploadRetryTime", "setMaxOperationRetryTime", "useEmulator", "connectEmulatorInternal", "storageExp", "identifier", "Storage", "INTERNAL", "registerComponent"], "mappings": "ibAiBMA,EAAoB,SAAUC,GAElC,IAAMC,EAAgB,GACtBC,IAAIC,EAAI,EACR,IAAKD,IAAIE,EAAI,EAAGA,EAAIJ,EAAIK,OAAQD,CAAC,GAAI,CACnCF,IAAII,EAAIN,EAAIO,WAAWH,CAAC,EACpBE,EAAI,IACNL,EAAIE,CAAC,IAAMG,GACFA,EAAI,KACbL,EAAIE,CAAC,IAAOG,GAAK,EAAK,KAGL,QAAZ,MAAJA,IACDF,EAAI,EAAIJ,EAAIK,QACyB,QAAZ,MAAxBL,EAAIO,WAAWH,EAAI,CAAC,IAGrBE,EAAI,QAAgB,KAAJA,IAAe,KAA6B,KAAtBN,EAAIO,WAAW,EAAEH,CAAC,GACxDH,EAAIE,CAAC,IAAOG,GAAK,GAAM,IACvBL,EAAIE,CAAC,IAAQG,GAAK,GAAM,GAAM,KAI9BL,EAAIE,CAAC,IAAOG,GAAK,GAAM,IACvBL,EAAIE,CAAC,IAAQG,GAAK,EAAK,GAAM,KAC7BL,EAAIE,CAAC,IAAW,GAAJG,EAAU,IAEzB,CACD,OAAOL,CACT,EA6DaO,EAAiB,CAI5BC,eAAgB,KAKhBC,eAAgB,KAMhBC,sBAAuB,KAMvBC,sBAAuB,KAMvBC,kBACE,iEAKFC,mBACE,OAAOC,KAAKF,kBAAoB,KACjC,EAKDG,2BACE,OAAOD,KAAKF,kBAAoB,KACjC,EASDI,mBAAoC,YAAhB,OAAOC,KAW3BC,gBAAgBC,EAA8BC,GAC5C,GAAI,CAACC,MAAMC,QAAQH,CAAK,EACtB,MAAMI,MAAM,+CAA+C,EAG7DT,KAAKU,MAAK,EAEV,IAAMC,EAAgBL,EAClBN,KAAKJ,sBACLI,KAAKN,eAEHkB,EAAS,GAEf,IAAKzB,IAAIE,EAAI,EAAGA,EAAIgB,EAAMf,OAAQD,GAAK,EAAG,CACxC,IAAMwB,EAAQR,EAAMhB,GACdyB,EAAYzB,EAAI,EAAIgB,EAAMf,OAC1ByB,EAAQD,EAAYT,EAAMhB,EAAI,GAAK,EACnC2B,EAAY3B,EAAI,EAAIgB,EAAMf,OAC1B2B,EAAQD,EAAYX,EAAMhB,EAAI,GAAK,EAIzCF,IAAI+B,GAAqB,GAARH,IAAiB,EAAME,GAAS,EAC7CE,EAAmB,GAARF,EAEVD,IACHG,EAAW,GAENL,KACHI,EAAW,IAIfN,EAAOQ,KACLT,EAdeE,GAAS,GAexBF,GAdyB,EAARE,IAAiB,EAAME,GAAS,GAejDJ,EAAcO,GACdP,EAAcQ,EAAS,CAE1B,CAED,OAAOP,EAAOS,KAAK,EAAE,CACtB,EAUDC,aAAajB,EAAeC,GAG1B,OAAIN,KAAKE,oBAAsB,CAACI,EACvBiB,KAAKlB,CAAK,EAEZL,KAAKI,gBAAgBpB,EAAkBqB,CAAK,EAAGC,CAAO,CAC9D,EAUDkB,aAAanB,EAAeC,GAG1B,GAAIN,KAAKE,oBAAsB,CAACI,EAC9B,OAAOH,KAAKE,CAAK,EAEZoB,CAAAA,IA9LyBC,EA8LP1B,KAAK2B,wBAAwBtB,EAAOC,CAAO,EA5LtE,IAAMpB,EAAgB,GACtBC,IAAIyC,EAAM,EACRrC,EAAI,EACN,KAAOqC,EAAMF,EAAMpC,QAAQ,CACzB,IAWQuC,EAMAC,EACAC,EAlBFC,EAAKN,EAAME,CAAG,IAChBI,EAAK,IACP9C,EAAIK,CAAC,IAAM0C,OAAOC,aAAaF,CAAE,EACnB,IAALA,GAAYA,EAAK,KACpBF,EAAKJ,EAAME,CAAG,IACpB1C,EAAIK,CAAC,IAAM0C,OAAOC,cAAoB,GAALF,IAAY,EAAW,GAALF,CAAQ,GAC7C,IAALE,GAAYA,EAAK,KAKpBH,IACI,EAALG,IAAW,IAAa,GAJlBN,EAAME,CAAG,MAIgB,IAAa,GAHtCF,EAAME,CAAG,MAGoC,EAAW,GAFxDF,EAAME,CAAG,KAGlB,MACF1C,EAAIK,CAAC,IAAM0C,OAAOC,aAAa,OAAUL,GAAK,GAAG,EACjD3C,EAAIK,CAAC,IAAM0C,OAAOC,aAAa,OAAc,KAAJL,EAAS,IAE5CC,EAAKJ,EAAME,CAAG,IACdG,EAAKL,EAAME,CAAG,IACpB1C,EAAIK,CAAC,IAAM0C,OAAOC,cACT,GAALF,IAAY,IAAa,GAALF,IAAY,EAAW,GAALC,CAAQ,EAGrD,CACD,OAAO7C,EAAImC,KAAK,EAAE,EAgKTI,MAA8D,CACtE,EAiBDE,wBAAwBtB,EAAeC,GACrCN,KAAKU,MAAK,EAEV,IAAMyB,EAAgB7B,EAClBN,KAAKH,sBACLG,KAAKL,eAEHiB,EAAmB,GAEzB,IAAKzB,IAAIE,EAAI,EAAGA,EAAIgB,EAAMf,QAAU,CAClC,IAAMuB,EAAQsB,EAAc9B,EAAM+B,OAAO/C,CAAC,EAAE,GAGtC0B,EADY1B,EAAIgB,EAAMf,OACF6C,EAAc9B,EAAM+B,OAAO/C,CAAC,GAAK,EAIrD4B,EAHN,EAAE5B,EAEoBgB,EAAMf,OACF6C,EAAc9B,EAAM+B,OAAO/C,CAAC,GAAK,GAIrDgD,EAHN,EAAEhD,EAEoBgB,EAAMf,OACF6C,EAAc9B,EAAM+B,OAAO/C,CAAC,GAAK,GAG3D,GAFA,EAAEA,EAEW,MAATwB,GAA0B,MAATE,GAA0B,MAATE,GAA0B,MAAToB,EACrD,MAAM,IAAIC,EAIZ1B,EAAOQ,KADWP,GAAS,EAAME,GAAS,CACtB,EAEN,KAAVE,IAEFL,EAAOQ,KADYL,GAAS,EAAK,IAASE,GAAS,CAC/B,EAEN,KAAVoB,IAEFzB,EAAOQ,KADYH,GAAS,EAAK,IAAQoB,CACrB,CAGzB,CAED,OAAOzB,CACR,EAODF,QACE,GAAI,CAACV,KAAKN,eAAgB,CACxBM,KAAKN,eAAiB,GACtBM,KAAKL,eAAiB,GACtBK,KAAKJ,sBAAwB,GAC7BI,KAAKH,sBAAwB,GAG7B,IAAKV,IAAIE,EAAI,EAAGA,EAAIW,KAAKD,aAAaT,OAAQD,CAAC,GAC7CW,KAAKN,eAAeL,GAAKW,KAAKD,aAAaqC,OAAO/C,CAAC,EACnDW,KAAKL,eAAeK,KAAKN,eAAeL,IAAMA,EAC9CW,KAAKJ,sBAAsBP,GAAKW,KAAKC,qBAAqBmC,OAAO/C,CAAC,GAClEW,KAAKH,sBAAsBG,KAAKJ,sBAAsBP,IAAMA,IAGnDW,KAAKF,kBAAkBR,SAC9BU,KAAKL,eAAeK,KAAKC,qBAAqBmC,OAAO/C,CAAC,GAAKA,EAC3DW,KAAKH,sBAAsBG,KAAKD,aAAaqC,OAAO/C,CAAC,GAAKA,EAG/D,CACF,CACD,QAKWiD,UAAgC7B,MAA7C8B,kCACWvC,KAAIwC,KAAG,yBACjB,CAAA,CAKM,IAAMC,EAAe,SAAUxD,GACpC,IAAMyD,EAAY1D,EAAkBC,CAAG,EACvC,OAAOQ,EAAOW,gBAAgBsC,EAAW,CAAA,CAAI,CAC/C,EAMaC,EAAgC,SAAU1D,GAErD,OAAOwD,EAAaxD,CAAG,EAAE2D,QAAQ,MAAO,EAAE,CAC5C,QC7RaC,UAAsBpC,MAIjC8B,YAEWO,EACTC,EAEOC,GAEPC,MAAMF,CAAO,EALJ/C,KAAI8C,KAAJA,EAGF9C,KAAUgD,WAAVA,EAPAhD,KAAIwC,KAdI,gBA6BfU,OAAOC,eAAenD,KAAM6C,EAAcO,SAAS,EAI/C3C,MAAM4C,mBACR5C,MAAM4C,kBAAkBrD,KAAMsD,EAAaF,UAAUG,MAAM,CAE9D,CACF,OAEYD,EAIXf,YACmBiB,EACAC,EACAC,GAFA1D,KAAOwD,QAAPA,EACAxD,KAAWyD,YAAXA,EACAzD,KAAM0D,OAANA,CACf,CAEJH,OACET,KACGa,GAEH,IAcuCA,EAdjCX,EAAcW,EAAK,IAAoB,GACvCC,EAAc5D,KAAKwD,QAAR,IAAmBV,EAC9Be,EAAW7D,KAAK0D,OAAOZ,GAEvBC,EAAUc,GAUuBF,EAVcX,EAAVa,EAW7BjB,QAAQkB,EAAS,CAACC,EAAGC,KACnC,IAAMC,EAAQN,EAAKK,GACnB,OAAgB,MAATC,EAAgBhC,OAAOgC,CAAK,MAAQD,KAC7C,CAAC,GAdoE,QAE7DE,EAAiBlE,KAAKyD,iBAAgBV,MAAYa,MAIxD,OAFc,IAAIf,EAAce,EAAUM,EAAalB,CAAU,CAGlE,CACF,CASD,IAAMc,EAAU,gBClHV,SAAUK,EACdX,GAEA,OAAIA,GAAYA,EAA+BY,UACrCZ,EAA+BY,UAEhCZ,CAEX,OCDaa,EAiBX9B,YACWC,EACA8B,EACAC,GAFAvE,KAAIwC,KAAJA,EACAxC,KAAesE,gBAAfA,EACAtE,KAAIuE,KAAJA,EAnBXvE,KAAiBwE,kBAAG,CAAA,EAIpBxE,KAAYyE,aAAe,GAE3BzE,KAAA0E,kBAA2C,OAE3C1E,KAAiB2E,kBAAwC,IAYrD,CAEJC,qBAAqBC,GAEnB,OADA7E,KAAK0E,kBAAoBG,EAClB7E,IACR,CAED8E,qBAAqBN,GAEnB,OADAxE,KAAKwE,kBAAoBA,EAClBxE,IACR,CAED+E,gBAAgBC,GAEd,OADAhF,KAAKyE,aAAeO,EACbhF,IACR,CAEDiF,2BAA2BC,GAEzB,OADAlF,KAAK2E,kBAAoBO,EAClBlF,IACR,CACF,CC/CM,IAAMmF,EAAe,iCAKfC,EAA4B,sBCH5BC,UAAqBxC,EAahCN,YAAYO,EAAwBC,EAAyBuC,EAAU,GACrErC,MACEsC,EAAYzC,CAAI,uBACKC,MAAYwC,EAAYzC,CAAI,IAAI,EAHI9C,KAAOsF,QAAPA,EAR7DtF,KAAAgD,WAAgD,CAAEwC,eAAgB,IAAI,EAapExF,KAAKyF,aAAezF,KAAK+C,QAGzBG,OAAOC,eAAenD,KAAMqF,EAAajC,SAAS,CACnD,CAEDsC,aACE,OAAO1F,KAAKsF,OACb,CAEDI,WAAWA,GACT1F,KAAKsF,QAAUI,CAChB,CAKDC,YAAY7C,GACV,OAAOyC,EAAYzC,CAAI,IAAM9C,KAAK8C,IACnC,CAKD0C,qBACE,OAAOxF,KAAKgD,WAAWwC,cACxB,CAEDA,mBAAmBA,GACjBxF,KAAKgD,WAAWwC,eAAiBA,EAC7BxF,KAAKgD,WAAWwC,eAClBxF,KAAK+C,QAAa/C,KAAKyF;EAAiBzF,KAAKgD,WAAWwC,eAExDxF,KAAK+C,QAAU/C,KAAKyF,YAEvB,CACF,KAQWG,EChBAC,ECXoBC,EACxBC,EFwDF,SAAUR,EAAYzC,GAC1B,MAAO,WAAaA,CACtB,CAEgB,SAAAkD,IAId,OAAO,IAAIX,EAAaO,EAAiBK,QAFvC,gFAEuD,CAC3D,CAsDgB,SAAAC,IACd,OAAO,IAAIb,EACTO,EAAiBO,qBACjB,0DAA0D,CAE9D,CAmBgB,SAAAC,IACd,OAAO,IAAIf,EACTO,EAAiBS,SACjB,oCAAoC,CAExC,CAiCgB,SAAAC,IACd,OAAO,IAAIjB,EACTO,EAAiBW,kBACjB,wDAAwD,CAE5D,CA0BM,SAAUC,EAAgBzD,GAC9B,OAAO,IAAIsC,EAAaO,EAAiBa,iBAAkB1D,CAAO,CACpE,CA+BgB,SAAA2D,IACd,OAAO,IAAIrB,EACTO,EAAiBe,YACjB,+BAA+B,CAEnC,CAOM,SAAUC,EAAqBpE,GACnC,OAAO,IAAI6C,EACTO,EAAiBiB,uBACjB,kBACErE,EAEA,iHAAoD,CAE1D,CAMgB,SAAAsE,EAAcC,EAAgBhE,GAC5C,OAAO,IAAIsC,EACTO,EAAiBoB,eACjB,iCAAmCD,EAAS,MAAQhE,CAAO,CAE/D,CAYM,SAAUkE,EAAclE,GAC5B,MAAM,IAAIsC,EACRO,EAAiBsB,eACjB,mBAAqBnE,CAAO,CAEhC,EA3QY6C,EAAAA,EAAAA,GA4BX,IA1BC,QAAA,UACAA,EAAA,iBAAA,mBACAA,EAAA,iBAAA,mBACAA,EAAA,kBAAA,oBACAA,EAAA,eAAA,iBACAA,EAAA,gBAAA,kBACAA,EAAA,aAAA,eACAA,EAAA,iBAAA,mBACAA,EAAA,qBAAA,uBACAA,EAAA,iBAAA,mBACAA,EAAA,SAAA,WAEAA,EAAA,mBAAA,qBACAA,EAAA,YAAA,cACAA,EAAA,uBAAA,yBACAA,EAAA,kBAAA,oBACAA,EAAA,kBAAA,oBACAA,EAAA,uBAAA,yBACAA,EAAA,gBAAA,kBACAA,EAAA,iBAAA,mBACAA,EAAA,uBAAA,yBACAA,EAAA,YAAA,cACAA,EAAA,uBAAA,yBACAA,EAAA,eAAA,iBACAA,EAAA,eAAA,iBACAA,EAAA,wBAAA,gCGpFWuB,EAGX5E,YAA4B6E,EAAgBC,GAAhBrH,KAAMoH,OAANA,EAC1BpH,KAAKsH,MAAQD,CACd,CAEDA,WACE,OAAOrH,KAAKsH,KACb,CAEDC,aACE,OAA4B,IAArBvH,KAAKqH,KAAK/H,MAClB,CAEDkI,gBACE,IAAMC,EAASC,mBACf,MAAO,MAAQD,EAAOzH,KAAKoH,MAAM,EAAI,MAAQK,EAAOzH,KAAKqH,IAAI,CAC9D,CAEDM,sBAEE,MAAO,MADQD,mBACO1H,KAAKoH,MAAM,EAAI,IACtC,CAEDQ,0BAA0BC,EAAsBC,GAC9C3I,IAAI4I,EACJ,IACEA,EAAiBZ,EAASa,YAAYH,EAAcC,CAAI,CAKzD,CAJC,MAAOG,GAGP,OAAO,IAAId,EAASU,EAAc,EAAE,CACrC,CACD,GAA4B,KAAxBE,EAAeV,KACjB,OAAOU,EAEP,MH8J+BX,EG9JJS,EH+JxB,IAAIxC,EACTO,EAAiBsC,uBACjB,2BAA6Bd,EAAS,IAAI,CG/J3C,CAEDY,mBAAmBG,EAAaL,GAC9B3I,IAAIiJ,EAA4B,KAChC,IAAMC,EAAe,sBAOrB,IACMC,EAAU,IAAIC,OAAO,SAAWF,EADvB,YAC8C,GAAG,EAGhE,SAASG,EAAWC,GAClBA,EAAInB,MAAQoB,mBAAmBD,EAAIpB,IAAI,CACxC,CACD,IACMsB,EAAsBb,EAAKlF,QAAQ,OAAQ,KAAK,EAEhDgG,EAAwB,IAAIL,oBACnBI,sBAAoCN,qBACjD,GAAG,EAICQ,EACJf,IAAS3C,EACL,sDACA2C,EAQAgB,EAAS,CACb,CAAEC,MAAOT,EAASU,QA1BF,CAAE5B,OAAQ,EAAGC,KAAM,CAAC,EA0BE4B,WAjCxC,SAAkBR,GAC6B,MAAzCA,EAAIpB,KAAKjF,OAAOqG,EAAIpB,KAAK/H,OAAS,CAAC,IACrCmJ,EAAInB,MAAQmB,EAAInB,MAAM4B,MAAM,EAAG,CAAC,CAAC,EAEpC,CA6B6D,EAC5D,CACEH,MAAOH,EACPI,QAjB2B,CAAE5B,OAAQ,EAAGC,KAAM,CAAC,EAkB/C4B,WAAYT,CACb,EACD,CACEO,MAduB,IAAIR,oBAChBM,KAAoBR,aACjC,GAAG,EAaDW,QAXwB,CAAE5B,OAAQ,EAAGC,KAAM,CAAC,EAY5C4B,WAAYT,CACb,GAEH,IAAKrJ,IAAIE,EAAI,EAAGA,EAAIyJ,EAAOxJ,OAAQD,CAAC,GAAI,CACtC,IAAM8J,EAAQL,EAAOzJ,GACf+J,EAAWD,EAAMJ,MAAMM,KAAKlB,CAAG,EACrC,GAAIiB,EAAU,CACZ,IAAME,EAAcF,EAASD,EAAMH,QAAQ5B,QAC3CjI,IAAIoK,EAAYH,EAASD,EAAMH,QAAQ3B,MAClCkC,EAAAA,GACS,GAEdnB,EAAW,IAAIjB,EAASmC,EAAaC,CAAS,EAC9CJ,EAAMF,WAAWb,CAAQ,EACzB,KACD,CACF,CACD,GAAgB,MAAZA,EACF,MHmFqBD,EGnFJA,EHoFd,IAAI9C,EACTO,EAAiB4D,YACjB,gBAAkBrB,EAAM,IAAI,EGpF5B,OAAOC,CACR,CACF,OCrHYqB,GAGXlH,YAAYmH,GACV1J,KAAK2J,SAAWC,QAAQC,OAAUH,CAAK,CACxC,CAGDI,aACE,OAAO9J,KAAK2J,QACb,CAGDI,OAAOC,EAAAA,IACR,CCJK,SAAUC,EAAS7K,GACvB,MAAoB,UAAb,OAAOA,GAAkBA,aAAa6C,MAC/C,CAEM,SAAUiI,GAAa9K,GAC3B,OAAO+K,EAAmB,GAAM/K,aAAagL,IAC/C,CAEgB,SAAAD,IACd,MAAuB,aAAhB,OAAOC,IAChB,CAEM,SAAUC,EACdC,EACAC,EACAC,EACAvG,GAEA,GAAIA,EAAQsG,EACV,MAAM/D,wBACkB8D,gBAAuBC,eAAsB,EAGvE,GAAYC,EAARvG,EACF,MAAMuC,wBACkB8D,gBAAuBE,YAAmB,CAGtE,CCtCgB,SAAAC,EACdC,EACA5C,EACA6C,GAEAxL,IAAIyL,EACY,MAAZD,EACO,WAAW7C,EAFTA,EAIb,SAAU6C,OAAcC,OAAYF,CACtC,CAEM,SAAUG,GAAgBC,GAC9B,IAEW9G,EAED+G,EAJJtD,EAASC,mBACfvI,IAAI6L,EAAY,IAChB,IAAWhH,KAAO8G,EACZA,EAAOG,eAAejH,CAAG,IACrB+G,EAAWtD,EAAOzD,CAAG,EAAI,IAAMyD,EAAOqD,EAAO9G,EAAI,EACvDgH,EAAYA,EAAYD,EAAW,KAMvC,OADAC,EAAYA,EAAU9B,MAAM,EAAG,CAAC,CAAC,CAEnC,CCxBgB,SAAAgC,GACdxF,EACAyF,GAIA,IAAMC,EAA8B,KAAV1F,GAAiBA,EAAS,IAO9C2F,EAAuD,CAAC,IANtC,CAEtB,IAEA,KAEuCC,QAAQ5F,CAAM,EACjD6F,EAAiE,CAAC,IAA1CJ,EAAqBG,QAAQ5F,CAAM,EACjE,OAAO0F,GAAqBC,GAAoBE,CAClD,ENgCY1F,EAAAA,EAAAA,GAIX,IAHCA,EAAA,SAAA,GAAA,WACAA,EAAAA,EAAA,cAAA,GAAA,gBACAA,EAAAA,EAAA,MAAA,GAAA,cOvBI2F,GAUJjJ,YACUkJ,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAAQ,CAAA,GAXRpM,KAAIyL,KAAJA,EACAzL,KAAO0L,QAAPA,EACA1L,KAAQ2L,SAARA,EACA3L,KAAK4L,MAALA,EACA5L,KAAa6L,cAAbA,EACA7L,KAAqB8L,sBAArBA,EACA9L,KAAS+L,UAATA,EACA/L,KAAcgM,eAAdA,EACAhM,KAAQiM,SAARA,EACAjM,KAAiBkM,kBAAjBA,EACAlM,KAAkBmM,mBAAlBA,EACAnM,KAAKoM,MAALA,EArBFpM,KAAkBqM,mBAAyB,KAC3CrM,KAAUsM,WAAqB,KAI/BtM,KAASuM,UAAY,CAAA,EACrBvM,KAAUwM,WAAY,CAAA,EAiB5BxM,KAAK2J,SAAW,IAAIC,QAAQ,CAAC6C,EAAS5C,KACpC7J,KAAK0M,SAAWD,EAChBzM,KAAK2M,QAAU9C,EACf7J,KAAK4M,OAAM,CACb,CAAC,CACF,CAKOA,SACN,IAAMC,EAGM,CAACC,EAAiB1G,KAC5B,GAAIA,EACF0G,EAAgB,CAAA,EAAO,IAAIC,EAAiB,CAAA,EAAO,KAAM,CAAA,CAAI,CAAC,MADhE,CAIA,IAAMC,EAAahN,KAAKmM,qBAGlBc,GAFNjN,KAAKqM,mBAAqBW,EAIdE,IACV,IAAMC,EAASD,EAAcC,OACvBC,EAAQF,EAAcG,iBAAmBH,EAAcE,MAAQ,CAAC,EACvC,OAA3BpN,KAAKkM,mBACPlM,KAAKkM,kBAAkBiB,EAAQC,CAAK,CAExC,GAC+B,OAA3BpN,KAAKkM,mBACPc,EAAWM,0BAA0BL,CAAgB,EAKvDD,EACGO,KAAKvN,KAAKyL,KAAMzL,KAAK0L,QAAS1L,KAAK4L,MAAO5L,KAAK2L,QAAQ,EACvD6B,KAAK,KAC2B,OAA3BxN,KAAKkM,mBACPc,EAAWS,6BAA6BR,CAAgB,EAE1DjN,KAAKqM,mBAAqB,KAC1B,IAAMqB,EAAYV,EAAWW,aAAY,IAAO9H,EAAU+H,SACpDlI,EAASsH,EAAWa,YAExB,CAACH,GACAxC,GAAkBxF,EAAQ1F,KAAK8L,qBAAqB,GACnD9L,KAAKoM,OAED0B,EAAcd,EAAWW,aAAY,IAAO9H,EAAUkI,MAC5DjB,EACE,CAAA,EACA,IAAIC,EAAiB,CAAA,EAAO,KAAMe,CAAW,CAAC,IAI5CE,EAAqD,CAAC,IAAxChO,KAAK6L,cAAcP,QAAQ5F,CAAM,EACrDoH,EAAgB,CAAA,EAAM,IAAIC,EAAiBiB,EAAahB,CAAU,CAAC,EACrE,CAAC,CA1CF,CA2CH,EAMMiB,EAGM,CAACC,EAAoBxI,KAC/B,IAAM+G,EAAUzM,KAAK0M,SACf7C,EAAS7J,KAAK2M,QACdK,EAAatH,EAAOsH,WAC1B,GAAItH,EAAOyI,eACT,IACE,IAAMC,EAASpO,KAAK+L,UAAUiB,EAAYA,EAAWqB,YAAW,CAAE,EHnI7D,KAAA,IGoISD,EACZ3B,EAAQ2B,CAAM,EAEd3B,GAIH,CAFC,MAAOxE,GACP4B,EAAO5B,CAAC,CACT,MAEkB,OAAf+E,IACIsB,EAAMtI,KACRR,eAAiBwH,EAAWuB,eAC5BvO,KAAKgM,eACPnC,EAAO7J,KAAKgM,eAAegB,EAAYsB,CAAG,CAAC,EAE3CzE,EAAOyE,CAAG,GAGR5I,EAAOU,SAETyD,GADY7J,KAAKwM,WAAa9F,EAAeN,GAAH,CAChC,EAGVyD,EADY3D,GACF,CAIlB,EACIlG,KAAKuM,UACP0B,EAAY,EAAO,IAAIlB,EAAiB,CAAA,EAAO,KAAM,CAAA,CAAI,CAAC,EAE1D/M,KAAKsM,YClJL,CACJkC,EAKAC,EACAC,KAIAvP,IAAIwP,EAAc,EAIdC,EAAsB,KAEtBC,EAAuB,KACvBC,EAAa,CAAA,EACbC,EAAc,EAElB,SAAS3I,IACP,OAAuB,IAAhB2I,CACR,CACD5P,IAAI6P,EAAoB,CAAA,EAExB,SAASC,KAAmBC,GACrBF,IACHA,EAAoB,CAAA,EACpBP,EAAkBU,MAAM,KAAMD,CAAI,EAErC,CAED,SAASE,EAAcC,GACrBT,EAAiBU,WAAW,KAC1BV,EAAiB,KACjBJ,EAAUe,EAAiBnJ,EAAQ,CAAE,CACtC,EAAEiJ,CAAM,CACV,CAED,SAASG,IACHX,GACFY,aAAaZ,CAAe,CAE/B,CAED,SAASU,EAAgBG,KAAqBR,GAC5C,GAAIF,EACFQ,SAGF,GAAIE,EACFF,IACAP,EAAgBU,KAAK,KAAMD,EAAS,GAAGR,CAAI,OAI7C,GADiB9I,EAAU,GAAI0I,EAE7BU,IACAP,EAAgBU,KAAK,KAAMD,EAAS,GAAGR,CAAI,MAF7C,CAKIP,EAAc,KAEhBA,GAAe,GAEjBxP,IAAIyQ,EAOJR,EAJEQ,EAFkB,IAAhBb,GACFA,EAAc,EACD,GAEgC,KAA/BJ,EAAckB,KAAKC,OAAM,EAEjB,CAZvB,CAaF,CACD3Q,IAAI4Q,EAAU,CAAA,EAEd,SAASC,EAAKC,GACRF,IAGJA,EAAU,CAAA,EACVP,IACIR,KAGmB,OAAnBJ,GACGqB,IACHlB,EAAc,GAEhBU,aAAab,CAAc,EAC3BQ,EAAc,CAAC,GAEVa,IACHlB,EAAc,GAGnB,CAMD,OALAK,EAAc,CAAC,EACfP,EAAkBS,WAAW,KAE3BU,EADAlB,EAAa,CAAA,CACJ,CACV,EAAEJ,CAAO,EACHsB,CACT,GD0C8BnD,EAAcoB,EAAajO,KAAKiM,QAAQ,CAEnE,CAGDnC,aACE,OAAO9J,KAAK2J,QACb,CAGDI,OAAOmG,GACLlQ,KAAKuM,UAAY,CAAA,EACjBvM,KAAKwM,WAAa0D,GAAa,CAAA,EACP,OAApBlQ,KAAKsM,aC7CX6D,ED8CSnQ,KAAKsM,YC9CX,CAAA,CAAK,EDgD0B,OAA5BtM,KAAKqM,oBACPrM,KAAKqM,mBAAmB+D,OAE3B,CACF,OAMYrD,EAMXxK,YACS4L,EACAnB,EACP5G,GAFOpG,KAAcmO,eAAdA,EACAnO,KAAUgN,WAAVA,EAGPhN,KAAKoG,SAAW,CAAC,CAACA,CACnB,CACF,CEtLe,SAAAiK,MAAWnB,GACzB,IAAMoB,EAhBqB,aAAvB,OAAOA,YACFA,YAC+B,aAA7B,OAAOC,kBACTA,kBADF,KAAA,EAeP,GAAoBC,KAAAA,IAAhBF,EAA2B,CAC7B,IAAMG,EAAK,IAAIH,EACf,IAAKnR,IAAIE,EAAI,EAAGA,EAAI6P,EAAK5P,OAAQD,CAAC,GAChCoR,EAAGC,OAAOxB,EAAK7P,EAAE,EAEnB,OAAOoR,EAAGJ,SACX,CACC,GAAIlG,EAAmB,EACrB,OAAO,IAAIC,KAAK8E,CAAI,EAEpB,MAAM,IAAI7J,EACRO,EAAiB+K,wBACjB,qDAAqD,CAI7D,CCtCM,SAAUC,GAAaC,GAC3B,GAAoB,aAAhB,OAAO1Q,KACT,MXkPK,IAAIkF,EACTO,EAAiB+K,wBACjB,+JAAmK,EWlPrK,OAAOxQ,KAAK0Q,CAAO,CACrB,CCIa,IAAAC,EAAe,CAQ1BC,IAAK,MAOLC,OAAQ,SAORC,UAAW,YAUXC,SAAU,UACD,QAEEC,EAGX5O,YAAmBoB,EAAkByN,GAAlBpR,KAAI2D,KAAJA,EACjB3D,KAAKoR,YAAcA,GAAe,IACnC,CACF,CAKe,SAAAC,GACdtK,EACAuK,GAEA,OAAQvK,GACN,KAAK+J,EAAaC,IAChB,OAAO,IAAII,EAAWI,GAAWD,CAAU,CAAC,EAC9C,KAAKR,EAAaE,OAClB,KAAKF,EAAaG,UAChB,OAAO,IAAIE,EAAWK,GAAazK,EAAQuK,CAAU,CAAC,EACxD,KAAKR,EAAaI,SAChB,OAAO,IAAIC,GAwIaM,EAvIRH,GAwIdI,EAAQ,IAAIC,GAAaF,CAAO,GAC5BhS,OACD+R,GAAaV,EAAaE,OAAQU,EAAME,IAAI,GArFlB3N,IACnC9E,IAAI0S,EACJ,IACEA,EAAUnJ,mBAAmBzE,CAAK,CAGnC,CAFC,MAAOgE,GACP,MAAMnB,EAAcgK,EAAaI,SAAU,qBAAqB,CACjE,CACD,OAAOK,GAAWM,CAAO,CAC3B,GA+EgCH,EAAME,IAAI,IAINH,EA/IRH,EAgJZ,IAAIK,GAAaF,CAAO,EACzBL,YAjJwB,CAIpC,CA2IG,IATwBK,EACtBC,EAhIN,MAAM1L,EAAO,CACf,CAEM,SAAUuL,GAAWtN,GACzB,IAiBgB6N,EACAC,EAlBVC,EAAc,GACpB,IAAK7S,IAAIE,EAAI,EAAGA,EAAI4E,EAAM3E,OAAQD,CAAC,GAAI,CACrCF,IAAII,EAAI0E,EAAMzE,WAAWH,CAAC,EACtBE,GAAK,IACPyS,EAAE5Q,KAAK7B,CAAC,EAEJA,GAAK,KACPyS,EAAE5Q,KAAK,IAAO7B,GAAK,EAAI,IAAW,GAAJA,CAAO,EAEjB,QAAX,MAAJA,GAGDF,EAAI4E,EAAM3E,OAAS,GAA2C,QAAX,MAA1B2E,EAAMzE,WAAWH,EAAI,CAAC,IAKzCyS,EAAKvS,EACLwS,EAAK9N,EAAMzE,WAAW,EAAEH,CAAC,EAC/BE,EAAI,OAAe,KAALuS,IAAc,GAAY,KAALC,EACnCC,EAAE5Q,KACA,IAAO7B,GAAK,GACZ,IAAQA,GAAK,GAAM,GACnB,IAAQA,GAAK,EAAK,GAClB,IAAW,GAAJA,CAAO,GAThByS,EAAE5Q,KAAK,IAAK,IAAK,GAAG,EAaF,QAAX,MAAJ7B,GAEHyS,EAAE5Q,KAAK,IAAK,IAAK,GAAG,EAEpB4Q,EAAE5Q,KAAK,IAAO7B,GAAK,GAAK,IAAQA,GAAK,EAAK,GAAK,IAAW,GAAJA,CAAO,CAKtE,CACD,OAAO,IAAI0S,WAAWD,CAAC,CACzB,CAYgB,SAAAR,GAAazK,EAAsB9C,GACjD,OAAQ8C,GACN,KAAK+J,EAAaE,OAChB,IAAMkB,EAAkC,CAAC,IAAxBjO,EAAMqH,QAAQ,GAAG,EAC5B6G,EAAkC,CAAC,IAAxBlO,EAAMqH,QAAQ,GAAG,EAClC,GAAI4G,GAAYC,EAEd,MAAMrL,EACJC,EACA,uBAHkBmL,EAAW,IAAM,KAKjC,mCAAmC,EAGzC,MAEF,KAAKpB,EAAaG,UACVmB,EAAiC,CAAC,IAAxBnO,EAAMqH,QAAQ,GAAG,EAC3B+G,EAAkC,CAAC,IAAxBpO,EAAMqH,QAAQ,GAAG,EAClC,GAAI8G,GAAWC,EAEb,MAAMvL,EACJC,EACA,uBAHkBqL,EAAU,IAAM,KAGI,gCAAgC,EAG1EnO,EAAQA,EAAMrB,QAAQ,KAAM,GAAG,EAAEA,QAAQ,KAAM,GAAG,CAKrD,CACDzD,IAAIuC,EACJ,IACEA,EAAQkP,GAAa3M,CAAK,CAM3B,CALC,MAAOgE,GACP,GAAKA,EAAYlF,QAAQuP,SAAS,UAAU,EAC1C,MAAMrK,EAER,MAAMnB,EAAcC,EAAQ,yBAAyB,CACtD,CACD,IAAMwL,EAAQ,IAAIN,WAAWvQ,EAAMpC,MAAM,EACzC,IAAKH,IAAIE,EAAI,EAAGA,EAAIqC,EAAMpC,OAAQD,CAAC,GACjCkT,EAAMlT,GAAKqC,EAAMlC,WAAWH,CAAC,EAE/B,OAAOkT,CACT,OAEMZ,GAKJpP,YAAYiQ,GAJZxS,KAAMP,OAAY,CAAA,EAClBO,KAAWoR,YAAkB,KAI3B,IAAMqB,EAAUD,EAAQE,MAAM,iBAAiB,EAC/C,GAAgB,OAAZD,EACF,MAAM3L,EACJgK,EAAaI,SACb,uDAAuD,EAG3D,IAyBcyB,EAAWC,EAzBnBC,EAASJ,EAAQ,IAAM,KACf,MAAVI,IACF7S,KAAKP,QAuBkBmT,EAvBQ,WAuBnBD,EAvBWE,GAwBNvT,QAAUsT,EAAItT,QAK5BqT,EAAEG,UAAUH,EAAErT,OAASsT,EAAItT,MAAM,IAAMsT,GA5B1C5S,KAAKoR,YAAcpR,KAAKP,OACpBoT,EAAOC,UAAU,EAAGD,EAAOvT,OAAS,UAAUA,MAAM,EACpDuT,GAEN7S,KAAK4R,KAAOY,EAAQM,UAAUN,EAAQlH,QAAQ,GAAG,EAAI,CAAC,CACvD,CACF,OC3LYyH,EAKXxQ,YAAYoB,EAAuCqP,GACjD7T,IAAI8T,EAAe,EACfC,EAAmB,GACnBhJ,GAAavG,CAAI,GACnB3D,KAAKmT,MAAQxP,EACbsP,EAAQtP,EAAcsP,KACtBC,EAAYvP,EAAcY,MACjBZ,aAAgByP,aACrBJ,EACFhT,KAAKmT,MAAQ,IAAIlB,WAAWtO,CAAI,GAEhC3D,KAAKmT,MAAQ,IAAIlB,WAAWtO,EAAK0P,UAAU,EAC3CrT,KAAKmT,MAAMG,IAAI,IAAIrB,WAAWtO,CAAI,CAAC,GAErCsP,EAAOjT,KAAKmT,MAAM7T,QACTqE,aAAgBsO,aACrBe,EACFhT,KAAKmT,MAAQxP,GAEb3D,KAAKmT,MAAQ,IAAIlB,WAAWtO,EAAKrE,MAAM,EACvCU,KAAKmT,MAAMG,IAAI3P,CAAkB,GAEnCsP,EAAOtP,EAAKrE,QAEdU,KAAKuT,MAAQN,EACbjT,KAAKwT,MAAQN,CACd,CAEDD,OACE,OAAOjT,KAAKuT,KACb,CAEDhP,OACE,OAAOvE,KAAKwT,KACb,CAEDtK,MAAMuK,EAAmBC,GACvB,IAQQxK,EHdcyK,EAAYC,EAAehB,EGMjD,OAAI1I,GAAalK,KAAKmT,KAAK,GACnBU,EAAW7T,KAAKmT,MHPUS,EGQGH,EHRYb,EGQDc,EAC/B,QADTI,GHRcH,EGQKE,GHPpBE,YACAJ,EAAKI,YAAYH,EAAOhB,CAAG,EACzBe,EAAKK,SACPL,EAAKK,SAASJ,EAAOhB,CAAG,EACtBe,EAAKzK,MACPyK,EAAKzK,MAAM0K,EAAOhB,CAAG,EAEvB,MGEM,KAEF,IAAIG,EAAQe,CAAM,IAEnB5K,EAAQ,IAAI+I,WACfjS,KAAKmT,MAAqBc,OAC3BR,EACAC,EAAUD,CAAS,EAEd,IAAIV,EAAQ7J,EAAO,CAAA,CAAI,EAEjC,CAEDmH,kBAAkBnB,GAChB,GAAI/E,EAAmB,EAUrB,OATM+J,EAA4ChF,EAAKiF,IACrD,GACMC,aAAerB,EACVqB,EAAIjB,MAEJiB,CAEV,EAEI,IAAIrB,EAAQ1C,GAAQlB,MAAM,KAAM+E,CAAM,CAAC,EACzC,CACL,IAAMG,EAA4BnF,EAAKiF,IACrC,GACMlK,EAASmK,CAAG,EACP/C,GAAeP,EAAaC,IAAKqD,CAAa,EAAEzQ,KAG/CyQ,EAAgBjB,KAE3B,EAEHhU,IAAImV,EAAc,EAIZC,GAHNF,EAAYG,QAAQ,IAClBF,GAAe/B,EAAMc,UACvB,CAAC,EACc,IAAIpB,WAAWqC,CAAW,GACrCG,EAAQ,EAMZ,OALAJ,EAAYG,QAAQ,IAClB,IAAKrV,IAAIE,EAAI,EAAGA,EAAIkT,EAAMjT,OAAQD,CAAC,GACjCkV,EAAOE,CAAK,IAAMlC,EAAMlT,EAE5B,CAAC,EACM,IAAI0T,EAAQwB,EAAQ,CAAA,CAAI,CAChC,CACF,CAEDG,aACE,OAAO1U,KAAKmT,KACb,CACF,CC/GK,SAAUwB,EACdhC,GAEAxT,IAAIyV,EACJ,IACEA,EAAMC,KAAKC,MAAMnC,CAAC,CAGnB,CAFC,MAAO1K,GACP,OAAO,IACR,CACD,MTFoB,UAAb,OADwB7I,ESGVwV,ITFYrU,MAAMC,QAAQpB,CAAC,ESKvC,KAFAwV,CAIX,CCkBM,SAAUG,GAAc1N,GAC5B,IAAMoN,EAAQpN,EAAK2N,YAAY,IAAK3N,EAAK/H,OAAS,CAAC,EACnD,MAAc,CAAC,IAAXmV,EACKpN,EAEAA,EAAK6B,MAAMuL,EAAQ,CAAC,CAE/B,CC/BgB,SAAAQ,GAAYC,EAAoBjR,GAC9C,OAAOA,CACT,OAEMkR,EAKJ5S,YACS6S,EACPC,EACAC,EACAC,GAHOvV,KAAMoV,OAANA,EAKPpV,KAAKqV,MAAQA,GAASD,EACtBpV,KAAKsV,SAAW,CAAC,CAACA,EAClBtV,KAAKuV,MAAQA,GAASN,EACvB,CACF,CAKD9V,IAAIqW,EAA6B,KAUjB,SAAAC,IACd,IAGMC,EA6BAC,EAaN,OA7CIH,KAGEE,EAAqB,IAClBtU,KAAK,IAAI+T,EAAgB,QAAQ,CAAC,EAC3CO,EAAStU,KAAK,IAAI+T,EAAgB,YAAY,CAAC,EAC/CO,EAAStU,KAAK,IAAI+T,EAAgB,gBAAgB,CAAC,EACnDO,EAAStU,KAAK,IAAI+T,EAAgB,OAAQ,WAAY,CAAA,CAAI,CAAC,GAQrDS,EAAc,IAAIT,EAAgB,MAAM,GAClCI,MAPZ,SACEM,EACAC,GAEA,MArBE,CAAC7L,EADmB6L,EAsBLA,CArBG,GAAKA,EAASxW,OAAS,EACpCwW,EAEAf,GAAce,CAAQ,CAmB9B,EAGDJ,EAAStU,KAAKwU,CAAW,GAenBD,EAAc,IAAIR,EAAgB,MAAM,GAClCI,MAXZ,SACEM,EACA5C,GAEA,OAAazC,KAAAA,IAATyC,EACK8C,OAAO9C,CAAI,EAEXA,CAEV,EAGDyC,EAAStU,KAAKuU,CAAW,EACzBD,EAAStU,KAAK,IAAI+T,EAAgB,aAAa,CAAC,EAChDO,EAAStU,KAAK,IAAI+T,EAAgB,SAAS,CAAC,EAC5CO,EAAStU,KAAK,IAAI+T,EAAgB,UAAW,KAAM,CAAA,CAAI,CAAC,EACxDO,EAAStU,KAAK,IAAI+T,EAAgB,eAAgB,KAAM,CAAA,CAAI,CAAC,EAC7DO,EAAStU,KAAK,IAAI+T,EAAgB,qBAAsB,KAAM,CAAA,CAAI,CAAC,EACnEO,EAAStU,KAAK,IAAI+T,EAAgB,kBAAmB,KAAM,CAAA,CAAI,CAAC,EAChEO,EAAStU,KAAK,IAAI+T,EAAgB,kBAAmB,KAAM,CAAA,CAAI,CAAC,EAChEO,EAAStU,KAAK,IAAI+T,EAAgB,cAAe,KAAM,CAAA,CAAI,CAAC,EAC5DO,EAAStU,KAAK,IAAI+T,EAAgB,WAAY,iBAAkB,CAAA,CAAI,CAAC,EACrEK,EAAYE,GACLF,CACT,CAEgB,SAAAQ,GAAOd,EAAoB1R,GAOzCN,OAAO+S,eAAef,EAAU,MAAO,CAAEgB,IANzC,WACE,IAAM9O,EAAiB8N,EAAiB,OAClC7N,EAAe6N,EAAmB,SAClCzM,EAAM,IAAItB,EAASC,EAAQC,CAAI,EACrC,OAAO7D,EAAQ2S,sBAAsB1N,CAAG,CACzC,CACwD,CAAE,CAC7D,CAqBgB,SAAA2N,GACd5S,EACA6S,EACAX,GAEA,IAAMd,EAAMD,EAAiB0B,CAAc,EAC3C,GAAY,OAARzB,EACF,OAAO,KAET,IA3BApR,EA4BoBA,EA3BpB8S,EA0BiB1B,EAzBjBc,EA0BuCA,EAxBjCR,EAAqB,CAC3B3Q,KAAmB,MADQ,EAErBgS,EAAMb,EAASpW,OACrB,IAAKH,IAAIE,EAAI,EAAGA,EAAIkX,EAAKlX,CAAC,GAAI,CAC5B,IAAMmX,EAAUd,EAASrW,GACzB6V,EAASsB,EAAQnB,OAAUmB,EAA6BjB,MACtDL,EACAoB,EAASE,EAAQpB,OAAO,CAE3B,CAED,OADAY,GAAOd,EAAU1R,CAAO,EACjB0R,CAcT,CAqCgB,SAAAuB,GACdvB,EACAQ,GAEA,IAAMY,EAEF,GACEC,EAAMb,EAASpW,OACrB,IAAKH,IAAIE,EAAI,EAAGA,EAAIkX,EAAKlX,CAAC,GAAI,CAC5B,IAAMmX,EAAUd,EAASrW,GACrBmX,EAAQlB,WACVgB,EAASE,EAAQpB,QAAUF,EAASsB,EAAQnB,OAE/C,CACD,OAAOR,KAAK6B,UAAUJ,CAAQ,CAChC,CCjKA,IAAMK,GAAe,WACfC,GAAY,QAiCF,SAAAC,GACdrT,EACA4D,EACAiP,GAEA,IAAMzB,EAAMD,EAAiB0B,CAAc,EAC3C,GAAY,OAARzB,EACF,OAAO,KAET,IAvCApR,EAwC2BA,EAvC3B4D,EAuCoCA,EAtCpCkP,EAqCiB1B,EAnCXkC,EAAyB,CAC7BC,SAAU,GACVC,MAAO,GACPC,cAAeX,EAAwB,eAEzC,GAAIA,EAASK,IACX,IAAK,IAAMtP,KAAQiP,EAASK,IAAe,CACnCO,EAA2B7P,EAAKzE,QAAQ,MAAO,EAAE,EACjDuU,EAAY3T,EAAQ2S,sBACxB,IAAIhP,EAASC,EAAQ8P,CAAwB,CAAC,EAEhDJ,EAAWC,SAAS3V,KAAK+V,CAAS,CACnC,CAGH,GAAIb,EAASM,IACX,IAAK,IAAMQ,KAAQd,EAASM,IAAY,CAChCO,EAAY3T,EAAQ2S,sBACxB,IAAIhP,EAASC,EAAQgQ,EAAW,IAAC,CAAC,EAEpCN,EAAWE,MAAM5V,KAAK+V,CAAS,CAChC,CAEH,OAAOL,CAcT,OCvCaO,EAcX9U,YACS4F,EACAmP,EAQAC,EACA7I,GAVA1O,KAAGmI,IAAHA,EACAnI,KAAMsX,OAANA,EAQAtX,KAAOuX,QAAPA,EACAvX,KAAO0O,QAAPA,EAxBT1O,KAASwX,UAAc,GACvBxX,KAAOyX,QAAY,GACnBzX,KAAI0X,KAAsC,KAC1C1X,KAAY2X,aAAwB,KAMpC3X,KAAgB4X,iBAA8C,KAC9D5X,KAAA6X,aAAyB,CAAC,KAC1B7X,KAAoBmL,qBAAa,EAc7B,CACL,CCzBK,SAAU2M,EAAaC,GAC3B,GAAI,CAACA,EACH,MAAM/R,EAAO,CAEjB,CAEgB,SAAAgS,EACdxU,EACAkS,GAOA,OALA,SAAiBuC,EAAyBC,GACxC,IAAMhD,EAAWkB,GAAmB5S,EAAS0U,EAAMxC,CAAQ,EAE3D,OADAoC,EAA0B,OAAb5C,CAAiB,EACvBA,CACR,CAEH,CAEgB,SAAAiD,GACd3U,EACA4D,GAOA,OALA,SAAiB6Q,EAAyBC,GACxC,IAAMpB,EAAaD,GAAmBrT,EAAS4D,EAAQ8Q,CAAI,EAE3D,OADAJ,EAA4B,OAAfhB,CAAmB,EACzBA,CACR,CAEH,CAEgB,SAAAsB,GACd5U,EACAkS,GAYA,OAVA,SAAiBuC,EAAyBC,GACxC,IAAMhD,EAAWkB,GAAmB5S,EAAS0U,EAAMxC,CAAQ,EAC3DoC,EAA0B,OAAb5C,CAAiB,EACvBmD,CAAAA,IHmETnD,EGlEIA,EHoEJpN,EGlEItE,EAAQsE,KHmEZ6C,EGlEInH,EAAQ8U,UHqEZ,GAAY,QADN1D,EAAMD,EAAiB0B,CAAc,GAEzC,OAAO,KAET,GAAI,CAACpM,EAAS2K,EAAoB,cAAC,EAGjC,OAAO,KAGT,GAAsB,KADhB2D,EAAiB3D,EAAoB,gBAChCtV,OACT,OAAO,KAET,IAAMmI,EAASC,mBAaf,OAZmB6Q,EAAOC,MAAM,GAAG,EACXrE,IAAI,IAC1B,IAAM/M,EAAiB8N,EAAiB,OAClC7N,EAAe6N,EAAmB,SAOxC,OALazK,EADG,MAAQhD,EAAOL,CAAM,EAAI,MAAQK,EAAOJ,CAAI,EAC9BS,EAAM6C,CAAQ,EACxBE,GAAgB,CAClC4N,IAAK,QACLC,MAAAA,CACD,CAAA,CAEH,CAAC,EACW,EG9FS,CAEpB,CAEH,CAEM,SAAUC,EACdvQ,GAgCA,OA9BA,SACE6P,EACA3J,GAEAnP,IAAIyZ,EnBmEF,IAxBwBxR,EmBnB1B,OAjBIwR,EANoB,MAApBX,EAAIpK,UAAW,EAIfoK,EAAI1J,aAAY,EAAG+D,SAAS,qCAAqC,EnBwDhE,IAAIjN,EACTO,EAAiBiT,iBACjB,+EAA+E,EAN1E,IAAIxT,EAAaO,EAAiBkT,gBAFvC,6FAE+D,EmB7CrC,MAApBb,EAAIpK,UAAW,GnB+BKzG,EmB9BCgB,EAAShB,OnB+B/B,IAAI/B,EACTO,EAAiBmT,eACjB,qBACE3R,EAEA,wEAAuC,GmBlCb,MAApB6Q,EAAIpK,UAAW,GnBoDExG,EmBnDGe,EAASf,KnBoDhC,IAAIhC,EACTO,EAAiBoT,aACjB,4CAA8C3R,EAAO,IAAI,GmBpD1CiH,GAIR5I,OAASuS,EAAIpK,YACpB+K,EAAOpT,eAAiB8I,EAAI9I,eACrBoT,CACR,CAEH,CAEM,SAAUK,EACd7Q,GAEA,IAAM8Q,EAASP,EAAmBvQ,CAAQ,EAa1C,OAXA,SACE6P,EACA3J,GAEAnP,IAAIyZ,EAASM,EAAOjB,EAAK3J,CAAG,EAK5B,OAHEsK,EADsB,MAApBX,EAAIpK,UAAW,GnBjBQxG,EmBkBDe,EAASf,KnBjB9B,IAAIhC,EACTO,EAAiBuT,iBACjB,WAAa9R,EAAO,mBAAmB,GmBiBvCuR,GAAOpT,eAAiB8I,EAAI9I,eACrBoT,CACR,CAEH,CAEgBQ,SAAAA,GACd5V,EACA4E,EACAsN,GAEA,IACMvN,EAAMsC,EADIrC,EAASZ,gBACIhE,EAAQsE,KAAMtE,EAAQ8U,SAAS,EAEtD5J,EAAUlL,EAAQ6V,sBAClBC,EAAc,IAAIjC,EACtBlP,EAHa,MAKb6P,EAAgBxU,EAASkS,CAAQ,EACjChH,CAAO,EAGT,OADA4K,EAAY3B,aAAesB,EAAmB7Q,CAAQ,EAC/CkR,CACT,CAoIgB,SAAAC,GACdnR,EACAuL,EACAuB,GAEA,IAAMsE,EAAgBtW,OAAOuW,OAAO,GAAIvE,CAAQ,EAMhD,OALAsE,EAAwB,SAAIpR,EAASf,KACrCmS,EAAoB,KAAI7F,EAAKV,KAAI,EAC5BuG,EAA2B,cAC9BA,EAA2B,aAlB7B7F,EAkB6DA,GAnB7DuB,EAmBuD,OAfxCA,EAAsB,aAClCvB,GAAQA,EAAKpP,QACd,6BAeKiV,CACT,CAKM,SAAUE,GACdlW,EACA4E,EACAsN,EACA/B,EACAuB,GAEA,IAAMxK,EAAUtC,EAAST,sBACnB8P,EAAsC,CAC1CkC,yBAA0B,aAU5B,IAAMC,GAPN,KACEza,IAAIF,EAAM,GACV,IAAKE,IAAIE,EAAI,EAAGA,EAAI,EAAGA,CAAC,GACtBJ,GAAY4Q,KAAKC,OAAM,EAAG+J,WAAW3Q,MAAM,CAAC,EAE9C,OAAOjK,CACR,KAGK6a,GADNrC,EAAQ,gBAAkB,+BAAiCmC,EACzCL,GAAmBnR,EAAUuL,EAAMuB,CAAQ,GAEvD6E,EACJ,KACAH,EAEA,4DALqBnD,GAAiBqD,EAAWpE,CAAQ,EAOzD,SACAkE,EAEA,qBACAE,EAAuB,YACvB,WACIE,EAAe,SAAWJ,EAAW,KACrClC,EAAO3E,EAAQ1C,QAAQ0J,EAAapG,EAAMqG,CAAY,EAC5D,GAAa,OAATtC,EACF,MAAMpR,EAAe,EAEjBkR,EAAuB,CAAEhV,KAAMsX,EAAoB,QAAE,EACrD3R,EAAMsC,EAAQC,EAASlH,EAAQsE,KAAMtE,EAAQ8U,SAAS,EAEtD5J,EAAUlL,EAAQyW,mBAClBX,EAAc,IAAIjC,EACtBlP,EAHa,OAKb6P,EAAgBxU,EAASkS,CAAQ,EACjChH,CAAO,EAMT,OAJA4K,EAAY9B,UAAYA,EACxB8B,EAAY7B,QAAUA,EACtB6B,EAAY5B,KAAOA,EAAKhD,aACxB4E,EAAY3B,aAAegB,EAAmBvQ,CAAQ,EAC/CkR,CACT,OASaY,EAIX3X,YACS4X,EACA/M,EACPgN,EACAlF,GAHOlV,KAAOma,QAAPA,EACAna,KAAKoN,MAALA,EAIPpN,KAAKoa,UAAY,CAAC,CAACA,EACnBpa,KAAKkV,SAAWA,GAAY,IAC7B,CACF,CAEe,SAAAmF,GACdpC,EACAqC,GAEAnb,IAAIuG,EAAwB,KAC5B,IACEA,EAASuS,EAAIsC,kBAAkB,sBAAsB,CAGtD,CAFC,MAAOtS,GACP6P,EAAa,CAAA,CAAK,CACnB,CAGD,OADAA,EAAa,CAAC,CAACpS,GAA4C,CAAC,KADtC4U,GAAW,CAAC,WACKhP,QAAQ5F,CAAM,CAAQ,EACtDA,CACT,CAEM,SAAU8U,GACdhX,EACA4E,EACAsN,EACA/B,EACAuB,GAEA,IAAMxK,EAAUtC,EAAST,sBACnB8S,EAAoBlB,GAAmBnR,EAAUuL,EAAMuB,CAAQ,EAC/DsC,EAAuB,CAAEhV,KAAMiY,EAA4B,QAAE,EAC7DtS,EAAMsC,EAAQC,EAASlH,EAAQsE,KAAMtE,EAAQ8U,SAAS,EAEtDb,EAAU,CACdkC,yBAA0B,YAC1Be,wBAAyB,QACzBC,sCAAuC,GAAGhH,EAAKV,KAAM,EACrD2H,oCAAqCH,EAA+B,YACpEI,eAAgB,mCAEZnD,EAAOjB,GAAiBgE,EAAmB/E,CAAQ,EACnDhH,EAAUlL,EAAQyW,mBAalBX,EAAc,IAAIjC,EAAYlP,EAtBrB,OAWf,SAAiB8P,GACfoC,GAAmBpC,CAAG,EACtB9Y,IAAIgJ,EACJ,IACEA,EAAM8P,EAAIsC,kBAAkB,mBAAmB,CAGhD,CAFC,MAAOtS,GACP6P,EAAa,CAAA,CAAK,CACnB,CAED,OADAA,EAAa7N,EAAS9B,CAAG,CAAC,EACnBA,CACR,EACyDuG,CAAO,EAKjE,OAJA4K,EAAY9B,UAAYA,EACxB8B,EAAY7B,QAAUA,EACtB6B,EAAY5B,KAAOA,EACnB4B,EAAY3B,aAAegB,EAAmBvQ,CAAQ,EAC/CkR,CACT,CAKM,SAAUwB,GACdtX,EACA4E,EACAD,EACAwL,GAsBA,IACMjF,EAAUlL,EAAQyW,mBAClBX,EAAc,IAAIjC,EAAYlP,EAFrB,OAlBf,SAAiB8P,GACf,IAAMvS,EAAS2U,GAAmBpC,EAAK,CAAC,SAAU,QAAQ,EAC1D9Y,IAAI4b,EAA4B,KAChC,IACEA,EAAa9C,EAAIsC,kBAAkB,6BAA6B,CAGjE,CAFC,MAAOtS,GACP6P,EAAa,CAAA,CAAK,CACnB,CAEIiD,GAEHjD,EAAa,CAAA,CAAK,EAGpB,IAAM7E,EAAO8C,OAAOgF,CAAU,EAE9B,OADAjD,EAAa,CAACkD,MAAM/H,CAAI,CAAC,EAClB,IAAIiH,EAAsBjH,EAAMU,EAAKV,OAAmB,UAAXvN,CAAkB,CACvE,EAGyDgJ,CAAO,EAGjE,OAFA4K,EAAY7B,QAvBI,CAAEiD,wBAAyB,SAwB3CpB,EAAY3B,aAAegB,EAAmBvQ,CAAQ,EAC/CkR,CACT,CAiBgB,SAAA2B,GACd7S,EACA5E,EACA2E,EACAwL,EACAuH,EACAxF,EACAhQ,EACAkS,GAIA,IAAMtS,EAAU,IAAI4U,EAAsB,EAAG,CAAC,EAQ9C,GAPIxU,GACFJ,EAAQ6U,QAAUzU,EAAOyU,QACzB7U,EAAQ8H,MAAQ1H,EAAO0H,QAEvB9H,EAAQ6U,QAAU,EAClB7U,EAAQ8H,MAAQuG,EAAKV,QAEnBU,EAAKV,SAAW3N,EAAQ8H,MAC1B,MnBtRK,IAAI/H,EACTO,EAAiBuV,uBACjB,sEAAsE,EmBsRxE,IAAMC,EAAY9V,EAAQ8H,MAAQ9H,EAAQ6U,QAC1Chb,IAAIkc,EAAgBD,EACJ,EAAZF,IACFG,EAAgBxL,KAAKyL,IAAID,EAAeH,CAAS,GAEnD,IAAMzH,EAAYnO,EAAQ6U,QACpBzG,EAAUD,EAAY4H,EAC5Blc,IAAIoc,EAAgB,GAQd9D,EAAU,CACdiD,wBAPAa,EADoB,IAAlBF,EACc,WACPD,IAAcC,EACP,mBAEA,SAIhBG,uBAAwB,GAAGlW,EAAQ6U,SAE/BzC,EAAO/D,EAAKzK,MAAMuK,EAAWC,CAAO,EAC1C,GAAa,OAATgE,EACF,MAAMpR,EAAe,EA4BjBoI,EAAUlL,EAAQyW,mBAClBX,EAAc,IAAIjC,EAAYlP,EAFrB,OAxBf,SACE8P,EACAC,GAMA,IAAMuD,EAAepB,GAAmBpC,EAAK,CAAC,SAAU,QAAQ,EAC1DyD,EAAapW,EAAQ6U,QAAUkB,EAC/BpI,EAAOU,EAAKV,OAClB9T,IAAI+V,EAMJ,OAJEA,EADmB,UAAjBuG,EACSzD,EAAgBxU,EAASkS,CAAQ,EAAEuC,EAAKC,CAAI,EAE5C,KAEN,IAAIgC,EACTwB,EACAzI,EACiB,UAAjBwI,EACAvG,CAAQ,CAEX,EAGyDxG,CAAO,EAKjE,OAJA4K,EAAY7B,QAAUA,EACtB6B,EAAY5B,KAAOA,EAAKhD,aACxB4E,EAAY1B,iBAAmBA,GAAoB,KACnD0B,EAAY3B,aAAegB,EAAmBvQ,CAAQ,EAC/CkR,CACT,CC3iBa,IAAAqC,GAAY,CAavBC,cAAe,eACf,EA0BWC,EAAY,CAEvBC,QAAS,UAGTC,OAAQ,SAGRC,QAAS,UAGT3V,SAAU,WAGV4V,MAAO,OACE,EAEL,SAAUC,GACdC,GAEA,OAAQA,GACN,IAA+B,UAC/B,IAA+B,UAC/B,IAAA,YACE,OAAON,EAAUC,QACnB,IAAA,SACE,OAAOD,EAAUE,OACnB,IAAA,UACE,OAAOF,EAAUG,QACnB,IAAA,WACE,OAAOH,EAAUxV,SAGnB,QAEE,OAAOwV,EAAUI,KACpB,CACH,OCvCaG,GAKX7Z,YACE8Z,EACA3S,EACA4S,GAEA,IAOQC,EhB7DU,YAAb,OgBuDQF,GAA4B,MAAT3S,GAA6B,MAAZ4S,GAE/Ctc,KAAKwc,KAAOH,EACZrc,KAAK0J,MAAQA,MAAAA,EAAAA,EAAS8G,KAAAA,EACtBxQ,KAAKsc,SAAWA,MAAAA,EAAAA,EAAY9L,KAAAA,IAO5BxQ,KAAKwc,MALCD,EAAWF,GAKIG,KACrBxc,KAAK0J,MAAQ6S,EAAS7S,MACtB1J,KAAKsc,SAAWC,EAASD,SAE5B,CACF,CCzEK,SAAUG,EAAMC,GACpB,MAAO,IAAIC,KAET/S,QAAQ6C,QAAO,EAAGe,KAAK,IAAMkP,EAAE,GAAGC,CAAa,CAAC,CAClD,CACF,OCiHaC,iBArGXra,cAFUvC,KAAK6c,MAAY,CAAA,EAGzB7c,KAAK8c,KAAO,IAAIC,eAChB/c,KAAKgd,QAAO,EACZhd,KAAKid,WAAapX,EAAU+H,SAC5B5N,KAAKkd,aAAe,IAAItT,QAAQ6C,IAC9BzM,KAAK8c,KAAKK,iBAAiB,QAAS,KAClCnd,KAAKid,WAAapX,EAAUkI,MAC5BtB,GACF,CAAC,EACDzM,KAAK8c,KAAKK,iBAAiB,QAAS,KAClCnd,KAAKid,WAAapX,EAAUuX,cAC5B3Q,GACF,CAAC,EACDzM,KAAK8c,KAAKK,iBAAiB,OAAQ,KACjC1Q,GACF,CAAC,CACH,CAAC,CACF,CAIDc,KACEpF,EACAmP,EACAI,EACAD,GAEA,GAAIzX,KAAK6c,MACP,MAAM5V,EAAc,+BAA+B,EAIrD,GAFAjH,KAAK6c,MAAQ,CAAA,EACb7c,KAAK8c,KAAKO,KAAK/F,EAAQnP,EAAK,CAAA,CAAI,EAChBqI,KAAAA,IAAZiH,EACF,IAAK,IAAMzT,KAAOyT,EACZA,EAAQxM,eAAejH,CAAG,GAC5BhE,KAAK8c,KAAKQ,iBAAiBtZ,EAAKyT,EAAQzT,GAAK6V,SAAQ,CAAE,EAS7D,OALarJ,KAAAA,IAATkH,EACF1X,KAAK8c,KAAKvP,KAAKmK,CAAI,EAEnB1X,KAAK8c,KAAKvP,OAELvN,KAAKkd,YACb,CAEDvP,eACE,GAAK3N,KAAK6c,MAGV,OAAO7c,KAAKid,WAFV,MAAMhW,EAAc,uCAAuC,CAG9D,CAED4G,YACE,GAAI,CAAC7N,KAAK6c,MACR,MAAM5V,EAAc,oCAAoC,EAE1D,IACE,OAAOjH,KAAK8c,KAAKpX,MAGlB,CAFC,MAAOuC,GACP,MAAO,CAAC,CACT,CACF,CAEDoG,cACE,GAAKrO,KAAK6c,MAGV,OAAO7c,KAAK8c,KAAKS,SAFf,MAAMtW,EAAc,sCAAsC,CAG7D,CAEDsH,eACE,GAAKvO,KAAK6c,MAGV,OAAO7c,KAAK8c,KAAKU,WAFf,MAAMvW,EAAc,uCAAuC,CAG9D,CAGDmJ,QACEpQ,KAAK8c,KAAK1M,OACX,CAEDmK,kBAAkBkD,GAChB,OAAOzd,KAAK8c,KAAKvC,kBAAkBkD,CAAM,CAC1C,CAEDnQ,0BAA0BoQ,GACA,MAApB1d,KAAK8c,KAAKa,QACZ3d,KAAK8c,KAAKa,OAAOR,iBAAiB,WAAYO,CAAQ,CAEzD,CAEDjQ,6BAA6BiQ,GACH,MAApB1d,KAAK8c,KAAKa,QACZ3d,KAAK8c,KAAKa,OAAOC,oBAAoB,WAAYF,CAAQ,CAE5D,CACF,EAGCV,UACEhd,KAAK8c,KAAKe,aAAe,MAC1B,CACF,CAEe,SAAAC,IACd,OAAqD,IAAIlB,EAC3D,OCpFamB,GAsCXC,8BACE,OAAOhe,KAAKie,UAAYje,KAAKke,YAC9B,CAOD3b,YAAY4b,EAAgBxK,EAAeuB,EAA4B,MAjCvElV,KAAYoe,aAAW,EACfpe,KAAkBqe,mBAAY,CAAA,EAC9Bre,KAAoBse,qBAAY,CAAA,EAChCte,KAAUue,WAAuD,GAMjEve,KAAMwe,OAAkBhO,KAAAA,EACxBxQ,KAAUye,WAAYjO,KAAAA,EACtBxQ,KAAQ0e,SAAsBlO,KAAAA,EAC9BxQ,KAAgB2e,iBAAW,EAG3B3e,KAAQ4e,SAAsCpO,KAAAA,EAC9CxQ,KAAO6e,QAAgCrO,KAAAA,EAkB7CxQ,KAAK8e,KAAOX,EACZne,KAAK+e,MAAQpL,EACb3T,KAAK6V,UAAYX,EACjBlV,KAAKgf,UAAYvJ,IACjBzV,KAAKif,WAAajf,KAAKkf,mBAAmBlf,KAAK+e,KAAK,EACpD/e,KAAKmf,OAAM,UACXnf,KAAKof,cAAgB1V,IAGnB,GAFA1J,KAAK0e,SAAWlO,KAAAA,EAChBxQ,KAAK2e,iBAAmB,EACpBjV,EAAM/D,YAAYC,EAAiBS,QAAQ,EAC7CrG,KAAKqe,mBAAqB,CAAA,EAC1Bre,KAAKqf,qBAAoB,MACpB,CACL,IAAMC,EAAiBtf,KAAKge,8BAC5B,GAAI9S,GAAkBxB,EAAMhE,OAAQ,EAAE,EAAG,CACvC,GAAI4Z,CAAAA,EASF,OANAtf,KAAKie,UAAYpO,KAAK0P,IACH,EAAjBvf,KAAKie,UzBrF0B,GyBsFF,EAE/Bje,KAAKqe,mBAAqB,CAAA,EAJ1Bre,KAKAA,KAAKqf,qBAAoB,EAPzB3V,EAAQxD,EAAkB,CAU7B,CACDlG,KAAKwe,OAAS9U,EACd1J,KAAKwf,YAAW,QACjB,CACH,EACAxf,KAAKyf,sBAAwB/V,IAC3B1J,KAAK0e,SAAWlO,KAAAA,EACZ9G,EAAM/D,YAAYC,EAAiBS,QAAQ,EAC7CrG,KAAKqf,qBAAoB,GAEzBrf,KAAKwe,OAAS9U,EACd1J,KAAKwf,YAAW,SAEpB,EACAxf,KAAKie,UAAY,EACjBje,KAAKke,aAAele,KAAK8e,KAAKY,QAAQzF,mBACtCja,KAAK2f,SAAW,IAAI/V,QAAQ,CAAC6C,EAAS5C,KACpC7J,KAAK4e,SAAWnS,EAChBzM,KAAK6e,QAAUhV,EACf7J,KAAK4f,OAAM,CACb,CAAC,EAID5f,KAAK2f,SAASnS,KAAK,KAAM,MAAQ,CAClC,CAEOqS,wBACN,IAAMC,EAAa9f,KAAKoe,aACxB,OAAOjR,GAAUnN,KAAK+f,gBAAgBD,EAAa3S,CAAM,CAC1D,CAEO+R,mBAAmBvL,GACzB,OAAqB,OAAdA,EAAKV,MACb,CAEO2M,SACS,YAAX5f,KAAKmf,QAIa3O,KAAAA,IAAlBxQ,KAAK0e,WAGL1e,KAAKif,WACiBzO,KAAAA,IAApBxQ,KAAKye,WACPze,KAAKggB,iBAAgB,EAEjBhgB,KAAKqe,mBACPre,KAAKigB,aAAY,EAEbjgB,KAAKse,qBAEPte,KAAKkgB,eAAc,EAEnBlgB,KAAKmgB,eAAiB7Q,WAAW,KAC/BtP,KAAKmgB,eAAiB3P,KAAAA,EACtBxQ,KAAKogB,gBAAe,CACtB,EAAGpgB,KAAKie,SAAS,EAKvBje,KAAKqgB,eAAc,EAEtB,CAEOC,cACNpb,GAGA0E,QAAQ2W,IAAI,CACVvgB,KAAK8e,KAAKY,QAAQc,cAAe,EACjCxgB,KAAK8e,KAAKY,QAAQe,kBAAmB,EACtC,EAAEjT,KAAK,CAAA,CAAEkT,EAAWC,MACnB,OAAQ3gB,KAAKmf,QACX,IAAA,UACEja,EAASwb,EAAWC,CAAa,EACjC,MACF,IAAA,YACE3gB,KAAKwf,YAAW,YAChB,MACF,IAAA,UACExf,KAAKwf,YAAW,SAGnB,CACH,CAAC,CACF,CAIOQ,mBACNhgB,KAAKsgB,cAAc,CAACI,EAAWC,KAC7B,IAAMrH,EAAckB,GAClBxa,KAAK8e,KAAKY,QACV1f,KAAK8e,KAAK8B,UACV5gB,KAAKgf,UACLhf,KAAK+e,MACL/e,KAAK6V,SAAS,EAEVgL,EAAgB7gB,KAAK8e,KAAKY,QAAQoB,aACtCxH,EACAwE,EACA4C,EACAC,CAAa,GAEf3gB,KAAK0e,SAAWmC,GACF/W,WAAU,EAAG0D,KAAK,IAC9BxN,KAAK0e,SAAWlO,KAAAA,EAChBxQ,KAAKye,WAAatW,EAClBnI,KAAKqe,mBAAqB,CAAA,EAC1Bre,KAAKqf,qBAAoB,CAC3B,EAAGrf,KAAKof,aAAa,CACvB,CAAC,CACF,CAEOa,eAEN,IAAM9X,EAAMnI,KAAKye,WACjBze,KAAKsgB,cAAc,CAACI,EAAWC,KAC7B,IAAMrH,EAAcwB,GAClB9a,KAAK8e,KAAKY,QACV1f,KAAK8e,KAAK8B,UACVzY,EACAnI,KAAK+e,KAAK,EAENgC,EAAgB/gB,KAAK8e,KAAKY,QAAQoB,aACtCxH,EACAwE,EACA4C,EACAC,CAAa,GAEf3gB,KAAK0e,SAAWqC,GACFjX,WAAU,EAAG0D,KAAK9H,IAE9B1F,KAAK0e,SAAWlO,KAAAA,EAChBxQ,KAAK+f,gBAAgBra,EAAOyU,OAAO,EACnCna,KAAKqe,mBAAqB,CAAA,EACtB3Y,EAAO0U,YACTpa,KAAKse,qBAAuB,CAAA,GAE9Bte,KAAKqf,qBAAoB,CAC3B,EAAGrf,KAAKof,aAAa,CACvB,CAAC,CACF,CAEOgB,kBACN,IAAMlF,ELiNyC,OKjNClb,KAAK2e,iBAC/CjZ,EAAS,IAAIwU,EACjBla,KAAKoe,aACLpe,KAAK+e,MAAM9L,KAAI,CAAE,EAIb9K,EAAMnI,KAAKye,WACjBze,KAAKsgB,cAAc,CAACI,EAAWC,KAC7BxhB,IAAIma,EACJ,IACEA,EAAc2B,GACZjb,KAAK8e,KAAK8B,UACV5gB,KAAK8e,KAAKY,QACVvX,EACAnI,KAAK+e,MACL7D,EACAlb,KAAKgf,UACLtZ,EACA1F,KAAK6f,sBAAqB,CAAE,CAM/B,CAJC,MAAO5X,GAGP,OAFAjI,KAAKwe,OAASvW,EAAdjI,KACAA,KAAKwf,YAAW,QAEjB,CACD,IAAMwB,EAAgBhhB,KAAK8e,KAAKY,QAAQoB,aACtCxH,EACAwE,EACA4C,EACAC,EACW,CAAA,IAEb3gB,KAAK0e,SAAWsC,GACFlX,WAAU,EAAG0D,KAAK,IAC9BxN,KAAKihB,oBAAmB,EACxBjhB,KAAK0e,SAAWlO,KAAAA,EAChBxQ,KAAK+f,gBAAgBmB,EAAU/G,OAAO,EAClC+G,EAAU9G,WACZpa,KAAK6V,UAAYqL,EAAUhM,SAC3BlV,KAAKwf,YAAW,YAEhBxf,KAAKqf,qBAAoB,CAE7B,EAAGrf,KAAKof,aAAa,CACvB,CAAC,CACF,CAEO6B,sBAIY,GL6J6B,OKhKGjhB,KAAK2e,kBAGjC,WACpB3e,KAAK2e,kBAAoB,EAE5B,CAEOuB,iBACNlgB,KAAKsgB,cAAc,CAACI,EAAWC,KAC7B,IAAMrH,EAAcF,GAClBpZ,KAAK8e,KAAKY,QACV1f,KAAK8e,KAAK8B,UACV5gB,KAAKgf,SAAS,EAEVmC,EAAkBnhB,KAAK8e,KAAKY,QAAQoB,aACxCxH,EACAwE,EACA4C,EACAC,CAAa,GAEf3gB,KAAK0e,SAAWyC,GACArX,WAAU,EAAG0D,KAAK0H,IAChClV,KAAK0e,SAAWlO,KAAAA,EAChBxQ,KAAK6V,UAAYX,EACjBlV,KAAKwf,YAAW,UAClB,EAAGxf,KAAKyf,qBAAqB,CAC/B,CAAC,CACF,CAEOY,iBACNrgB,KAAKsgB,cAAc,CAACI,EAAWC,KAC7B,IAAMrH,EAAcI,GAClB1Z,KAAK8e,KAAKY,QACV1f,KAAK8e,KAAK8B,UACV5gB,KAAKgf,UACLhf,KAAK+e,MACL/e,KAAK6V,SAAS,EAEVuL,EAAmBphB,KAAK8e,KAAKY,QAAQoB,aACzCxH,EACAwE,EACA4C,EACAC,CAAa,GAEf3gB,KAAK0e,SAAW0C,GACCtX,WAAU,EAAG0D,KAAK0H,IACjClV,KAAK0e,SAAWlO,KAAAA,EAChBxQ,KAAK6V,UAAYX,EACjBlV,KAAK+f,gBAAgB/f,KAAK+e,MAAM9L,KAAM,CAAA,EACtCjT,KAAKwf,YAAW,UAClB,EAAGxf,KAAKof,aAAa,CACvB,CAAC,CACF,CAEOW,gBAAgBsB,GACtB,IAAMC,EAAMthB,KAAKoe,aACjBpe,KAAKoe,aAAeiD,EAKhBrhB,KAAKoe,eAAiBkD,GACxBthB,KAAKuhB,iBAAgB,CAExB,CAEO/B,YAAYrD,GAClB,GAAInc,KAAKmf,SAAWhD,EAGpB,OAAQA,GACN,IAAiC,YACjC,IAAA,UAIEnc,KAAKmf,OAAShD,EACQ3L,KAAAA,IAAlBxQ,KAAK0e,SACP1e,KAAK0e,SAAS3U,SACL/J,KAAKmgB,iBACd1Q,aAAazP,KAAKmgB,cAAc,EAChCngB,KAAKmgB,eAAiB3P,KAAAA,EACtBxQ,KAAKqf,qBAAoB,GAE3B,MACF,IAAA,UAIE,IAAMmC,EAAqD,WAAzCxhB,KAAKmf,OACvBnf,KAAKmf,OAAShD,EACVqF,IACFxhB,KAAKuhB,iBAAgB,EACrBvhB,KAAK4f,OAAM,GAEb,MACF,IAAA,SAGE5f,KAAKmf,OAAShD,EACdnc,KAAKuhB,iBAAgB,EACrB,MACF,IAAA,WAIEvhB,KAAKwe,OAASpY,IACdpG,KAAKmf,OAAShD,EACdnc,KAAKuhB,iBAAgB,EACrB,MACF,IAAA,QAQA,IAAA,UAKEvhB,KAAKmf,OAAShD,EACdnc,KAAKuhB,iBAAgB,CAGxB,CACF,CAEOlC,uBACN,OAAQrf,KAAKmf,QACX,IAAA,UACEnf,KAAKwf,YAAW,UAChB,MACF,IAAA,YACExf,KAAKwf,YAAW,YAChB,MACF,IAAA,UACExf,KAAK4f,OAAM,CAKd,CACF,CAKD6B,eACE,IAAMC,EAAgBxF,GAA+Blc,KAAKmf,MAAM,EAChE,MAAO,CACLwC,iBAAkB3hB,KAAKoe,aACvBwD,WAAY5hB,KAAK+e,MAAM9L,KAAM,EAC7BkJ,MAAOuF,EACPxM,SAAUlV,KAAK6V,UACfgM,KAAM7hB,KACNme,IAAKne,KAAK8e,KAEb,CAmBDgD,GACEvd,EACA8X,EAIA3S,EACAqY,GAGA,IAAMxF,EAAW,IAAIH,GAClBC,GAEkC7L,KAAAA,EACnC9G,GAAS8G,KAAAA,EACTuR,GAAavR,KAAAA,CAAS,EAGxB,OADAxQ,KAAKgiB,aAAazF,CAAQ,EACnB,KACLvc,KAAKiiB,gBAAgB1F,CAAQ,CAC/B,CACD,CAQD/O,KACE0U,EACAC,GAIA,OAAOniB,KAAK2f,SAASnS,KACnB0U,EACAC,CAAyD,CAE5D,CAKDC,MAASD,GACP,OAAOniB,KAAKwN,KAAK,KAAM2U,CAAU,CAClC,CAKOH,aAAazF,GACnBvc,KAAKue,WAAWnd,KAAKmb,CAAQ,EAC7Bvc,KAAKqiB,gBAAgB9F,CAAQ,CAC9B,CAKO0F,gBAAgB1F,GACtB,IAAMld,EAAIW,KAAKue,WAAWjT,QAAQiR,CAAQ,EAChC,CAAC,IAAPld,GACFW,KAAKue,WAAW+D,OAAOjjB,EAAG,CAAC,CAE9B,CAEOkiB,mBACNvhB,KAAKuiB,eAAc,EACDviB,KAAKue,WAAWrV,MAAK,EAC7BsL,QAAQ+H,IAChBvc,KAAKqiB,gBAAgB9F,CAAQ,CAC/B,CAAC,CACF,CAEOgG,iBACN,GAAsB/R,KAAAA,IAAlBxQ,KAAK4e,SAAwB,CAC/Bzf,IAAIqjB,EAAY,CAAA,EAChB,OAAQtG,GAA+Blc,KAAKmf,MAAM,GAChD,KAAKtD,EAAUG,QACbyG,EAASziB,KAAK4e,SAAS8D,KAAK,KAAM1iB,KAAKyhB,QAAQ,CAAC,IAChD,MACF,KAAK5F,EAAUxV,SACf,KAAKwV,EAAUI,MAEbwG,EADeziB,KAAK6e,QACJ6D,KAAK,KAAM1iB,KAAKwe,MAAsB,CAAC,IACvD,MACF,QACEgE,EAAY,CAAA,CAEf,CACGA,IACFxiB,KAAK4e,SAAWpO,KAAAA,EAChBxQ,KAAK6e,QAAUrO,KAAAA,EAElB,CACF,CAEO6R,gBAAgB9F,GAEtB,OADsBL,GAA+Blc,KAAKmf,MAAM,GAE9D,KAAKtD,EAAUC,QACf,KAAKD,EAAUE,OACTQ,EAASC,MACXiG,EAASlG,EAASC,KAAKkG,KAAKnG,EAAUvc,KAAKyhB,QAAQ,CAAC,IAEtD,MACF,KAAK5F,EAAUG,QACTO,EAASD,UACXmG,EAASlG,EAASD,SAASoG,KAAKnG,CAAQ,CAAC,EAAC,EAE5C,MACF,KAAKV,EAAUxV,SACf,KAAKwV,EAAUI,MAOf,QAEMM,EAAS7S,OACX+Y,EACElG,EAAS7S,MAAMgZ,KAAKnG,EAAUvc,KAAKwe,MAAsB,CAAC,GAGjE,CACF,CAMDmE,SACE,IAAMC,EACoC,WAAxC5iB,KAAKmf,QACM,YAAXnf,KAAKmf,OAIP,OAHIyD,GACF5iB,KAAKwf,YAAW,WAEXoD,CACR,CAMDC,QACE,IAAMD,EAAkD,YAA1C5iB,KAAKmf,OAInB,OAHIyD,GACF5iB,KAAKwf,YAAW,WAEXoD,CACR,CAOD7Y,SACE,IAAM6Y,EACqC,YAAzC5iB,KAAKmf,QACM,YAAXnf,KAAKmf,OAIP,OAHIyD,GACF5iB,KAAKwf,YAAW,aAEXoD,CACR,CACF,OC/mBYE,EAGXvgB,YACUwgB,EACR3a,GADQpI,KAAQ+iB,SAARA,EAGJ3a,aAAoBjB,EACtBnH,KAAK4gB,UAAYxY,EAEjBpI,KAAK4gB,UAAYzZ,EAASa,YAAYI,EAAU2a,EAASjb,IAAI,CAEhE,CAOD+R,WACE,MAAO,QAAU7Z,KAAK4gB,UAAUxZ,OAAS,IAAMpH,KAAK4gB,UAAUvZ,IAC/D,CAES2b,QACRxf,EACA4E,GAEA,OAAO,IAAI0a,EAAUtf,EAAS4E,CAAQ,CACvC,CAKD6a,WACE,IAAM7a,EAAW,IAAIjB,EAASnH,KAAK4gB,UAAUxZ,OAAQ,EAAE,EACvD,OAAOpH,KAAKgjB,QAAQhjB,KAAK+iB,SAAU3a,CAAQ,CAC5C,CAKDhB,aACE,OAAOpH,KAAK4gB,UAAUxZ,MACvB,CAKD0O,eACE,OAAO9V,KAAK4gB,UAAUvZ,IACvB,CAMD7E,WACE,OAAOuS,GAAc/U,KAAK4gB,UAAUvZ,IAAI,CACzC,CAKDqY,cACE,OAAO1f,KAAK+iB,QACb,CAMDG,aACE,IV9GmB7b,EUkHbe,EAJA+a,EV7GY,KADC9b,EU8GIrH,KAAK4gB,UAAUvZ,MV7G/B/H,OACA,KAGK,CAAC,KADTmV,EAAQpN,EAAK2N,YAAY,GAAG,GAEzB,GAEO3N,EAAK6B,MAAM,EAAGuL,CAAK,EUuGjC,OAAgB,OAAZ0O,EACK,MAEH/a,EAAW,IAAIjB,EAASnH,KAAK4gB,UAAUxZ,OAAQ+b,CAAO,EACrD,IAAIL,EAAU9iB,KAAK+iB,SAAU3a,CAAQ,EAC7C,CAKDgb,aAAa5gB,GACX,GAA4B,KAAxBxC,KAAK4gB,UAAUvZ,KACjB,MAAMT,EAAqBpE,CAAI,CAElC,CACF,CA0LK,SAAU6gB,GAAQlF,GACtB,IAAMmF,EAA0B,CAC9BvM,SAAU,GACVC,MAAO,IAET,OASFyF,eAAe8G,EACbpF,EACAmF,EACAE,GAEA,IAAMC,EAAmB,CAEvBD,UAAAA,GAEF,IAAME,EAAWC,MAAMC,GAAKzF,EAAKsF,CAAG,EACpCH,EAAYvM,SAAS3V,KAAK,GAAGsiB,EAAS3M,QAAQ,EAC9CuM,EAAYtM,MAAM5V,KAAK,GAAGsiB,EAAS1M,KAAK,EACV,MAA1B0M,EAASzM,eACX0M,MAAMJ,EAAcpF,EAAKmF,EAAaI,EAASzM,aAAa,CAEhE,EAxBuBkH,EAAKmF,CAAW,EAAE9V,KAAK,IAAM8V,CAAW,CAC/D,CA+CgB,SAAAM,GACdzF,EACA0F,GAEe,MAAXA,GACgC,UAA9B,OAAOA,EAAQC,YACjBzZ,EACE,qBACgB,EACA,IAChBwZ,EAAQC,UAAU,EAIxB,INpOA1b,EACA2b,EACAP,EACAM,EAkBM3b,EM+MA6b,EAAKH,GAAW,GAChBvK,GNtON9V,EMuOE2a,EAAIuB,QNtONtX,EMuOE+V,EAAIyC,UNtONmD,EMuOkB,INtOlBP,EMuOEQ,EAAGR,UNtOLM,EMuOEE,EAAGF,WNrOCtM,EAAuB,GACzBpP,EAASb,OACXiQ,EAAkB,OAAI,GAEtBA,EAAkB,OAAIpP,EAASf,KAAO,IAEpC0c,GAAgC,EAAnBA,EAAUzkB,SACzBkY,EAAqB,UAAIuM,GAEvBP,IACFhM,EAAqB,UAAIgM,GAEvBM,IACFtM,EAAsB,WAAIsM,GAGtB3b,EAAMsC,EAAQC,EADJtC,EAAST,sBACInE,EAAQsE,KAAMtE,EAAQ8U,SAAS,EAEtD5J,EAAUlL,EAAQ6V,uBAOxBC,EANoB,IAAIjC,EACtBlP,EAHa,MAKbgQ,GAAY3U,EAAS4E,EAAShB,MAAM,EACpCsH,CAAO,GAEG8I,UAAYA,EACxB8B,EAAY3B,aAAegB,EAAmBvQ,CAAQ,EAC/CkR,GM4MP,OAAO6E,EAAIuB,QAAQuE,sBAAsB3K,EAAawE,CAAiB,CACzE,CA8BgB,SAAAoG,GACd/F,EACAjJ,GAEAiJ,EAAIiF,aAAa,gBAAgB,ENjMjC5f,EMmME2a,EAAIuB,QNlMNtX,EMmME+V,EAAIyC,UNlMN1L,EMmMEA,ENlMFQ,EMmMED,EAAW,ENhMPtN,EAAMsC,EADIrC,EAASZ,gBACIhE,EAAQsE,KAAMtE,EAAQ8U,SAAS,EAEtDZ,EAAOjB,GAAiBvB,EAAUQ,CAAQ,EAE1ChH,EAAUlL,EAAQ6V,uBAOxBC,EANoB,IAAIjC,EACtBlP,EALa,QAOb6P,EAAgBxU,EAASkS,CAAQ,EACjChH,CAAO,GAEG+I,QARI,CAAEoD,eAAgB,mCASlCvB,EAAY5B,KAAOA,EACnB4B,EAAY3B,aAAesB,EAAmB7Q,CAAQ,EM+KtD,INlMA5E,EACA4E,EAEAsN,EAGMvN,EAEAuP,EM0LA4B,EN9KCA,EMoLP,OAAO6E,EAAIuB,QAAQuE,sBAAsB3K,EAAawE,CAAiB,CACzE,CAQM,SAAUqG,GAAehG,GAC7BA,EAAIiF,aAAa,gBAAgB,ENrOjC5f,EMuOE2a,EAAIuB,QNtONtX,EMuOE+V,EAAIyC,UNtONlL,EMuOED,EAAW,ENpOPtN,EAAMsC,EADIrC,EAASZ,gBACIhE,EAAQsE,KAAMtE,EAAQ8U,SAAS,EAEtD5J,EAAUlL,EAAQ6V,uBAOxBC,EANoB,IAAIjC,EACtBlP,EAHa,MAKbiQ,GAAmB5U,EAASkS,CAAQ,EACpChH,CAAO,GAEGiJ,aAAesB,EAAmB7Q,CAAQ,EMwNtD,INtOA5E,EACA4E,EACAsN,EAGMvN,EMiOAmR,ENvNCA,EM4NP,OAAO6E,EAAIuB,QACRuE,sBAAsB3K,EAAawE,CAAiB,EACpDtQ,KAAKrF,IACJ,GAAY,OAARA,EACF,MzBxNC,IAAI9C,EACTO,EAAiBwe,gBACjB,iDAAiD,EyBwN/C,OAAOjc,CACT,CAAC,CACL,CAQM,SAAUkc,GAAalG,GAC3BA,EAAIiF,aAAa,cAAc,ENjN/B5f,EMkNyC2a,EAAIuB,QN9MvCvX,EAAMsC,GAHZrC,EMiNsD+V,EAAIyC,WN/MjCpZ,gBACIhE,EAAQsE,KAAMtE,EAAQ8U,SAAS,EAEtD5J,EAAUlL,EAAQ6V,uBAGlBC,EAAc,IAAIjC,EAAYlP,EAJrB,SAGf,SAAiBmc,EAA0BC,KACe7V,CAAO,GACrDmJ,aAAe,CAAC,IAAK,KACjCyB,EAAY3B,aAAesB,EAAmB7Q,CAAQ,EMuMtD,INlNA5E,EACA4E,EAGMD,EM8MAmR,ENtMCA,EMuMP,OAAO6E,EAAIuB,QAAQuE,sBAAsB3K,EAAawE,CAAiB,CACzE,CAYgB,SAAA0G,GAAUrG,EAAgBsG,GVjdpBpd,EUkdE8W,EAAIyC,UAAUvZ,KVjd9Bqd,EUidoCD,EVhdvCjM,MAAM,GAAG,EACTmM,OAAOC,GAAgC,EAAnBA,EAAUtlB,MAAU,EACxC+B,KAAK,GAAG,EU8cX,IVldoBgG,EUkdd8b,EV7cc,IAAhB9b,EAAK/H,OACAolB,EAEArd,EAAO,IAAMqd,EU2chBtc,EAAW,IAAIjB,EAASgX,EAAIyC,UAAUxZ,OAAQ+b,CAAO,EAC3D,OAAO,IAAIL,EAAU3E,EAAIuB,QAAStX,CAAQ,CAC5C,CC3bA,SAASyc,GACP1G,EACA9W,GAEA,GAAI8W,aAAe2G,GAAqB,CACtC,IAAMthB,EAAU2a,EAChB,GAAuB,MAAnB3a,EAAQuhB,QACV,M1BoKG,IAAI1f,EACTO,EAAiBof,kBACjB,6CAEE5f,EACA,uCAAuC,E0BvKnC+R,EAAY,IAAI2L,EAAUtf,EAASA,EAAQuhB,OAAQ,EACzD,OAAY,MAAR1d,EACKwd,GAAY1N,EAAW9P,CAAI,EAE3B8P,CAEV,CAEC,OAAa3G,KAAAA,IAATnJ,EACKmd,GAAUrG,EAAK9W,CAAI,EAEnB8W,CAGb,CAqBgB,SAAAA,GACd8G,EACAC,GAEA,GAAIA,GA9DG,kBAAkBC,KA8DFD,CA9DqB,EA8DT,CACjC,GAAID,aAAwBH,GAC1B,OA1DcthB,EA0DIyhB,EA1D0B9c,EA0DZ+c,EAzD7B,IAAIpC,EAAUtf,EAAS2E,CAAG,EA2D7B,MAAM3B,EACJ,0EAA0E,CAG/E,CACC,OAAOqe,GAAYI,EAAcC,CAAS,EAjE9C,IAAoB1hB,EAA8B2E,CAmElD,CAEA,SAASid,GACPtd,EACAud,GAEA,IAAMxd,EAAewd,MAAAA,EAAM,KAAA,EAANA,EAASjgB,GAC9B,OAAoB,MAAhByC,EACK,KAEFV,EAASS,mBAAmBC,EAAcC,CAAI,CACvD,CAEM,SAAUwd,GACd5F,EACA5X,EACAyd,EACA1B,EAEI,IAEJnE,EAAQ5X,KAAUA,EAAH,IAAWyd,EAC1B7F,EAAQpH,UAAY,OACpB,IAAQkN,EAAkB3B,EAAH,cACnB2B,IACF9F,EAAQ+F,mBACmB,UAAzB,OAAOD,EACHA,GCtDM,CACd9M,EACAgN,KAEA,GAAIhN,EAAMiN,IACR,MAAM,IAAIllB,MACR,8GAA8G,EAIlH,IAKMmlB,EAAUF,GAAa,eACvBG,EAAMnN,EAAMmN,KAAO,EACnBC,EAAMpN,EAAMoN,KAAOpN,EAAMqN,QAC/B,GAAKD,EAwBL,OApBME,EAAO9iB,OAAAuW,OAAA,CAEXwM,IAAK,kCAAkCL,EACvCM,IAAKN,EACLC,IAAAA,EACAM,IAAKN,EAAM,KACXO,UAAWP,EACXC,IAAAA,EACAC,QAASD,EACTO,SAAU,CACRC,iBAAkB,SAClBC,WAAY,GAIX,EAAA7N,CAAK,EAKH,CACL/V,EAA8BkS,KAAK6B,UAjCtB,CACb8P,IAAK,OACLjiB,KAAM,KACP,CA8BoD,CAAC,EACpD5B,EAA8BkS,KAAK6B,UAAUsP,CAAO,CAAC,EAHrC,IAKhB3kB,KAAK,GAAG,EA3BR,MAAM,IAAIZ,MAAM,sDAAsD,CA4B1E,GDQ8B+kB,EAAe9F,EAAQ+G,IAAI5C,QAAQ6B,SAAS,EAE1E,OAQaZ,GAgBXviB,YAIWkkB,EACAC,EAIAC,EAIAC,EACAC,GAVA7mB,KAAGymB,IAAHA,EACAzmB,KAAa0mB,cAAbA,EAIA1mB,KAAiB2mB,kBAAjBA,EAIA3mB,KAAI4mB,KAAJA,EACA5mB,KAAgB6mB,iBAAhBA,EA7BX7mB,KAAO+kB,QAAoB,KAMnB/kB,KAAK8mB,MAAW3hB,EACxBnF,KAASsY,UAAW,QACDtY,KAAM+mB,OAAkB,KAEnC/mB,KAAQgnB,SAAY,CAAA,EAqB1BhnB,KAAKinB,uB3B5JuC,K2B6J5CjnB,KAAKknB,oB3BtJoC,I2BuJzClnB,KAAKmnB,UAAY,IAAIC,IAEnBpnB,KAAK+kB,QADK,MAAR6B,EACazf,EAASS,mBAAmBgf,EAAM5mB,KAAK8mB,KAAK,EAE5C1B,GAAcplB,KAAK8mB,MAAO9mB,KAAKymB,IAAI5C,OAAO,CAE5D,CAMD/b,WACE,OAAO9H,KAAK8mB,KACb,CAEDhf,SAASA,GACP9H,KAAK8mB,MAAQhf,EACI,MAAb9H,KAAK4mB,KACP5mB,KAAK+kB,QAAU5d,EAASS,mBAAmB5H,KAAK4mB,KAAM9e,CAAI,EAE1D9H,KAAK+kB,QAAUK,GAActd,EAAM9H,KAAKymB,IAAI5C,OAAO,CAEtD,CAKD5J,yBACE,OAAOja,KAAKknB,mBACb,CAEDjN,uBAAuBoN,GACrBhd,EACE,OACe,EACC0L,OAAOuR,kBACvBD,CAAI,EAENrnB,KAAKknB,oBAAsBG,CAC5B,CAMDhO,4BACE,OAAOrZ,KAAKinB,sBACb,CAED5N,0BAA0BgO,GACxBhd,EACE,OACe,EACC0L,OAAOuR,kBACvBD,CAAI,EAENrnB,KAAKinB,uBAAyBI,CAC/B,CAED7G,sBACE,GAAIxgB,KAAKylB,mBACP,OAAOzlB,KAAKylB,mBAEd,IAAM8B,EAAOvnB,KAAK0mB,cAAcc,aAAa,CAAEC,SAAU,CAAA,CAAI,CAAE,EAC/D,GAAIF,EAAM,CACFG,EAAY/D,MAAM4D,EAAKI,WAC7B,GAAkB,OAAdD,EACF,OAAOA,EAAUE,WAEpB,CACD,OAAO,IACR,CAEDnH,0BACE,IAGMoH,EAHN,OAAIC,GAAAA,qBAAqB9nB,KAAKymB,GAAG,GAAKzmB,KAAKymB,IAAIsB,SAASpH,cAC/C3gB,KAAKymB,IAAIsB,SAASpH,eAErBkH,EAAW7nB,KAAK2mB,kBAAkBa,aAAa,CAAEC,SAAU,CAAA,CAAI,CAAE,IAEtD9D,MAAMkE,EAASF,YAKhBjP,MAET,IACR,CAKDsP,UAME,OALKhoB,KAAKgnB,WACRhnB,KAAKgnB,SAAW,CAAA,EAChBhnB,KAAKmnB,UAAU3S,QAAQyT,GAAWA,EAAQle,OAAM,CAAE,EAClD/J,KAAKmnB,UAAUe,SAEVte,QAAQ6C,SAChB,CAMD0J,sBAAsB1N,GACpB,OAAO,IAAIqa,EAAU9iB,KAAMyI,CAAG,CAC/B,CAMDqY,aACExH,EACA6O,EACAzH,EACAC,EACAvU,EAAQ,CAAA,GAER,GAAKpM,KAAKgnB,SAkBR,OAAO,IAAIvd,GAAY/C,EAAU,CAAE,EAlBjB,CACF0hB,ClB1DpB9O,EACA+O,EACA3H,EACAC,EACAwH,EACAG,EACAlc,EAAQ,CAAA,GkBoDYgc,CACd9O,EACAtZ,KAAK+mB,OACLrG,EACAC,EACAwH,EACAnoB,KAAK6mB,iBACLza,GlBzDApB,EAAYH,GAAgByO,EAAY9B,SAAS,EACjDrP,EAAMmR,EAAYnR,IAAM6C,EACxByM,EAAUvU,OAAOuW,OAAO,GAAIH,EAAY7B,OAAO,EA1BvBA,EA2BdA,GA3BgC4Q,EA2BvBA,KAzBvB5Q,EAAQ,oBAAsB4Q,GAlBhC5Q,EA4CeA,EAzCG,QAFlBiJ,EA2CwBA,IAzCqB,EAAnBA,EAAUphB,SAClCmY,EAAuB,cAAI,YAAciJ,GAM3C4H,EAmC2BA,EAAT7Q,EAjCV,8BACN,UAAY6Q,MAAAA,EAAAA,EAAmB,cAUjC7Q,EAuBmBA,EApBG,QAFtBkJ,EAsB4BA,KAnB1BlJ,EAAQ,uBAAyBkJ,GkB+D/B,IAAMsH,ElB3CH,IAAIzc,GACTrD,EACAmR,EAAYhC,OACZG,EACA6B,EAAY5B,KACZ4B,EAAYzB,aACZyB,EAAYnO,qBACZmO,EAAY/B,QACZ+B,EAAY3B,aACZ2B,EAAY5K,QACZ4K,EAAY1B,iBACZuQ,EACA/b,CAAK,EkB8CH,OANApM,KAAKmnB,UAAUoB,IAAIN,CAAO,EAE1BA,EAAQne,WAAY,EAAC0D,KACnB,IAAMxN,KAAKmnB,UAAUqB,OAAOP,CAAO,EACnC,IAAMjoB,KAAKmnB,UAAUqB,OAAOP,CAAO,CAAC,EAE/BA,CACR,ClB3EW,IAMdK,EACAlc,EAfAqL,EAiBMzM,EAEAyM,CkBmEL,CAEDwM,4BACE3K,EACA6O,GAEA,GAAM,CAACzH,EAAWC,GAAiBgD,MAAM/Z,QAAQ2W,IAAI,CACnDvgB,KAAKwgB,cAAe,EACpBxgB,KAAKygB,kBAAmB,EACzB,EAED,OAAOzgB,KAAK8gB,aACVxH,EACA6O,EACAzH,EACAC,CAAa,EACb7W,YACH,CACF,4BE9Le,SAAA2e,GACdtK,EACAxa,EACAuR,GAGA,OADAiJ,EAAMha,EAAmBga,CAAG,EHsH5Bxa,EGnHEA,EHoHFuR,EGnHEA,GHiHFiJ,EGnHEA,GHuHEiF,aAAa,sBAAsB,EAChC,IAAIrF,GAAWI,EAAK,IAAIpL,EAAQpP,CAAI,EAAGuR,CAAQ,CGpHxD,CASM,SAAUkE,GAAY+E,GAE1B,OADAA,EAAMha,EAAmBga,CAAG,GH8OFA,EG7OCA,GH8OvBiF,aAAa,aAAa,EACxB9J,EAAcoP,GAClBvK,EAAIuB,QACJvB,EAAIyC,UACJnL,EAAW,CAAE,EAER0I,EAAIuB,QAAQuE,sBAAsB3K,EAAawE,CAAiB,EAPnE,IAEExE,CG9OR,CAsHgB,SAAA6E,GACd8G,EACAC,GAGA,OAAOyD,GADP1D,EAAe9gB,EAAmB8gB,CAAY,EAG5CC,CAAS,CAEb,CC3QA,SAAS0D,GACPC,EACA,CAAEC,mBAAoB3gB,CAAG,GAEzB,IAAMse,EAAMoC,EAAUE,YAAY,KAAK,EAAEvB,aAAY,EAC/CwB,EAAeH,EAAUE,YAAY,eAAe,EACpDE,EAAmBJ,EAAUE,YAAY,oBAAoB,EAEnE,OAAO,IAAIjE,GACT2B,EACAuC,EACAC,EACA9gB,EACA+gB,GAAAA,WAAW,CAEf,CAGEC,sBACE,IAAI9kB,EC5CoB,UD8CtBukB,GAED,UAAC9jB,qBAAqB,CAAA,CAAI,CAAC,EAG9BskB,GAAAA,gBAAgB5mB,YAAe,EAAiB,EAEhD4mB,GAAAA,gBAAgB5mB,YAAe,SAAkB,QElDtC6mB,EAGX9mB,YACW6B,EACAyd,EACA1D,GAFAne,KAASoE,UAATA,EACApE,KAAI6hB,KAAJA,EACA7hB,KAAGme,IAAHA,CACP,CAEJwD,uBACE,OAAO3hB,KAAKoE,UAAUud,gBACvB,CACDzM,eACE,OAAOlV,KAAKoE,UAAU8Q,QACvB,CACDiH,YACE,OAAOnc,KAAKoE,UAAU+X,KACvB,CACDyF,iBACE,OAAO5hB,KAAKoE,UAAUwd,UACvB,CACF,OCfY0H,GACX/mB,YACW6B,EACQ0a,GADR9e,KAASoE,UAATA,EACQpE,KAAI8e,KAAJA,EAWnB9e,KAAA+J,OAAS/J,KAAKoE,UAAU2F,OAAO2Y,KAAK1iB,KAAKoE,SAAS,EAClDpE,KAAAoiB,MAAQpiB,KAAKoE,UAAUge,MAAMM,KAAK1iB,KAAKoE,SAAS,EAChDpE,KAAA6iB,MAAQ7iB,KAAKoE,UAAUye,MAAMH,KAAK1iB,KAAKoE,SAAS,EAChDpE,KAAA2iB,OAAS3iB,KAAKoE,UAAUue,OAAOD,KAAK1iB,KAAKoE,SAAS,CAb9C,CAEJqd,eACE,OAAO,IAAI4H,EACTrpB,KAAKoE,UAAUqd,SACfzhB,KACAA,KAAK8e,IAAI,CAEZ,CAODtR,KACE0U,EACAC,GAEA,OAAOniB,KAAKoE,UAAUoJ,KAAKiU,IACzB,GAAIS,EACF,OAAOA,EACL,IAAImH,EAAyB5H,EAAUzhB,KAAMA,KAAK8e,IAAI,CAAC,CAG5D,EAAEqD,CAAU,CACd,CAEDL,GACEvd,EACA8X,EAIA3S,EACAqY,GAEA5iB,IAAIoqB,EAGuC/Y,KAAAA,EAoB3C,OAnBM6L,IAEFkN,EAD4B,YAA1B,OAAOlN,EACe,GACtBA,EACE,IAAIgN,EAAyBG,EAAcxpB,KAAMA,KAAK8e,IAAI,CAAC,EAGvC,CACtBtC,KAAQH,EAAeG,KACnB,GACEH,EAAeG,KACb,IAAI6M,EAAyBG,EAAcxpB,KAAMA,KAAK8e,IAAI,CAAC,EAE/DtO,KAAAA,EACJ8L,SAAUD,EAAeC,UAAY9L,KAAAA,EACrC9G,MAAO2S,EAAe3S,OAAS8G,KAAAA,IAI9BxQ,KAAKoE,UAAU0d,GACpBvd,EACAglB,EACA7f,GAAS8G,KAAAA,EACTuR,GAAavR,KAAAA,CAAS,CAEzB,CACF,OC9EYiZ,GACXlnB,YACW6B,EACQ2e,GADR/iB,KAASoE,UAATA,EACQpE,KAAQ+iB,SAARA,CACf,CAEJhM,eACE,OAAO/W,KAAKoE,UAAU2S,SAAS5C,IAC7BgK,GAAO,IAAIuL,EAAgBvL,EAAKne,KAAK+iB,QAAQ,CAAC,CAEjD,CACD/L,YACE,OAAOhX,KAAKoE,UAAU4S,MAAM7C,IAC1BgK,GAAO,IAAIuL,EAAgBvL,EAAKne,KAAK+iB,QAAQ,CAAC,CAEjD,CACD9L,oBACE,OAAOjX,KAAKoE,UAAU6S,eAAiB,IACxC,CACF,OCKYyS,EAGXnnB,YACW6B,EACFsb,GADE1f,KAASoE,UAATA,EACFpE,KAAO0f,QAAPA,CACL,CAEJld,WACE,OAAOxC,KAAKoE,UAAU5B,IACvB,CAED4E,aACE,OAAOpH,KAAKoE,UAAUgD,MACvB,CAED0O,eACE,OAAO9V,KAAKoE,UAAU0R,QACvB,CAED+D,WACE,OAAO7Z,KAAKoE,UAAUyV,UACvB,CAOD8P,MAAMlF,GACJ,IAAMtN,ENkPDyS,GMlPuB5pB,KAAKoE,UAAWqgB,CAAS,EACrD,OAAO,IAAIiF,EAAgBvS,EAAWnX,KAAK0f,OAAO,CACnD,CAEDuD,WACE,OAAO,IAAIyG,EAAgB1pB,KAAKoE,UAAU6e,KAAMjjB,KAAK0f,OAAO,CAC7D,CAMDwD,aACE,IAAM/L,EAAYnX,KAAKoE,UAAU8e,OACjC,OAAiB,MAAb/L,EACK,KAEF,IAAIuS,EAAgBvS,EAAWnX,KAAK0f,OAAO,CACnD,CAQDmK,IACElmB,EACAuR,GAGA,OADAlV,KAAKojB,aAAa,KAAK,EAChB,IAAIkG,GACTb,GAAqBzoB,KAAKoE,UAAWT,EAAMuR,CAA0B,EACrElV,IAAI,CAEP,CASD8pB,UACE7lB,EACA8C,EAAuB+J,EAAaC,IACpCmE,GAEAlV,KAAKojB,aAAa,WAAW,EAC7B,IAAMzf,EAAOomB,GAAgBhjB,EAAQ9C,CAAK,EACpCuV,EAAatW,OAAAuW,OAAA,GAAQvE,CAAQ,EAInC,OAHoC,MAAhCsE,EAA2B,aAAiC,MAApB7V,EAAKyN,cAC/CoI,EAA2B,YAAI7V,EAAKyN,aAE/B,IAAIkY,GACT,IAAIU,GACFhqB,KAAKoE,UACL,IAAI6lB,EAAStmB,EAAKA,KAAM,CAAA,CAAI,EAC5B6V,CAAuD,EAEzDxZ,IAAI,CAEP,CAmBDqjB,UACE,ONmGK6G,GADD/lB,EMlGWnE,KAAKoE,SNkGM,CACW,EMnGNoJ,KAC7B2c,GAAK,IAAIV,GAAiBU,EAAGnqB,KAAK0f,OAAO,CAAC,CAE7C,CAqBDkE,KAAKC,GACH,ON8CF1F,EM9Ccne,KAAKoE,UN+CnByf,EM/C8BA,GAAWrT,KAAAA,ENkDlC4Z,GADPjM,EAAMha,EAAmBga,CAAG,EACU0F,CAAO,EMlDOrW,KAChD2c,GAAK,IAAIV,GAAiBU,EAAGnqB,KAAK0f,OAAO,CAAC,EN4ChC,IACdvB,CM3CC,CAOD/E,cACE,OAAOA,GAAYpZ,KAAKoE,SAAS,CAClC,CAWD8f,eACEhP,GAEA,ONTKmV,GADDlmB,EMWFnE,KAAKoE,SNXmB,EMYxB8Q,CNTmC,CMWtC,CAMDiP,iBACE,ONiDKmG,GADDnmB,EMhDkBnE,KAAKoE,SNgDD,CACkB,CMhD7C,CAMDokB,SAEE,OADAxoB,KAAKojB,aAAa,QAAQ,ENoDrBmH,GADDpmB,EMlDgBnE,KAAKoE,SNkDC,CACgB,CMlD3C,CAEOgf,aAAa5gB,GACnB,GAAsD,KAAjDxC,KAAKoE,UAAyBwc,UAAUvZ,KAC3C,MAAMmjB,EAAsBhoB,CAAI,CAEnC,CACF,OC3MYioB,GAGXloB,YAAmBkkB,EAA2BriB,GAA3BpE,KAAGymB,IAAHA,EAA2BzmB,KAASoE,UAATA,CAA8B,CAE5EiV,4BACE,OAAOrZ,KAAKoE,UAAUiV,qBACvB,CAEDY,yBACE,OAAOja,KAAKoE,UAAU6V,kBACvB,CAMDkE,IAAI9W,GACF,GAAIqjB,GAAMrjB,CAAI,EACZ,MAAMsjB,EACJ,oEAAoE,EAGxE,OAAO,IAAIjB,EAAgBvL,GAAIne,KAAKoE,UAAWiD,CAAI,EAAGrH,IAAI,CAC3D,CAMD4qB,WAAWziB,GACT,GAAI,CAACuiB,GAAMviB,CAAG,EACZ,MAAMwiB,EACJ,2EAA2E,EAG/E,IACEE,EAAU7iB,YAAYG,EAAMnI,KAAKoE,UAAmC0D,IAAI,CAKzE,CAJC,MAAOG,GACP,MAAM0iB,EACJ,gEAAgE,CAEnE,CACD,OAAO,IAAIjB,EAAgBvL,GAAIne,KAAKoE,UAAW+D,CAAG,EAAGnI,IAAI,CAC1D,CAED8qB,sBAAsBzD,GACpBrnB,KAAKoE,UAAU6V,mBAAqBoN,CACrC,CAED0D,yBAAyB1D,GACvBrnB,KAAKoE,UAAUiV,sBAAwBgO,CACxC,CAED2D,YACEljB,EACAyd,EACA1B,EAEI,IPqQF,IAIJA,EOvQEyB,CPoQF5F,EACA5X,EACAyd,EACA1B,EAEI,IOzQFyB,CAAuBtlB,KAAKoE,UAAW0D,EAAMyd,EAAM1B,GP2QrDoH,GAAwBvL,EAAgC5X,EAAMyd,EAAM1B,CAAO,CO1Q1E,CACF,CAED,SAAS6G,GAAMrjB,GACb,MAAO,kBAAkB8d,KAAK9d,CAAc,CAC9C,CjC3DA,SAASuhB,GACPC,EACA,CAAEC,mBAAoB3gB,CAAG,GAGzB,IAAMse,EAAMoC,EAAUE,YAAY,YAAY,EAAEvB,aAAY,EACtD0D,EAAarC,EAChBE,YAAY,SAAS,EACrBvB,aAAa,CAAE2D,WAAYhjB,CAAG,CAAE,EAMnC,OAJmD,IAAIsiB,GACrDhE,EACAyE,CAAU,CAGd,CAEgCplB,EAkBhBugB,UAjBRtgB,EAAmB,WAEvB8V,YACAF,GACA7K,aAAAA,EACAsa,QAASX,GACT3H,UAAW4G,GAEb5jB,EAASulB,SAASC,kBAChB,IAAIjnB,EA7Ba,iBA6BWukB,GAA8B,QAAA,EACvD7jB,gBAAgBgB,CAAgB,EAChCjB,qBAAqB,CAAA,CAAI,CAAC,EAG/BgB,EAASsjB,mDAA6B"}