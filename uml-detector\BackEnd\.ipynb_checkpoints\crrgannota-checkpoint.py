import os

# Dossier contenant les fichiers d'annotations
labels_folder = "C:/Users/<USER>/datasets/dataset/train/labels"

# Vérifier et corriger les annotations
for filename in os.listdir(labels_folder):
    if filename.endswith(".txt"):
        file_path = os.path.join(labels_folder, filename)
        
        with open(file_path, "r") as file:
            lines = file.readlines()
        
        corrected_lines = []
        for line in lines:
            values = line.split()
            if len(values) == 5:
                class_id = values[0]
                coords = [float(v) for v in values[1:]]
                corrected_coords = [min(1.0, max(0.0, c)) for c in coords]  # Limiter entre 0 et 1
                corrected_line = f"{class_id} {' '.join(map(str, corrected_coords))}\n"
                corrected_lines.append(corrected_line)

        # Écrire les corrections dans le fichier
        with open(file_path, "w") as file:
            file.writelines(corrected_lines)

print("Correction terminée ! 🚀")
