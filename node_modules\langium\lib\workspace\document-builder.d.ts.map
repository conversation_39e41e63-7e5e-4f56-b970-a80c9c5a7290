{"version": 3, "file": "document-builder.d.ts", "sourceRoot": "", "sources": ["../../src/workspace/document-builder.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAEhF,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAC7D,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AACpD,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,KAAK,EAAE,yBAAyB,EAAE,MAAM,gBAAgB,CAAC;AAChE,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AAC9D,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,2BAA2B,CAAC;AAC1D,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,qCAAqC,CAAC;AAC7E,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,+BAA+B,CAAC;AAClE,OAAO,KAAK,EAAE,eAAe,EAAE,gBAAgB,EAAE,sBAAsB,EAAE,oBAAoB,EAAE,MAAM,gBAAgB,CAAC;AACtH,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AAGnD,OAAO,KAAK,EAAE,GAAG,EAAE,MAAM,uBAAuB,CAAC;AACjD,OAAO,EAAE,kBAAkB,EAAE,MAAM,sCAAsC,CAAC;AAC1E,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAE/C,MAAM,WAAW,YAAY;IACzB;;;;;OAKG;IACH,UAAU,CAAC,EAAE,OAAO,GAAG,iBAAiB,CAAA;CAC3C;AAED,MAAM,WAAW,kBAAkB;IAC/B,+DAA+D;IAC/D,SAAS,EAAE,OAAO,CAAA;IAClB,mDAAmD;IACnD,OAAO,EAAE,YAAY,CAAA;IACrB,0DAA0D;IAC1D,MAAM,CAAC,EAAE;QACL,gBAAgB,CAAC,EAAE,kBAAkB,EAAE,CAAA;KAC1C,CAAA;CACJ;AAED;;GAEG;AACH,MAAM,WAAW,eAAe;IAE5B,iEAAiE;IACjE,kBAAkB,EAAE,YAAY,CAAC;IAEjC;;;;;;;OAOG;IACH,KAAK,CAAC,CAAC,SAAS,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,YAAY,EAAE,WAAW,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAEvI;;;;;;;;OAQG;IACH,MAAM,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE,WAAW,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAEvF;;;OAGG;IACH,QAAQ,CAAC,QAAQ,EAAE,sBAAsB,GAAG,UAAU,CAAC;IAEvD;;OAEG;IACH,YAAY,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ,EAAE,qBAAqB,GAAG,UAAU,CAAC;IAEtF;;;;;;;;;OASG;IACH,eAAe,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ,EAAE,qBAAqB,GAAG,UAAU,CAAC;IAEzF;;;;;;OAMG;IACH,SAAS,CAAC,KAAK,EAAE,aAAa,EAAE,WAAW,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAEhF;;;;;;;;OAQG;IACH,SAAS,CAAC,KAAK,EAAE,aAAa,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,WAAW,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,GAAG,GAAG,SAAS,CAAC,CAAC;CACzG;AAED,MAAM,MAAM,sBAAsB,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;AAC7F,MAAM,MAAM,qBAAqB,GAAG,CAAC,KAAK,EAAE,eAAe,EAAE,EAAE,WAAW,EAAE,iBAAiB,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;AACtH,MAAM,MAAM,qBAAqB,GAAG,CAAC,KAAK,EAAE,eAAe,EAAE,WAAW,EAAE,iBAAiB,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;AACpH,qBAAa,sBAAuB,YAAW,eAAe;IAE1D,kBAAkB,EAAE,YAAY,CAK9B;IAEF,SAAS,CAAC,QAAQ,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACtD,SAAS,CAAC,QAAQ,CAAC,sBAAsB,EAAE,sBAAsB,CAAC;IAClE,SAAS,CAAC,QAAQ,CAAC,aAAa,EAAE,oBAAoB,GAAG,SAAS,CAAC;IACnE,SAAS,CAAC,QAAQ,CAAC,YAAY,EAAE,YAAY,CAAC;IAC9C,SAAS,CAAC,QAAQ,CAAC,eAAe,EAAE,eAAe,CAAC;IACpD,SAAS,CAAC,QAAQ,CAAC,eAAe,EAAE,sBAAsB,EAAE,CAAM;IAClE,SAAS,CAAC,QAAQ,CAAC,mBAAmB,iDAAwD;IAC9F,SAAS,CAAC,QAAQ,CAAC,sBAAsB,iDAAwD;IACjG,SAAS,CAAC,QAAQ,CAAC,UAAU,kCAAyC;IACtE,SAAS,CAAC,QAAQ,CAAC,oBAAoB,8BAAqC;IAC5E,SAAS,CAAC,YAAY,gBAAyB;gBAEnC,QAAQ,EAAE,yBAAyB;IAQzC,KAAK,CAAC,CAAC,SAAS,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,GAAE,YAAiB,EAAE,WAAW,oBAAyB,GAAG,OAAO,CAAC,IAAI,CAAC;IA0C/I,MAAM,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE,WAAW,oBAAyB,GAAG,OAAO,CAAC,IAAI,CAAC;cAkDjF,UAAU,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAIzE;;;;;;OAMG;IACH,SAAS,CAAC,aAAa,CAAC,SAAS,EAAE,eAAe,EAAE,GAAG,eAAe,EAAE;IAqBxE,OAAO,CAAC,eAAe;IAIvB;;OAEG;IACH,SAAS,CAAC,YAAY,CAAC,QAAQ,EAAE,eAAe,EAAE,WAAW,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,OAAO;IASpF,QAAQ,CAAC,QAAQ,EAAE,sBAAsB,GAAG,UAAU;IAUtD;;;;;;;;OAQG;cACa,cAAc,CAAC,SAAS,EAAE,eAAe,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;IAuClI;;;;;OAKG;IACH,SAAS,CAAC,YAAY,CAAC,SAAS,EAAE,eAAe,EAAE,EAAE,OAAO,EAAE,YAAY,GAAG,IAAI;IAiBjF;;;;;;;;;OASG;cACa,aAAa,CAAC,SAAS,EAAE,eAAe,EAAE,EAAE,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE,iBAAiB,EAClH,QAAQ,EAAE,CAAC,QAAQ,EAAE,eAAe,KAAK,YAAY,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAiBlF,YAAY,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ,EAAE,qBAAqB,GAAG,UAAU;IAOrF,eAAe,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ,EAAE,qBAAqB,GAAG,UAAU;IAOxF,SAAS,CAAC,KAAK,EAAE,aAAa,EAAE,WAAW,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;IAC/E,SAAS,CAAC,KAAK,EAAE,aAAa,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,WAAW,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,GAAG,GAAG,SAAS,CAAC;cAuCrF,mBAAmB,CAAC,QAAQ,EAAE,eAAe,EAAE,KAAK,EAAE,aAAa,EAAE,WAAW,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;cAgBnH,gBAAgB,CAAC,SAAS,EAAE,eAAe,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,WAAW,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;IAanI;;;;OAIG;IACH,SAAS,CAAC,cAAc,CAAC,QAAQ,EAAE,eAAe,GAAG,OAAO;IAI5D;;;OAGG;cACa,QAAQ,CAAC,QAAQ,EAAE,eAAe,EAAE,WAAW,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;IAwBlG,SAAS,CAAC,eAAe,CAAC,QAAQ,EAAE,eAAe,GAAG,YAAY;CAIrE"}