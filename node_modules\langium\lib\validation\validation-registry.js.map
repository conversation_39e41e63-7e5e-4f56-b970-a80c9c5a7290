{"version": 3, "file": "validation-registry.js", "sourceRoot": "", "sources": ["../../src/validation/validation-registry.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAGhF,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAIhD,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AAEnD,OAAO,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AAEjE,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAsC5C;;GAEG;AACH,MAAM,UAAU,cAAc,CAAC,IAAY;IACvC,OAAO,EAAE,IAAI,EAAE,CAAC;AACpB,CAAC;AAqDD,MAAM,KAAW,kBAAkB,CAElC;AAFD,WAAiB,kBAAkB;IAClB,sBAAG,GAAkC,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;AACnF,CAAC,EAFgB,kBAAkB,KAAlB,kBAAkB,QAElC;AAOD;;GAEG;AACH,MAAM,OAAO,kBAAkB;IAO3B,YAAY,QAA6B;QANxB,YAAO,GAAG,IAAI,QAAQ,EAAgC,CAAC;QAGhE,kBAAa,GAA4B,EAAE,CAAC;QAC5C,iBAAY,GAA4B,EAAE,CAAC;QAG/C,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC;IACpD,CAAC;IAED;;;;;;;OAOG;IACH,QAAQ,CAAI,YAAiC,EAAE,UAAsC,IAAI,EAAE,WAA+B,MAAM;QAC5H,IAAI,QAAQ,KAAK,UAAU,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,2EAA2E,CAAC,CAAC;QACjG,CAAC;QACD,KAAK,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;YACpD,MAAM,SAAS,GAAG,EAAyC,CAAC;YAC5D,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC3B,KAAK,MAAM,KAAK,IAAI,SAAS,EAAE,CAAC;oBAC5B,MAAM,KAAK,GAAyB;wBAChC,KAAK,EAAE,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,OAAO,CAAC;wBACnD,QAAQ;qBACX,CAAC;oBACF,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC/B,CAAC;YACL,CAAC;iBAAM,IAAI,OAAO,SAAS,KAAK,UAAU,EAAE,CAAC;gBACzC,MAAM,KAAK,GAAyB;oBAChC,KAAK,EAAE,IAAI,CAAC,uBAAuB,CAAC,SAAS,EAAE,OAAO,CAAC;oBACvD,QAAQ;iBACX,CAAC;gBACF,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC/B,CAAC;iBAAM,CAAC;gBACJ,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACjC,CAAC;QACL,CAAC;IACL,CAAC;IAES,uBAAuB,CAAC,KAAsB,EAAE,OAAgB;QACtE,OAAO,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE;YACvC,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,CAAC,EAAE,qCAAqC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QAC1I,CAAC,CAAC;IACN,CAAC;IAES,KAAK,CAAC,eAAe,CAAC,aAAuC,EAAE,cAAsB,EAAE,MAA0B,EAAE,IAAa;QACtI,IAAI,CAAC;YACD,MAAM,aAAa,EAAE,CAAC;QAC1B,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,oBAAoB,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC5B,MAAM,GAAG,CAAC;YACd,CAAC;YACD,OAAO,CAAC,KAAK,CAAC,GAAG,cAAc,GAAG,EAAE,GAAG,CAAC,CAAC;YACzC,IAAI,GAAG,YAAY,KAAK,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC;gBACpC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC7B,CAAC;YACD,MAAM,cAAc,GAAG,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACxE,MAAM,CAAC,OAAO,EAAE,GAAG,cAAc,KAAK,cAAc,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;QACtE,CAAC;IACL,CAAC;IAES,QAAQ,CAAC,IAAY,EAAE,KAA2B;QACxD,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YACrB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YACnC,OAAO;QACX,CAAC;QACD,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;YACzD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACrC,CAAC;IACL,CAAC;IAED,SAAS,CAAC,IAAY,EAAE,UAAiC;QACrD,IAAI,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aACtC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;QACzC,IAAI,UAAU,EAAE,CAAC;YACb,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;QACzE,CAAC;QACD,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC5C,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,sBAAsB,CAAC,WAAkC,EAAE,UAAsC,IAAI;QACjG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE,mDAAmD,EAAE,OAAO,CAAC,CAAC,CAAC;IACtI,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,qBAAqB,CAAC,UAAiC,EAAE,UAAsC,IAAI;QAC/F,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,sDAAsD,EAAE,OAAO,CAAC,CAAC,CAAC;IACvI,CAAC;IAES,wBAAwB,CAAC,KAA4B,EAAE,cAAsB,EAAE,OAAgB;QACrG,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,EAAE;YACvD,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,CAAC,EAAE,cAAc,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QACvI,CAAC,CAAC;IACN,CAAC;IAED,IAAI,YAAY;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,IAAI,WAAW;QACX,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;CAEJ"}