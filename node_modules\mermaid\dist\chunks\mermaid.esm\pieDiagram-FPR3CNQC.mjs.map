{"version": 3, "sources": ["../../../src/diagrams/pie/pieDb.ts", "../../../src/diagrams/pie/pieParser.ts", "../../../src/diagrams/pie/pieStyles.ts", "../../../src/diagrams/pie/pieRenderer.ts", "../../../src/diagrams/pie/pieDiagram.ts"], "sourcesContent": ["import { log } from '../../logger.js';\nimport {\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription,\n  clear as commonClear,\n} from '../common/commonDb.js';\nimport type { PieFields, PieDB, Sections, D3Section } from './pieTypes.js';\nimport type { RequiredDeep } from 'type-fest';\nimport type { PieDiagramConfig } from '../../config.type.js';\nimport DEFAULT_CONFIG from '../../defaultConfig.js';\n\nexport const DEFAULT_PIE_CONFIG: Required<PieDiagramConfig> = DEFAULT_CONFIG.pie;\n\nexport const DEFAULT_PIE_DB: RequiredDeep<PieFields> = {\n  sections: new Map(),\n  showData: false,\n  config: DEFAULT_PIE_CONFIG,\n} as const;\n\nlet sections: Sections = DEFAULT_PIE_DB.sections;\nlet showData: boolean = DEFAULT_PIE_DB.showData;\nconst config: Required<PieDiagramConfig> = structuredClone(DEFAULT_PIE_CONFIG);\n\nconst getConfig = (): Required<PieDiagramConfig> => structuredClone(config);\n\nconst clear = (): void => {\n  sections = new Map();\n  showData = DEFAULT_PIE_DB.showData;\n  commonClear();\n};\n\nconst addSection = ({ label, value }: D3Section): void => {\n  if (!sections.has(label)) {\n    sections.set(label, value);\n    log.debug(`added new section: ${label}, with value: ${value}`);\n  }\n};\n\nconst getSections = (): Sections => sections;\n\nconst setShowData = (toggle: boolean): void => {\n  showData = toggle;\n};\n\nconst getShowData = (): boolean => showData;\n\nexport const db: PieDB = {\n  getConfig,\n\n  clear,\n  setDiagramTitle,\n  getDiagramTitle,\n  setAccTitle,\n  getAccTitle,\n  setAccDescription,\n  getAccDescription,\n\n  addSection,\n  getSections,\n  setShowData,\n  getShowData,\n};\n", "import type { Pie } from '@mermaid-js/parser';\nimport { parse } from '@mermaid-js/parser';\nimport { log } from '../../logger.js';\nimport type { ParserDefinition } from '../../diagram-api/types.js';\nimport { populateCommonDb } from '../common/populateCommonDb.js';\nimport type { PieDB } from './pieTypes.js';\nimport { db } from './pieDb.js';\n\nconst populateDb = (ast: Pie, db: PieDB) => {\n  populateCommonDb(ast, db);\n  db.setShowData(ast.showData);\n  ast.sections.map(db.addSection);\n};\n\nexport const parser: ParserDefinition = {\n  parse: async (input: string): Promise<void> => {\n    const ast: Pie = await parse('pie', input);\n    log.debug(ast);\n    populateDb(ast, db);\n  },\n};\n", "import type { DiagramStylesProvider } from '../../diagram-api/types.js';\nimport type { PieStyleOptions } from './pieTypes.js';\n\nconst getStyles: DiagramStylesProvider = (options: PieStyleOptions) =>\n  `\n  .pieCircle{\n    stroke: ${options.pieStrokeColor};\n    stroke-width : ${options.pieStrokeWidth};\n    opacity : ${options.pieOpacity};\n  }\n  .pieOuterCircle{\n    stroke: ${options.pieOuterStrokeColor};\n    stroke-width: ${options.pieOuterStrokeWidth};\n    fill: none;\n  }\n  .pieTitleText {\n    text-anchor: middle;\n    font-size: ${options.pieTitleTextSize};\n    fill: ${options.pieTitleTextColor};\n    font-family: ${options.fontFamily};\n  }\n  .slice {\n    font-family: ${options.fontFamily};\n    fill: ${options.pieSectionTextColor};\n    font-size:${options.pieSectionTextSize};\n    // fill: white;\n  }\n  .legend text {\n    fill: ${options.pieLegendTextColor};\n    font-family: ${options.fontFamily};\n    font-size: ${options.pieLegendTextSize};\n  }\n`;\n\nexport default getStyles;\n", "import type d3 from 'd3';\nimport { arc, pie as d3pie, scaleOrdinal } from 'd3';\nimport type { MermaidConfig, PieDiagramConfig } from '../../config.type.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport type { DrawDefinition, SVG, SVGGroup } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport { selectSvgElement } from '../../rendering-util/selectSvgElement.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\nimport { cleanAndMerge, parseFontSize } from '../../utils.js';\nimport type { D3Section, PieDB, Sections } from './pieTypes.js';\n\nconst createPieArcs = (sections: Sections): d3.PieArcDatum<D3Section>[] => {\n  // Compute the position of each group on the pie:\n  const pieData: D3Section[] = [...sections.entries()]\n    .map((element: [string, number]): D3Section => {\n      return {\n        label: element[0],\n        value: element[1],\n      };\n    })\n    .sort((a: D3Section, b: D3Section): number => {\n      return b.value - a.value;\n    });\n  const pie: d3.Pie<unknown, D3Section> = d3pie<D3Section>().value(\n    (d3Section: D3Section): number => d3Section.value\n  );\n  return pie(pieData);\n};\n\n/**\n * Draws a Pie Chart with the data given in text.\n *\n * @param text - pie chart code\n * @param id - diagram id\n * @param _version - MermaidJS version from package.json.\n * @param diagObj - A standard diagram containing the DB and the text and type etc of the diagram.\n */\nexport const draw: DrawDefinition = (text, id, _version, diagObj) => {\n  log.debug('rendering pie chart\\n' + text);\n  const db = diagObj.db as PieDB;\n  const globalConfig: MermaidConfig = getConfig();\n  const pieConfig: Required<PieDiagramConfig> = cleanAndMerge(db.getConfig(), globalConfig.pie);\n  const MARGIN = 40;\n  const LEGEND_RECT_SIZE = 18;\n  const LEGEND_SPACING = 4;\n  const height = 450;\n  const pieWidth: number = height;\n  const svg: SVG = selectSvgElement(id);\n  const group: SVGGroup = svg.append('g');\n  group.attr('transform', 'translate(' + pieWidth / 2 + ',' + height / 2 + ')');\n\n  const { themeVariables } = globalConfig;\n  let [outerStrokeWidth] = parseFontSize(themeVariables.pieOuterStrokeWidth);\n  outerStrokeWidth ??= 2;\n\n  const textPosition: number = pieConfig.textPosition;\n  const radius: number = Math.min(pieWidth, height) / 2 - MARGIN;\n  // Shape helper to build arcs:\n  const arcGenerator: d3.Arc<unknown, d3.PieArcDatum<D3Section>> = arc<d3.PieArcDatum<D3Section>>()\n    .innerRadius(0)\n    .outerRadius(radius);\n  const labelArcGenerator: d3.Arc<unknown, d3.PieArcDatum<D3Section>> = arc<\n    d3.PieArcDatum<D3Section>\n  >()\n    .innerRadius(radius * textPosition)\n    .outerRadius(radius * textPosition);\n\n  group\n    .append('circle')\n    .attr('cx', 0)\n    .attr('cy', 0)\n    .attr('r', radius + outerStrokeWidth / 2)\n    .attr('class', 'pieOuterCircle');\n\n  const sections: Sections = db.getSections();\n  const arcs: d3.PieArcDatum<D3Section>[] = createPieArcs(sections);\n\n  const myGeneratedColors = [\n    themeVariables.pie1,\n    themeVariables.pie2,\n    themeVariables.pie3,\n    themeVariables.pie4,\n    themeVariables.pie5,\n    themeVariables.pie6,\n    themeVariables.pie7,\n    themeVariables.pie8,\n    themeVariables.pie9,\n    themeVariables.pie10,\n    themeVariables.pie11,\n    themeVariables.pie12,\n  ];\n  // Set the color scale\n  const color: d3.ScaleOrdinal<string, 12, never> = scaleOrdinal(myGeneratedColors);\n\n  // Build the pie chart: each part of the pie is a path that we build using the arc function.\n  group\n    .selectAll('mySlices')\n    .data(arcs)\n    .enter()\n    .append('path')\n    .attr('d', arcGenerator)\n    .attr('fill', (datum: d3.PieArcDatum<D3Section>) => {\n      return color(datum.data.label);\n    })\n    .attr('class', 'pieCircle');\n\n  let sum = 0;\n  sections.forEach((section) => {\n    sum += section;\n  });\n  // Now add the percentage.\n  // Use the centroid method to get the best coordinates.\n  group\n    .selectAll('mySlices')\n    .data(arcs)\n    .enter()\n    .append('text')\n    .text((datum: d3.PieArcDatum<D3Section>): string => {\n      return ((datum.data.value / sum) * 100).toFixed(0) + '%';\n    })\n    .attr('transform', (datum: d3.PieArcDatum<D3Section>): string => {\n      // eslint-disable-next-line @typescript-eslint/restrict-plus-operands\n      return 'translate(' + labelArcGenerator.centroid(datum) + ')';\n    })\n    .style('text-anchor', 'middle')\n    .attr('class', 'slice');\n\n  group\n    .append('text')\n    .text(db.getDiagramTitle())\n    .attr('x', 0)\n    .attr('y', -(height - 50) / 2)\n    .attr('class', 'pieTitleText');\n\n  // Add the legends/annotations for each section\n  const legend = group\n    .selectAll('.legend')\n    .data(color.domain())\n    .enter()\n    .append('g')\n    .attr('class', 'legend')\n    .attr('transform', (_datum, index: number): string => {\n      const height = LEGEND_RECT_SIZE + LEGEND_SPACING;\n      const offset = (height * color.domain().length) / 2;\n      const horizontal = 12 * LEGEND_RECT_SIZE;\n      const vertical = index * height - offset;\n      return 'translate(' + horizontal + ',' + vertical + ')';\n    });\n\n  legend\n    .append('rect')\n    .attr('width', LEGEND_RECT_SIZE)\n    .attr('height', LEGEND_RECT_SIZE)\n    .style('fill', color)\n    .style('stroke', color);\n\n  legend\n    .data(arcs)\n    .append('text')\n    .attr('x', LEGEND_RECT_SIZE + LEGEND_SPACING)\n    .attr('y', LEGEND_RECT_SIZE - LEGEND_SPACING)\n    .text((datum: d3.PieArcDatum<D3Section>): string => {\n      const { label, value } = datum.data;\n      if (db.getShowData()) {\n        return `${label} [${value}]`;\n      }\n      return label;\n    });\n\n  const longestTextWidth = Math.max(\n    ...legend\n      .selectAll('text')\n      .nodes()\n      .map((node) => (node as Element)?.getBoundingClientRect().width ?? 0)\n  );\n\n  const totalWidth = pieWidth + MARGIN + LEGEND_RECT_SIZE + LEGEND_SPACING + longestTextWidth;\n\n  // Set viewBox\n  svg.attr('viewBox', `0 0 ${totalWidth} ${height}`);\n  configureSvgSize(svg, height, totalWidth, pieConfig.useMaxWidth);\n};\n\nexport const renderer = { draw };\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\nimport { parser } from './pieParser.js';\nimport { db } from './pieDb.js';\nimport styles from './pieStyles.js';\nimport { renderer } from './pieRenderer.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  db,\n  renderer,\n  styles,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeO,IAAM,qBAAiD,sBAAe;AAEtE,IAAM,iBAA0C;AAAA,EACrD,UAAU,oBAAI,IAAI;AAAA,EAClB,UAAU;AAAA,EACV,QAAQ;AACV;AAEA,IAAI,WAAqB,eAAe;AACxC,IAAI,WAAoB,eAAe;AACvC,IAAM,SAAqC,gBAAgB,kBAAkB;AAE7E,IAAMA,aAAY,6BAAkC,gBAAgB,MAAM,GAAxD;AAElB,IAAMC,SAAQ,6BAAY;AACxB,aAAW,oBAAI,IAAI;AACnB,aAAW,eAAe;AAC1B,QAAY;AACd,GAJc;AAMd,IAAM,aAAa,wBAAC,EAAE,OAAO,MAAM,MAAuB;AACxD,MAAI,CAAC,SAAS,IAAI,KAAK,GAAG;AACxB,aAAS,IAAI,OAAO,KAAK;AACzB,QAAI,MAAM,sBAAsB,KAAK,iBAAiB,KAAK,EAAE;AAAA,EAC/D;AACF,GALmB;AAOnB,IAAM,cAAc,6BAAgB,UAAhB;AAEpB,IAAM,cAAc,wBAAC,WAA0B;AAC7C,aAAW;AACb,GAFoB;AAIpB,IAAM,cAAc,6BAAe,UAAf;AAEb,IAAM,KAAY;AAAA,EACvB,WAAAD;AAAA,EAEA,OAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACzDA,IAAM,aAAa,wBAAC,KAAUC,QAAc;AAC1C,mBAAiB,KAAKA,GAAE;AACxB,EAAAA,IAAG,YAAY,IAAI,QAAQ;AAC3B,MAAI,SAAS,IAAIA,IAAG,UAAU;AAChC,GAJmB;AAMZ,IAAM,SAA2B;AAAA,EACtC,OAAO,8BAAO,UAAiC;AAC7C,UAAM,MAAW,MAAM,MAAM,OAAO,KAAK;AACzC,QAAI,MAAM,GAAG;AACb,eAAW,KAAK,EAAE;AAAA,EACpB,GAJO;AAKT;;;ACjBA,IAAM,YAAmC,wBAAC,YACxC;AAAA;AAAA,cAEY,QAAQ,cAAc;AAAA,qBACf,QAAQ,cAAc;AAAA,gBAC3B,QAAQ,UAAU;AAAA;AAAA;AAAA,cAGpB,QAAQ,mBAAmB;AAAA,oBACrB,QAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,iBAK9B,QAAQ,gBAAgB;AAAA,YAC7B,QAAQ,iBAAiB;AAAA,mBAClB,QAAQ,UAAU;AAAA;AAAA;AAAA,mBAGlB,QAAQ,UAAU;AAAA,YACzB,QAAQ,mBAAmB;AAAA,gBACvB,QAAQ,kBAAkB;AAAA;AAAA;AAAA;AAAA,YAI9B,QAAQ,kBAAkB;AAAA,mBACnB,QAAQ,UAAU;AAAA,iBACpB,QAAQ,iBAAiB;AAAA;AAAA,GA3BD;AA+BzC,IAAO,oBAAQ;;;ACvBf,IAAM,gBAAgB,wBAACC,cAAoD;AAEzE,QAAM,UAAuB,CAAC,GAAGA,UAAS,QAAQ,CAAC,EAChD,IAAI,CAAC,YAAyC;AAC7C,WAAO;AAAA,MACL,OAAO,QAAQ,CAAC;AAAA,MAChB,OAAO,QAAQ,CAAC;AAAA,IAClB;AAAA,EACF,CAAC,EACA,KAAK,CAAC,GAAc,MAAyB;AAC5C,WAAO,EAAE,QAAQ,EAAE;AAAA,EACrB,CAAC;AACH,QAAM,MAAkC,YAAiB,EAAE;AAAA,IACzD,CAAC,cAAiC,UAAU;AAAA,EAC9C;AACA,SAAO,IAAI,OAAO;AACpB,GAhBsB;AA0Bf,IAAM,OAAuB,wBAAC,MAAM,IAAI,UAAU,YAAY;AACnE,MAAI,MAAM,0BAA0B,IAAI;AACxC,QAAMC,MAAK,QAAQ;AACnB,QAAM,eAA8B,UAAU;AAC9C,QAAM,YAAwC,cAAcA,IAAG,UAAU,GAAG,aAAa,GAAG;AAC5F,QAAM,SAAS;AACf,QAAM,mBAAmB;AACzB,QAAM,iBAAiB;AACvB,QAAM,SAAS;AACf,QAAM,WAAmB;AACzB,QAAM,MAAW,iBAAiB,EAAE;AACpC,QAAM,QAAkB,IAAI,OAAO,GAAG;AACtC,QAAM,KAAK,aAAa,eAAe,WAAW,IAAI,MAAM,SAAS,IAAI,GAAG;AAE5E,QAAM,EAAE,eAAe,IAAI;AAC3B,MAAI,CAAC,gBAAgB,IAAI,cAAc,eAAe,mBAAmB;AACzE,uBAAqB;AAErB,QAAM,eAAuB,UAAU;AACvC,QAAM,SAAiB,KAAK,IAAI,UAAU,MAAM,IAAI,IAAI;AAExD,QAAM,eAA2D,YAA+B,EAC7F,YAAY,CAAC,EACb,YAAY,MAAM;AACrB,QAAM,oBAAgE,YAEpE,EACC,YAAY,SAAS,YAAY,EACjC,YAAY,SAAS,YAAY;AAEpC,QACG,OAAO,QAAQ,EACf,KAAK,MAAM,CAAC,EACZ,KAAK,MAAM,CAAC,EACZ,KAAK,KAAK,SAAS,mBAAmB,CAAC,EACvC,KAAK,SAAS,gBAAgB;AAEjC,QAAMD,YAAqBC,IAAG,YAAY;AAC1C,QAAM,OAAoC,cAAcD,SAAQ;AAEhE,QAAM,oBAAoB;AAAA,IACxB,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,EACjB;AAEA,QAAM,QAA4C,QAAa,iBAAiB;AAGhF,QACG,UAAU,UAAU,EACpB,KAAK,IAAI,EACT,MAAM,EACN,OAAO,MAAM,EACb,KAAK,KAAK,YAAY,EACtB,KAAK,QAAQ,CAAC,UAAqC;AAClD,WAAO,MAAM,MAAM,KAAK,KAAK;AAAA,EAC/B,CAAC,EACA,KAAK,SAAS,WAAW;AAE5B,MAAI,MAAM;AACV,EAAAA,UAAS,QAAQ,CAAC,YAAY;AAC5B,WAAO;AAAA,EACT,CAAC;AAGD,QACG,UAAU,UAAU,EACpB,KAAK,IAAI,EACT,MAAM,EACN,OAAO,MAAM,EACb,KAAK,CAAC,UAA6C;AAClD,YAAS,MAAM,KAAK,QAAQ,MAAO,KAAK,QAAQ,CAAC,IAAI;AAAA,EACvD,CAAC,EACA,KAAK,aAAa,CAAC,UAA6C;AAE/D,WAAO,eAAe,kBAAkB,SAAS,KAAK,IAAI;AAAA,EAC5D,CAAC,EACA,MAAM,eAAe,QAAQ,EAC7B,KAAK,SAAS,OAAO;AAExB,QACG,OAAO,MAAM,EACb,KAAKC,IAAG,gBAAgB,CAAC,EACzB,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,EAAE,SAAS,MAAM,CAAC,EAC5B,KAAK,SAAS,cAAc;AAG/B,QAAM,SAAS,MACZ,UAAU,SAAS,EACnB,KAAK,MAAM,OAAO,CAAC,EACnB,MAAM,EACN,OAAO,GAAG,EACV,KAAK,SAAS,QAAQ,EACtB,KAAK,aAAa,CAAC,QAAQ,UAA0B;AACpD,UAAMC,UAAS,mBAAmB;AAClC,UAAM,SAAUA,UAAS,MAAM,OAAO,EAAE,SAAU;AAClD,UAAM,aAAa,KAAK;AACxB,UAAM,WAAW,QAAQA,UAAS;AAClC,WAAO,eAAe,aAAa,MAAM,WAAW;AAAA,EACtD,CAAC;AAEH,SACG,OAAO,MAAM,EACb,KAAK,SAAS,gBAAgB,EAC9B,KAAK,UAAU,gBAAgB,EAC/B,MAAM,QAAQ,KAAK,EACnB,MAAM,UAAU,KAAK;AAExB,SACG,KAAK,IAAI,EACT,OAAO,MAAM,EACb,KAAK,KAAK,mBAAmB,cAAc,EAC3C,KAAK,KAAK,mBAAmB,cAAc,EAC3C,KAAK,CAAC,UAA6C;AAClD,UAAM,EAAE,OAAO,MAAM,IAAI,MAAM;AAC/B,QAAID,IAAG,YAAY,GAAG;AACpB,aAAO,GAAG,KAAK,KAAK,KAAK;AAAA,IAC3B;AACA,WAAO;AAAA,EACT,CAAC;AAEH,QAAM,mBAAmB,KAAK;AAAA,IAC5B,GAAG,OACA,UAAU,MAAM,EAChB,MAAM,EACN,IAAI,CAAC,SAAU,MAAkB,sBAAsB,EAAE,SAAS,CAAC;AAAA,EACxE;AAEA,QAAM,aAAa,WAAW,SAAS,mBAAmB,iBAAiB;AAG3E,MAAI,KAAK,WAAW,OAAO,UAAU,IAAI,MAAM,EAAE;AACjD,mBAAiB,KAAK,QAAQ,YAAY,UAAU,WAAW;AACjE,GAhJoC;AAkJ7B,IAAM,WAAW,EAAE,KAAK;;;ACjLxB,IAAM,UAA6B;AAAA,EACxC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": ["getConfig", "clear", "db", "sections", "db", "height"]}