{"version": 3, "sources": ["../../../src/diagrams/packet/db.ts", "../../../src/diagrams/packet/parser.ts", "../../../src/diagrams/packet/renderer.ts", "../../../src/diagrams/packet/styles.ts", "../../../src/diagrams/packet/diagram.ts"], "sourcesContent": ["import { getConfig as commonGetConfig } from '../../config.js';\nimport type { PacketDiagramConfig } from '../../config.type.js';\nimport DEFAULT_CONFIG from '../../defaultConfig.js';\nimport { cleanAndMerge } from '../../utils.js';\nimport {\n  clear as commonClear,\n  getAccDescription,\n  getAccTitle,\n  getDiagramTitle,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n} from '../common/commonDb.js';\nimport type { PacketDB, PacketData, PacketWord } from './types.js';\n\nconst defaultPacketData: PacketData = {\n  packet: [],\n};\n\nlet data: PacketData = structuredClone(defaultPacketData);\n\nconst DEFAULT_PACKET_CONFIG: Required<PacketDiagramConfig> = DEFAULT_CONFIG.packet;\n\nconst getConfig = (): Required<PacketDiagramConfig> => {\n  const config = cleanAndMerge({\n    ...DEFAULT_PACKET_CONFIG,\n    ...commonGetConfig().packet,\n  });\n  if (config.showBits) {\n    config.paddingY += 10;\n  }\n  return config;\n};\n\nconst getPacket = (): PacketWord[] => data.packet;\n\nconst pushWord = (word: PacketWord) => {\n  if (word.length > 0) {\n    data.packet.push(word);\n  }\n};\n\nconst clear = () => {\n  commonClear();\n  data = structuredClone(defaultPacketData);\n};\n\nexport const db: PacketDB = {\n  pushWord,\n  getPacket,\n  getConfig,\n  clear,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription,\n};\n", "import type { Packet } from '@mermaid-js/parser';\nimport { parse } from '@mermaid-js/parser';\nimport type { ParserDefinition } from '../../diagram-api/types.js';\nimport { log } from '../../logger.js';\nimport { populateCommonDb } from '../common/populateCommonDb.js';\nimport { db } from './db.js';\nimport type { PacketBlock, PacketWord } from './types.js';\n\nconst maxPacketSize = 10_000;\n\nconst populate = (ast: Packet) => {\n  populateCommonDb(ast, db);\n  let lastByte = -1;\n  let word: PacketWord = [];\n  let row = 1;\n  const { bitsPerRow } = db.getConfig();\n  for (let { start, end, label } of ast.blocks) {\n    if (end && end < start) {\n      throw new Error(`Packet block ${start} - ${end} is invalid. End must be greater than start.`);\n    }\n    if (start !== lastByte + 1) {\n      throw new Error(\n        `Packet block ${start} - ${end ?? start} is not contiguous. It should start from ${\n          lastByte + 1\n        }.`\n      );\n    }\n    lastByte = end ?? start;\n    log.debug(`Packet block ${start} - ${lastByte} with label ${label}`);\n\n    while (word.length <= bitsPerRow + 1 && db.getPacket().length < maxPacketSize) {\n      const [block, nextBlock] = getNextFittingBlock({ start, end, label }, row, bitsPerRow);\n      word.push(block);\n      if (block.end + 1 === row * bitsPerRow) {\n        db.pushWord(word);\n        word = [];\n        row++;\n      }\n      if (!nextBlock) {\n        break;\n      }\n      ({ start, end, label } = nextBlock);\n    }\n  }\n  db.pushWord(word);\n};\n\nconst getNextFittingBlock = (\n  block: PacketBlock,\n  row: number,\n  bitsPerRow: number\n): [Required<PacketBlock>, PacketBlock | undefined] => {\n  if (block.end === undefined) {\n    block.end = block.start;\n  }\n\n  if (block.start > block.end) {\n    throw new Error(`Block start ${block.start} is greater than block end ${block.end}.`);\n  }\n\n  if (block.end + 1 <= row * bitsPerRow) {\n    return [block as Required<PacketBlock>, undefined];\n  }\n\n  return [\n    {\n      start: block.start,\n      end: row * bitsPerRow - 1,\n      label: block.label,\n    },\n    {\n      start: row * bitsPerRow,\n      end: block.end,\n      label: block.label,\n    },\n  ];\n};\n\nexport const parser: ParserDefinition = {\n  parse: async (input: string): Promise<void> => {\n    const ast: Packet = await parse('packet', input);\n    log.debug(ast);\n    populate(ast);\n  },\n};\n", "import type { Diagram } from '../../Diagram.js';\nimport type { PacketDiagramConfig } from '../../config.type.js';\nimport type { DiagramRenderer, DrawDefinition, SVG, SVGGroup } from '../../diagram-api/types.js';\nimport { selectSvgElement } from '../../rendering-util/selectSvgElement.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\nimport type { PacketDB, PacketWord } from './types.js';\n\nconst draw: DrawDefinition = (_text, id, _version, diagram: Diagram) => {\n  const db = diagram.db as PacketDB;\n  const config = db.getConfig();\n  const { rowHeight, paddingY, bitWidth, bitsPerRow } = config;\n  const words = db.getPacket();\n  const title = db.getDiagramTitle();\n  const totalRowHeight = rowHeight + paddingY;\n  const svgHeight = totalRowHeight * (words.length + 1) - (title ? 0 : rowHeight);\n  const svgWidth = bitWidth * bitsPerRow + 2;\n  const svg: SVG = selectSvgElement(id);\n\n  svg.attr('viewbox', `0 0 ${svgWidth} ${svgHeight}`);\n  configureSvgSize(svg, svgHeight, svgWidth, config.useMaxWidth);\n\n  for (const [word, packet] of words.entries()) {\n    drawWord(svg, packet, word, config);\n  }\n\n  svg\n    .append('text')\n    .text(title)\n    .attr('x', svgWidth / 2)\n    .attr('y', svgHeight - totalRowHeight / 2)\n    .attr('dominant-baseline', 'middle')\n    .attr('text-anchor', 'middle')\n    .attr('class', 'packetTitle');\n};\n\nconst drawWord = (\n  svg: SVG,\n  word: PacketWord,\n  rowNumber: number,\n  { rowHeight, paddingX, paddingY, bitWidth, bitsPerRow, showBits }: Required<PacketDiagramConfig>\n) => {\n  const group: SVGGroup = svg.append('g');\n  const wordY = rowNumber * (rowHeight + paddingY) + paddingY;\n  for (const block of word) {\n    const blockX = (block.start % bitsPerRow) * bitWidth + 1;\n    const width = (block.end - block.start + 1) * bitWidth - paddingX;\n    // Block rectangle\n    group\n      .append('rect')\n      .attr('x', blockX)\n      .attr('y', wordY)\n      .attr('width', width)\n      .attr('height', rowHeight)\n      .attr('class', 'packetBlock');\n\n    // Block label\n    group\n      .append('text')\n      .attr('x', blockX + width / 2)\n      .attr('y', wordY + rowHeight / 2)\n      .attr('class', 'packetLabel')\n      .attr('dominant-baseline', 'middle')\n      .attr('text-anchor', 'middle')\n      .text(block.label);\n\n    if (!showBits) {\n      continue;\n    }\n    // Start byte count\n    const isSingleBlock = block.end === block.start;\n    const bitNumberY = wordY - 2;\n    group\n      .append('text')\n      .attr('x', blockX + (isSingleBlock ? width / 2 : 0))\n      .attr('y', bitNumberY)\n      .attr('class', 'packetByte start')\n      .attr('dominant-baseline', 'auto')\n      .attr('text-anchor', isSingleBlock ? 'middle' : 'start')\n      .text(block.start);\n\n    // Draw end byte count if it is not the same as start byte count\n    if (!isSingleBlock) {\n      group\n        .append('text')\n        .attr('x', blockX + width)\n        .attr('y', bitNumberY)\n        .attr('class', 'packetByte end')\n        .attr('dominant-baseline', 'auto')\n        .attr('text-anchor', 'end')\n        .text(block.end);\n    }\n  }\n};\nexport const renderer: DiagramRenderer = { draw };\n", "import type { DiagramStylesProvider } from '../../diagram-api/types.js';\nimport { cleanAndMerge } from '../../utils.js';\nimport type { PacketStyleOptions } from './types.js';\n\nconst defaultPacketStyleOptions: PacketStyleOptions = {\n  byteFontSize: '10px',\n  startByteColor: 'black',\n  endByteColor: 'black',\n  labelColor: 'black',\n  labelFontSize: '12px',\n  titleColor: 'black',\n  titleFontSize: '14px',\n  blockStrokeColor: 'black',\n  blockStrokeWidth: '1',\n  blockFillColor: '#efefef',\n};\n\nexport const styles: DiagramStylesProvider = ({ packet }: { packet?: PacketStyleOptions } = {}) => {\n  const options = cleanAndMerge(defaultPacketStyleOptions, packet);\n\n  return `\n\t.packetByte {\n\t\tfont-size: ${options.byteFontSize};\n\t}\n\t.packetByte.start {\n\t\tfill: ${options.startByteColor};\n\t}\n\t.packetByte.end {\n\t\tfill: ${options.endByteColor};\n\t}\n\t.packetLabel {\n\t\tfill: ${options.labelColor};\n\t\tfont-size: ${options.labelFontSize};\n\t}\n\t.packetTitle {\n\t\tfill: ${options.titleColor};\n\t\tfont-size: ${options.titleFontSize};\n\t}\n\t.packetBlock {\n\t\tstroke: ${options.blockStrokeColor};\n\t\tstroke-width: ${options.blockStrokeWidth};\n\t\tfill: ${options.blockFillColor};\n\t}\n\t`;\n};\n\nexport default styles;\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\nimport { db } from './db.js';\nimport { parser } from './parser.js';\nimport { renderer } from './renderer.js';\nimport { styles } from './styles.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  db,\n  renderer,\n  styles,\n};\n"], "mappings": "inBAeA,IAAMA,EAAgC,CACpC,OAAQ,CAAC,CACX,EAEIC,EAAmB,gBAAgBD,CAAiB,EAElDE,EAAuDC,EAAe,OAEtEC,EAAYC,EAAA,IAAqC,CACrD,IAAMC,EAASC,EAAc,CAC3B,GAAGL,EACH,GAAGE,EAAgB,EAAE,MACvB,CAAC,EACD,OAAIE,EAAO,WACTA,EAAO,UAAY,IAEdA,CACT,EATkB,aAWZE,EAAYH,EAAA,IAAoBJ,EAAK,OAAzB,aAEZQ,EAAWJ,EAACK,GAAqB,CACjCA,EAAK,OAAS,GAChBT,EAAK,OAAO,KAAKS,CAAI,CAEzB,EAJiB,YAMXC,EAAQN,EAAA,IAAM,CAClBM,EAAY,EACZV,EAAO,gBAAgBD,CAAiB,CAC1C,EAHc,SAKDY,EAAe,CAC1B,SAAAH,EACA,UAAAD,EACA,UAAAJ,EACA,MAAAO,EACA,YAAAE,EACA,YAAAC,EACA,gBAAAC,EACA,gBAAAC,EACA,kBAAAC,EACA,kBAAAC,CACF,EClDA,IAAMC,EAAgB,IAEhBC,EAAWC,EAACC,GAAgB,CAChCC,EAAiBD,EAAKE,CAAE,EACxB,IAAIC,EAAW,GACXC,EAAmB,CAAC,EACpBC,EAAM,EACJ,CAAE,WAAAC,CAAW,EAAIJ,EAAG,UAAU,EACpC,OAAS,CAAE,MAAAK,EAAO,IAAAC,EAAK,MAAAC,CAAM,IAAKT,EAAI,OAAQ,CAC5C,GAAIQ,GAAOA,EAAMD,EACf,MAAM,IAAI,MAAM,gBAAgBA,CAAK,MAAMC,CAAG,8CAA8C,EAE9F,GAAID,IAAUJ,EAAW,EACvB,MAAM,IAAI,MACR,gBAAgBI,CAAK,MAAMC,GAAOD,CAAK,4CACrCJ,EAAW,CACb,GACF,EAKF,IAHAA,EAAWK,GAAOD,EAClBG,EAAI,MAAM,gBAAgBH,CAAK,MAAMJ,CAAQ,eAAeM,CAAK,EAAE,EAE5DL,EAAK,QAAUE,EAAa,GAAKJ,EAAG,UAAU,EAAE,OAASL,GAAe,CAC7E,GAAM,CAACc,EAAOC,CAAS,EAAIC,EAAoB,CAAE,MAAAN,EAAO,IAAAC,EAAK,MAAAC,CAAM,EAAGJ,EAAKC,CAAU,EAOrF,GANAF,EAAK,KAAKO,CAAK,EACXA,EAAM,IAAM,IAAMN,EAAMC,IAC1BJ,EAAG,SAASE,CAAI,EAChBA,EAAO,CAAC,EACRC,KAEE,CAACO,EACH,OAED,CAAE,MAAAL,EAAO,IAAAC,EAAK,MAAAC,CAAM,EAAIG,EAC3B,CACF,CACAV,EAAG,SAASE,CAAI,CAClB,EAnCiB,YAqCXS,EAAsBd,EAAA,CAC1BY,EACAN,EACAC,IACqD,CAKrD,GAJIK,EAAM,MAAQ,SAChBA,EAAM,IAAMA,EAAM,OAGhBA,EAAM,MAAQA,EAAM,IACtB,MAAM,IAAI,MAAM,eAAeA,EAAM,KAAK,8BAA8BA,EAAM,GAAG,GAAG,EAGtF,OAAIA,EAAM,IAAM,GAAKN,EAAMC,EAClB,CAACK,EAAgC,MAAS,EAG5C,CACL,CACE,MAAOA,EAAM,MACb,IAAKN,EAAMC,EAAa,EACxB,MAAOK,EAAM,KACf,EACA,CACE,MAAON,EAAMC,EACb,IAAKK,EAAM,IACX,MAAOA,EAAM,KACf,CACF,CACF,EA7B4B,uBA+BfG,EAA2B,CACtC,MAAOf,EAAA,MAAOgB,GAAiC,CAC7C,IAAMf,EAAc,MAAMgB,EAAM,SAAUD,CAAK,EAC/CL,EAAI,MAAMV,CAAG,EACbF,EAASE,CAAG,CACd,EAJO,QAKT,EC7EA,IAAMiB,EAAuBC,EAAA,CAACC,EAAOC,EAAIC,EAAUC,IAAqB,CACtE,IAAMC,EAAKD,EAAQ,GACbE,EAASD,EAAG,UAAU,EACtB,CAAE,UAAAE,EAAW,SAAAC,EAAU,SAAAC,EAAU,WAAAC,CAAW,EAAIJ,EAChDK,EAAQN,EAAG,UAAU,EACrBO,EAAQP,EAAG,gBAAgB,EAC3BQ,EAAiBN,EAAYC,EAC7BM,EAAYD,GAAkBF,EAAM,OAAS,IAAMC,EAAQ,EAAIL,GAC/DQ,EAAWN,EAAWC,EAAa,EACnCM,EAAWC,EAAiBf,CAAE,EAEpCc,EAAI,KAAK,UAAW,OAAOD,CAAQ,IAAID,CAAS,EAAE,EAClDI,EAAiBF,EAAKF,EAAWC,EAAUT,EAAO,WAAW,EAE7D,OAAW,CAACa,EAAMC,CAAM,IAAKT,EAAM,QAAQ,EACzCU,EAASL,EAAKI,EAAQD,EAAMb,CAAM,EAGpCU,EACG,OAAO,MAAM,EACb,KAAKJ,CAAK,EACV,KAAK,IAAKG,EAAW,CAAC,EACtB,KAAK,IAAKD,EAAYD,EAAiB,CAAC,EACxC,KAAK,oBAAqB,QAAQ,EAClC,KAAK,cAAe,QAAQ,EAC5B,KAAK,QAAS,aAAa,CAChC,EA1B6B,QA4BvBQ,EAAWrB,EAAA,CACfgB,EACAG,EACAG,EACA,CAAE,UAAAf,EAAW,SAAAgB,EAAU,SAAAf,EAAU,SAAAC,EAAU,WAAAC,EAAY,SAAAc,CAAS,IAC7D,CACH,IAAMC,EAAkBT,EAAI,OAAO,GAAG,EAChCU,EAAQJ,GAAaf,EAAYC,GAAYA,EACnD,QAAWmB,KAASR,EAAM,CACxB,IAAMS,EAAUD,EAAM,MAAQjB,EAAcD,EAAW,EACjDoB,GAASF,EAAM,IAAMA,EAAM,MAAQ,GAAKlB,EAAWc,EAoBzD,GAlBAE,EACG,OAAO,MAAM,EACb,KAAK,IAAKG,CAAM,EAChB,KAAK,IAAKF,CAAK,EACf,KAAK,QAASG,CAAK,EACnB,KAAK,SAAUtB,CAAS,EACxB,KAAK,QAAS,aAAa,EAG9BkB,EACG,OAAO,MAAM,EACb,KAAK,IAAKG,EAASC,EAAQ,CAAC,EAC5B,KAAK,IAAKH,EAAQnB,EAAY,CAAC,EAC/B,KAAK,QAAS,aAAa,EAC3B,KAAK,oBAAqB,QAAQ,EAClC,KAAK,cAAe,QAAQ,EAC5B,KAAKoB,EAAM,KAAK,EAEf,CAACH,EACH,SAGF,IAAMM,EAAgBH,EAAM,MAAQA,EAAM,MACpCI,EAAaL,EAAQ,EAC3BD,EACG,OAAO,MAAM,EACb,KAAK,IAAKG,GAAUE,EAAgBD,EAAQ,EAAI,EAAE,EAClD,KAAK,IAAKE,CAAU,EACpB,KAAK,QAAS,kBAAkB,EAChC,KAAK,oBAAqB,MAAM,EAChC,KAAK,cAAeD,EAAgB,SAAW,OAAO,EACtD,KAAKH,EAAM,KAAK,EAGdG,GACHL,EACG,OAAO,MAAM,EACb,KAAK,IAAKG,EAASC,CAAK,EACxB,KAAK,IAAKE,CAAU,EACpB,KAAK,QAAS,gBAAgB,EAC9B,KAAK,oBAAqB,MAAM,EAChC,KAAK,cAAe,KAAK,EACzB,KAAKJ,EAAM,GAAG,CAErB,CACF,EAzDiB,YA0DJK,EAA4B,CAAE,KAAAjC,CAAK,ECzFhD,IAAMkC,EAAgD,CACpD,aAAc,OACd,eAAgB,QAChB,aAAc,QACd,WAAY,QACZ,cAAe,OACf,WAAY,QACZ,cAAe,OACf,iBAAkB,QAClB,iBAAkB,IAClB,eAAgB,SAClB,EAEaC,EAAgCC,EAAA,CAAC,CAAE,OAAAC,CAAO,EAAqC,CAAC,IAAM,CACjG,IAAMC,EAAUC,EAAcL,EAA2BG,CAAM,EAE/D,MAAO;AAAA;AAAA,eAEMC,EAAQ,YAAY;AAAA;AAAA;AAAA,UAGzBA,EAAQ,cAAc;AAAA;AAAA;AAAA,UAGtBA,EAAQ,YAAY;AAAA;AAAA;AAAA,UAGpBA,EAAQ,UAAU;AAAA,eACbA,EAAQ,aAAa;AAAA;AAAA;AAAA,UAG1BA,EAAQ,UAAU;AAAA,eACbA,EAAQ,aAAa;AAAA;AAAA;AAAA,YAGxBA,EAAQ,gBAAgB;AAAA,kBAClBA,EAAQ,gBAAgB;AAAA,UAChCA,EAAQ,cAAc;AAAA;AAAA,EAGhC,EA3B6C,UCXtC,IAAME,GAA6B,CACxC,OAAAC,EACA,GAAAC,EACA,SAAAC,EACA,OAAAC,CACF", "names": ["defaultPacketData", "data", "DEFAULT_PACKET_CONFIG", "defaultConfig_default", "getConfig", "__name", "config", "cleanAndMerge", "getPacket", "pushWord", "word", "clear", "db", "setAccTitle", "getAccTitle", "setDiagramTitle", "getDiagramTitle", "getAccDescription", "setAccDescription", "maxPacketSize", "populate", "__name", "ast", "populateCommonDb", "db", "lastByte", "word", "row", "bitsPerRow", "start", "end", "label", "log", "block", "nextBlock", "getNextFittingBlock", "parser", "input", "parse", "draw", "__name", "_text", "id", "_version", "diagram", "db", "config", "rowHeight", "paddingY", "bitWidth", "bitsPerRow", "words", "title", "totalRowHeight", "svgHeight", "svgWidth", "svg", "selectSvgElement", "configureSvgSize", "word", "packet", "drawWord", "rowNumber", "paddingX", "showBits", "group", "wordY", "block", "blockX", "width", "isSingleBlock", "bitNumberY", "renderer", "defaultPacketStyleOptions", "styles", "__name", "packet", "options", "cleanAndMerge", "diagram", "parser", "db", "renderer", "styles"]}