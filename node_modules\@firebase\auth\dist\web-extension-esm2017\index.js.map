{"version": 3, "file": "index.js", "sources": ["../../index.web-extension.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// Core functionality shared by all clients\nexport * from './src';\n\nimport { ClientPlatform } from './src/core/util/version';\n\nimport { indexedDBLocalPersistence } from './src/platform_browser/persistence/indexed_db';\n\nimport {\n  TotpMultiFactorGenerator,\n  TotpSecret\n} from './src/mfa/assertions/totp';\nimport { FirebaseApp, getApp, _getProvider } from '@firebase/app';\nimport { Auth, connectAuthEmulator, initializeAuth } from './index.shared';\nimport { getDefaultEmulatorHost } from '@firebase/util';\nimport { registerAuth } from './src/core/auth/register';\n\n/**\n * Returns the Auth instance associated with the provided {@link @firebase/app#FirebaseApp}.\n * If no instance exists, initializes an Auth instance with platform-specific default dependencies.\n *\n * @param app - The Firebase App.\n *\n * @public\n */\nfunction getAuth(app: FirebaseApp = getApp()): Auth {\n  const provider = _getProvider(app, 'auth');\n\n  if (provider.isInitialized()) {\n    return provider.getImmediate();\n  }\n\n  const auth = initializeAuth(app, {\n    persistence: [indexedDBLocalPersistence]\n  });\n\n  const authEmulatorHost = getDefaultEmulatorHost('auth');\n  if (authEmulatorHost) {\n    connectAuthEmulator(auth, `http://${authEmulatorHost}`);\n  }\n\n  return auth;\n}\n\nregisterAuth(ClientPlatform.WEB_EXTENSION);\n\nexport {\n  indexedDBLocalPersistence,\n  TotpMultiFactorGenerator,\n  TotpSecret,\n  getAuth\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAkBH;;;;;;;AAOG;AACH,SAAS,OAAO,CAAC,GAAmB,GAAA,MAAM,EAAE,EAAA;IAC1C,MAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAE3C,IAAA,IAAI,QAAQ,CAAC,aAAa,EAAE,EAAE;AAC5B,QAAA,OAAO,QAAQ,CAAC,YAAY,EAAE,CAAC;KAChC;AAED,IAAA,MAAM,IAAI,GAAG,cAAc,CAAC,GAAG,EAAE;QAC/B,WAAW,EAAE,CAAC,yBAAyB,CAAC;AACzC,KAAA,CAAC,CAAC;AAEH,IAAA,MAAM,gBAAgB,GAAG,sBAAsB,CAAC,MAAM,CAAC,CAAC;IACxD,IAAI,gBAAgB,EAAE;AACpB,QAAA,mBAAmB,CAAC,IAAI,EAAE,UAAU,gBAAgB,CAAA,CAAE,CAAC,CAAC;KACzD;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED,YAAY,mDAA8B;;;;"}