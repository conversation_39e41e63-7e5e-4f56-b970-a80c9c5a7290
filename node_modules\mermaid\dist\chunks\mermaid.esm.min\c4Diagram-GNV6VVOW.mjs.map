{"version": 3, "sources": ["../../../src/diagrams/c4/parser/c4Diagram.jison", "../../../src/diagrams/c4/c4Db.js", "../../../src/diagrams/c4/svgDraw.js", "../../../src/diagrams/c4/c4Renderer.js", "../../../src/diagrams/c4/styles.js", "../../../src/diagrams/c4/c4Diagram.ts"], "sourcesContent": ["/* parser generated by jison 0.4.18 */\n/*\n  Returns a Parser object of the following structure:\n\n  Parser: {\n    yy: {}\n  }\n\n  Parser.prototype: {\n    yy: {},\n    trace: function(),\n    symbols_: {associative list: name ==> number},\n    terminals_: {associative list: number ==> name},\n    productions_: [...],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),\n    table: [...],\n    defaultActions: {...},\n    parseError: function(str, hash),\n    parse: function(input),\n\n    lexer: {\n        EOF: 1,\n        parseError: function(str, hash),\n        setInput: function(input),\n        input: function(),\n        unput: function(str),\n        more: function(),\n        less: function(n),\n        pastInput: function(),\n        upcomingInput: function(),\n        showPosition: function(),\n        test_match: function(regex_match_array, rule_index),\n        next: function(),\n        lex: function(),\n        begin: function(condition),\n        popState: function(),\n        _currentRules: function(),\n        topState: function(),\n        pushState: function(condition),\n\n        options: {\n            ranges: boolean           (optional: true ==> token location info will include a .range[] member)\n            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)\n            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)\n        },\n\n        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),\n        rules: [...],\n        conditions: {associative list: name ==> set},\n    }\n  }\n\n\n  token location info (@$, _$, etc.): {\n    first_line: n,\n    last_line: n,\n    first_column: n,\n    last_column: n,\n    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)\n  }\n\n\n  the parseError function receives a 'hash' object with these members for lexer and parser errors: {\n    text:        (matched text)\n    token:       (the produced terminal token, if any)\n    line:        (yylineno)\n  }\n  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {\n    loc:         (yylloc)\n    expected:    (string describing the set of expected tokens)\n    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)\n  }\n*/\nvar parser = (function(){\nvar o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[1,24],$V1=[1,25],$V2=[1,26],$V3=[1,27],$V4=[1,28],$V5=[1,63],$V6=[1,64],$V7=[1,65],$V8=[1,66],$V9=[1,67],$Va=[1,68],$Vb=[1,69],$Vc=[1,29],$Vd=[1,30],$Ve=[1,31],$Vf=[1,32],$Vg=[1,33],$Vh=[1,34],$Vi=[1,35],$Vj=[1,36],$Vk=[1,37],$Vl=[1,38],$Vm=[1,39],$Vn=[1,40],$Vo=[1,41],$Vp=[1,42],$Vq=[1,43],$Vr=[1,44],$Vs=[1,45],$Vt=[1,46],$Vu=[1,47],$Vv=[1,48],$Vw=[1,50],$Vx=[1,51],$Vy=[1,52],$Vz=[1,53],$VA=[1,54],$VB=[1,55],$VC=[1,56],$VD=[1,57],$VE=[1,58],$VF=[1,59],$VG=[1,60],$VH=[14,42],$VI=[14,34,36,37,38,39,40,41,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74],$VJ=[12,14,34,36,37,38,39,40,41,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74],$VK=[1,82],$VL=[1,83],$VM=[1,84],$VN=[1,85],$VO=[12,14,42],$VP=[12,14,33,42],$VQ=[12,14,33,42,76,77,79,80],$VR=[12,33],$VS=[34,36,37,38,39,40,41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74];\nvar parser = {trace: function trace () { },\nyy: {},\nsymbols_: {\"error\":2,\"start\":3,\"mermaidDoc\":4,\"direction\":5,\"direction_tb\":6,\"direction_bt\":7,\"direction_rl\":8,\"direction_lr\":9,\"graphConfig\":10,\"C4_CONTEXT\":11,\"NEWLINE\":12,\"statements\":13,\"EOF\":14,\"C4_CONTAINER\":15,\"C4_COMPONENT\":16,\"C4_DYNAMIC\":17,\"C4_DEPLOYMENT\":18,\"otherStatements\":19,\"diagramStatements\":20,\"otherStatement\":21,\"title\":22,\"accDescription\":23,\"acc_title\":24,\"acc_title_value\":25,\"acc_descr\":26,\"acc_descr_value\":27,\"acc_descr_multiline_value\":28,\"boundaryStatement\":29,\"boundaryStartStatement\":30,\"boundaryStopStatement\":31,\"boundaryStart\":32,\"LBRACE\":33,\"ENTERPRISE_BOUNDARY\":34,\"attributes\":35,\"SYSTEM_BOUNDARY\":36,\"BOUNDARY\":37,\"CONTAINER_BOUNDARY\":38,\"NODE\":39,\"NODE_L\":40,\"NODE_R\":41,\"RBRACE\":42,\"diagramStatement\":43,\"PERSON\":44,\"PERSON_EXT\":45,\"SYSTEM\":46,\"SYSTEM_DB\":47,\"SYSTEM_QUEUE\":48,\"SYSTEM_EXT\":49,\"SYSTEM_EXT_DB\":50,\"SYSTEM_EXT_QUEUE\":51,\"CONTAINER\":52,\"CONTAINER_DB\":53,\"CONTAINER_QUEUE\":54,\"CONTAINER_EXT\":55,\"CONTAINER_EXT_DB\":56,\"CONTAINER_EXT_QUEUE\":57,\"COMPONENT\":58,\"COMPONENT_DB\":59,\"COMPONENT_QUEUE\":60,\"COMPONENT_EXT\":61,\"COMPONENT_EXT_DB\":62,\"COMPONENT_EXT_QUEUE\":63,\"REL\":64,\"BIREL\":65,\"REL_U\":66,\"REL_D\":67,\"REL_L\":68,\"REL_R\":69,\"REL_B\":70,\"REL_INDEX\":71,\"UPDATE_EL_STYLE\":72,\"UPDATE_REL_STYLE\":73,\"UPDATE_LAYOUT_CONFIG\":74,\"attribute\":75,\"STR\":76,\"STR_KEY\":77,\"STR_VALUE\":78,\"ATTRIBUTE\":79,\"ATTRIBUTE_EMPTY\":80,\"$accept\":0,\"$end\":1},\nterminals_: {2:\"error\",6:\"direction_tb\",7:\"direction_bt\",8:\"direction_rl\",9:\"direction_lr\",11:\"C4_CONTEXT\",12:\"NEWLINE\",14:\"EOF\",15:\"C4_CONTAINER\",16:\"C4_COMPONENT\",17:\"C4_DYNAMIC\",18:\"C4_DEPLOYMENT\",22:\"title\",23:\"accDescription\",24:\"acc_title\",25:\"acc_title_value\",26:\"acc_descr\",27:\"acc_descr_value\",28:\"acc_descr_multiline_value\",33:\"LBRACE\",34:\"ENTERPRISE_BOUNDARY\",36:\"SYSTEM_BOUNDARY\",37:\"BOUNDARY\",38:\"CONTAINER_BOUNDARY\",39:\"NODE\",40:\"NODE_L\",41:\"NODE_R\",42:\"RBRACE\",44:\"PERSON\",45:\"PERSON_EXT\",46:\"SYSTEM\",47:\"SYSTEM_DB\",48:\"SYSTEM_QUEUE\",49:\"SYSTEM_EXT\",50:\"SYSTEM_EXT_DB\",51:\"SYSTEM_EXT_QUEUE\",52:\"CONTAINER\",53:\"CONTAINER_DB\",54:\"CONTAINER_QUEUE\",55:\"CONTAINER_EXT\",56:\"CONTAINER_EXT_DB\",57:\"CONTAINER_EXT_QUEUE\",58:\"COMPONENT\",59:\"COMPONENT_DB\",60:\"COMPONENT_QUEUE\",61:\"COMPONENT_EXT\",62:\"COMPONENT_EXT_DB\",63:\"COMPONENT_EXT_QUEUE\",64:\"REL\",65:\"BIREL\",66:\"REL_U\",67:\"REL_D\",68:\"REL_L\",69:\"REL_R\",70:\"REL_B\",71:\"REL_INDEX\",72:\"UPDATE_EL_STYLE\",73:\"UPDATE_REL_STYLE\",74:\"UPDATE_LAYOUT_CONFIG\",76:\"STR\",77:\"STR_KEY\",78:\"STR_VALUE\",79:\"ATTRIBUTE\",80:\"ATTRIBUTE_EMPTY\"},\nproductions_: [0,[3,1],[3,1],[5,1],[5,1],[5,1],[5,1],[4,1],[10,4],[10,4],[10,4],[10,4],[10,4],[13,1],[13,1],[13,2],[19,1],[19,2],[19,3],[21,1],[21,1],[21,2],[21,2],[21,1],[29,3],[30,3],[30,3],[30,4],[32,2],[32,2],[32,2],[32,2],[32,2],[32,2],[32,2],[31,1],[20,1],[20,2],[20,3],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,1],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[35,1],[35,2],[75,1],[75,2],[75,1],[75,1]],\nperformAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {\n/* this == yyval */\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 3:\n yy.setDirection('TB');\nbreak;\ncase 4:\n yy.setDirection('BT');\nbreak;\ncase 5:\n yy.setDirection('RL');\nbreak;\ncase 6:\n yy.setDirection('LR');\nbreak;\ncase 8: case 9: case 10: case 11: case 12:\nyy.setC4Type($$[$0-3])\nbreak;\ncase 19:\nyy.setTitle($$[$0].substring(6));this.$=$$[$0].substring(6);\nbreak;\ncase 20:\nyy.setAccDescription($$[$0].substring(15));this.$=$$[$0].substring(15);\nbreak;\ncase 21:\n this.$=$$[$0].trim();yy.setTitle(this.$); \nbreak;\ncase 22: case 23:\n this.$=$$[$0].trim();yy.setAccDescription(this.$); \nbreak;\ncase 28:\n$$[$0].splice(2, 0, 'ENTERPRISE'); yy.addPersonOrSystemBoundary(...$$[$0]); this.$=$$[$0];\nbreak;\ncase 29:\n$$[$0].splice(2, 0, 'SYSTEM'); yy.addPersonOrSystemBoundary(...$$[$0]); this.$=$$[$0];\nbreak;\ncase 30:\nyy.addPersonOrSystemBoundary(...$$[$0]); this.$=$$[$0];\nbreak;\ncase 31:\n$$[$0].splice(2, 0, 'CONTAINER'); yy.addContainerBoundary(...$$[$0]); this.$=$$[$0];\nbreak;\ncase 32:\nyy.addDeploymentNode('node', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 33:\nyy.addDeploymentNode('nodeL', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 34:\nyy.addDeploymentNode('nodeR', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 35:\n yy.popBoundaryParseStack() \nbreak;\ncase 39:\nyy.addPersonOrSystem('person', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 40:\nyy.addPersonOrSystem('external_person', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 41:\nyy.addPersonOrSystem('system', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 42:\nyy.addPersonOrSystem('system_db', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 43:\nyy.addPersonOrSystem('system_queue', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 44:\nyy.addPersonOrSystem('external_system', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 45:\nyy.addPersonOrSystem('external_system_db', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 46:\nyy.addPersonOrSystem('external_system_queue', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 47:\nyy.addContainer('container', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 48:\nyy.addContainer('container_db', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 49:\nyy.addContainer('container_queue', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 50:\nyy.addContainer('external_container', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 51:\nyy.addContainer('external_container_db', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 52:\nyy.addContainer('external_container_queue', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 53:\nyy.addComponent('component', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 54:\nyy.addComponent('component_db', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 55:\nyy.addComponent('component_queue', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 56:\nyy.addComponent('external_component', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 57:\nyy.addComponent('external_component_db', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 58:\nyy.addComponent('external_component_queue', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 60:\nyy.addRel('rel', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 61:\nyy.addRel('birel', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 62:\nyy.addRel('rel_u', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 63:\nyy.addRel('rel_d', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 64:\nyy.addRel('rel_l', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 65:\nyy.addRel('rel_r', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 66:\nyy.addRel('rel_b', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 67:\n$$[$0].splice(0, 1); yy.addRel('rel', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 68:\nyy.updateElStyle('update_el_style', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 69:\nyy.updateRelStyle('update_rel_style', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 70:\nyy.updateLayoutConfig('update_layout_config', ...$$[$0]); this.$=$$[$0];\nbreak;\ncase 71:\n this.$ = [$$[$0]]; \nbreak;\ncase 72:\n $$[$0].unshift($$[$0-1]); this.$=$$[$0];\nbreak;\ncase 73: case 75:\n  this.$ = $$[$0].trim(); \nbreak;\ncase 74:\n let kv={}; kv[$$[$0-1].trim()]=$$[$0].trim(); this.$=kv; \nbreak;\ncase 76:\n  this.$ = \"\"; \nbreak;\n}\n},\ntable: [{3:1,4:2,5:3,6:[1,5],7:[1,6],8:[1,7],9:[1,8],10:4,11:[1,9],15:[1,10],16:[1,11],17:[1,12],18:[1,13]},{1:[3]},{1:[2,1]},{1:[2,2]},{1:[2,7]},{1:[2,3]},{1:[2,4]},{1:[2,5]},{1:[2,6]},{12:[1,14]},{12:[1,15]},{12:[1,16]},{12:[1,17]},{12:[1,18]},{13:19,19:20,20:21,21:22,22:$V0,23:$V1,24:$V2,26:$V3,28:$V4,29:49,30:61,32:62,34:$V5,36:$V6,37:$V7,38:$V8,39:$V9,40:$Va,41:$Vb,43:23,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi,51:$Vj,52:$Vk,53:$Vl,54:$Vm,55:$Vn,56:$Vo,57:$Vp,58:$Vq,59:$Vr,60:$Vs,61:$Vt,62:$Vu,63:$Vv,64:$Vw,65:$Vx,66:$Vy,67:$Vz,68:$VA,69:$VB,70:$VC,71:$VD,72:$VE,73:$VF,74:$VG},{13:70,19:20,20:21,21:22,22:$V0,23:$V1,24:$V2,26:$V3,28:$V4,29:49,30:61,32:62,34:$V5,36:$V6,37:$V7,38:$V8,39:$V9,40:$Va,41:$Vb,43:23,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi,51:$Vj,52:$Vk,53:$Vl,54:$Vm,55:$Vn,56:$Vo,57:$Vp,58:$Vq,59:$Vr,60:$Vs,61:$Vt,62:$Vu,63:$Vv,64:$Vw,65:$Vx,66:$Vy,67:$Vz,68:$VA,69:$VB,70:$VC,71:$VD,72:$VE,73:$VF,74:$VG},{13:71,19:20,20:21,21:22,22:$V0,23:$V1,24:$V2,26:$V3,28:$V4,29:49,30:61,32:62,34:$V5,36:$V6,37:$V7,38:$V8,39:$V9,40:$Va,41:$Vb,43:23,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi,51:$Vj,52:$Vk,53:$Vl,54:$Vm,55:$Vn,56:$Vo,57:$Vp,58:$Vq,59:$Vr,60:$Vs,61:$Vt,62:$Vu,63:$Vv,64:$Vw,65:$Vx,66:$Vy,67:$Vz,68:$VA,69:$VB,70:$VC,71:$VD,72:$VE,73:$VF,74:$VG},{13:72,19:20,20:21,21:22,22:$V0,23:$V1,24:$V2,26:$V3,28:$V4,29:49,30:61,32:62,34:$V5,36:$V6,37:$V7,38:$V8,39:$V9,40:$Va,41:$Vb,43:23,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi,51:$Vj,52:$Vk,53:$Vl,54:$Vm,55:$Vn,56:$Vo,57:$Vp,58:$Vq,59:$Vr,60:$Vs,61:$Vt,62:$Vu,63:$Vv,64:$Vw,65:$Vx,66:$Vy,67:$Vz,68:$VA,69:$VB,70:$VC,71:$VD,72:$VE,73:$VF,74:$VG},{13:73,19:20,20:21,21:22,22:$V0,23:$V1,24:$V2,26:$V3,28:$V4,29:49,30:61,32:62,34:$V5,36:$V6,37:$V7,38:$V8,39:$V9,40:$Va,41:$Vb,43:23,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi,51:$Vj,52:$Vk,53:$Vl,54:$Vm,55:$Vn,56:$Vo,57:$Vp,58:$Vq,59:$Vr,60:$Vs,61:$Vt,62:$Vu,63:$Vv,64:$Vw,65:$Vx,66:$Vy,67:$Vz,68:$VA,69:$VB,70:$VC,71:$VD,72:$VE,73:$VF,74:$VG},{14:[1,74]},o($VH,[2,13],{43:23,29:49,30:61,32:62,20:75,34:$V5,36:$V6,37:$V7,38:$V8,39:$V9,40:$Va,41:$Vb,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi,51:$Vj,52:$Vk,53:$Vl,54:$Vm,55:$Vn,56:$Vo,57:$Vp,58:$Vq,59:$Vr,60:$Vs,61:$Vt,62:$Vu,63:$Vv,64:$Vw,65:$Vx,66:$Vy,67:$Vz,68:$VA,69:$VB,70:$VC,71:$VD,72:$VE,73:$VF,74:$VG}),o($VH,[2,14]),o($VI,[2,16],{12:[1,76]}),o($VH,[2,36],{12:[1,77]}),o($VJ,[2,19]),o($VJ,[2,20]),{25:[1,78]},{27:[1,79]},o($VJ,[2,23]),{35:80,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:86,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:87,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:88,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:89,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:90,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:91,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:92,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:93,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:94,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:95,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:96,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:97,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:98,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:99,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:100,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:101,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:102,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:103,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:104,75:81,76:$VK,77:$VL,79:$VM,80:$VN},o($VO,[2,59]),{35:105,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:106,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:107,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:108,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:109,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:110,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:111,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:112,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:113,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:114,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:115,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{20:116,29:49,30:61,32:62,34:$V5,36:$V6,37:$V7,38:$V8,39:$V9,40:$Va,41:$Vb,43:23,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi,51:$Vj,52:$Vk,53:$Vl,54:$Vm,55:$Vn,56:$Vo,57:$Vp,58:$Vq,59:$Vr,60:$Vs,61:$Vt,62:$Vu,63:$Vv,64:$Vw,65:$Vx,66:$Vy,67:$Vz,68:$VA,69:$VB,70:$VC,71:$VD,72:$VE,73:$VF,74:$VG},{12:[1,118],33:[1,117]},{35:119,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:120,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:121,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:122,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:123,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:124,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{35:125,75:81,76:$VK,77:$VL,79:$VM,80:$VN},{14:[1,126]},{14:[1,127]},{14:[1,128]},{14:[1,129]},{1:[2,8]},o($VH,[2,15]),o($VI,[2,17],{21:22,19:130,22:$V0,23:$V1,24:$V2,26:$V3,28:$V4}),o($VH,[2,37],{19:20,20:21,21:22,43:23,29:49,30:61,32:62,13:131,22:$V0,23:$V1,24:$V2,26:$V3,28:$V4,34:$V5,36:$V6,37:$V7,38:$V8,39:$V9,40:$Va,41:$Vb,44:$Vc,45:$Vd,46:$Ve,47:$Vf,48:$Vg,49:$Vh,50:$Vi,51:$Vj,52:$Vk,53:$Vl,54:$Vm,55:$Vn,56:$Vo,57:$Vp,58:$Vq,59:$Vr,60:$Vs,61:$Vt,62:$Vu,63:$Vv,64:$Vw,65:$Vx,66:$Vy,67:$Vz,68:$VA,69:$VB,70:$VC,71:$VD,72:$VE,73:$VF,74:$VG}),o($VJ,[2,21]),o($VJ,[2,22]),o($VO,[2,39]),o($VP,[2,71],{75:81,35:132,76:$VK,77:$VL,79:$VM,80:$VN}),o($VQ,[2,73]),{78:[1,133]},o($VQ,[2,75]),o($VQ,[2,76]),o($VO,[2,40]),o($VO,[2,41]),o($VO,[2,42]),o($VO,[2,43]),o($VO,[2,44]),o($VO,[2,45]),o($VO,[2,46]),o($VO,[2,47]),o($VO,[2,48]),o($VO,[2,49]),o($VO,[2,50]),o($VO,[2,51]),o($VO,[2,52]),o($VO,[2,53]),o($VO,[2,54]),o($VO,[2,55]),o($VO,[2,56]),o($VO,[2,57]),o($VO,[2,58]),o($VO,[2,60]),o($VO,[2,61]),o($VO,[2,62]),o($VO,[2,63]),o($VO,[2,64]),o($VO,[2,65]),o($VO,[2,66]),o($VO,[2,67]),o($VO,[2,68]),o($VO,[2,69]),o($VO,[2,70]),{31:134,42:[1,135]},{12:[1,136]},{33:[1,137]},o($VR,[2,28]),o($VR,[2,29]),o($VR,[2,30]),o($VR,[2,31]),o($VR,[2,32]),o($VR,[2,33]),o($VR,[2,34]),{1:[2,9]},{1:[2,10]},{1:[2,11]},{1:[2,12]},o($VI,[2,18]),o($VH,[2,38]),o($VP,[2,72]),o($VQ,[2,74]),o($VO,[2,24]),o($VO,[2,35]),o($VS,[2,25]),o($VS,[2,26],{12:[1,138]}),o($VS,[2,27])],\ndefaultActions: {2:[2,1],3:[2,2],4:[2,7],5:[2,3],6:[2,4],7:[2,5],8:[2,6],74:[2,8],126:[2,9],127:[2,10],128:[2,11],129:[2,12]},\nparseError: function parseError (str, hash) {\n    if (hash.recoverable) {\n        this.trace(str);\n    } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n    }\n},\nparse: function parse(input) {\n    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n    var args = lstack.slice.call(arguments, 1);\n    var lexer = Object.create(this.lexer);\n    var sharedState = { yy: {} };\n    for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n            sharedState.yy[k] = this.yy[k];\n        }\n    }\n    lexer.setInput(input, sharedState.yy);\n    sharedState.yy.lexer = lexer;\n    sharedState.yy.parser = this;\n    if (typeof lexer.yylloc == 'undefined') {\n        lexer.yylloc = {};\n    }\n    var yyloc = lexer.yylloc;\n    lstack.push(yyloc);\n    var ranges = lexer.options && lexer.options.ranges;\n    if (typeof sharedState.yy.parseError === 'function') {\n        this.parseError = sharedState.yy.parseError;\n    } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n    }\n    function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n            function lex() {\n            var token;\n            token = tstack.pop() || lexer.lex() || EOF;\n            if (typeof token !== 'number') {\n                if (token instanceof Array) {\n                    tstack = token;\n                    token = tstack.pop();\n                }\n                token = self.symbols_[token] || token;\n            }\n            return token;\n        }\n    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n    while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol === null || typeof symbol == 'undefined') {\n                symbol = lex();\n            }\n            action = table[state] && table[state][symbol];\n        }\n        if (typeof action === 'undefined' || !action.length || !action[0]) {\n            var errStr = '';\n            expected = [];\n            for (p in table[state]) {\n                if (this.terminals_[p] && p > TERROR) {\n                    expected.push('\\'' + this.terminals_[p] + '\\'');\n                }\n            }\n            if (lexer.showPosition) {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ':\\n' + lexer.showPosition() + '\\nExpecting ' + expected.join(', ') + ', got \\'' + (this.terminals_[symbol] || symbol) + '\\'';\n            } else {\n                errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\\'' + (this.terminals_[symbol] || symbol) + '\\'');\n            }\n            this.parseError(errStr, {\n                text: lexer.match,\n                token: this.terminals_[symbol] || symbol,\n                line: lexer.yylineno,\n                loc: yyloc,\n                expected: expected\n            });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);\n        }\n        switch (action[0]) {\n        case 1:\n            stack.push(symbol);\n            vstack.push(lexer.yytext);\n            lstack.push(lexer.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n                yyleng = lexer.yyleng;\n                yytext = lexer.yytext;\n                yylineno = lexer.yylineno;\n                yyloc = lexer.yylloc;\n                if (recovering > 0) {\n                    recovering--;\n                }\n            } else {\n                symbol = preErrorSymbol;\n                preErrorSymbol = null;\n            }\n            break;\n        case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n                first_line: lstack[lstack.length - (len || 1)].first_line,\n                last_line: lstack[lstack.length - 1].last_line,\n                first_column: lstack[lstack.length - (len || 1)].first_column,\n                last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n                yyval._$.range = [\n                    lstack[lstack.length - (len || 1)].range[0],\n                    lstack[lstack.length - 1].range[1]\n                ];\n            }\n            r = this.performAction.apply(yyval, [\n                yytext,\n                yyleng,\n                yylineno,\n                sharedState.yy,\n                action[1],\n                vstack,\n                lstack\n            ].concat(args));\n            if (typeof r !== 'undefined') {\n                return r;\n            }\n            if (len) {\n                stack = stack.slice(0, -1 * len * 2);\n                vstack = vstack.slice(0, -1 * len);\n                lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n        case 3:\n            return true;\n        }\n    }\n    return true;\n}};\n/* generated by jison-lex 0.3.4 */\nvar lexer = (function(){\nvar lexer = ({\n\nEOF:1,\n\nparseError:function parseError(str, hash) {\n        if (this.yy.parser) {\n            this.yy.parser.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\n\n// resets the lexer, sets new input\nsetInput:function (input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = '';\n        this.conditionStack = ['INITIAL'];\n        this.yylloc = {\n            first_line: 1,\n            first_column: 0,\n            last_line: 1,\n            last_column: 0\n        };\n        if (this.options.ranges) {\n            this.yylloc.range = [0,0];\n        }\n        this.offset = 0;\n        return this;\n    },\n\n// consumes and returns one char from the input\ninput:function () {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno++;\n            this.yylloc.last_line++;\n        } else {\n            this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n            this.yylloc.range[1]++;\n        }\n\n        this._input = this._input.slice(1);\n        return ch;\n    },\n\n// unshifts one char (or a string) into the input\nunput:function (ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        //this.yyleng -= len;\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n\n        if (lines.length - 1) {\n            this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n\n        this.yylloc = {\n            first_line: this.yylloc.first_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.first_column,\n            last_column: lines ?\n                (lines.length === oldLines.length ? this.yylloc.first_column : 0)\n                 + oldLines[oldLines.length - lines.length].length - lines[0].length :\n              this.yylloc.first_column - len\n        };\n\n        if (this.options.ranges) {\n            this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n    },\n\n// When called from action, caches matched text and appends it on next action\nmore:function () {\n        this._more = true;\n        return this;\n    },\n\n// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\nreject:function () {\n        if (this.options.backtrack_lexer) {\n            this._backtrack = true;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n\n        }\n        return this;\n    },\n\n// retain first n characters of the match\nless:function (n) {\n        this.unput(this.match.slice(n));\n    },\n\n// displays already matched input, i.e. for error messages\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\\n/g, \"\");\n    },\n\n// displays upcoming input, i.e. for error messages\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\\n/g, \"\");\n    },\n\n// displays the character position where the lexing error occurred, i.e. for error messages\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n    },\n\n// test the lexed token: return FALSE when not a match, otherwise return token\ntest_match:function(match, indexed_rule) {\n        var token,\n            lines,\n            backup;\n\n        if (this.options.backtrack_lexer) {\n            // save context\n            backup = {\n                yylineno: this.yylineno,\n                yylloc: {\n                    first_line: this.yylloc.first_line,\n                    last_line: this.last_line,\n                    first_column: this.yylloc.first_column,\n                    last_column: this.yylloc.last_column\n                },\n                yytext: this.yytext,\n                match: this.match,\n                matches: this.matches,\n                matched: this.matched,\n                yyleng: this.yyleng,\n                offset: this.offset,\n                _more: this._more,\n                _input: this._input,\n                yy: this.yy,\n                conditionStack: this.conditionStack.slice(0),\n                done: this.done\n            };\n            if (this.options.ranges) {\n                backup.yylloc.range = this.yylloc.range.slice(0);\n            }\n        }\n\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n            this.yylineno += lines.length;\n        }\n        this.yylloc = {\n            first_line: this.yylloc.last_line,\n            last_line: this.yylineno + 1,\n            first_column: this.yylloc.last_column,\n            last_column: lines ?\n                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length :\n                         this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n            this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n            this.done = false;\n        }\n        if (token) {\n            return token;\n        } else if (this._backtrack) {\n            // recover context\n            for (var k in backup) {\n                this[k] = backup[k];\n            }\n            return false; // rule action called reject() implying the next rule should be tested instead.\n        }\n        return false;\n    },\n\n// return next match in input\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) {\n            this.done = true;\n        }\n\n        var token,\n            match,\n            tempMatch,\n            index;\n        if (!this._more) {\n            this.yytext = '';\n            this.match = '';\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (this.options.backtrack_lexer) {\n                    token = this.test_match(tempMatch, rules[i]);\n                    if (token !== false) {\n                        return token;\n                    } else if (this._backtrack) {\n                        match = false;\n                        continue; // rule action called reject() implying a rule MISmatch.\n                    } else {\n                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n                        return false;\n                    }\n                } else if (!this.options.flex) {\n                    break;\n                }\n            }\n        }\n        if (match) {\n            token = this.test_match(match, rules[index]);\n            if (token !== false) {\n                return token;\n            }\n            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)\n            return false;\n        }\n        if (this._input === \"\") {\n            return this.EOF;\n        } else {\n            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\\n' + this.showPosition(), {\n                text: \"\",\n                token: null,\n                line: this.yylineno\n            });\n        }\n    },\n\n// return next match that has a token\nlex:function lex () {\n        var r = this.next();\n        if (r) {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\n\n// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\nbegin:function begin (condition) {\n        this.conditionStack.push(condition);\n    },\n\n// pop the previously active lexer condition state off the condition stack\npopState:function popState () {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n            return this.conditionStack.pop();\n        } else {\n            return this.conditionStack[0];\n        }\n    },\n\n// produce the lexer rule set which is active for the currently active lexer condition state\n_currentRules:function _currentRules () {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n            return this.conditions[\"INITIAL\"].rules;\n        }\n    },\n\n// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\ntopState:function topState (n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n            return this.conditionStack[n];\n        } else {\n            return \"INITIAL\";\n        }\n    },\n\n// alias for begin(condition)\npushState:function pushState (condition) {\n        this.begin(condition);\n    },\n\n// return the number of states currently on the stack\nstateStackSize:function stateStackSize() {\n        return this.conditionStack.length;\n    },\noptions: {},\nperformAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\nvar YYSTATE=YY_START;\nswitch($avoiding_name_collisions) {\ncase 0:return 6;\nbreak;\ncase 1:return 7;\nbreak;\ncase 2:return 8;\nbreak;\ncase 3:return 9;\nbreak;\ncase 4:return 22;\nbreak;\ncase 5:return 23;\nbreak;\ncase 6: this.begin(\"acc_title\");return 24; \nbreak;\ncase 7: this.popState(); return \"acc_title_value\"; \nbreak;\ncase 8: this.begin(\"acc_descr\");return 26; \nbreak;\ncase 9: this.popState(); return \"acc_descr_value\"; \nbreak;\ncase 10: this.begin(\"acc_descr_multiline\");\nbreak;\ncase 11: this.popState(); \nbreak;\ncase 12:return \"acc_descr_multiline_value\";\nbreak;\ncase 13:/* skip comments */\nbreak;\ncase 14:c            /* skip comments */\nbreak;\ncase 15:return 12;\nbreak;\ncase 16:/* skip whitespace */\nbreak;\ncase 17:return 11;\nbreak;\ncase 18:return 15;\nbreak;\ncase 19:return 16;\nbreak;\ncase 20:return 17;\nbreak;\ncase 21:return 18;\nbreak;\ncase 22: this.begin(\"person_ext\"); return 45;\nbreak;\ncase 23: this.begin(\"person\"); return 44;\nbreak;\ncase 24: this.begin(\"system_ext_queue\"); return 51;\nbreak;\ncase 25: this.begin(\"system_ext_db\"); return 50;\nbreak;\ncase 26: this.begin(\"system_ext\"); return 49;\nbreak;\ncase 27: this.begin(\"system_queue\"); return 48;\nbreak;\ncase 28: this.begin(\"system_db\"); return 47;\nbreak;\ncase 29: this.begin(\"system\"); return 46;\nbreak;\ncase 30: this.begin(\"boundary\"); return 37;\nbreak;\ncase 31: this.begin(\"enterprise_boundary\"); return 34;\nbreak;\ncase 32: this.begin(\"system_boundary\"); return 36;\nbreak;\ncase 33: this.begin(\"container_ext_queue\"); return 57;\nbreak;\ncase 34: this.begin(\"container_ext_db\"); return 56;\nbreak;\ncase 35: this.begin(\"container_ext\"); return 55;\nbreak;\ncase 36: this.begin(\"container_queue\"); return 54;\nbreak;\ncase 37: this.begin(\"container_db\"); return 53;\nbreak;\ncase 38: this.begin(\"container\"); return 52;\nbreak;\ncase 39: this.begin(\"container_boundary\"); return 38;\nbreak;\ncase 40: this.begin(\"component_ext_queue\"); return 63;\nbreak;\ncase 41: this.begin(\"component_ext_db\"); return 62;\nbreak;\ncase 42: this.begin(\"component_ext\"); return 61;\nbreak;\ncase 43: this.begin(\"component_queue\"); return 60;\nbreak;\ncase 44: this.begin(\"component_db\"); return 59;\nbreak;\ncase 45: this.begin(\"component\"); return 58;\nbreak;\ncase 46: this.begin(\"node\"); return 39;\nbreak;\ncase 47: this.begin(\"node\"); return 39;\nbreak;\ncase 48: this.begin(\"node_l\"); return 40;\nbreak;\ncase 49: this.begin(\"node_r\"); return 41;\nbreak;\ncase 50: this.begin(\"rel\"); return 64;\nbreak;\ncase 51: this.begin(\"birel\"); return 65;\nbreak;\ncase 52: this.begin(\"rel_u\"); return 66;\nbreak;\ncase 53: this.begin(\"rel_u\"); return 66;\nbreak;\ncase 54: this.begin(\"rel_d\"); return 67;\nbreak;\ncase 55: this.begin(\"rel_d\"); return 67;\nbreak;\ncase 56: this.begin(\"rel_l\"); return 68;\nbreak;\ncase 57: this.begin(\"rel_l\"); return 68;\nbreak;\ncase 58: this.begin(\"rel_r\"); return 69;\nbreak;\ncase 59: this.begin(\"rel_r\"); return 69;\nbreak;\ncase 60: this.begin(\"rel_b\"); return 70;\nbreak;\ncase 61: this.begin(\"rel_index\"); return 71;\nbreak;\ncase 62: this.begin(\"update_el_style\"); return 72;\nbreak;\ncase 63: this.begin(\"update_rel_style\"); return 73;\nbreak;\ncase 64: this.begin(\"update_layout_config\"); return 74;\nbreak;\ncase 65:return \"EOF_IN_STRUCT\";\nbreak;\ncase 66: this.begin(\"attribute\"); return \"ATTRIBUTE_EMPTY\";\nbreak;\ncase 67: this.begin(\"attribute\"); \nbreak;\ncase 68: this.popState();this.popState();\nbreak;\ncase 69: return 80;\nbreak;\ncase 70: \nbreak;\ncase 71: return 80;\nbreak;\ncase 72: this.begin(\"string\");\nbreak;\ncase 73:  this.popState(); \nbreak;\ncase 74: return \"STR\";\nbreak;\ncase 75: this.begin(\"string_kv\");\nbreak;\ncase 76: this.begin(\"string_kv_key\"); return \"STR_KEY\";\nbreak;\ncase 77: this.popState(); this.begin(\"string_kv_value\"); \nbreak;\ncase 78: return \"STR_VALUE\";\nbreak;\ncase 79: this.popState(); this.popState(); \nbreak;\ncase 80: return \"STR\";\nbreak;\ncase 81: /* this.begin(\"lbrace\"); */ return \"LBRACE\";\nbreak;\ncase 82: /* this.popState(); */ return \"RBRACE\";\nbreak;\ncase 83:return 'SPACE';\nbreak;\ncase 84:return 'EOL';\nbreak;\ncase 85:return 14;\nbreak;\n}\n},\nrules: [/^(?:.*direction\\s+TB[^\\n]*)/,/^(?:.*direction\\s+BT[^\\n]*)/,/^(?:.*direction\\s+RL[^\\n]*)/,/^(?:.*direction\\s+LR[^\\n]*)/,/^(?:title\\s[^#\\n;]+)/,/^(?:accDescription\\s[^#\\n;]+)/,/^(?:accTitle\\s*:\\s*)/,/^(?:(?!\\n||)*[^\\n]*)/,/^(?:accDescr\\s*:\\s*)/,/^(?:(?!\\n||)*[^\\n]*)/,/^(?:accDescr\\s*\\{\\s*)/,/^(?:[\\}])/,/^(?:[^\\}]*)/,/^(?:%%(?!\\{)*[^\\n]*(\\r?\\n?)+)/,/^(?:%%[^\\n]*(\\r?\\n)*)/,/^(?:\\s*(\\r?\\n)+)/,/^(?:\\s+)/,/^(?:C4Context\\b)/,/^(?:C4Container\\b)/,/^(?:C4Component\\b)/,/^(?:C4Dynamic\\b)/,/^(?:C4Deployment\\b)/,/^(?:Person_Ext\\b)/,/^(?:Person\\b)/,/^(?:SystemQueue_Ext\\b)/,/^(?:SystemDb_Ext\\b)/,/^(?:System_Ext\\b)/,/^(?:SystemQueue\\b)/,/^(?:SystemDb\\b)/,/^(?:System\\b)/,/^(?:Boundary\\b)/,/^(?:Enterprise_Boundary\\b)/,/^(?:System_Boundary\\b)/,/^(?:ContainerQueue_Ext\\b)/,/^(?:ContainerDb_Ext\\b)/,/^(?:Container_Ext\\b)/,/^(?:ContainerQueue\\b)/,/^(?:ContainerDb\\b)/,/^(?:Container\\b)/,/^(?:Container_Boundary\\b)/,/^(?:ComponentQueue_Ext\\b)/,/^(?:ComponentDb_Ext\\b)/,/^(?:Component_Ext\\b)/,/^(?:ComponentQueue\\b)/,/^(?:ComponentDb\\b)/,/^(?:Component\\b)/,/^(?:Deployment_Node\\b)/,/^(?:Node\\b)/,/^(?:Node_L\\b)/,/^(?:Node_R\\b)/,/^(?:Rel\\b)/,/^(?:BiRel\\b)/,/^(?:Rel_Up\\b)/,/^(?:Rel_U\\b)/,/^(?:Rel_Down\\b)/,/^(?:Rel_D\\b)/,/^(?:Rel_Left\\b)/,/^(?:Rel_L\\b)/,/^(?:Rel_Right\\b)/,/^(?:Rel_R\\b)/,/^(?:Rel_Back\\b)/,/^(?:RelIndex\\b)/,/^(?:UpdateElementStyle\\b)/,/^(?:UpdateRelStyle\\b)/,/^(?:UpdateLayoutConfig\\b)/,/^(?:$)/,/^(?:[(][ ]*[,])/,/^(?:[(])/,/^(?:[)])/,/^(?:,,)/,/^(?:,)/,/^(?:[ ]*[\"][\"])/,/^(?:[ ]*[\"])/,/^(?:[\"])/,/^(?:[^\"]*)/,/^(?:[ ]*[\\$])/,/^(?:[^=]*)/,/^(?:[=][ ]*[\"])/,/^(?:[^\"]+)/,/^(?:[\"])/,/^(?:[^,]+)/,/^(?:\\{)/,/^(?:\\})/,/^(?:[\\s]+)/,/^(?:[\\n\\r]+)/,/^(?:$)/],\nconditions: {\"acc_descr_multiline\":{\"rules\":[11,12],\"inclusive\":false},\"acc_descr\":{\"rules\":[9],\"inclusive\":false},\"acc_title\":{\"rules\":[7],\"inclusive\":false},\"string_kv_value\":{\"rules\":[78,79],\"inclusive\":false},\"string_kv_key\":{\"rules\":[77],\"inclusive\":false},\"string_kv\":{\"rules\":[76],\"inclusive\":false},\"string\":{\"rules\":[73,74],\"inclusive\":false},\"attribute\":{\"rules\":[68,69,70,71,72,75,80],\"inclusive\":false},\"update_layout_config\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"update_rel_style\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"update_el_style\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"rel_b\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"rel_r\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"rel_l\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"rel_d\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"rel_u\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"rel_bi\":{\"rules\":[],\"inclusive\":false},\"rel\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"node_r\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"node_l\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"node\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"index\":{\"rules\":[],\"inclusive\":false},\"rel_index\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"component_ext_queue\":{\"rules\":[],\"inclusive\":false},\"component_ext_db\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"component_ext\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"component_queue\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"component_db\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"component\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"container_boundary\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"container_ext_queue\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"container_ext_db\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"container_ext\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"container_queue\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"container_db\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"container\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"birel\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"system_boundary\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"enterprise_boundary\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"boundary\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"system_ext_queue\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"system_ext_db\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"system_ext\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"system_queue\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"system_db\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"system\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"person_ext\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"person\":{\"rules\":[65,66,67,68],\"inclusive\":false},\"INITIAL\":{\"rules\":[0,1,2,3,4,5,6,8,10,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,81,82,83,84,85],\"inclusive\":true}}\n});\nreturn lexer;\n})();\nparser.lexer = lexer;\nfunction Parser () {\n  this.yy = {};\n}\nParser.prototype = parser;parser.Parser = Parser;\nreturn new Parser;\n})(); \n\tparser.parser = parser;\n\texport { parser };\n\texport default parser;\n\t", "import { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { sanitizeText } from '../common/common.js';\nimport {\n  setAccTitle,\n  getAccTitle,\n  getAccDescription,\n  setAccDescription,\n} from '../common/commonDb.js';\n\nlet c4ShapeArray = [];\nlet boundaryParseStack = [''];\nlet currentBoundaryParse = 'global';\nlet parentBoundaryParse = '';\nlet boundaries = [\n  {\n    alias: 'global',\n    label: { text: 'global' },\n    type: { text: 'global' },\n    tags: null,\n    link: null,\n    parentBoundary: '',\n  },\n];\nlet rels = [];\nlet title = '';\nlet wrapEnabled = false;\nlet c4ShapeInRow = 4;\nlet c4BoundaryInRow = 2;\nvar c4Type;\n\nexport const getC4Type = function () {\n  return c4Type;\n};\n\nexport const setC4Type = function (c4TypeParam) {\n  let sanitizedText = sanitizeText(c4TypeParam, getConfig());\n  c4Type = sanitizedText;\n};\n\n//type, from, to, label, ?techn, ?descr, ?sprite, ?tags, $link\nexport const addRel = function (type, from, to, label, techn, descr, sprite, tags, link) {\n  // Don't allow label nulling\n  if (\n    type === undefined ||\n    type === null ||\n    from === undefined ||\n    from === null ||\n    to === undefined ||\n    to === null ||\n    label === undefined ||\n    label === null\n  ) {\n    return;\n  }\n\n  let rel = {};\n  const old = rels.find((rel) => rel.from === from && rel.to === to);\n  if (old) {\n    rel = old;\n  } else {\n    rels.push(rel);\n  }\n\n  rel.type = type;\n  rel.from = from;\n  rel.to = to;\n  rel.label = { text: label };\n\n  if (techn === undefined || techn === null) {\n    rel.techn = { text: '' };\n  } else {\n    if (typeof techn === 'object') {\n      let [key, value] = Object.entries(techn)[0];\n      rel[key] = { text: value };\n    } else {\n      rel.techn = { text: techn };\n    }\n  }\n\n  if (descr === undefined || descr === null) {\n    rel.descr = { text: '' };\n  } else {\n    if (typeof descr === 'object') {\n      let [key, value] = Object.entries(descr)[0];\n      rel[key] = { text: value };\n    } else {\n      rel.descr = { text: descr };\n    }\n  }\n\n  if (typeof sprite === 'object') {\n    let [key, value] = Object.entries(sprite)[0];\n    rel[key] = value;\n  } else {\n    rel.sprite = sprite;\n  }\n  if (typeof tags === 'object') {\n    let [key, value] = Object.entries(tags)[0];\n    rel[key] = value;\n  } else {\n    rel.tags = tags;\n  }\n  if (typeof link === 'object') {\n    let [key, value] = Object.entries(link)[0];\n    rel[key] = value;\n  } else {\n    rel.link = link;\n  }\n  rel.wrap = autoWrap();\n};\n\n//type, alias, label, ?descr, ?sprite, ?tags, $link\nexport const addPersonOrSystem = function (typeC4Shape, alias, label, descr, sprite, tags, link) {\n  // Don't allow label nulling\n  if (alias === null || label === null) {\n    return;\n  }\n\n  let personOrSystem = {};\n  const old = c4ShapeArray.find((personOrSystem) => personOrSystem.alias === alias);\n  if (old && alias === old.alias) {\n    personOrSystem = old;\n  } else {\n    personOrSystem.alias = alias;\n    c4ShapeArray.push(personOrSystem);\n  }\n\n  // Don't allow null labels, either\n  if (label === undefined || label === null) {\n    personOrSystem.label = { text: '' };\n  } else {\n    personOrSystem.label = { text: label };\n  }\n\n  if (descr === undefined || descr === null) {\n    personOrSystem.descr = { text: '' };\n  } else {\n    if (typeof descr === 'object') {\n      let [key, value] = Object.entries(descr)[0];\n      personOrSystem[key] = { text: value };\n    } else {\n      personOrSystem.descr = { text: descr };\n    }\n  }\n\n  if (typeof sprite === 'object') {\n    let [key, value] = Object.entries(sprite)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.sprite = sprite;\n  }\n  if (typeof tags === 'object') {\n    let [key, value] = Object.entries(tags)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.tags = tags;\n  }\n  if (typeof link === 'object') {\n    let [key, value] = Object.entries(link)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.link = link;\n  }\n  personOrSystem.typeC4Shape = { text: typeC4Shape };\n  personOrSystem.parentBoundary = currentBoundaryParse;\n  personOrSystem.wrap = autoWrap();\n};\n\n//type, alias, label, ?techn, ?descr ?sprite, ?tags, $link\nexport const addContainer = function (typeC4Shape, alias, label, techn, descr, sprite, tags, link) {\n  // Don't allow label nulling\n  if (alias === null || label === null) {\n    return;\n  }\n\n  let container = {};\n  const old = c4ShapeArray.find((container) => container.alias === alias);\n  if (old && alias === old.alias) {\n    container = old;\n  } else {\n    container.alias = alias;\n    c4ShapeArray.push(container);\n  }\n\n  // Don't allow null labels, either\n  if (label === undefined || label === null) {\n    container.label = { text: '' };\n  } else {\n    container.label = { text: label };\n  }\n\n  if (techn === undefined || techn === null) {\n    container.techn = { text: '' };\n  } else {\n    if (typeof techn === 'object') {\n      let [key, value] = Object.entries(techn)[0];\n      container[key] = { text: value };\n    } else {\n      container.techn = { text: techn };\n    }\n  }\n\n  if (descr === undefined || descr === null) {\n    container.descr = { text: '' };\n  } else {\n    if (typeof descr === 'object') {\n      let [key, value] = Object.entries(descr)[0];\n      container[key] = { text: value };\n    } else {\n      container.descr = { text: descr };\n    }\n  }\n\n  if (typeof sprite === 'object') {\n    let [key, value] = Object.entries(sprite)[0];\n    container[key] = value;\n  } else {\n    container.sprite = sprite;\n  }\n  if (typeof tags === 'object') {\n    let [key, value] = Object.entries(tags)[0];\n    container[key] = value;\n  } else {\n    container.tags = tags;\n  }\n  if (typeof link === 'object') {\n    let [key, value] = Object.entries(link)[0];\n    container[key] = value;\n  } else {\n    container.link = link;\n  }\n  container.wrap = autoWrap();\n  container.typeC4Shape = { text: typeC4Shape };\n  container.parentBoundary = currentBoundaryParse;\n};\n\n//type, alias, label, ?techn, ?descr ?sprite, ?tags, $link\nexport const addComponent = function (typeC4Shape, alias, label, techn, descr, sprite, tags, link) {\n  // Don't allow label nulling\n  if (alias === null || label === null) {\n    return;\n  }\n\n  let component = {};\n  const old = c4ShapeArray.find((component) => component.alias === alias);\n  if (old && alias === old.alias) {\n    component = old;\n  } else {\n    component.alias = alias;\n    c4ShapeArray.push(component);\n  }\n\n  // Don't allow null labels, either\n  if (label === undefined || label === null) {\n    component.label = { text: '' };\n  } else {\n    component.label = { text: label };\n  }\n\n  if (techn === undefined || techn === null) {\n    component.techn = { text: '' };\n  } else {\n    if (typeof techn === 'object') {\n      let [key, value] = Object.entries(techn)[0];\n      component[key] = { text: value };\n    } else {\n      component.techn = { text: techn };\n    }\n  }\n\n  if (descr === undefined || descr === null) {\n    component.descr = { text: '' };\n  } else {\n    if (typeof descr === 'object') {\n      let [key, value] = Object.entries(descr)[0];\n      component[key] = { text: value };\n    } else {\n      component.descr = { text: descr };\n    }\n  }\n\n  if (typeof sprite === 'object') {\n    let [key, value] = Object.entries(sprite)[0];\n    component[key] = value;\n  } else {\n    component.sprite = sprite;\n  }\n  if (typeof tags === 'object') {\n    let [key, value] = Object.entries(tags)[0];\n    component[key] = value;\n  } else {\n    component.tags = tags;\n  }\n  if (typeof link === 'object') {\n    let [key, value] = Object.entries(link)[0];\n    component[key] = value;\n  } else {\n    component.link = link;\n  }\n  component.wrap = autoWrap();\n  component.typeC4Shape = { text: typeC4Shape };\n  component.parentBoundary = currentBoundaryParse;\n};\n\n//alias, label, ?type, ?tags, $link\nexport const addPersonOrSystemBoundary = function (alias, label, type, tags, link) {\n  // if (parentBoundary === null) return;\n\n  // Don't allow label nulling\n  if (alias === null || label === null) {\n    return;\n  }\n\n  let boundary = {};\n  const old = boundaries.find((boundary) => boundary.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n\n  // Don't allow null labels, either\n  if (label === undefined || label === null) {\n    boundary.label = { text: '' };\n  } else {\n    boundary.label = { text: label };\n  }\n\n  if (type === undefined || type === null) {\n    boundary.type = { text: 'system' };\n  } else {\n    if (typeof type === 'object') {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n\n  if (typeof tags === 'object') {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === 'object') {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n};\n\n//alias, label, ?type, ?tags, $link\nexport const addContainerBoundary = function (alias, label, type, tags, link) {\n  // if (parentBoundary === null) return;\n\n  // Don't allow label nulling\n  if (alias === null || label === null) {\n    return;\n  }\n\n  let boundary = {};\n  const old = boundaries.find((boundary) => boundary.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n\n  // Don't allow null labels, either\n  if (label === undefined || label === null) {\n    boundary.label = { text: '' };\n  } else {\n    boundary.label = { text: label };\n  }\n\n  if (type === undefined || type === null) {\n    boundary.type = { text: 'container' };\n  } else {\n    if (typeof type === 'object') {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n\n  if (typeof tags === 'object') {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === 'object') {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n};\n\n//alias, label, ?type, ?descr, ?sprite, ?tags, $link\nexport const addDeploymentNode = function (\n  nodeType,\n  alias,\n  label,\n  type,\n  descr,\n  sprite,\n  tags,\n  link\n) {\n  // if (parentBoundary === null) return;\n\n  // Don't allow label nulling\n  if (alias === null || label === null) {\n    return;\n  }\n\n  let boundary = {};\n  const old = boundaries.find((boundary) => boundary.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n\n  // Don't allow null labels, either\n  if (label === undefined || label === null) {\n    boundary.label = { text: '' };\n  } else {\n    boundary.label = { text: label };\n  }\n\n  if (type === undefined || type === null) {\n    boundary.type = { text: 'node' };\n  } else {\n    if (typeof type === 'object') {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n\n  if (descr === undefined || descr === null) {\n    boundary.descr = { text: '' };\n  } else {\n    if (typeof descr === 'object') {\n      let [key, value] = Object.entries(descr)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.descr = { text: descr };\n    }\n  }\n\n  if (typeof tags === 'object') {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === 'object') {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.nodeType = nodeType;\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n};\n\nexport const popBoundaryParseStack = function () {\n  currentBoundaryParse = parentBoundaryParse;\n  boundaryParseStack.pop();\n  parentBoundaryParse = boundaryParseStack.pop();\n  boundaryParseStack.push(parentBoundaryParse);\n};\n\n//elementName, ?bgColor, ?fontColor, ?borderColor, ?shadowing, ?shape, ?sprite, ?techn, ?legendText, ?legendSprite\nexport const updateElStyle = function (\n  typeC4Shape,\n  elementName,\n  bgColor,\n  fontColor,\n  borderColor,\n  shadowing,\n  shape,\n  sprite,\n  techn,\n  legendText,\n  legendSprite\n) {\n  let old = c4ShapeArray.find((element) => element.alias === elementName);\n  if (old === undefined) {\n    old = boundaries.find((element) => element.alias === elementName);\n    if (old === undefined) {\n      return;\n    }\n  }\n  if (bgColor !== undefined && bgColor !== null) {\n    if (typeof bgColor === 'object') {\n      let [key, value] = Object.entries(bgColor)[0];\n      old[key] = value;\n    } else {\n      old.bgColor = bgColor;\n    }\n  }\n  if (fontColor !== undefined && fontColor !== null) {\n    if (typeof fontColor === 'object') {\n      let [key, value] = Object.entries(fontColor)[0];\n      old[key] = value;\n    } else {\n      old.fontColor = fontColor;\n    }\n  }\n  if (borderColor !== undefined && borderColor !== null) {\n    if (typeof borderColor === 'object') {\n      let [key, value] = Object.entries(borderColor)[0];\n      old[key] = value;\n    } else {\n      old.borderColor = borderColor;\n    }\n  }\n  if (shadowing !== undefined && shadowing !== null) {\n    if (typeof shadowing === 'object') {\n      let [key, value] = Object.entries(shadowing)[0];\n      old[key] = value;\n    } else {\n      old.shadowing = shadowing;\n    }\n  }\n  if (shape !== undefined && shape !== null) {\n    if (typeof shape === 'object') {\n      let [key, value] = Object.entries(shape)[0];\n      old[key] = value;\n    } else {\n      old.shape = shape;\n    }\n  }\n  if (sprite !== undefined && sprite !== null) {\n    if (typeof sprite === 'object') {\n      let [key, value] = Object.entries(sprite)[0];\n      old[key] = value;\n    } else {\n      old.sprite = sprite;\n    }\n  }\n  if (techn !== undefined && techn !== null) {\n    if (typeof techn === 'object') {\n      let [key, value] = Object.entries(techn)[0];\n      old[key] = value;\n    } else {\n      old.techn = techn;\n    }\n  }\n  if (legendText !== undefined && legendText !== null) {\n    if (typeof legendText === 'object') {\n      let [key, value] = Object.entries(legendText)[0];\n      old[key] = value;\n    } else {\n      old.legendText = legendText;\n    }\n  }\n  if (legendSprite !== undefined && legendSprite !== null) {\n    if (typeof legendSprite === 'object') {\n      let [key, value] = Object.entries(legendSprite)[0];\n      old[key] = value;\n    } else {\n      old.legendSprite = legendSprite;\n    }\n  }\n};\n\n//textColor, lineColor, ?offsetX, ?offsetY\nexport const updateRelStyle = function (\n  typeC4Shape,\n  from,\n  to,\n  textColor,\n  lineColor,\n  offsetX,\n  offsetY\n) {\n  const old = rels.find((rel) => rel.from === from && rel.to === to);\n  if (old === undefined) {\n    return;\n  }\n  if (textColor !== undefined && textColor !== null) {\n    if (typeof textColor === 'object') {\n      let [key, value] = Object.entries(textColor)[0];\n      old[key] = value;\n    } else {\n      old.textColor = textColor;\n    }\n  }\n  if (lineColor !== undefined && lineColor !== null) {\n    if (typeof lineColor === 'object') {\n      let [key, value] = Object.entries(lineColor)[0];\n      old[key] = value;\n    } else {\n      old.lineColor = lineColor;\n    }\n  }\n  if (offsetX !== undefined && offsetX !== null) {\n    if (typeof offsetX === 'object') {\n      let [key, value] = Object.entries(offsetX)[0];\n      old[key] = parseInt(value);\n    } else {\n      old.offsetX = parseInt(offsetX);\n    }\n  }\n  if (offsetY !== undefined && offsetY !== null) {\n    if (typeof offsetY === 'object') {\n      let [key, value] = Object.entries(offsetY)[0];\n      old[key] = parseInt(value);\n    } else {\n      old.offsetY = parseInt(offsetY);\n    }\n  }\n};\n\n//?c4ShapeInRow, ?c4BoundaryInRow\nexport const updateLayoutConfig = function (typeC4Shape, c4ShapeInRowParam, c4BoundaryInRowParam) {\n  let c4ShapeInRowValue = c4ShapeInRow;\n  let c4BoundaryInRowValue = c4BoundaryInRow;\n\n  if (typeof c4ShapeInRowParam === 'object') {\n    const value = Object.values(c4ShapeInRowParam)[0];\n    c4ShapeInRowValue = parseInt(value);\n  } else {\n    c4ShapeInRowValue = parseInt(c4ShapeInRowParam);\n  }\n  if (typeof c4BoundaryInRowParam === 'object') {\n    const value = Object.values(c4BoundaryInRowParam)[0];\n    c4BoundaryInRowValue = parseInt(value);\n  } else {\n    c4BoundaryInRowValue = parseInt(c4BoundaryInRowParam);\n  }\n\n  if (c4ShapeInRowValue >= 1) {\n    c4ShapeInRow = c4ShapeInRowValue;\n  }\n  if (c4BoundaryInRowValue >= 1) {\n    c4BoundaryInRow = c4BoundaryInRowValue;\n  }\n};\n\nexport const getC4ShapeInRow = function () {\n  return c4ShapeInRow;\n};\nexport const getC4BoundaryInRow = function () {\n  return c4BoundaryInRow;\n};\nexport const getCurrentBoundaryParse = function () {\n  return currentBoundaryParse;\n};\n\nexport const getParentBoundaryParse = function () {\n  return parentBoundaryParse;\n};\n\nexport const getC4ShapeArray = function (parentBoundary) {\n  if (parentBoundary === undefined || parentBoundary === null) {\n    return c4ShapeArray;\n  } else {\n    return c4ShapeArray.filter((personOrSystem) => {\n      return personOrSystem.parentBoundary === parentBoundary;\n    });\n  }\n};\nexport const getC4Shape = function (alias) {\n  return c4ShapeArray.find((personOrSystem) => personOrSystem.alias === alias);\n};\nexport const getC4ShapeKeys = function (parentBoundary) {\n  return Object.keys(getC4ShapeArray(parentBoundary));\n};\n\nexport const getBoundaries = function (parentBoundary) {\n  if (parentBoundary === undefined || parentBoundary === null) {\n    return boundaries;\n  } else {\n    return boundaries.filter((boundary) => boundary.parentBoundary === parentBoundary);\n  }\n};\n\n/**\n * @deprecated Use {@link getBoundaries} instead\n */\nexport const getBoundarys = getBoundaries;\n\nexport const getRels = function () {\n  return rels;\n};\n\nexport const getTitle = function () {\n  return title;\n};\n\nexport const setWrap = function (wrapSetting) {\n  wrapEnabled = wrapSetting;\n};\n\nexport const autoWrap = function () {\n  return wrapEnabled;\n};\n\nexport const clear = function () {\n  c4ShapeArray = [];\n  boundaries = [\n    {\n      alias: 'global',\n      label: { text: 'global' },\n      type: { text: 'global' },\n      tags: null,\n      link: null,\n      parentBoundary: '',\n    },\n  ];\n  parentBoundaryParse = '';\n  currentBoundaryParse = 'global';\n  boundaryParseStack = [''];\n  rels = [];\n\n  boundaryParseStack = [''];\n  title = '';\n  wrapEnabled = false;\n  c4ShapeInRow = 4;\n  c4BoundaryInRow = 2;\n};\n\nexport const LINETYPE = {\n  SOLID: 0,\n  DOTTED: 1,\n  NOTE: 2,\n  SOLID_CROSS: 3,\n  DOTTED_CROSS: 4,\n  SOLID_OPEN: 5,\n  DOTTED_OPEN: 6,\n  LOOP_START: 10,\n  LOOP_END: 11,\n  ALT_START: 12,\n  ALT_ELSE: 13,\n  ALT_END: 14,\n  OPT_START: 15,\n  OPT_END: 16,\n  ACTIVE_START: 17,\n  ACTIVE_END: 18,\n  PAR_START: 19,\n  PAR_AND: 20,\n  PAR_END: 21,\n  RECT_START: 22,\n  RECT_END: 23,\n  SOLID_POINT: 24,\n  DOTTED_POINT: 25,\n};\n\nexport const ARROWTYPE = {\n  FILLED: 0,\n  OPEN: 1,\n};\n\nexport const PLACEMENT = {\n  LEFTOF: 0,\n  RIGHTOF: 1,\n  OVER: 2,\n};\n\nexport const setTitle = function (txt) {\n  let sanitizedText = sanitizeText(txt, getConfig());\n  title = sanitizedText;\n};\n\nexport default {\n  addPersonOrSystem,\n  addPersonOrSystemBoundary,\n  addContainer,\n  addContainerBoundary,\n  addComponent,\n  addDeploymentNode,\n  popBoundaryParseStack,\n  addRel,\n  updateElStyle,\n  updateRelStyle,\n  updateLayoutConfig,\n  autoWrap,\n  setWrap,\n  getC4ShapeArray,\n  getC4Shape,\n  getC4ShapeKeys,\n  getBoundaries,\n  getBoundarys,\n  getCurrentBoundaryParse,\n  getParentBoundaryParse,\n  getRels,\n  getTitle,\n  getC4Type,\n  getC4ShapeInRow,\n  getC4BoundaryInRow,\n  setAccTitle,\n  getAccTitle,\n  getAccDescription,\n  setAccDescription,\n  getConfig: () => getConfig().c4,\n  clear,\n  LINETYPE,\n  ARROWTYPE,\n  PLACEMENT,\n  setTitle,\n  setC4Type,\n  // apply,\n};\n", "import common from '../common/common.js';\nimport * as svgDrawCommon from '../common/svgDrawCommon.js';\nimport { sanitizeUrl } from '@braintree/sanitize-url';\n\nexport const drawRect = function (elem, rectData) {\n  return svgDrawCommon.drawRect(elem, rectData);\n};\n\nexport const drawImage = function (elem, width, height, x, y, link) {\n  const imageElem = elem.append('image');\n  imageElem.attr('width', width);\n  imageElem.attr('height', height);\n  imageElem.attr('x', x);\n  imageElem.attr('y', y);\n  let sanitizedLink = link.startsWith('data:image/png;base64') ? link : sanitizeUrl(link);\n  imageElem.attr('xlink:href', sanitizedLink);\n};\n\nexport const drawRels = (elem, rels, conf) => {\n  const relsElem = elem.append('g');\n  let i = 0;\n  for (let rel of rels) {\n    let textColor = rel.textColor ? rel.textColor : '#444444';\n    let strokeColor = rel.lineColor ? rel.lineColor : '#444444';\n    let offsetX = rel.offsetX ? parseInt(rel.offsetX) : 0;\n    let offsetY = rel.offsetY ? parseInt(rel.offsetY) : 0;\n\n    let url = '';\n    if (i === 0) {\n      let line = relsElem.append('line');\n      line.attr('x1', rel.startPoint.x);\n      line.attr('y1', rel.startPoint.y);\n      line.attr('x2', rel.endPoint.x);\n      line.attr('y2', rel.endPoint.y);\n\n      line.attr('stroke-width', '1');\n      line.attr('stroke', strokeColor);\n      line.style('fill', 'none');\n      if (rel.type !== 'rel_b') {\n        line.attr('marker-end', 'url(' + url + '#arrowhead)');\n      }\n      if (rel.type === 'birel' || rel.type === 'rel_b') {\n        line.attr('marker-start', 'url(' + url + '#arrowend)');\n      }\n      i = -1;\n    } else {\n      let line = relsElem.append('path');\n      line\n        .attr('fill', 'none')\n        .attr('stroke-width', '1')\n        .attr('stroke', strokeColor)\n        .attr(\n          'd',\n          'Mstartx,starty Qcontrolx,controly stopx,stopy '\n            .replaceAll('startx', rel.startPoint.x)\n            .replaceAll('starty', rel.startPoint.y)\n            .replaceAll(\n              'controlx',\n              rel.startPoint.x +\n                (rel.endPoint.x - rel.startPoint.x) / 2 -\n                (rel.endPoint.x - rel.startPoint.x) / 4\n            )\n            .replaceAll('controly', rel.startPoint.y + (rel.endPoint.y - rel.startPoint.y) / 2)\n            .replaceAll('stopx', rel.endPoint.x)\n            .replaceAll('stopy', rel.endPoint.y)\n        );\n      if (rel.type !== 'rel_b') {\n        line.attr('marker-end', 'url(' + url + '#arrowhead)');\n      }\n      if (rel.type === 'birel' || rel.type === 'rel_b') {\n        line.attr('marker-start', 'url(' + url + '#arrowend)');\n      }\n    }\n\n    let messageConf = conf.messageFont();\n    _drawTextCandidateFunc(conf)(\n      rel.label.text,\n      relsElem,\n      Math.min(rel.startPoint.x, rel.endPoint.x) +\n        Math.abs(rel.endPoint.x - rel.startPoint.x) / 2 +\n        offsetX,\n      Math.min(rel.startPoint.y, rel.endPoint.y) +\n        Math.abs(rel.endPoint.y - rel.startPoint.y) / 2 +\n        offsetY,\n      rel.label.width,\n      rel.label.height,\n      { fill: textColor },\n      messageConf\n    );\n\n    if (rel.techn && rel.techn.text !== '') {\n      messageConf = conf.messageFont();\n      _drawTextCandidateFunc(conf)(\n        '[' + rel.techn.text + ']',\n        relsElem,\n        Math.min(rel.startPoint.x, rel.endPoint.x) +\n          Math.abs(rel.endPoint.x - rel.startPoint.x) / 2 +\n          offsetX,\n        Math.min(rel.startPoint.y, rel.endPoint.y) +\n          Math.abs(rel.endPoint.y - rel.startPoint.y) / 2 +\n          conf.messageFontSize +\n          5 +\n          offsetY,\n        Math.max(rel.label.width, rel.techn.width),\n        rel.techn.height,\n        { fill: textColor, 'font-style': 'italic' },\n        messageConf\n      );\n    }\n  }\n};\n\n/**\n * Draws an boundary in the diagram\n *\n * @param {any} elem - The diagram we'll draw to.\n * @param {any} boundary - The boundary to draw.\n * @param {any} conf - DrawText implementation discriminator object\n */\nconst drawBoundary = function (elem, boundary, conf) {\n  const boundaryElem = elem.append('g');\n\n  let fillColor = boundary.bgColor ? boundary.bgColor : 'none';\n  let strokeColor = boundary.borderColor ? boundary.borderColor : '#444444';\n  let fontColor = boundary.fontColor ? boundary.fontColor : 'black';\n\n  let attrsValue = { 'stroke-width': 1.0, 'stroke-dasharray': '7.0,7.0' };\n  if (boundary.nodeType) {\n    attrsValue = { 'stroke-width': 1.0 };\n  }\n  let rectData = {\n    x: boundary.x,\n    y: boundary.y,\n    fill: fillColor,\n    stroke: strokeColor,\n    width: boundary.width,\n    height: boundary.height,\n    rx: 2.5,\n    ry: 2.5,\n    attrs: attrsValue,\n  };\n\n  drawRect(boundaryElem, rectData);\n\n  // draw label\n  let boundaryConf = conf.boundaryFont();\n  boundaryConf.fontWeight = 'bold';\n  boundaryConf.fontSize = boundaryConf.fontSize + 2;\n  boundaryConf.fontColor = fontColor;\n  _drawTextCandidateFunc(conf)(\n    boundary.label.text,\n    boundaryElem,\n    boundary.x,\n    boundary.y + boundary.label.Y,\n    boundary.width,\n    boundary.height,\n    { fill: '#444444' },\n    boundaryConf\n  );\n\n  // draw type\n  if (boundary.type && boundary.type.text !== '') {\n    boundaryConf = conf.boundaryFont();\n    boundaryConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf)(\n      boundary.type.text,\n      boundaryElem,\n      boundary.x,\n      boundary.y + boundary.type.Y,\n      boundary.width,\n      boundary.height,\n      { fill: '#444444' },\n      boundaryConf\n    );\n  }\n\n  // draw descr\n  if (boundary.descr && boundary.descr.text !== '') {\n    boundaryConf = conf.boundaryFont();\n    boundaryConf.fontSize = boundaryConf.fontSize - 2;\n    boundaryConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf)(\n      boundary.descr.text,\n      boundaryElem,\n      boundary.x,\n      boundary.y + boundary.descr.Y,\n      boundary.width,\n      boundary.height,\n      { fill: '#444444' },\n      boundaryConf\n    );\n  }\n};\n\nexport const drawC4Shape = function (elem, c4Shape, conf) {\n  let fillColor = c4Shape.bgColor ? c4Shape.bgColor : conf[c4Shape.typeC4Shape.text + '_bg_color'];\n  let strokeColor = c4Shape.borderColor\n    ? c4Shape.borderColor\n    : conf[c4Shape.typeC4Shape.text + '_border_color'];\n  let fontColor = c4Shape.fontColor ? c4Shape.fontColor : '#FFFFFF';\n\n  let personImg =\n    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=';\n  switch (c4Shape.typeC4Shape.text) {\n    case 'person':\n      personImg =\n        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=';\n      break;\n    case 'external_person':\n      personImg =\n        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAAB6ElEQVR4Xu2YLY+EMBCG9+dWr0aj0Wg0Go1Go0+j8Xdv2uTCvv1gpt0ebHKPuhDaeW4605Z9mJvx4AdXUyTUdd08z+u6flmWZRnHsWkafk9DptAwDPu+f0eAYtu2PEaGWuj5fCIZrBAC2eLBAnRCsEkkxmeaJp7iDJ2QMDdHsLg8SxKFEJaAo8lAXnmuOFIhTMpxxKATebo4UiFknuNo4OniSIXQyRxEA3YsnjGCVEjVXD7yLUAqxBGUyPv/Y4W2beMgGuS7kVQIBycH0fD+oi5pezQETxdHKmQKGk1eQEYldK+jw5GxPfZ9z7Mk0Qnhf1W1m3w//EUn5BDmSZsbR44QQLBEqrBHqOrmSKaQAxdnLArCrxZcM7A7ZKs4ioRq8LFC+NpC3WCBJsvpVw5edm9iEXFuyNfxXAgSwfrFQ1c0iNda8AdejvUgnktOtJQQxmcfFzGglc5WVCj7oDgFqU18boeFSs52CUh8LE8BIVQDT1ABrB0HtgSEYlX5doJnCwv9TXocKCaKbnwhdDKPq4lf3SwU3HLq4V/+WYhHVMa/3b4IlfyikAduCkcBc7mQ3/z/Qq/cTuikhkzB12Ae/mcJC9U+Vo8Ej1gWAtgbeGgFsAMHr50BIWOLCbezvhpBFUdY6EJuJ/QDW0XoMX60zZ0AAAAASUVORK5CYII=';\n      break;\n  }\n\n  const c4ShapeElem = elem.append('g');\n  c4ShapeElem.attr('class', 'person-man');\n\n  // <rect fill=\"#08427B\" height=\"119.2188\" rx=\"2.5\" ry=\"2.5\" stroke=\"#073B6F\" stroke-width=\"0.5\" width=\"110\" x=\"120\" y=\"7\"/>\n  // draw rect of c4Shape\n  const rect = svgDrawCommon.getNoteRect();\n\n  switch (c4Shape.typeC4Shape.text) {\n    case 'person':\n    case 'external_person':\n    case 'system':\n    case 'external_system':\n    case 'container':\n    case 'external_container':\n    case 'component':\n    case 'external_component':\n      rect.x = c4Shape.x;\n      rect.y = c4Shape.y;\n      rect.fill = fillColor;\n      rect.width = c4Shape.width;\n      rect.height = c4Shape.height;\n      rect.stroke = strokeColor;\n      rect.rx = 2.5;\n      rect.ry = 2.5;\n      rect.attrs = { 'stroke-width': 0.5 };\n      drawRect(c4ShapeElem, rect);\n      break;\n    case 'system_db':\n    case 'external_system_db':\n    case 'container_db':\n    case 'external_container_db':\n    case 'component_db':\n    case 'external_component_db':\n      c4ShapeElem\n        .append('path')\n        .attr('fill', fillColor)\n        .attr('stroke-width', '0.5')\n        .attr('stroke', strokeColor)\n        .attr(\n          'd',\n          'Mstartx,startyc0,-10 half,-10 half,-10c0,0 half,0 half,10l0,heightc0,10 -half,10 -half,10c0,0 -half,0 -half,-10l0,-height'\n            .replaceAll('startx', c4Shape.x)\n            .replaceAll('starty', c4Shape.y)\n            .replaceAll('half', c4Shape.width / 2)\n            .replaceAll('height', c4Shape.height)\n        );\n      c4ShapeElem\n        .append('path')\n        .attr('fill', 'none')\n        .attr('stroke-width', '0.5')\n        .attr('stroke', strokeColor)\n        .attr(\n          'd',\n          'Mstartx,startyc0,10 half,10 half,10c0,0 half,0 half,-10'\n            .replaceAll('startx', c4Shape.x)\n            .replaceAll('starty', c4Shape.y)\n            .replaceAll('half', c4Shape.width / 2)\n        );\n      break;\n    case 'system_queue':\n    case 'external_system_queue':\n    case 'container_queue':\n    case 'external_container_queue':\n    case 'component_queue':\n    case 'external_component_queue':\n      c4ShapeElem\n        .append('path')\n        .attr('fill', fillColor)\n        .attr('stroke-width', '0.5')\n        .attr('stroke', strokeColor)\n        .attr(\n          'd',\n          'Mstartx,startylwidth,0c5,0 5,half 5,halfc0,0 0,half -5,halfl-width,0c-5,0 -5,-half -5,-halfc0,0 0,-half 5,-half'\n            .replaceAll('startx', c4Shape.x)\n            .replaceAll('starty', c4Shape.y)\n            .replaceAll('width', c4Shape.width)\n            .replaceAll('half', c4Shape.height / 2)\n        );\n      c4ShapeElem\n        .append('path')\n        .attr('fill', 'none')\n        .attr('stroke-width', '0.5')\n        .attr('stroke', strokeColor)\n        .attr(\n          'd',\n          'Mstartx,startyc-5,0 -5,half -5,halfc0,half 5,half 5,half'\n            .replaceAll('startx', c4Shape.x + c4Shape.width)\n            .replaceAll('starty', c4Shape.y)\n            .replaceAll('half', c4Shape.height / 2)\n        );\n      break;\n  }\n\n  // draw type of c4Shape\n  let c4ShapeFontConf = getC4ShapeFont(conf, c4Shape.typeC4Shape.text);\n  c4ShapeElem\n    .append('text')\n    .attr('fill', fontColor)\n    .attr('font-family', c4ShapeFontConf.fontFamily)\n    .attr('font-size', c4ShapeFontConf.fontSize - 2)\n    .attr('font-style', 'italic')\n    .attr('lengthAdjust', 'spacing')\n    .attr('textLength', c4Shape.typeC4Shape.width)\n    .attr('x', c4Shape.x + c4Shape.width / 2 - c4Shape.typeC4Shape.width / 2)\n    .attr('y', c4Shape.y + c4Shape.typeC4Shape.Y)\n    .text('<<' + c4Shape.typeC4Shape.text + '>>');\n\n  // draw image/sprite\n  switch (c4Shape.typeC4Shape.text) {\n    case 'person':\n    case 'external_person':\n      drawImage(\n        c4ShapeElem,\n        48,\n        48,\n        c4Shape.x + c4Shape.width / 2 - 24,\n        c4Shape.y + c4Shape.image.Y,\n        personImg\n      );\n      break;\n  }\n\n  // draw label\n  let textFontConf = conf[c4Shape.typeC4Shape.text + 'Font']();\n  textFontConf.fontWeight = 'bold';\n  textFontConf.fontSize = textFontConf.fontSize + 2;\n  textFontConf.fontColor = fontColor;\n  _drawTextCandidateFunc(conf)(\n    c4Shape.label.text,\n    c4ShapeElem,\n    c4Shape.x,\n    c4Shape.y + c4Shape.label.Y,\n    c4Shape.width,\n    c4Shape.height,\n    { fill: fontColor },\n    textFontConf\n  );\n\n  // draw techn/type\n  textFontConf = conf[c4Shape.typeC4Shape.text + 'Font']();\n  textFontConf.fontColor = fontColor;\n\n  if (c4Shape.techn && c4Shape.techn?.text !== '') {\n    _drawTextCandidateFunc(conf)(\n      c4Shape.techn.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.techn.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor, 'font-style': 'italic' },\n      textFontConf\n    );\n  } else if (c4Shape.type && c4Shape.type.text !== '') {\n    _drawTextCandidateFunc(conf)(\n      c4Shape.type.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.type.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor, 'font-style': 'italic' },\n      textFontConf\n    );\n  }\n\n  // draw descr\n  if (c4Shape.descr && c4Shape.descr.text !== '') {\n    textFontConf = conf.personFont();\n    textFontConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf)(\n      c4Shape.descr.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.descr.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor },\n      textFontConf\n    );\n  }\n\n  return c4Shape.height;\n};\n\nexport const insertDatabaseIcon = function (elem) {\n  elem\n    .append('defs')\n    .append('symbol')\n    .attr('id', 'database')\n    .attr('fill-rule', 'evenodd')\n    .attr('clip-rule', 'evenodd')\n    .append('path')\n    .attr('transform', 'scale(.5)')\n    .attr(\n      'd',\n      'M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z'\n    );\n};\n\nexport const insertComputerIcon = function (elem) {\n  elem\n    .append('defs')\n    .append('symbol')\n    .attr('id', 'computer')\n    .attr('width', '24')\n    .attr('height', '24')\n    .append('path')\n    .attr('transform', 'scale(.5)')\n    .attr(\n      'd',\n      'M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z'\n    );\n};\n\nexport const insertClockIcon = function (elem) {\n  elem\n    .append('defs')\n    .append('symbol')\n    .attr('id', 'clock')\n    .attr('width', '24')\n    .attr('height', '24')\n    .append('path')\n    .attr('transform', 'scale(.5)')\n    .attr(\n      'd',\n      'M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z'\n    );\n};\n\n/**\n * Setup arrow head and define the marker. The result is appended to the svg.\n *\n * @param elem\n */\nexport const insertArrowHead = function (elem) {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', 'arrowhead')\n    .attr('refX', 9)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 12)\n    .attr('markerHeight', 12)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 0 0 L 10 5 L 0 10 z'); // this is actual shape for arrowhead\n};\n\nexport const insertArrowEnd = function (elem) {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', 'arrowend')\n    .attr('refX', 1)\n    .attr('refY', 5)\n    .attr('markerUnits', 'userSpaceOnUse')\n    .attr('markerWidth', 12)\n    .attr('markerHeight', 12)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 10 0 L 0 5 L 10 10 z'); // this is actual shape for arrowhead\n};\n\n/**\n * Setup arrow head and define the marker. The result is appended to the svg.\n *\n * @param {any} elem\n */\nexport const insertArrowFilledHead = function (elem) {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', 'filled-head')\n    .attr('refX', 18)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 18,7 L9,13 L14,7 L9,1 Z');\n};\n\n/**\n * Setup node number. The result is appended to the svg.\n *\n * @param {any} elem\n */\nexport const insertDynamicNumber = function (elem) {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', 'sequencenumber')\n    .attr('refX', 15)\n    .attr('refY', 15)\n    .attr('markerWidth', 60)\n    .attr('markerHeight', 40)\n    .attr('orient', 'auto')\n    .append('circle')\n    .attr('cx', 15)\n    .attr('cy', 15)\n    .attr('r', 6);\n  // .style(\"fill\", '#f00');\n};\n\n/**\n * Setup arrow head and define the marker. The result is appended to the svg.\n *\n * @param {any} elem\n */\nexport const insertArrowCrossHead = function (elem) {\n  const defs = elem.append('defs');\n  const marker = defs\n    .append('marker')\n    .attr('id', 'crosshead')\n    .attr('markerWidth', 15)\n    .attr('markerHeight', 8)\n    .attr('orient', 'auto')\n    .attr('refX', 16)\n    .attr('refY', 4);\n\n  // The arrow\n  marker\n    .append('path')\n    .attr('fill', 'black')\n    .attr('stroke', '#000000')\n    .style('stroke-dasharray', '0, 0')\n    .attr('stroke-width', '1px')\n    .attr('d', 'M 9,2 V 6 L16,4 Z');\n\n  // The cross\n  marker\n    .append('path')\n    .attr('fill', 'none')\n    .attr('stroke', '#000000')\n    .style('stroke-dasharray', '0, 0')\n    .attr('stroke-width', '1px')\n    .attr('d', 'M 0,1 L 6,7 M 6,1 L 0,7');\n  // this is actual shape for arrowhead\n};\n\nconst getC4ShapeFont = (cnf, typeC4Shape) => {\n  return {\n    fontFamily: cnf[typeC4Shape + 'FontFamily'],\n    fontSize: cnf[typeC4Shape + 'FontSize'],\n    fontWeight: cnf[typeC4Shape + 'FontWeight'],\n  };\n};\n\nconst _drawTextCandidateFunc = (function () {\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   */\n  function byText(content, g, x, y, width, height, textAttrs) {\n    const text = g\n      .append('text')\n      .attr('x', x + width / 2)\n      .attr('y', y + height / 2 + 5)\n      .style('text-anchor', 'middle')\n      .text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} conf\n   */\n  function byTspan(content, g, x, y, width, height, textAttrs, conf) {\n    const { fontSize, fontFamily, fontWeight } = conf;\n\n    const lines = content.split(common.lineBreakRegex);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * fontSize - (fontSize * (lines.length - 1)) / 2;\n      const text = g\n        .append('text')\n        .attr('x', x + width / 2)\n        .attr('y', y)\n        .style('text-anchor', 'middle')\n        .attr('dominant-baseline', 'middle')\n        .style('font-size', fontSize)\n        .style('font-weight', fontWeight)\n        .style('font-family', fontFamily);\n      text\n        .append('tspan')\n        // .attr('x', x + width / 2)\n        .attr('dy', dy)\n        .text(lines[i])\n        // .attr('y', y + height / 2)\n        .attr('alignment-baseline', 'mathematical');\n\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n\n  /**\n   * @param {any} content\n   * @param {any} g\n   * @param {any} x\n   * @param {any} y\n   * @param {any} width\n   * @param {any} height\n   * @param {any} textAttrs\n   * @param {any} conf\n   */\n  function byFo(content, g, x, y, width, height, textAttrs, conf) {\n    const s = g.append('switch');\n    const f = s\n      .append('foreignObject')\n      .attr('x', x)\n      .attr('y', y)\n      .attr('width', width)\n      .attr('height', height);\n\n    const text = f\n      .append('xhtml:div')\n      .style('display', 'table')\n      .style('height', '100%')\n      .style('width', '100%');\n\n    text\n      .append('div')\n      .style('display', 'table-cell')\n      .style('text-align', 'center')\n      .style('vertical-align', 'middle')\n      .text(content);\n\n    byTspan(content, s, x, y, width, height, textAttrs, conf);\n    _setTextAttrs(text, textAttrs);\n  }\n\n  /**\n   * @param {any} toText\n   * @param {any} fromTextAttrsDict\n   */\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (fromTextAttrsDict.hasOwnProperty(key)) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n\n  return function (conf) {\n    return conf.textPlacement === 'fo' ? byFo : conf.textPlacement === 'old' ? byText : byTspan;\n  };\n})();\n\nexport default {\n  drawRect,\n  drawBoundary,\n  drawC4Shape,\n  drawRels,\n  drawImage,\n  insertArrowHead,\n  insertArrowEnd,\n  insertArrowFilledHead,\n  insertDynamicNumber,\n  insertArrowCrossHead,\n  insertDatabaseIcon,\n  insertComputerIcon,\n  insertClockIcon,\n};\n\n// cspell:ignoreRegExp /'Mstartx.*/g\n", "import { select } from 'd3';\nimport svgDraw from './svgDraw.js';\nimport { log } from '../../logger.js';\nimport { parser } from './parser/c4Diagram.jison';\nimport common from '../common/common.js';\nimport c4Db from './c4Db.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport assignWithDepth from '../../assignWithDepth.js';\nimport { wrapLabel, calculateTextWidth, calculateTextHeight } from '../../utils.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\n\nlet globalBoundaryMaxX = 0,\n  globalBoundaryMaxY = 0;\n\nlet c4ShapeInRow = 4;\nlet c4BoundaryInRow = 2;\n\nparser.yy = c4Db;\n\nlet conf = {};\n\nclass Bounds {\n  constructor(diagObj) {\n    this.name = '';\n    this.data = {};\n    this.data.startx = undefined;\n    this.data.stopx = undefined;\n    this.data.starty = undefined;\n    this.data.stopy = undefined;\n    this.data.widthLimit = undefined;\n\n    this.nextData = {};\n    this.nextData.startx = undefined;\n    this.nextData.stopx = undefined;\n    this.nextData.starty = undefined;\n    this.nextData.stopy = undefined;\n    this.nextData.cnt = 0;\n\n    setConf(diagObj.db.getConfig());\n  }\n\n  setData(startx, stopx, starty, stopy) {\n    this.nextData.startx = this.data.startx = startx;\n    this.nextData.stopx = this.data.stopx = stopx;\n    this.nextData.starty = this.data.starty = starty;\n    this.nextData.stopy = this.data.stopy = stopy;\n  }\n\n  updateVal(obj, key, val, fun) {\n    if (obj[key] === undefined) {\n      obj[key] = val;\n    } else {\n      obj[key] = fun(val, obj[key]);\n    }\n  }\n\n  insert(c4Shape) {\n    this.nextData.cnt = this.nextData.cnt + 1;\n    let _startx =\n      this.nextData.startx === this.nextData.stopx\n        ? this.nextData.stopx + c4Shape.margin\n        : this.nextData.stopx + c4Shape.margin * 2;\n    let _stopx = _startx + c4Shape.width;\n    let _starty = this.nextData.starty + c4Shape.margin * 2;\n    let _stopy = _starty + c4Shape.height;\n    if (\n      _startx >= this.data.widthLimit ||\n      _stopx >= this.data.widthLimit ||\n      this.nextData.cnt > c4ShapeInRow\n    ) {\n      _startx = this.nextData.startx + c4Shape.margin + conf.nextLinePaddingX;\n      _starty = this.nextData.stopy + c4Shape.margin * 2;\n\n      this.nextData.stopx = _stopx = _startx + c4Shape.width;\n      this.nextData.starty = this.nextData.stopy;\n      this.nextData.stopy = _stopy = _starty + c4Shape.height;\n      this.nextData.cnt = 1;\n    }\n\n    c4Shape.x = _startx;\n    c4Shape.y = _starty;\n\n    this.updateVal(this.data, 'startx', _startx, Math.min);\n    this.updateVal(this.data, 'starty', _starty, Math.min);\n    this.updateVal(this.data, 'stopx', _stopx, Math.max);\n    this.updateVal(this.data, 'stopy', _stopy, Math.max);\n\n    this.updateVal(this.nextData, 'startx', _startx, Math.min);\n    this.updateVal(this.nextData, 'starty', _starty, Math.min);\n    this.updateVal(this.nextData, 'stopx', _stopx, Math.max);\n    this.updateVal(this.nextData, 'stopy', _stopy, Math.max);\n  }\n\n  init(diagObj) {\n    this.name = '';\n    this.data = {\n      startx: undefined,\n      stopx: undefined,\n      starty: undefined,\n      stopy: undefined,\n      widthLimit: undefined,\n    };\n    this.nextData = {\n      startx: undefined,\n      stopx: undefined,\n      starty: undefined,\n      stopy: undefined,\n      cnt: 0,\n    };\n    setConf(diagObj.db.getConfig());\n  }\n\n  bumpLastMargin(margin) {\n    this.data.stopx += margin;\n    this.data.stopy += margin;\n  }\n}\n\nexport const setConf = function (cnf) {\n  assignWithDepth(conf, cnf);\n\n  if (cnf.fontFamily) {\n    conf.personFontFamily = conf.systemFontFamily = conf.messageFontFamily = cnf.fontFamily;\n  }\n  if (cnf.fontSize) {\n    conf.personFontSize = conf.systemFontSize = conf.messageFontSize = cnf.fontSize;\n  }\n  if (cnf.fontWeight) {\n    conf.personFontWeight = conf.systemFontWeight = conf.messageFontWeight = cnf.fontWeight;\n  }\n};\n\nconst c4ShapeFont = (cnf, typeC4Shape) => {\n  return {\n    fontFamily: cnf[typeC4Shape + 'FontFamily'],\n    fontSize: cnf[typeC4Shape + 'FontSize'],\n    fontWeight: cnf[typeC4Shape + 'FontWeight'],\n  };\n};\n\nconst boundaryFont = (cnf) => {\n  return {\n    fontFamily: cnf.boundaryFontFamily,\n    fontSize: cnf.boundaryFontSize,\n    fontWeight: cnf.boundaryFontWeight,\n  };\n};\n\nconst messageFont = (cnf) => {\n  return {\n    fontFamily: cnf.messageFontFamily,\n    fontSize: cnf.messageFontSize,\n    fontWeight: cnf.messageFontWeight,\n  };\n};\n\n/**\n * @param textType\n * @param c4Shape\n * @param c4ShapeTextWrap\n * @param textConf\n * @param textLimitWidth\n */\nfunction calcC4ShapeTextWH(textType, c4Shape, c4ShapeTextWrap, textConf, textLimitWidth) {\n  if (!c4Shape[textType].width) {\n    if (c4ShapeTextWrap) {\n      c4Shape[textType].text = wrapLabel(c4Shape[textType].text, textLimitWidth, textConf);\n      c4Shape[textType].textLines = c4Shape[textType].text.split(common.lineBreakRegex).length;\n      // c4Shape[textType].width = calculateTextWidth(c4Shape[textType].text, textConf);\n      c4Shape[textType].width = textLimitWidth;\n      // c4Shape[textType].height = c4Shape[textType].textLines * textConf.fontSize;\n      c4Shape[textType].height = calculateTextHeight(c4Shape[textType].text, textConf);\n    } else {\n      let lines = c4Shape[textType].text.split(common.lineBreakRegex);\n      c4Shape[textType].textLines = lines.length;\n      let lineHeight = 0;\n      c4Shape[textType].height = 0;\n      c4Shape[textType].width = 0;\n      for (const line of lines) {\n        c4Shape[textType].width = Math.max(\n          calculateTextWidth(line, textConf),\n          c4Shape[textType].width\n        );\n        lineHeight = calculateTextHeight(line, textConf);\n        c4Shape[textType].height = c4Shape[textType].height + lineHeight;\n      }\n      // c4Shapes[textType].height = c4Shapes[textType].textLines * textConf.fontSize;\n    }\n  }\n}\n\nexport const drawBoundary = function (diagram, boundary, bounds) {\n  boundary.x = bounds.data.startx;\n  boundary.y = bounds.data.starty;\n  boundary.width = bounds.data.stopx - bounds.data.startx;\n  boundary.height = bounds.data.stopy - bounds.data.starty;\n\n  boundary.label.y = conf.c4ShapeMargin - 35;\n\n  let boundaryTextWrap = boundary.wrap && conf.wrap;\n  let boundaryLabelConf = boundaryFont(conf);\n  boundaryLabelConf.fontSize = boundaryLabelConf.fontSize + 2;\n  boundaryLabelConf.fontWeight = 'bold';\n  let textLimitWidth = calculateTextWidth(boundary.label.text, boundaryLabelConf);\n  calcC4ShapeTextWH('label', boundary, boundaryTextWrap, boundaryLabelConf, textLimitWidth);\n\n  svgDraw.drawBoundary(diagram, boundary, conf);\n};\n\nexport const drawC4ShapeArray = function (currentBounds, diagram, c4ShapeArray, c4ShapeKeys) {\n  // Upper Y is relative point\n  let Y = 0;\n  // Draw the c4ShapeArray\n  for (const c4ShapeKey of c4ShapeKeys) {\n    Y = 0;\n    const c4Shape = c4ShapeArray[c4ShapeKey];\n\n    // calc c4 shape type width and height\n\n    let c4ShapeTypeConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n    c4ShapeTypeConf.fontSize = c4ShapeTypeConf.fontSize - 2;\n    c4Shape.typeC4Shape.width = calculateTextWidth(\n      '«' + c4Shape.typeC4Shape.text + '»',\n      c4ShapeTypeConf\n    );\n    c4Shape.typeC4Shape.height = c4ShapeTypeConf.fontSize + 2;\n    c4Shape.typeC4Shape.Y = conf.c4ShapePadding;\n    Y = c4Shape.typeC4Shape.Y + c4Shape.typeC4Shape.height - 4;\n\n    // set image width and height c4Shape.x + c4Shape.width / 2 - 24, c4Shape.y + 28\n    // let imageWidth = 0,\n    //   imageHeight = 0,\n    //   imageY = 0;\n    //\n    c4Shape.image = { width: 0, height: 0, Y: 0 };\n    switch (c4Shape.typeC4Shape.text) {\n      case 'person':\n      case 'external_person':\n        c4Shape.image.width = 48;\n        c4Shape.image.height = 48;\n        c4Shape.image.Y = Y;\n        Y = c4Shape.image.Y + c4Shape.image.height;\n        break;\n    }\n    if (c4Shape.sprite) {\n      c4Shape.image.width = 48;\n      c4Shape.image.height = 48;\n      c4Shape.image.Y = Y;\n      Y = c4Shape.image.Y + c4Shape.image.height;\n    }\n\n    // Y = conf.c4ShapePadding + c4Shape.image.height;\n\n    let c4ShapeTextWrap = c4Shape.wrap && conf.wrap;\n    let textLimitWidth = conf.width - conf.c4ShapePadding * 2;\n\n    let c4ShapeLabelConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n    c4ShapeLabelConf.fontSize = c4ShapeLabelConf.fontSize + 2;\n    c4ShapeLabelConf.fontWeight = 'bold';\n    calcC4ShapeTextWH('label', c4Shape, c4ShapeTextWrap, c4ShapeLabelConf, textLimitWidth);\n    c4Shape.label.Y = Y + 8;\n    Y = c4Shape.label.Y + c4Shape.label.height;\n\n    if (c4Shape.type && c4Shape.type.text !== '') {\n      c4Shape.type.text = '[' + c4Shape.type.text + ']';\n      let c4ShapeTypeConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n      calcC4ShapeTextWH('type', c4Shape, c4ShapeTextWrap, c4ShapeTypeConf, textLimitWidth);\n      c4Shape.type.Y = Y + 5;\n      Y = c4Shape.type.Y + c4Shape.type.height;\n    } else if (c4Shape.techn && c4Shape.techn.text !== '') {\n      c4Shape.techn.text = '[' + c4Shape.techn.text + ']';\n      let c4ShapeTechnConf = c4ShapeFont(conf, c4Shape.techn.text);\n      calcC4ShapeTextWH('techn', c4Shape, c4ShapeTextWrap, c4ShapeTechnConf, textLimitWidth);\n      c4Shape.techn.Y = Y + 5;\n      Y = c4Shape.techn.Y + c4Shape.techn.height;\n    }\n\n    let rectHeight = Y;\n    let rectWidth = c4Shape.label.width;\n\n    if (c4Shape.descr && c4Shape.descr.text !== '') {\n      let c4ShapeDescrConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n      calcC4ShapeTextWH('descr', c4Shape, c4ShapeTextWrap, c4ShapeDescrConf, textLimitWidth);\n      c4Shape.descr.Y = Y + 20;\n      Y = c4Shape.descr.Y + c4Shape.descr.height;\n\n      rectWidth = Math.max(c4Shape.label.width, c4Shape.descr.width);\n      rectHeight = Y - c4Shape.descr.textLines * 5;\n    }\n\n    rectWidth = rectWidth + conf.c4ShapePadding;\n    // let rectHeight =\n\n    c4Shape.width = Math.max(c4Shape.width || conf.width, rectWidth, conf.width);\n    c4Shape.height = Math.max(c4Shape.height || conf.height, rectHeight, conf.height);\n    c4Shape.margin = c4Shape.margin || conf.c4ShapeMargin;\n\n    currentBounds.insert(c4Shape);\n\n    svgDraw.drawC4Shape(diagram, c4Shape, conf);\n  }\n\n  currentBounds.bumpLastMargin(conf.c4ShapeMargin);\n};\n\nclass Point {\n  constructor(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n}\n\n/* * *\n * Get the intersection of the line between the center point of a rectangle and a point outside the rectangle.\n * Algorithm idea.\n * Using a point outside the rectangle as the coordinate origin, the graph is divided into four quadrants, and each quadrant is divided into two cases, with separate treatment on the coordinate axes\n * 1. The case of coordinate axes.\n * 1. The case of the negative x-axis\n * 2. The case of the positive x-axis\n * 3. The case of the positive y-axis\n * 4. The negative y-axis case\n * 2. Quadrant cases.\n * 2.1. first quadrant: the case where the line intersects the left side of the rectangle; the case where it intersects the lower side of the rectangle\n * 2.2. second quadrant: the case where the line intersects the right side of the rectangle; the case where it intersects the lower edge of the rectangle\n * 2.3. third quadrant: the case where the line intersects the right side of the rectangle; the case where it intersects the upper edge of the rectangle\n * 2.4. fourth quadrant: the case where the line intersects the left side of the rectangle; the case where it intersects the upper side of the rectangle\n *\n */\nlet getIntersectPoint = function (fromNode, endPoint) {\n  let x1 = fromNode.x;\n\n  let y1 = fromNode.y;\n\n  let x2 = endPoint.x;\n\n  let y2 = endPoint.y;\n\n  let fromCenterX = x1 + fromNode.width / 2;\n\n  let fromCenterY = y1 + fromNode.height / 2;\n\n  let dx = Math.abs(x1 - x2);\n\n  let dy = Math.abs(y1 - y2);\n\n  let tanDYX = dy / dx;\n\n  let fromDYX = fromNode.height / fromNode.width;\n\n  let returnPoint = null;\n\n  if (y1 == y2 && x1 < x2) {\n    returnPoint = new Point(x1 + fromNode.width, fromCenterY);\n  } else if (y1 == y2 && x1 > x2) {\n    returnPoint = new Point(x1, fromCenterY);\n  } else if (x1 == x2 && y1 < y2) {\n    returnPoint = new Point(fromCenterX, y1 + fromNode.height);\n  } else if (x1 == x2 && y1 > y2) {\n    returnPoint = new Point(fromCenterX, y1);\n  }\n\n  if (x1 > x2 && y1 < y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1, fromCenterY + (tanDYX * fromNode.width) / 2);\n    } else {\n      returnPoint = new Point(\n        fromCenterX - ((dx / dy) * fromNode.height) / 2,\n        y1 + fromNode.height\n      );\n    }\n  } else if (x1 < x2 && y1 < y2) {\n    //\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1 + fromNode.width, fromCenterY + (tanDYX * fromNode.width) / 2);\n    } else {\n      returnPoint = new Point(\n        fromCenterX + ((dx / dy) * fromNode.height) / 2,\n        y1 + fromNode.height\n      );\n    }\n  } else if (x1 < x2 && y1 > y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1 + fromNode.width, fromCenterY - (tanDYX * fromNode.width) / 2);\n    } else {\n      returnPoint = new Point(fromCenterX + ((fromNode.height / 2) * dx) / dy, y1);\n    }\n  } else if (x1 > x2 && y1 > y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1, fromCenterY - (fromNode.width / 2) * tanDYX);\n    } else {\n      returnPoint = new Point(fromCenterX - ((fromNode.height / 2) * dx) / dy, y1);\n    }\n  }\n  return returnPoint;\n};\n\nlet getIntersectPoints = function (fromNode, endNode) {\n  let endIntersectPoint = { x: 0, y: 0 };\n  endIntersectPoint.x = endNode.x + endNode.width / 2;\n  endIntersectPoint.y = endNode.y + endNode.height / 2;\n  let startPoint = getIntersectPoint(fromNode, endIntersectPoint);\n\n  endIntersectPoint.x = fromNode.x + fromNode.width / 2;\n  endIntersectPoint.y = fromNode.y + fromNode.height / 2;\n  let endPoint = getIntersectPoint(endNode, endIntersectPoint);\n  return { startPoint: startPoint, endPoint: endPoint };\n};\n\nexport const drawRels = function (diagram, rels, getC4ShapeObj, diagObj) {\n  let i = 0;\n  for (let rel of rels) {\n    i = i + 1;\n    let relTextWrap = rel.wrap && conf.wrap;\n    let relConf = messageFont(conf);\n    let diagramType = diagObj.db.getC4Type();\n    if (diagramType === 'C4Dynamic') {\n      rel.label.text = i + ': ' + rel.label.text;\n    }\n    let textLimitWidth = calculateTextWidth(rel.label.text, relConf);\n    calcC4ShapeTextWH('label', rel, relTextWrap, relConf, textLimitWidth);\n\n    if (rel.techn && rel.techn.text !== '') {\n      textLimitWidth = calculateTextWidth(rel.techn.text, relConf);\n      calcC4ShapeTextWH('techn', rel, relTextWrap, relConf, textLimitWidth);\n    }\n\n    if (rel.descr && rel.descr.text !== '') {\n      textLimitWidth = calculateTextWidth(rel.descr.text, relConf);\n      calcC4ShapeTextWH('descr', rel, relTextWrap, relConf, textLimitWidth);\n    }\n\n    let fromNode = getC4ShapeObj(rel.from);\n    let endNode = getC4ShapeObj(rel.to);\n    let points = getIntersectPoints(fromNode, endNode);\n    rel.startPoint = points.startPoint;\n    rel.endPoint = points.endPoint;\n  }\n  svgDraw.drawRels(diagram, rels, conf);\n};\n\n/**\n * @param diagram\n * @param parentBoundaryAlias\n * @param parentBounds\n * @param currentBoundaries\n * @param diagObj\n */\nfunction drawInsideBoundary(\n  diagram,\n  parentBoundaryAlias,\n  parentBounds,\n  currentBoundaries,\n  diagObj\n) {\n  let currentBounds = new Bounds(diagObj);\n  // Calculate the width limit of the boundary.  label/type 的长度，\n  currentBounds.data.widthLimit =\n    parentBounds.data.widthLimit / Math.min(c4BoundaryInRow, currentBoundaries.length);\n  // Math.min(\n  //   conf.width * conf.c4ShapeInRow + conf.c4ShapeMargin * conf.c4ShapeInRow * 2,\n  //   parentBounds.data.widthLimit / Math.min(conf.c4BoundaryInRow, currentBoundaries.length)\n  // );\n  for (let [i, currentBoundary] of currentBoundaries.entries()) {\n    let Y = 0;\n    currentBoundary.image = { width: 0, height: 0, Y: 0 };\n    if (currentBoundary.sprite) {\n      currentBoundary.image.width = 48;\n      currentBoundary.image.height = 48;\n      currentBoundary.image.Y = Y;\n      Y = currentBoundary.image.Y + currentBoundary.image.height;\n    }\n\n    let currentBoundaryTextWrap = currentBoundary.wrap && conf.wrap;\n\n    let currentBoundaryLabelConf = boundaryFont(conf);\n    currentBoundaryLabelConf.fontSize = currentBoundaryLabelConf.fontSize + 2;\n    currentBoundaryLabelConf.fontWeight = 'bold';\n    calcC4ShapeTextWH(\n      'label',\n      currentBoundary,\n      currentBoundaryTextWrap,\n      currentBoundaryLabelConf,\n      currentBounds.data.widthLimit\n    );\n    currentBoundary.label.Y = Y + 8;\n    Y = currentBoundary.label.Y + currentBoundary.label.height;\n\n    if (currentBoundary.type && currentBoundary.type.text !== '') {\n      currentBoundary.type.text = '[' + currentBoundary.type.text + ']';\n      let currentBoundaryTypeConf = boundaryFont(conf);\n      calcC4ShapeTextWH(\n        'type',\n        currentBoundary,\n        currentBoundaryTextWrap,\n        currentBoundaryTypeConf,\n        currentBounds.data.widthLimit\n      );\n      currentBoundary.type.Y = Y + 5;\n      Y = currentBoundary.type.Y + currentBoundary.type.height;\n    }\n\n    if (currentBoundary.descr && currentBoundary.descr.text !== '') {\n      let currentBoundaryDescrConf = boundaryFont(conf);\n      currentBoundaryDescrConf.fontSize = currentBoundaryDescrConf.fontSize - 2;\n      calcC4ShapeTextWH(\n        'descr',\n        currentBoundary,\n        currentBoundaryTextWrap,\n        currentBoundaryDescrConf,\n        currentBounds.data.widthLimit\n      );\n      currentBoundary.descr.Y = Y + 20;\n      Y = currentBoundary.descr.Y + currentBoundary.descr.height;\n    }\n\n    if (i == 0 || i % c4BoundaryInRow === 0) {\n      // Calculate the drawing start point of the currentBoundaries.\n      let _x = parentBounds.data.startx + conf.diagramMarginX;\n      let _y = parentBounds.data.stopy + conf.diagramMarginY + Y;\n\n      currentBounds.setData(_x, _x, _y, _y);\n    } else {\n      // Calculate the drawing start point of the currentBoundaries.\n      let _x =\n        currentBounds.data.stopx !== currentBounds.data.startx\n          ? currentBounds.data.stopx + conf.diagramMarginX\n          : currentBounds.data.startx;\n      let _y = currentBounds.data.starty;\n\n      currentBounds.setData(_x, _x, _y, _y);\n    }\n    currentBounds.name = currentBoundary.alias;\n    let currentPersonOrSystemArray = diagObj.db.getC4ShapeArray(currentBoundary.alias);\n    let currentPersonOrSystemKeys = diagObj.db.getC4ShapeKeys(currentBoundary.alias);\n\n    if (currentPersonOrSystemKeys.length > 0) {\n      drawC4ShapeArray(\n        currentBounds,\n        diagram,\n        currentPersonOrSystemArray,\n        currentPersonOrSystemKeys\n      );\n    }\n    parentBoundaryAlias = currentBoundary.alias;\n    let nextCurrentBoundaries = diagObj.db.getBoundarys(parentBoundaryAlias);\n\n    if (nextCurrentBoundaries.length > 0) {\n      // draw boundary inside currentBoundary\n      drawInsideBoundary(\n        diagram,\n        parentBoundaryAlias,\n        currentBounds,\n        nextCurrentBoundaries,\n        diagObj\n      );\n    }\n    // draw boundary\n    if (currentBoundary.alias !== 'global') {\n      drawBoundary(diagram, currentBoundary, currentBounds);\n    }\n    parentBounds.data.stopy = Math.max(\n      currentBounds.data.stopy + conf.c4ShapeMargin,\n      parentBounds.data.stopy\n    );\n    parentBounds.data.stopx = Math.max(\n      currentBounds.data.stopx + conf.c4ShapeMargin,\n      parentBounds.data.stopx\n    );\n    globalBoundaryMaxX = Math.max(globalBoundaryMaxX, parentBounds.data.stopx);\n    globalBoundaryMaxY = Math.max(globalBoundaryMaxY, parentBounds.data.stopy);\n  }\n}\n\n/**\n * Draws a sequenceDiagram in the tag with id: id based on the graph definition in text.\n *\n * @param {any} _text\n * @param {any} id\n * @param {any} _version\n * @param diagObj\n */\nexport const draw = function (_text, id, _version, diagObj) {\n  conf = getConfig().c4;\n  const securityLevel = getConfig().securityLevel;\n  // Handle root and Document for when rendering in sandbox mode\n  let sandboxElement;\n  if (securityLevel === 'sandbox') {\n    sandboxElement = select('#i' + id);\n  }\n  const root =\n    securityLevel === 'sandbox'\n      ? select(sandboxElement.nodes()[0].contentDocument.body)\n      : select('body');\n\n  let db = diagObj.db;\n\n  diagObj.db.setWrap(conf.wrap);\n\n  c4ShapeInRow = db.getC4ShapeInRow();\n  c4BoundaryInRow = db.getC4BoundaryInRow();\n\n  log.debug(`C:${JSON.stringify(conf, null, 2)}`);\n\n  const diagram =\n    securityLevel === 'sandbox' ? root.select(`[id=\"${id}\"]`) : select(`[id=\"${id}\"]`);\n\n  svgDraw.insertComputerIcon(diagram);\n  svgDraw.insertDatabaseIcon(diagram);\n  svgDraw.insertClockIcon(diagram);\n\n  let screenBounds = new Bounds(diagObj);\n\n  screenBounds.setData(\n    conf.diagramMarginX,\n    conf.diagramMarginX,\n    conf.diagramMarginY,\n    conf.diagramMarginY\n  );\n\n  screenBounds.data.widthLimit = screen.availWidth;\n  globalBoundaryMaxX = conf.diagramMarginX;\n  globalBoundaryMaxY = conf.diagramMarginY;\n\n  const title = diagObj.db.getTitle();\n  let currentBoundaries = diagObj.db.getBoundarys('');\n  // switch (c4type) {\n  //   case 'C4Context':\n  drawInsideBoundary(diagram, '', screenBounds, currentBoundaries, diagObj);\n  //     break;\n  // }\n\n  // The arrow head definition is attached to the svg once\n  svgDraw.insertArrowHead(diagram);\n  svgDraw.insertArrowEnd(diagram);\n  svgDraw.insertArrowCrossHead(diagram);\n  svgDraw.insertArrowFilledHead(diagram);\n\n  drawRels(diagram, diagObj.db.getRels(), diagObj.db.getC4Shape, diagObj);\n\n  screenBounds.data.stopx = globalBoundaryMaxX;\n  screenBounds.data.stopy = globalBoundaryMaxY;\n\n  const box = screenBounds.data;\n\n  // Make sure the height of the diagram supports long menus.\n  let boxHeight = box.stopy - box.starty;\n\n  let height = boxHeight + 2 * conf.diagramMarginY;\n\n  // Make sure the width of the diagram supports wide menus.\n  let boxWidth = box.stopx - box.startx;\n  const width = boxWidth + 2 * conf.diagramMarginX;\n\n  if (title) {\n    diagram\n      .append('text')\n      .text(title)\n      .attr('x', (box.stopx - box.startx) / 2 - 4 * conf.diagramMarginX)\n      .attr('y', box.starty + conf.diagramMarginY);\n  }\n\n  configureSvgSize(diagram, height, width, conf.useMaxWidth);\n\n  const extraVertForTitle = title ? 60 : 0;\n  diagram.attr(\n    'viewBox',\n    box.startx -\n      conf.diagramMarginX +\n      ' -' +\n      (conf.diagramMarginY + extraVertForTitle) +\n      ' ' +\n      width +\n      ' ' +\n      (height + extraVertForTitle)\n  );\n\n  log.debug(`models:`, box);\n};\n\nexport default {\n  drawPersonOrSystemArray: drawC4ShapeArray,\n  drawBoundary,\n  setConf,\n  draw,\n};\n", "const getStyles = (options) =>\n  `.person {\n    stroke: ${options.personBorder};\n    fill: ${options.personBkg};\n  }\n`;\n\nexport default getStyles;\n", "// @ts-ignore: JISON doesn't support types\nimport parser from './parser/c4Diagram.jison';\nimport db from './c4Db.js';\nimport renderer from './c4Renderer.js';\nimport styles from './styles.js';\nimport type { MermaidConfig } from '../../config.type.js';\nimport type { DiagramDefinition } from '../../diagram-api/types.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  db,\n  renderer,\n  styles,\n  init: ({ c4, wrap }: MermaidConfig) => {\n    renderer.setConf(c4);\n    db.setWrap(wrap);\n  },\n};\n"], "mappings": "+VAyEA,IAAIA,GAAU,UAAU,CACxB,IAAIC,EAAEC,EAAA,SAASC,GAAEC,EAAEH,EAAEI,EAAE,CAAC,IAAIJ,EAAEA,GAAG,CAAC,EAAEI,EAAEF,GAAE,OAAOE,IAAIJ,EAAEE,GAAEE,CAAC,CAAC,EAAED,EAAE,CAAC,OAAOH,CAAC,EAAhE,KAAkEK,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,EAAE,EAAE,EAAEC,GAAI,CAAC,GAAG,EAAE,EAAEC,GAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,GAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,EAAE,EAAE,EAAEC,EAAI,CAAC,GAAG,GAAG,EAAE,EAAEC,GAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEC,GAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAEC,GAAI,CAAC,GAAG,EAAE,EAAEC,GAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EACrhC5D,GAAS,CAAC,MAAOE,EAAA,UAAkB,CAAE,EAApB,SACrB,GAAI,CAAC,EACL,SAAU,CAAC,MAAQ,EAAE,MAAQ,EAAE,WAAa,EAAE,UAAY,EAAE,aAAe,EAAE,aAAe,EAAE,aAAe,EAAE,aAAe,EAAE,YAAc,GAAG,WAAa,GAAG,QAAU,GAAG,WAAa,GAAG,IAAM,GAAG,aAAe,GAAG,aAAe,GAAG,WAAa,GAAG,cAAgB,GAAG,gBAAkB,GAAG,kBAAoB,GAAG,eAAiB,GAAG,MAAQ,GAAG,eAAiB,GAAG,UAAY,GAAG,gBAAkB,GAAG,UAAY,GAAG,gBAAkB,GAAG,0BAA4B,GAAG,kBAAoB,GAAG,uBAAyB,GAAG,sBAAwB,GAAG,cAAgB,GAAG,OAAS,GAAG,oBAAsB,GAAG,WAAa,GAAG,gBAAkB,GAAG,SAAW,GAAG,mBAAqB,GAAG,KAAO,GAAG,OAAS,GAAG,OAAS,GAAG,OAAS,GAAG,iBAAmB,GAAG,OAAS,GAAG,WAAa,GAAG,OAAS,GAAG,UAAY,GAAG,aAAe,GAAG,WAAa,GAAG,cAAgB,GAAG,iBAAmB,GAAG,UAAY,GAAG,aAAe,GAAG,gBAAkB,GAAG,cAAgB,GAAG,iBAAmB,GAAG,oBAAsB,GAAG,UAAY,GAAG,aAAe,GAAG,gBAAkB,GAAG,cAAgB,GAAG,iBAAmB,GAAG,oBAAsB,GAAG,IAAM,GAAG,MAAQ,GAAG,MAAQ,GAAG,MAAQ,GAAG,MAAQ,GAAG,MAAQ,GAAG,MAAQ,GAAG,UAAY,GAAG,gBAAkB,GAAG,iBAAmB,GAAG,qBAAuB,GAAG,UAAY,GAAG,IAAM,GAAG,QAAU,GAAG,UAAY,GAAG,UAAY,GAAG,gBAAkB,GAAG,QAAU,EAAE,KAAO,CAAC,EACt2C,WAAY,CAAC,EAAE,QAAQ,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,GAAG,aAAa,GAAG,UAAU,GAAG,MAAM,GAAG,eAAe,GAAG,eAAe,GAAG,aAAa,GAAG,gBAAgB,GAAG,QAAQ,GAAG,iBAAiB,GAAG,YAAY,GAAG,kBAAkB,GAAG,YAAY,GAAG,kBAAkB,GAAG,4BAA4B,GAAG,SAAS,GAAG,sBAAsB,GAAG,kBAAkB,GAAG,WAAW,GAAG,qBAAqB,GAAG,OAAO,GAAG,SAAS,GAAG,SAAS,GAAG,SAAS,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS,GAAG,YAAY,GAAG,eAAe,GAAG,aAAa,GAAG,gBAAgB,GAAG,mBAAmB,GAAG,YAAY,GAAG,eAAe,GAAG,kBAAkB,GAAG,gBAAgB,GAAG,mBAAmB,GAAG,sBAAsB,GAAG,YAAY,GAAG,eAAe,GAAG,kBAAkB,GAAG,gBAAgB,GAAG,mBAAmB,GAAG,sBAAsB,GAAG,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,YAAY,GAAG,kBAAkB,GAAG,mBAAmB,GAAG,uBAAuB,GAAG,MAAM,GAAG,UAAU,GAAG,YAAY,GAAG,YAAY,GAAG,iBAAiB,EACrjC,aAAc,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAC7hB,cAAeA,EAAA,SAAmB2D,EAAQC,EAAQC,EAAUC,EAAIC,EAAyBC,EAAiBC,GAAiB,CAG3H,IAAIC,EAAKF,EAAG,OAAS,EACrB,OAAQD,EAAS,CACjB,IAAK,GACJD,EAAG,aAAa,IAAI,EACrB,MACA,IAAK,GACJA,EAAG,aAAa,IAAI,EACrB,MACA,IAAK,GACJA,EAAG,aAAa,IAAI,EACrB,MACA,IAAK,GACJA,EAAG,aAAa,IAAI,EACrB,MACA,IAAK,GAAG,IAAK,GAAG,IAAK,IAAI,IAAK,IAAI,IAAK,IACvCA,EAAG,UAAUE,EAAGE,EAAG,CAAC,CAAC,EACrB,MACA,IAAK,IACLJ,EAAG,SAASE,EAAGE,CAAE,EAAE,UAAU,CAAC,CAAC,EAAE,KAAK,EAAEF,EAAGE,CAAE,EAAE,UAAU,CAAC,EAC1D,MACA,IAAK,IACLJ,EAAG,kBAAkBE,EAAGE,CAAE,EAAE,UAAU,EAAE,CAAC,EAAE,KAAK,EAAEF,EAAGE,CAAE,EAAE,UAAU,EAAE,EACrE,MACA,IAAK,IACJ,KAAK,EAAEF,EAAGE,CAAE,EAAE,KAAK,EAAEJ,EAAG,SAAS,KAAK,CAAC,EACxC,MACA,IAAK,IAAI,IAAK,IACb,KAAK,EAAEE,EAAGE,CAAE,EAAE,KAAK,EAAEJ,EAAG,kBAAkB,KAAK,CAAC,EACjD,MACA,IAAK,IACLE,EAAGE,CAAE,EAAE,OAAO,EAAG,EAAG,YAAY,EAAGJ,EAAG,0BAA0B,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EACxF,MACA,IAAK,IACLF,EAAGE,CAAE,EAAE,OAAO,EAAG,EAAG,QAAQ,EAAGJ,EAAG,0BAA0B,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EACpF,MACA,IAAK,IACLJ,EAAG,0BAA0B,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EACrD,MACA,IAAK,IACLF,EAAGE,CAAE,EAAE,OAAO,EAAG,EAAG,WAAW,EAAGJ,EAAG,qBAAqB,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EAClF,MACA,IAAK,IACLJ,EAAG,kBAAkB,OAAQ,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EACrD,MACA,IAAK,IACLJ,EAAG,kBAAkB,QAAS,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EACtD,MACA,IAAK,IACLJ,EAAG,kBAAkB,QAAS,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EACtD,MACA,IAAK,IACJJ,EAAG,sBAAsB,EAC1B,MACA,IAAK,IACLA,EAAG,kBAAkB,SAAU,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EACvD,MACA,IAAK,IACLJ,EAAG,kBAAkB,kBAAmB,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EAChE,MACA,IAAK,IACLJ,EAAG,kBAAkB,SAAU,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EACvD,MACA,IAAK,IACLJ,EAAG,kBAAkB,YAAa,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EAC1D,MACA,IAAK,IACLJ,EAAG,kBAAkB,eAAgB,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EAC7D,MACA,IAAK,IACLJ,EAAG,kBAAkB,kBAAmB,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EAChE,MACA,IAAK,IACLJ,EAAG,kBAAkB,qBAAsB,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EACnE,MACA,IAAK,IACLJ,EAAG,kBAAkB,wBAAyB,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EACtE,MACA,IAAK,IACLJ,EAAG,aAAa,YAAa,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EACrD,MACA,IAAK,IACLJ,EAAG,aAAa,eAAgB,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EACxD,MACA,IAAK,IACLJ,EAAG,aAAa,kBAAmB,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EAC3D,MACA,IAAK,IACLJ,EAAG,aAAa,qBAAsB,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EAC9D,MACA,IAAK,IACLJ,EAAG,aAAa,wBAAyB,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EACjE,MACA,IAAK,IACLJ,EAAG,aAAa,2BAA4B,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EACpE,MACA,IAAK,IACLJ,EAAG,aAAa,YAAa,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EACrD,MACA,IAAK,IACLJ,EAAG,aAAa,eAAgB,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EACxD,MACA,IAAK,IACLJ,EAAG,aAAa,kBAAmB,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EAC3D,MACA,IAAK,IACLJ,EAAG,aAAa,qBAAsB,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EAC9D,MACA,IAAK,IACLJ,EAAG,aAAa,wBAAyB,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EACjE,MACA,IAAK,IACLJ,EAAG,aAAa,2BAA4B,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EACpE,MACA,IAAK,IACLJ,EAAG,OAAO,MAAO,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EACzC,MACA,IAAK,IACLJ,EAAG,OAAO,QAAS,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EAC3C,MACA,IAAK,IACLJ,EAAG,OAAO,QAAS,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EAC3C,MACA,IAAK,IACLJ,EAAG,OAAO,QAAS,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EAC3C,MACA,IAAK,IACLJ,EAAG,OAAO,QAAS,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EAC3C,MACA,IAAK,IACLJ,EAAG,OAAO,QAAS,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EAC3C,MACA,IAAK,IACLJ,EAAG,OAAO,QAAS,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EAC3C,MACA,IAAK,IACLF,EAAGE,CAAE,EAAE,OAAO,EAAG,CAAC,EAAGJ,EAAG,OAAO,MAAO,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EAC9D,MACA,IAAK,IACLJ,EAAG,cAAc,kBAAmB,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EAC5D,MACA,IAAK,IACLJ,EAAG,eAAe,mBAAoB,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EAC9D,MACA,IAAK,IACLJ,EAAG,mBAAmB,uBAAwB,GAAGE,EAAGE,CAAE,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EACtE,MACA,IAAK,IACJ,KAAK,EAAI,CAACF,EAAGE,CAAE,CAAC,EACjB,MACA,IAAK,IACJF,EAAGE,CAAE,EAAE,QAAQF,EAAGE,EAAG,CAAC,CAAC,EAAG,KAAK,EAAEF,EAAGE,CAAE,EACvC,MACA,IAAK,IAAI,IAAK,IACZ,KAAK,EAAIF,EAAGE,CAAE,EAAE,KAAK,EACvB,MACA,IAAK,IACJ,IAAIC,GAAG,CAAC,EAAGA,GAAGH,EAAGE,EAAG,CAAC,EAAE,KAAK,CAAC,EAAEF,EAAGE,CAAE,EAAE,KAAK,EAAG,KAAK,EAAEC,GACtD,MACA,IAAK,IACH,KAAK,EAAI,GACX,KACA,CACA,EArKe,aAsKf,MAAO,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG/D,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,EAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG1C,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,EAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG1C,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,EAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG1C,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,EAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG1C,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,EAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE/C,EAAEgD,GAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAGtC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,EAAG,CAAC,EAAE/C,EAAEgD,GAAI,CAAC,EAAE,EAAE,CAAC,EAAEhD,EAAEiD,GAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EAAEjD,EAAEgD,GAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EAAEhD,EAAEkD,GAAI,CAAC,EAAE,EAAE,CAAC,EAAElD,EAAEkD,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAElD,EAAEkD,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAEtD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGJ,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG5C,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAG,GAAG,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,EAAG,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGI,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,GAAGH,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEtD,EAAEgD,GAAI,CAAC,EAAE,EAAE,CAAC,EAAEhD,EAAEiD,GAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG5C,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAET,EAAEgD,GAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG3C,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,GAAI,GAAGC,EAAG,CAAC,EAAE/C,EAAEkD,GAAI,CAAC,EAAE,EAAE,CAAC,EAAElD,EAAEkD,GAAI,CAAC,EAAE,EAAE,CAAC,EAAElD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEwD,GAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,GAAGL,EAAI,GAAGC,EAAI,GAAGC,EAAI,GAAGC,CAAG,CAAC,EAAEtD,EAAEyD,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAEzD,EAAEyD,GAAI,CAAC,EAAE,EAAE,CAAC,EAAEzD,EAAEyD,GAAI,CAAC,EAAE,EAAE,CAAC,EAAEzD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAEvD,EAAE0D,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE1D,EAAE0D,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE1D,EAAE0D,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE1D,EAAE0D,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE1D,EAAE0D,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE1D,EAAE0D,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE1D,EAAE0D,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE1D,EAAEiD,GAAI,CAAC,EAAE,EAAE,CAAC,EAAEjD,EAAEgD,GAAI,CAAC,EAAE,EAAE,CAAC,EAAEhD,EAAEwD,GAAI,CAAC,EAAE,EAAE,CAAC,EAAExD,EAAEyD,GAAI,CAAC,EAAE,EAAE,CAAC,EAAEzD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAEuD,EAAI,CAAC,EAAE,EAAE,CAAC,EAAEvD,EAAE2D,GAAI,CAAC,EAAE,EAAE,CAAC,EAAE3D,EAAE2D,GAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE3D,EAAE2D,GAAI,CAAC,EAAE,EAAE,CAAC,CAAC,EAC1rL,eAAgB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,EAC5H,WAAY1D,EAAA,SAAqBoE,EAAKC,EAAM,CACxC,GAAIA,EAAK,YACL,KAAK,MAAMD,CAAG,MACX,CACH,IAAIE,EAAQ,IAAI,MAAMF,CAAG,EACzB,MAAAE,EAAM,KAAOD,EACPC,CACV,CACJ,EARY,cASZ,MAAOtE,EAAA,SAAeuE,EAAO,CACzB,IAAIC,EAAO,KAAMC,EAAQ,CAAC,CAAC,EAAGC,EAAS,CAAC,EAAGC,EAAS,CAAC,IAAI,EAAGC,EAAS,CAAC,EAAGC,GAAQ,KAAK,MAAOlB,EAAS,GAAIE,GAAW,EAAGD,GAAS,EAAGkB,GAAa,EAAGC,GAAS,EAAGC,GAAM,EAClKC,GAAOL,EAAO,MAAM,KAAK,UAAW,CAAC,EACrCM,EAAQ,OAAO,OAAO,KAAK,KAAK,EAChCC,GAAc,CAAE,GAAI,CAAC,CAAE,EAC3B,QAASlF,MAAK,KAAK,GACX,OAAO,UAAU,eAAe,KAAK,KAAK,GAAIA,EAAC,IAC/CkF,GAAY,GAAGlF,EAAC,EAAI,KAAK,GAAGA,EAAC,GAGrCiF,EAAM,SAASX,EAAOY,GAAY,EAAE,EACpCA,GAAY,GAAG,MAAQD,EACvBC,GAAY,GAAG,OAAS,KACpB,OAAOD,EAAM,OAAU,MACvBA,EAAM,OAAS,CAAC,GAEpB,IAAIE,GAAQF,EAAM,OAClBN,EAAO,KAAKQ,EAAK,EACjB,IAAIC,GAASH,EAAM,SAAWA,EAAM,QAAQ,OACxC,OAAOC,GAAY,GAAG,YAAe,WACrC,KAAK,WAAaA,GAAY,GAAG,WAEjC,KAAK,WAAa,OAAO,eAAe,IAAI,EAAE,WAElD,SAASG,GAASC,EAAG,CACjBd,EAAM,OAASA,EAAM,OAAS,EAAIc,EAClCZ,EAAO,OAASA,EAAO,OAASY,EAChCX,EAAO,OAASA,EAAO,OAASW,CACpC,CAJSvF,EAAAsF,GAAA,YAKD,SAASE,IAAM,CACf,IAAIC,EACJ,OAAAA,EAAQf,EAAO,IAAI,GAAKQ,EAAM,IAAI,GAAKF,GACnC,OAAOS,GAAU,WACbA,aAAiB,QACjBf,EAASe,EACTA,EAAQf,EAAO,IAAI,GAEvBe,EAAQjB,EAAK,SAASiB,CAAK,GAAKA,GAE7BA,CACX,CAXazF,EAAAwF,GAAA,OAajB,QADIE,EAAQC,GAAgBC,GAAOC,EAAQC,GAAGC,GAAGC,GAAQ,CAAC,EAAGC,GAAGC,EAAKC,GAAUC,KAClE,CAUT,GATAR,GAAQnB,EAAMA,EAAM,OAAS,CAAC,EAC1B,KAAK,eAAemB,EAAK,EACzBC,EAAS,KAAK,eAAeD,EAAK,IAE9BF,IAAW,MAAQ,OAAOA,EAAU,OACpCA,EAASF,GAAI,GAEjBK,EAAShB,GAAMe,EAAK,GAAKf,GAAMe,EAAK,EAAEF,CAAM,GAE5C,OAAOG,EAAW,KAAe,CAACA,EAAO,QAAU,CAACA,EAAO,CAAC,EAAG,CAC/D,IAAIQ,GAAS,GACbD,GAAW,CAAC,EACZ,IAAKH,MAAKpB,GAAMe,EAAK,EACb,KAAK,WAAWK,EAAC,GAAKA,GAAIlB,IAC1BqB,GAAS,KAAK,IAAO,KAAK,WAAWH,EAAC,EAAI,GAAI,EAGlDf,EAAM,aACNmB,GAAS,wBAA0BxC,GAAW,GAAK;AAAA,EAAQqB,EAAM,aAAa,EAAI;AAAA,YAAiBkB,GAAS,KAAK,IAAI,EAAI,WAAc,KAAK,WAAWV,CAAM,GAAKA,GAAU,IAE5KW,GAAS,wBAA0BxC,GAAW,GAAK,iBAAmB6B,GAAUV,GAAM,eAAiB,KAAQ,KAAK,WAAWU,CAAM,GAAKA,GAAU,KAExJ,KAAK,WAAWW,GAAQ,CACpB,KAAMnB,EAAM,MACZ,MAAO,KAAK,WAAWQ,CAAM,GAAKA,EAClC,KAAMR,EAAM,SACZ,IAAKE,GACL,SAAUgB,EACd,CAAC,CACL,CACA,GAAIP,EAAO,CAAC,YAAa,OAASA,EAAO,OAAS,EAC9C,MAAM,IAAI,MAAM,oDAAsDD,GAAQ,YAAcF,CAAM,EAEtG,OAAQG,EAAO,CAAC,EAAG,CACnB,IAAK,GACDpB,EAAM,KAAKiB,CAAM,EACjBf,EAAO,KAAKO,EAAM,MAAM,EACxBN,EAAO,KAAKM,EAAM,MAAM,EACxBT,EAAM,KAAKoB,EAAO,CAAC,CAAC,EACpBH,EAAS,KACJC,IASDD,EAASC,GACTA,GAAiB,OATjB/B,GAASsB,EAAM,OACfvB,EAASuB,EAAM,OACfrB,GAAWqB,EAAM,SACjBE,GAAQF,EAAM,OACVJ,GAAa,GACbA,MAMR,MACJ,IAAK,GAwBD,GAvBAoB,EAAM,KAAK,aAAaL,EAAO,CAAC,CAAC,EAAE,CAAC,EACpCG,GAAM,EAAIrB,EAAOA,EAAO,OAASuB,CAAG,EACpCF,GAAM,GAAK,CACP,WAAYpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,WAC/C,UAAWtB,EAAOA,EAAO,OAAS,CAAC,EAAE,UACrC,aAAcA,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,aACjD,YAAatB,EAAOA,EAAO,OAAS,CAAC,EAAE,WAC3C,EACIS,KACAW,GAAM,GAAG,MAAQ,CACbpB,EAAOA,EAAO,QAAUsB,GAAO,EAAE,EAAE,MAAM,CAAC,EAC1CtB,EAAOA,EAAO,OAAS,CAAC,EAAE,MAAM,CAAC,CACrC,GAEJmB,GAAI,KAAK,cAAc,MAAMC,GAAO,CAChCrC,EACAC,GACAC,GACAsB,GAAY,GACZU,EAAO,CAAC,EACRlB,EACAC,CACJ,EAAE,OAAOK,EAAI,CAAC,EACV,OAAOc,GAAM,IACb,OAAOA,GAEPG,IACAzB,EAAQA,EAAM,MAAM,EAAG,GAAKyB,EAAM,CAAC,EACnCvB,EAASA,EAAO,MAAM,EAAG,GAAKuB,CAAG,EACjCtB,EAASA,EAAO,MAAM,EAAG,GAAKsB,CAAG,GAErCzB,EAAM,KAAK,KAAK,aAAaoB,EAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAC1ClB,EAAO,KAAKqB,GAAM,CAAC,EACnBpB,EAAO,KAAKoB,GAAM,EAAE,EACpBG,GAAWtB,GAAMJ,EAAMA,EAAM,OAAS,CAAC,CAAC,EAAEA,EAAMA,EAAM,OAAS,CAAC,CAAC,EACjEA,EAAM,KAAK0B,EAAQ,EACnB,MACJ,IAAK,GACD,MAAO,EACX,CACJ,CACA,MAAO,EACX,EA3IO,QA2IN,EAEGjB,GAAS,UAAU,CACvB,IAAIA,GAAS,CAEb,IAAI,EAEJ,WAAWlF,EAAA,SAAoBoE,EAAKC,EAAM,CAClC,GAAI,KAAK,GAAG,OACR,KAAK,GAAG,OAAO,WAAWD,EAAKC,CAAI,MAEnC,OAAM,IAAI,MAAMD,CAAG,CAE3B,EANO,cASX,SAASpE,EAAA,SAAUuE,EAAOT,EAAI,CACtB,YAAK,GAAKA,GAAM,KAAK,IAAM,CAAC,EAC5B,KAAK,OAASS,EACd,KAAK,MAAQ,KAAK,WAAa,KAAK,KAAO,GAC3C,KAAK,SAAW,KAAK,OAAS,EAC9B,KAAK,OAAS,KAAK,QAAU,KAAK,MAAQ,GAC1C,KAAK,eAAiB,CAAC,SAAS,EAChC,KAAK,OAAS,CACV,WAAY,EACZ,aAAc,EACd,UAAW,EACX,YAAa,CACjB,EACI,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAAC,EAAE,CAAC,GAE5B,KAAK,OAAS,EACP,IACX,EAlBK,YAqBT,MAAMvE,EAAA,UAAY,CACV,IAAIsG,EAAK,KAAK,OAAO,CAAC,EACtB,KAAK,QAAUA,EACf,KAAK,SACL,KAAK,SACL,KAAK,OAASA,EACd,KAAK,SAAWA,EAChB,IAAIC,EAAQD,EAAG,MAAM,iBAAiB,EACtC,OAAIC,GACA,KAAK,WACL,KAAK,OAAO,aAEZ,KAAK,OAAO,cAEZ,KAAK,QAAQ,QACb,KAAK,OAAO,MAAM,CAAC,IAGvB,KAAK,OAAS,KAAK,OAAO,MAAM,CAAC,EAC1BD,CACX,EApBE,SAuBN,MAAMtG,EAAA,SAAUsG,EAAI,CACZ,IAAIJ,EAAMI,EAAG,OACTC,EAAQD,EAAG,MAAM,eAAe,EAEpC,KAAK,OAASA,EAAK,KAAK,OACxB,KAAK,OAAS,KAAK,OAAO,OAAO,EAAG,KAAK,OAAO,OAASJ,CAAG,EAE5D,KAAK,QAAUA,EACf,IAAIM,EAAW,KAAK,MAAM,MAAM,eAAe,EAC/C,KAAK,MAAQ,KAAK,MAAM,OAAO,EAAG,KAAK,MAAM,OAAS,CAAC,EACvD,KAAK,QAAU,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,CAAC,EAEzDD,EAAM,OAAS,IACf,KAAK,UAAYA,EAAM,OAAS,GAEpC,IAAIR,EAAI,KAAK,OAAO,MAEpB,YAAK,OAAS,CACV,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,aAC1B,YAAaQ,GACRA,EAAM,SAAWC,EAAS,OAAS,KAAK,OAAO,aAAe,GAC5DA,EAASA,EAAS,OAASD,EAAM,MAAM,EAAE,OAASA,EAAM,CAAC,EAAE,OAChE,KAAK,OAAO,aAAeL,CACjC,EAEI,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAACH,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,KAAK,OAASG,CAAG,GAEvD,KAAK,OAAS,KAAK,OAAO,OACnB,IACX,EAhCE,SAmCN,KAAKlG,EAAA,UAAY,CACT,YAAK,MAAQ,GACN,IACX,EAHC,QAML,OAAOA,EAAA,UAAY,CACX,GAAI,KAAK,QAAQ,gBACb,KAAK,WAAa,OAElB,QAAO,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAAqI,KAAK,aAAa,EAAG,CAC9N,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACf,CAAC,EAGL,OAAO,IACX,EAZG,UAeP,KAAKA,EAAA,SAAUuF,EAAG,CACV,KAAK,MAAM,KAAK,MAAM,MAAMA,CAAC,CAAC,CAClC,EAFC,QAKL,UAAUvF,EAAA,UAAY,CACd,IAAIyG,EAAO,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,KAAK,MAAM,MAAM,EACzE,OAAQA,EAAK,OAAS,GAAK,MAAM,IAAMA,EAAK,OAAO,GAAG,EAAE,QAAQ,MAAO,EAAE,CAC7E,EAHM,aAMV,cAAczG,EAAA,UAAY,CAClB,IAAI0G,EAAO,KAAK,MAChB,OAAIA,EAAK,OAAS,KACdA,GAAQ,KAAK,OAAO,OAAO,EAAG,GAAGA,EAAK,MAAM,IAExCA,EAAK,OAAO,EAAE,EAAE,GAAKA,EAAK,OAAS,GAAK,MAAQ,KAAK,QAAQ,MAAO,EAAE,CAClF,EANU,iBASd,aAAa1G,EAAA,UAAY,CACjB,IAAI2G,EAAM,KAAK,UAAU,EACrBC,EAAI,IAAI,MAAMD,EAAI,OAAS,CAAC,EAAE,KAAK,GAAG,EAC1C,OAAOA,EAAM,KAAK,cAAc,EAAI;AAAA,EAAOC,EAAI,GACnD,EAJS,gBAOb,WAAW5G,EAAA,SAAS6G,EAAOC,EAAc,CACjC,IAAIrB,EACAc,EACAQ,EAwDJ,GAtDI,KAAK,QAAQ,kBAEbA,EAAS,CACL,SAAU,KAAK,SACf,OAAQ,CACJ,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,UAChB,aAAc,KAAK,OAAO,aAC1B,YAAa,KAAK,OAAO,WAC7B,EACA,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,QAAS,KAAK,QACd,QAAS,KAAK,QACd,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,OAAQ,KAAK,OACb,GAAI,KAAK,GACT,eAAgB,KAAK,eAAe,MAAM,CAAC,EAC3C,KAAM,KAAK,IACf,EACI,KAAK,QAAQ,SACbA,EAAO,OAAO,MAAQ,KAAK,OAAO,MAAM,MAAM,CAAC,IAIvDR,EAAQM,EAAM,CAAC,EAAE,MAAM,iBAAiB,EACpCN,IACA,KAAK,UAAYA,EAAM,QAE3B,KAAK,OAAS,CACV,WAAY,KAAK,OAAO,UACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,YAC1B,YAAaA,EACAA,EAAMA,EAAM,OAAS,CAAC,EAAE,OAASA,EAAMA,EAAM,OAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,OAC5E,KAAK,OAAO,YAAcM,EAAM,CAAC,EAAE,MACpD,EACA,KAAK,QAAUA,EAAM,CAAC,EACtB,KAAK,OAASA,EAAM,CAAC,EACrB,KAAK,QAAUA,EACf,KAAK,OAAS,KAAK,OAAO,OACtB,KAAK,QAAQ,SACb,KAAK,OAAO,MAAQ,CAAC,KAAK,OAAQ,KAAK,QAAU,KAAK,MAAM,GAEhE,KAAK,MAAQ,GACb,KAAK,WAAa,GAClB,KAAK,OAAS,KAAK,OAAO,MAAMA,EAAM,CAAC,EAAE,MAAM,EAC/C,KAAK,SAAWA,EAAM,CAAC,EACvBpB,EAAQ,KAAK,cAAc,KAAK,KAAM,KAAK,GAAI,KAAMqB,EAAc,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAClH,KAAK,MAAQ,KAAK,SAClB,KAAK,KAAO,IAEZrB,EACA,OAAOA,EACJ,GAAI,KAAK,WAAY,CAExB,QAASxF,KAAK8G,EACV,KAAK9G,CAAC,EAAI8G,EAAO9G,CAAC,EAEtB,MAAO,EACX,CACA,MAAO,EACX,EArEO,cAwEX,KAAKD,EAAA,UAAY,CACT,GAAI,KAAK,KACL,OAAO,KAAK,IAEX,KAAK,SACN,KAAK,KAAO,IAGhB,IAAIyF,EACAoB,EACAG,EACAC,EACC,KAAK,QACN,KAAK,OAAS,GACd,KAAK,MAAQ,IAGjB,QADIC,EAAQ,KAAK,cAAc,EACtBC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAE9B,GADAH,EAAY,KAAK,OAAO,MAAM,KAAK,MAAME,EAAMC,CAAC,CAAC,CAAC,EAC9CH,IAAc,CAACH,GAASG,EAAU,CAAC,EAAE,OAASH,EAAM,CAAC,EAAE,SAGvD,GAFAA,EAAQG,EACRC,EAAQE,EACJ,KAAK,QAAQ,gBAAiB,CAE9B,GADA1B,EAAQ,KAAK,WAAWuB,EAAWE,EAAMC,CAAC,CAAC,EACvC1B,IAAU,GACV,OAAOA,EACJ,GAAI,KAAK,WAAY,CACxBoB,EAAQ,GACR,QACJ,KAEI,OAAO,EAEf,SAAW,CAAC,KAAK,QAAQ,KACrB,MAIZ,OAAIA,GACApB,EAAQ,KAAK,WAAWoB,EAAOK,EAAMD,CAAK,CAAC,EACvCxB,IAAU,GACHA,EAGJ,IAEP,KAAK,SAAW,GACT,KAAK,IAEL,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAA2B,KAAK,aAAa,EAAG,CACpH,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACf,CAAC,CAET,EAvDC,QA0DL,IAAIzF,EAAA,UAAgB,CACZ,IAAI+F,EAAI,KAAK,KAAK,EAClB,OAAIA,GAGO,KAAK,IAAI,CAExB,EAPA,OAUJ,MAAM/F,EAAA,SAAgBoH,EAAW,CACzB,KAAK,eAAe,KAAKA,CAAS,CACtC,EAFE,SAKN,SAASpH,EAAA,UAAqB,CACtB,IAAIuF,EAAI,KAAK,eAAe,OAAS,EACrC,OAAIA,EAAI,EACG,KAAK,eAAe,IAAI,EAExB,KAAK,eAAe,CAAC,CAEpC,EAPK,YAUT,cAAcvF,EAAA,UAA0B,CAChC,OAAI,KAAK,eAAe,QAAU,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,EACzE,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAAE,MAErE,KAAK,WAAW,QAAW,KAE1C,EANU,iBASd,SAASA,EAAA,SAAmBuF,EAAG,CAEvB,OADAA,EAAI,KAAK,eAAe,OAAS,EAAI,KAAK,IAAIA,GAAK,CAAC,EAChDA,GAAK,EACE,KAAK,eAAeA,CAAC,EAErB,SAEf,EAPK,YAUT,UAAUvF,EAAA,SAAoBoH,EAAW,CACjC,KAAK,MAAMA,CAAS,CACxB,EAFM,aAKV,eAAepH,EAAA,UAA0B,CACjC,OAAO,KAAK,eAAe,MAC/B,EAFW,kBAGf,QAAS,CAAC,EACV,cAAeA,EAAA,SAAmB8D,EAAGuD,EAAIC,EAA0BC,EAAU,CAC7E,IAAIC,EAAQD,EACZ,OAAOD,EAA2B,CAClC,IAAK,GAAE,MAAO,GAEd,IAAK,GAAE,MAAO,GAEd,IAAK,GAAE,MAAO,GAEd,IAAK,GAAE,MAAO,GAEd,IAAK,GAAE,MAAO,IAEd,IAAK,GAAE,MAAO,IAEd,IAAK,GAAG,YAAK,MAAM,WAAW,EAAS,GACvC,MACA,IAAK,GAAG,YAAK,SAAS,EAAU,kBAChC,MACA,IAAK,GAAG,YAAK,MAAM,WAAW,EAAS,GACvC,MACA,IAAK,GAAG,YAAK,SAAS,EAAU,kBAChC,MACA,IAAK,IAAI,KAAK,MAAM,qBAAqB,EACzC,MACA,IAAK,IAAI,KAAK,SAAS,EACvB,MACA,IAAK,IAAG,MAAO,4BAEf,IAAK,IACL,MACA,IAAK,IAAG,EACR,MACA,IAAK,IAAG,MAAO,IAEf,IAAK,IACL,MACA,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAG,MAAO,IAEf,IAAK,IAAI,YAAK,MAAM,YAAY,EAAU,GAC1C,MACA,IAAK,IAAI,YAAK,MAAM,QAAQ,EAAU,GACtC,MACA,IAAK,IAAI,YAAK,MAAM,kBAAkB,EAAU,GAChD,MACA,IAAK,IAAI,YAAK,MAAM,eAAe,EAAU,GAC7C,MACA,IAAK,IAAI,YAAK,MAAM,YAAY,EAAU,GAC1C,MACA,IAAK,IAAI,YAAK,MAAM,cAAc,EAAU,GAC5C,MACA,IAAK,IAAI,YAAK,MAAM,WAAW,EAAU,GACzC,MACA,IAAK,IAAI,YAAK,MAAM,QAAQ,EAAU,GACtC,MACA,IAAK,IAAI,YAAK,MAAM,UAAU,EAAU,GACxC,MACA,IAAK,IAAI,YAAK,MAAM,qBAAqB,EAAU,GACnD,MACA,IAAK,IAAI,YAAK,MAAM,iBAAiB,EAAU,GAC/C,MACA,IAAK,IAAI,YAAK,MAAM,qBAAqB,EAAU,GACnD,MACA,IAAK,IAAI,YAAK,MAAM,kBAAkB,EAAU,GAChD,MACA,IAAK,IAAI,YAAK,MAAM,eAAe,EAAU,GAC7C,MACA,IAAK,IAAI,YAAK,MAAM,iBAAiB,EAAU,GAC/C,MACA,IAAK,IAAI,YAAK,MAAM,cAAc,EAAU,GAC5C,MACA,IAAK,IAAI,YAAK,MAAM,WAAW,EAAU,GACzC,MACA,IAAK,IAAI,YAAK,MAAM,oBAAoB,EAAU,GAClD,MACA,IAAK,IAAI,YAAK,MAAM,qBAAqB,EAAU,GACnD,MACA,IAAK,IAAI,YAAK,MAAM,kBAAkB,EAAU,GAChD,MACA,IAAK,IAAI,YAAK,MAAM,eAAe,EAAU,GAC7C,MACA,IAAK,IAAI,YAAK,MAAM,iBAAiB,EAAU,GAC/C,MACA,IAAK,IAAI,YAAK,MAAM,cAAc,EAAU,GAC5C,MACA,IAAK,IAAI,YAAK,MAAM,WAAW,EAAU,GACzC,MACA,IAAK,IAAI,YAAK,MAAM,MAAM,EAAU,GACpC,MACA,IAAK,IAAI,YAAK,MAAM,MAAM,EAAU,GACpC,MACA,IAAK,IAAI,YAAK,MAAM,QAAQ,EAAU,GACtC,MACA,IAAK,IAAI,YAAK,MAAM,QAAQ,EAAU,GACtC,MACA,IAAK,IAAI,YAAK,MAAM,KAAK,EAAU,GACnC,MACA,IAAK,IAAI,YAAK,MAAM,OAAO,EAAU,GACrC,MACA,IAAK,IAAI,YAAK,MAAM,OAAO,EAAU,GACrC,MACA,IAAK,IAAI,YAAK,MAAM,OAAO,EAAU,GACrC,MACA,IAAK,IAAI,YAAK,MAAM,OAAO,EAAU,GACrC,MACA,IAAK,IAAI,YAAK,MAAM,OAAO,EAAU,GACrC,MACA,IAAK,IAAI,YAAK,MAAM,OAAO,EAAU,GACrC,MACA,IAAK,IAAI,YAAK,MAAM,OAAO,EAAU,GACrC,MACA,IAAK,IAAI,YAAK,MAAM,OAAO,EAAU,GACrC,MACA,IAAK,IAAI,YAAK,MAAM,OAAO,EAAU,GACrC,MACA,IAAK,IAAI,YAAK,MAAM,OAAO,EAAU,GACrC,MACA,IAAK,IAAI,YAAK,MAAM,WAAW,EAAU,GACzC,MACA,IAAK,IAAI,YAAK,MAAM,iBAAiB,EAAU,GAC/C,MACA,IAAK,IAAI,YAAK,MAAM,kBAAkB,EAAU,GAChD,MACA,IAAK,IAAI,YAAK,MAAM,sBAAsB,EAAU,GACpD,MACA,IAAK,IAAG,MAAO,gBAEf,IAAK,IAAI,YAAK,MAAM,WAAW,EAAU,kBACzC,MACA,IAAK,IAAI,KAAK,MAAM,WAAW,EAC/B,MACA,IAAK,IAAI,KAAK,SAAS,EAAE,KAAK,SAAS,EACvC,MACA,IAAK,IAAI,MAAO,IAEhB,IAAK,IACL,MACA,IAAK,IAAI,MAAO,IAEhB,IAAK,IAAI,KAAK,MAAM,QAAQ,EAC5B,MACA,IAAK,IAAK,KAAK,SAAS,EACxB,MACA,IAAK,IAAI,MAAO,MAEhB,IAAK,IAAI,KAAK,MAAM,WAAW,EAC/B,MACA,IAAK,IAAI,YAAK,MAAM,eAAe,EAAU,UAC7C,MACA,IAAK,IAAI,KAAK,SAAS,EAAG,KAAK,MAAM,iBAAiB,EACtD,MACA,IAAK,IAAI,MAAO,YAEhB,IAAK,IAAI,KAAK,SAAS,EAAG,KAAK,SAAS,EACxC,MACA,IAAK,IAAI,MAAO,MAEhB,IAAK,IAAgC,MAAO,SAE5C,IAAK,IAA2B,MAAO,SAEvC,IAAK,IAAG,MAAO,QAEf,IAAK,IAAG,MAAO,MAEf,IAAK,IAAG,MAAO,GAEf,CACA,EAhLe,aAiLf,MAAO,CAAC,8BAA8B,8BAA8B,8BAA8B,8BAA8B,uBAAuB,gCAAgC,uBAAuB,uBAAuB,uBAAuB,uBAAuB,wBAAwB,YAAY,cAAc,gCAAgC,wBAAwB,mBAAmB,WAAW,mBAAmB,qBAAqB,qBAAqB,mBAAmB,sBAAsB,oBAAoB,gBAAgB,yBAAyB,sBAAsB,oBAAoB,qBAAqB,kBAAkB,gBAAgB,kBAAkB,6BAA6B,yBAAyB,4BAA4B,yBAAyB,uBAAuB,wBAAwB,qBAAqB,mBAAmB,4BAA4B,4BAA4B,yBAAyB,uBAAuB,wBAAwB,qBAAqB,mBAAmB,yBAAyB,cAAc,gBAAgB,gBAAgB,aAAa,eAAe,gBAAgB,eAAe,kBAAkB,eAAe,kBAAkB,eAAe,mBAAmB,eAAe,kBAAkB,kBAAkB,4BAA4B,wBAAwB,4BAA4B,SAAS,kBAAkB,WAAW,WAAW,UAAU,SAAS,kBAAkB,eAAe,WAAW,aAAa,gBAAgB,aAAa,kBAAkB,aAAa,WAAW,aAAa,UAAU,UAAU,aAAa,eAAe,QAAQ,EAC9nD,WAAY,CAAC,oBAAsB,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,CAAC,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,CAAC,EAAE,UAAY,EAAK,EAAE,gBAAkB,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,cAAgB,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,EAAE,EAAE,UAAY,EAAK,EAAE,OAAS,CAAC,MAAQ,CAAC,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,qBAAuB,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,iBAAmB,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,gBAAkB,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,MAAQ,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,MAAQ,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,MAAQ,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,MAAQ,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,MAAQ,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,OAAS,CAAC,MAAQ,CAAC,EAAE,UAAY,EAAK,EAAE,IAAM,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,OAAS,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,OAAS,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,KAAO,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,MAAQ,CAAC,MAAQ,CAAC,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,oBAAsB,CAAC,MAAQ,CAAC,EAAE,UAAY,EAAK,EAAE,iBAAmB,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,cAAgB,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,gBAAkB,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,aAAe,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,mBAAqB,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,oBAAsB,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,iBAAmB,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,cAAgB,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,gBAAkB,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,aAAe,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,MAAQ,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,gBAAkB,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,oBAAsB,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,SAAW,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,iBAAmB,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,cAAgB,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,WAAa,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,aAAe,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,UAAY,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,OAAS,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,WAAa,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,OAAS,CAAC,MAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAK,EAAE,QAAU,CAAC,MAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,UAAY,EAAI,CAAC,CACxxF,EACA,OAAOpC,EACP,EAAG,EACHpF,GAAO,MAAQoF,GACf,SAASuC,IAAU,CACjB,KAAK,GAAK,CAAC,CACb,CAFS,OAAAzH,EAAAyH,GAAA,UAGTA,GAAO,UAAY3H,GAAOA,GAAO,OAAS2H,GACnC,IAAIA,EACX,EAAG,EACF3H,GAAO,OAASA,GAEhB,IAAO4H,GAAQC,GCx4BhB,IAAIC,EAAe,CAAC,EAChBC,GAAqB,CAAC,EAAE,EACxBC,EAAuB,SACvBC,EAAsB,GACtBC,EAAa,CACf,CACE,MAAO,SACP,MAAO,CAAE,KAAM,QAAS,EACxB,KAAM,CAAE,KAAM,QAAS,EACvB,KAAM,KACN,KAAM,KACN,eAAgB,EAClB,CACF,EACIC,GAAO,CAAC,EACRC,GAAQ,GACRC,GAAc,GACdC,GAAe,EACfC,GAAkB,EAClBC,GAESC,GAAYC,EAAA,UAAY,CACnC,OAAOF,EACT,EAFyB,aAIZG,GAAYD,EAAA,SAAUE,EAAa,CAE9CJ,GADoBK,GAAaD,EAAaE,GAAU,CAAC,CAE3D,EAHyB,aAMZC,GAASL,EAAA,SAAUM,EAAMC,EAAMC,EAAIC,EAAOC,EAAOC,EAAOC,EAAQC,EAAMC,EAAM,CAEvF,GAEER,GAAS,MACTC,IAAS,QACTA,IAAS,MACTC,IAAO,QACPA,IAAO,MACPC,IAAU,QACVA,IAAU,KAEV,OAGF,IAAIM,EAAM,CAAC,EACLC,EAAMvB,GAAK,KAAMsB,GAAQA,EAAI,OAASR,GAAQQ,EAAI,KAAOP,CAAE,EAYjE,GAXIQ,EACFD,EAAMC,EAENvB,GAAK,KAAKsB,CAAG,EAGfA,EAAI,KAAOT,EACXS,EAAI,KAAOR,EACXQ,EAAI,GAAKP,EACTO,EAAI,MAAQ,CAAE,KAAMN,CAAM,EAECC,GAAU,KACnCK,EAAI,MAAQ,CAAE,KAAM,EAAG,UAEnB,OAAOL,GAAU,SAAU,CAC7B,GAAI,CAACO,EAAKC,CAAK,EAAI,OAAO,QAAQR,CAAK,EAAE,CAAC,EAC1CK,EAAIE,CAAG,EAAI,CAAE,KAAMC,CAAM,CAC3B,MACEH,EAAI,MAAQ,CAAE,KAAML,CAAM,EAI9B,GAA2BC,GAAU,KACnCI,EAAI,MAAQ,CAAE,KAAM,EAAG,UAEnB,OAAOJ,GAAU,SAAU,CAC7B,GAAI,CAACM,EAAKC,CAAK,EAAI,OAAO,QAAQP,CAAK,EAAE,CAAC,EAC1CI,EAAIE,CAAG,EAAI,CAAE,KAAMC,CAAM,CAC3B,MACEH,EAAI,MAAQ,CAAE,KAAMJ,CAAM,EAI9B,GAAI,OAAOC,GAAW,SAAU,CAC9B,GAAI,CAACK,EAAKC,CAAK,EAAI,OAAO,QAAQN,CAAM,EAAE,CAAC,EAC3CG,EAAIE,CAAG,EAAIC,CACb,MACEH,EAAI,OAASH,EAEf,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACI,EAAKC,CAAK,EAAI,OAAO,QAAQL,CAAI,EAAE,CAAC,EACzCE,EAAIE,CAAG,EAAIC,CACb,MACEH,EAAI,KAAOF,EAEb,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACG,EAAKC,CAAK,EAAI,OAAO,QAAQJ,CAAI,EAAE,CAAC,EACzCC,EAAIE,CAAG,EAAIC,CACb,MACEH,EAAI,KAAOD,EAEbC,EAAI,KAAOI,GAAS,CACtB,EArEsB,UAwETC,GAAoBpB,EAAA,SAAUqB,EAAaC,EAAOb,EAAOE,EAAOC,EAAQC,EAAMC,EAAM,CAE/F,GAAIQ,IAAU,MAAQb,IAAU,KAC9B,OAGF,IAAIc,EAAiB,CAAC,EAChBP,EAAM5B,EAAa,KAAMmC,GAAmBA,EAAe,QAAUD,CAAK,EAehF,GAdIN,GAAOM,IAAUN,EAAI,MACvBO,EAAiBP,GAEjBO,EAAe,MAAQD,EACvBlC,EAAa,KAAKmC,CAAc,GAIPd,GAAU,KACnCc,EAAe,MAAQ,CAAE,KAAM,EAAG,EAElCA,EAAe,MAAQ,CAAE,KAAMd,CAAM,EAGZE,GAAU,KACnCY,EAAe,MAAQ,CAAE,KAAM,EAAG,UAE9B,OAAOZ,GAAU,SAAU,CAC7B,GAAI,CAACM,EAAKC,CAAK,EAAI,OAAO,QAAQP,CAAK,EAAE,CAAC,EAC1CY,EAAeN,CAAG,EAAI,CAAE,KAAMC,CAAM,CACtC,MACEK,EAAe,MAAQ,CAAE,KAAMZ,CAAM,EAIzC,GAAI,OAAOC,GAAW,SAAU,CAC9B,GAAI,CAACK,EAAKC,CAAK,EAAI,OAAO,QAAQN,CAAM,EAAE,CAAC,EAC3CW,EAAeN,CAAG,EAAIC,CACxB,MACEK,EAAe,OAASX,EAE1B,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACI,EAAKC,CAAK,EAAI,OAAO,QAAQL,CAAI,EAAE,CAAC,EACzCU,EAAeN,CAAG,EAAIC,CACxB,MACEK,EAAe,KAAOV,EAExB,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACG,EAAKC,CAAK,EAAI,OAAO,QAAQJ,CAAI,EAAE,CAAC,EACzCS,EAAeN,CAAG,EAAIC,CACxB,MACEK,EAAe,KAAOT,EAExBS,EAAe,YAAc,CAAE,KAAMF,CAAY,EACjDE,EAAe,eAAiBjC,EAChCiC,EAAe,KAAOJ,GAAS,CACjC,EAtDiC,qBAyDpBK,GAAexB,EAAA,SAAUqB,EAAaC,EAAOb,EAAOC,EAAOC,EAAOC,EAAQC,EAAMC,EAAM,CAEjG,GAAIQ,IAAU,MAAQb,IAAU,KAC9B,OAGF,IAAIgB,EAAY,CAAC,EACXT,EAAM5B,EAAa,KAAMqC,GAAcA,EAAU,QAAUH,CAAK,EAetE,GAdIN,GAAOM,IAAUN,EAAI,MACvBS,EAAYT,GAEZS,EAAU,MAAQH,EAClBlC,EAAa,KAAKqC,CAAS,GAIFhB,GAAU,KACnCgB,EAAU,MAAQ,CAAE,KAAM,EAAG,EAE7BA,EAAU,MAAQ,CAAE,KAAMhB,CAAM,EAGPC,GAAU,KACnCe,EAAU,MAAQ,CAAE,KAAM,EAAG,UAEzB,OAAOf,GAAU,SAAU,CAC7B,GAAI,CAACO,EAAKC,CAAK,EAAI,OAAO,QAAQR,CAAK,EAAE,CAAC,EAC1Ce,EAAUR,CAAG,EAAI,CAAE,KAAMC,CAAM,CACjC,MACEO,EAAU,MAAQ,CAAE,KAAMf,CAAM,EAIpC,GAA2BC,GAAU,KACnCc,EAAU,MAAQ,CAAE,KAAM,EAAG,UAEzB,OAAOd,GAAU,SAAU,CAC7B,GAAI,CAACM,EAAKC,CAAK,EAAI,OAAO,QAAQP,CAAK,EAAE,CAAC,EAC1Cc,EAAUR,CAAG,EAAI,CAAE,KAAMC,CAAM,CACjC,MACEO,EAAU,MAAQ,CAAE,KAAMd,CAAM,EAIpC,GAAI,OAAOC,GAAW,SAAU,CAC9B,GAAI,CAACK,EAAKC,CAAK,EAAI,OAAO,QAAQN,CAAM,EAAE,CAAC,EAC3Ca,EAAUR,CAAG,EAAIC,CACnB,MACEO,EAAU,OAASb,EAErB,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACI,EAAKC,CAAK,EAAI,OAAO,QAAQL,CAAI,EAAE,CAAC,EACzCY,EAAUR,CAAG,EAAIC,CACnB,MACEO,EAAU,KAAOZ,EAEnB,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACG,EAAKC,CAAK,EAAI,OAAO,QAAQJ,CAAI,EAAE,CAAC,EACzCW,EAAUR,CAAG,EAAIC,CACnB,MACEO,EAAU,KAAOX,EAEnBW,EAAU,KAAON,GAAS,EAC1BM,EAAU,YAAc,CAAE,KAAMJ,CAAY,EAC5CI,EAAU,eAAiBnC,CAC7B,EAjE4B,gBAoEfoC,GAAe1B,EAAA,SAAUqB,EAAaC,EAAOb,EAAOC,EAAOC,EAAOC,EAAQC,EAAMC,EAAM,CAEjG,GAAIQ,IAAU,MAAQb,IAAU,KAC9B,OAGF,IAAIkB,EAAY,CAAC,EACXX,EAAM5B,EAAa,KAAMuC,GAAcA,EAAU,QAAUL,CAAK,EAetE,GAdIN,GAAOM,IAAUN,EAAI,MACvBW,EAAYX,GAEZW,EAAU,MAAQL,EAClBlC,EAAa,KAAKuC,CAAS,GAIFlB,GAAU,KACnCkB,EAAU,MAAQ,CAAE,KAAM,EAAG,EAE7BA,EAAU,MAAQ,CAAE,KAAMlB,CAAM,EAGPC,GAAU,KACnCiB,EAAU,MAAQ,CAAE,KAAM,EAAG,UAEzB,OAAOjB,GAAU,SAAU,CAC7B,GAAI,CAACO,EAAKC,CAAK,EAAI,OAAO,QAAQR,CAAK,EAAE,CAAC,EAC1CiB,EAAUV,CAAG,EAAI,CAAE,KAAMC,CAAM,CACjC,MACES,EAAU,MAAQ,CAAE,KAAMjB,CAAM,EAIpC,GAA2BC,GAAU,KACnCgB,EAAU,MAAQ,CAAE,KAAM,EAAG,UAEzB,OAAOhB,GAAU,SAAU,CAC7B,GAAI,CAACM,EAAKC,CAAK,EAAI,OAAO,QAAQP,CAAK,EAAE,CAAC,EAC1CgB,EAAUV,CAAG,EAAI,CAAE,KAAMC,CAAM,CACjC,MACES,EAAU,MAAQ,CAAE,KAAMhB,CAAM,EAIpC,GAAI,OAAOC,GAAW,SAAU,CAC9B,GAAI,CAACK,EAAKC,CAAK,EAAI,OAAO,QAAQN,CAAM,EAAE,CAAC,EAC3Ce,EAAUV,CAAG,EAAIC,CACnB,MACES,EAAU,OAASf,EAErB,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACI,EAAKC,CAAK,EAAI,OAAO,QAAQL,CAAI,EAAE,CAAC,EACzCc,EAAUV,CAAG,EAAIC,CACnB,MACES,EAAU,KAAOd,EAEnB,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACG,EAAKC,CAAK,EAAI,OAAO,QAAQJ,CAAI,EAAE,CAAC,EACzCa,EAAUV,CAAG,EAAIC,CACnB,MACES,EAAU,KAAOb,EAEnBa,EAAU,KAAOR,GAAS,EAC1BQ,EAAU,YAAc,CAAE,KAAMN,CAAY,EAC5CM,EAAU,eAAiBrC,CAC7B,EAjE4B,gBAoEfsC,GAA4B5B,EAAA,SAAUsB,EAAOb,EAAOH,EAAMO,EAAMC,EAAM,CAIjF,GAAIQ,IAAU,MAAQb,IAAU,KAC9B,OAGF,IAAIoB,EAAW,CAAC,EACVb,EAAMxB,EAAW,KAAMqC,GAAaA,EAAS,QAAUP,CAAK,EAelE,GAdIN,GAAOM,IAAUN,EAAI,MACvBa,EAAWb,GAEXa,EAAS,MAAQP,EACjB9B,EAAW,KAAKqC,CAAQ,GAICpB,GAAU,KACnCoB,EAAS,MAAQ,CAAE,KAAM,EAAG,EAE5BA,EAAS,MAAQ,CAAE,KAAMpB,CAAM,EAGPH,GAAS,KACjCuB,EAAS,KAAO,CAAE,KAAM,QAAS,UAE7B,OAAOvB,GAAS,SAAU,CAC5B,GAAI,CAACW,EAAKC,CAAK,EAAI,OAAO,QAAQZ,CAAI,EAAE,CAAC,EACzCuB,EAASZ,CAAG,EAAI,CAAE,KAAMC,CAAM,CAChC,MACEW,EAAS,KAAO,CAAE,KAAMvB,CAAK,EAIjC,GAAI,OAAOO,GAAS,SAAU,CAC5B,GAAI,CAACI,EAAKC,CAAK,EAAI,OAAO,QAAQL,CAAI,EAAE,CAAC,EACzCgB,EAASZ,CAAG,EAAIC,CAClB,MACEW,EAAS,KAAOhB,EAElB,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACG,EAAKC,CAAK,EAAI,OAAO,QAAQJ,CAAI,EAAE,CAAC,EACzCe,EAASZ,CAAG,EAAIC,CAClB,MACEW,EAAS,KAAOf,EAElBe,EAAS,eAAiBvC,EAC1BuC,EAAS,KAAOV,GAAS,EAEzB5B,EAAsBD,EACtBA,EAAuBgC,EACvBjC,GAAmB,KAAKE,CAAmB,CAC7C,EArDyC,6BAwD5BuC,GAAuB9B,EAAA,SAAUsB,EAAOb,EAAOH,EAAMO,EAAMC,EAAM,CAI5E,GAAIQ,IAAU,MAAQb,IAAU,KAC9B,OAGF,IAAIoB,EAAW,CAAC,EACVb,EAAMxB,EAAW,KAAMqC,GAAaA,EAAS,QAAUP,CAAK,EAelE,GAdIN,GAAOM,IAAUN,EAAI,MACvBa,EAAWb,GAEXa,EAAS,MAAQP,EACjB9B,EAAW,KAAKqC,CAAQ,GAICpB,GAAU,KACnCoB,EAAS,MAAQ,CAAE,KAAM,EAAG,EAE5BA,EAAS,MAAQ,CAAE,KAAMpB,CAAM,EAGPH,GAAS,KACjCuB,EAAS,KAAO,CAAE,KAAM,WAAY,UAEhC,OAAOvB,GAAS,SAAU,CAC5B,GAAI,CAACW,EAAKC,CAAK,EAAI,OAAO,QAAQZ,CAAI,EAAE,CAAC,EACzCuB,EAASZ,CAAG,EAAI,CAAE,KAAMC,CAAM,CAChC,MACEW,EAAS,KAAO,CAAE,KAAMvB,CAAK,EAIjC,GAAI,OAAOO,GAAS,SAAU,CAC5B,GAAI,CAACI,EAAKC,CAAK,EAAI,OAAO,QAAQL,CAAI,EAAE,CAAC,EACzCgB,EAASZ,CAAG,EAAIC,CAClB,MACEW,EAAS,KAAOhB,EAElB,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACG,EAAKC,CAAK,EAAI,OAAO,QAAQJ,CAAI,EAAE,CAAC,EACzCe,EAASZ,CAAG,EAAIC,CAClB,MACEW,EAAS,KAAOf,EAElBe,EAAS,eAAiBvC,EAC1BuC,EAAS,KAAOV,GAAS,EAEzB5B,EAAsBD,EACtBA,EAAuBgC,EACvBjC,GAAmB,KAAKE,CAAmB,CAC7C,EArDoC,wBAwDvBwC,GAAoB/B,EAAA,SAC/BgC,EACAV,EACAb,EACAH,EACAK,EACAC,EACAC,EACAC,EACA,CAIA,GAAIQ,IAAU,MAAQb,IAAU,KAC9B,OAGF,IAAIoB,EAAW,CAAC,EACVb,EAAMxB,EAAW,KAAMqC,GAAaA,EAAS,QAAUP,CAAK,EAelE,GAdIN,GAAOM,IAAUN,EAAI,MACvBa,EAAWb,GAEXa,EAAS,MAAQP,EACjB9B,EAAW,KAAKqC,CAAQ,GAICpB,GAAU,KACnCoB,EAAS,MAAQ,CAAE,KAAM,EAAG,EAE5BA,EAAS,MAAQ,CAAE,KAAMpB,CAAM,EAGPH,GAAS,KACjCuB,EAAS,KAAO,CAAE,KAAM,MAAO,UAE3B,OAAOvB,GAAS,SAAU,CAC5B,GAAI,CAACW,EAAKC,CAAK,EAAI,OAAO,QAAQZ,CAAI,EAAE,CAAC,EACzCuB,EAASZ,CAAG,EAAI,CAAE,KAAMC,CAAM,CAChC,MACEW,EAAS,KAAO,CAAE,KAAMvB,CAAK,EAIjC,GAA2BK,GAAU,KACnCkB,EAAS,MAAQ,CAAE,KAAM,EAAG,UAExB,OAAOlB,GAAU,SAAU,CAC7B,GAAI,CAACM,EAAKC,CAAK,EAAI,OAAO,QAAQP,CAAK,EAAE,CAAC,EAC1CkB,EAASZ,CAAG,EAAI,CAAE,KAAMC,CAAM,CAChC,MACEW,EAAS,MAAQ,CAAE,KAAMlB,CAAM,EAInC,GAAI,OAAOE,GAAS,SAAU,CAC5B,GAAI,CAACI,EAAKC,CAAK,EAAI,OAAO,QAAQL,CAAI,EAAE,CAAC,EACzCgB,EAASZ,CAAG,EAAIC,CAClB,MACEW,EAAS,KAAOhB,EAElB,GAAI,OAAOC,GAAS,SAAU,CAC5B,GAAI,CAACG,EAAKC,CAAK,EAAI,OAAO,QAAQJ,CAAI,EAAE,CAAC,EACzCe,EAASZ,CAAG,EAAIC,CAClB,MACEW,EAAS,KAAOf,EAElBe,EAAS,SAAWG,EACpBH,EAAS,eAAiBvC,EAC1BuC,EAAS,KAAOV,GAAS,EAEzB5B,EAAsBD,EACtBA,EAAuBgC,EACvBjC,GAAmB,KAAKE,CAAmB,CAC7C,EA1EiC,qBA4EpB0C,GAAwBjC,EAAA,UAAY,CAC/CV,EAAuBC,EACvBF,GAAmB,IAAI,EACvBE,EAAsBF,GAAmB,IAAI,EAC7CA,GAAmB,KAAKE,CAAmB,CAC7C,EALqC,yBAQxB2C,GAAgBlC,EAAA,SAC3BqB,EACAc,EACAC,EACAC,EACAC,EACAC,EACAC,EACA5B,EACAF,EACA+B,EACAC,EACA,CACA,IAAI1B,EAAM5B,EAAa,KAAMuD,GAAYA,EAAQ,QAAUR,CAAW,EACtE,GAAI,EAAAnB,IAAQ,SACVA,EAAMxB,EAAW,KAAMmD,GAAYA,EAAQ,QAAUR,CAAW,EAC5DnB,IAAQ,SAId,IAA6BoB,GAAY,KACvC,GAAI,OAAOA,GAAY,SAAU,CAC/B,GAAI,CAACnB,EAAKC,CAAK,EAAI,OAAO,QAAQkB,CAAO,EAAE,CAAC,EAC5CpB,EAAIC,CAAG,EAAIC,CACb,MACEF,EAAI,QAAUoB,EAGlB,GAA+BC,GAAc,KAC3C,GAAI,OAAOA,GAAc,SAAU,CACjC,GAAI,CAACpB,EAAKC,CAAK,EAAI,OAAO,QAAQmB,CAAS,EAAE,CAAC,EAC9CrB,EAAIC,CAAG,EAAIC,CACb,MACEF,EAAI,UAAYqB,EAGpB,GAAiCC,GAAgB,KAC/C,GAAI,OAAOA,GAAgB,SAAU,CACnC,GAAI,CAACrB,EAAKC,CAAK,EAAI,OAAO,QAAQoB,CAAW,EAAE,CAAC,EAChDtB,EAAIC,CAAG,EAAIC,CACb,MACEF,EAAI,YAAcsB,EAGtB,GAA+BC,GAAc,KAC3C,GAAI,OAAOA,GAAc,SAAU,CACjC,GAAI,CAACtB,EAAKC,CAAK,EAAI,OAAO,QAAQqB,CAAS,EAAE,CAAC,EAC9CvB,EAAIC,CAAG,EAAIC,CACb,MACEF,EAAI,UAAYuB,EAGpB,GAA2BC,GAAU,KACnC,GAAI,OAAOA,GAAU,SAAU,CAC7B,GAAI,CAACvB,EAAKC,CAAK,EAAI,OAAO,QAAQsB,CAAK,EAAE,CAAC,EAC1CxB,EAAIC,CAAG,EAAIC,CACb,MACEF,EAAI,MAAQwB,EAGhB,GAA4B5B,GAAW,KACrC,GAAI,OAAOA,GAAW,SAAU,CAC9B,GAAI,CAACK,EAAKC,CAAK,EAAI,OAAO,QAAQN,CAAM,EAAE,CAAC,EAC3CI,EAAIC,CAAG,EAAIC,CACb,MACEF,EAAI,OAASJ,EAGjB,GAA2BF,GAAU,KACnC,GAAI,OAAOA,GAAU,SAAU,CAC7B,GAAI,CAACO,EAAKC,CAAK,EAAI,OAAO,QAAQR,CAAK,EAAE,CAAC,EAC1CM,EAAIC,CAAG,EAAIC,CACb,MACEF,EAAI,MAAQN,EAGhB,GAAgC+B,GAAe,KAC7C,GAAI,OAAOA,GAAe,SAAU,CAClC,GAAI,CAACxB,EAAKC,CAAK,EAAI,OAAO,QAAQuB,CAAU,EAAE,CAAC,EAC/CzB,EAAIC,CAAG,EAAIC,CACb,MACEF,EAAI,WAAayB,EAGrB,GAAkCC,GAAiB,KACjD,GAAI,OAAOA,GAAiB,SAAU,CACpC,GAAI,CAACzB,EAAKC,CAAK,EAAI,OAAO,QAAQwB,CAAY,EAAE,CAAC,EACjD1B,EAAIC,CAAG,EAAIC,CACb,MACEF,EAAI,aAAe0B,EAGzB,EA5F6B,iBA+FhBE,GAAiB5C,EAAA,SAC5BqB,EACAd,EACAC,EACAqC,EACAC,EACAC,EACAC,EACA,CACA,IAAMhC,EAAMvB,GAAK,KAAMsB,GAAQA,EAAI,OAASR,GAAQQ,EAAI,KAAOP,CAAE,EACjE,GAAIQ,IAAQ,OAGZ,IAA+B6B,GAAc,KAC3C,GAAI,OAAOA,GAAc,SAAU,CACjC,GAAI,CAAC5B,EAAKC,CAAK,EAAI,OAAO,QAAQ2B,CAAS,EAAE,CAAC,EAC9C7B,EAAIC,CAAG,EAAIC,CACb,MACEF,EAAI,UAAY6B,EAGpB,GAA+BC,GAAc,KAC3C,GAAI,OAAOA,GAAc,SAAU,CACjC,GAAI,CAAC7B,EAAKC,CAAK,EAAI,OAAO,QAAQ4B,CAAS,EAAE,CAAC,EAC9C9B,EAAIC,CAAG,EAAIC,CACb,MACEF,EAAI,UAAY8B,EAGpB,GAA6BC,GAAY,KACvC,GAAI,OAAOA,GAAY,SAAU,CAC/B,GAAI,CAAC9B,EAAKC,CAAK,EAAI,OAAO,QAAQ6B,CAAO,EAAE,CAAC,EAC5C/B,EAAIC,CAAG,EAAI,SAASC,CAAK,CAC3B,MACEF,EAAI,QAAU,SAAS+B,CAAO,EAGlC,GAA6BC,GAAY,KACvC,GAAI,OAAOA,GAAY,SAAU,CAC/B,GAAI,CAAC/B,EAAKC,CAAK,EAAI,OAAO,QAAQ8B,CAAO,EAAE,CAAC,EAC5ChC,EAAIC,CAAG,EAAI,SAASC,CAAK,CAC3B,MACEF,EAAI,QAAU,SAASgC,CAAO,EAGpC,EA7C8B,kBAgDjBC,GAAqBjD,EAAA,SAAUqB,EAAa6B,EAAmBC,EAAsB,CAChG,IAAIC,EAAoBxD,GACpByD,EAAuBxD,GAE3B,GAAI,OAAOqD,GAAsB,SAAU,CACzC,IAAMhC,EAAQ,OAAO,OAAOgC,CAAiB,EAAE,CAAC,EAChDE,EAAoB,SAASlC,CAAK,CACpC,MACEkC,EAAoB,SAASF,CAAiB,EAEhD,GAAI,OAAOC,GAAyB,SAAU,CAC5C,IAAMjC,EAAQ,OAAO,OAAOiC,CAAoB,EAAE,CAAC,EACnDE,EAAuB,SAASnC,CAAK,CACvC,MACEmC,EAAuB,SAASF,CAAoB,EAGlDC,GAAqB,IACvBxD,GAAewD,GAEbC,GAAwB,IAC1BxD,GAAkBwD,EAEtB,EAvBkC,sBAyBrBC,GAAkBtD,EAAA,UAAY,CACzC,OAAOJ,EACT,EAF+B,mBAGlB2D,GAAqBvD,EAAA,UAAY,CAC5C,OAAOH,EACT,EAFkC,sBAGrB2D,GAA0BxD,EAAA,UAAY,CACjD,OAAOV,CACT,EAFuC,2BAI1BmE,GAAyBzD,EAAA,UAAY,CAChD,OAAOT,CACT,EAFsC,0BAIzBmE,GAAkB1D,EAAA,SAAU2D,EAAgB,CACvD,OAAoCA,GAAmB,KAC9CvE,EAEAA,EAAa,OAAQmC,GACnBA,EAAe,iBAAmBoC,CAC1C,CAEL,EAR+B,mBASlBC,GAAa5D,EAAA,SAAUsB,EAAO,CACzC,OAAOlC,EAAa,KAAMmC,GAAmBA,EAAe,QAAUD,CAAK,CAC7E,EAF0B,cAGbuC,GAAiB7D,EAAA,SAAU2D,EAAgB,CACtD,OAAO,OAAO,KAAKD,GAAgBC,CAAc,CAAC,CACpD,EAF8B,kBAIjBG,GAAgB9D,EAAA,SAAU2D,EAAgB,CACrD,OAAoCA,GAAmB,KAC9CnE,EAEAA,EAAW,OAAQqC,GAAaA,EAAS,iBAAmB8B,CAAc,CAErF,EAN6B,iBAWhBI,GAAeD,GAEfE,GAAUhE,EAAA,UAAY,CACjC,OAAOP,EACT,EAFuB,WAIVwE,GAAWjE,EAAA,UAAY,CAClC,OAAON,EACT,EAFwB,YAIXwE,GAAUlE,EAAA,SAAUmE,EAAa,CAC5CxE,GAAcwE,CAChB,EAFuB,WAIVhD,GAAWnB,EAAA,UAAY,CAClC,OAAOL,EACT,EAFwB,YAIXyE,GAAQpE,EAAA,UAAY,CAC/BZ,EAAe,CAAC,EAChBI,EAAa,CACX,CACE,MAAO,SACP,MAAO,CAAE,KAAM,QAAS,EACxB,KAAM,CAAE,KAAM,QAAS,EACvB,KAAM,KACN,KAAM,KACN,eAAgB,EAClB,CACF,EACAD,EAAsB,GACtBD,EAAuB,SACvBD,GAAqB,CAAC,EAAE,EACxBI,GAAO,CAAC,EAERJ,GAAqB,CAAC,EAAE,EACxBK,GAAQ,GACRC,GAAc,GACdC,GAAe,EACfC,GAAkB,CACpB,EAtBqB,SAwBRwE,GAAW,CACtB,MAAO,EACP,OAAQ,EACR,KAAM,EACN,YAAa,EACb,aAAc,EACd,WAAY,EACZ,YAAa,EACb,WAAY,GACZ,SAAU,GACV,UAAW,GACX,SAAU,GACV,QAAS,GACT,UAAW,GACX,QAAS,GACT,aAAc,GACd,WAAY,GACZ,UAAW,GACX,QAAS,GACT,QAAS,GACT,WAAY,GACZ,SAAU,GACV,YAAa,GACb,aAAc,EAChB,EAEaC,GAAY,CACvB,OAAQ,EACR,KAAM,CACR,EAEaC,GAAY,CACvB,OAAQ,EACR,QAAS,EACT,KAAM,CACR,EAEaC,GAAWxE,EAAA,SAAUyE,EAAK,CAErC/E,GADoBS,GAAasE,EAAKrE,GAAU,CAAC,CAEnD,EAHwB,YAKjBsE,GAAQ,CACb,kBAAAtD,GACA,0BAAAQ,GACA,aAAAJ,GACA,qBAAAM,GACA,aAAAJ,GACA,kBAAAK,GACA,sBAAAE,GACA,OAAA5B,GACA,cAAA6B,GACA,eAAAU,GACA,mBAAAK,GACA,SAAA9B,GACA,QAAA+C,GACA,gBAAAR,GACA,WAAAE,GACA,eAAAC,GACA,cAAAC,GACA,aAAAC,GACA,wBAAAP,GACA,uBAAAC,GACA,QAAAO,GACA,SAAAC,GACA,UAAAlE,GACA,gBAAAuD,GACA,mBAAAC,GACA,YAAAoB,GACA,YAAAC,GACA,kBAAAC,GACA,kBAAAC,GACA,UAAW9E,EAAA,IAAMI,GAAU,EAAE,GAAlB,aACX,MAAAgE,GACA,SAAAC,GACA,UAAAC,GACA,UAAAC,GACA,SAAAC,GACA,UAAAvE,EAEF,EC9zBA,IAAA8E,GAA4B,WAErB,IAAMC,GAAWC,EAAA,SAAUC,EAAMC,EAAU,CAChD,OAAqBH,GAASE,EAAMC,CAAQ,CAC9C,EAFwB,YAIXC,GAAYH,EAAA,SAAUC,EAAMG,EAAOC,EAAQC,EAAGC,EAAGC,EAAM,CAClE,IAAMC,EAAYR,EAAK,OAAO,OAAO,EACrCQ,EAAU,KAAK,QAASL,CAAK,EAC7BK,EAAU,KAAK,SAAUJ,CAAM,EAC/BI,EAAU,KAAK,IAAKH,CAAC,EACrBG,EAAU,KAAK,IAAKF,CAAC,EACrB,IAAIG,EAAgBF,EAAK,WAAW,uBAAuB,EAAIA,KAAO,gBAAYA,CAAI,EACtFC,EAAU,KAAK,aAAcC,CAAa,CAC5C,EARyB,aAUZC,GAAWX,EAAA,CAACC,EAAMW,EAAMC,IAAS,CAC5C,IAAMC,EAAWb,EAAK,OAAO,GAAG,EAC5Bc,EAAI,EACR,QAASC,KAAOJ,EAAM,CACpB,IAAIK,EAAYD,EAAI,UAAYA,EAAI,UAAY,UAC5CE,EAAcF,EAAI,UAAYA,EAAI,UAAY,UAC9CG,EAAUH,EAAI,QAAU,SAASA,EAAI,OAAO,EAAI,EAChDI,EAAUJ,EAAI,QAAU,SAASA,EAAI,OAAO,EAAI,EAEhDK,EAAM,GACV,GAAIN,IAAM,EAAG,CACX,IAAIO,EAAOR,EAAS,OAAO,MAAM,EACjCQ,EAAK,KAAK,KAAMN,EAAI,WAAW,CAAC,EAChCM,EAAK,KAAK,KAAMN,EAAI,WAAW,CAAC,EAChCM,EAAK,KAAK,KAAMN,EAAI,SAAS,CAAC,EAC9BM,EAAK,KAAK,KAAMN,EAAI,SAAS,CAAC,EAE9BM,EAAK,KAAK,eAAgB,GAAG,EAC7BA,EAAK,KAAK,SAAUJ,CAAW,EAC/BI,EAAK,MAAM,OAAQ,MAAM,EACrBN,EAAI,OAAS,SACfM,EAAK,KAAK,aAAc,OAASD,EAAM,aAAa,GAElDL,EAAI,OAAS,SAAWA,EAAI,OAAS,UACvCM,EAAK,KAAK,eAAgB,OAASD,EAAM,YAAY,EAEvDN,EAAI,EACN,KAAO,CACL,IAAIO,EAAOR,EAAS,OAAO,MAAM,EACjCQ,EACG,KAAK,OAAQ,MAAM,EACnB,KAAK,eAAgB,GAAG,EACxB,KAAK,SAAUJ,CAAW,EAC1B,KACC,IACA,iDACG,WAAW,SAAUF,EAAI,WAAW,CAAC,EACrC,WAAW,SAAUA,EAAI,WAAW,CAAC,EACrC,WACC,WACAA,EAAI,WAAW,GACZA,EAAI,SAAS,EAAIA,EAAI,WAAW,GAAK,GACrCA,EAAI,SAAS,EAAIA,EAAI,WAAW,GAAK,CAC1C,EACC,WAAW,WAAYA,EAAI,WAAW,GAAKA,EAAI,SAAS,EAAIA,EAAI,WAAW,GAAK,CAAC,EACjF,WAAW,QAASA,EAAI,SAAS,CAAC,EAClC,WAAW,QAASA,EAAI,SAAS,CAAC,CACvC,EACEA,EAAI,OAAS,SACfM,EAAK,KAAK,aAAc,OAASD,EAAM,aAAa,GAElDL,EAAI,OAAS,SAAWA,EAAI,OAAS,UACvCM,EAAK,KAAK,eAAgB,OAASD,EAAM,YAAY,CAEzD,CAEA,IAAIE,EAAcV,EAAK,YAAY,EACnCW,EAAuBX,CAAI,EACzBG,EAAI,MAAM,KACVF,EACA,KAAK,IAAIE,EAAI,WAAW,EAAGA,EAAI,SAAS,CAAC,EACvC,KAAK,IAAIA,EAAI,SAAS,EAAIA,EAAI,WAAW,CAAC,EAAI,EAC9CG,EACF,KAAK,IAAIH,EAAI,WAAW,EAAGA,EAAI,SAAS,CAAC,EACvC,KAAK,IAAIA,EAAI,SAAS,EAAIA,EAAI,WAAW,CAAC,EAAI,EAC9CI,EACFJ,EAAI,MAAM,MACVA,EAAI,MAAM,OACV,CAAE,KAAMC,CAAU,EAClBM,CACF,EAEIP,EAAI,OAASA,EAAI,MAAM,OAAS,KAClCO,EAAcV,EAAK,YAAY,EAC/BW,EAAuBX,CAAI,EACzB,IAAMG,EAAI,MAAM,KAAO,IACvBF,EACA,KAAK,IAAIE,EAAI,WAAW,EAAGA,EAAI,SAAS,CAAC,EACvC,KAAK,IAAIA,EAAI,SAAS,EAAIA,EAAI,WAAW,CAAC,EAAI,EAC9CG,EACF,KAAK,IAAIH,EAAI,WAAW,EAAGA,EAAI,SAAS,CAAC,EACvC,KAAK,IAAIA,EAAI,SAAS,EAAIA,EAAI,WAAW,CAAC,EAAI,EAC9CH,EAAK,gBACL,EACAO,EACF,KAAK,IAAIJ,EAAI,MAAM,MAAOA,EAAI,MAAM,KAAK,EACzCA,EAAI,MAAM,OACV,CAAE,KAAMC,EAAW,aAAc,QAAS,EAC1CM,CACF,EAEJ,CACF,EA5FwB,YAqGlBE,GAAezB,EAAA,SAAUC,EAAMyB,EAAUb,EAAM,CACnD,IAAMc,EAAe1B,EAAK,OAAO,GAAG,EAEhC2B,EAAYF,EAAS,QAAUA,EAAS,QAAU,OAClDR,EAAcQ,EAAS,YAAcA,EAAS,YAAc,UAC5DG,EAAYH,EAAS,UAAYA,EAAS,UAAY,QAEtDI,EAAa,CAAE,eAAgB,EAAK,mBAAoB,SAAU,EAClEJ,EAAS,WACXI,EAAa,CAAE,eAAgB,CAAI,GAErC,IAAI5B,EAAW,CACb,EAAGwB,EAAS,EACZ,EAAGA,EAAS,EACZ,KAAME,EACN,OAAQV,EACR,MAAOQ,EAAS,MAChB,OAAQA,EAAS,OACjB,GAAI,IACJ,GAAI,IACJ,MAAOI,CACT,EAEA/B,GAAS4B,EAAczB,CAAQ,EAG/B,IAAI6B,EAAelB,EAAK,aAAa,EACrCkB,EAAa,WAAa,OAC1BA,EAAa,SAAWA,EAAa,SAAW,EAChDA,EAAa,UAAYF,EACzBL,EAAuBX,CAAI,EACzBa,EAAS,MAAM,KACfC,EACAD,EAAS,EACTA,EAAS,EAAIA,EAAS,MAAM,EAC5BA,EAAS,MACTA,EAAS,OACT,CAAE,KAAM,SAAU,EAClBK,CACF,EAGIL,EAAS,MAAQA,EAAS,KAAK,OAAS,KAC1CK,EAAelB,EAAK,aAAa,EACjCkB,EAAa,UAAYF,EACzBL,EAAuBX,CAAI,EACzBa,EAAS,KAAK,KACdC,EACAD,EAAS,EACTA,EAAS,EAAIA,EAAS,KAAK,EAC3BA,EAAS,MACTA,EAAS,OACT,CAAE,KAAM,SAAU,EAClBK,CACF,GAIEL,EAAS,OAASA,EAAS,MAAM,OAAS,KAC5CK,EAAelB,EAAK,aAAa,EACjCkB,EAAa,SAAWA,EAAa,SAAW,EAChDA,EAAa,UAAYF,EACzBL,EAAuBX,CAAI,EACzBa,EAAS,MAAM,KACfC,EACAD,EAAS,EACTA,EAAS,EAAIA,EAAS,MAAM,EAC5BA,EAAS,MACTA,EAAS,OACT,CAAE,KAAM,SAAU,EAClBK,CACF,EAEJ,EAzEqB,gBA2ERC,GAAchC,EAAA,SAAUC,EAAMgC,EAASpB,EAAM,CACxD,IAAIe,EAAYK,EAAQ,QAAUA,EAAQ,QAAUpB,EAAKoB,EAAQ,YAAY,KAAO,WAAW,EAC3Ff,EAAce,EAAQ,YACtBA,EAAQ,YACRpB,EAAKoB,EAAQ,YAAY,KAAO,eAAe,EAC/CJ,EAAYI,EAAQ,UAAYA,EAAQ,UAAY,UAEpDC,EACF,qyBACF,OAAQD,EAAQ,YAAY,KAAM,CAChC,IAAK,SACHC,EACE,qyBACF,MACF,IAAK,kBACHA,EACE,ivBACF,KACJ,CAEA,IAAMC,EAAclC,EAAK,OAAO,GAAG,EACnCkC,EAAY,KAAK,QAAS,YAAY,EAItC,IAAMC,EAAqBC,GAAY,EAEvC,OAAQJ,EAAQ,YAAY,KAAM,CAChC,IAAK,SACL,IAAK,kBACL,IAAK,SACL,IAAK,kBACL,IAAK,YACL,IAAK,qBACL,IAAK,YACL,IAAK,qBACHG,EAAK,EAAIH,EAAQ,EACjBG,EAAK,EAAIH,EAAQ,EACjBG,EAAK,KAAOR,EACZQ,EAAK,MAAQH,EAAQ,MACrBG,EAAK,OAASH,EAAQ,OACtBG,EAAK,OAASlB,EACdkB,EAAK,GAAK,IACVA,EAAK,GAAK,IACVA,EAAK,MAAQ,CAAE,eAAgB,EAAI,EACnCrC,GAASoC,EAAaC,CAAI,EAC1B,MACF,IAAK,YACL,IAAK,qBACL,IAAK,eACL,IAAK,wBACL,IAAK,eACL,IAAK,wBACHD,EACG,OAAO,MAAM,EACb,KAAK,OAAQP,CAAS,EACtB,KAAK,eAAgB,KAAK,EAC1B,KAAK,SAAUV,CAAW,EAC1B,KACC,IACA,4HACG,WAAW,SAAUe,EAAQ,CAAC,EAC9B,WAAW,SAAUA,EAAQ,CAAC,EAC9B,WAAW,OAAQA,EAAQ,MAAQ,CAAC,EACpC,WAAW,SAAUA,EAAQ,MAAM,CACxC,EACFE,EACG,OAAO,MAAM,EACb,KAAK,OAAQ,MAAM,EACnB,KAAK,eAAgB,KAAK,EAC1B,KAAK,SAAUjB,CAAW,EAC1B,KACC,IACA,0DACG,WAAW,SAAUe,EAAQ,CAAC,EAC9B,WAAW,SAAUA,EAAQ,CAAC,EAC9B,WAAW,OAAQA,EAAQ,MAAQ,CAAC,CACzC,EACF,MACF,IAAK,eACL,IAAK,wBACL,IAAK,kBACL,IAAK,2BACL,IAAK,kBACL,IAAK,2BACHE,EACG,OAAO,MAAM,EACb,KAAK,OAAQP,CAAS,EACtB,KAAK,eAAgB,KAAK,EAC1B,KAAK,SAAUV,CAAW,EAC1B,KACC,IACA,kHACG,WAAW,SAAUe,EAAQ,CAAC,EAC9B,WAAW,SAAUA,EAAQ,CAAC,EAC9B,WAAW,QAASA,EAAQ,KAAK,EACjC,WAAW,OAAQA,EAAQ,OAAS,CAAC,CAC1C,EACFE,EACG,OAAO,MAAM,EACb,KAAK,OAAQ,MAAM,EACnB,KAAK,eAAgB,KAAK,EAC1B,KAAK,SAAUjB,CAAW,EAC1B,KACC,IACA,2DACG,WAAW,SAAUe,EAAQ,EAAIA,EAAQ,KAAK,EAC9C,WAAW,SAAUA,EAAQ,CAAC,EAC9B,WAAW,OAAQA,EAAQ,OAAS,CAAC,CAC1C,EACF,KACJ,CAGA,IAAIK,EAAkBC,GAAe1B,EAAMoB,EAAQ,YAAY,IAAI,EAcnE,OAbAE,EACG,OAAO,MAAM,EACb,KAAK,OAAQN,CAAS,EACtB,KAAK,cAAeS,EAAgB,UAAU,EAC9C,KAAK,YAAaA,EAAgB,SAAW,CAAC,EAC9C,KAAK,aAAc,QAAQ,EAC3B,KAAK,eAAgB,SAAS,EAC9B,KAAK,aAAcL,EAAQ,YAAY,KAAK,EAC5C,KAAK,IAAKA,EAAQ,EAAIA,EAAQ,MAAQ,EAAIA,EAAQ,YAAY,MAAQ,CAAC,EACvE,KAAK,IAAKA,EAAQ,EAAIA,EAAQ,YAAY,CAAC,EAC3C,KAAK,KAAOA,EAAQ,YAAY,KAAO,IAAI,EAGtCA,EAAQ,YAAY,KAAM,CAChC,IAAK,SACL,IAAK,kBACH9B,GACEgC,EACA,GACA,GACAF,EAAQ,EAAIA,EAAQ,MAAQ,EAAI,GAChCA,EAAQ,EAAIA,EAAQ,MAAM,EAC1BC,CACF,EACA,KACJ,CAGA,IAAIM,EAAe3B,EAAKoB,EAAQ,YAAY,KAAO,MAAM,EAAE,EAC3D,OAAAO,EAAa,WAAa,OAC1BA,EAAa,SAAWA,EAAa,SAAW,EAChDA,EAAa,UAAYX,EACzBL,EAAuBX,CAAI,EACzBoB,EAAQ,MAAM,KACdE,EACAF,EAAQ,EACRA,EAAQ,EAAIA,EAAQ,MAAM,EAC1BA,EAAQ,MACRA,EAAQ,OACR,CAAE,KAAMJ,CAAU,EAClBW,CACF,EAGAA,EAAe3B,EAAKoB,EAAQ,YAAY,KAAO,MAAM,EAAE,EACvDO,EAAa,UAAYX,EAErBI,EAAQ,OAASA,EAAQ,OAAO,OAAS,GAC3CT,EAAuBX,CAAI,EACzBoB,EAAQ,MAAM,KACdE,EACAF,EAAQ,EACRA,EAAQ,EAAIA,EAAQ,MAAM,EAC1BA,EAAQ,MACRA,EAAQ,OACR,CAAE,KAAMJ,EAAW,aAAc,QAAS,EAC1CW,CACF,EACSP,EAAQ,MAAQA,EAAQ,KAAK,OAAS,IAC/CT,EAAuBX,CAAI,EACzBoB,EAAQ,KAAK,KACbE,EACAF,EAAQ,EACRA,EAAQ,EAAIA,EAAQ,KAAK,EACzBA,EAAQ,MACRA,EAAQ,OACR,CAAE,KAAMJ,EAAW,aAAc,QAAS,EAC1CW,CACF,EAIEP,EAAQ,OAASA,EAAQ,MAAM,OAAS,KAC1CO,EAAe3B,EAAK,WAAW,EAC/B2B,EAAa,UAAYX,EACzBL,EAAuBX,CAAI,EACzBoB,EAAQ,MAAM,KACdE,EACAF,EAAQ,EACRA,EAAQ,EAAIA,EAAQ,MAAM,EAC1BA,EAAQ,MACRA,EAAQ,OACR,CAAE,KAAMJ,CAAU,EAClBW,CACF,GAGKP,EAAQ,MACjB,EA3M2B,eA6MdQ,GAAqBzC,EAAA,SAAUC,EAAM,CAChDA,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAM,UAAU,EACrB,KAAK,YAAa,SAAS,EAC3B,KAAK,YAAa,SAAS,EAC3B,OAAO,MAAM,EACb,KAAK,YAAa,WAAW,EAC7B,KACC,IACA,i1ZACF,CACJ,EAbkC,sBAerByC,GAAqB1C,EAAA,SAAUC,EAAM,CAChDA,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAM,UAAU,EACrB,KAAK,QAAS,IAAI,EAClB,KAAK,SAAU,IAAI,EACnB,OAAO,MAAM,EACb,KAAK,YAAa,WAAW,EAC7B,KACC,IACA,0JACF,CACJ,EAbkC,sBAerB0C,GAAkB3C,EAAA,SAAUC,EAAM,CAC7CA,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAM,OAAO,EAClB,KAAK,QAAS,IAAI,EAClB,KAAK,SAAU,IAAI,EACnB,OAAO,MAAM,EACb,KAAK,YAAa,WAAW,EAC7B,KACC,IACA,2UACF,CACJ,EAb+B,mBAoBlB2C,GAAkB5C,EAAA,SAAUC,EAAM,CAC7CA,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAM,WAAW,EACtB,KAAK,OAAQ,CAAC,EACd,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,gBAAgB,EACpC,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,uBAAuB,CACtC,EAb+B,mBAelB4C,GAAiB7C,EAAA,SAAUC,EAAM,CAC5CA,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAM,UAAU,EACrB,KAAK,OAAQ,CAAC,EACd,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,gBAAgB,EACpC,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,wBAAwB,CACvC,EAb8B,kBAoBjB6C,GAAwB9C,EAAA,SAAUC,EAAM,CACnDA,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAM,aAAa,EACxB,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,CAAC,EACd,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,IAAK,2BAA2B,CAC1C,EAZqC,yBAmBxB8C,GAAsB/C,EAAA,SAAUC,EAAM,CACjDA,EACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,KAAM,gBAAgB,EAC3B,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,EAAE,EACf,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,EAAE,EACvB,KAAK,SAAU,MAAM,EACrB,OAAO,QAAQ,EACf,KAAK,KAAM,EAAE,EACb,KAAK,KAAM,EAAE,EACb,KAAK,IAAK,CAAC,CAEhB,EAfmC,uBAsBtB+C,GAAuBhD,EAAA,SAAUC,EAAM,CAElD,IAAMgD,EADOhD,EAAK,OAAO,MAAM,EAE5B,OAAO,QAAQ,EACf,KAAK,KAAM,WAAW,EACtB,KAAK,cAAe,EAAE,EACtB,KAAK,eAAgB,CAAC,EACtB,KAAK,SAAU,MAAM,EACrB,KAAK,OAAQ,EAAE,EACf,KAAK,OAAQ,CAAC,EAGjBgD,EACG,OAAO,MAAM,EACb,KAAK,OAAQ,OAAO,EACpB,KAAK,SAAU,SAAS,EACxB,MAAM,mBAAoB,MAAM,EAChC,KAAK,eAAgB,KAAK,EAC1B,KAAK,IAAK,mBAAmB,EAGhCA,EACG,OAAO,MAAM,EACb,KAAK,OAAQ,MAAM,EACnB,KAAK,SAAU,SAAS,EACxB,MAAM,mBAAoB,MAAM,EAChC,KAAK,eAAgB,KAAK,EAC1B,KAAK,IAAK,yBAAyB,CAExC,EA7BoC,wBA+B9BV,GAAiBvC,EAAA,CAACkD,EAAKC,KACpB,CACL,WAAYD,EAAIC,EAAc,YAAY,EAC1C,SAAUD,EAAIC,EAAc,UAAU,EACtC,WAAYD,EAAIC,EAAc,YAAY,CAC5C,GALqB,kBAQjB3B,EAA0B,UAAY,CAU1C,SAAS4B,EAAOC,EAASC,EAAGhD,EAAGC,EAAGH,EAAOC,EAAQkD,EAAW,CAC1D,IAAMC,EAAOF,EACV,OAAO,MAAM,EACb,KAAK,IAAKhD,EAAIF,EAAQ,CAAC,EACvB,KAAK,IAAKG,EAAIF,EAAS,EAAI,CAAC,EAC5B,MAAM,cAAe,QAAQ,EAC7B,KAAKgD,CAAO,EACfI,EAAcD,EAAMD,CAAS,CAC/B,CARSvD,EAAAoD,EAAA,UAoBT,SAASM,EAAQL,EAASC,EAAGhD,EAAGC,EAAGH,EAAOC,EAAQkD,EAAW1C,EAAM,CACjE,GAAM,CAAE,SAAA8C,EAAU,WAAAC,EAAY,WAAAC,CAAW,EAAIhD,EAEvCiD,EAAQT,EAAQ,MAAMU,GAAO,cAAc,EACjD,QAAShD,EAAI,EAAGA,EAAI+C,EAAM,OAAQ/C,IAAK,CACrC,IAAMiD,EAAKjD,EAAI4C,EAAYA,GAAYG,EAAM,OAAS,GAAM,EACtDN,EAAOF,EACV,OAAO,MAAM,EACb,KAAK,IAAKhD,EAAIF,EAAQ,CAAC,EACvB,KAAK,IAAKG,CAAC,EACX,MAAM,cAAe,QAAQ,EAC7B,KAAK,oBAAqB,QAAQ,EAClC,MAAM,YAAaoD,CAAQ,EAC3B,MAAM,cAAeE,CAAU,EAC/B,MAAM,cAAeD,CAAU,EAClCJ,EACG,OAAO,OAAO,EAEd,KAAK,KAAMQ,CAAE,EACb,KAAKF,EAAM/C,CAAC,CAAC,EAEb,KAAK,qBAAsB,cAAc,EAE5C0C,EAAcD,EAAMD,CAAS,CAC/B,CACF,CAzBSvD,EAAA0D,EAAA,WAqCT,SAASO,EAAKZ,EAASC,EAAGhD,EAAGC,EAAGH,EAAOC,EAAQkD,EAAW1C,EAAM,CAC9D,IAAMqD,EAAIZ,EAAE,OAAO,QAAQ,EAQrBE,EAPIU,EACP,OAAO,eAAe,EACtB,KAAK,IAAK5D,CAAC,EACX,KAAK,IAAKC,CAAC,EACX,KAAK,QAASH,CAAK,EACnB,KAAK,SAAUC,CAAM,EAGrB,OAAO,WAAW,EAClB,MAAM,UAAW,OAAO,EACxB,MAAM,SAAU,MAAM,EACtB,MAAM,QAAS,MAAM,EAExBmD,EACG,OAAO,KAAK,EACZ,MAAM,UAAW,YAAY,EAC7B,MAAM,aAAc,QAAQ,EAC5B,MAAM,iBAAkB,QAAQ,EAChC,KAAKH,CAAO,EAEfK,EAAQL,EAASa,EAAG5D,EAAGC,EAAGH,EAAOC,EAAQkD,EAAW1C,CAAI,EACxD4C,EAAcD,EAAMD,CAAS,CAC/B,CAxBSvD,EAAAiE,EAAA,QA8BT,SAASR,EAAcU,EAAQC,EAAmB,CAChD,QAAWC,KAAOD,EACZA,EAAkB,eAAeC,CAAG,GACtCF,EAAO,KAAKE,EAAKD,EAAkBC,CAAG,CAAC,CAG7C,CANS,OAAArE,EAAAyD,EAAA,iBAQF,SAAU5C,EAAM,CACrB,OAAOA,EAAK,gBAAkB,KAAOoD,EAAOpD,EAAK,gBAAkB,MAAQuC,EAASM,CACtF,CACF,EAAG,EAEIY,EAAQ,CACb,SAAAvE,GACA,aAAA0B,GACA,YAAAO,GACA,SAAArB,GACA,UAAAR,GACA,gBAAAyC,GACA,eAAAC,GACA,sBAAAC,GACA,oBAAAC,GACA,qBAAAC,GACA,mBAAAP,GACA,mBAAAC,GACA,gBAAAC,EACF,ECrqBA,IAAI4B,GAAqB,EACvBC,GAAqB,EAEnBC,GAAe,EACfC,GAAkB,EAEtBC,GAAO,GAAKC,GAEZ,IAAIC,EAAO,CAAC,EAENC,GAAN,KAAa,CArBb,MAqBa,CAAAC,EAAA,eACX,YAAYC,EAAS,CACnB,KAAK,KAAO,GACZ,KAAK,KAAO,CAAC,EACb,KAAK,KAAK,OAAS,OACnB,KAAK,KAAK,MAAQ,OAClB,KAAK,KAAK,OAAS,OACnB,KAAK,KAAK,MAAQ,OAClB,KAAK,KAAK,WAAa,OAEvB,KAAK,SAAW,CAAC,EACjB,KAAK,SAAS,OAAS,OACvB,KAAK,SAAS,MAAQ,OACtB,KAAK,SAAS,OAAS,OACvB,KAAK,SAAS,MAAQ,OACtB,KAAK,SAAS,IAAM,EAEpBC,GAAQD,EAAQ,GAAG,UAAU,CAAC,CAChC,CAEA,QAAQE,EAAQC,EAAOC,EAAQC,EAAO,CACpC,KAAK,SAAS,OAAS,KAAK,KAAK,OAASH,EAC1C,KAAK,SAAS,MAAQ,KAAK,KAAK,MAAQC,EACxC,KAAK,SAAS,OAAS,KAAK,KAAK,OAASC,EAC1C,KAAK,SAAS,MAAQ,KAAK,KAAK,MAAQC,CAC1C,CAEA,UAAUC,EAAKC,EAAKC,EAAKC,EAAK,CACxBH,EAAIC,CAAG,IAAM,OACfD,EAAIC,CAAG,EAAIC,EAEXF,EAAIC,CAAG,EAAIE,EAAID,EAAKF,EAAIC,CAAG,CAAC,CAEhC,CAEA,OAAOG,EAAS,CACd,KAAK,SAAS,IAAM,KAAK,SAAS,IAAM,EACxC,IAAIC,EACF,KAAK,SAAS,SAAW,KAAK,SAAS,MACnC,KAAK,SAAS,MAAQD,EAAQ,OAC9B,KAAK,SAAS,MAAQA,EAAQ,OAAS,EACzCE,EAASD,EAAUD,EAAQ,MAC3BG,EAAU,KAAK,SAAS,OAASH,EAAQ,OAAS,EAClDI,EAASD,EAAUH,EAAQ,QAE7BC,GAAW,KAAK,KAAK,YACrBC,GAAU,KAAK,KAAK,YACpB,KAAK,SAAS,IAAMnB,MAEpBkB,EAAU,KAAK,SAAS,OAASD,EAAQ,OAASb,EAAK,iBACvDgB,EAAU,KAAK,SAAS,MAAQH,EAAQ,OAAS,EAEjD,KAAK,SAAS,MAAQE,EAASD,EAAUD,EAAQ,MACjD,KAAK,SAAS,OAAS,KAAK,SAAS,MACrC,KAAK,SAAS,MAAQI,EAASD,EAAUH,EAAQ,OACjD,KAAK,SAAS,IAAM,GAGtBA,EAAQ,EAAIC,EACZD,EAAQ,EAAIG,EAEZ,KAAK,UAAU,KAAK,KAAM,SAAUF,EAAS,KAAK,GAAG,EACrD,KAAK,UAAU,KAAK,KAAM,SAAUE,EAAS,KAAK,GAAG,EACrD,KAAK,UAAU,KAAK,KAAM,QAASD,EAAQ,KAAK,GAAG,EACnD,KAAK,UAAU,KAAK,KAAM,QAASE,EAAQ,KAAK,GAAG,EAEnD,KAAK,UAAU,KAAK,SAAU,SAAUH,EAAS,KAAK,GAAG,EACzD,KAAK,UAAU,KAAK,SAAU,SAAUE,EAAS,KAAK,GAAG,EACzD,KAAK,UAAU,KAAK,SAAU,QAASD,EAAQ,KAAK,GAAG,EACvD,KAAK,UAAU,KAAK,SAAU,QAASE,EAAQ,KAAK,GAAG,CACzD,CAEA,KAAKd,EAAS,CACZ,KAAK,KAAO,GACZ,KAAK,KAAO,CACV,OAAQ,OACR,MAAO,OACP,OAAQ,OACR,MAAO,OACP,WAAY,MACd,EACA,KAAK,SAAW,CACd,OAAQ,OACR,MAAO,OACP,OAAQ,OACR,MAAO,OACP,IAAK,CACP,EACAC,GAAQD,EAAQ,GAAG,UAAU,CAAC,CAChC,CAEA,eAAee,EAAQ,CACrB,KAAK,KAAK,OAASA,EACnB,KAAK,KAAK,OAASA,CACrB,CACF,EAEad,GAAUF,EAAA,SAAUiB,EAAK,CACpCC,GAAgBpB,EAAMmB,CAAG,EAErBA,EAAI,aACNnB,EAAK,iBAAmBA,EAAK,iBAAmBA,EAAK,kBAAoBmB,EAAI,YAE3EA,EAAI,WACNnB,EAAK,eAAiBA,EAAK,eAAiBA,EAAK,gBAAkBmB,EAAI,UAErEA,EAAI,aACNnB,EAAK,iBAAmBA,EAAK,iBAAmBA,EAAK,kBAAoBmB,EAAI,WAEjF,EAZuB,WAcjBE,GAAcnB,EAAA,CAACiB,EAAKG,KACjB,CACL,WAAYH,EAAIG,EAAc,YAAY,EAC1C,SAAUH,EAAIG,EAAc,UAAU,EACtC,WAAYH,EAAIG,EAAc,YAAY,CAC5C,GALkB,eAQdC,GAAerB,EAACiB,IACb,CACL,WAAYA,EAAI,mBAChB,SAAUA,EAAI,iBACd,WAAYA,EAAI,kBAClB,GALmB,gBAQfK,GAActB,EAACiB,IACZ,CACL,WAAYA,EAAI,kBAChB,SAAUA,EAAI,gBACd,WAAYA,EAAI,iBAClB,GALkB,eAepB,SAASM,EAAkBC,EAAUb,EAASc,EAAiBC,EAAUC,EAAgB,CACvF,GAAI,CAAChB,EAAQa,CAAQ,EAAE,MACrB,GAAIC,EACFd,EAAQa,CAAQ,EAAE,KAAOI,GAAUjB,EAAQa,CAAQ,EAAE,KAAMG,EAAgBD,CAAQ,EACnFf,EAAQa,CAAQ,EAAE,UAAYb,EAAQa,CAAQ,EAAE,KAAK,MAAMK,GAAO,cAAc,EAAE,OAElFlB,EAAQa,CAAQ,EAAE,MAAQG,EAE1BhB,EAAQa,CAAQ,EAAE,OAASM,GAAoBnB,EAAQa,CAAQ,EAAE,KAAME,CAAQ,MAC1E,CACL,IAAIK,EAAQpB,EAAQa,CAAQ,EAAE,KAAK,MAAMK,GAAO,cAAc,EAC9DlB,EAAQa,CAAQ,EAAE,UAAYO,EAAM,OACpC,IAAIC,EAAa,EACjBrB,EAAQa,CAAQ,EAAE,OAAS,EAC3Bb,EAAQa,CAAQ,EAAE,MAAQ,EAC1B,QAAWS,KAAQF,EACjBpB,EAAQa,CAAQ,EAAE,MAAQ,KAAK,IAC7BU,GAAmBD,EAAMP,CAAQ,EACjCf,EAAQa,CAAQ,EAAE,KACpB,EACAQ,EAAaF,GAAoBG,EAAMP,CAAQ,EAC/Cf,EAAQa,CAAQ,EAAE,OAASb,EAAQa,CAAQ,EAAE,OAASQ,CAG1D,CAEJ,CA1BShC,EAAAuB,EAAA,qBA4BF,IAAMY,GAAenC,EAAA,SAAUoC,EAASC,EAAUC,EAAQ,CAC/DD,EAAS,EAAIC,EAAO,KAAK,OACzBD,EAAS,EAAIC,EAAO,KAAK,OACzBD,EAAS,MAAQC,EAAO,KAAK,MAAQA,EAAO,KAAK,OACjDD,EAAS,OAASC,EAAO,KAAK,MAAQA,EAAO,KAAK,OAElDD,EAAS,MAAM,EAAIvC,EAAK,cAAgB,GAExC,IAAIyC,EAAmBF,EAAS,MAAQvC,EAAK,KACzC0C,EAAoBnB,GAAavB,CAAI,EACzC0C,EAAkB,SAAWA,EAAkB,SAAW,EAC1DA,EAAkB,WAAa,OAC/B,IAAIb,EAAiBO,GAAmBG,EAAS,MAAM,KAAMG,CAAiB,EAC9EjB,EAAkB,QAASc,EAAUE,EAAkBC,EAAmBb,CAAc,EAExFc,EAAQ,aAAaL,EAASC,EAAUvC,CAAI,CAC9C,EAhB4B,gBAkBf4C,GAAmB1C,EAAA,SAAU2C,EAAeP,EAASQ,EAAcC,EAAa,CAE3F,IAAIC,EAAI,EAER,QAAWC,KAAcF,EAAa,CACpCC,EAAI,EACJ,IAAMnC,EAAUiC,EAAaG,CAAU,EAInCC,EAAkB7B,GAAYrB,EAAMa,EAAQ,YAAY,IAAI,EAgBhE,OAfAqC,EAAgB,SAAWA,EAAgB,SAAW,EACtDrC,EAAQ,YAAY,MAAQuB,GAC1B,OAAMvB,EAAQ,YAAY,KAAO,OACjCqC,CACF,EACArC,EAAQ,YAAY,OAASqC,EAAgB,SAAW,EACxDrC,EAAQ,YAAY,EAAIb,EAAK,eAC7BgD,EAAInC,EAAQ,YAAY,EAAIA,EAAQ,YAAY,OAAS,EAOzDA,EAAQ,MAAQ,CAAE,MAAO,EAAG,OAAQ,EAAG,EAAG,CAAE,EACpCA,EAAQ,YAAY,KAAM,CAChC,IAAK,SACL,IAAK,kBACHA,EAAQ,MAAM,MAAQ,GACtBA,EAAQ,MAAM,OAAS,GACvBA,EAAQ,MAAM,EAAImC,EAClBA,EAAInC,EAAQ,MAAM,EAAIA,EAAQ,MAAM,OACpC,KACJ,CACIA,EAAQ,SACVA,EAAQ,MAAM,MAAQ,GACtBA,EAAQ,MAAM,OAAS,GACvBA,EAAQ,MAAM,EAAImC,EAClBA,EAAInC,EAAQ,MAAM,EAAIA,EAAQ,MAAM,QAKtC,IAAIc,EAAkBd,EAAQ,MAAQb,EAAK,KACvC6B,EAAiB7B,EAAK,MAAQA,EAAK,eAAiB,EAEpDmD,EAAmB9B,GAAYrB,EAAMa,EAAQ,YAAY,IAAI,EAOjE,GANAsC,EAAiB,SAAWA,EAAiB,SAAW,EACxDA,EAAiB,WAAa,OAC9B1B,EAAkB,QAASZ,EAASc,EAAiBwB,EAAkBtB,CAAc,EACrFhB,EAAQ,MAAM,EAAImC,EAAI,EACtBA,EAAInC,EAAQ,MAAM,EAAIA,EAAQ,MAAM,OAEhCA,EAAQ,MAAQA,EAAQ,KAAK,OAAS,GAAI,CAC5CA,EAAQ,KAAK,KAAO,IAAMA,EAAQ,KAAK,KAAO,IAC9C,IAAIqC,EAAkB7B,GAAYrB,EAAMa,EAAQ,YAAY,IAAI,EAChEY,EAAkB,OAAQZ,EAASc,EAAiBuB,EAAiBrB,CAAc,EACnFhB,EAAQ,KAAK,EAAImC,EAAI,EACrBA,EAAInC,EAAQ,KAAK,EAAIA,EAAQ,KAAK,MACpC,SAAWA,EAAQ,OAASA,EAAQ,MAAM,OAAS,GAAI,CACrDA,EAAQ,MAAM,KAAO,IAAMA,EAAQ,MAAM,KAAO,IAChD,IAAIuC,EAAmB/B,GAAYrB,EAAMa,EAAQ,MAAM,IAAI,EAC3DY,EAAkB,QAASZ,EAASc,EAAiByB,EAAkBvB,CAAc,EACrFhB,EAAQ,MAAM,EAAImC,EAAI,EACtBA,EAAInC,EAAQ,MAAM,EAAIA,EAAQ,MAAM,MACtC,CAEA,IAAIwC,EAAaL,EACbM,EAAYzC,EAAQ,MAAM,MAE9B,GAAIA,EAAQ,OAASA,EAAQ,MAAM,OAAS,GAAI,CAC9C,IAAI0C,EAAmBlC,GAAYrB,EAAMa,EAAQ,YAAY,IAAI,EACjEY,EAAkB,QAASZ,EAASc,EAAiB4B,EAAkB1B,CAAc,EACrFhB,EAAQ,MAAM,EAAImC,EAAI,GACtBA,EAAInC,EAAQ,MAAM,EAAIA,EAAQ,MAAM,OAEpCyC,EAAY,KAAK,IAAIzC,EAAQ,MAAM,MAAOA,EAAQ,MAAM,KAAK,EAC7DwC,EAAaL,EAAInC,EAAQ,MAAM,UAAY,CAC7C,CAEAyC,EAAYA,EAAYtD,EAAK,eAG7Ba,EAAQ,MAAQ,KAAK,IAAIA,EAAQ,OAASb,EAAK,MAAOsD,EAAWtD,EAAK,KAAK,EAC3Ea,EAAQ,OAAS,KAAK,IAAIA,EAAQ,QAAUb,EAAK,OAAQqD,EAAYrD,EAAK,MAAM,EAChFa,EAAQ,OAASA,EAAQ,QAAUb,EAAK,cAExC6C,EAAc,OAAOhC,CAAO,EAE5B8B,EAAQ,YAAYL,EAASzB,EAASb,CAAI,CAC5C,CAEA6C,EAAc,eAAe7C,EAAK,aAAa,CACjD,EA9FgC,oBAgG1BwD,EAAN,KAAY,CAjTZ,MAiTY,CAAAtD,EAAA,cACV,YAAYuD,EAAGC,EAAG,CAChB,KAAK,EAAID,EACT,KAAK,EAAIC,CACX,CACF,EAkBIC,GAAoBzD,EAAA,SAAU0D,EAAUC,EAAU,CACpD,IAAIC,EAAKF,EAAS,EAEdG,EAAKH,EAAS,EAEdI,EAAKH,EAAS,EAEdI,EAAKJ,EAAS,EAEdK,EAAcJ,EAAKF,EAAS,MAAQ,EAEpCO,EAAcJ,EAAKH,EAAS,OAAS,EAErCQ,EAAK,KAAK,IAAIN,EAAKE,CAAE,EAErBK,EAAK,KAAK,IAAIN,EAAKE,CAAE,EAErBK,EAASD,EAAKD,EAEdG,EAAUX,EAAS,OAASA,EAAS,MAErCY,EAAc,KAElB,OAAIT,GAAME,GAAMH,EAAKE,EACnBQ,EAAc,IAAIhB,EAAMM,EAAKF,EAAS,MAAOO,CAAW,EAC/CJ,GAAME,GAAMH,EAAKE,EAC1BQ,EAAc,IAAIhB,EAAMM,EAAIK,CAAW,EAC9BL,GAAME,GAAMD,EAAKE,EAC1BO,EAAc,IAAIhB,EAAMU,EAAaH,EAAKH,EAAS,MAAM,EAChDE,GAAME,GAAMD,EAAKE,IAC1BO,EAAc,IAAIhB,EAAMU,EAAaH,CAAE,GAGrCD,EAAKE,GAAMD,EAAKE,EACdM,GAAWD,EACbE,EAAc,IAAIhB,EAAMM,EAAIK,EAAeG,EAASV,EAAS,MAAS,CAAC,EAEvEY,EAAc,IAAIhB,EAChBU,EAAgBE,EAAKC,EAAMT,EAAS,OAAU,EAC9CG,EAAKH,EAAS,MAChB,EAEOE,EAAKE,GAAMD,EAAKE,EAErBM,GAAWD,EACbE,EAAc,IAAIhB,EAAMM,EAAKF,EAAS,MAAOO,EAAeG,EAASV,EAAS,MAAS,CAAC,EAExFY,EAAc,IAAIhB,EAChBU,EAAgBE,EAAKC,EAAMT,EAAS,OAAU,EAC9CG,EAAKH,EAAS,MAChB,EAEOE,EAAKE,GAAMD,EAAKE,EACrBM,GAAWD,EACbE,EAAc,IAAIhB,EAAMM,EAAKF,EAAS,MAAOO,EAAeG,EAASV,EAAS,MAAS,CAAC,EAExFY,EAAc,IAAIhB,EAAMU,EAAgBN,EAAS,OAAS,EAAKQ,EAAMC,EAAIN,CAAE,EAEpED,EAAKE,GAAMD,EAAKE,IACrBM,GAAWD,EACbE,EAAc,IAAIhB,EAAMM,EAAIK,EAAeP,EAAS,MAAQ,EAAKU,CAAM,EAEvEE,EAAc,IAAIhB,EAAMU,EAAgBN,EAAS,OAAS,EAAKQ,EAAMC,EAAIN,CAAE,GAGxES,CACT,EAlEwB,qBAoEpBC,GAAqBvE,EAAA,SAAU0D,EAAUc,EAAS,CACpD,IAAIC,EAAoB,CAAE,EAAG,EAAG,EAAG,CAAE,EACrCA,EAAkB,EAAID,EAAQ,EAAIA,EAAQ,MAAQ,EAClDC,EAAkB,EAAID,EAAQ,EAAIA,EAAQ,OAAS,EACnD,IAAIE,EAAajB,GAAkBC,EAAUe,CAAiB,EAE9DA,EAAkB,EAAIf,EAAS,EAAIA,EAAS,MAAQ,EACpDe,EAAkB,EAAIf,EAAS,EAAIA,EAAS,OAAS,EACrD,IAAIC,EAAWF,GAAkBe,EAASC,CAAiB,EAC3D,MAAO,CAAE,WAAYC,EAAY,SAAUf,CAAS,CACtD,EAVyB,sBAYZgB,GAAW3E,EAAA,SAAUoC,EAASwC,EAAMC,EAAe5E,EAAS,CACvE,IAAI6E,EAAI,EACR,QAASC,KAAOH,EAAM,CACpBE,EAAIA,EAAI,EACR,IAAIE,EAAcD,EAAI,MAAQjF,EAAK,KAC/BmF,EAAU3D,GAAYxB,CAAI,EACZG,EAAQ,GAAG,UAAU,IACnB,cAClB8E,EAAI,MAAM,KAAOD,EAAI,KAAOC,EAAI,MAAM,MAExC,IAAIpD,EAAiBO,GAAmB6C,EAAI,MAAM,KAAME,CAAO,EAC/D1D,EAAkB,QAASwD,EAAKC,EAAaC,EAAStD,CAAc,EAEhEoD,EAAI,OAASA,EAAI,MAAM,OAAS,KAClCpD,EAAiBO,GAAmB6C,EAAI,MAAM,KAAME,CAAO,EAC3D1D,EAAkB,QAASwD,EAAKC,EAAaC,EAAStD,CAAc,GAGlEoD,EAAI,OAASA,EAAI,MAAM,OAAS,KAClCpD,EAAiBO,GAAmB6C,EAAI,MAAM,KAAME,CAAO,EAC3D1D,EAAkB,QAASwD,EAAKC,EAAaC,EAAStD,CAAc,GAGtE,IAAI+B,EAAWmB,EAAcE,EAAI,IAAI,EACjCP,EAAUK,EAAcE,EAAI,EAAE,EAC9BG,EAASX,GAAmBb,EAAUc,CAAO,EACjDO,EAAI,WAAaG,EAAO,WACxBH,EAAI,SAAWG,EAAO,QACxB,CACAzC,EAAQ,SAASL,EAASwC,EAAM9E,CAAI,CACtC,EA9BwB,YAuCxB,SAASqF,GACP/C,EACAgD,EACAC,EACAC,EACArF,EACA,CACA,IAAI0C,EAAgB,IAAI5C,GAAOE,CAAO,EAEtC0C,EAAc,KAAK,WACjB0C,EAAa,KAAK,WAAa,KAAK,IAAI1F,GAAiB2F,EAAkB,MAAM,EAKnF,OAAS,CAACR,EAAGS,CAAe,IAAKD,EAAkB,QAAQ,EAAG,CAC5D,IAAIxC,EAAI,EACRyC,EAAgB,MAAQ,CAAE,MAAO,EAAG,OAAQ,EAAG,EAAG,CAAE,EAChDA,EAAgB,SAClBA,EAAgB,MAAM,MAAQ,GAC9BA,EAAgB,MAAM,OAAS,GAC/BA,EAAgB,MAAM,EAAIzC,EAC1BA,EAAIyC,EAAgB,MAAM,EAAIA,EAAgB,MAAM,QAGtD,IAAIC,EAA0BD,EAAgB,MAAQzF,EAAK,KAEvD2F,EAA2BpE,GAAavB,CAAI,EAahD,GAZA2F,EAAyB,SAAWA,EAAyB,SAAW,EACxEA,EAAyB,WAAa,OACtClE,EACE,QACAgE,EACAC,EACAC,EACA9C,EAAc,KAAK,UACrB,EACA4C,EAAgB,MAAM,EAAIzC,EAAI,EAC9BA,EAAIyC,EAAgB,MAAM,EAAIA,EAAgB,MAAM,OAEhDA,EAAgB,MAAQA,EAAgB,KAAK,OAAS,GAAI,CAC5DA,EAAgB,KAAK,KAAO,IAAMA,EAAgB,KAAK,KAAO,IAC9D,IAAIG,EAA0BrE,GAAavB,CAAI,EAC/CyB,EACE,OACAgE,EACAC,EACAE,EACA/C,EAAc,KAAK,UACrB,EACA4C,EAAgB,KAAK,EAAIzC,EAAI,EAC7BA,EAAIyC,EAAgB,KAAK,EAAIA,EAAgB,KAAK,MACpD,CAEA,GAAIA,EAAgB,OAASA,EAAgB,MAAM,OAAS,GAAI,CAC9D,IAAII,EAA2BtE,GAAavB,CAAI,EAChD6F,EAAyB,SAAWA,EAAyB,SAAW,EACxEpE,EACE,QACAgE,EACAC,EACAG,EACAhD,EAAc,KAAK,UACrB,EACA4C,EAAgB,MAAM,EAAIzC,EAAI,GAC9BA,EAAIyC,EAAgB,MAAM,EAAIA,EAAgB,MAAM,MACtD,CAEA,GAAIT,GAAK,GAAKA,EAAInF,KAAoB,EAAG,CAEvC,IAAIiG,EAAKP,EAAa,KAAK,OAASvF,EAAK,eACrC+F,EAAKR,EAAa,KAAK,MAAQvF,EAAK,eAAiBgD,EAEzDH,EAAc,QAAQiD,EAAIA,EAAIC,EAAIA,CAAE,CACtC,KAAO,CAEL,IAAID,EACFjD,EAAc,KAAK,QAAUA,EAAc,KAAK,OAC5CA,EAAc,KAAK,MAAQ7C,EAAK,eAChC6C,EAAc,KAAK,OACrBkD,EAAKlD,EAAc,KAAK,OAE5BA,EAAc,QAAQiD,EAAIA,EAAIC,EAAIA,CAAE,CACtC,CACAlD,EAAc,KAAO4C,EAAgB,MACrC,IAAIO,EAA6B7F,EAAQ,GAAG,gBAAgBsF,EAAgB,KAAK,EAC7EQ,EAA4B9F,EAAQ,GAAG,eAAesF,EAAgB,KAAK,EAE3EQ,EAA0B,OAAS,GACrCrD,GACEC,EACAP,EACA0D,EACAC,CACF,EAEFX,EAAsBG,EAAgB,MACtC,IAAIS,EAAwB/F,EAAQ,GAAG,aAAamF,CAAmB,EAEnEY,EAAsB,OAAS,GAEjCb,GACE/C,EACAgD,EACAzC,EACAqD,EACA/F,CACF,EAGEsF,EAAgB,QAAU,UAC5BpD,GAAaC,EAASmD,EAAiB5C,CAAa,EAEtD0C,EAAa,KAAK,MAAQ,KAAK,IAC7B1C,EAAc,KAAK,MAAQ7C,EAAK,cAChCuF,EAAa,KAAK,KACpB,EACAA,EAAa,KAAK,MAAQ,KAAK,IAC7B1C,EAAc,KAAK,MAAQ7C,EAAK,cAChCuF,EAAa,KAAK,KACpB,EACA7F,GAAqB,KAAK,IAAIA,GAAoB6F,EAAa,KAAK,KAAK,EACzE5F,GAAqB,KAAK,IAAIA,GAAoB4F,EAAa,KAAK,KAAK,CAC3E,CACF,CA5HSrF,EAAAmF,GAAA,sBAsIF,IAAMc,GAAOjG,EAAA,SAAUkG,EAAOC,EAAIC,EAAUnG,EAAS,CAC1DH,EAAOuG,GAAU,EAAE,GACnB,IAAMC,EAAgBD,GAAU,EAAE,cAE9BE,EACAD,IAAkB,YACpBC,EAAiBC,GAAO,KAAOL,CAAE,GAEnC,IAAMM,EACJH,IAAkB,UACdE,GAAOD,EAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,EACrDC,GAAO,MAAM,EAEfE,EAAKzG,EAAQ,GAEjBA,EAAQ,GAAG,QAAQH,EAAK,IAAI,EAE5BJ,GAAegH,EAAG,gBAAgB,EAClC/G,GAAkB+G,EAAG,mBAAmB,EAExCC,GAAI,MAAM,KAAK,KAAK,UAAU7G,EAAM,KAAM,CAAC,CAAC,EAAE,EAE9C,IAAMsC,EACJkE,IAAkB,UAAYG,EAAK,OAAO,QAAQN,CAAE,IAAI,EAAIK,GAAO,QAAQL,CAAE,IAAI,EAEnF1D,EAAQ,mBAAmBL,CAAO,EAClCK,EAAQ,mBAAmBL,CAAO,EAClCK,EAAQ,gBAAgBL,CAAO,EAE/B,IAAIwE,EAAe,IAAI7G,GAAOE,CAAO,EAErC2G,EAAa,QACX9G,EAAK,eACLA,EAAK,eACLA,EAAK,eACLA,EAAK,cACP,EAEA8G,EAAa,KAAK,WAAa,OAAO,WACtCpH,GAAqBM,EAAK,eAC1BL,GAAqBK,EAAK,eAE1B,IAAM+G,EAAQ5G,EAAQ,GAAG,SAAS,EAC9BqF,EAAoBrF,EAAQ,GAAG,aAAa,EAAE,EAGlDkF,GAAmB/C,EAAS,GAAIwE,EAActB,EAAmBrF,CAAO,EAKxEwC,EAAQ,gBAAgBL,CAAO,EAC/BK,EAAQ,eAAeL,CAAO,EAC9BK,EAAQ,qBAAqBL,CAAO,EACpCK,EAAQ,sBAAsBL,CAAO,EAErCuC,GAASvC,EAASnC,EAAQ,GAAG,QAAQ,EAAGA,EAAQ,GAAG,WAAYA,CAAO,EAEtE2G,EAAa,KAAK,MAAQpH,GAC1BoH,EAAa,KAAK,MAAQnH,GAE1B,IAAMqH,EAAMF,EAAa,KAKrBG,EAFYD,EAAI,MAAQA,EAAI,OAEP,EAAIhH,EAAK,eAI5BkH,EADSF,EAAI,MAAQA,EAAI,OACN,EAAIhH,EAAK,eAE9B+G,GACFzE,EACG,OAAO,MAAM,EACb,KAAKyE,CAAK,EACV,KAAK,KAAMC,EAAI,MAAQA,EAAI,QAAU,EAAI,EAAIhH,EAAK,cAAc,EAChE,KAAK,IAAKgH,EAAI,OAAShH,EAAK,cAAc,EAG/CmH,GAAiB7E,EAAS2E,EAAQC,EAAOlH,EAAK,WAAW,EAEzD,IAAMoH,EAAoBL,EAAQ,GAAK,EACvCzE,EAAQ,KACN,UACA0E,EAAI,OACFhH,EAAK,eACL,MACCA,EAAK,eAAiBoH,GACvB,IACAF,EACA,KACCD,EAASG,EACd,EAEAP,GAAI,MAAM,UAAWG,CAAG,CAC1B,EAhGoB,QAkGbK,GAAQ,CACb,wBAAyBzE,GACzB,aAAAP,GACA,QAAAjC,GACA,KAAA+F,EACF,EC5qBA,IAAMmB,GAAYC,EAACC,GACjB;AAAA,cACYA,EAAQ,YAAY;AAAA,YACtBA,EAAQ,SAAS;AAAA;AAAA,EAHX,aAOXC,GAAQH,GCCR,IAAMI,GAA6B,CACxC,OAAAC,GACA,GAAAC,GACA,SAAAC,GACA,OAAAC,GACA,KAAMC,EAAA,CAAC,CAAE,GAAAC,EAAI,KAAAC,CAAK,IAAqB,CACrCJ,GAAS,QAAQG,CAAE,EACnBJ,GAAG,QAAQK,CAAI,CACjB,EAHM,OAIR", "names": ["parser", "o", "__name", "k", "v", "l", "$V0", "$V1", "$V2", "$V3", "$V4", "$V5", "$V6", "$V7", "$V8", "$V9", "$Va", "$Vb", "$Vc", "$Vd", "$Ve", "$Vf", "$Vg", "$Vh", "$Vi", "$Vj", "$Vk", "$Vl", "$Vm", "$Vn", "$Vo", "$Vp", "$Vq", "$Vr", "$Vs", "$Vt", "$Vu", "$Vv", "$Vw", "$Vx", "$Vy", "$Vz", "$VA", "$VB", "$VC", "$VD", "$VE", "$VF", "$VG", "$VH", "$VI", "$VJ", "$VK", "$VL", "$VM", "$VN", "$VO", "$VP", "$VQ", "$VR", "$VS", "yytext", "yyleng", "y<PERSON><PERSON>o", "yy", "yystate", "$$", "_$", "$0", "kv", "str", "hash", "error", "input", "self", "stack", "tstack", "vstack", "lstack", "table", "recovering", "TERROR", "EOF", "args", "lexer", "sharedState", "yyloc", "ranges", "popStack", "n", "lex", "token", "symbol", "preErrorSymbol", "state", "action", "a", "r", "yyval", "p", "len", "newState", "expected", "errStr", "ch", "lines", "oldLines", "past", "next", "pre", "c", "match", "indexed_rule", "backup", "tempMatch", "index", "rules", "i", "condition", "yy_", "$avoiding_name_collisions", "YY_START", "YYSTATE", "<PERSON><PERSON><PERSON>", "c4Diagram_default", "parser", "c4ShapeArray", "boundaryParseStack", "currentBoundaryParse", "parentBoundaryParse", "boundaries", "rels", "title", "wrapEnabled", "c4ShapeInRow", "c4BoundaryInRow", "c4Type", "getC4Type", "__name", "setC4Type", "c4TypeParam", "sanitizeText", "getConfig", "addRel", "type", "from", "to", "label", "techn", "descr", "sprite", "tags", "link", "rel", "old", "key", "value", "autoWrap", "addPersonOrSystem", "typeC4Shape", "alias", "personOrSystem", "addContainer", "container", "addComponent", "component", "addPersonOrSystemBoundary", "boundary", "addContainerBoundary", "addDeploymentNode", "nodeType", "popBoundaryParseStack", "updateElStyle", "elementName", "bgColor", "fontColor", "borderColor", "shadowing", "shape", "legendText", "legendSprite", "element", "updateRelStyle", "textColor", "lineColor", "offsetX", "offsetY", "updateLayoutConfig", "c4ShapeInRowParam", "c4BoundaryInRowParam", "c4ShapeInRowValue", "c4BoundaryInRowValue", "getC4ShapeInRow", "getC4BoundaryInRow", "getCurrentBoundaryParse", "getParentBoundaryParse", "getC4ShapeArray", "parentBoundary", "getC4Shape", "getC4ShapeKeys", "getBoundaries", "getBoundarys", "getRels", "getTitle", "setWrap", "wrapSetting", "clear", "LINETYPE", "ARROWTYPE", "PLACEMENT", "setTitle", "txt", "c4Db_default", "setAccTitle", "getAccTitle", "getAccDescription", "setAccDescription", "import_sanitize_url", "drawRect", "__name", "elem", "rectData", "drawImage", "width", "height", "x", "y", "link", "imageElem", "sanitizedLink", "drawRels", "rels", "conf", "relsElem", "i", "rel", "textColor", "strokeColor", "offsetX", "offsetY", "url", "line", "messageConf", "_drawTextCandidateFunc", "drawBoundary", "boundary", "boundaryElem", "fillColor", "fontColor", "attrsValue", "boundaryConf", "drawC4Shape", "c4Shape", "personImg", "c4ShapeElem", "rect", "getNoteRect", "c4ShapeFontConf", "getC4ShapeFont", "textFontConf", "insertDatabaseIcon", "insertComputerIcon", "insertClockIcon", "insertArrowHead", "insertArrowEnd", "insertArrowFilledHead", "insertDynamicNumber", "insertArrowCrossHead", "marker", "cnf", "typeC4Shape", "byText", "content", "g", "textAttrs", "text", "_setTextAttrs", "byTspan", "fontSize", "fontFamily", "fontWeight", "lines", "common_default", "dy", "byFo", "s", "toText", "fromTextAttrsDict", "key", "svgDraw_default", "globalBoundaryMaxX", "globalBoundaryMaxY", "c4ShapeInRow", "c4BoundaryInRow", "parser", "c4Db_default", "conf", "Bounds", "__name", "diagObj", "setConf", "startx", "stopx", "starty", "stopy", "obj", "key", "val", "fun", "c4Shape", "_startx", "_stopx", "_starty", "_stopy", "margin", "cnf", "assignWithDepth_default", "c4ShapeFont", "typeC4Shape", "boundaryFont", "messageFont", "calcC4ShapeTextWH", "textType", "c4ShapeTextWrap", "textConf", "textLimitWidth", "wrapLabel", "common_default", "calculateTextHeight", "lines", "lineHeight", "line", "calculateTextWidth", "drawBoundary", "diagram", "boundary", "bounds", "boundaryTextWrap", "boundaryLabelConf", "svgDraw_default", "drawC4ShapeArray", "currentBounds", "c4ShapeArray", "c4Shape<PERSON>eys", "Y", "c4Shape<PERSON>ey", "c4ShapeTypeConf", "c4ShapeLabelConf", "c4ShapeTechnConf", "rectHeight", "rectWidth", "c4ShapeDescrConf", "Point", "x", "y", "getIntersectPoint", "fromNode", "endPoint", "x1", "y1", "x2", "y2", "fromCenterX", "fromCenterY", "dx", "dy", "tanDYX", "fromDYX", "returnPoint", "getIntersectPoints", "endNode", "endIntersectPoint", "startPoint", "drawRels", "rels", "getC4ShapeObj", "i", "rel", "relTextWrap", "rel<PERSON>onf", "points", "drawInsideBoundary", "parentBoundaryAlias", "parentBounds", "currentBoundaries", "currentBoundary", "currentBoundaryTextWrap", "currentBoundaryLabelConf", "currentBoundaryTypeConf", "currentBoundaryDescrConf", "_x", "_y", "currentPersonOrSystemArray", "currentPersonOrSystemKeys", "nextCurrentBoundaries", "draw", "_text", "id", "_version", "getConfig", "securityLevel", "sandboxElement", "select_default", "root", "db", "log", "screenBounds", "title", "box", "height", "width", "configureSvgSize", "extraVertForTitle", "c4Renderer_default", "getStyles", "__name", "options", "styles_default", "diagram", "c4Diagram_default", "c4Db_default", "c4Renderer_default", "styles_default", "__name", "c4", "wrap"]}