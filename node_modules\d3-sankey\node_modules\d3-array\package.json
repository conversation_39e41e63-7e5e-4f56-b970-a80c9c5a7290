{"name": "d3-array", "version": "2.12.1", "description": "Array manipulation, ordering, searching, summarizing, etc.", "keywords": ["d3", "d3-module", "histogram", "bisect", "shuffle", "statistics", "search", "sort", "array"], "homepage": "https://d3js.org/d3-array/", "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "<PERSON>", "url": "http://bost.ocks.org/mike"}, "main": "dist/d3-array.js", "unpkg": "dist/d3-array.min.js", "module": "src/index.js", "repository": {"type": "git", "url": "https://github.com/d3/d3-array.git"}, "files": ["dist/**/*.js", "src/**/*.js"], "scripts": {"pretest": "rollup -c", "test": "./test/run.sh", "prepublishOnly": "rm -rf dist && yarn test", "postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd - && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js"}, "sideEffects": false, "devDependencies": {"@rollup/plugin-node-resolve": "11", "d3-random": "2", "eslint": "7", "jsdom": "16", "rollup": "2", "rollup-plugin-terser": "7", "tape": "4", "tape-await": "0.1"}, "dependencies": {"internmap": "^1.0.0"}}