{"version": 3, "file": "semantic-token-provider.js", "sourceRoot": "", "sources": ["../../src/lsp/semantic-token-provider.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAKhF,OAAO,EAAE,qBAAqB,IAAI,yBAAyB,EAAE,sBAAsB,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AACvI,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAE7D,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAClD,OAAO,EAAE,OAAO,EAAE,MAAM,uBAAuB,CAAC;AAChD,OAAO,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AAE/H,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAI9D,MAAM,CAAC,MAAM,qBAAqB,GAA2B;IACzD,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;IAC7B,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC;IAC/B,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;IAC5B,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;IAClC,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;IAC7B,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC;IAChC,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC;IACjC,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC;IAC/B,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;IAC7B,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC;IAC9B,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,EAAE;IACjC,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,EAAE;IAClC,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE,EAAE;IAC/B,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,EAAE;IACjC,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,EAAE;IAClC,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,EAAE;IACjC,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE,EAAE;IAC/B,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE,EAAE;IAC/B,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE,EAAE;IAC/B,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,EAAE;IAC7B,CAAC,kBAAkB,CAAC,aAAa,CAAC,EAAE,EAAE;IACtC,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,EAAE;IACjC,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,EAAE;CACrC,CAAC;AAEF,MAAM,CAAC,MAAM,yBAAyB,GAA2B;IAC7D,CAAC,sBAAsB,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;IACzC,CAAC,sBAAsB,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC;IACtC,CAAC,sBAAsB,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC;IAC5C,CAAC,sBAAsB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC;IAC/C,CAAC,sBAAsB,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC;IAC3C,CAAC,sBAAsB,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC;IAC3C,CAAC,sBAAsB,CAAC,aAAa,CAAC,EAAE,CAAC,IAAI,CAAC;IAC9C,CAAC,sBAAsB,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC;IAC7C,CAAC,sBAAsB,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;IACzC,CAAC,sBAAsB,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC;CAC1C,CAAC;AAEF,MAAM,CAAC,MAAM,2BAA2B,GAA0B;IAC9D,MAAM,EAAE;QACJ,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC;QAC9C,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC;KACzD;IACD,IAAI,EAAE;QACF,KAAK,EAAE,IAAI;KACd;IACD,KAAK,EAAE,IAAI;CACd,CAAC;AAWF,MAAM,UAAU,iCAAiC,CAAC,OAAiD;IAC/F,MAAM,UAAU,GAAa,EAAE,CAAC;IAChC,MAAM,cAAc,GAAa,EAAE,CAAC;IACpC,IAAI,IAAI,GAAG,IAAI,CAAC;IAChB,IAAI,KAAK,GAAG,IAAI,CAAC;IACjB,IAAI,KAAK,GAAG,IAAI,CAAC;IACjB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,SAAS;QACb,CAAC;QACD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE;YAClD,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;YACnC,IAAI,QAAQ,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;gBACrC,MAAM,IAAI,KAAK,CAAC,iBAAiB,QAAQ,UAAU,SAAS,0CAA0C,KAAK,GAAG,CAAC,CAAC;YACpH,CAAC;iBAAM,CAAC;gBACJ,UAAU,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC;YAClC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;YAC1D,MAAM,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;YACvC,IAAI,QAAQ,IAAI,QAAQ,KAAK,aAAa,EAAE,CAAC;gBACzC,MAAM,IAAI,KAAK,CAAC,iBAAiB,QAAQ,UAAU,aAAa,6CAA6C,KAAK,GAAG,CAAC,CAAC;YAC3H,CAAC;iBAAM,CAAC;gBACJ,cAAc,CAAC,KAAK,CAAC,GAAG,aAAa,CAAC;YAC1C,CAAC;QACL,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACf,IAAI,GAAG,KAAK,CAAC;QACjB,CAAC;aAAM,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAC/D,KAAK,GAAG,KAAK,CAAC;QAClB,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YAChB,KAAK,GAAG,KAAK,CAAC;QAClB,CAAC;IACL,CAAC;IACD,OAAO;QACH,MAAM,EAAE;YACJ,UAAU;YACV,cAAc;SACjB;QACD,IAAI,EAAE,IAAI,IAAI,EAAE,KAAK,EAAE;QACvB,KAAK;KACR,CAAC;AACN,CAAC;AA2DD,MAAM,OAAO,qBAAsB,SAAQ,yBAAyB;IAApE;;QACY,YAAO,GAAoB,EAAE,CAAC;IA0C1C,CAAC;IAxCY,IAAI,CAAC,IAAY,EAAE,IAAY,EAAE,MAAc,EAAE,SAAiB,EAAE,cAAsB;QAC/F,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YACd,IAAI;YACJ,IAAI;YACJ,MAAM;YACN,SAAS;YACT,cAAc;SACjB,CAAC,CAAC;IACP,CAAC;IAEQ,KAAK;QACV,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,OAAO,KAAK,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;IAEQ,UAAU;QACf,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,OAAO,KAAK,CAAC,UAAU,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,KAAK;QACD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACjC,CAAC;IAEO,WAAW;QACf,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;YACxD,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,cAAc,CAAC,CAAC;QAC5F,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;IACtB,CAAC;IAEO,aAAa,CAAC,CAAgB,EAAE,CAAgB;QACpD,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;YACpB,OAAO,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;QAC3B,CAAC;QACD,OAAO,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;IAC3B,CAAC;CACJ;AAID;;;;;GAKG;AACH,MAAM,OAAgB,6BAA6B;IAW/C,YAAY,QAAyB;QATrC;;WAEG;QACO,mBAAc,GAAG,IAAI,GAAG,EAAiC,CAAC;QAOhE,kEAAkE;QAClE,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;YACnD,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QACH,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;;YACrD,IAAI,CAAC,UAAU,CAAC,MAAA,MAAM,CAAC,YAAY,CAAC,YAAY,0CAAE,cAAc,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;IACP,CAAC;IAED,UAAU,CAAC,kBAAqD;QAC5D,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;IACjD,CAAC;IAED,IAAI,UAAU;QACV,OAAO,qBAAqB,CAAC;IACjC,CAAC;IAED,IAAI,cAAc;QACd,OAAO,yBAAyB,CAAC;IACrC,CAAC;IAED,IAAI,qBAAqB;QACrB,OAAO;YACH,MAAM,EAAE;gBACJ,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;gBACxC,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC;aACnD;YACD,IAAI,EAAE;gBACF,KAAK,EAAE,IAAI;aACd;YACD,KAAK,EAAE,IAAI;SACd,CAAC;IACN,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,QAAyB,EAAE,OAA6B,EAAE,WAAW,GAAG,iBAAiB,CAAC,IAAI;QAClH,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAC9B,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;QAChC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACpE,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;QAClC,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,WAAW,CAAC,CAAC;QAC7E,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;IAC7C,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,QAAyB,EAAE,MAAiC,EAAE,WAAW,GAAG,iBAAiB,CAAC,IAAI;QAC3H,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC;QACjC,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;QAChC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACpE,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;QAClC,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,WAAW,CAAC,CAAC;QAC7E,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;IAC7C,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,QAAyB,EAAE,MAAiC,EAAE,WAAW,GAAG,iBAAiB,CAAC,IAAI;QAC3H,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAC9B,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;QAChC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QACpE,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QAClE,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,WAAW,CAAC,CAAC;QAC7E,OAAO,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,CAAC;IAClD,CAAC;IAES,cAAc;QACpB,MAAM,QAAQ,GAA0B,OAAO,CAAC,EAAE;YAC9C,IAAI,MAAM,IAAI,OAAO,EAAE,CAAC;gBACpB,IAAI,CAAC,cAAc,CAAC;oBAChB,KAAK,EAAE;wBACH,KAAK,EAAE;4BACH,IAAI,EAAE,OAAO,CAAC,IAAI;4BAClB,SAAS,EAAE,OAAO,CAAC,IAAI;yBAC1B;wBACD,GAAG,EAAE;4BACD,IAAI,EAAE,OAAO,CAAC,IAAI;4BAClB,SAAS,EAAE,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM;yBAC3C;qBACJ;oBACD,IAAI,EAAE,OAAO,CAAC,IAAI;oBAClB,QAAQ,EAAE,OAAO,CAAC,QAAQ;iBAC7B,CAAC,CAAC;YACP,CAAC;iBAAM,IAAI,OAAO,IAAI,OAAO,EAAE,CAAC;gBAC5B,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACjC,CAAC;iBAAM,IAAI,SAAS,IAAI,OAAO,EAAE,CAAC;gBAC9B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC;iBAAM,IAAI,UAAU,IAAI,OAAO,EAAE,CAAC;gBAC/B,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACpC,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,aAAa,CAAC;oBACf,IAAI,EAAE,OAAO,CAAC,GAAG;oBACjB,IAAI,EAAE,OAAO,CAAC,IAAI;oBAClB,QAAQ,EAAE,OAAO,CAAC,QAAQ;iBAC7B,CAAC,CAAC;YACP,CAAC;QACL,CAAC,CAAC;QACF,OAAO,QAAQ,CAAC;IACpB,CAAC;IAES,wBAAwB,CAAC,QAAyB;QACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;QAClE,IAAI,QAAQ,EAAE,CAAC;YACX,OAAO,QAAQ,CAAC;QACpB,CAAC;QACD,MAAM,OAAO,GAAG,IAAI,qBAAqB,EAAE,CAAC;QAC5C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC;QAC1D,OAAO,OAAO,CAAC;IACnB,CAAC;IAES,KAAK,CAAC,mBAAmB,CAAC,QAAyB,EAAE,QAA+B,EAAE,WAA8B;QAC1H,MAAM,IAAI,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC;QACxC,MAAM,YAAY,GAAG,SAAS,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC9E,IAAI,MAA+B,CAAC;QACpC,GAAG,CAAC;YACA,MAAM,GAAG,YAAY,CAAC,IAAI,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gBACf,MAAM,iBAAiB,CAAC,WAAW,CAAC,CAAC;gBACrC,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC;gBAC1B,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,OAAO,EAAE,CAAC;oBACpD,YAAY,CAAC,KAAK,EAAE,CAAC;gBACzB,CAAC;YACL,CAAC;QACL,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE;IAC3B,CAAC;IAOS,cAAc,CAAC,OAAkC;;QACvD,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;QAChC,IAAI,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACnH,OAAO;QACX,CAAC;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACtC,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC1B,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;gBAChC,SAAS,GAAG,CAAC,SAAS,CAAC,CAAC;YAC5B,CAAC;YACD,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBAC/B,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBAClD,aAAa,IAAI,WAAW,CAAC;YACjC,CAAC;QACL,CAAC;QACD,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC;QACnC,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;QAC/B,IAAI,SAAS,KAAK,OAAO,EAAE,CAAC;YACxB,iCAAiC;YACjC,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC;YACnC,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC;YAC1C,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;QACpF,CAAC;aAAM,IAAI,MAAA,IAAI,CAAC,kBAAkB,0CAAE,qBAAqB,EAAE,CAAC;YACxD,gCAAgC;YAChC,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC;YACxC,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACxE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,GAAG,WAAW,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;QAC1G,CAAC;aAAM,CAAC;YACJ,gEAAgE;YAChE,gDAAgD;YAChD,MAAM,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC;YACnC,IAAI,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC;gBAC5D,IAAI,EAAE,SAAS,GAAG,CAAC;gBACnB,SAAS,EAAE,CAAC;aACf,CAAC,CAAC;YACH,mBAAmB;YACnB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAC1B,cAAc,CAAC,IAAI,EACnB,cAAc,CAAC,SAAS,EACxB,cAAc,GAAG,cAAc,CAAC,SAAS,GAAG,CAAC,EAC7C,OAAO,EACP,aAAa,CAChB,CAAC;YACF,4CAA4C;YAC5C,KAAK,IAAI,CAAC,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3C,MAAM,iBAAiB,GAAG,cAAc,CAAC;gBACzC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC;oBACxD,IAAI,EAAE,CAAC,GAAG,CAAC;oBACX,SAAS,EAAE,CAAC;iBACf,CAAC,CAAC;gBACH,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAC1B,CAAC,EACD,CAAC,EACD,cAAc,GAAG,iBAAiB,GAAG,CAAC,EACtC,OAAO,EAAE,aAAa,CACzB,CAAC;YACN,CAAC;YACD,kBAAkB;YAClB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAC1B,OAAO,EACP,CAAC,EACD,KAAK,CAAC,GAAG,CAAC,SAAS,EACnB,OAAO,EACP,aAAa,CAChB,CAAC;QACN,CAAC;IACL,CAAC;IAES,iBAAiB,CAAoB,OAAwC;QACnF,MAAM,KAAK,GAAc,EAAE,CAAC;QAC5B,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;YACpC,MAAM,IAAI,GAAG,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YACzF,IAAI,IAAI,EAAE,CAAC;gBACP,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrB,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,KAAK,CAAC,IAAI,CAAC,GAAG,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;QACjF,CAAC;QACD,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;QACnC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACvB,IAAI,CAAC,aAAa,CAAC;gBACf,IAAI;gBACJ,IAAI;gBACJ,QAAQ;aACX,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAES,gBAAgB,CAAC,OAAoC;QAC3D,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;QACzD,MAAM,KAAK,GAAc,EAAE,CAAC;QAC5B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC5B,MAAM,WAAW,GAAG,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;YACtE,IAAI,WAAW,EAAE,CAAC;gBACd,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5B,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,KAAK,CAAC,IAAI,CAAC,GAAG,mBAAmB,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;QAC/D,CAAC;QACD,KAAK,MAAM,WAAW,IAAI,KAAK,EAAE,CAAC;YAC9B,IAAI,CAAC,aAAa,CAAC;gBACf,IAAI,EAAE,WAAW;gBACjB,IAAI;gBACJ,QAAQ;aACX,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAES,aAAa,CAAC,OAAiC;QACrD,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;QACzC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,cAAc,CAAC;YAChB,KAAK;YACL,IAAI;YACJ,QAAQ;SACX,CAAC,CAAC;IACP,CAAC;CAEJ;AAED,MAAM,KAAW,qBAAqB,CAsCrC;AAtCD,WAAiB,qBAAqB;IAQlC,SAAgB,MAAM,CAA8B,MAAsB,EAAE,UAAkC,EAAE,QAA4B;QACxI,MAAM,OAAO,GAAG,IAAI,GAAG,EAA8B,CAAC;QACtD,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,IAA0B,CAAC,CAAC,CAAC;QACtG,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,OAAO,eAAe,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;YAC3C,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;gBACb,SAAS,GAAG,CAAC,CAAC;YAClB,CAAC;YACD,SAAS,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,MAAM,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,MAAM,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;YACnE,OAAO;gBACH,MAAM;gBACN,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAE;gBAC7B,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;gBACpB,IAAI,EAAE,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,GAAG,MAAM,EAAE,EAAE,CAAC;aAC5F,CAAC;QAC9B,CAAC,CAAC,CAAC;IACP,CAAC;IApBe,4BAAM,SAoBrB,CAAA;IAED,SAAS,eAAe,CAAI,GAAQ,EAAE,SAAiB;QACnD,MAAM,GAAG,GAAG,EAAE,CAAC;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;YAC7C,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;YAC1C,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpB,CAAC;QACD,OAAO,GAAG,CAAC;IACf,CAAC;AACL,CAAC,EAtCgB,qBAAqB,KAArB,qBAAqB,QAsCrC"}