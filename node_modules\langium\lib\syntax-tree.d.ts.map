{"version": 3, "file": "syntax-tree.d.ts", "sourceRoot": "", "sources": ["../src/syntax-tree.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAEhF,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;AAC5C,OAAO,KAAK,EAAE,GAAG,EAAE,MAAM,sBAAsB,CAAC;AAChD,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,8BAA8B,CAAC;AACpE,OAAO,KAAK,EAAE,eAAe,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAEjF;;GAEG;AACH,MAAM,WAAW,OAAO;IACpB,gGAAgG;IAChG,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC;IACvB,sFAAsF;IACtF,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC;IAC9B,oHAAoH;IACpH,QAAQ,CAAC,kBAAkB,CAAC,EAAE,MAAM,CAAC;IACrC,gFAAgF;IAChF,QAAQ,CAAC,eAAe,CAAC,EAAE,MAAM,CAAC;IAClC,6FAA6F;IAC7F,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC;IAC5B,kGAAkG;IAClG,QAAQ,CAAC,SAAS,CAAC,EAAE,eAAe,CAAC;CACxC;AAED,wBAAgB,SAAS,CAAC,GAAG,EAAE,OAAO,GAAG,GAAG,IAAI,OAAO,CAEtD;AAED,MAAM,WAAW,cAAe,SAAQ,OAAO;IAC3C,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAA;CACzB;AAED,KAAK,sBAAsB,CAAC,CAAC,SAAS,OAAO,IAAI,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,OAAO,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC;AAEhG;;GAEG;AACH,MAAM,MAAM,UAAU,CAAC,CAAC,SAAS,OAAO,IAAI,sBAAsB,CAAC,CAAC,CAAC,SAAS,KAAK,GAAG,MAAM,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAA;AAExH;;GAEG;AACH,MAAM,WAAW,SAAS,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO;IAClD;;;;OAIG;IACH,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAEjB,+FAA+F;IAC/F,QAAQ,CAAC,KAAK,CAAC,EAAE,YAAY,CAAC;IAC9B,uDAAuD;IACvD,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC;IAC5B,+DAA+D;IAC/D,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC;IAC1B,8DAA8D;IAC9D,QAAQ,CAAC,gBAAgB,CAAC,EAAE,kBAAkB,CAAC;CAClD;AAED,wBAAgB,WAAW,CAAC,GAAG,EAAE,OAAO,GAAG,GAAG,IAAI,SAAS,CAE1D;AAED,MAAM,MAAM,iBAAiB,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG;IACxE,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;CACnB,CAAA;AAED;;GAEG;AACH,MAAM,WAAW,kBAAkB;IAC/B,mGAAmG;IACnG,IAAI,CAAC,EAAE,OAAO,CAAC;IACf;;OAEG;IACH,WAAW,CAAC,EAAE,eAAe,CAAC;IAC9B;;OAEG;IACH,gBAAgB,CAAC,EAAE,eAAe,CAAC;IACnC,6CAA6C;IAC7C,IAAI,EAAE,MAAM,CAAC;IACb,sFAAsF;IACtF,IAAI,EAAE,MAAM,CAAC;IACb,kDAAkD;IAClD,WAAW,EAAE,GAAG,CAAC;IACjB,0CAA0C;IAC1C,IAAI,EAAE,MAAM,CAAC;CAChB;AAED,wBAAgB,oBAAoB,CAAC,GAAG,EAAE,OAAO,GAAG,GAAG,IAAI,kBAAkB,CAK5E;AAED;;;GAGG;AACH,MAAM,WAAW,aAAa;IAC1B,SAAS,EAAE,SAAS,CAAA;IACpB,SAAS,EAAE,OAAO,CAAA;IAClB,QAAQ,EAAE,MAAM,CAAA;IAChB,KAAK,CAAC,EAAE,MAAM,CAAA;CACjB;AAED;;GAEG;AACH,MAAM,WAAW,YAAa,SAAQ,aAAa;IAC/C,OAAO,EAAE,MAAM,CAAC;IAChB,iBAAiB,CAAC,EAAE,kBAAkB,CAAC;CAC1C;AAED,wBAAgB,cAAc,CAAC,GAAG,EAAE,OAAO,GAAG,GAAG,IAAI,YAAY,CAKhE;AAED;;;GAGG;AACH,MAAM,WAAW,aAAa;IAC1B,WAAW,IAAI,MAAM,EAAE,CAAA;IACvB,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE,CAAA;IACtC,gBAAgB,CAAC,OAAO,EAAE,aAAa,GAAG,MAAM,CAAA;IAChD,eAAe,CAAC,IAAI,EAAE,MAAM,GAAG,YAAY,CAAA;IAC3C,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,GAAG,OAAO,CAAA;IAChD,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,OAAO,CAAA;CACzD;AAED;;;GAGG;AACH,8BAAsB,qBAAsB,YAAW,aAAa;IAEhE,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,GAAG,SAAS,CAAC,CAAC,CAAM;IAC7E,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,SAAS,CAAC,CAAM;IAEjE,QAAQ,CAAC,WAAW,IAAI,MAAM,EAAE;IAChC,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,aAAa,GAAG,MAAM;IACzD,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,GAAG,YAAY;IACpD,SAAS,CAAC,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,OAAO;IAEhF,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,GAAG,OAAO;IAIhD,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,OAAO;IAkBtD,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE;CAgBzC;AAED;;GAEG;AACH,MAAM,WAAW,YAAY;IACzB,kFAAkF;IAClF,IAAI,EAAE,MAAM,CAAA;IACZ,sGAAsG;IACtG,UAAU,EAAE,YAAY,EAAE,CAAA;CAC7B;AAED;;;;;GAKG;AACH,MAAM,WAAW,YAAY;IACzB,IAAI,EAAE,MAAM,CAAA;IACZ,YAAY,CAAC,EAAE,YAAY,CAAA;CAC9B;AAED;;GAEG;AACH,MAAM,MAAM,YAAY,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,GAAG,YAAY,EAAE,CAAC;AAEtE;;GAEG;AACH,MAAM,WAAW,OAAQ,SAAQ,eAAe;IAC5C,oCAAoC;IACpC,QAAQ,CAAC,SAAS,CAAC,EAAE,gBAAgB,CAAC;IACtC,2CAA2C;IAC3C,QAAQ,CAAC,MAAM,CAAC,EAAE,gBAAgB,CAAC;IACnC,sBAAsB;IACtB,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACtB,wBAAwB;IACxB,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC;IAC3B,0DAA0D;IAC1D,QAAQ,CAAC,aAAa,CAAC,EAAE,eAAe,CAAC;IACzC,+CAA+C;IAC/C,QAAQ,CAAC,OAAO,CAAC,EAAE,eAAe,CAAC;IACnC,8CAA8C;IAC9C,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC;IAC1B,yCAAyC;IACzC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC;IAC1B,2FAA2F;IAC3F,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC;CAC5B;AAED;;GAEG;AACH,MAAM,WAAW,gBAAiB,SAAQ,OAAO;IAC7C,QAAQ,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC;IAC5B,yCAAyC;IACzC,QAAQ,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;CAChC;AAED,wBAAgB,kBAAkB,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI,IAAI,gBAAgB,CAE1E;AAED;;GAEG;AACH,MAAM,WAAW,WAAY,SAAQ,OAAO;IACxC,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC;CACjC;AAED,wBAAgB,aAAa,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI,IAAI,WAAW,CAEhE;AAED,MAAM,WAAW,WAAY,SAAQ,gBAAgB;IACjD,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAA;CAC5B;AAED,wBAAgB,aAAa,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI,IAAI,WAAW,CAEhE;AAED;;GAEG;AACH,KAAK,sBAAsB,CAAC,CAAC,EAAE,CAAC,IAAI;KAAG,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,KAAK;CAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AAE5F;;;GAGG;AACH,MAAM,MAAM,4BAA4B,CAAC,CAAC,SAAS,OAAO,IAAI,CAC1D,sBAAsB,CAAC,CAAC,EAAE,SAAS,GAAC,SAAS,CAAC,GAC5C,sBAAsB,CAAC,CAAC,EAAE,KAAK,CAAC,SAAS,GAAC,SAAS,CAAC,GAAC,SAAS,CAAC,CAEpE,GAAG,EAAE,CAAC;AAEP;;GAEG;AACH,MAAM,MAAM,WAAW,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC;AAEtD;;;GAGG;AACH,MAAM,MAAM,+BAA+B,CAAC,CAAC,SAAS,WAAW,CAAC,CAAC,CAAC,IAAI;KACnE,CAAC,IAAI,MAAM,CAAC,GAAG,4BAA4B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;CAClF,CAAC,MAAM,CAAC,CAAC,CAAC;AAEX,MAAM,MAAM,OAAO,CAAC,CAAC,IAAI;IACrB,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACjC,CAAC"}