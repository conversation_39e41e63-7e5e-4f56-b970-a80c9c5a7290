{"name": "package-manager-detector", "type": "module", "version": "0.2.11", "description": "Package manager detector", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/antfu-collective/package-manager-detector#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu-collective/package-manager-detector.git"}, "bugs": {"url": "https://github.com/antfu-collective/package-manager-detector/issues"}, "sideEffects": false, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./commands": {"import": "./dist/commands.mjs", "require": "./dist/commands.cjs"}, "./detect": {"import": "./dist/detect.mjs", "require": "./dist/detect.cjs"}, "./constants": {"import": "./dist/constants.mjs", "require": "./dist/constants.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "typesVersions": {"*": {"commands": ["./dist/commands.d.ts"], "detect": ["./dist/detect.d.ts"], "constants": ["./dist/constants.d.ts"]}}, "files": ["dist"], "dependencies": {"quansync": "^0.2.7"}, "devDependencies": {"@antfu/eslint-config": "^4.3.0", "@types/fs-extra": "^11.0.4", "@types/node": "^22.13.8", "bumpp": "^10.0.3", "eslint": "^9.21.0", "fs-extra": "^11.3.0", "typescript": "^5.8.2", "unbuild": "^3.5.0", "unplugin-quansync": "^0.3.3", "vitest": "^3.0.7"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub", "release": "bumpp", "lint": "eslint .", "test": "vitest"}}