{"version": 3, "file": "errors_public.js", "sourceRoot": "", "sources": ["../../../src/parse/errors_public.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AACrE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AAC/C,OAAO,EAEL,oBAAoB,EACpB,WAAW,EACX,IAAI,EACJ,QAAQ,GACT,MAAM,kBAAkB,CAAC;AAW1B,MAAM,CAAC,MAAM,0BAA0B,GAAgC;IACrE,yBAAyB,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE;QAChE,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAC;QACzC,MAAM,WAAW,GAAG,QAAQ;YAC1B,CAAC,CAAC,OAAO,UAAU,CAAC,QAAQ,CAAC,MAAM;YACnC,CAAC,CAAC,qBAAqB,QAAQ,CAAC,IAAI,MAAM,CAAC;QAE7C,MAAM,GAAG,GAAG,aAAa,WAAW,mBAAmB,MAAM,CAAC,KAAK,OAAO,CAAC;QAE3E,OAAO,GAAG,CAAC;IACb,CAAC;IAED,6BAA6B,CAAC,EAAE,cAAc,EAAE,QAAQ,EAAE;QACxD,OAAO,4CAA4C,GAAG,cAAc,CAAC,KAAK,CAAC;IAC7E,CAAC;IAED,uBAAuB,CAAC,EACtB,mBAAmB,EACnB,MAAM,EACN,QAAQ,EACR,qBAAqB,EACrB,QAAQ,GACT;QACC,MAAM,SAAS,GAAG,aAAa,CAAC;QAChC,6EAA6E;QAC7E,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAE,CAAC,KAAK,CAAC;QACxC,MAAM,SAAS,GAAG,gBAAgB,GAAG,UAAU,GAAG,GAAG,CAAC;QAEtD,IAAI,qBAAqB,EAAE;YACzB,OAAO,SAAS,GAAG,qBAAqB,GAAG,SAAS,CAAC;SACtD;aAAM;YACL,MAAM,iBAAiB,GAAG,MAAM,CAC9B,mBAAmB,EACnB,CAAC,MAAM,EAAE,YAAY,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,EACrD,EAAmB,CACpB,CAAC;YACF,MAAM,uBAAuB,GAAG,GAAG,CACjC,iBAAiB,EACjB,CAAC,QAAQ,EAAE,EAAE,CACX,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC,aAAa,EAAE,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAClE,IAAI,CACL,GAAG,CACP,CAAC;YACF,MAAM,sBAAsB,GAAG,GAAG,CAChC,uBAAuB,EACvB,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,OAAO,EAAE,CAC7C,CAAC;YACF,MAAM,qBAAqB,GAAG,2CAA2C,sBAAsB,CAAC,IAAI,CAClG,IAAI,CACL,EAAE,CAAC;YAEJ,OAAO,SAAS,GAAG,qBAAqB,GAAG,SAAS,CAAC;SACtD;IACH,CAAC;IAED,qBAAqB,CAAC,EACpB,sBAAsB,EACtB,MAAM,EACN,qBAAqB,EACrB,QAAQ,GACT;QACC,MAAM,SAAS,GAAG,aAAa,CAAC;QAChC,6EAA6E;QAC7E,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAE,CAAC,KAAK,CAAC;QACxC,MAAM,SAAS,GAAG,gBAAgB,GAAG,UAAU,GAAG,GAAG,CAAC;QAEtD,IAAI,qBAAqB,EAAE;YACzB,OAAO,SAAS,GAAG,qBAAqB,GAAG,SAAS,CAAC;SACtD;aAAM;YACL,MAAM,uBAAuB,GAAG,GAAG,CACjC,sBAAsB,EACtB,CAAC,QAAQ,EAAE,EAAE,CACX,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC,aAAa,EAAE,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAClE,GAAG,CACJ,GAAG,CACP,CAAC;YACF,MAAM,qBAAqB,GACzB,gGAAgG;gBAChG,IAAI,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;YAE5C,OAAO,SAAS,GAAG,qBAAqB,GAAG,SAAS,CAAC;SACtD;IACH,CAAC;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC;AAE1C,MAAM,CAAC,MAAM,mCAAmC,GAC9C;IACE,sBAAsB,CACpB,YAAkB,EAClB,aAA0B;QAE1B,MAAM,GAAG,GACP,+DAA+D;YAC/D,aAAa,CAAC,eAAe;YAC7B,MAAM;YACN,2BAA2B;YAC3B,YAAY,CAAC,IAAI;YACjB,IAAI,CAAC;QACP,OAAO,GAAG,CAAC;IACb,CAAC;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,oCAAoC,GAC/C;IACE,wBAAwB,CACtB,YAAkB,EAClB,cAA2C;QAE3C,SAAS,0BAA0B,CACjC,IAA+B;YAE/B,IAAI,IAAI,YAAY,QAAQ,EAAE;gBAC5B,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;aAC/B;iBAAM,IAAI,IAAI,YAAY,WAAW,EAAE;gBACtC,OAAO,IAAI,CAAC,eAAe,CAAC;aAC7B;iBAAM;gBACL,OAAO,EAAE,CAAC;aACX;QACH,CAAC;QAED,MAAM,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC;QACvC,MAAM,aAAa,GAAG,KAAK,CAAC,cAAc,CAAE,CAAC;QAC7C,MAAM,KAAK,GAAG,aAAa,CAAC,GAAG,CAAC;QAChC,MAAM,OAAO,GAAG,oBAAoB,CAAC,aAAa,CAAC,CAAC;QACpD,MAAM,aAAa,GAAG,0BAA0B,CAAC,aAAa,CAAC,CAAC;QAEhE,MAAM,gBAAgB,GAAG,KAAK,GAAG,CAAC,CAAC;QACnC,IAAI,GAAG,GAAG,KAAK,OAAO,GAAG,gBAAgB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,MACpD,aAAa,CAAC,CAAC,CAAC,oBAAoB,aAAa,IAAI,CAAC,CAAC,CAAC,EAC1D;4CAEc,cAAc,CAAC,MACjB,oCAAoC,YAAY;;mBAE/C,CAAC;QAEd,yHAAyH;QACzH,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QAClC,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAElC,OAAO,GAAG,CAAC;IACb,CAAC;IAED,2BAA2B,CAAC,IAAU;QACpC,MAAM,MAAM,GACV,wCAAwC;YACxC,2EAA2E,IAAI,CAAC,IAAI,MAAM;YAC1F,6EAA6E;YAC7E,yGAAyG;YACzG,wDAAwD,CAAC;QAE3D,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,oCAAoC,CAAC,OAKpC;QACC,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,OAAO,EAAE,EAAE,CAClD,UAAU,CAAC,OAAO,CAAC,CACpB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACb,MAAM,UAAU,GACd,OAAO,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC;QAC/D,MAAM,MAAM,GACV,4BAA4B,OAAO,CAAC,gBAAgB,CAAC,IAAI,CACvD,IAAI,CACL,oCAAoC;YACrC,SAAS,UAAU,aAAa,OAAO,CAAC,YAAY,CAAC,IAAI,WAAW;YACpE,IAAI,OAAO,6DAA6D;YACxE,qFAAqF;YACrF,sBAAsB,CAAC;QAEzB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,8BAA8B,CAAC,OAK9B;QACC,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,OAAO,EAAE,EAAE,CAClD,UAAU,CAAC,OAAO,CAAC,CACpB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACb,MAAM,UAAU,GACd,OAAO,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC;QAC/D,IAAI,WAAW,GACb,qCAAqC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAChE,IAAI,CACL,WAAW,UAAU,GAAG;YACzB,YAAY,OAAO,CAAC,YAAY,CAAC,IAAI,WAAW;YAChD,IAAI,OAAO,6DAA6D,CAAC;QAE3E,WAAW;YACT,WAAW;gBACX,8FAA8F;gBAC9F,sBAAsB,CAAC;QACzB,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,yBAAyB,CAAC,OAGzB;QACC,IAAI,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACvD,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,EAAE;YAChC,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC;SACnC;QAED,MAAM,MAAM,GACV,mBAAmB,OAAO,kBAAkB,OAAO,CAAC,YAAY,CAAC,IAAI,mCAAmC;YACxG,sCAAsC,CAAC;QAEzC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,4DAA4D;IAC5D,yDAAyD;IACzD,mBAAmB,CAAC,OAGnB;QACC,0BAA0B;QAC1B,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,0BAA0B,CAAC,OAI1B;QACC,MAAM,MAAM,GACV,iCAAiC,OAAO,CAAC,cAAc,GAAG,CAAC,GAAG;YAC9D,UAAU,OAAO,CAAC,WAAW,CAAC,GAAG,aAAa,OAAO,CAAC,YAAY,CAAC,IAAI,WAAW;YAClF,wDAAwD,CAAC;QAE3D,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,6BAA6B,CAAC,OAG7B;QACC,MAAM,MAAM,GACV,0DAA0D;YAC1D,MAAM,OAAO,CAAC,WAAW,CAAC,GAAG,aAC3B,OAAO,CAAC,YAAY,CAAC,IACvB,iBACE,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,GAAG,CAC1C,gBAAgB,CAAC;QAEnB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,uBAAuB,CAAC,OAGvB;QACC,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC;QAC3C,MAAM,SAAS,GAAG,GAAG,CACnB,OAAO,CAAC,iBAAiB,EACzB,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAC5B,CAAC;QACF,MAAM,iBAAiB,GAAG,GAAG,QAAQ,QAAQ,SAAS;aACnD,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC;aAClB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;QACnB,MAAM,MAAM,GACV,oCAAoC;YACpC,UAAU,QAAQ,yDAAyD;YAC3E,0EAA0E,iBAAiB,IAAI;YAC/F,oEAAoE;YACpE,8DAA8D,CAAC;QAEjE,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,4DAA4D;IAC5D,yDAAyD;IACzD,yBAAyB,CAAC,OAGzB;QACC,0BAA0B;QAC1B,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,2BAA2B,CAAC,OAG3B;QACC,IAAI,QAAQ,CAAC;QACb,IAAI,OAAO,CAAC,YAAY,YAAY,IAAI,EAAE;YACxC,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC;SACtC;aAAM;YACL,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC;SACjC;QAED,MAAM,MAAM,GAAG,iCAAiC,QAAQ,2CAA2C,OAAO,CAAC,WAAW,IAAI,CAAC;QAE3H,OAAO,MAAM,CAAC;IAChB,CAAC;CACF,CAAC"}