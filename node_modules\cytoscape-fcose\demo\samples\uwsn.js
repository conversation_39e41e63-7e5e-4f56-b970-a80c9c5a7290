uwsn = {
	"elements": {
		"nodes": [
			{
				"data": {
					"id": "nwtN_91cff953-5f55-4626-96fa-e2f675e13e54",
					"bbox": {
						"x": 155.38677910343796,
						"y": 230.06625595029607,
						"w": 140,
						"h": 75
					},
					"class": "nucleic acid feature",
					"label": "Surface\nBuoy 1",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#f4a582",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 155.38677910343796,
					"y": 230.06625595029607
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_b20cc3f4-dc9c-4031-ac79-df665d4c53cd",
					"bbox": {
						"x": 584.0229153054016,
						"y": 223.74407633970037,
						"w": 140,
						"h": 75
					},
					"class": "nucleic acid feature",
					"label": "Surface\nBuoy 2",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#f4a582",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 584.0229153054016,
					"y": 223.74407633970037
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_5d6f7198-03e4-48e5-9004-919221f87e66",
					"bbox": {
						"x": 964.8261492755391,
						"y": 225.00851226181948,
						"w": 140,
						"h": 75
					},
					"class": "nucleic acid feature",
					"label": "Surface\nBuoy 3",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#f4a582",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 964.8261492755391,
					"y": 225.00851226181948
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_7e09bf9e-a4da-4618-baba-60a2b3a0b134",
					"bbox": {
						"x": 149.1895292305374,
						"y": 445.5622580973788,
						"w": 140,
						"h": 75
					},
					"class": "unspecified entity",
					"label": "UW Sink 1",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#f7f7f7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 149.1895292305374,
					"y": 445.5622580973788
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "661498c7-ace9-bd34-0289-7c9931d3e1ba",
					"bbox": {
						"x": 586.7621909195482,
						"y": 383.25228042246914,
						"w": 140,
						"h": 75
					},
					"class": "unspecified entity",
					"label": "UW Sink 2",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#f7f7f7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 586.7621909195482,
					"y": 383.25228042246914
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "aeee80bf-8280-f4ec-c1f3-26f3a7a0651c",
					"bbox": {
						"x": 966.963391645274,
						"y": 484.473671607378,
						"w": 140,
						"h": 75
					},
					"class": "unspecified entity",
					"label": "UW Sink 3",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#f7f7f7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 966.963391645274,
					"y": 484.473671607378
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_9ad406e1-da86-489d-84aa-627c8f5898e6",
					"bbox": {
						"x": -18.820589676794867,
						"y": 580.4283573759083,
						"w": 172.45719865368562,
						"h": 192.7296338070105
					},
					"originalW": 200.45719865368562,
					"originalH": 220.7296338070105,
					"class": "submap",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 2.25,
					"border-color": "#555555",
					"background-color": "#f7f7f7",
					"background-opacity": 0.5,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 14,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": -18.82058967679486,
					"y": 580.4283573759083
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_cbc5909d-c0a3-4be5-9c36-988ca48a9d30",
					"bbox": {
						"x": -65.92418900363768,
						"y": 613.8182505696195,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s3",
					"statesandinfos": [],
					"parent": "nwtN_9ad406e1-da86-489d-84aa-627c8f5898e6",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": -65.92418900363768,
					"y": 613.8182505696195
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_4a12169f-f588-4c76-bc39-2ae07f77d7c4",
					"bbox": {
						"x": -30.1493034389469,
						"y": 523.188540472403,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s2",
					"statesandinfos": [],
					"parent": "nwtN_9ad406e1-da86-489d-84aa-627c8f5898e6",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": -30.1493034389469,
					"y": 523.188540472403
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_be627c29-b885-460c-bf92-8f9bb7b8c8c5",
					"bbox": {
						"x": -8.684372100132464,
						"y": 578.043365004929,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s1",
					"statesandinfos": [],
					"parent": "nwtN_9ad406e1-da86-489d-84aa-627c8f5898e6",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": -8.684372100132464,
					"y": 578.043365004929
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_48d663f4-edd0-4706-a5ca-5a87f77edf8e",
					"bbox": {
						"x": 28.28300965004796,
						"y": 637.6681742794135,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s4",
					"statesandinfos": [],
					"parent": "nwtN_9ad406e1-da86-489d-84aa-627c8f5898e6",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 28.28300965004796,
					"y": 637.6681742794135
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_a892743e-a23c-48b7-8d64-c6e77ca26bbe",
					"bbox": {
						"x": 211.78152237753034,
						"y": 586.7225735218967,
						"w": 186.76715287956208,
						"h": 162.91722916976835
					},
					"originalW": 214.76715287956208,
					"originalH": 190.91722916976835,
					"class": "submap",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 2.25,
					"border-color": "#555555",
					"background-color": "#f7f7f7",
					"background-opacity": 0.5,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 14,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 211.78152237753034,
					"y": 586.7225735218967
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_2ecfaa1e-8dd3-4008-ac3d-6d4207f518b4",
					"bbox": {
						"x": 157.5229459377493,
						"y": 544.3889589370125,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s7",
					"statesandinfos": [],
					"parent": "nwtN_a892743e-a23c-48b7-8d64-c6e77ca26bbe",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 157.5229459377493,
					"y": 544.3889589370125
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_64abed47-d1b1-474c-b944-b9ad6354c086",
					"bbox": {
						"x": 209.99277809929575,
						"y": 568.2388826468066,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s5",
					"statesandinfos": [],
					"parent": "nwtN_a892743e-a23c-48b7-8d64-c6e77ca26bbe",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 209.99277809929575,
					"y": 568.2388826468066
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_1c41d32b-42f9-4e9f-a609-91c2eebbf28a",
					"bbox": {
						"x": 266.0400988173114,
						"y": 550.3514398644611,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s6",
					"statesandinfos": [],
					"parent": "nwtN_a892743e-a23c-48b7-8d64-c6e77ca26bbe",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 266.0400988173114,
					"y": 550.3514398644611
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_8af34100-fdea-4636-a978-fe75c7a38fb1",
					"bbox": {
						"x": 198.0678162443988,
						"y": 629.0561881067808,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s8",
					"statesandinfos": [],
					"parent": "nwtN_a892743e-a23c-48b7-8d64-c6e77ca26bbe",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 198.0678162443988,
					"y": 629.0561881067808
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_7a108aac-2ff5-4012-abc4-1f8418ef91ae",
					"bbox": {
						"x": 463.24537734506043,
						"y": 632.6006814534348,
						"w": 178.41967958113423,
						"h": 165.30222154074738
					},
					"originalW": 206.41967958113423,
					"originalH": 193.30222154074738,
					"class": "submap",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 2.25,
					"border-color": "#555555",
					"background-color": "#f7f7f7",
					"background-opacity": 0.5,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 14,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 463.24537734506043,
					"y": 632.6006814534348
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_0185dada-235a-4b51-bce7-e8bd3b8ee661",
					"bbox": {
						"x": 458.47539260310174,
						"y": 632.00443336069,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s9",
					"statesandinfos": [],
					"parent": "nwtN_7a108aac-2ff5-4012-abc4-1f8418ef91ae",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 458.47539260310174,
					"y": 632.00443336069
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_78116f70-d871-4b8e-83cf-7b3a684067a7",
					"bbox": {
						"x": 413.1605375544933,
						"y": 589.0745706830611,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s11",
					"statesandinfos": [],
					"parent": "nwtN_7a108aac-2ff5-4012-abc4-1f8418ef91ae",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 413.1605375544933,
					"y": 589.0745706830611
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_348778d0-85d5-442f-b73a-9dd9f5d9f2b3",
					"bbox": {
						"x": 415.5455299254728,
						"y": 676.1267922238085,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s12",
					"statesandinfos": [],
					"parent": "nwtN_7a108aac-2ff5-4012-abc4-1f8418ef91ae",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 415.5455299254728,
					"y": 676.1267922238085
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_5c29f820-d40e-4c85-b7b6-c7b8a378b49d",
					"bbox": {
						"x": 513.3302171356275,
						"y": 641.5444028446076,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s10",
					"statesandinfos": [],
					"parent": "nwtN_7a108aac-2ff5-4012-abc4-1f8418ef91ae",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 513.3302171356275,
					"y": 641.5444028446076
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_13b19118-54eb-4400-a949-ca664386ec06",
					"bbox": {
						"x": 680.3516228408145,
						"y": 630.668057701942,
						"w": 185.57465669407202,
						"h": 140.25980164546388
					},
					"originalW": 213.57465669407202,
					"originalH": 168.25980164546388,
					"class": "submap",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 2.25,
					"border-color": "#555555",
					"background-color": "#f7f7f7",
					"background-opacity": 0.5,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 14,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 680.3516228408145,
					"y": 630.668057701942
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_c5547527-64c5-4e4d-acb8-e3f7e6b0a9a5",
					"bbox": {
						"x": 734.0139511878505,
						"y": 661.6729585246738,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s15",
					"statesandinfos": [],
					"parent": "nwtN_13b19118-54eb-4400-a949-ca664386ec06",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 734.0139511878505,
					"y": 661.6729585246738
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_6bf42b3c-579e-4187-8299-cb314373c278",
					"bbox": {
						"x": 626.6892944937784,
						"y": 627.0905691454727,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s16",
					"statesandinfos": [],
					"parent": "nwtN_13b19118-54eb-4400-a949-ca664386ec06",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 626.6892944937784,
					"y": 627.0905691454727
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_4d424656-f0bb-4154-9a51-1d15429c8f71",
					"bbox": {
						"x": 726.8589740749126,
						"y": 599.66315687921,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s14",
					"statesandinfos": [],
					"parent": "nwtN_13b19118-54eb-4400-a949-ca664386ec06",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 726.8589740749126,
					"y": 599.66315687921
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtN_273f6927-f05a-425b-aa2b-92de2be06bd9",
					"bbox": {
						"x": 681.5441190263043,
						"y": 631.8605538874315,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s13",
					"statesandinfos": [],
					"parent": "nwtN_13b19118-54eb-4400-a949-ca664386ec06",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 681.5441190263043,
					"y": 631.8605538874315
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "0fcd19b1-d058-1326-c349-64cc1fce6569",
					"bbox": {
						"x": 880.0889730362344,
						"y": 655.0860634639737,
						"w": 178.41967958113423,
						"h": 165.30222154074738
					},
					"originalW": 206.41967958113423,
					"originalH": 193.30222154074738,
					"class": "submap",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 2.25,
					"border-color": "#555555",
					"background-color": "#f7f7f7",
					"background-opacity": 0.5,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 14,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 880.0889730362344,
					"y": 655.0860634639737
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "3aa07735-3104-5ae3-5b94-16be7319a912",
					"bbox": {
						"x": 930.1738128268015,
						"y": 664.0297848551465,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s18",
					"statesandinfos": [],
					"parent": "0fcd19b1-d058-1326-c349-64cc1fce6569",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 930.1738128268015,
					"y": 664.0297848551465
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "212adf44-8401-5b11-c49a-b43b67369698",
					"bbox": {
						"x": 832.3891256166468,
						"y": 698.6121742343474,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s20",
					"statesandinfos": [],
					"parent": "0fcd19b1-d058-1326-c349-64cc1fce6569",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 832.3891256166468,
					"y": 698.6121742343474
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "4f9485a2-a66f-2cf1-cc4e-126f7889fcd0",
					"bbox": {
						"x": 830.0041332456673,
						"y": 611.5599526936,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s19",
					"statesandinfos": [],
					"parent": "0fcd19b1-d058-1326-c349-64cc1fce6569",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 830.0041332456673,
					"y": 611.5599526936
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "5c108d5c-88e4-7b97-95a8-67238c33d283",
					"bbox": {
						"x": 875.3189882942758,
						"y": 654.4898153712289,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s17",
					"statesandinfos": [],
					"parent": "0fcd19b1-d058-1326-c349-64cc1fce6569",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 875.3189882942758,
					"y": 654.4898153712289
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "b4dac823-ff22-5ee3-02f8-825fcda40038",
					"bbox": {
						"x": 1150.256078598176,
						"y": 628.0465159213652,
						"w": 185.57465669407156,
						"h": 140.25980164546388
					},
					"originalW": 213.57465669407156,
					"originalH": 168.25980164546388,
					"class": "submap",
					"statesandinfos": [],
					"language": "PD",
					"border-width": 2.25,
					"border-color": "#555555",
					"background-color": "#f7f7f7",
					"background-opacity": 0.5,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 14,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "normal",
					"color": "#000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 1150.256078598176,
					"y": 628.0465159213652
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "f1bf3c87-1357-3ce2-4645-8cb4ed59ac9b",
					"bbox": {
						"x": 1203.9184069452117,
						"y": 659.0514167440972,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s23",
					"statesandinfos": [],
					"parent": "b4dac823-ff22-5ee3-02f8-825fcda40038",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 1203.9184069452117,
					"y": 659.0514167440972
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "2f96a4e1-9767-4df9-6ad7-a4010baa376b",
					"bbox": {
						"x": 1096.5937502511401,
						"y": 624.4690273648961,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s24",
					"statesandinfos": [],
					"parent": "b4dac823-ff22-5ee3-02f8-825fcda40038",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 1096.5937502511401,
					"y": 624.4690273648961
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "cae247cf-9ad9-695c-4042-09cfd4f23475",
					"bbox": {
						"x": 1196.7634298322737,
						"y": 597.0416150986333,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s22",
					"statesandinfos": [],
					"parent": "b4dac823-ff22-5ee3-02f8-825fcda40038",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 1196.7634298322737,
					"y": 597.0416150986333
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "3ea6020e-8918-8abf-a8e5-62427070f84f",
					"bbox": {
						"x": 1151.*************,
						"y": 629.2390121068548,
						"w": 75,
						"h": 75
					},
					"class": "simple chemical",
					"label": "s21",
					"statesandinfos": [],
					"parent": "b4dac823-ff22-5ee3-02f8-825fcda40038",
					"language": "PD",
					"border-width": 1.25,
					"border-color": "#555555",
					"background-color": "#fddbc7",
					"background-opacity": 1,
					"background-image-opacity": 1,
					"text-wrap": "wrap",
					"font-size": 26,
					"font-family": "Helvetica",
					"font-style": "normal",
					"font-weight": "bold",
					"color": "#000000",
					"background-image": "",
					"ports": [],
					"infoboxCalculated": true,
					"auxunitlayouts": {}
				},
				"position": {
					"x": 1151.*************,
					"y": 629.2390121068548
				},
				"group": "nodes",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			}
		],
		"edges": [
			{
				"data": {
					"id": "nwtE_506a6914-916d-4822-ad99-2417ef62f8cd",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "5c108d5c-88e4-7b97-95a8-67238c33d283",
					"target": "aeee80bf-8280-f4ec-c1f3-26f3a7a0651c",
					"portsource": "5c108d5c-88e4-7b97-95a8-67238c33d283",
					"porttarget": "aeee80bf-8280-f4ec-c1f3-26f3a7a0651c"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "dc17f173-0623-8bac-2b1f-edac5f91ccef",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "cae247cf-9ad9-695c-4042-09cfd4f23475",
					"target": "3ea6020e-8918-8abf-a8e5-62427070f84f",
					"portsource": "cae247cf-9ad9-695c-4042-09cfd4f23475",
					"porttarget": "3ea6020e-8918-8abf-a8e5-62427070f84f"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_fb874a56-2ff6-4ad3-862f-0cabfda9fbbe",
					"class": "consumption",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "nwtN_91cff953-5f55-4626-96fa-e2f675e13e54",
					"target": "nwtN_7e09bf9e-a4da-4618-baba-60a2b3a0b134",
					"portsource": "nwtN_91cff953-5f55-4626-96fa-e2f675e13e54",
					"porttarget": "nwtN_7e09bf9e-a4da-4618-baba-60a2b3a0b134"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_c9636997-63c2-4bee-ae9f-d07ea6e2cb43",
					"class": "consumption",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "nwtN_b20cc3f4-dc9c-4031-ac79-df665d4c53cd",
					"target": "661498c7-ace9-bd34-0289-7c9931d3e1ba",
					"portsource": "nwtN_b20cc3f4-dc9c-4031-ac79-df665d4c53cd",
					"porttarget": "661498c7-ace9-bd34-0289-7c9931d3e1ba"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_9d07cf77-da86-48b4-9626-9bc6f90143b5",
					"class": "consumption",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "nwtN_5d6f7198-03e4-48e5-9004-919221f87e66",
					"target": "aeee80bf-8280-f4ec-c1f3-26f3a7a0651c",
					"portsource": "nwtN_5d6f7198-03e4-48e5-9004-919221f87e66",
					"porttarget": "aeee80bf-8280-f4ec-c1f3-26f3a7a0651c"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_800ad693-8319-44d5-b92a-2b6ec4e4e8fa",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "3ea6020e-8918-8abf-a8e5-62427070f84f",
					"target": "aeee80bf-8280-f4ec-c1f3-26f3a7a0651c",
					"portsource": "3ea6020e-8918-8abf-a8e5-62427070f84f",
					"porttarget": "aeee80bf-8280-f4ec-c1f3-26f3a7a0651c"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_7c04db5c-d599-40e2-830f-bd36c28457f1",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "nwtN_5c29f820-d40e-4c85-b7b6-c7b8a378b49d",
					"target": "nwtN_0185dada-235a-4b51-bce7-e8bd3b8ee661",
					"portsource": "nwtN_5c29f820-d40e-4c85-b7b6-c7b8a378b49d",
					"porttarget": "nwtN_0185dada-235a-4b51-bce7-e8bd3b8ee661"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_4aa3c2db-45bc-4058-91ea-acefd5b0e2bd",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "nwtN_348778d0-85d5-442f-b73a-9dd9f5d9f2b3",
					"target": "nwtN_0185dada-235a-4b51-bce7-e8bd3b8ee661",
					"portsource": "nwtN_348778d0-85d5-442f-b73a-9dd9f5d9f2b3",
					"porttarget": "nwtN_0185dada-235a-4b51-bce7-e8bd3b8ee661"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_0abb0b43-af9c-4579-823d-47b3acefb54b",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "nwtN_78116f70-d871-4b8e-83cf-7b3a684067a7",
					"target": "nwtN_0185dada-235a-4b51-bce7-e8bd3b8ee661",
					"portsource": "nwtN_78116f70-d871-4b8e-83cf-7b3a684067a7",
					"porttarget": "nwtN_0185dada-235a-4b51-bce7-e8bd3b8ee661"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_52d8b99f-3024-4c79-8f34-b74276f3a3ed",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "nwtN_4a12169f-f588-4c76-bc39-2ae07f77d7c4",
					"target": "nwtN_be627c29-b885-460c-bf92-8f9bb7b8c8c5",
					"portsource": "nwtN_4a12169f-f588-4c76-bc39-2ae07f77d7c4",
					"porttarget": "nwtN_be627c29-b885-460c-bf92-8f9bb7b8c8c5"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_906e4f53-8ebe-4246-bd59-88b38a7071f0",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "nwtN_cbc5909d-c0a3-4be5-9c36-988ca48a9d30",
					"target": "nwtN_be627c29-b885-460c-bf92-8f9bb7b8c8c5",
					"portsource": "nwtN_cbc5909d-c0a3-4be5-9c36-988ca48a9d30",
					"porttarget": "nwtN_be627c29-b885-460c-bf92-8f9bb7b8c8c5"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_3e77a1b5-2418-4dfc-b77d-b6280a5ea798",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "nwtN_48d663f4-edd0-4706-a5ca-5a87f77edf8e",
					"target": "nwtN_be627c29-b885-460c-bf92-8f9bb7b8c8c5",
					"portsource": "nwtN_48d663f4-edd0-4706-a5ca-5a87f77edf8e",
					"porttarget": "nwtN_be627c29-b885-460c-bf92-8f9bb7b8c8c5"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_a3959383-0737-483f-a751-6bf0d2b78896",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "nwtN_2ecfaa1e-8dd3-4008-ac3d-6d4207f518b4",
					"target": "nwtN_64abed47-d1b1-474c-b944-b9ad6354c086",
					"portsource": "nwtN_2ecfaa1e-8dd3-4008-ac3d-6d4207f518b4",
					"porttarget": "nwtN_64abed47-d1b1-474c-b944-b9ad6354c086"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_c7945a01-b5ed-4fc6-a3c9-f80c5ee23dda",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "nwtN_8af34100-fdea-4636-a978-fe75c7a38fb1",
					"target": "nwtN_64abed47-d1b1-474c-b944-b9ad6354c086",
					"portsource": "nwtN_8af34100-fdea-4636-a978-fe75c7a38fb1",
					"porttarget": "nwtN_64abed47-d1b1-474c-b944-b9ad6354c086"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_c2945754-dd7e-4947-95cd-b66c3dd776df",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "nwtN_1c41d32b-42f9-4e9f-a609-91c2eebbf28a",
					"target": "nwtN_64abed47-d1b1-474c-b944-b9ad6354c086",
					"portsource": "nwtN_1c41d32b-42f9-4e9f-a609-91c2eebbf28a",
					"porttarget": "nwtN_64abed47-d1b1-474c-b944-b9ad6354c086"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_49b115c1-d8f4-4f9f-80f2-da43dec66241",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "nwtN_c5547527-64c5-4e4d-acb8-e3f7e6b0a9a5",
					"target": "nwtN_273f6927-f05a-425b-aa2b-92de2be06bd9",
					"portsource": "nwtN_c5547527-64c5-4e4d-acb8-e3f7e6b0a9a5",
					"porttarget": "nwtN_273f6927-f05a-425b-aa2b-92de2be06bd9"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_17eb1da9-aaf4-463a-896c-a1ec8f37a6f2",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "nwtN_6bf42b3c-579e-4187-8299-cb314373c278",
					"target": "nwtN_273f6927-f05a-425b-aa2b-92de2be06bd9",
					"portsource": "nwtN_6bf42b3c-579e-4187-8299-cb314373c278",
					"porttarget": "nwtN_273f6927-f05a-425b-aa2b-92de2be06bd9"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_2d6fbdec-29c8-46af-9fee-090c2fb9bc75",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "nwtN_4d424656-f0bb-4154-9a51-1d15429c8f71",
					"target": "nwtN_273f6927-f05a-425b-aa2b-92de2be06bd9",
					"portsource": "nwtN_4d424656-f0bb-4154-9a51-1d15429c8f71",
					"porttarget": "nwtN_273f6927-f05a-425b-aa2b-92de2be06bd9"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_8b26c6e4-8035-4465-a038-a6219ba54bee",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "nwtN_be627c29-b885-460c-bf92-8f9bb7b8c8c5",
					"target": "nwtN_7e09bf9e-a4da-4618-baba-60a2b3a0b134",
					"portsource": "nwtN_be627c29-b885-460c-bf92-8f9bb7b8c8c5",
					"porttarget": "nwtN_7e09bf9e-a4da-4618-baba-60a2b3a0b134"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_a6d29cf3-24a6-4b0c-88b3-df08fda0825c",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "nwtN_64abed47-d1b1-474c-b944-b9ad6354c086",
					"target": "nwtN_7e09bf9e-a4da-4618-baba-60a2b3a0b134",
					"portsource": "nwtN_64abed47-d1b1-474c-b944-b9ad6354c086",
					"porttarget": "nwtN_7e09bf9e-a4da-4618-baba-60a2b3a0b134"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_cb84a698-6aee-4c1a-a9d8-e5eb87797a59",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "nwtN_0185dada-235a-4b51-bce7-e8bd3b8ee661",
					"target": "661498c7-ace9-bd34-0289-7c9931d3e1ba",
					"portsource": "nwtN_0185dada-235a-4b51-bce7-e8bd3b8ee661",
					"porttarget": "661498c7-ace9-bd34-0289-7c9931d3e1ba"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "nwtE_bf55af04-2f1f-432d-8d32-60486b9c5d08",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "nwtN_273f6927-f05a-425b-aa2b-92de2be06bd9",
					"target": "661498c7-ace9-bd34-0289-7c9931d3e1ba",
					"portsource": "nwtN_273f6927-f05a-425b-aa2b-92de2be06bd9",
					"porttarget": "661498c7-ace9-bd34-0289-7c9931d3e1ba"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "cae87ba6-1f44-b148-a8fa-e9b93fca71dd",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "3aa07735-3104-5ae3-5b94-16be7319a912",
					"target": "5c108d5c-88e4-7b97-95a8-67238c33d283",
					"portsource": "3aa07735-3104-5ae3-5b94-16be7319a912",
					"porttarget": "5c108d5c-88e4-7b97-95a8-67238c33d283"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "cba4c2db-9aa8-2429-37b7-ef58e51ccef5",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "212adf44-8401-5b11-c49a-b43b67369698",
					"target": "5c108d5c-88e4-7b97-95a8-67238c33d283",
					"portsource": "212adf44-8401-5b11-c49a-b43b67369698",
					"porttarget": "5c108d5c-88e4-7b97-95a8-67238c33d283"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "75e10cd7-b168-147e-9842-dd6859a94e61",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "4f9485a2-a66f-2cf1-cc4e-126f7889fcd0",
					"target": "5c108d5c-88e4-7b97-95a8-67238c33d283",
					"portsource": "4f9485a2-a66f-2cf1-cc4e-126f7889fcd0",
					"porttarget": "5c108d5c-88e4-7b97-95a8-67238c33d283"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "20b1d70b-c88f-acaf-0ced-07374db84055",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "f1bf3c87-1357-3ce2-4645-8cb4ed59ac9b",
					"target": "3ea6020e-8918-8abf-a8e5-62427070f84f",
					"portsource": "f1bf3c87-1357-3ce2-4645-8cb4ed59ac9b",
					"porttarget": "3ea6020e-8918-8abf-a8e5-62427070f84f"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			},
			{
				"data": {
					"id": "1fefa878-d380-55e4-4d18-ec760fc294f4",
					"class": "production",
					"bendPointPositions": [],
					"language": "PD",
					"line-color": "#555555",
					"width": 1.25,
					"background-image": "",
					"background-color": "#ffffff",
					"cardinality": 0,
					"source": "2f96a4e1-9767-4df9-6ad7-a4010baa376b",
					"target": "3ea6020e-8918-8abf-a8e5-62427070f84f",
					"portsource": "2f96a4e1-9767-4df9-6ad7-a4010baa376b",
					"porttarget": "3ea6020e-8918-8abf-a8e5-62427070f84f"
				},
				"position": {},
				"group": "edges",
				"removed": false,
				"selected": false,
				"selectable": true,
				"locked": false,
				"grabbable": true,
				"classes": ""
			}
		]
	},
	"style": [
	    {
	      "selector": "node",
	      "style": {
	        "shape": "rectangle",
	        "text-halign": "center",
	        "text-valign": "center",
	        "font-weight" : "normal",
	        "background-color": "#ffffff",
	        "opacity": "1",
	        "border-color": "#555555"
	      }
	    },
	    {
	      "selector": "node[class = 'simple chemical']",
	      "style": {
	        "shape": "ellipse",
	        "font-size": 14,
	        "background-color": "#fddbc7"
	      }
	    },
	    {
	      "selector": "node[class = 'macromolecule']",
	      "style": {
	        "shape": "roundrectangle"
	      }
	    },
	    {
	      "selector": "node[class = 'unspecified entity']",
	      "style": {
	        "shape": "ellipse",
	        "background-color": "#f7f7f7"
	      }
	    },
	    {
	      "selector": "node[class = 'nucleic acid feature']",
	      "style": {
	        "shape": "rectangle",
	        "background-color": "#f4a582",	        
	      }
	    },	    	    	   
	    {
	      "selector": ":parent",
	      "style": {
	        "background-opacity": "0.333",
	        "text-valign": "bottom",
	        "shape": "barrel",
	        "text-margin-y": "2px",
	        "font-weight" : "normal",
	        "border-color": "#555555"
	      }
	    },
	    {
	      "selector": "node:selected",
	      "style": {
	        "background-color": "#33ff00",
	        "border-color": "#22ee00"
	      }
	    },
	    {
	      "selector": "edge",
	      "style": {
	        "curve-style": "bezier",
	        "width": "2px",
	        "line-color": "rgb(58,126,207)",
	        "opacity": "1"
	      }
	    },
	    {
	      "selector": "edge:selected",
	      "style": {
	        "line-color": "#33ff00",
	        "font-size": "13px",
	        "text-opacity": "1",
	        "text-rotation": "autorotate",
	        "color": "#33ff00",
	        "font-weight": "bold",
	        "text-background-shape": "roundrectangle",
	        "text-background-opacity": "1",
	        "text-background-padding": "2px"
	      }
	    },
	    {
	      "selector": "edge[class = 'production']",
	      "style": {
	        "target-arrow-shape": "triangle",
	        "target-arrow-color": "rgb(58,126,207)"
	      }
	    },
	    {
	      "selector": "edge:selected[class = 'production']",
	      "style": {
	        "target-arrow-shape": "triangle",
	        "target-arrow-color": "#33ff00"
	      }
	    },	    
	  ],
	"zoomingEnabled": true,
	"userZoomingEnabled": true,
	"zoom": 0.8925320450269244,
	"minZoom": 0.125,
	"maxZoom": 16,
	"panningEnabled": true,
	"userPanningEnabled": true,
	"pan": {
		"x": 128.15184671589566,
		"y": -93.65929006599106
	},
	"boxSelectionEnabled": true,
	"renderer": {
		"name": "canvas"
	},
	"wheelSensitivity": 0.1,
	"motionBlur": true
}