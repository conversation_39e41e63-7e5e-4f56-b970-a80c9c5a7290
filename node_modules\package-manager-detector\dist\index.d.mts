export { COMMANDS, constructCommand, resolveCommand } from './commands.mjs';
export { AGENTS, INSTALL_PAGE, LOCKS } from './constants.mjs';
export { detect, detectSync, getUserAgent } from './detect.mjs';
export { A as Agent, b as AgentCommandValue, c as AgentCommands, a as AgentName, C as Command, D as DetectOptions, d as DetectResult, R as ResolvedCommand } from './shared/package-manager-detector.ncFwAKgD.mjs';
import 'quansync/types';
