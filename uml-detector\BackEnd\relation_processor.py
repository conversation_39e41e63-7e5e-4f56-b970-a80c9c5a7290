#relation_processor.py

import numpy as np
import math
from typing import Dict, List, Tuple, Optional
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("relation_processor.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RelationProcessor:
    """
    Classe pour traiter les relations entre les objets détectés dans les diagrammes UML.
    Associe les flèches détectées avec les types de relations et les classes correspondantes.
    """
    
    def __init__(self, distance_threshold: int = 100):
        """
        Initialise le processeur de relations.
        
        Args:
            distance_threshold: Distance maximale (en pixels) pour considérer qu'un point est proche d'un autre objet
        """
        self.distance_threshold = distance_threshold
        
        # Mappings des indices de classes pour les deux modèles
        self.MODEL1_NAMES = {0: 'arrow', 1: 'class'}
        self.MODEL2_NAMES = {
            0: 'A', 
            1: 'C', 
            2: 'generalization', 
            3: 'endpoint', 
            4: 'one-way-association', 
            5: 'composition', 
            6: 'aggregation'
        }
        
        # Descriptions des relations pour les phrases générées
        self.RELATION_DESCRIPTIONS = {
            'generalization': "héritage (généralisation)",
            'one-way-association': "association unidirectionnelle",
            'composition': "composition (partie intégrante)",
            'aggregation': "agrégation (contient)",
            'endpoint': "association",  # Simplification: endpoint devient association
            'association': "association",  # Relation par défaut si seulement des flèches sont détectées
            'A': "association de type A",
            'C': "association de type C"
        }
        
        # Définition de l'ordre de priorité des relations (du plus fort au plus faible)
        self.RELATION_PRIORITY = [
            'generalization',
            'composition',
            'aggregation',
            'one-way-association',
            'association',
            'endpoint',
            'A',
            'C'
        ]
        
        # Seuil pour considérer une flèche comme trop courte (potentiellement un symbole erroné)
        self.MIN_ARROW_LENGTH = 20  # en pixels
        
        logger.info("RelationProcessor initialisé avec un seuil de distance de %d pixels", distance_threshold)
    
    def calculate_distance(self, point1: Tuple[int, int], point2: Tuple[int, int]) -> float:
        """
        Calcule la distance euclidienne entre deux points.
        
        Args:
            point1: Premier point (x, y)
            point2: Deuxième point (x, y)
            
        Returns:
            La distance euclidienne entre les deux points
        """
        return math.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)
    
    def get_box_center(self, box: Tuple[int, int, int, int]) -> Tuple[int, int]:
        """
        Calcule le centre d'une boîte délimitante.
        
        Args:
            box: Boîte délimitante (x1, y1, x2, y2)
            
        Returns:
            Coordonnées du centre (center_x, center_y)
        """
        x1, y1, x2, y2 = box
        return ((x1 + x2) // 2, (y1 + y2) // 2)
    
    def get_arrow_orientation(self, arrow_box: Tuple[int, int, int, int]) -> str:
        """
        Détermine l'orientation d'une flèche (horizontale ou verticale).
        
        Args:
            arrow_box: Boîte délimitante de la flèche (x1, y1, x2, y2)
            
        Returns:
            'horizontal' ou 'vertical'
        """
        x1, y1, x2, y2 = arrow_box
        width = x2 - x1
        height = y2 - y1
        
        return 'horizontal' if width > height else 'vertical'
    
    def get_arrow_endpoints(self, arrow_box: Tuple[int, int, int, int]) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        """
        Obtient les points d'extrémité estimés d'une flèche.
        
        Args:
            arrow_box: Boîte délimitante de la flèche (x1, y1, x2, y2)
            
        Returns:
            Tuple contenant les deux extrémités ((x1, y1), (x2, y2))
        """
        x1, y1, x2, y2 = arrow_box
        orientation = self.get_arrow_orientation(arrow_box)
        
        if orientation == 'horizontal':
            # Pour les flèches horizontales, utiliser les points extrêmes gauche et droit
            start_point = (x1, (y1 + y2) // 2)
            end_point = (x2, (y1 + y2) // 2)
        else:
            # Pour les flèches verticales, utiliser les points extrêmes haut et bas
            start_point = ((x1 + x2) // 2, y1)
            end_point = ((x1 + x2) // 2, y2)
        
        return (start_point, end_point)
    
    def calculate_arrow_length(self, start_point: Tuple[int, int], end_point: Tuple[int, int]) -> float:
        """
        Calcule la longueur d'une flèche à partir de ses extrémités.
        
        Args:
            start_point: Point de départ (x, y)
            end_point: Point d'arrivée (x, y)
            
        Returns:
            Longueur de la flèche en pixels
        """
        return self.calculate_distance(start_point, end_point)
    
    def is_same_class(self, class1: Dict, class2: Dict) -> bool:
        """
        Détermine si deux objets de classe représentent la même classe.
        
        Args:
            class1: Premier objet classe
            class2: Deuxième objet classe
            
        Returns:
            True si c'est la même classe, False sinon
        """
        if not class1 or not class2:
            return False
        return class1['id'] == class2['id']
    
    def find_nearest_class(self, point: Tuple[int, int], class_boxes: List[Dict]) -> Optional[Dict]:
        """
        Trouve la classe la plus proche d'un point donné.
        
        Args:
            point: Coordonnées du point (x, y)
            class_boxes: Liste des boîtes de classe avec leurs informations
            
        Returns:
            La classe la plus proche si elle est dans le seuil de distance, sinon None
        """
        nearest_class = None
        min_distance = float('inf')
        
        for class_box in class_boxes:
            box = class_box['box']
            # Vérifier si le point est à l'intérieur de la boîte
            if (box[0] <= point[0] <= box[2]) and (box[1] <= point[1] <= box[3]):
                logger.debug(f"Point {point} à l'intérieur de la classe {class_box['label']}")
                return class_box
            
            # Calculer la distance au bord de la boîte le plus proche
            dist_x = max(box[0] - point[0], 0, point[0] - box[2])
            dist_y = max(box[1] - point[1], 0, point[1] - box[3])
            distance = math.sqrt(dist_x**2 + dist_y**2)
            
            if distance < min_distance and distance <= self.distance_threshold:
                min_distance = distance
                nearest_class = class_box
                logger.debug(f"Classe trouvée à distance {distance} pour le point {point}")
        
        return nearest_class
    
    def find_nearest_relation_type(self, point: Tuple[int, int], relation_boxes: List[Dict]) -> Optional[Dict]:
        """
        Trouve le type de relation le plus proche d'un point donné.
        
        Args:
            point: Coordonnées du point (x, y)
            relation_boxes: Liste des boîtes de relation avec leurs informations
            
        Returns:
            Dictionnaire contenant les informations sur la relation trouvée, ou None
        """
        nearest_relation = None
        min_distance = float('inf')
        
        for relation_box in relation_boxes:
            box = relation_box['box']
            # Vérifier si le point est à l'intérieur de la boîte
            if (box[0] <= point[0] <= box[2]) and (box[1] <= point[1] <= box[3]):
                logger.debug(f"Point {point} à l'intérieur de la relation {relation_box['class_name']}")
                return relation_box
            
            # Calculer la distance au centre de la boîte
            center_x = (box[0] + box[2]) // 2
            center_y = (box[1] + box[3]) // 2
            distance = self.calculate_distance(point, (center_x, center_y))
            
            # La boîte de relation doit être à une certaine distance maximale
            if distance < min_distance and distance <= self.distance_threshold:
                min_distance = distance
                nearest_relation = relation_box
                logger.debug(f"Relation '{relation_box['class_name']}' trouvée à distance {distance} pour le point {point}")
        
        return nearest_relation
    
    def extract_class_name(self, class_text: str) -> str:
        """
        Extrait le nom de la classe à partir du texte extrait.
        
        Args:
            class_text: Texte extrait de la classe par Groq
            
        Returns:
            Nom de la classe
        """
        # Si le texte est formaté comme attendu par Groq
        if "NOM_CLASSE:" in class_text:
            lines = class_text.split('\n')
            for line in lines:
                if line.startswith("NOM_CLASSE:"):
                    # Retourner le nom après "NOM_CLASSE:" en supprimant les espaces
                    return line[len("NOM_CLASSE:"):].strip()
        
        # Si format différent, essayer de prendre la première ligne comme nom de classe
        lines = class_text.split('\n')
        if lines:
            return lines[0].strip()
        
        return "Classe sans nom"
    
    def get_relation_priority(self, relation_type: str) -> int:
        """
        Obtient la priorité d'un type de relation.
        Plus le chiffre est petit, plus la priorité est élevée.
        
        Args:
            relation_type: Type de relation
            
        Returns:
            Valeur de priorité (plus petit = plus prioritaire)
        """
        try:
            return self.RELATION_PRIORITY.index(relation_type)
        except ValueError:
            # Si le type n'est pas dans la liste, lui donner la priorité la plus basse
            return len(self.RELATION_PRIORITY)
    
    def prioritize_relation_types(self, relation_types: List[str]) -> str:
        """
        Priorise les types de relations selon l'ordre défini.
        
        Args:
            relation_types: Liste des types de relations détectés
            
        Returns:
            Le type de relation le plus prioritaire
        """
        if not relation_types:
            return 'association'  # Type par défaut
        
        # Filtrer les valeurs None
        relation_types = [r for r in relation_types if r]
        
        if not relation_types:
            return 'association'
        
        # Trier par priorité (du plus prioritaire au moins prioritaire)
        sorted_types = sorted(relation_types, key=self.get_relation_priority)
        return sorted_types[0]
    
    def process_detections(self, model1_results: List[Dict], model2_results: List[Dict], extracted_texts: Dict) -> List[Dict]:
        """
        Traite les détections des deux modèles et génère des informations sur les relations.
        
        Args:
            model1_results: Résultats du modèle 1 (classes et flèches)
            model2_results: Résultats du modèle 2 (types de relations)
            extracted_texts: Dictionnaire des textes extraits par classe
            
        Returns:
            Liste de dictionnaires décrivant les relations trouvées
        """
        logger.info("Début du traitement des détections")
        
        # Extraire les classes et les flèches du modèle 1
        class_boxes = []
        arrow_boxes = []
        
        for detection in model1_results:
            if detection['class_name'] == 'class':
                # Stocker l'ID de la classe avec ses coordonnées
                class_id = detection.get('id', '')
                class_label = detection.get('label', '')
                
                # Vérifier si le texte extrait est disponible pour cette classe
                class_text = extracted_texts.get(class_id, extracted_texts.get(class_label, "Classe sans nom"))
                
                class_boxes.append({
                    'box': detection['box'],
                    'id': class_id,
                    'label': class_label,
                    'text': class_text
                })
            elif detection['class_name'] == 'arrow':
                arrow_boxes.append({
                    'box': detection['box'],
                    'id': detection.get('id', ''),
                    'label': detection.get('label', '')
                })
        
        # Extraire les relations du modèle 2
        relation_boxes = []
        for detection in model2_results:
            if detection['class_name'] in self.MODEL2_NAMES.values():
                relation_boxes.append({
                    'box': detection['box'],
                    'class_name': detection['class_name'],
                    'id': detection.get('id', ''),
                    'label': detection.get('label', ''),
                    'confidence': detection.get('confidence', 0.0)
                })
        
        logger.info(f"Détections extraites: {len(class_boxes)} classes, {len(arrow_boxes)} flèches, {len(relation_boxes)} relations")
        
        # Afficher les coordonnées des classes pour le debug
        for cls in class_boxes:
            logger.debug(f"Classe {cls['label']} à la position {cls['box']}")
        
        # Afficher les coordonnées des flèches pour le debug
        for arr in arrow_boxes:
            start, end = self.get_arrow_endpoints(arr['box'])
            logger.debug(f"Flèche {arr['label']} de {start} à {end}")
        
        # Analyser chaque flèche pour identifier les relations
        relations = []
        
        for arrow in arrow_boxes:
            # Obtenir les extrémités estimées de la flèche
            start_point, end_point = self.get_arrow_endpoints(arrow['box'])
            
            # Calculer la longueur de la flèche
            arrow_length = self.calculate_arrow_length(start_point, end_point)
            
            # Vérifier si la flèche n'est pas trop courte (symbole erroné potentiel)
            if arrow_length < self.MIN_ARROW_LENGTH:
                logger.warning(f"Flèche {arrow.get('label', 'sans nom')} trop courte ({arrow_length:.2f} px), ignorée")
                continue
            
            # Afficher les points calculés pour le debug
            logger.debug(f"Points calculés pour la flèche {arrow.get('label', 'sans nom')}: {start_point} -> {end_point}")
            
            # Trouver les classes les plus proches des deux extrémités de la flèche
            start_class = self.find_nearest_class(start_point, class_boxes)
            end_class = self.find_nearest_class(end_point, class_boxes)
            
            # Afficher les classes trouvées pour le debug
            if start_class:
                logger.debug(f"Classe de départ trouvée: {start_class['label']}")
            if end_class:
                logger.debug(f"Classe d'arrivée trouvée: {end_class['label']}")
            
            # Vérifier si c'est une relation réflexive (même classe)
            is_reflexive = self.is_same_class(start_class, end_class)
            
            # Trouver les types de relations les plus proches des extrémités et du milieu de la flèche
            relation_type_start = self.find_nearest_relation_type(start_point, relation_boxes)
            relation_type_end = self.find_nearest_relation_type(end_point, relation_boxes)
            
            # Calculer le point milieu de la flèche
            mid_point = ((start_point[0] + end_point[0]) // 2, (start_point[1] + end_point[1]) // 2)
            relation_type_mid = self.find_nearest_relation_type(mid_point, relation_boxes)
            
            # Afficher les types de relations trouvés pour le debug
            logger.debug(f"Type de relation au départ: {relation_type_start['class_name'] if relation_type_start else None}")
            logger.debug(f"Type de relation à l'arrivée: {relation_type_end['class_name'] if relation_type_end else None}")
            logger.debug(f"Type de relation au milieu: {relation_type_mid['class_name'] if relation_type_mid else None}")
            
            # Liste des types de relations détectés avec leur confiance
            detected_relations = []
            for rel in [relation_type_start, relation_type_end, relation_type_mid]:
                if rel:
                    detected_relations.append((rel['class_name'], rel.get('confidence', 0.0)))
            
            # Si plusieurs types, prendre celui avec la meilleure confiance et la meilleure priorité
            if detected_relations:
                # Trier d'abord par priorité, puis par confiance décroissante
                detected_relations.sort(key=lambda x: (self.get_relation_priority(x[0]), -x[1]))
                final_relation_type = detected_relations[0][0]
            else:
                final_relation_type = 'association'
            
            # Stocker la relation selon différents cas
            relation_info = {
                'arrow_id': arrow.get('id', ''),
                'arrow_label': arrow.get('label', ''),
                'start_point': start_point,
                'end_point': end_point,
                'relation_type': final_relation_type,
                'is_reflexive': is_reflexive,
                'orphan': False,  # Par défaut, pas orphelin
                'length': arrow_length
            }
            
            # Vérifier si c'est une flèche orpheline (pas de classe à une ou deux extrémités)
            if not start_class and not end_class:
                relation_info['orphan'] = True
                relation_info['start_class'] = None
                relation_info['end_class'] = None
                relation_info['start_class_name'] = None
                relation_info['end_class_name'] = None
                relation_info['relation_phrase'] = f"Flèche {arrow.get('label', '')} non interprétable (relation orpheline, aucune classe aux extrémités)"
                relations.append(relation_info)
                logger.warning(f"Flèche orpheline détectée: {arrow.get('label', 'sans nom')}")
                continue
            
            # Cas où une seule extrémité est liée à une classe
            if not start_class or not end_class:
                relation_info['orphan'] = True
                relation_info['start_class'] = start_class
                relation_info['end_class'] = end_class
                relation_info['start_class_name'] = self.extract_class_name(start_class['text']) if start_class else None
                relation_info['end_class_name'] = self.extract_class_name(end_class['text']) if end_class else None
                
                # Phrase décrivant la relation orpheline
                if start_class:
                    start_name = self.extract_class_name(start_class['text'])
                    relation_info['relation_phrase'] = f"Flèche partant de {start_name} sans classe d'arrivée identifiée (relation orpheline)"
                else:
                    end_name = self.extract_class_name(end_class['text'])
                    relation_info['relation_phrase'] = f"Flèche arrivant à {end_name} sans classe de départ identifiée (relation orpheline)"
                
                relations.append(relation_info)
                logger.warning(f"Flèche semi-orpheline détectée: {arrow.get('label', 'sans nom')}")
                continue
            
            # Cas standard: deux classes reliées par une flèche
            # Extraire les noms des classes
            start_class_name = self.extract_class_name(start_class['text'])
            end_class_name = self.extract_class_name(end_class['text'])
            
            relation_info['start_class'] = start_class
            relation_info['end_class'] = end_class
            relation_info['start_class_name'] = start_class_name
            relation_info['end_class_name'] = end_class_name
            
            # Description de la relation
            relation_description = self.RELATION_DESCRIPTIONS.get(final_relation_type, "association")
            
            # Formatage de phrase selon le type de relation et si c'est réflexif
            if is_reflexive:
                relation_info['relation_phrase'] = f"La classe {start_class_name} a une relation avec elle-même ({relation_description})"
            elif final_relation_type == "generalization":
                relation_info['relation_phrase'] = f"{end_class_name} hérite de {start_class_name} ({relation_description})"
            elif final_relation_type == "composition":
                relation_info['relation_phrase'] = f"{start_class_name} contient {end_class_name} comme partie intégrante ({relation_description})"
            elif final_relation_type == "aggregation":
                relation_info['relation_phrase'] = f"{start_class_name} contient des instances de {end_class_name} ({relation_description})"
            elif final_relation_type == "one-way-association":
                relation_info['relation_phrase'] = f"{start_class_name} est associé à {end_class_name} de façon unidirectionnelle ({relation_description})"
            else:
                relation_info['relation_phrase'] = f"Il y a une relation de {relation_description} entre {start_class_name} et {end_class_name}"
            
            relations.append(relation_info)
            logger.info(f"Relation détectée: {relation_info['relation_phrase']}")
        
        # Vérifier s'il existe des relations flottantes (types de relations sans flèche associée)
        floating_relations = []
        for relation in relation_boxes:
            is_floating = True
            relation_center = self.get_box_center(relation['box'])
            
            # Vérifier si cette relation est associée à une flèche
            for rel_info in relations:
                if not rel_info['orphan']:
                    # Calculer la distance aux extrémités et au milieu de la flèche
                    dist_start = self.calculate_distance(relation_center, rel_info['start_point'])
                    dist_end = self.calculate_distance(relation_center, rel_info['end_point'])
                    mid_point = ((rel_info['start_point'][0] + rel_info['end_point'][0]) // 2, 
                                (rel_info['start_point'][1] + rel_info['end_point'][1]) // 2)
                    dist_mid = self.calculate_distance(relation_center, mid_point)
                    
                    # Si la relation est proche d'une flèche, elle n'est pas flottante
                    if min(dist_start, dist_end, dist_mid) <= self.distance_threshold:
                        is_floating = False
                        break
            
            if is_floating:
                floating_relations.append({
                    'type': 'floating_relation',
                    'relation_type': relation['class_name'],
                    'box': relation['box'],
                    'center': relation_center,
                    'relation_phrase': f"Relation de type {relation['class_name']} détectée sans flèche associée (relation flottante)"
                })
                logger.warning(f"Relation flottante détectée: {relation['class_name']} à {relation_center}")
        
        # Si demandé, ajouter les relations flottantes à la liste des relations
        # relations.extend(floating_relations)
        
        # Si aucune relation n'a été trouvée, ajouter un message d'information
        if not relations:
            relations.append({
                'type': 'no_relations',
                'relation_phrase': "Aucune relation entre classes n'a été détectée sur ce diagramme."
            })
            logger.info("Aucune relation détectée")
        
        return relations
    
    def format_relations_output(self, relations: List[Dict]) -> str:
        """
        Formate la sortie des relations pour l'affichage.
        
        Args:
            relations: Liste des dictionnaires de relations
            
        Returns:
            Texte formaté pour l'affichage
        """
        if not relations:
            return "Aucune relation détectée."
        
        # Compter les types de relations valides
        relation_counts = {}
        valid_relations = [r for r in relations if not r.get('orphan', False) and 'relation_type' in r]
        
        for relation in valid_relations:
            rel_type = relation['relation_type']
            if rel_type in relation_counts:
                relation_counts[rel_type] += 1
            else:
                relation_counts[rel_type] = 1
        
        # Créer un résumé des relations
        summary_lines = []
        for rel_type, count in relation_counts.items():
            summary_lines.append(f"• {count} relation(s) de type {rel_type}")
        
        # Formater toutes les phrases de relations
        relation_lines = []
        for relation in relations:
            if 'relation_phrase' in relation:
                relation_lines.append(f"• {relation['relation_phrase']}")
        
        # Combiner le résumé et les phrases détaillées
        if summary_lines:
            output = "\n\n----- RÉSUMÉ DES RELATIONS -----\n" + "\n".join(summary_lines)
        else:
            output = ""
        
        output += "\n\n----- RELATIONS DÉTECTÉES -----\n" + "\n".join(relation_lines)
        
        return output


# Fonction pour convertir les résultats bruts des modèles au format attendu par le processeur
def convert_model_results(yolo_results, model_type):
    """
    Convertit les résultats bruts du modèle YOLO au format attendu par le processeur.
    
    Args:
        yolo_results: Résultats bruts du modèle YOLO
        model_type: Type de modèle ('model1' pour classes/flèches, 'model2' pour relations)
        
    Returns:
        Liste formatée des détections
    """
    formatted_results = []
    
    MODEL1_NAMES = {0: 'arrow', 1: 'class'}
    MODEL2_NAMES = {
        0: 'A', 
        1: 'C', 
        2: 'generalization', 
        3: 'endpoint', 
        4: 'one-way-association', 
        5: 'composition', 
        6: 'aggregation'
    }
    
    for result in yolo_results:
        if not hasattr(result, 'boxes') or not hasattr(result.boxes, 'xyxy'):
            continue
            
        boxes = result.boxes.xyxy.cpu().numpy()
        classes = result.boxes.cls.cpu().numpy()
        scores = result.boxes.conf.cpu().numpy()
        
        for i in range(len(boxes)):
            x1, y1, x2, y2 = map(int, boxes[i])
            class_index = int(classes[i])
            confidence = float(scores[i])
            
            if model_type == 'model1' and class_index in MODEL1_NAMES:
                class_name = MODEL1_NAMES[class_index]
                id_suffix = len([x for x in formatted_results if x['class_name'] == class_name]) + 1
                formatted_results.append({
                    'box': (x1, y1, x2, y2),
                    'class_name': class_name,
                    'id': f"{class_name}_{id_suffix}",
                    'label': f"{class_name} {id_suffix}",
                    'confidence': confidence
                })
            elif model_type == 'model2' and class_index in MODEL2_NAMES:
                class_name = MODEL2_NAMES[class_index]
                id_suffix = len([x for x in formatted_results if x['class_name'] == class_name]) + 1
                formatted_results.append({
                    'box': (x1, y1, x2, y2),
                    'class_name': class_name,
                    'id': f"{class_name}_{id_suffix}",
                    'label': f"{class_name} {id_suffix}",
                    'confidence': confidence
                })
    return formatted_results

# Fonction principale pour traiter les détections et extraire les relations
def process_diagram_relations(model1_results, model2_results, extracted_texts, distance_threshold=100):
    """
    Traite un diagramme UML et extrait les relations entre classes.
    
    Args:
        model1_results: Résultats du modèle 1 (classes et flèches)
        model2_results: Résultats du modèle 2 (types de relations)
        extracted_texts: Dictionnaire des textes extraits des classes
        distance_threshold: Seuil de distance pour associer les objets
        
    Returns:
        Texte formaté décrivant les relations détectées
    """
    # Convertir les résultats bruts des modèles au format attendu
    formatted_model1 = convert_model_results(model1_results, 'model1')
    formatted_model2 = convert_model_results(model2_results, 'model2')
    
    # Initialiser le processeur de relations
    processor = RelationProcessor(distance_threshold=distance_threshold)
    
    # Traiter les détections
    relations = processor.process_detections(formatted_model1, formatted_model2, extracted_texts)
    
    # Formater la sortie
    output_text = processor.format_relations_output(relations)
    
    return output_text, relations

# Classe pour l'interprétation avancée des relations UML
class AdvancedRelationInterpreter:
    """
    Classe pour effectuer une interprétation avancée des relations détectées dans un diagramme UML.
    Fournit des fonctionnalités supplémentaires pour analyser les cas complexes.
    """
    
    def __init__(self, relation_processor=None):
        """
        Initialise l'interpréteur avancé de relations.
        
        Args:
            relation_processor: Instance de RelationProcessor à utiliser, ou None pour en créer un nouveau
        """
        self.processor = relation_processor if relation_processor else RelationProcessor()
        logger.info("AdvancedRelationInterpreter initialisé")
    
    def detect_overlapping_arrows(self, relations, threshold=20):
        """
        Détecte les flèches qui se chevauchent ou sont très proches.
        
        Args:
            relations: Liste des relations détectées
            threshold: Seuil de distance pour considérer un chevauchement
            
        Returns:
            Liste des groupes de flèches qui se chevauchent
        """
        overlapping_groups = []
        processed_arrows = set()
        
        # Filtrer les relations non orphelines (avec start_point et end_point)
        valid_relations = [r for r in relations if not r.get('orphan', False) 
                          and 'start_point' in r and 'end_point' in r]
        
        for i, rel1 in enumerate(valid_relations):
            if rel1['arrow_id'] in processed_arrows:
                continue
                
            current_group = [rel1]
            processed_arrows.add(rel1['arrow_id'])
            
            for j, rel2 in enumerate(valid_relations):
                if i == j or rel2['arrow_id'] in processed_arrows:
                    continue
                
                # Calculer la distance moyenne entre les deux flèches
                dist1 = self.processor.calculate_distance(rel1['start_point'], rel2['start_point'])
                dist2 = self.processor.calculate_distance(rel1['end_point'], rel2['end_point'])
                dist3 = self.processor.calculate_distance(rel1['start_point'], rel2['end_point'])
                dist4 = self.processor.calculate_distance(rel1['end_point'], rel2['start_point'])
                
                min_dist = min(dist1, dist2, dist3, dist4)
                
                # Si les flèches sont proches et partagent au moins une classe
                if min_dist <= threshold and (
                    self.processor.is_same_class(rel1.get('start_class'), rel2.get('start_class')) or
                    self.processor.is_same_class(rel1.get('start_class'), rel2.get('end_class')) or
                    self.processor.is_same_class(rel1.get('end_class'), rel2.get('start_class')) or
                    self.processor.is_same_class(rel1.get('end_class'), rel2.get('end_class'))
                ):
                    current_group.append(rel2)
                    processed_arrows.add(rel2['arrow_id'])
            
            if len(current_group) > 1:
                overlapping_groups.append(current_group)
        
        return overlapping_groups
    
    def analyze_class_connections(self, relations):
        """
        Analyse les connexions entre les classes et identifie les modèles de conception potentiels.
        
        Args:
            relations: Liste des relations détectées
            
        Returns:
            Dictionnaire des classes avec leurs connexions et statistiques
        """
        class_connections = {}
        
        # Filtrer les relations valides
        valid_relations = [r for r in relations if not r.get('orphan', False) 
                          and 'start_class_name' in r and 'end_class_name' in r]
        
        # Construire un graphe de connexions
        for relation in valid_relations:
            start_name = relation['start_class_name']
            end_name = relation['end_class_name']
            rel_type = relation['relation_type']
            
            # Initialiser les entrées si nécessaire
            if start_name not in class_connections:
                class_connections[start_name] = {
                    'outgoing': [],
                    'incoming': [],
                    'relation_counts': {}
                }
            
            if end_name not in class_connections:
                class_connections[end_name] = {
                    'outgoing': [],
                    'incoming': [],
                    'relation_counts': {}
                }
            
            # Ajouter les connexions
            class_connections[start_name]['outgoing'].append({
                'target': end_name,
                'relation_type': rel_type,
                'relation': relation
            })
            
            class_connections[end_name]['incoming'].append({
                'source': start_name,
                'relation_type': rel_type,
                'relation': relation
            })
            
            # Incrémenter les compteurs de relations
            if rel_type in class_connections[start_name]['relation_counts']:
                class_connections[start_name]['relation_counts'][rel_type] += 1
            else:
                class_connections[start_name]['relation_counts'][rel_type] = 1
        
        # Calculer des statistiques supplémentaires
        for class_name, data in class_connections.items():
            data['total_connections'] = len(data['outgoing']) + len(data['incoming'])
            data['is_leaf'] = len(data['outgoing']) == 0 and len(data['incoming']) > 0
            data['is_root'] = len(data['outgoing']) > 0 and len(data['incoming']) == 0
            data['is_isolated'] = len(data['outgoing']) == 0 and len(data['incoming']) == 0
            
            # Identifier les classes abstraites potentielles (ayant des généralisations sortantes)
            data['potential_abstract'] = any(conn['relation_type'] == 'generalization' 
                                            for conn in data['incoming'])
        
        return class_connections
    
    def identify_design_patterns(self, relations):
        """
        Tente d'identifier des motifs de conception potentiels dans le diagramme.
        
        Args:
            relations: Liste des relations détectées
            
        Returns:
            Liste des motifs de conception potentiels identifiés
        """
        # Créer un graphe des connexions entre classes
        class_connections = self.analyze_class_connections(relations)
        patterns = []
        
        # Recherche de motif d'héritage (hiérarchie de classes)
        inheritance_hierarchies = self._find_inheritance_hierarchies(class_connections)
        for hierarchy in inheritance_hierarchies:
            if len(hierarchy['subclasses']) >= 2:
                patterns.append({
                    'pattern': 'Hiérarchie d\'héritage',
                    'description': f"Classe de base '{hierarchy['base']}' avec sous-classes: {', '.join(hierarchy['subclasses'])}",
                    'classes': [hierarchy['base']] + hierarchy['subclasses']
                })
        
        # Recherche de motif Composite
        composites = self._find_composite_pattern(class_connections)
        patterns.extend(composites)
        
        # Recherche de relations bidirectionnelles (associations dans les deux sens)
        bidirectional = self._find_bidirectional_relations(class_connections)
        patterns.extend(bidirectional)
        
        return patterns
    
    def _find_inheritance_hierarchies(self, class_connections):
        """
        Recherche les hiérarchies d'héritage dans le diagramme.
        
        Args:
            class_connections: Dictionnaire des connexions entre classes
            
        Returns:
            Liste des hiérarchies d'héritage trouvées
        """
        hierarchies = []
        
        for class_name, data in class_connections.items():
            # Chercher les classes qui ont des généralisations entrantes (sont des classes de base)
            generalization_incoming = [conn for conn in data['incoming'] 
                                      if conn['relation_type'] == 'generalization']
            
            if generalization_incoming:
                # Cette classe est une base pour d'autres classes
                subclasses = [conn['source'] for conn in generalization_incoming]
                
                hierarchies.append({
                    'base': class_name,
                    'subclasses': subclasses
                })
        
        return hierarchies
    
    def _find_composite_pattern(self, class_connections):
        """
        Recherche le motif de conception Composite dans le diagramme.
        
        Args:
            class_connections: Dictionnaire des connexions entre classes
            
        Returns:
            Liste des motifs Composite trouvés
        """
        composites = []
        
        for class_name, data in class_connections.items():
            # Chercher les classes qui ont à la fois des compositions et des agrégations
            composition_outgoing = [conn for conn in data['outgoing'] 
                                  if conn['relation_type'] == 'composition']
            
            # Si la classe a au moins une composition sortante
            if composition_outgoing:
                # Vérifier si elle a aussi une relation réflexive ou une association avec elle-même
                has_reflexive = any(conn['target'] == class_name for conn in data['outgoing'])
                
                if has_reflexive:
                    composites.append({
                        'pattern': 'Composite potentiel',
                        'description': f"La classe '{class_name}' contient des objets du même type (réflexif) et d'autres types",
                        'classes': [class_name] + [conn['target'] for conn in composition_outgoing]
                    })
        
        return composites
    
    def _find_bidirectional_relations(self, class_connections):
        """
        Recherche les relations bidirectionnelles entre classes.
        
        Args:
            class_connections: Dictionnaire des connexions entre classes
            
        Returns:
            Liste des relations bidirectionnelles trouvées
        """
        bidirectional = []
        processed_pairs = set()
        
        for class1, data1 in class_connections.items():
            for out_conn in data1['outgoing']:
                class2 = out_conn.get('target')
                if class2 and (class1, class2) not in processed_pairs and (class2, class1) not in processed_pairs:
                    # Vérifier si class2 a aussi une connexion vers class1
                    if class2 in class_connections:
                        has_reverse = any(conn['target'] == class1 for conn in class_connections[class2]['outgoing'])
                        
                        if has_reverse:
                            bidirectional.append({
                                'pattern': 'Relation bidirectionnelle',
                                'description': f"Association bidirectionnelle entre '{class1}' et '{class2}'",
                                'classes': [class1, class2]
                            })
                            
                            processed_pairs.add((class1, class2))
        
        return bidirectional
    
    def suggest_code_elements(self, relations):
        """
        Suggère des éléments de code correspondant aux relations détectées.
        
        Args:
            relations: Liste des relations détectées
            
        Returns:
            Dictionnaire des suggestions de code par classe
        """
        suggestions = {}
        
        # Filtrer les relations valides
        valid_relations = [r for r in relations if not r.get('orphan', False)]
        
        for relation in valid_relations:
            start_name = relation.get('start_class_name')
            end_name = relation.get('end_class_name')
            rel_type = relation.get('relation_type')
            
            if not start_name or not end_name or not rel_type:
                continue
            
            # Initialiser les entrées si nécessaire
            if start_name not in suggestions:
                suggestions[start_name] = {
                    'attributes': [],
                    'methods': [],
                    'inheritance': [],
                    'imports': []
                }
            
            if end_name not in suggestions:
                suggestions[end_name] = {
                    'attributes': [],
                    'methods': [],
                    'inheritance': [],
                    'imports': []
                }
            
            # Générer des suggestions selon le type de relation
            if rel_type == 'generalization':
                # Classe enfant hérite de la classe parent
                suggestions[end_name]['inheritance'].append(start_name)
                suggestions[end_name]['imports'].append(f"from {start_name.lower()} import {start_name}")
                
            elif rel_type == 'composition':
                # Composition: la classe contient un objet comme partie intégrante
                attribute_name = end_name.lower()
                suggestions[start_name]['attributes'].append(f"{attribute_name}: {end_name}")
                suggestions[start_name]['methods'].append(f"def __init__(self):\n    self.{attribute_name} = {end_name}()")
                suggestions[start_name]['imports'].append(f"from {end_name.lower()} import {end_name}")
                
            elif rel_type == 'aggregation':
                # Agrégation: la classe contient une référence à un objet externe
                attribute_name = end_name.lower() + "_list"
                suggestions[start_name]['attributes'].append(f"{attribute_name}: List[{end_name}]")
                suggestions[start_name]['methods'].append(f"def add_{end_name.lower()}(self, {end_name.lower()}: {end_name}):\n    self.{attribute_name}.append({end_name.lower()})")
                suggestions[start_name]['imports'].extend([
                    "from typing import List",
                    f"from {end_name.lower()} import {end_name}"
                ])
                
            elif rel_type == 'one-way-association':
                # Association unidirectionnelle
                attribute_name = end_name.lower()
                suggestions[start_name]['attributes'].append(f"{attribute_name}: {end_name}")
                suggestions[start_name]['imports'].append(f"from {end_name.lower()} import {end_name}")
                
            elif rel_type == 'association' or rel_type == 'endpoint':
                # Association bidirectionnelle
                attribute_name = end_name.lower()
                suggestions[start_name]['attributes'].append(f"{attribute_name}: {end_name}")
                suggestions[start_name]['imports'].append(f"from {end_name.lower()} import {end_name}")
                
                # Ajouter aussi l'association dans l'autre sens
                reverse_attribute = start_name.lower()
                suggestions[end_name]['attributes'].append(f"{reverse_attribute}: {start_name}")
                suggestions[end_name]['imports'].append(f"from {start_name.lower()} import {start_name}")
        
        # Éliminer les doublons et trier les suggestions
        for class_name, class_data in suggestions.items():
            for key in class_data:
                class_data[key] = sorted(list(set(class_data[key])))
        
        return suggestions
    
    def generate_mermaid_from_relations(self, relations):
        """
        Génère un diagramme Mermaid à partir des relations détectées.
        
        Args:
            relations: Liste des relations détectées
            
        Returns:
            Code Mermaid pour représenter le diagramme de classes
        """
        mermaid_lines = ["classDiagram"]
        relation_lines = []
        classes = set()
        
        # Filtrer les relations valides
        valid_relations = [r for r in relations if not r.get('orphan', False) 
                          and 'start_class_name' in r and 'end_class_name' in r]
        
        # Générer les lignes de relations
        for relation in valid_relations:
            start_name = relation['start_class_name']
            end_name = relation['end_class_name']
            rel_type = relation['relation_type']
            
            classes.add(start_name)
            classes.add(end_name)
            
            # Mapper le type de relation au format Mermaid
            if rel_type == 'generalization':
                arrow = "<|--"  # Héritage
            elif rel_type == 'composition':
                arrow = "*--"   # Composition
            elif rel_type == 'aggregation':
                arrow = "o--"   # Agrégation
            elif rel_type == 'one-way-association':
                arrow = "-->"   # Association unidirectionnelle
            elif rel_type == 'endpoint':
                arrow = "--"    # Association avec point d'extrémité
            else:  # association par défaut
                arrow = "--"    # Association simple
            
            # Générer la ligne Mermaid
            relation_line = f"{start_name} {arrow} {end_name}"
            relation_lines.append(relation_line)
        
        # Ajouter les classes isolées
        for class_name in classes:
            mermaid_lines.append(f"class {class_name}")
        
        # Ajouter les relations
        mermaid_lines.extend(relation_lines)
        
        return "\n".join(mermaid_lines)
    
    def generate_summary_report(self, relations):
        """
        Génère un rapport détaillé résumant les relations détectées.
        
        Args:
            relations: Liste des relations détectées
            
        Returns:
            Texte formaté avec le résumé des relations
        """
        # Filtrer les relations valides
        valid_relations = [r for r in relations if not r.get('orphan', False)]
        orphan_relations = [r for r in relations if r.get('orphan', False)]
        
        # Compter les types de relations
        relation_counts = {}
        for relation in valid_relations:
            rel_type = relation.get('relation_type', 'unknown')
            if rel_type in relation_counts:
                relation_counts[rel_type] += 1
            else:
                relation_counts[rel_type] = 1
        
        # Construire le rapport
        report_lines = []
        
        # En-tête
        report_lines.append("====== RAPPORT D'ANALYSE DES RELATIONS UML ======")
        report_lines.append("")
        
        # Statistiques générales
        report_lines.append("=== STATISTIQUES GÉNÉRALES ===")
        report_lines.append(f"Total des relations détectées: {len(relations)}")
        report_lines.append(f"Relations valides: {len(valid_relations)}")
        report_lines.append(f"Relations orphelines ou incomplètes: {len(orphan_relations)}")
        report_lines.append("")
        
        # Détail des types de relations
        report_lines.append("=== DÉTAIL DES TYPES DE RELATIONS ===")
        for rel_type, count in relation_counts.items():
            description = self.processor.RELATION_DESCRIPTIONS.get(rel_type, rel_type)
            report_lines.append(f"• {rel_type} ({description}): {count}")
        report_lines.append("")
        
        # Identification des classes
        class_names = set()
        for relation in valid_relations:
            if 'start_class_name' in relation:
                class_names.add(relation['start_class_name'])
            if 'end_class_name' in relation:
                class_names.add(relation['end_class_name'])
        
        report_lines.append("=== CLASSES IDENTIFIÉES ===")
        for class_name in sorted(class_names):
            report_lines.append(f"• {class_name}")
        report_lines.append("")
        
        # Relations par classe
        report_lines.append("=== RELATIONS PAR CLASSE ===")
        class_connections = self.analyze_class_connections(relations)
        
        for class_name in sorted(class_names):
            if class_name in class_connections:
                data = class_connections[class_name]
                report_lines.append(f"\n--- Classe: {class_name} ---")
                
                if data['incoming']:
                    report_lines.append("Relations entrantes:")
                    for conn in data['incoming']:
                        rel_type = conn['relation_type']
                        source = conn['source']
                        description = self.processor.RELATION_DESCRIPTIONS.get(rel_type, rel_type)
                        report_lines.append(f"  • {source} → {class_name} ({description})")
                
                if data['outgoing']:
                    report_lines.append("Relations sortantes:")
                    for conn in data['outgoing']:
                        rel_type = conn['relation_type']
                        target = conn['target']
                        description = self.processor.RELATION_DESCRIPTIONS.get(rel_type, rel_type)
                        report_lines.append(f"  • {class_name} → {target} ({description})")
        
        # Détection de motifs de conception
        patterns = self.identify_design_patterns(relations)
        if patterns:
            report_lines.append("\n=== MOTIFS DE CONCEPTION POTENTIELS ===")
            for pattern in patterns:
                report_lines.append(f"• {pattern['pattern']}: {pattern['description']}")
        
        # Recommandations et anomalies
        report_lines.append("\n=== RECOMMANDATIONS ET ANOMALIES ===")
        
        # Vérifier s'il y a des relations orphelines
        if orphan_relations:
            report_lines.append("⚠️ Relations orphelines détectées:")
            for relation in orphan_relations:
                report_lines.append(f"  • {relation.get('relation_phrase', 'Relation orpheline')}")
        
        # Vérifier les incohérences de hiérarchie (cycles d'héritage)
        inheritance_cycles = self._check_inheritance_cycles(class_connections)
        if inheritance_cycles:
            report_lines.append("⚠️ Cycles d'héritage détectés (erreur de conception):")
            for cycle in inheritance_cycles:
                report_lines.append(f"  • {' → '.join(cycle)} → {cycle[0]}")
        
        return "\n".join(report_lines)
    
    def _check_inheritance_cycles(self, class_connections):
        """
        Vérifie s'il y a des cycles d'héritage dans le diagramme.
        
        Args:
            class_connections: Dictionnaire des connexions entre classes
            
        Returns:
            Liste des cycles d'héritage trouvés
        """
        cycles = []
        
        def dfs(node, visited, path):
            visited[node] = True
            path.append(node)
            
            if node in class_connections:
                for conn in class_connections[node]['outgoing']:
                    if conn['relation_type'] == 'generalization':
                        next_node = conn['target']
                        if next_node in path:
                            # Cycle détecté
                            cycle_start = path.index(next_node)
                            cycles.append(path[cycle_start:])
                        elif next_node not in visited or not visited[next_node]:
                            dfs(next_node, visited, path)
            
            path.pop()
        
        for class_name in class_connections:
            visited = {name: False for name in class_connections}
            dfs(class_name, visited, [])
        
        return cycles

# Fonctions utilitaires supplémentaires

def filter_duplicate_relations(relations, distance_threshold=10):
    """
    Filtre les relations dupliquées en fonction de la proximité des flèches.
    
    Args:
        relations: Liste des relations détectées
        distance_threshold: Seuil de distance pour considérer que deux flèches sont identiques
        
    Returns:
        Liste des relations filtrées
    """
    # Créer une liste pour les relations filtrées
    filtered_relations = []
    processed_ids = set()
    
    for rel1 in relations:
        if rel1.get('orphan', False) or 'arrow_id' not in rel1 or rel1['arrow_id'] in processed_ids:
            continue
        
        # Stocker la relation actuelle comme la meilleure
        best_relation = rel1
        processed_ids.add(rel1['arrow_id'])
        
        # Comparer avec les autres relations pour trouver les doublons potentiels
        for rel2 in relations:
            if rel2.get('orphan', False) or 'arrow_id' not in rel2 or rel2['arrow_id'] in processed_ids:
                continue
            
            # Vérifier si les relations concernent les mêmes classes
            same_classes = (
                rel1.get('start_class_name') == rel2.get('start_class_name') and
                rel1.get('end_class_name') == rel2.get('end_class_name')
            )
            
            # Vérifier si les flèches sont proches
            close_arrows = False
            if 'start_point' in rel1 and 'end_point' in rel1 and 'start_point' in rel2 and 'end_point' in rel2:
                dist1 = math.sqrt((rel1['start_point'][0] - rel2['start_point'][0])**2 + 
                                 (rel1['start_point'][1] - rel2['start_point'][1])**2)
                dist2 = math.sqrt((rel1['end_point'][0] - rel2['end_point'][0])**2 + 
                                 (rel1['end_point'][1] - rel2['end_point'][1])**2)
                
                close_arrows = dist1 < distance_threshold and dist2 < distance_threshold
            
            # Si c'est potentiellement un doublon
            if same_classes and close_arrows:
                processed_ids.add(rel2['arrow_id'])
                
                # Garder la relation avec la meilleure confiance
                if rel2.get('confidence', 0) > best_relation.get('confidence', 0):
                    best_relation = rel2
        
        filtered_relations.append(best_relation)
    
    # Ajouter les relations orphelines à la fin
    for relation in relations:
        if relation.get('orphan', True) and relation not in filtered_relations:
            filtered_relations.append(relation)
    
    return filtered_relations

def convert_arrows_to_coordinates(arrows):
    """
    Convertit les flèches en coordonnées plus précises en fonction de leur orientation.
    
    Args:
        arrows: Liste des objets flèches
        
    Returns:
        Liste des flèches avec coordonnées ajustées
    """
    updated_arrows = []
    processor = RelationProcessor()
    
    for arrow in arrows:
        box = arrow.get('box')
        if not box:
            updated_arrows.append(arrow)
            continue
        
        # Déterminer les extrémités
        start_point, end_point = processor.get_arrow_endpoints(box)
        
        # Déterminer l'orientation
        orientation = processor.get_arrow_orientation(box)
        
        # Ajuster les coordonnées selon l'orientation
        adjusted_arrow = arrow.copy()
        adjusted_arrow['start_point'] = start_point
        adjusted_arrow['end_point'] = end_point
        adjusted_arrow['orientation'] = orientation
        
        updated_arrows.append(adjusted_arrow)
    
    return updated_arrows

def generate_code_from_relation(relation, indent=4):
    """
    Génère du code Python à partir d'une relation détectée.
    
    Args:
        relation: Dictionnaire décrivant la relation
        indent: Nombre d'espaces pour l'indentation
        
    Returns:
        Code Python généré
    """
    if relation.get('orphan', False) or 'relation_type' not in relation:
        return ""
    
    start_class = relation.get('start_class_name', 'ClassA')
    end_class = relation.get('end_class_name', 'ClassB')
    rel_type = relation.get('relation_type')
    
    indentation = ' ' * indent
    code_lines = []
    
    if rel_type == 'generalization':
        # Héritage
        code_lines.append(f"class {end_class}({start_class}):")
        code_lines.append(f"{indentation}def __init__(self):")
        code_lines.append(f"{indentation}{indentation}super().__init__()")
        code_lines.append(f"{indentation}{indentation}# Initialisation spécifique à {end_class}")
    
    elif rel_type == 'composition':
        # Composition
        attr_name = end_class.lower()
        code_lines.append(f"class {start_class}:")
        code_lines.append(f"{indentation}def __init__(self):")
        code_lines.append(f"{indentation}{indentation}self.{attr_name} = {end_class}()")
        code_lines.append("")
        code_lines.append(f"class {end_class}:")
        code_lines.append(f"{indentation}def __init__(self):")
        code_lines.append(f"{indentation}{indentation}pass")
    
    elif rel_type == 'aggregation':
        # Agrégation
        attr_name = end_class.lower() + "_list"
        code_lines.append(f"class {start_class}:")
        code_lines.append(f"{indentation}def __init__(self):")
        code_lines.append(f"{indentation}{indentation}self.{attr_name} = []")
        code_lines.append("")
        code_lines.append(f"{indentation}def add_{end_class.lower()}(self, {end_class.lower()}_item):")
        code_lines.append(f"{indentation}{indentation}self.{attr_name}.append({end_class.lower()}_item)")
        code_lines.append("")
        code_lines.append(f"class {end_class}:")
        code_lines.append(f"{indentation}def __init__(self):")
        code_lines.append(f"{indentation}{indentation}pass")    

    elif rel_type == 'one-way-association':
        # Association unidirectionnelle
        attr_name = end_class.lower()
        code_lines.append(f"class {start_class}:")
        code_lines.append(f"{indentation}def __init__(self, {attr_name}: {end_class}):")
        code_lines.append(f"{indentation}{indentation}self.{attr_name} = {attr_name}")
        code_lines.append("")
        code_lines.append(f"class {end_class}:")
        code_lines.append(f"{indentation}def __init__(self):")
        code_lines.append(f"{indentation}{indentation}pass")

    elif rel_type in ['association', 'endpoint']:
        # Association bidirectionnelle
        attr_name1 = end_class.lower()
        attr_name2 = start_class.lower()
        code_lines.append(f"class {start_class}:")
        code_lines.append(f"{indentation}def __init__(self):")
        code_lines.append(f"{indentation}{indentation}self.{attr_name1} = None")
        code_lines.append("")
        code_lines.append(f"class {end_class}:")
        code_lines.append(f"{indentation}def __init__(self):")
        code_lines.append(f"{indentation}{indentation}self.{attr_name2} = None")
        code_lines.append("")
        code_lines.append(f"# Pour établir la relation:")
        code_lines.append(f"a = {start_class}()")
        code_lines.append(f"b = {end_class}()")
        code_lines.append(f"a.{attr_name1} = b")
        code_lines.append(f"b.{attr_name2} = a")

    else:
        return ""

    return "\n".join(code_lines)


def visualize_relations_graph(relations):
    """
    Génère une visualisation graphique des relations entre classes.
    
    Args:
        relations: Liste des relations détectées
        
    Returns:
        Objet graphique pouvant être affiché ou sauvegardé
    """
    try:
        import networkx as nx
        import matplotlib.pyplot as plt
    except ImportError:
        logger.error("Les bibliothèques networkx et matplotlib sont requises pour la visualisation")
        return None

    # Créer un graphe orienté
    G = nx.DiGraph()
    
    # Ajouter les nœuds et les arêtes
    for relation in relations:
        if relation.get('orphan', False):
            continue
            
        start_class = relation.get('start_class_name', 'Unknown')
        end_class = relation.get('end_class_name', 'Unknown')
        rel_type = relation.get('relation_type', 'association')
        
        G.add_node(start_class)
        G.add_node(end_class)
        
        # Définir le style de l'arête selon le type de relation
        edge_style = {}
        if rel_type == 'generalization':
            edge_style['style'] = 'solid'
            edge_style['arrowstyle'] = '->'
            edge_style['color'] = 'black'
        elif rel_type == 'composition':
            edge_style['style'] = 'dashed'
            edge_style['arrowstyle'] = '-|>'
            edge_style['color'] = 'red'
        elif rel_type == 'aggregation':
            edge_style['style'] = 'dashed'
            edge_style['arrowstyle'] = '-o'
            edge_style['color'] = 'blue'
        elif rel_type == 'one-way-association':
            edge_style['style'] = 'solid'
            edge_style['arrowstyle'] = '->'
            edge_style['color'] = 'green'
        else:  # association par défaut
            edge_style['style'] = 'solid'
            edge_style['arrowstyle'] = '-'
            edge_style['color'] = 'gray'
        
        G.add_edge(start_class, end_class, **edge_style)
    
    # Dessiner le graphe
    pos = nx.spring_layout(G)
    plt.figure(figsize=(12, 8))
    
    # Dessiner les nœuds
    nx.draw_networkx_nodes(G, pos, node_size=2000, node_color='lightblue', alpha=0.9)
    
    # Dessiner les étiquettes
    nx.draw_networkx_labels(G, pos, font_size=12, font_weight='bold')
    
    # Dessiner les arêtes avec différents styles
    for edge in G.edges(data=True):
        nx.draw_networkx_edges(
            G, pos, 
            edgelist=[(edge[0], edge[1])],
            arrows=True,
            edge_color=edge[2].get('color', 'gray'),
            style=edge[2].get('style', 'solid'),
            arrowstyle=edge[2].get('arrowstyle', '-'),
            width=2
        )
    
    # Ajouter une légende
    legend_elements = [
        plt.Line2D([0], [0], color='black', lw=2, label='Héritage'),
        plt.Line2D([0], [0], color='red', lw=2, linestyle='dashed', label='Composition'),
        plt.Line2D([0], [0], color='blue', lw=2, linestyle='dashed', label='Agrégation'),
        plt.Line2D([0], [0], color='green', lw=2, label='Association unidir.'),
        plt.Line2D([0], [0], color='gray', lw=2, label='Association')
    ]
    plt.legend(handles=legend_elements, loc='upper right')
    
    plt.title("Diagramme des relations entre classes", fontsize=16)
    plt.axis('off')
    
    return plt


if __name__ == "__main__":
    # Exemple d'utilisation des classes et fonctions
    
    # Créer des données de test
    test_model1_results = [
        {'box': (100, 100, 200, 150), 'class_name': 'class', 'id': 'class_1', 'label': 'ClassA'},
        {'box': (400, 100, 500, 150), 'class_name': 'class', 'id': 'class_2', 'label': 'ClassB'},
        {'box': (250, 125, 350, 130), 'class_name': 'arrow', 'id': 'arrow_1', 'label': 'Arrow1'}
    ]
    
    test_model2_results = [
        {'box': (280, 120, 320, 130), 'class_name': 'generalization', 'id': 'gen_1', 'label': 'Generalization1'}
    ]
    
    test_extracted_texts = {
        'class_1': "NOM_CLASSE: ClassA\nATTRIBUTS:\n- attr1: int\nMETHODES:\n+ method1()",
        'class_2': "NOM_CLASSE: ClassB\nATTRIBUTS:\n- attr2: str\nMETHODES:\n+ method2()"
    }
    
    # Traiter les relations
    processor = RelationProcessor()
    relations = processor.process_detections(test_model1_results, test_model2_results, test_extracted_texts)
    
    # Afficher les résultats
    print("\n=== RELATIONS DÉTECTÉES ===")
    for relation in relations:
        print(relation.get('relation_phrase', 'Relation sans description'))
    
    # Utiliser l'interpréteur avancé
    interpreter = AdvancedRelationInterpreter(processor)
    
    # Générer un rapport
    print("\n=== RAPPORT D'ANALYSE ===")
    print(interpreter.generate_summary_report(relations))
    
    # Générer du code
    print("\n=== CODE GÉNÉRÉ ===")
    for relation in relations:
        if not relation.get('orphan', False):
            print(generate_code_from_relation(relation))
            print()
    
    # Visualiser le graphe (si les bibliothèques sont disponibles)
    plt = visualize_relations_graph(relations)
    if plt:
        plt.show()