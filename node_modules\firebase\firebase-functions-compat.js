((e,t)=>{"object"==typeof exports&&"undefined"!=typeof module?t(require("@firebase/app-compat"),require("@firebase/app")):"function"==typeof define&&define.amd?define(["@firebase/app-compat","@firebase/app"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).firebase,e.firebase.INTERNAL.modularAPIs)})(this,function(j,R){try{!(function(){function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var t,n=e(j);class s extends Error{constructor(e,t,n){super(t),this.code=e,this.customData=n,this.name="FirebaseError",Object.setPrototypeOf(this,s.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,r.prototype.create)}}class r{constructor(e,t,n){this.service=e,this.serviceName=t,this.errors=n}create(e,...t){var r,n=t[0]||{},a=this.service+"/"+e,i=this.errors[e],i=i?(r=n,i.replace(o,(e,t)=>{var n=r[t];return null!=n?String(n):`<${t}?>`})):"Error",i=this.serviceName+`: ${i} (${a}).`;return new s(a,i,n)}}let o=/\{\$([^}]+)}/g;function c(e){return e&&e._delegate?e._delegate:e}class a{constructor(e,t,n){this.name=e,this.instanceFactory=t,this.type=n,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}let i="type.googleapis.com/google.protobuf.Int64Value",l="type.googleapis.com/google.protobuf.UInt64Value";function u(e,t){var n,r={};for(n in e)e.hasOwnProperty(n)&&(r[n]=t(e[n]));return r}function d(e){if(null==e)return null;if("number"==typeof(e=e instanceof Number?e.valueOf():e)&&isFinite(e))return e;if(!0===e||!1===e)return e;if("[object String]"===Object.prototype.toString.call(e))return e;if(e instanceof Date)return e.toISOString();if(Array.isArray(e))return e.map(e=>d(e));if("function"==typeof e||"object"==typeof e)return u(e,e=>d(e));throw new Error("Data cannot be encoded in JSON: "+e)}function h(e){if(null==e)return e;if(e["@type"])switch(e["@type"]){case i:case l:var t=Number(e.value);if(isNaN(t))throw new Error("Data cannot be decoded from JSON: "+e);return t;default:throw new Error("Data cannot be decoded from JSON: "+e)}return Array.isArray(e)?e.map(e=>h(e)):"function"==typeof e||"object"==typeof e?u(e,e=>h(e)):e}let p="functions",m={OK:"ok",CANCELLED:"cancelled",UNKNOWN:"unknown",INVALID_ARGUMENT:"invalid-argument",DEADLINE_EXCEEDED:"deadline-exceeded",NOT_FOUND:"not-found",ALREADY_EXISTS:"already-exists",PERMISSION_DENIED:"permission-denied",UNAUTHENTICATED:"unauthenticated",RESOURCE_EXHAUSTED:"resource-exhausted",FAILED_PRECONDITION:"failed-precondition",ABORTED:"aborted",OUT_OF_RANGE:"out-of-range",UNIMPLEMENTED:"unimplemented",INTERNAL:"internal",UNAVAILABLE:"unavailable",DATA_LOSS:"data-loss"};class f extends s{constructor(e,t,n){super(p+"/"+e,t||""),this.details=n,Object.setPrototypeOf(this,f.prototype)}}function g(e,t){let n=(e=>{if(200<=e&&e<300)return"ok";switch(e){case 0:return"internal";case 400:return"invalid-argument";case 401:return"unauthenticated";case 403:return"permission-denied";case 404:return"not-found";case 409:return"aborted";case 429:return"resource-exhausted";case 499:return"cancelled";case 500:return"internal";case 501:return"unimplemented";case 503:return"unavailable";case 504:return"deadline-exceeded"}return"unknown"})(e),r=n,a=void 0;try{var i=t&&t.error;if(i){let e=i.status;if("string"==typeof e){if(!m[e])return new f("internal","internal");n=m[e],r=e}var s=i.message;"string"==typeof s&&(r=s),void 0!==(a=i.details)&&(a=h(a))}}catch(e){}return"ok"===n?null:new f(n,r,a)}class v{constructor(e,t,n,r){this.app=e,this.auth=null,this.messaging=null,this.appCheck=null,this.serverAppAppCheckToken=null,R._isFirebaseServerApp(e)&&e.settings.appCheckToken&&(this.serverAppAppCheckToken=e.settings.appCheckToken),this.auth=t.getImmediate({optional:!0}),this.messaging=n.getImmediate({optional:!0}),this.auth||t.get().then(e=>this.auth=e,()=>{}),this.messaging||n.get().then(e=>this.messaging=e,()=>{}),this.appCheck||null!=r&&r.get().then(e=>this.appCheck=e,()=>{})}async getAuthToken(){if(this.auth)try{var e=await this.auth.getToken();return null==e?void 0:e.accessToken}catch(e){}}async getMessagingToken(){if(this.messaging&&"Notification"in self&&"granted"===Notification.permission)try{return await this.messaging.getToken()}catch(e){}}async getAppCheckToken(e){var t;return this.serverAppAppCheckToken||(!this.appCheck||(t=e?await this.appCheck.getLimitedUseToken():await this.appCheck.getToken()).error?null:t.token)}async getContext(e){return{authToken:await this.getAuthToken(),messagingToken:await this.getMessagingToken(),appCheckToken:await this.getAppCheckToken(e)}}}let w="us-central1",y=/^data: (.*?)(?:\n|$)/;class k{constructor(e,t,n,r,a=w,i=(...e)=>fetch(...e)){this.app=e,this.fetchImpl=i,this.emulatorOrigin=null,this.contextProvider=new v(e,t,n,r),this.cancelAllRequests=new Promise(e=>{this.deleteService=()=>Promise.resolve(e())});try{var s=new URL(a);this.customDomain=s.origin+("/"===s.pathname?"":s.pathname),this.region=w}catch(e){this.customDomain=null,this.region=a}}_delete(){return this.deleteService()}_url(e){var t=this.app.options.projectId;return null!==this.emulatorOrigin?`${this.emulatorOrigin}/${t}/${this.region}/`+e:null!==this.customDomain?this.customDomain+"/"+e:`https://${this.region}-${t}.cloudfunctions.net/`+e}}function b(a,i,s){var e=e=>{return e=e,n=s||{},r=(t=a)._url(i),E(t,r,e,n);var t,n,r};return e.stream=(e,t)=>{return e=e,t=t,r=(n=a)._url(i),I(n,r,e,t||{});var n,r},e}async function T(e,t){var n={},r=await e.contextProvider.getContext(t.limitedUseAppCheckTokens);return r.authToken&&(n.Authorization="Bearer "+r.authToken),r.messagingToken&&(n["Firebase-Instance-ID-Token"]=r.messagingToken),null!==r.appCheckToken&&(n["X-Firebase-AppCheck"]=r.appCheckToken),n}async function E(e,t,n,r){var a={data:n=d(n)},i=await T(e,r),s=(n=>{let r=null;return{promise:new Promise((e,t)=>{r=setTimeout(()=>{t(new f("deadline-exceeded","deadline-exceeded"))},n)}),cancel:()=>{r&&clearTimeout(r)}}})(r.timeout||7e4),a=await Promise.race([(async(e,t,n,r)=>{n["Content-Type"]="application/json";let a;try{a=await r(e,{method:"POST",body:JSON.stringify(t),headers:n})}catch(e){return{status:0,json:null}}let i=null;try{i=await a.json()}catch(e){}return{status:a.status,json:i}})(t,a,i,e.fetchImpl),s.promise,e.cancelAllRequests]);if(s.cancel(),!a)throw new f("cancelled","Firebase Functions instance was deleted.");i=g(a.status,a.json);if(i)throw i;if(!a.json)throw new f("internal","Response is not valid JSON object.");let o=a.json.data;if(void 0===(o=void 0===o?a.json.result:o))throw new f("internal","Response is missing data field.");return{data:h(o)}}async function I(e,t,n,r){var a={data:n=d(n)},i=await T(e,r);i["Content-Type"]="application/json",i.Accept="text/event-stream";let s;try{s=await e.fetchImpl(t,{method:"POST",body:JSON.stringify(a),headers:i,signal:null==r?void 0:r.signal})}catch(e){if(e instanceof Error&&"AbortError"===e.name){let e=new f("cancelled","Request was cancelled.");return{data:Promise.reject(e),stream:{[Symbol.asyncIterator](){return{next(){return Promise.reject(e)}}}}}}let t=g(0,null);return{data:Promise.reject(t),stream:{[Symbol.asyncIterator](){return{next(){return Promise.reject(t)}}}}}}let o,c;a=new Promise((e,t)=>{o=e,c=t}),null!=(i=null==r?void 0:r.signal)&&i.addEventListener("abort",()=>{var e=new f("cancelled","Request was cancelled.");c(e)}),i=s.body.getReader();let l=((s,i,o,c)=>{let l=(e,t)=>{var n=e.match(y);if(n){n=n[1];try{var r,a=JSON.parse(n);"result"in a?i(h(a.result)):"message"in a?t.enqueue(h(a.message)):"error"in a&&(r=g(0,a),t.error(r),o(r))}catch(e){e instanceof f&&(t.error(e),o(e))}}},u=new TextDecoder;return new ReadableStream({start(a){let i="";return async function n(){if(null!=c&&c.aborted){let e=new f("cancelled","Request was cancelled");return a.error(e),o(e),Promise.resolve()}try{let{value:t,done:e}=await s.read();if(e)i.trim()&&l(i.trim(),a),a.close();else{if(null==c||!c.aborted){let e=(i+=u.decode(t,{stream:!0})).split("\n");i=e.pop()||"";for(var r of e)r.trim()&&l(r.trim(),a);return n()}{let e=new f("cancelled","Request was cancelled");a.error(e),o(e),void await s.cancel()}}}catch(e){let t=e instanceof f?e:g(0,null);a.error(t),o(t)}}()},cancel(){return s.cancel()}})})(i,o,c,null==r?void 0:r.signal);return{stream:{[Symbol.asyncIterator](){let n=l.getReader();return{async next(){var{value:e,done:t}=await n.read();return{value:e,done:t}},async return(){return await n.cancel(),{done:!0,value:void 0}}}}},data:a}}let A="@firebase/functions",N="0.12.3";function C(e,t,n){c(e).emulatorOrigin=`http://${t}:`+n}function P(e,t,n){return r=c(e),a=t,i=n,(s=e=>E(r,a,e,i||{})).stream=(e,t)=>I(r,a,e,t||{}),s;var r,a,i,s}R._registerComponent(new a(p,(e,{instanceIdentifier:t})=>{var n=e.getProvider("app").getImmediate(),r=e.getProvider("auth-internal"),a=e.getProvider("messaging-internal"),i=e.getProvider("app-check-internal");return new k(n,r,a,i,t)},"PUBLIC").setMultipleInstances(!0)),R.registerVersion(A,N,t),R.registerVersion(A,N,"esm2017");var O;class S{constructor(e,t){this.app=e,this._delegate=t,this._region=this._delegate.region,this._customDomain=this._delegate.customDomain}httpsCallable(e,t){return b(c(this._delegate),e,t)}httpsCallableFromURL(e,t){return P(this._delegate,e,t)}useFunctionsEmulator(e){var t=e.match("[a-zA-Z]+://([a-zA-Z0-9.-]+)(?::([0-9]+))?");if(null==t)throw new s("functions","No origin provided to useFunctionsEmulator()");if(null==t[2])throw new s("functions","Port missing in origin provided to useFunctionsEmulator()");return C(this._delegate,t[1],Number(t[2]))}useEmulator(e,t){return C(this._delegate,e,t)}}let D="us-central1",_=(e,{instanceIdentifier:t})=>{var n=e.getProvider("app-compat").getImmediate(),r=e.getProvider("functions").getImmediate({identifier:null!=t?t:D});return new S(n,r)};O={Functions:S},n.default.INTERNAL.registerComponent(new a("functions-compat",_,"PUBLIC").setServiceProps(O).setMultipleInstances(!0)),n.default.registerVersion("@firebase/functions-compat","0.3.20")}).apply(this,arguments)}catch(e){throw console.error(e),new Error("Cannot instantiate firebase-functions-compat.js - be sure to load firebase-app.js first.")}});
//# sourceMappingURL=firebase-functions-compat.js.map
