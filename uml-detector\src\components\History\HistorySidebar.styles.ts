import { CSSProperties } from 'react';

export const sidebarStyles = {
  container: (isOpen: boolean, darkMode: boolean): CSSProperties => ({
    width: isOpen ? "240px" : "0px",
    height: "100vh",
    position: "fixed",
    left: 0,
    top: 0,
    backgroundColor: darkMode ? "#1e293b" : "#f8fafc",
    borderRight: isOpen ? "1px solid rgba(148, 163, 184, 0.15)" : "none",
    transition: "width 0.3s ease",
    overflow: "hidden",
    zIndex: 10
  }),

  loginPrompt: {
    padding: "1rem",
    textAlign: "center" as const
  },

  header: {
    padding: "1.25rem 1rem",
    borderBottom: "1px solid rgba(148, 163, 184, 0.15)"
  },

  title: {
    margin: "0 0 12px 0",
    fontSize: "1rem",
    fontWeight: "600"
  },

  searchContainer: {
    position: "relative" as const,
    marginBottom: "12px"
  },

  searchInput: (darkMode: boolean): CSSProperties => ({
    width: "100%",
    padding: "0.5rem 0.5rem 0.5rem 2rem",
    border: "1px solid rgba(148, 163, 184, 0.2)",
    borderRadius: "6px",
    backgroundColor: darkMode ? "#334155" : "#ffffff",
    color: darkMode ? "#e2e8f0" : "#334155",
    fontSize: "0.875rem",
    fontWeight: "500",
    boxSizing: "border-box" as const
  }),

  searchIcon: {
    position: "absolute" as const,
    left: "12px",
    top: "50%",
    transform: "translateY(-50%)",
    width: "16px",
    height: "16px",
    opacity: 0.5
  },

  buttonContainer: {
    display: "flex",
    flexDirection: "column" as const,
    gap: "8px"
  },

  deleteAllButton: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center" as const,
    gap: "6px",
    padding: "0.5rem",
    backgroundColor: "rgba(239, 68, 68, 0.1)",
    color: "#ef4444",
    border: "1px solid rgba(239, 68, 68, 0.2)",
    borderRadius: "6px",
    fontSize: "0.875rem",
    fontWeight: "500",
    cursor: "pointer",
    width: "100%",
    boxSizing: "border-box" as const
  },

  listContainer: {
    padding: "0.5rem 0",
    height: "calc(100vh - 190px)",
    overflowY: "auto" as const
  },

  loadingContainer: {
    padding: "1rem",
    textAlign: "center" as const
  },

  emptyContainer: {
    padding: "1rem",
    textAlign: "center" as const,
    opacity: 0.6
  },

  listItem: {
    padding: "0.75rem 1rem",
    display: "flex",
    alignItems: "center",
    gap: "10px",
    cursor: "pointer",
    transition: "background-color 0.2s ease",
    borderLeft: "3px solid transparent",
    position: "relative" as const
  },

  thumbnailContainer: {
    width: "32px",
    height: "32px",
    marginRight: "8px"
  },

  thumbnail: {
    width: "100%",
    height: "100%",
    objectFit: "cover" as const,
    borderRadius: "4px"
  },

  itemContent: {
    flex: 1,
    overflow: "hidden"
  },

  itemTitle: {
    fontSize: "0.875rem",
    fontWeight: "500",
    whiteSpace: "nowrap" as const,
    overflow: "hidden",
    textOverflow: "ellipsis"
  },

  itemDate: {
    fontSize: "0.75rem",
    opacity: 0.6,
    marginTop: "2px"
  },

  actionsContainer: {
    display: "flex",
    gap: "4px"
  },

  actionButton: (color: string): CSSProperties => ({
    background: "none",
    border: "none",
    color: color,
    cursor: "pointer",
    padding: "4px",
    borderRadius: "4px",
    display: "flex",
    alignItems: "center"
  })
};