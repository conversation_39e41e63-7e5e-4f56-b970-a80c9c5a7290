/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { Content, GenerateContentRequest, Part } from '../types';
import { ImagenGenerationParams, PredictRequestBody } from '../types/internal';
export declare function formatSystemInstruction(input?: string | Part | Content): Content | undefined;
export declare function formatNewContent(request: string | Array<string | Part>): Content;
export declare function formatGenerateContentInput(params: GenerateContentRequest | string | Array<string | Part>): GenerateContentRequest;
/**
 * Convert the user-defined parameters in <code>{@link ImagenGenerationParams}</code> to the format
 * that is expected from the REST API.
 *
 * @internal
 */
export declare function createPredictRequestBody(prompt: string, { gcsURI, imageFormat, addWatermark, numberOfImages, negativePrompt, aspectRatio, safetyFilterLevel, personFilterLevel }: ImagenGenerationParams): PredictRequestBody;
