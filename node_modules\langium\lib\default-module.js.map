{"version": 3, "file": "default-module.js", "sourceRoot": "", "sources": ["../src/default-module.ts"], "names": [], "mappings": "AAAA;;;;+EAI+E;AAK/E,OAAO,EAAE,mBAAmB,EAAE,MAAM,+BAA+B,CAAC;AACpE,OAAO,EAAE,sBAAsB,EAAE,MAAM,uCAAuC,CAAC;AAC/E,OAAO,EAAE,mBAAmB,EAAE,MAAM,oCAAoC,CAAC;AACzE,OAAO,EAAE,mBAAmB,EAAE,MAAM,2BAA2B,CAAC;AAChE,OAAO,EAAE,qBAAqB,EAAE,MAAM,6BAA6B,CAAC;AACpE,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,mBAAmB,EAAE,MAAM,+BAA+B,CAAC;AACpE,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,EAAE,uBAAuB,EAAE,MAAM,mCAAmC,CAAC;AAC5E,OAAO,EAAE,oBAAoB,EAAE,MAAM,gCAAgC,CAAC;AACtE,OAAO,EAAE,qBAAqB,EAAE,MAAM,iCAAiC,CAAC;AACxE,OAAO,EAAE,sBAAsB,EAAE,MAAM,uBAAuB,CAAC;AAC/D,OAAO,EAAE,wBAAwB,EAAE,MAAM,oCAAoC,CAAC;AAC9E,OAAO,EAAE,kBAAkB,EAAE,MAAM,qCAAqC,CAAC;AACzE,OAAO,EAAE,iCAAiC,EAAE,mCAAmC,EAAE,MAAM,iCAAiC,CAAC;AACzH,OAAO,EAAE,qBAAqB,EAAE,MAAM,iCAAiC,CAAC;AACxE,OAAO,EAAE,4BAA4B,EAAE,MAAM,8BAA8B,CAAC;AAC5E,OAAO,EAAE,sBAAsB,EAAE,MAAM,iCAAiC,CAAC;AACzE,OAAO,EAAE,6BAA6B,EAAE,uBAAuB,EAAE,MAAM,0BAA0B,CAAC;AAClG,OAAO,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AACnE,OAAO,EAAE,uBAAuB,EAAE,MAAM,kCAAkC,CAAC;AAC3E,OAAO,EAAE,YAAY,EAAE,gCAAgC,EAAE,MAAM,mBAAmB,CAAC;AACnF,OAAO,EAAE,0BAA0B,EAAE,MAAM,2CAA2C,CAAC;AACvF,OAAO,EAAE,sBAAsB,EAAE,MAAM,qCAAqC,CAAC;AAC7E,OAAO,EAAE,iCAAiC,EAAE,MAAM,4BAA4B,CAAC;AAC/E,OAAO,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAC;AAC9D,OAAO,EAAE,oBAAoB,EAAE,MAAM,+BAA+B,CAAC;AACrE,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAS3D;;;GAGG;AACH,MAAM,UAAU,uBAAuB,CAAC,OAAiC;IACrE,OAAO;QACH,aAAa,EAAE;YACX,eAAe,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,sBAAsB,CAAC,QAAQ,CAAC;YACnE,qBAAqB,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,0BAA0B,CAAC,QAAQ,CAAC;SAChF;QACD,MAAM,EAAE;YACJ,WAAW,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,kBAAkB,CAAC,QAAQ,CAAC;YAC3D,aAAa,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,mBAAmB,CAAC,QAAQ,CAAC;YAC1D,aAAa,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,mBAAmB,CAAC,QAAQ,CAAC;YAC1D,gBAAgB,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,sBAAsB,CAAC,QAAQ,CAAC;YAChE,cAAc,EAAE,GAAG,EAAE,CAAC,IAAI,qBAAqB,EAAE;YACjD,YAAY,EAAE,GAAG,EAAE,CAAC,IAAI,mBAAmB,EAAE;YAC7C,KAAK,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC;YAC/C,0BAA0B,EAAE,GAAG,EAAE,CAAC,IAAI,iCAAiC,EAAE;YACzE,yBAAyB,EAAE,GAAG,EAAE,CAAC,IAAI,gCAAgC,EAAE;SAC1E;QACD,SAAS,EAAE;YACP,cAAc,EAAE,GAAG,EAAE,CAAC,IAAI,qBAAqB,EAAE;YACjD,0BAA0B,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,iCAAiC,CAAC,QAAQ,CAAC;YACzF,4BAA4B,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,mCAAmC,CAAC,QAAQ,CAAC;SAChG;QACD,UAAU,EAAE;YACR,MAAM,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC;YACjD,YAAY,EAAE,GAAG,EAAE,CAAC,IAAI,mBAAmB,EAAE;YAC7C,aAAa,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,oBAAoB,CAAC,QAAQ,CAAC;YAC/D,gBAAgB,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,uBAAuB,CAAC,QAAQ,CAAC;YACrE,UAAU,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,iBAAiB,CAAC,QAAQ,CAAC;SAC5D;QACD,UAAU,EAAE;YACR,QAAQ,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,eAAe,CAAC,QAAQ,CAAC;YACrD,cAAc,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,qBAAqB,CAAC,QAAQ,CAAC;SACpE;QACD,UAAU,EAAE;YACR,iBAAiB,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,wBAAwB,CAAC,QAAQ,CAAC;YACvE,kBAAkB,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,kBAAkB,CAAC,QAAQ,CAAC;SACrE;QACD,MAAM,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM;KAC/B,CAAC;AACN,CAAC;AAgBD;;;GAGG;AACH,MAAM,UAAU,6BAA6B,CAAC,OAAuC;IACjF,OAAO;QACH,eAAe,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,sBAAsB,CAAC,QAAQ,CAAC;QACnE,SAAS,EAAE;YACP,gBAAgB,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,uBAAuB,CAAC,QAAQ,CAAC;YACrE,sBAAsB,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,6BAA6B,CAAC,QAAQ,CAAC;YACjF,eAAe,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,sBAAsB,CAAC,QAAQ,CAAC;YACnE,YAAY,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,mBAAmB,CAAC,QAAQ,CAAC;YAC7D,gBAAgB,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,uBAAuB,CAAC,QAAQ,CAAC;YACrE,kBAAkB,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC;YACtE,aAAa,EAAE,GAAG,EAAE,CAAC,IAAI,oBAAoB,EAAE;YAC/C,qBAAqB,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,4BAA4B,CAAC,QAAQ,CAAC;SAClF;KACJ,CAAC;AACN,CAAC"}