/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { FirebasePerformance as FirebasePerformanceCompat } from '@firebase/performance-types';
declare module '@firebase/app-compat' {
    interface FirebaseNamespace {
        performance: {
            (app?: FirebaseApp): FirebasePerformanceCompat;
        };
    }
    interface FirebaseApp {
        performance(): FirebasePerformanceCompat;
    }
}

import { FirebaseApp as FirebaseAppCompat } from "@firebase/app-compat";
import { type FirebasePerformance, type PerformanceSettings, type PerformanceTrace } from "@firebase/performance";
declare module "@firebase/performance" {
    function getPerformance(app?: FirebaseAppCompat): FirebasePerformance;
    function initializePerformance(app: FirebaseAppCompat, settings?: PerformanceSettings): FirebasePerformance;
    function trace(performance: FirebasePerformanceCompat, name: string): PerformanceTrace;
}
