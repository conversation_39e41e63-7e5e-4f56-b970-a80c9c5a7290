{"name": "cytoscape-cose-bilkent", "version": "4.1.0", "description": "The CoSE layout for Cytoscape.js by Bilkent with enhanced compound node placement", "main": "cytoscape-cose-bilkent.js", "scripts": {"postpublish": "run-s gh-pages", "gh-pages": "gh-pages -d pages", "copyright": "update license", "lint": "eslint src", "build": "cross-env NODE_ENV=production webpack", "build:min": "cross-env NODE_ENV=production MIN=true webpack", "build:release": "run-s build copyright", "watch": "webpack --progress --watch", "dev": "webpack-dev-server --open", "test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/cytoscape/cytoscape.js-cose-bilkent.git"}, "keywords": ["cytoscape", "cytoscape-extension"], "license": "MIT", "bugs": {"url": "https://github.com/cytoscape/cytoscape.js-cose-bilkent/issues"}, "homepage": "https://github.com/cytoscape/cytoscape.js-cose-bilkent", "devDependencies": {"babel-core": "^6.24.1", "babel-loader": "^7.0.0", "babel-preset-env": "^1.5.1", "camelcase": "^4.1.0", "chai": "4.0.2", "cpy-cli": "^1.0.1", "cross-env": "^5.0.0", "eslint": "^3.9.1", "gh-pages": "^1.0.0", "mocha": "3.4.2", "npm-run-all": "^4.1.2", "rimraf": "^2.6.2", "update": "^0.7.4", "updater-license": "^1.0.0", "webpack": "^2.6.1", "webpack-dev-server": "^2.4.5"}, "peerDependencies": {"cytoscape": "^3.2.0"}, "dependencies": {"cose-base": "^1.0.0"}}