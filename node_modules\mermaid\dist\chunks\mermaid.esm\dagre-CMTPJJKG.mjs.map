{"version": 3, "sources": ["../../../../../node_modules/.pnpm/dagre-d3-es@7.0.11/node_modules/dagre-d3-es/src/graphlib/json.js", "../../../src/rendering-util/layout-algorithms/dagre/mermaid-graphlib.js", "../../../src/rendering-util/layout-algorithms/dagre/index.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { Graph } from './graph.js';\n\nexport { write, read };\n\nfunction write(g) {\n  var json = {\n    options: {\n      directed: g.isDirected(),\n      multigraph: g.isMultigraph(),\n      compound: g.isCompound(),\n    },\n    nodes: writeNodes(g),\n    edges: writeEdges(g),\n  };\n  if (!_.isUndefined(g.graph())) {\n    json.value = _.clone(g.graph());\n  }\n  return json;\n}\n\nfunction writeNodes(g) {\n  return _.map(g.nodes(), function (v) {\n    var nodeValue = g.node(v);\n    var parent = g.parent(v);\n    var node = { v: v };\n    if (!_.isUndefined(nodeValue)) {\n      node.value = nodeValue;\n    }\n    if (!_.isUndefined(parent)) {\n      node.parent = parent;\n    }\n    return node;\n  });\n}\n\nfunction writeEdges(g) {\n  return _.map(g.edges(), function (e) {\n    var edgeValue = g.edge(e);\n    var edge = { v: e.v, w: e.w };\n    if (!_.isUndefined(e.name)) {\n      edge.name = e.name;\n    }\n    if (!_.isUndefined(edgeValue)) {\n      edge.value = edgeValue;\n    }\n    return edge;\n  });\n}\n\nfunction read(json) {\n  var g = new Graph(json.options).setGraph(json.value);\n  _.each(json.nodes, function (entry) {\n    g.setNode(entry.v, entry.value);\n    if (entry.parent) {\n      g.setParent(entry.v, entry.parent);\n    }\n  });\n  _.each(json.edges, function (entry) {\n    g.setEdge({ v: entry.v, w: entry.w, name: entry.name }, entry.value);\n  });\n  return g;\n}\n", "/** Decorates with functions required by mermaids dagre-wrapper. */\nimport { log } from '../../../logger.js';\nimport * as graphlib from 'dagre-d3-es/src/graphlib/index.js';\nimport * as graphlibJson from 'dagre-d3-es/src/graphlib/json.js';\n\nexport let clusterDb = new Map();\nlet descendants = new Map();\nlet parents = new Map();\n\nexport const clear = () => {\n  descendants.clear();\n  parents.clear();\n  clusterDb.clear();\n};\n\nconst isDescendant = (id, ancestorId) => {\n  const ancestorDescendants = descendants.get(ancestorId) || [];\n  log.trace('In isDescendant', ancestorId, ' ', id, ' = ', ancestorDescendants.includes(id));\n  return ancestorDescendants.includes(id);\n};\n\nconst edgeInCluster = (edge, clusterId) => {\n  const clusterDescendants = descendants.get(clusterId) || [];\n  log.info('Descendants of ', clusterId, ' is ', clusterDescendants);\n  log.info('Edge is ', edge);\n  if (edge.v === clusterId || edge.w === clusterId) {\n    return false;\n  }\n\n  if (!clusterDescendants) {\n    log.debug('Tilt, ', clusterId, ',not in descendants');\n    return false;\n  }\n\n  return (\n    clusterDescendants.includes(edge.v) ||\n    isDescendant(edge.v, clusterId) ||\n    isDescendant(edge.w, clusterId) ||\n    clusterDescendants.includes(edge.w)\n  );\n};\n\nconst copy = (clusterId, graph, newGraph, rootId) => {\n  log.warn(\n    'Copying children of ',\n    clusterId,\n    'root',\n    rootId,\n    'data',\n    graph.node(clusterId),\n    rootId\n  );\n  const nodes = graph.children(clusterId) || [];\n\n  if (clusterId !== rootId) {\n    nodes.push(clusterId);\n  }\n\n  log.warn('Copying (nodes) clusterId', clusterId, 'nodes', nodes);\n\n  nodes.forEach((node) => {\n    if (graph.children(node).length > 0) {\n      copy(node, graph, newGraph, rootId);\n    } else {\n      const data = graph.node(node);\n      log.info('cp ', node, ' to ', rootId, ' with parent ', clusterId);\n      newGraph.setNode(node, data);\n      if (rootId !== graph.parent(node)) {\n        log.warn('Setting parent', node, graph.parent(node));\n        newGraph.setParent(node, graph.parent(node));\n      }\n\n      if (clusterId !== rootId && node !== clusterId) {\n        log.debug('Setting parent', node, clusterId);\n        newGraph.setParent(node, clusterId);\n      } else {\n        log.info('In copy ', clusterId, 'root', rootId, 'data', graph.node(clusterId), rootId);\n        log.debug(\n          'Not Setting parent for node=',\n          node,\n          'cluster!==rootId',\n          clusterId !== rootId,\n          'node!==clusterId',\n          node !== clusterId\n        );\n      }\n      const edges = graph.edges(node);\n      log.debug('Copying Edges', edges);\n      edges.forEach((edge) => {\n        log.info('Edge', edge);\n        const data = graph.edge(edge.v, edge.w, edge.name);\n        log.info('Edge data', data, rootId);\n        try {\n          if (edgeInCluster(edge, rootId)) {\n            log.info('Copying as ', edge.v, edge.w, data, edge.name);\n            newGraph.setEdge(edge.v, edge.w, data, edge.name);\n            log.info('newGraph edges ', newGraph.edges(), newGraph.edge(newGraph.edges()[0]));\n          } else {\n            log.info(\n              'Skipping copy of edge ',\n              edge.v,\n              '-->',\n              edge.w,\n              ' rootId: ',\n              rootId,\n              ' clusterId:',\n              clusterId\n            );\n          }\n        } catch (e) {\n          log.error(e);\n        }\n      });\n    }\n    log.debug('Removing node', node);\n    graph.removeNode(node);\n  });\n};\n\nexport const extractDescendants = (id, graph) => {\n  const children = graph.children(id);\n  let res = [...children];\n\n  for (const child of children) {\n    parents.set(child, id);\n    res = [...res, ...extractDescendants(child, graph)];\n  }\n\n  return res;\n};\n\nexport const validate = (graph) => {\n  const edges = graph.edges();\n  log.trace('Edges: ', edges);\n  for (const edge of edges) {\n    if (graph.children(edge.v).length > 0) {\n      log.trace('The node ', edge.v, ' is part of and edge even though it has children');\n      return false;\n    }\n    if (graph.children(edge.w).length > 0) {\n      log.trace('The node ', edge.w, ' is part of and edge even though it has children');\n      return false;\n    }\n  }\n  return true;\n};\n\nconst findCommonEdges = (graph, id1, id2) => {\n  const edges1 = graph.edges().filter((edge) => edge.v === id1 || edge.w === id1);\n  const edges2 = graph.edges().filter((edge) => edge.v === id2 || edge.w === id2);\n  const edges1Prim = edges1.map((edge) => {\n    return { v: edge.v === id1 ? id2 : edge.v, w: edge.w === id1 ? id1 : edge.w };\n  });\n  const edges2Prim = edges2.map((edge) => {\n    return { v: edge.v, w: edge.w };\n  });\n  const result = edges1Prim.filter((edgeIn1) => {\n    return edges2Prim.some((edge) => edgeIn1.v === edge.v && edgeIn1.w === edge.w);\n  });\n\n  return result;\n};\n\nexport const findNonClusterChild = (id, graph, clusterId) => {\n  const children = graph.children(id);\n  log.trace('Searching children of id ', id, children);\n  if (children.length < 1) {\n    return id;\n  }\n  let reserve;\n  for (const child of children) {\n    const _id = findNonClusterChild(child, graph, clusterId);\n\n    const commonEdges = findCommonEdges(graph, clusterId, _id);\n\n    if (_id) {\n      if (commonEdges.length > 0) {\n        reserve = _id;\n      } else {\n        return _id;\n      }\n    }\n  }\n  return reserve;\n};\n\nconst getAnchorId = (id) => {\n  if (!clusterDb.has(id)) {\n    return id;\n  }\n  if (!clusterDb.get(id).externalConnections) {\n    return id;\n  }\n\n  if (clusterDb.has(id)) {\n    return clusterDb.get(id).id;\n  }\n  return id;\n};\n\nexport const adjustClustersAndEdges = (graph, depth) => {\n  if (!graph || depth > 10) {\n    log.debug('Opting out, no graph ');\n    return;\n  } else {\n    log.debug('Opting in, graph ');\n  }\n\n  graph.nodes().forEach(function (id) {\n    const children = graph.children(id);\n    if (children.length > 0) {\n      log.warn(\n        'Cluster identified',\n        id,\n        ' Replacement id in edges: ',\n        findNonClusterChild(id, graph, id)\n      );\n      descendants.set(id, extractDescendants(id, graph));\n      clusterDb.set(id, { id: findNonClusterChild(id, graph, id), clusterData: graph.node(id) });\n    }\n  });\n\n  graph.nodes().forEach(function (id) {\n    const children = graph.children(id);\n    const edges = graph.edges();\n    if (children.length > 0) {\n      log.debug('Cluster identified', id, descendants);\n      edges.forEach((edge) => {\n        const d1 = isDescendant(edge.v, id);\n        const d2 = isDescendant(edge.w, id);\n\n        if (d1 ^ d2) {\n          log.warn('Edge: ', edge, ' leaves cluster ', id);\n          log.warn('Descendants of XXX ', id, ': ', descendants.get(id));\n          clusterDb.get(id).externalConnections = true;\n        }\n      });\n    } else {\n      log.debug('Not a cluster ', id, descendants);\n    }\n  });\n\n  for (let id of clusterDb.keys()) {\n    const nonClusterChild = clusterDb.get(id).id;\n    const parent = graph.parent(nonClusterChild);\n\n    if (parent !== id && clusterDb.has(parent) && !clusterDb.get(parent).externalConnections) {\n      clusterDb.get(id).id = parent;\n    }\n  }\n\n  graph.edges().forEach(function (e) {\n    const edge = graph.edge(e);\n    log.warn('Edge ' + e.v + ' -> ' + e.w + ': ' + JSON.stringify(e));\n    log.warn('Edge ' + e.v + ' -> ' + e.w + ': ' + JSON.stringify(graph.edge(e)));\n\n    let v = e.v;\n    let w = e.w;\n    log.warn(\n      'Fix XXX',\n      clusterDb,\n      'ids:',\n      e.v,\n      e.w,\n      'Translating: ',\n      clusterDb.get(e.v),\n      ' --- ',\n      clusterDb.get(e.w)\n    );\n    if (clusterDb.get(e.v) || clusterDb.get(e.w)) {\n      log.warn('Fixing and trying - removing XXX', e.v, e.w, e.name);\n      v = getAnchorId(e.v);\n      w = getAnchorId(e.w);\n      graph.removeEdge(e.v, e.w, e.name);\n      if (v !== e.v) {\n        const parent = graph.parent(v);\n        clusterDb.get(parent).externalConnections = true;\n        edge.fromCluster = e.v;\n      }\n      if (w !== e.w) {\n        const parent = graph.parent(w);\n        clusterDb.get(parent).externalConnections = true;\n        edge.toCluster = e.w;\n      }\n      log.warn('Fix Replacing with XXX', v, w, e.name);\n      graph.setEdge(v, w, edge, e.name);\n    }\n  });\n  log.warn('Adjusted Graph', graphlibJson.write(graph));\n  extractor(graph, 0);\n\n  log.trace(clusterDb);\n};\n\nexport const extractor = (graph, depth) => {\n  log.warn('extractor - ', depth, graphlibJson.write(graph), graph.children('D'));\n  if (depth > 10) {\n    log.error('Bailing out');\n    return;\n  }\n  let nodes = graph.nodes();\n  let hasChildren = false;\n  for (const node of nodes) {\n    const children = graph.children(node);\n    hasChildren = hasChildren || children.length > 0;\n  }\n\n  if (!hasChildren) {\n    log.debug('Done, no node has children', graph.nodes());\n    return;\n  }\n  log.debug('Nodes = ', nodes, depth);\n  for (const node of nodes) {\n    log.debug(\n      'Extracting node',\n      node,\n      clusterDb,\n      clusterDb.has(node) && !clusterDb.get(node).externalConnections,\n      !graph.parent(node),\n      graph.node(node),\n      graph.children('D'),\n      ' Depth ',\n      depth\n    );\n    if (!clusterDb.has(node)) {\n      log.debug('Not a cluster', node, depth);\n    } else if (\n      !clusterDb.get(node).externalConnections &&\n      graph.children(node) &&\n      graph.children(node).length > 0\n    ) {\n      log.warn(\n        'Cluster without external connections, without a parent and with children',\n        node,\n        depth\n      );\n\n      const graphSettings = graph.graph();\n      let dir = graphSettings.rankdir === 'TB' ? 'LR' : 'TB';\n      if (clusterDb.get(node)?.clusterData?.dir) {\n        dir = clusterDb.get(node).clusterData.dir;\n        log.warn('Fixing dir', clusterDb.get(node).clusterData.dir, dir);\n      }\n\n      const clusterGraph = new graphlib.Graph({\n        multigraph: true,\n        compound: true,\n      })\n        .setGraph({\n          rankdir: dir,\n          nodesep: 50,\n          ranksep: 50,\n          marginx: 8,\n          marginy: 8,\n        })\n        .setDefaultEdgeLabel(function () {\n          return {};\n        });\n\n      log.warn('Old graph before copy', graphlibJson.write(graph));\n      copy(node, graph, clusterGraph, node);\n      graph.setNode(node, {\n        clusterNode: true,\n        id: node,\n        clusterData: clusterDb.get(node).clusterData,\n        label: clusterDb.get(node).label,\n        graph: clusterGraph,\n      });\n      log.warn('New graph after copy node: (', node, ')', graphlibJson.write(clusterGraph));\n      log.debug('Old graph after copy', graphlibJson.write(graph));\n    } else {\n      log.warn(\n        'Cluster ** ',\n        node,\n        ' **not meeting the criteria !externalConnections:',\n        !clusterDb.get(node).externalConnections,\n        ' no parent: ',\n        !graph.parent(node),\n        ' children ',\n        graph.children(node) && graph.children(node).length > 0,\n        graph.children('D'),\n        depth\n      );\n      log.debug(clusterDb);\n    }\n  }\n\n  nodes = graph.nodes();\n  log.warn('New list of nodes', nodes);\n  for (const node of nodes) {\n    const data = graph.node(node);\n    log.warn(' Now next level', node, data);\n    if (data?.clusterNode) {\n      extractor(data.graph, depth + 1);\n    }\n  }\n};\n\nconst sorter = (graph, nodes) => {\n  if (nodes.length === 0) {\n    return [];\n  }\n  let result = Object.assign([], nodes);\n  nodes.forEach((node) => {\n    const children = graph.children(node);\n    const sorted = sorter(graph, children);\n    result = [...result, ...sorted];\n  });\n\n  return result;\n};\n\nexport const sortNodesByHierarchy = (graph) => sorter(graph, graph.children());\n", "import { layout as dagreLayout } from 'dagre-d3-es/src/dagre/index.js';\nimport * as graphlibJson from 'dagre-d3-es/src/graphlib/json.js';\nimport * as graphlib from 'dagre-d3-es/src/graphlib/index.js';\nimport insertMarkers from '../../rendering-elements/markers.js';\nimport { updateNodeBounds } from '../../rendering-elements/shapes/util.js';\nimport {\n  clear as clearGraphlib,\n  clusterDb,\n  adjustClustersAndEdges,\n  findNonClusterChild,\n  sortNodesByHierarchy,\n} from './mermaid-graphlib.js';\nimport {\n  insertNode,\n  positionNode,\n  clear as clearNodes,\n  setNodeElem,\n} from '../../rendering-elements/nodes.js';\nimport { insertCluster, clear as clearClusters } from '../../rendering-elements/clusters.js';\nimport {\n  insertEdgeLabel,\n  positionEdgeLabel,\n  insertEdge,\n  clear as clearEdges,\n} from '../../rendering-elements/edges.js';\nimport { log } from '../../../logger.js';\nimport { getSubGraphTitleMargins } from '../../../utils/subGraphTitleMargins.js';\nimport { getConfig } from '../../../diagram-api/diagramAPI.js';\n\nconst recursiveRender = async (_elem, graph, diagramType, id, parentCluster, siteConfig) => {\n  log.warn('Graph in recursive render:XAX', graphlibJson.write(graph), parentCluster);\n  const dir = graph.graph().rankdir;\n  log.trace('Dir in recursive render - dir:', dir);\n\n  const elem = _elem.insert('g').attr('class', 'root');\n  if (!graph.nodes()) {\n    log.info('No nodes found for', graph);\n  } else {\n    log.info('Recursive render XXX', graph.nodes());\n  }\n  if (graph.edges().length > 0) {\n    log.info('Recursive edges', graph.edge(graph.edges()[0]));\n  }\n  const clusters = elem.insert('g').attr('class', 'clusters');\n  const edgePaths = elem.insert('g').attr('class', 'edgePaths');\n  const edgeLabels = elem.insert('g').attr('class', 'edgeLabels');\n  const nodes = elem.insert('g').attr('class', 'nodes');\n\n  // Insert nodes, this will insert them into the dom and each node will get a size. The size is updated\n  // to the abstract node and is later used by dagre for the layout\n  await Promise.all(\n    graph.nodes().map(async function (v) {\n      const node = graph.node(v);\n      if (parentCluster !== undefined) {\n        const data = JSON.parse(JSON.stringify(parentCluster.clusterData));\n        // data.clusterPositioning = true;\n        log.trace(\n          'Setting data for parent cluster XXX\\n Node.id = ',\n          v,\n          '\\n data=',\n          data.height,\n          '\\nParent cluster',\n          parentCluster.height\n        );\n        graph.setNode(parentCluster.id, data);\n        if (!graph.parent(v)) {\n          log.trace('Setting parent', v, parentCluster.id);\n          graph.setParent(v, parentCluster.id, data);\n        }\n      }\n      log.info('(Insert) Node XXX' + v + ': ' + JSON.stringify(graph.node(v)));\n      if (node?.clusterNode) {\n        // const children = graph.children(v);\n        log.info('Cluster identified XBX', v, node.width, graph.node(v));\n\n        // `node.graph.setGraph` applies the graph configurations such as nodeSpacing to subgraphs as without this the default values would be used\n        // We override only the `ranksep` and `nodesep` configurations to allow for setting subgraph spacing while avoiding overriding other properties\n        const { ranksep, nodesep } = graph.graph();\n        node.graph.setGraph({\n          ...node.graph.graph(),\n          ranksep: ranksep + 25,\n          nodesep,\n        });\n\n        // \"o\" will contain the full cluster not just the children\n        const o = await recursiveRender(\n          nodes,\n          node.graph,\n          diagramType,\n          id,\n          graph.node(v),\n          siteConfig\n        );\n        const newEl = o.elem;\n        updateNodeBounds(node, newEl);\n        // node.height = o.diff;\n        node.diff = o.diff || 0;\n        log.info(\n          'New compound node after recursive render XAX',\n          v,\n          'width',\n          // node,\n          node.width,\n          'height',\n          node.height\n          // node.x,\n          // node.y\n        );\n        setNodeElem(newEl, node);\n      } else {\n        if (graph.children(v).length > 0) {\n          // This is a cluster but not to be rendered recursively\n          // Render as before\n          log.trace(\n            'Cluster - the non recursive path XBX',\n            v,\n            node.id,\n            node,\n            node.width,\n            'Graph:',\n            graph\n          );\n          log.trace(findNonClusterChild(node.id, graph));\n          clusterDb.set(node.id, { id: findNonClusterChild(node.id, graph), node });\n          // insertCluster(clusters, graph.node(v));\n        } else {\n          log.trace('Node - the non recursive path XAX', v, nodes, graph.node(v), dir);\n          await insertNode(nodes, graph.node(v), { config: siteConfig, dir });\n        }\n      }\n    })\n  );\n\n  const processEdges = async () => {\n    const edgePromises = graph.edges().map(async function (e) {\n      const edge = graph.edge(e.v, e.w, e.name);\n      log.info('Edge ' + e.v + ' -> ' + e.w + ': ' + JSON.stringify(e));\n      log.info('Edge ' + e.v + ' -> ' + e.w + ': ', e, ' ', JSON.stringify(graph.edge(e)));\n\n      // Check if link is either from or to a cluster\n      log.info(\n        'Fix',\n        clusterDb,\n        'ids:',\n        e.v,\n        e.w,\n        'Translating: ',\n        clusterDb.get(e.v),\n        clusterDb.get(e.w)\n      );\n      await insertEdgeLabel(edgeLabels, edge);\n    });\n\n    await Promise.all(edgePromises);\n  };\n\n  await processEdges();\n\n  log.info('Graph before layout:', JSON.stringify(graphlibJson.write(graph)));\n\n  log.info('############################################# XXX');\n  log.info('###                Layout                 ### XXX');\n  log.info('############################################# XXX');\n\n  dagreLayout(graph);\n\n  log.info('Graph after layout:', JSON.stringify(graphlibJson.write(graph)));\n  // Move the nodes to the correct place\n  let diff = 0;\n  let { subGraphTitleTotalMargin } = getSubGraphTitleMargins(siteConfig);\n  await Promise.all(\n    sortNodesByHierarchy(graph).map(async function (v) {\n      const node = graph.node(v);\n      log.info(\n        'Position XBX => ' + v + ': (' + node.x,\n        ',' + node.y,\n        ') width: ',\n        node.width,\n        ' height: ',\n        node.height\n      );\n      if (node?.clusterNode) {\n        // Adjust for padding when on root level\n        node.y += subGraphTitleTotalMargin;\n\n        log.info(\n          'A tainted cluster node XBX1',\n          v,\n          node.id,\n          node.width,\n          node.height,\n          node.x,\n          node.y,\n          graph.parent(v)\n        );\n        clusterDb.get(node.id).node = node;\n        positionNode(node);\n      } else {\n        // A tainted cluster node\n        if (graph.children(v).length > 0) {\n          log.info(\n            'A pure cluster node XBX1',\n            v,\n            node.id,\n            node.x,\n            node.y,\n            node.width,\n            node.height,\n            graph.parent(v)\n          );\n          node.height += subGraphTitleTotalMargin;\n          graph.node(node.parentId);\n          const halfPadding = node?.padding / 2 || 0;\n          const labelHeight = node?.labelBBox?.height || 0;\n          const offsetY = labelHeight - halfPadding || 0;\n          log.debug('OffsetY', offsetY, 'labelHeight', labelHeight, 'halfPadding', halfPadding);\n          await insertCluster(clusters, node);\n\n          // A cluster in the non-recursive way\n          clusterDb.get(node.id).node = node;\n        } else {\n          // Regular node\n          const parent = graph.node(node.parentId);\n          node.y += subGraphTitleTotalMargin / 2;\n          log.info(\n            'A regular node XBX1 - using the padding',\n            node.id,\n            'parent',\n            node.parentId,\n            node.width,\n            node.height,\n            node.x,\n            node.y,\n            'offsetY',\n            node.offsetY,\n            'parent',\n            parent,\n            parent?.offsetY,\n            node\n          );\n\n          positionNode(node);\n        }\n      }\n    })\n  );\n\n  // Move the edge labels to the correct place after layout\n  graph.edges().forEach(function (e) {\n    const edge = graph.edge(e);\n    log.info('Edge ' + e.v + ' -> ' + e.w + ': ' + JSON.stringify(edge), edge);\n\n    edge.points.forEach((point) => (point.y += subGraphTitleTotalMargin / 2));\n    const startNode = graph.node(e.v);\n    var endNode = graph.node(e.w);\n    const paths = insertEdge(edgePaths, edge, clusterDb, diagramType, startNode, endNode, id);\n    positionEdgeLabel(edge, paths);\n  });\n\n  graph.nodes().forEach(function (v) {\n    const n = graph.node(v);\n    log.info(v, n.type, n.diff);\n    if (n.isGroup) {\n      diff = n.diff;\n    }\n  });\n  log.warn('Returning from recursive render XAX', elem, diff);\n  return { elem, diff };\n};\n\nexport const render = async (data4Layout, svg) => {\n  const graph = new graphlib.Graph({\n    multigraph: true,\n    compound: true,\n  })\n    .setGraph({\n      rankdir: data4Layout.direction,\n      nodesep:\n        data4Layout.config?.nodeSpacing ||\n        data4Layout.config?.flowchart?.nodeSpacing ||\n        data4Layout.nodeSpacing,\n      ranksep:\n        data4Layout.config?.rankSpacing ||\n        data4Layout.config?.flowchart?.rankSpacing ||\n        data4Layout.rankSpacing,\n      marginx: 8,\n      marginy: 8,\n    })\n    .setDefaultEdgeLabel(function () {\n      return {};\n    });\n  const element = svg.select('g');\n  insertMarkers(element, data4Layout.markers, data4Layout.type, data4Layout.diagramId);\n  clearNodes();\n  clearEdges();\n  clearClusters();\n  clearGraphlib();\n\n  data4Layout.nodes.forEach((node) => {\n    graph.setNode(node.id, { ...node });\n    if (node.parentId) {\n      graph.setParent(node.id, node.parentId);\n    }\n  });\n\n  log.debug('Edges:', data4Layout.edges);\n  data4Layout.edges.forEach((edge) => {\n    // Handle self-loops\n    if (edge.start === edge.end) {\n      const nodeId = edge.start;\n      const specialId1 = nodeId + '---' + nodeId + '---1';\n      const specialId2 = nodeId + '---' + nodeId + '---2';\n      const node = graph.node(nodeId);\n      graph.setNode(specialId1, {\n        domId: specialId1,\n        id: specialId1,\n        parentId: node.parentId,\n        labelStyle: '',\n        label: '',\n        padding: 0,\n        shape: 'labelRect',\n        // shape: 'rect',\n        style: '',\n        width: 10,\n        height: 10,\n      });\n      graph.setParent(specialId1, node.parentId);\n      graph.setNode(specialId2, {\n        domId: specialId2,\n        id: specialId2,\n        parentId: node.parentId,\n        labelStyle: '',\n        padding: 0,\n        // shape: 'rect',\n        shape: 'labelRect',\n        label: '',\n        style: '',\n        width: 10,\n        height: 10,\n      });\n      graph.setParent(specialId2, node.parentId);\n\n      const edge1 = structuredClone(edge);\n      const edgeMid = structuredClone(edge);\n      const edge2 = structuredClone(edge);\n      edge1.label = '';\n      edge1.arrowTypeEnd = 'none';\n      edge1.id = nodeId + '-cyclic-special-1';\n      edgeMid.arrowTypeStart = 'none';\n      edgeMid.arrowTypeEnd = 'none';\n      edgeMid.id = nodeId + '-cyclic-special-mid';\n      edge2.label = '';\n      if (node.isGroup) {\n        edge1.fromCluster = nodeId;\n        edge2.toCluster = nodeId;\n      }\n      edge2.id = nodeId + '-cyclic-special-2';\n      edge2.arrowTypeStart = 'none';\n      graph.setEdge(nodeId, specialId1, edge1, nodeId + '-cyclic-special-0');\n      graph.setEdge(specialId1, specialId2, edgeMid, nodeId + '-cyclic-special-1');\n      graph.setEdge(specialId2, nodeId, edge2, nodeId + '-cyc<lic-special-2');\n    } else {\n      graph.setEdge(edge.start, edge.end, { ...edge }, edge.id);\n    }\n  });\n\n  log.warn('Graph at first:', JSON.stringify(graphlibJson.write(graph)));\n  adjustClustersAndEdges(graph);\n  log.warn('Graph after XAX:', JSON.stringify(graphlibJson.write(graph)));\n  const siteConfig = getConfig();\n  await recursiveRender(\n    element,\n    graph,\n    data4Layout.type,\n    data4Layout.diagramId,\n    undefined,\n    siteConfig\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,SAAS,MAAM,GAAG;AAChB,MAAI,OAAO;AAAA,IACT,SAAS;AAAA,MACP,UAAU,EAAE,WAAW;AAAA,MACvB,YAAY,EAAE,aAAa;AAAA,MAC3B,UAAU,EAAE,WAAW;AAAA,IACzB;AAAA,IACA,OAAO,WAAW,CAAC;AAAA,IACnB,OAAO,WAAW,CAAC;AAAA,EACrB;AACA,MAAI,CAAG,oBAAY,EAAE,MAAM,CAAC,GAAG;AAC7B,SAAK,QAAU,cAAM,EAAE,MAAM,CAAC;AAAA,EAChC;AACA,SAAO;AACT;AAdS;AAgBT,SAAS,WAAW,GAAG;AACrB,SAAS,YAAI,EAAE,MAAM,GAAG,SAAU,GAAG;AACnC,QAAI,YAAY,EAAE,KAAK,CAAC;AACxB,QAAI,SAAS,EAAE,OAAO,CAAC;AACvB,QAAI,OAAO,EAAE,EAAK;AAClB,QAAI,CAAG,oBAAY,SAAS,GAAG;AAC7B,WAAK,QAAQ;AAAA,IACf;AACA,QAAI,CAAG,oBAAY,MAAM,GAAG;AAC1B,WAAK,SAAS;AAAA,IAChB;AACA,WAAO;AAAA,EACT,CAAC;AACH;AAbS;AAeT,SAAS,WAAW,GAAG;AACrB,SAAS,YAAI,EAAE,MAAM,GAAG,SAAU,GAAG;AACnC,QAAI,YAAY,EAAE,KAAK,CAAC;AACxB,QAAI,OAAO,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE;AAC5B,QAAI,CAAG,oBAAY,EAAE,IAAI,GAAG;AAC1B,WAAK,OAAO,EAAE;AAAA,IAChB;AACA,QAAI,CAAG,oBAAY,SAAS,GAAG;AAC7B,WAAK,QAAQ;AAAA,IACf;AACA,WAAO;AAAA,EACT,CAAC;AACH;AAZS;;;AC/BF,IAAI,YAAY,oBAAI,IAAI;AAC/B,IAAI,cAAc,oBAAI,IAAI;AAC1B,IAAI,UAAU,oBAAI,IAAI;AAEf,IAAMA,SAAQ,6BAAM;AACzB,cAAY,MAAM;AAClB,UAAQ,MAAM;AACd,YAAU,MAAM;AAClB,GAJqB;AAMrB,IAAM,eAAe,wBAAC,IAAI,eAAe;AACvC,QAAM,sBAAsB,YAAY,IAAI,UAAU,KAAK,CAAC;AAC5D,MAAI,MAAM,mBAAmB,YAAY,KAAK,IAAI,OAAO,oBAAoB,SAAS,EAAE,CAAC;AACzF,SAAO,oBAAoB,SAAS,EAAE;AACxC,GAJqB;AAMrB,IAAM,gBAAgB,wBAAC,MAAM,cAAc;AACzC,QAAM,qBAAqB,YAAY,IAAI,SAAS,KAAK,CAAC;AAC1D,MAAI,KAAK,mBAAmB,WAAW,QAAQ,kBAAkB;AACjE,MAAI,KAAK,YAAY,IAAI;AACzB,MAAI,KAAK,MAAM,aAAa,KAAK,MAAM,WAAW;AAChD,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,oBAAoB;AACvB,QAAI,MAAM,UAAU,WAAW,qBAAqB;AACpD,WAAO;AAAA,EACT;AAEA,SACE,mBAAmB,SAAS,KAAK,CAAC,KAClC,aAAa,KAAK,GAAG,SAAS,KAC9B,aAAa,KAAK,GAAG,SAAS,KAC9B,mBAAmB,SAAS,KAAK,CAAC;AAEtC,GAnBsB;AAqBtB,IAAM,OAAO,wBAAC,WAAW,OAAO,UAAU,WAAW;AACnD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM,KAAK,SAAS;AAAA,IACpB;AAAA,EACF;AACA,QAAM,QAAQ,MAAM,SAAS,SAAS,KAAK,CAAC;AAE5C,MAAI,cAAc,QAAQ;AACxB,UAAM,KAAK,SAAS;AAAA,EACtB;AAEA,MAAI,KAAK,6BAA6B,WAAW,SAAS,KAAK;AAE/D,QAAM,QAAQ,CAAC,SAAS;AACtB,QAAI,MAAM,SAAS,IAAI,EAAE,SAAS,GAAG;AACnC,WAAK,MAAM,OAAO,UAAU,MAAM;AAAA,IACpC,OAAO;AACL,YAAM,OAAO,MAAM,KAAK,IAAI;AAC5B,UAAI,KAAK,OAAO,MAAM,QAAQ,QAAQ,iBAAiB,SAAS;AAChE,eAAS,QAAQ,MAAM,IAAI;AAC3B,UAAI,WAAW,MAAM,OAAO,IAAI,GAAG;AACjC,YAAI,KAAK,kBAAkB,MAAM,MAAM,OAAO,IAAI,CAAC;AACnD,iBAAS,UAAU,MAAM,MAAM,OAAO,IAAI,CAAC;AAAA,MAC7C;AAEA,UAAI,cAAc,UAAU,SAAS,WAAW;AAC9C,YAAI,MAAM,kBAAkB,MAAM,SAAS;AAC3C,iBAAS,UAAU,MAAM,SAAS;AAAA,MACpC,OAAO;AACL,YAAI,KAAK,YAAY,WAAW,QAAQ,QAAQ,QAAQ,MAAM,KAAK,SAAS,GAAG,MAAM;AACrF,YAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA,cAAc;AAAA,UACd;AAAA,UACA,SAAS;AAAA,QACX;AAAA,MACF;AACA,YAAM,QAAQ,MAAM,MAAM,IAAI;AAC9B,UAAI,MAAM,iBAAiB,KAAK;AAChC,YAAM,QAAQ,CAAC,SAAS;AACtB,YAAI,KAAK,QAAQ,IAAI;AACrB,cAAMC,QAAO,MAAM,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI;AACjD,YAAI,KAAK,aAAaA,OAAM,MAAM;AAClC,YAAI;AACF,cAAI,cAAc,MAAM,MAAM,GAAG;AAC/B,gBAAI,KAAK,eAAe,KAAK,GAAG,KAAK,GAAGA,OAAM,KAAK,IAAI;AACvD,qBAAS,QAAQ,KAAK,GAAG,KAAK,GAAGA,OAAM,KAAK,IAAI;AAChD,gBAAI,KAAK,mBAAmB,SAAS,MAAM,GAAG,SAAS,KAAK,SAAS,MAAM,EAAE,CAAC,CAAC,CAAC;AAAA,UAClF,OAAO;AACL,gBAAI;AAAA,cACF;AAAA,cACA,KAAK;AAAA,cACL;AAAA,cACA,KAAK;AAAA,cACL;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF,SAAS,GAAG;AACV,cAAI,MAAM,CAAC;AAAA,QACb;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,MAAM,iBAAiB,IAAI;AAC/B,UAAM,WAAW,IAAI;AAAA,EACvB,CAAC;AACH,GA3Ea;AA6EN,IAAM,qBAAqB,wBAAC,IAAI,UAAU;AAC/C,QAAM,WAAW,MAAM,SAAS,EAAE;AAClC,MAAI,MAAM,CAAC,GAAG,QAAQ;AAEtB,aAAW,SAAS,UAAU;AAC5B,YAAQ,IAAI,OAAO,EAAE;AACrB,UAAM,CAAC,GAAG,KAAK,GAAG,mBAAmB,OAAO,KAAK,CAAC;AAAA,EACpD;AAEA,SAAO;AACT,GAVkC;AA4BlC,IAAM,kBAAkB,wBAAC,OAAO,KAAK,QAAQ;AAC3C,QAAM,SAAS,MAAM,MAAM,EAAE,OAAO,CAAC,SAAS,KAAK,MAAM,OAAO,KAAK,MAAM,GAAG;AAC9E,QAAM,SAAS,MAAM,MAAM,EAAE,OAAO,CAAC,SAAS,KAAK,MAAM,OAAO,KAAK,MAAM,GAAG;AAC9E,QAAM,aAAa,OAAO,IAAI,CAAC,SAAS;AACtC,WAAO,EAAE,GAAG,KAAK,MAAM,MAAM,MAAM,KAAK,GAAG,GAAG,KAAK,MAAM,MAAM,MAAM,KAAK,EAAE;AAAA,EAC9E,CAAC;AACD,QAAM,aAAa,OAAO,IAAI,CAAC,SAAS;AACtC,WAAO,EAAE,GAAG,KAAK,GAAG,GAAG,KAAK,EAAE;AAAA,EAChC,CAAC;AACD,QAAM,SAAS,WAAW,OAAO,CAAC,YAAY;AAC5C,WAAO,WAAW,KAAK,CAAC,SAAS,QAAQ,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,EAC/E,CAAC;AAED,SAAO;AACT,GAdwB;AAgBjB,IAAM,sBAAsB,wBAAC,IAAI,OAAO,cAAc;AAC3D,QAAM,WAAW,MAAM,SAAS,EAAE;AAClC,MAAI,MAAM,6BAA6B,IAAI,QAAQ;AACnD,MAAI,SAAS,SAAS,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI;AACJ,aAAW,SAAS,UAAU;AAC5B,UAAM,MAAM,oBAAoB,OAAO,OAAO,SAAS;AAEvD,UAAM,cAAc,gBAAgB,OAAO,WAAW,GAAG;AAEzD,QAAI,KAAK;AACP,UAAI,YAAY,SAAS,GAAG;AAC1B,kBAAU;AAAA,MACZ,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT,GArBmC;AAuBnC,IAAM,cAAc,wBAAC,OAAO;AAC1B,MAAI,CAAC,UAAU,IAAI,EAAE,GAAG;AACtB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,UAAU,IAAI,EAAE,EAAE,qBAAqB;AAC1C,WAAO;AAAA,EACT;AAEA,MAAI,UAAU,IAAI,EAAE,GAAG;AACrB,WAAO,UAAU,IAAI,EAAE,EAAE;AAAA,EAC3B;AACA,SAAO;AACT,GAZoB;AAcb,IAAM,yBAAyB,wBAAC,OAAO,UAAU;AACtD,MAAI,CAAC,SAAS,QAAQ,IAAI;AACxB,QAAI,MAAM,uBAAuB;AACjC;AAAA,EACF,OAAO;AACL,QAAI,MAAM,mBAAmB;AAAA,EAC/B;AAEA,QAAM,MAAM,EAAE,QAAQ,SAAU,IAAI;AAClC,UAAM,WAAW,MAAM,SAAS,EAAE;AAClC,QAAI,SAAS,SAAS,GAAG;AACvB,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA,oBAAoB,IAAI,OAAO,EAAE;AAAA,MACnC;AACA,kBAAY,IAAI,IAAI,mBAAmB,IAAI,KAAK,CAAC;AACjD,gBAAU,IAAI,IAAI,EAAE,IAAI,oBAAoB,IAAI,OAAO,EAAE,GAAG,aAAa,MAAM,KAAK,EAAE,EAAE,CAAC;AAAA,IAC3F;AAAA,EACF,CAAC;AAED,QAAM,MAAM,EAAE,QAAQ,SAAU,IAAI;AAClC,UAAM,WAAW,MAAM,SAAS,EAAE;AAClC,UAAM,QAAQ,MAAM,MAAM;AAC1B,QAAI,SAAS,SAAS,GAAG;AACvB,UAAI,MAAM,sBAAsB,IAAI,WAAW;AAC/C,YAAM,QAAQ,CAAC,SAAS;AACtB,cAAM,KAAK,aAAa,KAAK,GAAG,EAAE;AAClC,cAAM,KAAK,aAAa,KAAK,GAAG,EAAE;AAElC,YAAI,KAAK,IAAI;AACX,cAAI,KAAK,UAAU,MAAM,oBAAoB,EAAE;AAC/C,cAAI,KAAK,uBAAuB,IAAI,MAAM,YAAY,IAAI,EAAE,CAAC;AAC7D,oBAAU,IAAI,EAAE,EAAE,sBAAsB;AAAA,QAC1C;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,UAAI,MAAM,kBAAkB,IAAI,WAAW;AAAA,IAC7C;AAAA,EACF,CAAC;AAED,WAAS,MAAM,UAAU,KAAK,GAAG;AAC/B,UAAM,kBAAkB,UAAU,IAAI,EAAE,EAAE;AAC1C,UAAM,SAAS,MAAM,OAAO,eAAe;AAE3C,QAAI,WAAW,MAAM,UAAU,IAAI,MAAM,KAAK,CAAC,UAAU,IAAI,MAAM,EAAE,qBAAqB;AACxF,gBAAU,IAAI,EAAE,EAAE,KAAK;AAAA,IACzB;AAAA,EACF;AAEA,QAAM,MAAM,EAAE,QAAQ,SAAU,GAAG;AACjC,UAAM,OAAO,MAAM,KAAK,CAAC;AACzB,QAAI,KAAK,UAAU,EAAE,IAAI,SAAS,EAAE,IAAI,OAAO,KAAK,UAAU,CAAC,CAAC;AAChE,QAAI,KAAK,UAAU,EAAE,IAAI,SAAS,EAAE,IAAI,OAAO,KAAK,UAAU,MAAM,KAAK,CAAC,CAAC,CAAC;AAE5E,QAAI,IAAI,EAAE;AACV,QAAI,IAAI,EAAE;AACV,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA,EAAE;AAAA,MACF,EAAE;AAAA,MACF;AAAA,MACA,UAAU,IAAI,EAAE,CAAC;AAAA,MACjB;AAAA,MACA,UAAU,IAAI,EAAE,CAAC;AAAA,IACnB;AACA,QAAI,UAAU,IAAI,EAAE,CAAC,KAAK,UAAU,IAAI,EAAE,CAAC,GAAG;AAC5C,UAAI,KAAK,oCAAoC,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI;AAC7D,UAAI,YAAY,EAAE,CAAC;AACnB,UAAI,YAAY,EAAE,CAAC;AACnB,YAAM,WAAW,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI;AACjC,UAAI,MAAM,EAAE,GAAG;AACb,cAAM,SAAS,MAAM,OAAO,CAAC;AAC7B,kBAAU,IAAI,MAAM,EAAE,sBAAsB;AAC5C,aAAK,cAAc,EAAE;AAAA,MACvB;AACA,UAAI,MAAM,EAAE,GAAG;AACb,cAAM,SAAS,MAAM,OAAO,CAAC;AAC7B,kBAAU,IAAI,MAAM,EAAE,sBAAsB;AAC5C,aAAK,YAAY,EAAE;AAAA,MACrB;AACA,UAAI,KAAK,0BAA0B,GAAG,GAAG,EAAE,IAAI;AAC/C,YAAM,QAAQ,GAAG,GAAG,MAAM,EAAE,IAAI;AAAA,IAClC;AAAA,EACF,CAAC;AACD,MAAI,KAAK,kBAA+B,MAAM,KAAK,CAAC;AACpD,YAAU,OAAO,CAAC;AAElB,MAAI,MAAM,SAAS;AACrB,GA5FsC;AA8F/B,IAAM,YAAY,wBAAC,OAAO,UAAU;AACzC,MAAI,KAAK,gBAAgB,OAAoB,MAAM,KAAK,GAAG,MAAM,SAAS,GAAG,CAAC;AAC9E,MAAI,QAAQ,IAAI;AACd,QAAI,MAAM,aAAa;AACvB;AAAA,EACF;AACA,MAAI,QAAQ,MAAM,MAAM;AACxB,MAAI,cAAc;AAClB,aAAW,QAAQ,OAAO;AACxB,UAAM,WAAW,MAAM,SAAS,IAAI;AACpC,kBAAc,eAAe,SAAS,SAAS;AAAA,EACjD;AAEA,MAAI,CAAC,aAAa;AAChB,QAAI,MAAM,8BAA8B,MAAM,MAAM,CAAC;AACrD;AAAA,EACF;AACA,MAAI,MAAM,YAAY,OAAO,KAAK;AAClC,aAAW,QAAQ,OAAO;AACxB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU,IAAI,IAAI,KAAK,CAAC,UAAU,IAAI,IAAI,EAAE;AAAA,MAC5C,CAAC,MAAM,OAAO,IAAI;AAAA,MAClB,MAAM,KAAK,IAAI;AAAA,MACf,MAAM,SAAS,GAAG;AAAA,MAClB;AAAA,MACA;AAAA,IACF;AACA,QAAI,CAAC,UAAU,IAAI,IAAI,GAAG;AACxB,UAAI,MAAM,iBAAiB,MAAM,KAAK;AAAA,IACxC,WACE,CAAC,UAAU,IAAI,IAAI,EAAE,uBACrB,MAAM,SAAS,IAAI,KACnB,MAAM,SAAS,IAAI,EAAE,SAAS,GAC9B;AACA,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,gBAAgB,MAAM,MAAM;AAClC,UAAI,MAAM,cAAc,YAAY,OAAO,OAAO;AAClD,UAAI,UAAU,IAAI,IAAI,GAAG,aAAa,KAAK;AACzC,cAAM,UAAU,IAAI,IAAI,EAAE,YAAY;AACtC,YAAI,KAAK,cAAc,UAAU,IAAI,IAAI,EAAE,YAAY,KAAK,GAAG;AAAA,MACjE;AAEA,YAAM,eAAe,IAAa,MAAM;AAAA,QACtC,YAAY;AAAA,QACZ,UAAU;AAAA,MACZ,CAAC,EACE,SAAS;AAAA,QACR,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC,EACA,oBAAoB,WAAY;AAC/B,eAAO,CAAC;AAAA,MACV,CAAC;AAEH,UAAI,KAAK,yBAAsC,MAAM,KAAK,CAAC;AAC3D,WAAK,MAAM,OAAO,cAAc,IAAI;AACpC,YAAM,QAAQ,MAAM;AAAA,QAClB,aAAa;AAAA,QACb,IAAI;AAAA,QACJ,aAAa,UAAU,IAAI,IAAI,EAAE;AAAA,QACjC,OAAO,UAAU,IAAI,IAAI,EAAE;AAAA,QAC3B,OAAO;AAAA,MACT,CAAC;AACD,UAAI,KAAK,gCAAgC,MAAM,KAAkB,MAAM,YAAY,CAAC;AACpF,UAAI,MAAM,wBAAqC,MAAM,KAAK,CAAC;AAAA,IAC7D,OAAO;AACL,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA,CAAC,UAAU,IAAI,IAAI,EAAE;AAAA,QACrB;AAAA,QACA,CAAC,MAAM,OAAO,IAAI;AAAA,QAClB;AAAA,QACA,MAAM,SAAS,IAAI,KAAK,MAAM,SAAS,IAAI,EAAE,SAAS;AAAA,QACtD,MAAM,SAAS,GAAG;AAAA,QAClB;AAAA,MACF;AACA,UAAI,MAAM,SAAS;AAAA,IACrB;AAAA,EACF;AAEA,UAAQ,MAAM,MAAM;AACpB,MAAI,KAAK,qBAAqB,KAAK;AACnC,aAAW,QAAQ,OAAO;AACxB,UAAM,OAAO,MAAM,KAAK,IAAI;AAC5B,QAAI,KAAK,mBAAmB,MAAM,IAAI;AACtC,QAAI,MAAM,aAAa;AACrB,gBAAU,KAAK,OAAO,QAAQ,CAAC;AAAA,IACjC;AAAA,EACF;AACF,GAtGyB;AAwGzB,IAAM,SAAS,wBAAC,OAAO,UAAU;AAC/B,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,SAAS,OAAO,OAAO,CAAC,GAAG,KAAK;AACpC,QAAM,QAAQ,CAAC,SAAS;AACtB,UAAM,WAAW,MAAM,SAAS,IAAI;AACpC,UAAM,SAAS,OAAO,OAAO,QAAQ;AACrC,aAAS,CAAC,GAAG,QAAQ,GAAG,MAAM;AAAA,EAChC,CAAC;AAED,SAAO;AACT,GAZe;AAcR,IAAM,uBAAuB,wBAAC,UAAU,OAAO,OAAO,MAAM,SAAS,CAAC,GAAzC;;;AC/XpC,IAAM,kBAAkB,8BAAO,OAAO,OAAO,aAAa,IAAI,eAAe,eAAe;AAC1F,MAAI,KAAK,iCAA8C,MAAM,KAAK,GAAG,aAAa;AAClF,QAAM,MAAM,MAAM,MAAM,EAAE;AAC1B,MAAI,MAAM,kCAAkC,GAAG;AAE/C,QAAM,OAAO,MAAM,OAAO,GAAG,EAAE,KAAK,SAAS,MAAM;AACnD,MAAI,CAAC,MAAM,MAAM,GAAG;AAClB,QAAI,KAAK,sBAAsB,KAAK;AAAA,EACtC,OAAO;AACL,QAAI,KAAK,wBAAwB,MAAM,MAAM,CAAC;AAAA,EAChD;AACA,MAAI,MAAM,MAAM,EAAE,SAAS,GAAG;AAC5B,QAAI,KAAK,mBAAmB,MAAM,KAAK,MAAM,MAAM,EAAE,CAAC,CAAC,CAAC;AAAA,EAC1D;AACA,QAAM,WAAW,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,UAAU;AAC1D,QAAM,YAAY,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,WAAW;AAC5D,QAAM,aAAa,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,YAAY;AAC9D,QAAM,QAAQ,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO;AAIpD,QAAM,QAAQ;AAAA,IACZ,MAAM,MAAM,EAAE,IAAI,eAAgB,GAAG;AACnC,YAAM,OAAO,MAAM,KAAK,CAAC;AACzB,UAAI,kBAAkB,QAAW;AAC/B,cAAM,OAAO,KAAK,MAAM,KAAK,UAAU,cAAc,WAAW,CAAC;AAEjE,YAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA,cAAc;AAAA,QAChB;AACA,cAAM,QAAQ,cAAc,IAAI,IAAI;AACpC,YAAI,CAAC,MAAM,OAAO,CAAC,GAAG;AACpB,cAAI,MAAM,kBAAkB,GAAG,cAAc,EAAE;AAC/C,gBAAM,UAAU,GAAG,cAAc,IAAI,IAAI;AAAA,QAC3C;AAAA,MACF;AACA,UAAI,KAAK,sBAAsB,IAAI,OAAO,KAAK,UAAU,MAAM,KAAK,CAAC,CAAC,CAAC;AACvE,UAAI,MAAM,aAAa;AAErB,YAAI,KAAK,0BAA0B,GAAG,KAAK,OAAO,MAAM,KAAK,CAAC,CAAC;AAI/D,cAAM,EAAE,SAAS,QAAQ,IAAI,MAAM,MAAM;AACzC,aAAK,MAAM,SAAS;AAAA,UAClB,GAAG,KAAK,MAAM,MAAM;AAAA,UACpB,SAAS,UAAU;AAAA,UACnB;AAAA,QACF,CAAC;AAGD,cAAM,IAAI,MAAM;AAAA,UACd;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA,MAAM,KAAK,CAAC;AAAA,UACZ;AAAA,QACF;AACA,cAAM,QAAQ,EAAE;AAChB,yBAAiB,MAAM,KAAK;AAE5B,aAAK,OAAO,EAAE,QAAQ;AACtB,YAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA;AAAA,UAEA,KAAK;AAAA,UACL;AAAA,UACA,KAAK;AAAA;AAAA;AAAA,QAGP;AACA,oBAAY,OAAO,IAAI;AAAA,MACzB,OAAO;AACL,YAAI,MAAM,SAAS,CAAC,EAAE,SAAS,GAAG;AAGhC,cAAI;AAAA,YACF;AAAA,YACA;AAAA,YACA,KAAK;AAAA,YACL;AAAA,YACA,KAAK;AAAA,YACL;AAAA,YACA;AAAA,UACF;AACA,cAAI,MAAM,oBAAoB,KAAK,IAAI,KAAK,CAAC;AAC7C,oBAAU,IAAI,KAAK,IAAI,EAAE,IAAI,oBAAoB,KAAK,IAAI,KAAK,GAAG,KAAK,CAAC;AAAA,QAE1E,OAAO;AACL,cAAI,MAAM,qCAAqC,GAAG,OAAO,MAAM,KAAK,CAAC,GAAG,GAAG;AAC3E,gBAAM,WAAW,OAAO,MAAM,KAAK,CAAC,GAAG,EAAE,QAAQ,YAAY,IAAI,CAAC;AAAA,QACpE;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,eAAe,mCAAY;AAC/B,UAAM,eAAe,MAAM,MAAM,EAAE,IAAI,eAAgB,GAAG;AACxD,YAAM,OAAO,MAAM,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI;AACxC,UAAI,KAAK,UAAU,EAAE,IAAI,SAAS,EAAE,IAAI,OAAO,KAAK,UAAU,CAAC,CAAC;AAChE,UAAI,KAAK,UAAU,EAAE,IAAI,SAAS,EAAE,IAAI,MAAM,GAAG,KAAK,KAAK,UAAU,MAAM,KAAK,CAAC,CAAC,CAAC;AAGnF,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA,EAAE;AAAA,QACF,EAAE;AAAA,QACF;AAAA,QACA,UAAU,IAAI,EAAE,CAAC;AAAA,QACjB,UAAU,IAAI,EAAE,CAAC;AAAA,MACnB;AACA,YAAM,gBAAgB,YAAY,IAAI;AAAA,IACxC,CAAC;AAED,UAAM,QAAQ,IAAI,YAAY;AAAA,EAChC,GArBqB;AAuBrB,QAAM,aAAa;AAEnB,MAAI,KAAK,wBAAwB,KAAK,UAAuB,MAAM,KAAK,CAAC,CAAC;AAE1E,MAAI,KAAK,mDAAmD;AAC5D,MAAI,KAAK,mDAAmD;AAC5D,MAAI,KAAK,mDAAmD;AAE5D,SAAY,KAAK;AAEjB,MAAI,KAAK,uBAAuB,KAAK,UAAuB,MAAM,KAAK,CAAC,CAAC;AAEzE,MAAI,OAAO;AACX,MAAI,EAAE,yBAAyB,IAAI,wBAAwB,UAAU;AACrE,QAAM,QAAQ;AAAA,IACZ,qBAAqB,KAAK,EAAE,IAAI,eAAgB,GAAG;AACjD,YAAM,OAAO,MAAM,KAAK,CAAC;AACzB,UAAI;AAAA,QACF,qBAAqB,IAAI,QAAQ,KAAK;AAAA,QACtC,MAAM,KAAK;AAAA,QACX;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA,KAAK;AAAA,MACP;AACA,UAAI,MAAM,aAAa;AAErB,aAAK,KAAK;AAEV,YAAI;AAAA,UACF;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,MAAM,OAAO,CAAC;AAAA,QAChB;AACA,kBAAU,IAAI,KAAK,EAAE,EAAE,OAAO;AAC9B,qBAAa,IAAI;AAAA,MACnB,OAAO;AAEL,YAAI,MAAM,SAAS,CAAC,EAAE,SAAS,GAAG;AAChC,cAAI;AAAA,YACF;AAAA,YACA;AAAA,YACA,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,MAAM,OAAO,CAAC;AAAA,UAChB;AACA,eAAK,UAAU;AACf,gBAAM,KAAK,KAAK,QAAQ;AACxB,gBAAM,cAAc,MAAM,UAAU,KAAK;AACzC,gBAAM,cAAc,MAAM,WAAW,UAAU;AAC/C,gBAAM,UAAU,cAAc,eAAe;AAC7C,cAAI,MAAM,WAAW,SAAS,eAAe,aAAa,eAAe,WAAW;AACpF,gBAAM,cAAc,UAAU,IAAI;AAGlC,oBAAU,IAAI,KAAK,EAAE,EAAE,OAAO;AAAA,QAChC,OAAO;AAEL,gBAAM,SAAS,MAAM,KAAK,KAAK,QAAQ;AACvC,eAAK,KAAK,2BAA2B;AACrC,cAAI;AAAA,YACF;AAAA,YACA,KAAK;AAAA,YACL;AAAA,YACA,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL;AAAA,YACA,KAAK;AAAA,YACL;AAAA,YACA;AAAA,YACA,QAAQ;AAAA,YACR;AAAA,UACF;AAEA,uBAAa,IAAI;AAAA,QACnB;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAGA,QAAM,MAAM,EAAE,QAAQ,SAAU,GAAG;AACjC,UAAM,OAAO,MAAM,KAAK,CAAC;AACzB,QAAI,KAAK,UAAU,EAAE,IAAI,SAAS,EAAE,IAAI,OAAO,KAAK,UAAU,IAAI,GAAG,IAAI;AAEzE,SAAK,OAAO,QAAQ,CAAC,UAAW,MAAM,KAAK,2BAA2B,CAAE;AACxE,UAAM,YAAY,MAAM,KAAK,EAAE,CAAC;AAChC,QAAI,UAAU,MAAM,KAAK,EAAE,CAAC;AAC5B,UAAM,QAAQ,WAAW,WAAW,MAAM,WAAW,aAAa,WAAW,SAAS,EAAE;AACxF,sBAAkB,MAAM,KAAK;AAAA,EAC/B,CAAC;AAED,QAAM,MAAM,EAAE,QAAQ,SAAU,GAAG;AACjC,UAAM,IAAI,MAAM,KAAK,CAAC;AACtB,QAAI,KAAK,GAAG,EAAE,MAAM,EAAE,IAAI;AAC1B,QAAI,EAAE,SAAS;AACb,aAAO,EAAE;AAAA,IACX;AAAA,EACF,CAAC;AACD,MAAI,KAAK,uCAAuC,MAAM,IAAI;AAC1D,SAAO,EAAE,MAAM,KAAK;AACtB,GA/OwB;AAiPjB,IAAM,SAAS,8BAAO,aAAa,QAAQ;AAChD,QAAM,QAAQ,IAAa,MAAM;AAAA,IAC/B,YAAY;AAAA,IACZ,UAAU;AAAA,EACZ,CAAC,EACE,SAAS;AAAA,IACR,SAAS,YAAY;AAAA,IACrB,SACE,YAAY,QAAQ,eACpB,YAAY,QAAQ,WAAW,eAC/B,YAAY;AAAA,IACd,SACE,YAAY,QAAQ,eACpB,YAAY,QAAQ,WAAW,eAC/B,YAAY;AAAA,IACd,SAAS;AAAA,IACT,SAAS;AAAA,EACX,CAAC,EACA,oBAAoB,WAAY;AAC/B,WAAO,CAAC;AAAA,EACV,CAAC;AACH,QAAM,UAAU,IAAI,OAAO,GAAG;AAC9B,kBAAc,SAAS,YAAY,SAAS,YAAY,MAAM,YAAY,SAAS;AACnF,EAAAC,OAAW;AACX,EAAAA,OAAW;AACX,QAAc;AACd,EAAAA,OAAc;AAEd,cAAY,MAAM,QAAQ,CAAC,SAAS;AAClC,UAAM,QAAQ,KAAK,IAAI,EAAE,GAAG,KAAK,CAAC;AAClC,QAAI,KAAK,UAAU;AACjB,YAAM,UAAU,KAAK,IAAI,KAAK,QAAQ;AAAA,IACxC;AAAA,EACF,CAAC;AAED,MAAI,MAAM,UAAU,YAAY,KAAK;AACrC,cAAY,MAAM,QAAQ,CAAC,SAAS;AAElC,QAAI,KAAK,UAAU,KAAK,KAAK;AAC3B,YAAM,SAAS,KAAK;AACpB,YAAM,aAAa,SAAS,QAAQ,SAAS;AAC7C,YAAM,aAAa,SAAS,QAAQ,SAAS;AAC7C,YAAM,OAAO,MAAM,KAAK,MAAM;AAC9B,YAAM,QAAQ,YAAY;AAAA,QACxB,OAAO;AAAA,QACP,IAAI;AAAA,QACJ,UAAU,KAAK;AAAA,QACf,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,OAAO;AAAA;AAAA,QAEP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,CAAC;AACD,YAAM,UAAU,YAAY,KAAK,QAAQ;AACzC,YAAM,QAAQ,YAAY;AAAA,QACxB,OAAO;AAAA,QACP,IAAI;AAAA,QACJ,UAAU,KAAK;AAAA,QACf,YAAY;AAAA,QACZ,SAAS;AAAA;AAAA,QAET,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,CAAC;AACD,YAAM,UAAU,YAAY,KAAK,QAAQ;AAEzC,YAAM,QAAQ,gBAAgB,IAAI;AAClC,YAAM,UAAU,gBAAgB,IAAI;AACpC,YAAM,QAAQ,gBAAgB,IAAI;AAClC,YAAM,QAAQ;AACd,YAAM,eAAe;AACrB,YAAM,KAAK,SAAS;AACpB,cAAQ,iBAAiB;AACzB,cAAQ,eAAe;AACvB,cAAQ,KAAK,SAAS;AACtB,YAAM,QAAQ;AACd,UAAI,KAAK,SAAS;AAChB,cAAM,cAAc;AACpB,cAAM,YAAY;AAAA,MACpB;AACA,YAAM,KAAK,SAAS;AACpB,YAAM,iBAAiB;AACvB,YAAM,QAAQ,QAAQ,YAAY,OAAO,SAAS,mBAAmB;AACrE,YAAM,QAAQ,YAAY,YAAY,SAAS,SAAS,mBAAmB;AAC3E,YAAM,QAAQ,YAAY,QAAQ,OAAO,SAAS,oBAAoB;AAAA,IACxE,OAAO;AACL,YAAM,QAAQ,KAAK,OAAO,KAAK,KAAK,EAAE,GAAG,KAAK,GAAG,KAAK,EAAE;AAAA,IAC1D;AAAA,EACF,CAAC;AAED,MAAI,KAAK,mBAAmB,KAAK,UAAuB,MAAM,KAAK,CAAC,CAAC;AACrE,yBAAuB,KAAK;AAC5B,MAAI,KAAK,oBAAoB,KAAK,UAAuB,MAAM,KAAK,CAAC,CAAC;AACtE,QAAM,aAAa,UAAU;AAC7B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,EACF;AACF,GA5GsB;", "names": ["clear", "data", "clear"]}