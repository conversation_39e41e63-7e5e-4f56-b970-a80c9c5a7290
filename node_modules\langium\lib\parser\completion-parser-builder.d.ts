/******************************************************************************
 * Copyright 2022 TypeFox GmbH
 * This program and the accompanying materials are made available under the
 * terms of the MIT License, which is available in the project root.
 ******************************************************************************/
import type { LangiumCoreServices } from '../services.js';
import { LangiumCompletionParser } from './langium-parser.js';
export declare function createCompletionParser(services: LangiumCoreServices): LangiumCompletionParser;
//# sourceMappingURL=completion-parser-builder.d.ts.map