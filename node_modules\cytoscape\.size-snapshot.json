{"build/cytoscape.umd.js": {"bundled": 1021871, "minified": 365007, "gzipped": 112477}, "build/cytoscape.cjs.js": {"bundled": 871227, "minified": 371337, "gzipped": 111837}, "build/cytoscape.esm.js": {"bundled": 870994, "minified": 371149, "gzipped": 111794, "treeshaked": {"rollup": {"code": 346486, "import_statements": 132}, "webpack": {"code": 347998}}}, "build/cytoscape.esm.min.js": {"bundled": 364812, "minified": 364287, "gzipped": 112306, "treeshaked": {"rollup": {"code": 363750, "import_statements": 0}, "webpack": {"code": 365038}}}, "build\\cytoscape.umd.js": {"bundled": 942118, "minified": 351104, "gzipped": 109191}, "build\\cytoscape.esm.min.js": {"bundled": 350909, "minified": 350411, "gzipped": 109012, "treeshaked": {"rollup": {"code": 349903, "import_statements": 0}, "webpack": {"code": 351193}}}, "build\\cytoscape.cjs.js": {"bundled": 868703, "minified": 370553, "gzipped": 111528}, "build\\cytoscape.esm.js": {"bundled": 868530, "minified": 370410, "gzipped": 111491, "treeshaked": {"rollup": {"code": 345754, "import_statements": 51}, "webpack": {"code": 347140}}}}