/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import type { FirebaseNamespace } from '@firebase/app-types';
import * as types from '@firebase/firestore-types';
import './register-module';
/**
 * Registers the main Firestore build with the components framework.
 * Persistence can be enabled via `firebase.firestore().enablePersistence()`.
 */
export declare function registerFirestore(instance: FirebaseNamespace): void;
declare module '@firebase/app-compat' {
    interface FirebaseNamespace {
        firestore: {
            (app?: FirebaseApp): types.FirebaseFirestore;
            Blob: typeof types.Blob;
            CollectionReference: typeof types.CollectionReference;
            DocumentReference: typeof types.DocumentReference;
            DocumentSnapshot: typeof types.DocumentSnapshot;
            FieldPath: typeof types.FieldPath;
            FieldValue: typeof types.FieldValue;
            Firestore: typeof types.FirebaseFirestore;
            GeoPoint: typeof types.GeoPoint;
            Query: typeof types.Query;
            QueryDocumentSnapshot: typeof types.QueryDocumentSnapshot;
            QuerySnapshot: typeof types.QuerySnapshot;
            Timestamp: typeof types.Timestamp;
            Transaction: typeof types.Transaction;
            WriteBatch: typeof types.WriteBatch;
            setLogLevel: typeof types.setLogLevel;
        };
    }
    interface FirebaseApp {
        firestore?(): types.FirebaseFirestore;
    }
}

import { FirebaseApp as FirebaseAppCompat } from "@firebase/app-compat";
import { type DocumentData, type WithFieldValue, type DocumentReference, type CollectionReference, type Query, type EmulatorMockTokenOptions, type PersistenceSettings, type AggregateSpec, type AggregateQuerySnapshot, type AggregateField, type DocumentSnapshot, type QuerySnapshot, type Firestore, type PersistentCacheIndexManager, type FirestoreSettings, type LoadBundleTask, type FirestoreError, type Unsubscribe, type SnapshotListenOptions, type QueryCompositeFilterConstraint, type QueryNonFilterConstraint, type QueryConstraint, type Transaction, type TransactionOptions, type PartialWithFieldValue, type SetOptions, type IndexConfiguration, type UpdateData, type FieldPath, type WriteBatch } from "@firebase/firestore";
declare module "@firebase/firestore" {
    function addDoc<AppModelType, DbModelType extends DocumentData>(reference: types.CollectionReference<AppModelType, DbModelType>, data: WithFieldValue<AppModelType>): Promise<DocumentReference<AppModelType, DbModelType>>;
    function clearIndexedDbPersistence(firestore: types.FirebaseFirestore): Promise<void>;
    function collection(firestore: types.FirebaseFirestore, path: string, ...pathSegments: string[]): CollectionReference<DocumentData, DocumentData>;
    function collection<AppModelType, DbModelType extends DocumentData>(reference: types.CollectionReference<AppModelType, DbModelType>, path: string, ...pathSegments: string[]): CollectionReference<DocumentData, DocumentData>;
    function collection<AppModelType, DbModelType extends DocumentData>(reference: types.DocumentReference<AppModelType, DbModelType>, path: string, ...pathSegments: string[]): CollectionReference<DocumentData, DocumentData>;
    function collectionGroup(firestore: types.FirebaseFirestore, collectionId: string): Query<DocumentData, DocumentData>;
    function connectFirestoreEmulator(firestore: types.FirebaseFirestore, host: string, port: number, options?: {
        mockUserToken?: EmulatorMockTokenOptions | string;
    }): void;
    function deleteDoc<AppModelType, DbModelType extends DocumentData>(reference: types.DocumentReference<AppModelType, DbModelType>): Promise<void>;
    function disableNetwork(firestore: types.FirebaseFirestore): Promise<void>;
    function doc(firestore: types.FirebaseFirestore, path: string, ...pathSegments: string[]): DocumentReference<DocumentData, DocumentData>;
    function doc<AppModelType, DbModelType extends DocumentData>(reference: types.CollectionReference<AppModelType, DbModelType>, path?: string, ...pathSegments: string[]): DocumentReference<AppModelType, DbModelType>;
    function doc<AppModelType, DbModelType extends DocumentData>(reference: types.DocumentReference<AppModelType, DbModelType>, path: string, ...pathSegments: string[]): DocumentReference<DocumentData, DocumentData>;
    function enableIndexedDbPersistence(firestore: types.FirebaseFirestore, persistenceSettings?: PersistenceSettings): Promise<void>;
    function enableMultiTabIndexedDbPersistence(firestore: types.FirebaseFirestore): Promise<void>;
    function enableNetwork(firestore: types.FirebaseFirestore): Promise<void>;
    function getAggregateFromServer<AggregateSpecType extends AggregateSpec, AppModelType, DbModelType extends DocumentData>(query: types.Query<AppModelType, DbModelType>, aggregateSpec: AggregateSpecType): Promise<AggregateQuerySnapshot<AggregateSpecType, AppModelType, DbModelType>>;
    function getCountFromServer<AppModelType, DbModelType extends DocumentData>(query: types.Query<AppModelType, DbModelType>): Promise<AggregateQuerySnapshot<{
        count: AggregateField<number>;
    }, AppModelType, DbModelType>>;
    function getDoc<AppModelType, DbModelType extends DocumentData>(reference: types.DocumentReference<AppModelType, DbModelType>): Promise<DocumentSnapshot<AppModelType, DbModelType>>;
    function getDocFromCache<AppModelType, DbModelType extends DocumentData>(reference: types.DocumentReference<AppModelType, DbModelType>): Promise<DocumentSnapshot<AppModelType, DbModelType>>;
    function getDocFromServer<AppModelType, DbModelType extends DocumentData>(reference: types.DocumentReference<AppModelType, DbModelType>): Promise<DocumentSnapshot<AppModelType, DbModelType>>;
    function getDocs<AppModelType, DbModelType extends DocumentData>(query: types.Query<AppModelType, DbModelType>): Promise<QuerySnapshot<AppModelType, DbModelType>>;
    function getDocsFromCache<AppModelType, DbModelType extends DocumentData>(query: types.Query<AppModelType, DbModelType>): Promise<QuerySnapshot<AppModelType, DbModelType>>;
    function getDocsFromServer<AppModelType, DbModelType extends DocumentData>(query: types.Query<AppModelType, DbModelType>): Promise<QuerySnapshot<AppModelType, DbModelType>>;
    function getFirestore(app: FirebaseAppCompat): Firestore;
    function getFirestore(app: FirebaseAppCompat, databaseId: string): Firestore;
    function getPersistentCacheIndexManager(firestore: types.FirebaseFirestore): PersistentCacheIndexManager | null;
    function initializeFirestore(app: FirebaseAppCompat, settings: FirestoreSettings, databaseId?: string): Firestore;
    function loadBundle(firestore: types.FirebaseFirestore, bundleData: ReadableStream<Uint8Array> | ArrayBuffer | string): LoadBundleTask;
    function namedQuery(firestore: types.FirebaseFirestore, name: string): Promise<Query | null>;
    function onSnapshot<AppModelType, DbModelType extends DocumentData>(reference: types.DocumentReference<AppModelType, DbModelType>, observer: {
        next?: (snapshot: DocumentSnapshot<AppModelType, DbModelType>) => void;
        error?: (error: FirestoreError) => void;
        complete?: () => void;
    }): Unsubscribe;
    function onSnapshot<AppModelType, DbModelType extends DocumentData>(reference: types.DocumentReference<AppModelType, DbModelType>, options: SnapshotListenOptions, observer: {
        next?: (snapshot: DocumentSnapshot<AppModelType, DbModelType>) => void;
        error?: (error: FirestoreError) => void;
        complete?: () => void;
    }): Unsubscribe;
    function onSnapshot<AppModelType, DbModelType extends DocumentData>(reference: types.DocumentReference<AppModelType, DbModelType>, onNext: (snapshot: DocumentSnapshot<AppModelType, DbModelType>) => void, onError?: (error: FirestoreError) => void, onCompletion?: () => void): Unsubscribe;
    function onSnapshot<AppModelType, DbModelType extends DocumentData>(reference: types.DocumentReference<AppModelType, DbModelType>, options: SnapshotListenOptions, onNext: (snapshot: DocumentSnapshot<AppModelType, DbModelType>) => void, onError?: (error: FirestoreError) => void, onCompletion?: () => void): Unsubscribe;
    function onSnapshot<AppModelType, DbModelType extends DocumentData>(query: types.Query<AppModelType, DbModelType>, observer: {
        next?: (snapshot: QuerySnapshot<AppModelType, DbModelType>) => void;
        error?: (error: FirestoreError) => void;
        complete?: () => void;
    }): Unsubscribe;
    function onSnapshot<AppModelType, DbModelType extends DocumentData>(query: types.Query<AppModelType, DbModelType>, options: SnapshotListenOptions, observer: {
        next?: (snapshot: QuerySnapshot<AppModelType, DbModelType>) => void;
        error?: (error: FirestoreError) => void;
        complete?: () => void;
    }): Unsubscribe;
    function onSnapshot<AppModelType, DbModelType extends DocumentData>(query: types.Query<AppModelType, DbModelType>, onNext: (snapshot: QuerySnapshot<AppModelType, DbModelType>) => void, onError?: (error: FirestoreError) => void, onCompletion?: () => void): Unsubscribe;
    function onSnapshot<AppModelType, DbModelType extends DocumentData>(query: types.Query<AppModelType, DbModelType>, options: SnapshotListenOptions, onNext: (snapshot: QuerySnapshot<AppModelType, DbModelType>) => void, onError?: (error: FirestoreError) => void, onCompletion?: () => void): Unsubscribe;
    function onSnapshotsInSync(firestore: types.FirebaseFirestore, observer: {
        next?: (value: void) => void;
        error?: (error: FirestoreError) => void;
        complete?: () => void;
    }): Unsubscribe;
    function onSnapshotsInSync(firestore: types.FirebaseFirestore, onSync: () => void): Unsubscribe;
    function query<AppModelType, DbModelType extends DocumentData>(query: types.Query<AppModelType, DbModelType>, compositeFilter: QueryCompositeFilterConstraint, ...queryConstraints: QueryNonFilterConstraint[]): Query<AppModelType, DbModelType>;
    function query<AppModelType, DbModelType extends DocumentData>(query: types.Query<AppModelType, DbModelType>, ...queryConstraints: QueryConstraint[]): Query<AppModelType, DbModelType>;
    function queryEqual<AppModelType, DbModelType extends DocumentData>(left: types.Query<AppModelType, DbModelType>, right: types.Query<AppModelType, DbModelType>): boolean;
    function runTransaction<T>(firestore: types.FirebaseFirestore, updateFunction: (transaction: Transaction) => Promise<T>, options?: TransactionOptions): Promise<T>;
    function setDoc<AppModelType, DbModelType extends DocumentData>(reference: types.DocumentReference<AppModelType, DbModelType>, data: WithFieldValue<AppModelType>): Promise<void>;
    function setDoc<AppModelType, DbModelType extends DocumentData>(reference: types.DocumentReference<AppModelType, DbModelType>, data: PartialWithFieldValue<AppModelType>, options: SetOptions): Promise<void>;
    function setIndexConfiguration(firestore: types.FirebaseFirestore, configuration: IndexConfiguration): Promise<void>;
    function setIndexConfiguration(firestore: types.FirebaseFirestore, json: string): Promise<void>;
    function terminate(firestore: types.FirebaseFirestore): Promise<void>;
    function updateDoc<AppModelType, DbModelType extends DocumentData>(reference: types.DocumentReference<AppModelType, DbModelType>, data: UpdateData<DbModelType>): Promise<void>;
    function updateDoc<AppModelType, DbModelType extends DocumentData>(reference: types.DocumentReference<AppModelType, DbModelType>, field: string | FieldPath, value: unknown, ...moreFieldsAndValues: unknown[]): Promise<void>;
    function waitForPendingWrites(firestore: types.FirebaseFirestore): Promise<void>;
    function writeBatch(firestore: types.FirebaseFirestore): WriteBatch;
}
