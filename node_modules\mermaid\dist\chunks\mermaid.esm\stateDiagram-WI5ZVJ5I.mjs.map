{"version": 3, "sources": ["../../../src/diagrams/state/id-cache.js", "../../../src/diagrams/state/shapes.js", "../../../src/diagrams/state/stateRenderer.js", "../../../src/diagrams/state/stateDiagram.ts"], "sourcesContent": ["const idCache = {};\n\nexport const set = (key, val) => {\n  idCache[key] = val;\n};\n\nexport const get = (k) => idCache[k];\nexport const keys = () => Object.keys(idCache);\nexport const size = () => keys().length;\n\nexport default {\n  get,\n  set,\n  keys,\n  size,\n};\n", "import { line, curveBasis } from 'd3';\nimport idCache from './id-cache.js';\nimport { StateDB } from './stateDb.js';\nimport utils from '../../utils.js';\nimport common from '../common/common.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { log } from '../../logger.js';\n\n/**\n * Draws a start state as a black circle\n *\n * @param {any} g\n */\nexport const drawStartState = (g) =>\n  g\n    .append('circle')\n    // .style('stroke', 'black')\n    // .style('fill', 'black')\n    .attr('class', 'start-state')\n    .attr('r', getConfig().state.sizeUnit)\n    .attr('cx', getConfig().state.padding + getConfig().state.sizeUnit)\n    .attr('cy', getConfig().state.padding + getConfig().state.sizeUnit);\n\n/**\n * Draws a start state as a black circle\n *\n * @param {any} g\n */\nexport const drawDivider = (g) =>\n  g\n    .append('line')\n    .style('stroke', 'grey')\n    .style('stroke-dasharray', '3')\n    .attr('x1', getConfig().state.textHeight)\n    .attr('class', 'divider')\n    .attr('x2', getConfig().state.textHeight * 2)\n    .attr('y1', 0)\n    .attr('y2', 0);\n\n/**\n * Draws a an end state as a black circle\n *\n * @param {any} g\n * @param {any} stateDef\n */\nexport const drawSimpleState = (g, stateDef) => {\n  const state = g\n    .append('text')\n    .attr('x', 2 * getConfig().state.padding)\n    .attr('y', getConfig().state.textHeight + 2 * getConfig().state.padding)\n    .attr('font-size', getConfig().state.fontSize)\n    .attr('class', 'state-title')\n    .text(stateDef.id);\n\n  const classBox = state.node().getBBox();\n  g.insert('rect', ':first-child')\n    .attr('x', getConfig().state.padding)\n    .attr('y', getConfig().state.padding)\n    .attr('width', classBox.width + 2 * getConfig().state.padding)\n    .attr('height', classBox.height + 2 * getConfig().state.padding)\n    .attr('rx', getConfig().state.radius);\n\n  return state;\n};\n\n/**\n * Draws a state with descriptions\n *\n * @param {any} g The d3 svg object to add the state to\n * @param {any} stateDef\n * @returns {any} The d3 svg state\n */\nexport const drawDescrState = (g, stateDef) => {\n  const addTspan = function (textEl, txt, isFirst) {\n    const tSpan = textEl\n      .append('tspan')\n      .attr('x', 2 * getConfig().state.padding)\n      .text(txt);\n    if (!isFirst) {\n      tSpan.attr('dy', getConfig().state.textHeight);\n    }\n  };\n  const title = g\n    .append('text')\n    .attr('x', 2 * getConfig().state.padding)\n    .attr('y', getConfig().state.textHeight + 1.3 * getConfig().state.padding)\n    .attr('font-size', getConfig().state.fontSize)\n    .attr('class', 'state-title')\n    .text(stateDef.descriptions[0]);\n\n  const titleBox = title.node().getBBox();\n  const titleHeight = titleBox.height;\n\n  const description = g\n    .append('text') // text label for the x axis\n    .attr('x', getConfig().state.padding)\n    .attr(\n      'y',\n      titleHeight +\n        getConfig().state.padding * 0.4 +\n        getConfig().state.dividerMargin +\n        getConfig().state.textHeight\n    )\n    .attr('class', 'state-description');\n\n  let isFirst = true;\n  let isSecond = true;\n  stateDef.descriptions.forEach(function (descr) {\n    if (!isFirst) {\n      addTspan(description, descr, isSecond);\n      isSecond = false;\n    }\n    isFirst = false;\n  });\n\n  const descrLine = g\n    .append('line') // text label for the x axis\n    .attr('x1', getConfig().state.padding)\n    .attr('y1', getConfig().state.padding + titleHeight + getConfig().state.dividerMargin / 2)\n    .attr('y2', getConfig().state.padding + titleHeight + getConfig().state.dividerMargin / 2)\n    .attr('class', 'descr-divider');\n  const descrBox = description.node().getBBox();\n  const width = Math.max(descrBox.width, titleBox.width);\n\n  descrLine.attr('x2', width + 3 * getConfig().state.padding);\n  // const classBox = title.node().getBBox();\n\n  g.insert('rect', ':first-child')\n    .attr('x', getConfig().state.padding)\n    .attr('y', getConfig().state.padding)\n    .attr('width', width + 2 * getConfig().state.padding)\n    .attr('height', descrBox.height + titleHeight + 2 * getConfig().state.padding)\n    .attr('rx', getConfig().state.radius);\n\n  return g;\n};\n\n/** Adds the creates a box around the existing content and adds a panel for the id on top of the content. */\n/**\n * Function that creates an title row and a frame around a substate for a composite state diagram.\n * The function returns a new d3 svg object with updated width and height properties;\n *\n * @param {any} g The d3 svg object for the substate to framed\n * @param {any} stateDef The info about the\n * @param {any} altBkg\n */\nexport const addTitleAndBox = (g, stateDef, altBkg) => {\n  const pad = getConfig().state.padding;\n  const dblPad = 2 * getConfig().state.padding;\n  const orgBox = g.node().getBBox();\n  const orgWidth = orgBox.width;\n  const orgX = orgBox.x;\n\n  const title = g\n    .append('text')\n    .attr('x', 0)\n    .attr('y', getConfig().state.titleShift)\n    .attr('font-size', getConfig().state.fontSize)\n    .attr('class', 'state-title')\n    .text(stateDef.id);\n\n  const titleBox = title.node().getBBox();\n  const titleWidth = titleBox.width + dblPad;\n  let width = Math.max(titleWidth, orgWidth); // + dblPad;\n  if (width === orgWidth) {\n    width = width + dblPad;\n  }\n  let startX;\n  // const lineY = 1 - getConfig().state.textHeight;\n  // const descrLine = g\n  //   .append('line') // text label for the x axis\n  //   .attr('x1', 0)\n  //   .attr('y1', lineY)\n  //   .attr('y2', lineY)\n  //   .attr('class', 'descr-divider');\n\n  const graphBox = g.node().getBBox();\n  // descrLine.attr('x2', graphBox.width + getConfig().state.padding);\n\n  if (stateDef.doc) {\n    // console.warn(\n    //   stateDef.id,\n    //   'orgX: ',\n    //   orgX,\n    //   'width: ',\n    //   width,\n    //   'titleWidth: ',\n    //   titleWidth,\n    //   'orgWidth: ',\n    //   orgWidth,\n    //   'width',\n    //   width\n    // );\n  }\n\n  startX = orgX - pad;\n  if (titleWidth > orgWidth) {\n    startX = (orgWidth - width) / 2 + pad;\n  }\n  if (Math.abs(orgX - graphBox.x) < pad && titleWidth > orgWidth) {\n    startX = orgX - (titleWidth - orgWidth) / 2;\n  }\n\n  const lineY = 1 - getConfig().state.textHeight;\n  // White color\n  g.insert('rect', ':first-child')\n    .attr('x', startX)\n    .attr('y', lineY)\n    .attr('class', altBkg ? 'alt-composit' : 'composit') // cspell:disable-line\n    .attr('width', width)\n    .attr(\n      'height',\n      graphBox.height + getConfig().state.textHeight + getConfig().state.titleShift + 1\n    )\n    .attr('rx', '0');\n\n  title.attr('x', startX + pad);\n  if (titleWidth <= orgWidth) {\n    title.attr('x', orgX + (width - dblPad) / 2 - titleWidth / 2 + pad);\n  }\n\n  // Title background\n  g.insert('rect', ':first-child')\n    .attr('x', startX)\n    .attr(\n      'y',\n      getConfig().state.titleShift - getConfig().state.textHeight - getConfig().state.padding\n    )\n    .attr('width', width)\n    // Just needs to be higher then the descr line, will be clipped by the white color box\n    .attr('height', getConfig().state.textHeight * 3)\n    .attr('rx', getConfig().state.radius);\n\n  // Full background\n  g.insert('rect', ':first-child')\n    .attr('x', startX)\n    .attr(\n      'y',\n      getConfig().state.titleShift - getConfig().state.textHeight - getConfig().state.padding\n    )\n    .attr('width', width)\n    .attr('height', graphBox.height + 3 + 2 * getConfig().state.textHeight)\n    .attr('rx', getConfig().state.radius);\n\n  return g;\n};\n\nconst drawEndState = (g) => {\n  g.append('circle')\n    // .style('stroke', 'black')\n    // .style('fill', 'white')\n    .attr('class', 'end-state-outer')\n    .attr('r', getConfig().state.sizeUnit + getConfig().state.miniPadding)\n    .attr(\n      'cx',\n      getConfig().state.padding + getConfig().state.sizeUnit + getConfig().state.miniPadding\n    )\n    .attr(\n      'cy',\n      getConfig().state.padding + getConfig().state.sizeUnit + getConfig().state.miniPadding\n    );\n\n  return (\n    g\n      .append('circle')\n      // .style('stroke', 'black')\n      // .style('fill', 'black')\n      .attr('class', 'end-state-inner')\n      .attr('r', getConfig().state.sizeUnit)\n      .attr('cx', getConfig().state.padding + getConfig().state.sizeUnit + 2)\n      .attr('cy', getConfig().state.padding + getConfig().state.sizeUnit + 2)\n  );\n};\nconst drawForkJoinState = (g, stateDef) => {\n  let width = getConfig().state.forkWidth;\n  let height = getConfig().state.forkHeight;\n\n  if (stateDef.parentId) {\n    let tmp = width;\n    width = height;\n    height = tmp;\n  }\n  return g\n    .append('rect')\n    .style('stroke', 'black')\n    .style('fill', 'black')\n    .attr('width', width)\n    .attr('height', height)\n    .attr('x', getConfig().state.padding)\n    .attr('y', getConfig().state.padding);\n};\n\nexport const drawText = function (elem, textData) {\n  // Remove and ignore br:s\n  const nText = textData.text.replace(common.lineBreakRegex, ' ');\n\n  const textElem = elem.append('text');\n  textElem.attr('x', textData.x);\n  textElem.attr('y', textData.y);\n  textElem.style('text-anchor', textData.anchor);\n  textElem.attr('fill', textData.fill);\n  if (textData.class !== undefined) {\n    textElem.attr('class', textData.class);\n  }\n\n  const span = textElem.append('tspan');\n  span.attr('x', textData.x + textData.textMargin * 2);\n  span.attr('fill', textData.fill);\n  span.text(nText);\n\n  return textElem;\n};\n\nconst _drawLongText = (_text, x, y, g) => {\n  let textHeight = 0;\n\n  const textElem = g.append('text');\n  textElem.style('text-anchor', 'start');\n  textElem.attr('class', 'noteText');\n\n  let text = _text.replace(/\\r\\n/g, '<br/>');\n  text = text.replace(/\\n/g, '<br/>');\n  const lines = text.split(common.lineBreakRegex);\n\n  let tHeight = 1.25 * getConfig().state.noteMargin;\n  for (const line of lines) {\n    const txt = line.trim();\n\n    if (txt.length > 0) {\n      const span = textElem.append('tspan');\n      span.text(txt);\n      if (tHeight === 0) {\n        const textBounds = span.node().getBBox();\n        tHeight += textBounds.height;\n      }\n      textHeight += tHeight;\n      span.attr('x', x + getConfig().state.noteMargin);\n      span.attr('y', y + textHeight + 1.25 * getConfig().state.noteMargin);\n    }\n  }\n  return { textWidth: textElem.node().getBBox().width, textHeight };\n};\n\n/**\n * Draws a note to the diagram\n *\n * @param text - The text of the given note.\n * @param g - The element the note is attached to.\n */\n\nexport const drawNote = (text, g) => {\n  g.attr('class', 'state-note');\n  const note = g.append('rect').attr('x', 0).attr('y', getConfig().state.padding);\n  const rectElem = g.append('g');\n\n  const { textWidth, textHeight } = _drawLongText(text, 0, 0, rectElem);\n  note.attr('height', textHeight + 2 * getConfig().state.noteMargin);\n  note.attr('width', textWidth + getConfig().state.noteMargin * 2);\n\n  return note;\n};\n\n/**\n * Starting point for drawing a state. The function finds out the specifics about the state and\n * renders with appropriate function.\n *\n * @param {any} elem\n * @param {any} stateDef\n */\n\nexport const drawState = function (elem, stateDef) {\n  const id = stateDef.id;\n  const stateInfo = {\n    id: id,\n    label: stateDef.id,\n    width: 0,\n    height: 0,\n  };\n\n  const g = elem.append('g').attr('id', id).attr('class', 'stateGroup');\n\n  if (stateDef.type === 'start') {\n    drawStartState(g);\n  }\n  if (stateDef.type === 'end') {\n    drawEndState(g);\n  }\n  if (stateDef.type === 'fork' || stateDef.type === 'join') {\n    drawForkJoinState(g, stateDef);\n  }\n  if (stateDef.type === 'note') {\n    drawNote(stateDef.note.text, g);\n  }\n  if (stateDef.type === 'divider') {\n    drawDivider(g);\n  }\n  if (stateDef.type === 'default' && stateDef.descriptions.length === 0) {\n    drawSimpleState(g, stateDef);\n  }\n  if (stateDef.type === 'default' && stateDef.descriptions.length > 0) {\n    drawDescrState(g, stateDef);\n  }\n\n  const stateBox = g.node().getBBox();\n  stateInfo.width = stateBox.width + 2 * getConfig().state.padding;\n  stateInfo.height = stateBox.height + 2 * getConfig().state.padding;\n\n  idCache.set(id, stateInfo);\n  // stateCnt++;\n  return stateInfo;\n};\n\nlet edgeCount = 0;\nexport const drawEdge = function (elem, path, relation) {\n  const getRelationType = function (type) {\n    switch (type) {\n      case StateDB.relationType.AGGREGATION:\n        return 'aggregation';\n      case StateDB.relationType.EXTENSION:\n        return 'extension';\n      case StateDB.relationType.COMPOSITION:\n        return 'composition';\n      case StateDB.relationType.DEPENDENCY:\n        return 'dependency';\n    }\n  };\n\n  path.points = path.points.filter((p) => !Number.isNaN(p.y));\n\n  // The data for our line\n  const lineData = path.points;\n\n  // This is the accessor function we talked about above\n  const lineFunction = line()\n    .x(function (d) {\n      return d.x;\n    })\n    .y(function (d) {\n      return d.y;\n    })\n    .curve(curveBasis);\n\n  const svgPath = elem\n    .append('path')\n    .attr('d', lineFunction(lineData))\n    .attr('id', 'edge' + edgeCount)\n    .attr('class', 'transition');\n  let url = '';\n  if (getConfig().state.arrowMarkerAbsolute) {\n    url =\n      window.location.protocol +\n      '//' +\n      window.location.host +\n      window.location.pathname +\n      window.location.search;\n    url = url.replace(/\\(/g, '\\\\(');\n    url = url.replace(/\\)/g, '\\\\)');\n  }\n\n  svgPath.attr(\n    'marker-end',\n    'url(' + url + '#' + getRelationType(StateDB.relationType.DEPENDENCY) + 'End' + ')'\n  );\n\n  if (relation.title !== undefined) {\n    const label = elem.append('g').attr('class', 'stateLabel');\n\n    const { x, y } = utils.calcLabelPosition(path.points);\n\n    const rows = common.getRows(relation.title);\n\n    let titleHeight = 0;\n    const titleRows = [];\n    let maxWidth = 0;\n    let minX = 0;\n\n    for (let i = 0; i <= rows.length; i++) {\n      const title = label\n        .append('text')\n        .attr('text-anchor', 'middle')\n        .text(rows[i])\n        .attr('x', x)\n        .attr('y', y + titleHeight);\n\n      const boundsTmp = title.node().getBBox();\n      maxWidth = Math.max(maxWidth, boundsTmp.width);\n      minX = Math.min(minX, boundsTmp.x);\n\n      log.info(boundsTmp.x, x, y + titleHeight);\n\n      if (titleHeight === 0) {\n        const titleBox = title.node().getBBox();\n        titleHeight = titleBox.height;\n        log.info('Title height', titleHeight, y);\n      }\n      titleRows.push(title);\n    }\n\n    let boxHeight = titleHeight * rows.length;\n    if (rows.length > 1) {\n      const heightAdj = (rows.length - 1) * titleHeight * 0.5;\n\n      titleRows.forEach((title, i) => title.attr('y', y + i * titleHeight - heightAdj));\n      boxHeight = titleHeight * rows.length;\n    }\n\n    const bounds = label.node().getBBox();\n\n    label\n      .insert('rect', ':first-child')\n      .attr('class', 'box')\n      .attr('x', x - maxWidth / 2 - getConfig().state.padding / 2)\n      .attr('y', y - boxHeight / 2 - getConfig().state.padding / 2 - 3.5)\n      .attr('width', maxWidth + getConfig().state.padding)\n      .attr('height', boxHeight + getConfig().state.padding);\n\n    log.info(bounds);\n\n    //label.attr('transform', '0 -' + (bounds.y / 2));\n\n    // Debug points\n    // path.points.forEach(point => {\n    //   g.append('circle')\n    //     .style('stroke', 'red')\n    //     .style('fill', 'red')\n    //     .attr('r', 1)\n    //     .attr('cx', point.x)\n    //     .attr('cy', point.y);\n    // });\n    // g.append('circle')\n    //   .style('stroke', 'blue')\n    //   .style('fill', 'blue')\n    //   .attr('r', 1)\n    //   .attr('cx', x)\n    //   .attr('cy', y);\n  }\n\n  edgeCount++;\n};\n", "import { select } from 'd3';\nimport { layout as dagreLayout } from 'dagre-d3-es/src/dagre/index.js';\nimport * as graphlib from 'dagre-d3-es/src/graphlib/index.js';\nimport { log } from '../../logger.js';\nimport common from '../common/common.js';\nimport { drawState, addTitleAndBox, drawEdge } from './shapes.js';\nimport { getConfig } from '../../diagram-api/diagramAPI.js';\nimport { configureSvgSize } from '../../setupGraphViewbox.js';\n\n// TODO Move conf object to main conf in mermaidAPI\nlet conf;\n\nconst transformationLog = {};\n\nexport const setConf = function () {\n  //no-op\n};\n\n/**\n * Setup arrow head and define the marker. The result is appended to the svg.\n *\n * @param {any} elem\n */\nconst insertMarkers = function (elem) {\n  elem\n    .append('defs')\n    .append('marker')\n    .attr('id', 'dependencyEnd')\n    .attr('refX', 19)\n    .attr('refY', 7)\n    .attr('markerWidth', 20)\n    .attr('markerHeight', 28)\n    .attr('orient', 'auto')\n    .append('path')\n    .attr('d', 'M 19,7 L9,13 L14,7 L9,1 Z');\n};\n\n/**\n * Draws a flowchart in the tag with id: id based on the graph definition in text.\n *\n * @param {any} text\n * @param {any} id\n * @param _version\n * @param diagObj\n */\nexport const draw = function (text, id, _version, diagObj) {\n  conf = getConfig().state;\n  const securityLevel = getConfig().securityLevel;\n  // Handle root and Document for when rendering in sandbox mode\n  let sandboxElement;\n  if (securityLevel === 'sandbox') {\n    sandboxElement = select('#i' + id);\n  }\n  const root =\n    securityLevel === 'sandbox'\n      ? select(sandboxElement.nodes()[0].contentDocument.body)\n      : select('body');\n  const doc = securityLevel === 'sandbox' ? sandboxElement.nodes()[0].contentDocument : document;\n\n  log.debug('Rendering diagram ' + text);\n\n  // Fetch the default direction, use TD if none was found\n  const diagram = root.select(`[id='${id}']`);\n  insertMarkers(diagram);\n\n  const rootDoc = diagObj.db.getRootDoc();\n  renderDoc(rootDoc, diagram, undefined, false, root, doc, diagObj);\n\n  const padding = conf.padding;\n  const bounds = diagram.node().getBBox();\n\n  const width = bounds.width + padding * 2;\n  const height = bounds.height + padding * 2;\n\n  // zoom in a bit\n  const svgWidth = width * 1.75;\n  configureSvgSize(diagram, height, svgWidth, conf.useMaxWidth);\n\n  diagram.attr(\n    'viewBox',\n    `${bounds.x - conf.padding}  ${bounds.y - conf.padding} ` + width + ' ' + height\n  );\n};\nconst getLabelWidth = (text) => {\n  return text ? text.length * conf.fontSizeFactor : 1;\n};\n\nconst renderDoc = (doc, diagram, parentId, altBkg, root, domDocument, diagObj) => {\n  // Layout graph, Create a new directed graph\n  const graph = new graphlib.Graph({\n    compound: true,\n    multigraph: true,\n  });\n\n  let i;\n  let edgeFreeDoc = true;\n  for (i = 0; i < doc.length; i++) {\n    if (doc[i].stmt === 'relation') {\n      edgeFreeDoc = false;\n      break;\n    }\n  }\n\n  // Set an object for the graph label\n  if (parentId) {\n    graph.setGraph({\n      rankdir: 'LR',\n      multigraph: true,\n      compound: true,\n      // acyclicer: 'greedy',\n      ranker: 'tight-tree',\n      ranksep: edgeFreeDoc ? 1 : conf.edgeLengthFactor,\n      nodeSep: edgeFreeDoc ? 1 : 50,\n      isMultiGraph: true,\n      // ranksep: 5,\n      // nodesep: 1\n    });\n  } else {\n    graph.setGraph({\n      rankdir: 'TB',\n      multigraph: true,\n      compound: true,\n      // isCompound: true,\n      // acyclicer: 'greedy',\n      // ranker: 'longest-path'\n      ranksep: edgeFreeDoc ? 1 : conf.edgeLengthFactor,\n      nodeSep: edgeFreeDoc ? 1 : 50,\n      ranker: 'tight-tree',\n      // ranker: 'network-simplex'\n      isMultiGraph: true,\n    });\n  }\n\n  // Default to assigning a new object as a label for each new edge.\n  graph.setDefaultEdgeLabel(function () {\n    return {};\n  });\n\n  const states = diagObj.db.getStates();\n  const relations = diagObj.db.getRelations();\n\n  const keys = Object.keys(states);\n\n  let first = true;\n\n  for (const key of keys) {\n    const stateDef = states[key];\n\n    if (parentId) {\n      stateDef.parentId = parentId;\n    }\n\n    let node;\n    if (stateDef.doc) {\n      let sub = diagram.append('g').attr('id', stateDef.id).attr('class', 'stateGroup');\n      node = renderDoc(stateDef.doc, sub, stateDef.id, !altBkg, root, domDocument, diagObj);\n\n      if (first) {\n        // first = false;\n        sub = addTitleAndBox(sub, stateDef, altBkg);\n        let boxBounds = sub.node().getBBox();\n        node.width = boxBounds.width;\n        node.height = boxBounds.height + conf.padding / 2;\n        transformationLog[stateDef.id] = { y: conf.compositTitleSize };\n      } else {\n        // sub = addIdAndBox(sub, stateDef);\n        let boxBounds = sub.node().getBBox();\n        node.width = boxBounds.width;\n        node.height = boxBounds.height;\n        // transformationLog[stateDef.id] = { y: conf.compositTitleSize };\n      }\n    } else {\n      node = drawState(diagram, stateDef, graph);\n    }\n\n    if (stateDef.note) {\n      // Draw note note\n      const noteDef = {\n        descriptions: [],\n        id: stateDef.id + '-note',\n        note: stateDef.note,\n        type: 'note',\n      };\n      const note = drawState(diagram, noteDef, graph);\n\n      // graph.setNode(node.id, node);\n      if (stateDef.note.position === 'left of') {\n        graph.setNode(node.id + '-note', note);\n        graph.setNode(node.id, node);\n      } else {\n        graph.setNode(node.id, node);\n        graph.setNode(node.id + '-note', note);\n      }\n      // graph.setNode(node.id);\n      graph.setParent(node.id, node.id + '-group');\n      graph.setParent(node.id + '-note', node.id + '-group');\n    } else {\n      // Add nodes to the graph. The first argument is the node id. The second is\n      // metadata about the node. In this case we're going to add labels to each of\n      // our nodes.\n      graph.setNode(node.id, node);\n    }\n  }\n\n  log.debug('Count=', graph.nodeCount(), graph);\n  let cnt = 0;\n  relations.forEach(function (relation) {\n    cnt++;\n    log.debug('Setting edge', relation);\n    graph.setEdge(\n      relation.id1,\n      relation.id2,\n      {\n        relation: relation,\n        width: getLabelWidth(relation.title),\n        height: conf.labelHeight * common.getRows(relation.title).length,\n        labelpos: 'c',\n      },\n      'id' + cnt\n    );\n  });\n\n  dagreLayout(graph);\n\n  log.debug('Graph after layout', graph.nodes());\n  const svgElem = diagram.node();\n\n  graph.nodes().forEach(function (v) {\n    if (v !== undefined && graph.node(v) !== undefined) {\n      log.warn('Node ' + v + ': ' + JSON.stringify(graph.node(v)));\n      root\n        .select('#' + svgElem.id + ' #' + v)\n        .attr(\n          'transform',\n          'translate(' +\n            (graph.node(v).x - graph.node(v).width / 2) +\n            ',' +\n            (graph.node(v).y +\n              (transformationLog[v] ? transformationLog[v].y : 0) -\n              graph.node(v).height / 2) +\n            ' )'\n        );\n      root\n        .select('#' + svgElem.id + ' #' + v)\n        .attr('data-x-shift', graph.node(v).x - graph.node(v).width / 2);\n      const dividers = domDocument.querySelectorAll('#' + svgElem.id + ' #' + v + ' .divider');\n      dividers.forEach((divider) => {\n        const parent = divider.parentElement;\n        let pWidth = 0;\n        let pShift = 0;\n        if (parent) {\n          if (parent.parentElement) {\n            pWidth = parent.parentElement.getBBox().width;\n          }\n          pShift = parseInt(parent.getAttribute('data-x-shift'), 10);\n          if (Number.isNaN(pShift)) {\n            pShift = 0;\n          }\n        }\n        divider.setAttribute('x1', 0 - pShift + 8);\n        divider.setAttribute('x2', pWidth - pShift - 8);\n      });\n    } else {\n      log.debug('No Node ' + v + ': ' + JSON.stringify(graph.node(v)));\n    }\n  });\n\n  let stateBox = svgElem.getBBox();\n\n  graph.edges().forEach(function (e) {\n    if (e !== undefined && graph.edge(e) !== undefined) {\n      log.debug('Edge ' + e.v + ' -> ' + e.w + ': ' + JSON.stringify(graph.edge(e)));\n      drawEdge(diagram, graph.edge(e), graph.edge(e).relation);\n    }\n  });\n\n  stateBox = svgElem.getBBox();\n\n  const stateInfo = {\n    id: parentId ? parentId : 'root',\n    label: parentId ? parentId : 'root',\n    width: 0,\n    height: 0,\n  };\n\n  stateInfo.width = stateBox.width + 2 * conf.padding;\n  stateInfo.height = stateBox.height + 2 * conf.padding;\n\n  log.debug('Doc rendered', stateInfo, graph);\n  return stateInfo;\n};\n\nexport default {\n  setConf,\n  draw,\n};\n", "import type { DiagramDefinition } from '../../diagram-api/types.js';\n// @ts-ignore: JISON doesn't support types\nimport parser from './parser/stateDiagram.jison';\nimport { StateDB } from './stateDb.js';\nimport styles from './styles.js';\nimport renderer from './stateRenderer.js';\n\nexport const diagram: DiagramDefinition = {\n  parser,\n  get db() {\n    return new StateDB(1);\n  },\n  renderer,\n  styles,\n  init: (cnf) => {\n    if (!cnf.state) {\n      cnf.state = {};\n    }\n    cnf.state.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;\n  },\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAM,UAAU,CAAC;AAEV,IAAM,MAAM,wBAAC,KAAK,QAAQ;AAC/B,UAAQ,GAAG,IAAI;AACjB,GAFmB;AAIZ,IAAM,MAAM,wBAAC,MAAM,QAAQ,CAAC,GAAhB;AACZ,IAAM,OAAO,6BAAM,OAAO,KAAK,OAAO,GAAzB;AACb,IAAM,OAAO,6BAAM,KAAK,EAAE,QAAb;AAEpB,IAAO,mBAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACFO,IAAM,iBAAiB,wBAAC,MAC7B,EACG,OAAO,QAAQ,EAGf,KAAK,SAAS,aAAa,EAC3B,KAAK,KAAK,UAAU,EAAE,MAAM,QAAQ,EACpC,KAAK,MAAM,UAAU,EAAE,MAAM,UAAU,UAAU,EAAE,MAAM,QAAQ,EACjE,KAAK,MAAM,UAAU,EAAE,MAAM,UAAU,UAAU,EAAE,MAAM,QAAQ,GARxC;AAevB,IAAM,cAAc,wBAAC,MAC1B,EACG,OAAO,MAAM,EACb,MAAM,UAAU,MAAM,EACtB,MAAM,oBAAoB,GAAG,EAC7B,KAAK,MAAM,UAAU,EAAE,MAAM,UAAU,EACvC,KAAK,SAAS,SAAS,EACvB,KAAK,MAAM,UAAU,EAAE,MAAM,aAAa,CAAC,EAC3C,KAAK,MAAM,CAAC,EACZ,KAAK,MAAM,CAAC,GATU;AAiBpB,IAAM,kBAAkB,wBAAC,GAAG,aAAa;AAC9C,QAAM,QAAQ,EACX,OAAO,MAAM,EACb,KAAK,KAAK,IAAI,UAAU,EAAE,MAAM,OAAO,EACvC,KAAK,KAAK,UAAU,EAAE,MAAM,aAAa,IAAI,UAAU,EAAE,MAAM,OAAO,EACtE,KAAK,aAAa,UAAU,EAAE,MAAM,QAAQ,EAC5C,KAAK,SAAS,aAAa,EAC3B,KAAK,SAAS,EAAE;AAEnB,QAAM,WAAW,MAAM,KAAK,EAAE,QAAQ;AACtC,IAAE,OAAO,QAAQ,cAAc,EAC5B,KAAK,KAAK,UAAU,EAAE,MAAM,OAAO,EACnC,KAAK,KAAK,UAAU,EAAE,MAAM,OAAO,EACnC,KAAK,SAAS,SAAS,QAAQ,IAAI,UAAU,EAAE,MAAM,OAAO,EAC5D,KAAK,UAAU,SAAS,SAAS,IAAI,UAAU,EAAE,MAAM,OAAO,EAC9D,KAAK,MAAM,UAAU,EAAE,MAAM,MAAM;AAEtC,SAAO;AACT,GAlB+B;AA2BxB,IAAM,iBAAiB,wBAAC,GAAG,aAAa;AAC7C,QAAM,WAAW,gCAAU,QAAQ,KAAKA,UAAS;AAC/C,UAAM,QAAQ,OACX,OAAO,OAAO,EACd,KAAK,KAAK,IAAI,UAAU,EAAE,MAAM,OAAO,EACvC,KAAK,GAAG;AACX,QAAI,CAACA,UAAS;AACZ,YAAM,KAAK,MAAM,UAAU,EAAE,MAAM,UAAU;AAAA,IAC/C;AAAA,EACF,GARiB;AASjB,QAAM,QAAQ,EACX,OAAO,MAAM,EACb,KAAK,KAAK,IAAI,UAAU,EAAE,MAAM,OAAO,EACvC,KAAK,KAAK,UAAU,EAAE,MAAM,aAAa,MAAM,UAAU,EAAE,MAAM,OAAO,EACxE,KAAK,aAAa,UAAU,EAAE,MAAM,QAAQ,EAC5C,KAAK,SAAS,aAAa,EAC3B,KAAK,SAAS,aAAa,CAAC,CAAC;AAEhC,QAAM,WAAW,MAAM,KAAK,EAAE,QAAQ;AACtC,QAAM,cAAc,SAAS;AAE7B,QAAM,cAAc,EACjB,OAAO,MAAM,EACb,KAAK,KAAK,UAAU,EAAE,MAAM,OAAO,EACnC;AAAA,IACC;AAAA,IACA,cACE,UAAU,EAAE,MAAM,UAAU,MAC5B,UAAU,EAAE,MAAM,gBAClB,UAAU,EAAE,MAAM;AAAA,EACtB,EACC,KAAK,SAAS,mBAAmB;AAEpC,MAAI,UAAU;AACd,MAAI,WAAW;AACf,WAAS,aAAa,QAAQ,SAAU,OAAO;AAC7C,QAAI,CAAC,SAAS;AACZ,eAAS,aAAa,OAAO,QAAQ;AACrC,iBAAW;AAAA,IACb;AACA,cAAU;AAAA,EACZ,CAAC;AAED,QAAM,YAAY,EACf,OAAO,MAAM,EACb,KAAK,MAAM,UAAU,EAAE,MAAM,OAAO,EACpC,KAAK,MAAM,UAAU,EAAE,MAAM,UAAU,cAAc,UAAU,EAAE,MAAM,gBAAgB,CAAC,EACxF,KAAK,MAAM,UAAU,EAAE,MAAM,UAAU,cAAc,UAAU,EAAE,MAAM,gBAAgB,CAAC,EACxF,KAAK,SAAS,eAAe;AAChC,QAAM,WAAW,YAAY,KAAK,EAAE,QAAQ;AAC5C,QAAM,QAAQ,KAAK,IAAI,SAAS,OAAO,SAAS,KAAK;AAErD,YAAU,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE,MAAM,OAAO;AAG1D,IAAE,OAAO,QAAQ,cAAc,EAC5B,KAAK,KAAK,UAAU,EAAE,MAAM,OAAO,EACnC,KAAK,KAAK,UAAU,EAAE,MAAM,OAAO,EACnC,KAAK,SAAS,QAAQ,IAAI,UAAU,EAAE,MAAM,OAAO,EACnD,KAAK,UAAU,SAAS,SAAS,cAAc,IAAI,UAAU,EAAE,MAAM,OAAO,EAC5E,KAAK,MAAM,UAAU,EAAE,MAAM,MAAM;AAEtC,SAAO;AACT,GA/D8B;AA0EvB,IAAM,iBAAiB,wBAAC,GAAG,UAAU,WAAW;AACrD,QAAM,MAAM,UAAU,EAAE,MAAM;AAC9B,QAAM,SAAS,IAAI,UAAU,EAAE,MAAM;AACrC,QAAM,SAAS,EAAE,KAAK,EAAE,QAAQ;AAChC,QAAM,WAAW,OAAO;AACxB,QAAM,OAAO,OAAO;AAEpB,QAAM,QAAQ,EACX,OAAO,MAAM,EACb,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,UAAU,EAAE,MAAM,UAAU,EACtC,KAAK,aAAa,UAAU,EAAE,MAAM,QAAQ,EAC5C,KAAK,SAAS,aAAa,EAC3B,KAAK,SAAS,EAAE;AAEnB,QAAM,WAAW,MAAM,KAAK,EAAE,QAAQ;AACtC,QAAM,aAAa,SAAS,QAAQ;AACpC,MAAI,QAAQ,KAAK,IAAI,YAAY,QAAQ;AACzC,MAAI,UAAU,UAAU;AACtB,YAAQ,QAAQ;AAAA,EAClB;AACA,MAAI;AASJ,QAAM,WAAW,EAAE,KAAK,EAAE,QAAQ;AAGlC,MAAI,SAAS,KAAK;AAAA,EAclB;AAEA,WAAS,OAAO;AAChB,MAAI,aAAa,UAAU;AACzB,cAAU,WAAW,SAAS,IAAI;AAAA,EACpC;AACA,MAAI,KAAK,IAAI,OAAO,SAAS,CAAC,IAAI,OAAO,aAAa,UAAU;AAC9D,aAAS,QAAQ,aAAa,YAAY;AAAA,EAC5C;AAEA,QAAM,QAAQ,IAAI,UAAU,EAAE,MAAM;AAEpC,IAAE,OAAO,QAAQ,cAAc,EAC5B,KAAK,KAAK,MAAM,EAChB,KAAK,KAAK,KAAK,EACf,KAAK,SAAS,SAAS,iBAAiB,UAAU,EAClD,KAAK,SAAS,KAAK,EACnB;AAAA,IACC;AAAA,IACA,SAAS,SAAS,UAAU,EAAE,MAAM,aAAa,UAAU,EAAE,MAAM,aAAa;AAAA,EAClF,EACC,KAAK,MAAM,GAAG;AAEjB,QAAM,KAAK,KAAK,SAAS,GAAG;AAC5B,MAAI,cAAc,UAAU;AAC1B,UAAM,KAAK,KAAK,QAAQ,QAAQ,UAAU,IAAI,aAAa,IAAI,GAAG;AAAA,EACpE;AAGA,IAAE,OAAO,QAAQ,cAAc,EAC5B,KAAK,KAAK,MAAM,EAChB;AAAA,IACC;AAAA,IACA,UAAU,EAAE,MAAM,aAAa,UAAU,EAAE,MAAM,aAAa,UAAU,EAAE,MAAM;AAAA,EAClF,EACC,KAAK,SAAS,KAAK,EAEnB,KAAK,UAAU,UAAU,EAAE,MAAM,aAAa,CAAC,EAC/C,KAAK,MAAM,UAAU,EAAE,MAAM,MAAM;AAGtC,IAAE,OAAO,QAAQ,cAAc,EAC5B,KAAK,KAAK,MAAM,EAChB;AAAA,IACC;AAAA,IACA,UAAU,EAAE,MAAM,aAAa,UAAU,EAAE,MAAM,aAAa,UAAU,EAAE,MAAM;AAAA,EAClF,EACC,KAAK,SAAS,KAAK,EACnB,KAAK,UAAU,SAAS,SAAS,IAAI,IAAI,UAAU,EAAE,MAAM,UAAU,EACrE,KAAK,MAAM,UAAU,EAAE,MAAM,MAAM;AAEtC,SAAO;AACT,GAnG8B;AAqG9B,IAAM,eAAe,wBAAC,MAAM;AAC1B,IAAE,OAAO,QAAQ,EAGd,KAAK,SAAS,iBAAiB,EAC/B,KAAK,KAAK,UAAU,EAAE,MAAM,WAAW,UAAU,EAAE,MAAM,WAAW,EACpE;AAAA,IACC;AAAA,IACA,UAAU,EAAE,MAAM,UAAU,UAAU,EAAE,MAAM,WAAW,UAAU,EAAE,MAAM;AAAA,EAC7E,EACC;AAAA,IACC;AAAA,IACA,UAAU,EAAE,MAAM,UAAU,UAAU,EAAE,MAAM,WAAW,UAAU,EAAE,MAAM;AAAA,EAC7E;AAEF,SACE,EACG,OAAO,QAAQ,EAGf,KAAK,SAAS,iBAAiB,EAC/B,KAAK,KAAK,UAAU,EAAE,MAAM,QAAQ,EACpC,KAAK,MAAM,UAAU,EAAE,MAAM,UAAU,UAAU,EAAE,MAAM,WAAW,CAAC,EACrE,KAAK,MAAM,UAAU,EAAE,MAAM,UAAU,UAAU,EAAE,MAAM,WAAW,CAAC;AAE5E,GAzBqB;AA0BrB,IAAM,oBAAoB,wBAAC,GAAG,aAAa;AACzC,MAAI,QAAQ,UAAU,EAAE,MAAM;AAC9B,MAAI,SAAS,UAAU,EAAE,MAAM;AAE/B,MAAI,SAAS,UAAU;AACrB,QAAI,MAAM;AACV,YAAQ;AACR,aAAS;AAAA,EACX;AACA,SAAO,EACJ,OAAO,MAAM,EACb,MAAM,UAAU,OAAO,EACvB,MAAM,QAAQ,OAAO,EACrB,KAAK,SAAS,KAAK,EACnB,KAAK,UAAU,MAAM,EACrB,KAAK,KAAK,UAAU,EAAE,MAAM,OAAO,EACnC,KAAK,KAAK,UAAU,EAAE,MAAM,OAAO;AACxC,GAjB0B;AAwC1B,IAAM,gBAAgB,wBAAC,OAAO,GAAG,GAAG,MAAM;AACxC,MAAI,aAAa;AAEjB,QAAM,WAAW,EAAE,OAAO,MAAM;AAChC,WAAS,MAAM,eAAe,OAAO;AACrC,WAAS,KAAK,SAAS,UAAU;AAEjC,MAAI,OAAO,MAAM,QAAQ,SAAS,OAAO;AACzC,SAAO,KAAK,QAAQ,OAAO,OAAO;AAClC,QAAM,QAAQ,KAAK,MAAM,eAAO,cAAc;AAE9C,MAAI,UAAU,OAAO,UAAU,EAAE,MAAM;AACvC,aAAW,QAAQ,OAAO;AACxB,UAAM,MAAM,KAAK,KAAK;AAEtB,QAAI,IAAI,SAAS,GAAG;AAClB,YAAM,OAAO,SAAS,OAAO,OAAO;AACpC,WAAK,KAAK,GAAG;AACb,UAAI,YAAY,GAAG;AACjB,cAAM,aAAa,KAAK,KAAK,EAAE,QAAQ;AACvC,mBAAW,WAAW;AAAA,MACxB;AACA,oBAAc;AACd,WAAK,KAAK,KAAK,IAAI,UAAU,EAAE,MAAM,UAAU;AAC/C,WAAK,KAAK,KAAK,IAAI,aAAa,OAAO,UAAU,EAAE,MAAM,UAAU;AAAA,IACrE;AAAA,EACF;AACA,SAAO,EAAE,WAAW,SAAS,KAAK,EAAE,QAAQ,EAAE,OAAO,WAAW;AAClE,GA5BsB;AAqCf,IAAM,WAAW,wBAAC,MAAM,MAAM;AACnC,IAAE,KAAK,SAAS,YAAY;AAC5B,QAAM,OAAO,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,UAAU,EAAE,MAAM,OAAO;AAC9E,QAAM,WAAW,EAAE,OAAO,GAAG;AAE7B,QAAM,EAAE,WAAW,WAAW,IAAI,cAAc,MAAM,GAAG,GAAG,QAAQ;AACpE,OAAK,KAAK,UAAU,aAAa,IAAI,UAAU,EAAE,MAAM,UAAU;AACjE,OAAK,KAAK,SAAS,YAAY,UAAU,EAAE,MAAM,aAAa,CAAC;AAE/D,SAAO;AACT,GAVwB;AAoBjB,IAAM,YAAY,gCAAU,MAAM,UAAU;AACjD,QAAM,KAAK,SAAS;AACpB,QAAM,YAAY;AAAA,IAChB;AAAA,IACA,OAAO,SAAS;AAAA,IAChB,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AAEA,QAAM,IAAI,KAAK,OAAO,GAAG,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,SAAS,YAAY;AAEpE,MAAI,SAAS,SAAS,SAAS;AAC7B,mBAAe,CAAC;AAAA,EAClB;AACA,MAAI,SAAS,SAAS,OAAO;AAC3B,iBAAa,CAAC;AAAA,EAChB;AACA,MAAI,SAAS,SAAS,UAAU,SAAS,SAAS,QAAQ;AACxD,sBAAkB,GAAG,QAAQ;AAAA,EAC/B;AACA,MAAI,SAAS,SAAS,QAAQ;AAC5B,aAAS,SAAS,KAAK,MAAM,CAAC;AAAA,EAChC;AACA,MAAI,SAAS,SAAS,WAAW;AAC/B,gBAAY,CAAC;AAAA,EACf;AACA,MAAI,SAAS,SAAS,aAAa,SAAS,aAAa,WAAW,GAAG;AACrE,oBAAgB,GAAG,QAAQ;AAAA,EAC7B;AACA,MAAI,SAAS,SAAS,aAAa,SAAS,aAAa,SAAS,GAAG;AACnE,mBAAe,GAAG,QAAQ;AAAA,EAC5B;AAEA,QAAM,WAAW,EAAE,KAAK,EAAE,QAAQ;AAClC,YAAU,QAAQ,SAAS,QAAQ,IAAI,UAAU,EAAE,MAAM;AACzD,YAAU,SAAS,SAAS,SAAS,IAAI,UAAU,EAAE,MAAM;AAE3D,mBAAQ,IAAI,IAAI,SAAS;AAEzB,SAAO;AACT,GAxCyB;AA0CzB,IAAI,YAAY;AACT,IAAM,WAAW,gCAAU,MAAM,MAAM,UAAU;AACtD,QAAM,kBAAkB,gCAAU,MAAM;AACtC,YAAQ,MAAM;AAAA,MACZ,KAAK,QAAQ,aAAa;AACxB,eAAO;AAAA,MACT,KAAK,QAAQ,aAAa;AACxB,eAAO;AAAA,MACT,KAAK,QAAQ,aAAa;AACxB,eAAO;AAAA,MACT,KAAK,QAAQ,aAAa;AACxB,eAAO;AAAA,IACX;AAAA,EACF,GAXwB;AAaxB,OAAK,SAAS,KAAK,OAAO,OAAO,CAAC,MAAM,CAAC,OAAO,MAAM,EAAE,CAAC,CAAC;AAG1D,QAAM,WAAW,KAAK;AAGtB,QAAM,eAAe,aAAK,EACvB,EAAE,SAAU,GAAG;AACd,WAAO,EAAE;AAAA,EACX,CAAC,EACA,EAAE,SAAU,GAAG;AACd,WAAO,EAAE;AAAA,EACX,CAAC,EACA,MAAM,aAAU;AAEnB,QAAM,UAAU,KACb,OAAO,MAAM,EACb,KAAK,KAAK,aAAa,QAAQ,CAAC,EAChC,KAAK,MAAM,SAAS,SAAS,EAC7B,KAAK,SAAS,YAAY;AAC7B,MAAI,MAAM;AACV,MAAI,UAAU,EAAE,MAAM,qBAAqB;AACzC,UACE,OAAO,SAAS,WAChB,OACA,OAAO,SAAS,OAChB,OAAO,SAAS,WAChB,OAAO,SAAS;AAClB,UAAM,IAAI,QAAQ,OAAO,KAAK;AAC9B,UAAM,IAAI,QAAQ,OAAO,KAAK;AAAA,EAChC;AAEA,UAAQ;AAAA,IACN;AAAA,IACA,SAAS,MAAM,MAAM,gBAAgB,QAAQ,aAAa,UAAU,IAAI;AAAA,EAC1E;AAEA,MAAI,SAAS,UAAU,QAAW;AAChC,UAAM,QAAQ,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,YAAY;AAEzD,UAAM,EAAE,GAAG,EAAE,IAAI,cAAM,kBAAkB,KAAK,MAAM;AAEpD,UAAM,OAAO,eAAO,QAAQ,SAAS,KAAK;AAE1C,QAAI,cAAc;AAClB,UAAM,YAAY,CAAC;AACnB,QAAI,WAAW;AACf,QAAI,OAAO;AAEX,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,KAAK;AACrC,YAAM,QAAQ,MACX,OAAO,MAAM,EACb,KAAK,eAAe,QAAQ,EAC5B,KAAK,KAAK,CAAC,CAAC,EACZ,KAAK,KAAK,CAAC,EACX,KAAK,KAAK,IAAI,WAAW;AAE5B,YAAM,YAAY,MAAM,KAAK,EAAE,QAAQ;AACvC,iBAAW,KAAK,IAAI,UAAU,UAAU,KAAK;AAC7C,aAAO,KAAK,IAAI,MAAM,UAAU,CAAC;AAEjC,UAAI,KAAK,UAAU,GAAG,GAAG,IAAI,WAAW;AAExC,UAAI,gBAAgB,GAAG;AACrB,cAAM,WAAW,MAAM,KAAK,EAAE,QAAQ;AACtC,sBAAc,SAAS;AACvB,YAAI,KAAK,gBAAgB,aAAa,CAAC;AAAA,MACzC;AACA,gBAAU,KAAK,KAAK;AAAA,IACtB;AAEA,QAAI,YAAY,cAAc,KAAK;AACnC,QAAI,KAAK,SAAS,GAAG;AACnB,YAAM,aAAa,KAAK,SAAS,KAAK,cAAc;AAEpD,gBAAU,QAAQ,CAAC,OAAO,MAAM,MAAM,KAAK,KAAK,IAAI,IAAI,cAAc,SAAS,CAAC;AAChF,kBAAY,cAAc,KAAK;AAAA,IACjC;AAEA,UAAM,SAAS,MAAM,KAAK,EAAE,QAAQ;AAEpC,UACG,OAAO,QAAQ,cAAc,EAC7B,KAAK,SAAS,KAAK,EACnB,KAAK,KAAK,IAAI,WAAW,IAAI,UAAU,EAAE,MAAM,UAAU,CAAC,EAC1D,KAAK,KAAK,IAAI,YAAY,IAAI,UAAU,EAAE,MAAM,UAAU,IAAI,GAAG,EACjE,KAAK,SAAS,WAAW,UAAU,EAAE,MAAM,OAAO,EAClD,KAAK,UAAU,YAAY,UAAU,EAAE,MAAM,OAAO;AAEvD,QAAI,KAAK,MAAM;AAAA,EAmBjB;AAEA;AACF,GA7HwB;;;ACnZxB,IAAI;AAEJ,IAAM,oBAAoB,CAAC;AAEpB,IAAM,UAAU,kCAAY;AAEnC,GAFuB;AASvB,IAAM,gBAAgB,gCAAU,MAAM;AACpC,OACG,OAAO,MAAM,EACb,OAAO,QAAQ,EACf,KAAK,MAAM,eAAe,EAC1B,KAAK,QAAQ,EAAE,EACf,KAAK,QAAQ,CAAC,EACd,KAAK,eAAe,EAAE,EACtB,KAAK,gBAAgB,EAAE,EACvB,KAAK,UAAU,MAAM,EACrB,OAAO,MAAM,EACb,KAAK,KAAK,2BAA2B;AAC1C,GAZsB;AAsBf,IAAM,OAAO,gCAAU,MAAM,IAAI,UAAU,SAAS;AACzD,SAAO,UAAU,EAAE;AACnB,QAAM,gBAAgB,UAAU,EAAE;AAElC,MAAI;AACJ,MAAI,kBAAkB,WAAW;AAC/B,qBAAiB,eAAO,OAAO,EAAE;AAAA,EACnC;AACA,QAAM,OACJ,kBAAkB,YACd,eAAO,eAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,IACrD,eAAO,MAAM;AACnB,QAAM,MAAM,kBAAkB,YAAY,eAAe,MAAM,EAAE,CAAC,EAAE,kBAAkB;AAEtF,MAAI,MAAM,uBAAuB,IAAI;AAGrC,QAAMC,WAAU,KAAK,OAAO,QAAQ,EAAE,IAAI;AAC1C,gBAAcA,QAAO;AAErB,QAAM,UAAU,QAAQ,GAAG,WAAW;AACtC,YAAU,SAASA,UAAS,QAAW,OAAO,MAAM,KAAK,OAAO;AAEhE,QAAM,UAAU,KAAK;AACrB,QAAM,SAASA,SAAQ,KAAK,EAAE,QAAQ;AAEtC,QAAM,QAAQ,OAAO,QAAQ,UAAU;AACvC,QAAM,SAAS,OAAO,SAAS,UAAU;AAGzC,QAAM,WAAW,QAAQ;AACzB,mBAAiBA,UAAS,QAAQ,UAAU,KAAK,WAAW;AAE5D,EAAAA,SAAQ;AAAA,IACN;AAAA,IACA,GAAG,OAAO,IAAI,KAAK,OAAO,KAAK,OAAO,IAAI,KAAK,OAAO,MAAM,QAAQ,MAAM;AAAA,EAC5E;AACF,GArCoB;AAsCpB,IAAM,gBAAgB,wBAAC,SAAS;AAC9B,SAAO,OAAO,KAAK,SAAS,KAAK,iBAAiB;AACpD,GAFsB;AAItB,IAAM,YAAY,wBAAC,KAAKA,UAAS,UAAU,QAAQ,MAAM,aAAa,YAAY;AAEhF,QAAM,QAAQ,IAAa,MAAM;AAAA,IAC/B,UAAU;AAAA,IACV,YAAY;AAAA,EACd,CAAC;AAED,MAAI;AACJ,MAAI,cAAc;AAClB,OAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAC/B,QAAI,IAAI,CAAC,EAAE,SAAS,YAAY;AAC9B,oBAAc;AACd;AAAA,IACF;AAAA,EACF;AAGA,MAAI,UAAU;AACZ,UAAM,SAAS;AAAA,MACb,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA,MAEV,QAAQ;AAAA,MACR,SAAS,cAAc,IAAI,KAAK;AAAA,MAChC,SAAS,cAAc,IAAI;AAAA,MAC3B,cAAc;AAAA;AAAA;AAAA,IAGhB,CAAC;AAAA,EACH,OAAO;AACL,UAAM,SAAS;AAAA,MACb,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA,MAIV,SAAS,cAAc,IAAI,KAAK;AAAA,MAChC,SAAS,cAAc,IAAI;AAAA,MAC3B,QAAQ;AAAA;AAAA,MAER,cAAc;AAAA,IAChB,CAAC;AAAA,EACH;AAGA,QAAM,oBAAoB,WAAY;AACpC,WAAO,CAAC;AAAA,EACV,CAAC;AAED,QAAM,SAAS,QAAQ,GAAG,UAAU;AACpC,QAAM,YAAY,QAAQ,GAAG,aAAa;AAE1C,QAAMC,QAAO,OAAO,KAAK,MAAM;AAE/B,MAAI,QAAQ;AAEZ,aAAW,OAAOA,OAAM;AACtB,UAAM,WAAW,OAAO,GAAG;AAE3B,QAAI,UAAU;AACZ,eAAS,WAAW;AAAA,IACtB;AAEA,QAAI;AACJ,QAAI,SAAS,KAAK;AAChB,UAAI,MAAMD,SAAQ,OAAO,GAAG,EAAE,KAAK,MAAM,SAAS,EAAE,EAAE,KAAK,SAAS,YAAY;AAChF,aAAO,UAAU,SAAS,KAAK,KAAK,SAAS,IAAI,CAAC,QAAQ,MAAM,aAAa,OAAO;AAEpF,UAAI,OAAO;AAET,cAAM,eAAe,KAAK,UAAU,MAAM;AAC1C,YAAI,YAAY,IAAI,KAAK,EAAE,QAAQ;AACnC,aAAK,QAAQ,UAAU;AACvB,aAAK,SAAS,UAAU,SAAS,KAAK,UAAU;AAChD,0BAAkB,SAAS,EAAE,IAAI,EAAE,GAAG,KAAK,kBAAkB;AAAA,MAC/D,OAAO;AAEL,YAAI,YAAY,IAAI,KAAK,EAAE,QAAQ;AACnC,aAAK,QAAQ,UAAU;AACvB,aAAK,SAAS,UAAU;AAAA,MAE1B;AAAA,IACF,OAAO;AACL,aAAO,UAAUA,UAAS,UAAU,KAAK;AAAA,IAC3C;AAEA,QAAI,SAAS,MAAM;AAEjB,YAAM,UAAU;AAAA,QACd,cAAc,CAAC;AAAA,QACf,IAAI,SAAS,KAAK;AAAA,QAClB,MAAM,SAAS;AAAA,QACf,MAAM;AAAA,MACR;AACA,YAAM,OAAO,UAAUA,UAAS,SAAS,KAAK;AAG9C,UAAI,SAAS,KAAK,aAAa,WAAW;AACxC,cAAM,QAAQ,KAAK,KAAK,SAAS,IAAI;AACrC,cAAM,QAAQ,KAAK,IAAI,IAAI;AAAA,MAC7B,OAAO;AACL,cAAM,QAAQ,KAAK,IAAI,IAAI;AAC3B,cAAM,QAAQ,KAAK,KAAK,SAAS,IAAI;AAAA,MACvC;AAEA,YAAM,UAAU,KAAK,IAAI,KAAK,KAAK,QAAQ;AAC3C,YAAM,UAAU,KAAK,KAAK,SAAS,KAAK,KAAK,QAAQ;AAAA,IACvD,OAAO;AAIL,YAAM,QAAQ,KAAK,IAAI,IAAI;AAAA,IAC7B;AAAA,EACF;AAEA,MAAI,MAAM,UAAU,MAAM,UAAU,GAAG,KAAK;AAC5C,MAAI,MAAM;AACV,YAAU,QAAQ,SAAU,UAAU;AACpC;AACA,QAAI,MAAM,gBAAgB,QAAQ;AAClC,UAAM;AAAA,MACJ,SAAS;AAAA,MACT,SAAS;AAAA,MACT;AAAA,QACE;AAAA,QACA,OAAO,cAAc,SAAS,KAAK;AAAA,QACnC,QAAQ,KAAK,cAAc,eAAO,QAAQ,SAAS,KAAK,EAAE;AAAA,QAC1D,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,IACT;AAAA,EACF,CAAC;AAED,SAAY,KAAK;AAEjB,MAAI,MAAM,sBAAsB,MAAM,MAAM,CAAC;AAC7C,QAAM,UAAUA,SAAQ,KAAK;AAE7B,QAAM,MAAM,EAAE,QAAQ,SAAU,GAAG;AACjC,QAAI,MAAM,UAAa,MAAM,KAAK,CAAC,MAAM,QAAW;AAClD,UAAI,KAAK,UAAU,IAAI,OAAO,KAAK,UAAU,MAAM,KAAK,CAAC,CAAC,CAAC;AAC3D,WACG,OAAO,MAAM,QAAQ,KAAK,OAAO,CAAC,EAClC;AAAA,QACC;AAAA,QACA,gBACG,MAAM,KAAK,CAAC,EAAE,IAAI,MAAM,KAAK,CAAC,EAAE,QAAQ,KACzC,OACC,MAAM,KAAK,CAAC,EAAE,KACZ,kBAAkB,CAAC,IAAI,kBAAkB,CAAC,EAAE,IAAI,KACjD,MAAM,KAAK,CAAC,EAAE,SAAS,KACzB;AAAA,MACJ;AACF,WACG,OAAO,MAAM,QAAQ,KAAK,OAAO,CAAC,EAClC,KAAK,gBAAgB,MAAM,KAAK,CAAC,EAAE,IAAI,MAAM,KAAK,CAAC,EAAE,QAAQ,CAAC;AACjE,YAAM,WAAW,YAAY,iBAAiB,MAAM,QAAQ,KAAK,OAAO,IAAI,WAAW;AACvF,eAAS,QAAQ,CAAC,YAAY;AAC5B,cAAM,SAAS,QAAQ;AACvB,YAAI,SAAS;AACb,YAAI,SAAS;AACb,YAAI,QAAQ;AACV,cAAI,OAAO,eAAe;AACxB,qBAAS,OAAO,cAAc,QAAQ,EAAE;AAAA,UAC1C;AACA,mBAAS,SAAS,OAAO,aAAa,cAAc,GAAG,EAAE;AACzD,cAAI,OAAO,MAAM,MAAM,GAAG;AACxB,qBAAS;AAAA,UACX;AAAA,QACF;AACA,gBAAQ,aAAa,MAAM,IAAI,SAAS,CAAC;AACzC,gBAAQ,aAAa,MAAM,SAAS,SAAS,CAAC;AAAA,MAChD,CAAC;AAAA,IACH,OAAO;AACL,UAAI,MAAM,aAAa,IAAI,OAAO,KAAK,UAAU,MAAM,KAAK,CAAC,CAAC,CAAC;AAAA,IACjE;AAAA,EACF,CAAC;AAED,MAAI,WAAW,QAAQ,QAAQ;AAE/B,QAAM,MAAM,EAAE,QAAQ,SAAU,GAAG;AACjC,QAAI,MAAM,UAAa,MAAM,KAAK,CAAC,MAAM,QAAW;AAClD,UAAI,MAAM,UAAU,EAAE,IAAI,SAAS,EAAE,IAAI,OAAO,KAAK,UAAU,MAAM,KAAK,CAAC,CAAC,CAAC;AAC7E,eAASA,UAAS,MAAM,KAAK,CAAC,GAAG,MAAM,KAAK,CAAC,EAAE,QAAQ;AAAA,IACzD;AAAA,EACF,CAAC;AAED,aAAW,QAAQ,QAAQ;AAE3B,QAAM,YAAY;AAAA,IAChB,IAAI,WAAW,WAAW;AAAA,IAC1B,OAAO,WAAW,WAAW;AAAA,IAC7B,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AAEA,YAAU,QAAQ,SAAS,QAAQ,IAAI,KAAK;AAC5C,YAAU,SAAS,SAAS,SAAS,IAAI,KAAK;AAE9C,MAAI,MAAM,gBAAgB,WAAW,KAAK;AAC1C,SAAO;AACT,GA3MkB;AA6MlB,IAAO,wBAAQ;AAAA,EACb;AAAA,EACA;AACF;;;AChSO,IAAM,UAA6B;AAAA,EACxC;AAAA,EACA,IAAI,KAAK;AACP,WAAO,IAAI,QAAQ,CAAC;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM,wBAAC,QAAQ;AACb,QAAI,CAAC,IAAI,OAAO;AACd,UAAI,QAAQ,CAAC;AAAA,IACf;AACA,QAAI,MAAM,sBAAsB,IAAI;AAAA,EACtC,GALM;AAMR;", "names": ["<PERSON><PERSON><PERSON><PERSON>", "diagram", "keys"]}