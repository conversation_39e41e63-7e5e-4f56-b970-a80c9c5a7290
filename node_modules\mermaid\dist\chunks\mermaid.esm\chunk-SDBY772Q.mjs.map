{"version": 3, "sources": ["../../../../parser/dist/chunks/mermaid-parser.core/chunk-ROXG7S4E.mjs"], "sourcesContent": ["import {\n  AbstractMermaidTokenBuilder,\n  AbstractMermaidValueConverter,\n  MermaidGeneratedSharedModule,\n  PieGeneratedModule,\n  __name\n} from \"./chunk-7PKI6E2E.mjs\";\n\n// src/language/pie/module.ts\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject\n} from \"langium\";\n\n// src/language/pie/tokenBuilder.ts\nvar PieTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"PieTokenBuilder\");\n  }\n  constructor() {\n    super([\"pie\", \"showData\"]);\n  }\n};\n\n// src/language/pie/valueConverter.ts\nvar PieValueConverter = class extends AbstractMermaidValueConverter {\n  static {\n    __name(this, \"PieValueConverter\");\n  }\n  runCustomConverter(rule, input, _cstNode) {\n    if (rule.name !== \"PIE_SECTION_LABEL\") {\n      return void 0;\n    }\n    return input.replace(/\"/g, \"\").trim();\n  }\n};\n\n// src/language/pie/module.ts\nvar PieModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */ __name(() => new PieTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */ __name(() => new PieValueConverter(), \"ValueConverter\")\n  }\n};\nfunction createPieServices(context = EmptyFileSystem) {\n  const shared = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Pie = inject(\n    createDefaultCoreModule({ shared }),\n    PieGeneratedModule,\n    PieModule\n  );\n  shared.ServiceRegistry.register(Pie);\n  return { shared, Pie };\n}\n__name(createPieServices, \"createPieServices\");\n\nexport {\n  PieModule,\n  createPieServices\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;AAiBA,IAAI,kBAAkB,cAAc,4BAA4B;AAAA,EAjBhE,OAiBgE;AAAA;AAAA;AAAA,EAC9D,OAAO;AACL,IAAAA,QAAO,MAAM,iBAAiB;AAAA,EAChC;AAAA,EACA,cAAc;AACZ,UAAM,CAAC,OAAO,UAAU,CAAC;AAAA,EAC3B;AACF;AAGA,IAAI,oBAAoB,cAAc,8BAA8B;AAAA,EA3BpE,OA2BoE;AAAA;AAAA;AAAA,EAClE,OAAO;AACL,IAAAA,QAAO,MAAM,mBAAmB;AAAA,EAClC;AAAA,EACA,mBAAmB,MAAM,OAAO,UAAU;AACxC,QAAI,KAAK,SAAS,qBAAqB;AACrC,aAAO;AAAA,IACT;AACA,WAAO,MAAM,QAAQ,MAAM,EAAE,EAAE,KAAK;AAAA,EACtC;AACF;AAGA,IAAI,YAAY;AAAA,EACd,QAAQ;AAAA,IACN,cAA8B,gBAAAA,QAAO,MAAM,IAAI,gBAAgB,GAAG,cAAc;AAAA,IAChF,gBAAgC,gBAAAA,QAAO,MAAM,IAAI,kBAAkB,GAAG,gBAAgB;AAAA,EACxF;AACF;AACA,SAAS,kBAAkB,UAAU,iBAAiB;AACpD,QAAM,SAAS;AAAA,IACb,8BAA8B,OAAO;AAAA,IACrC;AAAA,EACF;AACA,QAAM,MAAM;AAAA,IACV,wBAAwB,EAAE,OAAO,CAAC;AAAA,IAClC;AAAA,IACA;AAAA,EACF;AACA,SAAO,gBAAgB,SAAS,GAAG;AACnC,SAAO,EAAE,QAAQ,IAAI;AACvB;AAZS;AAaTA,QAAO,mBAAmB,mBAAmB;", "names": ["__name"]}