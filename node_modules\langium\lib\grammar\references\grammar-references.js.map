{"version": 3, "file": "grammar-references.js", "sourceRoot": "", "sources": ["../../../src/grammar/references/grammar-references.ts"], "names": [], "mappings": "AAAA;;;;+EAI+E;AAS/E,OAAO,EAAE,iBAAiB,EAAE,MAAM,gCAAgC,CAAC;AACnE,OAAO,EAAE,kBAAkB,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AAC3E,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAC7D,OAAO,EAAE,cAAc,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,MAAM,8BAA8B,CAAC;AACvG,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACpD,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,kCAAkC,CAAC;AAC9H,OAAO,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AACjE,OAAO,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,MAAM,8BAA8B,CAAC;AAEvF,MAAM,OAAO,wBAAyB,SAAQ,iBAAiB;IAG3D,YAAY,QAA6B;QACrC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAChB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;IAChE,CAAC;IAEQ,eAAe,CAAC,aAAsB;QAC3C,MAAM,QAAQ,GAAG,aAAa,CAAC,OAAO,CAAC;QACvC,MAAM,UAAU,GAAG,cAAc,CAAC,aAAa,CAAC,CAAC;QACjD,IAAI,UAAU,IAAI,UAAU,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YACjD,yGAAyG;YACzG,IAAI,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACzB,OAAO,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC;YACpD,CAAC;iBAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5B,OAAO,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YAChD,CAAC;QACL,CAAC;QACD,OAAO,KAAK,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;IAChD,CAAC;IAEQ,cAAc,CAAC,UAAmB,EAAE,OAA8B;;QACvE,IAAI,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAC,6BAA6B,CAAC,UAAU,EAAE,MAAA,OAAO,CAAC,kBAAkB,mCAAI,KAAK,CAAC,CAAC;QAC/F,CAAC;aAAM,CAAC;YACJ,OAAO,KAAK,CAAC,cAAc,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACrD,CAAC;IACL,CAAC;IAES,6BAA6B,CAAC,UAAyB,EAAE,kBAA2B;QAC1F,MAAM,IAAI,GAA2B,EAAE,CAAC;QACxC,MAAM,aAAa,GAAG,kBAAkB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QAClE,IAAI,aAAa,EAAE,CAAC;YAChB,IAAI,kBAAkB,EAAE,CAAC;gBACrB,MAAM,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;gBAChD,IAAI,GAAG,EAAE,CAAC;oBACN,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACnB,CAAC;YACL,CAAC;YACD,MAAM,UAAU,GAAG,oBAAoB,CAAC,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YAC/F,MAAM,WAAW,GAA+B,EAAE,CAAC;YACnD,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACxB,MAAM,KAAK,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;gBACnD,WAAW,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC;YACH,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACvB,MAAM,UAAU,GAAG,IAAI,CAAC,2BAA2B,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;gBACtE,IAAI,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC;QACP,CAAC;QACD,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;IACxB,CAAC;IAES,2BAA2B,CAAC,YAAiC,EAAE,SAAwB;QAC7F,MAAM,IAAI,GAA2B,EAAE,CAAC;QACxC,IAAI,YAAY,CAAC,YAAY,CAAC,EAAE,CAAC;YAC7B,MAAM,UAAU,GAAG,kBAAkB,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,KAAK,SAAS,CAAC,IAAI,CAAC,CAAC;YACvG,IAAI,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,QAAQ,EAAE,CAAC;gBACvB,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;gBACvD,IAAI,IAAI,EAAE,CAAC;oBACP,IAAI,CAAC,IAAI,CAAC;wBACN,SAAS,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC,GAAG;wBACtC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,UAAU,CAAC;wBACvD,SAAS,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,GAAG;wBACrC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC;wBACtD,OAAO,EAAE,iBAAiB,CAAC,IAAI,CAAC;wBAChC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,GAAG,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC;qBAClF,CAAC,CAAC;gBACP,CAAC;YACL,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,kDAAkD;YAClD,IAAI,YAAY,CAAC,OAAO,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;gBAC1C,MAAM,IAAI,GAAG,mBAAmB,CAAC,YAAY,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;gBACnE,IAAI,IAAI,EAAE,CAAC;oBACP,IAAI,CAAC,IAAI,CAAC;wBACN,SAAS,EAAE,WAAW,CAAC,YAAY,CAAC,CAAC,GAAG;wBACxC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,YAAY,CAAC;wBACzD,SAAS,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,GAAG;wBACrC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC;wBACtD,OAAO,EAAE,iBAAiB,CAAC,IAAI,CAAC;wBAChC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,GAAG,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC;qBACpF,CAAC,CAAC;gBACP,CAAC;YACL,CAAC;YACD,uEAAuE;YACvE,MAAM,UAAU,GAAG,kBAAkB,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;YAClE,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC,UAAW,EAAE,SAAS,CAAC,CAAC,CAAC;QAC3E,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,yBAAyB,CAAC,UAAsB;;QACtD,MAAM,UAAU,GAAG,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QAChE,MAAM,MAAM,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;QAC9C,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;YACjF,IAAI,iBAAiB,EAAE,CAAC;gBACpB,OAAO,iBAAiB,CAAC;YAC7B,CAAC;QACL,CAAC;QACD,IAAI,MAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,UAAU,0CAAE,GAAG,EAAE,CAAC;YAC9B,IAAI,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9E,MAAM,UAAU,GAAG,iBAAiB,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;gBAChE,KAAK,MAAM,MAAM,IAAI,UAAU,EAAE,CAAC;oBAC9B,MAAM,aAAa,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,UAAU,CAAC,OAAO,CAAC,CAAC;oBACrF,IAAI,aAAa,EAAE,CAAC;wBAChB,OAAO,aAAa,CAAC;oBACzB,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QACD,OAAO,UAAU,CAAC;IACtB,CAAC;IAES,qBAAqB,CAAC,MAAc,EAAE,WAAoB;;QAChE,IAAI,MAAA,MAAM,CAAC,IAAI,0CAAE,GAAG,EAAE,CAAC;YACnB,MAAM,OAAO,GAAG,WAAW,aAAX,WAAW,cAAX,WAAW,GAAI,MAAM,CAAC,OAAO,CAAC;YAC9C,MAAM,UAAU,GAAG,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACtD,KAAK,MAAM,MAAM,IAAI,UAAU,EAAE,CAAC;gBAC9B,MAAM,aAAa,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;gBAC1E,IAAI,aAAa,EAAE,CAAC;oBAChB,OAAO,aAAa,CAAC;gBACzB,CAAC;YACL,CAAC;QACL,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAES,uBAAuB,CAAC,MAAwB;QACtD,MAAM,KAAK,GAA+B,EAAE,CAAC;QAC7C,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;QAC3F,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACrB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACtD,IAAI,GAAG,EAAE,CAAC;gBACN,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;gBACnF,IAAI,YAAY,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC7C,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACxB,CAAC;YACL,CAAC;QACL,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;CACJ"}