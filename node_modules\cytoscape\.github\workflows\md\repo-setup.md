## Instructions to setup repository for automated releases

### Tokens

- `NPM_TOKEN`: Token for npmjs package publishing. The token should a automation token with no expiration. Ref: [How to create legacy token in npm](https://docs.npmjs.com/creating-and-viewing-access-tokens#creating-legacy-tokens-on-the-website)

- `MAIN_GH_TOKEN`: Token for accessing GitHub API to publish GitHub Releases on Cytoscape/Cytoscape.js repo. The token can be set to expire at 1 year (maximum limit of github PAT is 1 year). Ref: [Create fine-grained-personal-access-tokens](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens#fine-grained-personal-access-tokens)
    - Permissions Required:
        - Actions: Read and Write
        - Contents: Read and Write

- `CYTOSCAPE_JS_BLOG_TOKEN`: Token for creating issues on the repository's blog Cytoscape/Cytoscape.js-blog repo.
    - Permissions Required:
        - Issues: Read and Write

### Repository Setup

- Provide Github Actions permissions to read and write. Ref: [Managing Github Actions](https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/enabling-features-for-your-repository/managing-github-actions-settings-for-a-repository)
