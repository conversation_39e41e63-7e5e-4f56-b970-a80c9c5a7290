export function clear(): void;
export function getLabelStyles(styleArray: any): any;
export function insertEdgeLabel(elem: any, edge: any): Promise<any>;
export function positionEdgeLabel(edge: any, paths: any): void;
export function intersection(node: any, outsidePoint: any, insidePoint: any): {
    x: any;
    y: number;
} | {
    x: number;
    y: any;
};
export function insertEdge(elem: any, edge: any, clusterDb: any, diagramType: any, startNode: any, endNode: any, id: any): {
    updatedPath: any;
    originalPath: any;
};
