{"cells": [{"cell_type": "code", "execution_count": 1, "id": "03cfb817-88eb-49f4-88ec-bd74945afaf5", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: ultralytics in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (8.3.80)\n", "Requirement already satisfied: opencv-python in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (4.11.0.86)\n", "Requirement already satisfied: matplotlib in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (3.10.0)\n", "Requirement already satisfied: numpy<=2.1.1,>=1.23.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from ultralytics) (2.1.1)\n", "Requirement already satisfied: pillow>=7.1.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from ultralytics) (11.1.0)\n", "Requirement already satisfied: pyyaml>=5.3.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from ultralytics) (6.0.2)\n", "Requirement already satisfied: requests>=2.23.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from ultralytics) (2.32.3)\n", "Requirement already satisfied: scipy>=1.4.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from ultralytics) (1.15.2)\n", "Requirement already satisfied: torch>=1.8.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from ultralytics) (2.6.0)\n", "Requirement already satisfied: torchvision>=0.9.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from ultralytics) (0.21.0)\n", "Requirement already satisfied: tqdm>=4.64.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from ultralytics) (4.67.1)\n", "Requirement already satisfied: psutil in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from ultralytics) (7.0.0)\n", "Requirement already satisfied: py-cpuinfo in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from ultralytics) (9.0.0)\n", "Requirement already satisfied: pandas>=1.1.4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from ultralytics) (2.2.3)\n", "Requirement already satisfied: seaborn>=0.11.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from ultralytics) (0.13.2)\n", "Requirement already satisfied: ultralytics-thop>=2.0.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from ultralytics) (2.0.14)\n", "Requirement already satisfied: contourpy>=1.0.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from matplotlib) (1.3.1)\n", "Requirement already satisfied: cycler>=0.10 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from matplotlib) (4.56.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from matplotlib) (1.4.8)\n", "Requirement already satisfied: packaging>=20.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from matplotlib) (24.2)\n", "Requirement already satisfied: pyparsing>=2.3.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from matplotlib) (3.2.1)\n", "Requirement already satisfied: python-dateutil>=2.7 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from matplotlib) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from pandas>=1.1.4->ultralytics) (2025.1)\n", "Requirement already satisfied: tzdata>=2022.7 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from pandas>=1.1.4->ultralytics) (2025.1)\n", "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from python-dateutil>=2.7->matplotlib) (1.17.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from requests>=2.23.0->ultralytics) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from requests>=2.23.0->ultralytics) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from requests>=2.23.0->ultralytics) (2.3.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from requests>=2.23.0->ultralytics) (2025.1.31)\n", "Requirement already satisfied: filelock in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from torch>=1.8.0->ultralytics) (3.17.0)\n", "Requirement already satisfied: typing-extensions>=4.10.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from torch>=1.8.0->ultralytics) (4.12.2)\n", "Requirement already satisfied: networkx in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from torch>=1.8.0->ultralytics) (3.4.2)\n", "Requirement already satisfied: jinja2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from torch>=1.8.0->ultralytics) (3.1.5)\n", "Requirement already satisfied: fsspec in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from torch>=1.8.0->ultralytics) (2025.2.0)\n", "Requirement already satisfied: setuptools in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from torch>=1.8.0->ultralytics) (75.8.0)\n", "Requirement already satisfied: sympy==1.13.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from torch>=1.8.0->ultralytics) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from sympy==1.13.1->torch>=1.8.0->ultralytics) (1.3.0)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from tqdm>=4.64.0->ultralytics) (0.4.6)\n", "Requirement already satisfied: MarkupSafe>=2.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from jinja2->torch>=1.8.0->ultralytics) (3.0.2)\n"]}], "source": ["!pip install ultralytics opencv-python matplotlib\n"]}, {"cell_type": "code", "execution_count": 2, "id": "e5a07ea4-97ff-4e70-8a5d-b5a713894ea0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: kaggle in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (1.6.17)\n", "Requirement already satisfied: six>=1.10 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from kaggle) (1.17.0)\n", "Requirement already satisfied: certifi>=2023.7.22 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from kaggle) (2025.1.31)\n", "Requirement already satisfied: python-dateutil in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from kaggle) (2.9.0.post0)\n", "Requirement already satisfied: requests in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from kaggle) (2.32.3)\n", "Requirement already satisfied: tqdm in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from kaggle) (4.67.1)\n", "Requirement already satisfied: python-slugify in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from kaggle) (8.0.4)\n", "Requirement already satisfied: urllib3 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from kaggle) (2.3.0)\n", "Requirement already satisfied: bleach in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from kaggle) (6.2.0)\n", "Requirement already satisfied: webencodings in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from bleach->kaggle) (0.5.1)\n", "Requirement already satisfied: text-unidecode>=1.3 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from python-slugify->kaggle) (1.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from requests->kaggle) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from requests->kaggle) (3.10)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from tqdm->kaggle) (0.4.6)\n"]}], "source": ["!pip install kaggle"]}, {"cell_type": "code", "execution_count": 3, "id": "2adb509f-73e4-4c54-ad44-ca6005adb402", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["'chmod' n'est pas reconnu en tant que commande interne\n", "ou externe, un programme ex‚cutable ou un fichier de commandes.\n"]}], "source": ["!chmod 600 ~/.kaggle/kaggle.json"]}, {"cell_type": "code", "execution_count": 1, "id": "0795edcd-b844-47ef-8c85-f6fdf7565015", "metadata": {}, "outputs": [], "source": ["from ultralytics import YOLO\n", "\n", "# Load a pre-trained YOLOv8 model\n", "model = YOLO('yolov8n.pt')  # 'n' stands for nano, you can use 's', 'm', 'l', or 'x'\n"]}, {"cell_type": "code", "execution_count": 3, "id": "a3b08b66-0ad4-4656-9f07-8d32fa48af89", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://download.pytorch.org/whl/cpu\n", "Requirement already satisfied: torch in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (2.6.0)\n", "Requirement already satisfied: torchvision in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (0.21.0)\n", "Requirement already satisfied: torchaudio in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (2.6.0)\n", "Requirement already satisfied: filelock in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from torch) (3.17.0)\n", "Requirement already satisfied: typing-extensions>=4.10.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from torch) (4.12.2)\n", "Requirement already satisfied: networkx in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from torch) (3.4.2)\n", "Requirement already satisfied: jinja2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from torch) (3.1.5)\n", "Requirement already satisfied: fsspec in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from torch) (2025.2.0)\n", "Requirement already satisfied: setuptools in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from torch) (75.8.0)\n", "Requirement already satisfied: sympy==1.13.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from torch) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from sympy==1.13.1->torch) (1.3.0)\n", "Requirement already satisfied: numpy in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from torchvision) (2.1.1)\n", "Requirement already satisfied: pillow!=8.3.*,>=5.3.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from torchvision) (11.1.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from jinja2->torch) (3.0.2)\n"]}], "source": ["!pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu\n"]}, {"cell_type": "code", "execution_count": 2, "id": "35742787-4117-4d07-9a04-f701ead54c7b", "metadata": {}, "outputs": [], "source": ["from ultralytics import YOLO\n", "\n", "# Load a pre-trained YOLOv8 model\n", "model = YOLO('yolov8s.pt')  # 'n' stands for nano, you can use 's', 'm', 'l', or 'x'\n"]}, {"cell_type": "code", "execution_count": null, "id": "d7236119-93a6-4b6d-865f-375ee3367bd9", "metadata": {}, "outputs": [], "source": ["!yolo task=detect mode=train data=C:/Users/<USER>/datasets/dataset/data.yaml model=yolov8s.pt epochs=150 imgsz=640\n"]}, {"cell_type": "code", "execution_count": 1, "id": "18643b7a-ba97-43bc-8c19-a10e6a29057d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.6.0+cpu\n"]}], "source": ["import torch\n", "print(torch.__version__)\n"]}, {"cell_type": "code", "execution_count": 3, "id": "0e2e170c-7e32-4c58-9636-53f20a555bc9", "metadata": {"scrolled": true}, "outputs": [{"ename": "NameError", "evalue": "name 'model' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[3], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mmodel\u001b[49m\u001b[38;5;241m.\u001b[39mtrain(data\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mC:/Users/<USER>/FixTorchUMLDGM/dataset/data1.yaml\u001b[39m\u001b[38;5;124m'\u001b[39m, epochs\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m150\u001b[39m , imgsz\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m640\u001b[39m)\n\u001b[0;32m      3\u001b[0m \u001b[38;5;66;03m#C:\\Users\\<USER>\\FixTorchUMLDGM\\dataset\u001b[39;00m\n", "\u001b[1;31mNameError\u001b[0m: name 'model' is not defined"]}], "source": ["model.train(data='C:/Users/<USER>/FixTorchUMLDGM/dataset/data1.yaml', epochs=150 , imgsz=640)\n", "\n", "#C:\\Users\\<USER>\\FixTorchUMLDGM\\dataset"]}, {"cell_type": "code", "execution_count": 1, "id": "00fbec82-d752-4522-a26e-932911d90fad", "metadata": {}, "outputs": [], "source": ["from ultralytics import YOLO\n", "\n", "# Load a pre-trained YOLOv8 model\n", "model = YOLO('charge49.pt')  # 'n' stands for nano, you can use 's', 'm', 'l', or 'x'\n"]}, {"cell_type": "code", "execution_count": 3, "id": "58b10346-bfc6-40f5-bad3-c8772a03fe19", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "image 1/1 C:\\Users\\<USER>\\FixTorchUMLDGM\\dataset\\1234.jpg: 640x448 (no detections), 124.2ms\n", "Speed: 3.4ms preprocess, 124.2ms inference, 0.4ms postprocess per image at shape (1, 3, 640, 448)\n", "Results saved to \u001b[1mruns\\detect\\predict4\u001b[0m\n"]}], "source": ["results = model('dataset/1234.jpg', save=True, show=True)"]}, {"cell_type": "code", "execution_count": 1, "id": "620d44cc-ca4e-475e-bb00-96a41dff14f8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["C:\\Users\\<USER>\\FixTorchUMLDGM\n"]}], "source": ["import os\n", "print(os.getcwd())\n"]}, {"cell_type": "code", "execution_count": 1, "id": "c493ad79-55f1-4e7a-b35f-4ae8bb235cd7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Nombre de classes : 8\n", "Noms des classes : ['aggregation', 'association', 'class', 'composition', 'endpoin', 'generalization', 'head', 'one-way-association']\n"]}], "source": ["import yaml\n", "\n", "with open('C:/Users/<USER>/datasets/dataset/data.yaml', 'r') as f:\n", "    data_yaml = yaml.safe_load(f)\n", "\n", "print(\"Nombre de classes :\", data_yaml['nc'])\n", "print(\"Noms des classes :\", data_yaml['names'])\n"]}, {"cell_type": "code", "execution_count": 4, "id": "b13e7051-2abc-4e69-9f5e-03a533eba739", "metadata": {}, "outputs": [], "source": ["import glob\n", "\n", "annots = glob.glob('C:/Users/<USER>/datasets/dataset/train/labels/*.txt')\n", "for path in annots:\n", "    with open(path, 'r') as f:\n", "        for line in f:\n", "            class_id = int(line.split()[0])\n", "            if class_id >= 8:\n", "                print(f\"❌ Classe invalide dans {path}: class {class_id}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "7225888d-a5da-4bac-a8fa-50efd9a10a16", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}