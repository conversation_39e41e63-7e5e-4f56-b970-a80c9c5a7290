**Cross-references to related issues.**  If there is no existing issue that describes your bug or feature request, then [create an issue](https://github.com/cytoscape/cytoscape.js/issues/new/choose) before making your pull request.

Associated issues: 

- #X
- #Y
- #Z

**Notes re. the content of the pull request.** Give context to reviewers or serve as a general record of the changes made.  Add a screenshot or video to demonstrate your new feature, if possible.

- This PR adds this.
- This PR does that.
- This PR allows us to do some other thing.

**Checklist**

Author:

- [ ] The proper base branch has been selected.  New features go on `unstable`.  Bug-fix patches can go on either `unstable` or `master`.
- [ ] Automated tests have been included in this pull request, if possible, for the new feature(s) or bug fix.  Check this box if tests are not pragmatically possible (e.g. rendering features could include screenshots or videos instead of automated tests).
- [ ] The associated GitHub issues are included (above).
- [ ] Notes have been included (above).
- [ ] For new or updated API, the `index.d.ts` Typescript definition file has been appropriately updated.

Reviewers:

- [ ] All automated checks are passing (green check next to latest commit).
- [ ] At least one reviewer has signed off on the pull request.
- [ ] For bug fixes:  Just after this pull request is merged, it should be applied to both the `master` branch and the `unstable` branch.  Normally, this just requires cherry-picking the corresponding merge commit from `master` to `unstable` -- or vice versa.
