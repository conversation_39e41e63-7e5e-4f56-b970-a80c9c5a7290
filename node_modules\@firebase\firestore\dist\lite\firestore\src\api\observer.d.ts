/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { FirestoreError } from '../util/error';
/**
 * Observer/Subscribe interfaces.
 */
export type NextFn<T> = (value: T) => void;
export type ErrorFn = (error: FirestoreError) => void;
export type CompleteFn = () => void;
export interface PartialObserver<T> {
    next?: NextFn<T>;
    error?: ErrorFn;
    complete?: CompleteFn;
}
export declare function isPartialObserver<T>(obj: unknown): obj is PartialObserver<T>;
