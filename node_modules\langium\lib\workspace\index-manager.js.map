{"version": 3, "file": "index-manager.js", "sourceRoot": "", "sources": ["../../src/workspace/index-manager.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAKhF,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AACpD,OAAO,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;AACnD,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAE7D,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAE5C,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AAoEjD,MAAM,OAAO,mBAAmB;IAuB5B,YAAY,QAAmC;QAjB/C;;;WAGG;QACgB,gBAAW,GAAG,IAAI,GAAG,EAAgC,CAAC;QACzE;;;WAGG;QACgB,sBAAiB,GAAG,IAAI,YAAY,EAAwC,CAAC;QAChG;;;;WAIG;QACgB,mBAAc,GAAG,IAAI,GAAG,EAAkC,CAAC;QAG1E,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,gBAAgB,CAAC;QACrD,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAC;QAChD,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC;IAChD,CAAC;IAED,iBAAiB,CAAC,UAAmB,EAAE,WAAmB;QACtD,MAAM,YAAY,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC;QACjD,MAAM,MAAM,GAA2B,EAAE,CAAC;QAC1C,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAClC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBACvB,IAAI,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,YAAY,CAAC,IAAI,QAAQ,CAAC,UAAU,KAAK,WAAW,EAAE,CAAC;oBAC3F,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC1B,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QACH,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAED,WAAW,CAAC,QAAiB,EAAE,IAAkB;QAC7C,IAAI,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;QACnD,IAAI,IAAI,EAAE,CAAC;YACP,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACtE,CAAC;QACD,OAAO,YAAY;aACd,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;aACnD,IAAI,EAAE,CAAC;IAChB,CAAC;IAES,mBAAmB,CAAC,GAAW,EAAE,QAAiB;;QACxD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,OAAO,MAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,mCAAI,EAAE,CAAC;QAC3C,CAAC;QACD,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE;;YAChE,MAAM,mBAAmB,GAAG,MAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,mCAAI,EAAE,CAAC;YAC5D,OAAO,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC;QACH,OAAO,YAAY,CAAC;IACxB,CAAC;IAED,MAAM,CAAC,GAAQ;QACX,MAAM,SAAS,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;QACjC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACnC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACxC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,QAAyB,EAAE,WAAW,GAAG,iBAAiB,CAAC,IAAI;QAC/E,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAChE,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,cAAc,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QACjG,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;QACpC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACnC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,QAAyB,EAAE,WAAW,GAAG,iBAAiB,CAAC,IAAI;QAClF,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAChE,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,SAAS,CAAC,4BAA4B,CAAC,kBAAkB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QAClH,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,SAAS,CAAC,CAAC;IAChE,CAAC;IAED,UAAU,CAAC,QAAyB,EAAE,WAAwB;QAC1D,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;QACpE,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IAC3F,CAAC;CAEJ"}