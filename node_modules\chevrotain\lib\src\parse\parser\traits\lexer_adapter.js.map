{"version": 3, "file": "lexer_adapter.js", "sourceRoot": "", "sources": ["../../../../../src/parse/parser/traits/lexer_adapter.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAI3C;;;;;;GAMG;AACH,MAAM,OAAO,YAAY;IAKvB,gBAAgB;QACd,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;IACpB,CAAC;IAED,IAAI,KAAK,CAAC,QAAkB;QAC1B,iEAAiE;QACjE,kFAAkF;QAClF,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,EAAE;YAClC,MAAM,KAAK,CACT,kFAAkF,CACnF,CAAC;SACH;QACD,iEAAiE;QACjE,kFAAkF;QAClF,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,MAAM,CAAC;IACzC,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,2CAA2C;IAC3C,UAAU;QACR,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7C,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SACnB;aAAM;YACL,OAAO,WAAW,CAAC;SACpB;IACH,CAAC;IAED,kGAAkG;IAClG,yCAAyC;IACzC,EAAE,CAAsB,OAAe;QACrC,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACzC,IAAI,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,eAAe,IAAI,SAAS,EAAE;YACtD,OAAO,WAAW,CAAC;SACpB;aAAM;YACL,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;SAClC;IACH,CAAC;IAED,YAAY;QACV,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAED,gBAAgB;QACd,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,gBAAgB,CAAsB,QAAgB;QACpD,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC;IAC1B,CAAC;IAED,eAAe;QACb,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;IACpB,CAAC;IAED,qBAAqB;QACnB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;IAC3C,CAAC;IAED,gBAAgB;QACd,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;IACjC,CAAC;CACF"}