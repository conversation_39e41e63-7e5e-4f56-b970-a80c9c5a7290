import{a as e,b as i,c as a,d as o}from"./chunk-D7JRX7UM.mjs";import"./chunk-IO347I67.mjs";import"./chunk-L736DJ4U.mjs";import"./chunk-QTJCGBHB.mjs";import"./chunk-USR3SDWQ.mjs";import"./chunk-2VPXETT4.mjs";import"./chunk-S67DUUA5.mjs";import"./chunk-LM6QDVU5.mjs";import"./chunk-HESFG3RP.mjs";import"./chunk-YM3XIQPS.mjs";import"./chunk-TI4EEUUG.mjs";import"./chunk-ZKYS2E5M.mjs";import"./chunk-6BY5RJGC.mjs";import{a as t}from"./chunk-GTKDMUJJ.mjs";var f={parser:e,get db(){return new a(2)},renderer:i,styles:o,init:t(r=>{r.state||(r.state={}),r.state.arrowMarkerAbsolute=r.arrowMarkerAbsolute},"init")};export{f as diagram};
