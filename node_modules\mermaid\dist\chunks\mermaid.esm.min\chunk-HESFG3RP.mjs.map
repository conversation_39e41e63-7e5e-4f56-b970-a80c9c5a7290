{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ts-dedent@2.2.0/node_modules/ts-dedent/src/index.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/defaults.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/rules.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/helpers.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/Tokenizer.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/Lexer.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/Renderer.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/TextRenderer.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/Parser.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/Hooks.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/Instance.ts", "../../../../../node_modules/.pnpm/marked@15.0.7/node_modules/marked/src/marked.ts", "../../../src/rendering-util/handle-markdown-text.ts", "../../../src/rendering-util/splitText.ts", "../../../src/rendering-util/createText.ts"], "sourcesContent": ["export function dedent(\n  templ: TemplateStringsArray | string,\n  ...values: unknown[]\n): string {\n  let strings = Array.from(typeof templ === 'string' ? [templ] : templ);\n\n  // 1. Remove trailing whitespace.\n  strings[strings.length - 1] = strings[strings.length - 1].replace(\n    /\\r?\\n([\\t ]*)$/,\n    '',\n  );\n\n  // 2. Find all line breaks to determine the highest common indentation level.\n  const indentLengths = strings.reduce((arr, str) => {\n    const matches = str.match(/\\n([\\t ]+|(?!\\s).)/g);\n    if (matches) {\n      return arr.concat(\n        matches.map((match) => match.match(/[\\t ]/g)?.length ?? 0),\n      );\n    }\n    return arr;\n  }, <number[]>[]);\n\n  // 3. Remove the common indentation from all strings.\n  if (indentLengths.length) {\n    const pattern = new RegExp(`\\n[\\t ]{${Math.min(...indentLengths)}}`, 'g');\n\n    strings = strings.map((str) => str.replace(pattern, '\\n'));\n  }\n\n  // 4. Remove leading whitespace.\n  strings[0] = strings[0].replace(/^\\r?\\n/, '');\n\n  // 5. Perform interpolation.\n  let string = strings[0];\n\n  values.forEach((value, i) => {\n    // 5.1 Read current indentation level\n    const endentations = string.match(/(?:^|\\n)( *)$/)\n    const endentation = endentations ? endentations[1] : ''\n    let indentedValue = value\n    // 5.2 Add indentation to values with multiline strings\n    if (typeof value === 'string' && value.includes('\\n')) {\n      indentedValue = String(value)\n        .split('\\n')\n        .map((str, i) => {\n          return i === 0 ? str : `${endentation}${str}`\n        })\n        .join('\\n');\n    }\n\n    string += indentedValue + strings[i + 1];\n  });\n\n  return string;\n}\n\nexport default dedent;\n", "/**\n * Gets the original marked default options.\n */\nexport function _getDefaults() {\n    return {\n        async: false,\n        breaks: false,\n        extensions: null,\n        gfm: true,\n        hooks: null,\n        pedantic: false,\n        renderer: null,\n        silent: false,\n        tokenizer: null,\n        walkTokens: null,\n    };\n}\nexport let _defaults = _getDefaults();\nexport function changeDefaults(newDefaults) {\n    _defaults = newDefaults;\n}\n", "const noopTest = { exec: () => null };\nfunction edit(regex, opt = '') {\n    let source = typeof regex === 'string' ? regex : regex.source;\n    const obj = {\n        replace: (name, val) => {\n            let valSource = typeof val === 'string' ? val : val.source;\n            valSource = valSource.replace(other.caret, '$1');\n            source = source.replace(name, valSource);\n            return obj;\n        },\n        getRegex: () => {\n            return new RegExp(source, opt);\n        },\n    };\n    return obj;\n}\nexport const other = {\n    codeRemoveIndent: /^(?: {1,4}| {0,3}\\t)/gm,\n    outputLinkReplace: /\\\\([\\[\\]])/g,\n    indentCodeCompensation: /^(\\s+)(?:```)/,\n    beginningSpace: /^\\s+/,\n    endingHash: /#$/,\n    startingSpaceChar: /^ /,\n    endingSpaceChar: / $/,\n    nonSpaceChar: /[^ ]/,\n    newLineCharGlobal: /\\n/g,\n    tabCharGlobal: /\\t/g,\n    multipleSpaceGlobal: /\\s+/g,\n    blankLine: /^[ \\t]*$/,\n    doubleBlankLine: /\\n[ \\t]*\\n[ \\t]*$/,\n    blockquoteStart: /^ {0,3}>/,\n    blockquoteSetextReplace: /\\n {0,3}((?:=+|-+) *)(?=\\n|$)/g,\n    blockquoteSetextReplace2: /^ {0,3}>[ \\t]?/gm,\n    listReplaceTabs: /^\\t+/,\n    listReplaceNesting: /^ {1,4}(?=( {4})*[^ ])/g,\n    listIsTask: /^\\[[ xX]\\] /,\n    listReplaceTask: /^\\[[ xX]\\] +/,\n    anyLine: /\\n.*\\n/,\n    hrefBrackets: /^<(.*)>$/,\n    tableDelimiter: /[:|]/,\n    tableAlignChars: /^\\||\\| *$/g,\n    tableRowBlankLine: /\\n[ \\t]*$/,\n    tableAlignRight: /^ *-+: *$/,\n    tableAlignCenter: /^ *:-+: *$/,\n    tableAlignLeft: /^ *:-+ *$/,\n    startATag: /^<a /i,\n    endATag: /^<\\/a>/i,\n    startPreScriptTag: /^<(pre|code|kbd|script)(\\s|>)/i,\n    endPreScriptTag: /^<\\/(pre|code|kbd|script)(\\s|>)/i,\n    startAngleBracket: /^</,\n    endAngleBracket: />$/,\n    pedanticHrefTitle: /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/,\n    unicodeAlphaNumeric: /[\\p{L}\\p{N}]/u,\n    escapeTest: /[&<>\"']/,\n    escapeReplace: /[&<>\"']/g,\n    escapeTestNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/,\n    escapeReplaceNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/g,\n    unescapeTest: /&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig,\n    caret: /(^|[^\\[])\\^/g,\n    percentDecode: /%25/g,\n    findPipe: /\\|/g,\n    splitPipe: / \\|/,\n    slashPipe: /\\\\\\|/g,\n    carriageReturn: /\\r\\n|\\r/g,\n    spaceLine: /^ +$/gm,\n    notSpaceStart: /^\\S*/,\n    endingNewline: /\\n$/,\n    listItemRegex: (bull) => new RegExp(`^( {0,3}${bull})((?:[\\t ][^\\\\n]*)?(?:\\\\n|$))`),\n    nextBulletRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \\t][^\\\\n]*)?(?:\\\\n|$))`),\n    hrRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`),\n    fencesBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:\\`\\`\\`|~~~)`),\n    headingBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}#`),\n    htmlBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}<(?:[a-z].*>|!--)`, 'i'),\n};\n/**\n * Block-Level Grammar\n */\nconst newline = /^(?:[ \\t]*(?:\\n|$))+/;\nconst blockCode = /^((?: {4}| {0,3}\\t)[^\\n]+(?:\\n(?:[ \\t]*(?:\\n|$))*)?)+/;\nconst fences = /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/;\nconst hr = /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/;\nconst heading = /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/;\nconst bullet = /(?:[*+-]|\\d{1,9}[.)])/;\nconst lheadingCore = /^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\\n(?!\\s*?\\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/;\nconst lheading = edit(lheadingCore)\n    .replace(/bull/g, bullet) // lists can interrupt\n    .replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n    .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n    .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n    .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n    .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n    .replace(/\\|table/g, '') // table not in commonmark\n    .getRegex();\nconst lheadingGfm = edit(lheadingCore)\n    .replace(/bull/g, bullet) // lists can interrupt\n    .replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n    .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n    .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n    .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n    .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n    .replace(/table/g, / {0,3}\\|?(?:[:\\- ]*\\|)+[\\:\\- ]*\\n/) // table can interrupt\n    .getRegex();\nconst _paragraph = /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/;\nconst blockText = /^[^\\n]+/;\nconst _blockLabel = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/;\nconst def = edit(/^ {0,3}\\[(label)\\]: *(?:\\n[ \\t]*)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n[ \\t]*)?| *\\n[ \\t]*)(title))? *(?:\\n+|$)/)\n    .replace('label', _blockLabel)\n    .replace('title', /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/)\n    .getRegex();\nconst list = edit(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/)\n    .replace(/bull/g, bullet)\n    .getRegex();\nconst _tag = 'address|article|aside|base|basefont|blockquote|body|caption'\n    + '|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption'\n    + '|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe'\n    + '|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option'\n    + '|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title'\n    + '|tr|track|ul';\nconst _comment = /<!--(?:-?>|[\\s\\S]*?(?:-->|$))/;\nconst html = edit('^ {0,3}(?:' // optional indentation\n    + '<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)' // (1)\n    + '|comment[^\\\\n]*(\\\\n+|$)' // (2)\n    + '|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)' // (3)\n    + '|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)' // (4)\n    + '|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)' // (5)\n    + '|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (6)\n    + '|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) open tag\n    + '|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) closing tag\n    + ')', 'i')\n    .replace('comment', _comment)\n    .replace('tag', _tag)\n    .replace('attribute', / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/)\n    .getRegex();\nconst paragraph = edit(_paragraph)\n    .replace('hr', hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n    .replace('|table', '')\n    .replace('blockquote', ' {0,3}>')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n    .getRegex();\nconst blockquote = edit(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/)\n    .replace('paragraph', paragraph)\n    .getRegex();\n/**\n * Normal Block Grammar\n */\nconst blockNormal = {\n    blockquote,\n    code: blockCode,\n    def,\n    fences,\n    heading,\n    hr,\n    html,\n    lheading,\n    list,\n    newline,\n    paragraph,\n    table: noopTest,\n    text: blockText,\n};\n/**\n * GFM Block Grammar\n */\nconst gfmTable = edit('^ *([^\\\\n ].*)\\\\n' // Header\n    + ' {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)' // Align\n    + '(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)') // Cells\n    .replace('hr', hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('blockquote', ' {0,3}>')\n    .replace('code', '(?: {4}| {0,3}\\t)[^\\\\n]')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', _tag) // tables can be interrupted by type (6) html blocks\n    .getRegex();\nconst blockGfm = {\n    ...blockNormal,\n    lheading: lheadingGfm,\n    table: gfmTable,\n    paragraph: edit(_paragraph)\n        .replace('hr', hr)\n        .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n        .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n        .replace('table', gfmTable) // interrupt paragraphs with table\n        .replace('blockquote', ' {0,3}>')\n        .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n        .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n        .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n        .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n        .getRegex(),\n};\n/**\n * Pedantic grammar (original John Gruber's loose markdown specification)\n */\nconst blockPedantic = {\n    ...blockNormal,\n    html: edit('^ *(?:comment *(?:\\\\n|\\\\s*$)'\n        + '|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)' // closed tag\n        + '|<tag(?:\"[^\"]*\"|\\'[^\\']*\\'|\\\\s[^\\'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))')\n        .replace('comment', _comment)\n        .replace(/tag/g, '(?!(?:'\n        + 'a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub'\n        + '|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)'\n        + '\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b')\n        .getRegex(),\n    def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n    heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n    fences: noopTest, // fences not supported\n    lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n    paragraph: edit(_paragraph)\n        .replace('hr', hr)\n        .replace('heading', ' *#{1,6} *[^\\n]')\n        .replace('lheading', lheading)\n        .replace('|table', '')\n        .replace('blockquote', ' {0,3}>')\n        .replace('|fences', '')\n        .replace('|list', '')\n        .replace('|html', '')\n        .replace('|tag', '')\n        .getRegex(),\n};\n/**\n * Inline-Level Grammar\n */\nconst escape = /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/;\nconst inlineCode = /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/;\nconst br = /^( {2,}|\\\\)\\n(?!\\s*$)/;\nconst inlineText = /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/;\n// list of unicode punctuation marks, plus any missing characters from CommonMark spec\nconst _punctuation = /[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpace = /[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpace = /[^\\s\\p{P}\\p{S}]/u;\nconst punctuation = edit(/^((?![*_])punctSpace)/, 'u')\n    .replace(/punctSpace/g, _punctuationOrSpace).getRegex();\n// GFM allows ~ inside strong and em for strikethrough\nconst _punctuationGfmStrongEm = /(?!~)[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpaceGfmStrongEm = /(?!~)[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpaceGfmStrongEm = /(?:[^\\s\\p{P}\\p{S}]|~)/u;\n// sequences em should skip over [title](link), `code`, <html>\nconst blockSkip = /\\[[^[\\]]*?\\]\\((?:\\\\.|[^\\\\\\(\\)]|\\((?:\\\\.|[^\\\\\\(\\)])*\\))*\\)|`[^`]*?`|<[^<>]*?>/g;\nconst emStrongLDelimCore = /^(?:\\*+(?:((?!\\*)punct)|[^\\s*]))|^_+(?:((?!_)punct)|([^\\s_]))/;\nconst emStrongLDelim = edit(emStrongLDelimCore, 'u')\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst emStrongLDelimGfm = edit(emStrongLDelimCore, 'u')\n    .replace(/punct/g, _punctuationGfmStrongEm)\n    .getRegex();\nconst emStrongRDelimAstCore = '^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)' // Skip orphan inside strong\n    + '|[^*]+(?=[^*])' // Consume to delim\n    + '|(?!\\\\*)punct(\\\\*+)(?=[\\\\s]|$)' // (1) #*** can only be a Right Delimiter\n    + '|notPunctSpace(\\\\*+)(?!\\\\*)(?=punctSpace|$)' // (2) a***#, a*** can only be a Right Delimiter\n    + '|(?!\\\\*)punctSpace(\\\\*+)(?=notPunctSpace)' // (3) #***a, ***a can only be Left Delimiter\n    + '|[\\\\s](\\\\*+)(?!\\\\*)(?=punct)' // (4) ***# can only be Left Delimiter\n    + '|(?!\\\\*)punct(\\\\*+)(?!\\\\*)(?=punct)' // (5) #***# can be either Left or Right Delimiter\n    + '|notPunctSpace(\\\\*+)(?=notPunctSpace)'; // (6) a***a can be either Left or Right Delimiter\nconst emStrongRDelimAst = edit(emStrongRDelimAstCore, 'gu')\n    .replace(/notPunctSpace/g, _notPunctuationOrSpace)\n    .replace(/punctSpace/g, _punctuationOrSpace)\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst emStrongRDelimAstGfm = edit(emStrongRDelimAstCore, 'gu')\n    .replace(/notPunctSpace/g, _notPunctuationOrSpaceGfmStrongEm)\n    .replace(/punctSpace/g, _punctuationOrSpaceGfmStrongEm)\n    .replace(/punct/g, _punctuationGfmStrongEm)\n    .getRegex();\n// (6) Not allowed for _\nconst emStrongRDelimUnd = edit('^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)' // Skip orphan inside strong\n    + '|[^_]+(?=[^_])' // Consume to delim\n    + '|(?!_)punct(_+)(?=[\\\\s]|$)' // (1) #___ can only be a Right Delimiter\n    + '|notPunctSpace(_+)(?!_)(?=punctSpace|$)' // (2) a___#, a___ can only be a Right Delimiter\n    + '|(?!_)punctSpace(_+)(?=notPunctSpace)' // (3) #___a, ___a can only be Left Delimiter\n    + '|[\\\\s](_+)(?!_)(?=punct)' // (4) ___# can only be Left Delimiter\n    + '|(?!_)punct(_+)(?!_)(?=punct)', 'gu') // (5) #___# can be either Left or Right Delimiter\n    .replace(/notPunctSpace/g, _notPunctuationOrSpace)\n    .replace(/punctSpace/g, _punctuationOrSpace)\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst anyPunctuation = edit(/\\\\(punct)/, 'gu')\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst autolink = edit(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/)\n    .replace('scheme', /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/)\n    .replace('email', /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/)\n    .getRegex();\nconst _inlineComment = edit(_comment).replace('(?:-->|$)', '-->').getRegex();\nconst tag = edit('^comment'\n    + '|^</[a-zA-Z][\\\\w:-]*\\\\s*>' // self-closing tag\n    + '|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>' // open tag\n    + '|^<\\\\?[\\\\s\\\\S]*?\\\\?>' // processing instruction, e.g. <?php ?>\n    + '|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>' // declaration, e.g. <!DOCTYPE html>\n    + '|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>') // CDATA section\n    .replace('comment', _inlineComment)\n    .replace('attribute', /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/)\n    .getRegex();\nconst _inlineLabel = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/;\nconst link = edit(/^!?\\[(label)\\]\\(\\s*(href)(?:\\s+(title))?\\s*\\)/)\n    .replace('label', _inlineLabel)\n    .replace('href', /<(?:\\\\.|[^\\n<>\\\\])+>|[^\\s\\x00-\\x1f]*/)\n    .replace('title', /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/)\n    .getRegex();\nconst reflink = edit(/^!?\\[(label)\\]\\[(ref)\\]/)\n    .replace('label', _inlineLabel)\n    .replace('ref', _blockLabel)\n    .getRegex();\nconst nolink = edit(/^!?\\[(ref)\\](?:\\[\\])?/)\n    .replace('ref', _blockLabel)\n    .getRegex();\nconst reflinkSearch = edit('reflink|nolink(?!\\\\()', 'g')\n    .replace('reflink', reflink)\n    .replace('nolink', nolink)\n    .getRegex();\n/**\n * Normal Inline Grammar\n */\nconst inlineNormal = {\n    _backpedal: noopTest, // only used for GFM url\n    anyPunctuation,\n    autolink,\n    blockSkip,\n    br,\n    code: inlineCode,\n    del: noopTest,\n    emStrongLDelim,\n    emStrongRDelimAst,\n    emStrongRDelimUnd,\n    escape,\n    link,\n    nolink,\n    punctuation,\n    reflink,\n    reflinkSearch,\n    tag,\n    text: inlineText,\n    url: noopTest,\n};\n/**\n * Pedantic Inline Grammar\n */\nconst inlinePedantic = {\n    ...inlineNormal,\n    link: edit(/^!?\\[(label)\\]\\((.*?)\\)/)\n        .replace('label', _inlineLabel)\n        .getRegex(),\n    reflink: edit(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/)\n        .replace('label', _inlineLabel)\n        .getRegex(),\n};\n/**\n * GFM Inline Grammar\n */\nconst inlineGfm = {\n    ...inlineNormal,\n    emStrongRDelimAst: emStrongRDelimAstGfm,\n    emStrongLDelim: emStrongLDelimGfm,\n    url: edit(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/, 'i')\n        .replace('email', /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/)\n        .getRegex(),\n    _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n    del: /^(~~?)(?=[^\\s~])((?:\\\\.|[^\\\\])*?(?:\\\\.|[^\\s~\\\\]))\\1(?=[^~]|$)/,\n    text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/,\n};\n/**\n * GFM + Line Breaks Inline Grammar\n */\nconst inlineBreaks = {\n    ...inlineGfm,\n    br: edit(br).replace('{2,}', '*').getRegex(),\n    text: edit(inlineGfm.text)\n        .replace('\\\\b_', '\\\\b_| {2,}\\\\n')\n        .replace(/\\{2,\\}/g, '*')\n        .getRegex(),\n};\n/**\n * exports\n */\nexport const block = {\n    normal: blockNormal,\n    gfm: blockGfm,\n    pedantic: blockPedantic,\n};\nexport const inline = {\n    normal: inlineNormal,\n    gfm: inlineGfm,\n    breaks: inlineBreaks,\n    pedantic: inlinePedantic,\n};\n", "import { other } from './rules.ts';\n/**\n * Helpers\n */\nconst escapeReplacements = {\n    '&': '&amp;',\n    '<': '&lt;',\n    '>': '&gt;',\n    '\"': '&quot;',\n    \"'\": '&#39;',\n};\nconst getEscapeReplacement = (ch) => escapeReplacements[ch];\nexport function escape(html, encode) {\n    if (encode) {\n        if (other.escapeTest.test(html)) {\n            return html.replace(other.escapeReplace, getEscapeReplacement);\n        }\n    }\n    else {\n        if (other.escapeTestNoEncode.test(html)) {\n            return html.replace(other.escapeReplaceNoEncode, getEscapeReplacement);\n        }\n    }\n    return html;\n}\nexport function unescape(html) {\n    // explicitly match decimal, hex, and named HTML entities\n    return html.replace(other.unescapeTest, (_, n) => {\n        n = n.toLowerCase();\n        if (n === 'colon')\n            return ':';\n        if (n.charAt(0) === '#') {\n            return n.charAt(1) === 'x'\n                ? String.fromCharCode(parseInt(n.substring(2), 16))\n                : String.fromCharCode(+n.substring(1));\n        }\n        return '';\n    });\n}\nexport function cleanUrl(href) {\n    try {\n        href = encodeURI(href).replace(other.percentDecode, '%');\n    }\n    catch {\n        return null;\n    }\n    return href;\n}\nexport function splitCells(tableRow, count) {\n    // ensure that every cell-delimiting pipe has a space\n    // before it to distinguish it from an escaped pipe\n    const row = tableRow.replace(other.findPipe, (match, offset, str) => {\n        let escaped = false;\n        let curr = offset;\n        while (--curr >= 0 && str[curr] === '\\\\')\n            escaped = !escaped;\n        if (escaped) {\n            // odd number of slashes means | is escaped\n            // so we leave it alone\n            return '|';\n        }\n        else {\n            // add space before unescaped |\n            return ' |';\n        }\n    }), cells = row.split(other.splitPipe);\n    let i = 0;\n    // First/last cell in a row cannot be empty if it has no leading/trailing pipe\n    if (!cells[0].trim()) {\n        cells.shift();\n    }\n    if (cells.length > 0 && !cells.at(-1)?.trim()) {\n        cells.pop();\n    }\n    if (count) {\n        if (cells.length > count) {\n            cells.splice(count);\n        }\n        else {\n            while (cells.length < count)\n                cells.push('');\n        }\n    }\n    for (; i < cells.length; i++) {\n        // leading or trailing whitespace is ignored per the gfm spec\n        cells[i] = cells[i].trim().replace(other.slashPipe, '|');\n    }\n    return cells;\n}\n/**\n * Remove trailing 'c's. Equivalent to str.replace(/c*$/, '').\n * /c*$/ is vulnerable to REDOS.\n *\n * @param str\n * @param c\n * @param invert Remove suffix of non-c chars instead. Default falsey.\n */\nexport function rtrim(str, c, invert) {\n    const l = str.length;\n    if (l === 0) {\n        return '';\n    }\n    // Length of suffix matching the invert condition.\n    let suffLen = 0;\n    // Step left until we fail to match the invert condition.\n    while (suffLen < l) {\n        const currChar = str.charAt(l - suffLen - 1);\n        if (currChar === c && !invert) {\n            suffLen++;\n        }\n        else if (currChar !== c && invert) {\n            suffLen++;\n        }\n        else {\n            break;\n        }\n    }\n    return str.slice(0, l - suffLen);\n}\nexport function findClosingBracket(str, b) {\n    if (str.indexOf(b[1]) === -1) {\n        return -1;\n    }\n    let level = 0;\n    for (let i = 0; i < str.length; i++) {\n        if (str[i] === '\\\\') {\n            i++;\n        }\n        else if (str[i] === b[0]) {\n            level++;\n        }\n        else if (str[i] === b[1]) {\n            level--;\n            if (level < 0) {\n                return i;\n            }\n        }\n    }\n    return -1;\n}\n", "import { _defaults } from './defaults.ts';\nimport { rtrim, splitCells, findClosingBracket, } from './helpers.ts';\nfunction outputLink(cap, link, raw, lexer, rules) {\n    const href = link.href;\n    const title = link.title || null;\n    const text = cap[1].replace(rules.other.outputLinkReplace, '$1');\n    if (cap[0].charAt(0) !== '!') {\n        lexer.state.inLink = true;\n        const token = {\n            type: 'link',\n            raw,\n            href,\n            title,\n            text,\n            tokens: lexer.inlineTokens(text),\n        };\n        lexer.state.inLink = false;\n        return token;\n    }\n    return {\n        type: 'image',\n        raw,\n        href,\n        title,\n        text,\n    };\n}\nfunction indentCodeCompensation(raw, text, rules) {\n    const matchIndentToCode = raw.match(rules.other.indentCodeCompensation);\n    if (matchIndentToCode === null) {\n        return text;\n    }\n    const indentToCode = matchIndentToCode[1];\n    return text\n        .split('\\n')\n        .map(node => {\n        const matchIndentInNode = node.match(rules.other.beginningSpace);\n        if (matchIndentInNode === null) {\n            return node;\n        }\n        const [indentInNode] = matchIndentInNode;\n        if (indentInNode.length >= indentToCode.length) {\n            return node.slice(indentToCode.length);\n        }\n        return node;\n    })\n        .join('\\n');\n}\n/**\n * Tokenizer\n */\nexport class _Tokenizer {\n    options;\n    rules; // set by the lexer\n    lexer; // set by the lexer\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    space(src) {\n        const cap = this.rules.block.newline.exec(src);\n        if (cap && cap[0].length > 0) {\n            return {\n                type: 'space',\n                raw: cap[0],\n            };\n        }\n    }\n    code(src) {\n        const cap = this.rules.block.code.exec(src);\n        if (cap) {\n            const text = cap[0].replace(this.rules.other.codeRemoveIndent, '');\n            return {\n                type: 'code',\n                raw: cap[0],\n                codeBlockStyle: 'indented',\n                text: !this.options.pedantic\n                    ? rtrim(text, '\\n')\n                    : text,\n            };\n        }\n    }\n    fences(src) {\n        const cap = this.rules.block.fences.exec(src);\n        if (cap) {\n            const raw = cap[0];\n            const text = indentCodeCompensation(raw, cap[3] || '', this.rules);\n            return {\n                type: 'code',\n                raw,\n                lang: cap[2] ? cap[2].trim().replace(this.rules.inline.anyPunctuation, '$1') : cap[2],\n                text,\n            };\n        }\n    }\n    heading(src) {\n        const cap = this.rules.block.heading.exec(src);\n        if (cap) {\n            let text = cap[2].trim();\n            // remove trailing #s\n            if (this.rules.other.endingHash.test(text)) {\n                const trimmed = rtrim(text, '#');\n                if (this.options.pedantic) {\n                    text = trimmed.trim();\n                }\n                else if (!trimmed || this.rules.other.endingSpaceChar.test(trimmed)) {\n                    // CommonMark requires space before trailing #s\n                    text = trimmed.trim();\n                }\n            }\n            return {\n                type: 'heading',\n                raw: cap[0],\n                depth: cap[1].length,\n                text,\n                tokens: this.lexer.inline(text),\n            };\n        }\n    }\n    hr(src) {\n        const cap = this.rules.block.hr.exec(src);\n        if (cap) {\n            return {\n                type: 'hr',\n                raw: rtrim(cap[0], '\\n'),\n            };\n        }\n    }\n    blockquote(src) {\n        const cap = this.rules.block.blockquote.exec(src);\n        if (cap) {\n            let lines = rtrim(cap[0], '\\n').split('\\n');\n            let raw = '';\n            let text = '';\n            const tokens = [];\n            while (lines.length > 0) {\n                let inBlockquote = false;\n                const currentLines = [];\n                let i;\n                for (i = 0; i < lines.length; i++) {\n                    // get lines up to a continuation\n                    if (this.rules.other.blockquoteStart.test(lines[i])) {\n                        currentLines.push(lines[i]);\n                        inBlockquote = true;\n                    }\n                    else if (!inBlockquote) {\n                        currentLines.push(lines[i]);\n                    }\n                    else {\n                        break;\n                    }\n                }\n                lines = lines.slice(i);\n                const currentRaw = currentLines.join('\\n');\n                const currentText = currentRaw\n                    // precede setext continuation with 4 spaces so it isn't a setext\n                    .replace(this.rules.other.blockquoteSetextReplace, '\\n    $1')\n                    .replace(this.rules.other.blockquoteSetextReplace2, '');\n                raw = raw ? `${raw}\\n${currentRaw}` : currentRaw;\n                text = text ? `${text}\\n${currentText}` : currentText;\n                // parse blockquote lines as top level tokens\n                // merge paragraphs if this is a continuation\n                const top = this.lexer.state.top;\n                this.lexer.state.top = true;\n                this.lexer.blockTokens(currentText, tokens, true);\n                this.lexer.state.top = top;\n                // if there is no continuation then we are done\n                if (lines.length === 0) {\n                    break;\n                }\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'code') {\n                    // blockquote continuation cannot be preceded by a code block\n                    break;\n                }\n                else if (lastToken?.type === 'blockquote') {\n                    // include continuation in nested blockquote\n                    const oldToken = lastToken;\n                    const newText = oldToken.raw + '\\n' + lines.join('\\n');\n                    const newToken = this.blockquote(newText);\n                    tokens[tokens.length - 1] = newToken;\n                    raw = raw.substring(0, raw.length - oldToken.raw.length) + newToken.raw;\n                    text = text.substring(0, text.length - oldToken.text.length) + newToken.text;\n                    break;\n                }\n                else if (lastToken?.type === 'list') {\n                    // include continuation in nested list\n                    const oldToken = lastToken;\n                    const newText = oldToken.raw + '\\n' + lines.join('\\n');\n                    const newToken = this.list(newText);\n                    tokens[tokens.length - 1] = newToken;\n                    raw = raw.substring(0, raw.length - lastToken.raw.length) + newToken.raw;\n                    text = text.substring(0, text.length - oldToken.raw.length) + newToken.raw;\n                    lines = newText.substring(tokens.at(-1).raw.length).split('\\n');\n                    continue;\n                }\n            }\n            return {\n                type: 'blockquote',\n                raw,\n                tokens,\n                text,\n            };\n        }\n    }\n    list(src) {\n        let cap = this.rules.block.list.exec(src);\n        if (cap) {\n            let bull = cap[1].trim();\n            const isordered = bull.length > 1;\n            const list = {\n                type: 'list',\n                raw: '',\n                ordered: isordered,\n                start: isordered ? +bull.slice(0, -1) : '',\n                loose: false,\n                items: [],\n            };\n            bull = isordered ? `\\\\d{1,9}\\\\${bull.slice(-1)}` : `\\\\${bull}`;\n            if (this.options.pedantic) {\n                bull = isordered ? bull : '[*+-]';\n            }\n            // Get next list item\n            const itemRegex = this.rules.other.listItemRegex(bull);\n            let endsWithBlankLine = false;\n            // Check if current bullet point can start a new List Item\n            while (src) {\n                let endEarly = false;\n                let raw = '';\n                let itemContents = '';\n                if (!(cap = itemRegex.exec(src))) {\n                    break;\n                }\n                if (this.rules.block.hr.test(src)) { // End list if bullet was actually HR (possibly move into itemRegex?)\n                    break;\n                }\n                raw = cap[0];\n                src = src.substring(raw.length);\n                let line = cap[2].split('\\n', 1)[0].replace(this.rules.other.listReplaceTabs, (t) => ' '.repeat(3 * t.length));\n                let nextLine = src.split('\\n', 1)[0];\n                let blankLine = !line.trim();\n                let indent = 0;\n                if (this.options.pedantic) {\n                    indent = 2;\n                    itemContents = line.trimStart();\n                }\n                else if (blankLine) {\n                    indent = cap[1].length + 1;\n                }\n                else {\n                    indent = cap[2].search(this.rules.other.nonSpaceChar); // Find first non-space char\n                    indent = indent > 4 ? 1 : indent; // Treat indented code blocks (> 4 spaces) as having only 1 indent\n                    itemContents = line.slice(indent);\n                    indent += cap[1].length;\n                }\n                if (blankLine && this.rules.other.blankLine.test(nextLine)) { // Items begin with at most one blank line\n                    raw += nextLine + '\\n';\n                    src = src.substring(nextLine.length + 1);\n                    endEarly = true;\n                }\n                if (!endEarly) {\n                    const nextBulletRegex = this.rules.other.nextBulletRegex(indent);\n                    const hrRegex = this.rules.other.hrRegex(indent);\n                    const fencesBeginRegex = this.rules.other.fencesBeginRegex(indent);\n                    const headingBeginRegex = this.rules.other.headingBeginRegex(indent);\n                    const htmlBeginRegex = this.rules.other.htmlBeginRegex(indent);\n                    // Check if following lines should be included in List Item\n                    while (src) {\n                        const rawLine = src.split('\\n', 1)[0];\n                        let nextLineWithoutTabs;\n                        nextLine = rawLine;\n                        // Re-align to follow commonmark nesting rules\n                        if (this.options.pedantic) {\n                            nextLine = nextLine.replace(this.rules.other.listReplaceNesting, '  ');\n                            nextLineWithoutTabs = nextLine;\n                        }\n                        else {\n                            nextLineWithoutTabs = nextLine.replace(this.rules.other.tabCharGlobal, '    ');\n                        }\n                        // End list item if found code fences\n                        if (fencesBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of new heading\n                        if (headingBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of html block\n                        if (htmlBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of new bullet\n                        if (nextBulletRegex.test(nextLine)) {\n                            break;\n                        }\n                        // Horizontal rule found\n                        if (hrRegex.test(nextLine)) {\n                            break;\n                        }\n                        if (nextLineWithoutTabs.search(this.rules.other.nonSpaceChar) >= indent || !nextLine.trim()) { // Dedent if possible\n                            itemContents += '\\n' + nextLineWithoutTabs.slice(indent);\n                        }\n                        else {\n                            // not enough indentation\n                            if (blankLine) {\n                                break;\n                            }\n                            // paragraph continuation unless last line was a different block level element\n                            if (line.replace(this.rules.other.tabCharGlobal, '    ').search(this.rules.other.nonSpaceChar) >= 4) { // indented code block\n                                break;\n                            }\n                            if (fencesBeginRegex.test(line)) {\n                                break;\n                            }\n                            if (headingBeginRegex.test(line)) {\n                                break;\n                            }\n                            if (hrRegex.test(line)) {\n                                break;\n                            }\n                            itemContents += '\\n' + nextLine;\n                        }\n                        if (!blankLine && !nextLine.trim()) { // Check if current line is blank\n                            blankLine = true;\n                        }\n                        raw += rawLine + '\\n';\n                        src = src.substring(rawLine.length + 1);\n                        line = nextLineWithoutTabs.slice(indent);\n                    }\n                }\n                if (!list.loose) {\n                    // If the previous item ended with a blank line, the list is loose\n                    if (endsWithBlankLine) {\n                        list.loose = true;\n                    }\n                    else if (this.rules.other.doubleBlankLine.test(raw)) {\n                        endsWithBlankLine = true;\n                    }\n                }\n                let istask = null;\n                let ischecked;\n                // Check for task list items\n                if (this.options.gfm) {\n                    istask = this.rules.other.listIsTask.exec(itemContents);\n                    if (istask) {\n                        ischecked = istask[0] !== '[ ] ';\n                        itemContents = itemContents.replace(this.rules.other.listReplaceTask, '');\n                    }\n                }\n                list.items.push({\n                    type: 'list_item',\n                    raw,\n                    task: !!istask,\n                    checked: ischecked,\n                    loose: false,\n                    text: itemContents,\n                    tokens: [],\n                });\n                list.raw += raw;\n            }\n            // Do not consume newlines at end of final item. Alternatively, make itemRegex *start* with any newlines to simplify/speed up endsWithBlankLine logic\n            const lastItem = list.items.at(-1);\n            if (lastItem) {\n                lastItem.raw = lastItem.raw.trimEnd();\n                lastItem.text = lastItem.text.trimEnd();\n            }\n            else {\n                // not a list since there were no items\n                return;\n            }\n            list.raw = list.raw.trimEnd();\n            // Item child tokens handled here at end because we needed to have the final item to trim it first\n            for (let i = 0; i < list.items.length; i++) {\n                this.lexer.state.top = false;\n                list.items[i].tokens = this.lexer.blockTokens(list.items[i].text, []);\n                if (!list.loose) {\n                    // Check if list should be loose\n                    const spacers = list.items[i].tokens.filter(t => t.type === 'space');\n                    const hasMultipleLineBreaks = spacers.length > 0 && spacers.some(t => this.rules.other.anyLine.test(t.raw));\n                    list.loose = hasMultipleLineBreaks;\n                }\n            }\n            // Set all items to loose if list is loose\n            if (list.loose) {\n                for (let i = 0; i < list.items.length; i++) {\n                    list.items[i].loose = true;\n                }\n            }\n            return list;\n        }\n    }\n    html(src) {\n        const cap = this.rules.block.html.exec(src);\n        if (cap) {\n            const token = {\n                type: 'html',\n                block: true,\n                raw: cap[0],\n                pre: cap[1] === 'pre' || cap[1] === 'script' || cap[1] === 'style',\n                text: cap[0],\n            };\n            return token;\n        }\n    }\n    def(src) {\n        const cap = this.rules.block.def.exec(src);\n        if (cap) {\n            const tag = cap[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal, ' ');\n            const href = cap[2] ? cap[2].replace(this.rules.other.hrefBrackets, '$1').replace(this.rules.inline.anyPunctuation, '$1') : '';\n            const title = cap[3] ? cap[3].substring(1, cap[3].length - 1).replace(this.rules.inline.anyPunctuation, '$1') : cap[3];\n            return {\n                type: 'def',\n                tag,\n                raw: cap[0],\n                href,\n                title,\n            };\n        }\n    }\n    table(src) {\n        const cap = this.rules.block.table.exec(src);\n        if (!cap) {\n            return;\n        }\n        if (!this.rules.other.tableDelimiter.test(cap[2])) {\n            // delimiter row must have a pipe (|) or colon (:) otherwise it is a setext heading\n            return;\n        }\n        const headers = splitCells(cap[1]);\n        const aligns = cap[2].replace(this.rules.other.tableAlignChars, '').split('|');\n        const rows = cap[3]?.trim() ? cap[3].replace(this.rules.other.tableRowBlankLine, '').split('\\n') : [];\n        const item = {\n            type: 'table',\n            raw: cap[0],\n            header: [],\n            align: [],\n            rows: [],\n        };\n        if (headers.length !== aligns.length) {\n            // header and align columns must be equal, rows can be different.\n            return;\n        }\n        for (const align of aligns) {\n            if (this.rules.other.tableAlignRight.test(align)) {\n                item.align.push('right');\n            }\n            else if (this.rules.other.tableAlignCenter.test(align)) {\n                item.align.push('center');\n            }\n            else if (this.rules.other.tableAlignLeft.test(align)) {\n                item.align.push('left');\n            }\n            else {\n                item.align.push(null);\n            }\n        }\n        for (let i = 0; i < headers.length; i++) {\n            item.header.push({\n                text: headers[i],\n                tokens: this.lexer.inline(headers[i]),\n                header: true,\n                align: item.align[i],\n            });\n        }\n        for (const row of rows) {\n            item.rows.push(splitCells(row, item.header.length).map((cell, i) => {\n                return {\n                    text: cell,\n                    tokens: this.lexer.inline(cell),\n                    header: false,\n                    align: item.align[i],\n                };\n            }));\n        }\n        return item;\n    }\n    lheading(src) {\n        const cap = this.rules.block.lheading.exec(src);\n        if (cap) {\n            return {\n                type: 'heading',\n                raw: cap[0],\n                depth: cap[2].charAt(0) === '=' ? 1 : 2,\n                text: cap[1],\n                tokens: this.lexer.inline(cap[1]),\n            };\n        }\n    }\n    paragraph(src) {\n        const cap = this.rules.block.paragraph.exec(src);\n        if (cap) {\n            const text = cap[1].charAt(cap[1].length - 1) === '\\n'\n                ? cap[1].slice(0, -1)\n                : cap[1];\n            return {\n                type: 'paragraph',\n                raw: cap[0],\n                text,\n                tokens: this.lexer.inline(text),\n            };\n        }\n    }\n    text(src) {\n        const cap = this.rules.block.text.exec(src);\n        if (cap) {\n            return {\n                type: 'text',\n                raw: cap[0],\n                text: cap[0],\n                tokens: this.lexer.inline(cap[0]),\n            };\n        }\n    }\n    escape(src) {\n        const cap = this.rules.inline.escape.exec(src);\n        if (cap) {\n            return {\n                type: 'escape',\n                raw: cap[0],\n                text: cap[1],\n            };\n        }\n    }\n    tag(src) {\n        const cap = this.rules.inline.tag.exec(src);\n        if (cap) {\n            if (!this.lexer.state.inLink && this.rules.other.startATag.test(cap[0])) {\n                this.lexer.state.inLink = true;\n            }\n            else if (this.lexer.state.inLink && this.rules.other.endATag.test(cap[0])) {\n                this.lexer.state.inLink = false;\n            }\n            if (!this.lexer.state.inRawBlock && this.rules.other.startPreScriptTag.test(cap[0])) {\n                this.lexer.state.inRawBlock = true;\n            }\n            else if (this.lexer.state.inRawBlock && this.rules.other.endPreScriptTag.test(cap[0])) {\n                this.lexer.state.inRawBlock = false;\n            }\n            return {\n                type: 'html',\n                raw: cap[0],\n                inLink: this.lexer.state.inLink,\n                inRawBlock: this.lexer.state.inRawBlock,\n                block: false,\n                text: cap[0],\n            };\n        }\n    }\n    link(src) {\n        const cap = this.rules.inline.link.exec(src);\n        if (cap) {\n            const trimmedUrl = cap[2].trim();\n            if (!this.options.pedantic && this.rules.other.startAngleBracket.test(trimmedUrl)) {\n                // commonmark requires matching angle brackets\n                if (!(this.rules.other.endAngleBracket.test(trimmedUrl))) {\n                    return;\n                }\n                // ending angle bracket cannot be escaped\n                const rtrimSlash = rtrim(trimmedUrl.slice(0, -1), '\\\\');\n                if ((trimmedUrl.length - rtrimSlash.length) % 2 === 0) {\n                    return;\n                }\n            }\n            else {\n                // find closing parenthesis\n                const lastParenIndex = findClosingBracket(cap[2], '()');\n                if (lastParenIndex > -1) {\n                    const start = cap[0].indexOf('!') === 0 ? 5 : 4;\n                    const linkLen = start + cap[1].length + lastParenIndex;\n                    cap[2] = cap[2].substring(0, lastParenIndex);\n                    cap[0] = cap[0].substring(0, linkLen).trim();\n                    cap[3] = '';\n                }\n            }\n            let href = cap[2];\n            let title = '';\n            if (this.options.pedantic) {\n                // split pedantic href and title\n                const link = this.rules.other.pedanticHrefTitle.exec(href);\n                if (link) {\n                    href = link[1];\n                    title = link[3];\n                }\n            }\n            else {\n                title = cap[3] ? cap[3].slice(1, -1) : '';\n            }\n            href = href.trim();\n            if (this.rules.other.startAngleBracket.test(href)) {\n                if (this.options.pedantic && !(this.rules.other.endAngleBracket.test(trimmedUrl))) {\n                    // pedantic allows starting angle bracket without ending angle bracket\n                    href = href.slice(1);\n                }\n                else {\n                    href = href.slice(1, -1);\n                }\n            }\n            return outputLink(cap, {\n                href: href ? href.replace(this.rules.inline.anyPunctuation, '$1') : href,\n                title: title ? title.replace(this.rules.inline.anyPunctuation, '$1') : title,\n            }, cap[0], this.lexer, this.rules);\n        }\n    }\n    reflink(src, links) {\n        let cap;\n        if ((cap = this.rules.inline.reflink.exec(src))\n            || (cap = this.rules.inline.nolink.exec(src))) {\n            const linkString = (cap[2] || cap[1]).replace(this.rules.other.multipleSpaceGlobal, ' ');\n            const link = links[linkString.toLowerCase()];\n            if (!link) {\n                const text = cap[0].charAt(0);\n                return {\n                    type: 'text',\n                    raw: text,\n                    text,\n                };\n            }\n            return outputLink(cap, link, cap[0], this.lexer, this.rules);\n        }\n    }\n    emStrong(src, maskedSrc, prevChar = '') {\n        let match = this.rules.inline.emStrongLDelim.exec(src);\n        if (!match)\n            return;\n        // _ can't be between two alphanumerics. \\p{L}\\p{N} includes non-english alphabet/numbers as well\n        if (match[3] && prevChar.match(this.rules.other.unicodeAlphaNumeric))\n            return;\n        const nextChar = match[1] || match[2] || '';\n        if (!nextChar || !prevChar || this.rules.inline.punctuation.exec(prevChar)) {\n            // unicode Regex counts emoji as 1 char; spread into array for proper count (used multiple times below)\n            const lLength = [...match[0]].length - 1;\n            let rDelim, rLength, delimTotal = lLength, midDelimTotal = 0;\n            const endReg = match[0][0] === '*' ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;\n            endReg.lastIndex = 0;\n            // Clip maskedSrc to same section of string as src (move to lexer?)\n            maskedSrc = maskedSrc.slice(-1 * src.length + lLength);\n            while ((match = endReg.exec(maskedSrc)) != null) {\n                rDelim = match[1] || match[2] || match[3] || match[4] || match[5] || match[6];\n                if (!rDelim)\n                    continue; // skip single * in __abc*abc__\n                rLength = [...rDelim].length;\n                if (match[3] || match[4]) { // found another Left Delim\n                    delimTotal += rLength;\n                    continue;\n                }\n                else if (match[5] || match[6]) { // either Left or Right Delim\n                    if (lLength % 3 && !((lLength + rLength) % 3)) {\n                        midDelimTotal += rLength;\n                        continue; // CommonMark Emphasis Rules 9-10\n                    }\n                }\n                delimTotal -= rLength;\n                if (delimTotal > 0)\n                    continue; // Haven't found enough closing delimiters\n                // Remove extra characters. *a*** -> *a*\n                rLength = Math.min(rLength, rLength + delimTotal + midDelimTotal);\n                // char length can be >1 for unicode characters;\n                const lastCharLength = [...match[0]][0].length;\n                const raw = src.slice(0, lLength + match.index + lastCharLength + rLength);\n                // Create `em` if smallest delimiter has odd char count. *a***\n                if (Math.min(lLength, rLength) % 2) {\n                    const text = raw.slice(1, -1);\n                    return {\n                        type: 'em',\n                        raw,\n                        text,\n                        tokens: this.lexer.inlineTokens(text),\n                    };\n                }\n                // Create 'strong' if smallest delimiter has even char count. **a***\n                const text = raw.slice(2, -2);\n                return {\n                    type: 'strong',\n                    raw,\n                    text,\n                    tokens: this.lexer.inlineTokens(text),\n                };\n            }\n        }\n    }\n    codespan(src) {\n        const cap = this.rules.inline.code.exec(src);\n        if (cap) {\n            let text = cap[2].replace(this.rules.other.newLineCharGlobal, ' ');\n            const hasNonSpaceChars = this.rules.other.nonSpaceChar.test(text);\n            const hasSpaceCharsOnBothEnds = this.rules.other.startingSpaceChar.test(text) && this.rules.other.endingSpaceChar.test(text);\n            if (hasNonSpaceChars && hasSpaceCharsOnBothEnds) {\n                text = text.substring(1, text.length - 1);\n            }\n            return {\n                type: 'codespan',\n                raw: cap[0],\n                text,\n            };\n        }\n    }\n    br(src) {\n        const cap = this.rules.inline.br.exec(src);\n        if (cap) {\n            return {\n                type: 'br',\n                raw: cap[0],\n            };\n        }\n    }\n    del(src) {\n        const cap = this.rules.inline.del.exec(src);\n        if (cap) {\n            return {\n                type: 'del',\n                raw: cap[0],\n                text: cap[2],\n                tokens: this.lexer.inlineTokens(cap[2]),\n            };\n        }\n    }\n    autolink(src) {\n        const cap = this.rules.inline.autolink.exec(src);\n        if (cap) {\n            let text, href;\n            if (cap[2] === '@') {\n                text = cap[1];\n                href = 'mailto:' + text;\n            }\n            else {\n                text = cap[1];\n                href = text;\n            }\n            return {\n                type: 'link',\n                raw: cap[0],\n                text,\n                href,\n                tokens: [\n                    {\n                        type: 'text',\n                        raw: text,\n                        text,\n                    },\n                ],\n            };\n        }\n    }\n    url(src) {\n        let cap;\n        if (cap = this.rules.inline.url.exec(src)) {\n            let text, href;\n            if (cap[2] === '@') {\n                text = cap[0];\n                href = 'mailto:' + text;\n            }\n            else {\n                // do extended autolink path validation\n                let prevCapZero;\n                do {\n                    prevCapZero = cap[0];\n                    cap[0] = this.rules.inline._backpedal.exec(cap[0])?.[0] ?? '';\n                } while (prevCapZero !== cap[0]);\n                text = cap[0];\n                if (cap[1] === 'www.') {\n                    href = 'http://' + cap[0];\n                }\n                else {\n                    href = cap[0];\n                }\n            }\n            return {\n                type: 'link',\n                raw: cap[0],\n                text,\n                href,\n                tokens: [\n                    {\n                        type: 'text',\n                        raw: text,\n                        text,\n                    },\n                ],\n            };\n        }\n    }\n    inlineText(src) {\n        const cap = this.rules.inline.text.exec(src);\n        if (cap) {\n            const escaped = this.lexer.state.inRawBlock;\n            return {\n                type: 'text',\n                raw: cap[0],\n                text: cap[0],\n                escaped,\n            };\n        }\n    }\n}\n", "import { _Tokenizer } from './Tokenizer.ts';\nimport { _defaults } from './defaults.ts';\nimport { other, block, inline } from './rules.ts';\n/**\n * Block Lexer\n */\nexport class _Lexer {\n    tokens;\n    options;\n    state;\n    tokenizer;\n    inlineQueue;\n    constructor(options) {\n        // TokenList cannot be created in one go\n        this.tokens = [];\n        this.tokens.links = Object.create(null);\n        this.options = options || _defaults;\n        this.options.tokenizer = this.options.tokenizer || new _Tokenizer();\n        this.tokenizer = this.options.tokenizer;\n        this.tokenizer.options = this.options;\n        this.tokenizer.lexer = this;\n        this.inlineQueue = [];\n        this.state = {\n            inLink: false,\n            inRawBlock: false,\n            top: true,\n        };\n        const rules = {\n            other,\n            block: block.normal,\n            inline: inline.normal,\n        };\n        if (this.options.pedantic) {\n            rules.block = block.pedantic;\n            rules.inline = inline.pedantic;\n        }\n        else if (this.options.gfm) {\n            rules.block = block.gfm;\n            if (this.options.breaks) {\n                rules.inline = inline.breaks;\n            }\n            else {\n                rules.inline = inline.gfm;\n            }\n        }\n        this.tokenizer.rules = rules;\n    }\n    /**\n     * Expose Rules\n     */\n    static get rules() {\n        return {\n            block,\n            inline,\n        };\n    }\n    /**\n     * Static Lex Method\n     */\n    static lex(src, options) {\n        const lexer = new _Lexer(options);\n        return lexer.lex(src);\n    }\n    /**\n     * Static Lex Inline Method\n     */\n    static lexInline(src, options) {\n        const lexer = new _Lexer(options);\n        return lexer.inlineTokens(src);\n    }\n    /**\n     * Preprocessing\n     */\n    lex(src) {\n        src = src.replace(other.carriageReturn, '\\n');\n        this.blockTokens(src, this.tokens);\n        for (let i = 0; i < this.inlineQueue.length; i++) {\n            const next = this.inlineQueue[i];\n            this.inlineTokens(next.src, next.tokens);\n        }\n        this.inlineQueue = [];\n        return this.tokens;\n    }\n    blockTokens(src, tokens = [], lastParagraphClipped = false) {\n        if (this.options.pedantic) {\n            src = src.replace(other.tabCharGlobal, '    ').replace(other.spaceLine, '');\n        }\n        while (src) {\n            let token;\n            if (this.options.extensions?.block?.some((extTokenizer) => {\n                if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n                    src = src.substring(token.raw.length);\n                    tokens.push(token);\n                    return true;\n                }\n                return false;\n            })) {\n                continue;\n            }\n            // newline\n            if (token = this.tokenizer.space(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (token.raw.length === 1 && lastToken !== undefined) {\n                    // if there's a single \\n as a spacer, it's terminating the last line,\n                    // so move it there so that we don't get unnecessary paragraph tags\n                    lastToken.raw += '\\n';\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // code\n            if (token = this.tokenizer.code(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                // An indented code block cannot interrupt a paragraph.\n                if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // fences\n            if (token = this.tokenizer.fences(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // heading\n            if (token = this.tokenizer.heading(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // hr\n            if (token = this.tokenizer.hr(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // blockquote\n            if (token = this.tokenizer.blockquote(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // list\n            if (token = this.tokenizer.list(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // html\n            if (token = this.tokenizer.html(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // def\n            if (token = this.tokenizer.def(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.raw;\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else if (!this.tokens.links[token.tag]) {\n                    this.tokens.links[token.tag] = {\n                        href: token.href,\n                        title: token.title,\n                    };\n                }\n                continue;\n            }\n            // table (gfm)\n            if (token = this.tokenizer.table(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // lheading\n            if (token = this.tokenizer.lheading(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // top-level paragraph\n            // prevent paragraph consuming extensions by clipping 'src' to extension start\n            let cutSrc = src;\n            if (this.options.extensions?.startBlock) {\n                let startIndex = Infinity;\n                const tempSrc = src.slice(1);\n                let tempStart;\n                this.options.extensions.startBlock.forEach((getStartIndex) => {\n                    tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n                    if (typeof tempStart === 'number' && tempStart >= 0) {\n                        startIndex = Math.min(startIndex, tempStart);\n                    }\n                });\n                if (startIndex < Infinity && startIndex >= 0) {\n                    cutSrc = src.substring(0, startIndex + 1);\n                }\n            }\n            if (this.state.top && (token = this.tokenizer.paragraph(cutSrc))) {\n                const lastToken = tokens.at(-1);\n                if (lastParagraphClipped && lastToken?.type === 'paragraph') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.pop();\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                lastParagraphClipped = cutSrc.length !== src.length;\n                src = src.substring(token.raw.length);\n                continue;\n            }\n            // text\n            if (token = this.tokenizer.text(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'text') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.pop();\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            if (src) {\n                const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n                if (this.options.silent) {\n                    console.error(errMsg);\n                    break;\n                }\n                else {\n                    throw new Error(errMsg);\n                }\n            }\n        }\n        this.state.top = true;\n        return tokens;\n    }\n    inline(src, tokens = []) {\n        this.inlineQueue.push({ src, tokens });\n        return tokens;\n    }\n    /**\n     * Lexing/Compiling\n     */\n    inlineTokens(src, tokens = []) {\n        // String with links masked to avoid interference with em and strong\n        let maskedSrc = src;\n        let match = null;\n        // Mask out reflinks\n        if (this.tokens.links) {\n            const links = Object.keys(this.tokens.links);\n            if (links.length > 0) {\n                while ((match = this.tokenizer.rules.inline.reflinkSearch.exec(maskedSrc)) != null) {\n                    if (links.includes(match[0].slice(match[0].lastIndexOf('[') + 1, -1))) {\n                        maskedSrc = maskedSrc.slice(0, match.index)\n                            + '[' + 'a'.repeat(match[0].length - 2) + ']'\n                            + maskedSrc.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);\n                    }\n                }\n            }\n        }\n        // Mask out other blocks\n        while ((match = this.tokenizer.rules.inline.blockSkip.exec(maskedSrc)) != null) {\n            maskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n        }\n        // Mask out escaped characters\n        while ((match = this.tokenizer.rules.inline.anyPunctuation.exec(maskedSrc)) != null) {\n            maskedSrc = maskedSrc.slice(0, match.index) + '++' + maskedSrc.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n        }\n        let keepPrevChar = false;\n        let prevChar = '';\n        while (src) {\n            if (!keepPrevChar) {\n                prevChar = '';\n            }\n            keepPrevChar = false;\n            let token;\n            // extensions\n            if (this.options.extensions?.inline?.some((extTokenizer) => {\n                if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n                    src = src.substring(token.raw.length);\n                    tokens.push(token);\n                    return true;\n                }\n                return false;\n            })) {\n                continue;\n            }\n            // escape\n            if (token = this.tokenizer.escape(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // tag\n            if (token = this.tokenizer.tag(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // link\n            if (token = this.tokenizer.link(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // reflink, nolink\n            if (token = this.tokenizer.reflink(src, this.tokens.links)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (token.type === 'text' && lastToken?.type === 'text') {\n                    lastToken.raw += token.raw;\n                    lastToken.text += token.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // em & strong\n            if (token = this.tokenizer.emStrong(src, maskedSrc, prevChar)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // code\n            if (token = this.tokenizer.codespan(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // br\n            if (token = this.tokenizer.br(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // del (gfm)\n            if (token = this.tokenizer.del(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // autolink\n            if (token = this.tokenizer.autolink(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // url (gfm)\n            if (!this.state.inLink && (token = this.tokenizer.url(src))) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // text\n            // prevent inlineText consuming extensions by clipping 'src' to extension start\n            let cutSrc = src;\n            if (this.options.extensions?.startInline) {\n                let startIndex = Infinity;\n                const tempSrc = src.slice(1);\n                let tempStart;\n                this.options.extensions.startInline.forEach((getStartIndex) => {\n                    tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n                    if (typeof tempStart === 'number' && tempStart >= 0) {\n                        startIndex = Math.min(startIndex, tempStart);\n                    }\n                });\n                if (startIndex < Infinity && startIndex >= 0) {\n                    cutSrc = src.substring(0, startIndex + 1);\n                }\n            }\n            if (token = this.tokenizer.inlineText(cutSrc)) {\n                src = src.substring(token.raw.length);\n                if (token.raw.slice(-1) !== '_') { // Track prevChar before string of ____ started\n                    prevChar = token.raw.slice(-1);\n                }\n                keepPrevChar = true;\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'text') {\n                    lastToken.raw += token.raw;\n                    lastToken.text += token.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            if (src) {\n                const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n                if (this.options.silent) {\n                    console.error(errMsg);\n                    break;\n                }\n                else {\n                    throw new Error(errMsg);\n                }\n            }\n        }\n        return tokens;\n    }\n}\n", "import { _defaults } from './defaults.ts';\nimport { cleanUrl, escape, } from './helpers.ts';\nimport { other } from './rules.ts';\n/**\n * Renderer\n */\nexport class _Renderer {\n    options;\n    parser; // set by the parser\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    space(token) {\n        return '';\n    }\n    code({ text, lang, escaped }) {\n        const langString = (lang || '').match(other.notSpaceStart)?.[0];\n        const code = text.replace(other.endingNewline, '') + '\\n';\n        if (!langString) {\n            return '<pre><code>'\n                + (escaped ? code : escape(code, true))\n                + '</code></pre>\\n';\n        }\n        return '<pre><code class=\"language-'\n            + escape(langString)\n            + '\">'\n            + (escaped ? code : escape(code, true))\n            + '</code></pre>\\n';\n    }\n    blockquote({ tokens }) {\n        const body = this.parser.parse(tokens);\n        return `<blockquote>\\n${body}</blockquote>\\n`;\n    }\n    html({ text }) {\n        return text;\n    }\n    heading({ tokens, depth }) {\n        return `<h${depth}>${this.parser.parseInline(tokens)}</h${depth}>\\n`;\n    }\n    hr(token) {\n        return '<hr>\\n';\n    }\n    list(token) {\n        const ordered = token.ordered;\n        const start = token.start;\n        let body = '';\n        for (let j = 0; j < token.items.length; j++) {\n            const item = token.items[j];\n            body += this.listitem(item);\n        }\n        const type = ordered ? 'ol' : 'ul';\n        const startAttr = (ordered && start !== 1) ? (' start=\"' + start + '\"') : '';\n        return '<' + type + startAttr + '>\\n' + body + '</' + type + '>\\n';\n    }\n    listitem(item) {\n        let itemBody = '';\n        if (item.task) {\n            const checkbox = this.checkbox({ checked: !!item.checked });\n            if (item.loose) {\n                if (item.tokens[0]?.type === 'paragraph') {\n                    item.tokens[0].text = checkbox + ' ' + item.tokens[0].text;\n                    if (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === 'text') {\n                        item.tokens[0].tokens[0].text = checkbox + ' ' + escape(item.tokens[0].tokens[0].text);\n                        item.tokens[0].tokens[0].escaped = true;\n                    }\n                }\n                else {\n                    item.tokens.unshift({\n                        type: 'text',\n                        raw: checkbox + ' ',\n                        text: checkbox + ' ',\n                        escaped: true,\n                    });\n                }\n            }\n            else {\n                itemBody += checkbox + ' ';\n            }\n        }\n        itemBody += this.parser.parse(item.tokens, !!item.loose);\n        return `<li>${itemBody}</li>\\n`;\n    }\n    checkbox({ checked }) {\n        return '<input '\n            + (checked ? 'checked=\"\" ' : '')\n            + 'disabled=\"\" type=\"checkbox\">';\n    }\n    paragraph({ tokens }) {\n        return `<p>${this.parser.parseInline(tokens)}</p>\\n`;\n    }\n    table(token) {\n        let header = '';\n        // header\n        let cell = '';\n        for (let j = 0; j < token.header.length; j++) {\n            cell += this.tablecell(token.header[j]);\n        }\n        header += this.tablerow({ text: cell });\n        let body = '';\n        for (let j = 0; j < token.rows.length; j++) {\n            const row = token.rows[j];\n            cell = '';\n            for (let k = 0; k < row.length; k++) {\n                cell += this.tablecell(row[k]);\n            }\n            body += this.tablerow({ text: cell });\n        }\n        if (body)\n            body = `<tbody>${body}</tbody>`;\n        return '<table>\\n'\n            + '<thead>\\n'\n            + header\n            + '</thead>\\n'\n            + body\n            + '</table>\\n';\n    }\n    tablerow({ text }) {\n        return `<tr>\\n${text}</tr>\\n`;\n    }\n    tablecell(token) {\n        const content = this.parser.parseInline(token.tokens);\n        const type = token.header ? 'th' : 'td';\n        const tag = token.align\n            ? `<${type} align=\"${token.align}\">`\n            : `<${type}>`;\n        return tag + content + `</${type}>\\n`;\n    }\n    /**\n     * span level renderer\n     */\n    strong({ tokens }) {\n        return `<strong>${this.parser.parseInline(tokens)}</strong>`;\n    }\n    em({ tokens }) {\n        return `<em>${this.parser.parseInline(tokens)}</em>`;\n    }\n    codespan({ text }) {\n        return `<code>${escape(text, true)}</code>`;\n    }\n    br(token) {\n        return '<br>';\n    }\n    del({ tokens }) {\n        return `<del>${this.parser.parseInline(tokens)}</del>`;\n    }\n    link({ href, title, tokens }) {\n        const text = this.parser.parseInline(tokens);\n        const cleanHref = cleanUrl(href);\n        if (cleanHref === null) {\n            return text;\n        }\n        href = cleanHref;\n        let out = '<a href=\"' + href + '\"';\n        if (title) {\n            out += ' title=\"' + (escape(title)) + '\"';\n        }\n        out += '>' + text + '</a>';\n        return out;\n    }\n    image({ href, title, text }) {\n        const cleanHref = cleanUrl(href);\n        if (cleanHref === null) {\n            return escape(text);\n        }\n        href = cleanHref;\n        let out = `<img src=\"${href}\" alt=\"${text}\"`;\n        if (title) {\n            out += ` title=\"${escape(title)}\"`;\n        }\n        out += '>';\n        return out;\n    }\n    text(token) {\n        return 'tokens' in token && token.tokens\n            ? this.parser.parseInline(token.tokens)\n            : ('escaped' in token && token.escaped ? token.text : escape(token.text));\n    }\n}\n", "/**\n * Text<PERSON><PERSON>er\n * returns only the textual part of the token\n */\nexport class _TextRenderer {\n    // no need for block level renderers\n    strong({ text }) {\n        return text;\n    }\n    em({ text }) {\n        return text;\n    }\n    codespan({ text }) {\n        return text;\n    }\n    del({ text }) {\n        return text;\n    }\n    html({ text }) {\n        return text;\n    }\n    text({ text }) {\n        return text;\n    }\n    link({ text }) {\n        return '' + text;\n    }\n    image({ text }) {\n        return '' + text;\n    }\n    br() {\n        return '';\n    }\n}\n", "import { _Renderer } from './Renderer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { _defaults } from './defaults.ts';\n/**\n * Parsing & Compiling\n */\nexport class _Parser {\n    options;\n    renderer;\n    textRenderer;\n    constructor(options) {\n        this.options = options || _defaults;\n        this.options.renderer = this.options.renderer || new _Renderer();\n        this.renderer = this.options.renderer;\n        this.renderer.options = this.options;\n        this.renderer.parser = this;\n        this.textRenderer = new _TextRenderer();\n    }\n    /**\n     * Static Parse Method\n     */\n    static parse(tokens, options) {\n        const parser = new _Parser(options);\n        return parser.parse(tokens);\n    }\n    /**\n     * Static Parse Inline Method\n     */\n    static parseInline(tokens, options) {\n        const parser = new _Parser(options);\n        return parser.parseInline(tokens);\n    }\n    /**\n     * Parse Loop\n     */\n    parse(tokens, top = true) {\n        let out = '';\n        for (let i = 0; i < tokens.length; i++) {\n            const anyToken = tokens[i];\n            // Run any renderer extensions\n            if (this.options.extensions?.renderers?.[anyToken.type]) {\n                const genericToken = anyToken;\n                const ret = this.options.extensions.renderers[genericToken.type].call({ parser: this }, genericToken);\n                if (ret !== false || !['space', 'hr', 'heading', 'code', 'table', 'blockquote', 'list', 'html', 'paragraph', 'text'].includes(genericToken.type)) {\n                    out += ret || '';\n                    continue;\n                }\n            }\n            const token = anyToken;\n            switch (token.type) {\n                case 'space': {\n                    out += this.renderer.space(token);\n                    continue;\n                }\n                case 'hr': {\n                    out += this.renderer.hr(token);\n                    continue;\n                }\n                case 'heading': {\n                    out += this.renderer.heading(token);\n                    continue;\n                }\n                case 'code': {\n                    out += this.renderer.code(token);\n                    continue;\n                }\n                case 'table': {\n                    out += this.renderer.table(token);\n                    continue;\n                }\n                case 'blockquote': {\n                    out += this.renderer.blockquote(token);\n                    continue;\n                }\n                case 'list': {\n                    out += this.renderer.list(token);\n                    continue;\n                }\n                case 'html': {\n                    out += this.renderer.html(token);\n                    continue;\n                }\n                case 'paragraph': {\n                    out += this.renderer.paragraph(token);\n                    continue;\n                }\n                case 'text': {\n                    let textToken = token;\n                    let body = this.renderer.text(textToken);\n                    while (i + 1 < tokens.length && tokens[i + 1].type === 'text') {\n                        textToken = tokens[++i];\n                        body += '\\n' + this.renderer.text(textToken);\n                    }\n                    if (top) {\n                        out += this.renderer.paragraph({\n                            type: 'paragraph',\n                            raw: body,\n                            text: body,\n                            tokens: [{ type: 'text', raw: body, text: body, escaped: true }],\n                        });\n                    }\n                    else {\n                        out += body;\n                    }\n                    continue;\n                }\n                default: {\n                    const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n                    if (this.options.silent) {\n                        console.error(errMsg);\n                        return '';\n                    }\n                    else {\n                        throw new Error(errMsg);\n                    }\n                }\n            }\n        }\n        return out;\n    }\n    /**\n     * Parse Inline Tokens\n     */\n    parseInline(tokens, renderer = this.renderer) {\n        let out = '';\n        for (let i = 0; i < tokens.length; i++) {\n            const anyToken = tokens[i];\n            // Run any renderer extensions\n            if (this.options.extensions?.renderers?.[anyToken.type]) {\n                const ret = this.options.extensions.renderers[anyToken.type].call({ parser: this }, anyToken);\n                if (ret !== false || !['escape', 'html', 'link', 'image', 'strong', 'em', 'codespan', 'br', 'del', 'text'].includes(anyToken.type)) {\n                    out += ret || '';\n                    continue;\n                }\n            }\n            const token = anyToken;\n            switch (token.type) {\n                case 'escape': {\n                    out += renderer.text(token);\n                    break;\n                }\n                case 'html': {\n                    out += renderer.html(token);\n                    break;\n                }\n                case 'link': {\n                    out += renderer.link(token);\n                    break;\n                }\n                case 'image': {\n                    out += renderer.image(token);\n                    break;\n                }\n                case 'strong': {\n                    out += renderer.strong(token);\n                    break;\n                }\n                case 'em': {\n                    out += renderer.em(token);\n                    break;\n                }\n                case 'codespan': {\n                    out += renderer.codespan(token);\n                    break;\n                }\n                case 'br': {\n                    out += renderer.br(token);\n                    break;\n                }\n                case 'del': {\n                    out += renderer.del(token);\n                    break;\n                }\n                case 'text': {\n                    out += renderer.text(token);\n                    break;\n                }\n                default: {\n                    const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n                    if (this.options.silent) {\n                        console.error(errMsg);\n                        return '';\n                    }\n                    else {\n                        throw new Error(errMsg);\n                    }\n                }\n            }\n        }\n        return out;\n    }\n}\n", "import { _defaults } from './defaults.ts';\nimport { _<PERSON>er } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nexport class _Hooks {\n    options;\n    block;\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    static passThroughHooks = new Set([\n        'preprocess',\n        'postprocess',\n        'processAllTokens',\n    ]);\n    /**\n     * Process markdown before marked\n     */\n    preprocess(markdown) {\n        return markdown;\n    }\n    /**\n     * Process HTML after marked is finished\n     */\n    postprocess(html) {\n        return html;\n    }\n    /**\n     * Process all tokens before walk tokens\n     */\n    processAllTokens(tokens) {\n        return tokens;\n    }\n    /**\n     * Provide function to tokenize markdown\n     */\n    provideLexer() {\n        return this.block ? _Lexer.lex : _Lexer.lexInline;\n    }\n    /**\n     * Provide function to parse tokens\n     */\n    provideParser() {\n        return this.block ? _Parser.parse : _Parser.parseInline;\n    }\n}\n", "import { _getDefaults } from './defaults.ts';\nimport { _<PERSON>er } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport { _Hooks } from './Hooks.ts';\nimport { _Renderer } from './Renderer.ts';\nimport { _Tokenizer } from './Tokenizer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { escape } from './helpers.ts';\nexport class Marked {\n    defaults = _getDefaults();\n    options = this.setOptions;\n    parse = this.parseMarkdown(true);\n    parseInline = this.parseMarkdown(false);\n    Parser = _Parser;\n    Renderer = _Renderer;\n    TextRenderer = _TextRenderer;\n    Lexer = _Lexer;\n    Tokenizer = _Tokenizer;\n    Hooks = _Hooks;\n    constructor(...args) {\n        this.use(...args);\n    }\n    /**\n     * Run callback for every token\n     */\n    walkTokens(tokens, callback) {\n        let values = [];\n        for (const token of tokens) {\n            values = values.concat(callback.call(this, token));\n            switch (token.type) {\n                case 'table': {\n                    const tableToken = token;\n                    for (const cell of tableToken.header) {\n                        values = values.concat(this.walkTokens(cell.tokens, callback));\n                    }\n                    for (const row of tableToken.rows) {\n                        for (const cell of row) {\n                            values = values.concat(this.walkTokens(cell.tokens, callback));\n                        }\n                    }\n                    break;\n                }\n                case 'list': {\n                    const listToken = token;\n                    values = values.concat(this.walkTokens(listToken.items, callback));\n                    break;\n                }\n                default: {\n                    const genericToken = token;\n                    if (this.defaults.extensions?.childTokens?.[genericToken.type]) {\n                        this.defaults.extensions.childTokens[genericToken.type].forEach((childTokens) => {\n                            const tokens = genericToken[childTokens].flat(Infinity);\n                            values = values.concat(this.walkTokens(tokens, callback));\n                        });\n                    }\n                    else if (genericToken.tokens) {\n                        values = values.concat(this.walkTokens(genericToken.tokens, callback));\n                    }\n                }\n            }\n        }\n        return values;\n    }\n    use(...args) {\n        const extensions = this.defaults.extensions || { renderers: {}, childTokens: {} };\n        args.forEach((pack) => {\n            // copy options to new object\n            const opts = { ...pack };\n            // set async to true if it was set to true before\n            opts.async = this.defaults.async || opts.async || false;\n            // ==-- Parse \"addon\" extensions --== //\n            if (pack.extensions) {\n                pack.extensions.forEach((ext) => {\n                    if (!ext.name) {\n                        throw new Error('extension name required');\n                    }\n                    if ('renderer' in ext) { // Renderer extensions\n                        const prevRenderer = extensions.renderers[ext.name];\n                        if (prevRenderer) {\n                            // Replace extension with func to run new extension but fall back if false\n                            extensions.renderers[ext.name] = function (...args) {\n                                let ret = ext.renderer.apply(this, args);\n                                if (ret === false) {\n                                    ret = prevRenderer.apply(this, args);\n                                }\n                                return ret;\n                            };\n                        }\n                        else {\n                            extensions.renderers[ext.name] = ext.renderer;\n                        }\n                    }\n                    if ('tokenizer' in ext) { // Tokenizer Extensions\n                        if (!ext.level || (ext.level !== 'block' && ext.level !== 'inline')) {\n                            throw new Error(\"extension level must be 'block' or 'inline'\");\n                        }\n                        const extLevel = extensions[ext.level];\n                        if (extLevel) {\n                            extLevel.unshift(ext.tokenizer);\n                        }\n                        else {\n                            extensions[ext.level] = [ext.tokenizer];\n                        }\n                        if (ext.start) { // Function to check for start of token\n                            if (ext.level === 'block') {\n                                if (extensions.startBlock) {\n                                    extensions.startBlock.push(ext.start);\n                                }\n                                else {\n                                    extensions.startBlock = [ext.start];\n                                }\n                            }\n                            else if (ext.level === 'inline') {\n                                if (extensions.startInline) {\n                                    extensions.startInline.push(ext.start);\n                                }\n                                else {\n                                    extensions.startInline = [ext.start];\n                                }\n                            }\n                        }\n                    }\n                    if ('childTokens' in ext && ext.childTokens) { // Child tokens to be visited by walkTokens\n                        extensions.childTokens[ext.name] = ext.childTokens;\n                    }\n                });\n                opts.extensions = extensions;\n            }\n            // ==-- Parse \"overwrite\" extensions --== //\n            if (pack.renderer) {\n                const renderer = this.defaults.renderer || new _Renderer(this.defaults);\n                for (const prop in pack.renderer) {\n                    if (!(prop in renderer)) {\n                        throw new Error(`renderer '${prop}' does not exist`);\n                    }\n                    if (['options', 'parser'].includes(prop)) {\n                        // ignore options property\n                        continue;\n                    }\n                    const rendererProp = prop;\n                    const rendererFunc = pack.renderer[rendererProp];\n                    const prevRenderer = renderer[rendererProp];\n                    // Replace renderer with func to run extension, but fall back if false\n                    renderer[rendererProp] = (...args) => {\n                        let ret = rendererFunc.apply(renderer, args);\n                        if (ret === false) {\n                            ret = prevRenderer.apply(renderer, args);\n                        }\n                        return ret || '';\n                    };\n                }\n                opts.renderer = renderer;\n            }\n            if (pack.tokenizer) {\n                const tokenizer = this.defaults.tokenizer || new _Tokenizer(this.defaults);\n                for (const prop in pack.tokenizer) {\n                    if (!(prop in tokenizer)) {\n                        throw new Error(`tokenizer '${prop}' does not exist`);\n                    }\n                    if (['options', 'rules', 'lexer'].includes(prop)) {\n                        // ignore options, rules, and lexer properties\n                        continue;\n                    }\n                    const tokenizerProp = prop;\n                    const tokenizerFunc = pack.tokenizer[tokenizerProp];\n                    const prevTokenizer = tokenizer[tokenizerProp];\n                    // Replace tokenizer with func to run extension, but fall back if false\n                    // @ts-expect-error cannot type tokenizer function dynamically\n                    tokenizer[tokenizerProp] = (...args) => {\n                        let ret = tokenizerFunc.apply(tokenizer, args);\n                        if (ret === false) {\n                            ret = prevTokenizer.apply(tokenizer, args);\n                        }\n                        return ret;\n                    };\n                }\n                opts.tokenizer = tokenizer;\n            }\n            // ==-- Parse Hooks extensions --== //\n            if (pack.hooks) {\n                const hooks = this.defaults.hooks || new _Hooks();\n                for (const prop in pack.hooks) {\n                    if (!(prop in hooks)) {\n                        throw new Error(`hook '${prop}' does not exist`);\n                    }\n                    if (['options', 'block'].includes(prop)) {\n                        // ignore options and block properties\n                        continue;\n                    }\n                    const hooksProp = prop;\n                    const hooksFunc = pack.hooks[hooksProp];\n                    const prevHook = hooks[hooksProp];\n                    if (_Hooks.passThroughHooks.has(prop)) {\n                        // @ts-expect-error cannot type hook function dynamically\n                        hooks[hooksProp] = (arg) => {\n                            if (this.defaults.async) {\n                                return Promise.resolve(hooksFunc.call(hooks, arg)).then(ret => {\n                                    return prevHook.call(hooks, ret);\n                                });\n                            }\n                            const ret = hooksFunc.call(hooks, arg);\n                            return prevHook.call(hooks, ret);\n                        };\n                    }\n                    else {\n                        // @ts-expect-error cannot type hook function dynamically\n                        hooks[hooksProp] = (...args) => {\n                            let ret = hooksFunc.apply(hooks, args);\n                            if (ret === false) {\n                                ret = prevHook.apply(hooks, args);\n                            }\n                            return ret;\n                        };\n                    }\n                }\n                opts.hooks = hooks;\n            }\n            // ==-- Parse WalkTokens extensions --== //\n            if (pack.walkTokens) {\n                const walkTokens = this.defaults.walkTokens;\n                const packWalktokens = pack.walkTokens;\n                opts.walkTokens = function (token) {\n                    let values = [];\n                    values.push(packWalktokens.call(this, token));\n                    if (walkTokens) {\n                        values = values.concat(walkTokens.call(this, token));\n                    }\n                    return values;\n                };\n            }\n            this.defaults = { ...this.defaults, ...opts };\n        });\n        return this;\n    }\n    setOptions(opt) {\n        this.defaults = { ...this.defaults, ...opt };\n        return this;\n    }\n    lexer(src, options) {\n        return _Lexer.lex(src, options ?? this.defaults);\n    }\n    parser(tokens, options) {\n        return _Parser.parse(tokens, options ?? this.defaults);\n    }\n    parseMarkdown(blockType) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const parse = (src, options) => {\n            const origOpt = { ...options };\n            const opt = { ...this.defaults, ...origOpt };\n            const throwError = this.onError(!!opt.silent, !!opt.async);\n            // throw error if an extension set async to true but parse was called with async: false\n            if (this.defaults.async === true && origOpt.async === false) {\n                return throwError(new Error('marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise.'));\n            }\n            // throw error in case of non string input\n            if (typeof src === 'undefined' || src === null) {\n                return throwError(new Error('marked(): input parameter is undefined or null'));\n            }\n            if (typeof src !== 'string') {\n                return throwError(new Error('marked(): input parameter is of type '\n                    + Object.prototype.toString.call(src) + ', string expected'));\n            }\n            if (opt.hooks) {\n                opt.hooks.options = opt;\n                opt.hooks.block = blockType;\n            }\n            const lexer = opt.hooks ? opt.hooks.provideLexer() : (blockType ? _Lexer.lex : _Lexer.lexInline);\n            const parser = opt.hooks ? opt.hooks.provideParser() : (blockType ? _Parser.parse : _Parser.parseInline);\n            if (opt.async) {\n                return Promise.resolve(opt.hooks ? opt.hooks.preprocess(src) : src)\n                    .then(src => lexer(src, opt))\n                    .then(tokens => opt.hooks ? opt.hooks.processAllTokens(tokens) : tokens)\n                    .then(tokens => opt.walkTokens ? Promise.all(this.walkTokens(tokens, opt.walkTokens)).then(() => tokens) : tokens)\n                    .then(tokens => parser(tokens, opt))\n                    .then(html => opt.hooks ? opt.hooks.postprocess(html) : html)\n                    .catch(throwError);\n            }\n            try {\n                if (opt.hooks) {\n                    src = opt.hooks.preprocess(src);\n                }\n                let tokens = lexer(src, opt);\n                if (opt.hooks) {\n                    tokens = opt.hooks.processAllTokens(tokens);\n                }\n                if (opt.walkTokens) {\n                    this.walkTokens(tokens, opt.walkTokens);\n                }\n                let html = parser(tokens, opt);\n                if (opt.hooks) {\n                    html = opt.hooks.postprocess(html);\n                }\n                return html;\n            }\n            catch (e) {\n                return throwError(e);\n            }\n        };\n        return parse;\n    }\n    onError(silent, async) {\n        return (e) => {\n            e.message += '\\nPlease report this to https://github.com/markedjs/marked.';\n            if (silent) {\n                const msg = '<p>An error occurred:</p><pre>'\n                    + escape(e.message + '', true)\n                    + '</pre>';\n                if (async) {\n                    return Promise.resolve(msg);\n                }\n                return msg;\n            }\n            if (async) {\n                return Promise.reject(e);\n            }\n            throw e;\n        };\n    }\n}\n", "import { _<PERSON>er } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport { _Tokenizer } from './Tokenizer.ts';\nimport { _Renderer } from './Renderer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { _Hooks } from './Hooks.ts';\nimport { Marked } from './Instance.ts';\nimport { _getDefaults, changeDefaults, _defaults, } from './defaults.ts';\nconst markedInstance = new Marked();\nexport function marked(src, opt) {\n    return markedInstance.parse(src, opt);\n}\n/**\n * Sets the default options.\n *\n * @param options Hash of options\n */\nmarked.options =\n    marked.setOptions = function (options) {\n        markedInstance.setOptions(options);\n        marked.defaults = markedInstance.defaults;\n        changeDefaults(marked.defaults);\n        return marked;\n    };\n/**\n * Gets the original marked default options.\n */\nmarked.getDefaults = _getDefaults;\nmarked.defaults = _defaults;\n/**\n * Use Extension\n */\nmarked.use = function (...args) {\n    markedInstance.use(...args);\n    marked.defaults = markedInstance.defaults;\n    changeDefaults(marked.defaults);\n    return marked;\n};\n/**\n * Run callback for every token\n */\nmarked.walkTokens = function (tokens, callback) {\n    return markedInstance.walkTokens(tokens, callback);\n};\n/**\n * Compiles markdown to HTML without enclosing `p` tag.\n *\n * @param src String of markdown source to be compiled\n * @param options Hash of options\n * @return String of compiled HTML\n */\nmarked.parseInline = markedInstance.parseInline;\n/**\n * Expose\n */\nmarked.Parser = _Parser;\nmarked.parser = _Parser.parse;\nmarked.Renderer = _Renderer;\nmarked.TextRenderer = _TextRenderer;\nmarked.Lexer = _Lexer;\nmarked.lexer = _Lexer.lex;\nmarked.Tokenizer = _Tokenizer;\nmarked.Hooks = _Hooks;\nmarked.parse = marked;\nexport const options = marked.options;\nexport const setOptions = marked.setOptions;\nexport const use = marked.use;\nexport const walkTokens = marked.walkTokens;\nexport const parseInline = marked.parseInline;\nexport const parse = marked;\nexport const parser = _Parser.parse;\nexport const lexer = _Lexer.lex;\nexport { _defaults as defaults, _getDefaults as getDefaults } from './defaults.ts';\nexport { _Lexer as Lexer } from './Lexer.ts';\nexport { _Parser as Parser } from './Parser.ts';\nexport { _Tokenizer as Tokenizer } from './Tokenizer.ts';\nexport { _Renderer as Renderer } from './Renderer.ts';\nexport { _TextRenderer as TextRenderer } from './TextRenderer.ts';\nexport { _Hooks as Hooks } from './Hooks.ts';\nexport { Marked } from './Instance.ts';\n", "import type { MarkedToken, Token } from 'marked';\nimport { marked } from 'marked';\nimport { dedent } from 'ts-dedent';\nimport type { MarkdownLine, MarkdownWordType } from './types.js';\nimport type { MermaidConfig } from '../config.type.js';\n\n/**\n * @param markdown - markdown to process\n * @returns processed markdown\n */\nfunction preprocessMarkdown(markdown: string, { markdownAutoWrap }: MermaidConfig): string {\n  //Replace <br/>with \\n\n  const withoutBR = markdown.replace(/<br\\/>/g, '\\n');\n  // Replace multiple newlines with a single newline\n  const withoutMultipleNewlines = withoutBR.replace(/\\n{2,}/g, '\\n');\n  // Remove extra spaces at the beginning of each line\n  const withoutExtraSpaces = dedent(withoutMultipleNewlines);\n  if (markdownAutoWrap === false) {\n    return withoutExtraSpaces.replace(/ /g, '&nbsp;');\n  }\n  return withoutExtraSpaces;\n}\n\n/**\n * @param markdown - markdown to split into lines\n */\nexport function markdownToLines(markdown: string, config: MermaidConfig = {}): MarkdownLine[] {\n  const preprocessedMarkdown = preprocessMarkdown(markdown, config);\n  const nodes = marked.lexer(preprocessedMarkdown);\n  const lines: MarkdownLine[] = [[]];\n  let currentLine = 0;\n\n  function processNode(node: MarkedToken, parentType: MarkdownWordType = 'normal') {\n    if (node.type === 'text') {\n      const textLines = node.text.split('\\n');\n      textLines.forEach((textLine, index) => {\n        if (index !== 0) {\n          currentLine++;\n          lines.push([]);\n        }\n        textLine.split(' ').forEach((word) => {\n          word = word.replace(/&#39;/g, `'`);\n          if (word) {\n            lines[currentLine].push({ content: word, type: parentType });\n          }\n        });\n      });\n    } else if (node.type === 'strong' || node.type === 'em') {\n      node.tokens.forEach((contentNode) => {\n        processNode(contentNode as MarkedToken, node.type);\n      });\n    } else if (node.type === 'html') {\n      lines[currentLine].push({ content: node.text, type: 'normal' });\n    }\n  }\n\n  nodes.forEach((treeNode) => {\n    if (treeNode.type === 'paragraph') {\n      treeNode.tokens?.forEach((contentNode) => {\n        processNode(contentNode as MarkedToken);\n      });\n    } else if (treeNode.type === 'html') {\n      lines[currentLine].push({ content: treeNode.text, type: 'normal' });\n    }\n  });\n\n  return lines;\n}\n\nexport function markdownToHTML(markdown: string, { markdownAutoWrap }: MermaidConfig = {}) {\n  const nodes = marked.lexer(markdown);\n\n  function output(node: Token): string {\n    if (node.type === 'text') {\n      if (markdownAutoWrap === false) {\n        return node.text.replace(/\\n */g, '<br/>').replace(/ /g, '&nbsp;');\n      }\n      return node.text.replace(/\\n */g, '<br/>');\n    } else if (node.type === 'strong') {\n      return `<strong>${node.tokens?.map(output).join('')}</strong>`;\n    } else if (node.type === 'em') {\n      return `<em>${node.tokens?.map(output).join('')}</em>`;\n    } else if (node.type === 'paragraph') {\n      return `<p>${node.tokens?.map(output).join('')}</p>`;\n    } else if (node.type === 'space') {\n      return '';\n    } else if (node.type === 'html') {\n      return `${node.text}`;\n    } else if (node.type === 'escape') {\n      return node.text;\n    }\n    return `Unsupported markdown: ${node.type}`;\n  }\n\n  return nodes.map(output).join('');\n}\n", "import type { CheckFitFunction, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MarkdownWordType } from './types.js';\n\n/**\n * Splits a string into graphemes if available, otherwise characters.\n */\nexport function splitTextToChars(text: string): string[] {\n  if (Intl.Segmenter) {\n    return [...new Intl.Segmenter().segment(text)].map((s) => s.segment);\n  }\n  return [...text];\n}\n\n/**\n * Splits a string into words by using `Intl.Segmenter` if available, or splitting by ' '.\n * `Intl.Segmenter` uses the default locale, which might be different across browsers.\n */\nexport function splitLineToWords(text: string): string[] {\n  if (Intl.Segmenter) {\n    return [...new Intl.Segmenter(undefined, { granularity: 'word' }).segment(text)].map(\n      (s) => s.segment\n    );\n  }\n  // Split by ' ' removes the ' 's from the result.\n  const words = text.split(' ');\n  // Add the ' 's back to the result.\n  const wordsWithSpaces = words.flatMap((s) => [s, ' ']).filter((s) => s);\n  // Remove last space.\n  wordsWithSpaces.pop();\n  return wordsWithSpaces;\n}\n\n/**\n * Splits a word into two parts, the first part fits the width and the remaining part.\n * @param checkFit - Function to check if word fits\n * @param word - Word to split\n * @returns [first part of word that fits, rest of word]\n */\nexport function splitWordToFitWidth(\n  checkFit: CheckFitFunction,\n  word: MarkdownWord\n): [MarkdownWord, MarkdownWord] {\n  const characters = splitTextToChars(word.content);\n  return splitWordToFitWidthRecursion(checkFit, [], characters, word.type);\n}\n\nfunction splitWordToFitWidthRecursion(\n  checkFit: CheckFitFunction,\n  usedChars: string[],\n  remainingChars: string[],\n  type: MarkdownWordType\n): [MarkdownWord, MarkdownWord] {\n  if (remainingChars.length === 0) {\n    return [\n      { content: usedChars.join(''), type },\n      { content: '', type },\n    ];\n  }\n  const [nextChar, ...rest] = remainingChars;\n  const newWord = [...usedChars, nextChar];\n  if (checkFit([{ content: newWord.join(''), type }])) {\n    return splitWordToFitWidthRecursion(checkFit, newWord, rest, type);\n  }\n  if (usedChars.length === 0 && nextChar) {\n    // If the first character does not fit, split it anyway\n    usedChars.push(nextChar);\n    remainingChars.shift();\n  }\n  return [\n    { content: usedChars.join(''), type },\n    { content: remainingChars.join(''), type },\n  ];\n}\n\n/**\n * Splits a line into multiple lines that satisfy the checkFit function.\n * @param line - Line to split\n * @param checkFit - Function to check if line fits\n * @returns Array of lines that fit\n */\nexport function splitLineToFitWidth(\n  line: MarkdownLine,\n  checkFit: CheckFitFunction\n): MarkdownLine[] {\n  if (line.some(({ content }) => content.includes('\\n'))) {\n    throw new Error('splitLineToFitWidth does not support newlines in the line');\n  }\n  return splitLineToFitWidthRecursion(line, checkFit);\n}\n\nfunction splitLineToFitWidthRecursion(\n  words: MarkdownWord[],\n  checkFit: CheckFitFunction,\n  lines: MarkdownLine[] = [],\n  newLine: MarkdownLine = []\n): MarkdownLine[] {\n  // Return if there is nothing left to split\n  if (words.length === 0) {\n    // If there is a new line, add it to the lines\n    if (newLine.length > 0) {\n      lines.push(newLine);\n    }\n    return lines.length > 0 ? lines : [];\n  }\n  let joiner = '';\n  if (words[0].content === ' ') {\n    joiner = ' ';\n    words.shift();\n  }\n  const nextWord: MarkdownWord = words.shift() ?? { content: ' ', type: 'normal' };\n  const lineWithNextWord: MarkdownLine = [...newLine];\n  if (joiner !== '') {\n    lineWithNextWord.push({ content: joiner, type: 'normal' });\n  }\n  lineWithNextWord.push(nextWord);\n\n  if (checkFit(lineWithNextWord)) {\n    // nextWord fits, so we can add it to the new line and continue\n    return splitLineToFitWidthRecursion(words, checkFit, lines, lineWithNextWord);\n  }\n\n  // nextWord doesn't fit, so we need to split it\n  if (newLine.length > 0) {\n    // There was text in newLine, so add it to lines and push nextWord back into words.\n    lines.push(newLine);\n    words.unshift(nextWord);\n  } else if (nextWord.content) {\n    // There was no text in newLine, so we need to split nextWord\n    const [line, rest] = splitWordToFitWidth(checkFit, nextWord);\n    lines.push([line]);\n    if (rest.content) {\n      words.unshift(rest);\n    }\n  }\n  return splitLineToFitWidthRecursion(words, checkFit, lines);\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\n// @ts-nocheck TODO: Fix types\nimport { getConfig } from '../diagram-api/diagramAPI.js';\nimport common, { hasKatex, renderKatex } from '../diagrams/common/common.js';\nimport { select } from 'd3';\nimport type { MermaidConfig } from '../config.type.js';\nimport type { SVGGroup } from '../diagram-api/types.js';\nimport type { D3TSpanElement, D3TextElement } from '../diagrams/common/commonTypes.js';\nimport { log } from '../logger.js';\nimport { markdownToHTML, markdownToLines } from '../rendering-util/handle-markdown-text.js';\nimport { decodeEntities } from '../utils.js';\nimport { splitLineToFitWidth } from './splitText.js';\nimport type { MarkdownLine, MarkdownWord } from './types.js';\n\nfunction applyStyle(dom, styleFn) {\n  if (styleFn) {\n    dom.attr('style', styleFn);\n  }\n}\n\nasync function addHtmlSpan(element, node, width, classes, addBackground = false) {\n  const fo = element.append('foreignObject');\n  // This is not the final width but used in order to make sure the foreign\n  // object in firefox gets a width at all. The final width is fetched from the div\n  fo.attr('width', `${10 * width}px`);\n  fo.attr('height', `${10 * width}px`);\n\n  const div = fo.append('xhtml:div');\n  let label = node.label;\n  if (node.label && hasKatex(node.label)) {\n    label = await renderKatex(node.label.replace(common.lineBreakRegex, '\\n'), getConfig());\n  }\n  const labelClass = node.isNode ? 'nodeLabel' : 'edgeLabel';\n  const span = div.append('span');\n  span.html(label);\n  applyStyle(span, node.labelStyle);\n  span.attr('class', `${labelClass} ${classes}`);\n\n  applyStyle(div, node.labelStyle);\n  div.style('display', 'table-cell');\n  div.style('white-space', 'nowrap');\n  div.style('line-height', '1.5');\n  div.style('max-width', width + 'px');\n  div.style('text-align', 'center');\n  div.attr('xmlns', 'http://www.w3.org/1999/xhtml');\n  if (addBackground) {\n    div.attr('class', 'labelBkg');\n  }\n\n  let bbox = div.node().getBoundingClientRect();\n  if (bbox.width === width) {\n    div.style('display', 'table');\n    div.style('white-space', 'break-spaces');\n    div.style('width', width + 'px');\n    bbox = div.node().getBoundingClientRect();\n  }\n\n  // fo.style('width', bbox.width);\n  // fo.style('height', bbox.height);\n\n  return fo.node();\n}\n\n/**\n * Creates a tspan element with the specified attributes for text positioning.\n *\n * @param textElement - The parent text element to append the tspan element.\n * @param lineIndex - The index of the current line in the structuredText array.\n * @param lineHeight - The line height value for the text.\n * @returns The created tspan element.\n */\nfunction createTspan(textElement: any, lineIndex: number, lineHeight: number) {\n  return textElement\n    .append('tspan')\n    .attr('class', 'text-outer-tspan')\n    .attr('x', 0)\n    .attr('y', lineIndex * lineHeight - 0.1 + 'em')\n    .attr('dy', lineHeight + 'em');\n}\n\nfunction computeWidthOfText(parentNode: any, lineHeight: number, line: MarkdownLine): number {\n  const testElement = parentNode.append('text');\n  const testSpan = createTspan(testElement, 1, lineHeight);\n  updateTextContentAndStyles(testSpan, line);\n  const textLength = testSpan.node().getComputedTextLength();\n  testElement.remove();\n  return textLength;\n}\n\nexport function computeDimensionOfText(\n  parentNode: SVGGroup,\n  lineHeight: number,\n  text: string\n): DOMRect | undefined {\n  const testElement: D3TextElement = parentNode.append('text');\n  const testSpan: D3TSpanElement = createTspan(testElement, 1, lineHeight);\n  updateTextContentAndStyles(testSpan, [{ content: text, type: 'normal' }]);\n  const textDimension: DOMRect | undefined = testSpan.node()?.getBoundingClientRect();\n  if (textDimension) {\n    testElement.remove();\n  }\n  return textDimension;\n}\n\n/**\n * Creates a formatted text element by breaking lines and applying styles based on\n * the given structuredText.\n *\n * @param width - The maximum allowed width of the text.\n * @param g - The parent group element to append the formatted text.\n * @param structuredText - The structured text data to format.\n * @param addBackground - Whether to add a background to the text.\n */\nfunction createFormattedText(\n  width: number,\n  g: any,\n  structuredText: MarkdownWord[][],\n  addBackground = false\n) {\n  const lineHeight = 1.1;\n  const labelGroup = g.append('g');\n  const bkg = labelGroup.insert('rect').attr('class', 'background').attr('style', 'stroke: none');\n  const textElement = labelGroup.append('text').attr('y', '-10.1');\n  let lineIndex = 0;\n  for (const line of structuredText) {\n    /**\n     * Preprocess raw string content of line data\n     * Creating an array of strings pre-split to satisfy width limit\n     */\n    const checkWidth = (line: MarkdownLine) =>\n      computeWidthOfText(labelGroup, lineHeight, line) <= width;\n    const linesUnderWidth = checkWidth(line) ? [line] : splitLineToFitWidth(line, checkWidth);\n    /** Add each prepared line as a tspan to the parent node */\n    for (const preparedLine of linesUnderWidth) {\n      const tspan = createTspan(textElement, lineIndex, lineHeight);\n      updateTextContentAndStyles(tspan, preparedLine);\n      lineIndex++;\n    }\n  }\n  if (addBackground) {\n    const bbox = textElement.node().getBBox();\n    const padding = 2;\n    bkg\n      .attr('x', bbox.x - padding)\n      .attr('y', bbox.y - padding)\n      .attr('width', bbox.width + 2 * padding)\n      .attr('height', bbox.height + 2 * padding);\n\n    return labelGroup.node();\n  } else {\n    return textElement.node();\n  }\n}\n\n/**\n * Updates the text content and styles of the given tspan element based on the\n * provided wrappedLine data.\n *\n * @param tspan - The tspan element to update.\n * @param wrappedLine - The line data to apply to the tspan element.\n */\nfunction updateTextContentAndStyles(tspan: any, wrappedLine: MarkdownWord[]) {\n  tspan.text('');\n\n  wrappedLine.forEach((word, index) => {\n    const innerTspan = tspan\n      .append('tspan')\n      .attr('font-style', word.type === 'em' ? 'italic' : 'normal')\n      .attr('class', 'text-inner-tspan')\n      .attr('font-weight', word.type === 'strong' ? 'bold' : 'normal');\n    if (index === 0) {\n      innerTspan.text(word.content);\n    } else {\n      // TODO: check what joiner to use.\n      innerTspan.text(' ' + word.content);\n    }\n  });\n}\n\n/**\n * Convert fontawesome labels into fontawesome icons by using a regex pattern\n * @param text - The raw string to convert\n * @returns string with fontawesome icons as i tags\n */\nexport function replaceIconSubstring(text: string) {\n  // The letters 'bklrs' stand for possible endings of the fontawesome prefix (e.g. 'fab' for brands, 'fak' for fa-kit) // cspell: disable-line\n  return text.replace(\n    /fa[bklrs]?:fa-[\\w-]+/g, // cspell: disable-line\n    (s) => `<i class='${s.replace(':', ' ')}'></i>`\n  );\n}\n\n// Note when using from flowcharts converting the API isNode means classes should be set accordingly. When using htmlLabels => to sett classes to'nodeLabel' when isNode=true otherwise 'edgeLabel'\n// When not using htmlLabels => to set classes to 'title-row' when isTitle=true otherwise 'title-row'\nexport const createText = async (\n  el,\n  text = '',\n  {\n    style = '',\n    isTitle = false,\n    classes = '',\n    useHtmlLabels = true,\n    isNode = true,\n    width = 200,\n    addSvgBackground = false,\n  } = {},\n  config?: MermaidConfig\n) => {\n  log.debug(\n    'XYZ createText',\n    text,\n    style,\n    isTitle,\n    classes,\n    useHtmlLabels,\n    isNode,\n    'addSvgBackground: ',\n    addSvgBackground\n  );\n  if (useHtmlLabels) {\n    // TODO: addHtmlLabel accepts a labelStyle. Do we possibly have that?\n\n    const htmlText = markdownToHTML(text, config);\n    const decodedReplacedText = replaceIconSubstring(decodeEntities(htmlText));\n\n    //for Katex the text could contain escaped characters, \\\\relax that should be transformed to \\relax\n    const inputForKatex = text.replace(/\\\\\\\\/g, '\\\\');\n\n    const node = {\n      isNode,\n      label: hasKatex(text) ? inputForKatex : decodedReplacedText,\n      labelStyle: style.replace('fill:', 'color:'),\n    };\n    const vertexNode = await addHtmlSpan(el, node, width, classes, addSvgBackground);\n    return vertexNode;\n  } else {\n    //sometimes the user might add br tags with 1 or more spaces in between, so we need to replace them with <br/>\n    const sanitizeBR = text.replace(/<br\\s*\\/?>/g, '<br/>');\n    const structuredText = markdownToLines(sanitizeBR.replace('<br>', '<br/>'), config);\n    const svgLabel = createFormattedText(\n      width,\n      el,\n      structuredText,\n      text ? addSvgBackground : false\n    );\n    if (isNode) {\n      if (/stroke:/.exec(style)) {\n        style = style.replace('stroke:', 'lineColor:');\n      }\n\n      const nodeLabelTextStyle = style\n        .replace(/stroke:[^;]+;?/g, '')\n        .replace(/stroke-width:[^;]+;?/g, '')\n        .replace(/fill:[^;]+;?/g, '')\n        .replace(/color:/g, 'fill:');\n      select(svgLabel).attr('style', nodeLabelTextStyle);\n      // svgLabel.setAttribute('style', style);\n    } else {\n      //On style, assume `stroke`, `stroke-width` are used for edge path, so remove them\n      // remove `fill`\n      //  use  `background` as `fill` for label rect,\n\n      const edgeLabelRectStyle = style\n        .replace(/stroke:[^;]+;?/g, '')\n        .replace(/stroke-width:[^;]+;?/g, '')\n        .replace(/fill:[^;]+;?/g, '')\n        .replace(/background:/g, 'fill:');\n      select(svgLabel)\n        .select('rect')\n        .attr('style', edgeLabelRectStyle.replace(/background:/g, 'fill:'));\n\n      // for text, update fill color with `color`\n      const edgeLabelTextStyle = style\n        .replace(/stroke:[^;]+;?/g, '')\n        .replace(/stroke-width:[^;]+;?/g, '')\n        .replace(/fill:[^;]+;?/g, '')\n        .replace(/color:/g, 'fill:');\n      select(svgLabel).select('text').attr('style', edgeLabelTextStyle);\n    }\n    return svgLabel;\n  }\n};\n"], "mappings": "oKAAM,SAAUA,GACdC,EAAoC,SACpCC,EAAA,CAAA,EAAAC,EAAA,EAAAA,EAAA,UAAA,OAAAA,IAAAD,EAAAC,EAAA,CAAA,EAAA,UAAAA,CAAA,EAEA,IAAIC,EAAU,MAAM,KAAK,OAAOH,GAAU,SAAW,CAACA,CAAK,EAAIA,CAAK,EAGpEG,EAAQA,EAAQ,OAAS,CAAC,EAAIA,EAAQA,EAAQ,OAAS,CAAC,EAAE,QACxD,iBACA,EAAE,EAIJ,IAAMC,EAAgBD,EAAQ,OAAO,SAACE,EAAKC,EAAG,CAC5C,IAAMC,EAAUD,EAAI,MAAM,qBAAqB,EAC/C,OAAIC,EACKF,EAAI,OACTE,EAAQ,IAAI,SAACC,EAAK,CAAA,IAAAC,EAAAC,EAAK,OAAAA,GAAAD,EAAAD,EAAM,MAAM,QAAQ,KAAC,MAAAC,IAAA,OAAA,OAAAA,EAAE,UAAM,MAAAC,IAAA,OAAAA,EAAI,CAAC,CAAA,CAAC,EAGvDL,CACT,EAAa,CAAA,CAAE,EAGf,GAAID,EAAc,OAAQ,CACxB,IAAMO,EAAU,IAAI,OAAO;OAAW,KAAK,IAAG,MAAR,KAAYP,CAAa,EAAA,IAAM,GAAG,EAExED,EAAUA,EAAQ,IAAI,SAACG,EAAG,CAAK,OAAAA,EAAI,QAAQK,EAAS;CAAI,CAAzB,CAA0B,EAI3DR,EAAQ,CAAC,EAAIA,EAAQ,CAAC,EAAE,QAAQ,SAAU,EAAE,EAG5C,IAAIS,EAAST,EAAQ,CAAC,EAEtB,OAAAF,EAAO,QAAQ,SAACY,EAAOC,EAAC,CAEtB,IAAMC,EAAeH,EAAO,MAAM,eAAe,EAC3CI,EAAcD,EAAeA,EAAa,CAAC,EAAI,GACjDE,EAAgBJ,EAEhB,OAAOA,GAAU,UAAYA,EAAM,SAAS;CAAI,IAClDI,EAAgB,OAAOJ,CAAK,EACzB,MAAM;CAAI,EACV,IAAI,SAACP,EAAKQ,EAAC,CACV,OAAOA,IAAM,EAAIR,EAAM,GAAGU,EAAcV,CAC1C,CAAC,EACA,KAAK;CAAI,GAGdM,GAAUK,EAAgBd,EAAQW,EAAI,CAAC,CACzC,CAAC,EAEMF,CACT,CAvDgBM,EAAAnB,GAAA,UCGT,SAASoB,GAAe,CAC3B,MAAO,CACH,MAAO,GACP,OAAQ,GACR,WAAY,KACZ,IAAK,GACL,MAAO,KACP,SAAU,GACV,SAAU,KACV,OAAQ,GACR,UAAW,KACX,WAAY,IACpB,CACA,CAbgBC,EAAAD,EAAA,gBAcN,IAACE,EAAYF,EAAY,EAC5B,SAASG,GAAeC,EAAa,CACxCF,EAAYE,CAChB,CAFgBH,EAAAE,GAAA,kBClBhB,IAAME,EAAW,CAAE,KAAMJ,EAAA,IAAM,KAAN,OAAU,EACnC,SAASK,EAAKC,EAAOC,EAAM,GAAI,CAC3B,IAAIC,EAAS,OAAOF,GAAU,SAAWA,EAAQA,EAAM,OACjDG,EAAM,CACR,QAAST,EAAA,CAACU,EAAMC,IAAQ,CACpB,IAAIC,EAAY,OAAOD,GAAQ,SAAWA,EAAMA,EAAI,OACpD,OAAAC,EAAYA,EAAU,QAAQC,EAAM,MAAO,IAAI,EAC/CL,EAASA,EAAO,QAAQE,EAAME,CAAS,EAChCH,CACnB,EALiB,WAMT,SAAUT,EAAA,IACC,IAAI,OAAOQ,EAAQD,CAAG,EADvB,WAGlB,EACI,OAAOE,CACX,CAdST,EAAAK,EAAA,QAeF,IAAMQ,EAAQ,CACjB,iBAAkB,yBAClB,kBAAmB,cACnB,uBAAwB,gBACxB,eAAgB,OAChB,WAAY,KACZ,kBAAmB,KACnB,gBAAiB,KACjB,aAAc,OACd,kBAAmB,MACnB,cAAe,MACf,oBAAqB,OACrB,UAAW,WACX,gBAAiB,oBACjB,gBAAiB,WACjB,wBAAyB,iCACzB,yBAA0B,mBAC1B,gBAAiB,OACjB,mBAAoB,0BACpB,WAAY,cACZ,gBAAiB,eACjB,QAAS,SACT,aAAc,WACd,eAAgB,OAChB,gBAAiB,aACjB,kBAAmB,YACnB,gBAAiB,YACjB,iBAAkB,aAClB,eAAgB,YAChB,UAAW,QACX,QAAS,UACT,kBAAmB,iCACnB,gBAAiB,mCACjB,kBAAmB,KACnB,gBAAiB,KACjB,kBAAmB,gCACnB,oBAAqB,gBACrB,WAAY,UACZ,cAAe,WACf,mBAAoB,oDACpB,sBAAuB,qDACvB,aAAc,6CACd,MAAO,eACP,cAAe,OACf,SAAU,MACV,UAAW,MACX,UAAW,QACX,eAAgB,WAChB,UAAW,SACX,cAAe,OACf,cAAe,MACf,cAAeb,EAACc,GAAS,IAAI,OAAO,WAAWA,CAAI,8BAA+B,EAAnE,iBACf,gBAAiBd,EAACe,GAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGA,EAAS,CAAC,CAAC,oDAAqD,EAA3G,mBACjB,QAASf,EAACe,GAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGA,EAAS,CAAC,CAAC,oDAAoD,EAA1G,WACT,iBAAkBf,EAACe,GAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGA,EAAS,CAAC,CAAC,iBAAiB,EAAvE,oBAClB,kBAAmBf,EAACe,GAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGA,EAAS,CAAC,CAAC,IAAI,EAA1D,qBACnB,eAAgBf,EAACe,GAAW,IAAI,OAAO,QAAQ,KAAK,IAAI,EAAGA,EAAS,CAAC,CAAC,qBAAsB,GAAG,EAA/E,iBACpB,EAIMC,GAAU,uBACVC,GAAY,wDACZC,GAAS,8GACTC,EAAK,qEACLC,GAAU,uCACVC,EAAS,wBACTC,GAAe,iKACfC,GAAWlB,EAAKiB,EAAY,EAC7B,QAAQ,QAASD,CAAM,EACvB,QAAQ,aAAc,mBAAmB,EACzC,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,cAAe,SAAS,EAChC,QAAQ,WAAY,cAAc,EAClC,QAAQ,QAAS,mBAAmB,EACpC,QAAQ,WAAY,EAAE,EACtB,SAAQ,EACPG,GAAcnB,EAAKiB,EAAY,EAChC,QAAQ,QAASD,CAAM,EACvB,QAAQ,aAAc,mBAAmB,EACzC,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,cAAe,SAAS,EAChC,QAAQ,WAAY,cAAc,EAClC,QAAQ,QAAS,mBAAmB,EACpC,QAAQ,SAAU,mCAAmC,EACrD,SAAQ,EACPI,EAAa,uFACbC,GAAY,UACZC,EAAc,8BACdC,GAAMvB,EAAK,6GAA6G,EACzH,QAAQ,QAASsB,CAAW,EAC5B,QAAQ,QAAS,8DAA8D,EAC/E,SAAQ,EACPE,GAAOxB,EAAK,sCAAsC,EACnD,QAAQ,QAASgB,CAAM,EACvB,SAAQ,EACPS,EAAO,gWAMPC,EAAW,gCACXC,GAAO3B,EAAK,4dASP,GAAG,EACT,QAAQ,UAAW0B,CAAQ,EAC3B,QAAQ,MAAOD,CAAI,EACnB,QAAQ,YAAa,0EAA0E,EAC/F,SAAQ,EACPG,GAAY5B,EAAKoB,CAAU,EAC5B,QAAQ,KAAMN,CAAE,EAChB,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,YAAa,EAAE,EACvB,QAAQ,SAAU,EAAE,EACpB,QAAQ,aAAc,SAAS,EAC/B,QAAQ,SAAU,gDAAgD,EAClE,QAAQ,OAAQ,wBAAwB,EACxC,QAAQ,OAAQ,6DAA6D,EAC7E,QAAQ,MAAOW,CAAI,EACnB,SAAQ,EACPI,GAAa7B,EAAK,yCAAyC,EAC5D,QAAQ,YAAa4B,EAAS,EAC9B,SAAQ,EAIPE,EAAc,CAChB,WAAAD,GACA,KAAMjB,GACN,IAAAW,GACA,OAAAV,GACA,QAAAE,GACA,GAAAD,EACA,KAAAa,GACA,SAAAT,GACA,KAAAM,GACA,QAAAb,GACA,UAAAiB,GACA,MAAO7B,EACP,KAAMsB,EACV,EAIMU,GAAW/B,EAAK,6JAEsE,EACvF,QAAQ,KAAMc,CAAE,EAChB,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,aAAc,SAAS,EAC/B,QAAQ,OAAQ,wBAAyB,EACzC,QAAQ,SAAU,gDAAgD,EAClE,QAAQ,OAAQ,wBAAwB,EACxC,QAAQ,OAAQ,6DAA6D,EAC7E,QAAQ,MAAOW,CAAI,EACnB,SAAQ,EACPO,GAAW,CACb,GAAGF,EACH,SAAUX,GACV,MAAOY,GACP,UAAW/B,EAAKoB,CAAU,EACrB,QAAQ,KAAMN,CAAE,EAChB,QAAQ,UAAW,uBAAuB,EAC1C,QAAQ,YAAa,EAAE,EACvB,QAAQ,QAASiB,EAAQ,EACzB,QAAQ,aAAc,SAAS,EAC/B,QAAQ,SAAU,gDAAgD,EAClE,QAAQ,OAAQ,wBAAwB,EACxC,QAAQ,OAAQ,6DAA6D,EAC7E,QAAQ,MAAON,CAAI,EACnB,SAAQ,CACjB,EAIMQ,GAAgB,CAClB,GAAGH,EACH,KAAM9B,EAAK,wIAEiE,EACvE,QAAQ,UAAW0B,CAAQ,EAC3B,QAAQ,OAAQ,mKAGgB,EAChC,SAAQ,EACb,IAAK,oEACL,QAAS,yBACT,OAAQ3B,EACR,SAAU,mCACV,UAAWC,EAAKoB,CAAU,EACrB,QAAQ,KAAMN,CAAE,EAChB,QAAQ,UAAW;EAAiB,EACpC,QAAQ,WAAYI,EAAQ,EAC5B,QAAQ,SAAU,EAAE,EACpB,QAAQ,aAAc,SAAS,EAC/B,QAAQ,UAAW,EAAE,EACrB,QAAQ,QAAS,EAAE,EACnB,QAAQ,QAAS,EAAE,EACnB,QAAQ,OAAQ,EAAE,EAClB,SAAQ,CACjB,EAIMgB,GAAS,8CACTC,GAAa,sCACbC,GAAK,wBACLC,GAAa,8EAEbC,EAAe,gBACfC,EAAsB,kBACtBC,GAAyB,mBACzBC,GAAczC,EAAK,wBAAyB,GAAG,EAChD,QAAQ,cAAeuC,CAAmB,EAAE,SAAQ,EAEnDG,GAA0B,qBAC1BC,GAAiC,uBACjCC,GAAoC,yBAEpCC,GAAY,gFACZC,GAAqB,gEACrBC,GAAiB/C,EAAK8C,GAAoB,GAAG,EAC9C,QAAQ,SAAUR,CAAY,EAC9B,SAAQ,EACPU,GAAoBhD,EAAK8C,GAAoB,GAAG,EACjD,QAAQ,SAAUJ,EAAuB,EACzC,SAAQ,EACPO,GAAwB,wQAQxBC,GAAoBlD,EAAKiD,GAAuB,IAAI,EACrD,QAAQ,iBAAkBT,EAAsB,EAChD,QAAQ,cAAeD,CAAmB,EAC1C,QAAQ,SAAUD,CAAY,EAC9B,SAAQ,EACPa,GAAuBnD,EAAKiD,GAAuB,IAAI,EACxD,QAAQ,iBAAkBL,EAAiC,EAC3D,QAAQ,cAAeD,EAA8B,EACrD,QAAQ,SAAUD,EAAuB,EACzC,SAAQ,EAEPU,GAAoBpD,EAAK,mNAMQ,IAAI,EACtC,QAAQ,iBAAkBwC,EAAsB,EAChD,QAAQ,cAAeD,CAAmB,EAC1C,QAAQ,SAAUD,CAAY,EAC9B,SAAQ,EACPe,GAAiBrD,EAAK,YAAa,IAAI,EACxC,QAAQ,SAAUsC,CAAY,EAC9B,SAAQ,EACPgB,GAAWtD,EAAK,qCAAqC,EACtD,QAAQ,SAAU,8BAA8B,EAChD,QAAQ,QAAS,8IAA8I,EAC/J,SAAQ,EACPuD,GAAiBvD,EAAK0B,CAAQ,EAAE,QAAQ,YAAa,KAAK,EAAE,SAAQ,EACpE8B,GAAMxD,EAAK,0JAKuB,EACnC,QAAQ,UAAWuD,EAAc,EACjC,QAAQ,YAAa,6EAA6E,EAClG,SAAQ,EACPE,EAAe,sDACfC,GAAO1D,EAAK,+CAA+C,EAC5D,QAAQ,QAASyD,CAAY,EAC7B,QAAQ,OAAQ,sCAAsC,EACtD,QAAQ,QAAS,6DAA6D,EAC9E,SAAQ,EACPE,GAAU3D,EAAK,yBAAyB,EACzC,QAAQ,QAASyD,CAAY,EAC7B,QAAQ,MAAOnC,CAAW,EAC1B,SAAQ,EACPsC,GAAS5D,EAAK,uBAAuB,EACtC,QAAQ,MAAOsB,CAAW,EAC1B,SAAQ,EACPuC,GAAgB7D,EAAK,wBAAyB,GAAG,EAClD,QAAQ,UAAW2D,EAAO,EAC1B,QAAQ,SAAUC,EAAM,EACxB,SAAQ,EAIPE,EAAe,CACjB,WAAY/D,EACZ,eAAAsD,GACA,SAAAC,GACA,UAAAT,GACA,GAAAT,GACA,KAAMD,GACN,IAAKpC,EACL,eAAAgD,GACA,kBAAAG,GACA,kBAAAE,GACJ,OAAIlB,GACA,KAAAwB,GACA,OAAAE,GACA,YAAAnB,GACA,QAAAkB,GACA,cAAAE,GACA,IAAAL,GACA,KAAMnB,GACN,IAAKtC,CACT,EAIMgE,GAAiB,CACnB,GAAGD,EACH,KAAM9D,EAAK,yBAAyB,EAC/B,QAAQ,QAASyD,CAAY,EAC7B,SAAQ,EACb,QAASzD,EAAK,+BAA+B,EACxC,QAAQ,QAASyD,CAAY,EAC7B,SAAQ,CACjB,EAIMO,EAAY,CACd,GAAGF,EACH,kBAAmBX,GACnB,eAAgBH,GAChB,IAAKhD,EAAK,mEAAoE,GAAG,EAC5E,QAAQ,QAAS,2EAA2E,EAC5F,SAAQ,EACb,WAAY,6EACZ,IAAK,gEACL,KAAM,4NACV,EAIMiE,GAAe,CACjB,GAAGD,EACH,GAAIhE,EAAKoC,EAAE,EAAE,QAAQ,OAAQ,GAAG,EAAE,SAAQ,EAC1C,KAAMpC,EAAKgE,EAAU,IAAI,EACpB,QAAQ,OAAQ,eAAe,EAC/B,QAAQ,UAAW,GAAG,EACtB,SAAQ,CACjB,EAIaE,EAAQ,CACjB,OAAQpC,EACR,IAAKE,GACL,SAAUC,EACd,EACakC,EAAS,CAClB,OAAQL,EACR,IAAKE,EACL,OAAQC,GACR,SAAUF,EACd,EClYMK,GAAqB,CACvB,IAAK,QACL,IAAK,OACL,IAAK,OACL,IAAK,SACL,IAAK,OACT,EACMC,GAAuB1E,EAAC2E,GAAOF,GAAmBE,CAAE,EAA7B,wBACtB,SAASpC,EAAOP,EAAM4C,EAAQ,CACjC,GAAIA,GACA,GAAI/D,EAAM,WAAW,KAAKmB,CAAI,EAC1B,OAAOA,EAAK,QAAQnB,EAAM,cAAe6D,EAAoB,UAI7D7D,EAAM,mBAAmB,KAAKmB,CAAI,EAClC,OAAOA,EAAK,QAAQnB,EAAM,sBAAuB6D,EAAoB,EAG7E,OAAO1C,CACX,CAZgBhC,EAAAuC,EAAA,UA2BT,SAASsC,GAASC,EAAM,CAC3B,GAAI,CACAA,EAAO,UAAUA,CAAI,EAAE,QAAQjE,EAAM,cAAe,GAAG,CAC/D,MACU,CACF,OAAO,IACf,CACI,OAAOiE,CACX,CARgB9E,EAAA6E,GAAA,YAST,SAASE,GAAWC,EAAUC,EAAO,CAGxC,IAAMC,EAAMF,EAAS,QAAQnE,EAAM,SAAU,CAACsE,EAAOC,EAAQC,IAAQ,CACjE,IAAIC,EAAU,GACVC,EAAOH,EACX,KAAO,EAAEG,GAAQ,GAAKF,EAAIE,CAAI,IAAM,MAChCD,EAAU,CAACA,EACf,OAAIA,EAGO,IAIA,IAEnB,CAAK,EAAGE,EAAQN,EAAI,MAAMrE,EAAM,SAAS,EACjC4E,EAAI,EAQR,GANKD,EAAM,CAAC,EAAE,KAAI,GACdA,EAAM,MAAK,EAEXA,EAAM,OAAS,GAAK,CAACA,EAAM,GAAG,EAAE,GAAG,KAAI,GACvCA,EAAM,IAAG,EAETP,EACA,GAAIO,EAAM,OAASP,EACfO,EAAM,OAAOP,CAAK,MAGlB,MAAOO,EAAM,OAASP,GAClBO,EAAM,KAAK,EAAE,EAGzB,KAAOC,EAAID,EAAM,OAAQC,IAErBD,EAAMC,CAAC,EAAID,EAAMC,CAAC,EAAE,KAAI,EAAG,QAAQ5E,EAAM,UAAW,GAAG,EAE3D,OAAO2E,CACX,CAxCgBxF,EAAA+E,GAAA,cAiDT,SAASW,EAAML,EAAKM,EAAGC,EAAQ,CAClC,IAAMC,EAAIR,EAAI,OACd,GAAIQ,IAAM,EACN,MAAO,GAGX,IAAIC,EAAU,EAEd,KAAOA,EAAUD,GACIR,EAAI,OAAOQ,EAAIC,EAAU,CAAC,IAC1BH,GACbG,IASR,OAAOT,EAAI,MAAM,EAAGQ,EAAIC,CAAO,CACnC,CArBgB9F,EAAA0F,EAAA,SAsBT,SAASK,GAAmBV,EAAKW,EAAG,CACvC,GAAIX,EAAI,QAAQW,EAAE,CAAC,CAAC,IAAM,GACtB,MAAO,GAEX,IAAIC,EAAQ,EACZ,QAASR,EAAI,EAAGA,EAAIJ,EAAI,OAAQI,IAC5B,GAAIJ,EAAII,CAAC,IAAM,KACXA,YAEKJ,EAAII,CAAC,IAAMO,EAAE,CAAC,EACnBC,YAEKZ,EAAII,CAAC,IAAMO,EAAE,CAAC,IACnBC,IACIA,EAAQ,GACR,OAAOR,EAInB,MAAO,EACX,CApBgBzF,EAAA+F,GAAA,sBCrHhB,SAASG,GAAWC,EAAKpC,EAAMqC,EAAKC,EAAOC,EAAO,CAC9C,IAAMxB,EAAOf,EAAK,KACZwC,EAAQxC,EAAK,OAAS,KACtByC,EAAOL,EAAI,CAAC,EAAE,QAAQG,EAAM,MAAM,kBAAmB,IAAI,EAC/D,GAAIH,EAAI,CAAC,EAAE,OAAO,CAAC,IAAM,IAAK,CAC1BE,EAAM,MAAM,OAAS,GACrB,IAAMI,EAAQ,CACV,KAAM,OACN,IAAAL,EACA,KAAAtB,EACA,MAAAyB,EACA,KAAAC,EACA,OAAQH,EAAM,aAAaG,CAAI,CAC3C,EACQ,OAAAH,EAAM,MAAM,OAAS,GACdI,CACf,CACI,MAAO,CACH,KAAM,QACN,IAAAL,EACA,KAAAtB,EACA,MAAAyB,EACA,KAAAC,CACR,CACA,CAxBSxG,EAAAkG,GAAA,cAyBT,SAASQ,GAAuBN,EAAKI,EAAMF,EAAO,CAC9C,IAAMK,EAAoBP,EAAI,MAAME,EAAM,MAAM,sBAAsB,EACtE,GAAIK,IAAsB,KACtB,OAAOH,EAEX,IAAMI,EAAeD,EAAkB,CAAC,EACxC,OAAOH,EACF,MAAM;CAAI,EACV,IAAIK,GAAQ,CACb,IAAMC,EAAoBD,EAAK,MAAMP,EAAM,MAAM,cAAc,EAC/D,GAAIQ,IAAsB,KACtB,OAAOD,EAEX,GAAM,CAACE,CAAY,EAAID,EACvB,OAAIC,EAAa,QAAUH,EAAa,OAC7BC,EAAK,MAAMD,EAAa,MAAM,EAElCC,CACf,CAAK,EACI,KAAK;CAAI,CAClB,CApBS7G,EAAA0G,GAAA,0BAwBF,IAAMM,EAAN,KAAiB,OAAA,CAAAhH,EAAA,mBACpB,QACA,MACA,MACA,YAAYiH,EAAS,CACjB,KAAK,QAAUA,GAAWhH,CAClC,CACI,MAAMiH,EAAK,CACP,IAAMf,EAAM,KAAK,MAAM,MAAM,QAAQ,KAAKe,CAAG,EAC7C,GAAIf,GAAOA,EAAI,CAAC,EAAE,OAAS,EACvB,MAAO,CACH,KAAM,QACN,IAAKA,EAAI,CAAC,CAC1B,CAEA,CACI,KAAKe,EAAK,CACN,IAAMf,EAAM,KAAK,MAAM,MAAM,KAAK,KAAKe,CAAG,EAC1C,GAAIf,EAAK,CACL,IAAMK,EAAOL,EAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,iBAAkB,EAAE,EACjE,MAAO,CACH,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,eAAgB,WAChB,KAAO,KAAK,QAAQ,SAEdK,EADAd,EAAMc,EAAM;CAAI,CAEtC,CACA,CACA,CACI,OAAOU,EAAK,CACR,IAAMf,EAAM,KAAK,MAAM,MAAM,OAAO,KAAKe,CAAG,EAC5C,GAAIf,EAAK,CACL,IAAMC,EAAMD,EAAI,CAAC,EACXK,EAAOE,GAAuBN,EAAKD,EAAI,CAAC,GAAK,GAAI,KAAK,KAAK,EACjE,MAAO,CACH,KAAM,OACN,IAAAC,EACA,KAAMD,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,KAAI,EAAG,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,EAAIA,EAAI,CAAC,EACpF,KAAAK,CAChB,CACA,CACA,CACI,QAAQU,EAAK,CACT,IAAMf,EAAM,KAAK,MAAM,MAAM,QAAQ,KAAKe,CAAG,EAC7C,GAAIf,EAAK,CACL,IAAIK,EAAOL,EAAI,CAAC,EAAE,KAAI,EAEtB,GAAI,KAAK,MAAM,MAAM,WAAW,KAAKK,CAAI,EAAG,CACxC,IAAMW,EAAUzB,EAAMc,EAAM,GAAG,GAC3B,KAAK,QAAQ,UAGR,CAACW,GAAW,KAAK,MAAM,MAAM,gBAAgB,KAAKA,CAAO,KAE9DX,EAAOW,EAAQ,KAAI,EAEvC,CACY,MAAO,CACH,KAAM,UACN,IAAKhB,EAAI,CAAC,EACV,MAAOA,EAAI,CAAC,EAAE,OACd,KAAAK,EACA,OAAQ,KAAK,MAAM,OAAOA,CAAI,CAC9C,CACA,CACA,CACI,GAAGU,EAAK,CACJ,IAAMf,EAAM,KAAK,MAAM,MAAM,GAAG,KAAKe,CAAG,EACxC,GAAIf,EACA,MAAO,CACH,KAAM,KACN,IAAKT,EAAMS,EAAI,CAAC,EAAG;CAAI,CACvC,CAEA,CACI,WAAWe,EAAK,CACZ,IAAMf,EAAM,KAAK,MAAM,MAAM,WAAW,KAAKe,CAAG,EAChD,GAAIf,EAAK,CACL,IAAIiB,EAAQ1B,EAAMS,EAAI,CAAC,EAAG;CAAI,EAAE,MAAM;CAAI,EACtCC,EAAM,GACNI,EAAO,GACLa,EAAS,CAAA,EACf,KAAOD,EAAM,OAAS,GAAG,CACrB,IAAIE,EAAe,GACbC,EAAe,CAAA,EACjB9B,EACJ,IAAKA,EAAI,EAAGA,EAAI2B,EAAM,OAAQ3B,IAE1B,GAAI,KAAK,MAAM,MAAM,gBAAgB,KAAK2B,EAAM3B,CAAC,CAAC,EAC9C8B,EAAa,KAAKH,EAAM3B,CAAC,CAAC,EAC1B6B,EAAe,WAEV,CAACA,EACNC,EAAa,KAAKH,EAAM3B,CAAC,CAAC,MAG1B,OAGR2B,EAAQA,EAAM,MAAM3B,CAAC,EACrB,IAAM+B,EAAaD,EAAa,KAAK;CAAI,EACnCE,EAAcD,EAEf,QAAQ,KAAK,MAAM,MAAM,wBAAyB;OAAU,EAC5D,QAAQ,KAAK,MAAM,MAAM,yBAA0B,EAAE,EAC1DpB,EAAMA,EAAM,GAAGA,CAAG;EAAKoB,CAAU,GAAKA,EACtChB,EAAOA,EAAO,GAAGA,CAAI;EAAKiB,CAAW,GAAKA,EAG1C,IAAMC,EAAM,KAAK,MAAM,MAAM,IAK7B,GAJA,KAAK,MAAM,MAAM,IAAM,GACvB,KAAK,MAAM,YAAYD,EAAaJ,EAAQ,EAAI,EAChD,KAAK,MAAM,MAAM,IAAMK,EAEnBN,EAAM,SAAW,EACjB,MAEJ,IAAMO,EAAYN,EAAO,GAAG,EAAE,EAC9B,GAAIM,GAAW,OAAS,OAEpB,MAEC,GAAIA,GAAW,OAAS,aAAc,CAEvC,IAAMC,EAAWD,EACXE,EAAUD,EAAS,IAAM;EAAOR,EAAM,KAAK;CAAI,EAC/CU,EAAW,KAAK,WAAWD,CAAO,EACxCR,EAAOA,EAAO,OAAS,CAAC,EAAIS,EAC5B1B,EAAMA,EAAI,UAAU,EAAGA,EAAI,OAASwB,EAAS,IAAI,MAAM,EAAIE,EAAS,IACpEtB,EAAOA,EAAK,UAAU,EAAGA,EAAK,OAASoB,EAAS,KAAK,MAAM,EAAIE,EAAS,KACxE,KACpB,SACyBH,GAAW,OAAS,OAAQ,CAEjC,IAAMC,EAAWD,EACXE,EAAUD,EAAS,IAAM;EAAOR,EAAM,KAAK;CAAI,EAC/CU,EAAW,KAAK,KAAKD,CAAO,EAClCR,EAAOA,EAAO,OAAS,CAAC,EAAIS,EAC5B1B,EAAMA,EAAI,UAAU,EAAGA,EAAI,OAASuB,EAAU,IAAI,MAAM,EAAIG,EAAS,IACrEtB,EAAOA,EAAK,UAAU,EAAGA,EAAK,OAASoB,EAAS,IAAI,MAAM,EAAIE,EAAS,IACvEV,EAAQS,EAAQ,UAAUR,EAAO,GAAG,EAAE,EAAE,IAAI,MAAM,EAAE,MAAM;CAAI,EAC9D,QACpB,CACA,CACY,MAAO,CACH,KAAM,aACN,IAAAjB,EACA,OAAAiB,EACA,KAAAb,CAChB,CACA,CACA,CACI,KAAKU,EAAK,CACN,IAAIf,EAAM,KAAK,MAAM,MAAM,KAAK,KAAKe,CAAG,EACxC,GAAIf,EAAK,CACL,IAAIrF,EAAOqF,EAAI,CAAC,EAAE,KAAI,EAChB4B,EAAYjH,EAAK,OAAS,EAC1Be,EAAO,CACT,KAAM,OACN,IAAK,GACL,QAASkG,EACT,MAAOA,EAAY,CAACjH,EAAK,MAAM,EAAG,EAAE,EAAI,GACxC,MAAO,GACP,MAAO,CAAA,CACvB,EACYA,EAAOiH,EAAY,aAAajH,EAAK,MAAM,EAAE,CAAC,GAAK,KAAKA,CAAI,GACxD,KAAK,QAAQ,WACbA,EAAOiH,EAAYjH,EAAO,SAG9B,IAAMkH,EAAY,KAAK,MAAM,MAAM,cAAclH,CAAI,EACjDmH,EAAoB,GAExB,KAAOf,GAAK,CACR,IAAIgB,EAAW,GACX9B,EAAM,GACN+B,EAAe,GAInB,GAHI,EAAEhC,EAAM6B,EAAU,KAAKd,CAAG,IAG1B,KAAK,MAAM,MAAM,GAAG,KAAKA,CAAG,EAC5B,MAEJd,EAAMD,EAAI,CAAC,EACXe,EAAMA,EAAI,UAAUd,EAAI,MAAM,EAC9B,IAAIgC,EAAOjC,EAAI,CAAC,EAAE,MAAM;EAAM,CAAC,EAAE,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,gBAAkBkC,GAAM,IAAI,OAAO,EAAIA,EAAE,MAAM,CAAC,EACzGC,EAAWpB,EAAI,MAAM;EAAM,CAAC,EAAE,CAAC,EAC/BqB,EAAY,CAACH,EAAK,KAAI,EACtBrH,EAAS,EAmBb,GAlBI,KAAK,QAAQ,UACbA,EAAS,EACToH,EAAeC,EAAK,UAAS,GAExBG,EACLxH,EAASoF,EAAI,CAAC,EAAE,OAAS,GAGzBpF,EAASoF,EAAI,CAAC,EAAE,OAAO,KAAK,MAAM,MAAM,YAAY,EACpDpF,EAASA,EAAS,EAAI,EAAIA,EAC1BoH,EAAeC,EAAK,MAAMrH,CAAM,EAChCA,GAAUoF,EAAI,CAAC,EAAE,QAEjBoC,GAAa,KAAK,MAAM,MAAM,UAAU,KAAKD,CAAQ,IACrDlC,GAAOkC,EAAW;EAClBpB,EAAMA,EAAI,UAAUoB,EAAS,OAAS,CAAC,EACvCJ,EAAW,IAEX,CAACA,EAAU,CACX,IAAMM,EAAkB,KAAK,MAAM,MAAM,gBAAgBzH,CAAM,EACzD0H,GAAU,KAAK,MAAM,MAAM,QAAQ1H,CAAM,EACzC2H,GAAmB,KAAK,MAAM,MAAM,iBAAiB3H,CAAM,EAC3D4H,GAAoB,KAAK,MAAM,MAAM,kBAAkB5H,CAAM,EAC7D6H,GAAiB,KAAK,MAAM,MAAM,eAAe7H,CAAM,EAE7D,KAAOmG,GAAK,CACR,IAAM2B,EAAU3B,EAAI,MAAM;EAAM,CAAC,EAAE,CAAC,EAChC4B,EA2BJ,GA1BAR,EAAWO,EAEP,KAAK,QAAQ,UACbP,EAAWA,EAAS,QAAQ,KAAK,MAAM,MAAM,mBAAoB,IAAI,EACrEQ,EAAsBR,GAGtBQ,EAAsBR,EAAS,QAAQ,KAAK,MAAM,MAAM,cAAe,MAAM,EAG7EI,GAAiB,KAAKJ,CAAQ,GAI9BK,GAAkB,KAAKL,CAAQ,GAI/BM,GAAe,KAAKN,CAAQ,GAI5BE,EAAgB,KAAKF,CAAQ,GAI7BG,GAAQ,KAAKH,CAAQ,EACrB,MAEJ,GAAIQ,EAAoB,OAAO,KAAK,MAAM,MAAM,YAAY,GAAK/H,GAAU,CAACuH,EAAS,KAAI,EACrFH,GAAgB;EAAOW,EAAoB,MAAM/H,CAAM,MAEtD,CAeD,GAbIwH,GAIAH,EAAK,QAAQ,KAAK,MAAM,MAAM,cAAe,MAAM,EAAE,OAAO,KAAK,MAAM,MAAM,YAAY,GAAK,GAG9FM,GAAiB,KAAKN,CAAI,GAG1BO,GAAkB,KAAKP,CAAI,GAG3BK,GAAQ,KAAKL,CAAI,EACjB,MAEJD,GAAgB;EAAOG,CACnD,CAC4B,CAACC,GAAa,CAACD,EAAS,KAAI,IAC5BC,EAAY,IAEhBnC,GAAOyC,EAAU;EACjB3B,EAAMA,EAAI,UAAU2B,EAAQ,OAAS,CAAC,EACtCT,EAAOU,EAAoB,MAAM/H,CAAM,CAC/D,CACA,CACqBc,EAAK,QAEFoG,EACApG,EAAK,MAAQ,GAER,KAAK,MAAM,MAAM,gBAAgB,KAAKuE,CAAG,IAC9C6B,EAAoB,KAG5B,IAAIc,EAAS,KACTC,GAEA,KAAK,QAAQ,MACbD,EAAS,KAAK,MAAM,MAAM,WAAW,KAAKZ,CAAY,EAClDY,IACAC,GAAYD,EAAO,CAAC,IAAM,OAC1BZ,EAAeA,EAAa,QAAQ,KAAK,MAAM,MAAM,gBAAiB,EAAE,IAGhFtG,EAAK,MAAM,KAAK,CACZ,KAAM,YACN,IAAAuE,EACA,KAAM,CAAC,CAAC2C,EACR,QAASC,GACT,MAAO,GACP,KAAMb,EACN,OAAQ,CAAA,CAC5B,CAAiB,EACDtG,EAAK,KAAOuE,CAC5B,CAEY,IAAM6C,EAAWpH,EAAK,MAAM,GAAG,EAAE,EACjC,GAAIoH,EACAA,EAAS,IAAMA,EAAS,IAAI,QAAO,EACnCA,EAAS,KAAOA,EAAS,KAAK,QAAO,MAIrC,QAEJpH,EAAK,IAAMA,EAAK,IAAI,QAAO,EAE3B,QAAS4D,EAAI,EAAGA,EAAI5D,EAAK,MAAM,OAAQ4D,IAGnC,GAFA,KAAK,MAAM,MAAM,IAAM,GACvB5D,EAAK,MAAM4D,CAAC,EAAE,OAAS,KAAK,MAAM,YAAY5D,EAAK,MAAM4D,CAAC,EAAE,KAAM,CAAA,CAAE,EAChE,CAAC5D,EAAK,MAAO,CAEb,IAAMqH,EAAUrH,EAAK,MAAM4D,CAAC,EAAE,OAAO,OAAO4C,GAAKA,EAAE,OAAS,OAAO,EAC7Dc,EAAwBD,EAAQ,OAAS,GAAKA,EAAQ,KAAKb,GAAK,KAAK,MAAM,MAAM,QAAQ,KAAKA,EAAE,GAAG,CAAC,EAC1GxG,EAAK,MAAQsH,CACjC,CAGY,GAAItH,EAAK,MACL,QAAS4D,EAAI,EAAGA,EAAI5D,EAAK,MAAM,OAAQ4D,IACnC5D,EAAK,MAAM4D,CAAC,EAAE,MAAQ,GAG9B,OAAO5D,CACnB,CACA,CACI,KAAKqF,EAAK,CACN,IAAMf,EAAM,KAAK,MAAM,MAAM,KAAK,KAAKe,CAAG,EAC1C,GAAIf,EAQA,MAPc,CACV,KAAM,OACN,MAAO,GACP,IAAKA,EAAI,CAAC,EACV,IAAKA,EAAI,CAAC,IAAM,OAASA,EAAI,CAAC,IAAM,UAAYA,EAAI,CAAC,IAAM,QAC3D,KAAMA,EAAI,CAAC,CAC3B,CAGA,CACI,IAAIe,EAAK,CACL,IAAMf,EAAM,KAAK,MAAM,MAAM,IAAI,KAAKe,CAAG,EACzC,GAAIf,EAAK,CACL,IAAMtC,EAAMsC,EAAI,CAAC,EAAE,YAAW,EAAG,QAAQ,KAAK,MAAM,MAAM,oBAAqB,GAAG,EAC5ErB,EAAOqB,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,aAAc,IAAI,EAAE,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,EAAI,GACtHI,EAAQJ,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,UAAU,EAAGA,EAAI,CAAC,EAAE,OAAS,CAAC,EAAE,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,EAAIA,EAAI,CAAC,EACrH,MAAO,CACH,KAAM,MACN,IAAAtC,EACA,IAAKsC,EAAI,CAAC,EACV,KAAArB,EACA,MAAAyB,CAChB,CACA,CACA,CACI,MAAMW,EAAK,CACP,IAAMf,EAAM,KAAK,MAAM,MAAM,MAAM,KAAKe,CAAG,EAI3C,GAHI,CAACf,GAGD,CAAC,KAAK,MAAM,MAAM,eAAe,KAAKA,EAAI,CAAC,CAAC,EAE5C,OAEJ,IAAMiD,EAAUrE,GAAWoB,EAAI,CAAC,CAAC,EAC3BkD,EAASlD,EAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,gBAAiB,EAAE,EAAE,MAAM,GAAG,EACvEmD,EAAOnD,EAAI,CAAC,GAAG,KAAI,EAAKA,EAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,kBAAmB,EAAE,EAAE,MAAM;CAAI,EAAI,CAAA,EAC7FoD,EAAO,CACT,KAAM,QACN,IAAKpD,EAAI,CAAC,EACV,OAAQ,CAAA,EACR,MAAO,CAAA,EACP,KAAM,CAAA,CAClB,EACQ,GAAIiD,EAAQ,SAAWC,EAAO,OAI9B,SAAWG,KAASH,EACZ,KAAK,MAAM,MAAM,gBAAgB,KAAKG,CAAK,EAC3CD,EAAK,MAAM,KAAK,OAAO,EAElB,KAAK,MAAM,MAAM,iBAAiB,KAAKC,CAAK,EACjDD,EAAK,MAAM,KAAK,QAAQ,EAEnB,KAAK,MAAM,MAAM,eAAe,KAAKC,CAAK,EAC/CD,EAAK,MAAM,KAAK,MAAM,EAGtBA,EAAK,MAAM,KAAK,IAAI,EAG5B,QAAS9D,EAAI,EAAGA,EAAI2D,EAAQ,OAAQ3D,IAChC8D,EAAK,OAAO,KAAK,CACb,KAAMH,EAAQ3D,CAAC,EACf,OAAQ,KAAK,MAAM,OAAO2D,EAAQ3D,CAAC,CAAC,EACpC,OAAQ,GACR,MAAO8D,EAAK,MAAM9D,CAAC,CACnC,CAAa,EAEL,QAAWP,KAAOoE,EACdC,EAAK,KAAK,KAAKxE,GAAWG,EAAKqE,EAAK,OAAO,MAAM,EAAE,IAAI,CAACE,EAAMhE,KACnD,CACH,KAAMgE,EACN,OAAQ,KAAK,MAAM,OAAOA,CAAI,EAC9B,OAAQ,GACR,MAAOF,EAAK,MAAM9D,CAAC,CACvC,EACa,CAAC,EAEN,OAAO8D,EACf,CACI,SAASrC,EAAK,CACV,IAAMf,EAAM,KAAK,MAAM,MAAM,SAAS,KAAKe,CAAG,EAC9C,GAAIf,EACA,MAAO,CACH,KAAM,UACN,IAAKA,EAAI,CAAC,EACV,MAAOA,EAAI,CAAC,EAAE,OAAO,CAAC,IAAM,IAAM,EAAI,EACtC,KAAMA,EAAI,CAAC,EACX,OAAQ,KAAK,MAAM,OAAOA,EAAI,CAAC,CAAC,CAChD,CAEA,CACI,UAAUe,EAAK,CACX,IAAMf,EAAM,KAAK,MAAM,MAAM,UAAU,KAAKe,CAAG,EAC/C,GAAIf,EAAK,CACL,IAAMK,EAAOL,EAAI,CAAC,EAAE,OAAOA,EAAI,CAAC,EAAE,OAAS,CAAC,IAAM;EAC5CA,EAAI,CAAC,EAAE,MAAM,EAAG,EAAE,EAClBA,EAAI,CAAC,EACX,MAAO,CACH,KAAM,YACN,IAAKA,EAAI,CAAC,EACV,KAAAK,EACA,OAAQ,KAAK,MAAM,OAAOA,CAAI,CAC9C,CACA,CACA,CACI,KAAKU,EAAK,CACN,IAAMf,EAAM,KAAK,MAAM,MAAM,KAAK,KAAKe,CAAG,EAC1C,GAAIf,EACA,MAAO,CACH,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,KAAMA,EAAI,CAAC,EACX,OAAQ,KAAK,MAAM,OAAOA,EAAI,CAAC,CAAC,CAChD,CAEA,CACI,OAAOe,EAAK,CACR,IAAMf,EAAM,KAAK,MAAM,OAAO,OAAO,KAAKe,CAAG,EAC7C,GAAIf,EACA,MAAO,CACH,KAAM,SACN,IAAKA,EAAI,CAAC,EACV,KAAMA,EAAI,CAAC,CAC3B,CAEA,CACI,IAAIe,EAAK,CACL,IAAMf,EAAM,KAAK,MAAM,OAAO,IAAI,KAAKe,CAAG,EAC1C,GAAIf,EACA,MAAI,CAAC,KAAK,MAAM,MAAM,QAAU,KAAK,MAAM,MAAM,UAAU,KAAKA,EAAI,CAAC,CAAC,EAClE,KAAK,MAAM,MAAM,OAAS,GAErB,KAAK,MAAM,MAAM,QAAU,KAAK,MAAM,MAAM,QAAQ,KAAKA,EAAI,CAAC,CAAC,IACpE,KAAK,MAAM,MAAM,OAAS,IAE1B,CAAC,KAAK,MAAM,MAAM,YAAc,KAAK,MAAM,MAAM,kBAAkB,KAAKA,EAAI,CAAC,CAAC,EAC9E,KAAK,MAAM,MAAM,WAAa,GAEzB,KAAK,MAAM,MAAM,YAAc,KAAK,MAAM,MAAM,gBAAgB,KAAKA,EAAI,CAAC,CAAC,IAChF,KAAK,MAAM,MAAM,WAAa,IAE3B,CACH,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,OAAQ,KAAK,MAAM,MAAM,OACzB,WAAY,KAAK,MAAM,MAAM,WAC7B,MAAO,GACP,KAAMA,EAAI,CAAC,CAC3B,CAEA,CACI,KAAKe,EAAK,CACN,IAAMf,EAAM,KAAK,MAAM,OAAO,KAAK,KAAKe,CAAG,EAC3C,GAAIf,EAAK,CACL,IAAMuD,EAAavD,EAAI,CAAC,EAAE,KAAI,EAC9B,GAAI,CAAC,KAAK,QAAQ,UAAY,KAAK,MAAM,MAAM,kBAAkB,KAAKuD,CAAU,EAAG,CAE/E,GAAI,CAAE,KAAK,MAAM,MAAM,gBAAgB,KAAKA,CAAU,EAClD,OAGJ,IAAMC,EAAajE,EAAMgE,EAAW,MAAM,EAAG,EAAE,EAAG,IAAI,EACtD,IAAKA,EAAW,OAASC,EAAW,QAAU,IAAM,EAChD,MAEpB,KACiB,CAED,IAAMC,EAAiB7D,GAAmBI,EAAI,CAAC,EAAG,IAAI,EACtD,GAAIyD,EAAiB,GAAI,CAErB,IAAMC,GADQ1D,EAAI,CAAC,EAAE,QAAQ,GAAG,IAAM,EAAI,EAAI,GACtBA,EAAI,CAAC,EAAE,OAASyD,EACxCzD,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,UAAU,EAAGyD,CAAc,EAC3CzD,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,UAAU,EAAG0D,CAAO,EAAE,KAAI,EAC1C1D,EAAI,CAAC,EAAI,EAC7B,CACA,CACY,IAAIrB,EAAOqB,EAAI,CAAC,EACZI,EAAQ,GACZ,GAAI,KAAK,QAAQ,SAAU,CAEvB,IAAMxC,EAAO,KAAK,MAAM,MAAM,kBAAkB,KAAKe,CAAI,EACrDf,IACAe,EAAOf,EAAK,CAAC,EACbwC,EAAQxC,EAAK,CAAC,EAElC,MAEgBwC,EAAQJ,EAAI,CAAC,EAAIA,EAAI,CAAC,EAAE,MAAM,EAAG,EAAE,EAAI,GAE3C,OAAArB,EAAOA,EAAK,KAAI,EACZ,KAAK,MAAM,MAAM,kBAAkB,KAAKA,CAAI,IACxC,KAAK,QAAQ,UAAY,CAAE,KAAK,MAAM,MAAM,gBAAgB,KAAK4E,CAAU,EAE3E5E,EAAOA,EAAK,MAAM,CAAC,EAGnBA,EAAOA,EAAK,MAAM,EAAG,EAAE,GAGxBoB,GAAWC,EAAK,CACnB,KAAMrB,GAAOA,EAAK,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,EAChE,MAAOyB,GAAQA,EAAM,QAAQ,KAAK,MAAM,OAAO,eAAgB,IAAI,CACnF,EAAeJ,EAAI,CAAC,EAAG,KAAK,MAAO,KAAK,KAAK,CAC7C,CACA,CACI,QAAQe,EAAK4C,EAAO,CAChB,IAAI3D,EACJ,IAAKA,EAAM,KAAK,MAAM,OAAO,QAAQ,KAAKe,CAAG,KACrCf,EAAM,KAAK,MAAM,OAAO,OAAO,KAAKe,CAAG,GAAI,CAC/C,IAAM6C,GAAc5D,EAAI,CAAC,GAAKA,EAAI,CAAC,GAAG,QAAQ,KAAK,MAAM,MAAM,oBAAqB,GAAG,EACjFpC,EAAO+F,EAAMC,EAAW,YAAW,CAAE,EAC3C,GAAI,CAAChG,EAAM,CACP,IAAMyC,EAAOL,EAAI,CAAC,EAAE,OAAO,CAAC,EAC5B,MAAO,CACH,KAAM,OACN,IAAKK,EACL,KAAAA,CACpB,CACA,CACY,OAAON,GAAWC,EAAKpC,EAAMoC,EAAI,CAAC,EAAG,KAAK,MAAO,KAAK,KAAK,CACvE,CACA,CACI,SAASe,EAAK8C,EAAWC,EAAW,GAAI,CACpC,IAAI9E,EAAQ,KAAK,MAAM,OAAO,eAAe,KAAK+B,CAAG,EAIrD,GAHI,CAAC/B,GAGDA,EAAM,CAAC,GAAK8E,EAAS,MAAM,KAAK,MAAM,MAAM,mBAAmB,EAC/D,OAEJ,GAAI,EADa9E,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAK,KACxB,CAAC8E,GAAY,KAAK,MAAM,OAAO,YAAY,KAAKA,CAAQ,EAAG,CAExE,IAAMC,EAAU,CAAC,GAAG/E,EAAM,CAAC,CAAC,EAAE,OAAS,EACnCgF,EAAQC,EAASC,EAAaH,EAASI,EAAgB,EACrDC,EAASpF,EAAM,CAAC,EAAE,CAAC,IAAM,IAAM,KAAK,MAAM,OAAO,kBAAoB,KAAK,MAAM,OAAO,kBAI7F,IAHAoF,EAAO,UAAY,EAEnBP,EAAYA,EAAU,MAAM,GAAK9C,EAAI,OAASgD,CAAO,GAC7C/E,EAAQoF,EAAO,KAAKP,CAAS,IAAM,MAAM,CAE7C,GADAG,EAAShF,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAKA,EAAM,CAAC,EACxE,CAACgF,EACD,SAEJ,GADAC,EAAU,CAAC,GAAGD,CAAM,EAAE,OAClBhF,EAAM,CAAC,GAAKA,EAAM,CAAC,EAAG,CACtBkF,GAAcD,EACd,QACpB,UACyBjF,EAAM,CAAC,GAAKA,EAAM,CAAC,IACpB+E,EAAU,GAAK,GAAGA,EAAUE,GAAW,GAAI,CAC3CE,GAAiBF,EACjB,QACxB,CAGgB,GADAC,GAAcD,EACVC,EAAa,EACb,SAEJD,EAAU,KAAK,IAAIA,EAASA,EAAUC,EAAaC,CAAa,EAEhE,IAAME,EAAiB,CAAC,GAAGrF,EAAM,CAAC,CAAC,EAAE,CAAC,EAAE,OAClCiB,EAAMc,EAAI,MAAM,EAAGgD,EAAU/E,EAAM,MAAQqF,EAAiBJ,CAAO,EAEzE,GAAI,KAAK,IAAIF,EAASE,CAAO,EAAI,EAAG,CAChC,IAAM5D,EAAOJ,EAAI,MAAM,EAAG,EAAE,EAC5B,MAAO,CACH,KAAM,KACN,IAAAA,EACA,KAAAI,EACA,OAAQ,KAAK,MAAM,aAAaA,CAAI,CAC5D,CACA,CAEgB,IAAMA,EAAOJ,EAAI,MAAM,EAAG,EAAE,EAC5B,MAAO,CACH,KAAM,SACN,IAAAA,EACA,KAAAI,EACA,OAAQ,KAAK,MAAM,aAAaA,CAAI,CACxD,CACA,CACA,CACA,CACI,SAASU,EAAK,CACV,IAAMf,EAAM,KAAK,MAAM,OAAO,KAAK,KAAKe,CAAG,EAC3C,GAAIf,EAAK,CACL,IAAIK,EAAOL,EAAI,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM,kBAAmB,GAAG,EAC3DsE,EAAmB,KAAK,MAAM,MAAM,aAAa,KAAKjE,CAAI,EAC1DkE,EAA0B,KAAK,MAAM,MAAM,kBAAkB,KAAKlE,CAAI,GAAK,KAAK,MAAM,MAAM,gBAAgB,KAAKA,CAAI,EAC3H,OAAIiE,GAAoBC,IACpBlE,EAAOA,EAAK,UAAU,EAAGA,EAAK,OAAS,CAAC,GAErC,CACH,KAAM,WACN,IAAKL,EAAI,CAAC,EACV,KAAAK,CAChB,CACA,CACA,CACI,GAAGU,EAAK,CACJ,IAAMf,EAAM,KAAK,MAAM,OAAO,GAAG,KAAKe,CAAG,EACzC,GAAIf,EACA,MAAO,CACH,KAAM,KACN,IAAKA,EAAI,CAAC,CAC1B,CAEA,CACI,IAAIe,EAAK,CACL,IAAMf,EAAM,KAAK,MAAM,OAAO,IAAI,KAAKe,CAAG,EAC1C,GAAIf,EACA,MAAO,CACH,KAAM,MACN,IAAKA,EAAI,CAAC,EACV,KAAMA,EAAI,CAAC,EACX,OAAQ,KAAK,MAAM,aAAaA,EAAI,CAAC,CAAC,CACtD,CAEA,CACI,SAASe,EAAK,CACV,IAAMf,EAAM,KAAK,MAAM,OAAO,SAAS,KAAKe,CAAG,EAC/C,GAAIf,EAAK,CACL,IAAIK,EAAM1B,EACV,OAAIqB,EAAI,CAAC,IAAM,KACXK,EAAOL,EAAI,CAAC,EACZrB,EAAO,UAAY0B,IAGnBA,EAAOL,EAAI,CAAC,EACZrB,EAAO0B,GAEJ,CACH,KAAM,OACN,IAAKL,EAAI,CAAC,EACV,KAAAK,EACA,KAAA1B,EACA,OAAQ,CACJ,CACI,KAAM,OACN,IAAK0B,EACL,KAAAA,CACxB,CACA,CACA,CACA,CACA,CACI,IAAIU,EAAK,CACL,IAAIf,EACJ,GAAIA,EAAM,KAAK,MAAM,OAAO,IAAI,KAAKe,CAAG,EAAG,CACvC,IAAIV,EAAM1B,EACV,GAAIqB,EAAI,CAAC,IAAM,IACXK,EAAOL,EAAI,CAAC,EACZrB,EAAO,UAAY0B,MAElB,CAED,IAAImE,EACJ,GACIA,EAAcxE,EAAI,CAAC,EACnBA,EAAI,CAAC,EAAI,KAAK,MAAM,OAAO,WAAW,KAAKA,EAAI,CAAC,CAAC,IAAI,CAAC,GAAK,SACtDwE,IAAgBxE,EAAI,CAAC,GAC9BK,EAAOL,EAAI,CAAC,EACRA,EAAI,CAAC,IAAM,OACXrB,EAAO,UAAYqB,EAAI,CAAC,EAGxBrB,EAAOqB,EAAI,CAAC,CAEhC,CACY,MAAO,CACH,KAAM,OACN,IAAKA,EAAI,CAAC,EACV,KAAAK,EACA,KAAA1B,EACA,OAAQ,CACJ,CACI,KAAM,OACN,IAAK0B,EACL,KAAAA,CACxB,CACA,CACA,CACA,CACA,CACI,WAAWU,EAAK,CACZ,IAAMf,EAAM,KAAK,MAAM,OAAO,KAAK,KAAKe,CAAG,EAC3C,GAAIf,EAAK,CACL,IAAMb,EAAU,KAAK,MAAM,MAAM,WACjC,MAAO,CACH,KAAM,OACN,IAAKa,EAAI,CAAC,EACV,KAAMA,EAAI,CAAC,EACX,QAAAb,CAChB,CACA,CACA,CACA,EClxBasF,EAAN,MAAMC,CAAO,OAAA,CAAA7K,EAAA,eAChB,OACA,QACA,MACA,UACA,YACA,YAAYiH,EAAS,CAEjB,KAAK,OAAS,CAAA,EACd,KAAK,OAAO,MAAQ,OAAO,OAAO,IAAI,EACtC,KAAK,QAAUA,GAAWhH,EAC1B,KAAK,QAAQ,UAAY,KAAK,QAAQ,WAAa,IAAI+G,EACvD,KAAK,UAAY,KAAK,QAAQ,UAC9B,KAAK,UAAU,QAAU,KAAK,QAC9B,KAAK,UAAU,MAAQ,KACvB,KAAK,YAAc,CAAA,EACnB,KAAK,MAAQ,CACT,OAAQ,GACR,WAAY,GACZ,IAAK,EACjB,EACQ,IAAMV,EAAQ,CACV,MAAAzF,EACA,MAAO0D,EAAM,OACb,OAAQC,EAAO,MAC3B,EACY,KAAK,QAAQ,UACb8B,EAAM,MAAQ/B,EAAM,SACpB+B,EAAM,OAAS9B,EAAO,UAEjB,KAAK,QAAQ,MAClB8B,EAAM,MAAQ/B,EAAM,IAChB,KAAK,QAAQ,OACb+B,EAAM,OAAS9B,EAAO,OAGtB8B,EAAM,OAAS9B,EAAO,KAG9B,KAAK,UAAU,MAAQ8B,CAC/B,CAII,WAAW,OAAQ,CACf,MAAO,CACH,MAAA/B,EACA,OAAAC,CACZ,CACA,CAII,OAAO,IAAI0C,EAAKD,EAAS,CAErB,OADc,IAAI4D,EAAO5D,CAAO,EACnB,IAAIC,CAAG,CAC5B,CAII,OAAO,UAAUA,EAAKD,EAAS,CAE3B,OADc,IAAI4D,EAAO5D,CAAO,EACnB,aAAaC,CAAG,CACrC,CAII,IAAIA,EAAK,CACLA,EAAMA,EAAI,QAAQrG,EAAM,eAAgB;CAAI,EAC5C,KAAK,YAAYqG,EAAK,KAAK,MAAM,EACjC,QAASzB,EAAI,EAAGA,EAAI,KAAK,YAAY,OAAQA,IAAK,CAC9C,IAAMqF,EAAO,KAAK,YAAYrF,CAAC,EAC/B,KAAK,aAAaqF,EAAK,IAAKA,EAAK,MAAM,CACnD,CACQ,YAAK,YAAc,CAAA,EACZ,KAAK,MACpB,CACI,YAAY5D,EAAKG,EAAS,CAAA,EAAI0D,EAAuB,GAAO,CAIxD,IAHI,KAAK,QAAQ,WACb7D,EAAMA,EAAI,QAAQrG,EAAM,cAAe,MAAM,EAAE,QAAQA,EAAM,UAAW,EAAE,GAEvEqG,GAAK,CACR,IAAIT,EACJ,GAAI,KAAK,QAAQ,YAAY,OAAO,KAAMuE,IAClCvE,EAAQuE,EAAa,KAAK,CAAE,MAAO,IAAI,EAAI9D,EAAKG,CAAM,IACtDH,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACV,IAEJ,EACV,EACG,SAGJ,GAAIA,EAAQ,KAAK,UAAU,MAAMS,CAAG,EAAG,CACnCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpC,IAAMkB,EAAYN,EAAO,GAAG,EAAE,EAC1BZ,EAAM,IAAI,SAAW,GAAKkB,IAAc,OAGxCA,EAAU,KAAO;EAGjBN,EAAO,KAAKZ,CAAK,EAErB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,KAAKS,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpC,IAAMkB,EAAYN,EAAO,GAAG,EAAE,EAE1BM,GAAW,OAAS,aAAeA,GAAW,OAAS,QACvDA,EAAU,KAAO;EAAOlB,EAAM,IAC9BkB,EAAU,MAAQ;EAAOlB,EAAM,KAC/B,KAAK,YAAY,GAAG,EAAE,EAAE,IAAMkB,EAAU,MAGxCN,EAAO,KAAKZ,CAAK,EAErB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,OAAOS,CAAG,EAAG,CACpCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,QAAQS,CAAG,EAAG,CACrCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,GAAGS,CAAG,EAAG,CAChCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,WAAWS,CAAG,EAAG,CACxCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,KAAKS,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,KAAKS,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,IAAIS,CAAG,EAAG,CACjCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpC,IAAMkB,EAAYN,EAAO,GAAG,EAAE,EAC1BM,GAAW,OAAS,aAAeA,GAAW,OAAS,QACvDA,EAAU,KAAO;EAAOlB,EAAM,IAC9BkB,EAAU,MAAQ;EAAOlB,EAAM,IAC/B,KAAK,YAAY,GAAG,EAAE,EAAE,IAAMkB,EAAU,MAElC,KAAK,OAAO,MAAMlB,EAAM,GAAG,IACjC,KAAK,OAAO,MAAMA,EAAM,GAAG,EAAI,CAC3B,KAAMA,EAAM,KACZ,MAAOA,EAAM,KACrC,GAEgB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,MAAMS,CAAG,EAAG,CACnCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,SAASS,CAAG,EAAG,CACtCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAGY,IAAIwE,EAAS/D,EACb,GAAI,KAAK,QAAQ,YAAY,WAAY,CACrC,IAAIgE,EAAa,IACXC,EAAUjE,EAAI,MAAM,CAAC,EACvBkE,EACJ,KAAK,QAAQ,WAAW,WAAW,QAASC,GAAkB,CAC1DD,EAAYC,EAAc,KAAK,CAAE,MAAO,IAAI,EAAIF,CAAO,EACnD,OAAOC,GAAc,UAAYA,GAAa,IAC9CF,EAAa,KAAK,IAAIA,EAAYE,CAAS,EAEnE,CAAiB,EACGF,EAAa,KAAYA,GAAc,IACvCD,EAAS/D,EAAI,UAAU,EAAGgE,EAAa,CAAC,EAE5D,CACY,GAAI,KAAK,MAAM,MAAQzE,EAAQ,KAAK,UAAU,UAAUwE,CAAM,GAAI,CAC9D,IAAMtD,EAAYN,EAAO,GAAG,EAAE,EAC1B0D,GAAwBpD,GAAW,OAAS,aAC5CA,EAAU,KAAO;EAAOlB,EAAM,IAC9BkB,EAAU,MAAQ;EAAOlB,EAAM,KAC/B,KAAK,YAAY,IAAG,EACpB,KAAK,YAAY,GAAG,EAAE,EAAE,IAAMkB,EAAU,MAGxCN,EAAO,KAAKZ,CAAK,EAErBsE,EAAuBE,EAAO,SAAW/D,EAAI,OAC7CA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpC,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,KAAKS,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpC,IAAMkB,EAAYN,EAAO,GAAG,EAAE,EAC1BM,GAAW,OAAS,QACpBA,EAAU,KAAO;EAAOlB,EAAM,IAC9BkB,EAAU,MAAQ;EAAOlB,EAAM,KAC/B,KAAK,YAAY,IAAG,EACpB,KAAK,YAAY,GAAG,EAAE,EAAE,IAAMkB,EAAU,MAGxCN,EAAO,KAAKZ,CAAK,EAErB,QAChB,CACY,GAAIS,EAAK,CACL,IAAMoE,EAAS,0BAA4BpE,EAAI,WAAW,CAAC,EAC3D,GAAI,KAAK,QAAQ,OAAQ,CACrB,QAAQ,MAAMoE,CAAM,EACpB,KACpB,KAEoB,OAAM,IAAI,MAAMA,CAAM,CAE1C,CACA,CACQ,YAAK,MAAM,IAAM,GACVjE,CACf,CACI,OAAOH,EAAKG,EAAS,CAAA,EAAI,CACrB,YAAK,YAAY,KAAK,CAAE,IAAAH,EAAK,OAAAG,CAAM,CAAE,EAC9BA,CACf,CAII,aAAaH,EAAKG,EAAS,CAAA,EAAI,CAE3B,IAAI2C,EAAY9C,EACZ/B,EAAQ,KAEZ,GAAI,KAAK,OAAO,MAAO,CACnB,IAAM2E,EAAQ,OAAO,KAAK,KAAK,OAAO,KAAK,EAC3C,GAAIA,EAAM,OAAS,EACf,MAAQ3E,EAAQ,KAAK,UAAU,MAAM,OAAO,cAAc,KAAK6E,CAAS,IAAM,MACtEF,EAAM,SAAS3E,EAAM,CAAC,EAAE,MAAMA,EAAM,CAAC,EAAE,YAAY,GAAG,EAAI,EAAG,EAAE,CAAC,IAChE6E,EAAYA,EAAU,MAAM,EAAG7E,EAAM,KAAK,EACpC,IAAM,IAAI,OAAOA,EAAM,CAAC,EAAE,OAAS,CAAC,EAAI,IACxC6E,EAAU,MAAM,KAAK,UAAU,MAAM,OAAO,cAAc,SAAS,EAIjG,CAEQ,MAAQ7E,EAAQ,KAAK,UAAU,MAAM,OAAO,UAAU,KAAK6E,CAAS,IAAM,MACtEA,EAAYA,EAAU,MAAM,EAAG7E,EAAM,KAAK,EAAI,IAAM,IAAI,OAAOA,EAAM,CAAC,EAAE,OAAS,CAAC,EAAI,IAAM6E,EAAU,MAAM,KAAK,UAAU,MAAM,OAAO,UAAU,SAAS,EAG/J,MAAQ7E,EAAQ,KAAK,UAAU,MAAM,OAAO,eAAe,KAAK6E,CAAS,IAAM,MAC3EA,EAAYA,EAAU,MAAM,EAAG7E,EAAM,KAAK,EAAI,KAAO6E,EAAU,MAAM,KAAK,UAAU,MAAM,OAAO,eAAe,SAAS,EAE7H,IAAIuB,EAAe,GACftB,EAAW,GACf,KAAO/C,GAAK,CACHqE,IACDtB,EAAW,IAEfsB,EAAe,GACf,IAAI9E,EAEJ,GAAI,KAAK,QAAQ,YAAY,QAAQ,KAAMuE,IACnCvE,EAAQuE,EAAa,KAAK,CAAE,MAAO,IAAI,EAAI9D,EAAKG,CAAM,IACtDH,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACV,IAEJ,EACV,EACG,SAGJ,GAAIA,EAAQ,KAAK,UAAU,OAAOS,CAAG,EAAG,CACpCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,IAAIS,CAAG,EAAG,CACjCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,KAAKS,CAAG,EAAG,CAClCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,QAAQS,EAAK,KAAK,OAAO,KAAK,EAAG,CACxDA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpC,IAAMkB,EAAYN,EAAO,GAAG,EAAE,EAC1BZ,EAAM,OAAS,QAAUkB,GAAW,OAAS,QAC7CA,EAAU,KAAOlB,EAAM,IACvBkB,EAAU,MAAQlB,EAAM,MAGxBY,EAAO,KAAKZ,CAAK,EAErB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,SAASS,EAAK8C,EAAWC,CAAQ,EAAG,CAC3D/C,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,SAASS,CAAG,EAAG,CACtCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,GAAGS,CAAG,EAAG,CAChCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,IAAIS,CAAG,EAAG,CACjCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAIA,EAAQ,KAAK,UAAU,SAASS,CAAG,EAAG,CACtCA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAEY,GAAI,CAAC,KAAK,MAAM,SAAWA,EAAQ,KAAK,UAAU,IAAIS,CAAG,GAAI,CACzDA,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EACpCY,EAAO,KAAKZ,CAAK,EACjB,QAChB,CAGY,IAAIwE,EAAS/D,EACb,GAAI,KAAK,QAAQ,YAAY,YAAa,CACtC,IAAIgE,EAAa,IACXC,EAAUjE,EAAI,MAAM,CAAC,EACvBkE,EACJ,KAAK,QAAQ,WAAW,YAAY,QAASC,GAAkB,CAC3DD,EAAYC,EAAc,KAAK,CAAE,MAAO,IAAI,EAAIF,CAAO,EACnD,OAAOC,GAAc,UAAYA,GAAa,IAC9CF,EAAa,KAAK,IAAIA,EAAYE,CAAS,EAEnE,CAAiB,EACGF,EAAa,KAAYA,GAAc,IACvCD,EAAS/D,EAAI,UAAU,EAAGgE,EAAa,CAAC,EAE5D,CACY,GAAIzE,EAAQ,KAAK,UAAU,WAAWwE,CAAM,EAAG,CAC3C/D,EAAMA,EAAI,UAAUT,EAAM,IAAI,MAAM,EAChCA,EAAM,IAAI,MAAM,EAAE,IAAM,MACxBwD,EAAWxD,EAAM,IAAI,MAAM,EAAE,GAEjC8E,EAAe,GACf,IAAM5D,EAAYN,EAAO,GAAG,EAAE,EAC1BM,GAAW,OAAS,QACpBA,EAAU,KAAOlB,EAAM,IACvBkB,EAAU,MAAQlB,EAAM,MAGxBY,EAAO,KAAKZ,CAAK,EAErB,QAChB,CACY,GAAIS,EAAK,CACL,IAAMoE,EAAS,0BAA4BpE,EAAI,WAAW,CAAC,EAC3D,GAAI,KAAK,QAAQ,OAAQ,CACrB,QAAQ,MAAMoE,CAAM,EACpB,KACpB,KAEoB,OAAM,IAAI,MAAMA,CAAM,CAE1C,CACA,CACQ,OAAOjE,CACf,CACA,EC5ZamE,EAAN,KAAgB,OAAA,CAAAxL,EAAA,kBACnB,QACA,OACA,YAAYiH,EAAS,CACjB,KAAK,QAAUA,GAAWhH,CAClC,CACI,MAAMwG,EAAO,CACT,MAAO,EACf,CACI,KAAK,CAAE,KAAAD,EAAM,KAAAiF,EAAM,QAAAnG,CAAO,EAAI,CAC1B,IAAMoG,GAAcD,GAAQ,IAAI,MAAM5K,EAAM,aAAa,IAAI,CAAC,EACxD8K,EAAOnF,EAAK,QAAQ3F,EAAM,cAAe,EAAE,EAAI;EACrD,OAAK6K,EAKE,8BACDnJ,EAAOmJ,CAAU,EACjB,MACCpG,EAAUqG,EAAOpJ,EAAOoJ,EAAM,EAAI,GACnC;EARK,eACArG,EAAUqG,EAAOpJ,EAAOoJ,EAAM,EAAI,GACnC;CAOlB,CACI,WAAW,CAAE,OAAAtE,CAAM,EAAI,CAEnB,MAAO;EADM,KAAK,OAAO,MAAMA,CAAM,CACT;CACpC,CACI,KAAK,CAAE,KAAAb,CAAI,EAAI,CACX,OAAOA,CACf,CACI,QAAQ,CAAE,OAAAa,EAAQ,MAAAuE,CAAK,EAAI,CACvB,MAAO,KAAKA,CAAK,IAAI,KAAK,OAAO,YAAYvE,CAAM,CAAC,MAAMuE,CAAK;CACvE,CACI,GAAGnF,EAAO,CACN,MAAO;CACf,CACI,KAAKA,EAAO,CACR,IAAMoF,EAAUpF,EAAM,QAChBqF,EAAQrF,EAAM,MAChBsF,EAAO,GACX,QAASC,EAAI,EAAGA,EAAIvF,EAAM,MAAM,OAAQuF,IAAK,CACzC,IAAMzC,EAAO9C,EAAM,MAAMuF,CAAC,EAC1BD,GAAQ,KAAK,SAASxC,CAAI,CACtC,CACQ,IAAM0C,EAAOJ,EAAU,KAAO,KACxBK,EAAaL,GAAWC,IAAU,EAAM,WAAaA,EAAQ,IAAO,GAC1E,MAAO,IAAMG,EAAOC,EAAY;EAAQH,EAAO,KAAOE,EAAO;CACrE,CACI,SAAS1C,EAAM,CACX,IAAI4C,EAAW,GACf,GAAI5C,EAAK,KAAM,CACX,IAAM6C,EAAW,KAAK,SAAS,CAAE,QAAS,CAAC,CAAC7C,EAAK,OAAO,CAAE,EACtDA,EAAK,MACDA,EAAK,OAAO,CAAC,GAAG,OAAS,aACzBA,EAAK,OAAO,CAAC,EAAE,KAAO6C,EAAW,IAAM7C,EAAK,OAAO,CAAC,EAAE,KAClDA,EAAK,OAAO,CAAC,EAAE,QAAUA,EAAK,OAAO,CAAC,EAAE,OAAO,OAAS,GAAKA,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,OAAS,SAC/FA,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,KAAO6C,EAAW,IAAM7J,EAAOgH,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,IAAI,EACrFA,EAAK,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,QAAU,KAIvCA,EAAK,OAAO,QAAQ,CAChB,KAAM,OACN,IAAK6C,EAAW,IAChB,KAAMA,EAAW,IACjB,QAAS,EACjC,CAAqB,EAILD,GAAYC,EAAW,GAEvC,CACQ,OAAAD,GAAY,KAAK,OAAO,MAAM5C,EAAK,OAAQ,CAAC,CAACA,EAAK,KAAK,EAChD,OAAO4C,CAAQ;CAC9B,CACI,SAAS,CAAE,QAAAE,CAAO,EAAI,CAClB,MAAO,WACAA,EAAU,cAAgB,IAC3B,8BACd,CACI,UAAU,CAAE,OAAAhF,CAAM,EAAI,CAClB,MAAO,MAAM,KAAK,OAAO,YAAYA,CAAM,CAAC;CACpD,CACI,MAAMZ,EAAO,CACT,IAAI6F,EAAS,GAET7C,EAAO,GACX,QAASuC,EAAI,EAAGA,EAAIvF,EAAM,OAAO,OAAQuF,IACrCvC,GAAQ,KAAK,UAAUhD,EAAM,OAAOuF,CAAC,CAAC,EAE1CM,GAAU,KAAK,SAAS,CAAE,KAAM7C,CAAI,CAAE,EACtC,IAAIsC,EAAO,GACX,QAASC,EAAI,EAAGA,EAAIvF,EAAM,KAAK,OAAQuF,IAAK,CACxC,IAAM9G,EAAMuB,EAAM,KAAKuF,CAAC,EACxBvC,EAAO,GACP,QAAS8C,EAAI,EAAGA,EAAIrH,EAAI,OAAQqH,IAC5B9C,GAAQ,KAAK,UAAUvE,EAAIqH,CAAC,CAAC,EAEjCR,GAAQ,KAAK,SAAS,CAAE,KAAMtC,CAAI,CAAE,CAChD,CACQ,OAAIsC,IACAA,EAAO,UAAUA,CAAI,YAClB;;EAEDO,EACA;EACAP,EACA;CACd,CACI,SAAS,CAAE,KAAAvF,CAAI,EAAI,CACf,MAAO;EAASA,CAAI;CAC5B,CACI,UAAUC,EAAO,CACb,IAAM+F,EAAU,KAAK,OAAO,YAAY/F,EAAM,MAAM,EAC9CwF,EAAOxF,EAAM,OAAS,KAAO,KAInC,OAHYA,EAAM,MACZ,IAAIwF,CAAI,WAAWxF,EAAM,KAAK,KAC9B,IAAIwF,CAAI,KACDO,EAAU,KAAKP,CAAI;CACxC,CAII,OAAO,CAAE,OAAA5E,CAAM,EAAI,CACf,MAAO,WAAW,KAAK,OAAO,YAAYA,CAAM,CAAC,WACzD,CACI,GAAG,CAAE,OAAAA,CAAM,EAAI,CACX,MAAO,OAAO,KAAK,OAAO,YAAYA,CAAM,CAAC,OACrD,CACI,SAAS,CAAE,KAAAb,CAAI,EAAI,CACf,MAAO,SAASjE,EAAOiE,EAAM,EAAI,CAAC,SAC1C,CACI,GAAGC,EAAO,CACN,MAAO,MACf,CACI,IAAI,CAAE,OAAAY,CAAM,EAAI,CACZ,MAAO,QAAQ,KAAK,OAAO,YAAYA,CAAM,CAAC,QACtD,CACI,KAAK,CAAE,KAAAvC,EAAM,MAAAyB,EAAO,OAAAc,CAAM,EAAI,CAC1B,IAAMb,EAAO,KAAK,OAAO,YAAYa,CAAM,EACrCoF,EAAY5H,GAASC,CAAI,EAC/B,GAAI2H,IAAc,KACd,OAAOjG,EAEX1B,EAAO2H,EACP,IAAIC,EAAM,YAAc5H,EAAO,IAC/B,OAAIyB,IACAmG,GAAO,WAAcnK,EAAOgE,CAAK,EAAK,KAE1CmG,GAAO,IAAMlG,EAAO,OACbkG,CACf,CACI,MAAM,CAAE,KAAA5H,EAAM,MAAAyB,EAAO,KAAAC,CAAI,EAAI,CACzB,IAAMiG,EAAY5H,GAASC,CAAI,EAC/B,GAAI2H,IAAc,KACd,OAAOlK,EAAOiE,CAAI,EAEtB1B,EAAO2H,EACP,IAAIC,EAAM,aAAa5H,CAAI,UAAU0B,CAAI,IACzC,OAAID,IACAmG,GAAO,WAAWnK,EAAOgE,CAAK,CAAC,KAEnCmG,GAAO,IACAA,CACf,CACI,KAAKjG,EAAO,CACR,MAAO,WAAYA,GAASA,EAAM,OAC5B,KAAK,OAAO,YAAYA,EAAM,MAAM,EACnC,YAAaA,GAASA,EAAM,QAAUA,EAAM,KAAOlE,EAAOkE,EAAM,IAAI,CACnF,CACA,EC7KakG,EAAN,KAAoB,OAAA,CAAA3M,EAAA,sBAEvB,OAAO,CAAE,KAAAwG,CAAI,EAAI,CACb,OAAOA,CACf,CACI,GAAG,CAAE,KAAAA,CAAI,EAAI,CACT,OAAOA,CACf,CACI,SAAS,CAAE,KAAAA,CAAI,EAAI,CACf,OAAOA,CACf,CACI,IAAI,CAAE,KAAAA,CAAI,EAAI,CACV,OAAOA,CACf,CACI,KAAK,CAAE,KAAAA,CAAI,EAAI,CACX,OAAOA,CACf,CACI,KAAK,CAAE,KAAAA,CAAI,EAAI,CACX,OAAOA,CACf,CACI,KAAK,CAAE,KAAAA,CAAI,EAAI,CACX,MAAO,GAAKA,CACpB,CACI,MAAM,CAAE,KAAAA,CAAI,EAAI,CACZ,MAAO,GAAKA,CACpB,CACI,IAAK,CACD,MAAO,EACf,CACA,EC3BaoG,EAAN,MAAMC,CAAQ,OAAA,CAAA7M,EAAA,gBACjB,QACA,SACA,aACA,YAAYiH,EAAS,CACjB,KAAK,QAAUA,GAAWhH,EAC1B,KAAK,QAAQ,SAAW,KAAK,QAAQ,UAAY,IAAIuL,EACrD,KAAK,SAAW,KAAK,QAAQ,SAC7B,KAAK,SAAS,QAAU,KAAK,QAC7B,KAAK,SAAS,OAAS,KACvB,KAAK,aAAe,IAAImB,CAChC,CAII,OAAO,MAAMtF,EAAQJ,EAAS,CAE1B,OADe,IAAI4F,EAAQ5F,CAAO,EACpB,MAAMI,CAAM,CAClC,CAII,OAAO,YAAYA,EAAQJ,EAAS,CAEhC,OADe,IAAI4F,EAAQ5F,CAAO,EACpB,YAAYI,CAAM,CACxC,CAII,MAAMA,EAAQK,EAAM,GAAM,CACtB,IAAIgF,EAAM,GACV,QAASjH,EAAI,EAAGA,EAAI4B,EAAO,OAAQ5B,IAAK,CACpC,IAAMqH,EAAWzF,EAAO5B,CAAC,EAEzB,GAAI,KAAK,QAAQ,YAAY,YAAYqH,EAAS,IAAI,EAAG,CACrD,IAAMC,EAAeD,EACfE,EAAM,KAAK,QAAQ,WAAW,UAAUD,EAAa,IAAI,EAAE,KAAK,CAAE,OAAQ,IAAI,EAAIA,CAAY,EACpG,GAAIC,IAAQ,IAAS,CAAC,CAAC,QAAS,KAAM,UAAW,OAAQ,QAAS,aAAc,OAAQ,OAAQ,YAAa,MAAM,EAAE,SAASD,EAAa,IAAI,EAAG,CAC9IL,GAAOM,GAAO,GACd,QACpB,CACA,CACY,IAAMvG,EAAQqG,EACd,OAAQrG,EAAM,KAAI,CACd,IAAK,QAAS,CACViG,GAAO,KAAK,SAAS,MAAMjG,CAAK,EAChC,QACpB,CACgB,IAAK,KAAM,CACPiG,GAAO,KAAK,SAAS,GAAGjG,CAAK,EAC7B,QACpB,CACgB,IAAK,UAAW,CACZiG,GAAO,KAAK,SAAS,QAAQjG,CAAK,EAClC,QACpB,CACgB,IAAK,OAAQ,CACTiG,GAAO,KAAK,SAAS,KAAKjG,CAAK,EAC/B,QACpB,CACgB,IAAK,QAAS,CACViG,GAAO,KAAK,SAAS,MAAMjG,CAAK,EAChC,QACpB,CACgB,IAAK,aAAc,CACfiG,GAAO,KAAK,SAAS,WAAWjG,CAAK,EACrC,QACpB,CACgB,IAAK,OAAQ,CACTiG,GAAO,KAAK,SAAS,KAAKjG,CAAK,EAC/B,QACpB,CACgB,IAAK,OAAQ,CACTiG,GAAO,KAAK,SAAS,KAAKjG,CAAK,EAC/B,QACpB,CACgB,IAAK,YAAa,CACdiG,GAAO,KAAK,SAAS,UAAUjG,CAAK,EACpC,QACpB,CACgB,IAAK,OAAQ,CACT,IAAIwG,EAAYxG,EACZsF,EAAO,KAAK,SAAS,KAAKkB,CAAS,EACvC,KAAOxH,EAAI,EAAI4B,EAAO,QAAUA,EAAO5B,EAAI,CAAC,EAAE,OAAS,QACnDwH,EAAY5F,EAAO,EAAE5B,CAAC,EACtBsG,GAAQ;EAAO,KAAK,SAAS,KAAKkB,CAAS,EAE3CvF,EACAgF,GAAO,KAAK,SAAS,UAAU,CAC3B,KAAM,YACN,IAAKX,EACL,KAAMA,EACN,OAAQ,CAAC,CAAE,KAAM,OAAQ,IAAKA,EAAM,KAAMA,EAAM,QAAS,EAAI,CAAE,CAC3F,CAAyB,EAGDW,GAAOX,EAEX,QACpB,CACgB,QAAS,CACL,IAAMT,EAAS,eAAiB7E,EAAM,KAAO,wBAC7C,GAAI,KAAK,QAAQ,OACb,eAAQ,MAAM6E,CAAM,EACb,GAGP,MAAM,IAAI,MAAMA,CAAM,CAE9C,CACA,CACA,CACQ,OAAOoB,CACf,CAII,YAAYrF,EAAQ6F,EAAW,KAAK,SAAU,CAC1C,IAAIR,EAAM,GACV,QAASjH,EAAI,EAAGA,EAAI4B,EAAO,OAAQ5B,IAAK,CACpC,IAAMqH,EAAWzF,EAAO5B,CAAC,EAEzB,GAAI,KAAK,QAAQ,YAAY,YAAYqH,EAAS,IAAI,EAAG,CACrD,IAAME,EAAM,KAAK,QAAQ,WAAW,UAAUF,EAAS,IAAI,EAAE,KAAK,CAAE,OAAQ,IAAI,EAAIA,CAAQ,EAC5F,GAAIE,IAAQ,IAAS,CAAC,CAAC,SAAU,OAAQ,OAAQ,QAAS,SAAU,KAAM,WAAY,KAAM,MAAO,MAAM,EAAE,SAASF,EAAS,IAAI,EAAG,CAChIJ,GAAOM,GAAO,GACd,QACpB,CACA,CACY,IAAMvG,EAAQqG,EACd,OAAQrG,EAAM,KAAI,CACd,IAAK,SAAU,CACXiG,GAAOQ,EAAS,KAAKzG,CAAK,EAC1B,KACpB,CACgB,IAAK,OAAQ,CACTiG,GAAOQ,EAAS,KAAKzG,CAAK,EAC1B,KACpB,CACgB,IAAK,OAAQ,CACTiG,GAAOQ,EAAS,KAAKzG,CAAK,EAC1B,KACpB,CACgB,IAAK,QAAS,CACViG,GAAOQ,EAAS,MAAMzG,CAAK,EAC3B,KACpB,CACgB,IAAK,SAAU,CACXiG,GAAOQ,EAAS,OAAOzG,CAAK,EAC5B,KACpB,CACgB,IAAK,KAAM,CACPiG,GAAOQ,EAAS,GAAGzG,CAAK,EACxB,KACpB,CACgB,IAAK,WAAY,CACbiG,GAAOQ,EAAS,SAASzG,CAAK,EAC9B,KACpB,CACgB,IAAK,KAAM,CACPiG,GAAOQ,EAAS,GAAGzG,CAAK,EACxB,KACpB,CACgB,IAAK,MAAO,CACRiG,GAAOQ,EAAS,IAAIzG,CAAK,EACzB,KACpB,CACgB,IAAK,OAAQ,CACTiG,GAAOQ,EAAS,KAAKzG,CAAK,EAC1B,KACpB,CACgB,QAAS,CACL,IAAM6E,EAAS,eAAiB7E,EAAM,KAAO,wBAC7C,GAAI,KAAK,QAAQ,OACb,eAAQ,MAAM6E,CAAM,EACb,GAGP,MAAM,IAAI,MAAMA,CAAM,CAE9C,CACA,CACA,CACQ,OAAOoB,CACf,CACA,EC5LaS,EAAN,KAAa,OAAA,CAAAnN,EAAA,eAChB,QACA,MACA,YAAYiH,EAAS,CACjB,KAAK,QAAUA,GAAWhH,CAClC,CACI,OAAO,iBAAmB,IAAI,IAAI,CAC9B,aACA,cACA,kBACR,CAAK,EAID,WAAWmN,EAAU,CACjB,OAAOA,CACf,CAII,YAAYpL,EAAM,CACd,OAAOA,CACf,CAII,iBAAiBqF,EAAQ,CACrB,OAAOA,CACf,CAII,cAAe,CACX,OAAO,KAAK,MAAQuD,EAAO,IAAMA,EAAO,SAChD,CAII,eAAgB,CACZ,OAAO,KAAK,MAAQgC,EAAQ,MAAQA,EAAQ,WACpD,CACA,ECpCaS,EAAN,KAAa,OAAA,CAAArN,EAAA,eAChB,SAAWD,EAAY,EACvB,QAAU,KAAK,WACf,MAAQ,KAAK,cAAc,EAAI,EAC/B,YAAc,KAAK,cAAc,EAAK,EACtC,OAAS6M,EACT,SAAWpB,EACX,aAAemB,EACf,MAAQ/B,EACR,UAAY5D,EACZ,MAAQmG,EACR,eAAeG,EAAM,CACjB,KAAK,IAAI,GAAGA,CAAI,CACxB,CAII,WAAWjG,EAAQkG,EAAU,CACzB,IAAIC,EAAS,CAAA,EACb,QAAW/G,KAASY,EAEhB,OADAmG,EAASA,EAAO,OAAOD,EAAS,KAAK,KAAM9G,CAAK,CAAC,EACzCA,EAAM,KAAI,CACd,IAAK,QAAS,CACV,IAAMgH,EAAahH,EACnB,QAAWgD,KAAQgE,EAAW,OAC1BD,EAASA,EAAO,OAAO,KAAK,WAAW/D,EAAK,OAAQ8D,CAAQ,CAAC,EAEjE,QAAWrI,KAAOuI,EAAW,KACzB,QAAWhE,KAAQvE,EACfsI,EAASA,EAAO,OAAO,KAAK,WAAW/D,EAAK,OAAQ8D,CAAQ,CAAC,EAGrE,KACpB,CACgB,IAAK,OAAQ,CACT,IAAMG,EAAYjH,EAClB+G,EAASA,EAAO,OAAO,KAAK,WAAWE,EAAU,MAAOH,CAAQ,CAAC,EACjE,KACpB,CACgB,QAAS,CACL,IAAMR,EAAetG,EACjB,KAAK,SAAS,YAAY,cAAcsG,EAAa,IAAI,EACzD,KAAK,SAAS,WAAW,YAAYA,EAAa,IAAI,EAAE,QAASY,GAAgB,CAC7E,IAAMtG,EAAS0F,EAAaY,CAAW,EAAE,KAAK,GAAQ,EACtDH,EAASA,EAAO,OAAO,KAAK,WAAWnG,EAAQkG,CAAQ,CAAC,CACpF,CAAyB,EAEIR,EAAa,SAClBS,EAASA,EAAO,OAAO,KAAK,WAAWT,EAAa,OAAQQ,CAAQ,CAAC,EAE7F,CACA,CAEQ,OAAOC,CACf,CACI,OAAOF,EAAM,CACT,IAAMM,EAAa,KAAK,SAAS,YAAc,CAAE,UAAW,CAAA,EAAI,YAAa,CAAA,CAAE,EAC/E,OAAAN,EAAK,QAASO,GAAS,CAEnB,IAAMC,EAAO,CAAE,GAAGD,CAAI,EA8DtB,GA5DAC,EAAK,MAAQ,KAAK,SAAS,OAASA,EAAK,OAAS,GAE9CD,EAAK,aACLA,EAAK,WAAW,QAASE,GAAQ,CAC7B,GAAI,CAACA,EAAI,KACL,MAAM,IAAI,MAAM,yBAAyB,EAE7C,GAAI,aAAcA,EAAK,CACnB,IAAMC,EAAeJ,EAAW,UAAUG,EAAI,IAAI,EAC9CC,EAEAJ,EAAW,UAAUG,EAAI,IAAI,EAAI,YAAaT,EAAM,CAChD,IAAIN,EAAMe,EAAI,SAAS,MAAM,KAAMT,CAAI,EACvC,OAAIN,IAAQ,KACRA,EAAMgB,EAAa,MAAM,KAAMV,CAAI,GAEhCN,CACvC,EAG4BY,EAAW,UAAUG,EAAI,IAAI,EAAIA,EAAI,QAEjE,CACoB,GAAI,cAAeA,EAAK,CACpB,GAAI,CAACA,EAAI,OAAUA,EAAI,QAAU,SAAWA,EAAI,QAAU,SACtD,MAAM,IAAI,MAAM,6CAA6C,EAEjE,IAAME,EAAWL,EAAWG,EAAI,KAAK,EACjCE,EACAA,EAAS,QAAQF,EAAI,SAAS,EAG9BH,EAAWG,EAAI,KAAK,EAAI,CAACA,EAAI,SAAS,EAEtCA,EAAI,QACAA,EAAI,QAAU,QACVH,EAAW,WACXA,EAAW,WAAW,KAAKG,EAAI,KAAK,EAGpCH,EAAW,WAAa,CAACG,EAAI,KAAK,EAGjCA,EAAI,QAAU,WACfH,EAAW,YACXA,EAAW,YAAY,KAAKG,EAAI,KAAK,EAGrCH,EAAW,YAAc,CAACG,EAAI,KAAK,GAIvE,CACwB,gBAAiBA,GAAOA,EAAI,cAC5BH,EAAW,YAAYG,EAAI,IAAI,EAAIA,EAAI,YAE/D,CAAiB,EACDD,EAAK,WAAaF,GAGlBC,EAAK,SAAU,CACf,IAAMX,EAAW,KAAK,SAAS,UAAY,IAAI1B,EAAU,KAAK,QAAQ,EACtE,QAAW0C,KAAQL,EAAK,SAAU,CAC9B,GAAI,EAAEK,KAAQhB,GACV,MAAM,IAAI,MAAM,aAAagB,CAAI,kBAAkB,EAEvD,GAAI,CAAC,UAAW,QAAQ,EAAE,SAASA,CAAI,EAEnC,SAEJ,IAAMC,EAAeD,EACfE,EAAeP,EAAK,SAASM,CAAY,EACzCH,EAAed,EAASiB,CAAY,EAE1CjB,EAASiB,CAAY,EAAI,IAAIb,IAAS,CAClC,IAAIN,EAAMoB,EAAa,MAAMlB,EAAUI,CAAI,EAC3C,OAAIN,IAAQ,KACRA,EAAMgB,EAAa,MAAMd,EAAUI,CAAI,GAEpCN,GAAO,EACtC,CACA,CACgBc,EAAK,SAAWZ,CAChC,CACY,GAAIW,EAAK,UAAW,CAChB,IAAMQ,EAAY,KAAK,SAAS,WAAa,IAAIrH,EAAW,KAAK,QAAQ,EACzE,QAAWkH,KAAQL,EAAK,UAAW,CAC/B,GAAI,EAAEK,KAAQG,GACV,MAAM,IAAI,MAAM,cAAcH,CAAI,kBAAkB,EAExD,GAAI,CAAC,UAAW,QAAS,OAAO,EAAE,SAASA,CAAI,EAE3C,SAEJ,IAAMI,EAAgBJ,EAChBK,EAAgBV,EAAK,UAAUS,CAAa,EAC5CE,EAAgBH,EAAUC,CAAa,EAG7CD,EAAUC,CAAa,EAAI,IAAIhB,IAAS,CACpC,IAAIN,EAAMuB,EAAc,MAAMF,EAAWf,CAAI,EAC7C,OAAIN,IAAQ,KACRA,EAAMwB,EAAc,MAAMH,EAAWf,CAAI,GAEtCN,CAC/B,CACA,CACgBc,EAAK,UAAYO,CACjC,CAEY,GAAIR,EAAK,MAAO,CACZ,IAAMY,EAAQ,KAAK,SAAS,OAAS,IAAItB,EACzC,QAAWe,KAAQL,EAAK,MAAO,CAC3B,GAAI,EAAEK,KAAQO,GACV,MAAM,IAAI,MAAM,SAASP,CAAI,kBAAkB,EAEnD,GAAI,CAAC,UAAW,OAAO,EAAE,SAASA,CAAI,EAElC,SAEJ,IAAMQ,EAAYR,EACZS,EAAYd,EAAK,MAAMa,CAAS,EAChCE,EAAWH,EAAMC,CAAS,EAC5BvB,EAAO,iBAAiB,IAAIe,CAAI,EAEhCO,EAAMC,CAAS,EAAKG,GAAQ,CACxB,GAAI,KAAK,SAAS,MACd,OAAO,QAAQ,QAAQF,EAAU,KAAKF,EAAOI,CAAG,CAAC,EAAE,KAAK7B,GAC7C4B,EAAS,KAAKH,EAAOzB,CAAG,CAClC,EAEL,IAAMA,EAAM2B,EAAU,KAAKF,EAAOI,CAAG,EACrC,OAAOD,EAAS,KAAKH,EAAOzB,CAAG,CAC3D,EAIwByB,EAAMC,CAAS,EAAI,IAAIpB,IAAS,CAC5B,IAAIN,EAAM2B,EAAU,MAAMF,EAAOnB,CAAI,EACrC,OAAIN,IAAQ,KACRA,EAAM4B,EAAS,MAAMH,EAAOnB,CAAI,GAE7BN,CACnC,CAEA,CACgBc,EAAK,MAAQW,CAC7B,CAEY,GAAIZ,EAAK,WAAY,CACjB,IAAMiB,EAAa,KAAK,SAAS,WAC3BC,EAAiBlB,EAAK,WAC5BC,EAAK,WAAa,SAAUrH,EAAO,CAC/B,IAAI+G,EAAS,CAAA,EACb,OAAAA,EAAO,KAAKuB,EAAe,KAAK,KAAMtI,CAAK,CAAC,EACxCqI,IACAtB,EAASA,EAAO,OAAOsB,EAAW,KAAK,KAAMrI,CAAK,CAAC,GAEhD+G,CAC3B,CACA,CACY,KAAK,SAAW,CAAE,GAAG,KAAK,SAAU,GAAGM,CAAI,CACvD,CAAS,EACM,IACf,CACI,WAAWvN,EAAK,CACZ,YAAK,SAAW,CAAE,GAAG,KAAK,SAAU,GAAGA,CAAG,EACnC,IACf,CACI,MAAM2G,EAAKD,EAAS,CAChB,OAAO2D,EAAO,IAAI1D,EAAKD,GAAW,KAAK,QAAQ,CACvD,CACI,OAAOI,EAAQJ,EAAS,CACpB,OAAO2F,EAAQ,MAAMvF,EAAQJ,GAAW,KAAK,QAAQ,CAC7D,CACI,cAAc+H,EAAW,CAsDrB,OApDchP,EAAA,CAACkH,EAAKD,IAAY,CAC5B,IAAMgI,EAAU,CAAE,GAAGhI,CAAO,EACtB1G,EAAM,CAAE,GAAG,KAAK,SAAU,GAAG0O,CAAO,EACpCC,EAAa,KAAK,QAAQ,CAAC,CAAC3O,EAAI,OAAQ,CAAC,CAACA,EAAI,KAAK,EAEzD,GAAI,KAAK,SAAS,QAAU,IAAQ0O,EAAQ,QAAU,GAClD,OAAOC,EAAW,IAAI,MAAM,oIAAoI,CAAC,EAGrK,GAAI,OAAOhI,EAAQ,KAAeA,IAAQ,KACtC,OAAOgI,EAAW,IAAI,MAAM,gDAAgD,CAAC,EAEjF,GAAI,OAAOhI,GAAQ,SACf,OAAOgI,EAAW,IAAI,MAAM,wCACtB,OAAO,UAAU,SAAS,KAAKhI,CAAG,EAAI,mBAAmB,CAAC,EAEhE3G,EAAI,QACJA,EAAI,MAAM,QAAUA,EACpBA,EAAI,MAAM,MAAQyO,GAEtB,IAAM3I,EAAQ9F,EAAI,MAAQA,EAAI,MAAM,aAAY,EAAMyO,EAAYpE,EAAO,IAAMA,EAAO,UAChFuE,EAAS5O,EAAI,MAAQA,EAAI,MAAM,cAAa,EAAMyO,EAAYpC,EAAQ,MAAQA,EAAQ,YAC5F,GAAIrM,EAAI,MACJ,OAAO,QAAQ,QAAQA,EAAI,MAAQA,EAAI,MAAM,WAAW2G,CAAG,EAAIA,CAAG,EAC7D,KAAKA,GAAOb,EAAMa,EAAK3G,CAAG,CAAC,EAC3B,KAAK8G,GAAU9G,EAAI,MAAQA,EAAI,MAAM,iBAAiB8G,CAAM,EAAIA,CAAM,EACtE,KAAKA,GAAU9G,EAAI,WAAa,QAAQ,IAAI,KAAK,WAAW8G,EAAQ9G,EAAI,UAAU,CAAC,EAAE,KAAK,IAAM8G,CAAM,EAAIA,CAAM,EAChH,KAAKA,GAAU8H,EAAO9H,EAAQ9G,CAAG,CAAC,EAClC,KAAKyB,GAAQzB,EAAI,MAAQA,EAAI,MAAM,YAAYyB,CAAI,EAAIA,CAAI,EAC3D,MAAMkN,CAAU,EAEzB,GAAI,CACI3O,EAAI,QACJ2G,EAAM3G,EAAI,MAAM,WAAW2G,CAAG,GAElC,IAAIG,EAAShB,EAAMa,EAAK3G,CAAG,EACvBA,EAAI,QACJ8G,EAAS9G,EAAI,MAAM,iBAAiB8G,CAAM,GAE1C9G,EAAI,YACJ,KAAK,WAAW8G,EAAQ9G,EAAI,UAAU,EAE1C,IAAIyB,EAAOmN,EAAO9H,EAAQ9G,CAAG,EAC7B,OAAIA,EAAI,QACJyB,EAAOzB,EAAI,MAAM,YAAYyB,CAAI,GAE9BA,CACvB,OACmBoN,EAAG,CACN,OAAOF,EAAWE,CAAC,CACnC,CACA,EAnDsB,QAqDtB,CACI,QAAQC,EAAQC,EAAO,CACnB,OAAQF,GAAM,CAEV,GADAA,EAAE,SAAW;2DACTC,EAAQ,CACR,IAAME,EAAM,iCACNhN,EAAO6M,EAAE,QAAU,GAAI,EAAI,EAC3B,SACN,OAAIE,EACO,QAAQ,QAAQC,CAAG,EAEvBA,CACvB,CACY,GAAID,EACA,OAAO,QAAQ,OAAOF,CAAC,EAE3B,MAAMA,CAClB,CACA,CACA,ECtTMI,EAAiB,IAAInC,EACpB,SAASoC,EAAOvI,EAAK3G,EAAK,CAC7B,OAAOiP,EAAe,MAAMtI,EAAK3G,CAAG,CACxC,CAFgBP,EAAAyP,EAAA,UAQhBA,EAAO,QACHA,EAAO,WAAa,SAAUxI,EAAS,CACnC,OAAAuI,EAAe,WAAWvI,CAAO,EACjCwI,EAAO,SAAWD,EAAe,SACjCtP,GAAeuP,EAAO,QAAQ,EACvBA,CACf,EAIAA,EAAO,YAAc1P,EACrB0P,EAAO,SAAWxP,EAIlBwP,EAAO,IAAM,YAAanC,EAAM,CAC5B,OAAAkC,EAAe,IAAI,GAAGlC,CAAI,EAC1BmC,EAAO,SAAWD,EAAe,SACjCtP,GAAeuP,EAAO,QAAQ,EACvBA,CACX,EAIAA,EAAO,WAAa,SAAUpI,EAAQkG,EAAU,CAC5C,OAAOiC,EAAe,WAAWnI,EAAQkG,CAAQ,CACrD,EAQAkC,EAAO,YAAcD,EAAe,YAIpCC,EAAO,OAAS7C,EAChB6C,EAAO,OAAS7C,EAAQ,MACxB6C,EAAO,SAAWjE,EAClBiE,EAAO,aAAe9C,EACtB8C,EAAO,MAAQ7E,EACf6E,EAAO,MAAQ7E,EAAO,IACtB6E,EAAO,UAAYzI,EACnByI,EAAO,MAAQtC,EACfsC,EAAO,MAAQA,EACH,IAACxI,GAAUwI,EAAO,QACjBC,GAAaD,EAAO,WACpBE,GAAMF,EAAO,IACbX,GAAaW,EAAO,WACpBG,GAAcH,EAAO,YAEtB,IAACI,GAASC,EAAQ,MACjBC,GAAQC,EAAO,IC7D5B,SAASC,GAAmBC,EAAkB,CAAE,iBAAAC,CAAiB,EAA0B,CAIzF,IAAMC,EAFYF,EAAS,QAAQ,UAAW;AAAA,CAAI,EAER,QAAQ,UAAW;AAAA,CAAI,EAE3DG,EAAqBC,GAAOF,CAAuB,EACzD,OAAID,IAAqB,GAChBE,EAAmB,QAAQ,KAAM,QAAQ,EAE3CA,CACT,CAXSE,EAAAN,GAAA,sBAgBF,SAASO,GAAgBN,EAAkBO,EAAwB,CAAC,EAAmB,CAC5F,IAAMC,EAAuBT,GAAmBC,EAAUO,CAAM,EAC1DE,EAAQC,EAAO,MAAMF,CAAoB,EACzCG,EAAwB,CAAC,CAAC,CAAC,EAC7BC,EAAc,EAElB,SAASC,EAAYC,EAAmBC,EAA+B,SAAU,CAC3ED,EAAK,OAAS,OACEA,EAAK,KAAK,MAAM;AAAA,CAAI,EAC5B,QAAQ,CAACE,EAAUC,IAAU,CACjCA,IAAU,IACZL,IACAD,EAAM,KAAK,CAAC,CAAC,GAEfK,EAAS,MAAM,GAAG,EAAE,QAASE,GAAS,CACpCA,EAAOA,EAAK,QAAQ,SAAU,GAAG,EAC7BA,GACFP,EAAMC,CAAW,EAAE,KAAK,CAAE,QAASM,EAAM,KAAMH,CAAW,CAAC,CAE/D,CAAC,CACH,CAAC,EACQD,EAAK,OAAS,UAAYA,EAAK,OAAS,KACjDA,EAAK,OAAO,QAASK,GAAgB,CACnCN,EAAYM,EAA4BL,EAAK,IAAI,CACnD,CAAC,EACQA,EAAK,OAAS,QACvBH,EAAMC,CAAW,EAAE,KAAK,CAAE,QAASE,EAAK,KAAM,KAAM,QAAS,CAAC,CAElE,CAtBS,OAAAT,EAAAQ,EAAA,eAwBTJ,EAAM,QAASW,GAAa,CACtBA,EAAS,OAAS,YACpBA,EAAS,QAAQ,QAASD,GAAgB,CACxCN,EAAYM,CAA0B,CACxC,CAAC,EACQC,EAAS,OAAS,QAC3BT,EAAMC,CAAW,EAAE,KAAK,CAAE,QAASQ,EAAS,KAAM,KAAM,QAAS,CAAC,CAEtE,CAAC,EAEMT,CACT,CAzCgBN,EAAAC,GAAA,mBA2CT,SAASe,GAAerB,EAAkB,CAAE,iBAAAC,CAAiB,EAAmB,CAAC,EAAG,CACzF,IAAMQ,EAAQC,EAAO,MAAMV,CAAQ,EAEnC,SAASsB,EAAOR,EAAqB,CACnC,OAAIA,EAAK,OAAS,OACZb,IAAqB,GAChBa,EAAK,KAAK,QAAQ,QAAS,OAAO,EAAE,QAAQ,KAAM,QAAQ,EAE5DA,EAAK,KAAK,QAAQ,QAAS,OAAO,EAChCA,EAAK,OAAS,SAChB,WAAWA,EAAK,QAAQ,IAAIQ,CAAM,EAAE,KAAK,EAAE,CAAC,YAC1CR,EAAK,OAAS,KAChB,OAAOA,EAAK,QAAQ,IAAIQ,CAAM,EAAE,KAAK,EAAE,CAAC,QACtCR,EAAK,OAAS,YAChB,MAAMA,EAAK,QAAQ,IAAIQ,CAAM,EAAE,KAAK,EAAE,CAAC,OACrCR,EAAK,OAAS,QAChB,GACEA,EAAK,OAAS,OAChB,GAAGA,EAAK,IAAI,GACVA,EAAK,OAAS,SAChBA,EAAK,KAEP,yBAAyBA,EAAK,IAAI,EAC3C,CApBS,OAAAT,EAAAiB,EAAA,UAsBFb,EAAM,IAAIa,CAAM,EAAE,KAAK,EAAE,CAClC,CA1BgBjB,EAAAgB,GAAA,kBChET,SAASE,GAAiBC,EAAwB,CACvD,OAAI,KAAK,UACA,CAAC,GAAG,IAAI,KAAK,UAAU,EAAE,QAAQA,CAAI,CAAC,EAAE,IAAKC,GAAMA,EAAE,OAAO,EAE9D,CAAC,GAAGD,CAAI,CACjB,CALgBE,EAAAH,GAAA,oBAgCT,SAASI,GACdC,EACAC,EAC8B,CAC9B,IAAMC,EAAaC,GAAiBF,EAAK,OAAO,EAChD,OAAOG,GAA6BJ,EAAU,CAAC,EAAGE,EAAYD,EAAK,IAAI,CACzE,CANgBI,EAAAN,GAAA,uBAQhB,SAASK,GACPJ,EACAM,EACAC,EACAC,EAC8B,CAC9B,GAAID,EAAe,SAAW,EAC5B,MAAO,CACL,CAAE,QAASD,EAAU,KAAK,EAAE,EAAG,KAAAE,CAAK,EACpC,CAAE,QAAS,GAAI,KAAAA,CAAK,CACtB,EAEF,GAAM,CAACC,EAAU,GAAGC,CAAI,EAAIH,EACtBI,EAAU,CAAC,GAAGL,EAAWG,CAAQ,EACvC,OAAIT,EAAS,CAAC,CAAE,QAASW,EAAQ,KAAK,EAAE,EAAG,KAAAH,CAAK,CAAC,CAAC,EACzCJ,GAA6BJ,EAAUW,EAASD,EAAMF,CAAI,GAE/DF,EAAU,SAAW,GAAKG,IAE5BH,EAAU,KAAKG,CAAQ,EACvBF,EAAe,MAAM,GAEhB,CACL,CAAE,QAASD,EAAU,KAAK,EAAE,EAAG,KAAAE,CAAK,EACpC,CAAE,QAASD,EAAe,KAAK,EAAE,EAAG,KAAAC,CAAK,CAC3C,EACF,CA1BSH,EAAAD,GAAA,gCAkCF,SAASQ,GACdC,EACAb,EACgB,CAChB,GAAIa,EAAK,KAAK,CAAC,CAAE,QAAAC,CAAQ,IAAMA,EAAQ,SAAS;AAAA,CAAI,CAAC,EACnD,MAAM,IAAI,MAAM,2DAA2D,EAE7E,OAAOC,EAA6BF,EAAMb,CAAQ,CACpD,CARgBK,EAAAO,GAAA,uBAUhB,SAASG,EACPC,EACAhB,EACAiB,EAAwB,CAAC,EACzBC,EAAwB,CAAC,EACT,CAEhB,GAAIF,EAAM,SAAW,EAEnB,OAAIE,EAAQ,OAAS,GACnBD,EAAM,KAAKC,CAAO,EAEbD,EAAM,OAAS,EAAIA,EAAQ,CAAC,EAErC,IAAIE,EAAS,GACTH,EAAM,CAAC,EAAE,UAAY,MACvBG,EAAS,IACTH,EAAM,MAAM,GAEd,IAAMI,EAAyBJ,EAAM,MAAM,GAAK,CAAE,QAAS,IAAK,KAAM,QAAS,EACzEK,EAAiC,CAAC,GAAGH,CAAO,EAMlD,GALIC,IAAW,IACbE,EAAiB,KAAK,CAAE,QAASF,EAAQ,KAAM,QAAS,CAAC,EAE3DE,EAAiB,KAAKD,CAAQ,EAE1BpB,EAASqB,CAAgB,EAE3B,OAAON,EAA6BC,EAAOhB,EAAUiB,EAAOI,CAAgB,EAI9E,GAAIH,EAAQ,OAAS,EAEnBD,EAAM,KAAKC,CAAO,EAClBF,EAAM,QAAQI,CAAQ,UACbA,EAAS,QAAS,CAE3B,GAAM,CAACP,EAAMH,CAAI,EAAIX,GAAoBC,EAAUoB,CAAQ,EAC3DH,EAAM,KAAK,CAACJ,CAAI,CAAC,EACbH,EAAK,SACPM,EAAM,QAAQN,CAAI,CAEtB,CACA,OAAOK,EAA6BC,EAAOhB,EAAUiB,CAAK,CAC5D,CA7CSZ,EAAAU,EAAA,gCC3ET,SAASO,GAAWC,EAAKC,EAAS,CAC5BA,GACFD,EAAI,KAAK,QAASC,CAAO,CAE7B,CAJSC,EAAAH,GAAA,cAMT,eAAeI,GAAYC,EAASC,EAAMC,EAAOC,EAASC,EAAgB,GAAO,CAC/E,IAAMC,EAAKL,EAAQ,OAAO,eAAe,EAGzCK,EAAG,KAAK,QAAS,GAAG,GAAKH,CAAK,IAAI,EAClCG,EAAG,KAAK,SAAU,GAAG,GAAKH,CAAK,IAAI,EAEnC,IAAMI,EAAMD,EAAG,OAAO,WAAW,EAC7BE,EAAQN,EAAK,MACbA,EAAK,OAASO,EAASP,EAAK,KAAK,IACnCM,EAAQ,MAAME,GAAYR,EAAK,MAAM,QAAQS,GAAO,eAAgB;AAAA,CAAI,EAAGC,GAAU,CAAC,GAExF,IAAMC,EAAaX,EAAK,OAAS,YAAc,YACzCY,EAAOP,EAAI,OAAO,MAAM,EAC9BO,EAAK,KAAKN,CAAK,EACfZ,GAAWkB,EAAMZ,EAAK,UAAU,EAChCY,EAAK,KAAK,QAAS,GAAGD,CAAU,IAAIT,CAAO,EAAE,EAE7CR,GAAWW,EAAKL,EAAK,UAAU,EAC/BK,EAAI,MAAM,UAAW,YAAY,EACjCA,EAAI,MAAM,cAAe,QAAQ,EACjCA,EAAI,MAAM,cAAe,KAAK,EAC9BA,EAAI,MAAM,YAAaJ,EAAQ,IAAI,EACnCI,EAAI,MAAM,aAAc,QAAQ,EAChCA,EAAI,KAAK,QAAS,8BAA8B,EAC5CF,GACFE,EAAI,KAAK,QAAS,UAAU,EAG9B,IAAIQ,EAAOR,EAAI,KAAK,EAAE,sBAAsB,EAC5C,OAAIQ,EAAK,QAAUZ,IACjBI,EAAI,MAAM,UAAW,OAAO,EAC5BA,EAAI,MAAM,cAAe,cAAc,EACvCA,EAAI,MAAM,QAASJ,EAAQ,IAAI,EAC/BY,EAAOR,EAAI,KAAK,EAAE,sBAAsB,GAMnCD,EAAG,KAAK,CACjB,CAzCeP,EAAAC,GAAA,eAmDf,SAASgB,GAAYC,EAAkBC,EAAmBC,EAAoB,CAC5E,OAAOF,EACJ,OAAO,OAAO,EACd,KAAK,QAAS,kBAAkB,EAChC,KAAK,IAAK,CAAC,EACX,KAAK,IAAKC,EAAYC,EAAa,GAAM,IAAI,EAC7C,KAAK,KAAMA,EAAa,IAAI,CACjC,CAPSpB,EAAAiB,GAAA,eAST,SAASI,GAAmBC,EAAiBF,EAAoBG,EAA4B,CAC3F,IAAMC,EAAcF,EAAW,OAAO,MAAM,EACtCG,EAAWR,GAAYO,EAAa,EAAGJ,CAAU,EACvDM,GAA2BD,EAAUF,CAAI,EACzC,IAAMI,EAAaF,EAAS,KAAK,EAAE,sBAAsB,EACzD,OAAAD,EAAY,OAAO,EACZG,CACT,CAPS3B,EAAAqB,GAAA,sBASF,SAASO,GACdN,EACAF,EACAS,EACqB,CACrB,IAAML,EAA6BF,EAAW,OAAO,MAAM,EACrDG,EAA2BR,GAAYO,EAAa,EAAGJ,CAAU,EACvEM,GAA2BD,EAAU,CAAC,CAAE,QAASI,EAAM,KAAM,QAAS,CAAC,CAAC,EACxE,IAAMC,EAAqCL,EAAS,KAAK,GAAG,sBAAsB,EAClF,OAAIK,GACFN,EAAY,OAAO,EAEdM,CACT,CAbgB9B,EAAA4B,GAAA,0BAwBhB,SAASG,GACP3B,EACA4B,EACAC,EACA3B,EAAgB,GAChB,CAEA,IAAM4B,EAAaF,EAAE,OAAO,GAAG,EACzBG,EAAMD,EAAW,OAAO,MAAM,EAAE,KAAK,QAAS,YAAY,EAAE,KAAK,QAAS,cAAc,EACxFhB,EAAcgB,EAAW,OAAO,MAAM,EAAE,KAAK,IAAK,OAAO,EAC3Df,EAAY,EAChB,QAAWI,KAAQU,EAAgB,CAKjC,IAAMG,EAAapC,EAACuB,GAClBF,GAAmBa,EAAY,IAAYX,CAAI,GAAKnB,EADnC,cAEbiC,EAAkBD,EAAWb,CAAI,EAAI,CAACA,CAAI,EAAIe,GAAoBf,EAAMa,CAAU,EAExF,QAAWG,KAAgBF,EAAiB,CAC1C,IAAMG,EAAQvB,GAAYC,EAAaC,EAAW,GAAU,EAC5DO,GAA2Bc,EAAOD,CAAY,EAC9CpB,GACF,CACF,CACA,GAAIb,EAAe,CACjB,IAAMU,EAAOE,EAAY,KAAK,EAAE,QAAQ,EAClCuB,EAAU,EAChB,OAAAN,EACG,KAAK,IAAKnB,EAAK,EAAIyB,CAAO,EAC1B,KAAK,IAAKzB,EAAK,EAAIyB,CAAO,EAC1B,KAAK,QAASzB,EAAK,MAAQ,EAAIyB,CAAO,EACtC,KAAK,SAAUzB,EAAK,OAAS,EAAIyB,CAAO,EAEpCP,EAAW,KAAK,CACzB,KACE,QAAOhB,EAAY,KAAK,CAE5B,CAvCSlB,EAAA+B,GAAA,uBAgDT,SAASL,GAA2Bc,EAAYE,EAA6B,CAC3EF,EAAM,KAAK,EAAE,EAEbE,EAAY,QAAQ,CAACC,EAAMC,IAAU,CACnC,IAAMC,EAAaL,EAChB,OAAO,OAAO,EACd,KAAK,aAAcG,EAAK,OAAS,KAAO,SAAW,QAAQ,EAC3D,KAAK,QAAS,kBAAkB,EAChC,KAAK,cAAeA,EAAK,OAAS,SAAW,OAAS,QAAQ,EAC7DC,IAAU,EACZC,EAAW,KAAKF,EAAK,OAAO,EAG5BE,EAAW,KAAK,IAAMF,EAAK,OAAO,CAEtC,CAAC,CACH,CAhBS3C,EAAA0B,GAAA,8BAuBF,SAASoB,GAAqBjB,EAAc,CAEjD,OAAOA,EAAK,QACV,wBACCkB,GAAM,aAAaA,EAAE,QAAQ,IAAK,GAAG,CAAC,QACzC,CACF,CANgB/C,EAAA8C,GAAA,wBAUT,IAAME,GAAahD,EAAA,MACxBiD,EACApB,EAAO,GACP,CACE,MAAAqB,EAAQ,GACR,QAAAC,EAAU,GACV,QAAA9C,EAAU,GACV,cAAA+C,EAAgB,GAChB,OAAAC,EAAS,GACT,MAAAjD,EAAQ,IACR,iBAAAkD,EAAmB,EACrB,EAAI,CAAC,EACLC,IACG,CAYH,GAXAC,GAAI,MACF,iBACA3B,EACAqB,EACAC,EACA9C,EACA+C,EACAC,EACA,qBACAC,CACF,EACIF,EAAe,CAGjB,IAAMK,EAAWC,GAAe7B,EAAM0B,CAAM,EACtCI,EAAsBb,GAAqBc,GAAeH,CAAQ,CAAC,EAGnEI,EAAgBhC,EAAK,QAAQ,QAAS,IAAI,EAE1C1B,EAAO,CACX,OAAAkD,EACA,MAAO3C,EAASmB,CAAI,EAAIgC,EAAgBF,EACxC,WAAYT,EAAM,QAAQ,QAAS,QAAQ,CAC7C,EAEA,OADmB,MAAMjD,GAAYgD,EAAI9C,EAAMC,EAAOC,EAASiD,CAAgB,CAEjF,KAAO,CAEL,IAAMQ,EAAajC,EAAK,QAAQ,cAAe,OAAO,EAChDI,EAAiB8B,GAAgBD,EAAW,QAAQ,OAAQ,OAAO,EAAGP,CAAM,EAC5ES,EAAWjC,GACf3B,EACA6C,EACAhB,EACAJ,EAAOyB,EAAmB,EAC5B,EACA,GAAID,EAAQ,CACN,UAAU,KAAKH,CAAK,IACtBA,EAAQA,EAAM,QAAQ,UAAW,YAAY,GAG/C,IAAMe,EAAqBf,EACxB,QAAQ,kBAAmB,EAAE,EAC7B,QAAQ,wBAAyB,EAAE,EACnC,QAAQ,gBAAiB,EAAE,EAC3B,QAAQ,UAAW,OAAO,EAC7BgB,EAAOF,CAAQ,EAAE,KAAK,QAASC,CAAkB,CAEnD,KAAO,CAKL,IAAME,EAAqBjB,EACxB,QAAQ,kBAAmB,EAAE,EAC7B,QAAQ,wBAAyB,EAAE,EACnC,QAAQ,gBAAiB,EAAE,EAC3B,QAAQ,eAAgB,OAAO,EAClCgB,EAAOF,CAAQ,EACZ,OAAO,MAAM,EACb,KAAK,QAASG,EAAmB,QAAQ,eAAgB,OAAO,CAAC,EAGpE,IAAMC,EAAqBlB,EACxB,QAAQ,kBAAmB,EAAE,EAC7B,QAAQ,wBAAyB,EAAE,EACnC,QAAQ,gBAAiB,EAAE,EAC3B,QAAQ,UAAW,OAAO,EAC7BgB,EAAOF,CAAQ,EAAE,OAAO,MAAM,EAAE,KAAK,QAASI,CAAkB,CAClE,CACA,OAAOJ,CACT,CACF,EAvF0B", "names": ["dedent", "templ", "values", "_i", "strings", "indentLengths", "arr", "str", "matches", "match", "_a", "_b", "pattern_1", "string", "value", "i", "endentations", "endentation", "indentedValue", "__name", "_getDefaults", "__name", "_defaults", "changeDefaults", "newDefaults", "noopTest", "edit", "regex", "opt", "source", "obj", "name", "val", "valSource", "other", "bull", "indent", "newline", "blockCode", "fences", "hr", "heading", "bullet", "lheadingCore", "lheading", "lheadingGfm", "_paragraph", "blockText", "_blockLabel", "def", "list", "_tag", "_comment", "html", "paragraph", "blockquote", "blockNormal", "gfmTable", "blockGfm", "blockPedantic", "escape", "inlineCode", "br", "inlineText", "_punctuation", "_punctuationOrSpace", "_notPunctuationOrSpace", "punctuation", "_punctuationGfmStrongEm", "_punctuationOrSpaceGfmStrongEm", "_notPunctuationOrSpaceGfmStrongEm", "blockSkip", "emStrongLDelimCore", "em<PERSON><PERSON>g<PERSON><PERSON><PERSON>", "emStrongLDelimGfm", "emStrongRDelimAstCore", "emStrongRDelim<PERSON>t", "emStrongRDelimAstGfm", "emStrongRDelimUnd", "anyPunctuation", "autolink", "_inlineComment", "tag", "_inlineLabel", "link", "reflink", "nolink", "reflinkSearch", "inlineNormal", "inlinePedantic", "inlineGfm", "inlineBreaks", "block", "inline", "escapeReplacements", "getEscapeReplacement", "ch", "encode", "cleanUrl", "href", "splitCells", "tableRow", "count", "row", "match", "offset", "str", "escaped", "curr", "cells", "i", "rtrim", "c", "invert", "l", "suffLen", "findClosingBracket", "b", "level", "outputLink", "cap", "raw", "lexer", "rules", "title", "text", "token", "indentCodeCompensation", "matchIndentToCode", "indentToCode", "node", "matchIndentInNode", "indentInNode", "_Tokenizer", "options", "src", "trimmed", "lines", "tokens", "inBlockquote", "currentLines", "currentRaw", "currentText", "top", "lastToken", "oldToken", "newText", "newToken", "isordered", "itemRegex", "endsWithBlankLine", "endEarly", "itemContents", "line", "t", "nextLine", "blankLine", "nextBulletRegex", "hrRegex", "fencesBeginRegex", "headingBeginRegex", "htmlBeginRegex", "rawLine", "nextLineWithoutTabs", "istask", "ischecked", "lastItem", "spacers", "hasMultipleLineBreaks", "headers", "aligns", "rows", "item", "align", "cell", "trimmedUrl", "rtrimSlash", "lastParenIndex", "linkLen", "links", "linkString", "maskedSrc", "prevChar", "l<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "r<PERSON><PERSON><PERSON>", "delimTotal", "midDelimTotal", "endReg", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasNonSpaceChars", "hasSpaceCharsOnBothEnds", "prevCapZero", "_<PERSON>er", "__<PERSON><PERSON>", "next", "lastParagraphClipped", "extTokenizer", "cutSrc", "startIndex", "tempSrc", "tempStart", "getStartIndex", "errMsg", "keepPrevChar", "_Renderer", "lang", "langString", "code", "depth", "ordered", "start", "body", "j", "type", "startAttr", "itemBody", "checkbox", "checked", "header", "k", "content", "cleanHref", "out", "_<PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON>r", "__<PERSON><PERSON><PERSON>", "anyToken", "genericToken", "ret", "textToken", "renderer", "_Hooks", "markdown", "Marked", "args", "callback", "values", "tableToken", "listToken", "childTokens", "extensions", "pack", "opts", "ext", "prev<PERSON><PERSON><PERSON>", "extLevel", "prop", "rendererProp", "rendererFunc", "tokenizer", "tokenizerProp", "tokenizerFunc", "prevTokenizer", "hooks", "hooksProp", "hooksFunc", "prevHook", "arg", "walkTokens", "packWalktokens", "blockType", "origOpt", "throwError", "parser", "e", "silent", "async", "msg", "markedInstance", "marked", "setOptions", "use", "parseInline", "parser", "_<PERSON><PERSON>r", "lexer", "_<PERSON>er", "preprocessMarkdown", "markdown", "markdownAutoWrap", "withoutMultipleNewlines", "withoutExtraSpaces", "dedent", "__name", "markdownToLines", "config", "preprocessedMarkdown", "nodes", "marked", "lines", "currentLine", "processNode", "node", "parentType", "textLine", "index", "word", "contentNode", "treeNode", "markdownToHTML", "output", "splitTextToChars", "text", "s", "__name", "splitWordToFitWidth", "checkFit", "word", "characters", "splitTextToChars", "splitWordToFitWidthRecursion", "__name", "usedChars", "remainingChars", "type", "nextChar", "rest", "newWord", "splitLineToFitWidth", "line", "content", "splitLineToFitWidthRecursion", "words", "lines", "newLine", "joiner", "nextWord", "lineWithNextWord", "applyStyle", "dom", "styleFn", "__name", "addHtmlSpan", "element", "node", "width", "classes", "addBackground", "fo", "div", "label", "hasKatex", "renderKatex", "common_default", "getConfig", "labelClass", "span", "bbox", "createTspan", "textElement", "lineIndex", "lineHeight", "computeWidthOfText", "parentNode", "line", "testElement", "testSpan", "updateTextContentAndStyles", "textLength", "computeDimensionOfText", "text", "textDimension", "createFormattedText", "g", "structuredText", "labelGroup", "bkg", "checkWidth", "linesUnderWidth", "splitLineToFitWidth", "preparedLine", "tspan", "padding", "wrappedLine", "word", "index", "innerTspan", "replaceIconSubstring", "s", "createText", "el", "style", "isTitle", "useHtmlLabels", "isNode", "addSvgBackground", "config", "log", "htmlText", "markdownToHTML", "decodedReplacedText", "decodeEntities", "inputForKatex", "sanitizeBR", "markdownToLines", "svgLabel", "nodeLabelTextStyle", "select_default", "edgeLabelRectStyle", "edgeLabelTextStyle"]}