{"version": 3, "file": "fuzzy-matcher.js", "sourceRoot": "", "sources": ["../../src/lsp/fuzzy-matcher.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAmBhF,MAAM,OAAO,mBAAmB;IAE5B,KAAK,CAAC,KAAa,EAAE,IAAY;QAC7B,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,qBAAqB,GAAG,KAAK,CAAC;QAClC,IAAI,QAA4B,CAAC;QACjC,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;QACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAC7C,IAAI,OAAO,KAAK,QAAQ,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,KAAK,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC3F,qBAAqB,KAArB,qBAAqB,GACjB,QAAQ,KAAK,SAAS,IAAI,oBAAoB;oBAC9C,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAC;gBAC7C,IAAI,qBAAqB,EAAE,CAAC;oBACxB,SAAS,EAAE,CAAC;gBAChB,CAAC;gBACD,IAAI,SAAS,KAAK,KAAK,CAAC,MAAM,EAAE,CAAC;oBAC7B,OAAO,IAAI,CAAC;gBAChB,CAAC;YACL,CAAC;YACD,QAAQ,GAAG,OAAO,CAAC;QACvB,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAES,gBAAgB,CAAC,QAAgB,EAAE,OAAe;QACxD,OAAO,CAAC,IAAI,QAAQ,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,IAAI,OAAO,IAAI,CAAC,IAAI,uBAAuB;YAC5F,QAAQ,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,wBAAwB;IACjE,CAAC;IAES,eAAe,CAAC,QAAgB;QACtC,IAAI,CAAC,IAAI,QAAQ,IAAI,QAAQ,IAAI,CAAC,EAAE,CAAC;YACjC,OAAO,QAAQ,GAAG,EAAE,CAAC;QACzB,CAAC;QACD,OAAO,QAAQ,CAAC;IACpB,CAAC;CACJ;AAED,MAAM,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC5B,MAAM,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC5B,MAAM,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC5B,MAAM,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC5B,MAAM,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC"}