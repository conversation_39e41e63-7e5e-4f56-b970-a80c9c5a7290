{"name": "@chevrotain/utils", "version": "11.0.3", "description": "common utilities", "keywords": [], "bugs": {"url": "https://github.com/Chevrotain/chevrotain/issues"}, "license": "Apache-2.0", "author": {"name": "<PERSON><PERSON>"}, "type": "module", "types": "./lib/src/api.d.ts", "exports": {".": {"import": "./lib/src/api.js", "types": "./lib/src/api.d.ts"}}, "files": ["lib/src/**/*.js", "lib/src/**/*.map", "lib/src/**/*.d.ts", "src/**/*.ts"], "repository": {"type": "git", "url": "git://github.com/Chevrotain/chevrotain.git"}, "scripts": {"---------- CI FLOWS --------": "", "ci": "npm-run-all build test", "build": "npm-run-all clean compile", "test": "npm-run-all coverage", "---------- BUILD STEPS --------": "", "clean": "shx rm -rf lib coverage", "compile:watch": "tsc -w", "compile": "tsc", "coverage": "c8 mocha --enable-source-maps"}, "publishConfig": {"access": "public"}, "gitHead": "ac5806631779035c2c1955744a47d8ed4f25a175"}