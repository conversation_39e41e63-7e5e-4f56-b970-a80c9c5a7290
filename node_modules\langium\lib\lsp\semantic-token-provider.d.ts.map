{"version": 3, "file": "semantic-token-provider.d.ts", "sourceRoot": "", "sources": ["../../src/lsp/semantic-token-provider.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAIhF,OAAO,KAAK,EAAE,KAAK,EAAE,cAAc,EAAE,gCAAgC,EAAE,mBAAmB,EAAE,yBAAyB,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,yBAAyB,EAAE,MAAM,uBAAuB,CAAC;AAC7N,OAAO,EAAE,qBAAqB,IAAI,yBAAyB,EAA0B,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AACvI,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAC7D,OAAO,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAItE,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AAE9D,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,2BAA2B,CAAC;AACjE,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEzD,eAAO,MAAM,qBAAqB,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAwBxD,CAAC;AAEF,eAAO,MAAM,yBAAyB,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAW5D,CAAC;AAEF,eAAO,MAAM,2BAA2B,EAAE,qBASzC,CAAC;AAEF,MAAM,WAAW,qBAAqB;IAClC,iBAAiB,CAAC,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,oBAAoB,EAAE,WAAW,CAAC,EAAE,iBAAiB,GAAG,YAAY,CAAC,cAAc,CAAC,CAAA;IACzI,sBAAsB,CAAC,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,yBAAyB,EAAE,WAAW,CAAC,EAAE,iBAAiB,GAAG,YAAY,CAAC,cAAc,CAAC,CAAA;IACnJ,sBAAsB,CAAC,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,yBAAyB,EAAE,WAAW,CAAC,EAAE,iBAAiB,GAAG,YAAY,CAAC,cAAc,GAAG,mBAAmB,CAAC,CAAA;IACzK,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IAC3C,QAAQ,CAAC,cAAc,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IAC/C,QAAQ,CAAC,qBAAqB,EAAE,qBAAqB,CAAA;CACxD;AAED,wBAAgB,iCAAiC,CAAC,OAAO,EAAE,KAAK,CAAC,qBAAqB,GAAG,SAAS,CAAC,GAAG,qBAAqB,CA2C1H;AAED,MAAM,WAAW,aAAa;IAC1B,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,EAAE,MAAM,CAAA;IACZ,MAAM,EAAE,MAAM,CAAA;IACd,SAAS,EAAE,MAAM,CAAA;IACjB,cAAc,EAAE,MAAM,CAAA;CACzB;AAED,MAAM,MAAM,4BAA4B,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,IAAI,CAAC;IACrE,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;CAClB,GAAG;IACA,IAAI,EAAE,CAAC,CAAC;IACR,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;IACxB,KAAK,CAAC,EAAE,MAAM,CAAC;CAClB,GAAG;IACA,IAAI,EAAE,CAAC,CAAC;IACR,OAAO,EAAE,MAAM,CAAC;IAChB,KAAK,CAAC,EAAE,MAAM,CAAC;CAClB,GAAG;IACA,GAAG,EAAE,OAAO,CAAC;CAChB,GAAG;IACA,KAAK,EAAE,KAAK,CAAC;CAChB,CAAC,GAAG;IACD,IAAI,EAAE,MAAM,CAAC;IACb,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;CAChC,CAAA;AAED,MAAM,WAAW,4BAA4B,CAAC,CAAC,SAAS,OAAO;IAC3D,IAAI,EAAE,CAAC,CAAC;IACR,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;IACxB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,IAAI,EAAE,MAAM,CAAC;IACb,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;CAChC;AAED,MAAM,WAAW,2BAA2B;IACxC,IAAI,EAAE,OAAO,CAAC;IACd,OAAO,EAAE,MAAM,CAAC;IAChB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,IAAI,EAAE,MAAM,CAAC;IACb,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;CAChC;AAED,MAAM,WAAW,wBAAwB;IACrC,IAAI,EAAE,OAAO,CAAC;IACd,IAAI,EAAE,MAAM,CAAC;IACb,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;CAChC;AAED,MAAM,WAAW,yBAAyB;IACtC,KAAK,EAAE,KAAK,CAAA;IACZ,IAAI,EAAE,MAAM,CAAA;IACZ,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,CAAA;CAC/B;AAED,qBAAa,qBAAsB,SAAQ,yBAAyB;IAChE,OAAO,CAAC,OAAO,CAAuB;IAE7B,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,GAAG,IAAI;IAUjG,KAAK,IAAI,cAAc;IAKvB,UAAU,IAAI,cAAc,GAAG,mBAAmB;IAK3D;;OAEG;IACH,KAAK,IAAI,IAAI;IAIb,OAAO,CAAC,WAAW;IAOnB,OAAO,CAAC,aAAa;CAMxB;AAED,MAAM,MAAM,qBAAqB,GAAG,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,OAAO,EAAE,4BAA4B,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;AAEpH;;;;;GAKG;AACH,8BAAsB,6BAA8B,YAAW,qBAAqB;IAEhF;;OAEG;IACH,SAAS,CAAC,cAAc,qCAA4C;IACpE,SAAS,CAAC,eAAe,CAAC,EAAE,eAAe,CAAC;IAC5C,SAAS,CAAC,oBAAoB,CAAC,EAAE,qBAAqB,CAAC;IACvD,SAAS,CAAC,YAAY,CAAC,EAAE,KAAK,CAAC;IAC/B,SAAS,CAAC,kBAAkB,CAAC,EAAE,gCAAgC,CAAC;gBAEpD,QAAQ,EAAE,eAAe;IAUrC,UAAU,CAAC,kBAAkB,CAAC,EAAE,gCAAgC,GAAG,IAAI;IAIvE,IAAI,UAAU,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAEvC;IAED,IAAI,cAAc,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAE3C;IAED,IAAI,qBAAqB,IAAI,qBAAqB,CAWjD;IAEK,iBAAiB,CAAC,QAAQ,EAAE,eAAe,EAAE,OAAO,EAAE,oBAAoB,EAAE,WAAW,oBAAyB,GAAG,OAAO,CAAC,cAAc,CAAC;IAS1I,sBAAsB,CAAC,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,yBAAyB,EAAE,WAAW,oBAAyB,GAAG,OAAO,CAAC,cAAc,CAAC;IASnJ,sBAAsB,CAAC,QAAQ,EAAE,eAAe,EAAE,MAAM,EAAE,yBAAyB,EAAE,WAAW,oBAAyB,GAAG,OAAO,CAAC,cAAc,GAAG,mBAAmB,CAAC;IAS/K,SAAS,CAAC,cAAc,IAAI,qBAAqB;IAkCjD,SAAS,CAAC,wBAAwB,CAAC,QAAQ,EAAE,eAAe,GAAG,qBAAqB;cAUpE,mBAAmB,CAAC,QAAQ,EAAE,eAAe,EAAE,QAAQ,EAAE,qBAAqB,EAAE,WAAW,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;IAgB9I;;OAEG;IACH,SAAS,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,qBAAqB,GAAG,IAAI,GAAG,SAAS,GAAG,OAAO;IAE/G,SAAS,CAAC,cAAc,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI;IAuElE,SAAS,CAAC,iBAAiB,CAAC,CAAC,SAAS,OAAO,EAAE,OAAO,EAAE,4BAA4B,CAAC,CAAC,CAAC,GAAG,IAAI;IAoB9F,SAAS,CAAC,gBAAgB,CAAC,OAAO,EAAE,2BAA2B,GAAG,IAAI;IAoBtE,SAAS,CAAC,aAAa,CAAC,OAAO,EAAE,wBAAwB,GAAG,IAAI;CAUnE;AAED,yBAAiB,qBAAqB,CAAC;IACnC,UAAiB,oBAAoB;QACjC,MAAM,EAAE,MAAM,CAAC;QACf,SAAS,EAAE,kBAAkB,CAAC;QAC9B,cAAc,EAAE,MAAM,CAAC;QACvB,IAAI,EAAE,MAAM,CAAC;KAChB;IAED,SAAgB,MAAM,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC,GAAG,oBAAoB,EAAE,CAoBpK;CAUJ"}