export class Graph {
    constructor(opts?: {});
    _isDirected: any;
    _isMultigraph: any;
    _isCompound: any;
    _label: any;
    _defaultNodeLabelFn: () => any;
    _defaultEdgeLabelFn: () => any;
    _nodes: {};
    _parent: {};
    _children: {};
    _in: {};
    _preds: {};
    _out: {};
    _sucs: {};
    _edgeObjs: {};
    _edgeLabels: {};
    isDirected(): any;
    isMultigraph(): any;
    isCompound(): any;
    setGraph(label: any): this;
    graph(): any;
    setDefaultNodeLabel(newDefault: any): this;
    nodeCount(): number;
    nodes(): string[];
    sources(): string[];
    sinks(): string[];
    setNodes(vs: any, value: any, ...args: any[]): this;
    setNode(v: any, value: any, ...args: any[]): this;
    node(v: any): any;
    hasNode(v: any): any;
    removeNode(v: any): this;
    setParent(v: any, parent: any): this;
    _removeFromParentsChildList(v: any): void;
    parent(v: any): any;
    children(v: any): string[];
    predecessors(v: any): string[];
    successors(v: any): string[];
    neighbors(v: any): string[];
    isLeaf(v: any): boolean;
    filterNodes(filter: any): any;
    setDefaultEdgeLabel(newDefault: any): this;
    edgeCount(): number;
    edges(): any[];
    setPath(vs: any, value: any, ...args: any[]): this;
    setEdge(...args: any[]): this;
    edge(v: any, w: any, name: any, ...args: any[]): any;
    hasEdge(v: any, w: any, name: any, ...args: any[]): any;
    removeEdge(v: any, w: any, name: any, ...args: any[]): this;
    inEdges(v: any, u: any): any[];
    outEdges(v: any, w: any): any[];
    nodeEdges(v: any, w: any): any[];
    _nodeCount: number;
    _edgeCount: number;
}
