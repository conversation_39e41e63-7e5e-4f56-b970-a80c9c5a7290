{"name": "@braintree/sanitize-url", "version": "7.1.1", "description": "A url sanitizer", "main": "dist/index.js", "types": "dist/index.d.ts", "author": "", "scripts": {"prepublishOnly": "npm run build", "prebuild": "prettier --write .", "build": "tsc --declaration", "lint": "eslint --ext js,ts .", "posttest": "npm run lint", "test": "vitest", "coverage": "vitest run --coverage"}, "repository": {"type": "git", "url": "git+https://github.com/braintree/sanitize-url.git"}, "keywords": [], "license": "MIT", "bugs": {"url": "https://github.com/braintree/sanitize-url/issues"}, "homepage": "https://github.com/braintree/sanitize-url#readme", "devDependencies": {"@types/jest": "^29.4.0", "@typescript-eslint/eslint-plugin": "^5.54.1", "@vitest/coverage-v8": "^0.34.2", "chai": "^4.3.7", "eslint": "^8.36.0", "eslint-config-braintree": "^6.0.0-typescript-prep-rc.2", "eslint-plugin-prettier": "^4.2.1", "happy-dom": "^15.11.6", "prettier": "^2.8.4", "typescript": "^5.1.6", "vitest": "^0.34.2"}}