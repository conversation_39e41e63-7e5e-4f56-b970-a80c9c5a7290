{"name": "path-data-parser", "version": "0.1.0", "description": "Yet another SVG path parser. This one's tiny", "main": "lib/index.js", "module": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"build": "rm -rf lib && tsc", "lint": "tslint -p tsconfig.json", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/pshihn/path-data-parser.git"}, "keywords": ["svg path parser", "SVG", "path parser", "SVGPATH", "pathdata", "svg parser"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/pshihn/path-data-parser/issues"}, "homepage": "https://github.com/pshihn/path-data-parser#readme", "devDependencies": {"tslint": "^6.1.1", "typescript": "^3.8.3"}}