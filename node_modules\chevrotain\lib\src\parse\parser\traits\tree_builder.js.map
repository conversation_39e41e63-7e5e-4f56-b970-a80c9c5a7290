{"version": 3, "file": "tree_builder.js", "sourceRoot": "", "sources": ["../../../../../src/parse/parser/traits/tree_builder.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,oBAAoB,EACpB,gBAAgB,EAChB,mBAAmB,EACnB,yBAAyB,GAC1B,MAAM,kBAAkB,CAAC;AAC1B,OAAO,EAAE,GAAG,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AACzD,OAAO,EACL,oCAAoC,EACpC,wCAAwC,GACzC,MAAM,0BAA0B,CAAC;AAUlC,OAAO,EAAE,qBAAqB,EAAE,MAAM,cAAc,CAAC;AAErD;;GAEG;AACH,MAAM,OAAO,WAAW;IAoBtB,eAAe,CAAsB,MAAqB;QACxD,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QAEpB,0DAA0D;QAC1D,IAAI,CAAC,SAAS,GAAI,MAAc,CAAC,SAAS,CAAC;QAE3C,IAAI,CAAC,oBAAoB,GAAG,GAAG,CAAC,MAAM,EAAE,sBAAsB,CAAC;YAC7D,CAAC,CAAE,MAAM,CAAC,oBAAoD,CAAC,0DAA0D;YACzH,CAAC,CAAC,qBAAqB,CAAC,oBAAoB,CAAC;QAE/C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;YACrC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;YAClC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;SACzB;aAAM;YACL,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE;gBAC3C,IAAI,IAAI,CAAC,eAAe,EAAE;oBACxB,IAAI,CAAC,wBAAwB,GAAG,mBAAmB,CAAC;oBACpD,IAAI,CAAC,uBAAuB,GAAG,mBAAmB,CAAC;oBACnD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;oBACxB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,kCAAkC,CAAC;iBACvE;qBAAM;oBACL,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;oBACrC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;oBACpC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC;oBACxC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,iCAAiC,CAAC;iBACtE;aACF;iBAAM,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE;gBACxD,IAAI,IAAI,CAAC,eAAe,EAAE;oBACxB,IAAI,CAAC,wBAAwB,GAAQ,yBAAyB,CAAC;oBAC/D,IAAI,CAAC,uBAAuB,GAAQ,yBAAyB,CAAC;oBAC9D,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;oBACxB,IAAI,CAAC,sBAAsB;wBACzB,IAAI,CAAC,wCAAwC,CAAC;iBACjD;qBAAM;oBACL,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;oBACrC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;oBACpC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC;oBAC9C,IAAI,CAAC,sBAAsB;wBACzB,IAAI,CAAC,uCAAuC,CAAC;iBAChD;aACF;iBAAM,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE;gBAClD,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;gBACrC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;gBACpC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;aACpC;iBAAM;gBACL,MAAM,KAAK,CACT,kDAAkD,MAAM,CAAC,oBAAoB,GAAG,CACjF,CAAC;aACH;SACF;IACH,CAAC;IAED,wCAAwC,CAEtC,OAAY;QAEZ,OAAO,CAAC,QAAQ,GAAG;YACjB,WAAW,EAAE,GAAG;YAChB,SAAS,EAAE,GAAG;SACf,CAAC;IACJ,CAAC;IAED,uCAAuC,CAErC,OAAY;QAEZ,OAAO,CAAC,QAAQ,GAAG;YACjB,8EAA8E;YAC9E,yDAAyD;YACzD,oEAAoE;YACpE,6BAA6B;YAC7B,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,WAAW;YACnC,SAAS,EAAE,GAAG;SACf,CAAC;IACJ,CAAC;IAED,kCAAkC,CAAsB,OAAY;QAClE,OAAO,CAAC,QAAQ,GAAG;YACjB,WAAW,EAAE,GAAG;YAChB,SAAS,EAAE,GAAG;YACd,WAAW,EAAE,GAAG;YAChB,SAAS,EAAE,GAAG;YACd,OAAO,EAAE,GAAG;YACZ,SAAS,EAAE,GAAG;SACf,CAAC;IACJ,CAAC;IAED;;;;SAIK;IACL,iCAAiC,CAAsB,OAAY;QACjE,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC7B,OAAO,CAAC,QAAQ,GAAG;YACjB,WAAW,EAAE,SAAS,CAAC,WAAW;YAClC,SAAS,EAAE,SAAS,CAAC,SAAS;YAC9B,WAAW,EAAE,SAAS,CAAC,WAAW;YAClC,SAAS,EAAE,GAAG;YACd,OAAO,EAAE,GAAG;YACZ,SAAS,EAAE,GAAG;SACf,CAAC;IACJ,CAAC;IAED,wBAAwB,CAAsB,YAAoB;QAChE,MAAM,OAAO,GAAY;YACvB,IAAI,EAAE,YAAY;YAClB,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;SAC9B,CAAC;QAEF,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QACrC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAED,qBAAqB;QACnB,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;IACvB,CAAC;IAED,eAAe,CAAsB,WAAoB;QACvD,+HAA+H;QAC/H,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAA8B,CAAC;QAC1D,MAAM,GAAG,GAAG,WAAW,CAAC,QAAqC,CAAC;QAE9D,oEAAoE;QACpE,mBAAmB;QACnB,IAAI,GAAG,CAAC,WAAW,IAAI,SAAS,CAAC,WAAW,KAAK,IAAI,EAAE;YACrD,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;YACpC,GAAG,CAAC,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;YAChC,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;SACrC;QACD,4BAA4B;aACvB;YACH,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC;YACtB,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC;YACpB,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC;SACvB;IACH,CAAC;IAED,qBAAqB,CAAsB,WAAoB;QAC7D,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC7B,iHAAiH;QACjH,MAAM,GAAG,GAAG,WAAW,CAAC,QAAS,CAAC;QAElC,oEAAoE;QACpE,mBAAmB;QACnB,IAAI,GAAG,CAAC,WAAW,IAAI,SAAS,CAAC,WAAW,KAAK,IAAI,EAAE;YACrD,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;SACrC;QACD,4BAA4B;aACvB;YACH,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC;SACvB;IACH,CAAC;IAED,eAAe,CAEb,GAAW,EACX,aAAqB;QAErB,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC1D,gBAAgB,CAAC,OAAO,EAAE,aAAa,EAAE,GAAG,CAAC,CAAC;QAC9C,6EAA6E;QAC7E,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,QAAS,EAAO,aAAa,CAAC,CAAC;IACvE,CAAC;IAED,kBAAkB,CAEhB,aAAsB,EACtB,QAAgB;QAEhB,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC7D,oBAAoB,CAAC,UAAU,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QAC1D,6EAA6E;QAC7E,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,QAAS,EAAE,aAAa,CAAC,QAAS,CAAC,CAAC;IAC9E,CAAC;IAED,4BAA4B;QAK1B,IAAI,WAAW,CAAC,IAAI,CAAC,yBAAyB,CAAC,EAAE;YAC/C,MAAM,4BAA4B,GAAG,oCAAoC,CACvE,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAChC,CAAC;YACF,IAAI,CAAC,yBAAyB,GAAG,4BAA4B,CAAC;YAC9D,OAAO,4BAA4B,CAAC;SACrC;QAED,OAAY,IAAI,CAAC,yBAAyB,CAAC;IAC7C,CAAC;IAED,wCAAwC;QAKtC,IAAI,WAAW,CAAC,IAAI,CAAC,qCAAqC,CAAC,EAAE;YAC3D,MAAM,cAAc,GAAG,wCAAwC,CAC7D,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAC/B,IAAI,CAAC,4BAA4B,EAAE,CACpC,CAAC;YACF,IAAI,CAAC,qCAAqC,GAAG,cAAc,CAAC;YAC5D,OAAO,cAAc,CAAC;SACvB;QAED,OAAY,IAAI,CAAC,qCAAqC,CAAC;IACzD,CAAC;IAED,4BAA4B;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,OAAO,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACzC,CAAC;IAED,gCAAgC;QAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,OAAO,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACzC,CAAC;IAED,kCAAkC;QAChC,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACnD,OAAO,eAAe,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACrD,CAAC;CACF"}