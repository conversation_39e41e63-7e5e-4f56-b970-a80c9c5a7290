{"version": 3, "file": "keys.js", "sourceRoot": "", "sources": ["../../../../src/parse/grammar/keys.ts"], "names": [], "mappings": "AAAA,gDAAgD;AAChD,sCAAsC;AACtC,mCAAmC;AACnC,kCAAkC;AAClC,8CAA8C;AAC9C,oDAAoD;AAEpD,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAAC,CAAC;AACtC,MAAM,CAAC,MAAM,uBAAuB,GAAG,CAAC,CAAC;AACzC,MAAM,CAAC,MAAM,iBAAiB,GAAG,EAAE,CAAC;AACpC,mGAAmG;AACnG,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,CAAC;AAElC,6CAA6C;AAC7C,iFAAiF;AACjF,+EAA+E;AAC/E,MAAM,CAAC,MAAM,MAAM,GAAG,CAAC,IAAI,uBAAuB,CAAC;AACnD,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,IAAI,uBAAuB,CAAC;AACvD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,IAAI,uBAAuB,CAAC;AACrD,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,IAAI,uBAAuB,CAAC;AAC7D,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,IAAI,uBAAuB,CAAC;AACzD,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAAC,IAAI,uBAAuB,CAAC;AAEjE,sFAAsF;AACtF,MAAM,UAAU,2BAA2B,CACzC,OAAe,EACf,YAAoB,EACpB,UAAkB;IAElB,OAAO,UAAU,GAAG,YAAY,GAAG,OAAO,CAAC;AAC7C,CAAC;AAED,MAAM,sBAAsB,GAAG,EAAE,GAAG,gBAAgB,CAAC"}