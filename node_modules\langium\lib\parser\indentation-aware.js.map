{"version": 3, "file": "indentation-aware.js", "sourceRoot": "", "sources": ["../../src/parser/indentation-aware.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAOhF,OAAO,EAAE,WAAW,EAAE,mBAAmB,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACrE,OAAO,EAAE,mBAAmB,EAAE,MAAM,oBAAoB,CAAC;AACzD,OAAO,EAAE,wBAAwB,EAAE,YAAY,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAC;AA8CtF,MAAM,CAAC,MAAM,gCAAgC,GAAmC;IAC5E,eAAe,EAAE,QAAQ;IACzB,eAAe,EAAE,QAAQ;IACzB,mBAAmB,EAAE,IAAI;IACzB,2BAA2B,EAAE,EAAE;CAClC,CAAC;AAEF,MAAM,CAAN,IAAY,UAGX;AAHD,WAAY,UAAU;IAClB,+CAAiC,CAAA;IACjC,uDAAyC,CAAA;AAC7C,CAAC,EAHW,UAAU,KAAV,UAAU,QAGrB;AAOD;;;;;;;;;GASG;AACH,MAAM,OAAO,4BAAqG,SAAQ,mBAAmB;IAyBzI,YAAY,UAA6F,gCAA0F;QAC/L,KAAK,EAAE,CAAC;QAzBZ;;;WAGG;QACO,qBAAgB,GAAa,CAAC,CAAC,CAAC,CAAC;QAc3C;;;WAGG;QACO,qBAAgB,GAAG,SAAS,CAAC;QAInC,IAAI,CAAC,OAAO,mCACL,gCAA0F,GAC1F,OAAO,CACb,CAAC;QAEF,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC;YAC/B,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;YAClC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;YACtC,WAAW,EAAE,KAAK;SACrB,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC;YAC/B,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;YAClC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;YACtC,WAAW,EAAE,KAAK;SACrB,CAAC,CAAC;IACP,CAAC;IAEQ,WAAW,CAAC,OAAgB,EAAE,OAAyC;QAC5E,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACvD,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC/D,CAAC;QAED,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,mBAAmB,EAAE,2BAA2B,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAE5G,yHAAyH;QACzH,0CAA0C;QAC1C,IAAI,MAA6B,CAAC;QAClC,IAAI,MAA6B,CAAC;QAClC,IAAI,EAAyB,CAAC;QAC9B,MAAM,WAAW,GAAgB,EAAE,CAAC;QACpC,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACjC,KAAK,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,2BAA2B,EAAE,CAAC;gBACrD,IAAI,SAAS,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;oBAC3B,SAAS,CAAC,SAAS,GAAG,UAAU,CAAC,kBAAkB,CAAC;gBACxD,CAAC;qBAAM,IAAI,SAAS,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;oBAChC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC;gBAC9B,CAAC;YACL,CAAC;YACD,IAAI,SAAS,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;gBACrC,MAAM,GAAG,SAAS,CAAC;YACvB,CAAC;iBAAM,IAAI,SAAS,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;gBAC5C,MAAM,GAAG,SAAS,CAAC;YACvB,CAAC;iBAAM,IAAI,SAAS,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;gBAChD,EAAE,GAAG,SAAS,CAAC;YACnB,CAAC;iBAAM,CAAC;gBACJ,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChC,CAAC;QACL,CAAC;QACD,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,2BAA2B,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzC,MAAM,iBAAiB,GAA8B;gBACjD,KAAK,EAAE;oBACH,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,WAAW,EAAE,EAAE,CAAC;oBAC1D,CAAC,UAAU,CAAC,kBAAkB,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,EAAE,CAAC;iBACxD;gBACD,WAAW,EAAE,UAAU,CAAC,OAAO;aAClC,CAAC;YACF,OAAO,iBAAiB,CAAC;QAC7B,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,WAAW,CAAC,CAAC;QAChD,CAAC;IACL,CAAC;IAEQ,iBAAiB,CAAC,IAAY;QACnC,MAAM,MAAM,GAAG,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC7C,uCACO,MAAM,KACT,gBAAgB,EAAE,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IACpD;IACN,CAAC;IAED;;;;;;OAMG;IACO,aAAa,CAAC,IAAY,EAAE,MAAc;QAChD,OAAO,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;;;;OAQG;IACH,6DAA6D;IACnD,eAAe,CAAC,IAAY,EAAE,MAAc,EAAE,MAAgB,EAAE,MAAgC;;QACtG,IAAI,CAAC,gBAAgB,CAAC,SAAS,GAAG,MAAM,CAAC;QACzC,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,OAAO;YACH,eAAe,EAAE,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAG,CAAC,EAAE,MAAM,mCAAI,CAAC;YACvC,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE;YAC9C,KAAK;SACR,CAAC;IACN,CAAC;IAED;;;;;;;;OAQG;IACO,8BAA8B,CAAC,SAAoB,EAAE,IAAY,EAAE,KAAa,EAAE,MAAc;QACtG,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACpD,OAAO,mBAAmB,CACtB,SAAS,EACT,KAAK,EACL,MAAM,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,EAC7B,UAAU,EAAE,UAAU,EACtB,CAAC,EAAE,KAAK,CAAC,MAAM,CAClB,CAAC;IACN,CAAC;IAED;;;;;;OAMG;IACO,aAAa,CAAC,IAAY,EAAE,MAAc;QAChD,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC;IAChE,CAAC;IAED;;;;;;;OAOG;IACO,aAAa,CAAC,IAAY,EAAE,MAAc,EAAE,MAAgB,EAAE,MAAgC;QACpG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAEvG,IAAI,eAAe,IAAI,eAAe,EAAE,CAAC;YACrC,sDAAsD;YACtD,0EAA0E;YAC1E,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAE5C,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;OAOG;IACO,aAAa,CAAC,IAAY,EAAE,MAAc,EAAE,MAAgB,EAAE,MAAgC;;QACpG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAEvG,IAAI,eAAe,IAAI,eAAe,EAAE,CAAC;YACrC,mDAAmD;YACnD,0EAA0E;YAC1E,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;QAE5E,yDAAyD;QACzD,IAAI,gBAAgB,KAAK,CAAC,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;gBAClB,QAAQ,EAAE,OAAO;gBACjB,OAAO,EAAE,wBAAwB,eAAe,eAAe,MAAM,gCAAgC,IAAI,CAAC,gBAAgB,EAAE;gBAC5H,MAAM;gBACN,MAAM,EAAE,MAAA,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAG,CAAC,CAAC,0CAAE,MAAM,mCAAI,CAAC;gBAC/B,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC;gBACtC,MAAM,EAAE,CAAC;aACZ,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,gBAAgB,GAAG,CAAC,CAAC;QAC5E,MAAM,oBAAoB,GAAG,MAAA,MAAA,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,0CAAG,CAAC,EAAE,MAAM,mCAAI,CAAC,CAAC;QAE1F,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,MAAM,KAAK,GAAG,IAAI,CAAC,8BAA8B,CAC7C,IAAI,CAAC,eAAe,EACpB,IAAI,EACJ,EAAE,EAAG,6BAA6B;YAClC,MAAM,GAAG,CAAC,oBAAoB,GAAG,CAAC,CAAC,CACtC,CAAC;YACF,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnB,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,CAAC;QAChC,CAAC;QAED,8FAA8F;QAC9F,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,kBAAkB,CAAC,QAAsB;QACxD,MAAM,SAAS,GAAG,KAAK,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QACrD,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,mBAAmB,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAE/E,IAAI,SAAS,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,eAAe,CAAC;QAChC,CAAC;aAAM,IAAI,SAAS,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;YAC5C,OAAO,IAAI,CAAC,eAAe,CAAC;QAChC,CAAC;aAAM,IAAI,SAAS,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;YAChD,OAAO,WAAW,CAAC;gBACf,IAAI,EAAE,mBAAmB;gBACzB,OAAO,EAAE,IAAI,CAAC,gBAAgB;gBAC9B,KAAK,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC,CAAC;QACP,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;;OAKG;IACH,qBAAqB,CAAC,IAAY;QAC9B,MAAM,gBAAgB,GAAa,EAAE,CAAC;QACtC,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtC,gBAAgB,CAAC,IAAI,CACjB,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CACnF,CAAC;YACF,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5B,OAAO,gBAAgB,CAAC;IAC5B,CAAC;CACJ;AAED;;;;;;;;;;;;GAYG;AACH,MAAM,OAAO,qBAAsB,SAAQ,YAAY;IAInD,YAAY,QAA6B;QACrC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAChB,IAAI,QAAQ,CAAC,MAAM,CAAC,YAAY,YAAY,4BAA4B,EAAE,CAAC;YACvE,IAAI,CAAC,uBAAuB,GAAG,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC;QAChE,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,KAAK,CAAC,6EAA6E,CAAC,CAAC;QACnG,CAAC;IACL,CAAC;IAEQ,QAAQ,CAAC,IAAY,EAAE,UAA2B,wBAAwB;QAC/E,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEpC,oFAAoF;QACpF,MAAM,MAAM,GAAG,MAAM,CAAC,MAAiC,CAAC;QACxD,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,MAAK,MAAM,EAAE,CAAC;YAC3B,gDAAgD;YAChD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC;QACnD,CAAC;QACD,MAAM,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAE7B,4EAA4E;QAC5E,yEAAyE;QACzE,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC,uBAAuB,CAAC;QAC1E,uCAAuC;QACvC,MAAM,cAAc,GAAG,eAAe,CAAC,YAAY,CAAC;QACpD,MAAM,cAAc,GAAG,eAAe,CAAC,YAAY,CAAC;QACpD,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9B,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACvC,IAAI,KAAK,CAAC,YAAY,KAAK,cAAc,IAAI,SAAS,CAAC,YAAY,KAAK,cAAc,EAAE,CAAC;gBACrF,CAAC,EAAE,CAAC;gBACJ,SAAS;YACb,CAAC;YAED,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC;QACD,6BAA6B;QAC7B,IAAI,MAAM,IAAI,CAAC,EAAE,CAAC;YACd,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QAC5C,CAAC;QACD,MAAM,CAAC,MAAM,GAAG,WAAW,CAAC;QAE5B,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ"}