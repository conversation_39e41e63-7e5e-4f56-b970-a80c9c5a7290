{"version": 3, "file": "recognizer_api.js", "sourceRoot": "", "sources": ["../../../../../src/parse/parser/traits/recognizer_api.ts"], "names": [], "mappings": "AAeA,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AAC7C,OAAO,EAAE,sBAAsB,EAAE,MAAM,4BAA4B,CAAC;AACpE,OAAO,EAAE,mBAAmB,EAAE,yBAAyB,EAAE,MAAM,cAAc,CAAC;AAC9E,OAAO,EAAE,oCAAoC,EAAE,MAAM,wBAAwB,CAAC;AAC9E,OAAO,EAAE,wBAAwB,EAAE,MAAM,yBAAyB,CAAC;AAEnE,OAAO,EAAQ,gBAAgB,EAAE,MAAM,kBAAkB,CAAC;AAI1D;;;;;;;GAOG;AACH,MAAM,OAAO,aAAa;IACxB,MAAM,CAAyB,IAAa;QAC1C,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAED,OAAO,CAEL,GAAW,EACX,OAAkB,EAClB,OAA2B;QAE3B,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;IACrD,CAAC;IAED,OAAO,CAEL,GAAW,EACX,UAAyC,EACzC,OAAiC;QAEjC,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;IACxD,CAAC;IAED,MAAM,CAEJ,GAAW,EACX,iBAA0D;QAE1D,OAAO,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;IACrD,CAAC;IAED,EAAE,CAEA,GAAW,EACX,UAA6C;QAE7C,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IAC1C,CAAC;IAED,IAAI,CAEF,GAAW,EACX,iBAA0D;QAE1D,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;IACnD,CAAC;IAED,UAAU,CAER,GAAW,EACX,iBAAiE;QAEjE,OAAO,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;IACzD,CAAC;IAED,OAAO,CAEL,OAAkB,EAClB,OAA2B;QAE3B,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAED,QAAQ,CAEN,OAAkB,EAClB,OAA2B;QAE3B,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAED,QAAQ,CAEN,OAAkB,EAClB,OAA2B;QAE3B,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAED,QAAQ,CAEN,OAAkB,EAClB,OAA2B;QAE3B,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAED,QAAQ,CAEN,OAAkB,EAClB,OAA2B;QAE3B,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAED,QAAQ,CAEN,OAAkB,EAClB,OAA2B;QAE3B,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAED,QAAQ,CAEN,OAAkB,EAClB,OAA2B;QAE3B,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAED,QAAQ,CAEN,OAAkB,EAClB,OAA2B;QAE3B,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAED,QAAQ,CAEN,OAAkB,EAClB,OAA2B;QAE3B,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAED,QAAQ,CAEN,OAAkB,EAClB,OAA2B;QAE3B,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAED,OAAO,CAEL,UAAyC,EACzC,OAAiC;QAEjC,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IAED,QAAQ,CAEN,UAAyC,EACzC,OAAiC;QAEjC,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IAED,QAAQ,CAEN,UAAyC,EACzC,OAAiC;QAEjC,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IAED,QAAQ,CAEN,UAAyC,EACzC,OAAiC;QAEjC,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IAED,QAAQ,CAEN,UAAyC,EACzC,OAAiC;QAEjC,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IAED,QAAQ,CAEN,UAAyC,EACzC,OAAiC;QAEjC,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IAED,QAAQ,CAEN,UAAyC,EACzC,OAAiC;QAEjC,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IAED,QAAQ,CAEN,UAAyC,EACzC,OAAiC;QAEjC,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IAED,QAAQ,CAEN,UAAyC,EACzC,OAAiC;QAEjC,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IAED,QAAQ,CAEN,UAAyC,EACzC,OAAiC;QAEjC,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IAED,MAAM,CAEJ,iBAA0D;QAE1D,OAAO,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC;IAED,OAAO,CAEL,iBAA0D;QAE1D,OAAO,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC;IAED,OAAO,CAEL,iBAA0D;QAE1D,OAAO,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC;IAED,OAAO,CAEL,iBAA0D;QAE1D,OAAO,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC;IAED,OAAO,CAEL,iBAA0D;QAE1D,OAAO,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC;IAED,OAAO,CAEL,iBAA0D;QAE1D,OAAO,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC;IAED,OAAO,CAEL,iBAA0D;QAE1D,OAAO,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC;IAED,OAAO,CAEL,iBAA0D;QAE1D,OAAO,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC;IAED,OAAO,CAEL,iBAA0D;QAE1D,OAAO,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC;IAED,OAAO,CAEL,iBAA0D;QAE1D,OAAO,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC;IAED,EAAE,CAEA,UAAiD;QAEjD,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,GAAG,CAED,UAAiD;QAEjD,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,GAAG,CAED,UAAiD;QAEjD,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,GAAG,CAED,UAAiD;QAEjD,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,GAAG,CAED,UAAiD;QAEjD,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,GAAG,CAED,UAAiD;QAEjD,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,GAAG,CAED,UAAiD;QAEjD,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,GAAG,CAED,UAAiD;QAEjD,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,GAAG,CAED,UAAiD;QAEjD,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,GAAG,CAED,UAAiD;QAEjD,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IACxC,CAAC;IAED,IAAI,CAEF,iBAA0D;QAE1D,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAEH,iBAA0D;QAE1D,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAEH,iBAA0D;QAE1D,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAEH,iBAA0D;QAE1D,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAEH,iBAA0D;QAE1D,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAEH,iBAA0D;QAE1D,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAEH,iBAA0D;QAE1D,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAEH,iBAA0D;QAE1D,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAEH,iBAA0D;QAE1D,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAEH,iBAA0D;QAE1D,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAC1C,CAAC;IAED,QAAQ,CAA2B,OAA+B;QAChE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,SAAS,CAA2B,OAA+B;QACjE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,SAAS,CAA2B,OAA+B;QACjE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,SAAS,CAA2B,OAA+B;QACjE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,SAAS,CAA2B,OAA+B;QACjE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,SAAS,CAA2B,OAA+B;QACjE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,SAAS,CAA2B,OAA+B;QACjE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,SAAS,CAA2B,OAA+B;QACjE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,SAAS,CAA2B,OAA+B;QACjE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,SAAS,CAA2B,OAA+B;QACjE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,YAAY,CAEV,iBAAiE;QAEjE,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAChD,CAAC;IAED,aAAa,CAEX,iBAAiE;QAEjE,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IACvD,CAAC;IAED,aAAa,CAEX,iBAAiE;QAEjE,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAChD,CAAC;IAED,aAAa,CAEX,iBAAiE;QAEjE,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAChD,CAAC;IAED,aAAa,CAEX,iBAAiE;QAEjE,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAChD,CAAC;IAED,aAAa,CAEX,iBAAiE;QAEjE,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAChD,CAAC;IAED,aAAa,CAEX,iBAAiE;QAEjE,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAChD,CAAC;IAED,aAAa,CAEX,iBAAiE;QAEjE,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAChD,CAAC;IAED,aAAa,CAEX,iBAAiE;QAEjE,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAChD,CAAC;IAED,aAAa,CAEX,iBAAiE;QAEjE,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAChD,CAAC;IAED,gBAAgB,CAEd,OAAqC;QAErC,IAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,iBAAiB,CAEf,OAAqC;QAErC,IAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,iBAAiB,CAEf,OAAqC;QAErC,IAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,iBAAiB,CAEf,OAAqC;QAErC,IAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,iBAAiB,CAEf,OAAqC;QAErC,IAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,iBAAiB,CAEf,OAAqC;QAErC,IAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,iBAAiB,CAEf,OAAqC;QAErC,IAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,iBAAiB,CAEf,OAAqC;QAErC,IAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,iBAAiB,CAEf,OAAqC;QAErC,IAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,iBAAiB,CAEf,OAAqC;QAErC,IAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,IAAI,CAEF,IAAY,EACZ,cAAyC,EACzC,SAAyB,mBAAmB;QAE5C,IAAI,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,EAAE;YAC1C,MAAM,MAAM,GACV,oCAAoC,CAAC,2BAA2B,CAAC;gBAC/D,YAAY,EAAE,IAAI;gBAClB,WAAW,EAAE,IAAI,CAAC,SAAS;aAC5B,CAAC,CAAC;YAEL,MAAM,KAAK,GAAG;gBACZ,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE,yBAAyB,CAAC,mBAAmB;gBACnD,QAAQ,EAAE,IAAI;aACf,CAAC;YACF,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACnC;QAED,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAElC,MAAM,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;QACxE,IAAY,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC;QACzC,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAED,aAAa,CAEX,IAAY,EACZ,IAA+B,EAC/B,SAAyB,mBAAmB;QAE5C,MAAM,UAAU,GAA6B,wBAAwB,CACnE,IAAI,EACJ,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,SAAS,CACf,CAAC;QACF,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAEjE,MAAM,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QAC9D,IAAY,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC;QACzC,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAED,SAAS,CAEP,WAAkC,EAClC,IAAY;QAEZ,OAAO;YACL,iBAAiB;YACjB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACvC,IAAI;gBACF,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC9B,+DAA+D;gBAC/D,OAAO,IAAI,CAAC;aACb;YAAC,OAAO,CAAC,EAAE;gBACV,IAAI,sBAAsB,CAAC,CAAC,CAAC,EAAE;oBAC7B,OAAO,KAAK,CAAC;iBACd;qBAAM;oBACL,MAAM,CAAC,CAAC;iBACT;aACF;oBAAS;gBACR,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;gBAChC,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,CAAC;aAChC;QACH,CAAC,CAAC;IACJ,CAAC;IAED,mBAAmB;IACZ,kBAAkB;QACvB,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACnC,CAAC;IAEM,4BAA4B;QACjC,OAAO,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC;IAC7D,CAAC;CACF"}