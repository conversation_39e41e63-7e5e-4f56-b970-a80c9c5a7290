/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M16 3h2a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1h-2", key: "tum69e" }],
  ["path", { d: "M8 21H6a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h2", key: "z7wn0n" }]
];
const Brackets = createLucideIcon("brackets", __iconNode);

export { __iconNode, Brackets as default };
//# sourceMappingURL=brackets.js.map
